"use client";

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../../types/outline';

// 节点类型中文映射函数
const getNodeTypeLabel = (type: string): string => {
  switch (type) {
    case 'chapter':
      return '章节';
    case 'plot':
      return '剧情节点';
    case 'dialogue':
      return '对话设计';
    case 'scene':
      return '场景';
    case 'note':
      return '笔记';
    case 'synopsis':
      return '核心故事梗概';
    default:
      return type || '未知';
  }
};

interface BatchDeleteConfirmDialogProps {
  isOpen: boolean;
  nodesToDelete: OutlineNodeType[];
  onConfirm: () => Promise<void> | void;
  onCancel: () => void;
  showRecoveryHint?: boolean;
}

const BatchDeleteConfirmDialog: React.FC<BatchDeleteConfirmDialogProps> = ({
  isOpen,
  nodesToDelete,
  onConfirm,
  onCancel,
  showRecoveryHint = true
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const cancelButtonRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 统计节点类型
  const getNodeTypeStats = () => {
    const stats = nodesToDelete.reduce((acc, node) => {
      const type = node.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(stats)
      .map(([type, count]) => `${count}个${getNodeTypeLabel(type)}`)
      .join('、');
  };

  // 计算总的子节点数量
  const getTotalChildrenCount = () => {
    return nodesToDelete.reduce((total, node) => {
      const countChildren = (n: OutlineNodeType): number => {
        let count = 0;
        if (n.children && n.children.length > 0) {
          count += n.children.length;
          n.children.forEach(child => {
            count += countChildren(child);
          });
        }
        return count;
      };
      return total + countChildren(node);
    }, 0);
  };

  // 处理确认删除
  const handleConfirm = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setError(null);
      await onConfirm();
    } catch (err) {
      setError(err instanceof Error ? err.message : '批量删除失败，请重试');
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onCancel();
      } else if (e.key === 'Enter' && !isLoading) {
        handleConfirm();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, isLoading, onCancel]);

  // 自动聚焦到取消按钮
  useEffect(() => {
    if (isOpen && cancelButtonRef.current) {
      cancelButtonRef.current.focus();
    }
  }, [isOpen]);

  // 重置状态
  useEffect(() => {
    if (!isOpen) {
      setIsLoading(false);
      setError(null);
    }
  }, [isOpen]);

  if (!isOpen || nodesToDelete.length === 0) return null;

  const nodeTypeStats = getNodeTypeStats();
  const totalChildrenCount = getTotalChildrenCount();

  return createPortal(
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)',
        animation: 'fadeIn 0.3s ease-out'
      }}
      onClick={onCancel}
    >
      <div
        ref={dialogRef}
        className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4 overflow-hidden"
        style={{
          animation: 'scaleIn 0.3s ease-out',
          transformOrigin: 'center center'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 标题区域 */}
        <div className="px-6 pt-6 pb-4 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-50 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            确认批量删除
          </h3>
        </div>

        {/* 删除统计信息 */}
        <div className="px-6 pb-4">
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900 mb-2">
                将要删除 <span className="text-red-600">{nodesToDelete.length}</span> 个节点
              </p>
              <p className="text-sm text-gray-600 mb-3">
                包含：{nodeTypeStats}
              </p>
              
              {totalChildrenCount > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-yellow-800 text-sm font-medium mb-1">
                    ⚠️ 连带影响
                  </p>
                  <p className="text-yellow-700 text-sm">
                    同时删除 {totalChildrenCount} 个子节点
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 节点列表预览 */}
          {nodesToDelete.length <= 5 ? (
            <div className="space-y-2 mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">删除的节点：</p>
              {nodesToDelete.map((node) => (
                <div key={node.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md">
                  <div
                    className="flex-shrink-0 w-6 h-6 rounded-md flex items-center justify-center text-white text-xs font-medium"
                    style={{
                      background: `var(--outline-${node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'})`
                    }}
                  >
                    {getNodeTypeLabel(node.type).charAt(0)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {node.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {getNodeTypeLabel(node.type)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="mb-4">
              <p className="text-sm text-gray-600 text-center">
                节点数量较多，将批量删除所有选中的节点
              </p>
            </div>
          )}

          {/* 恢复提示 */}
          {showRecoveryHint && (
            <div className="text-xs text-gray-500 mb-4 text-center">
              💡 删除的内容可在30天内恢复
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="px-6 pb-6 flex space-x-3">
          <button
            ref={cancelButtonRef}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-gray-300"
            onClick={onCancel}
            disabled={isLoading}
          >
            取消
          </button>
          <button
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
            ) : (
              `确认删除 ${nodesToDelete.length} 个节点`
            )}
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes scaleIn {
          from { 
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
          }
          to { 
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
    </div>,
    document.body
  );
};

export default BatchDeleteConfirmDialog;
