/**
 * 反馈评论对话框
 * 支持用户为AI回复添加具体的评论说明
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FeedbackRating } from '../../../types/ai-feedback';

interface FeedbackCommentDialogProps {
  isOpen: boolean;
  rating: FeedbackRating;
  initialComment?: string;
  onConfirm: (rating: FeedbackRating, comment: string) => void;
  onCancel: () => void;
  aiResponse: string;
}

export const FeedbackCommentDialog: React.FC<FeedbackCommentDialogProps> = ({
  isOpen,
  rating,
  initialComment = '',
  onConfirm,
  onCancel,
  aiResponse
}) => {
  const [comment, setComment] = useState(initialComment);
  const [selectedRating, setSelectedRating] = useState(rating);

  // 重置状态当对话框打开时
  useEffect(() => {
    if (isOpen) {
      setComment(initialComment);
      setSelectedRating(rating);
    }
  }, [isOpen, rating, initialComment]);

  // 评分配置
  const ratingConfig = {
    good: {
      icon: '👍',
      label: '好',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-500',
      placeholder: '请说明这个回复好在哪里，比如：专业性强、建议实用、表达清晰等...',
      prompts: [
        '专业性强，建议很实用',
        '回复详细，解决了我的问题',
        '表达清晰，容易理解',
        '创意新颖，给了我灵感',
        '结构清晰，逻辑性强'
      ]
    },
    average: {
      icon: '👌',
      label: '一般',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      borderColor: 'border-yellow-500',
      placeholder: '请说明这个回复一般在哪里，有什么可以改进的地方...',
      prompts: [
        '回复有用但不够详细',
        '建议可以更具体一些',
        '内容正确但缺乏创新',
        '表达可以更清晰',
        '需要更多实例说明'
      ]
    },
    poor: {
      icon: '👎',
      label: '差',
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      borderColor: 'border-red-500',
      placeholder: '请说明这个回复差在哪里，具体有什么问题...',
      prompts: [
        '回复不够专业，建议太泛泛',
        '没有理解我的问题',
        '建议不实用，缺乏可操作性',
        '回复太简单，没有深度',
        '表达不清晰，容易误解'
      ]
    }
  };

  const currentConfig = ratingConfig[selectedRating];

  const handleConfirm = () => {
    onConfirm(selectedRating, comment.trim());
  };

  const handlePromptClick = (prompt: string) => {
    if (comment.trim()) {
      setComment(comment + '；' + prompt);
    } else {
      setComment(prompt);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onCancel}
      >
        <motion.div
          className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 标题栏 */}
          <div className={`p-4 ${currentConfig.bgColor} border-b`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{currentConfig.icon}</span>
                <h3 className={`text-lg font-semibold ${currentConfig.color}`}>
                  评价回复质量 - {currentConfig.label}
                </h3>
              </div>
              <button
                onClick={onCancel}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div className="p-4 max-h-[60vh] overflow-y-auto">
            {/* AI回复预览 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">AI回复内容：</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-800 max-h-32 overflow-y-auto">
                {aiResponse}
              </div>
            </div>

            {/* 评分选择 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">调整评分：</h4>
              <div className="flex space-x-2">
                {Object.entries(ratingConfig).map(([key, config]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedRating(key as FeedbackRating)}
                    className={`flex items-center space-x-1 px-3 py-2 rounded-lg border-2 transition-colors ${
                      selectedRating === key
                        ? `${config.bgColor} ${config.borderColor} ${config.color}`
                        : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <span>{config.icon}</span>
                    <span className="text-sm font-medium">{config.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* 评论输入 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                具体说明 <span className="text-gray-500">(可选，但建议填写)</span>：
              </h4>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder={currentConfig.placeholder}
                className="w-full h-24 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"
              />
              <div className="text-xs text-gray-500 mt-1">
                {comment.length}/200 字符
              </div>
            </div>

            {/* 快速评论建议 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">快速选择：</h4>
              <div className="flex flex-wrap gap-2">
                {currentConfig.prompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => handlePromptClick(prompt)}
                    className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                      comment.includes(prompt)
                        ? `${currentConfig.bgColor} ${currentConfig.borderColor} ${currentConfig.color}`
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            </div>

            {/* 说明文字 */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-start space-x-2">
                <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-xs text-blue-700">
                  <p className="font-medium mb-1">您的反馈很重要！</p>
                  <p>具体的评论说明能帮助AI更好地理解您的需求，从而提供更优质的创作指导。</p>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="p-4 border-t bg-gray-50 flex justify-end space-x-2">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className={`px-6 py-2 rounded-lg text-white transition-colors ${
                selectedRating === 'good' ? 'bg-green-500 hover:bg-green-600' :
                selectedRating === 'average' ? 'bg-yellow-500 hover:bg-yellow-600' :
                'bg-red-500 hover:bg-red-600'
              }`}
            >
              确认反馈
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
