import React from 'react';
import { OutlineNodeType } from '../../types/outline';

interface NodeDetailsPanelProps {
  node: OutlineNodeType | null;
  onClose: () => void;
}

/**
 * 节点详情面板组件
 * 用于显示节点的详细信息，包括描述、元数据和子节点列表
 */
const NodeDetailsPanel: React.FC<NodeDetailsPanelProps> = ({ node, onClose }) => {
  if (!node) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl border-l border-gray-200 z-50 overflow-auto">
      <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">节点详情</h3>
        <button
          className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
          onClick={onClose}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="p-4">
        {/* 节点标题 */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-500 mb-1">标题</h4>
          <div className="text-lg font-medium">{node.title}</div>
        </div>

        {/* 节点类型 */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-500 mb-1">类型</h4>
          <div className="inline-block px-2 py-1 rounded-full text-xs font-medium text-white"
            style={{
              background: `var(--outline-${
                node.type === 'volume' ? 'volume' :
                node.type === 'chapter' ? 'primary' :
                node.type === 'plot' ? 'secondary' :
                node.type === 'dialogue' ? 'success' : 'info'
              })`
            }}
          >
            {node.type === 'volume' ? '总纲/卷' :
             node.type === 'chapter' ? '章节' :
             node.type === 'plot' ? '剧情节点' :
             node.type === 'dialogue' ? '对话设计' : '未知'}
          </div>
        </div>

        {/* 节点描述 */}
        {node.description && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-1">描述</h4>
            <div className="text-sm text-gray-700 whitespace-pre-wrap bg-gray-50 p-3 rounded-md border border-gray-100">
              {node.description}
            </div>
          </div>
        )}

        {/* 创作建议 */}
        {node.creativeNotes && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-2">
              <span>💡</span>
              <span>创作建议</span>
            </h4>
            <div className="text-sm text-gray-700 whitespace-pre-wrap bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-md border border-blue-200">
              {node.creativeNotes}
            </div>
          </div>
        )}

        {/* 节点ID */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-500 mb-1">节点ID</h4>
          <div className="text-xs text-gray-500 font-mono bg-gray-50 p-2 rounded overflow-auto">
            {node.id}
          </div>
        </div>

        {/* 子节点列表 */}
        {node.children && node.children.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-1">子节点 ({node.children.length})</h4>
            <ul className="bg-gray-50 rounded-md border border-gray-100 overflow-hidden">
              {node.children.map((child) => (
                <li key={child.id} className="border-b border-gray-100 last:border-b-0">
                  <div className="px-3 py-2 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{
                          background: `var(--outline-${
                            child.type === 'volume' ? 'volume' :
                            child.type === 'chapter' ? 'primary' :
                            child.type === 'plot' ? 'secondary' :
                            child.type === 'dialogue' ? 'success' : 'info'
                          })`
                        }}
                      ></div>
                      <div className="text-sm font-medium">{child.title}</div>
                    </div>
                    {child.description && (
                      <div className="text-xs text-gray-500 mt-1 ml-4 truncate">
                        {child.description.substring(0, 50)}{child.description.length > 50 ? '...' : ''}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 节点位置信息 */}
        {node.position && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-1">位置信息</h4>
            <div className="text-xs text-gray-500 font-mono">
              X: {Math.round(node.position.x)}, Y: {Math.round(node.position.y)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NodeDetailsPanel;
