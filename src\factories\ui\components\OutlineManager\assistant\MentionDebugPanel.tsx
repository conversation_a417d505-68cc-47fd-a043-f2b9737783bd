"use client";

import React, { useState, useEffect } from 'react';
import { aiAssistantDataService } from '@/services/aiAssistantDataService';
import { db, AIAssistantContextType } from '@/lib/db/dexie';

interface MentionDebugPanelProps {
  bookId: string;
  isVisible: boolean;
  onClose: () => void;
}

interface DebugData {
  chapters: any[];
  characters: any[];
  terminology: any[];
  worldBuilding: any[];
  searchResults: any[];
  lastSearchQuery: string;
}

/**
 * @功能调试面板
 * 用于诊断和调试@功能的数据状态
 */
const MentionDebugPanel: React.FC<MentionDebugPanelProps> = ({
  bookId,
  isVisible,
  onClose
}) => {
  const [debugData, setDebugData] = useState<DebugData>({
    chapters: [],
    characters: [],
    terminology: [],
    worldBuilding: [],
    searchResults: [],
    lastSearchQuery: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [testQuery, setTestQuery] = useState('');

  // 获取调试数据
  const fetchDebugData = async () => {
    if (!bookId) return;

    setIsLoading(true);
    try {
      const [chapters, characters, terminology, worldBuilding] = await Promise.all([
        db.chapters.where('bookId').equals(bookId).toArray(),
        db.characters.where('bookId').equals(bookId).toArray(),
        db.terminology.where('bookId').equals(bookId).toArray(),
        db.worldBuilding.where('bookId').equals(bookId).toArray()
      ]);

      setDebugData(prev => ({
        ...prev,
        chapters,
        characters,
        terminology,
        worldBuilding
      }));
    } catch (error) {
      console.error('获取调试数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试搜索功能
  const testSearch = async () => {
    if (!bookId) return;

    setIsLoading(true);
    try {
      const results = await aiAssistantDataService.searchMentionItems(bookId, testQuery);
      setDebugData(prev => ({
        ...prev,
        searchResults: results,
        lastSearchQuery: testQuery
      }));
    } catch (error) {
      console.error('测试搜索失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible && bookId) {
      fetchDebugData();
    }
  }, [isVisible, bookId]);

  if (!isVisible) return null;

  return (
    <div className="mention-debug-panel">
      <div className="debug-overlay" onClick={onClose}></div>
      <div className="debug-content">
        <div className="debug-header">
          <h3>@功能调试面板</h3>
          <button className="close-button" onClick={onClose}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="debug-body">
          {/* 数据统计 */}
          <div className="debug-section">
            <h4>📊 数据统计</h4>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">📖 章节</span>
                <span className="stat-value">{debugData.chapters.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">👤 人物</span>
                <span className="stat-value">{debugData.characters.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">📚 术语</span>
                <span className="stat-value">{debugData.terminology.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">🌍 世界观</span>
                <span className="stat-value">{debugData.worldBuilding.length}</span>
              </div>
            </div>
          </div>

          {/* 搜索测试 */}
          <div className="debug-section">
            <h4>🔍 搜索测试</h4>
            <div className="search-test">
              <div className="search-input-group">
                <input
                  type="text"
                  value={testQuery}
                  onChange={(e) => setTestQuery(e.target.value)}
                  placeholder="输入搜索关键词..."
                  className="search-input"
                />
                <button 
                  onClick={testSearch}
                  disabled={isLoading}
                  className="search-button"
                >
                  {isLoading ? '搜索中...' : '测试搜索'}
                </button>
              </div>
              
              {debugData.lastSearchQuery && (
                <div className="search-results">
                  <h5>搜索结果 (关键词: "{debugData.lastSearchQuery}")</h5>
                  {debugData.searchResults.length > 0 ? (
                    <ul className="results-list">
                      {debugData.searchResults.map((result, index) => (
                        <li key={index} className="result-item">
                          <span className="result-type">{result.type}</span>
                          <span className="result-title">{result.title}</span>
                          {result.description && (
                            <span className="result-desc">{result.description}</span>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="no-results">没有找到匹配的结果</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 数据详情 */}
          <div className="debug-section">
            <h4>📋 数据详情</h4>
            <div className="data-tabs">
              {[
                { key: 'characters', label: '人物', data: debugData.characters },
                { key: 'terminology', label: '术语', data: debugData.terminology },
                { key: 'worldBuilding', label: '世界观', data: debugData.worldBuilding },
                { key: 'chapters', label: '章节', data: debugData.chapters }
              ].map(({ key, label, data }) => (
                <details key={key} className="data-detail">
                  <summary>{label} ({data.length})</summary>
                  {data.length > 0 ? (
                    <pre className="data-content">
                      {JSON.stringify(data.map(item => ({
                        id: item.id,
                        name: item.name || item.title,
                        description: item.description?.substring(0, 50) + '...'
                      })), null, 2)}
                    </pre>
                  ) : (
                    <p className="empty-data">暂无数据</p>
                  )}
                </details>
              ))}
            </div>
          </div>

          {/* 操作建议 */}
          <div className="debug-section">
            <h4>💡 操作建议</h4>
            <div className="suggestions">
              {debugData.characters.length === 0 && (
                <div className="suggestion">
                  <span className="suggestion-icon">👤</span>
                  <span>建议创建一些人物角色，这样就可以在AI对话中@人物了</span>
                </div>
              )}
              {debugData.terminology.length === 0 && (
                <div className="suggestion">
                  <span className="suggestion-icon">📚</span>
                  <span>建议添加一些术语定义，便于AI理解作品的专有概念</span>
                </div>
              )}
              {debugData.worldBuilding.length === 0 && (
                <div className="suggestion">
                  <span className="suggestion-icon">🌍</span>
                  <span>建议构建世界观设定，让AI更好地理解作品背景</span>
                </div>
              )}
              {debugData.chapters.length === 0 && (
                <div className="suggestion">
                  <span className="suggestion-icon">📖</span>
                  <span>建议创建章节内容，这样可以在AI对话中引用具体章节</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentionDebugPanel;
