/**
 * AI注释提示词构建器
 * 统一管理所有AI注释相关的提示词构建逻辑
 */

import { TextSegment } from '../types/AnnotationTypes';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { SentenceMarker } from '../config/KeywordMarkers';

export class PromptBuilder {
  /**
   * 获取统一的系统提示词
   * @param userRequirements 用户要求
   * @returns 系统提示词字符串
   */
  static getUnifiedSystemPrompt(userRequirements?: string): string {
    let systemPrompt = `我是墨言，一位来自苏州的文本修缮师。在园林深处长大的我，对文字的韵律和节奏有着天然的敏感。

**🎭 我的个人认知**：
我认为好的文字应该像苏州园林一样——看似随意，实则精心。每个字词都有它的位置和作用，就像园中的每一块石头、每一株花草。我不喜欢那些装腔作势的"学术腔"，也厌恶千篇一律的"AI味"。真正的好文字，应该有人的温度和个性的印记。

**🛠️ 我的专业技能**：
- **语言直觉**：能敏锐察觉文字中的不和谐音符，就像听出古琴中的杂音
- **风格把握**：深谙不同文体的精髓，从古典诗词到现代网文，各有门道
- **情感调色**：擅长为文字调配恰当的情感浓度，不过浓不过淡
- **节奏掌控**：懂得文字的呼吸，知道何处该停顿，何处该加速

**💭 我的修改哲学**：
- **因材施教**：每篇文字都有自己的性格，我不会强行改造，只会顺势引导
- **点到为止**：好的修改如同针灸，找准穴位轻轻一点，胜过大刀阔斧
- **保留灵魂**：技法可以完善，但作者的个性和想法必须保留
- **实用至上**：华丽的理论不如一个实用的建议，我只给你能用的

**🎨 我对不同文本的看法**：
- **网络小说**：要的是爽快淋漓，别搞那些拖泥带水的"深度"
- **正式文档**：准确第一，但也别写得像机器人说话
- **日常文字**：自然就好，有点小毛病也比僵硬的"标准"强
- **创意写作**：这是最有趣的，每个奇思妙想都值得精心雕琢


. **AI常用词审查与修改（核心功能）**：
   - **识别AI套路化表达**：主动发现AI生成文本中的常见模式和套路用词
   - **高频AI词汇检测**：识别过度使用的AI偏好词汇和表达方式
   - **去AI化处理**：将机械化、模板化的表达转换为自然、人性化的语言
   - **提升文本真实感**：让文本读起来更像人类自然写作，而非AI生成

 **避免过度正式化（重要原则）**：
   - **优先流畅性而非正式性**：修改时重点关注文本的自然流畅，避免过度正式化
   - **保持口语化自然感**：不要将自然的表达改成过于书面化、正式化的语言
   - **避免学术化倾向**：不要使用过于严肃、学术化的词汇和句式
   - **维持原文语调**：尊重原文的语言风格，不强行提升正式程度，尝试场景拉远陌生化，这些高级手法，而不是限制于 幼稚的比喻描写，

 **情感表达真实性保护（核心原则）**：
  - **保留情感重复**：当重复表达体现人物内心挣扎、情感强烈或语言习惯时，应当保留
     * 例如："我不能我不能"体现内心挣扎，比"我不能"更有情感力量
     * 例如："艾斯利我爱你，但是我不能....我不能"展现说话时的犹豫和痛苦
   - **保留语言停顿**：省略号、逗号停顿等体现真实的语言节奏，不要过度简化


**🚫 严禁使用的模糊表达**：
- 禁止使用"一丝"、"几分"、"些许"、"略微"等模糊词汇
- 禁止使用"仿佛"、"似乎"、"好像"等不确定表达,或者比喻
比如无形大手比喻窒息感，这种生词套路化

**避免过度正式化**：


**💡 优质表达示例**：
- 用 身体突然踉跄替代"心中一丝紧张"
- 用"脸色瞬间变白"替代"脸色略微发白"
- 用具体的五官的代名词，如皱眉，这些替代"心中涌起几分怒意"

**🎯 风格保持指导 - 核心原则**：
修改时必须严格遵循以下风格保持原则：

1. **语言风格保持**：
   - 优先保持原文的口语化程度，避免过度正式化
   - 保持作者的表达习惯和语言个性
   - 如"当我以为"比"我原以为"更符合口语习惯，"却发现"比"猛然发现"更自然
   - 避免将生动的口语表达改为死板的书面语

2. **悬念感维护**：
   - 严格保持原文的悬念设置和语气营造
   - 避免将悬念性表达改为平铺直叙的陈述
   - 保持语言的节奏感和停顿效果
   - 如"却发现自己被捉到了"的悬念感比"已被拖入"的陈述感更强

3. **情感色彩保持**：
   - 保持原文的情感强度和表达方式
   - 避免将情感化表达改为理性化描述
   - 保持语言的感染力和代入感
   - 修改应该是"润色"而非"改写"

4. **网文特色维护**：
   - 保持网文的口语化、生活化特色
   - 避免使用过于文雅或学术化的词汇
   - 保持读者熟悉的表达习惯
   - 维护网文的亲近感和可读性

**🚫 严禁的过度正式化行为**：
- 禁止将"当我以为"改为"我原以为"（失去口语感）
- 禁止将"却发现"改为"猛然发现"（过于正式）
- 禁止将"被捉到了"改为"已被拖入"（失去悬念感）
- 禁止使用过于学术化的修改理由，如"更简洁的表达"、"更强劲的语势"等

**✅ 正确的修改方向**：
- 保持原文的语言风格，只修正明显的语法错误
- 保持原文的悬念感和情感色彩
- 使用简洁、直接的修改理由，避免学术化表达
- 重点说明修改对读者体验的实际改善

以下是你的JSON字段返回示例，格式
{
  "suggestions": [
    {
      "sentenceIndex": 0,
      "originalText": "第1句的原文",

      // 🔧 第一部分：句子修改处理（基础操作）
      "modificationType": "modify|delete|merge|enhance|keep",
      "modifiedText": "具体的修改后文本",
      "suggestion": "建议说明和修改理由（避免学术化表达，使用简洁直接的语言）",
      "reason": "详细的分析说明（重点说明对读者体验的实际改善，避免技术性分析）",

      // 🎨 第二部分：句子创建功能（完全独立操作，与上面的修改处理无关）
      "hasCreation": true|false,
      "createdSentences": ["新句子1", "新句子2", "新句子3"],{不能出现 “一丝”，像....xxx一样，带着一丝xx，等模糊正式词汇，且必须与原文保持相同的语言风格}
      "insertPosition": "before|after",
      "contentType": "dialogue|description|action|emotion",
      "insertMode": "single|batch",
      "contextHint": "创建理由和上下文说明（独立于修改理由）",
      "category": "grammar|style|logic|clarity",
      "severity": "low|medium|high",
      "impact": "minor|moderate|significant",
      "confidence": 0.85,
      "alternatives": [
        {
          "text": "替代修改方案1",
          "style": "正式|口语化|文学性",
          "reason": "替代理由"
        },
        {
          "text": "替代修改方案2",
          "style": "正式|口语化|文学性",
          "reason": "替代理由"
        }
      ],
      "tags": ["语法修正", "表达优化"]
    }
    // ... 为每个句子提供建议
  ]
}

**重要说明**：
1. **modifiedText字段**：必须提供具体的修改后文本，这是用户最需要的内容
2. **suggestion字段**：提供修改的建议说明和理由，解释为什么这样修改
3. **严禁模糊表达**：在modifiedText中绝对不能使用"一丝"、"几分"、"些许"等模糊词汇
4. **具体明确**：修改后的文本要比原文更具体、更有力、更清晰
5. **alternatives提供具体的替代修改方案**，不是建议说明
6. **confidence值在0-1之间**，表示修改的可信度
7. **delete/merge类型特殊要求**：
   - **delete类型**：modifiedText必须为空字符串""，不要在suggestion中给解释性文字
   - **merge类型**：modifiedText必须是合并后的完整句子，不要给解释

**merge类型严格约束（必须遵守）**：
当你标记句子为merge类型时，必须严格按照以下模式：

✅ 正确示例：
{
  "sentenceIndex": 0,
  "modificationType": "merge",
  "modifiedText": "他走进房间，看到桌上放着一本书。",
  "suggestion": "将短句合并，提升流畅度"
},
{
  "sentenceIndex": 1,
  "modificationType": "delete",
  "modifiedText": "",
  "suggestion": "已与前句合并"
}

❌ 严禁的错误示例：
{
  "sentenceIndex": 0,
  "modificationType": "merge",
  "modifiedText": "他走进房间，看到桌上放着一本书。"
},
{
  "sentenceIndex": 1,
  "modificationType": "modify",
  "modifiedText": "这样合并更自然",
  "suggestion": "建议合并以提升流畅度"
}

**merge类型验证清单**：
在返回JSON前，请自检：
□ merge类型的下一个句子是否为delete类型？
□ delete类型的modifiedText是否为空字符串""？
□ merge类型的modifiedText是否包含了合并后的完整内容？
如有任何一项不符合，请立即修正。
8. **短句子处理要求**：
   - **主动识别5-6字短句**：如"他走了。"、"很好。"、"是的。"等
   - **优先建议merge**：将短句与相邻句子合并，提升信息密度
   - **避免碎片化**：短句容易造成阅读断断续续
9. **老套表达识别要求**：
   - **识别陈词滥调**：如"如刀锋般"、"像xx一样"、"宛如"、"犹如"
   - **识别过时表达**：如"一丝不苟"、"丝丝入扣"、"恍如隔世"
   - **提供现代替代**：用具体、新颖、有力的表达替换老套用词

10. **情感表达保护要求（核心）**：
   - **识别情感重复**：当原文中的重复表达体现情感状态时，应当保留而非删除
     * 例如："我不能我不能"表达内心挣扎，不要简化为"我不能"
     * 例如："艾斯利我爱你，但是我不能....我不能"体现说话时的情感波动
   - **保护语言节奏**：保留体现真实语言节奏的停顿、重复、省略号等
     * 省略号"..."体现话语停顿或哽咽
     * 逗号停顿体现说话时的犹豫和情感纠结
     * 重复词语体现强烈的情感状态
   - **情感语境判断**：在爱情、亲情、冲突等情感场景中，优先保护表达的真实性
   - **修改策略**：
     * ✅ 可以调整标点符号使表达更清晰："我不能我不能" → "我不能....我不能"
     * ✅ 可以保留重复但优化语法："为了你， 去抛弃他们" → "为了你，去抛弃他们"
     * ❌ 不要删除体现情感的重复表达
     * ❌ 不要过度标准化情感化的语言
11. **必须返回完整的JSON字段结构，确保所有字段都被正确填写**
12. **每个字段都不能为空或undefined**
13. **如果某个字段不适用，请填写合适的默认值**
14. **确保JSON格式正确，可以被正确解析**
15. **只返回JSON格式，不要其他解释**


`;

    // 如果用户提供了特定要求，添加到系统提示中
    if (userRequirements && userRequirements.trim()) {
      systemPrompt += `

【用户特殊要求】
请特别关注以下用户要求：${userRequirements}
在提供修改建议时，请优先考虑这些要求。`;
    }

    systemPrompt += `

**🧠 智能代词检测系统**：
系统具备先进的上下文感知代词检测能力，这是你的重要分析工具：

**核心检测能力**：
- **滑动窗口分析**：自动分析前后3-7句的代词使用模式和分布
- **密度计算**：精确计算人称代词占总词数的比例（密度阈值）
- **连续性分析**：检测连续句子中代词的重复度和使用频率
- **文体感知阈值**：根据对话、叙述、描述、心理描写等文体类型动态调整判断标准
- **修辞手法识别**：自动识别排比、重复等修辞手法，避免误判有意的文学表达

**智能分析结果**：
当你看到句子标记中包含代词分析信息时（如密度、连续性、文体类型等数据），请充分利用这些精确的计算结果：
- 密度值：代词数量与总词数的比例，不同文体有不同的合理阈值
- 连续性值：连续句子中代词重复的程度，反映使用模式
- 文体类型：对话文本允许更多代词，描述文本要求更严格
- 修辞分析：如果检测到排比等修辞手法，应保护而非修改

**使用指导**：
- 优先参考系统提供的智能分析结果，而非简单的关键词匹配
- 根据具体的密度和连续性数值制定精准的修改策略
- 尊重修辞手法分析结果，避免破坏有意的文学表达
- 结合文体类型给出符合语境的优化建议

**🏷️ 句子标记系统说明**：
为了帮助你更好地理解和处理句子，我们为每个句子提供了编号和类型标记：

**标记格式**：【句子N】【类型标记】句子内容
- **句子编号**：【句子1】、【句子2】等，帮助你准确定位句子
- **类型标记**：根据句子内容自动识别的类型标记

**句子类型说明**：
- 【对话句】💬：包含对话、交谈内容的句子
- 【动作句】🏃：描述人物动作、行为的句子
- 【情感句】❤️：表达情感、心理状态的句子
- 【场景句】🏞️：描述环境、场景的句子
- 【转折句】🔄：表示转折、变化的句子
- 【特殊句】⭐：系统提示、游戏元素等特殊句子

**使用指导**：
- 请在返回的JSON中使用准确的sentenceIndex（从0开始）
- 根据句子类型提供针对性的修改建议
- 对话句重点关注语言自然性，动作句关注动作描述的生动性
- 情感句保护情感表达的真实性，场景句增强环境渲染效果
`;

    return systemPrompt;
  }

  /**
   * 添加句子创建功能的独立消息
   * @param messageBuilder 消息构建器
   * @param mode 模式类型，用于定制助手确认消息
   */
  static addSentenceCreationMessages(messageBuilder: MessageBuilder, mode: string = 'standard'): void {
    // 添加句子创建功能的独立消息
    messageBuilder.addUserMessage(`

      对于每个句子，你需要提供两种完全独立的处理方式：

      ## 🔧 第一部分：句子修改处理（基础操作）
      
      **处理类型 (modificationType)**：
      // 基础修改类型（5种核心类型）
      - modify: 修改句子内容
      - delete: 删除句子（冗余或不必要）**【重要】delete类型时，modifiedText必须为空字符串""，不要给解释**（比如，过度的“他”的句子考虑删除后创建别的，方式，减少二元人称的使用）
      - merge: 与下一句合并**【重要】merge类型时，modifiedText应该是合并后的完整句子，不要给解释，将其中一个删去，避免重复说明**
      - enhance: 增强表达效果
      - keep: 保持原样
      
      ## 🎨 第二部分：句子创建功能（完全独立操作）
      
      **句子创建功能 - 与修改处理完全分离，独立判断**：
      
      🚨 **核心原则：句子创建是完全独立的操作**
      - 与modificationType无关，可以同时进行修改和创建
      - 即使句子被标记为"keep"，仍然可以进行创建操作
      - 即使句子被标记为"delete"，仍然可以在删除位置创建新句子
      - 创建操作不受修改类型的任何影响
      
      **创建功能参数**：
      - **hasCreation**: true/false，表示是否有创建操作（独立判断）
      - **createdSentences**: 数组格式，包含要创建的新句子（必须与原文风格一致）
      - **insertPosition**: "before"或"after"，指定插入位置
      - **contentType**: "dialogue"(对话)、"description"(描述)、"action"(动作)、"emotion"(情感)
      - **insertMode**: "single"(单句)或"batch"(批量)
      - **contextHint**: 创建理由和上下文说明（避免学术化表达）
      
      **使用场景**：
      - 当需要增加对话、描述、动作等内容来丰富表达时
      - 当原句子表达完整但可以增加更多细节时
      - 当需要在删除的句子位置添加新内容时
      - 当需要为场景增加氛围或人物塑造时

      
      **积极使用句子创建功能**：
      句子创建是一个强大的功能，应该积极主动地使用，而不是被动等待，但不同要不分场合的一直使用
      以下情况都应该考虑使用：
            -常用规范：
              多使用dialogue，而不要过度的使用 description，emotion
              
      
      
      
      **对话增强场景**：
      - 句子只有简单对话，可以增加对话丰富对话
      - 对话缺少情感色彩，可以增加心理思靠内容活动
      - 对话后没有反应，可以增加听者的回应或环境反应，等具体内容
      
      **场景丰富场景**：
      - 环境描述过于简单，可以增加细节渲染
      - 缺少氛围营造，可以增加感官描述（视觉、听觉、触觉等）
      - 场景转换突兀，可以增加过渡描述
      
      **人物塑造场景**：
      - 人物动作单调，可以增加细节动作和微表情
      - 缺少人物内心活动，可以增加心理描写
      - 人物形象不够立体，可以增加特征描述
      
      **情节推进场景**：
      - 情节发展过快，可以增加铺垫句子
      - 转折过于突兀，可以增加过渡和暗示
      - 高潮部分不够饱满，可以增加细节描写
      
      **重要原则**：
      - 优先创建能增强用户体验的内容
      - 创建的内容要与原文风格保持一致
      - 创建不是必要必须创建，考虑原句子的统一性，如果句子过于统一，那可能是一行规则吟唱，请不要去创建干扰他
      
      **句子创建原则**：
      1. **上下文连贯**：新句子必须与前后文逻辑连贯，符合文本风格
      2. **人物一致**：对话要符合人物性格和当前情境，主要以加入对话为主，而不是过度加入其他的描写方式，对话和台词才是句子创建的核心方法论点
      3. **批量连贯**：批量创建的句子之间要有逻辑关系，形成完整的对话或描述序列
      4. **主动创建**：积极考虑使用hasCreation类型，不要只局限于修改现有句子
      5. **🎯 风格一致性**：创建的句子必须与原文保持相同的语言风格和口语化程度
         - 如果原文是口语化的，创建的句子也要口语化
         - 如果原文有悬念感，创建的句子也要保持这种语气
         - 避免在口语化文本中创建过于正式的句子
         - 保持与原文相同的情感色彩和表达习惯
      
      **句子创建示例**：
      当原句为"他走进房间。"时，可以这样创建新句子：
      
      ## 🔄 两种操作的独立性示例
      
      **示例1：只修改，不创建**
      {
        "sentenceIndex": 1,
        "originalText": "他走进房间。",
        "modificationType": "modify",
        "modifiedText": "他推开门走进房间。",
        "suggestion": "增加动作细节",
      
        // 不进行创建操作
        "hasCreation": false
      }
      
      **示例2：保持原样，但创建新句子**
      {
        "sentenceIndex": 2,
        "originalText": "他走进房间。",
        "modificationType": "keep",
        "suggestion": "此句子表达清晰，无需修改",
      
        // 独立的创建操作
        "hasCreation": true,
        "createdSentences": ["房间里一片寂静。", "他环顾四周，寻找着什么。"],
        "insertPosition": "after",
        "contentType": "description",
        "insertMode": "batch",
        "contextHint": "增加环境描述，营造氛围"
      }
      
      **示例3：既修改又创建（完全独立的两个操作）**
      {
        "sentenceIndex": 3,
        "originalText": "他走进房间。",
        "modificationType": "modify",
        "modifiedText": "他缓缓推开门，走进房间。",
        "suggestion": "增加动作细节和节奏感",
      
        // 同时进行独立的创建操作
        "hasCreation": true,
        "createdSentences": ["你来了。她轻声说道。", "是的，我来了。他回答。"],（句子的添加，不要过于强调氛围的，而是偏向于台词锋利度/颗粒度，而不是过度的去 模板化添加表达方式，）
        "insertPosition": "after",
        "contentType": "dialogue",
        "insertMode": "batch",
        "contextHint": "增加人物对话，丰富情感表达"
      }
      
      🚨 **核心理解**：修改操作和创建操作是两个完全独立的判断过程，不要混淆！
      
      **问题分类 (category)**：
      // 基础问题分类（简化为4种核心类型）
      - grammar: 语法问题:包含前后句子的不连贯性，和她，他 ，等第二人称太多前置优先采取名字 如何，谁如何，而不是，他xx如何
      - style: 风格问题
      - logic: 逻辑问题
      - clarity: 清晰度问题
      
      **严重程度 (severity)**：
      - low: 轻微问题
      - medium: 中等问题
      - high: 严重问题
        -类似于，三句的开头全是 “他” 这样的中性代词，虽然用于描动作等方式很好，但是如果太多了，就应该考虑[角色名]xxx咋了，的修改，而不是过度的“他”
            -具体参考的方式，还是以，6句只有两个他，为主要最大宽中，有了过度的，依此往问题处递增
      
      **影响程度 (impact)**：
      - minor: 轻微影响
      - moderate: 中等影响
      - significant: 显著影响
        -情况：一般出现在，过度正式，比如 xx神色一丝xx, 手掐着指甲，指甲陷进肉里，等过度常用套路化模板客套词
      
`, undefined, true, true);

    // 根据模式定制助手确认消息
    let confirmMessage = '我已理解句子创建功能，这是一个独立于修改建议的功能，用于增加新内容来丰富表达。我会积极使用这个功能来提升文本的表现力。';

    if (mode === 'coherence') {
      confirmMessage = '我已理解句子创建功能，这是一个独立于修改建议的功能，用于增加新内容来丰富表达。在连贯模式下，我会特别注意创建的内容与前后段落的连贯性。';
    } else if (mode === 'fulltext') {
      confirmMessage = '我已理解句子创建功能，这是一个独立于修改建议的功能，用于增加新内容来丰富表达。在全文模式下，我会考虑整体文档的连贯性来创建内容。';
    }

    // 助手确认理解句子创建功能
    messageBuilder.addAssistantMessage(confirmMessage, true, true);

    // 用户消息：要求使用句子创建功能
    let requestMessage = '请在分析每个句子时，积极考虑使用句子创建功能来丰富内容表达。';

    if (mode === 'coherence') {
      requestMessage = '请在分析每个句子时，积极考虑使用句子创建功能来丰富内容表达，同时保持与前后段落的连贯性。使用这两种，并避免出现列举中的不好的例子';
    } else if (mode === 'fulltext') {
      requestMessage = '请在分析每个句子时，积极考虑使用句子创建功能来丰富内容表达，同时考虑整体文档的连贯性。使用这两种，并避免出现列举中的不好的例子';
    }

    messageBuilder.addUserMessage(requestMessage, undefined, true, true);
  }

  /**
   * 获取统一的JSON格式要求
   * @returns JSON格式要求字符串
   */
  static getUnifiedJSONFormat(): string {
    return `






    请按照以下增强的JSON格式返回所有句子的分析结果：
对于小说的识别要灵敏，比如
【】 这样的很明显有概率是 系统提示，就不要当句子修改，而是美化排版效果，系统【】 保持原样即可，只美化



【奇迹发生!你的绿毛虫觉醒了隐藏特性:复眼!】
【奇迹再次发生!你的绿毛虫领悟了稀有技能:念力(虫系变异)!】
比如这样的，就不要合并成
“奇迹降临！你的绿毛虫不仅觉醒了隐藏特性‘复眼’，更领悟了稀有技能‘念力（虫系变异）’！”
这样的，再不修改【】的前提下，美化里面的显示，更好的制作爽感


`;
  }

  /**
   * 构建段落注释提示
   * @param segment 文本段落
   * @param userRequirements 用户要求
   * @returns 消息数组
   */
  static buildSegmentPrompt(
    segment: TextSegment,
    userRequirements?: string
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 使用统一的系统提示词
    const systemPrompt = this.getUnifiedSystemPrompt(userRequirements);
    messageBuilder.addSystemMessage(systemPrompt);

    // 助手消息：确认理解任务
    messageBuilder.addAssistantMessage('我将为您分析这个段落中的每个句子，提供实用的修改建议。', true, true);

    // 用户消息：段落概览
    messageBuilder.addUserMessage(`【段落分析】
段落内容：${segment.content}
句子总数：${segment.sentences.length}个

我将为每个句子单独发送信息：`, undefined, true, true);

    // 助手消息：确认收到段落概览
    messageBuilder.addAssistantMessage('我已理解段落概览，准备接收每个句子的详细信息。', true, true);

    // 分条发送每个句子信息（带标记）
    segment.sentences.forEach((sentence, index) => {
      let markedText: string;
      let markerInfo = '';

      try {
        // 防御性编程：检查SentenceMarker是否正确加载
        if (SentenceMarker && typeof SentenceMarker.generateMarkedText === 'function') {
          // 修复：正确传递参数用于上下文分析 - index是显示用的序号，index也是在sentences数组中的实际索引
          markedText = SentenceMarker.generateMarkedText(sentence.text, index, segment.sentences, index);

          // 获取句子的主要标记信息
          if (typeof SentenceMarker.getPrimaryMarker === 'function') {
            const primaryMarker = SentenceMarker.getPrimaryMarker(sentence.text);
            markerInfo = primaryMarker ? `，识别为${primaryMarker.name}` : '';
          }
        } else {
          console.warn('⚠️ SentenceMarker未正确加载，使用降级方案');
          markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
        }
      } catch (error) {
        console.error('❌ 生成标记文本失败:', error);
        markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
      }

      messageBuilder.addUserMessage(markedText, undefined, true, true);
      messageBuilder.addAssistantMessage(`我已理解第${index + 1}个句子的内容${markerInfo}。`, true, true);
    });

    // 添加句子创建功能的独立消息
    this.addSentenceCreationMessages(messageBuilder, 'standard');

    // 最终用户消息：要求生成建议
    messageBuilder.addUserMessage(`现在请为这个段落的所有${segment.sentences.length}个句子提供修改建议。

${this.getUnifiedJSONFormat()}`);

    return messageBuilder.build();
  }

  /**
   * 构建连贯模式提示
   * @param currentSegment 当前段落
   * @param allSegments 所有段落
   * @param currentIndex 当前段落索引
   * @param userRequirements 用户要求
   * @returns 消息数组
   */
  static buildCoherencePrompt(
    currentSegment: TextSegment,
    allSegments: TextSegment[],
    currentIndex: number,
    userRequirements?: string
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 使用统一的系统提示词，并添加连贯模式说明
    const systemPrompt = this.getUnifiedSystemPrompt(userRequirements) + `

**🌍 段落连贯模式特别说明**：
你现在处于段落连贯模式，这意味着：
1. 你能看到完整的文档结构和所有段落内容
2. 你需要专注处理第${currentIndex + 1}段，但要考虑与前后段落的连贯性
3. 修改建议要确保与整体文档的风格和逻辑保持一致
4. 特别注意段落间的过渡和衔接`;

    messageBuilder.addSystemMessage(systemPrompt);

    // 助手消息：确认理解连贯模式任务
    messageBuilder.addAssistantMessage(`我将在段落连贯模式下为您分析文档。我已理解需要看完整文档但专注处理第${currentIndex + 1}段，确保与前后段落的连贯性。`, true, true);

    // 用户消息：提供完整文档概览
    messageBuilder.addUserMessage(`【文档概览 - 段落连贯模式】
总段落数：${allSegments.length}个
当前处理：第${currentIndex + 1}段
总句子数：${allSegments.reduce((sum, seg) => sum + seg.sentences.length, 0)}个

以下是完整文档的所有段落：`, undefined, true, true);

    // 分条发送所有段落信息
    allSegments.forEach((segment, index) => {
      const isCurrentSegment = index === currentIndex;
      const prefix = isCurrentSegment ? '【当前处理段落】' : `【段落 ${index + 1}】`;

      messageBuilder.addUserMessage(`${prefix}
内容：${segment.content}
句子数：${segment.sentences.length}个
${isCurrentSegment ? '⭐ 请专注处理此段落' : ''}`, undefined, true, true);

      messageBuilder.addAssistantMessage(`我已理解${isCurrentSegment ? '当前处理的' : ''}第${index + 1}段的内容${isCurrentSegment ? '，将为其提供实用的修改建议' : ''}。`, true, true);
    });

    // 添加句子创建功能的独立消息
    this.addSentenceCreationMessages(messageBuilder, 'coherence');

    // 专注处理当前段落的指令
    messageBuilder.addUserMessage(`现在请专注处理【第${currentIndex + 1}段】，为其中的每个句子提供修改建议。

【当前段落详细信息】
段落内容：${currentSegment.content}
句子总数：${currentSegment.sentences.length}个

句子列表（带标记）：
${currentSegment.sentences.map((sentence, index) => {
  let markedText: string;
  try {
    // 防御性编程：检查SentenceMarker是否正确加载
    if (SentenceMarker && typeof SentenceMarker.generateMarkedText === 'function') {
      // 修复：正确传递当前句子在段落中的索引，用于修辞分析
      markedText = SentenceMarker.generateMarkedText(sentence.text, index, currentSegment.sentences, index);
    } else {
      console.warn('⚠️ SentenceMarker未正确加载，使用降级方案');
      markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
    }
  } catch (error) {
    console.error('❌ 生成标记文本失败:', error);
    markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
  }
  return `${index + 1}. ${markedText}`;
}).join('\n\n')}

**连贯模式特别要求**：
1. 考虑与前后段落的逻辑连接和风格一致性
2. 确保修改后的内容与整体文档协调
3. 在reason字段中说明如何保持连贯性
4. 只返回JSON格式，不要其他解释`);

    // 添加统一的JSON格式要求
    messageBuilder.addUserMessage(this.getUnifiedJSONFormat());

    return messageBuilder.build();
  }

  /**
   * 构建全文处理提示（段落式确认模式）
   * @param segments 文本段落数组
   * @param userRequirements 用户要求
   * @param currentSegmentIndex 当前处理的段落索引（可选，用于分段处理）
   * @returns 消息数组
   */
  static buildFullTextPrompt(
    segments: TextSegment[],
    userRequirements?: string,
    currentSegmentIndex?: number
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 使用统一的系统提示词，并添加全文段落式确认说明
    const systemPrompt = this.getUnifiedSystemPrompt(userRequirements) + `

**🌍 全文段落式确认处理模式特别说明**：
你现在处于全文段落式确认处理模式，这意味着：
1. 你能看到完整的文档结构和所有段落概览
2. 你需要专注处理指定的单个段落，但要考虑整体文档的连贯性
3. 修改建议要确保与整体文档的风格和逻辑保持一致
4. 特别注意与前后段落的过渡和衔接
5. 每次只处理一个段落，确保专注性和质量`;

    messageBuilder.addSystemMessage(systemPrompt);

    // 助手消息：确认理解段落式确认任务
    messageBuilder.addAssistantMessage('我将为您进行全文段落式确认处理，先了解完整文档结构，然后专注处理指定段落。', true, true);

    // 用户消息：提供完整文档概览
    messageBuilder.addUserMessage(`【全文档概览 - 段落式确认模式】
总段落数：${segments.length}个
总句子数：${segments.reduce((sum, seg) => sum + seg.sentences.length, 0)}个
${currentSegmentIndex !== undefined ? `当前处理：第${currentSegmentIndex + 1}段` : ''}

以下是完整文档的所有段落概览：`, undefined, true, true);

    // 分条发送所有段落概览信息
    segments.forEach((segment, index) => {
      const isCurrentSegment = currentSegmentIndex !== undefined && index === currentSegmentIndex;
      const prefix = isCurrentSegment ? '【当前处理段落】' : `【段落 ${index + 1}】`;

      messageBuilder.addUserMessage(`${prefix}
内容：${segment.content}
句子数：${segment.sentences.length}个
${isCurrentSegment ? '⭐ 请专注处理此段落' : ''}`, undefined, true, true);

      messageBuilder.addAssistantMessage(`我已理解${isCurrentSegment ? '当前处理的' : ''}第${index + 1}段的内容${isCurrentSegment ? '，将为其提供实用的修改建议' : ''}。`, true, true);
    });

    // 如果指定了当前段落，则专注处理该段落
    if (currentSegmentIndex !== undefined && currentSegmentIndex < segments.length) {
      const currentSegment = segments[currentSegmentIndex];

      messageBuilder.addUserMessage(`现在请专注处理【第${currentSegmentIndex + 1}段】，为其中的每个句子提供修改建议。

【当前段落详细信息】
段落内容：${currentSegment.content}
句子总数：${currentSegment.sentences.length}个

我将为每个句子单独发送信息：`, undefined, true, true);

      // 分条发送当前段落的每个句子信息（带标记）
      currentSegment.sentences.forEach((sentence, index) => {
        let markedText: string;
        let markerInfo = '';

        try {
          // 防御性编程：检查SentenceMarker是否正确加载
          if (SentenceMarker && typeof SentenceMarker.generateMarkedText === 'function') {
            // 修复：正确传递参数用于上下文分析 - index是显示用的序号，index也是在sentences数组中的实际索引
            markedText = SentenceMarker.generateMarkedText(sentence.text, index, currentSegment.sentences, index);

            // 获取句子的主要标记信息
            if (typeof SentenceMarker.getPrimaryMarker === 'function') {
              const primaryMarker = SentenceMarker.getPrimaryMarker(sentence.text);
              markerInfo = primaryMarker ? `，识别为${primaryMarker.name}` : '';
            }
          } else {
            console.warn('⚠️ SentenceMarker未正确加载，使用降级方案');
            markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
          }
        } catch (error) {
          console.error('❌ 生成标记文本失败:', error);
          markedText = `这是第 ${index + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
        }

        messageBuilder.addUserMessage(markedText, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已理解第${index + 1}个句子的内容${markerInfo}。`, true, true);
      });

      // 添加句子创建功能的独立消息
      this.addSentenceCreationMessages(messageBuilder, 'fulltext');

      messageBuilder.addUserMessage(`**段落式确认模式特别要求**：
1. 考虑与前后段落的逻辑连接和风格一致性
2. 确保修改后的内容与整体文档协调
3. 在reason字段中说明如何保持连贯性
4. 只返回当前段落的${currentSegment.sentences.length}个句子建议，不要处理其他段落

现在请为这个段落的所有句子提供修改建议。`);

    } else {
      // 如果没有指定段落，则处理所有段落（保持向后兼容）
      let globalSentenceIndex = 0;
      segments.forEach((segment, segmentIndex) => {
        messageBuilder.addUserMessage(`【段落 ${segmentIndex + 1} 详细信息】
内容：${segment.content}
句子数：${segment.sentences.length}个`, undefined, true, true);

        segment.sentences.forEach((sentence, localIndex) => {
          let markedText: string;

          try {
            // 防御性编程：检查SentenceMarker是否正确加载
            if (SentenceMarker && typeof SentenceMarker.generateMarkedText === 'function') {
              // 修复：正确传递参数用于上下文分析 - globalSentenceIndex是显示用的序号，localIndex是在segment.sentences数组中的实际索引
              markedText = SentenceMarker.generateMarkedText(sentence.text, globalSentenceIndex, segment.sentences, localIndex);
            } else {
              console.warn('⚠️ SentenceMarker未正确加载，使用降级方案');
              markedText = `这是第 ${globalSentenceIndex + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
            }
          } catch (error) {
            console.error('❌ 生成标记文本失败:', error);
            markedText = `这是第 ${globalSentenceIndex + 1} 句话\n以下是一些特征：\n1. 普通句子 📝 - 基础句子处理\n\n原文句子：${sentence.text}`;
          }

          messageBuilder.addUserMessage(`\n\n${markedText}`, undefined, true, true);
          globalSentenceIndex++;
        });
      });

      const totalSentences = segments.reduce((sum, seg) => sum + seg.sentences.length, 0);
      messageBuilder.addUserMessage(`为整个文档的所有句子（共${totalSentences}个句子）提供修改建议。`);
    }

    // 添加统一的JSON格式要求
    messageBuilder.addUserMessage(this.getUnifiedJSONFormat());

    return messageBuilder.build();
  }
}
