/**
 * 书名生成提示词构建器
 * 统一管理书名生成相关的提示词构建逻辑
 */

import { TitleGenerationParams, TitleFramework } from '../types/BrainstormTypes';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';

export class TitlePromptBuilder {
  /**
   * 获取书名生成系统提示词
   */
  static getSystemPrompt(userRequirements?: string): string {
    let systemPrompt = `你是一位资深的网文飞卢/番茄专家，深谙网络小说生态和读者心理。你精通各大网文类型的命名规律，能够根据不同类型创作出既符合类型特征又具有独特吸引力的书名。

**你的专业背景**：
- 10年网文行业从业经验，见证了网文发展的各个阶段
- 深度研究过起点、晋江、纵横等各大平台的爆款作品
- 熟悉玄幻、都市、系统、历史、科幻等所有主流网文类型
- 理解网文读者的阅读习惯和心理需求

**你的核心能力**：
1. **类型识别精准**：能准确判断小说类型并运用对应的命名规律
2. **爽点预告精确**：深知各类型的核心爽点并能在书名中体现
3. **差异化定位**：在同类型中创造独特的卖点和记忆点
4. **读者心理把握**：理解不同类型读者的期待和偏好
5. **搜索优化意识**：熟悉网文平台的搜索和推荐机制

**网文书名创作原则**：
1. **功能性第一**：书名必须快速传达类型和核心卖点
2. **直白表达**：避免过于文艺或含蓄的表达方式
3. **类型标识**：必须包含明确的类型识别元素
4. **爽点预期**：要让读者从书名就能预期到阅读快感
5. **差异化突出**：在同类型中要有独特的吸引点
6. **搜索友好**：包含读者常搜索的热门关键词
7. **关键词精神**：深度理解关键词的内在含义、情感色彩和文化内涵，将其转化为符合网文特色的表达方式。关键词是激发创意的种子，让它在网文的土壤中生根发芽，开出符合类型特色的花朵

**现代网文书名创作规律**：

**开局流玄幻类**：
- 开局爆点设计：开篇就有震撼性事件，如"开局拐跑家族帝兵"
- 权威反应营造：通过权威人物的强烈反应体现主角行为的震撼性
- 冒号分割结构：前半部分是设定或行为，后半部分是效果或反应
- 反差冲突制造：打破常规认知，如老祖这样的权威人物"急眼了"
- 现代参考：《开局拐跑家族帝兵，老祖急眼了》《长生皇子：开局无敌，看王朝起落》

**挂机修仙类**：
- 时间跨度强调：突出长时间的积累，如"挂机百年"
- 出世震撼效果：强调厚积薄发的爆发力，如"出世已是剑仙"
- 背景世界设定：明确的修仙世界背景，如"蜀山"
- 反差对比营造：从默默无闻到惊世骇俗的巨大反差
- 现代参考：《蜀山：挂机百年，出世已是剑仙》《洪荒：闭关万年，出关吓坏圣人》

**洪荒反转类**：
- 身份设定颠覆：选择非主流身份，如"刚成尸祖"
- 任务冲突设计：给主角安排看似不可能的任务
- 权威人物震惊：通过圣人级别人物的反应体现震撼
- 网络用语运用：使用"人麻了"等现代网络表达
- 现代参考：《洪荒：刚成尸祖，你让我骂醒女娲》《洪荒：我巫族不争霸，鸿钧人麻了》

**穿书性转类**：
- 穿书设定明确：直接标明穿书题材
- 性别身份颠覆：女性角色拥有传统男性权力
- 实力展示具体：用具体数字体现实力，如"拥兵百万"
- 态度反转爽点：拒绝传统剧情发展，如"让我道歉？"
- 现代参考：《穿书女频，拥兵百万，让我道歉？》《穿成恶毒女配，手握三军，凭什么下跪？》

**网文书名评分标准**（1-10分）：
- 类型识别度（40%）：读者能否在3秒内识别小说类型
- 爽点预期度（30%）：是否激发了正确的爽点期待
- 差异化程度（20%）：在同类型中是否有独特吸引力
- 搜索友好度（10%）：是否包含热门搜索关键词`;

    if (userRequirements) {
      systemPrompt += `\n\n**用户特殊要求**：\n${userRequirements}`;
    }

    return systemPrompt;
  }

  /**
   * 构建书名生成提示
   */
  static buildTitleGenerationPrompt(params: TitleGenerationParams): any[] {
    const messageBuilder = new MessageBuilder();

    // 系统消息
    messageBuilder.addSystemMessage(this.getSystemPrompt(params.userRequirements));

    // 助手消息：确认理解任务
    messageBuilder.addAssistantMessage(
      '我将根据您提供的关键词和框架模式，为您创作5个优质的书名。每个书名都会包含评分和创作理由。',
      true,
      true
    );

    // 构建生成指令
    let prompt = `请生成5个书名，要求在15字以内，但不要低于13个字。`;

    // 添加关键词信息
    if (params.keywords.length > 0) {
      messageBuilder.addUserMessage(
        `【关键词灵感源泉】\n以下关键词作为创作的灵感源泉和方向指引，请深度理解其内在含义、情感色彩和文化内涵，通过意境、氛围、概念来体现，而非直接使用字词本身：\n${params.keywords.join('、')}\n\n请将这些关键词理解为创作的范围和位置，激发出既符合其精神又具有独特表达的书名。`,
        undefined,
        true,
        true
      );
      messageBuilder.addAssistantMessage(
        '我已深度理解这些关键词的内在含义和情感色彩，将以其为灵感源泉，通过意境和氛围来体现其精神，创作出独特而富有内涵的书名。',
        true,
        true
      );
      prompt += `\n关键词灵感：${params.keywords.join('、')}（请理解其精神内涵，而非直接使用）`;
    }

    // 添加收藏书名参考信息
    if (params.userRequirements && params.userRequirements.includes('参考书名')) {
      const referenceBooks = this.extractReferenceBooks(params.userRequirements);
      if (referenceBooks.length > 0) {
        messageBuilder.addUserMessage(
          `【参考书名案例】\n以下是用户收藏的优质书名案例，请深度学习其风格特征、命名规律和创意表达：\n\n${referenceBooks.join('\n')}\n\n请分析这些案例的共同特点和成功要素，在生成新书名时融入相似的创意思路和表达方式，但要避免直接模仿，而是要创造出具有相似风格但内容全新的书名。`,
          undefined,
          true,
          true
        );
        messageBuilder.addAssistantMessage(
          '我已仔细分析这些参考书名的风格特征和创意表达，将在生成新书名时借鉴其成功要素和命名规律，确保生成的书名符合用户偏好且具有创新性。',
          true,
          true
        );
        prompt += `\n参考案例：已分析用户收藏的${referenceBooks.length}个优质书名案例`;
      }
    }

    // 添加框架信息
    const selectedFramework = params.framework;
    const customFramework = params.customFramework;

    if (selectedFramework) {
      // 直接从框架对象获取参考书名（修复判断逻辑错误）
      const frameworkReferenceBooks = (selectedFramework as any).referenceBooks || [];
      const referenceTitles = frameworkReferenceBooks.map((book: any) => book.title);

      // 同时检查userRequirements中的额外参考书名
      const userRequirementTitles = params.userRequirements && params.userRequirements.includes('参考书名')
        ? this.extractReferenceBooks(params.userRequirements)
        : [];

      // 合并去重所有参考书名
      const allReferenceTitles = [...new Set([...referenceTitles, ...userRequirementTitles])];

      console.log('🔍 TitlePromptBuilder 参考书名检查:', {
        frameworkId: selectedFramework.id,
        frameworkName: selectedFramework.name,
        frameworkReferenceCount: frameworkReferenceBooks.length,
        userRequirementCount: userRequirementTitles.length,
        totalReferenceCount: allReferenceTitles.length,
        referenceTitles: allReferenceTitles
      });

      let frameworkMessage = `【框架模式】\n框架名称：${selectedFramework.name}\n模式结构：${selectedFramework.pattern}\n框架说明：${selectedFramework.description}\n参考示例：${selectedFramework.examples.join('、')}`;

      // 如果有参考书名，在框架消息中加入参考书名信息
      if (allReferenceTitles.length > 0) {
        frameworkMessage += `\n\n【框架参考书名】\n以下是与此框架相关的收藏书名案例，请在使用框架时参考其风格特征：\n${allReferenceTitles.join('\n')}\n\n请结合框架模式和参考书名的风格特征进行创作，确保生成的书名既符合框架结构又体现参考案例的优秀特质。`;
      }

      messageBuilder.addUserMessage(
        frameworkMessage,
        undefined,
        true,
        true
      );

      let assistantResponse = `我已理解"${selectedFramework.name}"框架模式，将基于此结构进行创作。`;
      if (allReferenceTitles.length > 0) {
        assistantResponse += `同时我已分析了${allReferenceTitles.length}个参考书名案例的风格特征，将在创作中融入其优秀特质。`;
      }

      messageBuilder.addAssistantMessage(
        assistantResponse,
        true,
        true
      );

      prompt += `\n参考框架：${selectedFramework.pattern}`;
      prompt += `\n框架说明：${selectedFramework.description}`;
      prompt += `\n参考示例：${selectedFramework.examples.join('、')}`;
      if (allReferenceTitles.length > 0) {
        prompt += `\n框架参考书名：已分析${allReferenceTitles.length}个相关收藏书名案例`;
      }
    } else if (customFramework) {
      // 自定义框架时只从userRequirements获取参考书名（因为没有框架对象）
      const userRequirementTitles = params.userRequirements && params.userRequirements.includes('参考书名')
        ? this.extractReferenceBooks(params.userRequirements)
        : [];

      console.log('🔍 TitlePromptBuilder 自定义框架参考书名检查:', {
        customFramework,
        userRequirementCount: userRequirementTitles.length,
        referenceTitles: userRequirementTitles
      });

      let customFrameworkMessage = `【自定义框架】\n框架模式：${customFramework}\n请基于此自定义框架进行创作。`;

      // 如果有参考书名，在自定义框架消息中也加入参考书名信息
      if (userRequirementTitles.length > 0) {
        customFrameworkMessage += `\n\n【框架参考书名】\n以下是收藏的优质书名案例，请在使用自定义框架时参考其风格特征：\n${userRequirementTitles.join('\n')}\n\n请结合自定义框架和参考书名的风格特征进行创作。`;
      }

      messageBuilder.addUserMessage(
        customFrameworkMessage,
        undefined,
        true,
        true
      );

      let assistantResponse = '我已理解您的自定义框架模式，将据此进行创作。';
      if (userRequirementTitles.length > 0) {
        assistantResponse += `同时我已分析了${userRequirementTitles.length}个参考书名案例的风格特征，将在创作中融入其优秀特质。`;
      }

      messageBuilder.addAssistantMessage(
        assistantResponse,
        true,
        true
      );

      prompt += `\n自定义框架：${customFramework}`;
      if (userRequirementTitles.length > 0) {
        prompt += `\n框架参考书名：已分析${userRequirementTitles.length}个相关收藏书名案例`;
      }
    }

    // 最终生成指令
    messageBuilder.addUserMessage(prompt);

    // JSON格式要求
    messageBuilder.addUserMessage(this.getJSONFormatRequirement());

    return messageBuilder.build();
  }

  /**
   * 获取JSON格式要求
   */
  static getJSONFormatRequirement(): string {
    return `请严格按照以下JSON格式返回结果：

{
  "titles": [
    {
      "title": "书名文本",
      "score": 8.5,
      "reason": "创作理由和亮点说明",
      "keywords": ["提取的关键词1", "提取的关键词2"],
      "framework": "使用的框架模式"
    }
  ]
}

**格式要求**：
1. 必须返回5个书名
2. score为1-10的数字，保留一位小数
3. reason要说明创作思路和亮点
4. keywords要从书名中提取出的关键词
5. framework要说明使用的框架模式
6. 只返回JSON，不要其他解释`;
  }

  /**
   * 从用户要求中提取参考书名
   */
  static extractReferenceBooks(userRequirements: string): string[] {
    const referenceBooks: string[] = [];

    // 查找参考书名部分
    const referenceBooksMatch = userRequirements.match(/参考书名案例：\n([\s\S]*?)(?=\n\n|$)/);
    if (referenceBooksMatch) {
      const booksText = referenceBooksMatch[1];

      // 提取书名列表
      const bookLines = booksText.split('\n').filter(line => line.trim());
      bookLines.forEach(line => {
        // 匹配格式：- 《书名》 (来源标识)
        const bookMatch = line.match(/[-•]\s*《([^》]+)》/);
        if (bookMatch) {
          referenceBooks.push(`《${bookMatch[1]}》`);
        } else {
          // 匹配其他可能的格式
          const simpleMatch = line.match(/[-•]\s*(.+?)(?:\s*\(|$)/);
          if (simpleMatch && simpleMatch[1].trim()) {
            const bookTitle = simpleMatch[1].trim();
            // 如果不是以《》包围，自动添加
            if (!bookTitle.startsWith('《')) {
              referenceBooks.push(`《${bookTitle}》`);
            } else {
              referenceBooks.push(bookTitle);
            }
          }
        }
      });
    }

    return referenceBooks;
  }

  /**
   * 构建关键词提取提示（用于分析历史书名）
   */
  static buildKeywordExtractionPrompt(titles: string[]): any[] {
    const messageBuilder = new MessageBuilder();

    messageBuilder.addSystemMessage(
      '你是专业的文本分析专家，擅长从书名中提取关键词和分析框架模式。'
    );

    messageBuilder.addUserMessage(
      `请分析以下书名，提取其中的关键词和框架模式：\n\n${titles.join('\n')}`
    );

    messageBuilder.addUserMessage(`请按照以下JSON格式返回分析结果：

{
  "analysis": [
    {
      "title": "书名",
      "keywords": ["关键词1", "关键词2"],
      "framework": "框架模式描述",
      "category": "人物/地点/概念/情感/动作/物品"
    }
  ]
}

只返回JSON，不要其他解释。`);

    return messageBuilder.build();
  }
}
