"use client";

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useThinkingCanvas } from '@/contexts/ThinkingCanvasContext';
import { ThinkingCanvas } from '@/types/thinking-canvas';

// 搜索输入组件
const SearchInput: React.FC<{
  value: string;
  onChange: (value: string) => void;
}> = ({ value, onChange }) => {
  return (
    <div className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="搜索画布..."
        className="w-full px-3 py-2 pl-9 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
      />
      <div className="absolute left-3 top-2.5 text-gray-400">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
    </div>
  );
};

// 创建画布按钮
const CreateCanvasButton: React.FC = () => {
  const { createCanvas } = useThinkingCanvas();
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async () => {
    setIsCreating(true);

    // 简单的标题输入
    const title = prompt('请输入画布标题：');
    if (title && title.trim()) {
      createCanvas(title.trim());
    }

    setIsCreating(false);
  };

  return (
    <motion.button
      onClick={handleCreate}
      disabled={isCreating}
      className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2.5 px-4 rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {isCreating ? (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      ) : (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          <span>新建画布</span>
        </>
      )}
    </motion.button>
  );
};

// 画布列表项组件
const CanvasListItem: React.FC<{
  canvas: ThinkingCanvas;
  isActive: boolean;
  onSelect: () => void;
  onDelete: () => void;
}> = ({ canvas, isActive, onSelect, onDelete }) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (showDeleteConfirm) {
      onDelete();
      setShowDeleteConfirm(false);
    } else {
      setShowDeleteConfirm(true);
      // 3秒后自动取消确认状态
      setTimeout(() => setShowDeleteConfirm(false), 3000);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);

    // 确保date是有效的Date对象
    if (isNaN(date.getTime())) return '无效日期';

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <motion.div
      className={`p-3 border-b border-gray-100 cursor-pointer transition-all duration-200 ${
        isActive
          ? 'bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200'
          : 'hover:bg-gray-50'
      }`}
      onClick={onSelect}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
      layout
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h3 className={`font-medium text-sm truncate ${
            isActive ? 'text-purple-900' : 'text-gray-900'
          }`}>
            {canvas.title}
          </h3>

          <div className="flex items-center space-x-2 mt-1">
            <p className="text-xs text-gray-500">
              {formatDate(canvas.updatedAt)}
            </p>

            {canvas.metadata.wordCount > 0 && (
              <>
                <span className="text-xs text-gray-300">•</span>
                <p className="text-xs text-gray-500">
                  {canvas.metadata.wordCount} 字
                </p>
              </>
            )}

            {canvas.metadata.isStarred && (
              <>
                <span className="text-xs text-gray-300">•</span>
                <span className="text-xs text-yellow-500">⭐</span>
              </>
            )}
          </div>

          {canvas.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {canvas.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded"
                >
                  {tag}
                </span>
              ))}
              {canvas.tags.length > 2 && (
                <span className="text-xs text-gray-400">+{canvas.tags.length - 2}</span>
              )}
            </div>
          )}
        </div>

        <div className="ml-2 flex items-center space-x-1">
          {/* 收藏按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              // TODO: 实现收藏功能
            }}
            className={`p-1 rounded hover:bg-gray-200 transition-colors ${
              canvas.metadata.isStarred ? 'text-yellow-500' : 'text-gray-400'
            }`}
          >
            <svg className="w-3 h-3" fill={canvas.metadata.isStarred ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
          </button>

          {/* 删除按钮 */}
          <button
            onClick={handleDelete}
            className={`p-1 rounded transition-colors ${
              showDeleteConfirm
                ? 'text-red-600 bg-red-100 hover:bg-red-200'
                : 'text-gray-400 hover:text-red-500 hover:bg-gray-200'
            }`}
            title={showDeleteConfirm ? '确认删除' : '删除画布'}
          >
            {showDeleteConfirm ? (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            )}
          </button>
        </div>
      </div>
    </motion.div>
  );
};

// 主侧边栏组件
const CanvasSidebar: React.FC = () => {
  const {
    canvases,
    currentCanvas,
    searchQuery,
    sortBy,
    sidebarCollapsed,
    setSearchQuery,
    setSortBy,
    switchCanvas,
    deleteCanvas,
    toggleSidebar
  } = useThinkingCanvas();

  // 过滤和排序画布
  const filteredAndSortedCanvases = useMemo(() => {
    let filtered = canvases;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = canvases.filter(canvas =>
        canvas.title.toLowerCase().includes(query) ||
        canvas.content.toLowerCase().includes(query) ||
        canvas.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 排序
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'updated':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
    });
  }, [canvases, searchQuery, sortBy]);

  if (sidebarCollapsed) {
    return (
      <motion.div
        className="w-12 bg-gray-50 border-r border-gray-200 flex flex-col items-center py-4"
        initial={{ width: 320 }}
        animate={{ width: 48 }}
        transition={{ duration: 0.3 }}
      >
        <button
          onClick={toggleSidebar}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col"
      initial={{ width: 48 }}
      animate={{ width: 320 }}
      transition={{ duration: 0.3 }}
    >
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="font-semibold text-gray-900">思考画布</h2>
          <button
            onClick={toggleSidebar}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        </div>

        <CreateCanvasButton />
      </div>

      {/* 搜索和排序 */}
      <div className="p-4 border-b border-gray-200 space-y-3">
        <SearchInput value={searchQuery} onChange={setSearchQuery} />

        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">排序:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'date' | 'name' | 'updated')}
            className="text-xs border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-purple-500"
          >
            <option value="updated">最近更新</option>
            <option value="date">创建时间</option>
            <option value="name">名称</option>
          </select>
        </div>
      </div>

      {/* 画布列表 */}
      <div className="flex-1 overflow-y-auto">
        <AnimatePresence>
          {filteredAndSortedCanvases.length > 0 ? (
            filteredAndSortedCanvases.map(canvas => (
              <CanvasListItem
                key={canvas.id}
                canvas={canvas}
                isActive={currentCanvas?.id === canvas.id}
                onSelect={() => switchCanvas(canvas.id)}
                onDelete={() => deleteCanvas(canvas.id)}
              />
            ))
          ) : (
            <motion.div
              className="p-8 text-center text-gray-500"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm">
                {searchQuery ? '没有找到匹配的画布' : '还没有画布，点击上方按钮创建第一个'}
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 底部统计 */}
      {canvases.length > 0 && (
        <div className="p-3 border-t border-gray-200 text-xs text-gray-500 text-center">
          共 {canvases.length} 个画布
          {searchQuery && filteredAndSortedCanvases.length !== canvases.length && (
            <span> • 显示 {filteredAndSortedCanvases.length} 个</span>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default CanvasSidebar;
