"use client";

/**
 * 小说上下文构建器
 * 负责构建完整、准确的续写上下文信息
 */

export interface Chapter {
  id: string;
  title: string;
  order: number;
  content: string;
}

export interface CurrentPosition {
  cursorPosition: number;
  beforeText: string;
  afterText: string;
  currentParagraph: number;
  totalParagraphs: number;
  beforeParagraphs: number;
  afterParagraphs: number;
}

export interface ContextInfo {
  currentChapter: Chapter;
  currentPosition: CurrentPosition;
  beforeChapters: Chapter[];
  afterChapters: Chapter[];
}

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export class NovelContextBuilder {
  constructor(
    private chapters: Chapter[],
    private currentChapter: Chapter,
    private cursorPosition: number
  ) {}

  /**
   * 构建完整上下文信息
   */
  buildFullContext(): ContextInfo {
    const currentPosition = this.calculateCurrentPosition();
    const beforeChapters = this.getBeforeChapters();
    const afterChapters = this.getAfterChapters();
    
    return {
      currentChapter: this.currentChapter,
      currentPosition,
      beforeChapters,
      afterChapters
    };
  }

  /**
   * 构建上下文消息数组（支持十句一分段）
   */
  buildContextMessages(): ConversationMessage[] {
    const context = this.buildFullContext();
    const messages: ConversationMessage[] = [];

  // ... (您的其他代码)

    // 判断写作模式 (导演版)
    const isEmptyChapter = context.currentChapter.content.length === 0;
    const hasBeforeChapters = context.beforeChapters.length > 0;
    const hasAfterChapters = context.afterChapters.length > 0;
    const hasBeforeText = context.currentPosition.beforeText.length > 0;
    const hasAfterText = context.currentPosition.afterText.length > 0;

    let shootingMode = ''; // 影视化术语替换
    if (isEmptyChapter && hasBeforeChapters && hasAfterChapters) {
      shootingMode = '补拍独立场次 (空章节)';
    } else if (hasBeforeText && hasAfterText) {
      shootingMode = '补拍转场镜头 (章节内)';
    } else if (hasBeforeText && !hasAfterText) {
      shootingMode = '顺拍新场景 (章节末尾)';
    } else if (!hasBeforeText && hasAfterText) {
      shootingMode = '拍摄开场戏 (章节开头)';
    } else {
      shootingMode = '纯续写模式 (自由创作新场次)';
    }

    // 1. 添加位置概述和写作模式说明 (导演版)
    messages.push({
      role: 'user',
      content: `【拍摄计划】场次：${context.currentChapter.order + 1}《${context.currentChapter.title}》镜头：${context.currentPosition.currentParagraph}\n【拍摄模式】${shootingMode}`
    });

    // 2. 添加前序章节（完整内容，十句一分段）(导演版)
    context.beforeChapters.forEach(chapter => {
      const chapterSegments = this.segmentTextBySentences(chapter.content, 10);

      // 添加章节概述（系统消息）
      messages.push({
        role: 'user',
        content: `【前情回顾：第${chapter.order + 1}场《${chapter.title}》完整剧本】\n总字数：${chapter.content.length}字\n分镜数：${chapterSegments.length}\n\n请仔细研读以下剧本的导演风格、镜头节奏和叙事特点，理解情节发展。`
      });

      // 添加助手确认
      messages.push({
        role: 'assistant',
        content: `收到。我将仔细研读第${chapter.order + 1}场《${chapter.title}》的剧本，重点分析导演风格、镜头节奏和叙事特点，为接下来的拍摄做好准备。`
      });

      // 添加每个分段（系统消息）
      chapterSegments.forEach((segment, index) => {
        if (segment.trim()) {
          messages.push({
            role: 'user',
            content: `【第${chapter.order + 1}场 - 分镜${index + 1}】\n请仔细研读以下分镜的导演风格和镜头语言：\n\n${segment}`
          });

          // 添加助手确认学习
          messages.push({
            role: 'assistant',
            content: `收到。已研读第${chapter.order + 1}场 - 分镜${index + 1}，理解其导演风格和镜头语言。`
          });
        }
      });
    });

    // 3. 添加当前章节前文（十句一分段）(导演版)
    if (context.currentPosition.beforeText) {
      const beforeSegments = this.segmentTextBySentences(context.currentPosition.beforeText, 10);

      // 添加前文概述（系统消息）
      messages.push({
        role: 'user',
        content: `【本场已拍板内容】\n总字数：${context.currentPosition.beforeText.length}字\n分镜数：${beforeSegments.length}\n\n请仔细研读以下已拍板内容的导演风格、镜头节奏和叙事特点，确保新镜头能无缝衔接。`
      });

      // 添加助手确认
      messages.push({
        role: 'assistant',
        content: `收到。我将仔细研读本场已拍板内容，重点分析其导演风格与节奏，确保新镜头能够自然衔接。`
      });

      // 添加每个前文分段（系统消息）
      beforeSegments.forEach((segment, index) => {
        if (segment.trim()) {
          messages.push({
            role: 'user',
            content: `【本场已拍板 - 分镜${index + 1}】\n请仔细研读以下已拍板分镜的导演风格和镜头语言：\n\n${segment}`
          });

          // 添加助手确认学习
          messages.push({
            role: 'assistant',
            content: `收到。已研读本场已拍板 - 分镜${index + 1}，理解其风格与语言。`
          });
        }
      });
    }

    // 4. 添加当前章节后文（十句一分段）(导演版)
    if (context.currentPosition.afterText) {
      const afterSegments = this.segmentTextBySentences(context.currentPosition.afterText, 10);

      // 添加后文概述（系统消息）
      messages.push({
        role: 'user',
        content: `【本场待拍板内容参考】\n总字数：${context.currentPosition.afterText.length}字\n分镜数：${afterSegments.length}\n\n请仔细研读以下待拍板内容的导演风格，确保新镜头能自然过渡。`
      });

      // 添加助手确认
      messages.push({
        role: 'assistant',
        content: `收到。我将仔细研读本场待拍板内容，确保拍摄能够自然过渡到后续镜头。`
      });

      // 添加每个后文分段（系统消息）
      afterSegments.forEach((segment, index) => {
        if (segment.trim()) {
          messages.push({
            role: 'user',
            content: `【本场待拍板 - 分镜${index + 1}】\n请仔细研读以下待拍板分镜的导演风格和镜头语言：\n\n${segment}`
          });

          // 添加助手确认学习
          messages.push({
            role: 'assistant',
            content: `收到。已研读本场待拍板 - 分镜${index + 1}，理解其风格与语言。`
          });
        }
      });
    }

    // 5. 添加后续章节（完整内容，十句一分段）(导演版)
    context.afterChapters.forEach(chapter => {
      const chapterSegments = this.segmentTextBySentences(chapter.content, 10);

      // 添加章节概述（系统消息）
      messages.push({
        role: 'user',
        content: `【后续剧本参考：第${chapter.order + 1}场《${chapter.title}》】\n总字数：${chapter.content.length}字\n分镜数：${chapterSegments.length}\n\n请仔细研读以下后续剧本，确保当前拍摄与后续情节保持连贯。`
      });

      // 添加助手确认
      messages.push({
        role: 'assistant',
        content: `收到。我将仔细研读第${chapter.order + 1}场《${chapter.title}》的后续剧本，确保情节连贯性。`
      });

      // 添加每个分段（系统消息）
      chapterSegments.forEach((segment, index) => {
        if (segment.trim()) {
          messages.push({
            role: 'user',
            content: `【第${chapter.order + 1}场 - 分镜${index + 1}】\n请仔细研读以下分镜的导演风格和镜头语言：\n\n${segment}`
          });

          // 添加助手确认学习
          messages.push({
            role: 'assistant',
            content: `收到。已研读第${chapter.order + 1}场 - 分镜${index + 1}，理解其风格与语言。`
          });
        }
      });
    });


    return messages;
  }

  /**
   * 计算当前位置信息
   */
  private calculateCurrentPosition(): CurrentPosition {
    const content = this.currentChapter.content;
    const cursorPosition = this.cursorPosition;

    // 获取前文和后文
    const beforeText = content.substring(0, cursorPosition);
    const afterText = content.substring(cursorPosition);

    // 验证前文后文分割的正确性
    this.validateContextSplit(beforeText, afterText, cursorPosition, content);

    // 计算段落信息
    const paragraphs = this.splitIntoParagraphs(content);
    const { currentParagraph, beforeParagraphs, afterParagraphs } = this.calculateParagraphPosition(
      content,
      cursorPosition,
      paragraphs
    );

    // 添加调试信息
    this.debugPositionCalculation(cursorPosition, beforeText, afterText, currentParagraph);

    return {
      cursorPosition,
      beforeText,
      afterText,
      currentParagraph,
      totalParagraphs: paragraphs.length,
      beforeParagraphs,
      afterParagraphs
    };
  }

  /**
   * 将内容分割为段落
   */
  private splitIntoParagraphs(content: string): string[] {
    if (!content) return [];

    // 优先使用双换行符分割
    let paragraphs = content.split(/\n\s*\n/);
    
    // 如果没有双换行符，使用单换行符
    if (paragraphs.length === 1 && content.includes('\n')) {
      paragraphs = content.split(/\n/);
    }

    // 过滤空段落
    return paragraphs.filter(p => p.trim().length > 0);
  }

  /**
   * 计算段落位置
   */
  private calculateParagraphPosition(
    content: string,
    cursorPosition: number,
    paragraphs: string[]
  ): { currentParagraph: number; beforeParagraphs: number; afterParagraphs: number } {
    if (paragraphs.length === 0) {
      return { currentParagraph: 1, beforeParagraphs: 0, afterParagraphs: 0 };
    }

    // 如果光标在内容开头，设为第一段
    if (cursorPosition <= 0) {
      return { currentParagraph: 1, beforeParagraphs: 0, afterParagraphs: paragraphs.length - 1 };
    }

    // 如果光标在内容末尾或超出内容，设为最后一段
    if (cursorPosition >= content.length) {
      return {
        currentParagraph: paragraphs.length,
        beforeParagraphs: paragraphs.length - 1,
        afterParagraphs: 0
      };
    }

    // 基于实际分割重新计算位置，而不是使用 indexOf 查找
    let currentPos = 0;
    let currentParagraph = 1;
    let beforeParagraphs = 0;

    // 检查是否使用双换行符分割
    const hasDoubleNewlines = content.includes('\n\n');

    if (hasDoubleNewlines) {
      // 使用双换行符分割的段落
      const paragraphSeparators = content.split(/\n\s*\n/);

      for (let i = 0; i < paragraphSeparators.length; i++) {
        const paragraphEnd = currentPos + paragraphSeparators[i].length;

        if (cursorPosition <= paragraphEnd) {
          currentParagraph = i + 1;
          beforeParagraphs = i;
          break;
        }

        // 计算分隔符的实际长度
        if (i < paragraphSeparators.length - 1) {
          // 查找实际的分隔符内容
          const remainingContent = content.substring(paragraphEnd);
          const separatorMatch = remainingContent.match(/^(\n\s*\n)/);
          if (separatorMatch) {
            currentPos = paragraphEnd + separatorMatch[1].length;
          } else {
            currentPos = paragraphEnd + 2; // 默认双换行符长度
          }
        }
      }
    } else if (content.includes('\n')) {
      // 使用单换行符分割的段落
      const singleLineParagraphs = content.split(/\n/);

      for (let i = 0; i < singleLineParagraphs.length; i++) {
        const paragraphEnd = currentPos + singleLineParagraphs[i].length;

        if (cursorPosition <= paragraphEnd) {
          currentParagraph = i + 1;
          beforeParagraphs = i;
          break;
        }

        // 加上换行符的长度
        if (i < singleLineParagraphs.length - 1) {
          currentPos = paragraphEnd + 1;
        }
      }
    } else {
      // 没有换行符，整个内容是一个段落
      currentParagraph = 1;
      beforeParagraphs = 0;
    }

    const afterParagraphs = paragraphs.length - currentParagraph;

    // 添加调试信息（开发模式）
    if (process.env.NODE_ENV === 'development') {
      console.log('段落位置计算调试:', {
        cursorPosition,
        contentLength: content.length,
        totalParagraphs: paragraphs.length,
        currentParagraph,
        beforeParagraphs,
        afterParagraphs,
        hasDoubleNewlines
      });
    }

    return { currentParagraph, beforeParagraphs, afterParagraphs };
  }

  /**
   * 获取前序章节
   */
  private getBeforeChapters(): Chapter[] {
    return this.chapters
      .filter(chapter => chapter.order < this.currentChapter.order)
      .sort((a, b) => a.order - b.order);
  }

  /**
   * 获取后续章节
   */
  private getAfterChapters(): Chapter[] {
    return this.chapters
      .filter(chapter => chapter.order > this.currentChapter.order)
      .sort((a, b) => a.order - b.order);
  }

  /**
   * 按句子分割文本（十句一分段）
   * @param text 要分割的文本
   * @param sentencesPerSegment 每段的句子数量
   * @returns 分段后的文本数组
   */
  private segmentTextBySentences(text: string, sentencesPerSegment: number = 10): string[] {
    if (!text || !text.trim()) {
      return [];
    }

    // 预处理：统一换行符
    const normalizedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // 按句子分割（中文和英文句号、问号、感叹号）
    // 关键修复：移除 \s* 以保留换行符和空白字符
    const sentences = normalizedText.split(/(?<=[。！？.!?])/).filter(s => s.trim());

    if (sentences.length === 0) {
      return [normalizedText]; // 如果没有句子分隔符，返回原文本
    }

    const segments: string[] = [];

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);

      // 智能连接句子，保持原有的换行结构
      let segmentText = '';

      for (let j = 0; j < segmentSentences.length; j++) {
        const sentence = segmentSentences[j];

        if (j === 0) {
          // 第一个句子直接添加
          segmentText = sentence;
        } else {
          // 后续句子：检查是否需要智能换行处理
          const prevSentence = segmentSentences[j - 1];

          // 如果前一个句子以换行符结尾，或者当前句子看起来像新段落的开始
          if (this.shouldPreserveLineBreak(prevSentence, sentence)) {
            segmentText += sentence;
          } else {
            // 如果句子之间没有换行，保持原有连接方式
            segmentText += sentence;
          }
        }
      }

      // 清理和规范化：移除开头结尾的多余空白，但保留内部换行
      segmentText = segmentText.trim();

      // 规范化换行：将多个连续换行符替换为双换行符（段落分隔）
      segmentText = segmentText.replace(/\n{3,}/g, '\n\n');

      if (segmentText) {
        segments.push(segmentText);
      }
    }

    // 如果没有分割出任何段落，回退到原始内容
    if (segments.length === 0 && normalizedText.trim()) {
      segments.push(normalizedText);
    }

    return segments;
  }

  /**
   * 判断是否应该保留换行符
   * @param prevSentence 前一个句子
   * @param currentSentence 当前句子
   * @returns 是否应该保留换行
   */
  private shouldPreserveLineBreak(prevSentence: string, currentSentence: string): boolean {
    // 如果前一个句子以换行符结尾，保留换行
    if (prevSentence.endsWith('\n')) {
      return true;
    }

    // 如果当前句子以换行符开头，保留换行
    if (currentSentence.startsWith('\n')) {
      return true;
    }

    // 检查当前句子是否看起来像新段落的开始
    const trimmedCurrent = currentSentence.trim();
    const newParagraphIndicators = [
      '第', '然而', '但是', '不过', '于是', '接着', '随后', '此时', '这时',
      '突然', '忽然', '只见', '只听', '只是', '原来', '果然', '竟然', '后来',
      '接下来', '紧接着', '与此同时', '同时', '另一方面', '另外', '此外'
    ];

    return newParagraphIndicators.some(indicator => trimmedCurrent.startsWith(indicator));
  }

  /**
   * 构建简化的上下文字符串（用于兼容现有接口）
   */
  buildContextString(): string {
    const messages = this.buildContextMessages();
    return messages.map(msg => msg.content).join('\n\n');
  }

  /**
   * 验证前文后文分割的正确性
   */
  private validateContextSplit(
    beforeText: string,
    afterText: string,
    cursorPosition: number,
    content: string
  ): void {
    const totalLength = beforeText.length + afterText.length;
    const expectedLength = content.length;

    if (totalLength !== expectedLength) {
      console.error('前文后文分割错误:', {
        beforeTextLength: beforeText.length,
        afterTextLength: afterText.length,
        totalLength,
        expectedLength,
        cursorPosition
      });
    }

    if (beforeText.length !== cursorPosition) {
      console.error('前文长度与光标位置不匹配:', {
        beforeTextLength: beforeText.length,
        cursorPosition,
        difference: beforeText.length - cursorPosition
      });
    }
  }

  /**
   * 调试位置计算信息
   */
  private debugPositionCalculation(
    cursorPosition: number,
    beforeText: string,
    afterText: string,
    currentParagraph: number
  ): void {
    if (process.env.NODE_ENV === 'development') {
      console.log('光标位置调试信息:', {
        cursorPosition,
        beforeTextLength: beforeText.length,
        afterTextLength: afterText.length,
        totalLength: this.currentChapter.content.length,
        hasBeforeText: beforeText.length > 0,
        hasAfterText: afterText.length > 0,
        currentParagraph,
        isAtBeginning: cursorPosition === 0,
        isAtEnd: cursorPosition === this.currentChapter.content.length,
        contextType: beforeText.length === 0 ? '纯续写' :
                    afterText.length === 0 ? '纯续写' : '衔接模式'
      });
    }
  }
}
