"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

import { EmptyState } from './EmptyState';
import { WorldBuildingView } from './WorldBuildingView';
import { WorldBuildingEdit } from './WorldBuildingEdit';
import { useWorldBuildingDetail } from './WorldBuildingDetailHooks';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorldBuildingDetailProps {
  worldBuilding: WorldBuilding | null;
  bookId: string;
  worldBuildings: WorldBuilding[];
  isEditing: boolean;
  onEdit: () => void;
  onDelete: (worldBuildingId: string) => void;
  onSave: (worldBuilding: WorldBuilding) => void;
  onCancel: () => void;
  chapters?: GenericChapter[]; // 可选的章节列表，用于关联
}

/**
 * 世界观详情/编辑组件
 */
export const WorldBuildingDetail: React.FC<WorldBuildingDetailProps> = ({
  worldBuilding,
  bookId,
  worldBuildings,
  isEditing,
  onEdit,
  onDelete,
  onSave,
  onCancel,
  chapters = []
}) => {
  // 使用自定义Hook管理状态和事件
  const { editingWorldBuilding, handleInputChange, handleSave } = useWorldBuildingDetail(
    worldBuilding,
    isEditing,
    onSave
  );

  // 如果没有选中的世界观，显示空状态
  if (!worldBuilding) {
    return <EmptyState />;
  }

  // 创建按钮组件 - 使用简单的React按钮
  const editButtonNode = (
    <button
      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
      onClick={onEdit}
    >
      编辑
    </button>
  );

  const deleteButtonNode = (
    <button
      className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm"
      onClick={() => onDelete(worldBuilding.id!)}
    >
      删除
    </button>
  );

  return (
    <div className="h-full overflow-y-auto p-6 bg-gray-50 rounded-r-lg">
      {isEditing ? (
        <WorldBuildingEdit
          editingWorldBuilding={editingWorldBuilding!}
          handleInputChange={handleInputChange}
          handleSave={handleSave}
          onCancel={onCancel}
          saveButton={null}
          cancelButton={null}
        />
      ) : (
        <WorldBuildingView
          worldBuilding={worldBuilding}
          worldBuildings={worldBuildings}
          chapters={chapters}
          onEdit={onEdit}
          onDelete={onDelete}
          onSave={onSave}
          editButton={editButtonNode}
          deleteButton={deleteButtonNode}
        />
      )}
    </div>
  );
};
