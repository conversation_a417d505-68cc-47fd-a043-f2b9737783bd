"use client";

import React, { useState, useEffect } from 'react';
import { TopNavigationBar } from './TopNavigationBar';
import { StatusBar } from './StatusBar';
import { CreationControlPanel } from '../panels/CreationControlPanel';
import { MainEditorPanel } from '../panels/MainEditorPanel';
import { RhythmControlPanel } from '../panels/RhythmControlPanel';
import { useShortStoryStore } from '../stores/shortStoryStore';

interface ShortStoryWorkspaceProps {
  bookId: string;
  onContentGenerated?: (content: string) => void;
  className?: string;
}

/**
 * AI短篇创作工作区 - 三栏布局主组件
 * 左侧：创作控制面板 (30%)
 * 中间：正文编辑面板 (45%)
 * 右侧：节奏控制面板 (25%)
 */
export const ShortStoryWorkspace: React.FC<ShortStoryWorkspaceProps> = ({
  bookId,
  onContentGenerated,
  className = ''
}) => {
  // 全局状态管理
  const {
    layoutState,
    setLayoutState,
    currentStep,
    isGenerating
  } = useShortStoryStore();

  // 本地状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartWidth, setDragStartWidth] = useState(0);

  // 处理面板宽度拖拽调整
  const handleMouseDown = (e: React.MouseEvent, panelType: 'left' | 'right') => {
    e.preventDefault();
    e.stopPropagation();

    const startX = e.clientX;
    const startWidth = panelType === 'left' ? layoutState.leftPanelWidth : layoutState.rightPanelWidth;

    setIsDragging(true);
    setDragStartX(startX);
    setDragStartWidth(startWidth);

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const containerWidth = window.innerWidth;
      const deltaPercent = (deltaX / containerWidth) * 100;

      // 计算新的宽度，确保在合理范围内
      let newWidth;
      if (panelType === 'left') {
        newWidth = Math.max(20, Math.min(50, startWidth + deltaPercent));
        setLayoutState({
          leftPanelWidth: newWidth
        });
      } else {
        newWidth = Math.max(15, Math.min(40, startWidth - deltaPercent));
        setLayoutState({
          rightPanelWidth: newWidth
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  };

  // 响应式布局处理
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      if (width < 768) {
        // 移动端：单栏布局
        setLayoutState({
          isMobile: true,
          rightPanelCollapsed: true
        });
      } else if (width < 1200) {
        // 平板端：左侧面板可折叠
        setLayoutState({
          isMobile: false,
          leftPanelWidth: Math.min(layoutState.leftPanelWidth, 35)
        });
      } else {
        // 桌面端：完整三栏布局
        setLayoutState({
          isMobile: false
        });
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化

    return () => window.removeEventListener('resize', handleResize);
  }, [setLayoutState]); // 移除 layoutState 依赖

  // 计算面板宽度
  const leftWidth = layoutState.isMobile ? 100 : layoutState.leftPanelWidth;
  const rightWidth = layoutState.rightPanelCollapsed ? 0 : layoutState.rightPanelWidth;
  const centerWidth = layoutState.isMobile ? 100 : 100 - leftWidth - rightWidth;

  return (
    <div className={`h-full flex flex-col bg-gray-50 ${className}`}>
      {/* 顶部导航栏 */}
      <TopNavigationBar 
        currentStep={currentStep}
        isGenerating={isGenerating}
        onToggleRightPanel={() => setLayoutState({
          rightPanelCollapsed: !layoutState.rightPanelCollapsed
        })}
      />
      
      {/* 主要内容区域 - 三栏布局 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 移动端Tab切换 */}
        {layoutState.isMobile ? (
          <MobileTabView 
            bookId={bookId}
            onContentGenerated={onContentGenerated}
          />
        ) : (
          <>
            {/* 左侧控制面板 */}
            <div 
              style={{ width: `${leftWidth}%` }}
              className="relative border-r border-gray-200 bg-white"
            >
              <CreationControlPanel 
                bookId={bookId}
                onContentGenerated={onContentGenerated}
              />
              
              {/* 拖拽调整手柄 */}
              <div
                className="absolute right-0 top-0 w-2 h-full cursor-col-resize hover:bg-blue-500 transition-colors z-10 bg-gray-300 hover:bg-blue-400"
                onMouseDown={(e) => handleMouseDown(e, 'left')}
                title="拖拽调整面板宽度"
              >
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-gray-500 rounded-full opacity-60" />
              </div>
            </div>
            
            {/* 中间正文编辑区域 */}
            <div 
              style={{ width: `${centerWidth}%` }}
              className="relative bg-white"
            >
              <MainEditorPanel 
                bookId={bookId}
                onContentGenerated={onContentGenerated}
              />
            </div>
            
            {/* 右侧节奏控制面板 */}
            {!layoutState.rightPanelCollapsed && (
              <div
                style={{ width: `${rightWidth}%` }}
                className="relative border-l border-gray-200 bg-gray-50"
              >
                {/* 拖拽调整手柄 */}
                <div
                  className="absolute left-0 top-0 w-2 h-full cursor-col-resize hover:bg-blue-500 transition-colors z-10 bg-gray-300 hover:bg-blue-400"
                  onMouseDown={(e) => handleMouseDown(e, 'right')}
                  title="拖拽调整面板宽度"
                >
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-gray-500 rounded-full opacity-60" />
                </div>
                <RhythmControlPanel />
              </div>
            )}
          </>
        )}
      </div>
      
      {/* 底部状态栏 */}
      <StatusBar />
    </div>
  );
};

/**
 * 移动端Tab视图组件
 */
const MobileTabView: React.FC<{
  bookId: string;
  onContentGenerated?: (content: string) => void;
}> = ({ bookId, onContentGenerated }) => {
  const [activeTab, setActiveTab] = useState<'control' | 'editor' | 'rhythm'>('editor');

  return (
    <div className="flex-1 flex flex-col">
      {/* Tab内容区域 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'control' && (
          <CreationControlPanel 
            bookId={bookId}
            onContentGenerated={onContentGenerated}
          />
        )}
        {activeTab === 'editor' && (
          <MainEditorPanel 
            bookId={bookId}
            onContentGenerated={onContentGenerated}
          />
        )}
        {activeTab === 'rhythm' && (
          <RhythmControlPanel />
        )}
      </div>
      
      {/* 底部Tab导航 */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex justify-around">
          <button
            onClick={() => setActiveTab('control')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors ${
              activeTab === 'control' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <span className="text-lg mb-1">⚙️</span>
            <span className="text-xs">控制</span>
          </button>
          
          <button
            onClick={() => setActiveTab('editor')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors ${
              activeTab === 'editor' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <span className="text-lg mb-1">✏️</span>
            <span className="text-xs">编辑</span>
          </button>
          
          <button
            onClick={() => setActiveTab('rhythm')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors ${
              activeTab === 'rhythm' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <span className="text-lg mb-1">📊</span>
            <span className="text-xs">节奏</span>
          </button>
        </div>
      </div>
    </div>
  );
};
