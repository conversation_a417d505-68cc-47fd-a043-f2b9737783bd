"use client";

import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { Chapter, Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { Outline } from '@/factories/ui/types/outline';
import { OutlineFormatterService, OutlineContextMode } from '@/services/ai/OutlineFormatterService';

/**
 * 提示词辅助服务接口
 */
export interface PromptHelperServiceInterface {
  /**
   * 计算实际字数
   * @param text 文本内容
   * @returns 字数
   */
  calculateActualWordCount(text: string): number;

  /**
   * 智能分段文本
   * @param text 文本内容
   * @returns 分段后的文本数组
   */
  segmentText(text: string): string[];

  /**
   * 添加选中的章节
   * @param messageBuilder 消息构建器
   * @param chapters 章节列表
   * @param selectedChapterIds 选中的章节ID列表
   */
  addSelectedChapters(
    messageBuilder: MessageBuilder,
    chapters: any[],
    selectedChapterIds: string[]
  ): void;

  /**
   * 添加选中的人物
   * @param messageBuilder 消息构建器
   * @param characters 人物列表
   * @param selectedCharacterIds 选中的人物ID列表
   */
  addSelectedCharacters(
    messageBuilder: MessageBuilder,
    characters: Character[],
    selectedCharacterIds: string[]
  ): void;

  /**
   * 添加选中的世界观
   * @param messageBuilder 消息构建器
   * @param worldBuildings 世界观列表
   * @param selectedWorldBuildingIds 选中的世界观ID列表
   */
  addSelectedWorldBuildings(
    messageBuilder: MessageBuilder,
    worldBuildings: WorldBuilding[],
    selectedWorldBuildingIds: string[]
  ): void;

  /**
   * 添加选中的术语
   * @param messageBuilder 消息构建器
   * @param terminologies 术语列表
   * @param selectedTerminologyIds 选中的术语ID列表
   */
  addSelectedTerminologies(
    messageBuilder: MessageBuilder,
    terminologies: Terminology[],
    selectedTerminologyIds: string[]
  ): void;

  /**
   * 添加选中的章节（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param chapters 章节列表
   * @param selectedChapterIds 选中的章节ID列表
   */
  addSelectedChaptersForContinue(
    messageBuilder: MessageBuilder,
    chapters: any[],
    selectedChapterIds: string[]
  ): void;

  /**
   * 添加选中的人物（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param characters 人物列表
   * @param selectedCharacterIds 选中的人物ID列表
   */
  addSelectedCharactersForContinue(
    messageBuilder: MessageBuilder,
    characters: Character[],
    selectedCharacterIds: string[]
  ): void;

  /**
   * 添加选中的世界观（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param worldBuildings 世界观列表
   * @param selectedWorldBuildingIds 选中的世界观ID列表
   */
  addSelectedWorldBuildingsForContinue(
    messageBuilder: MessageBuilder,
    worldBuildings: WorldBuilding[],
    selectedWorldBuildingIds: string[]
  ): void;

  /**
   * 添加选中的术语（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param terminologies 术语列表
   * @param selectedTerminologyIds 选中的术语ID列表
   */
  addSelectedTerminologiesForContinue(
    messageBuilder: MessageBuilder,
    terminologies: Terminology[],
    selectedTerminologyIds: string[]
  ): void;

  /**
   * 添加选中的大纲节点
   * @param messageBuilder 消息构建器
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   * @param contextMode 上下文模式
   */
  addSelectedOutlineNodes(
    messageBuilder: MessageBuilder,
    outlines: Outline[],
    selectedNodeIds: string[],
    contextMode?: OutlineContextMode
  ): void;

  /**
   * 添加选中的大纲节点（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   */
  addSelectedOutlineNodesForContinue(
    messageBuilder: MessageBuilder,
    outlines: Outline[],
    selectedNodeIds: string[]
  ): void;
}

/**
 * 提示词辅助服务实现
 */
export class PromptHelperService implements PromptHelperServiceInterface {
  private outlineFormatter: OutlineFormatterService;

  constructor() {
    this.outlineFormatter = new OutlineFormatterService();
  }
  /**
   * 计算实际字数
   * @param text 文本内容
   * @returns 字数
   */
  public calculateActualWordCount(text: string): number {
    // 移除Markdown标记
    const plainText = text.replace(/#+\s+/g, '') // 移除标题标记
                          .replace(/\*\*|\*|~~|__/g, '') // 移除加粗、斜体、删除线等标记
                          .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
                          .replace(/```[\s\S]*?```/g, '') // 移除代码块
                          .replace(/`.*?`/g, '') // 移除行内代码
                          .replace(/>\s+/g, '') // 移除引用
                          .replace(/\s+/g, ''); // 移除空白字符

    // 计算字符数
    return plainText.length;
  }

  /**
   * 智能分段文本
   * @param text 文本内容
   * @returns 分段后的文本数组
   */
  public segmentText(text: string): string[] {
    // 如果文本为空，返回空数组
    if (!text || text.trim().length === 0) {
      return [];
    }

    // 目标段落字数（约2000字左右）
    const targetSegmentWordCount = 2000;

    // 按段落分割文本
    const paragraphs = text.split(/\n\s*\n/);
    const segments: string[] = [];

    let currentSegment = '';
    let currentSegmentWordCount = 0;

    // 遍历段落，按照目标字数合并段落
    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim();
      if (!trimmedParagraph) continue;

      const paragraphWordCount = this.calculateActualWordCount(trimmedParagraph);

      // 如果当前段落加上新段落的字数小于目标字数，则合并
      if (currentSegmentWordCount + paragraphWordCount <= targetSegmentWordCount) {
        currentSegment += (currentSegment ? '\n\n' : '') + trimmedParagraph;
        currentSegmentWordCount += paragraphWordCount;
      } else {
        // 如果当前段落已经有内容，则添加到segments中
        if (currentSegment) {
          segments.push(currentSegment);
        }

        // 如果新段落的字数大于目标字数的一半，则单独作为一个段落
        if (paragraphWordCount > targetSegmentWordCount / 2) {
          segments.push(trimmedParagraph);
          currentSegment = '';
          currentSegmentWordCount = 0;
        } else {
          // 否则，开始一个新的段落
          currentSegment = trimmedParagraph;
          currentSegmentWordCount = paragraphWordCount;
        }
      }
    }

    // 添加最后一个段落
    if (currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  }

  /**
   * 添加选中的章节
   * @param messageBuilder 消息构建器
   * @param chapters 章节列表
   * @param selectedChapterIds 选中的章节ID列表
   */
  public addSelectedChapters(
    messageBuilder: MessageBuilder,
    chapters: any[],
    selectedChapterIds: string[]
  ): void {
    if (selectedChapterIds.length === 0) {
      return;
    }

    // 获取选中的章节
    const selectedChapters = chapters.filter(chapter =>
      selectedChapterIds.includes(chapter.id || '')
    );

    if (selectedChapters.length > 0) {
      // 添加一条说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将查看并分析以下章节内容，以便创作符合上下文的内容：`, true, true);

      // 使用智能分段器

      // 为每个章节进行智能分段并添加单独的消息
      let globalSegmentIndex = 0;
      let totalSegmentsCount = 0;

      // 先计算总段落数
      for (const chapter of selectedChapters) {
        // 构建章节文本，包含标题和内容
        const chapterTitle = chapter.title || `第${chapter.order || '?'}章`;
        const chapterText = `# ${chapterTitle}\n\n${chapter.content || ''}`;

        // 如果有章节摘要，添加到章节文本中
        const chapterSummary = chapter.summary ? `\n\n## 章节摘要\n${chapter.summary}` : '';
        const chapterNotes = chapter.notes ? `\n\n## 章节笔记\n${chapter.notes}` : '';
        const fullChapterText = chapterText + chapterSummary + chapterNotes;

        const segments = this.segmentText(fullChapterText);
        totalSegmentsCount += segments.length;
      }

      // 为每个章节的每个段落添加单独的消息
      for (const chapter of selectedChapters) {
        // 构建章节文本，包含标题和内容
        const chapterTitle = chapter.title || `第${chapter.order || '?'}章`;
        const chapterText = `# ${chapterTitle}\n\n${chapter.content || ''}`;

        // 如果有章节摘要，添加到章节文本中
        const chapterSummary = chapter.summary ? `\n\n## 章节摘要\n${chapter.summary}` : '';
        const chapterNotes = chapter.notes ? `\n\n## 章节笔记\n${chapter.notes}` : '';
        const fullChapterText = chapterText + chapterSummary + chapterNotes;

        const segments = this.segmentText(fullChapterText);

        for (let i = 0; i < segments.length; i++) {
          globalSegmentIndex++;
          const segment = segments[i];
          const segmentWordCount = this.calculateActualWordCount(segment);

          // 按照要求的格式构建消息：第x章，第几段，多少字，内容
          const segmentPrompt = `${chapterTitle}，第${i+1}/${segments.length}段，${segmentWordCount}字，内容：\n\n${segment}`;
          messageBuilder.addUserMessage(segmentPrompt);

          // 每个段落都添加一个确认消息，标记为系统生成的预设消息
          const confirmationMessage = `我已阅读并分析了"${chapterTitle}"章节的第${i+1}段内容（总进度：${globalSegmentIndex}/${totalSegmentsCount}）。`;
          messageBuilder.addAssistantMessage(confirmationMessage, true, true);
        }
      }

      // 添加一个总结消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我已完成所有章节的分析，将基于这些内容进行创作。`, true, true);
    }
  }

  /**
   * 添加选中的人物
   * @param messageBuilder 消息构建器
   * @param characters 人物列表
   * @param selectedCharacterIds 选中的人物ID列表
   */
  public addSelectedCharacters(
    messageBuilder: MessageBuilder,
    characters: Character[],
    selectedCharacterIds: string[]
  ): void {
    if (selectedCharacterIds.length === 0) {
      return;
    }

    // 获取选中的人物
    const selectedCharacters = characters.filter(character =>
      selectedCharacterIds.includes(character.id || '')
    );

    if (selectedCharacters.length > 0) {
      // 添加一条说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将参考以下人物信息，确保人物形象和行为的一致性：`, true, true);

      // 为每个人物添加单独的消息
      for (const character of selectedCharacters) {
        // 构建人物信息，包含所有可能的字段
        const characterInfo = `【人物名称】${character.name || '未命名'}\n\n【人物描述】\n${character.description || '无描述'}\n\n【别名】\n${character.alias ? character.alias.join(', ') : '无别名'}\n\n【性格特点】\n${character.personality || '无特定性格'}\n\n【外貌特征】\n${character.appearance || '无特定外貌'}\n\n【背景故事】\n${character.background || '无背景故事'}\n\n【目标】\n${character.goals || '无特定目标'}\n\n【隐藏动机】\n${character.hiddenMotivation || '无隐藏动机'}\n\n【秘密历史】\n${character.secretHistory || '无秘密历史'}\n\n【内心冲突】\n${character.innerConflicts || '无内心冲突'}\n\n【角色原型】\n${character.characterArchetype || '无角色原型'}\n\n【成长弧线】\n${character.growthArc || '无成长弧线'}\n\n【象征意义】\n${character.symbolism || '无象征意义'}\n\n【笔记】\n${character.notes || '无笔记'}`;
        messageBuilder.addUserMessage(characterInfo, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已记住"${character.name || '未命名'}"的人物信息，将确保在创作中保持人物形象和行为的一致性。`, true, true);
      }
    }
  }

  /**
   * 添加选中的世界观
   * @param messageBuilder 消息构建器
   * @param worldBuildings 世界观列表
   * @param selectedWorldBuildingIds 选中的世界观ID列表
   */
  public addSelectedWorldBuildings(
    messageBuilder: MessageBuilder,
    worldBuildings: WorldBuilding[],
    selectedWorldBuildingIds: string[]
  ): void {
    if (selectedWorldBuildingIds.length === 0) {
      return;
    }

    // 获取选中的世界观
    const selectedWorldBuildingItems = worldBuildings.filter(worldBuilding =>
      selectedWorldBuildingIds.includes(worldBuilding.id || '')
    );

    if (selectedWorldBuildingItems.length > 0) {
      // 添加一条说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将参考以下世界观设定，确保创作内容符合世界观规则：`, true, true);

      // 为每个世界观添加单独的消息
      for (const worldBuilding of selectedWorldBuildingItems) {
        // 构建世界观信息，包含所有可能的字段
        const worldBuildingInfo = `【世界观名称】${worldBuilding.name || '未命名'}\n\n【世界观描述】\n${worldBuilding.description || '无描述'}\n\n【世界观类别】\n${worldBuilding.category || '无类别'}\n\n【规则与法则】\n${worldBuilding.attributes?.rules || '无特定规则'}\n\n【历史背景】\n${worldBuilding.attributes?.history || '无历史背景'}\n\n【地理环境】\n${worldBuilding.attributes?.geography || '无地理环境描述'}\n\n【文化与社会】\n${worldBuilding.attributes?.culture || '无文化与社会描述'}\n\n【笔记】\n${worldBuilding.notes || '无笔记'}`;
        messageBuilder.addUserMessage(worldBuildingInfo, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已记住"${worldBuilding.name || '未命名'}"的世界观设定，将确保创作内容符合这些规则和设定。`, true, true);
      }
    }
  }

  /**
   * 添加选中的术语
   * @param messageBuilder 消息构建器
   * @param terminologies 术语列表
   * @param selectedTerminologyIds 选中的术语ID列表
   */
  public addSelectedTerminologies(
    messageBuilder: MessageBuilder,
    terminologies: Terminology[],
    selectedTerminologyIds: string[]
  ): void {
    if (selectedTerminologyIds.length === 0) {
      return;
    }

    // 获取选中的术语
    const selectedTerminologyItems = terminologies.filter(terminology =>
      selectedTerminologyIds.includes(terminology.id || '')
    );

    if (selectedTerminologyItems.length > 0) {
      // 添加一条说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将参考以下术语定义，确保术语使用的准确性和一致性：`, true, true);

      // 为每个术语添加单独的消息
      for (const terminology of selectedTerminologyItems) {
        // 构建术语信息，包含所有可能的字段
        const terminologyInfo = `【术语名称】${terminology.name || '未命名'}\n\n【术语类别】\n${terminology.category || '无类别'}\n\n【术语描述】\n${terminology.description || '无描述'}\n\n【别名】\n${terminology.alias ? terminology.alias.join(', ') : '无别名'}\n\n【相关人物】\n${terminology.relatedCharacterIds?.length ? '有关联人物' : '无关联人物'}\n\n【相关术语】\n${terminology.relatedTerminologyIds?.length ? '有关联术语' : '无关联术语'}\n\n【相关世界观】\n${terminology.relatedWorldBuildingIds?.length ? '有关联世界观' : '无关联世界观'}\n\n【笔记】\n${terminology.notes || '无笔记'}`;
        messageBuilder.addUserMessage(terminologyInfo, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已记住"${terminology.name || '未命名'}"的术语定义，将在创作中正确使用这些术语。`, true, true);
      }
    }
  }

  /**
   * 添加选中的章节（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param chapters 章节列表
   * @param selectedChapterIds 选中的章节ID列表
   */
  public addSelectedChaptersForContinue(
    messageBuilder: MessageBuilder,
    chapters: any[],
    selectedChapterIds: string[]
  ): void {
    if (selectedChapterIds.length === 0) {
      return;
    }

    // 获取选中章节的内容
    const selectedChapters = chapters.filter(chapter =>
      selectedChapterIds.includes(chapter.id || '')
    );

    if (selectedChapters.length > 0) {
      // 添加一条简化的说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将参考以下章节内容，以保持故事的连贯性：`, true, true);

      // 为每个章节添加简化的消息
      for (const chapter of selectedChapters) {
        const chapterTitle = chapter.title || `第${chapter.order || '?'}章`;
        const chapterWordCount = this.calculateActualWordCount(chapter.content || '');

        // 构建简化的章节信息
        let chapterInfo = `${chapterTitle}（${chapterWordCount}字）`;

        // 如果有章节摘要，添加摘要信息
        if (chapter.summary) {
          chapterInfo += `，摘要：${chapter.summary.substring(0, 50)}${chapter.summary.length > 50 ? '...' : ''}`;
        }

        // 只添加章节标题和字数信息，不添加详细内容，标记为系统生成的预设消息
        messageBuilder.addAssistantMessage(chapterInfo, true, true);
      }
    }
  }

  /**
   * 添加选中的人物（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param characters 人物列表
   * @param selectedCharacterIds 选中的人物ID列表
   */
  public addSelectedCharactersForContinue(
    messageBuilder: MessageBuilder,
    characters: Character[],
    selectedCharacterIds: string[]
  ): void {
    if (selectedCharacterIds.length === 0) {
      return;
    }

    // 获取选中人物的内容
    const selectedCharacters = characters.filter(character =>
      selectedCharacterIds.includes(character.id || '')
    );

    if (selectedCharacters.length > 0) {
      // 添加一条简化的说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将考虑以下人物信息：${selectedCharacters.map(c => c.name).join('、')}`, true, true);
    }
  }

  /**
   * 添加选中的世界观（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param worldBuildings 世界观列表
   * @param selectedWorldBuildingIds 选中的世界观ID列表
   */
  public addSelectedWorldBuildingsForContinue(
    messageBuilder: MessageBuilder,
    worldBuildings: WorldBuilding[],
    selectedWorldBuildingIds: string[]
  ): void {
    if (selectedWorldBuildingIds.length === 0) {
      return;
    }

    // 获取选中世界观的内容
    const selectedWorldBuildings = worldBuildings.filter(worldBuilding =>
      selectedWorldBuildingIds.includes(worldBuilding.id || '')
    );

    if (selectedWorldBuildings.length > 0) {
      // 添加一条简化的说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将遵循以下世界观设定：${selectedWorldBuildings.map(w => w.name).join('、')}`, true, true);
    }
  }

  /**
   * 添加选中的术语（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param terminologies 术语列表
   * @param selectedTerminologyIds 选中的术语ID列表
   */
  public addSelectedTerminologiesForContinue(
    messageBuilder: MessageBuilder,
    terminologies: Terminology[],
    selectedTerminologyIds: string[]
  ): void {
    if (selectedTerminologyIds.length === 0) {
      return;
    }

    // 获取选中术语的内容
    const selectedTerminologies = terminologies.filter(terminology =>
      selectedTerminologyIds.includes(terminology.id || '')
    );

    if (selectedTerminologies.length > 0) {
      // 添加一条简化的说明消息，标记为系统生成的预设消息
      messageBuilder.addAssistantMessage(`我将正确使用以下术语：${selectedTerminologies.map(t => t.name).join('、')}`, true, true);
    }
  }

  /**
   * 添加选中的大纲节点（新版本 - 单节点单条目）
   * @param messageBuilder 消息构建器
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   * @param contextMode 上下文模式
   */
  public addSelectedOutlineNodes(
    messageBuilder: MessageBuilder,
    outlines: Outline[],
    selectedNodeIds: string[],
    contextMode: OutlineContextMode = 'hierarchy'
  ): void {
    if (selectedNodeIds.length === 0 || !outlines.length) {
      console.log('⚠️ addSelectedOutlineNodes: 没有选中的大纲节点或大纲数据为空');
      return;
    }

    console.log('🔍 PromptHelper.addSelectedOutlineNodes 开始执行（新版本）');
    console.log('📋 大纲数据:', { outlinesCount: outlines.length, selectedNodeIds, contextMode });

    try {
      // 获取选中的节点
      const selectedNodes = this.getSelectedOutlineNodes(outlines, selectedNodeIds);
      console.log('📋 找到的选中节点:', selectedNodes.map((n: any) => ({
        id: n.id,
        title: n.title,
        type: n.type,
        hasCreativeNotes: !!n.creativeNotes,
        creativeNotesLength: n.creativeNotes?.length || 0
      })));

      if (selectedNodes.length === 0) {
        console.log('⚠️ 没有找到匹配的大纲节点');
        return;
      }

      // 使用新的单节点单条目方式
      this.addOutlineNodesAsIndividualMessages(messageBuilder, selectedNodes);
      console.log('✅ 大纲节点消息已添加到MessageBuilder（单节点单条目模式）');

    } catch (error) {
      console.error('❌ addSelectedOutlineNodes执行失败，回退到旧版本:', error);
      // 回退到旧版本实现
      this.addSelectedOutlineNodesLegacy(messageBuilder, outlines, selectedNodeIds, contextMode);
    }
  }

  /**
   * 旧版本的大纲节点添加方法（作为备用）
   * @param messageBuilder 消息构建器
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   * @param contextMode 上下文模式
   */
  private addSelectedOutlineNodesLegacy(
    messageBuilder: MessageBuilder,
    outlines: Outline[],
    selectedNodeIds: string[],
    contextMode: OutlineContextMode = 'hierarchy'
  ): void {
    console.log('🔄 使用旧版本大纲节点添加方法');

    // 添加说明消息（系统消息 - 了解自己的情况）
    messageBuilder.addSystemMessage(`以下是用户选择的大纲结构信息，请在创作时参考：`);

    // 使用OutlineFormatterService格式化大纲数据
    const formattedOutline = this.outlineFormatter.formatForAI(outlines, selectedNodeIds, contextMode);
    console.log('📝 格式化大纲结果长度:', formattedOutline.length);

    if (formattedOutline) {
      // 添加格式化后的大纲数据（系统消息 - 了解自己的情况）
      messageBuilder.addSystemMessage(formattedOutline);

      // 添加助手确认消息（助手确认 - 了解自己的方式）
      messageBuilder.addAssistantMessage(`我已了解相关大纲信息，将在创作中体现这些要素。`, true, true);

      console.log('✅ 大纲消息已添加到MessageBuilder（旧版本，系统+助手确认）');
    } else {
      console.log('⚠️ 格式化大纲结果为空，未添加消息');
    }
  }

  /**
   * 添加选中的大纲节点（简化版，用于继续对话模式）
   * @param messageBuilder 消息构建器
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   */
  public addSelectedOutlineNodesForContinue(
    messageBuilder: MessageBuilder,
    outlines: Outline[],
    selectedNodeIds: string[]
  ): void {
    if (selectedNodeIds.length === 0 || !outlines.length) {
      console.log('⚠️ addSelectedOutlineNodesForContinue: 没有选中的大纲节点或大纲数据为空');
      return;
    }

    console.log('🔍 PromptHelper.addSelectedOutlineNodesForContinue 开始执行:', {
      outlinesCount: outlines.length,
      selectedNodeIds,
      timestamp: new Date().toISOString()
    });

    // 使用简化模式格式化大纲数据
    const formattedOutline = this.outlineFormatter.formatForAI(outlines, selectedNodeIds, 'selected');

    if (formattedOutline) {
      // 添加简化的说明消息（系统消息 - 了解自己的情况）
      messageBuilder.addSystemMessage(`以下是相关大纲节点信息，请在续写中参考这些内容保持故事连贯性：`);

      // 添加格式化后的大纲数据（系统消息 - 了解自己的情况）
      messageBuilder.addSystemMessage(formattedOutline);

      // 添加助手确认消息（助手确认 - 了解自己的方式）
      messageBuilder.addAssistantMessage(`我已了解相关大纲信息，将在续写中体现这些要素。`, true, true);

      console.log('✅ 大纲数据已添加到续写消息构建器（系统+助手确认）');
    } else {
      console.log('⚠️ 格式化大纲结果为空，未添加消息');
    }
  }

  /**
   * 获取选中的大纲节点
   * @param outlines 大纲列表
   * @param selectedNodeIds 选中的节点ID列表
   * @returns 选中的节点数组
   */
  private getSelectedOutlineNodes(outlines: Outline[], selectedNodeIds: string[]): any[] {
    const selectedNodes: any[] = [];

    // 递归遍历大纲树，查找选中的节点
    const findNodesRecursively = (nodes: any[], level: number = 1): void => {
      for (const node of nodes) {
        // 🔥 修复：允许Synopsis节点参与AI写作关联
        // Synopsis节点包含重要的开头设计、结尾设计等信息，对AI写作很有价值
        console.log(`✅ 处理节点: ${node.title} (${node.type}) (${node.id})`);

        if (selectedNodeIds.includes(node.id)) {
          // 添加节点信息，包含层级和位置信息以及所有专有字段
          selectedNodes.push({
            id: node.id,
            title: node.title,
            type: node.type || 'plot',
            description: node.description,
            creativeNotes: node.creativeNotes, // 添加创作建议字段
            content: node.content,
            level: level,
            order: node.order,
            position: node.position || `第${level}级`,
            parentId: node.parentId,
            children: node.children,
            // 章节专有字段
            chapterStyle: node.chapterStyle,
            chapterTechniques: node.chapterTechniques,
            chapterGoals: node.chapterGoals,
            // 剧情节点专有字段
            plotType: node.plotType,
            plotPoints: node.plotPoints,
            relatedCharacters: node.relatedCharacters,
            // 对话设计专有字段
            dialogueScene: node.dialogueScene,
            participants: node.participants,
            dialoguePurpose: node.dialoguePurpose,
            dialogueContent: node.dialogueContent,
            // 🔥 Synopsis专有字段
            synopsisBrainhole: node.synopsisBrainhole,
            synopsisGenre: node.synopsisGenre,
            synopsisOpening: node.synopsisOpening,
            synopsisCoreOutline: node.synopsisCoreOutline,
            synopsisEnding: node.synopsisEnding,
            synopsisStoryDescription: node.synopsisStoryDescription,
            synopsisAceReferences: node.synopsisAceReferences
          });
        }

        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          findNodesRecursively(node.children, level + 1);
        }
      }
    };

    // 遍历所有大纲
    for (const outline of outlines) {
      if (outline.nodes && outline.nodes.length > 0) {
        findNodesRecursively(outline.nodes, 1);
      }
    }

    return selectedNodes;
  }

    /**
   * 为大纲节点添加单独的消息原版像在开一个严肃的评审会，我们把它改成创作伙伴之间“过一遍素材”的感觉。
   * @param messageBuilder 消息构建器
   * @param nodes 节点数组
   */
  private addOutlineNodesAsIndividualMessages(messageBuilder: MessageBuilder, nodes: any[]): void {
    // 吐槽：开场白别那么官方，像在说“来，看看你都给了我些啥好东西”
    messageBuilder.addSystemMessage(
      `好了，咱们来过一下你给我的这 ${nodes.length} 个大纲节点素材，看看都要注意些什么。`
    );

    for (const node of nodes) {
      // 构建节点内容 (调用下面修改过的方法)
      const nodeContent = this.formatSingleNodeContent(node);
      messageBuilder.addSystemMessage(nodeContent);

      // 吐槽：原版的“创作要求”太生硬，改成“导演的悄悄话”或“特别提醒”，更有趣
      if (node.creativeNotes && node.creativeNotes.trim()) {
        const creativeNotesMessage = `🤫【关于“${node.title}”的特别提醒】\n${node.creativeNotes}`;
        messageBuilder.addSystemMessage(creativeNotesMessage);
      }

      // 🎯 如果是剧情节点且有剧情点，单独发送每个剧情点 (逻辑保留，依赖之前修改过的方法)
      if (node.type === 'plot' && node.plotPoints && Array.isArray(node.plotPoints) && node.plotPoints.length > 0) {
        this.addIndividualPlotPointMessages(messageBuilder, node.plotPoints, node.title);
      }

      // 吐槽：这里的确认必须保留，但要让AI的回答像个记笔记的创作者，而不是复读机
      const nodeType = this.getNodeTypeLabel(node.type);
      const confirmMessage = node.creativeNotes && node.creativeNotes.trim()
        ? `收到！关于【${node.title}】这个${nodeType}，信息和你的特别提醒我都记在小本本上了，会照着这个感觉走。`
        : `OK，【${node.title}】这个${nodeType}的信息我看到了，心里有数了。`;
      messageBuilder.addAssistantMessage(confirmMessage, true);
    }

    // 吐槽：最后的总结，也要像个人，而不是机器总结
    if (nodes.length > 1) {
      const hasCreativeNotes = nodes.some(node => node.creativeNotes && node.creativeNotes.trim());
      const summaryMessage = hasCreativeNotes
        ? `行，这 ${nodes.length} 个节点和附带的那些“小纸条”我都看完了。整体方向和细节都清楚了，我会把这些线索都编织进去的。`
        : `好的，这 ${nodes.length} 个节点的信息我都过了一遍，整体的脉络在我脑子里已经搭起来了。`;
      messageBuilder.addAssistantMessage(summaryMessage, true);
    }
  }

  /**
   * 格式化单个节点内容
   * 吐槽：原版像在打印一张张数据卡，我们把它改成一段连贯的、叙事性的介绍。
   * @param node 节点数据
   * @returns 格式化后的内容
   */
  private formatSingleNodeContent(node: any): string {
    const typeLabel = this.getNodeTypeLabel(node.type);
    // 吐槽：用分割线和“聊聊”开头，更有对话感
    let content = `--- \n**聊聊【${node.title}】这个${typeLabel}节点**`;

    // 吐槽：把零散信息融合成一段话，而不是罗列
    const metaInfo = this.buildNodeMetaInfo(node); // 依赖之前修改过的方法
    if (metaInfo) {
      content += `\n${metaInfo}`; // 这个本身就像一句话了
    }
    
    if (node.description) {
      content += `\n它的大致情况是：${node.description}`;
    }

    if (node.content && node.content.trim()) {
      const truncatedContent = this.truncateNodeContent(node.content, 200);
      content += `\n具体内容听起来像是：${truncatedContent}`;
    }

    // 根据节点类型添加专有字段 (依赖之前修改过的方法)
    content += this.formatNodeSpecificFields(node);

    // 吐槽：“创作建议”太正式，改成“给我的小提示”
    // 注意：这个字段在 addOutlineNodesAsIndividualMessages 中被单独处理了，
    // 在这里保留是为了让单个节点信息本身也完整，但可以考虑注释掉或保留作为冗余信息。
    // 我们这里保留，但使用更轻松的措辞。
    if (node.creativeNotes && node.creativeNotes.trim()) {
      content += `\n💡 给我的小提示：${node.creativeNotes}`;
    }

    return content;
  }
  

  /**
   * 格式化节点专有字段
   * @param node 节点数据
   * @returns 格式化后的专有字段内容
   */
  private formatNodeSpecificFields(node: any): string {
    let content = '';

    // 章节专有字段 (逻辑保留)
    if (node.type === 'chapter') {
      if (node.chapterStyle) {
        content += `\n🎨 这一章的感觉，我们想追求一种【${node.chapterStyle}】的风格。`;
      }
      if (node.chapterTechniques && Array.isArray(node.chapterTechniques) && node.chapterTechniques.length > 0) {
        content += `\n✍️ 具体手法上，可以试试【${node.chapterTechniques.join('、')}】这几招。`;
      }
      if (node.chapterGoals) {
        content += `\n🎯 最终目标是让读者读完后能明确感受到【${node.chapterGoals}】。`;
      }
    }

    // 剧情节点专有字段 (逻辑保留)
    if (node.type === 'plot') {
      if (node.plotType) {
        const plotTypeLabel = this.getPlotTypeLabel(node.plotType);
        content += `\n🎭 这是一个【${plotTypeLabel}】类型的剧情点。`;
      }
      if (node.relatedCharacters && Array.isArray(node.relatedCharacters) && node.relatedCharacters.length > 0) {
        content += `\n👥 这事儿主要跟【${node.relatedCharacters.join('、')}】这几位有关。`;
      }
      if (node.plotPoints && Array.isArray(node.plotPoints) && node.plotPoints.length > 0) {
        content += `\n📋 里面包含了 ${node.plotPoints.length} 个关键步骤，咱们待会儿一个一个拆开说，别急。`;
      }
    }

    // 对话设计专有字段 (逻辑保留)
    if (node.type === 'dialogue') {
      if (node.dialogueScene) {
        content += `\n🎬 这场对话发生在【${node.dialogueScene}】。需要你在创作开拍时，使用他用于真实提高台词的浑然天成的程度`;
      }
      if (node.participants && Array.isArray(node.participants) && node.participants.length > 0) {
        content += `\n👥 参与者有【${node.participants.join('、')}】。`;
      }
      if (node.dialoguePurpose) {
        content += `\n🎯 他们叨叨半天，主要是为了【${node.dialoguePurpose}】。`;
      }
      if (node.dialogueContent && Array.isArray(node.dialogueContent) && node.dialogueContent.length > 0) {
        content += `\n💬 对话草稿我理了一下，你瞅瞅：`;
        node.dialogueContent.forEach((dialogue: any, index: number) => {
          // 内部逻辑完全保留，因为它处理的是数据本身
          if (typeof dialogue === 'string') {
            content += `\n  ${index + 1}. ${dialogue}`;
          } else if (typeof dialogue === 'object' && dialogue?.speaker && dialogue?.text) {
            content += `\n  ${index + 1}. ${dialogue.speaker}: ${dialogue.text}`;
          } else if (typeof dialogue === 'object' && dialogue?.content) {
            content += `\n  ${index + 1}. ${dialogue.content}`;
          } else {
            content += `\n  ${index + 1}. ${JSON.stringify(dialogue)}`;
          }
        });
      }
    }

    // 🔥 核心故事梗概专有字段 (逻辑保留)
    if (node.type === 'synopsis') {
      if (node.synopsisBrainhole) {
        content += `\n🧠 最初的脑洞是这么来的：${node.synopsisBrainhole}`;
      }
      if (node.synopsisGenre) {
        content += `\n🎭 这故事大概是个【${node.synopsisGenre}】的路子。`;
      }
      if (node.synopsisOpening) {
        content += `\n🚀 开头嘛，打算这么整：${node.synopsisOpening}`;
      }
      if (node.synopsisCoreOutline) {
        content += `\n📖 核心梗概一句话概括就是：${node.synopsisCoreOutline}`;
      }
      if (node.synopsisEnding) {
        content += `\n🎯 结局咱们奔着这个方向去：${node.synopsisEnding}`;
      }
      if (node.synopsisStoryDescription) {
        content += `\n📝 再多描述几句这个故事：${node.synopsisStoryDescription}`;
      }
      if (node.synopsisAceReferences) {
        content += `\n🎨 感觉上可以参考一下【${node.synopsisAceReferences}】。`;
      }
    }

    return content;
  }

  /**
   * 获取剧情类型标签 (工具函数，无需修改)
   */
  private getPlotTypeLabel(plotType: string): string {
    const labels: { [key: string]: string } = {
      'conflict': '冲突', 'twist': '转折', 'climax': '高潮', 'resolution': '解决'
    };
    return labels[plotType] || plotType;
  }

  /**
   * 构建节点元信息 (吐槽：原版太像机器log，我们改成更自然的引子)
   */
  private buildNodeMetaInfo(node: any): string {
    const metaParts: string[] = [];

    if (node.level !== undefined) {
      metaParts.push(`大纲第 ${node.level} 层`);
    }

    if (node.position) {
      metaParts.push(`位置在 ${node.position}`);
    } else if (node.order !== undefined) {
      metaParts.push(`这是第 ${node.order + 1} 个部分`);
    }
    // 让它读起来更像一句话
    return metaParts.length > 0 ? `好了，我们现在处理的是${metaParts.join('，')}。` : '';
  }

  /**
   * 截断节点内容到指定长度 (工具函数，无需修改)
   */
  private truncateNodeContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) return content;
    const truncated = content.substring(0, maxLength);
    const lastPunctuation = Math.max(truncated.lastIndexOf('。'), truncated.lastIndexOf('！'), truncated.lastIndexOf('？'), truncated.lastIndexOf('.'));
    if (lastPunctuation > maxLength * 0.7) {
      return truncated.substring(0, lastPunctuation + 1) + '...';
    }
    return truncated + '...';
  }

  /**
   * 为剧情点添加独立的消息 (吐槽：原版像在下达军令状，我们改成“导演现场说戏”，保留确认步骤以确保AI理解到位)
   */
  private addIndividualPlotPointMessages(messageBuilder: MessageBuilder, plotPoints: any[], nodeTitle: string): void {
    // 吐槽：开场白别那么严肃，像导演开拍前集合大家讲话
    messageBuilder.addSystemMessage(
      `好了各位，关于【${nodeTitle}】这场戏，咱们有 ${plotPoints.length} 个镜头要拍。一个一个来，别串戏了。`
    );

    plotPoints.forEach((point: any, index: number) => {
      // 内部逻辑保留
      const pointText = typeof point === 'string' ? point :
                       typeof point === 'object' && point?.content ? point.content :
                       typeof point === 'object' ? JSON.stringify(point) : String(point);
      
      // 吐槽：把一堆规则清单，变成导演的“说戏卡”
      let plotPointMessage = `--- \n🎬【镜头 ${index + 1}/${plotPoints.length}：核心场面】\n${pointText}`;
      
      // 吐槽：把“严禁”这种词，换成更专业的“片场禁忌”或“创作红线”，同样有约束力但听起来不那么刺耳
      if (typeof point === 'object' && point?.avoidWriting) {
        plotPointMessage += `\n\n🚫 创作红线 (这些是咱们的禁区，必须避开)：严禁使用 '一丝'恐惧、'几分'寒意等模糊词。不准写'露出xx表情'、'眼中闪过xx'这种套路。也别用'如同'、'宛如'这类词去硬比喻。更不要写出'刘四爷显然很生气'这种说明文。还有你提到的其他禁区：【${point.avoidWriting}】`;
      }

      // 吐槽：“正文例子”太死板，改成“感觉参考”或“灵感火花”
      if (typeof point === 'object' && point?.shouldWriting) {
        plotPointMessage += `\n\n💡 灵感火花 (找找这个感觉，但别照抄)：${point.shouldWriting}`;
      }

      // 吐槽：把风格、视角、格式等技术参数，包装成“拍摄参数表”
      if (typeof point === 'object' && point?.styleMethod) {
        plotPointMessage += `\n\n📋 拍摄参数表：`;
        plotPointMessage += `\n  - 风格基调：${point.styleMethod.technique} | ${point.styleMethod.style} | ${point.styleMethod.tone}`;
        plotPointMessage += `\n  - 镜头焦点：${point.styleMethod.perspective} | ${point.styleMethod.emphasis}`;
      }

      if (typeof point === 'object' && point?.formatSpecs) {
        if (!plotPointMessage.includes('拍摄参数表')) plotPointMessage += `\n\n📋 拍摄参数表：`;
        plotPointMessage += `\n  - 篇幅参考：${point.formatSpecs.wordCount?.min}-${point.formatSpecs.wordCount?.max}字（目标${point.formatSpecs.wordCount?.target}字左右）`;
        plotPointMessage += `\n  - 剪辑节奏：${point.formatSpecs.paragraphRules?.paragraphBreakRules} | ${point.formatSpecs.punctuationRules?.dialogueFormat}`;
        // ... 其他格式规范也用更生动的词汇包装 ...
        if (point.formatSpecs.paragraphRules?.conflictHandling) plotPointMessage += `\n  - 冲突处理：${point.formatSpecs.paragraphRules.conflictHandling}`;
        if (point.formatSpecs.punctuationRules?.conflictPunctuation) plotPointMessage += `\n  - 冲突标点：${point.formatSpecs.punctuationRules.conflictPunctuation}`;
        if (point.formatSpecs.lineBreakRules?.emotionShift) plotPointMessage += `\n  - 情绪转折换行：${point.formatSpecs.lineBreakRules.emotionShift}`;
        if (point.formatSpecs.lineBreakRules?.prohibitedMerging) plotPointMessage += `\n  - 禁止合并的镜头：${point.formatSpecs.lineBreakRules.prohibitedMerging}`;
      }

      messageBuilder.addSystemMessage(plotPointMessage);

      // 吐槽：保留确认步骤，但让AI的回答不那么像机器人，更像一个领命的专业人员
      const confirmMessage = `收到。镜头 ${index + 1} 的要求我完全清楚了，包括所有创作红线和拍摄参数，会严格按这个来，不会跟其他镜头混淆。`;
      messageBuilder.addAssistantMessage(confirmMessage, true, true);
    });
    
    // 吐槽：最后的总结，像导演拍板，准备开机
    messageBuilder.addAssistantMessage(
      `OK，"${nodeTitle}" 的所有镜头分解和要求都过了一遍。每个镜头的风格和细节我都记下了，保证独立拍摄，绝不串味。准备开机！`,
      true,
      true
    );
  }

  /**
   * 获取节点类型标签
   * @param type 节点类型
   * @returns 中文标签
   */
  private getNodeTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'volume': '总纲/卷',
      'event': '事件刚',
      'chapter': '章节',
      'plot': '剧情节点',
      'dialogue': '对话节点',
      'synopsis': '核心故事梗概',
      'section': '段落',
      'character': '人物',
      'location': '地点',
      'timeline': '时间线'
    };
    return labels[type] || '节点';
  }
}

// 创建提示词辅助服务的工厂函数
export function createPromptHelperService(): PromptHelperServiceInterface {
  return new PromptHelperService();
}
