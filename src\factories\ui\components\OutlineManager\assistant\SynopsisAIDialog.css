
/* Synopsis AI Dialog 样式 */
.synopsis-ai-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.synopsis-ai-dialog {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-out;
}

/* 选择阶段弹窗放大 */
.synopsis-ai-dialog.selecting-stage {
  width: 95%;
  max-width: 1200px;
  max-height: 85vh;
}

/* 桌面端进一步优化 */
@media (min-width: 1024px) {
  .synopsis-ai-dialog.selecting-stage {
    width: 90%;
    max-width: 1400px;
    max-height: 80vh;
  }
}

.synopsis-ai-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.synopsis-ai-dialog-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.synopsis-ai-dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* 阶段通用样式 */
.stage-header {
  margin-bottom: 24px;
  text-align: center;
}

.stage-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1f2937;
}

.stage-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.stage-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

/* 输入阶段 */
.user-input {
  width: 100%;
  min-height: 120px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s;
}

.user-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.frameworks-info {
  margin: 16px 0;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 14px;
}

.error-message {
  margin: 16px 0;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
}

.generate-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 生成中阶段 */
.streaming-content {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  min-height: 200px;
}

.streaming-text {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #6b7280;
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 选择阶段 - 横向布局 */
.core-ideas-list {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  overflow-y: hidden;
  gap: 16px;
  padding: 24px 0;
  margin-bottom: 24px;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  opacity: 0;
  animation: fadeInList 0.3s ease-out forwards;
}

@keyframes fadeInList {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条样式 */
.core-ideas-list::-webkit-scrollbar {
  height: 4px;
}

.core-ideas-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.core-ideas-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.core-ideas-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.core-idea-card {
  min-width: 320px;
  max-width: 320px;
  height: 420px;
  flex-shrink: 0;
  scroll-snap-align: start;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease-out;
  background: white;
  will-change: transform, box-shadow;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateX(50px) scale(0.95);
  animation: slideInCard 0.4s ease-out forwards;
}

/* 桌面端选择阶段卡片放大 */
@media (min-width: 1024px) {
  .synopsis-ai-dialog.selecting-stage .core-idea-card {
    min-width: 380px;
    max-width: 380px;
    height: 480px;
    padding: 24px;
  }

  .synopsis-ai-dialog.selecting-stage .core-ideas-list {
    gap: 20px;
    padding: 32px 0;
  }

  .synopsis-ai-dialog.selecting-stage .idea-field label {
    font-size: 15px;
    margin-bottom: 6px;
  }

  .synopsis-ai-dialog.selecting-stage .idea-field p {
    font-size: 14px;
    line-height: 1.5;
    -webkit-line-clamp: 4;
    line-clamp: 4;
  }

  .synopsis-ai-dialog.selecting-stage .select-button {
    padding: 10px 20px;
    font-size: 15px;
  }
}

/* 卡片依次出现动画 */
.core-idea-card:nth-child(1) { animation-delay: 0.1s; }
.core-idea-card:nth-child(2) { animation-delay: 0.2s; }
.core-idea-card:nth-child(3) { animation-delay: 0.3s; }
.core-idea-card:nth-child(4) { animation-delay: 0.4s; }
.core-idea-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateX(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.core-idea-card:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-4px);
}

.core-idea-card:active {
  transform: translateY(-2px) scale(0.98);
}

.idea-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.idea-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.idea-content {
  flex: 1;
  margin-bottom: 16px;
  overflow-y: auto;
}

.idea-field {
  margin-bottom: 12px;
}

.idea-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 14px;
}

.idea-field p {
  margin: 0;
  color: #6b7280;
  line-height: 1.4;
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.idea-actions {
  text-align: right;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.select-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-button:hover {
  background: #5a67d8;
}

/* 填充阶段 */
.selected-idea-summary {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.selected-idea-summary h4 {
  margin: 0 0 12px 0;
  color: #0369a1;
  font-size: 16px;
}

.selected-idea-summary p {
  margin: 8px 0;
  color: #0c4a6e;
  font-size: 14px;
}

.synopsis-fields-preview {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.synopsis-fields-preview h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
}

.field-preview {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.field-preview strong {
  display: block;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.field-preview p {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 按钮样式 */
.back-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #4b5563;
}

.complete-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.complete-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 预览阶段时隐藏背景内容 */
.synopsis-ai-dialog.previewing-stage .selecting-stage {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-out;
  transform: scale(0.95);
}

/* 预览阶段时弹窗样式调整 */
.synopsis-ai-dialog.previewing-stage {
  background: transparent;
  box-shadow: none;
  border: none;
}

/* 预览阶段时弹窗头部隐藏 */
.synopsis-ai-dialog.previewing-stage .synopsis-ai-dialog-header {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-out;
}

/* 预览阶段时弹窗内容区域相对定位 */
.synopsis-ai-dialog.previewing-stage .synopsis-ai-dialog-content {
  position: relative;
  background: transparent;
  padding: 0;
}

/* 选择阶段的默认过渡 */
.selecting-stage {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* 预览模态框样式 */
.idea-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  animation: overlayFadeIn 0.3s ease-out forwards;
}

@keyframes overlayFadeIn {
  to {
    opacity: 1;
  }
}

.idea-preview-modal {
  width: 480px;
  max-width: 90vw;
  max-height: 80vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  animation: modalSlideIn 0.3s ease-out 0.1s forwards;
}

@keyframes modalSlideIn {
  to {
    transform: scale(1) translateY(0);
  }
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.preview-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.preview-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.preview-field {
  margin-bottom: 20px;
  opacity: 0;
  transform: translateY(10px);
  animation: contentSlideIn 0.3s ease-out forwards;
}

.preview-field:nth-child(1) { animation-delay: 0.2s; }
.preview-field:nth-child(2) { animation-delay: 0.3s; }
.preview-field:nth-child(3) { animation-delay: 0.4s; }

@keyframes contentSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 16px;
}

.preview-field .full-text {
  color: #6b7280;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.preview-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.confirm-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.save-direction-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.save-direction-btn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.preview-main-actions {
  display: flex;
  gap: 12px;
}

/* 已保存方向选择器样式 */
.saved-directions-button {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.saved-directions-button:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.saved-directions-selector {
  margin-top: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selector-header {
  padding: 20px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.selector-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.selector-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.saved-directions-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
}

.saved-direction-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.saved-direction-card:hover {
  border-color: #8b5cf6;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
  transform: translateY(-2px);
}

.saved-direction-card:last-child {
  margin-bottom: 0;
}

.direction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.direction-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.direction-usage {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.direction-content {
  margin-bottom: 12px;
}

.direction-content p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.direction-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.direction-tag {
  background: #ede9fe;
  color: #7c3aed;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.selector-actions {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: right;
}

.close-selector-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-selector-button:hover {
  background: #4b5563;
}

/* 方向选择模态框样式 */
.direction-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  opacity: 0;
  animation: overlayFadeIn 0.3s ease-out forwards;
}

.direction-modal {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  animation: modalSlideIn 0.3s ease-out 0.1s forwards;
}

.direction-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.direction-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.direction-modal-search {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.direction-search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.direction-search-input:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.direction-modal-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.direction-stats {
  padding: 16px 24px;
  font-size: 13px;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.direction-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.direction-item {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.direction-item:hover {
  border-color: #8b5cf6;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
}

.direction-item:last-child {
  margin-bottom: 0;
}

.direction-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.direction-name {
  font-weight: 600;
  color: #374151;
  font-size: 16px;
}

.direction-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.direction-usage {
  font-size: 12px;
  color: #8b5cf6;
  background: #ede9fe;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.direction-date {
  font-size: 11px;
  color: #9ca3af;
}

.direction-item-content {
  margin-bottom: 16px;
}

.direction-item-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.direction-item-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.direction-genre {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.direction-keyword {
  background: #f3f4f6;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.no-directions {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
  font-size: 14px;
}

.direction-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: right;
}

.direction-modal-close {
  background: #6b7280;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.direction-modal-close:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* 定制化阶段样式 */
.customizing-stage {
  padding: 24px;
}

.selected-idea-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.selected-idea-summary h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
}

.selected-idea-summary p {
  margin: 8px 0;
  color: #6b7280;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.customization-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.customization-category {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.category-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  padding: 16px 20px;
}

.category-header h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.category-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.category-options {
  padding: 16px;
  display: grid;
  gap: 12px;
}

.customization-option {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.customization-option:hover {
  border-color: #8b5cf6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

.customization-option.selected {
  border-color: #8b5cf6;
  background: #f3f4f6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
}

.option-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 15px;
}

.option-description {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

.stage-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.back-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.continue-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.continue-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.continue-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 定制化选择操作区域 */
.customization-actions {
  margin: 24px 0;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.action-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.load-customization-button,
.save-customization-button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.load-customization-button:hover,
.save-customization-button:hover:not(:disabled) {
  background: #4f46e5;
  transform: translateY(-1px);
}

.save-customization-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* 定制化选择模态框样式 */
.customization-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  opacity: 0;
  animation: overlayFadeIn 0.3s ease-out forwards;
}

.customization-modal {
  width: 700px;
  max-width: 90vw;
  max-height: 80vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  animation: modalSlideIn 0.3s ease-out 0.1s forwards;
}

.customization-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.customization-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.customization-modal-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.customization-stats {
  padding: 16px 24px;
  font-size: 13px;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.customization-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.customization-item {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.customization-item:hover {
  border-color: #6366f1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

.customization-item:last-child {
  margin-bottom: 0;
}

.customization-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.customization-name {
  font-weight: 600;
  color: #374151;
  font-size: 16px;
}

.customization-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.customization-usage {
  font-size: 12px;
  color: #6366f1;
  background: #e0e7ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.customization-date {
  font-size: 11px;
  color: #9ca3af;
}

.customization-item-description {
  margin-bottom: 12px;
}

.customization-item-description p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.customization-item-details {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.customization-detail {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.no-customizations {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
  font-size: 14px;
}

.customization-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: right;
}

.customization-modal-close {
  background: #6b7280;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.customization-modal-close:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1023px) {
  .core-idea-card {
    min-width: 280px;
    max-width: 280px;
    height: 380px;
  }

  .core-ideas-list {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .synopsis-ai-dialog {
    width: 95%;
    max-height: 95vh;
  }

  .synopsis-ai-dialog-content {
    padding: 16px;
  }

  .core-idea-card {
    min-width: 260px;
    max-width: 260px;
    height: 360px;
    padding: 16px;
  }

  .core-ideas-list {
    gap: 8px;
    padding: 16px 0;
  }

  .stage-actions {
    flex-direction: column;
  }

  .idea-preview-modal {
    width: 95vw;
    max-height: 90vh;
  }

  .preview-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .direction-modal {
    width: 95vw;
    max-height: 85vh;
  }

  .direction-modal-header {
    padding: 20px;
  }

  .direction-modal-search {
    padding: 16px 20px;
  }

  .direction-list {
    padding: 12px 20px;
  }

  .direction-item {
    padding: 16px;
  }

  .direction-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .direction-meta {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  .customizing-stage {
    padding: 16px;
  }

  .selected-idea-summary {
    padding: 16px;
    margin-bottom: 20px;
  }

  .category-header {
    padding: 12px 16px;
  }

  .category-options {
    padding: 12px;
  }

  .customization-option {
    padding: 12px;
  }

  .stage-actions {
    flex-direction: column;
    gap: 12px;
    margin-top: 24px;
  }

  .back-button,
  .continue-button {
    width: 100%;
  }

  .action-group {
    flex-direction: column;
  }

  .load-customization-button,
  .save-customization-button {
    width: 100%;
    justify-content: center;
  }

  .customization-modal {
    width: 95vw;
    max-height: 85vh;
  }

  .customization-modal-header {
    padding: 20px;
  }

  .customization-list {
    padding: 12px 20px;
  }

  .customization-item {
    padding: 16px;
  }

  .customization-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .customization-meta {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .core-idea-card {
    min-width: 240px;
    max-width: 240px;
    height: 340px;
    padding: 14px;
  }

  .idea-field p {
    font-size: 12px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
}
