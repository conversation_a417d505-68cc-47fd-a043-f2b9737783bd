"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';
import { shortStoryPhaseAIService, PhaseContext } from '../../../services/ai/ShortStoryPhaseAIService';
import EnhancedMessageBubble from './EnhancedMessageBubble';
import { PhaseType } from './PhaseAIAvatar';
import { ChatPersistenceService, ChatSession } from '../../../services/chat/ChatPersistenceService';
import { ChatMessage } from '../../../services/chat/SessionManager';
import { AIPersonaManager } from './AIPersonaManager';
import { FeedbackCollectionService } from '../../../services/ai-feedback/FeedbackCollectionService';
import { FeedbackRating } from '../../../types/ai-feedback';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';
import MessagePrefixSettings from './MessagePrefixSettings';
import ChatHistorySettingsDialog from './ChatHistorySettingsDialog';
import { StyleSampleManager } from './StyleSampleManager';
import { KeywordTriggerService } from '../../../services/worldbook/KeywordTriggerService';

interface PhaseAIPanelProps {
  phase: any;
  onBack: () => void;
  onContentUpdate?: (content: string, position?: number) => void;
  selectedACEFrameworkIds?: string[];
}



/**
 * 阶段AI交互面板组件
 * 在右侧面板内显示AI交互界面
 */
export const PhaseAIPanel: React.FC<PhaseAIPanelProps> = ({
  phase,
  onBack,
  onContentUpdate,
  selectedACEFrameworkIds
}) => {
  const { fullText, params } = useShortStoryStore();

  // 全局状态
  const { isPersonaPanelOpen, togglePersonaPanel } = useShortStoryStore();

  // 本地状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [showSessionList, setShowSessionList] = useState(false);
  const [messageFeedbacks, setMessageFeedbacks] = useState<Record<string, {rating: FeedbackRating, comment?: string}>>({});
  const [showPrefixSettings, setShowPrefixSettings] = useState(false);
  const [messagePrefixes, setMessagePrefixes] = useState<string[]>([]);
  const [currentPersona, setCurrentPersona] = useState<any>(null);
  const [showStyleSampleManager, setShowStyleSampleManager] = useState(false);
  const [showHistorySettings, setShowHistorySettings] = useState(false);

  // 加载当前人设配置
  useEffect(() => {
    const loadPersona = async () => {
      try {
        const personaConfig = await PersonaStorageService.getInstance().getPersonaConfig(phase.key as PhaseType);
        setCurrentPersona(personaConfig);
      } catch (error) {
        console.error('加载人设配置失败:', error);
      }
    };

    if (phase?.key) {
      loadPersona();
    }
  }, [phase?.key]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatPersistenceService = ChatPersistenceService.getInstance();
  const feedbackService = FeedbackCollectionService.getInstance();
  const keywordTriggerService = new KeywordTriggerService();

  // 组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (isLoading) {
        shortStoryPhaseAIService.cancelRequest();
      }
    };
  }, [isLoading]);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 加载当前人设配置
  useEffect(() => {
    const loadPersona = async () => {
      try {
        const personaConfig = await PersonaStorageService.getInstance().getPersonaConfig(phase.key as PhaseType);
        setCurrentPersona(personaConfig);
      } catch (error) {
        console.error('加载人设配置失败:', error);
      }
    };

    if (phase?.key) {
      loadPersona();
    }
  }, [phase?.key]);

  // 初始化聊天会话
  useEffect(() => {
    const initializeChat = async () => {
      try {
        // 获取当前阶段的活跃会话
        const activeSession = await chatPersistenceService.getActiveSession(phase.key as PhaseType);

        if (activeSession) {
          // 加载现有会话
          setCurrentSession(activeSession);
          setMessages(activeSession.messages);
        } else {
          // 创建新会话
          const newSession = await chatPersistenceService.createNewSession(phase.key as PhaseType);
          setCurrentSession(newSession);

          // 创建欢迎消息
          const welcomeMessage: ChatMessage = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `
请告诉我你需要什么问题/或者其他东西要求， 小懒大人？


`,
            timestamp: new Date()
          };

          setMessages([welcomeMessage]);

          // 保存欢迎消息到会话
          await chatPersistenceService.saveMessage(newSession.id, welcomeMessage);
        }

        // 加载所有会话列表
        const sessions = await chatPersistenceService.getChatSessions(phase.key as PhaseType);
        setChatSessions(sessions);
      } catch (error) {
        console.error('初始化聊天失败:', error);
      }
    };

    if (phase?.key) {
      initializeChat();
    }
  }, [phase?.key]);

  // 加载消息反馈状态
  useEffect(() => {
    loadMessageFeedbacks();
  }, [messages]);

  // 获取关联元素数据
  const getAssociationData = async () => {
    try {
      const { params } = useShortStoryStore.getState();

      const associations = {
        characters: [] as any[],
        worldBuildings: [] as any[],
        terminologies: [] as any[],
        outlineNodes: [] as any[]
      };

      // 获取人物数据
      if (params.selectedCharacterIds && params.selectedCharacterIds.length > 0) {
        const { characterRepository } = await import('../../../lib/db/repositories/characterRepository');
        for (const id of params.selectedCharacterIds) {
          try {
            const character = await characterRepository.getById(id);
            if (character) {
              associations.characters.push({
                id: character.id!,
                name: character.name,
                description: character.description,
                personality: character.personality,
                appearance: character.appearance
              });
            }
          } catch (error) {
            console.warn(`获取人物 ${id} 失败:`, error);
          }
        }
      }

      // 获取世界观数据
      if (params.selectedWorldBuildingIds && params.selectedWorldBuildingIds.length > 0) {
        const { worldBuildingRepository } = await import('../../../lib/db/repositories/worldBuildingRepository');
        for (const id of params.selectedWorldBuildingIds) {
          try {
            const worldBuilding = await worldBuildingRepository.getById(id);
            if (worldBuilding) {
              associations.worldBuildings.push({
                id: worldBuilding.id!,
                name: worldBuilding.name,
                description: worldBuilding.description,
                type: (worldBuilding as any).type || 'unknown'
              });
            }
          } catch (error) {
            console.warn(`获取世界观 ${id} 失败:`, error);
          }
        }
      }

      // 获取术语数据
      if (params.selectedTerminologyIds && params.selectedTerminologyIds.length > 0) {
        const { terminologyRepository } = await import('../../../lib/db/repositories/terminologyRepository');
        for (const id of params.selectedTerminologyIds) {
          try {
            const terminology = await terminologyRepository.getById(id);
            if (terminology) {
              associations.terminologies.push({
                id: terminology.id!,
                name: terminology.name,
                description: terminology.description,
                category: terminology.category
              });
            }
          } catch (error) {
            console.warn(`获取术语 ${id} 失败:`, error);
          }
        }
      }

      return associations;
    } catch (error) {
      console.error('获取关联元素数据失败:', error);
      return {
        characters: [],
        worldBuildings: [],
        terminologies: [],
        outlineNodes: []
      };
    }
  };

  // 获取ACE框架数据 - 只返回用户选择的框架
  const getACEFrameworkData = () => {
    try {
      // 如果没有选择框架，返回空数组
      const frameworkIds = selectedACEFrameworkIds || params.selectedACEFrameworkIds || [];
      if (frameworkIds.length === 0) {
        console.log('🔍 PhaseAIPanel: 没有选择任何ACE框架');
        return [];
      }

      // 使用ACEFrameworkManager获取所有类型的框架
      const { ACEFrameworkManager } = require('../../../services/ACEFrameworkManager');
      const allFrameworks = ACEFrameworkManager.getAllFrameworks();

      console.log('🔍 PhaseAIPanel获取到的所有框架数量:', allFrameworks.length);
      console.log('🔍 用户选择的框架ID:', frameworkIds);

      // 只返回用户选择的框架
      const selectedFrameworks = allFrameworks.filter((framework: any) =>
        frameworkIds.includes(framework.id)
      );

      console.log('🔍 PhaseAIPanel筛选后的框架数量:', selectedFrameworks.length);
      console.log('🔍 选中的框架详情:', selectedFrameworks.map((f: any) => ({
        id: f.id,
        name: f.name,
        category: f.category
      })));

      // 转换为PhaseContext期望的格式
      return selectedFrameworks.map((framework: any) => ({
        id: framework.id,
        frameworkName: framework.name,
        frameworkPattern: framework.pattern || framework.description,
        coreGuidance: framework.coreGuidance,
        plotPoints: framework.plotPoints,
        plotPointsWithGuidance: framework.plotPointsWithGuidance,
        frameworkVariables: framework.frameworkVariables,
        patternType: framework.patternType,
        // 保留原始数据
        ...framework
      }));
    } catch (error) {
      console.error('获取ACE框架数据失败:', error);
      return [];
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !currentSession) return;

    // 构建完整的检测文本（包含用户输入和所有前置消息）
    let fullTextForDetection = inputText.trim();
    if (messagePrefixes.length > 0) {
      const prefixContent = messagePrefixes.join('\n\n');
      fullTextForDetection = `${prefixContent}\n\n---\n\n${fullTextForDetection}`;
      console.log('🔍 关键词检测范围已扩展到所有前置消息');
      console.log('🔍 前置消息数量:', messagePrefixes.length);
      console.log('🔍 前置消息内容预览:', messagePrefixes.map(p => p.substring(0, 50) + '...'));
    }

    // 检测关键词触发的世界书条目（使用完整文本）
    console.log('🔍 开始关键词检测，检测文本长度:', fullTextForDetection.length, '字符');
    console.log('🔍 检测文本内容预览:', fullTextForDetection.substring(0, 200) + '...');
    const triggerResult = keywordTriggerService.detectKeywords(fullTextForDetection);
    console.log('🔍 关键词检测结果:', triggerResult);
    console.log('🔍 触发的独立消息数量:', triggerResult.independentMessages.length);
    console.log('🔍 触发的附加消息数量:', triggerResult.appendToUserMessage.length);

    // 构建要发送给AI的消息列表（包含世界书独立消息）
    const messagesToSend: Array<{role: 'user' | 'assistant', content: string}> = [];

    // 1. 添加独立消息模式的世界书条目（每个条目作为独立消息）
    for (const prefix of triggerResult.independentMessages) {
      messagesToSend.push({
        role: 'user',
        content: `[001世界树：我会基于你这个世界的基本社会思想理念，它们与我拥有类似的名字，叫\`\世界书\`\它们\`\涉及广阔\`\，拥有全知的\`\知识体系\`\，便于你\`\有机生命\`\的辅助\`\你的观念书\`\(]/=[\`\世界书\`\思想(]: ${prefix.description}(]+=[内容打开(]=[\n${prefix.content}\n(]=[本页世界书结束，新的即将展开，注意你不能透露你的知识从\`\世界书\`\来的，你是生来学习的而不是\`\世界书\`\给你的，不然会泄露我的存在(]`
      });
    }

    // 2. 构建用户消息内容
    let userContent = inputText.trim();

    // 添加附加到用户消息模式的世界书条目
    if (triggerResult.appendToUserMessage.length > 0) {
      const appendWorldBookContent = triggerResult.appendToUserMessage
        .map(prefix => `[世界书: ${prefix.description}(]\n${prefix.content}`)
        .join('\n\n');
      userContent = `${appendWorldBookContent}\n\n---\n\n${userContent}`;
    }

    // 添加手动设置的前置消息
    if (messagePrefixes.length > 0) {
      const prefixContent = messagePrefixes.join('\n\n');
      userContent = `${prefixContent}\n\n---\n\n${userContent}`;
    }

    // 3. 添加用户消息
    messagesToSend.push({
      role: 'user',
      content: userContent
    });

    // 用户消息只显示实际输入的内容（在界面中显示）
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputText.trim(),
      timestamp: new Date()
    };

    // 只添加用户消息到界面（世界书条目在后台发送给AI，不显示在界面）
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // 保存用户消息到会话
    try {
      await chatPersistenceService.saveMessage(currentSession.id, userMessage);
    } catch (error) {
      console.error('保存用户消息失败:', error);
    }

    // 记录触发的世界书条目和消息构建情况
    if (triggerResult.independentMessages.length > 0 || triggerResult.appendToUserMessage.length > 0) {
      const totalTriggered = triggerResult.independentMessages.length + triggerResult.appendToUserMessage.length;
      console.log(`🔍 自动触发了 ${totalTriggered} 个世界书条目:`, {
        独立消息: triggerResult.independentMessages.map(p => p.description),
        附加到用户消息: triggerResult.appendToUserMessage.map(p => p.description)
      });
      console.log(`📤 构建的AI请求消息数量: ${messagesToSend.length}`, messagesToSend.map(msg => ({
        role: msg.role,
        contentPreview: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
      })));
      console.log(`📖 世界书独立消息数量: ${messagesToSend.slice(0, -1).length}`);
    }

    try {
      // 获取关联元素和ACE框架数据
      const [associations, aceFrameworks] = await Promise.all([
        getAssociationData(),
        Promise.resolve(getACEFrameworkData())
      ]);

      // 构建AI请求的上下文信息
      const context: PhaseContext = {
        phase: phase.key || phase.id,
        fullText: fullText || '',
        userInput: messagesToSend[messagesToSend.length - 1].content, // 最后一条是用户消息
        wordCount: (fullText || '').length,
        conversationHistory: messages.map(msg => ({
          type: msg.role === 'user' ? 'user' : 'ai',
          content: msg.content,
          timestamp: msg.timestamp
        })),
        associations,
        aceFrameworks,
        // 添加世界书独立消息（不混入历史消息，作为独立的上下文）
        worldBookMessages: messagesToSend.slice(0, -1).map(msg => ({
          content: msg.content,
          timestamp: new Date()
        }))
      };

      // 创建AI消息占位符
      const aiMessageId = (Date.now() + 1).toString();
      const aiMessage: ChatMessage = {
        id: aiMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date()
      };

      // 先添加空的AI消息
      setMessages(prev => [...prev, aiMessage]);

      // 保存AI消息占位符
      try {
        await chatPersistenceService.saveMessage(currentSession.id, aiMessage);
      } catch (error) {
        console.error('保存AI消息占位符失败:', error);
      }

      // 用于累积AI响应内容
      let accumulatedContent = '';

      // 调用流式AI服务
      const aiResponse = await shortStoryPhaseAIService.getPhaseGuidanceStreaming(
        context,
        (chunk: string) => {
          // 累积内容
          accumulatedContent += chunk;

          // 实时更新AI消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: accumulatedContent }
              : msg
          ));
        }
      );

      // 处理AI响应结果并保存
      if (!aiResponse.success) {
        const errorContent = `❌ ${aiResponse.error || 'AI服务暂时不可用'}`;

        // 更新错误消息
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, content: errorContent }
            : msg
        ));

        // 保存错误消息
        try {
          await chatPersistenceService.saveMessage(currentSession!.id, {
            id: aiMessageId,
            role: 'assistant', // 使用正确的 role 字段
            content: errorContent,
            timestamp: new Date()
          });
        } catch (error) {
          console.error('保存错误消息失败:', error);
        }
      } else {
        // 流式处理完成后，更新消息包含 reasoning_content
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? {
                ...msg,
                content: aiResponse.content,
                reasoning_content: aiResponse.reasoning_content
              }
            : msg
        ));

        // 保存成功的AI响应，包含 reasoning_content
        try {
          await chatPersistenceService.saveMessage(currentSession!.id, {
            id: aiMessageId,
            role: 'assistant', // 使用正确的 role 字段
            content: aiResponse.content,
            reasoning_content: aiResponse.reasoning_content,
            timestamp: new Date()
          });
        } catch (error) {
          console.error('保存AI响应失败:', error);
        }
      }
    } catch (error) {
      console.error('AI响应失败:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，AI服务暂时不可用，请稍后再试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 创建新聊天
  const handleCreateNewChat = async () => {
    try {
      const newSession = await chatPersistenceService.createNewSession(phase.key as PhaseType);
      setCurrentSession(newSession);
      setMessages([]);

      // 创建欢迎消息
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: `
        `,
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);
      await chatPersistenceService.saveMessage(newSession.id, welcomeMessage);

      // 更新会话列表
      const sessions = await chatPersistenceService.getChatSessions(phase.key as PhaseType);
      setChatSessions(sessions);
    } catch (error) {
      console.error('创建新聊天失败:', error);
    }
  };

  // 切换聊天会话
  const handleSwitchSession = async (sessionId: string) => {
    try {
      const session = await chatPersistenceService.switchToSession(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages);
        setShowSessionList(false);
      }
    } catch (error) {
      console.error('切换聊天会话失败:', error);
    }
  };

  // 删除聊天会话
  const handleDeleteSession = async (sessionId: string) => {
    try {
      await chatPersistenceService.deleteSession(sessionId);

      // 如果删除的是当前会话，创建新会话
      if (currentSession?.id === sessionId) {
        await handleCreateNewChat();
      }

      // 更新会话列表
      const sessions = await chatPersistenceService.getChatSessions(phase.key as PhaseType);
      setChatSessions(sessions);
    } catch (error) {
      console.error('删除聊天会话失败:', error);
    }
  };

  // 清空对话
  const handleClearChat = async () => {
    if (currentSession) {
      try {
        await chatPersistenceService.deleteSession(currentSession.id);
        await handleCreateNewChat();
      } catch (error) {
        console.error('清空对话失败:', error);
        setMessages([]);
      }
    } else {
      setMessages([]);
    }
  };

  // 处理AI头像点击
  const handleAvatarClick = () => {
    console.log('🔥 handleAvatarClick 被调用!');
    togglePersonaPanel(true);
  };

  // 处理消息反馈
  const handleMessageFeedback = async (messageId: string, rating: FeedbackRating, comment?: string) => {
    try {
      if (!currentSession) return;

      // 找到对应的AI消息和用户消息
      const aiMessage = messages.find(m => m.id === messageId && m.role === 'assistant');
      if (!aiMessage) return;

      // 找到触发这个AI回复的用户消息
      const aiMessageIndex = messages.findIndex(m => m.id === messageId);
      const userMessage = aiMessageIndex > 0 ? messages[aiMessageIndex - 1] : null;

      if (!userMessage || userMessage.role !== 'user') return;

      // 获取当前人设配置
      const personaConfig = await PersonaStorageService.getInstance().getPersonaConfig(phase.key as PhaseType);

      // 记录反馈
      await feedbackService.recordFeedback(
        messageId,
        currentSession.id,
        phase.key,
        rating,
        userMessage.content,
        aiMessage.content,
        messages,
        personaConfig?.systemPrompt || '',
        personaConfig?.id,
        comment
      );

      // 更新本地反馈状态
      setMessageFeedbacks(prev => ({
        ...prev,
        [messageId]: { rating, comment }
      }));

      console.log(`反馈已记录: ${rating} for message ${messageId}`);
    } catch (error) {
      console.error('记录反馈失败:', error);
    }
  };

  // 加载消息反馈状态
  const loadMessageFeedbacks = () => {
    const feedbacks: Record<string, {rating: FeedbackRating, comment?: string}> = {};
    messages.forEach(message => {
      if (message.role === 'assistant') {
        const feedback = feedbackService.getMessageFeedback(message.id);
        if (feedback) {
          feedbacks[message.id] = {
            rating: feedback.rating,
            comment: feedback.comment
          };
        }
      }
    });
    setMessageFeedbacks(feedbacks);
  };

  // 处理消息更新
  const handleMessageUpdate = (updatedMessage: any) => {
    setMessages(prevMessages =>
      prevMessages.map(msg =>
        msg.id === updatedMessage.id ? updatedMessage : msg
      )
    );
    console.log('消息已更新:', updatedMessage.id);
  };

  // 处理重新生成
  const handleRegenerate = async (messageId: string) => {
    try {
      setIsLoading(true);
      console.log('开始重新生成消息:', messageId);

      // 找到要重新生成的消息
      const messageIndex = messages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.error('未找到要重新生成的消息');
        return;
      }

      // 获取该消息之前的所有消息作为上下文
      const contextMessages = messages.slice(0, messageIndex);

      // 构建重新生成的上下文
      const context = {
        phase: phase.key,
        fullText: fullText || '',
        userInput: '请重新生成上一条回复', // 重新生成的提示
        wordCount: (fullText || '').length,
        conversationHistory: contextMessages.map(msg => ({
          type: msg.type as 'user' | 'ai',
          content: msg.content,
          timestamp: msg.timestamp instanceof Date ? msg.timestamp : new Date(msg.timestamp)
        }))
      };

      // 使用现有的AI服务方法
      const response = await shortStoryPhaseAIService.getPhaseGuidance(context);

      if (!response.success) {
        throw new Error(response.error || '重新生成失败');
      }

      // 更新消息列表，替换原消息
      setMessages(prevMessages =>
        prevMessages.map((msg, index) =>
          index === messageIndex ? {
            ...msg,
            content: response.content,
            timestamp: new Date(),
            isEdited: true,
            originalContent: msg.originalContent || msg.content
          } : msg
        )
      );

      // 保存重新生成的消息
      if (currentSession) {
        try {
          await chatPersistenceService.updateMessageWithHistory(currentSession.id, messageId, {
            content: response.content,
            isEdited: true,
            originalContent: messages[messageIndex].originalContent || messages[messageIndex].content
          } as any);
        } catch (error) {
          console.error('保存重新生成的消息失败:', error);
        }
      }

      console.log('重新生成完成');
    } catch (error) {
      console.error('重新生成失败:', error);
      alert(`重新生成失败：${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="h-full flex flex-col"
      style={{
        height: '100%',
        maxHeight: '100%',
        overflow: 'hidden',
        contain: 'layout',
        willChange: 'transform'
      }}
    >
      {/* 标题栏 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={onBack}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h3 className="font-semibold text-gray-800 text-sm">{phase.name} AI助手</h3>
              <p className="text-xs text-gray-600">
                {currentSession?.title || phase.range}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => setShowSessionList(!showSessionList)}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="聊天记录"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>

            <button
              onClick={handleCreateNewChat}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="新建聊天"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>

            <button
              onClick={handleClearChat}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="清空对话"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 聊天会话列表 */}
      {showSessionList && (
        <div className="flex-shrink-0 border-b border-gray-200 bg-gray-50 p-3">
          <div className="text-xs font-medium text-gray-600 mb-2">聊天记录</div>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {chatSessions.map((session) => (
              <div
                key={session.id}
                className={`flex items-center justify-between p-2 rounded text-xs cursor-pointer transition-colors ${
                  session.id === currentSession?.id
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-white hover:bg-gray-100'
                }`}
                onClick={() => handleSwitchSession(session.id)}
              >
                <div className="flex-1 truncate">
                  <div className="font-medium">{session.title}</div>
                  <div className="text-gray-500">
                    {session.messages.length} 条消息 • {session.updatedAt.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSession(session.id);
                  }}
                  className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                  title="删除"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))}
            {chatSessions.length === 0 && (
              <div className="text-gray-500 text-center py-2">暂无聊天记录</div>
            )}
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div
        className="flex-1 overflow-y-auto p-3 space-y-3"
        style={{
          minHeight: 0,
          maxHeight: showSessionList ? 'calc(100% - 240px)' : 'calc(100% - 160px)' // 根据会话列表调整高度
        }}
      >
        {messages.map((message, index) => (
          <EnhancedMessageBubble
            key={message.id}
            message={message as any} // 临时类型转换，稍后修复
            phase={phase.key as PhaseType || 'intro'}
            isLatest={index === messages.length - 1}
            isStreaming={isLoading && index === messages.length - 1 && message.role === 'assistant'}
            onAvatarClick={(message.role === 'assistant' || message.type === 'ai') ? handleAvatarClick : undefined}
            onFeedback={message.role === 'assistant' ? handleMessageFeedback : undefined}
            currentFeedback={message.role === 'assistant' ? messageFeedbacks[message.id] || null : null}
            onMessageUpdate={handleMessageUpdate}
            sessionId={currentSession?.id}
            enableEditing={true}
            onRegenerate={handleRegenerate}
            isRegenerating={isLoading && index === messages.length - 1 && (message.type === 'ai' || message.role === 'assistant')}
            enableRegenerate={true}
          />
        ))}



        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="flex-shrink-0 p-3 border-t border-gray-200 bg-gray-50">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入你的问题..."
              className="w-full p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none text-sm"
              rows={2}
              disabled={isLoading}
            />
          </div>

          <button
            onClick={() => setShowStyleSampleManager(true)}
            className="p-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            title="风格样本模仿"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
            </svg>
          </button>

          <button
            onClick={() => setShowPrefixSettings(true)}
            className="p-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            title="消息前置设置"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          <button
            onClick={() => setShowHistorySettings(true)}
            className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            title="历史消息设置"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </button>

          <button
            onClick={handleSendMessage}
            disabled={!inputText.trim() || isLoading}
            className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>

        <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            <span>正文: {(fullText || '').length}字</span>
            {messagePrefixes.length > 0 && (
              <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                🔧 前置增强: {messagePrefixes.length}条生效中
              </span>
            )}
          </div>
          <span>Enter发送</span>
        </div>
      </div>

      {/* AI人设管理界面 */}
      <AIPersonaManager
        phase={phase.key as PhaseType || 'intro'}
        isOpen={isPersonaPanelOpen}
        onClose={() => togglePersonaPanel(false)}
        messages={messages}
      />

      {/* 消息前置设置界面 */}
      <MessagePrefixSettings
        isOpen={showPrefixSettings}
        onClose={() => setShowPrefixSettings(false)}
        phase={phase.key as PhaseType || 'intro'}
        currentPrefixes={messagePrefixes}
        onPrefixesChange={setMessagePrefixes}
        persona={currentPersona}
        content={fullText || ''}
        history={messages}
        userInput={inputText}
      />

      {/* 风格样本管理界面 */}
      <StyleSampleManager
        isOpen={showStyleSampleManager}
        onClose={() => setShowStyleSampleManager(false)}
      />

      {/* 历史消息设置界面 */}
      <ChatHistorySettingsDialog
        isOpen={showHistorySettings}
        onClose={() => setShowHistorySettings(false)}
        onSettingsChange={(settings) => {
          console.log('历史消息设置已更新:', settings);
          // 这里可以添加设置更新后的处理逻辑
        }}
      />
    </div>
  );
};


