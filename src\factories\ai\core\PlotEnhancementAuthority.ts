/**
 * 剧情点增强权威管理类
 * 
 * 专门处理剧情点详细化、ACE技巧学习、创作核心驱动等增强功能
 * 与WritingGuidanceAuthority分离，避免混淆
 */

import { ConversationMessage } from '../types/SharedTypes';

export class PlotEnhancementAuthority {
  private static instance: PlotEnhancementAuthority;

  private constructor() {}

  public static getInstance(): PlotEnhancementAuthority {
    if (!PlotEnhancementAuthority.instance) {
      PlotEnhancementAuthority.instance = new PlotEnhancementAuthority();
    }
    return PlotEnhancementAuthority.instance;
  }

  /**
   * 构建剧情点详细化要求消息
<answer_operator>
  /**
   * 构建剧情点详细化要求的消息
   */
  buildPlotDetailMessage(): ConversationMessage {
    return {
      role: 'user',
      content: `【怎么让剧情“刀刀见血”】

朋友，咱们得在同一个频道上。我之前可能说得太泛了，让我们用一个具体的、有力的例子来校准一下我们的标准。你提供的这个例子就非常好，直接、紧张、信息量爆炸，把它作为我们的“黄金标准”：

---
**【黄金标准示例 - 由你提供】**

1.  承接剧情点：开场即是危机，[外部威胁（女鬼）]正在撞击[隐藏地点（房间门）]，角色们（[凌风]、[眼镜男]等）处于恐惧和绝望中。眼镜男表现颓废，但[凌风]眼神锐利，暗示其不同寻常。
2.  独立剧情点1（智斗与揭秘）：[凌风]质问[眼镜男]为何在危急时刻不锁门，并进行逻辑推理：[眼镜男]心思缜密，第一个进房，却没锁门，而后面时间更紧迫的人都锁了门；[眼镜男]在一楼听到声音就异常急切地让大家跑，反应反常；[眼镜男]前后对"鬼在我们中间"的态度转变。通过一连串的质问和细节分析，[凌风]逐步揭示[眼镜男]是隐藏的"鬼"。
3.  独立剧情点2（摊牌与反转）：面对[凌风]的步步紧逼，[眼镜男]从辩解到冷笑，最终承认身份。他的外表开始变化（溢出黑气），声音变得浑浊，并威胁要杀死[凌风]。[凌风]表明自己是赌博，并推断[眼镜男]不会轻易杀他，因为这不"好玩"，戳中对方痛点。

---
看到了吗？这就是我们要的“有效剧情”。当然，这是一种“开场即高潮”的玩法，非常适合强悬疑和快节奏的文。如果咱们写的是那种需要慢慢铺垫的短篇，或者角色间需要“高超拉扯”的感情戏，那“烈度”的表现方式会不同，但**“每一笔都有用”**这个核心原则是不变的。

**所以，基于这个共识，咱们定几个活的规矩：**

**🔗 咱们的铁律：**
⚠️ **有剧情就必须有章节，有章节就必须有剧情！** ⚠️
-   别让剧情点在外面流浪，必须把它塞进一个章节里。
-   也别搞个空壳子章节，里面啥剧情都没有。
-   JSON结构里，这种“父子关系”要明明白白地体现出来。

**🎬 怎么把剧情写“狠”一点：**
1.  **别写废话，刀刀见血**：每个剧情节点都要塞满信息，至少来个10个以上的剧情点。多写对话、心理、推进剧情的线索，少描绘风景。咱们这是编剧思维，不是风光摄影。要的就是高冲突、高回报、高张力，砍掉所有拖泥带水-的过渡。
2.  **像多米诺骨牌一样**：剧情点之间必须环环相扣，一个倒下，必须推倒下一个。起承转合要流畅，让读者一口气看完，停不下来。
3.  **多写“干了啥”，少写“看到了啥”**：我们要的是角色的行动和决策，是他怎么解决问题的，而不是他站在那里看风景发呆。

**🚫 这些东西，一概砍掉：**
纯粹的景色描写、没营养的闲聊、角色长篇大论的内心独白、和主线无关的日常琐事、翻来覆去说的车轱辘话。记住，快！准！狠！`
    };
  }

  /**
   * 构建ACE技巧学习要求消息
   */
  buildACETechniqueMessage(): ConversationMessage {
    return {
      role: 'user',
      content: `【偷师学艺：怎么学ACE的神韵】

好了，关于ACE框架，咱们得聊得深入一点。

**记住，咱们不是在做填空题，是在做解剖课：**
1.  **学的是思路，不是画皮**：别光盯着模板的框框，要去想ACE为什么这么设计。对话、节奏、剧情...这些东西背后的“创作逻辑”才是我们要偷的精髓。
2.  **specificDescription 和 avoidanceGuidance 是金矿**：这两个地方藏着具体描写的秘诀，多挖挖，看看高手是怎么把一个模糊的场景写得活色生香的。
3.  **一个内部秘密**：别在你的创作建议里直接提“ACE框架”这个词，读者不知道这玩意儿。你要做的是把它的精髓揉碎了，变成你自己的话，自然地塞进shouldWriting里，做到润物细无声。
4.  **抓住那个节奏感**：ACE的核心技法之一就是“动作先行，对话跟进，结果立现”。感觉一下，像不像打拳？先一拳出去（动作），再喝问一声（对话），对手应声倒地（结果）。模仿并改造这种一气呵成的节奏。

**🎯 顺便提一句，关于对话：**
拜托，千万别把对话写得像机器翻译，尤其是什么“哦，我亲爱的朋友，你难道要去那该死的森林里吗？”这种西幻翻译腔。让角色说人话，说符合他们身份和性格的人话，这是小说的灵魂！`
    };
  }

  /**
   * 构建创作核心驱动要求消息
   */
  buildCoreDriveMessage(): ConversationMessage {
    return {
      role: 'user',
      content: `【爽文的发动机：怎么让读者停不下来】

朋友，记住，写网文就像开一家情绪过山车公司，我们的核心业务就是让读者爽。

**🎢 读者的情绪过山车（必须每3-5章完整跑一圈）：**
这个循环是咱们的印钞机，必须强制执行，高密度、快节奏地来！
1.  **压力锅** (压力来源)：给主角上压力，让他陷入困境。
2.  **秀肌肉** (实力展示)：主角开始掏底牌，展示他凭什么牛逼。
3.  **世界观碎了** (认知颠覆)：让配角和反派的三观受到剧烈冲击。
4.  **大爆金币** (资源增值)：打完收工，主角拿到实打实的好处。
5.  **更大的坑** (悬念增殖)：别让读者歇着，马上抛出新的悬念和危机。

**🎭 给读者下套：每章都要有“卧槽”时刻**
别按常理出牌，读者猜到的剧情就不香了。每章至少给我搞2-3个反套路惊喜：
-   **敌人不按剧本走**：反派突然用了个你想不到的骚操作。
-   **解题思路清奇**：主角用了一个谁也想不到的鬼点子破局。
-   **剧情神反转**：天大的危机，结果是个大机遇；或者平平无奇的事，引爆了惊天阴谋。
-   **金手指还能这么用？**：主角的能力或宝贝，用出了意想不到的效果。
-   **TA居然是...?!**：某个角色的身份或目的，以一种震撼的方式揭晓。
-   **结果出乎意料**：以为血赚，结果血亏；以为完蛋了，结果是天大的机缘。

**🔄 “装逼打脸”的艺术（咱们的经典保留节目）：**
这套连招要打得漂亮，有新意！
a. **起手式**：先制造个冲突或危机，给装逼搭好舞台。
b. **亮底牌**：主角凭啥这么横？把他的实力、背景或骚操作亮出来。
c. **神操作**：解决问题的方式要巧，要有创意，不能是简单粗暴的等级碾压。
d. **全场懵逼**：结果必须让所有人大跌眼镜，惊掉下巴。
e. **摸装备**：装完逼得有收获，不管是宝贝、信息、地位，还是新的敌人。

**📚 咱们的故事，没有终点：**
记住，我们写的是可以连载到天荒地老的长篇小说。别想着完结！这一章的胜利，只是下一场更大战争的序幕。所谓的“放慢节奏”，指的是控制最终大决战的到来，不是让你在章节里灌水。每一章，都必须让读者感觉到“卧槽，又发生了好多事！”、“下一章呢？快给我下一章！”`
    };
  }

  /**
   * 构建所有增强消息的组合
   */
  buildAllEnhancementMessages(): ConversationMessage[] {
    return [
      this.buildPlotDetailMessage(),
      this.buildACETechniqueMessage(),
      this.buildCoreDriveMessage()
    ];
  }
}
