/**
 * 书名生成服务主类
 * 协调各个处理器完成书名生成任务，提供统一的API接口
 */

import {
  TitleGenerationParams,
  TitleGenerationCallbacks,
  TitleGenerationResult,
  BookTitle,
  BookTitleGenerationServiceInterface
} from './types/BrainstormTypes';
import { TitlePromptBuilder } from './builders/TitlePromptBuilder';
import { TitleResponseParser } from './processors/TitleResponseParser';

export class BookTitleGenerationService implements BookTitleGenerationServiceInterface {
  constructor(
    private apiSettings: any,
    private aiSender: any
  ) {}

  /**
   * 生成书名 - 主入口方法
   */
  async generateTitles(
    params: TitleGenerationParams,
    callbacks: TitleGenerationCallbacks
  ): Promise<TitleGenerationResult> {
    try {
      callbacks.onStart?.();

      console.log('🚀 开始书名生成处理:', {
        keywordCount: params.keywords.length,
        hasFramework: !!params.framework,
        hasCustomFramework: !!params.customFramework,
        userRequirements: params.userRequirements || '无'
      });

      // 验证参数
      this.validateParams(params);

      // 构建提示词
      const messages = TitlePromptBuilder.buildTitleGenerationPrompt(params);

      console.log('📝 提示词构建完成:', {
        messageCount: messages.length,
        hasKeywords: params.keywords.length > 0,
        hasFramework: !!(params.framework || params.customFramework)
      });

      // 发送AI请求
      let streamResponse = '';
      const generatedTitles: BookTitle[] = [];

      callbacks.onProgress?.(10); // 开始请求

      const result = await this.sendAIRequest(
        messages,
        (chunk: string) => {
          streamResponse += chunk;
          callbacks.onProgress?.(30 + (streamResponse.length / 1000) * 40); // 30-70%

          // 尝试流式解析
          const streamTitles = TitleResponseParser.tryParseStreamingResponse(
            streamResponse,
            params.keywords,
            params.framework,
            params.customFramework,
            (title: BookTitle) => {
              if (!generatedTitles.find(t => t.title === title.title)) {
                generatedTitles.push(title);
                callbacks.onTitleGenerated?.(title);
              }
            }
          );
        }
      );

      callbacks.onProgress?.(80); // 请求完成

      if (!result.success || !result.text) {
        throw new Error(result.error || '书名生成失败');
      }

      // 解析最终响应
      const finalTitles = TitleResponseParser.parseGenerationResponse(
        result.text,
        params.keywords,
        params.framework,
        params.customFramework
      );

      callbacks.onProgress?.(100); // 完成

      const generationResult: TitleGenerationResult = {
        titles: finalTitles,
        success: true,
        totalGenerated: finalTitles.length,
        averageScore: finalTitles.reduce((sum, t) => sum + t.aiScore, 0) / finalTitles.length
      };

      console.log('✅ 书名生成完成:', {
        totalGenerated: finalTitles.length,
        averageScore: generationResult.averageScore.toFixed(2)
      });

      callbacks.onComplete?.(finalTitles);
      return generationResult;

    } catch (error: any) {
      console.error('❌ 书名生成失败:', error);

      const errorResult: TitleGenerationResult = {
        titles: [],
        success: false,
        error: error instanceof Error ? error.message : '书名生成失败',
        totalGenerated: 0,
        averageScore: 0
      };

      callbacks.onError?.(error);
      return errorResult;
    }
  }

  /**
   * 验证生成参数
   */
  private validateParams(params: TitleGenerationParams): void {
    if (!params.keywords || params.keywords.length === 0) {
      if (!params.framework && !params.customFramework) {
        throw new Error('请至少提供关键词或框架模式');
      }
    }

    // 验证关键词长度
    for (const keyword of params.keywords) {
      if (keyword.length > 10) {
        throw new Error(`关键词"${keyword}"过长，请控制在10字以内`);
      }
    }

    // 验证自定义框架
    if (params.customFramework && params.customFramework.length > 50) {
      throw new Error('自定义框架模式过长，请控制在50字以内');
    }
  }

  /**
   * 发送AI请求
   */
  private async sendAIRequest(
    messages: any[],
    onChunk?: (chunk: string) => void
  ): Promise<any> {
    try {
      // 获取API配置
      const provider = this.apiSettings.getCurrentProvider() || 'openai';
      const model = this.apiSettings.getCurrentModel() || 'gemini-2.5-pro-exp-03-25';
      const apiKey = this.apiSettings.getAPIKey(provider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(provider);
      const maxTokens = this.apiSettings.getMaxTokens() || 4000;

      // 预先验证API配置
      if (!apiKey) {
        const detailedError = `请先在设置中配置${provider === 'openai' ? 'OpenAI' : provider === 'google' ? 'Google' : '自定义'}的API密钥。当前提供商：${provider}，端点：${apiEndpoint}`;
        console.error('❌ API密钥缺失详情:', {
          provider,
          apiEndpoint,
          error: detailedError
        });
        throw new Error(detailedError);
      }

      console.log('📡 开始发送书名生成请求...', {
        provider,
        model,
        hasApiKey: !!apiKey,
        apiEndpoint,
        maxTokens,
        messagesCount: messages.length
      });

      // 发送AI请求
      const result = await this.aiSender.sendStreamingRequest(
        '',
        onChunk || (() => {}),
        {
          messages: messages,
          provider: provider,
          model: model,
          apiKey: apiKey,
          apiEndpoint: apiEndpoint,
          temperature: 0.7, // 书名生成需要一定创意性
          maxTokens: maxTokens,
          streaming: !!onChunk
        }
      );

      console.log('✅ 书名生成请求完成:', {
        success: result.success,
        hasText: !!result.text,
        textLength: result.text ? result.text.length : 0
      });

      return result;
    } catch (error: any) {
      console.error('❌ 书名生成请求失败:', {
        error: error.message,
        errorType: error.name || 'Unknown',
        stack: error.stack
      });
      throw error;
    }
  }
}

/**
 * 创建书名生成服务的工厂函数
 */
export function createBookTitleGenerationService(
  apiSettings: any,
  aiSender: any
): BookTitleGenerationServiceInterface {
  return new BookTitleGenerationService(apiSettings, aiSender);
}
