"use client";

import { useState, useCallback, useRef } from 'react';

interface ClickAnimation {
  id: string;
  x: number;
  y: number;
  timestamp: number;
}

/**
 * 点击背景动画Hook
 * 管理编辑器点击时的背景动画效果
 */
export const useClickAnimation = () => {
  const [clickAnimations, setClickAnimations] = useState<ClickAnimation[]>([]);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // 添加点击动画
  const addClickAnimation = useCallback((clientX: number, clientY: number) => {
    // 防抖处理，避免过于频繁的动画
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      const animation: ClickAnimation = {
        id: `click-${Date.now()}-${Math.random()}`,
        x: clientX,
        y: clientY,
        timestamp: Date.now()
      };

      setClickAnimations(prev => [...prev, animation]);

      // 2.5秒后自动清理动画
      setTimeout(() => {
        setClickAnimations(prev => prev.filter(a => a.id !== animation.id));
      }, 2500);
    }, 100); // 100ms防抖
  }, []);

  // 处理点击事件
  const handleClick = useCallback((e: React.MouseEvent<HTMLElement>) => {
    // 获取相对于视口的坐标
    const clientX = e.clientX;
    const clientY = e.clientY;
    
    addClickAnimation(clientX, clientY);
  }, [addClickAnimation]);

  // 处理容器点击事件（用于编辑器容器）
  const handleContainerClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    // 只在点击容器本身时触发，不包括子元素
    if (e.target === e.currentTarget) {
      handleClick(e);
    }
  }, [handleClick]);

  // 处理文本区域点击事件
  const handleTextareaClick = useCallback((e: React.MouseEvent<HTMLTextAreaElement>) => {
    handleClick(e);
  }, [handleClick]);

  // 清理所有动画
  const clearClickAnimations = useCallback(() => {
    setClickAnimations([]);
  }, []);

  // 获取动画数量
  const getAnimationCount = useCallback(() => {
    return clickAnimations.length;
  }, [clickAnimations.length]);

  // 检查是否有活跃动画
  const hasActiveAnimations = useCallback(() => {
    return clickAnimations.length > 0;
  }, [clickAnimations.length]);

  return {
    clickAnimations,
    handleClick,
    handleContainerClick,
    handleTextareaClick,
    clearClickAnimations,
    getAnimationCount,
    hasActiveAnimations
  };
};
