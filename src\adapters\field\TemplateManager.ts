"use client";

import { db } from '@/lib/db/dexie';
import { v4 as uuidv4 } from 'uuid';

/**
 * 字段模板定义
 */
export interface FieldTemplate {
  id?: string;
  name: string;
  description: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
  fields: FieldDefinition[];
  layout?: LayoutConfig;
}

/**
 * 字段定义
 */
export interface FieldDefinition {
  id: string;
  name: string;
  displayName: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  validationRules?: ValidationRule[];
  order: number;
  group?: string;
}

/**
 * 验证规则
 */
export interface ValidationRule {
  type: string;
  params?: any;
  message: string;
}

/**
 * 布局配置
 */
export interface LayoutConfig {
  type: string;
  columns?: number;
  groups?: GroupConfig[];
}

/**
 * 分组配置
 */
export interface GroupConfig {
  id: string;
  name: string;
  order: number;
}

/**
 * 模板管理器
 * 负责模板的CRUD操作
 */
export class TemplateManager {
  /**
   * 创建模板
   * @param template 模板数据
   * @returns 创建的模板ID
   */
  async createTemplate(template: Omit<FieldTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();
    
    // 确保每个字段都有唯一ID
    const fields = template.fields.map(field => ({
      ...field,
      id: field.id || uuidv4()
    }));
    
    // 创建模板
    await db.fieldTemplates.add({
      ...template,
      id,
      fields,
      createdAt: now,
      updatedAt: now
    });
    
    return id;
  }
  
  /**
   * 获取模板
   * @param id 模板ID
   * @returns 模板数据
   */
  async getTemplate(id: string): Promise<FieldTemplate | null> {
    return await db.fieldTemplates.get(id);
  }
  
  /**
   * 获取所有模板
   * @param category 可选的类别筛选
   * @returns 模板列表
   */
  async getAllTemplates(category?: string): Promise<FieldTemplate[]> {
    if (category) {
      return await db.fieldTemplates.where('category').equals(category).toArray();
    } else {
      return await db.fieldTemplates.toArray();
    }
  }
  
  /**
   * 更新模板
   * @param id 模板ID
   * @param template 更新的模板数据
   */
  async updateTemplate(id: string, template: Partial<FieldTemplate>): Promise<void> {
    await db.fieldTemplates.update(id, {
      ...template,
      updatedAt: new Date()
    });
  }
  
  /**
   * 删除模板
   * @param id 模板ID
   */
  async deleteTemplate(id: string): Promise<void> {
    await db.fieldTemplates.delete(id);
  }
  
  /**
   * 应用模板
   * @param templateId 模板ID
   * @param targetObject 目标对象
   * @returns 应用模板后的对象
   */
  async applyTemplate(templateId: string, targetObject: any): Promise<any> {
    // 获取模板
    const template = await this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template with ID ${templateId} not found`);
    }
    
    // 创建结果对象
    const result = { ...targetObject };
    
    // 应用模板字段
    for (const field of template.fields) {
      // 如果目标对象没有该字段的值，使用默认值
      if (result[field.name] === undefined) {
        result[field.name] = field.defaultValue !== undefined ? field.defaultValue : null;
      }
      
      // 如果目标对象没有attributes对象，创建一个
      if (!result.attributes) {
        result.attributes = {};
      }
      
      // 如果字段名称不是基本字段，添加到attributes中
      if (!['id', 'name', 'category', 'description', 'createdAt', 'updatedAt'].includes(field.name)) {
        if (result.attributes[field.name] === undefined) {
          result.attributes[field.name] = field.defaultValue !== undefined ? field.defaultValue : null;
        }
      }
    }
    
    return result;
  }
  
  /**
   * 导出模板
   * @param id 模板ID
   * @returns 导出的模板JSON字符串
   */
  async exportTemplate(id: string): Promise<string> {
    const template = await this.getTemplate(id);
    if (!template) {
      throw new Error(`Template with ID ${id} not found`);
    }
    
    return JSON.stringify(template);
  }
  
  /**
   * 导入模板
   * @param templateJson 模板JSON字符串
   * @returns 导入的模板ID
   */
  async importTemplate(templateJson: string): Promise<string> {
    try {
      const template = JSON.parse(templateJson) as FieldTemplate;
      
      // 移除ID，确保创建新模板
      const { id, ...templateData } = template;
      
      return await this.createTemplate(templateData);
    } catch (error) {
      throw new Error(`Failed to import template: ${error.message}`);
    }
  }
  
  /**
   * 创建预设模板
   * @param category 模板类别
   * @param type 模板类型
   * @returns 创建的模板ID
   */
  async createPresetTemplate(category: string, type: string): Promise<string> {
    // 根据类别和类型创建预设模板
    switch (category) {
      case 'worldbuilding':
        return await this.createWorldBuildingPresetTemplate(type);
      case 'character':
        return await this.createCharacterPresetTemplate(type);
      case 'terminology':
        return await this.createTerminologyPresetTemplate(type);
      default:
        throw new Error(`Unsupported category: ${category}`);
    }
  }
  
  /**
   * 创建世界观预设模板
   * @param type 模板类型
   * @returns 创建的模板ID
   */
  private async createWorldBuildingPresetTemplate(type: string): Promise<string> {
    // 基础字段
    const baseFields: FieldDefinition[] = [
      {
        id: uuidv4(),
        name: 'name',
        displayName: '名称',
        type: 'string',
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        name: 'category',
        displayName: '类别',
        type: 'select',
        required: true,
        order: 1
      },
      {
        id: uuidv4(),
        name: 'description',
        displayName: '描述',
        type: 'textarea',
        required: true,
        order: 2
      },
      {
        id: uuidv4(),
        name: '重要性',
        displayName: '重要性',
        type: 'select',
        required: false,
        order: 3
      },
      {
        id: uuidv4(),
        name: '时间跨度',
        displayName: '时间跨度',
        type: 'select',
        required: false,
        order: 4
      },
      {
        id: uuidv4(),
        name: '影响范围',
        displayName: '影响范围',
        type: 'select',
        required: false,
        order: 5
      }
    ];
    
    // 特定类型的字段
    let specificFields: FieldDefinition[] = [];
    let templateName = '';
    let templateDescription = '';
    
    switch (type) {
      case 'geography':
        templateName = '地理环境模板';
        templateDescription = '用于创建地理环境元素，如地点、地形、区域等';
        specificFields = [
          {
            id: uuidv4(),
            name: 'significance',
            displayName: '在故事中的意义',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'inhabitants',
            displayName: '居民/生物',
            type: 'string',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'features',
            displayName: '特殊地理特征',
            type: 'textarea',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'accessibility',
            displayName: '可达性/限制',
            type: 'string',
            required: false,
            order: 9
          }
        ];
        break;
        
      case 'history':
        templateName = '历史事件模板';
        templateDescription = '用于创建历史事件元素，如战争、变革、传说等';
        specificFields = [
          {
            id: uuidv4(),
            name: 'event',
            displayName: '事件描述',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'timeframe',
            displayName: '时间框架',
            type: 'string',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'keyFigures',
            displayName: '关键人物',
            type: 'string',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'impact',
            displayName: '对当前世界的影响',
            type: 'textarea',
            required: false,
            order: 9
          },
          {
            id: uuidv4(),
            name: 'documentation',
            displayName: '记录方式',
            type: 'string',
            required: false,
            order: 10
          }
        ];
        break;
        
      case 'culture':
        templateName = '文化传统模板';
        templateDescription = '用于创建文化传统元素，如习俗、礼仪、传统等';
        specificFields = [
          {
            id: uuidv4(),
            name: 'overview',
            displayName: '文化概述',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'customs',
            displayName: '重要习俗',
            type: 'textarea',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'beliefs',
            displayName: '信仰与禁忌',
            type: 'textarea',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'arts',
            displayName: '艺术与表达',
            type: 'textarea',
            required: false,
            order: 9
          },
          {
            id: uuidv4(),
            name: 'values',
            displayName: '核心价值观',
            type: 'textarea',
            required: false,
            order: 10
          }
        ];
        break;
        
      default:
        templateName = '通用世界观模板';
        templateDescription = '用于创建通用世界观元素';
        specificFields = [
          {
            id: uuidv4(),
            name: 'mainFeatures',
            displayName: '主要特征',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'relevance',
            displayName: '与故事的关联',
            type: 'textarea',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'uniqueAspects',
            displayName: '独特之处',
            type: 'textarea',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'integration',
            displayName: '与世界的融合方式',
            type: 'textarea',
            required: false,
            order: 9
          }
        ];
        break;
    }
    
    // 合并字段
    const fields = [...baseFields, ...specificFields];
    
    // 创建模板
    return await this.createTemplate({
      name: templateName,
      description: templateDescription,
      category: 'worldbuilding',
      fields,
      layout: {
        type: 'default',
        columns: 1
      }
    });
  }
  
  /**
   * 创建人物预设模板
   * @param type 模板类型
   * @returns 创建的模板ID
   */
  private async createCharacterPresetTemplate(type: string): Promise<string> {
    // 基础字段
    const baseFields: FieldDefinition[] = [
      {
        id: uuidv4(),
        name: 'name',
        displayName: '名称',
        type: 'string',
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        name: 'alias',
        displayName: '别名',
        type: 'array',
        required: false,
        order: 1
      },
      {
        id: uuidv4(),
        name: 'description',
        displayName: '描述',
        type: 'textarea',
        required: true,
        order: 2
      }
    ];
    
    // 特定类型的字段
    let specificFields: FieldDefinition[] = [];
    let templateName = '';
    let templateDescription = '';
    
    switch (type) {
      case 'protagonist':
        templateName = '主角模板';
        templateDescription = '用于创建主角人物';
        specificFields = [
          {
            id: uuidv4(),
            name: 'appearance',
            displayName: '外貌',
            type: 'textarea',
            required: false,
            order: 3
          },
          {
            id: uuidv4(),
            name: 'personality',
            displayName: '性格',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'background',
            displayName: '背景',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'goals',
            displayName: '目标',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'abilities',
            displayName: '能力',
            type: 'textarea',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'weaknesses',
            displayName: '弱点',
            type: 'textarea',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'arc',
            displayName: '角色弧',
            type: 'textarea',
            required: false,
            order: 9
          }
        ];
        break;
        
      case 'antagonist':
        templateName = '反派模板';
        templateDescription = '用于创建反派人物';
        specificFields = [
          {
            id: uuidv4(),
            name: 'appearance',
            displayName: '外貌',
            type: 'textarea',
            required: false,
            order: 3
          },
          {
            id: uuidv4(),
            name: 'personality',
            displayName: '性格',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'background',
            displayName: '背景',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'motivation',
            displayName: '动机',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'abilities',
            displayName: '能力',
            type: 'textarea',
            required: false,
            order: 7
          },
          {
            id: uuidv4(),
            name: 'weaknesses',
            displayName: '弱点',
            type: 'textarea',
            required: false,
            order: 8
          },
          {
            id: uuidv4(),
            name: 'threat',
            displayName: '威胁程度',
            type: 'select',
            required: false,
            order: 9
          }
        ];
        break;
        
      default:
        templateName = '通用人物模板';
        templateDescription = '用于创建通用人物';
        specificFields = [
          {
            id: uuidv4(),
            name: 'appearance',
            displayName: '外貌',
            type: 'textarea',
            required: false,
            order: 3
          },
          {
            id: uuidv4(),
            name: 'personality',
            displayName: '性格',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'background',
            displayName: '背景',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'role',
            displayName: '角色',
            type: 'string',
            required: false,
            order: 6
          }
        ];
        break;
    }
    
    // 合并字段
    const fields = [...baseFields, ...specificFields];
    
    // 创建模板
    return await this.createTemplate({
      name: templateName,
      description: templateDescription,
      category: 'character',
      fields,
      layout: {
        type: 'default',
        columns: 1
      }
    });
  }
  
  /**
   * 创建术语预设模板
   * @param type 模板类型
   * @returns 创建的模板ID
   */
  private async createTerminologyPresetTemplate(type: string): Promise<string> {
    // 基础字段
    const baseFields: FieldDefinition[] = [
      {
        id: uuidv4(),
        name: 'name',
        displayName: '名称',
        type: 'string',
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        name: 'pronunciation',
        displayName: '读音',
        type: 'string',
        required: false,
        order: 1
      },
      {
        id: uuidv4(),
        name: 'category',
        displayName: '类别',
        type: 'select',
        required: true,
        order: 2
      },
      {
        id: uuidv4(),
        name: 'description',
        displayName: '描述',
        type: 'textarea',
        required: true,
        order: 3
      }
    ];
    
    // 特定类型的字段
    let specificFields: FieldDefinition[] = [];
    let templateName = '';
    let templateDescription = '';
    
    switch (type) {
      case 'concept':
        templateName = '概念术语模板';
        templateDescription = '用于创建概念类术语';
        specificFields = [
          {
            id: uuidv4(),
            name: 'definition',
            displayName: '定义',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'origin',
            displayName: '起源',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'usage',
            displayName: '用法',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'examples',
            displayName: '示例',
            type: 'textarea',
            required: false,
            order: 7
          }
        ];
        break;
        
      case 'object':
        templateName = '物品术语模板';
        templateDescription = '用于创建物品类术语';
        specificFields = [
          {
            id: uuidv4(),
            name: 'appearance',
            displayName: '外观',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'function',
            displayName: '功能',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'origin',
            displayName: '来源',
            type: 'textarea',
            required: false,
            order: 6
          },
          {
            id: uuidv4(),
            name: 'rarity',
            displayName: '稀有度',
            type: 'select',
            required: false,
            order: 7
          }
        ];
        break;
        
      default:
        templateName = '通用术语模板';
        templateDescription = '用于创建通用术语';
        specificFields = [
          {
            id: uuidv4(),
            name: 'definition',
            displayName: '定义',
            type: 'textarea',
            required: false,
            order: 4
          },
          {
            id: uuidv4(),
            name: 'usage',
            displayName: '用法',
            type: 'textarea',
            required: false,
            order: 5
          },
          {
            id: uuidv4(),
            name: 'notes',
            displayName: '备注',
            type: 'textarea',
            required: false,
            order: 6
          }
        ];
        break;
    }
    
    // 合并字段
    const fields = [...baseFields, ...specificFields];
    
    // 创建模板
    return await this.createTemplate({
      name: templateName,
      description: templateDescription,
      category: 'terminology',
      fields,
      layout: {
        type: 'default',
        columns: 1
      }
    });
  }
}
