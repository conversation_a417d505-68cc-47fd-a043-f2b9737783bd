/* 数据状态指示器样式 */
.data-status-indicator {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.data-status-indicator:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.data-status-indicator.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 数据摘要 */
.data-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 0;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.data-summary:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.total-count {
  font-size: 18px;
  font-weight: 600;
  color: #007bff;
}

.summary-text {
  font-size: 14px;
  color: #666;
}

.toggle-details {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.toggle-details:hover {
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

/* 数据详情 */
.data-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 数据类型项 */
.data-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.data-type-item:last-child {
  border-bottom: none;
}

.data-type-item.empty {
  background-color: rgba(255, 193, 7, 0.05);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 4px 0;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.type-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.type-count {
  font-size: 12px;
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 10px;
}

.data-type-item.empty .type-count {
  background-color: rgba(255, 193, 7, 0.2);
  color: #856404;
}

/* 创建按钮 */
.create-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.create-button:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.create-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
  background-color: rgba(0, 123, 255, 0.02);
  border-radius: 8px;
  margin-top: 12px;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.empty-state p {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.empty-state small {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: block;
  max-width: 250px;
  margin: 0 auto 12px auto;
}

.quick-create-trigger {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.quick-create-trigger:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .data-status-indicator {
    padding: 10px;
  }

  .data-type-item {
    padding: 6px 0;
  }

  .type-name {
    font-size: 13px;
  }

  .create-button {
    padding: 3px 6px;
    font-size: 11px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .data-status-indicator {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
  }

  .summary-text,
  .type-count {
    color: #b0b0b0;
  }

  .type-name {
    color: #e0e0e0;
  }

  .data-type-item.empty {
    background-color: rgba(255, 193, 7, 0.1);
  }

  .empty-state {
    background-color: rgba(255, 255, 255, 0.05);
    color: #b0b0b0;
  }

  .empty-state p {
    color: #e0e0e0;
  }
}
