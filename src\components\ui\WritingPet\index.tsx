"use client";

import React, { useState, useEffect } from 'react';
import { PetSVG } from './components/PetSVG';
import { PetEffects } from './components/PetEffects';
import { usePetState } from './hooks/usePetState';
import { useWritingDetection } from './hooks/useWritingDetection';
import { usePetPreferences } from './hooks/usePetPreferences';
import './styles/pet-animations.css';

interface WritingPetProps {
  isWriting: boolean;
  inputFrequency: number;
  lastInputTime: number;
  editorFocused?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export type PetState = 'sleeping' | 'waking' | 'idle' | 'writing' | 'excited' | 'sleepy' | 'celebrating';
export type PetType = 'cat' | 'bird' | 'dragon' | 'rabbit';

/**
 * 写作宠物组件
 * 一个可爱的SVG宠物，会根据用户的写作状态做出不同反应
 */
export const WritingPet: React.FC<WritingPetProps> = ({
  isWriting,
  inputFrequency,
  lastInputTime,
  editorFocused = false,
  className = "",
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  // 宠物偏好设置
  const {
    petType,
    animationLevel,
    isEnabled,
    setPetType,
    setAnimationLevel
  } = usePetPreferences();

  // 写作状态检测
  const writingState = useWritingDetection(isWriting, inputFrequency, lastInputTime);

  // 宠物状态管理
  const {
    currentState,
    isAnimating,
    triggerCelebration,
    triggerSpecialAction,
    triggerWakeUp,
    triggerSleep
  } = usePetState(writingState, animationLevel);

  // 组件挂载时的入场动画
  useEffect(() => {
    if (isEnabled) {
      const timer = setTimeout(() => setIsVisible(true), 500);
      return () => clearTimeout(timer);
    }
  }, [isEnabled]);

  // 监听编辑器焦点状态变化，触发唤醒/入睡
  useEffect(() => {
    if (editorFocused && currentState === 'sleeping') {
      triggerWakeUp();
    } else if (!editorFocused && (currentState === 'idle' || currentState === 'writing')) {
      // 编辑器失去焦点时，开始准备入睡
      triggerSleep();
    }
  }, [editorFocused, currentState, triggerWakeUp, triggerSleep]);

  // 处理宠物点击
  const handlePetClick = () => {
    if (!isEnabled || !isVisible) return;

    setClickCount(prev => prev + 1);
    triggerSpecialAction('click');

    // 连续点击彩蛋
    if (clickCount >= 4) {
      triggerCelebration();
      setClickCount(0);
    }
  };

  // 如果宠物被禁用，不渲染
  if (!isEnabled) return null;

  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'fixed',
    top: '20px',
    right: '20px',
    width: '40px',
    height: '40px',
    zIndex: 1000,
    cursor: 'pointer',
    transition: 'all 0.3s ease-in-out',
    transform: isVisible ? 'translateY(0) scale(1)' : 'translateY(-20px) scale(0.8)',
    opacity: isVisible ? 1 : 0,
    filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))',
    ...style
  };

  return (
    <div
      className={`writing-pet ${className}`}
      style={containerStyle}
      onClick={handlePetClick}
      role="button"
      tabIndex={0}
      aria-label={`写作宠物 - 当前状态: ${getStateDescription(currentState)}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handlePetClick();
        }
      }}
    >
      {/* 主要的宠物SVG */}
      <PetSVG
        petType={petType}
        state={currentState}
        isAnimating={isAnimating}
        animationLevel={animationLevel}
      />

      {/* 特效系统 */}
      <PetEffects
        state={currentState}
        isAnimating={isAnimating}
        animationLevel={animationLevel}
        triggerKey={clickCount} // 用于触发新的特效
      />

      {/* 状态指示器（调试用，生产环境可移除） */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'absolute',
            top: '-25px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '10px',
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '2px 6px',
            borderRadius: '4px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none'
          }}
        >
          {currentState} | {writingState.activity}
        </div>
      )}
    </div>
  );
};

// 状态描述函数，用于无障碍
function getStateDescription(state: PetState): string {
  switch (state) {
    case 'sleeping':
      return '睡眠中 - 小猫正在安静地睡觉，点击编辑器可以唤醒它';
    case 'waking':
      return '苏醒中 - 小猫正在慢慢醒来，请稍等片刻';
    case 'idle':
      return '休息中 - 小猫正在安静地陪伴您';
    case 'writing':
      return '专注中 - 小猫在认真听您写作';
    case 'excited':
      return '兴奋中 - 小猫为您的创作热情而开心';
    case 'sleepy':
      return '困倦中 - 小猫提醒您该继续写作了';
    case 'celebrating':
      return '庆祝中 - 小猫为您的成就感到骄傲';
    default:
      return '陪伴中 - 小猫在身边守护您的创作';
  }
}

// 宠物类型中文名称映射
export const PetTypeNames: Record<PetType, string> = {
  cat: '小猫咪',
  bird: '小鸟儿',
  dragon: '小龙龙',
  rabbit: '小兔子'
};

// 动画级别中文名称映射
export const AnimationLevelNames: Record<'low' | 'medium' | 'high', string> = {
  low: '安静模式',
  medium: '标准模式',
  high: '活跃模式'
};

export default WritingPet;
