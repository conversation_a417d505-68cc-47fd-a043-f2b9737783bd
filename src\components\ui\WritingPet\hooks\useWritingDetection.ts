"use client";

import { useState, useEffect, useRef } from 'react';

interface WritingState {
  activity: 'idle' | 'writing' | 'fast-writing' | 'paused';
  frequency: number;
  timeSinceLastInput: number;
}

/**
 * 写作状态检测Hook
 * 分析用户的输入模式，判断当前的写作状态
 */
export const useWritingDetection = (
  isWriting: boolean,
  inputFrequency: number,
  lastInputTime: number
): WritingState => {
  const [writingState, setWritingState] = useState<WritingState>({
    activity: 'idle',
    frequency: 0,
    timeSinceLastInput: 0
  });

  const lastUpdateRef = useRef(Date.now());
  const inputHistoryRef = useRef<number[]>([]);

  useEffect(() => {
    const now = Date.now();
    const timeSinceLastInput = lastInputTime > 0 ? now - lastInputTime : Infinity;
    
    // 更新输入历史
    if (isWriting && lastInputTime > lastUpdateRef.current) {
      inputHistoryRef.current.push(lastInputTime);
      
      // 只保留最近30秒的输入记录
      const thirtySecondsAgo = now - 30000;
      inputHistoryRef.current = inputHistoryRef.current.filter(time => time > thirtySecondsAgo);
    }

    // 计算实际的输入频率（每分钟字符数）
    const recentInputs = inputHistoryRef.current.length;
    const actualFrequency = recentInputs * 2; // 简化计算

    // 判断写作活动状态
    let activity: WritingState['activity'] = 'idle';

    if (timeSinceLastInput < 1000) {
      // 1秒内有输入
      if (actualFrequency > 60) {
        activity = 'fast-writing';
      } else if (actualFrequency > 20) {
        activity = 'writing';
      } else {
        activity = 'writing';
      }
    } else if (timeSinceLastInput < 5000) {
      // 1-5秒内有输入，可能在思考
      activity = 'paused';
    } else if (timeSinceLastInput < 30000) {
      // 5-30秒内有输入，短暂休息
      activity = 'idle';
    } else {
      // 超过30秒无输入，完全空闲
      activity = 'idle';
    }

    const newState: WritingState = {
      activity,
      frequency: actualFrequency,
      timeSinceLastInput
    };

    // 只在状态真正改变时更新
    if (
      newState.activity !== writingState.activity ||
      Math.abs(newState.frequency - writingState.frequency) > 10 ||
      Math.abs(newState.timeSinceLastInput - writingState.timeSinceLastInput) > 1000
    ) {
      setWritingState(newState);
    }

    lastUpdateRef.current = now;
  }, [isWriting, inputFrequency, lastInputTime, writingState]);

  return writingState;
};
