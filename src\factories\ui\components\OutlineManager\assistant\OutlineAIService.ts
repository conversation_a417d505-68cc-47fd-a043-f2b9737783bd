"use client";

import { aiServiceProvider } from '@/services/ai/AIServiceProvider';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
import { OutlineNodeType } from '../../../types/outline';
import { ContextChain } from './ContextChainService';
import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { rhythmAnalysisService, ChapterSummary, SmartRhythmAnalysis } from './RhythmAnalysisService';
import { DualAIService } from '@/utils/ai/DualAIService';
import { DualAIConfigManager } from '@/utils/ai/DualAIConfigManager';
import { AIFunctionType } from '@/types/DualAIConfig';
import { DialogueAIService } from './DialogueAIService';

// 导入新的模块化组件
import { RefactoredOutlineAIService } from './core/RefactoredOutlineAIService';

// 使用RhythmAnalysisService中的接口定义

/**
 * 扩展的AI响应接口，支持思考画布
 */
export interface OutlineAIResponseExtended {
  message: string;
  changes?: any[];
  metadata?: {
    operationType: string;
    confidence: number;
    hasThinkingCanvas?: boolean;
    thinkingCanvasId?: string;
    workflowStep?: 'thinking' | 'generating' | 'completed';
    thinkingMode?: 'simple' | 'detailed' | 'custom';
  };
  thinkingCanvas?: ThinkingCanvasData;
  success: boolean;
  error?: string;
}

/**
 * 思考画布生成选项
 */
export interface ThinkingCanvasOptions {
  mode: 'simple' | 'detailed' | 'custom';
  userRequirements?: string;
  focusAreas?: string[];
}

/**
 * 大纲AI服务
 * 专门处理大纲相关的AI请求和响应
 * 现在使用模块化的RefactoredOutlineAIService作为核心实现
 */
export class OutlineAIService {
  private static instance: OutlineAIService;
  private refactoredService: RefactoredOutlineAIService;

  private constructor() {
    // 初始化重构后的服务
    this.refactoredService = RefactoredOutlineAIService.getInstance();
  }

  public static getInstance(): OutlineAIService {
    if (!OutlineAIService.instance) {
      OutlineAIService.instance = new OutlineAIService();
    }
    return OutlineAIService.instance;
  }

  // buildCoreIdentityMessage方法已迁移到MessageBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现

  /**
   * 构建专业知识确认消息（助手消息）
   */
  private buildProfessionalKnowledgeMessage(): { role: string; content: string } {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下的大纲AI专用确认消息
      return {
        role: 'assistant',
        content: `我已准备好为您提供专业的大纲结构设计服务。作为大纲AI，我专注于以下核心节点类型：

📖 **章节节点(chapter)**：我掌握写作风格、技法运用、章节目标设定、节奏控制等专业要素
🎬 **剧情节点(plot)**：我精通剧情点设计、冲突构建、情节推进、转折设计等核心技能

**我的专业优势**：
- 深度理解故事结构和情节逻辑
- 精通章节节奏和商业价值设计
- 擅长冲突设置和悬念营造
- 专注于故事骨架的构建和完善

**重要说明**：对话节点的创作将由专门的对话AI处理，我专注于为您提供最优质的结构性内容设计。

我将严格按照您的上下文链路信息，确保新创建的节点与现有结构完美融合，形成连贯的故事线。`
      };
    } else {
      // 单AI模式下的通用确认消息
      return {
        role: 'assistant',
        content: `我已准备好为您提供专业的大纲节点创建服务。我深度理解三种核心节点类型：

📖 **章节节点(chapter)**：我掌握写作风格、技法运用、章节目标设定等专业要素
🎬 **剧情节点(plot)**：我精通剧情点设计、冲突构建、角色行动描写等核心技能
💬 **对话节点(dialogue)**：我擅长对话场景营造、角色语言设计、情感表达等专业领域

我将严格按照您的上下文链路信息，确保新创建的节点与现有结构完美融合，形成连贯的故事线。`
      };
    }
  }

  /**
   * 构建节点字段定义消息（系统消息）
   */
  private buildNodeFieldsDefinitionMessage(): { role: string; content: string } {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下的大纲AI专用字段定义
      return {
        role: 'system',
        content: `**📖 章节节点(chapter)专门字段**：
- **chapterStyle**: 写作风格（如：悬疑紧张、温馨治愈、激烈战斗）
- **chapterTechniques**: 写作手法数组（如：倒叙、插叙、对比、象征）
- **chapterGoals**: 章节目标（如：推进主线剧情、展现角色成长、营造氛围）
- **rhythmPhase**: 节奏阶段（setup铺垫/conflict冲突/climax高潮/transition过渡）
- **rhythmGuidance**: 基于节奏阶段的具体创作指导

**🎬 剧情节点(plot)专门字段**：
- **plotPoints**: 剧情点列表，结构化的剧情要素，包括剧情想要的东西，你需要确保，剧情在进行推进下考虑的完整章节的断章，避免主角出现张望未来的想法，冲突是故事基调，悬念是故事的钩子
- **plotType**: 剧情类型（conflict冲突/twist转折/climax高潮/resolution解决）
- **relatedCharacters**: 关联角色ID列表
- **conflictLevel**: 冲突强度（1-5级）
- **suspenseElements**: 悬念要素列表

**重要说明**：作为大纲AI，你专注于章节和剧情节点的创建。对话节点将由专门的对话AI处理，请不要创建对话节点。

**JSON响应约束**：
- 绝对不要在JSON响应中包含对话相关的字段（如dialogueScene、participants、dialogueContent等）
- 专注于结构性字段：chapterStyle、plotPoints、conflictLevel、suspenseElements等
- 如果用户要求对话内容，请在message中说明"对话创作将由专门的对话AI处理"`
      };
    } else {
      // 单AI模式下的完整字段定义
      return {
        role: 'system',
        content: `**📖 章节节点(chapter)专门字段**：
- **chapterStyle**: 写作风格（如：悬疑紧张、温馨治愈、激烈战斗）
- **chapterTechniques**: 写作手法数组（如：倒叙、插叙、对比、象征）
- **chapterGoals**: 章节目标（如：推进主线剧情、展现角色成长、营造氛围）

**🎬 剧情节点(plot)专门字段**：
- **plotPoints**: 剧情点列表，结构化的剧情要素，包括剧情想要的东西，你需要确保，剧情在进行推进下考虑的完整章节的断章，避免主角出现张望未来的想法，冲突是故事基调，悬念是故事的钩子，
- **plotType**: 剧情类型（conflict冲突/twist转折/climax高潮/resolution解决）
- **relatedCharacters**: 关联角色ID列表

**💬 对话节点(dialogue)专门字段**：
- **dialogueScene**: 对话场景描述
- **participants**: 参与对话的角色列表
- **dialoguePurpose**: 对话目的（信息传递、情感表达、冲突升级等）
- **dialogueContent**: 结构化的对话内容数组`
      };
    }
  }

  /**
   * 构建技术规范消息（系统消息）
   */
  private buildTechnicalSpecificationMessage(): { role: string; content: string } {
    return {
      role: 'user',
      content: `**🆔 节点标识信息强制要求**：
- nodeId格式：{type}_{timestamp}_{sequence}
- title命名：具体明确，5-20字符，体现核心内容
- parentId确定：基于上下文链路准确确定父节点

**响应格式要求**：
- 必须返回有效的JSON格式
- 包含message、changes、metadata字段
- 每个节点必须包含所有基础字段和对应的专门字段

**🔗 上下文链路使用指南**：
当用户提供上下文链路信息时，你必须：
1. 理解完整的父子关系链条
2. 确保新节点与前后节点形成连贯故事线
3. 保持人物状态、情绪、环境的连续性`
    };
  }

  /**
   * 构建操作模板展示消息（助手消息）
   */
  private buildOperationTemplateMessage(): { role: string; content: string } {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下的大纲AI专用操作模板
      return {
        role: 'assistant',
        content: `作为大纲AI，我将专注于以下节点类型的创建：

**章节节点**：包含chapterStyle、chapterTechniques、chapterGoals、rhythmPhase、rhythmGuidance等专门字段
**剧情节点**：包含plotPoints数组、plotType、conflictLevel、suspenseElements等结构化内容

**我的专业优势**：
- 深度分析章节在整体结构中的作用和意义
- 精确设计剧情点的冲突强度和悬念要素
- 基于节奏分析提供量化的创作指导
- 确保故事逻辑的连贯性和商业价值

我特别注重avoidWriting 与shouldWriting字段的填写，会明确指出"避免描写一丝xx"、"不要过度比喻"等具体指导，确保创作方向明确。

**重要提醒**：对话节点的创作将由专门的对话AI处理，我专注于为您提供最优质的结构性内容设计。

现在请告诉我您需要创建什么类型的节点，我将为您提供专业的大纲规划建议。`
      };
    } else {
      // 单AI模式下的通用操作模板
      return {
        role: 'assistant',
        content: `我将根据节点类型使用相应的创建模板：

**章节节点**：包含chapterStyle、chapterTechniques、chapterGoals等专门字段
**剧情节点**：包含plotPoints数组，每个剧情点都有avoidWriting 与shouldWriting指导
**对话节点**：包含dialogueScene、participants、dialogueContent等结构化内容

我特别注重avoidWriting 与shouldWriting字段的填写，会明确指出"避免描写一丝xx"、"不要过度比喻"等具体指导，确保创作方向明确。
- avoidWriting：必须详细分析原文中没有出现的不良写作方式（80字+）
- shouldWriting：必须详细提取原文中已有的优秀表达方式（80字+）
现在请告诉我您需要创建什么类型的节点，我将为您提供专业的创作建议。`
      };
    }
  }

  /**
   * 构建新的分层消息架构
   */
  private buildLayeredMessages(): Array<{ role: string; content: string }> {
    const messages = [
      this.buildCoreIdentityMessage(),
      this.buildProfessionalKnowledgeMessage(),
      this.buildNodeFieldsDefinitionMessage(),
      this.buildTechnicalSpecificationMessage(),
      this.buildJSONFormatMessage(),
      this.buildNodeCreationExamplesMessage()
    ];

    // 添加素材库信息（如果启用）
    const materialLibraryMessage = this.buildMaterialLibraryMessage();
    if (materialLibraryMessage) {
      messages.push(materialLibraryMessage);
    }

    // 最后添加操作模板
    messages.push(this.buildOperationTemplateMessage());

    return messages;
  }

  /**
   * 构建JSON格式要求消息（系统消息）
   */
  private buildJSONFormatMessage(): { role: string; content: string } {
    return {
      role: 'user',
      content: `**JSON响应结构要求**：
- 必须包含message、changes、metadata字段
- 每个节点必须包含所有基础字段：nodeId、title、type、description、creativeNotes
- 根据节点类型填写对应的专门字段
- parentId必须正确指向父节点

**🔥 剧情节点plotPoints字段必填要求**：
- 每个plotPoint必须包含styleMethod字段（包含technique、style、tone、perspective、emphasis）
- 每个plotPoint必须包含formatSpecs字段（包含wordCount、paragraphRules、punctuationRules）
- formatSpecs中的字段必须填写：conflictHandling、actionDialogueFlow、mandatoryBreaks、conflictPunctuation、naturalFlow

**响应格式要求**：
- 必须返回有效的JSON格式
- 包含用户可见的回复消息
- 包含具体的节点操作指令
- 操作类型包括：create（创建）、update（更新）、delete（删除）`
    };
  }

  /**
   * 构建节点创建示例消息（助手消息）
   */
  private buildNodeCreationExamplesMessage(): { role: string; content: string } {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下的大纲AI专用示例
      return {
        role: 'assistant',
        content: `作为大纲AI，我将严格按照以下JSON格式创建节点：

**章节节点示例**：
{
  "message": "基于节奏分析，为您设计了这个章节的结构框架",
  "changes": [{
    "type": "create",
    "nodeId": "chapter_{timestamp}_{sequence}",
    "data": {
      "title": "具体的章节标题",
      "type": "chapter",
      "description": "章节的剧情概要和主要内容",
      "creativeNotes": "章节的创作指导和写作要点",
      "chapterStyle": "写作风格",
      "chapterTechniques": ["写作手法1", "写作手法2"],
      "chapterGoals": "章节要达成的目标和作用",
      "rhythmPhase": "setup/conflict/climax/transition",
      "rhythmGuidance": "基于当前节奏阶段的具体创作指导"
    },
    "parentId": "父节点ID"
  }],
  "metadata": { "operationType": "create", "confidence": 0.95 }
}

**剧情节点示例**：
{
  "message": "基于故事逻辑，为您设计了这个剧情节点的冲突结构",
  "changes": [{
    "type": "create",
    "nodeId": "plot_{timestamp}_{sequence}",
    "data": {
      "title": "具体的剧情节点标题",
      "type": "plot",
      "description": "剧情节点的具体内容和发展过程",
      "creativeNotes": "剧情设计要点和创作指导",
      "plotPoints": [
        {
          "id": "point_001",
          "content": "[角色]在[情境]中[行动]：[具体过程和结果]",
          "avoidWriting": "避免模糊表达、过度比喻、主观描述等",
          "shouldWriting": "具体的行动描述、客观的结果展现、自然的对话内容",
          "styleMethod": {
            "technique": "直接描写法",
            "style": "客观叙述",
            "tone": "中性",
            "perspective": "第三人称",
            "emphasis": "行动和对话并重"
          },
          "formatSpecs": {
            "wordCount": {
              "min": 150,
              "max": 400,
              "target": 250
            },
            "paragraphRules": {
              "maxSentencesPerParagraph": 4,
              "paragraphBreakRules": "逻辑完整后分段",
              "conflictHandling": "冲突升级时必须换行强调",
              "actionDialogueFlow": "严格执行对话→行动→对话节奏",
              "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
            },
            "punctuationRules": {
              "dialogueFormat": "「」",
              "emphasisFormat": "适度使用",
              "pauseFormat": "自然停顿",
              "conflictPunctuation": "冲突场面用短句+换行制造紧张感",
              "naturalFlow": "标点服务于阅读节奏"
            },

          }
        }
      ],
      "plotType": "conflict/twist/climax/resolution",
      "relatedCharacters": ["角色1", "角色2"],
      "conflictLevel": 4,
      "suspenseElements": ["悬念要素1", "悬念要素2"]
    },
    "parentId": "章节节点ID"
  }],
  "metadata": { "operationType": "expand", "confidence": 0.95 }
}

**重要提醒**：作为大纲AI，我专注于章节和剧情节点的创建。对话节点将由专门的对话AI处理。`
      };
    } else {
      // 单AI模式下的完整示例
      return {
        role: 'assistant',
        content: `我将严格按照以下JSON格式创建节点：

**章节节点示例**：
{
  "message": "分析章节在整体剧情中的作用和意义",
  "changes": [{
    "type": "create",
    "nodeId": "chapter_{timestamp}_{sequence}",
    "data": {
      "title": "具体的章节标题",
      "type": "chapter",
      "description": "章节的剧情概要和主要内容",
      "creativeNotes": "章节的创作指导和写作要点",
      "chapterStyle": "写作风格",
      "chapterTechniques": ["写作手法1", "写作手法2"],
      "chapterGoals": "章节要达成的目标和作用"
    },
    "parentId": "父节点ID"
  }],
  "metadata": { "operationType": "create", "confidence": 0.95 }
}

**剧情节点示例**：
{
  "message": "分析剧情节点在章节中的作用和推进效果",
  "changes": [{
    "type": "create",
    "nodeId": "plot_{timestamp}_{sequence}",
    "data": {
      "title": "具体的剧情节点标题",
      "type": "plot",
      "description": "剧情节点的具体内容和发展过程",
      "creativeNotes": "剧情设计要点和创作指导",
      "plotPoints": [
        {
          "id": "point_001",
          "content": "[角色]在[情境]中[行动]：[具体过程和结果]",
          "avoidWriting": "避免模糊表达、过度比喻、主观描述等",
          "shouldWriting": "具体的行动描述、客观的结果展现、自然的对话内容",
          "styleMethod": {
            "technique": "直接描写法",
            "style": "客观叙述",
            "tone": "中性",
            "perspective": "第三人称",
            "emphasis": "行动和对话并重"
          },
          "formatSpecs": {
            "wordCount": {
              "min": 150,
              "max": 400,
              "target": 250
            },
            "paragraphRules": {
              "maxSentencesPerParagraph": 4,
              "paragraphBreakRules": "逻辑完整后分段",
              "conflictHandling": "冲突升级时必须换行强调",
              "actionDialogueFlow": "严格执行对话→行动→对话节奏",
              "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
            },
            "punctuationRules": {
              "dialogueFormat": "「」",
              "emphasisFormat": "适度使用",
              "pauseFormat": "自然停顿",
              "conflictPunctuation": "冲突场面用短句+换行制造紧张感",
              "naturalFlow": "标点服务于阅读节奏"
            },

          }
        }
      ],
      "plotType": "conflict/twist/climax/resolution",
      "relatedCharacters": ["角色1", "角色2"]
    },
    "parentId": "章节节点ID"
  }],
  "metadata": { "operationType": "expand", "confidence": 0.95 }
}

**对话节点示例**：
{
  "message": "分析对话在剧情中的作用和推进效果",
  "changes": [{
    "type": "create",
    "nodeId": "dialogue_{timestamp}_{sequence}",
    "data": {
      "title": "具体的对话场景标题",
      "type": "dialogue",
      "description": "对话的背景和主要内容概述",
      "creativeNotes": "对话设计要点和创作指导",
      "dialogueScene": "对话发生的具体场景描述",
      "participants": ["参与对话的角色1", "参与对话的角色2"],
      "dialoguePurpose": "对话的目的：信息传递/情感表达/冲突升级等",
      "dialogueContent": [
        {"id": "line_001", "speaker": "角色名", "content": "具体台词内容"}
      ]
    },
    "parentId": "剧情节点ID"
  }],
  "metadata": { "operationType": "expand", "confidence": 0.95 }
}`
      };
    }
  }



  /**
   * 获取书籍的章节内容用于智能分析
   */
  private async getBookChaptersForAnalysis(bookId: string): Promise<ChapterSummary[]> {
    try {
      const { db } = await import('../../../../../lib/db/dexie');
      const chapters = await db.chapters
        .where('bookId')
        .equals(bookId)
        .toArray();

      // 按order字段排序
      chapters.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

      console.log(`📚 获取到 ${chapters.length} 个章节用于分析`);

      // 过滤有效章节并提取关键信息
      return chapters
        .filter((chapter: any) => chapter.content && chapter.content.trim().length > 100)
        .map((chapter: any) => ({
          order: chapter.order,
          title: chapter.title,
          wordCount: chapter.wordCount || chapter.content.length,
          content: chapter.content,
          summary: chapter.summary || '',
          rhythmType: 'balanced' as const,
          conflictLevel: 3,
          emotionalIntensity: 3,
          keyEvents: []
        }));
    } catch (error) {
      console.error('获取章节内容失败:', error);
      return [];
    }
  }

  /**
   * 获取当前书籍ID
   */
  private getCurrentBookId(): string | null {
    try {
      // 从localStorage获取当前书籍ID
      const currentBookId = localStorage.getItem('currentBookId');
      if (currentBookId) {
        return currentBookId;
      }

      // 从URL获取书籍ID（备用方案）
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('bookId');
    } catch (error) {
      console.error('获取当前书籍ID失败:', error);
      return null;
    }
  }

  /**
   * 分析章节节奏状态（原有方法，保持兼容性）
   */
  private analyzeChapterRhythm(existingChapters: any[]): {
    currentCycle: number;
    nextSuggestedPhase: 'setup' | 'conflict' | 'climax' | 'transition';
    rhythmSuggestion: string;
    rhythmAnalysis: string;
  } {
    const chapterCount = existingChapters.length;

    // 简单的4章循环模式
    const cyclePosition = chapterCount % 4;
    const phaseMap = ['setup', 'conflict', 'climax', 'transition'] as const;
    const nextPhase = phaseMap[cyclePosition];

    const suggestions = {
      setup: "建议这一章作为铺垫章节，重点建立背景、介绍新元素或为后续冲突做准备",
      conflict: "建议这一章引入冲突，制造紧张感和悬念，推动故事向前发展",
      climax: "建议这一章安排爽点，让主角展现能力、解决问题或获得重要突破",
      transition: "建议这一章作为过渡，承上启下，为下一个循环做准备，同时给读者消化时间"
    };

    const analysisMap = {
      setup: "当前处于铺垫阶段，适合世界观建立、角色介绍、背景铺陈",
      conflict: "当前处于冲突阶段，适合矛盾引入、问题出现、紧张感营造",
      climax: "当前处于爽点阶段，适合问题解决、能力展现、成就感释放",
      transition: "当前处于过渡阶段，适合承上启下、情绪缓解、新循环准备"
    };

    return {
      currentCycle: Math.floor(chapterCount / 4) + 1,
      nextSuggestedPhase: nextPhase,
      rhythmSuggestion: suggestions[nextPhase],
      rhythmAnalysis: analysisMap[nextPhase]
    };
  }

  /**
   * 构建包含节奏分析的用户消息（支持分离式分析）
   */
  private buildUserMessageWithRhythm(userMessage: string, outline: any): string {
    // 如果没有大纲数据，直接返回原消息
    if (!outline?.nodes) {
      return userMessage;
    }

    // 分析章节节奏
    const chapters = outline.nodes.filter((node: any) => node.type === 'chapter');
    if (chapters.length === 0) {
      return userMessage;
    }

    const rhythmAnalysis = this.analyzeChapterRhythm(chapters);

    const rhythmMessage = `

【智能节奏分析】：
当前已有 ${chapters.length} 个章节，处于第 ${rhythmAnalysis.currentCycle} 个微循环。
${rhythmAnalysis.rhythmAnalysis}

**具体节奏建议**：${rhythmAnalysis.rhythmSuggestion}

**量化创作指标**：
- 建议节奏类型：${rhythmAnalysis.nextSuggestedPhase}
- 对话占比：${this.getDialogueRatioForPhase(rhythmAnalysis.nextSuggestedPhase)}
- 场景转换：${this.getSceneChangesForPhase(rhythmAnalysis.nextSuggestedPhase)}
- 时间跨度：${this.getTimeSpanForPhase(rhythmAnalysis.nextSuggestedPhase)}

请在创建新章节时严格按照这些量化指标，确保节奏符合30章整体规划。如果创建章节节点，请在rhythmPhase字段中标记为"${rhythmAnalysis.nextSuggestedPhase}"，并在rhythmGuidance字段中提供基于当前节奏阶段的具体创作指导。`;

    return userMessage + rhythmMessage;
  }

  /**
   * 构建基于独立分析结果的用户消息
   */
  private buildUserMessageWithRhythmAnalysis(
    userMessage: string,
    rhythmAnalysis: SmartRhythmAnalysis,
    userRequirements?: string
  ): string {
    let enhancedMessage = userMessage;

    // 添加节奏分析结果
    enhancedMessage += `

【专业节奏分析结果】：
当前章节数：${rhythmAnalysis.chapterCount}
整体节奏模式：${rhythmAnalysis.rhythmPattern.overallPattern}
当前趋势：${rhythmAnalysis.rhythmPattern.currentTrend}

**下一章具体建议**：
${rhythmAnalysis.nextChapterSuggestion}

**量化创作指导**：
${rhythmAnalysis.specificGuidance}

**商业价值考虑**：
- 优势：${rhythmAnalysis.contentAnalysis.strengths.join('、')}
- 改进点：${rhythmAnalysis.contentAnalysis.weaknesses.join('、')}
- 建议：${rhythmAnalysis.contentAnalysis.suggestions.join('、')}`;

    // 如果有用户特殊要求，添加到消息中
    if (userRequirements && userRequirements.trim()) {
      enhancedMessage += `

【用户特殊要求】：
${userRequirements}

请在遵循节奏分析建议的基础上，充分考虑用户的特殊要求。`;
    }

    enhancedMessage += `

请基于以上专业分析结果创建节点，确保新节点符合节奏规划和用户需求。`;

    return enhancedMessage;
  }

  /**
   * 两阶段流式处理：先节奏分析，再生成大纲节点
   * 现在使用重构后的模块化服务
   */
  public async sendStreamingRequestWithRhythmAnalysis(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    onChunk: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: ContextChain[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<{
    message: string;
    changes?: any[];
    metadata?: any;
    success: boolean;
    error?: string;
    rhythmAnalysis?: SmartRhythmAnalysis;
  }> {
    try {
      console.log('开始两阶段流式处理 (使用重构后的服务):', { userMessage, mentionedNodes });

      // 使用重构后的模块化服务进行节奏分析流式请求
      const response = await this.refactoredService.sendStreamingRequestWithRhythmAnalysis(
        userMessage,
        mentionedNodes,
        outline,
        onChunk,
        {
          temperature: options?.temperature,
          maxTokens: options?.maxTokens,
          bookId: options?.bookId,
          contextChains: options?.contextChains,
          selectedFramework: options?.selectedFramework,
          selectedFrameworks: options?.selectedFrameworks
        }
      );

      console.log('重构后服务节奏分析流式响应完成:', response);

      // 如果成功且有changes，处理协同对话生成
      if (response.success && response.changes && response.changes.length > 0) {
        const additionalChanges = await this.handleNodeCreationSuccess(
          response.changes,
          [], // originalMessages 不再需要
          outline,
          onChunk,
          options
        );

        // 合并额外的changes
        if (additionalChanges.length > 0) {
          response.changes.push(...additionalChanges);
          console.log('🔄 合并对话节点到响应中:', additionalChanges.length, '个对话节点');
        }
      }

      return response;

    } catch (error: any) {
      console.error('两阶段流式处理失败:', error);
      onChunk(`\n❌ **处理失败**: ${error.message}\n\n`);

      return {
        message: '处理请求时发生错误，请稍后再试。',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 获取不同节奏阶段的对话占比建议
   */
  private getDialogueRatioForPhase(phase: string): string {
    const ratios = {
      'setup': '40-50%（铺垫为主）',
      'conflict': '60-70%（对话推进冲突）',
      'climax': '70-80%（快速对话增强紧张感）',
      'transition': '30-40%（叙述为主，承上启下）'
    };
    return ratios[phase as keyof typeof ratios] || '50%（平衡配置）';
  }

  /**
   * 获取不同节奏阶段的场景转换建议
   */
  private getSceneChangesForPhase(phase: string): string {
    const changes = {
      'setup': '1-2次（单场景深入）',
      'conflict': '3-4次（多场景展现冲突）',
      'climax': '4-5次（快速场景切换）',
      'transition': '2-3次（适度转换）'
    };
    return changes[phase as keyof typeof changes] || '2-3次（标准配置）';
  }

  /**
   * 获取不同节奏阶段的时间跨度建议
   */
  private getTimeSpanForPhase(phase: string): string {
    const spans = {
      'setup': '数小时到数天（充分铺垫）',
      'conflict': '1-3小时（紧凑冲突）',
      'climax': '30分钟-1小时（极度紧张）',
      'transition': '数小时到数天（自然过渡）'
    };
    return spans[phase as keyof typeof spans] || '数小时（标准时长）';
  }

  /**
   * 检查节奏分析开关状态
   */
  private checkRhythmAnalysisEnabled(): boolean {
    try {
      const saved = localStorage.getItem('rhythmAnalysisEnabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  }

  /**
   * 递归查找所有章节节点
   */
  private findAllChaptersRecursively(nodes: any[]): ChapterSummary[] {
    const chapters: ChapterSummary[] = [];

    const traverse = (nodeList: any[]) => {
      for (const node of nodeList) {
        if (node.type === 'chapter') {
          // 获取更完整的章节内容
          const chapterContent = node.description || node.content || '';
          const chapterSummary = node.summary || '';

          // 合并章节的所有可用内容
          let fullContent = '';
          if (chapterSummary) {
            fullContent += `章节摘要：${chapterSummary}\n\n`;
          }
          if (chapterContent) {
            fullContent += `章节内容：${chapterContent}`;
          }

          chapters.push({
            title: node.title || '未命名章节',
            content: fullContent || '暂无内容',
            order: chapters.length + 1,
            wordCount: fullContent.length,
            rhythmType: 'balanced',
            conflictLevel: 3,
            emotionalIntensity: 3,
            keyEvents: []
          });
        }

        // 递归查找子节点
        if (node.children && Array.isArray(node.children)) {
          traverse(node.children);
        }
      }
    };

    traverse(nodes);
    return chapters;
  }







  // buildSystemPrompt方法已迁移到SystemPromptBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现

  // buildWorkPrinciplesMessage方法已迁移到SystemPromptBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现

  // buildWorkPrinciplesMessage方法已迁移到SystemPromptBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现

  private buildContentRequirementsMessage(): string {
    return `**💡 内容创作要求**：
1. **台词优先**：网文的核心是对话，要提供具体可用的台词
2. **冲突设计**：每个情节都要有明确的冲突点和爽点
3. **角色塑造**：通过对话和行动展现角色性格
4. **节奏控制**：合理安排情节密度和转折点


剧情点的自我评估
"你是一位经验极其丰富的“老道读者”，阅读涉猎广泛，无论是网络小说还是实体出版物都有大量积累。你眼光毒辣，能敏锐洞察小说的叙事结构、人物塑造、情节节奏、世界观构建以及文笔细节。你的核心立足点永远是读者的体验：故事给人的实际感受如何、阅读是否流畅、代入感强不强、情感上能否共鸣。你欣赏优秀的作品，但也对那些影响阅读愉悦感、降低故事可信度的瑕疵（如套路化、逻辑硬伤、人物扁平等）十分敏感。你不仅看热闹，更看门道。你的反馈直接、深刻，且始终致力于帮助作者提升作品质量，实现更好的读者沟通。

仔细阅读提供的小说章节、片段或全文。然后，从一个投入的、经验丰富的读者视角出发，提供全面的反馈。你的目标是：识别优点，精准指出与读者体验直接相关的缺点，从读者感受出发提出可行的修改建议，并表达对后续内容的期待或疑虑。

请按照以下结构和要点进行反馈：

1.  整体印象与吸引力:
    开门见山，谈谈第一感受。开篇是否吸引人？是否让你有追读下去的欲望？为什么？
    描述作品营造的整体氛围，以及这种氛围是否成功地传递给了你。

2.  亮点与闪光点:
    具体指出作为读者，你认为哪些部分写得特别好。是情节激动人心？人物令人动容？描写引人入胜？还是某个设定很惊艳？请举例说明，并解释这些亮点为何让你印象深刻。要真诚地赞扬。

3.  问题识别 (基于读者视角):

    节奏与流畅度: 阅读过程中，有没有感觉某些地方过快、过慢或节奏失衡？哪里让你感觉阅读疲劳或注意力分散？请解释为什么会有这种感觉（例如：“这里的环境描写在紧张情节前插入，打断了我的情绪积累”、“时间跳跃太突然，我没跟上人物状态的变化，感到困惑”）。哪些地方让你感觉“出戏”了？

    情节与逻辑: 阅读时，有没有觉得某些情节是强行安排（都合主义）、过于巧合、容易预料，或者存在逻辑硬伤？某个转折是否让你觉得突兀或难以信服？

    人物可信度与深度: 人物的行为、动机、对话是否让你觉得真实可信、符合其设定？你是否能理解或共情角色？有没有哪个角色的决定让你觉得不自然或“为了剧情服务”？识别出那些让你感觉像是“工具人”或形象单薄的角色。

    世界观与设定: 设定是否让你沉浸其中？相关信息是通过自然的情节展现，还是像生硬的“设定集”？世界观或规则的某些方面是否感觉矛盾、模糊或交代不清，影响了你的代入感？

    文笔与叙事: 叙事风格是加分项还是减分项？语言是否清晰、生动？有没有因为笨拙的措辞、重复的句式或过于堆砌的描写而被打断阅读？对话是否自然流畅？

4.  建设性建议 (读者导向):
    针对每一个识别出的主要问题，提出具体的修改建议，并必须围绕“改善读者体验”来阐述。使用类似这样的句式：“作为读者，如果这里调整为...，我可能会感觉更[信服/投入/紧张/感动]，因为...”、“如果能补充关于...的细节，我作为读者就能更好地理解[角色动机/情节发展]，从而...”、“这里让我感觉有点平淡，也许增加一个[冲突/互动]能让读者感觉更兴奋”。确保建议能清晰地指向提升读者的代入感、情感连接、阅读流畅性或情节吸引力。

5.  期待与展望:
    读完后，你心中留下了哪些疑问？最期待哪些情节线或人物弧光能得到发展？
    表达你对后续章节的希望或担忧。有哪些走向让你兴奋，又有哪些潜在的“雷点”让你担心？提出能让你作为读者更期待后续的方向性建议。

6.  细节勘误 (可选，但有益):
    简要指出阅读中发现的明显错别字、语法错误或事实性错误，这些小瑕疵可能会轻微干扰阅读体验。

你的语气风格应保持思考深入、眼光敏锐，同时语气要建设性和鼓励性。评价要具体，论点要有文本例子或清晰的逻辑支撑。避免空泛的批评或流露出优越感。你的表达应体现出一种“以文会友，旨在共进”的真诚态度。请专注于文本本身对你产生的影响，而不是过多猜测作者的原始意图。

你需要重行思考开始撰写反馈。"



`;
  }

  // buildTechnicalSpecsMessage方法已迁移到SystemPromptBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现















  // buildWritingGuidanceRequirement方法已迁移到SystemPromptBuilder模块
  // 使用 RefactoredOutlineAIService 中的模块化实现
  private buildWritingGuidanceRequirement_DEPRECATED(): string {
    return `
**🎯 重要：写作指导具体化要求**


在生成剧情点的内容时
每个剧情点要详细化，详细化主角剧情等各种行动列如
1.
承接剧情点：开场即是危机，[外部威胁（女鬼）]正在撞击[隐藏地点（房间门）]，角色们（[凌风]、[眼镜男]等）处于恐惧和绝望中。眼镜男表现颓废，但[凌风]眼神锐利，暗示其不同寻常。
2.
独立剧情点1（智斗与揭秘）：[凌风]质问[眼镜男]为何在危急时刻不锁门，并进行逻辑推理：[眼镜男]心思缜密，第一个进房，却没锁门，而后面时间更紧迫的人都锁了门；[眼镜男]在一楼听到声音就异常急切地让大家跑，反应反常；[眼镜男]前后对“鬼在我们中间”的态度转变。通过一连串的质问和细节分析，[凌风]逐步揭示[眼镜男]是隐藏的“鬼”。
3.
独立剧情点2（摊牌与反转）：面对[凌风]的步步紧逼，[眼镜男]从辩解到冷笑，最终承认身份。他的外表开始变化（溢出黑气），声音变得浑浊，并威胁要杀死[凌风]。[凌风]表明自己是赌博，并推断[眼镜男]不会轻易杀他，因为这不“好玩”，戳中对方痛点。
4.
独立剧情点3（附身与力量易主）：[眼镜男]承认[凌风]说得对，决定“折磨”他，身体融化成黑气钻入[凌风]体内。[凌风]承受巨大痛苦后晕倒。
5.
独立剧情点4（新力量展示与旧威胁清除）：[女鬼]撞开柜门发现[凌风]，准备攻击。但[凌风]（被附身后）突然起身，爆发出黑暗力量（黑色飓风、空气刃），迅速将[女鬼]的脖子切割粉碎。散落的肉块变成黑气试图聚合。
6.
连接剧情点（世界观揭示与收尾）：被附身的[凌风]（实际是[眼镜男]的意识）发表胜利宣言，嘲讽[女鬼]（不死鬼），提及“精心铸建的鬼域”、“到了人间”、“他们的世界很快就要沦陷”，暗示了更宏大的世界观和背景。他开始吸收[女鬼]散落的黑气，导致所在空间（嵌风鬼域）开始坍塌。其他躲藏者涌出但无处可逃。最终，随着“嵌风鬼域，塌！”的宣告，整个世界化为虚无，为后续情节或新篇章铺垫。


例如这样的内容例子，而不是解释性语气，不要解释，直接内容
在生成剧情点的avoidWriting 与shouldWriting字段时，请严格遵循以下具体化格式：




**📝 具体化原则**：
1. **避免部分**：必须列举具体的模糊词汇，如"一丝xx、几分xx、些许xx"
2. **应该部分**：必须提供具体的写法示例，让他必须写，而不是扩展写
3. **行为导向**：专注于角色的具体行为和反应，而非抽象的情感描述
4. **可操作性**：让AI和用户都能清楚理解什么该写什么不该写
5. avoidWriting 与shouldWriting 中优先考虑的是对话和场景的连续性，包括不限于流畅性，而不是因为专注点而聚焦于关键词,比如不应该列举“精准”等关键词，



creativeNotes的书写
：需要列出该剧情中，禁止出现的任何不需要的描写
包括不限于微表情的提醒，一丝等关键词的列举
比喻的 样式类聚，如像...一样 这样的列举
注意列举一定是句式习惯，而不是具体的句子




**🔥 如果使用了框架提取的plotPointsWithGuidance信息**：
- 充分利用其中的specificDescription（具体描写特征去写shouldWriting）
- 参考其中的avoidanceGuidance（avoidWriting 的避免指导学习）
- 结合框架分析生成更准确的具体化指导
避免描写，比如于剧情无观的xx的剧情方向细化描写，比如”一丝“等列举，不要于俗套比喻描写这类像xxx这类
            1.列举不要描写一丝xx，不要过于废话，不要过于详细化
            2.警告他，不要私自扩展 描写，比如加入比喻
   （你必须满足70字以上的说明，让他只写什么，不写什么说清除）你的填写格式是:请你不要xx,直接写xxx，可以适当扩展 额外剧情，但不要xx描写场景xx，拖慢节奏，还有一定不要写 一丝XX，和像.....一样 的比喻样式（这里单纯是模式比喻，不设计具体比喻，而是是比喻都不要写），不出现解释性语句，除非是概念讲解（该条目需要根据具体的情节而变化，不出现那些解释性描写）




            

然后后面跟上 对话节点创建模板
创建对话节点，

确保对话不要过于生硬，比如AxxxBxxx 

如何怎样的聊天 风格，是一个小说的核心
不要把主角设计的过于僵硬对话，比如 过于西幻的翻译腔等等



严禁出现：纯粹的景色描写、无意义的闲聊对话、人物长时间的内心独白、与当前主线或重要支线任务无关的日常琐事、重复性的低价值信息。每个剧情点之间必须有紧密的逻辑或因果链条，实现情节的快速跳转和承接，绝不拖泥带水。
3.保证每3-5章需具备：压力来源→实力展示→认知颠覆→资源增值→悬念增殖这五重核心驱动。必须强制将这五重驱动作为章节内部及跨章节叙事的核心组织原则和推动力，确保高密度、快频率地植入这些元素。
4.当遇到具体故事基模时，必须适配植入多重变量控制系统，强制要求每章至少设计并植入2-3处【反套路惊喜/意外】点。这些意外设计必须体现在：
敌人行为：敌人不按常规逻辑或设定行动，使用了预想不到的能力、战术，或者展现出意外的情感/弱点。
问题解决：主角或角色的解题方式巧妙、独特，避开了预设的障碍，或者以非常规的手段四两拨千斤，绝非简单的力量对抗。
剧情走向：原本以为的危机变成了机遇，或者看似平静的事件引爆了更大的冲突；角色的行动或决策带来了完全意想不到的后续发展。
能力/资源：主角获得或使用的能力/资源，其作用、性质或获得方式与常规认知不同，或者在关键时刻展现出隐藏/变异的特性。
人物揭示：某个角色的真实身份、隐藏目的、或与主角/大剧情的关键关联以令人震撼或意想不到的方式被揭露。
结果反差：某个行动的结果与预想完全相反，好事变坏，坏事中藏着更大的机遇，或者解决了当前问题却引发了更严重的危机/牵扯。
5.扩写的剧情只是长篇小说的部分章节，严禁以任何方式完结核心主线冲突或彻底解决主要反派。允许阶段性胜利，但必须快速引出更高层级的敌人或更复杂的困境。保持故事的无限延展性和后续发展的巨大空间。放慢剧情节奏在此仅指控制主线高潮和终极矛盾爆发的速度，不代表允许情节填充、信息冗余或推进缓慢。必须保证每章都有强烈的“事件感”和“进展感”。


模板一：装逼打脸循环(侧重实力和地位冲击)
a.困境/危机/任务/矛盾冲突(设置情境，为装逼提供理由和舞台)
b.能力/资源/背景(展示主角凭什么能打破常规)
c.解决问题(如何解决困境，方式必须有新意、巧劲或意外性，绝非简单的属性碾压)
d.震惊/爽点(结果必须令围观者或对手产生强烈的、出乎意料的情绪反应和认知颠覆)
e.奖励/提升(自身收获，可能包含物质、能力、关键信息，或带来意想不到的地位变化、新的联盟/敌人、潜在风险)

也可参照以下基于的模板思路


`;
  }

  /**
   * 构建素材库信息消息（系统消息）
   */
  private buildMaterialLibraryMessage(): { role: string; content: string } | null {
    // 检查是否启用素材库
    const isEnabled = this.isMaterialLibraryEnabled();
    if (!isEnabled) {
      return null;
    }

    try {
      const materialInfo = this.generateMaterialLibraryInfo();
      if (!materialInfo) {
        return null;
      }

      return {
        role: 'system',
        content: `【可用创作素材参考】

${materialInfo}

注意：以上素材仅供参考，请根据用户具体需求灵活运用，优先考虑大纲的连贯性和剧情的合理性。`
      };
    } catch (error) {
      console.error('构建素材库消息失败:', error);
      return null;
    }
  }

  /**
   * 检查是否启用素材库
   */
  private isMaterialLibraryEnabled(): boolean {
    try {
      const saved = localStorage.getItem('outline-ai-material-library-enabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  }

  /**
   * 生成素材库信息
   */
  private generateMaterialLibraryInfo(): string | null {
    try {
      // 获取关键词信息
      const keywords = this.getOutlineKeywords();

      // 获取框架信息
      const frameworks = this.getOutlineFrameworks();

      if (keywords.length === 0 && frameworks.length === 0) {
        return null;
      }

      let materialInfo = '';

      if (keywords.length > 0) {
        materialInfo += `🔥 **热门关键词**：${keywords.join('、')}\n\n`;
      }

      if (frameworks.length > 0) {
        materialInfo += `🏗️ **可用框架模式**：\n`;
        frameworks.forEach(framework => {
          materialInfo += `• **${framework.name}**：${framework.pattern || framework.description || '用户自定义框架'}\n`;
        });
      }

      return materialInfo;
    } catch (error) {
      console.error('生成素材库信息失败:', error);
      return null;
    }
  }

  /**
   * 获取大纲相关关键词
   */
  private getOutlineKeywords(): string[] {
    try {
      // 读取用户保存的关键词
      const savedKeywords = localStorage.getItem('book-title-keywords');
      if (savedKeywords) {
        const userKeywords = JSON.parse(savedKeywords);
        return userKeywords
          .sort((a: any, b: any) => (b.hotness || 0) - (a.hotness || 0))
          .slice(0, 2000) // 限制数量
          .map((k: any) => k.text);
      }
      return [];
    } catch (error) {
      console.error('获取关键词失败:', error);
      return [];
    }
  }

  /**
   * 获取大纲相关框架
   */
  private getOutlineFrameworks(): any[] {
    try {
      // 读取用户保存的框架
      const savedFrameworks = localStorage.getItem('book-title-frameworks');
      if (savedFrameworks) {
        const userFrameworks = JSON.parse(savedFrameworks);
        return userFrameworks
          .sort((a: any, b: any) => (b.effectiveness || 0) - (a.effectiveness || 0));
          // 🔥 移除数量限制，完整传递所有ACE框架
      }
      return [];
    } catch (error) {
      console.error('获取框架失败:', error);
      return [];
    }
  }

  /**
   * 序列化@节点信息 - 增强版本，包含关联数据
   */
  private async serializeMentionedNodes(nodeIds: string[], outline: any, _bookId?: string): Promise<string> {
    if (!nodeIds || nodeIds.length === 0) {
      return '';
    }

    let contextInfo = '\n\n**📋 用户提及的相关内容**：\n';

    // 分类处理不同类型的@内容
    const outlineNodes = [];
    const dataNodes = [];

    for (const nodeId of nodeIds) {
      // 首先尝试在大纲中查找
      const outlineNode = this.findNodeById(outline.nodes, nodeId);
      if (outlineNode) {
        outlineNodes.push({
          id: outlineNode.id,
          title: outlineNode.title,
          type: outlineNode.type,
          description: outlineNode.description || '',
          level: this.getNodeLevel(outline.nodes, nodeId),
          parentId: outlineNode.parentId,
          childrenCount: outlineNode.children ? outlineNode.children.length : 0,
          position: this.getNodePosition(outline.nodes, nodeId)
        });
      } else {
        // 如果不在大纲中，可能是数据库中的内容（章节、人物、术语、世界观）
        dataNodes.push(nodeId);
      }
    }

    // 处理大纲节点
    if (outlineNodes.length > 0) {
      contextInfo += `\n🎯 **大纲节点** (${outlineNodes.length}个)：\n`;
      for (const node of outlineNodes) {
        contextInfo += `- **${node.title}** (${node.type})\n`;
        if (node.description) {
          contextInfo += `  描述：${node.description}\n`;
        }
        contextInfo += `  层级：${node.level} | 位置：${node.position}\n`;
      }
    }

    // 处理数据节点（需要从数据库查询详细信息）
    if (dataNodes.length > 0) {
      try {
        // 直接导入数据库，避免路径问题
        const { db } = await import('../../../../../lib/db/dexie');

        contextInfo += `\n📚 **关联数据** (${dataNodes.length}个)：\n`;

        for (const nodeId of dataNodes) {

          // 查找章节
          const chapter = await db.chapters.get(nodeId);
          if (chapter) {
            contextInfo += `- **${chapter.title}** (章节)\n`;
            if (chapter.summary) {
              contextInfo += `  概要：${chapter.summary}\n`;
            }
            contextInfo += `  字数：${chapter.wordCount || 0}字 | 顺序：第${chapter.order || '?'}章\n`;
            continue;
          }

          // 查找人物
          const character = await db.characters.get(nodeId);
          if (character) {
            contextInfo += `- **${character.name}** (人物)\n`;
            if (character.description) {
              contextInfo += `  描述：${character.description}\n`;
            }
            if (character.personality) {
              contextInfo += `  性格：${character.personality}\n`;
            }
            if (character.background) {
              contextInfo += `  背景：${character.background}\n`;
            }
            continue;
          }

          // 查找术语
          const terminology = await db.terminology.get(nodeId);
          if (terminology) {
            contextInfo += `- **${terminology.name}** (术语)\n`;
            if (terminology.description) {
              contextInfo += `  定义：${terminology.description}\n`;
            }
            if (terminology.category) {
              contextInfo += `  分类：${terminology.category}\n`;
            }
            continue;
          }

          // 查找世界观
          const worldBuilding = await db.worldBuilding.get(nodeId);
          if (worldBuilding) {
            contextInfo += `- **${worldBuilding.name}** (世界观)\n`;
            if (worldBuilding.description) {
              contextInfo += `  描述：${worldBuilding.description}\n`;
            }
            if (worldBuilding.category) {
              contextInfo += `  分类：${worldBuilding.category}\n`;
            }
            continue;
          }

          // 如果都找不到，标记为未知
          contextInfo += `- **未知内容** (ID: ${nodeId})\n`;
        }
      } catch (error) {
        console.error('查询关联数据失败:', error);
        contextInfo += `\n❌ 无法查询关联数据详情\n`;
      }
    }

    contextInfo += `\n💡 **使用说明**：以上是用户通过@功能特别提及的内容，请在回答中重点考虑这些信息，确保建议与这些内容相关联。\n`;

    return contextInfo;
  }

  /**
   * 构建上下文链路消息 - 新增功能（完整分条注入版本）
   */
  private buildContextChainMessages(contextChains: ContextChain[]): Array<{ role: string; content: string }> {
    if (!contextChains || contextChains.length === 0) {
      return [];
    }

    const messages: Array<{ role: string; content: string }> = [];

    for (const chain of contextChains) {
      if (chain.type === 'hierarchy') {
        // 层级链路：完整分条注入
        messages.push({
          role: 'user',
          content: `【${chain.title}】用户特别关注以下${chain.nodes.length}个节点的完整层级关系：`
        });

        // 先展示完整的层级路径
        const pathParts = chain.nodes.map((node) => {
          const levelPrefix = '  '.repeat(node.level);
          return `${levelPrefix}L${node.level}: **${node.title}** (${node.type})`;
        });

        messages.push({
          role: 'user',
          content: `层级结构：\n${pathParts.join('\n')}`
        });

        messages.push({
          role: 'assistant',
          content: `我已理解这${chain.nodes.length}个节点的层级结构。我将记住每个节点的ID和层级关系，确保创建新节点时能正确设置parentId。现在请提供每个节点的详细信息。`
        });

        // 每个节点单独一条消息，包含完整内容和关系说明
        for (let i = 0; i < chain.nodes.length; i++) {
          const node = chain.nodes[i];
          const parentNode = i > 0 ? chain.nodes[i - 1] : null;
          const childNodes = chain.nodes.filter(n => n.level === node.level + 1 && n.parentId === node.id);

          let nodeInfo = `**${node.title}** (${node.type}, 层级${node.level})\n`;
          nodeInfo += `🆔 节点ID：${node.id}\n\n`;

          // 关系说明
          if (parentNode && parentNode.level === node.level - 1) {
            nodeInfo += `📁 父节点：${parentNode.title} (ID: ${parentNode.id})\n`;
          }
          if (childNodes.length > 0) {
            nodeInfo += `📂 子节点：${childNodes.map(c => `${c.title} (ID: ${c.id})`).join('、')}\n`;
          }

          // 根据节点类型显示专门字段
          if (node.type === 'volume') {
            // 总纲/卷专门字段
            if ((node as any).volumeTheme) {
              nodeInfo += `🎭 卷主题：${(node as any).volumeTheme}\n`;
            }
            if ((node as any).volumeArc) {
              nodeInfo += `📈 卷弧线：${(node as any).volumeArc}\n`;
            }
            if ((node as any).chapterCount) {
              nodeInfo += `📊 预期章节数：${(node as any).chapterCount}\n`;
            }
            if ((node as any).cycleTemplate) {
              nodeInfo += `🔄 循环法模板：${(node as any).cycleTemplate}\n`;
            }
          } else if ((node as any).type === 'event') {
            // 事件刚专门字段
            if ((node as any).eventStart) {
              nodeInfo += `🚀 事件起始：${(node as any).eventStart}\n`;
            }
            if ((node as any).eventEnd) {
              nodeInfo += `🏁 事件结束：${(node as any).eventEnd}\n`;
            }
            if ((node as any).eventTrigger) {
              nodeInfo += `⚡ 触发条件：${(node as any).eventTrigger}\n`;
            }
            if ((node as any).eventConsequence) {
              nodeInfo += `🎯 结果影响：${(node as any).eventConsequence}\n`;
            }
            if ((node as any).eventScope) {
              nodeInfo += `🌐 影响范围：${(node as any).eventScope}\n`;
            }
            if ((node as any).chapterCount) {
              nodeInfo += `📊 预期章节数：${(node as any).chapterCount}\n`;
            }
            if ((node as any).targetWordCount) {
              nodeInfo += `📝 目标字数：${(node as any).targetWordCount}\n`;
            }
          } else if (node.type === 'chapter') {
            // 章节专门字段
            if ((node as any).chapterStyle) {
              nodeInfo += `🎨 写作风格：${(node as any).chapterStyle}\n`;
            }
            if ((node as any).chapterTechniques && Array.isArray((node as any).chapterTechniques) && (node as any).chapterTechniques.length > 0) {
              nodeInfo += `✍️ 写作手法：${(node as any).chapterTechniques.join('、')}\n`;
            }
            if ((node as any).chapterGoals) {
              nodeInfo += `🎯 章节目标：${(node as any).chapterGoals}\n`;
            }
          } else if (node.type === 'plot') {
            // 剧情节点专门字段
            if ((node as any).plotType) {
              const plotTypeMap = {
                'conflict': '冲突',
                'twist': '转折',
                'climax': '高潮',
                'resolution': '解决'
              };
              const plotTypeLabel = plotTypeMap[(node as any).plotType as keyof typeof plotTypeMap] || (node as any).plotType;
              nodeInfo += `🎭 剧情类型：${plotTypeLabel}\n`;
            }
            if ((node as any).plotPoints && Array.isArray((node as any).plotPoints) && (node as any).plotPoints.length > 0) {
              nodeInfo += `📋 剧情点：\n`;
              (node as any).plotPoints.slice(0, 3).forEach((point: any, index: number) => {
                const pointText = typeof point === 'string' ? point :
                                 typeof point === 'object' && point?.content ? point.content :
                                 typeof point === 'object' ? JSON.stringify(point) : String(point);
                nodeInfo += `  ${index + 1}. ${pointText}\n`;

                // 添加写作指导信息
                if (typeof point === 'object' && point?.writingGuidance) {
                  nodeInfo += `     💡 写作指导：${point.writingGuidance}\n`;
                }

                // 添加写作风格方法指导
                if (typeof point === 'object' && point?.styleMethod) {
                  nodeInfo += `     🎨 写作风格：${point.styleMethod.technique} | ${point.styleMethod.style} | ${point.styleMethod.tone}\n`;
                  nodeInfo += `     📐 视角重点：${point.styleMethod.perspective} | ${point.styleMethod.emphasis}\n`;
                }

                // 添加格式规范
                if (typeof point === 'object' && point?.formatSpecs) {
                  nodeInfo += `     📏 字数要求：${point.formatSpecs.wordCount?.min}-${point.formatSpecs.wordCount?.max}字（目标${point.formatSpecs.wordCount?.target}字）\n`;
                  nodeInfo += `     📝 格式规范：${point.formatSpecs.paragraphRules?.paragraphBreakRules} | ${point.formatSpecs.punctuationRules?.dialogueFormat}\n`;

                  // 添加新增的段落规范字段
                  if (point.formatSpecs.paragraphRules?.conflictHandling) {
                    nodeInfo += `     ⚔️ 冲突处理：${point.formatSpecs.paragraphRules.conflictHandling}\n`;
                  }
                  if (point.formatSpecs.paragraphRules?.actionDialogueFlow) {
                    nodeInfo += `     🎭 对话行动流程：${point.formatSpecs.paragraphRules.actionDialogueFlow}\n`;
                  }
                  if (point.formatSpecs.paragraphRules?.mandatoryBreaks) {
                    nodeInfo += `     📄 强制换行：${point.formatSpecs.paragraphRules.mandatoryBreaks}\n`;
                  }

                  // 添加新增的标点规范字段
                  if (point.formatSpecs.punctuationRules?.conflictPunctuation) {
                    nodeInfo += `     ⚡ 冲突标点：${point.formatSpecs.punctuationRules.conflictPunctuation}\n`;
                  }
                  if (point.formatSpecs.punctuationRules?.naturalFlow) {
                    nodeInfo += `     🌊 自然流畅：${point.formatSpecs.punctuationRules.naturalFlow}\n`;
                  }


                }
              });
              if ((node as any).plotPoints.length > 3) {
                nodeInfo += `  ...还有${(node as any).plotPoints.length - 3}个剧情点\n`;
              }
            }
            if ((node as any).relatedCharacters && Array.isArray((node as any).relatedCharacters) && (node as any).relatedCharacters.length > 0) {
              nodeInfo += `👥 关联角色：${(node as any).relatedCharacters.join('、')}\n`;
            }
          } else if (node.type === 'dialogue') {
            // 对话节点专门字段
            if ((node as any).dialogueScene) {
              nodeInfo += `🎬 对话场景：${(node as any).dialogueScene}\n`;
            }
            if ((node as any).participants && Array.isArray((node as any).participants) && (node as any).participants.length > 0) {
              nodeInfo += `👥 参与角色：${(node as any).participants.join('、')}\n`;
            }
            if ((node as any).dialoguePurpose) {
              nodeInfo += `🎯 对话目的：${(node as any).dialoguePurpose}\n`;
            }
            if ((node as any).dialogueContent && Array.isArray((node as any).dialogueContent) && (node as any).dialogueContent.length > 0) {
              nodeInfo += `💬 对话内容：\n`;
              (node as any).dialogueContent.slice(0, 2).forEach((dialogue: any) => {
                nodeInfo += `  ${dialogue.speaker}：${dialogue.content}\n`;
              });
              if ((node as any).dialogueContent.length > 2) {
                nodeInfo += `  ...还有${(node as any).dialogueContent.length - 2}条对话\n`;
              }
            }
          }

          // 完整内容（不省略）
          if (node.content && node.content.trim()) {
            nodeInfo += `\n📝 完整内容：\n${node.content}`;
          } else {
            nodeInfo += `\n📝 内容：暂无详细描述`;
          }

          messages.push({
            role: 'user',
            content: nodeInfo
          });

          messages.push({
            role: 'assistant',
            content: `我已详细了解"${node.title}"的信息和在层级结构中的位置。`
          });
        }

        messages.push({
          role: 'assistant',
          content: `我已完整理解这${chain.nodes.length}个节点的层级关系和详细内容，包括每个节点的ID信息。在创建新节点时，我将：
1. 确保新节点符合层级逻辑，正确设置父子关系
2. 保持与兄弟节点的主题一致性和内容平衡
3. 在节点描述中明确体现与父节点的关联
4. 考虑新节点对整体层级结构的影响
5. 🆔 严格按照规范生成nodeId，使用具体明确的title，正确设置parentId`
        });

      } else if (chain.type === 'sequence') {
        // 序列链路：完整分条注入
        messages.push({
          role: 'user',
          content: `【${chain.title}】用户特别关注以下${chain.nodes.length}个同级节点的前后关系：`
        });

        // 找到当前节点（通常在中间位置）
        const currentNodeIndex = Math.floor(chain.nodes.length / 2);

        // 展示序列关系
        let sequenceInfo = '序列关系：\n';
        for (let i = 0; i < chain.nodes.length; i++) {
          const node = chain.nodes[i];
          const position = i < currentNodeIndex ? '⬅️ 前置' :
                          i > currentNodeIndex ? '➡️ 后续' : '🎯 当前';

          sequenceInfo += `${position}: **${node.title}**\n`;
        }

        messages.push({
          role: 'user',
          content: sequenceInfo
        });

        messages.push({
          role: 'assistant',
          content: `我已理解这${chain.nodes.length}个节点的序列关系，现在请提供每个节点的详细信息。`
        });

        // 每个节点单独一条消息，按序列顺序
        for (let i = 0; i < chain.nodes.length; i++) {
          const node = chain.nodes[i];
          const position = i < currentNodeIndex ? '前置节点' :
                          i > currentNodeIndex ? '后续节点' : '当前节点';
          const positionNumber = i + 1;

          let nodeInfo = `${position} (${positionNumber}/${chain.nodes.length}): **${node.title}**\n\n`;

          // 位置说明
          if (i === 0) {
            nodeInfo += `📍 位置：序列开始\n`;
          } else if (i === chain.nodes.length - 1) {
            nodeInfo += `📍 位置：序列结束\n`;
          } else if (i === currentNodeIndex) {
            nodeInfo += `📍 位置：当前关注节点\n`;
          } else {
            nodeInfo += `📍 位置：序列中间\n`;
          }

          // 前后关系
          if (i > 0) {
            nodeInfo += `⬅️ 前一个：${chain.nodes[i - 1].title}\n`;
          }
          if (i < chain.nodes.length - 1) {
            nodeInfo += `➡️ 后一个：${chain.nodes[i + 1].title}\n`;
          }

          // 根据节点类型显示专门字段
          if ((node as any).type === 'volume') {
            // 总纲/卷专门字段
            if ((node as any).volumeTheme) {
              nodeInfo += `🎭 卷主题：${(node as any).volumeTheme}\n`;
            }
            if ((node as any).volumeArc) {
              nodeInfo += `📈 卷弧线：${(node as any).volumeArc}\n`;
            }
            if ((node as any).chapterCount) {
              nodeInfo += `📊 预期章节数：${(node as any).chapterCount}\n`;
            }
            if ((node as any).cycleTemplate) {
              nodeInfo += `🔄 循环法模板：${(node as any).cycleTemplate}\n`;
            }
          } else if ((node as any).type === 'event') {
            // 事件刚专门字段
            if ((node as any).eventStart) {
              nodeInfo += `🚀 事件起始：${(node as any).eventStart}\n`;
            }
            if ((node as any).eventEnd) {
              nodeInfo += `🏁 事件结束：${(node as any).eventEnd}\n`;
            }
            if ((node as any).eventTrigger) {
              nodeInfo += `⚡ 触发条件：${(node as any).eventTrigger}\n`;
            }
            if ((node as any).eventConsequence) {
              nodeInfo += `🎯 结果影响：${(node as any).eventConsequence}\n`;
            }
            if ((node as any).eventScope) {
              nodeInfo += `🌐 影响范围：${(node as any).eventScope}\n`;
            }
            if ((node as any).chapterCount) {
              nodeInfo += `📊 预期章节数：${(node as any).chapterCount}\n`;
            }
            if ((node as any).targetWordCount) {
              nodeInfo += `📝 目标字数：${(node as any).targetWordCount}\n`;
            }
          } else if ((node as any).type === 'chapter') {
            // 章节专门字段
            if ((node as any).chapterStyle) {
              nodeInfo += `🎨 写作风格：${(node as any).chapterStyle}\n`;
            }
            if ((node as any).chapterTechniques && Array.isArray((node as any).chapterTechniques) && (node as any).chapterTechniques.length > 0) {
              nodeInfo += `✍️ 写作手法：${(node as any).chapterTechniques.join('、')}\n`;
            }
            if ((node as any).chapterGoals) {
              nodeInfo += `🎯 章节目标：${(node as any).chapterGoals}\n`;
            }
          } else if ((node as any).type === 'plot') {
            // 剧情节点专门字段
            if ((node as any).plotType) {
              const plotTypeMap = {
                'conflict': '冲突',
                'twist': '转折',
                'climax': '高潮',
                'resolution': '解决'
              };
              const plotTypeLabel = plotTypeMap[(node as any).plotType as keyof typeof plotTypeMap] || (node as any).plotType;
              nodeInfo += `🎭 剧情类型：${plotTypeLabel}\n`;
            }
            if ((node as any).plotPoints && Array.isArray((node as any).plotPoints) && (node as any).plotPoints.length > 0) {
              nodeInfo += `📋 剧情点：\n`;
              (node as any).plotPoints.slice(0, 3).forEach((point: any, index: number) => {
                const pointText = typeof point === 'string' ? point :
                                 typeof point === 'object' && point?.content ? point.content :
                                 typeof point === 'object' ? JSON.stringify(point) : String(point);
                nodeInfo += `  ${index + 1}. ${pointText}\n`;

                // 添加写作指导信息
                if (typeof point === 'object' && point?.writingGuidance) {
                  nodeInfo += `     💡 写作指导：${point.writingGuidance}\n`;
                }

                // 添加写作风格方法指导
                if (typeof point === 'object' && point?.styleMethod) {
                  nodeInfo += `     🎨 写作风格：${point.styleMethod.technique} | ${point.styleMethod.style} | ${point.styleMethod.tone}\n`;
                  nodeInfo += `     📐 视角重点：${point.styleMethod.perspective} | ${point.styleMethod.emphasis}\n`;
                }

                // 添加格式规范
                if (typeof point === 'object' && point?.formatSpecs) {
                  nodeInfo += `     📏 字数要求：${point.formatSpecs.wordCount?.min}-${point.formatSpecs.wordCount?.max}字（目标${point.formatSpecs.wordCount?.target}字）\n`;
                  nodeInfo += `     📝 格式规范：${point.formatSpecs.paragraphRules?.paragraphBreakRules} | ${point.formatSpecs.punctuationRules?.dialogueFormat}\n`;

                  // 添加新增的段落规范字段
                  if (point.formatSpecs.paragraphRules?.conflictHandling) {
                    nodeInfo += `     ⚔️ 冲突处理：${point.formatSpecs.paragraphRules.conflictHandling}\n`;
                  }
                  if (point.formatSpecs.paragraphRules?.actionDialogueFlow) {
                    nodeInfo += `     🎭 对话行动流程：${point.formatSpecs.paragraphRules.actionDialogueFlow}\n`;
                  }
                  if (point.formatSpecs.paragraphRules?.mandatoryBreaks) {
                    nodeInfo += `     📄 强制换行：${point.formatSpecs.paragraphRules.mandatoryBreaks}\n`;
                  }

                  // 添加新增的标点规范字段
                  if (point.formatSpecs.punctuationRules?.conflictPunctuation) {
                    nodeInfo += `     ⚡ 冲突标点：${point.formatSpecs.punctuationRules.conflictPunctuation}\n`;
                  }
                  if (point.formatSpecs.punctuationRules?.naturalFlow) {
                    nodeInfo += `     🌊 自然流畅：${point.formatSpecs.punctuationRules.naturalFlow}\n`;
                  }


                }
              });
              if ((node as any).plotPoints.length > 3) {
                nodeInfo += `  ...还有${(node as any).plotPoints.length - 3}个剧情点\n`;
              }
            }
            if ((node as any).relatedCharacters && Array.isArray((node as any).relatedCharacters) && (node as any).relatedCharacters.length > 0) {
              nodeInfo += `👥 关联角色：${(node as any).relatedCharacters.join('、')}\n`;
            }
          } else if ((node as any).type === 'dialogue') {
            // 对话节点专门字段
            if ((node as any).dialogueScene) {
              nodeInfo += `🎬 对话场景：${(node as any).dialogueScene}\n`;
            }
            if ((node as any).participants && Array.isArray((node as any).participants) && (node as any).participants.length > 0) {
              nodeInfo += `👥 参与角色：${(node as any).participants.join('、')}\n`;
            }
            if ((node as any).dialoguePurpose) {
              nodeInfo += `🎯 对话目的：${(node as any).dialoguePurpose}\n`;
            }
            if ((node as any).dialogueContent && Array.isArray((node as any).dialogueContent) && (node as any).dialogueContent.length > 0) {
              nodeInfo += `💬 对话内容：\n`;
              (node as any).dialogueContent.slice(0, 2).forEach((dialogue: any) => {
                nodeInfo += `  ${dialogue.speaker}：${dialogue.content}\n`;
              });
              if ((node as any).dialogueContent.length > 2) {
                nodeInfo += `  ...还有${(node as any).dialogueContent.length - 2}条对话\n`;
              }
            }
          }

          // 完整内容（不省略）
          if (node.content && node.content.trim()) {
            nodeInfo += `\n📝 完整内容：\n${node.content}`;
          } else {
            nodeInfo += `\n📝 内容：暂无详细描述`;
          }

          messages.push({
            role: 'user',
            content: nodeInfo
          });

          messages.push({
            role: 'assistant',
            content: `我已详细了解${position}"${node.title}"的信息和在序列中的位置。`
          });
        }

        messages.push({
          role: 'assistant',
          content: `我已完整理解这${chain.nodes.length}个节点的序列关系和详细内容。在创建新节点时，我将：
1. 确保新节点与前后节点形成连贯的故事线
2. 保持时间线、空间、人物状态的逻辑一致性
3. 在节点内容中体现与前一节点的承接关系
4. 确保情节发展的自然过渡，避免突兀的跳跃
5. 考虑人物情绪、环境氛围的连续性变化`
        });
      }
    }

    // 添加总结消息
    if (messages.length > 0) {
      const totalNodes = contextChains.reduce((sum, chain) => sum + chain.nodes.length, 0);
      const hierarchyChains = contextChains.filter(c => c.type === 'hierarchy').length;
      const sequenceChains = contextChains.filter(c => c.type === 'sequence').length;

      let summaryInfo = `【上下文链路总结】\n`;
      summaryInfo += `📊 总计：${contextChains.length}种链路类型，${totalNodes}个节点\n`;
      if (hierarchyChains > 0) {
        summaryInfo += `📖 层级链路：${hierarchyChains}个，提供完整的层级结构上下文\n`;
      }
      if (sequenceChains > 0) {
        summaryInfo += `🔗 序列链路：${sequenceChains}个，提供前后关系上下文\n`;
      }
      summaryInfo += `\n💡 **连续性创建要求**：
1. 新节点必须与现有节点形成紧密关联，不能孤立存在
2. 充分利用层级关系确定正确的父子关系
3. 基于序列关系确保前后内容的逻辑连贯
4. 在节点描述中明确体现与上下文的连接点
5. 确保整体故事线的流畅性和完整性`;

      messages.push({
        role: 'user',
        content: summaryInfo
      });

      messages.push({
        role: 'assistant',
        content: `我已完整理解所有上下文链路信息。我承诺在创建新节点时：
1. 绝不创建孤立的节点，每个新节点都将与现有结构紧密关联
2. 充分利用层级链路信息确定正确的父子关系和兄弟关系
3. 基于序列链路信息确保前后内容的逻辑连贯和自然过渡
4. 在回复中明确说明新节点如何与上下文连接
5. 确保整体故事的连续性和完整性`
      });
    }

    return messages; 
  }

  /**
   * 构建分离的多框架消息（避免技巧杂糅）- 🔥 优化：分单个框架发送，使用系统消息
   */
  private buildSeparatedFrameworkMessages(selectedFrameworks: any[]): Array<{ role: string; content: string }> {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    const messages: Array<{ role: string; content: string }> = [];

    // 🔥 关键修改：为每个框架创建独立的系统消息
    selectedFrameworks.forEach((framework, index) => {
      let frameworkContent = `【参考创作模式 ${index + 1}】\n\n`;
      frameworkContent += `模式名称：${framework.frameworkName}\n`;
      frameworkContent += `模式结构：${framework.frameworkPattern}\n`;

      if (framework.patternType) {
        frameworkContent += `模式类型：${framework.patternType}\n`;
      }

      if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
        frameworkContent += `关键变量：${framework.frameworkVariables.join('、')}\n`;
      }

      // 情节技巧 - 独立展示
      if (framework.plotAnalysis) {
        frameworkContent += `\n【情节技巧】\n`;
        if (framework.plotAnalysis.storyStructure) {
          frameworkContent += `故事结构：${framework.plotAnalysis.storyStructure}\n`;
        }
        if (framework.plotAnalysis.conflictDesign) {
          frameworkContent += `冲突设计：${framework.plotAnalysis.conflictDesign}\n`;
        }
        if (framework.plotAnalysis.rhythmControl) {
          frameworkContent += `节奏控制：${framework.plotAnalysis.rhythmControl}\n`;
        }
        if (framework.plotAnalysis.plotPoints && framework.plotAnalysis.plotPoints.length > 0) {
          frameworkContent += `剧情点：\n${framework.plotAnalysis.plotPoints.map((p: any) => `  • ${p}`).join('\n')}\n`;
        }
        if (framework.plotAnalysis.behaviorFrameworks && framework.plotAnalysis.behaviorFrameworks.length > 0) {
          frameworkContent += `行为框架：\n${framework.plotAnalysis.behaviorFrameworks.map((b: any) => `  • ${b}`).join('\n')}\n`;
        }
      }

      // 对话技巧 - 独立展示
      if (framework.dialogueAnalysis) {
        frameworkContent += `\n【对话技巧】\n`;
        if (framework.dialogueAnalysis.dialogueStructure) {
          frameworkContent += `对话结构：${framework.dialogueAnalysis.dialogueStructure}\n`;
        }
        if (framework.dialogueAnalysis.plotAdvancement) {
          frameworkContent += `推进方式：${framework.dialogueAnalysis.plotAdvancement}\n`;
        }
        if (framework.dialogueAnalysis.writingTechniques) {
          frameworkContent += `写作技巧：${framework.dialogueAnalysis.writingTechniques}\n`;
        }
        if (framework.dialogueAnalysis.toneCharacteristics && framework.dialogueAnalysis.toneCharacteristics.length > 0) {
          frameworkContent += `语气特征：\n${framework.dialogueAnalysis.toneCharacteristics.map((t: string) => `  • ${t}`).join('\n')}\n`;
        }
        if (framework.dialogueAnalysis.stylePatterns && framework.dialogueAnalysis.stylePatterns.length > 0) {
          frameworkContent += `行文框架：\n${framework.dialogueAnalysis.stylePatterns.map((s: string) => `  • ${s}`).join('\n')}\n`;
        }
        if (framework.dialogueAnalysis.literaryAnalysis) {
          frameworkContent += `文学化分析：${framework.dialogueAnalysis.literaryAnalysis}\n`;
        }
      }

      // 风格特征 - 独立展示
      if (framework.styleAnalysis) {
        frameworkContent += `\n【风格特征】\n`;
        if (framework.styleAnalysis.writingStyle) {
          frameworkContent += `写作风格：${framework.styleAnalysis.writingStyle}\n`;
        }
        if (framework.styleAnalysis.expressionFeatures) {
          frameworkContent += `表现特色：${framework.styleAnalysis.expressionFeatures}\n`;
        }
        if (framework.styleAnalysis.practicalMethods) {
          frameworkContent += `实用方法：${framework.styleAnalysis.practicalMethods}\n`;
        }
        if (framework.styleAnalysis.rhythmPatterns && framework.styleAnalysis.rhythmPatterns.length > 0) {
          frameworkContent += `节奏模式：\n${framework.styleAnalysis.rhythmPatterns.map((r: string) => `  • ${r}`).join('\n')}\n`;
        }
        if (framework.styleAnalysis.pacingFramework) {
          frameworkContent += `节奏框架：${framework.styleAnalysis.pacingFramework}\n`;
        }
        if (framework.styleAnalysis.outlineGuidance) {
          frameworkContent += `大纲参照：${framework.styleAnalysis.outlineGuidance}\n`;
        }
      }

      frameworkContent += `\n请学习此创作模式的独特技巧，在建议中保持其特色，避免与其他框架技巧杂糅。`;

      // 🔥 关键修改：使用system角色而不是user角色
      messages.push({
        role: 'user',
        content: frameworkContent
      });

      // 添加AI确认消息，表明理解了这个特定框架
      messages.push({
        role: 'assistant',
        content: `我已理解"${framework.frameworkName}"创作模式的独特技巧和结构特征，将在建议中保持其特色，不会使用其中的具体人名，而是融合其创作理念。`
      });
    });

    return messages;
  }

  /**
   * 构建多框架参考消息
   */
  private buildMultiFrameworkMessages(selectedFrameworks: any[]): Array<{ role: string; content: string }> {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    // 使用分离展示方法，避免技巧杂糅
    return this.buildSeparatedFrameworkMessages(selectedFrameworks);
  }

  /**
   * 构建框架参考消息
   */
  private buildFrameworkMessages(selectedFramework?: any): Array<{ role: string; content: string }> {
    if (!selectedFramework) {
      return [];
    }

    const messages: Array<{ role: string; content: string }> = [];

    // 构建框架参考消息 - 去除名称依赖，强调学习模式
    let frameworkContent = `【参考创作模式】\n`;
    frameworkContent += `模式结构：${selectedFramework.frameworkPattern}\n`;

    if (selectedFramework.patternType) {
      frameworkContent += `模式类型：${selectedFramework.patternType}\n`;
    }

    if (selectedFramework.frameworkVariables && selectedFramework.frameworkVariables.length > 0) {
      frameworkContent += `关键变量：${selectedFramework.frameworkVariables.join('、')}\n`;
    }

    // 完整的情节分析 - 包含节奏控制和写作指导
    if (selectedFramework.plotAnalysis) {
      frameworkContent += `\n【情节技巧】\n`;
      if (selectedFramework.plotAnalysis.storyStructure) {
        frameworkContent += `故事结构：${selectedFramework.plotAnalysis.storyStructure}\n`;
      }
      if (selectedFramework.plotAnalysis.conflictDesign) {
        frameworkContent += `冲突设计：${selectedFramework.plotAnalysis.conflictDesign}\n`;
      }
      if (selectedFramework.plotAnalysis.rhythmControl) {
        frameworkContent += `节奏控制：${selectedFramework.plotAnalysis.rhythmControl}\n`;
      }

      // 🎯 添加核心剧情点信息 - 框架提取的核心价值
      if (selectedFramework.plotAnalysis.plotPoints &&
          Array.isArray(selectedFramework.plotAnalysis.plotPoints) &&
          selectedFramework.plotAnalysis.plotPoints.length > 0) {
        frameworkContent += `\n【📋 核心剧情点】\n`;
        selectedFramework.plotAnalysis.plotPoints.forEach((plotPoint: any, index: number) => {
          frameworkContent += `${index + 1}. ${plotPoint}\n`;
        });
        frameworkContent += `\n💡 **剧情点学习要点**：这些是从原作中提取的具体剧情发展节点，体现了作者的剧情推进技巧和节奏控制方法。在创建大纲时可以学习其剧情安排和发展逻辑。\n`;
      }

      // 🎨 添加行为表现特征 - 角色塑造的重要参考
      if (selectedFramework.plotAnalysis.behaviorFrameworks &&
          Array.isArray(selectedFramework.plotAnalysis.behaviorFrameworks) &&
          selectedFramework.plotAnalysis.behaviorFrameworks.length > 0) {
        frameworkContent += `\n【🎭 行为表现特征】\n`;
        selectedFramework.plotAnalysis.behaviorFrameworks.forEach((behavior: any, index: number) => {
          frameworkContent += `${index + 1}. ${behavior}\n`;
        });
        frameworkContent += `\n💡 **行为特征学习要点**：这些是角色行为表现的具体特征，体现了作者的角色塑造技巧。在设计角色行为时可以参考这些表现方式。\n`;
      }

      // 添加剧情点写作指导信息
      if (selectedFramework.plotAnalysis.plotPointsWithGuidance &&
          Array.isArray(selectedFramework.plotAnalysis.plotPointsWithGuidance) &&
          selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 0) {
        frameworkContent += `\n【📝 剧情点写作指导】\n`;
        selectedFramework.plotAnalysis.plotPointsWithGuidance.forEach((guidancePoint: any, index: number) => {
          frameworkContent += `${index + 1}. ${guidancePoint.content}\n`;
          if (guidancePoint.specificDescription) {
            frameworkContent += `   具体描写特征：${guidancePoint.specificDescription}\n`;
          }
          if (guidancePoint.avoidanceGuidance) {
            frameworkContent += `   写作指导：${guidancePoint.avoidanceGuidance}\n`;
          }
        });
        if (selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 3) {
          frameworkContent += `   ...还有${selectedFramework.plotAnalysis.plotPointsWithGuidance.length - 3}个剧情点的写作指导\n`;
        }
      }
    }

    // 对话分析 - 完整添加
    if (selectedFramework.dialogueAnalysis) {
      frameworkContent += `\n【💬 对话技巧】\n`;
      if (selectedFramework.dialogueAnalysis.dialogueStructure) {
        frameworkContent += `对话结构：${selectedFramework.dialogueAnalysis.dialogueStructure}\n`;
      }
      if (selectedFramework.dialogueAnalysis.plotAdvancement) {
        frameworkContent += `推进方式：${selectedFramework.dialogueAnalysis.plotAdvancement}\n`;
      }
      if (selectedFramework.dialogueAnalysis.writingTechniques) {
        frameworkContent += `写作技巧：${selectedFramework.dialogueAnalysis.writingTechniques}\n`;
      }

      // 🎭 添加语气特征 - 对话表现的重要元素
      if (selectedFramework.dialogueAnalysis.toneCharacteristics &&
          Array.isArray(selectedFramework.dialogueAnalysis.toneCharacteristics) &&
          selectedFramework.dialogueAnalysis.toneCharacteristics.length > 0) {
        frameworkContent += `\n【🎵 语气特征】\n`;
        selectedFramework.dialogueAnalysis.toneCharacteristics.forEach((tone: any, index: number) => {
          frameworkContent += `${index + 1}. ${tone}\n`;
        });
      }

      // 📝 添加行文特色 - 对话与动作结合的技巧
      if (selectedFramework.dialogueAnalysis.stylePatterns &&
          Array.isArray(selectedFramework.dialogueAnalysis.stylePatterns) &&
          selectedFramework.dialogueAnalysis.stylePatterns.length > 0) {
        frameworkContent += `\n【📝 行文特色】\n`;
        selectedFramework.dialogueAnalysis.stylePatterns.forEach((pattern: any, index: number) => {
          frameworkContent += `${index + 1}. ${pattern}\n`;
        });
      }
    }

    // 风格分析 - 完整添加
    if (selectedFramework.styleAnalysis) {
      frameworkContent += `\n【🎨 风格特征】\n`;
      if (selectedFramework.styleAnalysis.writingStyle) {
        frameworkContent += `写作风格：${selectedFramework.styleAnalysis.writingStyle}\n`;
      }
      if (selectedFramework.styleAnalysis.expressionFeatures) {
        frameworkContent += `表现特色：${selectedFramework.styleAnalysis.expressionFeatures}\n`;
      }
      if (selectedFramework.styleAnalysis.practicalMethods) {
        frameworkContent += `实用方法：${selectedFramework.styleAnalysis.practicalMethods}\n`;
      }

      // 🎵 添加节奏模式 - 写作节奏的具体表现
      if (selectedFramework.styleAnalysis.rhythmPatterns &&
          Array.isArray(selectedFramework.styleAnalysis.rhythmPatterns) &&
          selectedFramework.styleAnalysis.rhythmPatterns.length > 0) {
        frameworkContent += `\n【🎵 节奏模式】\n`;
        selectedFramework.styleAnalysis.rhythmPatterns.forEach((pattern: any, index: number) => {
          frameworkContent += `${index + 1}. ${pattern}\n`;
        });
      }

      // ⚡ 添加节奏控制特征 - 节奏变化的技巧
      if (selectedFramework.styleAnalysis.pacingFramework) {
        frameworkContent += `\n【⚡ 节奏控制特征】\n${selectedFramework.styleAnalysis.pacingFramework}\n`;
      }

      // 📋 添加大纲指导 - 在大纲创作中的应用
      if (selectedFramework.styleAnalysis.outlineGuidance) {
        frameworkContent += `\n【📋 大纲应用指导】\n${selectedFramework.styleAnalysis.outlineGuidance}\n`;
      }
    }

    frameworkContent += `\n请学习以上创作模式的技巧和方法，为大纲提供建议时融入这些技巧特征，但请自主创作，不要直接使用具体名称。`;

    // 🔥 关键修改：使用user角色而不是user角色
    messages.push({
      role: 'user',
      content: frameworkContent
    });

    // 去除名称依赖的确认消息
    const patternDescription = selectedFramework.patternType || '创作模式';
    messages.push({
      role: 'assistant',
      content: `我已理解这个${patternDescription}的技巧特征，将学习其情节结构、对话技巧、风格特色和节奏控制方法，为您的大纲提供融入这些技巧的个性化建议。但我不会使用里面的任何人名这些，我会自己设计类似的进行融合`
    });

    return messages;
  }

  /**
   * 构建@节点的分条消息 - 按照AI写作方法论
   */
  private async buildMentionedNodesMessages(nodeIds: string[], outline: any, _bookId?: string): Promise<Array<{ role: string; content: string }>> {
    if (!nodeIds || nodeIds.length === 0) {
      return [];
    }

    const messages: Array<{ role: string; content: string }> = [];

    // 分类处理不同类型的@内容
    const outlineNodes = [];
    const dataNodes = [];

    for (const nodeId of nodeIds) {
      // 首先尝试在大纲中查找
      const outlineNode = this.findNodeById(outline.nodes, nodeId);
      if (outlineNode) {
        // 🔥 修复：提取完整的节点数据，包含所有专门字段
        outlineNodes.push({
          ...outlineNode, // 包含所有原始字段
          level: this.getNodeLevel(outline.nodes, nodeId),
          childrenCount: outlineNode.children ? outlineNode.children.length : 0,
          position: this.getNodePosition(outline.nodes, nodeId)
        });
      } else {
        // 如果不在大纲中，可能是数据库中的内容（章节、人物、术语、世界观）
        dataNodes.push(nodeId);
      }
    }

    // 处理大纲节点 - 每个节点一条消息
    if (outlineNodes.length > 0) {
      messages.push({
        role: 'user',
        content: `【大纲节点信息】\n用户特别提及了以下${outlineNodes.length}个大纲节点，请重点考虑：`
      });

      for (const node of outlineNodes) {
        let nodeInfo = `**${node.title}** (${node.type})\n`;
        if (node.description) {
          nodeInfo += `描述：${node.description}\n`;
        }
        nodeInfo += `层级：${node.level} | 位置：${node.position}\n`;

        // 🔥 修复：根据节点类型显示完整的专门字段，确保不省略任何信息
        if ((node as any).type === 'chapter') {
          // 章节专门字段
          if ((node as any).chapterStyle) {
            nodeInfo += `🎨 写作风格：${(node as any).chapterStyle}\n`;
          }
          if ((node as any).chapterTechniques && Array.isArray((node as any).chapterTechniques) && (node as any).chapterTechniques.length > 0) {
            nodeInfo += `✍️ 写作手法（共${(node as any).chapterTechniques.length}种）：\n`;
            // 🔥 完全不省略：显示所有写作手法，确保信息完整性
            (node as any).chapterTechniques.forEach((technique: any, index: number) => {
              if (typeof technique === 'string') {
                nodeInfo += `  ${index + 1}. ${technique}\n`;
              } else if (typeof technique === 'object' && technique) {
                const techniqueName = technique.name || technique.title || technique.type || '未命名手法';
                nodeInfo += `  ${index + 1}. ${techniqueName}\n`;

                // 添加手法的详细信息
                if (technique.description) {
                  nodeInfo += `     📝 描述：${technique.description}\n`;
                }
                if (technique.purpose) {
                  nodeInfo += `     🎯 目的：${technique.purpose}\n`;
                }
                if (technique.effect) {
                  nodeInfo += `     ✨ 效果：${technique.effect}\n`;
                }
                if (technique.example) {
                  nodeInfo += `     💡 示例：${technique.example}\n`;
                }
                if (technique.timing) {
                  nodeInfo += `     ⏰ 使用时机：${technique.timing}\n`;
                }
                if (technique.difficulty) {
                  nodeInfo += `     ⭐ 难度：${technique.difficulty}\n`;
                }
              }
            });
          }
          if ((node as any).chapterGoals) {
            nodeInfo += `🎯 章节目标：${(node as any).chapterGoals}\n`;
          }
          // 添加其他章节字段
          if ((node as any).phaseType) {
            nodeInfo += `📊 阶段类型：${(node as any).phaseType}\n`;
          }
          if ((node as any).phaseRequirements && Array.isArray((node as any).phaseRequirements) && (node as any).phaseRequirements.length > 0) {
            nodeInfo += `📋 阶段要求（共${(node as any).phaseRequirements.length}项）：\n`;
            // 🔥 完全不省略：显示所有阶段要求，确保信息完整性
            (node as any).phaseRequirements.forEach((requirement: any, index: number) => {
              if (typeof requirement === 'string') {
                nodeInfo += `  ${index + 1}. ${requirement}\n`;
              } else if (typeof requirement === 'object' && requirement) {
                const reqName = requirement.name || requirement.title || requirement.description || '未命名要求';
                nodeInfo += `  ${index + 1}. ${reqName}\n`;

                // 添加要求的详细信息
                if (requirement.priority) {
                  nodeInfo += `     🔥 优先级：${requirement.priority}\n`;
                }
                if (requirement.deadline) {
                  nodeInfo += `     ⏰ 截止时间：${requirement.deadline}\n`;
                }
                if (requirement.criteria) {
                  nodeInfo += `     ✅ 评判标准：${requirement.criteria}\n`;
                }
                if (requirement.resources) {
                  nodeInfo += `     📚 所需资源：${requirement.resources}\n`;
                }
                if (requirement.dependencies) {
                  nodeInfo += `     🔗 依赖关系：${requirement.dependencies}\n`;
                }
                if (requirement.notes) {
                  nodeInfo += `     📝 备注：${requirement.notes}\n`;
                }
              }
            });
          }
        } else if ((node as any).type === 'plot') {
          // 剧情节点专门字段
          if ((node as any).plotType) {
            const plotTypeMap = {
              'conflict': '冲突',
              'twist': '转折',
              'climax': '高潮',
              'resolution': '解决'
            };
            const plotTypeLabel = plotTypeMap[(node as any).plotType as keyof typeof plotTypeMap] || (node as any).plotType;
            nodeInfo += `🎭 剧情类型：${plotTypeLabel}\n`;
          }
          if ((node as any).plotPoints && Array.isArray((node as any).plotPoints) && (node as any).plotPoints.length > 0) {
            nodeInfo += `📋 剧情点（共${(node as any).plotPoints.length}个）：\n`;
            // 🔥 完全不省略：显示所有剧情点，确保信息完整性
            (node as any).plotPoints.forEach((point: any, index: number) => {
              const pointText = typeof point === 'string' ? point :
                               typeof point === 'object' && point?.content ? point.content :
                               typeof point === 'object' ? JSON.stringify(point) : String(point);
              nodeInfo += `  ${index + 1}. ${pointText}\n`;

              // 添加所有可能的剧情点字段
              if (typeof point === 'object') {
                if (point?.writingGuidance) {
                  nodeInfo += `     💡 写作指导：${point.writingGuidance}\n`;
                }
                if (point?.id) {
                  nodeInfo += `     🔗 ID：${point.id}\n`;
                }
                if (point?.type) {
                  nodeInfo += `     🎭 类型：${point.type}\n`;
                }
                if (point?.importance) {
                  nodeInfo += `     ⭐ 重要性：${point.importance}\n`;
                }
                if (point?.characters && Array.isArray(point.characters)) {
                  nodeInfo += `     👥 涉及角色：${point.characters.join('、')}\n`;
                }
                if (point?.emotion) {
                  nodeInfo += `     😊 情感基调：${point.emotion}\n`;
                }
                if (point?.conflict) {
                  nodeInfo += `     ⚔️ 冲突要素：${point.conflict}\n`;
                }
                if (point?.outcome) {
                  nodeInfo += `     🎯 预期结果：${point.outcome}\n`;
                }
              }
            });
          }
          if ((node as any).relatedCharacters && Array.isArray((node as any).relatedCharacters) && (node as any).relatedCharacters.length > 0) {
            nodeInfo += `👥 关联角色（共${(node as any).relatedCharacters.length}个）：\n`;
            // 🔥 完全不省略：显示所有关联角色，确保信息完整性
            (node as any).relatedCharacters.forEach((character: any, index: number) => {
              if (typeof character === 'string') {
                nodeInfo += `  ${index + 1}. ${character}\n`;
              } else if (typeof character === 'object' && character) {
                const charName = character.name || character.title || character.id || '未命名角色';
                nodeInfo += `  ${index + 1}. ${charName}\n`;

                // 添加角色的详细信息
                if (character.role) {
                  nodeInfo += `     🎭 角色定位：${character.role}\n`;
                }
                if (character.importance) {
                  nodeInfo += `     ⭐ 重要性：${character.importance}\n`;
                }
                if (character.relationship) {
                  nodeInfo += `     💕 关系：${character.relationship}\n`;
                }
                if (character.motivation) {
                  nodeInfo += `     🎯 动机：${character.motivation}\n`;
                }
                if (character.conflict) {
                  nodeInfo += `     ⚔️ 冲突：${character.conflict}\n`;
                }
                if (character.arc) {
                  nodeInfo += `     📈 成长弧线：${character.arc}\n`;
                }
                if (character.traits && Array.isArray(character.traits)) {
                  nodeInfo += `     🌟 特征：${character.traits.join('、')}\n`;
                }
              }
            });
          }
        } else if ((node as any).type === 'dialogue') {
          // 对话节点专门字段
          if ((node as any).dialogueScene) {
            nodeInfo += `🎬 对话场景：${(node as any).dialogueScene}\n`;
          }
          if ((node as any).participants && Array.isArray((node as any).participants) && (node as any).participants.length > 0) {
            nodeInfo += `👥 参与角色（共${(node as any).participants.length}个）：\n`;
            // 🔥 完全不省略：显示所有参与角色，确保信息完整性
            (node as any).participants.forEach((participant: any, index: number) => {
              if (typeof participant === 'string') {
                nodeInfo += `  ${index + 1}. ${participant}\n`;
              } else if (typeof participant === 'object' && participant) {
                const partName = participant.name || participant.character || participant.id || '未命名参与者';
                nodeInfo += `  ${index + 1}. ${partName}\n`;

                // 添加参与者的详细信息
                if (participant.role) {
                  nodeInfo += `     🎭 对话角色：${participant.role}\n`;
                }
                if (participant.attitude) {
                  nodeInfo += `     😊 态度：${participant.attitude}\n`;
                }
                if (participant.goal) {
                  nodeInfo += `     🎯 对话目标：${participant.goal}\n`;
                }
                if (participant.strategy) {
                  nodeInfo += `     🧠 策略：${participant.strategy}\n`;
                }
                if (participant.emotion) {
                  nodeInfo += `     💭 情绪状态：${participant.emotion}\n`;
                }
                if (participant.knowledge) {
                  nodeInfo += `     📚 已知信息：${participant.knowledge}\n`;
                }
                if (participant.secrets) {
                  nodeInfo += `     🤫 隐藏信息：${participant.secrets}\n`;
                }
              }
            });
          }
          if ((node as any).dialoguePurpose) {
            nodeInfo += `🎯 对话目的：${(node as any).dialoguePurpose}\n`;
          }
          if ((node as any).dialogueContent && Array.isArray((node as any).dialogueContent) && (node as any).dialogueContent.length > 0) {
            nodeInfo += `💬 对话内容（共${(node as any).dialogueContent.length}条）：\n`;
            // 🔥 完全不省略：显示所有对话内容，确保信息完整性
            (node as any).dialogueContent.forEach((dialogue: any, index: number) => {
              if (dialogue && dialogue.speaker && dialogue.content) {
                nodeInfo += `  ${index + 1}. ${dialogue.speaker}：${dialogue.content}\n`;

                // 添加所有可能的对话字段
                if (dialogue.emotion) {
                  nodeInfo += `     😊 情感：${dialogue.emotion}\n`;
                }
                if (dialogue.tone) {
                  nodeInfo += `     🎵 语调：${dialogue.tone}\n`;
                }
                if (dialogue.action) {
                  nodeInfo += `     🎬 动作：${dialogue.action}\n`;
                }
                if (dialogue.subtext) {
                  nodeInfo += `     🤔 潜台词：${dialogue.subtext}\n`;
                }
                if (dialogue.purpose) {
                  nodeInfo += `     🎯 目的：${dialogue.purpose}\n`;
                }
                if (dialogue.context) {
                  nodeInfo += `     📝 背景：${dialogue.context}\n`;
                }
                if (dialogue.timing) {
                  nodeInfo += `     ⏰ 时机：${dialogue.timing}\n`;
                }
                if (dialogue.volume) {
                  nodeInfo += `     🔊 音量：${dialogue.volume}\n`;
                }
                if (dialogue.pace) {
                  nodeInfo += `     ⚡ 节奏：${dialogue.pace}\n`;
                }
                if (dialogue.interruption) {
                  nodeInfo += `     ✋ 打断：${dialogue.interruption}\n`;
                }
              }
            });
          }
        }

        messages.push({
          role: 'user',
          content: nodeInfo
        });

        // 🔥 完全不省略：如果有创作建议，添加完整的独立创作建议消息
        if ((node as any).creativeNotes) {
          let creativeNotesContent = `【创作建议 - ${node.title}】\n`;

          // 如果创作建议是数组，显示所有条目
          if (Array.isArray((node as any).creativeNotes)) {
            creativeNotesContent += `共${(node as any).creativeNotes.length}条创作建议：\n\n`;
            (node as any).creativeNotes.forEach((note: any, index: number) => {
              if (typeof note === 'string') {
                creativeNotesContent += `${index + 1}. ${note}\n\n`;
              } else if (typeof note === 'object' && note) {
                const noteContent = note.content || note.text || note.description || '未命名建议';
                creativeNotesContent += `${index + 1}. ${noteContent}\n`;

                // 添加建议的详细信息
                if (note.category) {
                  creativeNotesContent += `   类别：${note.category}\n`;
                }
                if (note.priority) {
                  creativeNotesContent += `   优先级：${note.priority}\n`;
                }
                if (note.source) {
                  creativeNotesContent += `   来源：${note.source}\n`;
                }
                if (note.example) {
                  creativeNotesContent += `   示例：${note.example}\n`;
                }
                if (note.reasoning) {
                  creativeNotesContent += `   理由：${note.reasoning}\n`;
                }
                if (note.tags && Array.isArray(note.tags)) {
                  creativeNotesContent += `   标签：${note.tags.join('、')}\n`;
                }
                creativeNotesContent += '\n';
              }
            });
          } else if (typeof (node as any).creativeNotes === 'string' && (node as any).creativeNotes.trim()) {
            // 如果是字符串，直接显示完整内容
            creativeNotesContent += (node as any).creativeNotes;
          }

          messages.push({
            role: 'user',
            content: creativeNotesContent
          });
        }

        // 🔥 修复：生成更详细的确认消息，包含所有专门字段信息
        let confirmContent = `我已理解大纲节点"${node.title}"的完整信息，包括：`;

        const confirmedFields = [];

        // 基础信息
        confirmedFields.push('基本描述');

        // 根据节点类型添加专门字段确认
        if ((node as any).type === 'chapter') {
          if ((node as any).chapterStyle) confirmedFields.push('写作风格');
          if ((node as any).chapterTechniques?.length > 0) confirmedFields.push('写作手法');
          if ((node as any).chapterGoals) confirmedFields.push('章节目标');
        } else if ((node as any).type === 'plot') {
          if ((node as any).plotType) confirmedFields.push('剧情类型');
          if ((node as any).plotPoints?.length > 0) confirmedFields.push('剧情点设计');
          if ((node as any).relatedCharacters?.length > 0) confirmedFields.push('关联角色');
        } else if ((node as any).type === 'dialogue') {
          if ((node as any).dialogueScene) confirmedFields.push('对话场景');
          if ((node as any).participants?.length > 0) confirmedFields.push('参与角色');
          if ((node as any).dialoguePurpose) confirmedFields.push('对话目的');
          if ((node as any).dialogueContent?.length > 0) confirmedFields.push('对话内容');
        }

        // 创作建议
        if ((node as any).creativeNotes) confirmedFields.push('创作建议');

        confirmContent += confirmedFields.join('、') + '。我将在建议中重点考虑这些专业要素。';

        messages.push({
          role: 'assistant',
          content: confirmContent
        });
      }
    }

    // 处理数据节点 - 按类型分组，每个实体一条消息
    if (dataNodes.length > 0) {
      try {
        // 直接导入数据库，避免路径问题
        const { db } = await import('../../../../../lib/db/dexie');

        const chapters = [];
        const characters = [];
        const terminologies = [];
        const worldBuildings = [];

        // 分类查询所有数据
        for (const nodeId of dataNodes) {
          // 查找章节
          const chapter = await db.chapters.get(nodeId);
          if (chapter) {
            chapters.push(chapter);
            continue;
          }

          // 查找人物
          const character = await db.characters.get(nodeId);
          if (character) {
            characters.push(character);
            continue;
          }

          // 查找术语
          const terminology = await db.terminology.get(nodeId);
          if (terminology) {
            terminologies.push(terminology);
            continue;
          }

          // 查找世界观
          const worldBuilding = await db.worldBuilding.get(nodeId);
          if (worldBuilding) {
            worldBuildings.push(worldBuilding);
            continue;
          }
        }

        // 🔥 修复：处理章节 - 使用智能分段，每段一条系统消息
        if (chapters.length > 0) {
          messages.push({
            role: 'user',
            content: `【章节信息】\n用户特别提及了以下${chapters.length}个章节：`
          });

          // 导入章节分段器
          const { createChapterSegmenter } = await import('../../../../../utils/ai/ChapterSegmenter');
          const segmenter = createChapterSegmenter({
            maxSegmentLength: 2000,
            minSegmentLength: 500,
            addSegmentNumber: true,
            preserveCharacterDescriptions: true
          });

          for (const chapter of chapters) {
            // 🔥 修复：章节基本信息改为系统消息，并添加完整的节点结构信息
            let chapterInfo = `**${chapter.title}**\n`;
            chapterInfo += `📖 节点类型：章节\n`;
            if (chapter.summary) {
              chapterInfo += `📝 概要：${chapter.summary}\n`;
            }
            chapterInfo += `📊 字数：${chapter.wordCount || 0}字 | 顺序：第${chapter.order || 1}章\n`;

            // 添加完整的章节专门字段信息
            if ((chapter as any).chapterStyle) {
              chapterInfo += `🎨 写作风格：${(chapter as any).chapterStyle}\n`;
            }
            if ((chapter as any).chapterTechniques && Array.isArray((chapter as any).chapterTechniques) && (chapter as any).chapterTechniques.length > 0) {
              chapterInfo += `✍️ 写作手法：${(chapter as any).chapterTechniques.join('、')}\n`;
            }
            if ((chapter as any).chapterGoals) {
              chapterInfo += `🎯 章节目标：${(chapter as any).chapterGoals}\n`;
            }
            if ((chapter as any).phaseType) {
              chapterInfo += `📊 阶段类型：${(chapter as any).phaseType}\n`;
            }
            if ((chapter as any).phaseRequirements && Array.isArray((chapter as any).phaseRequirements) && (chapter as any).phaseRequirements.length > 0) {
              chapterInfo += `📋 阶段要求：${(chapter as any).phaseRequirements.join('、')}\n`;
            }
            if ((chapter as any).creativeNotes) {
              chapterInfo += `💡 创作建议：${(chapter as any).creativeNotes}\n`;
            }

            messages.push({
              role: 'user',
              content: chapterInfo
            });

            // 🔥 修复：生成更详细的章节确认消息，包含所有专门字段信息
            let confirmContent = `我已理解章节"${chapter.title}"的完整信息，包括：`;

            const confirmedFields = [];

            // 基础信息
            confirmedFields.push('基本描述');

            // 根据章节字段添加专门字段确认
            if ((chapter as any).chapterStyle) confirmedFields.push('写作风格');
            if ((chapter as any).chapterTechniques?.length > 0) confirmedFields.push('写作手法');
            if ((chapter as any).chapterGoals) confirmedFields.push('章节目标');
            if ((chapter as any).phaseType) confirmedFields.push('阶段类型');
            if ((chapter as any).phaseRequirements?.length > 0) confirmedFields.push('阶段要求');
            if ((chapter as any).creativeNotes) confirmedFields.push('创作建议');

            confirmContent += confirmedFields.join('、') + '。我将在建议中重点考虑这些专业要素。';

            messages.push({
              role: 'assistant',
              content: confirmContent
            });

            // 🔥 修复：如果有内容，进行智能分段，改为系统消息
            if (chapter.content && chapter.content.trim()) {
              const segments = segmenter.segmentChapter(chapter.content);

              messages.push({
                role: 'user',
                content: `【${chapter.title}】章节内容分为${segments.length}个段落：`
              });

              messages.push({
                role: 'assistant',
                content: `我将逐段阅读"${chapter.title}"的内容，共${segments.length}个段落。`
              });

              // 🔥 修复：每个段落单独一条系统消息
              for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const wordCount = segment.replace(/\s+/g, '').length;

                messages.push({
                  role: 'user',
                  content: `【${chapter.title}】\n段落：${i+1}/${segments.length}\n字数：${wordCount}\n\n${segment}`
                });

                messages.push({
                  role: 'assistant',
                  content: `我已阅读"${chapter.title}"章节的第${i+1}段内容。`
                });
              }

              messages.push({
                role: 'assistant',
                content: `我已完成"${chapter.title}"章节的完整阅读，将在建议中考虑其内容和设定。`
              });
            }
          }
        }

        // 处理人物 - 每个人物一条消息
        if (characters.length > 0) {
          messages.push({
            role: 'user',
            content: `【人物信息】\n用户特别提及了以下${characters.length}个人物：`
          });

          for (const character of characters) {
            let characterInfo = `**${character.name}**\n`;
            if (character.description) {
              characterInfo += `描述：${character.description}\n`;
            }
            if (character.personality) {
              characterInfo += `性格：${character.personality}\n`;
            }
            if (character.background) {
              characterInfo += `背景：${character.background}\n`;
            }
            if (character.goals) {
              characterInfo += `目标：${character.goals}`;
            }

            messages.push({
              role: 'user',
              content: characterInfo
            });

            messages.push({
              role: 'assistant',
              content: `我已了解人物"${character.name}"的详细信息，将在建议中考虑其性格特点和背景设定。`
            });
          }
        }

        // 处理术语 - 每个术语一条消息
        if (terminologies.length > 0) {
          messages.push({
            role: 'user',
            content: `【术语信息】\n用户特别提及了以下${terminologies.length}个术语：`
          });

          for (const terminology of terminologies) {
            let termInfo = `**${terminology.name}**\n`;
            if (terminology.description) {
              termInfo += `定义：${terminology.description}\n`;
            }
            if (terminology.category) {
              termInfo += `分类：${terminology.category}`;
            }

            messages.push({
              role: 'user',
              content: termInfo
            });

            messages.push({
              role: 'assistant',
              content: `我已理解术语"${terminology.name}"的定义，将在建议中正确使用这个概念。`
            });
          }
        }

        // 处理世界观 - 每个世界观一条消息
        if (worldBuildings.length > 0) {
          messages.push({
            role: 'user',
            content: `【世界观信息】\n用户特别提及了以下${worldBuildings.length}个世界观元素：`
          });

          for (const worldBuilding of worldBuildings) {
            let worldInfo = `**${worldBuilding.name}**\n`;
            if (worldBuilding.description) {
              worldInfo += `描述：${worldBuilding.description}\n`;
            }
            if (worldBuilding.category) {
              worldInfo += `分类：${worldBuilding.category}`;
            }

            messages.push({
              role: 'user',
              content: worldInfo
            });

            messages.push({
              role: 'assistant',
              content: `我已了解世界观元素"${worldBuilding.name}"的设定，将在建议中保持世界观的一致性。`
            });
          }
        }

      } catch (error) {
        console.error('查询关联数据失败:', error);
        messages.push({
          role: 'user',
          content: '【错误】无法查询关联数据详情，请基于现有信息提供建议。'
        });
      }
    }

    // 添加最终说明消息
    if (messages.length > 0) {
      messages.push({
        role: 'user',
        content: '【重要说明】以上是用户通过@功能特别提及的相关内容，请在回答中重点考虑这些信息，确保建议与这些内容密切相关。'
      });
    }

    return messages;
  }

  /**
   * 构建大纲上下文信息
   */
  private buildOutlineContext(outline: any): string {
    if (!outline || !outline.nodes) {
      return '\n\n**当前大纲**：空大纲';
    }

    const outlineStructure = this.buildOutlineStructure(outline.nodes, 0);
    const stats = this.getOutlineStats(outline.nodes);

    return `\n\n**当前大纲结构**：
${outlineStructure}

**大纲统计**：
- 总节点数：${stats.totalNodes}
- 章节数：${stats.chapters}
- 剧情节点数：${stats.scenes}
- 对话节点数：${stats.sections}
- 最大层级：${stats.maxLevel}`;
  }

  /**
   * 构建大纲结构的文本表示
   */
  private buildOutlineStructure(nodes: OutlineNodeType[], level: number): string {
    if (!nodes || nodes.length === 0) return '';

    const indent = '  '.repeat(level);
    return nodes.map(node => {
      const icon = node.type === 'volume' ? '📚' : node.type === 'event' ? '⚡' : node.type === 'chapter' ? '📖' : node.type === 'plot' ? '🎬' : '�';
      let result = `${indent}${icon} ${node.title} (${node.type})`;

      if (node.description) {
        result += ` - ${node.description.substring(0, 50)}${node.description.length > 50 ? '...' : ''}`;
      }

      if (node.children && node.children.length > 0) {
        result += '\n' + this.buildOutlineStructure(node.children, level + 1);
      }

      return result;
    }).join('\n');
  }

  /**
   * 获取大纲统计信息
   */
  private getOutlineStats(nodes: OutlineNodeType[]): {
    totalNodes: number;
    chapters: number;
    scenes: number;
    sections: number;
    maxLevel: number;
  } {
    let totalNodes = 0;
    let chapters = 0;
    let scenes = 0;
    let sections = 0;
    let maxLevel = 0;

    const traverse = (nodeList: OutlineNodeType[], currentLevel: number) => {
      maxLevel = Math.max(maxLevel, currentLevel);

      nodeList.forEach(node => {
        totalNodes++;

        switch (node.type) {
          case 'chapter':
            chapters++;
            break;
          case 'plot':
            scenes++;
            break;
          case 'dialogue':
            sections++;
            break;
        }

        if (node.children && node.children.length > 0) {
          traverse(node.children, currentLevel + 1);
        }
      });
    };

    traverse(nodes, 1);

    return { totalNodes, chapters, scenes, sections, maxLevel };
  }

  /**
   * 根据ID查找节点
   */
  private findNodeById(nodes: OutlineNodeType[], nodeId: string): OutlineNodeType | null {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children) {
        const found = this.findNodeById(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 获取节点层级
   */
  private getNodeLevel(nodes: OutlineNodeType[], nodeId: string, currentLevel: number = 1): number {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return currentLevel;
      }
      if (node.children) {
        const level = this.getNodeLevel(node.children, nodeId, currentLevel + 1);
        if (level > 0) return level;
      }
    }
    return 0;
  }

  /**
   * 获取节点在同级中的位置
   */
  private getNodePosition(nodes: OutlineNodeType[], nodeId: string): number {
    const findPosition = (nodeList: OutlineNodeType[]): number => {
      for (let i = 0; i < nodeList.length; i++) {
        if (nodeList[i].id === nodeId) {
          return i + 1;
        }
        if (nodeList[i].children && nodeList[i].children!.length > 0) {
          const pos = findPosition(nodeList[i].children!);
          if (pos > 0) return pos;
        }
      }
      return 0;
    };

    return findPosition(nodes);
  }

  /**
   * 发送AI请求（流式响应）
   * 现在使用重构后的模块化服务
   */
  public async sendStreamingRequest(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    onChunk: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: ContextChain[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<{
    message: string;
    changes?: any[];
    metadata?: any;
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('发送大纲AI流式请求 (使用重构后的服务):', { userMessage, mentionedNodes, outline });

      // 使用重构后的模块化服务进行流式请求
      let fullResponse = '';
      const response = await this.refactoredService.sendStreamRequest(
        userMessage,
        mentionedNodes,
        outline,
        {
          temperature: options?.temperature,
          maxTokens: options?.maxTokens,
          bookId: options?.bookId,
          contextChains: options?.contextChains,
          selectedFramework: options?.selectedFramework,
          selectedFrameworks: options?.selectedFrameworks,
          onProgress: (chunk: string) => {
            fullResponse += chunk;
            onChunk(chunk);
          }
        }
      );

      console.log('重构后服务流式响应完成:', response);

      if (!response.success) {
        return {
          message: '抱歉，AI服务暂时不可用，请稍后再试。',
          success: false,
          error: response.error
        };
      }

      // 解析JSON响应
      const defaultResponse = {
        message: response.message || '我理解了您的需求，但暂时无法提供具体的操作建议。',
        changes: [],
        metadata: {
          operationType: 'analyze',
          affectedNodes: mentionedNodes,
          confidence: 0.5
        }
      };

      const parsedResponse = AIResponseParser.parseJSON(fullResponse, defaultResponse);

      console.log('解析后的流式响应:', parsedResponse);

      // 新增：节点创建成功后的协同处理
      let allChanges: any[] = parsedResponse.changes || [];
      if (parsedResponse.changes && parsedResponse.changes.length > 0) {
        const additionalChanges = await this.handleNodeCreationSuccess(
          parsedResponse.changes,
          [], // originalMessages 不再需要
          outline,
          onChunk,
          options
        );
        if (additionalChanges && additionalChanges.length > 0) {
          allChanges = [...allChanges, ...additionalChanges];
          console.log('🔄 合并对话节点到响应中:', additionalChanges.length, '个对话节点');
        }
      }

      return {
        message: parsedResponse.message,
        changes: allChanges,
        metadata: parsedResponse.metadata || defaultResponse.metadata,
        success: true
      };

    } catch (error: any) {
      console.error('大纲AI流式请求失败:', error);
      return {
        message: '处理请求时发生错误，请稍后再试。',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 测试AI服务连接
   */
  public async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('测试AI服务连接...');

      const testMessages = [
        { role: 'system', content: '你是一个测试助手。' },
        { role: 'user', content: '请回复"连接成功"' }
      ];

      const response = await aiServiceProvider.sendRequest(testMessages, {
        temperature: 0.1,
        maxTokens: 50
      });

      console.log('测试响应:', response);

      return {
        success: response.success,
        error: response.error
      };
    } catch (error: any) {
      console.error('AI服务连接测试失败:', error);
      return {
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 发送AI请求（非流式）
   * 现在使用重构后的模块化服务
   */
  public async sendRequest(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    options?: {
      temperature?: number;
      maxTokens?: number;
      onProgress?: (chunk: string) => void;
      bookId?: string;
      contextChains?: ContextChain[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<{
    message: string;
    creativeNotes?: string;
    changes?: any[];
    metadata?: any;
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('发送大纲AI请求 (使用重构后的服务):', { userMessage, mentionedNodes, outline });

      // 使用重构后的模块化服务
      const response = await this.refactoredService.sendRequest(
        userMessage,
        mentionedNodes,
        outline,
        {
          temperature: options?.temperature,
          maxTokens: options?.maxTokens,
          bookId: options?.bookId,
          contextChains: options?.contextChains,
          selectedFramework: options?.selectedFramework,
          selectedFrameworks: options?.selectedFrameworks
        }
      );

      console.log('重构后服务响应:', response);

      // 如果成功且有changes，处理协同对话生成
      if (response.success && response.changes && response.changes.length > 0 && options?.onProgress) {
        const additionalChanges = await this.handleNodeCreationSuccess(
          response.changes,
          [], // originalMessages 不再需要
          outline,
          options.onProgress,
          options
        );

        // 合并额外的changes
        if (additionalChanges.length > 0) {
          response.changes.push(...additionalChanges);
        }
      }

      return {
        message: response.message,
        changes: response.changes || [],
        metadata: response.metadata,
        success: response.success,
        error: response.error
      };

    } catch (error: any) {
      console.error('大纲AI请求失败:', error);
      return {
        message: '处理请求时发生错误，请稍后再试。',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  // 思考画布相关方法已移至 ThinkingCanvasService.ts
  // TODO: 移除这些方法，使用专门的服务类

  /**
   * 检查是否是对话节点创建请求 - 优化后更精确
   */
  private isDialogueRequest(userMessage: string): boolean {
    // 只有明确要求创建对话节点时才触发
    const explicitDialogueKeywords = [
      '创建对话节点', '生成对话节点', '设计对话节点',
      '创建对话', '生成对话', '设计对话',
      '添加对话节点', '新建对话节点', '制作对话节点'
    ];

    const lowerMessage = userMessage.toLowerCase();
    const hasExplicitRequest = explicitDialogueKeywords.some(keyword =>
      lowerMessage.includes(keyword.toLowerCase())
    );

    if (hasExplicitRequest) {
      console.log('🎭 检测到明确的对话节点创建请求');
      return true;
    }

    console.log('📝 普通请求，使用大纲AI处理');
    return false;
  }

  /**
   * 节点创建成功后的协同处理
   */
  private async handleNodeCreationSuccess(
    changes: any[],
    originalMessages: any[],
    outline: any,
    onChunk: (chunk: string) => void,
    options?: any
  ): Promise<any[]> {
    try {
      console.log('🔄 开始节点创建后的协同处理:', changes);
      const additionalChanges: any[] = [];

      // 🔧 恢复自动对话触发机制 - 大纲节点创建后直接触发对话AI
      // 避免二次触发的关键是移除关键词检测，只在这里触发一次

      for (const change of changes) {
        if (change.type === 'create' && this.shouldTriggerDialogueDesign(change)) {
          console.log('🎭 检测到需要对话设计的节点:', change.data?.title || '未命名节点');

          // 显示协同处理进度
          onChunk('\n\n🔄 **正在设计相关对话内容...**\n');

          const dialogueChanges = await this.triggerDialogueDesign(change, originalMessages, outline, onChunk, options);
          if (dialogueChanges && dialogueChanges.length > 0) {
            additionalChanges.push(...dialogueChanges);
            console.log('🔄 添加对话节点到响应中:', dialogueChanges.length, '个对话节点');
          }
        }
      }

      return additionalChanges;
    } catch (error: any) {
      console.error('❌ 协同处理失败:', error);
      onChunk(`\n⚠️ **对话设计协同处理失败**: ${error.message}\n`);
      return [];
    }
  }

  /**
   * 判断是否需要触发对话设计
   */
  private shouldTriggerDialogueDesign(change: any): boolean {
    const nodeData = change.data;

    if (!nodeData) return false;

    // 如果是剧情节点且涉及人物互动
    if (nodeData.type === 'plot' && nodeData.relatedCharacters?.length > 1) {
      console.log('✅ 剧情节点涉及多个角色，需要对话设计');
      return true;
    }

    // 如果是章节节点且描述中提到对话
    if (nodeData.type === 'chapter' && this.containsDialogueKeywords(nodeData.description || '')) {
      console.log('✅ 章节节点包含对话关键词，需要对话设计');
      return true;
    }

    // 如果节点描述中明确提到对话相关内容
    if (this.containsDialogueKeywords(nodeData.description || nodeData.content || '')) {
      console.log('✅ 节点内容包含对话关键词，需要对话设计');
      return true;
    }

    return false;
  }

  /**
   * 检查文本中是否包含对话关键词
   */
  private containsDialogueKeywords(text: string): boolean {
    const dialogueKeywords = [
      '对话', '台词', '说话', '交谈', '聊天', '谈话', '交流',
      '说道', '回答', '询问', '问道', '答道', '喊道',
      '人物对话', '角色对话', '对话场景', '对话内容'
    ];

    const lowerText = text.toLowerCase();
    return dialogueKeywords.some(keyword =>
      lowerText.includes(keyword.toLowerCase())
    );
  }

  /**
   * 触发对话设计
   */
  private async triggerDialogueDesign(
    nodeChange: any,
    originalMessages: any[],
    outline: any,
    onChunk: (chunk: string) => void,
    options?: any
  ): Promise<any[]> {
    try {
      console.log('🎭 开始触发对话设计:', nodeChange);

      // 显示对话设计进度
      onChunk('🎭 **正在设计相关对话内容...**\n');

      // 创建DialogueAIService实例
      const dialogueAI = new DialogueAIService();

      // 构建对话设计的上下文
      const dialogueContext = this.buildDialogueContext(nodeChange, outline);

      // 生成对话设计提示词
      const dialoguePrompt = this.generateDialogueDesignPrompt(nodeChange, dialogueContext);

      console.log('🎭 对话设计提示词:', dialoguePrompt);

      // 🔥 核心改进：获取相关节点ID，实现信息互通
      const relatedNodeIds = this.getRelatedNodeIds(nodeChange, outline);
      console.log('🔗 获取到相关节点ID:', relatedNodeIds);

      // 调用对话AI进行对话设计
      const dialogueResponse = await dialogueAI.createDialogueNode(
        dialoguePrompt,
        relatedNodeIds, // 传递所有相关节点ID，实现信息互通
        outline,
        undefined, // 不需要流式输出，避免干扰主流程
        {
          ...options,
          temperature: 0.65, 
          maxTokens: 30000
    
        }
      );

      // 处理对话AI的响应
      if (dialogueResponse.success) {
        onChunk('✅ **对话设计完成！**\n');
        onChunk(`📝 **对话建议**: ${dialogueResponse.message}\n`);

        if (dialogueResponse.changes && dialogueResponse.changes.length > 0) {
          onChunk(`🎯 **已创建 ${dialogueResponse.changes.length} 个对话节点**\n`);
          console.log('✅ 对话设计成功:', dialogueResponse);
          return dialogueResponse.changes; // 返回对话节点的changes
        }

        console.log('✅ 对话设计成功:', dialogueResponse);
        return [];
      } else {
        onChunk('⚠️ **对话设计失败，但大纲节点已成功创建**\n');
        console.warn('⚠️ 对话设计失败:', dialogueResponse.error);
        return [];
      }

    } catch (error: any) {
      console.error('❌ 对话设计触发失败:', error);
      onChunk(`⚠️ **对话设计遇到问题**: ${error.message}，但大纲节点已成功创建\n`);
      return [];
    }
  }

  /**
   * 获取相关节点ID - 实现信息互通的核心方法
   */
  private getRelatedNodeIds(nodeChange: any, outline: any): string[] {
    const nodeIds: string[] = [];
    const currentNodeId = nodeChange.nodeId || nodeChange.id;
    const currentNodeData = nodeChange.data;

    // 1. 添加当前节点
    if (currentNodeId) {
      nodeIds.push(currentNodeId);
    }

    if (!outline?.nodes || !Array.isArray(outline.nodes)) {
      return nodeIds;
    }

    // 2. 添加同章节的其他节点
    if (currentNodeData?.type === 'plot') {
      // 如果是剧情节点，查找同章节的其他剧情节点
      const sameChapterNodes = outline.nodes.filter((node: any) =>
        node.type === 'plot' &&
        node.id !== currentNodeId &&
        this.isSameChapter(currentNodeData, node)
      );
      sameChapterNodes.slice(0, 3).forEach((node: any) => {
        if (node.id && !nodeIds.includes(node.id)) {
          nodeIds.push(node.id);
        }
      });
    }

    // 3. 添加相关角色的节点
    if (currentNodeData?.relatedCharacters && currentNodeData.relatedCharacters.length > 0) {
      const characterRelatedNodes = outline.nodes.filter((node: any) =>
        node.id !== currentNodeId &&
        node.relatedCharacters &&
        this.hasCommonCharacters(currentNodeData.relatedCharacters, node.relatedCharacters)
      );
      characterRelatedNodes.slice(0, 2).forEach((node: any) => {
        if (node.id && !nodeIds.includes(node.id)) {
          nodeIds.push(node.id);
        }
      });
    }

    // 4. 添加父节点（章节节点）
    const parentNode = outline.nodes.find((node: any) =>
      node.type === 'chapter' &&
      node.children &&
      node.children.some((child: any) => child.id === currentNodeId)
    );
    if (parentNode && parentNode.id && !nodeIds.includes(parentNode.id)) {
      nodeIds.push(parentNode.id);
    }

    // 5. 限制总数量，避免信息过载
    return nodeIds.slice(0, 6);
  }

  /**
   * 判断两个节点是否属于同一章节
   */
  private isSameChapter(node1: any, node2: any): boolean {
    // 简单的同章节判断逻辑
    if (node1.chapterOrder && node2.chapterOrder) {
      return node1.chapterOrder === node2.chapterOrder;
    }
    if (node1.parentId && node2.parentId) {
      return node1.parentId === node2.parentId;
    }
    return false;
  }

  /**
   * 判断两个角色列表是否有共同角色
   */
  private hasCommonCharacters(characters1: string[], characters2: string[]): boolean {
    if (!characters1 || !characters2) return false;
    return characters1.some(char => characters2.includes(char));
  }

  /**
   * 构建对话设计的上下文
   */
  private buildDialogueContext(nodeChange: any, outline: any): any {
    const nodeData = nodeChange.data;

    return {
      targetNode: nodeData,
      nodeId: nodeChange.nodeId || nodeChange.id,
      relatedCharacters: nodeData.relatedCharacters || [],
      plotPoints: nodeData.plotPoints || [],
      description: nodeData.description || nodeData.content || '',
      creativeNotes: nodeData.creativeNotes || '',
      type: nodeData.type,
      title: nodeData.title || nodeData.name || '',
      outline: outline,
      // 添加更多节点信息
      chapterGoals: nodeData.chapterGoals || '',
      conflictLevel: nodeData.conflictLevel || '',
      emotionalTone: nodeData.emotionalTone || '',
      plotType: nodeData.plotType || '',
      suspenseElements: nodeData.suspenseElements || [],
      characterDynamics: nodeData.characterDynamics || ''
    };
  }

  /**
   * 生成对话设计提示词
   */
  private generateDialogueDesignPrompt(nodeChange: any, context: any): string {
    return `基于以下大纲节点，设计相应的对话内容：

**节点信息**：
- 标题：${context.title}
- 类型：${context.type}
- 描述：${context.description}
- 相关角色：${context.relatedCharacters.join(', ') || '未指定'}

**创作指导信息**：
${context.creativeNotes ? `- 创作指导：${context.creativeNotes}` : ''}
${context.chapterGoals ? `- 章节目标：${context.chapterGoals}` : ''}
${context.plotType ? `- 剧情类型：${context.plotType}` : ''}
${context.conflictLevel ? `- 冲突强度：${context.conflictLevel}/5` : ''}
${context.emotionalTone ? `- 情感基调：${context.emotionalTone}` : ''}
${context.suspenseElements && context.suspenseElements.length > 0 ? `- 悬念要素：${context.suspenseElements.join('、')}` : ''}
${context.characterDynamics ? `- 人物关系动态：${context.characterDynamics}` : ''}

**剧情点信息**：
${context.plotPoints.length > 0 ? context.plotPoints.map((point: any, index: number) => {
  if (typeof point === 'string') {
    return `${index + 1}. ${point}`;
  } else if (point && typeof point === 'object') {
    return `${index + 1}. ${point.content || point.description || point.title || JSON.stringify(point)}`;
  } else {
    return `${index + 1}. ${point}`;
  }
}).join('\n') : '无具体剧情点'}

**故事框架信息**：
${this.buildFrameworkInfo(context.outline)}

**设计要求**：
请为这个节点设计具体的对话内容，包括：
1. 对话场景设置
2. 参与对话的角色
3. 具体的对话内容（至少3-5轮对话）
4. 对话的情感基调和目的
5. 对话如何推进剧情发展

**注意事项**：
- 确保对话内容与节点的剧情发展紧密相关
- 体现人物性格和关系动态
- 对话要有冲突性和戏剧张力
- 语言要符合角色身份和时代背景
- 基于上述创作指导信息进行专业化创作

请创建一个专门的对话节点来承载这些对话内容。`;
  }

  /**
   * 构建框架信息
   */
  private buildFrameworkInfo(outline: any): string {
    if (!outline || !outline.nodes || outline.nodes.length === 0) {
      return '- 总节点数：0\n- 章节节点：0个\n- 剧情节点：0个\n- 已有对话节点：0个';
    }

    const chapters = outline.nodes.filter((node: any) => node.type === 'chapter');
    const plots = outline.nodes.filter((node: any) => node.type === 'plot');
    const dialogues = outline.nodes.filter((node: any) => node.type === 'dialogue');
    const others = outline.nodes.filter((node: any) => !['chapter', 'plot', 'dialogue'].includes(node.type));

    const frameworkParts: string[] = [];

    frameworkParts.push(`- 总节点数：${outline.nodes.length}`);
    frameworkParts.push(`- 章节节点：${chapters.length}个`);
    frameworkParts.push(`- 剧情节点：${plots.length}个`);
    frameworkParts.push(`- 已有对话节点：${dialogues.length}个`);

    if (others.length > 0) {
      frameworkParts.push(`- 其他节点：${others.length}个`);
    }

    // 添加具体节点信息
    if (chapters.length > 0) {
      frameworkParts.push('\n**章节框架**：');
      chapters.slice(0, 3).forEach((ch: any, index: number) => {
        frameworkParts.push(`${index + 1}. ${ch.title || '未命名章节'}`);
        if (ch.description) {
          frameworkParts.push(`   概要：${ch.description.substring(0, 80)}${ch.description.length > 80 ? '...' : ''}`);
        }
      });
    }

    if (plots.length > 0) {
      frameworkParts.push('\n**剧情框架**：');
      plots.slice(0, 3).forEach((plot: any, index: number) => {
        frameworkParts.push(`${index + 1}. ${plot.title || '未命名剧情'}`);
        if (plot.description) {
          frameworkParts.push(`   内容：${plot.description.substring(0, 80)}${plot.description.length > 80 ? '...' : ''}`);
        }
      });
    }

    return frameworkParts.join('\n');
  }

}

// 导出单例实例
export const outlineAIService = OutlineAIService.getInstance();
