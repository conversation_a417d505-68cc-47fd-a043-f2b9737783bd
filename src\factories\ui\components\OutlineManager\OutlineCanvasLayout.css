/* 大纲画布布局样式 */
.outline-canvas-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 画布容器 */
.outline-canvas-container {
  flex: 1;
  transition: margin-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 300px; /* 确保画布有最小宽度 */
}

/* 当助手打开时，画布向左收缩 */
.outline-canvas-wrapper.assistant-open .outline-canvas-container {
  margin-right: 480px; /* 助手抽屉的宽度 */
}

/* 助手抽屉容器 */
.outline-canvas-wrapper .assistant-drawer-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 480px;
  background: transparent;
  backdrop-filter: none;

  /* 默认隐藏在右侧 */
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  z-index: 1000;
}

/* 当助手打开时，抽屉从右侧滑出 */
.outline-canvas-wrapper.assistant-open .assistant-drawer-overlay {
  transform: translateX(0);
}

/* 助手抽屉本身 */
.outline-canvas-wrapper .assistant-drawer {
  width: 100%;
  height: 100%;
  transform: none;
  position: relative;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 中等屏幕：减小助手宽度 */
  .outline-canvas-wrapper.assistant-open .outline-canvas-container {
    margin-right: 400px;
  }

  .outline-canvas-wrapper .assistant-drawer-overlay {
    width: 400px;
  }
}

@media (max-width: 768px) {
  /* 小屏幕：回退到覆盖模式 */
  .outline-canvas-wrapper.assistant-open .outline-canvas-container {
    margin-right: 0;
  }

  .outline-canvas-wrapper .assistant-drawer-overlay {
    position: fixed;
    left: 0;
    width: 100vw;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .outline-canvas-wrapper .assistant-drawer {
    width: 90vw;
    max-width: 400px;
    margin-left: auto;
  }
}

/* 性能优化 */
.outline-canvas-wrapper,
.outline-canvas-container,
.assistant-drawer-overlay {
  will-change: transform, margin;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .outline-canvas-container,
  .assistant-drawer-overlay {
    transition: none;
  }
}

/* 确保ReactFlow组件正确适应容器 */
.outline-canvas-container .react-flow {
  width: 100% !important;
  height: 100% !important;
}

/* 调整Panel位置，避免与助手按钮冲突 */
.outline-canvas-wrapper.assistant-open .react-flow__panel.react-flow__panel-top.react-flow__panel-right {
  right: 500px; /* 助手宽度 + 一些间距 */
  transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 1200px) {
  .outline-canvas-wrapper.assistant-open .react-flow__panel.react-flow__panel-top.react-flow__panel-right {
    right: 420px;
  }
}

@media (max-width: 768px) {
  .outline-canvas-wrapper.assistant-open .react-flow__panel.react-flow__panel-top.react-flow__panel-right {
    right: 16px; /* 移动端保持原位置 */
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .outline-canvas-wrapper .assistant-drawer {
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
  }
}
