/**
 * 世界书导入相关类型定义
 * 用于JSON世界书文件的解析和前置消息转换
 */

import { SavedAIPrefix } from '../services/ai/AIGeneratedPrefixStorageService';
import { PrefixOption } from '../services/ai/PrefixMessageAIService';

/**
 * 世界书条目原始结构
 * 对应JSON文件中entries对象的每个条目
 */
export interface WorldBookEntry {
  /** 唯一标识符 */
  uid: number;
  /** 条目描述/标题 */
  comment: string;
  /** 实际内容 */
  content: string;
  /** 触发关键词数组 */
  key: string[];
  /** 次要关键词数组 */
  keysecondary: string[];
  /** 是否禁用 */
  disable: boolean;
  /** 排序顺序 */
  order: number;
  /** 插入位置 */
  position: number;
  /** 是否为常量 */
  constant: boolean;
  /** 是否向量化 */
  vectorized: boolean;
  /** 是否选择性触发 */
  selective: boolean;
  /** 选择性逻辑 */
  selectiveLogic: number;
  /** 是否添加备忘录 */
  addMemo: boolean;
  /** 是否排除递归 */
  excludeRecursion: boolean;
  /** 是否防止递归 */
  preventRecursion: boolean;
  /** 是否延迟到递归 */
  delayUntilRecursion: boolean;
  /** 触发概率 */
  probability: number;
  /** 是否使用概率 */
  useProbability: boolean;
  /** 深度 */
  depth: number;
  /** 分组 */
  group: string;
  /** 是否覆盖分组 */
  groupOverride: boolean;
  /** 分组权重 */
  groupWeight: number;
  /** 扫描深度 */
  scanDepth: number | null;
  /** 是否区分大小写 */
  caseSensitive: boolean | null;
  /** 是否匹配整词 */
  matchWholeWords: boolean | null;
  /** 是否使用分组评分 */
  useGroupScoring: boolean | null;
  /** 自动化ID */
  automationId: string;
  /** 角色 */
  role: number;
  /** 粘性 */
  sticky: number;
  /** 冷却时间 */
  cooldown: number;
  /** 延迟 */
  delay: number;
  /** 显示索引 */
  displayIndex: number;
}

/**
 * 世界书JSON文件结构
 */
export interface WorldBookData {
  /** 条目集合，键为UID字符串 */
  entries: Record<string, WorldBookEntry>;
}

/**
 * 扩展的前置消息接口，包含世界书来源信息
 */
export interface WorldBookPrefix extends SavedAIPrefix {
  /** 世界书来源文件名（不含扩展名） */
  worldBookSource: string;
  /** 原始UID */
  originalUid: number;
  /** 是否为常量 */
  isConstant: boolean;
  /** 原始排序 */
  originalOrder: number;
  /** 原始关键词 */
  originalKeys: string[];
  /** 原始次要关键词 */
  originalKeysSecondary: string[];
  /** 原始位置 */
  originalPosition: number;
  /** 触发概率 */
  triggerProbability: number;
  /** 是否选择性触发 */
  isSelective: boolean;
  /** 是否启用关键词自动触发 */
  enableKeywordTrigger?: boolean;
  /** 是否作为独立消息发送（而非添加到用户消息中） */
  sendAsIndependentMessage?: boolean;
  /** 是否启用触发内容变成关键词（触发后，该条目的内容会作为关键词去触发其他条目） */
  enableContentAsKeyword?: boolean;
}

/**
 * 导入结果统计
 */
export interface ImportResult {
  /** 总条目数 */
  total: number;
  /** 成功导入数 */
  imported: number;
  /** 跳过数（重复或无效） */
  skipped: number;
  /** 错误列表 */
  errors: string[];
  /** 导入的文件名 */
  fileName: string;
  /** 导入时间 */
  importedAt: Date;
}

/**
 * 世界书导入配置选项
 */
export interface ImportOptions {
  /** 是否跳过空内容条目 */
  skipEmptyContent?: boolean;
  /** 是否跳过禁用的条目 */
  skipDisabled?: boolean;
  /** 是否保留原始排序 */
  preserveOrder?: boolean;
  /** 自定义分类 */
  customCategory?: PrefixOption['category'];
  /** 是否覆盖重复内容 */
  overwriteDuplicates?: boolean;
  /** 最小内容长度 */
  minContentLength?: number;
}

/**
 * 世界书统计信息
 */
export interface WorldBookStats {
  /** 总文件数 */
  totalFiles: number;
  /** 总条目数 */
  totalEntries: number;
  /** 按文件分组的统计 */
  byFile: Record<string, {
    entryCount: number;
    importedAt: Date;
    lastUsed?: Date;
  }>;
  /** 按类别分组的统计 */
  byCategory: Record<string, number>;
  /** 最常用的条目 */
  mostUsed: WorldBookPrefix[];
}

/**
 * 世界书过滤选项
 */
export interface WorldBookFilter {
  /** 按文件名过滤 */
  fileName?: string;
  /** 按是否常量过滤 */
  isConstant?: boolean;
  /** 按是否禁用过滤 */
  isDisabled?: boolean;
  /** 按关键词搜索 */
  searchKeywords?: string;
  /** 按内容搜索 */
  searchContent?: string;
}

/**
 * 世界书条目转换为前置消息的映射配置
 */
export interface ConversionMapping {
  /** 内容字段映射 */
  contentField: keyof WorldBookEntry;
  /** 描述字段映射 */
  descriptionField: keyof WorldBookEntry;
  /** 标签字段映射 */
  tagsFields: (keyof WorldBookEntry)[];
  /** 默认分类 */
  defaultCategory: PrefixOption['category'];
  /** 置信度计算函数 */
  confidenceCalculator: (entry: WorldBookEntry) => number;
}

/**
 * 默认的转换映射配置
 */
export const DEFAULT_CONVERSION_MAPPING: ConversionMapping = {
  contentField: 'content',
  descriptionField: 'comment',
  tagsFields: ['key', 'keysecondary'],
  defaultCategory: 'custom',
  confidenceCalculator: (entry: WorldBookEntry) => {
    // 常量条目置信度更高
    if (entry.constant) return 0.9;
    // 有关键词的条目置信度中等
    if (entry.key.length > 0) return 0.7;
    // 其他条目置信度较低
    return 0.5;
  }
};

/**
 * 世界书导入错误类型
 */
export enum ImportErrorType {
  INVALID_JSON = 'INVALID_JSON',
  MISSING_ENTRIES = 'MISSING_ENTRIES',
  EMPTY_CONTENT = 'EMPTY_CONTENT',
  DUPLICATE_CONTENT = 'DUPLICATE_CONTENT',
  INVALID_ENTRY_FORMAT = 'INVALID_ENTRY_FORMAT',
  FILE_READ_ERROR = 'FILE_READ_ERROR'
}

/**
 * 世界书导入错误详情
 */
export interface ImportError {
  type: ImportErrorType;
  message: string;
  entryUid?: number;
  details?: any;
}
