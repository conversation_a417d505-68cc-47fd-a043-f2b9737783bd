"use client";

import React, { useRef, useCallback } from 'react';
import './FrameworkExtractButton.css';

interface FrameworkExtractButtonProps {
  onClick: (buttonPosition?: { x: number; y: number }) => void;
  isActive: boolean;
  disabled?: boolean;
}

/**
 * 框架提取按钮组件
 * 用于在画布右侧显示一个浮动的框架提取按钮
 */
const FrameworkExtractButton: React.FC<FrameworkExtractButtonProps> = ({
  onClick,
  isActive,
  disabled = false
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = useCallback(() => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const buttonPosition = {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
      onClick(buttonPosition);
    } else {
      onClick();
    }
  }, [onClick]);

  return (
    <button
      ref={buttonRef}
      className={`framework-extract-button ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
      onClick={handleClick}
      disabled={disabled}
      title="框架提取 - 分析大纲结构，提取创作技巧"
      aria-label="打开框架提取功能"
    >
      <div className="framework-extract-icon">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* 框架提取图标 - 分析图表 */}
          <path
            d="M3 3V21H21V19H5V3H3Z"
            fill="currentColor"
          />
          <path
            d="M7 17H9V10H7V17ZM11 17H13V7H11V17ZM15 17H17V13H15V17Z"
            fill="currentColor"
          />
          <path
            d="M19 8L17 6L15 8L13 6L11 8L9 6L7 8"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="none"
          />
          <circle cx="19" cy="5" r="2" fill="currentColor" />
          <circle cx="15" cy="7" r="1.5" fill="currentColor" />
          <circle cx="11" cy="5" r="1.5" fill="currentColor" />
          <circle cx="7" cy="7" r="1.5" fill="currentColor" />
        </svg>
      </div>

      {/* 活跃状态指示器 */}
      {isActive && (
        <div className="framework-extract-indicator">
          <div className="pulse-dot"></div>
        </div>
      )}

      {/* 悬停提示文本 */}
      <div className="framework-extract-tooltip">
        框架提取
      </div>
    </button>
  );
};

export default FrameworkExtractButton;
