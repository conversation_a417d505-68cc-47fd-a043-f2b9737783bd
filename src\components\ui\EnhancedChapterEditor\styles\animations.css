/* Enhanced Chapter Editor Animations */

.enhanced-chapter-editor {
  position: relative;
  overflow: hidden;
}

/* 基础动画变量 */
:root {
  --editor-golden: #D4AF37;
  --editor-golden-light: #FFD700;
  --editor-paper: #8B7355;
  --editor-ink: #2C3E50;
  --editor-ink-light: #34495E;
  
  --animation-duration-fast: 0.3s;
  --animation-duration-normal: 0.6s;
  --animation-duration-slow: 1.2s;
  
  --easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-elastic: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 容器过渡动画 */
.enhanced-chapter-editor {
  transition: all 0.7s var(--easing-elastic);
}

.enhanced-chapter-editor:hover {
  transform: translateY(-1px);
  box-shadow: 
    inset 0 0 10px rgba(0, 0, 0, 0.02),
    0 4px 20px rgba(212, 175, 55, 0.1);
}

.enhanced-chapter-editor:focus-within {
  transform: translateY(-2px);
  box-shadow: 
    inset 0 0 15px rgba(0, 0, 0, 0.03),
    0 8px 30px rgba(212, 175, 55, 0.15);
}

/* 文本区域动画 */
.enhanced-chapter-editor textarea {
  transition: all var(--animation-duration-normal) var(--easing-smooth);
}

.enhanced-chapter-editor textarea:focus {
  color: var(--color-text-primary);
  text-shadow: 0 0 1px rgba(212, 175, 55, 0.1);
}

/* 光标动画增强 */
.enhanced-chapter-editor textarea::selection {
  background-color: rgba(212, 175, 55, 0.2);
  color: inherit;
}

/* 滚动条样式 */
.enhanced-chapter-editor textarea::-webkit-scrollbar {
  width: 8px;
}

.enhanced-chapter-editor textarea::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.enhanced-chapter-editor textarea::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 4px;
  transition: background var(--animation-duration-fast) var(--easing-smooth);
}

.enhanced-chapter-editor textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* 占位符动画 */
.enhanced-chapter-editor textarea::placeholder {
  color: rgba(139, 115, 85, 0.6);
  transition: all var(--animation-duration-normal) var(--easing-smooth);
}

.enhanced-chapter-editor textarea:focus::placeholder {
  color: rgba(139, 115, 85, 0.3);
  transform: translateY(-2px);
}

/* 响应式动画调整 */
@media (max-width: 768px) {
  .enhanced-chapter-editor {
    transition-duration: 0.4s;
  }
  
  .enhanced-chapter-editor:hover {
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .enhanced-chapter-editor {
    border-width: 2px;
  }
  
  .enhanced-chapter-editor textarea::selection {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-chapter-editor,
  .enhanced-chapter-editor *,
  .enhanced-chapter-editor *::before,
  .enhanced-chapter-editor *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .enhanced-chapter-editor:hover,
  .enhanced-chapter-editor:focus-within {
    transform: none !important;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02) !important;
  }
}

/* 打印样式 */
@media print {
  .enhanced-chapter-editor {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    background: white !important;
  }
  
  .enhanced-chapter-editor textarea {
    color: black !important;
    background: white !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --editor-golden: #B8860B;
    --editor-golden-light: #DAA520;
    --editor-paper: #6B5B47;
    --editor-ink: #E8E8E8;
    --editor-ink-light: #D0D0D0;
  }
  
  .enhanced-chapter-editor textarea::selection {
    background-color: rgba(184, 134, 11, 0.3);
  }
  
  .enhanced-chapter-editor textarea::-webkit-scrollbar-thumb {
    background: rgba(184, 134, 11, 0.4);
  }
  
  .enhanced-chapter-editor textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(184, 134, 11, 0.6);
  }
}

/* 性能优化 */
.enhanced-chapter-editor * {
  will-change: auto;
}

.enhanced-chapter-editor:hover *,
.enhanced-chapter-editor:focus-within * {
  will-change: transform, opacity;
}

/* 无障碍支持 */
.enhanced-chapter-editor:focus-within {
  outline: 2px solid var(--editor-golden);
  outline-offset: 2px;
}

@media (forced-colors: active) {
  .enhanced-chapter-editor {
    border: 1px solid ButtonText;
    background: Field;
  }
  
  .enhanced-chapter-editor textarea {
    color: FieldText;
    background: Field;
  }
}
