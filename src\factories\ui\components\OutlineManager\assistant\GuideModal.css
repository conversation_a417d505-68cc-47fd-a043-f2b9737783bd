/* 引导弹窗样式 - 使用Portal挂载到body */
.guide-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 提高层级确保在最顶层 */
  animation: overlayFadeIn 0.3s ease-out;
  backdrop-filter: blur(4px);
  padding: 20px;
  box-sizing: border-box;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

.guide-modal {
  background: white;
  border-radius: 16px;
  width: 480px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  margin: auto;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-40px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 弹窗头部 */
.guide-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px 24px 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.guide-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.guide-title {
  flex: 1;
}

.guide-title h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
}

.guide-title p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  font-weight: 400;
}

.guide-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.guide-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 弹窗内容 */
.guide-content {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.guide-step {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.step-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.step-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* 好处说明 */
.guide-benefits {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.guide-benefits h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.guide-benefits ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.guide-benefits li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #4b5563;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-benefits li:last-child {
  margin-bottom: 0;
}

/* 弹窗底部 */
.guide-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px 24px 24px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.guide-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.guide-button.secondary {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.guide-button.secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.guide-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.guide-button.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.guide-button.primary:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .guide-modal {
    width: 95vw;
    margin: 20px;
    border-radius: 12px;
  }

  .guide-header {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .guide-close {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  .guide-content {
    padding: 20px;
  }

  .guide-step {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .step-number {
    align-self: center;
  }

  .guide-footer {
    flex-direction: column;
    padding: 16px 20px 20px 20px;
  }

  .guide-button {
    width: 100%;
  }
}

/* 滚动条样式 */
.guide-content::-webkit-scrollbar {
  width: 6px;
}

.guide-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.guide-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.guide-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
