/* Writing Pet Animations */

.writing-pet {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.writing-pet:hover {
  transform: translateY(-2px) scale(1.05) !important;
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15)) !important;
}

.writing-pet:active {
  transform: translateY(0) scale(0.95) !important;
  transition: transform 0.1s ease-out !important;
}

/* Pet SVG Container */
.pet-svg-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Base Pet Animations */
.pet-cat {
  animation: petBreathe 2s ease-in-out infinite;
}

.pet-cat.pet-animating {
  animation: petBreathe 2s ease-in-out infinite, petBounce 0.6s ease-out;
}

/* State-specific animations */
.pet-state-sleeping {
  filter: brightness(0.8) saturate(0.7);
  animation: sleepingBreathe 4s ease-in-out infinite;
}

.pet-state-sleeping .pet-eye {
  transform: scaleY(0.05);
  opacity: 0.7;
}

.pet-state-sleeping .pet-head {
  transform: translateY(1px) rotate(-1deg);
}

.pet-state-waking .pet-eye {
  animation: eyeWakeUpSequence 2s ease-out forwards;
}

.pet-state-waking .pet-pupil {
  animation: pupilFocus 2s ease-out forwards;
  animation-delay: 1.5s;
}

.pet-state-waking .pet-ear {
  animation: earPerception 0.5s ease-out forwards;
}

.pet-state-idle .pet-ear {
  animation: earGentle 3s ease-in-out infinite;
}

.pet-state-writing .pet-ear {
  animation: earPerk 0.3s ease-out forwards;
}

.pet-state-excited .pet-head {
  animation: petJump 0.8s ease-in-out infinite;
}

.pet-state-excited .pet-blush {
  opacity: 0.6 !important;
  animation: blushPulse 1s ease-in-out infinite;
}

.pet-state-sleepy .pet-eye {
  animation: eyeDrowsy 3s ease-in-out infinite;
}

.pet-state-sleepy .pet-head {
  animation: petSleep 4s ease-in-out infinite;
}

.pet-state-celebrating .pet-head {
  animation: petSpin 0.6s ease-in-out;
}

/* Eye animations */
.pet-eye {
  animation: eyeBlink 7s ease-in-out infinite;
}

.pet-eyelid {
  transform-origin: center;
}

.pet-state-writing .pet-pupil {
  animation: pupilFocusDown 2s ease-in-out infinite;
}

.pet-state-excited .pet-pupil {
  animation: pupilExcited 1s ease-in-out infinite;
}

.pet-state-sleepy .pet-pupil {
  animation: pupilSleepy 3s ease-in-out infinite;
}

.pet-state-celebrating .pet-eye {
  animation: eyeHappy 0.5s ease-out forwards;
}

/* Animation level modifiers */
.pet-level-low .pet-ear,
.pet-level-low .pet-pupil {
  animation: none !important;
}

.pet-level-low .pet-cat {
  animation: petBreatheSubtle 4s ease-in-out infinite;
}

.pet-level-high .pet-whiskers {
  animation: whiskersQuiver 1.5s ease-in-out infinite;
}

/* Keyframe definitions */
@keyframes petBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes petBreatheSubtle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes petBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1) translateY(-3px);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes petJump {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes petSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes petCelebrate {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
}

@keyframes petSleep {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
}

@keyframes sleepingBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.01);
  }
}

@keyframes eyeWakeUpSequence {
  0% {
    transform: scaleY(0.05);
    opacity: 0.7;
  }
  20% {
    transform: scaleY(0.1);
    opacity: 0.8;
  }
  40% {
    transform: scaleY(0.3);
    opacity: 0.85;
  }
  60% {
    transform: scaleY(0.6);
    opacity: 0.9;
  }
  80% {
    transform: scaleY(0.8);
    opacity: 0.95;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes pupilFocus {
  0% {
    transform: scale(0.4);
    opacity: 0.6;
  }
  50% {
    transform: scale(0.7);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes earPerception {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(-2deg);
  }
}

@keyframes earGentle {
  0%, 100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-2deg);
  }
}

@keyframes earPerk {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-5deg);
  }
}

@keyframes eyeBlink {
  0%, 85%, 100% {
    transform: scaleY(1);
  }
  90% {
    transform: scaleY(0.1);
  }
  95% {
    transform: scaleY(0.05);
  }
}

@keyframes eyeDrowsy {
  0%, 20%, 100% {
    transform: scaleY(0.6);
    opacity: 0.8;
  }
  10% {
    transform: scaleY(0.1);
    opacity: 0.6;
  }
  60% {
    transform: scaleY(0.3);
    opacity: 0.7;
  }
}

@keyframes pupilFocusDown {
  0%, 100% {
    transform: translateY(0.8px);
  }
  50% {
    transform: translateY(1.2px);
  }
}

@keyframes pupilExcited {
  0%, 100% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1.5);
  }
}

@keyframes pupilSleepy {
  0%, 100% {
    transform: scale(0.6);
  }
  50% {
    transform: scale(0.4);
  }
}

@keyframes eyeHappy {
  0% {
    transform: scaleY(1);
  }
  100% {
    transform: scaleY(0.3);
    border-radius: 50% 50% 80% 80%;
  }
}

@keyframes blushPulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes whiskersQuiver {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(0.5px);
  }
}

/* Effect animations */
.pet-effects-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: visible;
}

.pet-effect {
  position: absolute;
  animation: effectFloat 2s ease-out forwards;
}

.pet-effect-star {
  animation: effectFloat 2s ease-out forwards, effectSpin 2s linear infinite;
}

.pet-effect-heart {
  animation: effectFloat 2.5s ease-out forwards, effectPulse 0.5s ease-in-out infinite;
}

.pet-effect-sparkle {
  animation: effectFloat 1.5s ease-out forwards, effectTwinkle 0.3s ease-in-out infinite;
}

.pet-effect-note {
  animation: effectFloat 3s ease-out forwards, effectSway 1s ease-in-out infinite;
}

@keyframes effectFloat {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -150%) scale(0.8);
  }
}

@keyframes effectSpin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes effectPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes effectTwinkle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes effectSway {
  0%, 100% {
    transform: translate(-50%, -50%) rotate(-5deg);
  }
  50% {
    transform: translate(-50%, -50%) rotate(5deg);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .writing-pet,
  .writing-pet *,
  .pet-effect {
    animation: none !important;
    transition: none !important;
  }

  .writing-pet:hover {
    transform: none !important;
  }

  .pet-state-excited .pet-blush {
    opacity: 0.3 !important;
  }

  .pet-state-sleepy .pet-eye {
    transform: scaleY(0.6) !important;
  }
}

/* High contrast mode support */
@media (forced-colors: active) {
  .writing-pet {
    filter: none !important;
  }

  .pet-effect {
    opacity: 0.8 !important;
  }
}
