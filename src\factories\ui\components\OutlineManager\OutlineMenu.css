/* 菜单容器 */
.menu-container {
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
}

/* 菜单按钮基础样式 */
.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background-color: white;
  border: 1px solid #e5e7eb;
}

.menu-button:hover {
  background-color: #f9fafb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.menu-button:active {
  transform: scale(0.98);
}

.menu-button.active {
  background-color: var(--color-primary);
  color: white;
}

/* 菜单选择器样式 */
.menu-select {
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: 1px solid #e5e7eb;
  background-color: white;
}

.menu-select:hover {
  background-color: #f9fafb;
}

/* 剪刀模式样式 */
.scissors-mode-cursor {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%23ff0000" d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3h-3z"/></svg>') 0 24, auto;
}

/* 边高亮样式 */
.edge-highlight {
  stroke: #ff0000 !important;
  stroke-width: 3px !important;
  stroke-dasharray: 5, 5 !important;
  animation: edge-pulse 1s infinite;
}

@keyframes edge-pulse {
  0% {
    stroke-opacity: 0.5;
  }
  50% {
    stroke-opacity: 1;
  }
  100% {
    stroke-opacity: 0.5;
  }
}

/* 剪刀线样式 */
.scissors-line {
  stroke: #ff0000;
  stroke-width: 2px;
  stroke-dasharray: 5, 5;
  pointer-events: none;
}

/* 键盘快捷键提示样式 */
.keyboard-shortcuts {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  z-index: 1000;
  font-size: 0.875rem;
}

.keyboard-shortcuts h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1rem;
  font-weight: 600;
}

.keyboard-shortcuts ul {
  margin: 0;
  padding-left: 20px;
}

.keyboard-shortcuts li {
  margin-bottom: 4px;
}

.keyboard-shortcuts kbd {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  font-family: monospace;
  font-size: 0.85em;
  padding: 2px 4px;
}
