/* 大纲管理器样式 */
.outline-manager {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
}

.outline-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
}

.outline-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.outline-actions {
  display: flex;
  gap: 8px;
}

.outline-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* ReactFlow 自定义样式 */
.react-flow__node {
  transition: all 0.2s ease;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 自定义选择框样式 */
.react-flow__selection {
  background-color: rgba(59, 130, 246, 0.08);
  border: 2px dashed rgba(59, 130, 246, 0.6);
  border-radius: 4px;
}

/* 自定义连接线样式 */
.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path,
.selected-edge {
  stroke-width: 3;
  stroke: #3b82f6;
  filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.5));
}

/* 连接线交互区域样式 */
.edge-interaction-area {
  cursor: pointer !important;
  pointer-events: stroke !important; /* 只在线条区域触发事件 */
  z-index: 9000 !important; /* 确保在其他元素之上 */
  position: absolute !important; /* 确保z-index生效 */
  stroke: rgba(255, 0, 0, 0.01) !important; /* 添加一个几乎不可见的颜色，确保点击区域可见 */
  stroke-linecap: round !important; /* 确保线条端点是圆形的 */
  stroke-linejoin: round !important; /* 确保线条连接处是圆形的 */
}

/* 右键菜单样式 */
.edge-context-menu {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
  overflow: hidden !important;
  background-color: white !important;
  z-index: 10000 !important;
}

/* 连接线悬停样式 */
.hovered-edge {
  stroke-width: 3;
  filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.3));
}

/* 连接线删除动画 */
.edge-delete {
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 菜单出现动画 */
@keyframes menuAppear {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 菜单消失动画 */
@keyframes menuDisappear {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.95);
    opacity: 0;
  }

/* 拖拽状态下的连接线交互 */
body.dragging-active .edge-interaction-area,
body.dragging-cooldown .edge-interaction-area {
  pointer-events: none !important;
  cursor: grabbing !important;
}

/* 拖拽状态下的画布样式 */
body.dragging-active,
body.dragging-cooldown {
  cursor: grabbing !important;
}

/* 调试模式下显示连线点击区域 */
body.debug-mode .edge-interaction-area {
  stroke: rgba(255, 0, 0, 0.3) !important; /* 在调试模式下显示点击区域 */
  stroke-dasharray: 2, 2 !important; /* 添加虚线效果，更容易看清 */
}

/* 自定义控制面板样式 */
.react-flow__controls {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.react-flow__controls-button {
  background-color: white;
  border: none;
  border-bottom: 1px solid #f3f4f6;
  width: 24px;
  height: 24px;
  padding: 4px;
}

.react-flow__controls-button:hover {
  background-color: #f9fafb;
}

/* 自定义小地图样式 */
.react-flow__minimap {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 自定义背景样式 */
.react-flow__background {
  background-color: #f9fafb;
}

/* 节点拖动时的样式 */
.react-flow__node.dragging {
  z-index: 10;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 节点悬停样式 */
.react-flow__node:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 自定义连接线样式 */
@keyframes flowingDash {
  to {
    stroke-dashoffset: -20;
  }
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5, 5;
  animation: flowingDash 1s linear infinite;
}

/* 自定义节点样式 */
.outline-node {
  padding: 10px;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  min-width: 150px;
}

.outline-node.volume {
  border-left: 4px solid var(--outline-volume);
}

.outline-node.chapter {
  border-left: 4px solid var(--outline-primary);
}

.outline-node.scene {
  border-left: 4px solid var(--outline-secondary);
}

.outline-node.note {
  border-left: 4px solid var(--outline-info);
}

.outline-node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.outline-node-title {
  font-weight: 600;
  font-size: 14px;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.outline-node-type {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
}

.outline-node-type.volume {
  background-color: var(--outline-volume);
}

.outline-node-type.chapter {
  background-color: var(--outline-primary);
}

.outline-node-type.scene {
  background-color: var(--outline-secondary);
}

.outline-node-type.note {
  background-color: var(--outline-info);
}

.outline-node-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.outline-node-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
  margin-top: 8px;
}

.outline-node-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.outline-node-action:hover {
  background-color: #f3f4f6;
  color: #111827;
}

/* 拖拽选择指示器 */
.drag-selection-indicator {
  position: absolute;
  border: 2px dashed #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  pointer-events: none;
  z-index: 1000;
}

/* 批量操作工具栏 */
.batch-operations {
  display: flex;
  gap: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.batch-operation-button {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-operation-button:hover {
  background-color: #e5e7eb;
}

.batch-operation-button.danger {
  background-color: #fee2e2;
  border-color: #fecaca;
  color: #b91c1c;
}

.batch-operation-button.danger:hover {
  background-color: #fecaca;
}

/* 键盘快捷键提示 */
.keyboard-shortcuts {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #6b7280;
  z-index: 100;
}

.keyboard-shortcut {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.keyboard-shortcut:last-child {
  margin-bottom: 0;
}

.keyboard-shortcut-key {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 1px 4px;
  margin-right: 8px;
  font-family: monospace;
}

.keyboard-shortcut-description {
  flex: 1;
}
