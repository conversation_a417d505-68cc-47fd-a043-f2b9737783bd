import { IAIComponent } from './IAIComponent';

/**
 * AI写作组件接口
 * 用于根据用户提供的要求、风格和核心剧情生成内容
 */
export interface IAIWritingComponent extends IAIComponent {
  /**
   * 设置写作要求
   * @param requirements 写作要求
   */
  setRequirements(requirements: string): void;
  
  /**
   * 设置写作风格
   * @param style 写作风格
   */
  setWritingStyle(style: string): void;
  
  /**
   * 设置核心剧情
   * @param plot 核心剧情
   */
  setCorePlot(plot: string): void;
  
  /**
   * 生成内容
   * @returns 生成的内容
   */
  generate(): Promise<string>;
}
