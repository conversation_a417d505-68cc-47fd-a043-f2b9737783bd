/* 章节书籍图标3D翻页动画样式 */

.chapter-book-icon-container {
  position: relative;
  display: inline-block;
}

.chapter-book-icon {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: left center;
  transform-style: preserve-3d;
}

/* 基础状态 */
.chapter-book-icon.active {
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
}

.chapter-book-icon.active .book-outline {
  stroke: #3b82f6;
}

.chapter-book-icon.active .bookmark-ribbon {
  fill: url(#bookmark-gradient);
  stroke: #f59e0b;
  animation: bookmark-glow 2s ease-in-out infinite alternate;
}

/* 翻页动画 */
.chapter-book-icon.flipping-out {
  transform: rotateY(-90deg);
  opacity: 0.7;
}

.chapter-book-icon.flipping-in {
  animation: flip-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes flip-in {
  0% {
    transform: rotateY(90deg);
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    transform: rotateY(0deg);
    opacity: 1;
  }
}

/* 书签发光动画 */
@keyframes bookmark-glow {
  0% {
    filter: drop-shadow(0 0 2px rgba(251, 191, 36, 0.4));
  }
  100% {
    filter: drop-shadow(0 0 6px rgba(251, 191, 36, 0.8));
  }
}

/* 书签闪烁效果 */
.bookmark-shine {
  animation: shine-sweep 3s ease-in-out infinite;
}

@keyframes shine-sweep {
  0%, 90%, 100% {
    opacity: 0;
  }
  5%, 15% {
    opacity: 0.8;
  }
}

/* 页面内容动画 */
.page-content line {
  stroke-dasharray: 20;
  stroke-dashoffset: 20;
  animation: write-text 2s ease-out forwards;
}

.page-content line:nth-child(1) {
  animation-delay: 0.2s;
}

.page-content line:nth-child(2) {
  animation-delay: 0.4s;
}

.page-content line:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes write-text {
  to {
    stroke-dashoffset: 0;
  }
}

/* 悬停效果 */
.chapter-book-icon-container:hover .chapter-book-icon {
  transform: translateY(-2px) scale(1.05);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

.chapter-book-icon-container:hover .chapter-book-icon.active {
  filter: drop-shadow(0 6px 12px rgba(59, 130, 246, 0.3));
}

/* 纸质纹理动画 */
.paper-texture-overlay {
  animation: texture-shimmer 2s ease-in-out infinite;
}

@keyframes texture-shimmer {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
}

/* 3D翻页容器样式 */
.chapter-flip-container {
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* 章节项目3D效果 */
.chapter-item-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: left center;
}

.chapter-item-3d.flipping-out {
  transform: rotateY(-90deg);
}

.chapter-item-3d.flipping-in {
  transform: rotateY(90deg);
  animation: chapter-flip-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes chapter-flip-in {
  0% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

/* 书签位置动画 */
.bookmark-indicator {
  animation: bookmark-appear 0.5s ease-out;
}

@keyframes bookmark-appear {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chapter-book-icon {
    transition-duration: 0.3s;
  }
  
  .chapter-book-icon.flipping-in {
    animation-duration: 0.3s;
  }
  
  @keyframes flip-in {
    0% {
      transform: rotateY(45deg);
      opacity: 0.8;
    }
    100% {
      transform: rotateY(0deg);
      opacity: 1;
    }
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .chapter-book-icon,
  .chapter-item-3d {
    transition: opacity 0.2s ease;
    transform: none !important;
    animation: none !important;
  }
  
  .chapter-book-icon.flipping-out {
    opacity: 0.5;
  }
  
  .chapter-book-icon.flipping-in {
    opacity: 1;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chapter-book-icon.active .book-outline {
    stroke-width: 2;
  }
  
  .bookmark-ribbon {
    stroke-width: 2;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .chapter-book-icon.active {
    filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4));
  }
  
  .paper-texture-overlay {
    background: linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.05) 75%);
  }
}
