"use client";

import React from 'react';
import { Terminology } from '@/lib/db/dexie';
import { terminologyRepository } from '@/lib/db/repositories';
import { TerminologyPanelComponent } from '@/factories/ui/components/TerminologyPanel';

interface TerminologyPanelAdapterProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

/**
 * 术语面板适配器组件
 * 用于将术语面板工厂组件集成到React应用中
 */
export const TerminologyPanelAdapter: React.FC<TerminologyPanelAdapterProps> = ({
  bookId,
  isOpen,
  onClose,
  className
}) => {
  // 创建术语面板组件实例
  const terminologyPanelComponent = new TerminologyPanelComponent();

  // 设置属性
  terminologyPanelComponent.setBookId(bookId);
  terminologyPanelComponent.setIsOpen(isOpen);
  terminologyPanelComponent.onClose(onClose);
  if (className) terminologyPanelComponent.setClassName(className);

  // 设置创建回调
  terminologyPanelComponent.onCreate(async (terminology: Terminology) => {
    try {
      // 验证术语数据
      if (!terminology.name || terminology.name.trim() === '') {
        console.error('创建术语失败: 术语名称不能为空');
        alert('创建术语失败: 术语名称不能为空');
        return;
      }

      // 确保术语有必要的属性
      const terminologyToCreate: Terminology = {
        ...terminology,
        name: terminology.name.trim(),
        category: terminology.category || '',
        description: terminology.description || '',
        attributes: {
          ...terminology.attributes,
          importance: terminology.attributes?.importance || '3'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 创建术语 - 只打印一次日志，避免重复处理的错觉
      console.log('TerminologyPanelAdapter - 创建术语:', terminologyToCreate);

      // 创建术语
      const id = await terminologyRepository.create(terminologyToCreate);
      console.log('术语创建成功:', id);

      // 不再刷新页面，依赖组件内部状态更新
    } catch (error) {
      console.error('创建术语失败:', error);
      alert('创建术语失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  // 设置更新回调
  terminologyPanelComponent.onUpdate(async (terminology: Terminology) => {
    try {
      console.log('TerminologyPanelAdapter - 更新术语:', terminology);

      // 更新术语
      if (terminology.id) {
        // 确保更新时间
        const terminologyToUpdate = {
          ...terminology,
          updatedAt: new Date()
        };

        await terminologyRepository.update(terminology.id, terminologyToUpdate);
        console.log('术语更新成功:', terminology.name);

        // 不再刷新页面，依赖组件内部状态更新
      } else {
        console.error('更新术语失败: 缺少ID');
        alert('更新术语失败: 缺少ID');
      }
    } catch (error) {
      console.error('更新术语失败:', error);
      alert('更新术语失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  // 设置删除回调
  terminologyPanelComponent.onDelete(async (terminologyId: string) => {
    try {
      console.log('TerminologyPanelAdapter - 删除术语:', terminologyId);

      // 删除术语
      await terminologyRepository.delete(terminologyId);
      console.log('术语删除成功:', terminologyId);

      // 不再刷新页面，依赖组件内部状态更新
    } catch (error) {
      console.error('删除术语失败:', error);
      alert('删除术语失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  return <>{terminologyPanelComponent.render()}</>;
};
