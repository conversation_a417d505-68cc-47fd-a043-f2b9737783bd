"use client";

import React, { useState } from 'react';
import { Terminology } from '@/lib/db/dexie';

interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TerminologyViewProps {
  terminology: Terminology;
  terminologies: Terminology[];
  chapters?: GenericChapter[];
  onEdit: () => void;
  onDelete: (terminology: Terminology) => void;
  editButton: React.ReactNode;
  deleteButton: React.ReactNode;
}

/**
 * 术语详情查看组件
 */
export const TerminologyView: React.FC<TerminologyViewProps> = ({
  terminology,
  terminologies,
  chapters = [],
  onEdit,
  onDelete,
  editButton,
  deleteButton
}) => {
  // 当前活动标签
  const [activeTab, setActiveTab] = useState<'info' | 'relations' | 'chapters'>('info');

  // 获取术语类别的中文名称
  const getCategoryLabel = (category: string): string => {
    const categoryMap: Record<string, string> = {
      'item': '物品/道具',
      'skill': '技能/能力',
      'organization': '组织/势力',
      'location': '地点/区域',
      'concept': '概念/规则',
      'event': '事件/历史',
      'system': '系统/机制',
      'creature': '生物/种族',
      'other': '其他'
    };

    return categoryMap[category] || category;
  };

  // 获取重要性星级显示
  const getImportanceStars = (importance: string | undefined): string => {
    if (!importance) return '';

    const level = parseInt(importance);
    if (isNaN(level)) return '';

    return '⭐'.repeat(level);
  };

  // 获取重要性文本描述
  const getImportanceLabel = (importance: string | undefined): string => {
    if (!importance) return '';

    const level = parseInt(importance);
    if (isNaN(level)) return '';

    const labels = [
      '',
      '次要术语 - 背景补充',
      '支持术语 - 丰富世界观',
      '重要术语 - 影响情节',
      '核心术语 - 关键设定',
      '关键术语 - 世界基石'
    ];

    return labels[level] || '';
  };

  // 获取关联的术语
  const getRelatedTerminologies = () => {
    if (!terminology.relatedTerminologyIds || terminology.relatedTerminologyIds.length === 0) {
      return [];
    }

    return terminologies.filter(t => terminology.relatedTerminologyIds.includes(t.id!));
  };

  // 获取关联的章节
  const getRelatedChapters = () => {
    if (!terminology.extractedFromChapterIds || terminology.extractedFromChapterIds.length === 0) {
      return [];
    }

    return chapters.filter(c => c.id && terminology.extractedFromChapterIds.includes(c.id));
  };

  // 格式化日期
  const formatDate = (date: Date | undefined) => {
    if (!date) return '';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    return validDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* 标题和基本信息 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">{terminology.name}</h2>
        {terminology.category && (
          <div className="mt-2">
            <span className="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full">
              {getCategoryLabel(terminology.category)}
            </span>
            {terminology.attributes?.importance && (
              <span className="inline-block ml-2 text-yellow-500">
                {getImportanceStars(terminology.attributes.importance)}
              </span>
            )}
          </div>
        )}
        {terminology.alias && terminology.alias.length > 0 && (
          <div className="mt-2 text-gray-600">
            <span className="font-medium">别名：</span>
            {terminology.alias.join('、')}
          </div>
        )}
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'info'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('info')}
          >
            详细信息
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'relations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('relations')}
          >
            关联术语
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'chapters'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('chapters')}
          >
            关联章节
          </button>
        </nav>
      </div>

      {/* 标签页内容 */}
      <div>
        {/* 详细信息 */}
        {activeTab === 'info' && (
          <div className="space-y-6">
            {/* 描述 */}
            {terminology.description && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">描述</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">{terminology.description}</p>
                </div>
              </div>
            )}

            {/* 读音 */}
            {terminology.attributes?.pronunciation && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">读音</h3>
                <p className="text-gray-700">{terminology.attributes.pronunciation}</p>
              </div>
            )}

            {/* 使用示例 */}
            {terminology.attributes?.usage && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">使用示例</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">{terminology.attributes.usage}</p>
                </div>
              </div>
            )}

            {/* 起源 */}
            {terminology.attributes?.origin && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">起源</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">{terminology.attributes.origin}</p>
                </div>
              </div>
            )}

            {/* 重要性 */}
            {terminology.attributes?.importance && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">重要性</h3>
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-2">
                    {getImportanceStars(terminology.attributes.importance)}
                  </span>
                  <span className="text-gray-700">
                    {getImportanceLabel(terminology.attributes.importance)}
                  </span>
                </div>
              </div>
            )}

            {/* 备注 */}
            {terminology.notes && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">备注</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">{terminology.notes}</p>
                </div>
              </div>
            )}

            {/* 元数据 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">元数据</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">创建时间</p>
                  <p className="text-gray-700">{formatDate(terminology.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">最后更新</p>
                  <p className="text-gray-700">{formatDate(terminology.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 关联术语 */}
        {activeTab === 'relations' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">关联术语</h3>
            {getRelatedTerminologies().length === 0 ? (
              <div className="bg-gray-50 p-4 rounded-lg text-gray-500 text-center">
                <p>没有关联的术语</p>
                <p className="text-sm mt-2">点击"关联术语"按钮添加关联</p>
              </div>
            ) : (
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getRelatedTerminologies().map((relatedTerminology) => (
                  <li
                    key={relatedTerminology.id}
                    className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                    style={{
                      borderColor: 'rgba(139, 69, 19, 0.2)',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium" style={{ color: 'var(--color-primary)' }}>
                          {relatedTerminology.name}
                        </h4>
                        <div className="flex items-center mt-1">
                          {relatedTerminology.category && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {getCategoryLabel(relatedTerminology.category)}
                            </span>
                          )}
                          {relatedTerminology.attributes?.importance && (
                            <span className="ml-2 text-yellow-500 text-xs">
                              {getImportanceStars(relatedTerminology.attributes.importance)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    {relatedTerminology.description && (
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                        {relatedTerminology.description}
                      </p>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/* 关联章节 */}
        {activeTab === 'chapters' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">关联章节</h3>
            {getRelatedChapters().length === 0 ? (
              <div className="bg-gray-50 p-4 rounded-lg text-gray-500 text-center">
                <p>没有关联的章节</p>
              </div>
            ) : (
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-lg">
                {getRelatedChapters().map((chapter) => (
                  <li key={chapter.id} className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {chapter.title || `第${chapter.order !== undefined ? chapter.order + 1 : '?'}章`}
                        </h4>
                        {chapter.wordCount !== undefined && (
                          <p className="text-sm text-gray-500 mt-1">
                            字数: {chapter.wordCount}
                          </p>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
