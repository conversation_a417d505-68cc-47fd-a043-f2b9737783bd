"use client";

import React, { useState } from 'react';
import { useStyle } from './StyleContext';
import { EdgeStyle } from '../edges/CustomEdge';

// 样式控制面板组件接口
interface StyleControlPanelProps {
  onApply?: () => void;
}

// 样式控制面板组件
const StyleControlPanel: React.FC<StyleControlPanelProps> = ({ onApply }) => {
  const {
    currentStyle,
    setCurrentStyle,
    stylePresets,
    getCurrentPreset,
    getEdgeStyle,
    updateEdgeStyle
  } = useStyle();

  const [showPanel, setShowPanel] = useState(false);
  const [activeTab, setActiveTab] = useState<'presets' | 'edges' | 'nodes'>('presets');

  // 折叠状态管理
  const [expandedSections, setExpandedSections] = useState<{
    parentChild: boolean;
    association: boolean;
    sequence: boolean;
  }>({
    parentChild: true,
    association: false,
    sequence: false
  });

  // 当前预设
  const currentPreset = getCurrentPreset();

  // 切换面板显示
  const togglePanel = () => {
    setShowPanel(!showPanel);
  };

  // 切换折叠状态
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 渲染样式预设选择器
  const renderPresetSelector = () => {
    return (
      <div className="style-presets">
        <h3 className="text-sm font-medium mb-2">样式预设</h3>
        <div className="grid grid-cols-2 gap-2">
          {stylePresets.map(preset => (
            <button
              key={preset.id}
              className={`
                p-2 text-sm rounded-md transition-all
                ${currentStyle === preset.id
                  ? 'bg-primary-100 text-primary-700 border-primary-300 border'
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'}
              `}
              onClick={() => setCurrentStyle(preset.id)}
            >
              <div className="font-medium">{preset.name}</div>
              <div className="text-xs text-gray-500 mt-1">{preset.description}</div>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 渲染边样式编辑器
  const renderEdgeStyleEditor = () => {
    // 获取当前预设中的边样式
    const parentChildStyle = getEdgeStyle('parent-child');
    const associationStyle = getEdgeStyle('association');
    const sequenceStyle = getEdgeStyle('sequence');

    // 更新边样式的处理函数
    const handleEdgeStyleChange = (
      relationshipType: string,
      property: keyof EdgeStyle,
      value: any
    ) => {
      updateEdgeStyle(relationshipType, { [property]: value });
    };

    return (
      <div className="edge-styles">
        <h3 className="text-sm font-medium mb-2">连线样式</h3>

        {/* 父子关系样式 */}
        <div className="mb-4">
          <button
            className="w-full flex items-center justify-between text-xs font-medium text-gray-700 mb-2 hover:text-gray-900"
            onClick={() => toggleSection('parentChild')}
          >
            <span>父子关系</span>
            <svg
              className={`w-4 h-4 transition-transform ${expandedSections.parentChild ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.parentChild && (
            <div className="space-y-2">

          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={parentChildStyle.type}
                onChange={(e) => handleEdgeStyleChange('parent-child', 'type', e.target.value)}
              >
                <option value="bezier">贝塞尔曲线</option>
                <option value="straight">直线</option>
                <option value="step">阶梯线</option>
                <option value="smoothstep">平滑阶梯线</option>
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">箭头类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={parentChildStyle.arrowHeadType || 'none'}
                onChange={(e) => handleEdgeStyleChange('parent-child', 'arrowHeadType', e.target.value)}
              >
                <option value="none">无</option>
                <option value="arrow">开放箭头</option>
                <option value="arrowclosed">闭合箭头</option>
                <option value="circle">圆形</option>
                <option value="diamond">菱形</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条颜色</label>
              <input
                type="color"
                className="w-full h-6 border border-gray-300 rounded-md"
                value={parentChildStyle.strokeColor}
                onChange={(e) => handleEdgeStyleChange('parent-child', 'strokeColor', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">线条粗细</label>
              <input
                type="range"
                className="w-full"
                min="0.5"
                max="4"
                step="0.5"
                value={parentChildStyle.strokeWidth}
                onChange={(e) => handleEdgeStyleChange('parent-child', 'strokeWidth', parseFloat(e.target.value))}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">透明度</label>
              <input
                type="range"
                className="w-full"
                min="0.1"
                max="1"
                step="0.1"
                value={parentChildStyle.strokeOpacity}
                onChange={(e) => handleEdgeStyleChange('parent-child', 'strokeOpacity', parseFloat(e.target.value))}
              />
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">动画效果</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={!!parentChildStyle.animated}
                  onChange={(e) => handleEdgeStyleChange('parent-child', 'animated', e.target.checked)}
                />
                <span className="text-xs">启用动画</span>
              </div>
            </div>
          </div>
            </div>
          )}
        </div>

        {/* 关联关系样式 */}
        <div className="mb-4">
          <button
            className="w-full flex items-center justify-between text-xs font-medium text-gray-700 mb-2 hover:text-gray-900"
            onClick={() => toggleSection('association')}
          >
            <span>关联关系</span>
            <svg
              className={`w-4 h-4 transition-transform ${expandedSections.association ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.association && (
            <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={associationStyle.type}
                onChange={(e) => handleEdgeStyleChange('association', 'type', e.target.value)}
              >
                <option value="bezier">贝塞尔曲线</option>
                <option value="straight">直线</option>
                <option value="step">阶梯线</option>
                <option value="smoothstep">平滑阶梯线</option>
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">线条样式</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={associationStyle.strokeDasharray ? 'dashed' : 'solid'}
                onChange={(e) => handleEdgeStyleChange('association', 'strokeDasharray', e.target.value === 'dashed' ? '5,5' : undefined)}
              >
                <option value="solid">实线</option>
                <option value="dashed">虚线</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">箭头类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={associationStyle.arrowHeadType || 'none'}
                onChange={(e) => handleEdgeStyleChange('association', 'arrowHeadType', e.target.value)}
              >
                <option value="none">无</option>
                <option value="arrow">开放箭头</option>
                <option value="arrowclosed">闭合箭头</option>
                <option value="circle">圆形</option>
                <option value="diamond">菱形</option>
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">动画效果</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={!!associationStyle.animated}
                  onChange={(e) => handleEdgeStyleChange('association', 'animated', e.target.checked)}
                />
                <span className="text-xs">启用动画</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条颜色</label>
              <input
                type="color"
                className="w-full h-6 border border-gray-300 rounded-md"
                value={associationStyle.strokeColor}
                onChange={(e) => handleEdgeStyleChange('association', 'strokeColor', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">线条粗细</label>
              <input
                type="range"
                className="w-full"
                min="0.5"
                max="4"
                step="0.5"
                value={associationStyle.strokeWidth}
                onChange={(e) => handleEdgeStyleChange('association', 'strokeWidth', parseFloat(e.target.value))}
              />
            </div>
          </div>
            </div>
          )}
        </div>

        {/* 顺序关系样式 */}
        <div>
          <button
            className="w-full flex items-center justify-between text-xs font-medium text-gray-700 mb-2 hover:text-gray-900"
            onClick={() => toggleSection('sequence')}
          >
            <span>顺序关系</span>
            <svg
              className={`w-4 h-4 transition-transform ${expandedSections.sequence ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.sequence && (
            <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={sequenceStyle.type}
                onChange={(e) => handleEdgeStyleChange('sequence', 'type', e.target.value)}
              >
                <option value="bezier">贝塞尔曲线</option>
                <option value="straight">直线</option>
                <option value="step">阶梯线</option>
                <option value="smoothstep">平滑阶梯线</option>
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">箭头类型</label>
              <select
                className="w-full text-sm border border-gray-300 rounded-md p-1"
                value={sequenceStyle.arrowHeadType || 'none'}
                onChange={(e) => handleEdgeStyleChange('sequence', 'arrowHeadType', e.target.value)}
              >
                <option value="none">无</option>
                <option value="arrow">开放箭头</option>
                <option value="arrowclosed">闭合箭头</option>
                <option value="circle">圆形</option>
                <option value="diamond">菱形</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">动画效果</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={!!sequenceStyle.animated}
                  onChange={(e) => handleEdgeStyleChange('sequence', 'animated', e.target.checked)}
                />
                <span className="text-xs">启用动画</span>
              </div>
            </div>

            {sequenceStyle.animated && (
              <div>
                <label className="block text-xs text-gray-500 mb-1">动画速度</label>
                <input
                  type="range"
                  className="w-full"
                  min="0.1"
                  max="2"
                  step="0.1"
                  value={sequenceStyle.animationSpeed || 1}
                  onChange={(e) => handleEdgeStyleChange('sequence', 'animationSpeed', parseFloat(e.target.value))}
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">线条颜色</label>
              <input
                type="color"
                className="w-full h-6 border border-gray-300 rounded-md"
                value={sequenceStyle.strokeColor}
                onChange={(e) => handleEdgeStyleChange('sequence', 'strokeColor', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-xs text-gray-500 mb-1">线条粗细</label>
              <input
                type="range"
                className="w-full"
                min="0.5"
                max="4"
                step="0.5"
                value={sequenceStyle.strokeWidth}
                onChange={(e) => handleEdgeStyleChange('sequence', 'strokeWidth', parseFloat(e.target.value))}
              />
            </div>
          </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染节点样式编辑器（简化版，可以根据需要扩展）
  const renderNodeStyleEditor = () => {
    return (
      <div className="node-styles">
        <h3 className="text-sm font-medium mb-2">节点样式</h3>
        <p className="text-xs text-gray-500">节点样式编辑功能即将推出...</p>
      </div>
    );
  };

  return (
    <div className="style-control-panel">
      <button
        className="style-control-toggle flex items-center justify-center w-8 h-8 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
        onClick={togglePanel}
        title="样式设置"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>

      {showPanel && (
        <div className="style-control-content absolute right-10 top-0 w-64 bg-white rounded-lg shadow-lg z-50 max-h-[75vh] md:max-h-[80vh] lg:max-h-[75vh] flex flex-col">
          {/* 固定头部 - 标签页导航 */}
          <div className="flex-shrink-0 p-3 border-b border-gray-200">
            <div className="flex">
              <button
                className={`flex-1 pb-2 text-xs font-medium ${activeTab === 'presets' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('presets')}
              >
                预设
              </button>
              <button
                className={`flex-1 pb-2 text-xs font-medium ${activeTab === 'edges' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('edges')}
              >
                连线
              </button>
              <button
                className={`flex-1 pb-2 text-xs font-medium ${activeTab === 'nodes' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('nodes')}
              >
                节点
              </button>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <div className="flex-1 overflow-y-auto p-3">
            {activeTab === 'presets' && renderPresetSelector()}
            {activeTab === 'edges' && renderEdgeStyleEditor()}
            {activeTab === 'nodes' && renderNodeStyleEditor()}
          </div>

          {/* 固定底部 - 应用按钮 */}
          <div className="flex-shrink-0 p-3 border-t border-gray-200">
            <div className="flex justify-end">
              <button
                className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => {
                  // 关闭面板
                  setShowPanel(false);

                  // 显示成功通知
                  const showNotification = (message: string, type: string, duration: number, position: string) => {
                    console.log(`Notification (${type} @${position}, ${duration}ms): ${message}`);
                  };

                  // 输出当前样式信息，用于调试
                  const currentPreset = getCurrentPreset();
                  console.log('应用样式预设:', currentPreset.name);
                  console.log('父子关系样式:', getEdgeStyle('parent-child'));
                  console.log('关联关系样式:', getEdgeStyle('association'));
                  console.log('顺序关系样式:', getEdgeStyle('sequence'));

                  showNotification('样式已应用', 'success', 3000, 'top-center');

                  // 调用外部回调
                  if (onApply) {
                    onApply();
                  }
                }}
              >
                应用样式
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StyleControlPanel;
