"use client";

import React from 'react';
import IconBase, { IconBaseProps } from './IconBase';

/**
 * 羽毛笔图标 - 文学创作主题
 * 支持悬停摆动动画
 */
const FeatherPenIcon: React.FC<Omit<IconBaseProps, 'children'>> = (props) => {
  return (
    <IconBase {...props} className={`feather-pen-icon ${props.className || ''}`}>
      {/* 羽毛笔主体 */}
      <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z" />
      {/* 笔尖 */}
      <line x1="16" y1="8" x2="2" y2="22" />
      {/* 羽毛纹理 */}
      <line x1="17.5" y1="15" x2="9" y2="15" />
      <line x1="15" y1="13" x2="9" y2="13" />
      <line x1="12.5" y1="11" x2="9" y2="11" />
      {/* 羽毛装饰线条 */}
      <path d="M18 6l2-2" strokeWidth="1" opacity="0.6" />
      <path d="M19 7l1.5-1.5" strokeWidth="1" opacity="0.4" />
    </IconBase>
  );
};

export default FeatherPenIcon;
