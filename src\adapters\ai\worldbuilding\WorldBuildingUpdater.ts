"use client";

import { IAISenderComponent } from '@/factories/ai/interfaces/IAISenderComponent';
import { WorldBuilding } from '@/lib/db/dexie';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import createAdvancedMessageBuilder from '@/utils/ai/AdvancedMessageBuilder';
import { WorldBuildingPrompts } from '@/utils/ai/prompts/WorldBuildingPrompts';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
import { getAssociatedChapterContent } from '@/utils/chapterAssociation';
import { ChapterContent, ExtractionOptions, IWorldBuildingUpdater, UpdateSuggestion, WorldBuildingExtractionResult } from '../interfaces/WorldBuildingInterfaces';
import { WorldBuildingInfoFormatter } from './WorldBuildingInfoFormatter';
import { WorldBuildingUpdateSuggestionGenerator } from './WorldBuildingUpdateSuggestionGenerator';
import { SegmentOptions } from '@/utils/ai/ChapterSegmenter';

/**
 * 世界观更新器
 * 负责更新世界观元素的信息
 */
export class WorldBuildingUpdater implements IWorldBuildingUpdater {
  private currentRequest: AbortController | null = null;
  private infoFormatter: WorldBuildingInfoFormatter;
  private suggestionGenerator: WorldBuildingUpdateSuggestionGenerator;

  /**
   * 创建世界观更新器
   * @param aiSender AI发送器
   * @param segmenter 章节分段器
   */
  constructor(
    private aiSender: IAISenderComponent,
    private segmenter: any
  ) {
    this.infoFormatter = new WorldBuildingInfoFormatter();
    this.suggestionGenerator = new WorldBuildingUpdateSuggestionGenerator();
  }

  /**
   * 从章节内容中更新特定世界观元素的信息
   * @param worldBuilding 世界观对象
   * @param chapters 章节内容列表
   * @param options 提取选项
   * @returns 更新建议
   */
  async updateWorldBuildingFromChapters(
    worldBuilding: WorldBuilding,
    chapters: ChapterContent[],
    _options: ExtractionOptions = {}
  ): Promise<UpdateSuggestion[]> {
    try {
      // 合并章节内容
      const combinedContent = chapters.map((chapter, index) => {
        const chapterTitle = chapter.title || `章节 ${index + 1}`;
        return `# ${chapterTitle}\n\n${chapter.content}`;
      }).join('\n\n');

      // 设置分段选项
      const segmentOptions: SegmentOptions = {
        maxSegmentLength: 3000,
        minSegmentLength: 500
      };

      // 分段处理章节内容
      const segments = this.segmenter.segmentChapter(combinedContent, segmentOptions);

      // 创建请求控制器
      this.currentRequest = new AbortController();

      // 从每个段落中提取世界观信息
      const segmentResults = await Promise.all(
        segments.map((segment: string, index: number) =>
          this.extractWorldBuildingInfoFromSegment(segment, worldBuilding, index, segments.length)
        )
      );

      // 合并所有段落的提取结果
      const mergedResult: WorldBuildingExtractionResult = {
        newInfo: {},
        updateReasons: {}
      };

      for (const result of segmentResults) {
        // 合并新信息
        for (const [field, value] of Object.entries(result.newInfo)) {
          mergedResult.newInfo[field] = value as string;
        }

        // 合并更新原因
        if (result.updateReasons) {
          for (const [field, reason] of Object.entries(result.updateReasons)) {
            mergedResult.updateReasons = mergedResult.updateReasons || {};
            mergedResult.updateReasons[field] = reason as string;
          }
        }
      }

      // 生成更新建议
      return this.suggestionGenerator.generateUpdateSuggestions(worldBuilding, mergedResult);
    } catch (error) {
      console.error('从章节更新世界观信息失败:', error);
      throw error;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 从章节段落中提取特定世界观元素的信息
   * @param segment 章节段落
   * @param worldBuilding 世界观对象
   * @param segmentIndex 段落索引
   * @param totalSegments 总段落数
   * @param relatedElements 关联的世界观元素
   * @returns 提取到的世界观信息
   */
  private async extractWorldBuildingInfoFromSegment(
    segment: string,
    worldBuilding: WorldBuilding,
    segmentIndex: number = 0,
    totalSegments: number = 1,
    relatedElements: string[] = [],
    customPrompt?: string
  ): Promise<WorldBuildingExtractionResult> {
    try {
      // 格式化世界观信息
      const worldBuildingInfo = this.infoFormatter.formatWorldBuildingInfo(worldBuilding);

      // 构建基础提示词
      const basePrompt = `我将提供一个世界观元素的现有信息和一段章节内容。请分析章节内容，找出与该世界观元素相关的新信息，并提出更新建议。

现有世界观元素信息：
${worldBuildingInfo}

请提取与"${worldBuilding.name}"相关的新信息，并以JSON格式返回结果。如果发现与现有信息不同的内容，请提供更新建议和原因。`;

      // 创建高级消息构建器
      const advancedBuilder = createAdvancedMessageBuilder()
        // 添加系统角色提示词 - 使用专门的更新操作提示词
        .addSystemPrompt(WorldBuildingPrompts.updateSystemRolePrompt)
        // 添加基础提示词
        .addBasePrompt(basePrompt)
        // 添加章节内容
        .addChapterContent(segment, segmentIndex, totalSegments);

      // 为每个关联世界观元素添加单独的消息
      if (relatedElements.length > 0) {
        // 添加关联世界观元素
        await advancedBuilder.addRelatedWorldBuildings(worldBuilding, relatedElements, worldBuilding.bookId);
      } else {
        // 如果没有关联元素，添加一个简单的确认消息
        advancedBuilder.getMessageBuilder().addAssistantMessage(`我将分析章节内容，提取与"${worldBuilding.name}"相关的新信息，并提出更新建议。`);
      }

      // 添加输出格式指令
      advancedBuilder.addOutputFormat(WorldBuildingPrompts.updateOutputFormatPrompt);

      // 添加自定义提示词
      advancedBuilder.addCustomPrompt(customPrompt);

      // 构建消息数组
      const messages = advancedBuilder.build();

      // 调用AI模型
      const result = await this.aiSender.sendRequest('', {
        messages,
        temperature: 0.3, // 使用较低的温度，确保输出的一致性
        max_tokens: 2000,
        signal: this.currentRequest?.signal
      });

      // 使用通用的JSON解析工具方法
      return AIResponseParser.parseJSON<WorldBuildingExtractionResult>(result.text, { newInfo: {} });
    } catch (error) {
      console.error('从段落提取世界观信息失败:', error);
      return { newInfo: {} };
    }
  }

  /**
   * 使用关联章节内容更新世界观元素
   * @param worldBuilding 世界观对象
   * @param bookId 书籍ID
   * @param options 更新选项
   * @returns 更新建议
   */
  async updateWorldBuildingWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    bookId: string,
    options: ExtractionOptions = {}
  ): Promise<UpdateSuggestion[]> {
    try {
      if (!worldBuilding.id) {
        throw new Error('世界观ID不存在，无法获取关联章节');
      }

      // 获取关联章节内容
      let chapterContent = await getAssociatedChapterContent(worldBuilding.id, 'worldbuilding', bookId);

      if (!chapterContent) {
        console.log('没有找到关联章节内容，使用默认内容');
        // 即使没有关联章节，也继续处理，使用一个默认内容
        chapterContent = `# 默认章节\n\n这是一个默认章节内容，用于在没有关联章节时进行处理。`;
      }

      console.log(`已加载章节内容，总长度: ${chapterContent.length}`);

      // 获取关联的世界观元素和自定义提示词
      const relatedElements = options.relatedWorldBuildings || [];
      const customPrompt = options.customPrompt || '';

      // 设置分段选项
      const segmentOptions: SegmentOptions = {
        maxSegmentLength: 3000,
        minSegmentLength: 500
      };

      // 分段处理章节内容
      const segments = this.segmenter.segmentChapter(chapterContent, segmentOptions);

      // 创建请求控制器
      this.currentRequest = new AbortController();

      // 从每个段落中提取世界观信息
      const segmentResults = await Promise.all(
        segments.map((segment: string, index: number) =>
          this.extractWorldBuildingInfoFromSegment(segment, worldBuilding, index, segments.length, relatedElements, customPrompt)
        )
      );

      // 合并所有段落的提取结果
      const mergedResult: WorldBuildingExtractionResult = {
        newInfo: {},
        updateReasons: {}
      };

      for (const result of segmentResults) {
        // 合并新信息
        for (const [field, value] of Object.entries(result.newInfo)) {
          mergedResult.newInfo[field] = value as string;
        }

        // 合并更新原因
        if (result.updateReasons) {
          for (const [field, reason] of Object.entries(result.updateReasons)) {
            mergedResult.updateReasons = mergedResult.updateReasons || {};
            mergedResult.updateReasons[field] = reason as string;
          }
        }
      }

      // 生成更新建议
      return this.suggestionGenerator.generateUpdateSuggestions(worldBuilding, mergedResult);
    } catch (error) {
      console.error('使用关联章节更新世界观信息失败:', error);
      throw error;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.currentRequest = null;
    }
  }
}
