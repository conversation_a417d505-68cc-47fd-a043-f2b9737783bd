"use client";

import { AIRequestStatus, IAISenderComponent } from '../interfaces';
import { AIResponse, AIRequestOptions } from '../interfaces/IAIComponent';
import { AIMessage } from '../interfaces/IAISenderComponent';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { APIKeyConfig } from '@/types/apiKeyRotation';
import OpenAI from 'openai';
import { GoogleGenAI } from '@google/genai';

/**
 * 默认AI发送组件实现
 */
export class DefaultAISenderComponent implements IAISenderComponent {
  private requestStatus: AIRequestStatus = 'idle';
  private statusChangeCallback: ((status: AIRequestStatus) => void) | null = null;
  private abortController: AbortController | null = null;

  // 获取API设置
  private settingsFactory = createSettingsFactory();
  private apiSettings = this.settingsFactory.createAPISettingsDialogComponent();

  constructor() {
    // 确保在客户端环境中初始化时加载设置
    if (typeof window !== 'undefined') {
      // 延迟一点时间再获取API设置，确保localStorage已加载
      setTimeout(() => {
        try {
          // 重新获取API设置，确保从localStorage加载了最新的设置
          const newSettings = this.settingsFactory.createAPISettingsDialogComponent();

          // 验证新设置是否有效
          if (newSettings && typeof newSettings === 'object') {
            try {
              // 尝试调用方法验证对象是否有效
              const provider = newSettings.getCurrentProvider();
              const model = newSettings.getCurrentModel();

              // 如果能成功获取这些值，说明对象有效
              if (provider && model) {
                // 只更新引用，不修改原始设置
                this.apiSettings = newSettings;

              } else {
                console.warn('DefaultAISenderComponent: API设置无效，保留现有设置');
              }
            } catch (error) {
              console.warn('DefaultAISenderComponent: 验证API设置时出错，保留现有设置', error);
            }
          } else {
            console.warn('DefaultAISenderComponent: 无法加载有效的API设置，保留现有设置');
          }
        } catch (error) {
          console.error('DefaultAISenderComponent: 加载API设置失败', error);
        }
      }, 100);
    }
  }

  // OpenAI客户端实例
  private openaiClient: OpenAI | null = null;

  // Google GenAI客户端实例
  private googleClient: GoogleGenAI | null = null;

  /**
   * 获取API密钥（支持轮播）
   * @param provider 提供商
   * @returns API密钥信息
   */
  private getAPIKeyForProvider(provider: string): { key: string; keyId?: string; endpoint?: string } {
    // 检查是否启用轮播
    if (this.apiSettings.isRotationEnabled && this.apiSettings.isRotationEnabled()) {
      const endpoint = this.apiSettings.getAPIEndpoint(provider);
      const rotationKey = this.apiSettings.getNextRotationKey(endpoint);

      if (rotationKey) {
        return {
          key: rotationKey.key,
          keyId: rotationKey.id,
          endpoint: rotationKey.url
        };
      }
    }

    // 回退到传统方式
    return {
      key: this.apiSettings.getAPIKey(provider),
      endpoint: this.apiSettings.getAPIEndpoint(provider)
    };
  }

  /**
   * 报告请求结果（用于轮播系统）
   */
  private reportRequestResult(keyId: string | undefined, success: boolean, error?: string, responseTime?: number): void {
    if (keyId && this.apiSettings.reportRequestResult) {
      this.apiSettings.reportRequestResult(keyId, success, error, responseTime);
    }
  }

  /**
   * 获取OpenAI客户端
   * @returns OpenAI客户端实例
   */
  private getOpenAIClient(): OpenAI {
    if (!this.openaiClient) {
      const keyInfo = this.getAPIKeyForProvider('openai');
      if (!keyInfo.key) {
        throw new Error('OpenAI API密钥未设置');
      }

      this.openaiClient = new OpenAI({
        apiKey: keyInfo.key,
        baseURL: keyInfo.endpoint || this.apiSettings.getAPIEndpoint('openai'),
        dangerouslyAllowBrowser: true // 允许在浏览器环境中使用
      });
    }

    return this.openaiClient;
  }

  /**
   * 获取Google GenAI客户端
   * @returns Google GenAI客户端实例
   */
  private getGoogleClient(): GoogleGenAI {
    if (!this.googleClient) {
      const keyInfo = this.getAPIKeyForProvider('google');
      if (!keyInfo.key) {
        throw new Error('Google API密钥未设置');
      }

      this.googleClient = new GoogleGenAI({
        apiKey: keyInfo.key
      });
    }

    return this.googleClient;
  }

  /**
   * 发送AI请求
   * @param prompt 提示词
   * @param options 请求选项
   * @returns 响应结果
   */
  async sendRequest(prompt: string, options?: AIRequestOptions): Promise<AIResponse> {
    try {
      this.setRequestStatus('loading');

      // 准备请求参数
      const requestOptions = await this.prepareRequestOptions(prompt, options);

      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 根据提供商选择不同的API
      const provider = requestOptions.provider || 'openai';

      let response: AIResponse;

      switch (provider) {
        case 'openai':
          response = await this.sendOpenAIRequest(requestOptions);
          break;

        case 'google':
          response = await this.sendGoogleRequest(requestOptions);
          break;

        case 'custom':
          response = await this.sendCustomRequest(requestOptions);
          break;

        default:
          throw new Error(`不支持的提供商: ${provider}`);
      }

      this.setRequestStatus('success');
      return response;
    } catch (error: any) {
      // 如果是取消请求，不设置为错误状态
      if (error.name === 'AbortError') {
        return {
          text: '',
          success: false,
          error: 'Request cancelled'
        };
      }

      this.setRequestStatus('error');
      console.error('AI request failed:', error);

      return {
        text: '',
        success: false,
        error: error.message || 'Unknown error'
      };
    } finally {
      this.abortController = null;
    }
  }

  /**
   * 发送流式AI请求
   * @param prompt 提示词
   * @param onChunk 接收数据块的回调函数
   * @param options 请求选项
   * @returns 完整响应结果
   */
  async sendStreamingRequest(
    prompt: string,
    onChunk: (chunk: string) => void,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      this.setRequestStatus('streaming');

      // 准备请求参数
      const requestOptions = await this.prepareRequestOptions(prompt, options);
      requestOptions.streaming = true;

      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 根据提供商选择不同的API
      const provider = requestOptions.provider || 'openai';

      let response: AIResponse;

      switch (provider) {
        case 'openai':
          response = await this.sendOpenAIStreamingRequest(requestOptions, onChunk);
          break;

        case 'google':
          response = await this.sendGoogleStreamingRequest(requestOptions, onChunk);
          break;

        case 'custom':
          response = await this.sendCustomStreamingRequest(requestOptions, onChunk);
          break;

        default:
          throw new Error(`不支持的提供商: ${provider}`);
      }

      this.setRequestStatus('success');
      return response;
    } catch (error: any) {
      // 如果是取消请求，不设置为错误状态
      if (error.name === 'AbortError') {
        return {
          text: '',
          success: false,
          error: 'Request cancelled'
        };
      }

      this.setRequestStatus('error');
      console.error('AI streaming request failed:', error);

      return {
        text: '',
        success: false,
        error: error.message || 'Unknown error'
      };
    } finally {
      this.abortController = null;
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.setRequestStatus('cancelled');
    }
  }

  /**
   * 获取当前请求状态
   * @returns 请求状态
   */
  getRequestStatus(): AIRequestStatus {
    return this.requestStatus;
  }

  /**
   * 设置请求状态变更回调
   * @param callback 回调函数
   */
  onStatusChange(callback: (status: AIRequestStatus) => void): void {
    this.statusChangeCallback = callback;
  }

  /**
   * 设置请求状态
   * @param status 请求状态
   */
  private setRequestStatus(status: AIRequestStatus): void {
    this.requestStatus = status;

    if (this.statusChangeCallback) {
      this.statusChangeCallback(status);
    }
  }

  /**
   * 发送OpenAI请求
   * @param options 请求选项
   * @returns 响应结果
   */
  private async sendOpenAIRequest(options: AIRequestOptions): Promise<AIResponse> {
    const openai = this.getOpenAIClient();

    // 创建请求参数对象
    const requestParams: any = {
      model: options.model || 'gemini-2.5-pro-exp-03-25',
      messages: options.messages?.map((msg: AIMessage) => ({
        role: msg.role,
        content: msg.content
      })) || [],
      temperature: options.temperature,
      stream: false
    };

    // 只有当明确指定了maxTokens时才添加到请求参数中
    if (options.maxTokens) {
      requestParams.max_tokens = options.maxTokens;
    }

    // 添加top_p参数支持
    if (options.topP !== undefined) {
      requestParams.top_p = options.topP;
    }

    // 添加top_k参数支持（注意：OpenAI API使用logit_bias来模拟top_k效果）
    if (options.topK !== undefined) {
      // OpenAI没有直接的top_k参数，但我们可以记录这个值用于其他用途
      // 或者在未来OpenAI支持时使用
      requestParams.top_k = options.topK;
    }

    // 添加frequency_penalty和presence_penalty参数支持
    if (options.frequencyPenalty !== undefined) {
      requestParams.frequency_penalty = options.frequencyPenalty;
    }

    if (options.presencePenalty !== undefined) {
      requestParams.presence_penalty = options.presencePenalty;
    }

    const response = await openai.chat.completions.create(requestParams);

    return {
      text: response.choices[0]?.message?.content || '',
      success: true,
      model: response.model,
      provider: 'openai',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      },
      rawResponse: response
    };
  }

  /**
   * 发送OpenAI流式请求
   * @param options 请求选项
   * @param onChunk 接收数据块的回调函数
   * @returns 响应结果
   */
  private async sendOpenAIStreamingRequest(
    options: AIRequestOptions,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    const openai = this.getOpenAIClient();

    // 创建请求参数对象
    const requestParams: any = {
      model: options.model || 'gemini-2.5-pro-exp-03-25',
      messages: options.messages?.map((msg: AIMessage) => ({
        role: msg.role,
        content: msg.content
      })) || [],
      temperature: options.temperature,
      stream: true
    };

    // 只有当明确指定了maxTokens时才添加到请求参数中
    if (options.maxTokens) {
      requestParams.max_tokens = options.maxTokens;
    }

    // 添加top_p参数支持
    if (options.topP !== undefined) {
      requestParams.top_p = options.topP;
    }

    // 添加top_k参数支持（注意：OpenAI API使用logit_bias来模拟top_k效果）
    if (options.topK !== undefined) {
      // OpenAI没有直接的top_k参数，但我们可以记录这个值用于其他用途
      // 或者在未来OpenAI支持时使用
      requestParams.top_k = options.topK;
    }

    // 添加frequency_penalty和presence_penalty参数支持
    if (options.frequencyPenalty !== undefined) {
      requestParams.frequency_penalty = options.frequencyPenalty;
    }

    if (options.presencePenalty !== undefined) {
      requestParams.presence_penalty = options.presencePenalty;
    }

    // 确保stream参数为true
    requestParams.stream = true;

    const stream = await openai.chat.completions.create(requestParams);

    let fullText = '';

    // 流式响应处理
    try {
      // 使用类型断言处理流式响应
      const streamResponse = stream as any;

      // 检查是否支持异步迭代
      if (streamResponse[Symbol.asyncIterator]) {
        // 流式处理
        for await (const chunk of streamResponse) {
          const content = chunk.choices?.[0]?.delta?.content || '';
          if (content) {
            fullText += content;
            onChunk(content);
          }
        }
      } else {
        // 非流式响应，直接处理
        const content = streamResponse.choices?.[0]?.message?.content || '';
        if (content) {
          fullText = content;
          onChunk(content);
        }
      }
    } catch (error) {
      console.error('Error processing stream:', error);
    }

    return {
      text: fullText,
      success: true,
      model: options.model,
      provider: 'openai',
      usage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      }
    };
  }

  /**
   * 发送Google请求
   * @param options 请求选项
   * @returns 响应结果
   */
  private async sendGoogleRequest(options: AIRequestOptions): Promise<AIResponse> {
    const genAI = this.getGoogleClient();

    // 提取所有消息
    const messages = options.messages || [];

    // 准备请求内容
    const contents: any[] = [];

    // 处理系统消息
    const systemMessages = messages.filter((msg: AIMessage) => msg.role === 'system');
    const userAndAssistantMessages = messages.filter((msg: AIMessage) => msg.role === 'user' || msg.role === 'assistant');

    // 如果有系统消息，将其作为第一条用户消息添加
    if (systemMessages.length > 0) {
      // 合并所有系统消息
      const combinedSystemMessage = systemMessages.map((msg: AIMessage) => msg.content).join('\n\n');

      contents.push({
        role: 'user',
        parts: [{ text: `系统指令: ${combinedSystemMessage}\n\n请在回复中遵循以上系统指令。` }]
      });

      // 添加一个模型回复，确认系统指令
      contents.push({
        role: 'model',
        parts: [{ text: '我会遵循系统指令。' }]
      });
    }

    // 添加所有用户和助手消息
    for (const msg of userAndAssistantMessages) {
      contents.push({
        role: msg.role === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }]
      });
    }

    try {
      // 发送请求
      // 注意：Google GenAI API 可能有变化，这里使用兼容的方式
      // 创建生成配置对象
      // @ts-ignore - 忽略类型检查，因为 Google GenAI API 可能有变化
      const generationConfig: any = {
        temperature: options.temperature
      };

      // 只有当明确指定了maxTokens时才添加到配置中
      if (options.maxTokens) {
        generationConfig.maxOutputTokens = options.maxTokens;
      }

      // 添加top_p参数支持（Google API支持topP）
      if (options.topP !== undefined) {
        generationConfig.topP = options.topP;
      }

      // 添加top_k参数支持（Google API支持topK）
      if (options.topK !== undefined) {
        generationConfig.topK = options.topK;
      }

      // @ts-ignore - 忽略类型检查，因为 Google GenAI API 可能有变化
      const model = genAI.getGenerativeModel ?
        // @ts-ignore
        genAI.getGenerativeModel({
          model: options.model || 'gemini-2.5-pro-preview-06-05',
          generationConfig
        }) :
        // @ts-ignore
        genAI.models(options.model || 'gemini-2.5-pro-preview-06-05');

      // @ts-ignore
      const contentConfig: any = {
        contents
      };

      // 如果有生成配置，添加到请求中
      if (Object.keys(generationConfig).length > 0) {
        contentConfig.generationConfig = generationConfig;
      }

      // @ts-ignore
      const result = await model.generateContent(contentConfig);

      let resultText = '';
      try {
        // @ts-ignore
        resultText = typeof result.text === 'function' ? result.text() :
                    // @ts-ignore
                    result.response?.text() ||
                    // @ts-ignore
                    result.candidates?.[0]?.content?.parts?.[0]?.text || '';
      } catch (e) {
        console.error('Error extracting text from Google API response:', e);
        // @ts-ignore
        resultText = JSON.stringify(result);
      }

      return {
        text: resultText,
        success: true,
        model: options.model || 'gemini-2.5-pro-preview-06-05',
        provider: 'google',
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0
        },
        rawResponse: result
      };
    } catch (error) {
      console.error('Google API request failed:', error);
      throw new Error(`Google API request failed: ${error}`);
    }
  }

  /**
   * 发送Google流式请求
   * @param options 请求选项
   * @param onChunk 接收数据块的回调函数
   * @returns 响应结果
   */
  private async sendGoogleStreamingRequest(
    options: AIRequestOptions,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    const genAI = this.getGoogleClient();

    // 提取所有消息
    const messages = options.messages || [];

    // 准备请求内容
    const contents: any[] = [];

    // 处理系统消息
    const systemMessages = messages.filter((msg: AIMessage) => msg.role === 'system');
    const userAndAssistantMessages = messages.filter((msg: AIMessage) => msg.role === 'user' || msg.role === 'assistant');

    // 如果有系统消息，将其作为第一条用户消息添加
    if (systemMessages.length > 0) {
      // 合并所有系统消息
      const combinedSystemMessage = systemMessages.map((msg: AIMessage) => msg.content).join('\n\n');

      contents.push({
        role: 'user',
        parts: [{ text: `系统指令: ${combinedSystemMessage}\n\n请在回复中遵循以上系统指令。` }]
      });

      // 添加一个模型回复，确认系统指令
      contents.push({
        role: 'model',
        parts: [{ text: '我会遵循系统指令。' }]
      });
    }

    // 添加所有用户和助手消息
    for (const msg of userAndAssistantMessages) {
      contents.push({
        role: msg.role === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }]
      });
    }

    try {
      // 发送请求
      // 注意：Google GenAI API 可能有变化，这里使用兼容的方式
      // 创建生成配置对象
      // @ts-ignore - 忽略类型检查，因为 Google GenAI API 可能有变化
      const generationConfig: any = {
        temperature: options.temperature
      };

      // 只有当明确指定了maxTokens时才添加到配置中
      if (options.maxTokens) {
        generationConfig.maxOutputTokens = options.maxTokens;
      }

      // 添加top_p参数支持（Google API支持topP）
      if (options.topP !== undefined) {
        generationConfig.topP = options.topP;
      }

      // 添加top_k参数支持（Google API支持topK）
      if (options.topK !== undefined) {
        generationConfig.topK = options.topK;
      }

      // @ts-ignore - 忽略类型检查，因为 Google GenAI API 可能有变化
      const model = genAI.getGenerativeModel ?
        // @ts-ignore
        genAI.getGenerativeModel({
          model: options.model || 'gemini-2.5-pro-preview-06-05',
          generationConfig
        }) :
        // @ts-ignore
        genAI.models(options.model || 'gemini-2.5-pro-preview-06-05');

      // @ts-ignore
      const contentConfig: any = {
        contents
      };

      // 如果有生成配置，添加到请求中
      if (Object.keys(generationConfig).length > 0) {
        contentConfig.generationConfig = generationConfig;
      }

      // @ts-ignore
      const result = await model.generateContentStream(contentConfig);

      let fullText = '';

      // @ts-ignore
      for await (const chunk of result) {
        let text = '';
        try {
          // @ts-ignore
          text = typeof chunk.text === 'function' ? chunk.text() :
                // @ts-ignore
                chunk.response?.text() ||
                // @ts-ignore
                chunk.candidates?.[0]?.content?.parts?.[0]?.text || '';
        } catch (e) {
          console.error('Error extracting text from Google API chunk:', e);
          continue;
        }

        if (text) {
          fullText += text;
          onChunk(text);
        }
      }

      return {
        text: fullText,
        success: true,
        model: options.model || 'gemini-2.5-pro-thinking',
        provider: 'google',
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0
        }
      };
    } catch (error) {
      console.error('Google API streaming request failed:', error);
      throw new Error(`Google API streaming request failed: ${error}`);
    }
  }

  /**
   * 发送自定义请求（使用OpenAI兼容的API）
   * @param options 请求选项
   * @returns 响应结果
   */
  private async sendCustomRequest(options: AIRequestOptions): Promise<AIResponse> {
    // 获取自定义API密钥和端点
    const apiKey = options.apiKey || this.apiSettings.getAPIKey('custom');
    const apiEndpoint = options.apiEndpoint || this.apiSettings.getAPIEndpoint('custom');

    if (!apiKey) {
      throw new Error('自定义API密钥未设置');
    }

    if (!apiEndpoint) {
      throw new Error('自定义API端点未设置');
    }


    // 创建临时OpenAI客户端
    const customClient = new OpenAI({
      apiKey,
      baseURL: apiEndpoint,
      dangerouslyAllowBrowser: true // 允许在浏览器环境中使用
    });

    try {
      // 创建请求参数对象
      const requestParams: any = {
        model: options.model || 'gemini-2.5-pro-exp-03-25',
        messages: options.messages?.map((msg: AIMessage) => ({
          role: msg.role,
          content: msg.content
        })) || [],
        temperature: options.temperature,
        stream: false
      };

      // 只有当明确指定了maxTokens时才添加到请求参数中
      if (options.maxTokens) {
        requestParams.max_tokens = options.maxTokens;
      }

      // 添加top_p参数支持
      if (options.topP !== undefined) {
        requestParams.top_p = options.topP;
      }

      // 添加top_k参数支持（注意：OpenAI兼容API可能支持top_k）
      if (options.topK !== undefined) {
        requestParams.top_k = options.topK;
      }

      // 添加frequency_penalty和presence_penalty参数支持
      if (options.frequencyPenalty !== undefined) {
        requestParams.frequency_penalty = options.frequencyPenalty;
      }

      if (options.presencePenalty !== undefined) {
        requestParams.presence_penalty = options.presencePenalty;
      }

      const response = await customClient.chat.completions.create(requestParams);

      return {
        text: response.choices[0]?.message?.content || '',
        success: true,
        model: response.model,
        provider: 'custom',
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0
        },
        rawResponse: response
      };
    } catch (error: any) {
      console.error('自定义API请求失败:', error);
      throw new Error(`自定义API请求失败: ${error.message}`);
    }
  }

  /**
   * 发送自定义流式请求（使用OpenAI兼容的API）
   * @param options 请求选项
   * @param onChunk 接收数据块的回调函数
   * @returns 响应结果
   */
  private async sendCustomStreamingRequest(
    options: AIRequestOptions,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    // 获取自定义API密钥和端点
    const apiKey = options.apiKey || this.apiSettings.getAPIKey('custom');
    const apiEndpoint = options.apiEndpoint || this.apiSettings.getAPIEndpoint('custom');

    if (!apiKey) {
      throw new Error('自定义API密钥未设置');
    }

    if (!apiEndpoint) {
      throw new Error('自定义API端点未设置');
    }


    // 创建临时OpenAI客户端
    const customClient = new OpenAI({
      apiKey,
      baseURL: apiEndpoint,
      dangerouslyAllowBrowser: true // 允许在浏览器环境中使用
    });

    try {
      // 创建请求参数对象
      const requestParams: any = {
        model: options.model || 'gemini-2.5-pro-exp-03-25',
        messages: options.messages?.map((msg: AIMessage) => ({
          role: msg.role,
          content: msg.content
        })) || [],
        temperature: options.temperature,
        stream: true
      };

      // 只有当明确指定了maxTokens时才添加到请求参数中
      if (options.maxTokens) {
        requestParams.max_tokens = options.maxTokens;
      }

      // 添加top_p参数支持
      if (options.topP !== undefined) {
        requestParams.top_p = options.topP;
      }

      // 添加top_k参数支持（注意：OpenAI兼容API可能支持top_k）
      if (options.topK !== undefined) {
        requestParams.top_k = options.topK;
      }

      // 添加frequency_penalty和presence_penalty参数支持
      if (options.frequencyPenalty !== undefined) {
        requestParams.frequency_penalty = options.frequencyPenalty;
      }

      if (options.presencePenalty !== undefined) {
        requestParams.presence_penalty = options.presencePenalty;
      }

      // 确保stream参数为true
      requestParams.stream = true;

      const stream = await customClient.chat.completions.create(requestParams);

      let fullText = '';

      // 流式响应处理
      try {
        // 使用类型断言处理流式响应
        const streamResponse = stream as any;

        // 检查是否支持异步迭代
        if (streamResponse[Symbol.asyncIterator]) {
          // 流式处理
          for await (const chunk of streamResponse) {
            const content = chunk.choices?.[0]?.delta?.content || '';
            if (content) {
              fullText += content;
              onChunk(content);
            }
          }
        } else {
          // 非流式响应，直接处理
          const content = streamResponse.choices?.[0]?.message?.content || '';
          if (content) {
            fullText = content;
            onChunk(content);
          }
        }
      } catch (error) {
        console.error('Error processing custom stream:', error);
      }

      return {
        text: fullText,
        success: true,
        model: options.model,
        provider: 'custom',
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0
        }
      };
    } catch (error: any) {
      console.error('自定义API流式请求失败:', error);
      throw new Error(`自定义API流式请求失败: ${error.message}`);
    }
  }

  /**
   * 准备请求参数
   * @param prompt 提示词
   * @param options 请求选项
   * @returns 处理后的请求参数
   */
  private async prepareRequestOptions(prompt: string, options?: AIRequestOptions): Promise<AIRequestOptions> {
    // 获取当前设置，确保不会清除API设置
    let currentProvider = 'openai';
    let currentModel = 'gemini-2.5-pro-exp-03-25';
    let apiKey = '';
    let apiEndpoint = 'https://api.openai.com/v1';
    let streamingEnabled = true;

    try {
      // 尝试从API设置获取当前配置
      currentProvider = this.apiSettings.getCurrentProvider();
      currentModel = this.apiSettings.getCurrentModel();
      apiKey = this.apiSettings.getAPIKey(currentProvider);
      apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      // 尝试获取流式输出设置
      try {
        streamingEnabled = this.apiSettings.getStreamingEnabled();
      } catch (error) {
        // 如果方法不存在，使用默认值
        streamingEnabled = true;
      }
    } catch (error) {
      console.warn('获取API设置失败，使用默认值', error);
    }

    // 尝试从configService获取AI配置（包括top_p和top_k）
    let aiConfig: any = {};
    try {
      const { configService } = await import('@/services/configService');
      aiConfig = await configService.getAIConfig();
    } catch (error) {
      console.warn('无法获取AI配置，使用默认值', error);
    }

    // 默认选项
    const defaultOptions: AIRequestOptions = {
      model: currentModel,
      provider: currentProvider,
      temperature: aiConfig.temperature || 0.7,
      topP: aiConfig.topP || 0.75,        // 从configService获取默认值
      topK: aiConfig.topK || 120,         // 从configService获取默认值
      // 不设置maxTokens，让API自行决定最大token数
      streaming: streamingEnabled,
      systemMessage: '你是一位专业的小说创作助手，擅长提供有创意的写作建议和内容。',
      messages: [],
      apiKey: apiKey,
      apiEndpoint: apiEndpoint
    };

    // 合并选项，但确保 API 设置的参数优先级最高
    const mergedOptions = { ...defaultOptions, ...options };

    // 确保使用 API 设置中的参数
    mergedOptions.provider = options?.provider || currentProvider;
    mergedOptions.model = options?.model || currentModel;
    mergedOptions.apiKey = options?.apiKey || apiKey;
    mergedOptions.apiEndpoint = options?.apiEndpoint || apiEndpoint;

    // 检查是否已经在消息中包含了系统消息
    let hasSystemMessage = false;
    if (mergedOptions.messages && mergedOptions.messages.length > 0) {
      hasSystemMessage = mergedOptions.messages.some((msg: AIMessage) => msg.role === 'system');
    }

    // 构建消息数组
    let messages: AIMessage[] = [];

    // 如果消息中已经包含了系统消息，直接使用原始消息数组
    if (hasSystemMessage) {
      messages = [...mergedOptions.messages];
    } else {
      // 否则，添加系统消息和用户/助手消息
      const systemMsg = mergedOptions.systemMessage || defaultOptions.systemMessage || '';

      // 添加系统消息
      if (systemMsg) {
        messages.push({ role: 'system', content: systemMsg });
      }

      // 添加预设的用户消息和助手消息
      if (mergedOptions.messages && mergedOptions.messages.length > 0) {
        const userAndAssistantMessages = mergedOptions.messages.filter(
          (msg: AIMessage) => msg.role === 'user' || msg.role === 'assistant'
        );

        // 添加所有用户和助手消息
        messages.push(...userAndAssistantMessages);
      }
    }

    // 检查最后一条消息是否已经是当前的用户提示词，避免重复添加
    const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
    const lastMessageIsCurrentPrompt = lastMessage &&
                                      lastMessage.role === 'user' &&
                                      lastMessage.content === prompt;

    // 只有当最后一条消息不是当前的用户提示词时，才添加
    if (!lastMessageIsCurrentPrompt && prompt) {
      messages.push({ role: 'user', content: prompt });
    }

    // 更新消息数组
    mergedOptions.messages = messages;

    // 确保API密钥和端点已设置
    if (!mergedOptions.apiKey) {
      console.error(`API密钥未设置，提供商: ${mergedOptions.provider}`);
    }

    if (mergedOptions.provider === 'custom' && !mergedOptions.apiEndpoint) {
      console.error('自定义API端点未设置');
    }

    return mergedOptions;
  }
  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    return null;
  }
}
