/**
 * API密钥故障恢复服务
 */

import { APIKeyConfig, APIKeyStatus, HealthCheckResult, RotationEvent } from '../../types/apiKeyRotation';

export class FailureRecoveryService {
  private static instance: FailureRecoveryService;
  private recoveryTimers: Map<string, NodeJS.Timeout> = new Map();
  private healthCheckTimers: Map<string, NodeJS.Timeout> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): FailureRecoveryService {
    if (!FailureRecoveryService.instance) {
      FailureRecoveryService.instance = new FailureRecoveryService();
    }
    return FailureRecoveryService.instance;
  }

  /**
   * 处理密钥失败
   */
  handleKeyFailure(
    key: APIKeyConfig, 
    error: string, 
    maxFailures: number = 3,
    customWaitTime?: number
  ): APIKeyConfig {
    console.warn(`🔥 API密钥失败: ${key.id}, 错误: ${error}`);
    
    // 更新失败统计
    key.failureCount++;
    key.updatedAt = new Date();
    
    // 计算成功率
    key.successRate = key.usageCount > 0 ? 
      ((key.usageCount - key.failureCount) / key.usageCount) * 100 : 0;

    // 判断是否需要进入等待状态
    if (key.failureCount >= maxFailures) {
      this.moveToWaitingQueue(key, customWaitTime);
    }

    // 触发事件
    this.emitEvent({
      type: 'key-failed',
      timestamp: new Date(),
      url: key.url,
      keyId: key.id,
      data: { error, failureCount: key.failureCount },
      message: `密钥 ${key.id} 失败，失败次数: ${key.failureCount}`
    });

    return key;
  }

  /**
   * 将密钥移入等待队列
   */
  moveToWaitingQueue(key: APIKeyConfig, customWaitTime?: number): void {
    const waitTime = customWaitTime || key.customWaitTime || 60000; // 默认1分钟
    
    key.status = 'waiting';
    key.waitUntil = new Date(Date.now() + waitTime);
    
    console.log(`⏳ 密钥 ${key.id} 进入等待队列，等待时间: ${waitTime}ms`);
    
    // 清除现有的恢复定时器
    this.clearRecoveryTimer(key.id);
    
    // 设置恢复定时器
    const timer = setTimeout(() => {
      this.recoverKey(key);
    }, waitTime);
    
    this.recoveryTimers.set(key.id, timer);
    
    // 触发事件
    this.emitEvent({
      type: 'key-disabled',
      timestamp: new Date(),
      url: key.url,
      keyId: key.id,
      data: { waitTime, waitUntil: key.waitUntil },
      message: `密钥 ${key.id} 进入等待队列，等待 ${waitTime}ms`
    });
  }

  /**
   * 恢复密钥
   */
  recoverKey(key: APIKeyConfig): void {
    console.log(`🔄 尝试恢复密钥: ${key.id}`);
    
    // 重置状态
    key.status = 'active';
    key.failureCount = 0;
    key.waitUntil = undefined;
    key.updatedAt = new Date();
    
    // 清除恢复定时器
    this.clearRecoveryTimer(key.id);
    
    // 触发事件
    this.emitEvent({
      type: 'key-recovered',
      timestamp: new Date(),
      url: key.url,
      keyId: key.id,
      message: `密钥 ${key.id} 已恢复`
    });
    
    console.log(`✅ 密钥 ${key.id} 已恢复`);
  }

  /**
   * 手动恢复密钥
   */
  manualRecoverKey(key: APIKeyConfig): void {
    this.clearRecoveryTimer(key.id);
    this.recoverKey(key);
  }

  /**
   * 清除恢复定时器
   */
  private clearRecoveryTimer(keyId: string): void {
    const timer = this.recoveryTimers.get(keyId);
    if (timer) {
      clearTimeout(timer);
      this.recoveryTimers.delete(keyId);
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck(
    key: APIKeyConfig, 
    interval: number = 300000, // 5分钟
    healthCheckFn: (key: APIKeyConfig) => Promise<HealthCheckResult>
  ): void {
    // 清除现有的健康检查定时器
    this.stopHealthCheck(key.id);
    
    const timer = setInterval(async () => {
      try {
        const result = await healthCheckFn(key);
        this.handleHealthCheckResult(key, result);
      } catch (error) {
        console.error(`健康检查失败: ${key.id}`, error);
      }
    }, interval);
    
    this.healthCheckTimers.set(key.id, timer);
    console.log(`🏥 已启动密钥 ${key.id} 的健康检查，间隔: ${interval}ms`);
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck(keyId: string): void {
    const timer = this.healthCheckTimers.get(keyId);
    if (timer) {
      clearInterval(timer);
      this.healthCheckTimers.delete(keyId);
      console.log(`🛑 已停止密钥 ${keyId} 的健康检查`);
    }
  }

  /**
   * 处理健康检查结果
   */
  private handleHealthCheckResult(key: APIKeyConfig, result: HealthCheckResult): void {
    if (result.healthy) {
      // 如果密钥在等待状态但健康检查通过，可以考虑提前恢复
      if (key.status === 'waiting') {
        console.log(`💚 密钥 ${key.id} 健康检查通过，提前恢复`);
        this.recoverKey(key);
      }
    } else {
      // 健康检查失败，记录但不立即处理（避免过于敏感）
      console.warn(`💔 密钥 ${key.id} 健康检查失败: ${result.error}`);
    }
  }

  /**
   * 获取等待中的密钥
   */
  getWaitingKeys(keys: APIKeyConfig[]): APIKeyConfig[] {
    return keys.filter(key => key.status === 'waiting');
  }

  /**
   * 获取可用的密钥
   */
  getAvailableKeys(keys: APIKeyConfig[]): APIKeyConfig[] {
    const now = new Date();
    return keys.filter(key => {
      if (key.status === 'disabled') return false;
      if (key.status === 'waiting' && key.waitUntil && key.waitUntil > now) return false;
      if (key.status === 'waiting' && key.waitUntil && key.waitUntil <= now) {
        // 自动恢复过期的等待密钥
        this.recoverKey(key);
      }
      return key.status === 'active';
    });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: string, listener: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(event: RotationEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 清理所有定时器
   */
  cleanup(): void {
    // 清理恢复定时器
    this.recoveryTimers.forEach(timer => clearTimeout(timer));
    this.recoveryTimers.clear();
    
    // 清理健康检查定时器
    this.healthCheckTimers.forEach(timer => clearInterval(timer));
    this.healthCheckTimers.clear();
    
    console.log('🧹 故障恢复服务已清理');
  }
}
