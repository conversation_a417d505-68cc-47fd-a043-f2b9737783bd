import React, { useState, useEffect } from 'react';
import { SavedAIPrefix } from '../../../services/ai/AIGeneratedPrefixStorageService';
import { WorldBookPrefix } from '../../../types/worldbook';
import { KeywordTriggerService } from '../../../services/worldbook/KeywordTriggerService';
import {
  WorldBookIcon,
  EditIcon,
  DeleteIcon,
  StarIcon,
  StarFilledIcon,
  PlusIcon,
  XIcon
} from '../../common/icons/PrefixIcons';

interface WorldBookPrefixDialogProps {
  isOpen: boolean;
  onClose: () => void;
  worldBookSource: string;
  worldBookPrefixes: WorldBookPrefix[];
  selectedPrefixes: Set<string>;
  onPrefixToggle: (id: string) => void;
  onBatchSelect?: (ids: string[]) => void; // 新增批量选择回调
  onDeletePrefix: (id: string) => void;
  onToggleFavorite: (id: string) => void;
  onEditPrefix: (prefix: WorldBookPrefix) => void;
  onCreatePrefix: (worldBookSource: string) => void;
  onDeleteWorldBook: (source: string) => void;
  onApplySelected: () => void;
  onPrefixUpdate?: (updatedPrefix: WorldBookPrefix) => void;
}

export const WorldBookPrefixDialog: React.FC<WorldBookPrefixDialogProps> = ({
  isOpen,
  onClose,
  worldBookSource,
  worldBookPrefixes,
  selectedPrefixes,
  onPrefixToggle,
  onBatchSelect,
  onDeletePrefix,
  onToggleFavorite,
  onEditPrefix,
  onCreatePrefix,
  onDeleteWorldBook,
  onApplySelected,
  onPrefixUpdate
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [localPrefixes, setLocalPrefixes] = useState<WorldBookPrefix[]>(worldBookPrefixes);
  const keywordTriggerService = new KeywordTriggerService();

  // 批量标签功能状态
  const [showBatchTagDialog, setShowBatchTagDialog] = useState(false);
  const [batchTagInput, setBatchTagInput] = useState('');

  // 当外部传入的prefixes变化时，更新本地状态
  useEffect(() => {
    setLocalPrefixes(worldBookPrefixes);
  }, [worldBookPrefixes]);

  // 重置搜索状态
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setShowFavoritesOnly(false);
    }
  }, [isOpen]);

  // 过滤世界书前置消息
  const getFilteredPrefixes = (): WorldBookPrefix[] => {
    let filtered = localPrefixes;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(prefix =>
        prefix.content.toLowerCase().includes(query) ||
        prefix.description.toLowerCase().includes(query) ||
        String(prefix.originalUid).toLowerCase().includes(query) ||
        prefix.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(prefix => prefix.isFavorite);
    }

    return filtered.sort((a, b) => a.originalOrder - b.originalOrder);
  };

  const filteredPrefixes = getFilteredPrefixes();

  const handlePrefixClick = (prefix: WorldBookPrefix, e: React.MouseEvent) => {
    e.stopPropagation();
    onPrefixToggle(prefix.id);
  };

  const handleDeleteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('确定要删除这个世界书条目吗？')) {
      onDeletePrefix(prefixId);
    }
  };

  const handleFavoriteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(prefixId);
  };

  const handleDeleteWorldBookClick = () => {
    const confirmMessage = `确定要删除世界书 "${worldBookSource}" 吗？\n\n这将删除该世界书的所有 ${worldBookPrefixes.length} 个条目，此操作不可撤销。`;
    
    if (window.confirm(confirmMessage)) {
      onDeleteWorldBook(worldBookSource);
      onClose();
    }
  };

  const handleSelectAll = () => {
    // 修复全选功能：使用批量选择回调或逐个切换
    const allFilteredIds = filteredPrefixes.map(p => p.id);
    const isAllSelected = allFilteredIds.every(id => selectedPrefixes.has(id));

    if (onBatchSelect) {
      // 如果有批量选择回调，使用它
      if (isAllSelected) {
        // 取消全选：从当前选择中移除这些ID
        const remainingIds = Array.from(selectedPrefixes).filter(id => !allFilteredIds.includes(id));
        onBatchSelect(remainingIds);
      } else {
        // 全选：合并当前选择和过滤的ID
        const newSelectedIds = Array.from(new Set([...Array.from(selectedPrefixes), ...allFilteredIds]));
        onBatchSelect(newSelectedIds);
      }
    } else {
      // 回退到逐个切换的方式
      if (isAllSelected) {
        // 如果已全选，则取消全选当前过滤的条目
        allFilteredIds.forEach(id => {
          if (selectedPrefixes.has(id)) {
            onPrefixToggle(id);
          }
        });
      } else {
        // 如果未全选，则选择所有当前过滤的条目
        allFilteredIds.forEach(id => {
          if (!selectedPrefixes.has(id)) {
            onPrefixToggle(id);
          }
        });
      }
    }
  };

  const handleClearSelection = () => {
    filteredPrefixes.forEach(prefix => {
      if (selectedPrefixes.has(prefix.id)) {
        onPrefixToggle(prefix.id);
      }
    });
  };

  // 批量启用触发内容变成关键词
  const handleBatchEnableContentAsKeyword = () => {
    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    if (selectedIds.length === 0) return;

    const successCount = keywordTriggerService.batchUpdateContentAsKeywordSetting(selectedIds, true);

    if (successCount > 0) {
      // 更新本地状态
      const updatedPrefixes = localPrefixes.map(p =>
        selectedIds.includes(p.id)
          ? { ...p, enableContentAsKeyword: true }
          : p
      );
      setLocalPrefixes(updatedPrefixes);

      // 通知父组件更新
      if (onPrefixUpdate) {
        selectedIds.forEach(id => {
          const updatedPrefix = updatedPrefixes.find(p => p.id === id);
          if (updatedPrefix) {
            onPrefixUpdate(updatedPrefix);
          }
        });
      }

      alert(`✅ 成功为 ${successCount} 个条目启用了触发内容变成关键词功能`);
    }
  };

  // 批量禁用触发内容变成关键词
  const handleBatchDisableContentAsKeyword = () => {
    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    if (selectedIds.length === 0) return;

    const successCount = keywordTriggerService.batchUpdateContentAsKeywordSetting(selectedIds, false);

    if (successCount > 0) {
      // 更新本地状态
      const updatedPrefixes = localPrefixes.map(p =>
        selectedIds.includes(p.id)
          ? { ...p, enableContentAsKeyword: false }
          : p
      );
      setLocalPrefixes(updatedPrefixes);

      // 通知父组件更新
      if (onPrefixUpdate) {
        selectedIds.forEach(id => {
          const updatedPrefix = updatedPrefixes.find(p => p.id === id);
          if (updatedPrefix) {
            onPrefixUpdate(updatedPrefix);
          }
        });
      }

      alert(`✅ 成功为 ${successCount} 个条目禁用了触发内容变成关键词功能`);
    }
  };

  // 批量启用关键词触发
  const handleBatchEnableKeywordTrigger = () => {
    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    if (selectedIds.length === 0) return;

    const successCount = keywordTriggerService.batchUpdateKeywordTriggerSetting(selectedIds, true);

    if (successCount > 0) {
      // 更新本地状态
      const updatedPrefixes = localPrefixes.map(p =>
        selectedIds.includes(p.id)
          ? { ...p, enableKeywordTrigger: true }
          : p
      );
      setLocalPrefixes(updatedPrefixes);

      // 通知父组件更新
      if (onPrefixUpdate) {
        selectedIds.forEach(id => {
          const updatedPrefix = updatedPrefixes.find(p => p.id === id);
          if (updatedPrefix) {
            onPrefixUpdate(updatedPrefix);
          }
        });
      }

      alert(`✅ 成功为 ${successCount} 个条目启用了关键词触发功能`);
    }
  };

  // 批量禁用关键词触发
  const handleBatchDisableKeywordTrigger = () => {
    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    if (selectedIds.length === 0) return;

    const successCount = keywordTriggerService.batchUpdateKeywordTriggerSetting(selectedIds, false);

    if (successCount > 0) {
      // 更新本地状态
      const updatedPrefixes = localPrefixes.map(p =>
        selectedIds.includes(p.id)
          ? { ...p, enableKeywordTrigger: false }
          : p
      );
      setLocalPrefixes(updatedPrefixes);

      // 通知父组件更新
      if (onPrefixUpdate) {
        selectedIds.forEach(id => {
          const updatedPrefix = updatedPrefixes.find(p => p.id === id);
          if (updatedPrefix) {
            onPrefixUpdate(updatedPrefix);
          }
        });
      }

      alert(`✅ 成功为 ${successCount} 个条目禁用了关键词触发功能`);
    }
  };

  // 批量添加标签
  const handleBatchAddTags = () => {
    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    if (selectedIds.length === 0) {
      alert('⚠️ 请先选择要添加标签的条目');
      return;
    }

    setShowBatchTagDialog(true);
  };

  // 确认批量添加标签
  const handleConfirmBatchAddTags = async () => {
    const tagsToAdd = batchTagInput
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    if (tagsToAdd.length === 0) {
      alert('⚠️ 请输入要添加的标签');
      return;
    }

    const selectedIds = Array.from(selectedPrefixes).filter(id =>
      localPrefixes.some(p => p.id === id)
    );

    try {
      // 更新本地状态
      const updatedPrefixes = localPrefixes.map(p => {
        if (selectedIds.includes(p.id)) {
          // 合并标签，去重
          const existingTags = p.tags || [];
          const newTags = [...new Set([...existingTags, ...tagsToAdd])];
          return { ...p, tags: newTags };
        }
        return p;
      });

      setLocalPrefixes(updatedPrefixes);

      // 通知父组件更新
      if (onPrefixUpdate) {
        selectedIds.forEach(id => {
          const updatedPrefix = updatedPrefixes.find(p => p.id === id);
          if (updatedPrefix) {
            onPrefixUpdate(updatedPrefix);
          }
        });
      }

      // 关闭对话框并重置输入
      setShowBatchTagDialog(false);
      setBatchTagInput('');

      alert(`✅ 成功为 ${selectedIds.length} 个条目添加了标签: ${tagsToAdd.join(', ')}`);
    } catch (error) {
      console.error('批量添加标签失败:', error);
      alert('❌ 批量添加标签失败，请重试');
    }
  };

  // 处理关键词触发设置变更
  const handleKeywordTriggerChange = (prefixId: string, enabled: boolean, e: React.MouseEvent) => {
    e.stopPropagation();
    const prefix = localPrefixes.find(p => p.id === prefixId);
    if (prefix) {
      const success = keywordTriggerService.updateKeywordTriggerSettings(
        prefixId,
        enabled,
        prefix.sendAsIndependentMessage || true
      );

      if (success) {
        // 更新本地状态
        const updatedPrefixes = localPrefixes.map(p =>
          p.id === prefixId
            ? { ...p, enableKeywordTrigger: enabled }
            : p
        );
        setLocalPrefixes(updatedPrefixes);

        // 通知父组件更新
        const updatedPrefix = updatedPrefixes.find(p => p.id === prefixId);
        if (updatedPrefix && onPrefixUpdate) {
          onPrefixUpdate(updatedPrefix);
        }
      }
    }
  };

  // 处理发送方式设置变更
  const handleSendModeChange = (prefixId: string, sendAsIndependent: boolean, e: React.MouseEvent) => {
    e.stopPropagation();
    const prefix = localPrefixes.find(p => p.id === prefixId);
    if (prefix) {
      const success = keywordTriggerService.updateKeywordTriggerSettings(
        prefixId,
        prefix.enableKeywordTrigger || false,
        sendAsIndependent
      );

      if (success) {
        // 更新本地状态
        const updatedPrefixes = localPrefixes.map(p =>
          p.id === prefixId
            ? { ...p, sendAsIndependentMessage: sendAsIndependent }
            : p
        );
        setLocalPrefixes(updatedPrefixes);

        // 通知父组件更新
        const updatedPrefix = updatedPrefixes.find(p => p.id === prefixId);
        if (updatedPrefix && onPrefixUpdate) {
          onPrefixUpdate(updatedPrefix);
        }
      }
    }
  };

  // 处理触发内容变成关键词设置变更
  const handleContentAsKeywordChange = (prefixId: string, enableContentAsKeyword: boolean, e: React.MouseEvent) => {
    e.stopPropagation();
    const prefix = localPrefixes.find(p => p.id === prefixId);
    if (prefix) {
      const success = keywordTriggerService.updateContentAsKeywordSetting(
        prefixId,
        enableContentAsKeyword
      );

      if (success) {
        // 更新本地状态
        const updatedPrefixes = localPrefixes.map(p =>
          p.id === prefixId
            ? { ...p, enableContentAsKeyword: enableContentAsKeyword }
            : p
        );
        setLocalPrefixes(updatedPrefixes);

        // 通知父组件更新
        const updatedPrefix = updatedPrefixes.find(p => p.id === prefixId);
        if (updatedPrefix && onPrefixUpdate) {
          onPrefixUpdate(updatedPrefix);
        }
      }
    }
  };

  if (!isOpen) return null;

  const stats = {
    total: localPrefixes.length,
    constant: localPrefixes.filter(p => p.isConstant).length,
    selective: localPrefixes.filter(p => p.isSelective).length,
    favorites: localPrefixes.filter(p => p.isFavorite).length,
    totalUsage: localPrefixes.reduce((sum, p) => sum + p.usageCount, 0)
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10002] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-purple-800 flex items-center">
              <WorldBookIcon size={24} className="mr-2" />
              {worldBookSource}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              世界书条目管理 - {stats.total} 个条目，{stats.constant} 个常量，{stats.selective} 个选择性
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onCreatePrefix(worldBookSource)}
              className="flex items-center px-3 py-2 text-sm text-purple-600 border border-purple-300 rounded-lg hover:bg-purple-50"
              title="创建新的世界书条目"
            >
              <PlusIcon size={16} className="mr-1" />
              新建条目
            </button>
            <button
              onClick={handleDeleteWorldBookClick}
              className="flex items-center px-3 py-2 text-sm text-red-600 border border-red-300 rounded-lg hover:bg-red-50"
            >
              <DeleteIcon size={16} className="mr-1" />
              删除世界书
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XIcon size={24} />
            </button>
          </div>
        </div>

        {/* 过滤器区域 */}
        <div className="p-4 border-b border-gray-200 bg-purple-50">
          <div className="flex flex-wrap gap-4 items-center">
            {/* 搜索框 */}
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="搜索世界书条目..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* 收藏过滤 */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showFavoritesOnly}
                onChange={(e) => setShowFavoritesOnly(e.target.checked)}
                className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span className="text-sm text-gray-700">仅显示收藏</span>
            </label>

            {/* 统计信息 */}
            <div className="text-sm text-purple-600">
              显示 {filteredPrefixes.length} / {stats.total} 个条目
            </div>
          </div>
        </div>

        {/* 操作栏 */}
        <div className="p-4 border-b border-gray-200 bg-purple-50">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                disabled={filteredPrefixes.length === 0}
                className="px-3 py-1 text-sm text-purple-600 border border-purple-300 rounded hover:bg-purple-50 disabled:text-gray-400 disabled:border-gray-300"
              >
                {(() => {
                  const allFilteredIds = filteredPrefixes.map(p => p.id);
                  const isAllSelected = allFilteredIds.every(id => selectedPrefixes.has(id));
                  return isAllSelected ? `取消全选 (${filteredPrefixes.length})` : `全选 (${filteredPrefixes.length})`;
                })()}
              </button>
              <button
                onClick={handleClearSelection}
                disabled={selectedPrefixes.size === 0}
                className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:text-gray-400"
              >
                清空选择
              </button>
            </div>

            <div className="flex items-center space-x-4">
              {/* 批量操作按钮 */}
              {selectedPrefixes.size > 0 && (
                <div className="flex space-x-2">
                  <button
                    onClick={handleBatchAddTags}
                    className="px-3 py-1 text-sm text-green-600 border border-green-300 rounded hover:bg-green-50"
                    title="批量添加标签"
                  >
                    批量添加标签 ({selectedPrefixes.size})
                  </button>
                  <button
                    onClick={handleBatchEnableContentAsKeyword}
                    className="px-3 py-1 text-sm text-orange-600 border border-orange-300 rounded hover:bg-orange-50"
                    title="批量启用触发内容变成关键词"
                  >
                    批量启用内容触发 ({selectedPrefixes.size})
                  </button>
                  <button
                    onClick={handleBatchDisableContentAsKeyword}
                    className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                    title="批量禁用触发内容变成关键词"
                  >
                    批量禁用内容触发 ({selectedPrefixes.size})
                  </button>
                </div>
              )}

              <div className="text-sm text-gray-600">
                已选择 {Array.from(selectedPrefixes).filter(id =>
                  localPrefixes.some(p => p.id === id)
                ).length} 个条目
              </div>
            </div>
          </div>
        </div>

        {/* 前置消息列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredPrefixes.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">📚</div>
              <p>没有找到匹配的世界书条目</p>
              <p className="text-sm mt-2">尝试调整搜索条件</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPrefixes.map((prefix) => (
                <div
                  key={prefix.id}
                  onClick={(e) => handlePrefixClick(prefix, e)}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedPrefixes.has(prefix.id)
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-purple-200 hover:border-purple-300 hover:bg-purple-25'
                  }`}
                >
                  {/* 头部信息 */}
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2 flex-wrap">
                      <span className="text-xs px-2 py-1 bg-purple-100 text-purple-600 rounded">
                        UID: {prefix.originalUid}
                      </span>
                      {prefix.isConstant && (
                        <span className="text-xs px-1 py-0.5 bg-green-100 text-green-600 rounded">常量</span>
                      )}
                      {prefix.isSelective && (
                        <span className="text-xs px-1 py-0.5 bg-blue-100 text-blue-600 rounded">选择性</span>
                      )}
                    </div>

                    <div className="flex space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditPrefix(prefix);
                        }}
                        className="text-gray-400 hover:text-blue-500 transition-colors"
                        title="编辑"
                      >
                        <EditIcon size={16} />
                      </button>
                      <button
                        onClick={(e) => handleFavoriteClick(prefix.id, e)}
                        className={`${prefix.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-500 transition-colors`}
                        title="收藏"
                      >
                        {prefix.isFavorite ? <StarFilledIcon size={16} /> : <StarIcon size={16} />}
                      </button>
                      <button
                        onClick={(e) => handleDeleteClick(prefix.id, e)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        title="删除"
                      >
                        <DeleteIcon size={16} />
                      </button>
                    </div>
                  </div>

                  {/* 内容 */}
                  <div className="mb-3">
                    <p className="text-sm text-gray-800 line-clamp-3">
                      {prefix.content}
                    </p>
                  </div>

                  {/* 描述 */}
                  <div className="mb-2">
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {prefix.description}
                    </p>
                  </div>

                  {/* 关键词触发设置 */}
                  <div className="mb-2 p-2 bg-gray-50 rounded border">
                    <div className="text-xs text-gray-700 mb-2 font-medium">关键词触发设置</div>
                    <div className="space-y-1">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={prefix.enableKeywordTrigger || false}
                          onChange={(e) => handleKeywordTriggerChange(prefix.id, e.target.checked, e)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 text-xs"
                        />
                        <span className="text-xs text-gray-600">启用关键词自动触发</span>
                      </label>
                      {prefix.enableKeywordTrigger && (
                        <>
                          <label className="flex items-center space-x-2 ml-4">
                            <input
                              type="checkbox"
                              checked={prefix.sendAsIndependentMessage || false}
                              onChange={(e) => handleSendModeChange(prefix.id, e.target.checked, e)}
                              className="rounded border-gray-300 text-green-600 focus:ring-green-500 text-xs"
                            />
                            <span className="text-xs text-gray-600">作为独立消息发送</span>
                          </label>
                          <label className="flex items-center space-x-2 ml-4">
                            <input
                              type="checkbox"
                              checked={prefix.enableContentAsKeyword || false}
                              onChange={(e) => handleContentAsKeywordChange(prefix.id, e.target.checked, e)}
                              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500 text-xs"
                            />
                            <span className="text-xs text-gray-600">触发内容变成关键词</span>
                          </label>
                        </>
                      )}
                    </div>
                    {prefix.enableKeywordTrigger && (
                      <div className="mt-1 text-xs text-gray-500">
                        关键词: {[...prefix.originalKeys, ...prefix.originalKeysSecondary].slice(0, 3).join(', ')}
                        {[...prefix.originalKeys, ...prefix.originalKeysSecondary].length > 3 && '...'}
                      </div>
                    )}
                  </div>

                  {/* 底部信息 */}
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>使用 {prefix.usageCount} 次</span>
                    <span>{new Date(prefix.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部操作 */}
        <div className="p-4 border-t border-gray-200 bg-purple-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              显示 {filteredPrefixes.length} / {stats.total} 个世界书条目
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => {
                  onApplySelected();
                  onClose();
                }}
                disabled={Array.from(selectedPrefixes).filter(id =>
                  localPrefixes.some(p => p.id === id)
                ).length === 0}
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                应用选择 ({Array.from(selectedPrefixes).filter(id =>
                  localPrefixes.some(p => p.id === id)
                ).length}个)
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 批量标签对话框 */}
      {showBatchTagDialog && (
        <div className="fixed inset-0 flex items-center justify-center z-[10003] bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
            <h3 className="text-lg font-bold text-gray-800 mb-4">批量添加标签</h3>
            <p className="text-sm text-gray-600 mb-4">
              为选中的 {Array.from(selectedPrefixes).filter(id =>
                localPrefixes.some(p => p.id === id)
              ).length} 个条目添加标签
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                标签（用逗号分隔多个标签）
              </label>
              <input
                type="text"
                value={batchTagInput}
                onChange={(e) => setBatchTagInput(e.target.value)}
                placeholder="例如: 重要, 角色设定, 世界观"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleConfirmBatchAddTags();
                  }
                }}
              />
              <p className="text-xs text-gray-500 mt-1">
                提示：标签会自动去重，不会重复添加已存在的标签
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowBatchTagDialog(false);
                  setBatchTagInput('');
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleConfirmBatchAddTags}
                disabled={!batchTagInput.trim()}
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                添加标签
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
