"use client";

import React, { useState, useEffect, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PersonaCategory, CategoryPrompt } from '../../../types/ai-persona';
import PromptItem from './PromptItem';

interface CategoryItemProps {
  category: PersonaCategory;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onUpdateCategory: (updates: Partial<PersonaCategory>) => void;
  onDeleteCategory: () => void;
  onAddPrompt: (content: string) => void;
  onUpdatePrompt: (promptId: string, content: string) => void;
  onDeletePrompt: (promptId: string) => void;
  index: number;
}

const CategoryItem = forwardRef<HTMLDivElement, CategoryItemProps>(({
  category,
  isExpanded,
  onToggleExpanded,
  onUpdateCategory,
  onDeleteCategory,
  onAddPrompt,
  onUpdatePrompt,
  onDeletePrompt,
  index
}, ref) => {
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [newPromptContent, setNewPromptContent] = useState('');
  const [showAddPrompt, setShowAddPrompt] = useState(false);

  // 同步category.name到editedName状态
  useEffect(() => {
    setEditedName(category.name);
  }, [category.name]);

  // 处理分类名称编辑
  const handleNameEdit = () => {
    setIsEditingName(true);
    setEditedName(category.name);
  };

  const handleNameSave = () => {
    if (editedName.trim() && editedName.trim() !== category.name) {
      onUpdateCategory({ name: editedName.trim() });
    }
    setIsEditingName(false);
  };

  const handleNameCancel = () => {
    setEditedName(category.name);
    setIsEditingName(false);
  };

  const handleNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSave();
    } else if (e.key === 'Escape') {
      handleNameCancel();
    }
  };

  // 处理添加提示词
  const handleAddPromptSubmit = () => {
    if (newPromptContent.trim()) {
      onAddPrompt(newPromptContent.trim());
      setNewPromptContent('');
      setShowAddPrompt(false);
    }
  };

  const handleAddPromptCancel = () => {
    setNewPromptContent('');
    setShowAddPrompt(false);
  };

  const handleAddPromptKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddPromptSubmit();
    } else if (e.key === 'Escape') {
      handleAddPromptCancel();
    }
  };

  // 格式化创建时间
  const formatDate = (date: Date | string): string => {
    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    return validDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      ref={ref}
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.2,
        delay: index * 0.05
      }}
      className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800"
    >
      {/* 分类头部 */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1">
            {/* 展开/折叠按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onToggleExpanded}
              className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors mr-3"
            >
              <motion.svg
                animate={{ rotate: isExpanded ? 90 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500 dark:text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </motion.svg>
            </motion.button>

            {/* 分类名称 */}
            <div className="flex-1">
              {isEditingName ? (
                <input
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  onBlur={handleNameSave}
                  onKeyDown={handleNameKeyDown}
                  className="w-full px-2 py-1 text-sm font-medium bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-purple-500"
                  autoFocus
                />
              ) : (
                <button
                  onClick={handleNameEdit}
                  className="text-left text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                >
                  {category.name}
                </button>
              )}
            </div>

            {/* 提示词数量 */}
            <span className="ml-3 px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full">
              {category.prompts.length} 个提示词
            </span>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2 ml-4">
            {/* 添加提示词按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowAddPrompt(true)}
              className="p-1 text-green-500 hover:bg-green-100 dark:hover:bg-green-900/20 rounded transition-colors"
              title="添加提示词"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </motion.button>

            {/* 删除分类按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onDeleteCategory}
              className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
              title="删除分类"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </motion.button>
          </div>
        </div>

        {/* 分类信息 */}
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          创建于 {formatDate(category.createdAt)}
        </div>
      </div>

      {/* 展开内容 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4">
              {/* 添加提示词表单 */}
              <AnimatePresence>
                {showAddPrompt && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
                  >
                    <div className="space-y-3">
                      <textarea
                        value={newPromptContent}
                        onChange={(e) => setNewPromptContent(e.target.value)}
                        onKeyDown={handleAddPromptKeyDown}
                        placeholder="输入提示词内容..."
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm resize-none"
                        rows={3}
                        autoFocus
                      />
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={handleAddPromptCancel}
                          className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                        >
                          取消
                        </button>
                        <button
                          onClick={handleAddPromptSubmit}
                          disabled={!newPromptContent.trim()}
                          className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                        >
                          添加
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* 提示词列表 */}
              {category.prompts.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">暂无提示词</p>
                  <button
                    onClick={() => setShowAddPrompt(true)}
                    className="mt-2 text-purple-600 dark:text-purple-400 text-sm hover:underline"
                  >
                    添加第一个提示词
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <AnimatePresence mode="popLayout">
                    {category.prompts.map((prompt, promptIndex) => (
                      <PromptItem
                        key={prompt.id}
                        prompt={prompt}
                        onUpdate={(content) => onUpdatePrompt(prompt.id, content)}
                        onDelete={() => onDeletePrompt(prompt.id)}
                        index={promptIndex}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

CategoryItem.displayName = 'CategoryItem';

export default CategoryItem;
