"use client";

import React from 'react';
import { motion } from 'framer-motion';

export type PhaseType = 'intro' | 'setup' | 'compression' | 'climax' | 'resolution' | 'ending' | 'buildup' | 'custom';
export type AvatarState = 'default' | 'thinking' | 'responding' | 'excited';

interface PhaseAIAvatarProps {
  phase: PhaseType;
  state?: AvatarState;
  isAnimating?: boolean;
  className?: string;
  onClick?: () => void;
  showSettingsIcon?: boolean;
}

/**
 * 短篇创作阶段AI头像组件
 * 为不同创作阶段提供专属的SVG动态头像
 */
const PhaseAIAvatar: React.FC<PhaseAIAvatarProps> = ({
  phase,
  state = 'default',
  isAnimating = false,
  className = '',
  onClick,
  showSettingsIcon = false
}) => {
  // 阶段配置
  const phaseConfig = {
    intro: {
      name: '导语大师',
      primaryColor: '#3B82F6',
      secondaryColor: '#60A5FA',
      accentColor: '#93C5FD'
    },
    setup: {
      name: '铺垫专家',
      primaryColor: '#10B981',
      secondaryColor: '#34D399',
      accentColor: '#6EE7B7'
    },
    compression: {
      name: '情绪爆发师',
      primaryColor: '#F59E0B',
      secondaryColor: '#FBBF24',
      accentColor: '#FCD34D'
    },
    climax: {
      name: '反转大师',
      primaryColor: '#EF4444',
      secondaryColor: '#F87171',
      accentColor: '#FCA5A5'
    },
    resolution: {
      name: '解气专家',
      primaryColor: '#8B5CF6',
      secondaryColor: '#A78BFA',
      accentColor: '#C4B5FD'
    },
    ending: {
      name: '结局大师',
      primaryColor: '#EC4899',
      secondaryColor: '#F472B6',
      accentColor: '#F9A8D4'
    },
    // 兼容旧的命名
    buildup: {
      name: '情节建筑师',
      primaryColor: '#10B981',
      secondaryColor: '#34D399',
      accentColor: '#6EE7B7'
    },
    custom: {
      name: '自定义助手',
      primaryColor: '#6B7280',
      secondaryColor: '#9CA3AF',
      accentColor: '#D1D5DB'
    }
  };

  const config = phaseConfig[phase];

  // 头像动画变体
  const avatarVariants = {
    default: {
      scale: [1, 1.02, 1],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    thinking: {
      scale: 1,
      rotate: [0, -1, 1, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    responding: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    },
    excited: {
      scale: [1, 1.08, 1],
      rotate: [0, -2, 2, 0],
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  // 获取阶段特定的SVG图标
  const renderPhaseIcon = () => {
    switch (phase) {
      case 'intro':
        return (
          <g>
            {/* 戏剧面具 */}
            <path
              d="M8 12 Q12 8 16 12 Q20 16 16 20 Q12 24 8 20 Q4 16 8 12"
              fill={config.primaryColor}
              stroke={config.secondaryColor}
              strokeWidth="1"
            />
            {/* 面具眼睛 */}
            <circle cx="11" cy="15" r="1.5" fill="#FFFFFF" />
            <circle cx="17" cy="15" r="1.5" fill="#FFFFFF" />
            <circle cx="11" cy="15" r="0.8" fill="#1F2937" />
            <circle cx="17" cy="15" r="0.8" fill="#1F2937" />
            {/* 面具嘴巴 */}
            <path
              d="M10 19 Q14 22 18 19"
              stroke="#FFFFFF"
              strokeWidth="1.5"
              fill="none"
              strokeLinecap="round"
            />
          </g>
        );

      case 'setup':
      case 'buildup':
        return (
          <g>
            {/* 建筑工具 - 锤子 */}
            <rect
              x="12" y="8" width="4" height="12"
              fill={config.primaryColor}
              rx="1"
            />
            <rect
              x="8" y="8" width="12" height="4"
              fill={config.secondaryColor}
              rx="1"
            />
            {/* 工具手柄纹理 */}
            <line x1="13" y1="14" x2="15" y2="14" stroke={config.accentColor} strokeWidth="0.5" />
            <line x1="13" y1="16" x2="15" y2="16" stroke={config.accentColor} strokeWidth="0.5" />
            <line x1="13" y1="18" x2="15" y2="18" stroke={config.accentColor} strokeWidth="0.5" />
          </g>
        );

      case 'compression':
        return (
          <g>
            {/* 爆炸图标 */}
            <circle cx="14" cy="14" r="6" fill={config.primaryColor} opacity="0.8" />
            <path
              d="M8 14 L12 10 L12 12 L16 8 L16 12 L20 14 L16 16 L16 20 L12 16 L12 18 Z"
              fill={config.secondaryColor}
            />
            {/* 爆炸效果 */}
            <circle cx="14" cy="14" r="3" fill="#FFFFFF" opacity="0.6" />
          </g>
        );

      case 'climax':
        return (
          <g>
            {/* 闪电 */}
            <path
              d="M16 6 L10 14 L14 14 L12 22 L18 14 L14 14 Z"
              fill={config.primaryColor}
              stroke={config.secondaryColor}
              strokeWidth="1"
            />
            {/* 闪电内部高光 */}
            <path
              d="M15 8 L12 13 L13.5 13 L12.5 18 L15.5 13 L14 13 Z"
              fill="#FFFFFF"
              opacity="0.6"
            />
          </g>
        );

      case 'resolution':
        return (
          <g>
            {/* 天平图标 */}
            <rect x="13" y="8" width="2" height="12" fill={config.primaryColor} />
            <ellipse cx="10" cy="12" rx="3" ry="1.5" fill={config.secondaryColor} />
            <ellipse cx="18" cy="12" rx="3" ry="1.5" fill={config.secondaryColor} />
            <line x1="7" y1="12" x2="21" y2="12" stroke={config.primaryColor} strokeWidth="1" />
            {/* 平衡点 */}
            <circle cx="14" cy="12" r="1" fill={config.accentColor} />
          </g>
        );

      case 'ending':
        return (
          <g>
            {/* 皇冠图标 */}
            <path
              d="M8 18 L10 12 L14 16 L18 12 L20 18 Z"
              fill={config.primaryColor}
            />
            <circle cx="10" cy="12" r="1" fill={config.secondaryColor} />
            <circle cx="14" cy="16" r="1" fill={config.secondaryColor} />
            <circle cx="18" cy="12" r="1" fill={config.secondaryColor} />
            {/* 宝石 */}
            <polygon points="14,10 15,12 14,14 13,12" fill="#FFFFFF" />
          </g>
        );

      case 'custom':
        return (
          <g>
            {/* 通用工具图标 - 多功能扳手 */}
            <rect
              x="10" y="8" width="8" height="2"
              fill={config.primaryColor}
              rx="1"
            />
            <rect
              x="13" y="10" width="2" height="8"
              fill={config.primaryColor}
              rx="1"
            />
            {/* 可调节部分 */}
            <rect
              x="11" y="12" width="6" height="1"
              fill={config.secondaryColor}
            />
            <rect
              x="11" y="15" width="6" height="1"
              fill={config.secondaryColor}
            />
            {/* 中心连接点 */}
            <circle cx="14" cy="14" r="1.5" fill={config.accentColor} />
            <circle cx="14" cy="14" r="0.8" fill="#FFFFFF" />
          </g>
        );

      default:
        return null;
    }
  };

  // 获取状态指示器动画
  const getIndicatorAnimation = () => {
    if (state === 'thinking') {
      return {
        opacity: [0.3, 1, 0.3],
        scale: [1, 1.2, 1],
        transition: {
          duration: 1,
          repeat: Infinity,
          ease: "easeInOut"
        }
      };
    }
    if (state === 'responding' || isAnimating) {
      return {
        opacity: [0.5, 1, 0.5],
        transition: {
          duration: 0.6,
          repeat: Infinity,
          ease: "easeInOut"
        }
      };
    }
    return {
      opacity: 0.8,
      transition: { duration: 0.3 }
    };
  };

  // 获取状态指示器颜色
  const getIndicatorColor = () => {
    switch (state) {
      case 'thinking':
        return '#F59E0B';
      case 'responding':
        return '#10B981';
      case 'excited':
        return '#EF4444';
      default:
        return config.primaryColor;
    }
  };

  // 调试点击事件
  const handleClick = (e: React.MouseEvent) => {
    console.log('🔥 PhaseAIAvatar 点击事件触发!', { phase, onClick: !!onClick });
    e.stopPropagation();
    if (onClick) {
      onClick();
    }
  };

  return (
    <motion.div
      className={`flex-shrink-0 relative ${className} ${onClick ? 'cursor-pointer' : ''}`}
      variants={avatarVariants}
      animate={state}
      onClick={handleClick}
      onMouseDown={(e) => {
        console.log('🔥 PhaseAIAvatar mouseDown 事件触发!');
        e.stopPropagation();
      }}
      whileHover={onClick ? { scale: 1.05 } : {}}
      whileTap={onClick ? { scale: 0.95 } : {}}
      style={{
        zIndex: 10,
        pointerEvents: 'auto',
        userSelect: 'none'
      }}
    >
      <svg
        viewBox="0 0 28 28"
        className="w-10 h-10"
        style={{ filter: 'drop-shadow(0 2px 6px rgba(0,0,0,0.15))' }}
      >
        {/* 渐变定义 */}
        <defs>
          <linearGradient id={`phaseGradient-${phase}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={config.primaryColor} />
            <stop offset="50%" stopColor={config.secondaryColor} />
            <stop offset="100%" stopColor={config.accentColor} />
          </linearGradient>
          <radialGradient id={`phaseHighlight-${phase}`} cx="30%" cy="30%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.4" />
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0" />
          </radialGradient>
        </defs>

        {/* 主体背景圆 */}
        <circle
          cx="14"
          cy="14"
          r="12"
          fill={`url(#phaseGradient-${phase})`}
          stroke="#FFFFFF"
          strokeWidth="1"
          opacity="0.9"
        />

        {/* 高光效果 */}
        <ellipse
          cx="11"
          cy="10"
          rx="5"
          ry="3"
          fill={`url(#phaseHighlight-${phase})`}
        />

        {/* 阶段特定图标 */}
        {renderPhaseIcon()}

        {/* 状态指示器 */}
        <motion.circle
          cx="22"
          cy="6"
          r="2"
          fill={getIndicatorColor()}
          animate={getIndicatorAnimation()}
        />

        {/* 思考状态的额外效果 */}
        {state === 'thinking' && (
          <motion.g
            animate={{
              opacity: [0, 1, 0],
              y: [0, -2, 0],
              transition: { duration: 2, repeat: Infinity }
            }}
          >
            <circle cx="20" cy="8" r="1" fill="#F59E0B" opacity="0.6" />
            <circle cx="22" cy="10" r="0.5" fill="#F59E0B" opacity="0.4" />
            <circle cx="24" cy="12" r="0.3" fill="#F59E0B" opacity="0.2" />
          </motion.g>
        )}

        {/* 兴奋状态的星星效果 */}
        {state === 'excited' && (
          <>
            <motion.text
              x="4"
              y="8"
              fontSize="4"
              fill={config.primaryColor}
              animate={{
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
                transition: { duration: 0.8, delay: 0.1 }
              }}
            >
              ✨
            </motion.text>
            <motion.text
              x="22"
              y="6"
              fontSize="3"
              fill={config.secondaryColor}
              animate={{
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
                transition: { duration: 0.8, delay: 0.3 }
              }}
            >
              ✨
            </motion.text>
          </>
        )}

        {/* 设置图标 */}
        {showSettingsIcon && onClick && (
          <motion.g
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 0.8, scale: 1 }}
            whileHover={{ opacity: 1, scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            {/* 设置图标背景 */}
            <circle
              cx="22"
              cy="22"
              r="3"
              fill="#FFFFFF"
              stroke={config.primaryColor}
              strokeWidth="0.5"
              opacity="0.9"
            />
            {/* 齿轮图标 */}
            <motion.g
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <path
                d="M22 19.5 L23 20 L23 24 L22 24.5 L21 24 L21 20 Z M19.5 22 L20 21 L24 21 L24.5 22 L24 23 L20 23 Z"
                fill={config.primaryColor}
                opacity="0.8"
              />
              <circle
                cx="22"
                cy="22"
                r="1"
                fill={config.primaryColor}
              />
            </motion.g>
          </motion.g>
        )}
      </svg>

      {/* 悬停提示 */}
      {onClick && (
        <motion.div
          className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 pointer-events-none"
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          点击设置人设
        </motion.div>
      )}
    </motion.div>
  );
};

export default PhaseAIAvatar;
