"use client";

import React, { useState, useEffect } from 'react';
import { Terminology } from '@/lib/db/dexie';

interface TerminologyRelationSelectorProps {
  allTerminologies: Terminology[];
  selectedIds: string[];
  onChange: (ids: string[]) => void;
  currentCharacterId?: string;
}

/**
 * 术语关联选择器组件
 */
export const TerminologyRelationSelector: React.FC<TerminologyRelationSelectorProps> = ({
  allTerminologies,
  selectedIds,
  onChange,
  currentCharacterId
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // 过滤术语列表
  const availableTerminologies = allTerminologies
    .filter(terminology => {
      if (searchQuery.trim() === '') return true;
      return terminology.name.toLowerCase().includes(searchQuery.toLowerCase());
    });

  // 处理术语选择
  const handleToggleTerminology = (id: string) => {
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter(sid => sid !== id));
    } else {
      onChange([...selectedIds, id]);
    }
  };

  return (
    <div className="space-y-2">
      {/* 搜索框 */}
      <div className="relative">
        <input
          type="text"
          placeholder="搜索术语..."
          className="w-full pl-10 pr-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            borderColor: 'rgba(139, 69, 19, 0.2)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 已选择的术语 */}
      {selectedIds.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedIds.map(id => {
            const terminology = allTerminologies.find(t => t.id === id);
            if (!terminology) return null;
            
            return (
              <div
                key={id}
                className="flex items-center bg-blue-50 px-3 py-1 rounded-full"
                style={{
                  backgroundColor: 'rgba(139, 69, 19, 0.1)',
                  color: 'var(--color-primary)'
                }}
              >
                <span className="text-sm">{terminology.name}</span>
                <button
                  type="button"
                  onClick={() => handleToggleTerminology(id)}
                  className="ml-2 text-gray-500 hover:text-red-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* 术语列表 */}
      <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md" style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}>
        {availableTerminologies.length === 0 ? (
          <div className="p-2 text-gray-500 text-center">没有找到匹配的术语</div>
        ) : (
          <div className="divide-y divide-gray-100">
            {availableTerminologies.map(terminology => (
              <div
                key={terminology.id}
                className="p-2 flex items-center hover:bg-gray-50 cursor-pointer"
                onClick={() => terminology.id && handleToggleTerminology(terminology.id)}
              >
                <input
                  type="checkbox"
                  checked={selectedIds.includes(terminology.id || '')}
                  onChange={() => {}} // 通过父元素的onClick处理
                  className="mr-2 h-4 w-4"
                />
                <div>
                  <span className="text-sm font-medium" style={{ color: 'var(--color-primary)' }}>{terminology.name}</span>
                  {terminology.category && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-600">
                      {terminology.category}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
