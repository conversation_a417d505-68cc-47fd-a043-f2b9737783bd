"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';
import { shortStoryPhaseAIService } from '../../../services/ai/ShortStoryPhaseAIService';

interface MainEditorPanelProps {
  bookId: string;
  onContentGenerated?: (content: string) => void;
}

/**
 * 中间主编辑面板
 * 显示和编辑短篇小说内容，支持段落级别的操作
 */
export const MainEditorPanel: React.FC<MainEditorPanelProps> = ({
  bookId,
  onContentGenerated
}) => {
  return (
    <div className="h-full bg-white">
      <MainTextEditor onContentGenerated={onContentGenerated} />
    </div>
  );
};

/**
 * 主文本编辑器组件
 */
const MainTextEditor: React.FC<{
  onContentGenerated?: (content: string) => void;
}> = ({ onContentGenerated }) => {
  const { fullText, setFullText, setEditingState } = useShortStoryStore();
  const [localContent, setLocalContent] = useState(fullText || '');
  const [fontSize, setFontSize] = useState(16); // 字体大小状态
  const [isFullscreen, setIsFullscreen] = useState(false); // 专注模式状态
  const [selectedInfo, setSelectedInfo] = useState<{
    text: string;
    start: number;
    end: number;
  } | null>(null); // 选中内容信息
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 同步全局状态到本地状态
  useEffect(() => {
    setLocalContent(fullText || '');
  }, [fullText]);

  // 处理内容变化
  const handleContentChange = (content: string) => {
    setLocalContent(content);
    setFullText(content);
    setEditingState({ hasUnsavedChanges: true });
  };

  // 处理文本选择事件
  const handleTextSelection = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    // 如果有选中内容
    if (start !== end) {
      const selectedText = localContent.substring(start, end);

      // 更新选中信息状态
      setSelectedInfo({
        text: selectedText,
        start,
        end
      });

      // 保存选中内容到AI服务
      shortStoryPhaseAIService.saveSelectedContent(localContent, start, end);

      console.log('📝 文本选中:', {
        text: selectedText.substring(0, 50) + (selectedText.length > 50 ? '...' : ''),
        length: selectedText.length,
        position: `${start}-${end}`
      });
    } else {
      // 清除选中信息
      setSelectedInfo(null);
      // 注意：这里不清除AI服务中的选中内容，保持最后一次选择
    }
  }, [localContent]);

  // 处理鼠标抬起事件（用于检测选择完成）
  const handleMouseUp = useCallback(() => {
    // 延迟一点执行，确保selection已经更新
    setTimeout(handleTextSelection, 10);
  }, [handleTextSelection]);

  // 处理键盘事件（用于检测键盘选择）
  const handleKeyUp = useCallback((e: React.KeyboardEvent) => {
    // 检测可能改变选择的按键
    if (e.shiftKey || e.key === 'ArrowLeft' || e.key === 'ArrowRight' ||
        e.key === 'ArrowUp' || e.key === 'ArrowDown' ||
        e.key === 'Home' || e.key === 'End') {
      setTimeout(handleTextSelection, 10);
    }
  }, [handleTextSelection]);

  // 发送内容
  const handleSendContent = () => {
    if (localContent.trim() && onContentGenerated) {
      onContentGenerated(localContent.trim());
    }
  };

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'Enter') {
          e.preventDefault();
          handleSendContent();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [localContent, onContentGenerated]);

  const placeholderText = `在这里编写您的短篇小说内容...

您可以：
• 直接在此编写短篇内容
• 使用左侧面板的关联元素和ACE框架辅助创作
• 编辑完成后点击"发送内容"按钮

支持换行和格式化，让您的创作更加自由！`;

  return (
    <div className={`h-full flex flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      {/* 编辑器工具栏 */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <h3 className="font-medium text-gray-800">短篇正文编辑</h3>
          <div className="text-sm text-gray-500">
            字数: {localContent.length}
          </div>
          {selectedInfo && (
            <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
              {/* 动态思考表情 */}
              <svg
                className="w-5 h-5 mr-1"
                viewBox="0 0 24 24"
                fill="none"
              >
                {/* 脸部轮廓 */}
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="animate-pulse"
                />
                {/* 左眼 */}
                <circle
                  cx="9"
                  cy="10"
                  r="1"
                  fill="currentColor"
                  className="animate-bounce"
                  style={{ animationDelay: '0s', animationDuration: '2s' }}
                />
                {/* 右眼 */}
                <circle
                  cx="15"
                  cy="10"
                  r="1"
                  fill="currentColor"
                  className="animate-bounce"
                  style={{ animationDelay: '0.5s', animationDuration: '2s' }}
                />
                {/* 思考的嘴巴 */}
                <path
                  d="M10 15 Q12 13 14 15"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="animate-pulse"
                  style={{ animationDelay: '1s' }}
                />
                {/* 思考泡泡 */}
                <circle
                  cx="18"
                  cy="6"
                  r="1"
                  fill="currentColor"
                  opacity="0.6"
                  className="animate-ping"
                />
                <circle
                  cx="20"
                  cy="4"
                  r="0.5"
                  fill="currentColor"
                  opacity="0.4"
                  className="animate-ping"
                  style={{ animationDelay: '0.5s' }}
                />
              </svg>
              已选中: {selectedInfo.text.length}字
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 字体大小调整 */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setFontSize(Math.max(12, fontSize - 2))}
              className="p-1 text-gray-500 hover:text-gray-700"
              title="减小字体"
            >
              <span className="text-sm">A</span>
            </button>
            <span className="text-xs text-gray-400">{fontSize}px</span>
            <button
              onClick={() => setFontSize(Math.min(24, fontSize + 2))}
              className="p-1 text-gray-500 hover:text-gray-700"
              title="增大字体"
            >
              <span className="text-lg">A</span>
            </button>
          </div>

          {/* 专注模式切换 */}
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded"
            title={isFullscreen ? "退出专注模式" : "进入专注模式"}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>

          {/* 清除选中按钮 */}
          {selectedInfo && (
            <button
              onClick={() => {
                setSelectedInfo(null);
                shortStoryPhaseAIService.clearSelectedContent();
                console.log('🗑️ 手动清除选中内容');
              }}
              className="px-3 py-1.5 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
              title="清除选中内容"
            >
              清除选中
            </button>
          )}

          {/* 清空按钮 */}
          <button
            onClick={() => handleContentChange('')}
            className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
          >
            清空
          </button>

          {/* 发送按钮 */}
          <button
            onClick={handleSendContent}
            disabled={!localContent.trim()}
            className="px-4 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            发送内容
          </button>
        </div>
      </div>

      {/* 主编辑区域 */}
      <div className="flex-1 p-6 min-h-0">
        <textarea
          ref={textareaRef}
          value={localContent}
          onChange={(e) => handleContentChange(e.target.value)}
          onMouseUp={handleMouseUp}
          onKeyUp={handleKeyUp}
          onSelect={handleTextSelection}
          placeholder={placeholderText}
          className="w-full h-full resize-none border border-gray-200 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 leading-relaxed"
          style={{
            fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
            whiteSpace: 'pre-wrap',
            fontSize: `${fontSize}px`,
            lineHeight: fontSize < 16 ? '1.4' : '1.6'
          }}
        />
      </div>

      {/* 底部状态栏 */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>预计阅读时间: {Math.ceil(localContent.length / 300)} 分钟</span>
            <span>段落数: {localContent.split('\n\n').filter(p => p.trim()).length}</span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-xs">Ctrl+Enter 快速发送</span>
          </div>
        </div>
      </div>
    </div>
  );
};


