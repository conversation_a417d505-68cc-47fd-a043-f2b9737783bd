# 字体资源说明

## 字体文件

本项目使用以下字体：

1. **衬线字体（默认）**：
   - Georgia（系统字体）
   - Times New Roman（系统字体）

2. **无衬线字体**：
   - Roboto（已添加）
   - Inter（需要下载）
   - 系统字体备选：-apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif

3. **中文字体**：
   - 思源字体（Source Han Sans SC）
   - Noto Sans CJK SC（备选）
   - Microsoft YaHei（系统字体备选）

4. **等宽字体**：
   - Courier New（系统字体）

## 字体下载

### Inter字体下载

当前目录中的Inter字体文件仅为占位符。在实际项目中，您需要下载真实的字体文件：

1. 访问 [Google Fonts - Inter](https://fonts.google.com/specimen/Inter) 或 [Inter官网](https://rsms.me/inter/)
2. 下载字体文件
3. 将以下字体文件放置在此目录中：
   - Inter-Regular.woff2
   - Inter-Medium.woff2
   - Inter-SemiBold.woff2
   - Inter-Bold.woff2

### 思源字体下载

思源字体是Adobe和Google联合开发的开源中文字体，特别适合中文内容显示：

1. **Google Fonts方式**：
   - 访问 [Google Fonts - Noto Sans SC](https://fonts.google.com/noto/specimen/Noto+Sans+SC)
   - 选择需要的字重并下载

2. **Adobe官方方式**：
   - 访问 [Adobe Fonts - Source Han Sans](https://fonts.adobe.com/fonts/source-han-sans-simplified-chinese)
   - 下载简体中文版本

3. **GitHub开源方式**：
   - 访问 [Source Han Sans GitHub](https://github.com/adobe-fonts/source-han-sans)
   - 下载OTF或TTF格式文件

**推荐字重**：
- Regular (400)
- Medium (500)
- Bold (700)

**注意**：思源字体文件较大（每个字重约15-20MB），建议根据实际需要选择字重。如果系统已安装思源字体或Noto Sans CJK SC，会自动使用系统字体。

## 使用方法

字体已通过CSS类进行预设：

```css
.font-serif {
  font-family: Georgia, 'Times New Roman', serif;
}

.font-sans {
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.font-roboto {
  font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.font-siyuan {
  font-family: 'Source Han Sans SC', 'Noto Sans CJK SC', 'Microsoft YaHei', sans-serif;
}

.font-mono {
  font-family: 'Courier New', monospace;
}
```

### 在组件中使用

```jsx
// 使用思源字体
<div className="font-siyuan">
  这是使用思源字体显示的中文内容
</div>

// 在设置中选择思源字体
const fontOptions = [
  { id: 'siyuan', name: '思源字体', fontFamily: '"Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", sans-serif' }
];
```

在React组件中，可以通过以下方式切换字体：

```jsx
<div className="font-serif">衬线字体文本</div>
<div className="font-sans">无衬线字体文本</div>
<div className="font-mono">等宽字体文本</div>
```

或者通过内联样式：

```jsx
<div style={{ fontFamily: 'Georgia, "Times New Roman", serif' }}>衬线字体文本</div>
```
