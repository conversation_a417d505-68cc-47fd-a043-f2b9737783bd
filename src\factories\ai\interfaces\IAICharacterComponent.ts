import { IAIComponent } from './IAIComponent';
import { Character } from '@/lib/db/dexie';

/**
 * AI人物组件接口
 * 用于处理人物相关的AI功能
 */
export interface IAICharacterComponent extends IAIComponent {
  /**
   * 从文本中提取人物信息
   * @param text 文本内容
   * @param bookId 书籍ID
   * @param options 提取选项
   * @returns 提取的人物列表
   */
  extractCharacters(
    text: string, 
    bookId: string, 
    options?: CharacterExtractionOptions
  ): Promise<Character[]>;
  
  /**
   * 扩展人物信息
   * @param character 人物对象
   * @param fields 要扩展的字段
   * @returns 扩展后的人物对象
   */
  expandCharacter(
    character: Character, 
    fields?: CharacterField[]
  ): Promise<Character>;
  
  /**
   * 生成人物描述
   * @param character 人物对象
   * @param options 生成选项
   * @returns 生成的描述
   */
  generateCharacterDescription(
    character: Character, 
    options?: GenerationOptions
  ): Promise<string>;
  
  /**
   * 分析人物关系
   * @param character 人物对象
   * @param otherCharacters 其他人物列表
   * @returns 人物关系分析结果
   */
  analyzeCharacterRelationships(
    character: Character, 
    otherCharacters: Character[]
  ): Promise<CharacterRelationshipAnalysis[]>;
  
  /**
   * 设置请求状态变更回调
   * @param callback 回调函数
   */
  onStatusChange(callback: (status: string) => void): void;
  
  /**
   * 取消当前请求
   */
  cancelRequest(): void;
}

/**
 * 人物提取选项
 */
export interface CharacterExtractionOptions {
  // 最大提取数量
  maxCount?: number;
  
  // 提取详细程度：basic(基本信息), detailed(详细信息), complete(完整信息)
  detailLevel?: 'basic' | 'detailed' | 'complete';
  
  // 是否包含隐藏信息素
  includeHiddenTraits?: boolean;
  
  // 其他选项
  [key: string]: any;
}

/**
 * 人物字段
 */
export type CharacterField = 
  | 'description' 
  | 'appearance' 
  | 'personality' 
  | 'background' 
  | 'goals' 
  | 'characterArchetype'
  | 'growthArc'
  | 'hiddenMotivation'
  | 'secretHistory'
  | 'innerConflicts'
  | 'symbolism';

/**
 * 生成选项
 */
export interface GenerationOptions {
  // 生成长度
  length?: 'short' | 'medium' | 'long';
  
  // 生成风格
  style?: 'formal' | 'casual' | 'creative';
  
  // 其他选项
  [key: string]: any;
}

/**
 * 人物关系分析结果
 */
export interface CharacterRelationshipAnalysis {
  // 目标人物ID
  targetCharacterId: string;
  
  // 目标人物名称
  targetCharacterName: string;
  
  // 关系类型
  relationshipType: string;
  
  // 关系描述
  description: string;
  
  // 关系强度（1-10）
  strength: number;
  
  // 关系是否为对抗性
  isAntagonistic: boolean;
}
