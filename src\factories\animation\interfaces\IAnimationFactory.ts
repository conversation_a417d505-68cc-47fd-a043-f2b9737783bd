import { IFadeAnimation, FadeDirection } from './IFadeAnimation';
import { IScaleAnimation } from './IScaleAnimation';

/**
 * 动画工厂接口
 */
export interface IAnimationFactory {
  /**
   * 创建淡入淡出动画
   * @param direction 方向
   * @param duration 持续时间（毫秒）
   * @param delay 延迟时间（毫秒）
   * @param visible 是否显示
   */
  createFadeAnimation(
    direction?: FadeDirection,
    duration?: number,
    delay?: number,
    visible?: boolean
  ): IFadeAnimation;
  
  /**
   * 创建缩放动画
   * @param startScale 起始缩放比例
   * @param endScale 结束缩放比例
   * @param duration 持续时间（毫秒）
   * @param delay 延迟时间（毫秒）
   * @param visible 是否显示
   */
  createScaleAnimation(
    startScale?: number,
    endScale?: number,
    duration?: number,
    delay?: number,
    visible?: boolean
  ): IScaleAnimation;
}
