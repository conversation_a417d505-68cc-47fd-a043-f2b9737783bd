import { IUIComponent } from './IUIComponent';

/**
 * 确认对话框类型
 */
export type ConfirmDialogType = 'primary' | 'danger' | 'warning';

/**
 * 确认对话框组件接口
 */
export interface IConfirmDialogComponent extends IUIComponent {
  /**
   * 设置对话框标题
   * @param title 对话框标题
   */
  setTitle(title: string): void;
  
  /**
   * 设置对话框消息
   * @param message 对话框消息
   */
  setMessage(message: string): void;
  
  /**
   * 设置确认按钮文本
   * @param text 确认按钮文本
   */
  setConfirmText(text: string): void;
  
  /**
   * 设置取消按钮文本
   * @param text 取消按钮文本
   */
  setCancelText(text: string): void;
  
  /**
   * 设置确认按钮类型
   * @param type 确认按钮类型
   */
  setConfirmType(type: ConfirmDialogType): void;
  
  /**
   * 设置对话框是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 设置确认回调函数
   * @param handler 确认回调函数
   */
  onConfirm(handler: () => void): void;
  
  /**
   * 设置取消回调函数
   * @param handler 取消回调函数
   */
  onCancel(handler: () => void): void;
}
