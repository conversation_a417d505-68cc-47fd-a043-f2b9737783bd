"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';

interface PhaseAIDialogProps {
  isOpen: boolean;
  onClose: () => void;
  phaseKey: string;
  phaseName: string;
  phaseDescription: string;
  onContentUpdate?: (content: string, position?: number) => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

/**
 * 阶段AI交互弹窗组件
 * 为特定创作阶段提供AI指导和交互功能
 */
export const PhaseAIDialog: React.FC<PhaseAIDialogProps> = ({
  isOpen,
  onClose,
  phaseKey,
  phaseName,
  phaseDescription,
  onContentUpdate
}) => {
  const { fullText, params } = useShortStoryStore();
  
  // 本地状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化对话
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'ai',
        content: `你好！我是你的${phaseName}创作助手。

当前阶段：${phaseName}
阶段要求：${phaseDescription}

我可以帮助你：
• 分析当前正文内容
• 提供${phaseName}的创作建议
• 指定位置进行内容修改
• 优化文本表达

请告诉我你需要什么帮助？`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, phaseName, phaseDescription]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // 构建AI请求的上下文信息
      const contextInfo = {
        currentPhase: phaseKey,
        phaseName: phaseName,
        phaseDescription: phaseDescription,
        fullText: fullText || '',
        textLength: (fullText || '').length,
        userInput: inputText.trim(),
        creationParams: params
      };

      // 模拟AI响应（后续替换为真实AI服务调用）
      const aiResponse = await simulateAIResponse(contextInfo);

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('AI响应失败:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '抱歉，AI服务暂时不可用，请稍后再试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 键盘事件处理
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-[600px] h-[500px] flex flex-col overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">{phaseName} - AI创作助手</h3>
              <p className="text-sm text-gray-600">{phaseDescription}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleClearChat}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              title="清空对话"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                <div className="whitespace-pre-wrap text-sm leading-relaxed">
                  {message.content}
                </div>
                <div className={`text-xs mt-1 ${
                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  <span className="text-sm text-gray-600 ml-2">AI思考中...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入你的问题或需求..."
                className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={2}
                disabled={isLoading}
              />
            </div>
            
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim() || isLoading}
              className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <span>当前正文字数: {(fullText || '').length}</span>
            <span>Enter发送 • Shift+Enter换行</span>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * 模拟AI响应（临时实现，后续替换为真实AI服务）
 */
async function simulateAIResponse(context: any): Promise<string> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  const { currentPhase, fullText, userInput } = context;

  // 根据阶段和用户输入生成不同的响应
  if (currentPhase === 'intro') {
    if (userInput.includes('分析') || userInput.includes('当前')) {
      return `我来分析一下你当前的导语部分：

📊 **当前状态分析：**
• 字数：${fullText.length}字 ${fullText.length < 50 ? '(建议增加到50-150字)' : fullText.length > 150 ? '(建议精简到150字以内)' : '(字数合适)'}
• 悬念设置：${fullText.includes('？') || fullText.includes('...') ? '✅ 已设置悬念' : '⚠️ 建议增加悬念元素'}

💡 **导语优化建议：**
1. 开头要有强烈的冲击感
2. 快速建立悬念或反转
3. 让读者产生"这是怎么回事？"的疑问

需要我帮你优化具体的某个部分吗？`;
    }
    
    if (userInput.includes('修改') || userInput.includes('优化')) {
      return `我可以帮你优化导语！请告诉我：

🎯 **修改方式：**
1. **整体重写** - 我来重新创作导语
2. **局部修改** - 指定要修改的句子或段落
3. **风格调整** - 改变语调和表达方式

📝 **导语公式参考：**
• 受辱 + 反击 + 打脸
• 意外事件 + 身份反转
• 危机时刻 + 能力展现

你希望采用哪种方式？或者有其他具体要求？`;
    }
    
    return `作为导语阶段的AI助手，我注意到你的问题。

🎭 **导语创作要点：**
• 字数控制：50-150字
• 核心任务：建立悬念，抓住读者
• 情感目标：让读者产生强烈的好奇心

💬 **我能帮你：**
• 分析当前导语的效果
• 提供具体的修改建议
• 重写或优化特定部分
• 调整语言风格和节奏

请告诉我你具体需要什么帮助？`;
  }

  return '我正在学习如何更好地帮助你，请稍后再试。';
}
