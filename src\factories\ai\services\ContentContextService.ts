"use client";

import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { RewriteContentParams } from './AIRewriteService';
import { TextProcessingServiceInterface, createTextProcessingService } from './TextProcessingService';

/**
 * 内容上下文服务接口
 */
export interface ContentContextServiceInterface {
  /**
   * 添加选中的章节
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  addSelectedChapters(messageBuilder: MessageBuilder, params: RewriteContentParams): void;

  /**
   * 添加选中的人物
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  addSelectedCharacters(messageBuilder: MessageBuilder, params: RewriteContentParams): void;

  /**
   * 添加选中的世界观
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  addSelectedWorldBuildings(messageBuilder: MessageBuilder, params: RewriteContentParams): void;

  /**
   * 添加选中的术语
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  addSelectedTerminologies(messageBuilder: MessageBuilder, params: RewriteContentParams): void;
}

/**
 * 内容上下文服务实现
 */
export class ContentContextService implements ContentContextServiceInterface {
  private textProcessingService: TextProcessingServiceInterface;

  constructor(textProcessingService?: TextProcessingServiceInterface) {
    this.textProcessingService = textProcessingService || createTextProcessingService();
  }

  /**
   * 添加选中的章节
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  public addSelectedChapters(messageBuilder: MessageBuilder, params: RewriteContentParams): void {
    const { chapters, selectedChapterIds } = params;
    
    if (!chapters || !selectedChapterIds || selectedChapterIds.length === 0) {
      return;
    }

    // 获取选中的章节
    const selectedChapters = chapters.filter(chapter => 
      selectedChapterIds.includes(chapter.id || '')
    );

    if (selectedChapters.length > 0) {
      // 添加一条说明消息
      messageBuilder.addAssistantMessage(`我将查看并分析以下章节内容，以便创作符合上下文的内容：`);

      // 使用智能分段器

      // 为每个章节进行智能分段并添加单独的消息
      let globalSegmentIndex = 0;
      let totalSegmentsCount = 0;

      // 先计算总段落数
      for (const chapter of selectedChapters) {
        const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content}`;
        const segments = this.textProcessingService.segmentText(chapterText);
        totalSegmentsCount += segments.length;
      }

      // 为每个章节的每个段落添加单独的消息
      for (const chapter of selectedChapters) {
        const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content}`;
        const segments = this.textProcessingService.segmentText(chapterText);
        const chapterTitle = chapter.title || `第${chapter.chapterNumber || '?'}章`;

        for (let i = 0; i < segments.length; i++) {
          globalSegmentIndex++;
          const segment = segments[i];
          const segmentWordCount = this.textProcessingService.calculateActualWordCount(segment);

          // 按照要求的格式构建消息：第x章，第几段，多少字，内容
          const segmentPrompt = `${chapterTitle}，第${i+1}/${segments.length}段，${segmentWordCount}字，内容：\n\n${segment}`;
          messageBuilder.addUserMessage(segmentPrompt);

          // 每个段落都添加一个确认消息
          const confirmationMessage = `我已阅读并分析了"${chapterTitle}"章节的第${i+1}段内容（总进度：${globalSegmentIndex}/${totalSegmentsCount}）。`;
          messageBuilder.addAssistantMessage(confirmationMessage);
        }
      }
    }
  }

  /**
   * 添加选中的人物
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  public addSelectedCharacters(messageBuilder: MessageBuilder, params: RewriteContentParams): void {
    const { characters, selectedCharacterIds } = params;
    
    if (!characters || !selectedCharacterIds || selectedCharacterIds.length === 0) {
      return;
    }

    // 获取选中的人物
    const selectedCharacters = characters.filter(character => 
      selectedCharacterIds.includes(character.id || '')
    );

    if (selectedCharacters.length > 0) {
      // 添加一条说明消息
      messageBuilder.addAssistantMessage(`我将参考以下人物信息，确保人物形象和行为的一致性：`);

      // 为每个人物添加单独的消息
      for (const character of selectedCharacters) {
        const characterInfo = `【人物名称】${character.name || '未命名'}\n\n【人物描述】\n${character.description || '无描述'}\n\n【性格特点】\n${character.personality || '无特定性格'}\n\n【外貌特征】\n${character.appearance || '无特定外貌'}\n\n【背景故事】\n${character.background || '无背景故事'}`;
        messageBuilder.addUserMessage(characterInfo);
        messageBuilder.addAssistantMessage(`我已记住"${character.name || '未命名'}"的人物信息，将确保在创作中保持人物形象和行为的一致性。`);
      }
    }
  }

  /**
   * 添加选中的世界观
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  public addSelectedWorldBuildings(messageBuilder: MessageBuilder, params: RewriteContentParams): void {
    const { worldBuildings, selectedWorldBuildingIds } = params;
    
    if (!worldBuildings || !selectedWorldBuildingIds || selectedWorldBuildingIds.length === 0) {
      return;
    }

    // 获取选中的世界观
    const selectedWorldBuildingItems = worldBuildings.filter(worldBuilding => 
      selectedWorldBuildingIds.includes(worldBuilding.id || '')
    );

    if (selectedWorldBuildingItems.length > 0) {
      // 添加一条说明消息
      messageBuilder.addAssistantMessage(`我将参考以下世界观设定，确保创作内容符合世界观规则：`);

      // 为每个世界观添加单独的消息
      for (const worldBuilding of selectedWorldBuildingItems) {
        const worldBuildingInfo = `【世界观名称】${worldBuilding.name || '未命名'}\n\n【世界观描述】\n${worldBuilding.description || '无描述'}\n\n【规则与法则】\n${worldBuilding.rules || '无特定规则'}\n\n【历史背景】\n${worldBuilding.history || '无历史背景'}\n\n【地理环境】\n${worldBuilding.geography || '无地理环境描述'}\n\n【文化与社会】\n${worldBuilding.culture || '无文化与社会描述'}`;
        messageBuilder.addUserMessage(worldBuildingInfo);
        messageBuilder.addAssistantMessage(`我已记住"${worldBuilding.name || '未命名'}"的世界观设定，将确保创作内容符合这些规则和设定。`);
      }
    }
  }

  /**
   * 添加选中的术语
   * @param messageBuilder 消息构建器
   * @param params 改写参数
   */
  public addSelectedTerminologies(messageBuilder: MessageBuilder, params: RewriteContentParams): void {
    const { terminologies, selectedTerminologyIds } = params;
    
    if (!terminologies || !selectedTerminologyIds || selectedTerminologyIds.length === 0) {
      return;
    }

    // 获取选中的术语
    const selectedTerminologyItems = terminologies.filter(terminology => 
      selectedTerminologyIds.includes(terminology.id || '')
    );

    if (selectedTerminologyItems.length > 0) {
      // 添加一条说明消息
      messageBuilder.addAssistantMessage(`我将参考以下术语定义，确保术语使用的准确性和一致性：`);

      // 为每个术语添加单独的消息
      for (const terminology of selectedTerminologyItems) {
        const terminologyInfo = `【术语名称】${terminology.name || '未命名'}\n\n【术语定义】\n${terminology.definition || '无定义'}\n\n【使用场景】\n${terminology.usage || '无特定使用场景'}\n\n【相关术语】\n${terminology.relatedTerms || '无相关术语'}`;
        messageBuilder.addUserMessage(terminologyInfo);
        messageBuilder.addAssistantMessage(`我已记住"${terminology.name || '未命名'}"的术语定义，将在创作中正确使用这些术语。`);
      }
    }
  }
}

// 创建内容上下文服务的工厂函数
export function createContentContextService(
  textProcessingService?: TextProcessingServiceInterface
): ContentContextServiceInterface {
  return new ContentContextService(textProcessingService);
}
