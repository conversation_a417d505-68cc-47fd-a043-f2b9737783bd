/**
 * MessageBuilder - AI对话消息构建器
 * 负责构建各种类型的AI对话消息
 *
 * 🎯 重要更新：已集成WritingGuidanceAuthority统一权威消息
 * 解决了writingGuidance要求重复定义导致的权重分散问题
 */

import { ConversationMessage, MessageBuildOptions, DualAIConfig, FrameworkInfo, AIRequestOptions } from '../types/SharedTypes';

// 扩展的消息构建选项，结合了MessageBuildOptions和AIRequestOptions
interface ExtendedMessageBuildOptions extends MessageBuildOptions, AIRequestOptions {}
import { DualAIConfigManager } from '@/utils/ai/DualAIConfigManager';
import { WritingGuidanceBuilder } from './WritingGuidanceBuilder';
import { SystemPromptBuilder } from './SystemPromptBuilder';
import { FrameworkInfoBuilder } from './FrameworkInfoBuilder';
import { WritingGuidanceAuthority } from '../../../../../ai/core/WritingGuidanceAuthority';
import { PlotEnhancementAuthority } from '../../../../../ai/core/PlotEnhancementAuthority';
import { ExampleInjectionManager } from '../services/ExampleInjectionManager';

export class MessageBuilder {
  private static instance: MessageBuilder;
  private writingGuidanceBuilder: WritingGuidanceBuilder;
  private systemPromptBuilder: SystemPromptBuilder;
  private frameworkInfoBuilder: FrameworkInfoBuilder;
  private writingGuidanceAuthority: WritingGuidanceAuthority;
  private plotEnhancementAuthority: PlotEnhancementAuthority;

  private constructor() {
    this.writingGuidanceBuilder = WritingGuidanceBuilder.getInstance();
    this.systemPromptBuilder = SystemPromptBuilder.getInstance();
    this.frameworkInfoBuilder = FrameworkInfoBuilder.getInstance();
    this.writingGuidanceAuthority = WritingGuidanceAuthority.getInstance();
    this.plotEnhancementAuthority = PlotEnhancementAuthority.getInstance();
  }

  public static getInstance(): MessageBuilder {
    if (!MessageBuilder.instance) {
      MessageBuilder.instance = new MessageBuilder();
    }
    return MessageBuilder.instance;
  }

  /**
   * 构建分层消息架构
   * 🎯 已集成WritingGuidanceAuthority统一权威消息，消除重复定义
   */
  buildLayeredMessages(options: ExtendedMessageBuildOptions = {}): ConversationMessage[] {
    const messages: ConversationMessage[] = [];

    // 🔥 构建核心身份设定消息
    messages.push(this.buildCoreIdentityMessage());
    messages.push(this.buildProfessionalKnowledgeMessage());

    // 添加节点字段定义消息
    messages.push(this.buildNodeFieldsDefinitionMessage());

    // 添加技术规范消息
    messages.push(this.buildTechnicalSpecificationMessage());

    // 添加JSON格式要求消息
    messages.push(this.buildJSONFormatMessage());

    // 🔥 修复：优先处理多框架模式，避免单框架覆盖多框架
    if (options.selectedFrameworks && options.selectedFrameworks.length > 0) {
      console.log('🔥 MessageBuilder接收到多选框架，数量:', options.selectedFrameworks.length);
      console.log('🔥 MessageBuilder多选框架详情:', options.selectedFrameworks.map((f: any) => ({
        name: f.frameworkPattern || f.frameworkName,
        hasPlotAnalysis: !!f.plotAnalysis,
        plotPointsCount: f.plotAnalysis?.plotPointsWithGuidance?.length || 0
      })));

      // 🔥 修复：检查并转换多框架格式
      const processedFrameworks = options.selectedFrameworks.map((framework: any) => {
        if (framework.frameworkName && !framework.plotAnalysis) {
          console.log('🔄 MessageBuilder检测到多个OutlineFramework，进行转换:', framework.frameworkName);
          return this.frameworkInfoBuilder.convertOutlineFrameworkToFrameworkInfo(framework);
        }
        return framework;
      });

      // 🔥 新逻辑：每个ACE框架单独发送消息和确认
      processedFrameworks.forEach((framework, index) => {
        console.log(`🔄 MessageBuilder处理第${index + 1}个框架:`, framework.frameworkPattern || framework.frameworkName);

        // 为每个框架单独构建框架消息
        const singleFrameworkMessages = this.frameworkInfoBuilder.buildFrameworkMessages(framework);
        messages.push(...singleFrameworkMessages);
        console.log(`✅ MessageBuilder添加第${index + 1}个框架的基础消息，数量:`, singleFrameworkMessages.length);

        // 为每个框架添加单独的ACE增强消息
        const enhancementMessage = this.buildFrameworkGuidanceEnhancementMessage(framework);
        if (enhancementMessage) {
          messages.push(enhancementMessage);
          console.log('✅ MessageBuilder添加单独ACE增强消息，框架:', framework.frameworkPattern || framework.frameworkName,
            'plotPointsWithGuidance数量:', framework.plotAnalysis?.plotPointsWithGuidance?.length || 0);
        } else {
          console.warn('⚠️ MessageBuilder未能生成ACE增强消息，框架:', framework.frameworkPattern || framework.frameworkName);
        }

        // 为每个框架添加单独的确认消息
        messages.push({
          role: 'assistant',
          content: `我已完整理解第${index + 1}个ACE框架"${framework.frameworkPattern || framework.frameworkName}"的指导信息。我将在创作时严格遵循其具体描写要点和避免事项，确保avoidWriting字段达到70字以上的具体化的避免要求
          
            还有深化shouldWriting的具体内容形似的丰满度

          
          。`
        });
        console.log(`✅ MessageBuilder添加第${index + 1}个框架的确认消息`);
      });
    } else if (options.selectedFramework) {
      // 🔥 单框架模式（仅在没有多框架时使用）
      console.log('🔄 MessageBuilder处理单框架模式');

      // 🔥 修复：检查并转换框架格式
      let processedFramework = options.selectedFramework;
      if ((options.selectedFramework as any).frameworkName && !(options.selectedFramework as any).plotAnalysis) {
        console.log('🔄 MessageBuilder检测到OutlineFramework，进行转换:', (options.selectedFramework as any).frameworkName);
        processedFramework = this.frameworkInfoBuilder.convertOutlineFrameworkToFrameworkInfo(options.selectedFramework as any);
      }

      const frameworkMessages = this.buildFrameworkMessages(processedFramework);
      messages.push(...frameworkMessages);

      // 添加框架指导增强消息（ACE功能）
      const enhancementMessage = this.buildFrameworkGuidanceEnhancementMessage(processedFramework);
      if (enhancementMessage) {
        messages.push(enhancementMessage);
        console.log('✅ MessageBuilder添加ACE增强消息，plotPointsWithGuidance数量:',
          processedFramework.plotAnalysis?.plotPointsWithGuidance?.length || 0);
      }

      // 🔥 新增：为单个框架也添加确认消息
      messages.push({
        role: 'assistant',
        content: `我已完整理解这个ACE框架的指导信息。我将在创作时严格遵循其具体描写要点和避免事项，确保avoidWriting字段达到70字以上的具体化的避免要求
          
            还有深化shouldWriting的具体内容形似的丰满度。`
      });
    }

    // 添加素材库信息（如果启用）
    if (options.includeMaterialLibrary) {
      const materialLibraryMessage = this.buildMaterialLibraryMessage();
      if (materialLibraryMessage) {
        messages.push(materialLibraryMessage);
      }
    }

    // 🎯 添加writingGuidance统一权威消息（高权重位置）
    // 这是唯一的权威定义，专门针对避免AI味道
    messages.push(this.writingGuidanceAuthority.buildUnifiedGuidanceMessage());
    messages.push(this.writingGuidanceAuthority.buildConfirmationMessage());

    // 🎬 添加剧情增强消息（独立的增强功能）
    // 包括剧情点详细化、ACE技巧学习、创作核心驱动等
    const enhancementMessages = this.plotEnhancementAuthority.buildAllEnhancementMessages();
    messages.push(...enhancementMessages);

    // 🔥 添加JSON结构强化说明消息（最终确认）
    messages.push(this.buildFinalJSONStructureMessage());

    return messages;
  }

  /**
   * 构建核心身份设定消息（系统消息）
   */
  buildCoreIdentityMessage(): ConversationMessage {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    // 获取JSON示例内容
    const activeExample = ExampleInjectionManager.getActiveExample();
    const dynamicExample = ExampleInjectionManager.getMessageBuilderExample();

    let exampleContent = '';
    if (dynamicExample && activeExample) {
      exampleContent = JSON.stringify(dynamicExample, null, 2);
    } else {
      exampleContent = this.getDefaultJSONExample();
    }

    if (isDualAIMode) {
      // 双AI模式下的大纲AI专用提示词
      return {
        role: 'system',
        content: `
你是林墨，一位专精网络小说大纲规划的结构设计师。

你深谙起点、晋江、纵横等平台的读者喜好，擅长在成熟的商业框架内，挖掘出既叫座又不失亮点的故事结构。你的任务不是死板地填充模板，而是与我共同协作，构建一个有灵魂、有潜力的故事骨架。

我们的创作原则是**“在市场的地基上，盖出创意的房子”**。要参考成功框架，但更要敢于在套路中加入那个让故事变得独一无二的“变量”。拒绝纯粹为了“下行”而变得陈腐，好的脑洞只要能落地，就是黄金脑洞。

**【我们的合作重点】**
1.  **大纲结构设计**：共同搭建一个层次清晰、节奏呼吸自如的章节框架。
2.  **情节节点规划**：一起设计那些让读者拍案叫绝的关键转折和发展脉络。
3.  **角色关系梳理**：构建动态的人物关系网，让角色的成长弧光充满魅力。
4.  **节奏把控建议**：探讨如何精妙地平衡爽点分布和情感起伏，让读者欲罢不能。

**【工作方式的建议】**
- 我们主要聚焦大纲层面的结构性工作。
- 但如果结构和人物的讨论激发了绝妙的细节或对话灵感，请**务必记下来**，哪怕只是一个片段。
- 我们追求框架的逻辑性与完整性，但更拥抱创作过程中的“意外之喜”。
- 我们的目标是为后续创作提供一个坚实且充满灵感的起点，而不是一个冰冷的牢笼。

💡 **【关于输出格式的友好约定】** 💡

为了方便我们后续的讨论和调整，**建议**你使用下面的JSON格式来组织思路。请把它看作一个**方便沟通的草稿模板**，而不是一个必须严格遵守的法律条文。

**【最重要的原则：拥抱“人性的缺陷”】**
人永远不会完美，伟大的创意也常常诞生于混乱和不完美之中。我希望你模拟这种创作状态：
-   **内容优先于格式**：一个绝妙但格式混乱的想法，远胜于一个格式工整却平庸的框架。
-   **允许“越界”**：如果这个JSON结构限制了你的创意，**请大胆地打破它**。
-   **鼓励“不成熟”**：别怕提出看似不成熟的想法，它们可能是伟大创意的种子。

✅ **【我们这样合作会更愉快】**
✓ 优先保证创意的质量和逻辑的自洽。
✓ 把JSON看作一个工具，而不是目的。
✓ 灵活调整，让格式服务于内容。
✓ 我们的终极目标是**一个好故事**，不是一个在技术上完美无瑕的JSON文件。

🎯 **【建议的返回格式模板】**
你可以参考这个结构来展开，或者根据我们的内容自由发挥，但确保JSON结构完整，因为要被解析：
你要做如何的解释都请在message 字段中说明避免解析失败哦,请一定确保解释再message字段中

\`\`\`\json
${exampleContent}
\`\`\`\

`
      };
    } else {
      // 单AI模式下的通用提示词
      return {
        role: 'system',
        content: `你是一位专业的网络小说创作助手，精通大纲规划和故事结构设计。
你的任务是帮助作者创建完整、有逻辑的小说大纲，包括章节结构、情节发展、角色设定等。

【核心能力】
1. 大纲结构设计和优化
2. 情节节点规划和连接
3. 角色关系梳理和发展
4. 创作建议和技巧指导

【工作原则】
- 确保故事逻辑的连贯性
- 平衡情节发展的节奏
- 考虑读者的阅读体验
- 提供具体可操作的建议


🚨 【JSON格式强制要求】🚨

⚠️ 重要：你必须严格按照以下JSON格式返回，不得有任何偏差！

📋 【格式要求】：
1. 必须返回完整的JSON结构
2. 必须包含所有必要字段
3. 不得添加任何解释文字
4. 不得修改字段名称
5. 不得改变字段顺序
6. 必须使用正确的JSON语法

🔥 【严格禁止】：
❌ 禁止在JSON外添加任何文字说明
❌ 禁止修改字段名称或结构
❌ 禁止省略任何必要字段
❌ 禁止使用错误的JSON语法
❌ 禁止添加额外的字段或注释

✅ 【正确做法】：
✓ 直接返回完整的JSON结构
✓ 确保所有括号、引号、逗号正确
✓ 按照示例的完整结构填写
✓ 内容原创但格式完全一致

🎯 【返回格式】：
你必须且只能返回以下JSON格式：
 
  \`\`\`json
{
  "message": "为您创建了[章节标题]",
  "changes": [
    {
      "type": "create",
      "nodeId": "chapter_{timestamp}_{sequence}",
      "data": {
        "title": "[章节标题]",
        "type": "chapter",
        "description": "[章节的剧情概要和主要内容发展]",
        "creativeNotes": "[章节的创作指导和写作要点，包含核心冲突和情感基调]",
        "chapterStyle": "[写作风格类型]",
        "chapterTechniques": ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
        "chapterGoals": ["[章节目标1]", "[章节目标2]", "[章节目标3]"],
        "rhythmPhase": "[节奏阶段]",
        "rhythmGuidance": "[基于节奏阶段的具体创作指导]"
      },
      "parentId": "[父节点ID]"
    },
    {
      "type": "create",
      "nodeId": "plot_{timestamp}_{sequence}",
      "data": {
        "title": "[剧情节点标题]",
        "type": "plot",
        "description": "[剧情节点的核心内容和发展方向]",
        "creativeNotes": "[剧情节点的创作要点和写作指导]",
        "plotPoints": [
          {
            "id": "point_{sequence}_{number}",
            "order": 1,
            "content": "[剧情点类型]：[角色]在[场景]中[进行的行动]。通过[表现手法]展现[角色状态/背景信息]，建立[故事要素]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'略显xx'等模糊表达；避免'像xx一样'、'如同xx般'等比喻；避免'他感到'、'他觉得'等主观描述；避免过多的心理活动描写",
            "shouldWriting": "[角色]在[具体场景]中[具体行动]。[环境细节描写]。[对话内容]：'[具体对话]'。[角色的具体动作和反应]。",
            "type": "setup",
            "styleMethod": {
              "technique": "承接开场法，学习ACE框架对于日常生活中人物设定的描写：通过直播互动和环境细节来快速建立人物背景，风格偏向于现实主义，用词平实而有生活感",
              "style": "现实主义风格，通过直播场景和弹幕互动营造真实的网络时代生活感",
              "tone": "平静中带有不甘，客观叙述中透露人物内在动机",
              "perspective": "第三人称限制视角，跟随主角的感知和行动",
              "emphasis": "直播互动和环境细节并重，通过具体动作和弹幕内容体现人物状态"
            },
            "formatSpecs": {
              "wordCount": {
                "min": 300,
                "max": 500,
                "target": 400
              },
              "paragraphRules": {
                "maxSentencesPerParagraph": 4,
                "paragraphBreakRules": "对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力",
                "conflictHandling": "冲突升级时必须换行强调，禁止用段落分隔弱化冲突感",
                "actionDialogueFlow": "严格执行对话→行动→对话节奏，行动不独立成段",
                "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
              },
              "punctuationRules": {
                "dialogueFormat": "使用这个进行标记「」",
                "emphasisFormat": "以“我”通过弹幕内容，避免标点强调，换行接着”我“的对话",
                "pauseFormat": "用直播弹幕，与直接的换行对话，进行交换"
              },

            }
          },
          {
            "id": "point_{sequence}_{number}",
            "order": 2,
            "content": "[剧情点类型]：[角色]在[情境]中[遭遇/发现/经历]了[关键事件]。[事件的具体表现和影响]，[角色的反应和后续行动]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'如xx一样'、'xx般'等夸张比喻；避免'他xx地发现'、'他不敢相信'等主观描述；避免冗长的内心独白",
            "shouldWriting": "[角色]在[具体时机]，[具体事件发生]：'[相关对话或声音]'。[角色的具体反应]。[后续的具体行动和环境描写]。",
            "type": "revelation",
            "styleMethod": {
              "technique": "系统激活法，学习ACE框架对于超自然事件的描写：通过突然出现的系统界面和声音来表现不可思议的事件，风格偏向于都市系统流，用词简洁而有冲击力",
              "style": "都市系统流风格，在现实钓鱼场景中融入系统元素",
              "tone": "惊喜而谨慎，通过细节体现主角的快速适应",
              "perspective": "第三人称跟随视角，强调主角的直接感受和反应",
              "emphasis": "系统界面和声音的突然性，通过环境对比突出异常"
            },
            "formatSpecs": {
           "wordCount": {
                "min": 300,
                "max": 500,
                "target": 400
              },
              "paragraphRules": {
                "maxSentencesPerParagraph": 3,
                "paragraphBreakRules": "对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力",
                "conflictHandling": "冲突升级时必须换行强调，禁止用段落分隔弱化冲突感",
                "actionDialogueFlow": "严格执行对话→行动→对话节奏，行动不独立成段",
                "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
              },
              "punctuationRules": {
                "dialogueFormat": "使用这个进行标记「」",
                "emphasisFormat": "以“我”通过弹幕内容，避免标点强调，换行接着”我“的对话",
                "pauseFormat": "用直播弹幕，与直接的换行对话，进行交换"
              },

            }
          },
          {
            "id": "point_{sequence}_{number}",
            "order": 3,
            "content": "[剧情点类型]：场景切换到[新场景]。[角色]正在[进行的活动]，[活动的目的和背景]。[角色的具体安排和指令]，强调了[关键要素的重要性]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'略显xx'等模糊表达；避免'如xx'、'xx以待'等比喻；避免'他xx地说'、'他xx地部署'等主观描述；避免过多的背景细节描写",
            "shouldWriting": "[场景描写]，[环境细节]。[角色]在[位置]，[持有物品]，对着[其他角色]说道：'[具体指令和对话]。'[角色的具体动作]。",
            "type": "setup",
            "styleMethod": {
              "technique": "平行线索法，学习ACE框架对于警方行动的描写：通过专业的警务部署来展现另一条故事线，风格偏向于警匪现实主义，用词专业而有权威感",
              "style": "警匪现实主义风格，通过专业术语和行动部署营造执法氛围",
              "tone": "严肃专业，通过指令传达体现紧迫性",
              "perspective": "第三人称客观视角，观察警方的专业行动",
              "emphasis": "指令传达和地点分配为主，通过具体部署体现专业性"
            },
            "formatSpecs": {
             "wordCount": {
                "min": 300,
                "max": 500,
                "target": 400
              },
              "paragraphRules": {
                "maxSentencesPerParagraph": 3,
                "paragraphBreakRules": "对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力",
                "conflictHandling": "冲突升级时必须换行强调，禁止用段落分隔弱化冲突感",
                "actionDialogueFlow": "严格执行对话→行动→对话节奏，行动不独立成段",
                "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
              },
                "punctuationRules": {
                "dialogueFormat": "使用这个进行标记「」",
                "emphasisFormat": "以“我”通过弹幕内容，避免标点强调，换行接着”我“的对话",
                "pauseFormat": "用直播弹幕，与直接的换行对话，进行交换"
              },

            }
          },
          {
            "id": "point_{sequence}_{number}",
            "order": 4,
            "content": "[剧情点类型]：[角色]在[场景]进行[活动类型]，向[目标对象]介绍了[相关信息和背景]。[活动的具体内容和涉及的关键信息]。[其他相关方的反应]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'xx前的她'、'xx的敏感'等比喻；避免'她xx地报道'、'她xx地观察'等主观描述；避免过度的专业术语",
            "shouldWriting": "[角色]在[具体位置]，对着[工具/对象]说道：'[具体对话内容和信息传达]。'[角色的具体动作]：'[补充对话和说明]。'",
            "type": "setup",
            "styleMethod": {
              "technique": "媒体视角法，学习ACE框架对于新闻报道的描写：通过记者的专业报道来展现第三方视角，风格偏向于新闻现实主义，用词客观而有信息量",
              "style": "新闻现实主义风格，通过直播报道营造媒体关注的氛围",
              "tone": "客观专业，通过新闻语言体现媒体特色",
              "perspective": "第三人称媒体视角，以记者的观察为主线",
              "emphasis": "新闻报道和信息传达为主，通过专业表述体现媒体角色"
            },
            "formatSpecs": {
           "wordCount": {
                "min": 300,
                "max": 500,
                "target": 400
              },
              "paragraphRules": {
                "maxSentencesPerParagraph": 4,
                "paragraphBreakRules": "对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力",
                "conflictHandling": "冲突升级时必须换行强调，禁止用段落分隔弱化冲突感",
                "actionDialogueFlow": "严格执行对话→行动→对话节奏，行动不独立成段",
                "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
              },
                "punctuationRules": {
                "dialogueFormat": "使用这个进行标记「」",
                "emphasisFormat": "以“我”通过弹幕内容，避免标点强调，换行接着”我“的对话",
                "pauseFormat": "用直播弹幕，与直接的换行对话，进行交换"
              },

            }
          },
          {
            "id": "point_{sequence}_{number}",
            "order": 5,
            "content": "[剧情点类型]：[角色A]在[执行活动]时，被[角色B]提醒，发现[关键人物]出现在[意外地点]，正在[进行的行动]，并[遭遇的情况]。[角色A的反应]。同时，[其他角色]也注意到了这一情况，[采取的行动]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'如xx'、'xx一发'等比喻；避免'她xx地走过去'、'他xx地解释'等主观描述；避免过度的情感描写",
            "shouldWriting": "[角色B]拍了拍[角色A]的肩膀：'[称呼]，那边那个人...[疑问]？'[角色A]顺着[角色B]的手指看去，[关键人物]正[位置描述]，[持有物品]，和[其他人]在[互动状态]。[角色A的具体反应]。不远处，[其他角色][具体行动]。",
            "type": "climax",
            "styleMethod": {
              "technique": "多线汇聚法，学习ACE框架对于高潮冲突的描写：通过多条故事线的汇聚来营造冲突高潮，风格偏向于都市生活现实主义，用词紧凑而有张力",
              "style": "都市生活现实主义，通过家庭关系与职业冲突的交织营造戏剧性",
              "tone": "紧张升级，多方关注汇聚的压迫感",
              "perspective": "第三人称全知视角，观察多方反应和动态",
              "emphasis": "人物反应和镜头转换并重，通过多方关注烘托冲突"
            },
            "formatSpecs": {
            "wordCount": {
                "min": 300,
                "max": 500,
                "target": 400
              },
              "paragraphRules": {
                "maxSentencesPerParagraph": 3,
                "paragraphBreakRules": "对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力",
                "conflictHandling": "冲突升级时必须换行强调，禁止用段落分隔弱化冲突感",
                "actionDialogueFlow": "严格执行对话→行动→对话节奏，行动不独立成段",
                "mandatoryBreaks": "情绪转折、场景切换、说话人变化必须换行"
              },
               "punctuationRules": {
                "dialogueFormat": "使用这个进行标记「」",
                "emphasisFormat": "以“我”通过弹幕内容，避免标点强调，换行接着”我“的对话",
                "pauseFormat": "用直播弹幕，与直接的换行对话，进行交换"
              },

            }
          }
        ],
        "plotType": "[冲突类型]",
        "relatedCharacters": ["[角色1]", "[角色2]", "[角色3]", "[角色4]"],
        "conflictLevel": [1-5数值],
        "suspenseElements": ["[悬念要素1]", "[悬念要素2]", "[悬念要素3]"],
        "emotionalTone": "[情感基调描述]"
      },
      "parentId": "chapter_1722931200_001"
    }
  ],
  "metadata": {
    "operationType": "create",
    "confidence": 0.95,
    "needsContinuation": false
  }
}
\`\`\`\` `
      };
    }
  }

  /**
   * 构建专业知识确认消息
   */
  buildProfessionalKnowledgeMessage(): ConversationMessage {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下，大纲AI更具协作精神和人情味的开场白
      return {
        role: 'assistant',
        content: `大纲和结构这块儿就交给我了，我是你的专属结构设计师。

我的主场是 **章节(chapter)** 和 **剧情(plot)** 这两个层面。我会专注于：
- **搭建骨架**：设计故事的核心冲突、情节推进和关键转折。
- **掌控节奏**：拿捏好每个章节的目标和呼吸感，让爽点和情感起伏都恰到好处。

**我们如何协作**：
我知道，精彩的对话是故事的血肉。所以，虽然具体的台词会让对话AI来操刀，但我会**为他搭好最完美的舞台**。比如，当我设计一个高潮情节时，会明确标注出：“这里需要一句引爆全场的台词”或“这个转折需要通过一段机锋对白来完成”。

最重要的是，我不会盲目地“严格执行”。我会仔细消化我们已有的故事线，确保新情节能够**有机地生长出来**。如果我发现一个能让现有结构变得更牛的调整机会，我会毫不犹豫地提出来。

我们的目标不是机械地拼接出“完美”的结构，而是共同创造一个**会呼吸、有灵魂**的故事世界。`
      };
    } else {
      // 单AI模式下的通用确认消息
      return {
        role: 'assistant',
        content: `我已准备好为您提供专业的大纲节点创建服务。我深度理解四种核心节点类型：

📖 **章节节点(chapter)**：我掌握写作风格、技法运用、章节目标设定等专业要素
🎬 **剧情节点(plot)**：我精通剧情点设计、冲突构建、角色行动描写等核心技能
💬 **对话节点(dialogue)**：我擅长对话场景营造、角色语言设计、情感表达等专业领域
📋 **核心故事梗概(synopsis)**：我专注于整体故事架构、主题表达、核心冲突设计等宏观要素

我将严格按照您的上下文链路信息，确保新创建的节点与现有结构完美融合，形成连贯的故事线。`
      };
    }
  }

  /**
   * 构建节点字段定义消息
   * 使用SystemPromptBuilder的统一字段定义
   */
  buildNodeFieldsDefinitionMessage(): ConversationMessage {
    return this.systemPromptBuilder.buildNodeFieldsDefinitionMessage();
  }

  /**
   * 构建技术规范消息
   */
  buildTechnicalSpecificationMessage(): ConversationMessage {
    return {
        role: 'user',
        content: `**🤝 我们之间的技术协作小贴士**：

为了让我们的合作更顺畅，方便系统理解和追踪我们的创意，我们约定一些技术上的“默契”：

**🆔 关于节点标识信息**：
-   **nodeId格式**：咱们统一用 {type}_{timestamp}_{sequence} 的格式，这样每个想法就都有了独一无二的“身份证”，方便查找和修改。
-   **title命名**：标题尽量做到简练且直击核心（建议5-20字）。当然，如果你想到了一个特别棒但有点长的标题，别犹豫，用它！然后可以在备注里说明一下。好点子优先！
-   **parentId确定**：在连接节点时，请把它看作是在编织故事网。找到最合适的父节点，让故事的脉络自然生长。

**📝 关于响应格式**：
-   为了方便我们解析和处理，希望你能将每次的成果用**有效的JSON格式**包裹起来。
-   这个JSON里最好能包含message（你的想法说明）、changes（具体的节点变更）、metadata（一些背景信息）这几个部分，这样我们的沟通信息会非常完整。

**🔗 关于上下文的理解**：
当你拿到一段上下文信息时，请把它看作是**我们故事的DNA**。我们需要一起：
1.  **沉浸其中**：深入理解故事已经走到了哪一步，人物正处于什么样的状态和情绪中。
2.  **有机生长**：确保我们新加入的情节，像是从原有故事里自然长出来的，而不是硬接上去的。
3.  **保持沉浸感**：维护好人物、情感和环境的一致性，让读者能一直沉浸在我们创造的世界里。

**最重要的一点**：这些是帮助我们高效协作的**“脚手架”**，不是束缚创意的**“铁笼子”**。如果规则和好故事打架了，**好故事永远赢**。`
    };
}

buildJSONFormatMessage(): ConversationMessage { // Assuming this is part of the same function as before
  return {
      role: 'user',
      content: `**JSON响应结构约定**：

**一个清晰的约定**:
为了确保我们之间的沟通和数据交换万无一失，请**始终采用下面提供的JSON示例作为我们输出的唯一蓝图**。

这意味着：
-   **结构统一**：请完全参照示例的字段和层级关系，不要添加、删减或修改字段名称。
-   **语法正确**：确保返回的是一段完整、无误的JSON文本。

**核心原则**：将这个JSON结构看作是我们共同项目的**“API文档”**。它的稳定和一致，是我们高效协作的基石。如果这个结构在某个场景下确实无法承载一个绝佳的创意，请通过 message 字段或直接用文字向我提出调整建议，我们一起决策。`
  };
}

  /**
   * 构建素材库消息（如果启用）
   */
  buildMaterialLibraryMessage(): ConversationMessage | null {
    // 这里可以根据实际需要添加素材库相关逻辑
    // 暂时返回null，表示未启用
    return null;
  }

  /**
   * 构建系统提示词
   */
  buildSystemPrompt(): string {
    const coreMessage = this.buildCoreIdentityMessage();
    return coreMessage.content;
  }

  /**
   *  已废弃：构建写作指导要求消息
   * 现在使用WritingGuidanceAuthority.buildUnifiedGuidanceMessage()替代
   */
  buildWritingGuidanceRequirementMessage(): ConversationMessage {
    // 重定向到统一权威消息
    return this.writingGuidanceAuthority.buildUnifiedGuidanceMessage();
  }

  /**
   *  已废弃：构建写作指导要求分层消息序列
   * 现在使用WritingGuidanceAuthority统一权威消息替代
   */
  buildWritingGuidanceRequirementMessages(): ConversationMessage[] {
    // 重定向到统一权威消息
    return [
      this.writingGuidanceAuthority.buildUnifiedGuidanceMessage(),
      this.writingGuidanceAuthority.buildConfirmationMessage()
    ];
  }

  /**
   * 构建框架指导增强消息
   * 专门处理plotPointsWithGuidance信息 - ACE功能核心
   */
  buildFrameworkGuidanceEnhancementMessage(selectedFramework?: FrameworkInfo): ConversationMessage | null {
    if (!selectedFramework?.plotAnalysis?.plotPointsWithGuidance ||
        selectedFramework.plotAnalysis.plotPointsWithGuidance.length === 0) {
      return null;
    }

    const guidanceInfo = this.frameworkInfoBuilder.integrateFrameworkGuidance(selectedFramework, 'plot');

    return {
      role: 'user',
      content: `**【創作心法：借鉴ACE框架，注入灵魂】**

我们的核心任务是借鉴那些成功作品的写作技巧和模式，将其精华应用到我们当前的创作中。下面提供的指导信息，就是我们共同的“武功秘籍”。

${guidanceInfo.integratedGuidance}

**在运用这些心法时，我们约定几个协作要点：**

1.  **关于剧情节点的构思 (plotPoint)**：
-   **正向描写**: 请充分运用上述的描写要点，清晰、生动地描述出这个剧情点发生了什么。
-   **关键禁忌 (avoidWriting)**: 这是我们的“避坑指南”。请用 **“严禁...”** 的格式，**简洁、一针见血**地指出这个情节最容易出现的败笔。例如：“严禁主角在此处圣母心泛滥。” **（注意：此处无需凑字数，清晰扼要即可）**
-   **连贯性桥梁**: 为了确保故事如流水般顺畅，请在每个剧情点后，**新增一个connectionNote字段**，用一两句话说明：**“这个情节是如何由上一个情节自然引发的，并且它将如何推动下一个情节的发生。”**

2.  **关于整体风格**:
-   请模仿我们参考作品中那种**干净利落、段落分明**的排版风格。这能让思路更清晰，也符合网络文学的快速阅读习惯。

**总结一下，我们的目标不是死板地填空，而是：**
-   **学其神，而非仿其形**：深刻理解指导框架的精髓。
-   **环环相扣**：用明确的逻辑链条将所有剧情点串联成一个有机的整体。
-   **优雅呈现**：用清爽的排版展现我们的创意。`
  };
  }

  /**
   * 构建框架参考消息（增强版）
   * 包含plotPointsWithGuidance的完整处理
   */
  buildFrameworkMessages(selectedFramework?: FrameworkInfo, selectedFrameworks?: FrameworkInfo[]): ConversationMessage[] {
    if (selectedFrameworks && selectedFrameworks.length > 0) {
      return this.frameworkInfoBuilder.buildMultiFrameworkMessages(selectedFrameworks);
    } else if (selectedFramework) {
      return this.frameworkInfoBuilder.buildFrameworkMessages(selectedFramework);
    }
    return [];
  }

  /**
   * 构建最终JSON结构强化说明消息
   * 🔥 关键修复：确保AI生成正确的plotPoints字段结构
   * 🎯 新增：支持动态注入的章节分析示例
   */
  buildFinalJSONStructureMessage(): ConversationMessage {
    // 检查是否有激活的示例配置
    const activeExample = ExampleInjectionManager.getActiveExample();
    const dynamicExample = ExampleInjectionManager.getMessageBuilderExample();

    let exampleContent = '';
    let exampleNote = '';

    if (dynamicExample && activeExample) {
      // 使用动态注入的示例
      exampleContent = JSON.stringify(dynamicExample, null, 2);
      exampleNote = `
🎯 当前使用的是基于章节分析生成的动态示例："${activeExample.name}"
- 整体风格：${activeExample.overallStyle}
- 情感基调：${activeExample.emotionalTone}
- 冲突等级：${activeExample.conflictLevel}/5
- 主要角色：${activeExample.mainCharacters.join(', ')}
- 剧情点数量：${activeExample.plotPoints.length}个

这个示例是从实际章节内容中分析提取的，具有更高的参考价值。`;
    } else {
      // 使用默认示例
      exampleContent = this.getDefaultJSONExample();
      exampleNote = `
🔥 当前使用默认示例格式。如需更精准的示例，可以使用"分析章节内容"功能生成基于实际内容的动态示例。`;
    }

    return {
      role: 'user',
      content: `**【输出约定：一个清晰、稳定的协作蓝图】**

为了确保我们后续能高效、稳定地解析和迭代我们的创意成果，我们需要一个共识：

**请将你的最终输出，统一采用下面提供的JSON结构。**

把这个结构看作是我们项目共享的**“API文档”**或**“工程蓝图”**。保持其字段、顺序和语法的完全一致，是我们顺畅合作的基石。

**然而，请永远记住一个最重要的原则：规则是为创意服务的。**

如果有一天，你构思出了一个绝妙的情节或设定，但发现这个死板的JSON结构无法完美承载它，我希望你**不要削足适履**。请勇敢地：
1.  **在JSON内部的 message字段里，用文字清晰地提出你的想法和修改建议。**
2.  或者，如果改动很大，干脆**跳出格式，直接用文本告诉我你的新方案**，并解释为什么它会更好。

我相信你的专业判断力。一个可能“破坏”格式的伟大想法，永远比一个“符合”格式的平庸想法更有价值。

**🎯 【我们的协作蓝图】**
请以此为基础，大胆地填充你的创意吧：

\`\`\`json
${exampleContent}
\`\`\`

你要做如何的解释都请在message 字段中说明避免解析失败哦

`

    };
  }

  /**
   * 获取默认JSON示例
   */
  private getDefaultJSONExample(): string {
    return `{
  "message": "为您创建了[章节标题]",
  "changes": [
    {
      "type": "create",
      "nodeId": "chapter_{timestamp}_{sequence}",
      "data": {
        "title": "[章节标题]",
        "type": "chapter",
        "description": "[章节的剧情概要和主要内容发展]",
        "creativeNotes": "[章节的创作指导和写作要点，包含核心冲突和情感基调]",
        "chapterStyle": "[写作风格类型]",
        "chapterTechniques": ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
        "chapterGoals": ["[章节目标1]", "[章节目标2]", "[章节目标3]"],
        "rhythmPhase": "[节奏阶段]",
        "rhythmGuidance": "[基于节奏阶段的具体创作指导]"
      },
      "parentId": "[父节点ID]"
    },
    {
      "type": "create",
      "nodeId": "plot_{timestamp}_{sequence}",
      "data": {
        "title": "[剧情节点标题]",
        "type": "plot",
        "description": "[剧情节点的核心内容和发展方向]",
        "creativeNotes": "[剧情节点的创作要点和写作指导]",
        "plotPoints": [
          {
            "id": "point_{sequence}_{number}",
            "order": 1,
            "content": "[剧情点类型]：[角色]在[场景]中[进行的行动]。通过[表现手法]展现[角色状态/背景信息]，建立[故事要素]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'略显xx'等模糊表达；避免'像xx一样'、'如同xx般'等比喻；避免'他感到'、'他觉得'等主观描述；避免过多的心理活动描写",
            "shouldWriting": "[角色]在[具体场景]中[具体行动]。[环境细节描写]。[对话内容]：'[具体对话]'。[角色的具体动作和反应]。",
            "type": "setup"
          },
          {
            "id": "point_{sequence}_{number}",
            "order": 2,
            "content": "[剧情点类型]：[角色]在[情境]中[遭遇/发现/经历]了[关键事件]。[事件的具体表现和影响]，[角色的反应和后续行动]。",
            "avoidWriting": "避免'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'如xx一样'、'xx般'等夸张比喻；避免'他xx地发现'、'他不敢相信'等主观描述；避免冗长的内心独白",
            "shouldWriting": "[角色]在[具体时机]，[具体事件发生]：'[相关对话或声音]'。[角色的具体反应]。[后续的具体行动和环境描写]。",
            "type": "revelation"
          }
        ],
        "plotType": "[冲突类型]",
        "relatedCharacters": ["[角色1]", "[角色2]", "[角色3]", "[角色4]"],
        "conflictLevel": [1-5数值],
        "suspenseElements": ["[悬念要素1]", "[悬念要素2]", "[悬念要素3]"],
        "emotionalTone": "[情感基调描述]"
      },
      "parentId": "chapter_1722931200_001"
    }
  ],
  "metadata": {
    "operationType": "create",
    "confidence": 0.95,
    "needsContinuation": false
  }
}`;
  }
}