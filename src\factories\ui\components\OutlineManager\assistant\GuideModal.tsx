"use client";

import React from 'react';
import { createPortal } from 'react-dom';
import './GuideModal.css';

interface GuideModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenManager?: () => void;
}

/**
 * 个性化引导弹窗组件
 * 替代原生alert，提供更好的用户体验
 */
const GuideModal: React.FC<GuideModalProps> = ({
  isOpen,
  onClose,
  onOpenManager
}) => {
  if (!isOpen) return null;

  const handleOpenManager = () => {
    onClose();
    if (onOpenManager) {
      onOpenManager();
    }
  };

  return createPortal(
    <div className="guide-modal-overlay" onClick={onClose}>
      <div className="guide-modal" onClick={e => e.stopPropagation()}>
        {/* 弹窗头部 */}
        <div className="guide-header">
          <div className="guide-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
              <path d="M12 17h.01"/>
            </svg>
          </div>
          <div className="guide-title">
            <h3>🎯 关联功能使用指南</h3>
            <p>让AI助手更懂你的内容</p>
          </div>
          <button className="guide-close" onClick={onClose}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="guide-content">
          <div className="guide-step">
            <div className="step-number">1</div>
            <div className="step-content">
              <h4>添加关联内容</h4>
              <p>点击"管理选择"按钮，选择章节、人物、术语等内容添加到关联管理中</p>
            </div>
          </div>

          <div className="guide-step">
            <div className="step-number">2</div>
            <div className="step-content">
              <h4>一键@插入</h4>
              <p>添加后，点击@按钮就可以一键插入所有关联内容，无需重复选择</p>
            </div>
          </div>

          <div className="guide-step">
            <div className="step-number">3</div>
            <div className="step-content">
              <h4>持久化保存</h4>
              <p>你的关联选择会自动保存，下次打开AI助手时会自动恢复</p>
            </div>
          </div>

          <div className="guide-benefits">
            <h4>✨ 使用关联功能的好处</h4>
            <ul>
              <li>🚀 提升对话效率，减少重复@选择</li>
              <li>🎯 让AI更好理解你的内容上下文</li>
              <li>💾 选择状态持久保存，一次设置长期使用</li>
              <li>📚 支持批量管理，轻松处理大量内容</li>
            </ul>
          </div>
        </div>

        {/* 弹窗底部 */}
        <div className="guide-footer">
          <button className="guide-button secondary" onClick={onClose}>
            我知道了
          </button>
          <button className="guide-button primary" onClick={handleOpenManager}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2v20M2 12h20"/>
            </svg>
            立即添加关联
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default GuideModal;
