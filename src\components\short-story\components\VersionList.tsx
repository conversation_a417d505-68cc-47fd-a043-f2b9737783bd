"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PersonaVersion } from '../../../types/ai-persona';

interface VersionListProps {
  versions: PersonaVersion[];
  selectedVersion: PersonaVersion | null;
  onVersionSelect: (version: PersonaVersion) => void;
  onCreateVersion: (description?: string) => void;
  onDeleteVersion: (versionId: string) => void;
  onRestoreVersion: (version: PersonaVersion) => void;
  isCreatingVersion: boolean;
}

const VersionList: React.FC<VersionListProps> = ({
  versions,
  selectedVersion,
  onVersionSelect,
  onCreateVersion,
  onDeleteVersion,
  onRestoreVersion,
  isCreatingVersion
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newVersionDescription, setNewVersionDescription] = useState('');

  // 处理创建版本
  const handleCreateSubmit = () => {
    const description = newVersionDescription.trim() || `版本 ${versions.length + 1}`;
    onCreateVersion(description);
    setNewVersionDescription('');
    setShowCreateForm(false);
  };

  // 取消创建
  const handleCreateCancel = () => {
    setNewVersionDescription('');
    setShowCreateForm(false);
  };

  // 格式化日期
  const formatDate = (date?: Date): string => {
    if (!date) return '未知时间';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    const now = new Date();
    const diffMs = now.getTime() - validDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;

    return validDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">版本历史</h3>
          <span className="text-sm text-gray-500">{versions.length}/3</span>
        </div>

        {/* 创建新版本按钮 */}
        {!showCreateForm && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowCreateForm(true)}
            disabled={isCreatingVersion || versions.length >= 3}
            className="w-full px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
          >
            {isCreatingVersion ? '创建中...' : '创建新版本'}
          </motion.button>
        )}

        {/* 创建版本表单 */}
        <AnimatePresence>
          {showCreateForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              <input
                type="text"
                placeholder="版本描述（可选）"
                value={newVersionDescription}
                onChange={(e) => setNewVersionDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                maxLength={50}
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleCreateSubmit}
                  disabled={isCreatingVersion}
                  className="flex-1 px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 transition-colors text-sm"
                >
                  {isCreatingVersion ? '创建中...' : '创建'}
                </button>
                <button
                  onClick={handleCreateCancel}
                  disabled={isCreatingVersion}
                  className="flex-1 px-3 py-1.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                >
                  取消
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 版本列表 */}
      <div className="flex-1 overflow-y-auto">
        {versions.length === 0 ? (
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400">暂无版本历史</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">创建第一个版本开始管理</p>
          </div>
        ) : (
          <div className="p-2">
            <AnimatePresence mode="popLayout">
              {versions.map((version, index) => (
                <motion.div
                  key={version.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.2,
                    delay: index * 0.05
                  }}
                  className={`mb-2 p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                    selectedVersion?.id === version.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  onClick={() => onVersionSelect(version)}
                >
                  {/* 版本头部 */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        版本 {version.version}
                      </span>
                      {index === versions.length - 1 && (
                        <span className="ml-2 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                          最新
                        </span>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-1">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          onRestoreVersion(version);
                        }}
                        className="p-1 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors"
                        title="恢复此版本"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      </motion.button>

                      {versions.length > 1 && (
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteVersion(version.id);
                          }}
                          className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
                          title="删除此版本"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </motion.button>
                      )}
                    </div>
                  </div>

                  {/* 版本描述 */}
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                    {version.description || '无描述'}
                  </p>

                  {/* 创建时间 */}
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{formatDate(version.createdAt)}</span>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* 底部提示 */}
      {versions.length >= 3 && (
        <div className="p-3 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            已达到最大版本数量限制（3个）
          </p>
        </div>
      )}
    </div>
  );
};

export default VersionList;
