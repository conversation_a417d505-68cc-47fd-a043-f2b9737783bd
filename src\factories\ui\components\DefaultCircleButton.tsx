"use client";

import React, { useState, useEffect } from 'react';
import { ICircleButtonComponent, CircleButtonSize } from '../interfaces/ICircleButtonComponent';

/**
 * 默认圆形按钮组件实现
 */
export class DefaultCircleButtonComponent implements ICircleButtonComponent {
  private icon: React.ReactNode = null;
  private text: string = '';
  private color: string = 'var(--color-info)';
  private size: CircleButtonSize = 'medium';
  private disabled: boolean = false;
  private clickHandler: (() => void) | null = null;

  /**
   * 设置按钮图标
   * @param icon 图标JSX元素
   */
  setIcon(icon: React.ReactNode): void {
    this.icon = icon;
  }

  /**
   * 设置按钮文本
   * @param text 按钮文本
   */
  setText(text: string): void {
    this.text = text;
  }

  /**
   * 设置按钮颜色
   * @param color 按钮颜色
   */
  setColor(color: string): void {
    this.color = color;
  }

  /**
   * 设置按钮尺寸
   * @param size 按钮尺寸
   */
  setSize(size: CircleButtonSize): void {
    this.size = size;
  }

  /**
   * 设置按钮是否禁用
   * @param disabled 是否禁用
   */
  setDisabled(disabled: boolean): void {
    this.disabled = disabled;
  }

  /**
   * 设置点击事件处理函数
   * @param handler 点击事件处理函数
   */
  onClick(handler: () => void): void {
    this.clickHandler = handler;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const CircleButton = () => {
      const [icon, setIcon] = useState<React.ReactNode>(this.icon);
      const [text, setText] = useState(this.text);
      const [color, setColor] = useState(this.color);
      const [size, setSize] = useState(this.size);
      const [disabled, setDisabled] = useState(this.disabled);
      const [isHovered, setIsHovered] = useState(false);

      // 监听属性变化
      useEffect(() => {
        setIcon(this.icon);
      }, [this.icon]);

      useEffect(() => {
        setText(this.text);
      }, [this.text]);

      useEffect(() => {
        setColor(this.color);
      }, [this.color]);

      useEffect(() => {
        setSize(this.size);
      }, [this.size]);

      useEffect(() => {
        setDisabled(this.disabled);
      }, [this.disabled]);

      // 处理点击事件
      const handleClick = () => {
        if (!disabled && this.clickHandler) {
          this.clickHandler();
        }
      };

      // 获取按钮尺寸
      const getButtonSize = () => {
        switch (size) {
          case 'small':
            return {
              width: '40px',
              height: '40px',
              fontSize: '0.875rem'
            };
          case 'large':
            return {
              width: '60px',
              height: '60px',
              fontSize: '1.125rem'
            };
          case 'medium':
          default:
            return {
              width: '50px',
              height: '50px',
              fontSize: '1rem'
            };
        }
      };

      const sizeStyles = getButtonSize();

      return (
        <div className="flex flex-col items-center group relative">
          <button
            className="rounded-full flex items-center justify-center shadow-md transition-all duration-300 transform hover:scale-110 active:scale-95"
            style={{
              backgroundColor: color,
              color: 'white',
              ...sizeStyles,
              opacity: disabled ? 0.5 : 1,
              cursor: disabled ? 'not-allowed' : 'pointer',
            }}
            onClick={handleClick}
            disabled={disabled}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {icon}
          </button>
          {text && (
            <div
              className="absolute w-full h-0 flex justify-center items-start pointer-events-none"
              style={{
                bottom: '100%',
                left: 0,
                zIndex: 50
              }}
            >
              <span
                className={`text-xs font-medium text-center transition-all duration-300 bg-gray-800 text-white px-2 py-1 rounded-md shadow-md`}
                style={{
                  minWidth: '60px',
                  whiteSpace: 'nowrap',
                  opacity: isHovered ? 1 : 0,
                  transform: isHovered
                    ? 'translateY(0)'
                    : 'translateY(10px)',
                  marginBottom: isHovered ? '15px' : '5px',
                  transitionProperty: 'opacity, transform, margin-bottom',
                  position: 'relative'
                }}
              >
                {text}
                {/* 添加小三角形指向按钮 */}
                <div
                  className="absolute w-0 h-0 border-l-[5px] border-l-transparent border-r-[5px] border-r-transparent border-t-[5px] border-t-gray-800"
                  style={{
                    bottom: '-5px',
                    left: '50%',
                    transform: 'translateX(-50%)'
                  }}
                ></div>
              </span>
            </div>
          )}
        </div>
      );
    };

    return <CircleButton />;
  }
}
