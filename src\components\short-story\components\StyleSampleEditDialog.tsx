"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';
import { StyleSample } from '../../../services/ai/StyleSampleService';

interface StyleSampleEditDialogProps {
  isOpen: boolean;
  sample: StyleSample | null;
  onClose: () => void;
  onSave: (sampleData: Partial<StyleSample>) => void;
  isCreating?: boolean;
}

/**
 * 风格样本编辑弹窗组件
 * 提供独立的弹窗界面来编辑或创建风格样本
 */
const StyleSampleEditDialog: React.FC<StyleSampleEditDialogProps> = ({
  isOpen,
  sample,
  onClose,
  onSave,
  isCreating = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    content: '',
    description: '',
    tags: [] as string[],
    isActive: true
  });
  const [tagInput, setTagInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (sample) {
      setFormData({
        name: sample.name || '',
        content: sample.content || '',
        description: sample.description || '',
        tags: sample.tags || [],
        isActive: sample.isActive
      });
    } else if (isCreating) {
      setFormData({
        name: '',
        content: '',
        description: '',
        tags: [],
        isActive: true
      });
    }
  }, [sample, isCreating]);

  // 处理保存
  const handleSave = async () => {
    if (!formData.name.trim() || !formData.content.trim()) {
      alert('请填写样本名称和内容');
      return;
    }

    setIsSubmitting(true);
    try {
      onSave(formData);
      onClose();
    } catch (error) {
      console.error('保存风格样本失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setTagInput('');
    onClose();
  };

  // 添加标签
  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  };

  // 移除标签
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[10003] flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <motion.div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleCancel}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />
      
      {/* 弹窗内容 */}
      <motion.div 
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {isCreating ? '添加风格样本' : '编辑风格样本'}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {isCreating ? '创建新的写作风格样本' : '修改风格样本的信息和内容'}
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 编辑表单 */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* 样本名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              样本名称 *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="例如：金庸武侠风格、现代都市言情风格"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
              autoFocus
            />
          </div>

          {/* 样本描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              样本描述（可选）
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="简单描述这个样本的特点"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
            />
          </div>

          {/* 样本内容 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              样本内容 *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="粘贴你想要模仿的写作风格样本，建议500-2000字"
              rows={10}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 resize-none"
            />
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                支持 Ctrl+Enter 快速保存，Esc 取消编辑
              </p>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                <span>{formData.content.length} 字</span>
                {formData.content.length > 500 && (
                  <span className="text-blue-600 ml-2">
                    将分为 {Math.ceil(formData.content.length / 500)} 段注入
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* 风格标签 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              风格标签
            </label>
            <div className="flex flex-wrap gap-2 mb-3">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center space-x-2"
                >
                  <span>{tag}</span>
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
            <div className="flex space-x-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                placeholder="添加标签，如：古风、现代、幽默"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
              />
              <button
                onClick={handleAddTag}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                添加
              </button>
            </div>
          </div>

          {/* 激活状态 */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="isActive" className="text-sm text-gray-700 dark:text-gray-300">
              立即激活（AI会学习这个样本的风格）
            </label>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {sample && (
              <>
                创建时间: {new Date(sample.createdAt).toLocaleString()}
                {sample.updatedAt && (
                  <span className="ml-4">
                    最后更新: {new Date(sample.updatedAt).toLocaleString()}
                  </span>
                )}
              </>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 disabled:opacity-50 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={!formData.name.trim() || !formData.content.trim() || isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isSubmitting && (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
              <span>{isSubmitting ? '保存中...' : (isCreating ? '创建样本' : '保存更改')}</span>
            </button>
          </div>
        </div>
      </motion.div>
    </div>,
    document.body
  );
};

export default StyleSampleEditDialog;
