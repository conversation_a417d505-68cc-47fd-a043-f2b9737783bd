"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { OutlineNodeType } from '../../types/outline';
import ChatInterface from './assistant/ChatInterface';
import NodePreviewOverlay from './assistant/NodePreviewOverlay';
import AssociationPanel from './assistant/AssociationPanel';
import FrameworkSelector from './assistant/FrameworkSelector';
import CompactControlPanel from './assistant/CompactControlPanel';
import { outlineAIService } from './assistant/OutlineAIService';
import { configService } from '@/services/configService';
import { useAssociationState, AssociationItem } from '@/hooks/useAssociationState';
import { thinkingCanvasWorkflow } from '@/services/thinking-canvas/ThinkingCanvasWorkflow';
import { ContextChain } from './assistant/ContextChainService';
import './AssistantDrawer.css';
import './assistant/FrameworkSelector.css';

interface AssistantDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  outline: any; // 大纲数据
  bookId: string; // 书籍ID，用于AI助手功能
  onApplyChanges: (changes: NodeChange[]) => void;
  selectedNodeId?: string | null;
  buttonPosition?: { x: number; y: number }; // 按钮位置，用于动画起点
  onNodeHighlight?: (nodeIds: string[]) => void; // 节点高亮回调
  onPreviewNodesChange?: (previewNodes: OutlineNodeType[]) => void; // 预览节点变化回调
  onSelectedPreviewNodesChange?: (selectedIds: string[]) => void; // 预览节点选择变化回调
  onPreviewNodeToggleFromCanvas?: (nodeId: string, selected: boolean) => void; // 从画布切换预览节点选择状态
  onRegisterToggleFunction?: (toggleFn: (nodeId: string, selected: boolean) => void) => void; // 注册状态切换函数
  onRegisterClearFunction?: (clearFn: () => void) => void; // 注册状态清理函数
  // 🔥 新增：章节选择状态变化回调
  onSelectedChapterIdsChange?: (chapterIds: string[]) => void;
}

interface NodeChange {
  type: 'create' | 'update' | 'delete';
  nodeId: string;
  data?: Partial<OutlineNodeType>;
  parentId?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  mentionedNodes?: string[];
}

/**
 * AI助手抽屉组件
 * 提供与AI对话、节点操作预览和确认功能
 */
const AssistantDrawer: React.FC<AssistantDrawerProps> = ({
  isOpen,
  onClose,
  outline,
  bookId,
  onApplyChanges,
  selectedNodeId,
  buttonPosition,
  onNodeHighlight,
  onPreviewNodesChange,
  onSelectedPreviewNodesChange,
  onPreviewNodeToggleFromCanvas,
  onRegisterToggleFunction,
  onRegisterClearFunction,
  onSelectedChapterIdsChange // 🔥 新增：接收章节选择状态变化回调
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [pendingChanges, setPendingChanges] = useState<NodeChange[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  // 框架选择状态 - 支持多选
  const [selectedFrameworks, setSelectedFrameworks] = useState<any[]>([]);
  const [selectedFramework, setSelectedFramework] = useState<any>(null); // 保持向后兼容
  const [selectedChanges, setSelectedChanges] = useState<string[]>([]);
  const [showNodePreview, setShowNodePreview] = useState(false);
  const [associationItems, setAssociationItems] = useState<AssociationItem[]>([]);
  const [forceOpenAssociationManager, setForceOpenAssociationManager] = useState(false);
  // 素材库状态管理
  const [useMaterialLibrary, setUseMaterialLibrary] = useState<boolean>(() => {
    try {
      const saved = localStorage.getItem('outline-ai-material-library-enabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  });

  // 监控pendingChanges状态变化
  useEffect(() => {
    console.log('📊 pendingChanges状态变化:', {
      length: pendingChanges.length,
      changes: pendingChanges.map(c => ({ nodeId: c.nodeId, type: c.type, title: c.data?.title }))
    });
  }, [pendingChanges]);

  // 监控selectedChanges状态变化
  useEffect(() => {
    console.log('📊 selectedChanges状态变化:', {
      length: selectedChanges.length,
      ids: selectedChanges
    });
  }, [selectedChanges]);

  const drawerRef = useRef<HTMLDivElement>(null);

  // 关联状态管理
  const {
    selectedChapterIds,
    selectedCharacterIds,
    selectedTerminologyIds,
    selectedWorldBuildingIds,
    setSelectedChapterIds,
    setSelectedCharacterIds,
    setSelectedTerminologyIds,
    setSelectedWorldBuildingIds,
    addAssociation,
    removeAssociation,
    clearAllAssociations,
    hasAssociations
  } = useAssociationState(bookId);

  // 🔥 新增：监听章节选择变化，同步到OutlineCanvas
  useEffect(() => {
    if (onSelectedChapterIdsChange) {
      onSelectedChapterIdsChange(selectedChapterIds);
      console.log('📖 AssistantDrawer同步章节选择到OutlineCanvas:', selectedChapterIds);
    }
  }, [selectedChapterIds, onSelectedChapterIdsChange]);

  // 🔥 新增：强制同步机制 - 每次抽屉打开时强制同步一次
  useEffect(() => {
    if (isOpen && onSelectedChapterIdsChange && selectedChapterIds.length > 0) {
      console.log('📖 AssistantDrawer强制同步章节选择（抽屉打开）:', selectedChapterIds);
      onSelectedChapterIdsChange(selectedChapterIds);
    }
  }, [isOpen, onSelectedChapterIdsChange, selectedChapterIds]);

  // 框架选择持久化
  useEffect(() => {
    if (bookId) {
      // 优先加载多选框架
      const savedFrameworks = localStorage.getItem(`outline-ai-frameworks-${bookId}`);
      if (savedFrameworks) {
        try {
          const frameworks = JSON.parse(savedFrameworks);
          setSelectedFrameworks(frameworks);
          setSelectedFramework(frameworks.length > 0 ? frameworks[0] : null);
          console.log('✅ 多选框架已加载:', frameworks.map((f: any) => f.frameworkName).join(', '));
        } catch (error) {
          console.error('加载保存的多选框架失败:', error);
        }
      } else {
        // 回退到单选框架
        const savedFramework = localStorage.getItem(`outline-ai-framework-${bookId}`);
        if (savedFramework) {
          try {
            const framework = JSON.parse(savedFramework);
            setSelectedFramework(framework);
            setSelectedFrameworks([framework]);
            console.log('✅ 单选框架已加载:', framework.frameworkName);
          } catch (error) {
            console.error('加载保存的框架失败:', error);
          }
        }
      }
    }
  }, [bookId]);

  // 保存框架选择 - 向后兼容的单选处理
  const handleFrameworkSelect = useCallback((framework: any) => {
    setSelectedFramework(framework);
    // 单选时同步到多选状态
    if (framework) {
      setSelectedFrameworks([framework]);
    } else {
      setSelectedFrameworks([]);
    }

    if (bookId) {
      if (framework) {
        localStorage.setItem(`outline-ai-framework-${bookId}`, JSON.stringify(framework));
        console.log('✅ 框架选择已保存:', framework.frameworkName);
      } else {
        localStorage.removeItem(`outline-ai-framework-${bookId}`);
        console.log('✅ 框架选择已清除');
      }
    }
  }, [bookId]);

  // 多选框架处理
  const handleFrameworkMultiSelect = useCallback((frameworks: any[]) => {
    setSelectedFrameworks(frameworks);
    // 多选时更新单选状态（取第一个或null）
    setSelectedFramework(frameworks.length > 0 ? frameworks[0] : null);

    if (bookId) {
      if (frameworks.length > 0) {
        localStorage.setItem(`outline-ai-frameworks-${bookId}`, JSON.stringify(frameworks));
        console.log('✅ 多选框架已保存:', frameworks.map(f => f.frameworkName).join(', '));
      } else {
        localStorage.removeItem(`outline-ai-frameworks-${bookId}`);
        console.log('✅ 多选框架已清除');
      }
    }
  }, [bookId]);

  // 加载关联项目的详细信息
  useEffect(() => {
    const loadAssociationItems = async () => {
      if (!bookId) {
        console.log('🔍 跳过关联项目加载：bookId为空');
        return;
      }

      console.log('🔍 开始加载关联项目:', {
        bookId,
        selectedChapterIds,
        selectedCharacterIds,
        selectedTerminologyIds,
        selectedWorldBuildingIds,
        totalSelected: selectedChapterIds.length + selectedCharacterIds.length +
                      selectedTerminologyIds.length + selectedWorldBuildingIds.length
      });

      try {
        const items: AssociationItem[] = [];

        // 加载章节信息
        if (selectedChapterIds.length > 0) {
          console.log('📖 开始加载章节信息:', selectedChapterIds);
          const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
          const { AIAssistantContextType } = await import('@/lib/db/dexie');
          const chapterResults = await aiAssistantDataService.searchMentionItems(
            bookId, '', [AIAssistantContextType.CHAPTER], 100
          );
          console.log('📖 章节查询结果:', chapterResults);
          const selectedChapters = chapterResults.filter(item =>
            selectedChapterIds.includes(item.id)
          );
          console.log('📖 匹配的章节:', selectedChapters);
          items.push(...selectedChapters.map(item => ({
            id: item.id,
            title: item.title,
            type: 'chapter' as const,
            description: item.description,
            metadata: item.metadata
          })));
        }

        // 加载人物信息
        if (selectedCharacterIds.length > 0) {
          const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
          const { AIAssistantContextType } = await import('@/lib/db/dexie');
          const characterResults = await aiAssistantDataService.searchMentionItems(
            bookId, '', [AIAssistantContextType.CHARACTER], 100
          );
          const selectedCharacters = characterResults.filter(item =>
            selectedCharacterIds.includes(item.id)
          );
          items.push(...selectedCharacters.map(item => ({
            id: item.id,
            title: item.title,
            type: 'character' as const,
            description: item.description,
            metadata: item.metadata
          })));
        }

        // 加载术语信息
        if (selectedTerminologyIds.length > 0) {
          const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
          const { AIAssistantContextType } = await import('@/lib/db/dexie');
          const terminologyResults = await aiAssistantDataService.searchMentionItems(
            bookId, '', [AIAssistantContextType.TERMINOLOGY], 100
          );
          const selectedTerminologies = terminologyResults.filter(item =>
            selectedTerminologyIds.includes(item.id)
          );
          items.push(...selectedTerminologies.map(item => ({
            id: item.id,
            title: item.title,
            type: 'terminology' as const,
            description: item.description,
            metadata: item.metadata
          })));
        }

        // 加载世界观信息
        if (selectedWorldBuildingIds.length > 0) {
          const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
          const { AIAssistantContextType } = await import('@/lib/db/dexie');
          const worldBuildingResults = await aiAssistantDataService.searchMentionItems(
            bookId, '', [AIAssistantContextType.WORLD_BUILDING], 100
          );
          const selectedWorldBuildings = worldBuildingResults.filter(item =>
            selectedWorldBuildingIds.includes(item.id)
          );
          items.push(...selectedWorldBuildings.map(item => ({
            id: item.id,
            title: item.title,
            type: 'worldBuilding' as const,
            description: item.description,
            metadata: item.metadata
          })));
        }

        console.log('✅ 关联项目加载完成:', {
          totalItems: items.length,
          chapters: items.filter(item => item.type === 'chapter').length,
          characters: items.filter(item => item.type === 'character').length,
          terminologies: items.filter(item => item.type === 'terminology').length,
          worldBuildings: items.filter(item => item.type === 'worldBuilding').length,
          items: items.map(item => ({ id: item.id, title: item.title, type: item.type }))
        });
        setAssociationItems(items);
      } catch (error) {
        console.error('❌ 关联项目加载失败:', error);
        setAssociationItems([]);
      }
    };

    loadAssociationItems();
  }, [bookId, selectedChapterIds, selectedCharacterIds, selectedTerminologyIds, selectedWorldBuildingIds]);

  // 测试AI连接
  const testAIConnection = useCallback(async () => {
    console.log('开始测试AI连接...');
    try {
      const result = await outlineAIService.testConnection();
      console.log('AI连接测试结果:', result);

      const testMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: result.success
          ? '✅ AI服务连接成功！'
          : `❌ AI服务连接失败: ${result.error}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, testMessage]);
    } catch (error: any) {
      console.error('测试AI连接时出错:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `❌ 测试连接时出错: ${error.message}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  }, []);

  // 处理节点预览选择
  const handleToggleChange = useCallback((nodeId: string, selected: boolean) => {
    setSelectedChanges(prev => {
      const newSelectedChanges = selected
        ? [...prev, nodeId]
        : prev.filter(id => id !== nodeId);

      // 同步到画布预览节点选择状态
      if (onSelectedPreviewNodesChange) {
        onSelectedPreviewNodesChange(newSelectedChanges);
      }

      return newSelectedChanges;
    });
  }, [onSelectedPreviewNodesChange]);

  // 注册状态切换函数到OutlineCanvas
  useEffect(() => {
    if (onRegisterToggleFunction) {
      onRegisterToggleFunction(handleToggleChange);
    }
  }, [onRegisterToggleFunction, handleToggleChange]);

  // 创建状态清理函数
  const handleClearAllState = useCallback(() => {
    console.log('🧹 AI助手状态清理函数被调用');
    console.log('🧹 清理前状态:', {
      pendingChangesLength: pendingChanges.length,
      selectedChangesLength: selectedChanges.length,
      showNodePreview
    });

    // 添加调用栈信息
    console.trace('🧹 状态清理函数调用栈');

    setPendingChanges([]);
    setSelectedChanges([]);
    setShowNodePreview(false);

    // 清理画布上的预览节点
    if (onPreviewNodesChange) {
      onPreviewNodesChange([]);
    }
    if (onSelectedPreviewNodesChange) {
      onSelectedPreviewNodesChange([]);
    }

    console.log('🧹 AI助手状态已清理');
  }, [pendingChanges, selectedChanges, showNodePreview, onPreviewNodesChange, onSelectedPreviewNodesChange]);

  // 注册状态清理函数到OutlineCanvas
  useEffect(() => {
    if (onRegisterClearFunction) {
      onRegisterClearFunction(handleClearAllState);
    }
  }, [onRegisterClearFunction, handleClearAllState]);

  // 处理确认创建节点
  const handleConfirmChanges = useCallback(() => {
    console.log('🚀 开始确认创建节点');
    console.log('📊 当前状态:', {
      pendingChangesLength: pendingChanges.length,
      selectedChangesLength: selectedChanges.length,
      pendingChanges: pendingChanges,
      selectedChanges: selectedChanges
    });

    const changesToApply = pendingChanges.filter(change =>
      selectedChanges.includes(change.nodeId)
    );

    console.log('🎯 要应用的变更:', changesToApply);

    if (changesToApply.length > 0) {
      console.log('📤 调用onApplyChanges，传递变更:', changesToApply);

      if (onApplyChanges) {
        try {
          onApplyChanges(changesToApply);
          console.log('✅ onApplyChanges调用成功');
        } catch (error) {
          console.error('❌ onApplyChanges调用失败:', error);
        }
      } else {
        console.error('❌ onApplyChanges回调函数不存在');
      }

      // 显示成功消息
      const successMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `✨ 已成功创建 ${changesToApply.length} 个节点！`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, successMessage]);
    } else {
      console.warn('⚠️ 没有要应用的变更');
    }

    console.log('🧹 开始清理状态');

    // 清理状态
    setPendingChanges([]);
    setSelectedChanges([]);
    setShowNodePreview(false);

    // 清理画布上的预览节点
    if (onPreviewNodesChange) {
      console.log('🧹 清理画布预览节点');
      onPreviewNodesChange([]);
    }
    if (onSelectedPreviewNodesChange) {
      console.log('🧹 清理选中状态');
      onSelectedPreviewNodesChange([]);
    }

    console.log('✅ 确认创建节点流程完成');
  }, [pendingChanges, selectedChanges, onApplyChanges, onPreviewNodesChange, onSelectedPreviewNodesChange]);

  // 处理取消预览 - 改为收起预览而不是清空
  const handleCancelPreview = useCallback(() => {
    // 只是收起预览界面，保留数据
    setShowNodePreview(false);

    // 显示提示消息，告知用户数据已保留
    const hintMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'assistant',
      content: '💡 预览已收起，节点建议已保留。您可以随时重新查看或修改。',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, hintMessage]);
  }, []);

  // 处理完全清空预览（新增）
  const handleClearPreview = useCallback(() => {
    setPendingChanges([]);
    setSelectedChanges([]);
    setShowNodePreview(false);

    // 清理画布上的预览节点
    if (onPreviewNodesChange) {
      onPreviewNodesChange([]);
    }
    if (onSelectedPreviewNodesChange) {
      onSelectedPreviewNodesChange([]);
    }

    const clearMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'assistant',
      content: '🗑️ 所有预览节点已清空。',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, clearMessage]);
  }, [onPreviewNodesChange, onSelectedPreviewNodesChange]);

  // 处理冲突解决结果
  const handleConflictResolved = useCallback((resolvedChanges: NodeChange[]) => {
    console.log('🔧 冲突解决完成，更新数据:', resolvedChanges);

    // 1. 更新pendingChanges
    setPendingChanges(resolvedChanges);

    // 2. 更新selectedChanges
    setSelectedChanges(resolvedChanges.map(change => change.nodeId));

    // 3. 处理不同类型的节点
    const createNodes = resolvedChanges
      .filter(change => change.type === 'create' && change.data)
      .map(change => {
        const baseNode = {
          id: change.nodeId,
          title: change.data?.title || '新节点',
          description: change.data?.description || '',
          creativeNotes: change.data?.creativeNotes || '',
          type: change.data?.type || 'scene',
          children: [],
          parentId: change.parentId,
          isNew: true // 标记为新创建的节点
        };

        // 根据节点类型添加专门字段
        if (change.data?.type === 'volume') {
          return {
            ...baseNode,
            volumeTheme: change.data?.volumeTheme,
            volumeArc: change.data?.volumeArc,
            chapterCount: change.data?.chapterCount,
            cycleTemplate: change.data?.cycleTemplate,
            targetWordCount: change.data?.targetWordCount,
            rhythmStrategy: change.data?.rhythmStrategy
          } as OutlineNodeType;
        } else if (change.data?.type === 'event') {
          return {
            ...baseNode,
            eventStart: change.data?.eventStart,
            eventEnd: change.data?.eventEnd,
            eventTrigger: change.data?.eventTrigger,
            eventConsequence: change.data?.eventConsequence,
            eventScope: change.data?.eventScope,
            chapterCount: change.data?.chapterCount,
            targetWordCount: change.data?.targetWordCount
          } as OutlineNodeType;
        } else if (change.data?.type === 'chapter') {
          return {
            ...baseNode,
            chapterStyle: change.data?.chapterStyle,
            chapterTechniques: change.data?.chapterTechniques,
            chapterGoals: change.data?.chapterGoals
          } as OutlineNodeType;
        } else if (change.data?.type === 'plot') {
          return {
            ...baseNode,
            plotPoints: change.data?.plotPoints,
            plotType: change.data?.plotType,
            relatedCharacters: change.data?.relatedCharacters
          } as OutlineNodeType;
        } else if (change.data?.type === 'dialogue') {
          return {
            ...baseNode,
            dialogueScene: change.data?.dialogueScene,
            participants: change.data?.participants,
            dialoguePurpose: change.data?.dialoguePurpose,
            dialogueContent: change.data?.dialogueContent
          } as OutlineNodeType;
        }

        return baseNode as OutlineNodeType;
      });

    const updateNodes = resolvedChanges
      .filter(change => change.type === 'update' && change.data)
      .map(change => {
        const baseNode = {
          id: change.nodeId,
          title: change.data?.title || '更新节点',
          description: change.data?.description || '',
          creativeNotes: change.data?.creativeNotes || '',
          type: change.data?.type || 'plot',
          children: [],
          parentId: change.parentId,
          isUpdate: true // 标记为更新的节点
        };

        // 根据节点类型添加专门字段
        if (change.data?.type === 'chapter') {
          return {
            ...baseNode,
            chapterStyle: change.data?.chapterStyle,
            chapterTechniques: change.data?.chapterTechniques,
            chapterGoals: change.data?.chapterGoals
          } as OutlineNodeType;
        } else if (change.data?.type === 'plot') {
          return {
            ...baseNode,
            plotPoints: change.data?.plotPoints,
            plotType: change.data?.plotType,
            relatedCharacters: change.data?.relatedCharacters
          } as OutlineNodeType;
        } else if (change.data?.type === 'dialogue') {
          return {
            ...baseNode,
            dialogueScene: change.data?.dialogueScene,
            participants: change.data?.participants,
            dialoguePurpose: change.data?.dialoguePurpose,
            dialogueContent: change.data?.dialogueContent
          } as OutlineNodeType;
        }

        return baseNode as OutlineNodeType;
      });

    const allPreviewNodes = [...createNodes, ...updateNodes];

    console.log('🎨 生成的预览节点:', allPreviewNodes);

    // 4. 同步到画布
    if (onPreviewNodesChange) {
      console.log('📤 同步预览节点到画布');
      onPreviewNodesChange(allPreviewNodes);
    }

    if (onSelectedPreviewNodesChange) {
      console.log('📤 同步选中状态到画布');
      onSelectedPreviewNodesChange(allPreviewNodes.map(node => node.id));
    }

    // 5. 显示正常预览
    setShowNodePreview(true);

    // 6. 显示成功消息
    const successMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'assistant',
      content: `✅ 冲突解决完成！已处理 ${resolvedChanges.length} 个节点，其中 ${createNodes.length} 个新建，${updateNodes.length} 个更新。`,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, successMessage]);

  }, [onPreviewNodesChange, onSelectedPreviewNodesChange, setMessages]);



  // 处理关联更新
  const handleUpdateAssociations = useCallback((associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    setSelectedChapterIds(associations.chapterIds);
    setSelectedCharacterIds(associations.characterIds);
    setSelectedTerminologyIds(associations.terminologyIds);
    setSelectedWorldBuildingIds(associations.worldBuildingIds);
  }, [setSelectedChapterIds, setSelectedCharacterIds, setSelectedTerminologyIds, setSelectedWorldBuildingIds]);

  // 处理打开关联管理（从ChatInterface调用）
  const handleOpenAssociationManager = useCallback(() => {
    // 直接触发关联管理对话框，而不是下拉展开
    setForceOpenAssociationManager(true);
    // 重置状态，避免重复触发
    setTimeout(() => setForceOpenAssociationManager(false), 100);
  }, []);

  // 处理发送消息
  const handleSendMessage = useCallback(async (content: string, mentionedNodes: string[] = [], contextChains: any[] = []) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date(),
      mentionedNodes
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      console.log('发送消息到AI服务:', { content, mentionedNodes });

      // 高亮提及的节点
      if (mentionedNodes.length > 0 && onNodeHighlight) {
        onNodeHighlight(mentionedNodes);
      }

      // 获取API配置参数
      console.log('获取API配置参数...');
      const aiConfig = await configService.getAIConfig();
      console.log('获取到的API配置:', aiConfig);

      // 使用流式响应实现
      console.log('调用流式AI服务');

      // 创建临时的助手消息用于流式显示
      const tempAssistantId = (Date.now() + 1).toString();
      const tempAssistantMessage: ChatMessage = {
        id: tempAssistantId,
        type: 'assistant',
        content: '',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, tempAssistantMessage]);
      setIsStreaming(true);
      setStreamingMessage('');

      let fullResponse = '';

      // 🔥 调试：检查传递给AI服务的框架参数
      console.log('🔥 AssistantDrawer发送AI请求，框架参数:', {
        selectedFramework: selectedFramework ? {
          name: selectedFramework.frameworkPattern || selectedFramework.frameworkName,
          hasPlotAnalysis: !!selectedFramework.plotAnalysis
        } : null,
        selectedFrameworks: selectedFrameworks.map(f => ({
          name: f.frameworkPattern || f.frameworkName,
          hasPlotAnalysis: !!f.plotAnalysis,
          plotPointsCount: f.plotAnalysis?.plotPointsWithGuidance?.length || 0
        })),
        selectedFrameworksLength: selectedFrameworks.length
      });

      // 调用两阶段流式AI服务：先节奏分析，再生成大纲节点
      const aiResponse = await outlineAIService.sendStreamingRequestWithRhythmAnalysis(
        content,
        mentionedNodes,
        outline,
        (chunk: string) => {
          fullResponse += chunk;
          setStreamingMessage(fullResponse);

          // 实时更新消息内容
          setMessages(prev =>
            prev.map(msg =>
              msg.id === tempAssistantId
                ? { ...msg, content: fullResponse }
                : msg
            )
          );
        },
        {
          bookId: bookId,
          contextChains: contextChains, // 传递上下文链路
          selectedFramework: selectedFramework, // 传递选择的框架（向后兼容）
          selectedFrameworks: selectedFrameworks // 传递多选框架
        }
      );

      setIsStreaming(false);
      console.log('AI流式服务响应完成:', aiResponse);

      // 更新最终消息内容
      setMessages(prev =>
        prev.map(msg =>
          msg.id === tempAssistantId
            ? {
                ...msg,
                content: aiResponse.message
              }
            : msg
        )
      );

      // 如果有变更建议，显示预览
      if (aiResponse.success && aiResponse.changes && aiResponse.changes.length > 0) {
        console.log('🔍 AI响应成功，处理changes:', aiResponse.changes);
        console.log('🔍 changes详细信息:', JSON.stringify(aiResponse.changes, null, 2));

        // 设置待处理的变更
        setPendingChanges(aiResponse.changes);

        // 显示节点预览
        setShowNodePreview(true);

        // 默认选中所有变更
        const changeIds = aiResponse.changes.map(change => change.nodeId);
        setSelectedChanges(changeIds);
        console.log('🔍 设置selectedChanges:', changeIds);

        // 添加延迟检查状态同步
        setTimeout(() => {
          console.log('🔍 延迟检查状态同步:', {
            pendingChangesLength: aiResponse.changes?.length || 0,
            showNodePreview: true,
            selectedChangesLength: changeIds.length
          });
        }, 100);

        // 同步预览节点到画布
        const previewNodes = aiResponse.changes
          .filter(change => change.type === 'create' && change.data)
          .map(change => {
            console.log('🔍 处理change数据:', change);
            const baseNode = {
              id: change.nodeId,
              title: change.data?.title || '新节点',
              description: change.data?.description || '',
              creativeNotes: change.data?.creativeNotes || '',
              type: change.data?.type || 'scene',
              children: [],
              parentId: change.parentId,
              // 🔥 修复：确保所有字段都被正确映射
              ...change.data // 直接展开所有data字段，避免字段丢失
            };

            // 根据节点类型添加专门字段
            if (change.data?.type === 'chapter') {
              return {
                ...baseNode,
                chapterStyle: change.data?.chapterStyle,
                chapterTechniques: change.data?.chapterTechniques,
                chapterGoals: change.data?.chapterGoals
              } as OutlineNodeType;
            } else if (change.data?.type === 'plot') {
              return {
                ...baseNode,
                plotPoints: change.data?.plotPoints,
                plotType: change.data?.plotType,
                relatedCharacters: change.data?.relatedCharacters
              } as OutlineNodeType;
            } else if (change.data?.type === 'dialogue') {
              return {
                ...baseNode,
                dialogueScene: change.data?.dialogueScene,
                participants: change.data?.participants,
                dialoguePurpose: change.data?.dialoguePurpose,
                dialogueContent: change.data?.dialogueContent
              } as OutlineNodeType;
            }

            return baseNode as OutlineNodeType;
          });

        console.log('AI助手生成的预览节点:', previewNodes);

        if (onPreviewNodesChange) {
          console.log('调用onPreviewNodesChange，传递预览节点到画布');
          onPreviewNodesChange(previewNodes);
        }

        // 默认选中所有预览节点 - 使用nodeId而不是changeId
        if (onSelectedPreviewNodesChange) {
          console.log('调用onSelectedPreviewNodesChange，选中所有预览节点');
          onSelectedPreviewNodesChange(previewNodes.map(node => node.id));
        }
      } else {
        // AI响应问题的详细调试信息
        console.warn('🔍 AI响应问题，无法显示节点清单:', {
          success: aiResponse.success,
          hasChanges: !!aiResponse.changes,
          changesLength: aiResponse.changes?.length || 0,
          changesType: typeof aiResponse.changes,
          fullResponse: aiResponse
        });

        // 如果AI响应成功但没有changes，显示提示消息
        if (aiResponse.success && (!aiResponse.changes || aiResponse.changes.length === 0)) {
          const noChangesMessage: ChatMessage = {
            id: (Date.now() + 2).toString(),
            type: 'assistant',
            content: '✅ AI处理完成，但没有生成新的节点建议。可能是因为当前请求不需要创建新节点，或者AI认为现有结构已经足够完善。',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, noChangesMessage]);
        }
      }

      // 如果AI服务返回错误，显示错误信息
      if (!aiResponse.success && aiResponse.error) {
        console.warn('AI服务错误:', aiResponse.error);
      }

    } catch (error) {
      console.error('AI响应失败:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: '抱歉，我遇到了一些问题。请稍后再试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [outline, onNodeHighlight]);

  // 处理思考画布消息发送
  const handleSendThinkingMessage = useCallback(async (
    content: string,
    mentionedNodes: string[] = [],
    contextChains: ContextChain[] = []
  ) => {
    console.log('🎯 发送思考画布消息:', { content, mentionedNodes, contextChains });

    try {
      // 获取API配置参数
      const aiConfig = await configService.getAIConfig();

      // 使用思考画布工作流程生成思考画布
      const thinkingCanvas = await thinkingCanvasWorkflow.generateThinkingCanvas(
        content,
        mentionedNodes,
        outline,
        {
          mode: 'detailed', // 默认使用详细思考模式
          temperature: aiConfig.temperature || 0.7,
          maxTokens: aiConfig.maxTokens || 3000,
          bookId: bookId,
          contextChains: contextChains,
          selectedFramework: selectedFramework,
          selectedFrameworks: selectedFrameworks
        }
      );

      if (thinkingCanvas) {
        console.log('✅ 思考画布生成成功:', thinkingCanvas);

        // 添加用户消息到聊天记录
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'user',
          content,
          timestamp: new Date(),
          mentionedNodes
        };

        // 添加AI思考消息到聊天记录
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: `我已经完成了深度思考，生成了思考画布。您可以查看我的思考过程，并在需要时进行编辑。`,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage, assistantMessage]);

        // 高亮提及的节点
        if (mentionedNodes.length > 0 && onNodeHighlight) {
          onNodeHighlight(mentionedNodes);
        }
      } else {
        console.warn('思考画布生成失败，降级到标准模式');
        // 降级到标准模式
        await handleSendMessage(content, mentionedNodes, contextChains);
      }
    } catch (error) {
      console.error('❌ 思考画布消息处理失败:', error);
      // 降级到标准模式
      await handleSendMessage(content, mentionedNodes, contextChains);
    }
  }, [outline, bookId, selectedFramework, selectedFrameworks, onNodeHighlight, handleSendMessage]);

  // 素材库开关处理函数
  const handleMaterialLibraryToggle = useCallback(() => {
    const newState = !useMaterialLibrary;
    setUseMaterialLibrary(newState);

    // 持久化存储
    try {
      localStorage.setItem('outline-ai-material-library-enabled', JSON.stringify(newState));
    } catch (error) {
      console.error('保存素材库状态失败:', error);
    }

    console.log('🔄 大纲AI素材库状态切换:', newState);
  }, [useMaterialLibrary]);

  // 点击遮罩关闭（仅在移动端覆盖模式下使用）
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget && window.innerWidth <= 768) {
      onClose();
    }
  }, [onClose]);

  // ESC键关闭
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // 处理动画状态
  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
    } else {
      // 延迟隐藏组件，等待动画完成
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 400); // 与动画时长匹配
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // 如果抽屉未打开且不在动画中，则不渲染
  if (!isOpen && !isAnimating) return null;

  // 计算抽屉的动画样式
  const getDrawerStyle = () => {
    if (!buttonPosition) {
      // 如果没有按钮位置，使用默认样式
      return {};
    }

    // 计算相对于抽屉容器的transform-origin
    // 抽屉容器位于屏幕右侧，宽度为480px
    const drawerWidth = 480;
    const originX = buttonPosition.x - (window.innerWidth - drawerWidth);
    const originY = buttonPosition.y;

    // 设置缩放动画的起点为按钮位置
    return {
      transformOrigin: `${originX}px ${originY}px`,
      // 添加额外的缩放动画效果
      transform: isOpen ? 'scale(1)' : 'scale(0.1)',
      opacity: isOpen ? 1 : 0,
      transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease',
    };
  };

  return (
    <div className="assistant-drawer-overlay" onClick={handleOverlayClick}>
      <div
        ref={drawerRef}
        className={`assistant-drawer ${isOpen ? 'open' : ''}`}
        style={getDrawerStyle()}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 抽屉头部 */}
        <div className="assistant-drawer-header">
          <div className="assistant-drawer-title">
            <div className="assistant-avatar">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z"/>
              </svg>
            </div>
            <div>
              <h3>AI助手</h3>
              <p>智能大纲编辑助手</p>
            </div>
          </div>

          <button
            className="assistant-drawer-close"
            onClick={onClose}
            aria-label="关闭助手"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        {/* 抽屉内容 */}
        <div className="assistant-drawer-content">
          {/* 紧凑控制面板 */}
          <CompactControlPanel
            useMaterialLibrary={useMaterialLibrary}
            onMaterialLibraryToggle={handleMaterialLibraryToggle}
            selectedFramework={selectedFramework}
            selectedFrameworks={selectedFrameworks}
            onFrameworkSelect={handleFrameworkSelect}
            onFrameworkMultiSelect={handleFrameworkMultiSelect}
            associationCount={selectedChapterIds.length + selectedCharacterIds.length + selectedTerminologyIds.length + selectedWorldBuildingIds.length}
            onOpenAssociationManager={handleOpenAssociationManager}
          />

          {/* 关联管理面板 - 隐藏状态，通过forceOpenAssociationManager触发弹窗 */}
          <AssociationPanel
            bookId={bookId}
            selectedChapterIds={selectedChapterIds}
            selectedCharacterIds={selectedCharacterIds}
            selectedTerminologyIds={selectedTerminologyIds}
            selectedWorldBuildingIds={selectedWorldBuildingIds}
            onRemoveAssociation={removeAssociation}
            onClearAll={clearAllAssociations}

            onUpdateAssociations={handleUpdateAssociations}
            forceOpenManager={forceOpenAssociationManager}
          />

          {/* 聊天界面容器 */}
          <div className="chat-interface-container">
            <ChatInterface
              messages={messages}
              onSendMessage={handleSendMessage}
              onSendThinkingMessage={handleSendThinkingMessage}
              isLoading={isLoading}
              isStreaming={isStreaming}
              outline={outline}
              bookId={bookId}
              selectedNodeId={selectedNodeId}
              onNodeHighlight={onNodeHighlight}
              onTestConnection={testAIConnection}
              associationItems={associationItems}
              onOpenAssociationManager={handleOpenAssociationManager}
            />
          </div>

          {/* 预览节点提示条 - 当有待处理的节点但预览被收起时显示 */}
          {pendingChanges.length > 0 && !showNodePreview && (
            <div className="preview-hint-bar">
              <div className="hint-content">
                <span className="hint-icon">💡</span>
                <span className="hint-text">
                  有 {pendingChanges.length} 个节点建议待处理
                </span>
              </div>
              <button
                className="show-preview-btn"
                onClick={() => setShowNodePreview(true)}
              >
                查看预览
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 节点预览覆盖层 - 集成到抽屉内部 */}
      {showNodePreview && (
        <div className="node-preview-container">
          <NodePreviewOverlay
            changes={pendingChanges}
            outline={outline}
            onToggleChange={handleToggleChange}
            selectedChanges={selectedChanges}
            onConflictResolved={handleConflictResolved}
          />

          {/* 确认操作面板 */}
          <div className="preview-confirm-panel">
            <div className="confirm-panel-header">
              <h4>📋 节点创建确认</h4>
              <p>已选择 {selectedChanges.length} 个节点</p>
            </div>

            <div className="confirm-panel-actions">
              <div className="action-group">
                <button
                  className="cancel-btn"
                  onClick={handleCancelPreview}
                  title="收起预览，保留节点建议"
                >
                  收起
                </button>
                <button
                  className="clear-btn"
                  onClick={handleClearPreview}
                  title="清空所有预览节点"
                >
                  清空
                </button>
              </div>
              <button
                className="confirm-btn"
                onClick={handleConfirmChanges}
                disabled={selectedChanges.length === 0}
              >
                确认创建 ({selectedChanges.length})
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};



export default AssistantDrawer;
