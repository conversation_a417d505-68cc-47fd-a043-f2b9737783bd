"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessagePart } from '../utils/messageParser';

interface SpecialContentRendererProps {
  part: MessagePart;
  onToggleCollapse?: (partId: string, isCollapsed: boolean) => void;
}

/**
 * 特殊内容渲染器组件
 * 用于渲染 Thinking 标签和 reasoning_content 等特殊内容
 */
const SpecialContentRenderer: React.FC<SpecialContentRendererProps> = ({
  part,
  onToggleCollapse
}) => {
  const [isCollapsed, setIsCollapsed] = useState(part.isCollapsed ?? false);

  const handleToggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    onToggleCollapse?.(part.id || '', newCollapsedState);
  };

  // Thinking 内容渲染
  if (part.type === 'thinking') {
    return (
      <div className="my-4 border border-blue-200 rounded-lg overflow-hidden bg-blue-50/30 backdrop-blur-sm">
        {/* 标题栏 */}
        <div 
          className="flex items-center justify-between p-3 bg-blue-100/50 border-b border-blue-200 cursor-pointer hover:bg-blue-100/70 transition-colors"
          onClick={handleToggleCollapse}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-blue-800">
              {part.title || '思考过程'}
            </span>
            <span className="text-xs text-blue-600 bg-blue-200 px-2 py-1 rounded-full">
              AI推理
            </span>
          </div>
          <motion.div
            animate={{ rotate: isCollapsed ? 0 : 180 }}
            transition={{ duration: 0.2 }}
            className="text-blue-600"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </motion.div>
        </div>

        {/* 内容区域 */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="overflow-hidden"
            >
              <div className="p-4 text-sm text-blue-900 leading-relaxed">
                <div 
                  className="whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: formatThinkingContent(part.content)
                  }}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Reasoning 内容渲染（增强版本）
  if (part.type === 'reasoning') {
    console.log('🔍 SpecialContentRenderer - 渲染 reasoning 内容:', part.content?.length || 0, '字符');

    // 验证内容是否有效
    if (!part.content || !part.content.trim()) {
      console.log('🔍 SpecialContentRenderer - reasoning 内容为空，跳过渲染');
      return null;
    }

    return (
      <div className="my-4 border border-purple-200 rounded-lg overflow-hidden bg-purple-50/30 backdrop-blur-sm">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-3 bg-purple-100/50 border-b border-purple-200">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-purple-800">
              {part.title || '推理内容'}
            </span>
            <span className="text-xs text-purple-600 bg-purple-200 px-2 py-1 rounded-full">
              详细分析
            </span>
            <span className="text-xs text-purple-500 bg-purple-100 px-2 py-1 rounded-full">
              {part.content.length} 字符
            </span>
          </div>
          {/* 添加调试信息按钮（开发环境） */}
          {process.env.NODE_ENV === 'development' && (
            <button
              onClick={() => console.log('🔍 Reasoning 内容详情:', part)}
              className="text-xs text-purple-400 hover:text-purple-600 transition-colors"
              title="查看调试信息"
            >
              🔍
            </button>
          )}
        </div>

        {/* 内容区域 - reasoning_content 默认展开 */}
        <div className="p-4 text-sm text-purple-900 leading-relaxed">
          <div
            className="whitespace-pre-wrap"
            dangerouslySetInnerHTML={{
              __html: formatReasoningContent(part.content)
            }}
          />
        </div>
      </div>
    );
  }

  return null;
};

/**
 * 格式化 Thinking 内容
 */
function formatThinkingContent(content: string): string {
  return content
    // 处理粗体文本
    .replace(/\*\*(.*?)\*\*/g, '<strong class="text-blue-800">$1</strong>')
    // 处理斜体文本
    .replace(/\*(.*?)\*/g, '<em class="text-blue-700">$1</em>')
    // 处理代码片段
    .replace(/`(.*?)`/g, '<code class="bg-blue-200 text-blue-900 px-1 py-0.5 rounded text-xs">$1</code>')
    // 处理换行
    .replace(/\n/g, '<br>')
    // 处理列表项
    .replace(/^- (.*$)/gm, '<div class="flex items-start space-x-2 my-1"><span class="text-blue-500 mt-1">•</span><span>$1</span></div>')
    // 处理数字列表
    .replace(/^(\d+)\. (.*$)/gm, '<div class="flex items-start space-x-2 my-1"><span class="text-blue-500 font-medium">$1.</span><span>$2</span></div>');
}

/**
 * 格式化 Reasoning 内容（增强版本）
 */
function formatReasoningContent(content: string): string {
  if (!content) return '';

  try {
    console.log('🔍 formatReasoningContent - 开始格式化，内容长度:', content.length);

    let formatted = content
      // 处理粗体文本
      .replace(/\*\*(.*?)\*\*/g, '<strong class="text-purple-800 font-semibold">$1</strong>')
      // 处理斜体文本
      .replace(/\*(.*?)\*/g, '<em class="text-purple-700 italic">$1</em>')
      // 处理代码片段
      .replace(/`(.*?)`/g, '<code class="bg-purple-200 text-purple-900 px-2 py-1 rounded text-xs font-mono">$1</code>')
      // 处理标题（需要在列表之前处理）
      .replace(/^#### (.*$)/gm, '<h5 class="text-purple-700 font-medium text-sm mt-2 mb-1">$1</h5>')
      .replace(/^### (.*$)/gm, '<h4 class="text-purple-800 font-semibold text-base mt-3 mb-2">$1</h4>')
      .replace(/^## (.*$)/gm, '<h3 class="text-purple-900 font-bold text-lg mt-4 mb-2">$1</h3>')
      .replace(/^# (.*$)/gm, '<h2 class="text-purple-900 font-bold text-xl mt-4 mb-3">$1</h2>')
      // 处理列表项
      .replace(/^- (.*$)/gm, '<div class="flex items-start space-x-2 my-1 ml-2"><span class="text-purple-500 mt-1 font-bold">•</span><span class="flex-1">$1</span></div>')
      // 处理数字列表
      .replace(/^(\d+)\. (.*$)/gm, '<div class="flex items-start space-x-2 my-1 ml-2"><span class="text-purple-500 font-medium min-w-[1.5rem]">$1.</span><span class="flex-1">$2</span></div>')
      // 处理引用块
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-purple-300 pl-4 my-2 text-purple-700 italic bg-purple-50 py-2">$1</blockquote>')
      // 处理分隔线
      .replace(/^---+$/gm, '<hr class="border-purple-300 my-4">')
      // 处理换行（最后处理）
      .replace(/\n/g, '<br>');

    // 处理连续的<br>标签，避免过多空行
    formatted = formatted.replace(/(<br>\s*){3,}/g, '<br><br>');

    console.log('🔍 formatReasoningContent - 格式化完成');
    return formatted;
  } catch (error) {
    console.error('🔍 formatReasoningContent - 格式化过程中出错:', error);
    // 出错时返回基本的HTML转义内容
    return content.replace(/\n/g, '<br>').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  }
}

export default SpecialContentRenderer;
