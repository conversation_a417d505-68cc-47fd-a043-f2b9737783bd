"use client";

import React from 'react';

interface ConfirmContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  content: string;
  fieldLabel?: string;
}

/**
 * 确认内容对话框组件
 * 用于在应用生成内容前让用户确认
 */
export const ConfirmContentDialog: React.FC<ConfirmContentDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  content,
  fieldLabel = '内容'
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-4">
            <label className="block text-sm font-medium text-gray-700">
              {fieldLabel}
            </label>
            <div className="border border-gray-300 rounded-md p-3 bg-gray-50 max-h-[400px] overflow-y-auto">
              <div className="whitespace-pre-wrap text-gray-800">
                {content}
              </div>
            </div>
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            onClick={onConfirm}
          >
            确认应用
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmContentDialog;
