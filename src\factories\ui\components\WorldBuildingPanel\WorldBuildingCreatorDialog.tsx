"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding, PromptTemplate, PromptCategory } from '@/lib/db/dexie';
import { AIWorldBuildingExtractorAdapter } from '@/adapters/ai/AIWorldBuildingExtractorAdapter';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorldBuildingCreatorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (worldBuildings: WorldBuilding[]) => void;
  bookId: string;
}

/**
 * 世界观创建对话框组件
 */
export const WorldBuildingCreatorDialog: React.FC<WorldBuildingCreatorDialogProps> = ({
  isOpen,
  onClose,
  onCreate,
  bookId
}) => {
  // 章节列表
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  // 选中的章节ID列表
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  // 自定义提示词
  const [customPrompt, setCustomPrompt] = useState('');
  // 最大创建数量
  const [maxWorldBuildings, setMaxWorldBuildings] = useState(10);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 章节加载状态
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 成功消息
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  // 现有世界观元素列表
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);
  // 选中的关联世界观元素
  const [selectedRelatedWorldBuildings, setSelectedRelatedWorldBuildings] = useState<string[]>([]);
  // 世界观加载状态
  const [isLoadingWorldBuildings, setIsLoadingWorldBuildings] = useState(false);
  // 创建的世界观
  const [createdWorldBuildings, setCreatedWorldBuildings] = useState<WorldBuilding[]>([]);
  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');
  // 提示词模板名称
  const [templateName, setTemplateName] = useState('');
  // 提示词模板描述
  const [templateDescription, setTemplateDescription] = useState('');
  // 是否显示保存模板表单
  const [showSaveTemplateForm, setShowSaveTemplateForm] = useState(false);
  // 世界观AI适配器
  const worldBuildingAIAdapter = new AIWorldBuildingExtractorAdapter();
  // 提示词模板管理器状态
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);

  // 加载章节列表和世界观元素
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
      loadWorldBuildings();
    }
  }, [isOpen, bookId]);

  // 加载章节列表
  const loadChapters = async () => {
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 src/db/chapterRepository.ts
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
      setError('无法获取章节数据，请检查网络连接或联系管理员');
    } catch (error) {
      console.error('加载章节数据失败:', error);
      setError('获取章节数据失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 加载世界观元素
  const loadWorldBuildings = async () => {
    setIsLoadingWorldBuildings(true);
    setError(null);

    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 获取书籍的所有世界观元素
      const bookWorldBuildings = await worldBuildingRepository.getAllByBookId(bookId);

      // 按名称排序
      const sortedWorldBuildings = [...bookWorldBuildings].sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      setWorldBuildings(sortedWorldBuildings);
    } catch (error) {
      console.error('加载世界观元素失败:', error);
      setError('加载世界观元素失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingWorldBuildings(false);
    }
  };

  // 处理章节选择变化
  const handleChapterSelectionChange = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 处理世界观元素选择变化
  const handleWorldBuildingSelectionChange = (worldBuildingId: string, worldBuildingName: string) => {
    // 使用ID和名称的组合，格式为 "id:name"
    const worldBuildingInfo = `${worldBuildingId}:${worldBuildingName}`;

    setSelectedRelatedWorldBuildings(prev => {
      if (prev.includes(worldBuildingInfo)) {
        return prev.filter(info => info !== worldBuildingInfo);
      } else {
        return [...prev, worldBuildingInfo];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAllChapters = (checked: boolean) => {
    if (checked) {
      setSelectedChapterIds(chapters.map(chapter => chapter.id!).filter(Boolean));
    } else {
      setSelectedChapterIds([]);
    }
  };

  /**
   * 选择章节范围
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect') => {
    // 验证输入
    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end)) {
      setError('请输入有效的章节编号');
      return;
    }

    if (start > end) {
      setError('起始章节编号不能大于结束章节编号');
      return;
    }

    if (start < 1 || end > chapters.length) {
      setError(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  // 创建世界观
  const createWorldBuildings = async () => {
    if (selectedChapterIds.length === 0) {
      setError('请至少选择一个章节');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 获取选中章节的内容
      const selectedChapters = chapters.filter(chapter => selectedChapterIds.includes(chapter.id!));

      // 使用AI创建世界观
      const creationOptions = {
        maxWorldBuildings,
        customPrompt,
        relatedWorldBuildings: selectedRelatedWorldBuildings
      };

      const extractionResult = await worldBuildingAIAdapter.createWorldBuildingsFromChapters(
        selectedChapters as any,
        creationOptions
      );

      // 创建世界观对象
      const now = new Date();
      const newWorldBuildings: WorldBuilding[] = [];

      for (const [name, info] of Object.entries(extractionResult)) {
        if (info.newInfo) {
          const category = info.newInfo.类别 || info.newInfo.category || '';
          const description = info.newInfo.描述 || info.newInfo.description || '';

          // 获取关联的世界观元素ID
          const relatedWorldBuildingIds = selectedRelatedWorldBuildings
            .map(info => info.split(':')[0]) // 从 "id:name" 格式中提取ID
            .filter(Boolean);

          // 创建新的世界观对象
          const newWorldBuilding: WorldBuilding = {
            bookId,
            name,
            category,
            description,
            createdAt: now,
            updatedAt: now,
            extractedFromChapterIds: selectedChapterIds,
            relatedCharacterIds: [],
            relatedTerminologyIds: [],
            relatedWorldBuildingIds: relatedWorldBuildingIds,
            attributes: {}
          };

          // 添加其他属性
          for (const [key, value] of Object.entries(info.newInfo)) {
            if (key !== '类别' && key !== 'category' && key !== '描述' && key !== 'description' && key !== 'name') {
              newWorldBuilding.attributes![key] = value as string;
            }
          }

          newWorldBuildings.push(newWorldBuilding);
        }
      }

      // 设置创建的世界观
      setCreatedWorldBuildings(newWorldBuildings);

      console.log('创建完成，成功创建:', newWorldBuildings.length);
    } catch (error) {
      console.error('创建世界观失败:', error);
      setError('创建世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 取消创建
  const cancelCreate = () => {
    // 取消AI请求
    worldBuildingAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  // 应用创建
  const applyCreate = () => {
    if (createdWorldBuildings.length > 0) {
      onCreate(createdWorldBuildings);
      onClose();
    }
  };

  // 打开提示词模板管理器
  const openPromptManager = () => {
    setIsPromptManagerOpen(true);
  };

  // 处理提示词模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    setCustomPrompt(template.content);
    setIsPromptManagerOpen(false);
  };

  // 切换显示保存模板表单
  const toggleSaveTemplateForm = () => {
    setShowSaveTemplateForm(!showSaveTemplateForm);
    // 重置表单
    if (!showSaveTemplateForm) {
      setTemplateName('');
      setTemplateDescription('');
    }
  };

  // 保存提示词模板
  const savePromptTemplate = async () => {
    if (!templateName.trim()) {
      setError('请输入模板名称');
      return;
    }

    if (!customPrompt.trim()) {
      setError('请输入模板内容');
      return;
    }

    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 创建新模板
      const newTemplate: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        category: PromptCategory.WORLD_BUILDING,
        name: templateName,
        content: customPrompt,
        description: templateDescription || undefined
      };

      // 保存模板
      await promptTemplateRepository.create(newTemplate);

      // 重置表单
      setTemplateName('');
      setTemplateDescription('');
      setShowSaveTemplateForm(false);

      // 显示成功消息
      setSuccessMessage('提示词模板保存成功');

      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('保存提示词模板失败:', error);
      setError('保存提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">AI创建世界观</h2>
          <p className="text-sm text-gray-600">从选定的章节中提取信息，创建新的世界观元素</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-y-auto">
          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* 创建结果 */}
          {createdWorldBuildings.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-800">创建结果</h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p className="text-green-700">成功创建 {createdWorldBuildings.length} 个世界观元素。</p>
              </div>

              {/* 显示创建的世界观 */}
              <div className="max-h-96 overflow-y-auto">
                {createdWorldBuildings.map((wb, index) => (
                  <div key={index} className="mb-4 p-4 border rounded-lg">
                    <h4 className="font-medium text-gray-800">{wb.name}</h4>
                    <p className="text-sm text-gray-500">类别: {wb.category || '未设置'}</p>
                    <p className="text-sm text-gray-500 mt-2">描述:</p>
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">{wb.description || '未设置'}</p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 章节选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">选择章节</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="mb-2">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedChapterIds.length === chapters.length}
                        onChange={(e) => handleSelectAllChapters(e.target.checked)}
                      />
                      <span className="ml-2 text-sm font-medium text-gray-700">全选</span>
                    </label>
                  </div>

                  {/* 范围选择 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-100 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">范围选择:</span>
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <button
                      className="px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                      onClick={() => handleRangeSelect('select')}
                    >
                      选择
                    </button>
                    <button
                      className="px-2 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                      onClick={() => handleRangeSelect('deselect')}
                    >
                      取消选择
                    </button>
                  </div>

                  <div className="space-y-1">
                    {chapters.map((chapter) => (
                      <label key={chapter.id} className="flex items-center">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-blue-600"
                          checked={selectedChapterIds.includes(chapter.id!)}
                          onChange={() => handleChapterSelectionChange(chapter.id!)}
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {chapter.title || `章节 ${chapter.order !== undefined ? chapter.order + 1 : '未编号'}`}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* 最大创建数量 */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">最大创建数量</h3>
                <div className="flex items-center">
                  <input
                    type="range"
                    min="1"
                    max="30"
                    value={maxWorldBuildings}
                    onChange={(e) => setMaxWorldBuildings(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="ml-4 text-gray-700">{maxWorldBuildings}</span>
                </div>
              </div>

              {/* 关联世界观元素选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">关联世界观元素（可选）</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  {isLoadingWorldBuildings ? (
                    <div className="flex items-center justify-center p-4">
                      <svg className="animate-spin h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="text-gray-600">加载世界观元素...</span>
                    </div>
                  ) : worldBuildings.length === 0 ? (
                    <div className="text-center p-4 text-gray-500">
                      没有找到现有的世界观元素
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {worldBuildings.map((wb) => (
                        <label key={wb.id} className="flex items-center">
                          <input
                            type="checkbox"
                            className="form-checkbox h-4 w-4 text-blue-600"
                            checked={selectedRelatedWorldBuildings.includes(`${wb.id}:${wb.name}`)}
                            onChange={() => handleWorldBuildingSelectionChange(wb.id!, wb.name)}
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {wb.name} <span className="text-xs text-gray-500">({wb.category || '未分类'})</span>
                          </span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
                {selectedRelatedWorldBuildings.length > 0 && (
                  <div className="mt-2 p-2 bg-blue-50 rounded-md">
                    <p className="text-sm text-blue-700">已选择 {selectedRelatedWorldBuildings.length} 个关联世界观元素</p>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {selectedRelatedWorldBuildings.map(wb => {
                        const name = wb.split(':')[1] || wb; // 只显示名称部分
                        return (
                          <span key={wb} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {name}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* 自定义提示词 */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium text-gray-800">自定义提示词（可选）</h3>
                  <div className="flex space-x-2">
                    <button
                      className="px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors flex items-center"
                      onClick={toggleSaveTemplateForm}
                      title={showSaveTemplateForm ? "取消保存" : "保存为模板"}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                      </svg>
                      {showSaveTemplateForm ? "取消保存" : "保存模板"}
                    </button>
                    <button
                      className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors flex items-center"
                      onClick={openPromptManager}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      管理模板
                    </button>
                  </div>
                </div>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="输入特定要求，例如：关注特定类型的世界观元素、添加更多细节等。如果留空，将使用默认提示词。"
                />

                {/* 保存模板表单 */}
                {showSaveTemplateForm && (
                  <div className="mt-2 p-3 border rounded-md bg-gray-50">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">保存为提示词模板</h4>
                    <div className="mb-2">
                      <label className="block text-xs text-gray-600 mb-1">模板名称 <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm"
                        value={templateName}
                        onChange={(e) => setTemplateName(e.target.value)}
                        placeholder="输入模板名称..."
                      />
                    </div>
                    <div className="mb-2">
                      <label className="block text-xs text-gray-600 mb-1">模板描述</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm"
                        value={templateDescription}
                        onChange={(e) => setTemplateDescription(e.target.value)}
                        placeholder="输入模板描述（可选）..."
                      />
                    </div>
                    <div className="flex justify-end">
                      <button
                        type="button"
                        className="mr-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                        onClick={toggleSaveTemplateForm}
                      >
                        取消
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600"
                        onClick={savePromptTemplate}
                      >
                        保存
                      </button>
                    </div>
                  </div>
                )}

                {/* 成功消息 */}
                {successMessage && (
                  <div className="mt-2 p-2 bg-green-100 text-green-700 rounded-md">
                    {successMessage}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center mb-2">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-blue-700">正在创建世界观元素，这可能需要一些时间...</span>
              </div>
            </div>
          )}
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-2">
          {isLoading ? (
            <button
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelCreate}
            >
              取消
            </button>
          ) : createdWorldBuildings.length > 0 ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={applyCreate}
              >
                创建世界观
              </button>
            </>
          ) : (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                onClick={createWorldBuildings}
                disabled={selectedChapterIds.length === 0}
              >
                开始创建
              </button>
            </>
          )}
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.WORLD_BUILDING}
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />
    </div>
  );
};
