/**
 * AI推荐信息增强器
 * 负责管理AI持久化推荐信息，包含完整的写作技巧和框架分析数据
 */

/**
 * 完整的推荐信息数据结构
 */
export interface EnhancedRecommendationData {
  id: string;
  type: 'book-title' | 'synopsis' | 'combined';
  timestamp: Date;
  
  // 基础推荐信息
  keywords: Array<{
    text: string;
    category: string;
    hotness?: number;
    source: 'preset' | 'user-saved' | 'analysis';
  }>;
  
  frameworks: Array<{
    id: string;
    name: string;
    pattern?: string;
    description: string;
    effectiveness: number;
    examples: string[];
    
    // 🔥 完整的写作技巧分析字段
    writingTechniques?: Array<{
      id: string;
      name: string;
      category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
      description: string;
      examples: string[];
      techniqueType: string;
      effectiveness: number;
    }>;
    
    // 🔥 风格特征详细信息
    styleCharacteristics?: {
      // 📝 排版技巧
      layoutTechniques?: {
        paragraphStructure?: string[];   // 段落结构：单段式、多段式、递进式
        lineBreakStrategy?: string[];    // 换行策略：悬念换行、爽点换行、节奏换行
        rhythmControl?: string[];        // 节奏控制：快节奏展开、慢节奏铺垫、变节奏起伏
        visualImpact?: number;           // 视觉冲击力：0-1
      };
      
      // 🎯 省略与强调
      omissionAndEmphasis?: {
        omittedElements?: string[];      // 省略要素：背景细节、人物外貌、具体设定
        emphasizedElements?: string[];   // 强调要素：身份反差、能力觉醒、势力震惊
        contrastTechniques?: string[];   // 对比技巧：前后对比、身份对比、实力对比
        suspensePoints?: string[];       // 悬念设置：身份悬念、能力悬念、关系悬念
      };
      
      // 💥 爽点布局
      coolPointLayout?: {
        primaryCoolPoints?: string[];    // 主要爽点：装逼打脸、身份暴露、实力碾压、马甲掉落
        coolPointTiming?: string[];      // 爽点时机：开头爆点、中段高潮、结尾悬念
        coolPointIntensity?: number;     // 爽点强度：0-1
        anticipationBuilding?: string[]; // 期待营造：预告式、暗示式、对比式
      };
      
      // 💡 脑洞展现
      creativeConcept?: {
        coreCreativity?: string[];       // 核心创意：设定创新、身份创新、能力创新、世界观创新
        conceptPresentation?: string[];  // 概念展现：直接展示、逐步揭示、反转展现
        uniquenessLevel?: number;        // 独特性程度：0-1
        marketAppeal?: string[];         // 市场吸引力：热门元素、差异化卖点、读者痛点
      };
    };
    
    // 🔄 可复用模板
    reusableTemplates?: Array<{
      id: string;
      name: string;
      pattern: string;
      description: string;
      techniques: string[];
      effectiveness: number;
    }>;
  }>;
  
  // 分析置信度
  analysisConfidence?: number;
  
  // 使用统计
  usageCount: number;
  lastUsedAt?: Date;
}

/**
 * 推荐增强器配置
 */
export interface RecommendationEnhancerConfig {
  enabled: boolean;                    // 是否启用持久化推荐
  autoUpdate: boolean;                 // 是否自动更新推荐信息
  maxHistoryCount: number;             // 最大历史记录数量
  includeAnalysisData: boolean;        // 是否包含分析数据
  keywordLimit: number;                // 关键词数量限制
  frameworkLimit: number;              // 框架数量限制
}

/**
 * AI推荐信息增强器类
 */
export class RecommendationEnhancer {
  private static readonly STORAGE_KEY = 'ai-persistent-recommendations';
  private static readonly CONFIG_KEY = 'recommendation-enhancer-config';
  
  private static readonly DEFAULT_CONFIG: RecommendationEnhancerConfig = {
    enabled: false,
    autoUpdate: true,
    maxHistoryCount: 50,
    includeAnalysisData: true,
    keywordLimit: 100,
    frameworkLimit: 20
  };

  /**
   * 获取配置
   */
  static getConfig(): RecommendationEnhancerConfig {
    try {
      const saved = localStorage.getItem(this.CONFIG_KEY);
      if (saved) {
        return { ...this.DEFAULT_CONFIG, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('读取推荐增强器配置失败:', error);
    }
    return this.DEFAULT_CONFIG;
  }

  /**
   * 保存配置
   */
  static saveConfig(config: Partial<RecommendationEnhancerConfig>): void {
    try {
      const currentConfig = this.getConfig();
      const newConfig = { ...currentConfig, ...config };
      localStorage.setItem(this.CONFIG_KEY, JSON.stringify(newConfig));
      console.log('✅ 推荐增强器配置已保存:', newConfig);
    } catch (error) {
      console.error('保存推荐增强器配置失败:', error);
    }
  }

  /**
   * 检查是否启用
   */
  static isEnabled(): boolean {
    return this.getConfig().enabled;
  }

  /**
   * 启用/禁用推荐增强器
   */
  static setEnabled(enabled: boolean): void {
    this.saveConfig({ enabled });
  }

  /**
   * 获取持久化推荐数据
   */
  static getPersistentRecommendations(): EnhancedRecommendationData[] {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        const data = JSON.parse(saved);
        return Array.isArray(data) ? data : [];
      }
    } catch (error) {
      console.error('读取持久化推荐数据失败:', error);
    }
    return [];
  }

  /**
   * 保存推荐数据
   */
  static saveRecommendationData(data: EnhancedRecommendationData): void {
    try {
      const config = this.getConfig();
      const existing = this.getPersistentRecommendations();
      
      // 检查是否已存在相同类型的数据，如果存在则更新
      const existingIndex = existing.findIndex(item => item.type === data.type);
      
      if (existingIndex >= 0) {
        existing[existingIndex] = {
          ...data,
          usageCount: existing[existingIndex].usageCount + 1,
          lastUsedAt: new Date()
        };
      } else {
        existing.unshift(data);
      }
      
      // 限制历史记录数量
      const trimmed = existing.slice(0, config.maxHistoryCount);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(trimmed));
      console.log('✅ 推荐数据已保存:', { type: data.type, keywordCount: data.keywords.length, frameworkCount: data.frameworks.length });
    } catch (error) {
      console.error('保存推荐数据失败:', error);
    }
  }

  /**
   * 清空推荐数据
   */
  static clearRecommendations(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('✅ 推荐数据已清空');
    } catch (error) {
      console.error('清空推荐数据失败:', error);
    }
  }

  /**
   * 获取统计信息
   */
  static getStatistics(): {
    totalRecommendations: number;
    totalKeywords: number;
    totalFrameworks: number;
    lastUpdated?: Date;
  } {
    const data = this.getPersistentRecommendations();
    
    const totalKeywords = data.reduce((sum, item) => sum + item.keywords.length, 0);
    const totalFrameworks = data.reduce((sum, item) => sum + item.frameworks.length, 0);
    const lastUpdated = data.length > 0 ? new Date(Math.max(...data.map(item => new Date(item.timestamp).getTime()))) : undefined;
    
    return {
      totalRecommendations: data.length,
      totalKeywords,
      totalFrameworks,
      lastUpdated
    };
  }
}
