import { IUIComponent } from './IUIComponent';
import React from 'react';

/**
 * 面板尺寸
 */
export type PanelSize = 'small' | 'medium' | 'large';

/**
 * 面板组件接口
 */
export interface IPanelComponent extends IUIComponent {
  /**
   * 设置面板标题
   * @param title 面板标题
   */
  setTitle(title: string): void;

  /**
   * 设置面板是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void;

  /**
   * 设置面板尺寸
   * @param size 面板尺寸
   */
  setSize(size: PanelSize): void;

  /**
   * 设置面板内容
   * @param content 面板内容
   */
  setContent(content: React.ReactNode): void;

  /**
   * 设置面板头部
   * @param header 面板头部
   */
  setHeader(header: React.ReactNode): void;

  /**
   * 设置面板底部
   * @param footer 面板底部
   */
  setFooter(footer: React.ReactNode): void;

  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void;

  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void;

  /**
   * 设置是否固定高度
   * @param fixedHeight 是否固定高度
   */
  setFixedHeight(fixedHeight: boolean): void;

  /**
   * 设置背景颜色
   * @param backgroundColor 背景颜色
   */
  setBackgroundColor(backgroundColor: string): void;

  /**
   * 设置宽度
   * @param width 宽度
   */
  setWidth(width: string): void;

  /**
   * 设置高度
   * @param height 高度
   */
  setHeight(height: string): void;
}
