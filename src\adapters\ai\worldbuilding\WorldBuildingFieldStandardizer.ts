"use client";

import { FieldNameMappings } from '@/utils/ai/prompts/FieldNameMappings';

/**
 * 世界观字段标准化器
 * 负责标准化字段名称和世界观元素信息
 */
export class WorldBuildingFieldStandardizer {
  /**
   * 标准化字段名称
   * @param fieldName 原始字段名称
   * @returns 标准化后的字段名称
   */
  standardizeFieldName(fieldName: string): string {
    // 转换为小写并移除空格
    const lowerCaseName = fieldName.toLowerCase().trim();

    // 尝试直接匹配
    if (FieldNameMappings[lowerCaseName]) {
      return FieldNameMappings[lowerCaseName];
    }

    // 尝试部分匹配 - 优先匹配中文关键词
    const chineseKeys = Object.keys(FieldNameMappings).filter(key => /[\u4e00-\u9fa5]/.test(key));
    for (const key of chineseKeys) {
      if (lowerCaseName.includes(key)) {
        return FieldNameMappings[key];
      }
    }

    // 如果没有匹配到中文关键词，尝试匹配英文关键词
    const englishKeys = Object.keys(FieldNameMappings).filter(key => !/[\u4e00-\u9fa5]/.test(key));
    for (const key of englishKeys) {
      if (lowerCaseName.includes(key)) {
        return FieldNameMappings[key];
      }
    }

    // 如果没有匹配到，返回原始字段名称
    return fieldName;
  }

  /**
   * 标准化世界观元素信息
   * @param worldBuildingInfo 原始世界观元素信息
   * @returns 标准化后的世界观元素信息
   */
  standardizeWorldBuildingInfo(worldBuildingInfo: Record<string, any>): Record<string, any> {
    const standardizedInfo: Record<string, any> = {};

    // 处理newInfo对象
    if (worldBuildingInfo.newInfo) {
      standardizedInfo.newInfo = {};

      for (const [key, value] of Object.entries(worldBuildingInfo.newInfo)) {
        const standardizedKey = this.standardizeFieldName(key);
        standardizedInfo.newInfo[standardizedKey] = value;
      }
    } else {
      // 如果没有newInfo结构，尝试直接标准化顶层字段
      for (const [key, value] of Object.entries(worldBuildingInfo)) {
        if (key !== 'newInfo') {
          const standardizedKey = this.standardizeFieldName(key);

          // 如果是对象，递归标准化
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            standardizedInfo[standardizedKey] = this.standardizeWorldBuildingInfo(value as Record<string, any>);
          } else {
            standardizedInfo[standardizedKey] = value;
          }
        }
      }

      // 如果没有newInfo结构但有标准字段，创建newInfo结构
      // 使用中文字段名称
      const standardFields = ['类别', '描述', '重要性', '时间跨度', '影响范围', '起源', '规则', '象征意义', '冲突', '关联元素'];
      const hasStandardFields = standardFields.some(field => field in standardizedInfo);

      if (hasStandardFields) {
        standardizedInfo.newInfo = {};

        for (const field of standardFields) {
          if (field in standardizedInfo) {
            standardizedInfo.newInfo[field] = standardizedInfo[field];
            delete standardizedInfo[field];
          }
        }
      }
    }

    return standardizedInfo;
  }
}
