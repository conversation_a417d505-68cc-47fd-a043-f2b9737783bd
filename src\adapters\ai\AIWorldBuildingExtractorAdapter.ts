"use client";

import { WorldBuilding } from '@/lib/db/dexie';
import { ChapterContent, ExtractionOptions, IWorldBuildingCreator, IWorldBuildingExtractor, IWorldBuildingFieldGenerator, IWorldBuildingUpdater, UpdateSuggestion } from './interfaces/WorldBuildingInterfaces';
import { WorldBuildingFactory } from './worldbuilding/WorldBuildingFactory';

/**
 * AI世界观提取适配器
 * 用于从章节内容中提取世界观信息
 *
 * 注意：此类现在是一个适配器，使用新的组件实现功能
 */
export class AIWorldBuildingExtractorAdapter {
  private extractor: IWorldBuildingExtractor;
  private updater: IWorldBuildingUpdater;
  private creator: IWorldBuildingCreator;
  private fieldGenerator: IWorldBuildingFieldGenerator;

  /**
   * 创建世界观提取AI适配器
   */
  constructor() {
    const factory = WorldBuildingFactory.getInstance();
    this.extractor = factory.createWorldBuildingExtractor();
    this.updater = factory.createWorldBuildingUpdater();
    this.creator = factory.createWorldBuildingCreator();
    this.fieldGenerator = factory.createWorldBuildingFieldGenerator();
  }

  /**
   * 从章节内容中提取世界观信息
   * @param chapters 章节内容列表
   * @param options 提取选项
   * @returns 提取的世界观信息
   */
  async extractWorldBuildingsFromChapters(
    chapters: ChapterContent[],
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    return this.extractor.extractWorldBuildingsFromChapters(chapters, options);
  }

  /**
   * 从章节内容中更新特定世界观元素的信息
   * @param worldBuilding 世界观对象
   * @param chapters 章节内容列表
   * @param options 提取选项
   * @returns 更新建议
   */
  async updateWorldBuildingFromChapters(
    worldBuilding: WorldBuilding,
    chapters: ChapterContent[],
    options: ExtractionOptions = {}
  ): Promise<UpdateSuggestion[]> {
    return this.updater.updateWorldBuildingFromChapters(worldBuilding, chapters, options);
  }

  /**
   * 使用关联章节内容更新世界观元素
   * @param worldBuilding 世界观对象
   * @param bookId 书籍ID
   * @param options 更新选项
   * @returns 更新建议
   */
  async updateWorldBuildingWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    bookId: string,
    options: ExtractionOptions = {}
  ): Promise<UpdateSuggestion[]> {
    return this.updater.updateWorldBuildingWithAssociatedChapters(worldBuilding, bookId, options);
  }

  /**
   * 使用关联章节内容生成世界观元素的特定字段
   * @param worldBuilding 世界观对象
   * @param fieldName 字段名称
   * @param bookId 书籍ID
   * @param relatedWorldBuildings 关联的世界观元素
   * @param customPrompt 自定义提示词
   * @returns 生成的字段内容
   */
  async generateFieldWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    fieldName: string,
    bookId: string,
    relatedWorldBuildings: string[] = [],
    customPrompt?: string
  ): Promise<string> {
    return this.fieldGenerator.generateFieldWithAssociatedChapters(worldBuilding, fieldName, bookId, relatedWorldBuildings, customPrompt);
  }

  /**
   * 从章节内容中创建世界观元素
   * @param chapters 章节内容列表
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  async createWorldBuildingsFromChapters(
    chapters: ChapterContent[],
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    return this.creator.createWorldBuildingsFromChapters(chapters, options);
  }

  /**
   * 使用关联章节内容创建世界观元素
   * @param bookId 书籍ID
   * @param chapterId 章节ID
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  async createWorldBuildingsFromAssociatedChapter(
    bookId: string,
    chapterId: string,
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    return this.creator.createWorldBuildingsFromAssociatedChapter(bookId, chapterId, options);
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    this.extractor.cancelRequest();
    this.updater.cancelRequest();
    this.creator.cancelRequest();
    this.fieldGenerator.cancelRequest();
  }
}
