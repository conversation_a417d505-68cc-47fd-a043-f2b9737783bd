"use client";

import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { DeleteConfirmationOptions, deleteConfirmationService } from '@/services/ui/DeleteConfirmationService';

interface ConfirmationState {
  isOpen: boolean;
  confirmationId: string | null;
  options: DeleteConfirmationOptions | null;
}

/**
 * 全局删除确认对话框组件
 * 监听删除确认事件并显示对话框
 */
export const GlobalDeleteConfirmDialog: React.FC = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    confirmationId: null,
    options: null
  });

  const cancelButtonRef = useRef<HTMLButtonElement>(null);

  // 监听删除确认事件
  useEffect(() => {
    const handleShowConfirmation = (event: CustomEvent) => {
      const { confirmationId, options } = event.detail;
      setState({
        isOpen: true,
        confirmationId,
        options
      });
    };

    window.addEventListener('show-delete-confirmation', handleShowConfirmation as EventListener);
    
    return () => {
      window.removeEventListener('show-delete-confirmation', handleShowConfirmation as EventListener);
    };
  }, []);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!state.isOpen) return;
      
      if (e.key === 'Escape') {
        handleCancel();
      } else if (e.key === 'Enter' && e.ctrlKey) {
        // Ctrl+Enter 确认删除
        handleConfirm();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.isOpen]);

  // 当对话框打开时，聚焦到取消按钮
  useEffect(() => {
    if (state.isOpen && cancelButtonRef.current) {
      setTimeout(() => {
        cancelButtonRef.current?.focus();
      }, 50);
    }
  }, [state.isOpen]);

  // 处理确认
  const handleConfirm = () => {
    if (state.confirmationId) {
      deleteConfirmationService.handleConfirmationResult(state.confirmationId, true);
    }
    closeDialog();
  };

  // 处理取消
  const handleCancel = () => {
    if (state.confirmationId) {
      deleteConfirmationService.handleConfirmationResult(state.confirmationId, false);
    }
    closeDialog();
  };

  // 关闭对话框
  const closeDialog = () => {
    setState({
      isOpen: false,
      confirmationId: null,
      options: null
    });
  };

  // 获取危险等级样式
  const getDangerLevelStyles = (level: string = 'medium') => {
    switch (level) {
      case 'low':
        return {
          headerBg: 'bg-yellow-50',
          headerBorder: 'border-yellow-100',
          headerText: 'text-yellow-700',
          confirmButton: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500'
        };
      case 'high':
        return {
          headerBg: 'bg-red-50',
          headerBorder: 'border-red-100',
          headerText: 'text-red-700',
          confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
        };
      default: // medium
        return {
          headerBg: 'bg-orange-50',
          headerBorder: 'border-orange-100',
          headerText: 'text-orange-700',
          confirmButton: 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500'
        };
    }
  };

  if (!state.isOpen || !state.options) return null;

  const styles = getDangerLevelStyles(state.options.dangerLevel);

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
      <div 
        className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden animate-scaleIn"
        style={{ transform: 'translateZ(0)' }}
        onClick={e => e.stopPropagation()}
      >
        {/* 对话框标题 */}
        <div className={`px-6 py-4 border-b ${styles.headerBg} ${styles.headerBorder}`}>
          <h3 className={`text-lg font-medium ${styles.headerText} flex items-center`}>
            {state.options.customIcon && (
              <span className="mr-2 text-xl">{state.options.customIcon}</span>
            )}
            {state.options.title}
          </h3>
        </div>

        {/* 对话框内容 */}
        <div className="px-6 py-4">
          <p className="text-gray-700 leading-relaxed">
            {state.options.message}
          </p>
          
          {/* 恢复提示 */}
          {state.options.showRecoveryHint && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                💡 提示：删除的数据可能可以通过系统备份恢复，但建议谨慎操作。
              </p>
            </div>
          )}

          {/* 危险操作警告 */}
          {state.options.dangerLevel === 'high' && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700 font-medium">
                ⚠️ 警告：这是一个高风险操作，删除后无法恢复！
              </p>
            </div>
          )}
        </div>

        {/* 对话框按钮 */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
          <button
            ref={cancelButtonRef}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
            onClick={handleCancel}
          >
            {state.options.cancelText || '取消'}
          </button>
          <button
            className={`px-4 py-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${styles.confirmButton}`}
            onClick={handleConfirm}
          >
            {state.options.confirmText || '删除'}
          </button>
        </div>

        {/* 快捷键提示 */}
        <div className="px-6 py-2 bg-gray-100 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            按 ESC 取消 • 按 Ctrl+Enter 确认
          </p>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default GlobalDeleteConfirmDialog;
