"use client";

import React, { useState, useEffect } from 'react';
import { ICharacterPanelComponent } from '../../interfaces/ICharacterPanelComponent';
import { Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { CharacterList } from './CharacterList';
import { CharacterDetail } from './CharacterDetail';
import { CharacterPanelHeader } from './CharacterPanelHeader';
import { CharacterPanelFooter } from './CharacterPanelFooter';
import { DeleteConfirmDialog } from './DeleteConfirmDialog';
import { useCharacterData } from './useCharacterData';
import CharacterExtractorDialog from './CharacterExtractorDialog';
import CharacterCreatorDialog from './CharacterCreatorDialog';
import Panel from '../common/Panel';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * 人物面板组件实现
 */
export class CharacterPanelComponent implements ICharacterPanelComponent {
  private bookId: string = '';
  private isOpen: boolean = false;
  private closeHandler: (() => void) | null = null;
  private createHandler: ((character: Character) => void) | null = null;
  private updateHandler: ((character: Character) => void) | null = null;
  private deleteHandler: ((characterId: string) => void) | null = null;
  private className: string = '';

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this.bookId = bookId;
  }

  /**
   * 设置是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void {
    this.closeHandler = handler;
  }

  /**
   * 设置创建人物回调函数
   * @param handler 创建人物回调函数
   */
  onCreate(handler: (character: Character) => void): void {
    this.createHandler = handler;
  }

  /**
   * 设置更新人物回调函数
   * @param handler 更新人物回调函数
   */
  onUpdate(handler: (character: Character) => void): void {
    this.updateHandler = handler;
  }

  /**
   * 设置删除人物回调函数
   * @param handler 删除人物回调函数
   */
  onDelete(handler: (characterId: string) => void): void {
    this.deleteHandler = handler;
  }

  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void {
    this.className = className;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    return (
      <CharacterPanelComponentView
        bookId={this.bookId}
        isOpen={this.isOpen}
        onClose={this.closeHandler || (() => {})}
        onCharacterCreate={this.createHandler || (() => {})}
        onCharacterUpdate={this.updateHandler || (() => {})}
        onCharacterDelete={this.deleteHandler || (() => {})}
        className={this.className}
      />
    );
  }
}

interface CharacterPanelComponentViewProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  onCharacterCreate: (character: Character) => void;
  onCharacterUpdate: (character: Character) => void;
  onCharacterDelete: (characterId: string) => void;
  className: string;
}

/**
 * 人物面板组件视图
 */
const CharacterPanelComponentView: React.FC<CharacterPanelComponentViewProps> = ({
  bookId,
  isOpen,
  onClose,
  onCharacterCreate,
  onCharacterUpdate,
  onCharacterDelete,
  className
}) => {
  // 不再需要UI工厂实例

  // 删除确认对话框状态
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [characterToDelete, setCharacterToDelete] = useState<Character | null>(null);

  // 章节列表状态
  const [chapters, setChapters] = useState<GenericChapter[]>([]);

  // 术语列表状态
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);

  // 世界观列表状态
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);

  // 人物提取对话框状态
  const [isExtractorOpen, setIsExtractorOpen] = useState(false);

  // 人物创建对话框状态
  const [isCreatorOpen, setIsCreatorOpen] = useState(false);

  // 使用人物数据钩子
  const {
    characters,
    filteredCharacters,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedCharacter,
    isEditing,
    handleSelectCharacter,
    handleCreateCharacter,
    handleEditCharacter,
    handleSaveCharacter,
    handleCancelEdit,
    handleDeleteCharacter
  } = useCharacterData(bookId, isOpen);

  // 加载章节数据、术语数据和世界观数据 - 用于 CharacterDetail 组件
  useEffect(() => {
    if (isOpen) {
      // 从数据库获取章节列表
      import('@/lib/db/repositories').then(({ chapterRepository }) => {
        chapterRepository.getAllByBookId(bookId)
          .then(data => {
            setChapters(data);
          })
          .catch(error => {
            console.error('获取章节数据失败:', error);
            setChapters([]);
          });
      });

      // 从数据库获取术语列表
      import('@/lib/db/repositories').then(({ terminologyRepository }) => {
        terminologyRepository.getAllByBookId(bookId)
          .then(data => {
            setTerminologies(data);
          })
          .catch(error => {
            console.error('获取术语数据失败:', error);
            setTerminologies([]);
          });
      });

      // 从数据库获取世界观列表
      import('@/lib/db/repositories').then(({ worldBuildingRepository }) => {
        worldBuildingRepository.getAllByBookId(bookId)
          .then(data => {
            setWorldBuildings(data);
          })
          .catch(error => {
            console.error('获取世界观数据失败:', error);
            setWorldBuildings([]);
          });
      });
    }
  }, [isOpen, bookId]);

  // 处理删除人物
  const handleDeleteWithConfirm = (character: Character = selectedCharacter!) => {
    if (character && character.id) {
      console.log('准备删除人物:', character);

      // 打开删除确认对话框
      setCharacterToDelete(character);
      setIsDeleteDialogOpen(true);
    }
  };

  // 确认删除
  const confirmDelete = () => {
    if (characterToDelete && characterToDelete.id) {
      console.log('确认删除人物:', characterToDelete.id);

      // 调用删除回调
      onCharacterDelete(characterToDelete.id!);

      // 处理删除
      handleDeleteCharacter(characterToDelete.id!);

      console.log('删除完成');

      // 关闭对话框
      setIsDeleteDialogOpen(false);
      setCharacterToDelete(null);
    }
  };

  // 取消删除
  const cancelDelete = () => {
    console.log('取消删除');
    setIsDeleteDialogOpen(false);
    setCharacterToDelete(null);
  };

  // 处理保存人物
  const handleSaveWithCallback = (character: Character) => {
    // 保存人物
    const isNew = handleSaveCharacter(character);

    // 调用回调
    if (isNew) {
      onCharacterCreate(character);
    } else {
      onCharacterUpdate(character);
    }
  };

  // 渲染面板内容
  const renderPanelContent = () => {
    return (
      <div className="flex h-full">
        {/* 左侧列表 */}
        <div style={{ width: '35%' }}>
          <CharacterList
            characters={filteredCharacters}
            isLoading={isLoading}
            searchQuery={searchQuery}
            selectedCharacter={selectedCharacter}
            onSelectCharacter={handleSelectCharacter}
            onCreateCharacter={handleCreateCharacter}
            onDeleteCharacter={handleDeleteWithConfirm}
          />
        </div>

        {/* 右侧详情/编辑区域 */}
        <div className="p-4" style={{ width: '65%' }}>
          <CharacterDetail
            character={selectedCharacter}
            bookId={bookId}
            characters={characters}
            isEditing={isEditing}
            onEdit={handleEditCharacter}
            onDelete={handleDeleteWithConfirm}
            onSave={handleSaveWithCallback}
            onCancel={handleCancelEdit}
            chapters={chapters} // 传递章节列表
            terminologies={terminologies} // 传递术语列表
            worldBuildings={worldBuildings} // 传递世界观列表
          />
        </div>
      </div>
    );
  };



  return (
    <>
      <Panel
        title="人物管理"
        isOpen={isOpen}
        size="large"
        fixedHeight={true}
        backgroundColor="var(--color-white)"
        width="60%"
        height="80%"
        enhanced={true}
        literaryTheme={true}
        header={
          <CharacterPanelHeader
            searchQuery={searchQuery}
            sortBy={sortBy}
            onSearchChange={setSearchQuery}
            onSortChange={setSortBy}
            onExtractCharacters={() => setIsExtractorOpen(true)}
            onCreateCharacter={() => setIsCreatorOpen(true)}
          />
        }
        content={renderPanelContent()}
        footer={
          <CharacterPanelFooter
            characterCount={characters.length}
          />
        }
        onClose={onClose}
        className={className}
      />

      {/* 删除确认对话框 */}
      {characterToDelete && (
        <DeleteConfirmDialog
          character={characterToDelete}
          isOpen={isDeleteDialogOpen}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}

      {/* 人物提取对话框 */}
      <CharacterExtractorDialog
        isOpen={isExtractorOpen}
        onClose={() => setIsExtractorOpen(false)}
        existingCharacters={characters}
        onCreateCharacter={async (characterInfo) => {
          console.log('创建新人物:', characterInfo);

          // 创建完整的人物对象，确保所有字段都有默认值
          const now = new Date();
          const newCharacter: Character = {
            bookId,
            name: characterInfo.newInfo?.name || characterInfo.name || '新人物',
            description: characterInfo.newInfo?.description || '',
            appearance: characterInfo.newInfo?.appearance || '',
            personality: characterInfo.newInfo?.personality || '',
            background: characterInfo.newInfo?.background || '',
            goals: characterInfo.newInfo?.goals || '',
            characterArchetype: characterInfo.newInfo?.characterArchetype || '',
            growthArc: characterInfo.newInfo?.growthArc || '',
            hiddenMotivation: characterInfo.newInfo?.hiddenMotivation || '',
            secretHistory: characterInfo.newInfo?.secretHistory || '',
            innerConflicts: characterInfo.newInfo?.innerConflicts || '',
            symbolism: characterInfo.newInfo?.symbolism || '',
            relationships: characterInfo.newInfo?.relationships ? [{
              targetCharacterId: '',
              relationshipType: '其他',
              description: characterInfo.newInfo.relationships
            }] : [],
            createdAt: now,
            updatedAt: now,
            extractedFromChapterIds: [],
            relatedCharacterIds: [],
            relatedTerminologyIds: [],
            relatedWorldBuildingIds: []
          };

          console.log('完整的新人物对象:', newCharacter);
          console.log('AI提取的原始数据:', characterInfo.newInfo);

          // 使用 setTimeout 延迟执行，避免立即触发页面刷新
          setTimeout(() => {
            try {
              // 保存人物
              handleSaveWithCallback(newCharacter);
            } catch (error) {
              console.error('延迟保存新人物失败:', error);
            }
          }, 0);

          // 返回一个已解决的 Promise，避免阻塞调用者
          return Promise.resolve();
        }}
        onUpdateCharacter={async (character, characterInfo) => {
          console.log('更新人物:', character.name, characterInfo);

          // 更新人物，确保所有字段都正确处理
          const updatedCharacter = { ...character };
          updatedCharacter.updatedAt = new Date();

          // 更新AI提取的信息，确保字段正确映射
          if (characterInfo.newInfo) {
            // 明确处理每个字段，确保数据类型正确
            if (characterInfo.newInfo.name) updatedCharacter.name = characterInfo.newInfo.name;
            if (characterInfo.newInfo.description) updatedCharacter.description = characterInfo.newInfo.description;
            if (characterInfo.newInfo.appearance) updatedCharacter.appearance = characterInfo.newInfo.appearance;
            if (characterInfo.newInfo.personality) updatedCharacter.personality = characterInfo.newInfo.personality;
            if (characterInfo.newInfo.background) updatedCharacter.background = characterInfo.newInfo.background;
            if (characterInfo.newInfo.goals) updatedCharacter.goals = characterInfo.newInfo.goals;
            if (characterInfo.newInfo.characterArchetype) updatedCharacter.characterArchetype = characterInfo.newInfo.characterArchetype;
            if (characterInfo.newInfo.growthArc) updatedCharacter.growthArc = characterInfo.newInfo.growthArc;
            if (characterInfo.newInfo.hiddenMotivation) updatedCharacter.hiddenMotivation = characterInfo.newInfo.hiddenMotivation;
            if (characterInfo.newInfo.secretHistory) updatedCharacter.secretHistory = characterInfo.newInfo.secretHistory;
            if (characterInfo.newInfo.innerConflicts) updatedCharacter.innerConflicts = characterInfo.newInfo.innerConflicts;
            if (characterInfo.newInfo.symbolism) updatedCharacter.symbolism = characterInfo.newInfo.symbolism;

            // 处理关系字段
            if (characterInfo.newInfo.relationships) {
              if (!updatedCharacter.relationships) {
                updatedCharacter.relationships = [];
              }
              // 添加新的关系描述
              updatedCharacter.relationships.push({
                targetCharacterId: '',
                relationshipType: '其他',
                description: characterInfo.newInfo.relationships
              });
            }
          }

          console.log('更新后的人物对象:', updatedCharacter);
          console.log('AI提取的原始数据:', characterInfo.newInfo);

          // 使用 setTimeout 延迟执行，避免立即触发页面刷新
          setTimeout(() => {
            try {
              // 保存人物
              handleSaveWithCallback(updatedCharacter);
            } catch (error) {
              console.error('延迟更新人物失败:', error);
            }
          }, 0);

          // 返回一个已解决的 Promise，避免阻塞调用者
          return Promise.resolve();
        }}
        bookId={bookId}
      />

      {/* 人物创建对话框 */}
      <CharacterCreatorDialog
        isOpen={isCreatorOpen}
        onClose={() => setIsCreatorOpen(false)}
        onCreateCharacter={async (characterInfo) => {
          console.log('AI创建新人物:', characterInfo);

          // 创建完整的人物对象
          const now = new Date();
          const newCharacter: Character = {
            bookId,
            name: characterInfo.name || '新人物',
            description: characterInfo.description || '',
            appearance: characterInfo.appearance || '',
            personality: characterInfo.personality || '',
            background: characterInfo.background || '',
            goals: characterInfo.goals || '',
            characterArchetype: characterInfo.characterArchetype || '',
            growthArc: characterInfo.growthArc || '',
            hiddenMotivation: characterInfo.hiddenMotivation || '',
            secretHistory: characterInfo.secretHistory || '',
            innerConflicts: characterInfo.innerConflicts || '',
            symbolism: characterInfo.symbolism || '',
            createdAt: now,
            updatedAt: now,
            extractedFromChapterIds: characterInfo.extractedFromChapterIds || [],
            relatedCharacterIds: [],
            relatedTerminologyIds: [],
            relatedWorldBuildingIds: []
          };

          console.log('完整的新人物对象:', newCharacter);
          console.log('原始人物信息:', characterInfo);

          // 保存人物
          handleSaveWithCallback(newCharacter);
        }}
        bookId={bookId}
      />
    </>
  );
};

export default CharacterPanelComponent;
