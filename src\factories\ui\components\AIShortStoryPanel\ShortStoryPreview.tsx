"use client";

import React, { useState } from 'react';
import { CoreMystery, SegmentStructure } from '@/factories/ai/services/types/ShortStoryTypes';

interface ShortStoryPreviewProps {
  coreMystery: CoreMystery | null;
  segments: SegmentStructure[];
  fullText: string;
  isLoading: boolean;
  currentSegmentIndex: number;
  streamingContent: string;
}

/**
 * 短篇创作预览组件
 * 显示生成过程和最终结果
 */
export const ShortStoryPreview: React.FC<ShortStoryPreviewProps> = ({
  coreMystery,
  segments,
  fullText,
  isLoading,
  currentSegmentIndex,
  streamingContent
}) => {
  const [activeTab, setActiveTab] = useState<'structure' | 'preview' | 'full'>('structure');

  // 获取段落目的的颜色
  const getPurposeColor = (purpose: string) => {
    switch (purpose) {
      case 'setup': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'development': return 'bg-green-100 text-green-800 border-green-200';
      case 'climax': return 'bg-red-100 text-red-800 border-red-200';
      case 'resolution': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取段落目的的中文名称
  const getPurposeName = (purpose: string) => {
    switch (purpose) {
      case 'setup': return '建立悬念';
      case 'development': return '深入探索';
      case 'climax': return '真相逼近';
      case 'resolution': return '真相大白';
      default: return '未知';
    }
  };

  return (
    <div className="flex-1 pl-5 flex flex-col">
      {/* 标签页导航 */}
      <div className="flex space-x-1 mb-4 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('structure')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'structure'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          结构设计
        </button>
        <button
          onClick={() => setActiveTab('preview')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'preview'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          分段预览
        </button>
        <button
          onClick={() => setActiveTab('full')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'full'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          完整文本
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        
        {/* 结构设计标签页 */}
        {activeTab === 'structure' && (
          <div className="h-full overflow-y-auto space-y-4">
            
            {/* 核心悬念信息 */}
            {coreMystery && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                <h3 className="text-lg font-bold text-blue-800 mb-3">{coreMystery.title}</h3>
                
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-blue-700">核心悬念：</span>
                    <span className="text-gray-700">{coreMystery.coreQuestion}</span>
                  </div>
                  
                  <div>
                    <span className="font-medium text-green-700">透露的一半：</span>
                    <span className="text-gray-700">{coreMystery.revealedHalf}</span>
                  </div>
                  
                  <div>
                    <span className="font-medium text-amber-700">隐藏的一半：</span>
                    <span className="text-gray-700">{coreMystery.hiddenHalf}</span>
                  </div>
                  
                  <div>
                    <span className="font-medium text-purple-700">最终真相：</span>
                    <span className="text-gray-700">{coreMystery.finalTruth}</span>
                  </div>
                </div>
              </div>
            )}

            {/* 分段结构 */}
            {segments.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-base font-medium text-gray-800">分段结构</h4>
                
                {segments.map((segment, index) => (
                  <div
                    key={segment.segmentNumber}
                    className={`p-3 rounded-lg border transition-all ${
                      currentSegmentIndex === index && isLoading
                        ? 'border-blue-300 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                          {segment.segmentNumber}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded border ${getPurposeColor(segment.purpose)}`}>
                          {getPurposeName(segment.purpose)}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-3 text-xs text-gray-500">
                        <span>信息: {segment.informationLevel}/10</span>
                        <span>紧张: {segment.tensionLevel}/10</span>
                        {segment.wordCount && <span>字数: {segment.wordCount}</span>}
                      </div>
                    </div>
                    
                    {segment.content && (
                      <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                        {segment.content}
                      </div>
                    )}

                    {currentSegmentIndex === index && isLoading && streamingContent && (
                      <div className="text-sm text-gray-700 leading-relaxed mt-2 border-t pt-2">
                        <span className="text-blue-600 font-medium">正在生成：</span>
                        <div className="whitespace-pre-wrap inline">{streamingContent}</div>
                        <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse"></span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* 加载状态 */}
            {isLoading && !coreMystery && (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                  <p className="text-gray-600">正在生成核心悬念...</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 分段预览标签页 */}
        {activeTab === 'preview' && (
          <div className="h-full overflow-y-auto">
            {segments.length > 0 ? (
              <div className="space-y-6">
                {segments.map((segment, index) => (
                  <div key={segment.segmentNumber} className="bg-white p-4 rounded-lg border border-gray-200">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-sm font-bold rounded-full flex items-center justify-center">
                        {segment.segmentNumber}
                      </span>
                      <span className={`px-3 py-1 text-sm font-medium rounded-full ${getPurposeColor(segment.purpose)}`}>
                        {getPurposeName(segment.purpose)}
                      </span>
                    </div>
                    
                    {segment.content ? (
                      <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                        {segment.content}
                      </div>
                    ) : (
                      <div className="text-gray-400 italic">
                        {currentSegmentIndex === index && isLoading ? '正在生成...' : '等待生成'}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p>暂无分段内容</p>
                  <p className="text-sm mt-1">点击"生成短篇"开始创作</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 完整文本标签页 */}
        {activeTab === 'full' && (
          <div className="h-full overflow-y-auto">
            {fullText ? (
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                    {fullText}
                  </div>
                </div>
                
                {/* 统计信息 */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span>总字数: {fullText.length}</span>
                    <span>段落数: {segments.length}</span>
                    <span>平均段长: {Math.round(fullText.length / segments.length)}</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p>暂无完整文本</p>
                  <p className="text-sm mt-1">生成完成后将显示完整的短篇内容</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
