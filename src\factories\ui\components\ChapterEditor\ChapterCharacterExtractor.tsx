"use client";

import React, { useState } from 'react';
import { Chapter, Character } from '@/lib/db/dexie';
import createCharacterExtractorAIAdapter, { CharacterExtractionResult } from '@/adapters/ai/CharacterExtractorAIAdapter';

interface ChapterCharacterExtractorProps {
  chapter: {
    id?: string;
    content: string;
  };
  onCreateCharacter: (characterInfo: CharacterExtractionResult) => void;
  onUpdateCharacter: (character: Character, characterInfo: CharacterExtractionResult) => void;
  existingCharacters: Character[];
}

/**
 * 章节人物提取组件
 * 用于从章节内容中提取人物信息
 */
const ChapterCharacterExtractor: React.FC<ChapterCharacterExtractorProps> = ({
  chapter,
  onCreateCharacter,
  onUpdateCharacter,
  existingCharacters
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [extractedCharacters, setExtractedCharacters] = useState<{ [name: string]: CharacterExtractionResult }>({});
  const [error, setError] = useState<string | null>(null);

  // 创建人物提取AI适配器
  const characterExtractorAIAdapter = createCharacterExtractorAIAdapter();

  /**
   * 提取人物信息
   */
  const extractCharacters = async () => {
    if (!chapter.content) {
      setError('章节内容为空，无法提取人物信息');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 从章节内容中提取人物信息
      const result = await characterExtractorAIAdapter.extractCharactersFromChapter(chapter.content);

      // 设置提取结果
      setExtractedCharacters(result);

      // 如果没有提取到人物信息
      if (Object.keys(result).length === 0) {
        setError('未从章节中提取到人物信息');
      }
    } catch (error) {
      console.error('提取人物信息失败:', error);
      setError('提取人物信息失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消提取
   */
  const cancelExtraction = () => {
    characterExtractorAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  /**
   * 创建新人物
   * @param name 人物名称
   * @param info 人物信息
   */
  const handleCreateCharacter = (name: string, info: CharacterExtractionResult) => {
    // 创建新的人物对象
    const newCharacterInfo = {
      ...info,
      name
    };

    // 调用创建人物回调
    onCreateCharacter(newCharacterInfo);

    // 从提取结果中移除该人物
    const updatedExtractedCharacters = { ...extractedCharacters };
    delete updatedExtractedCharacters[name];
    setExtractedCharacters(updatedExtractedCharacters);
  };

  /**
   * 更新现有人物
   * @param name 人物名称
   * @param info 人物信息
   */
  const handleUpdateCharacter = (name: string, info: CharacterExtractionResult) => {
    // 查找现有人物
    const existingCharacter = existingCharacters.find(
      c => c.name === name || (c.alias && c.alias.includes(name))
    );

    if (existingCharacter) {
      // 调用更新人物回调
      onUpdateCharacter(existingCharacter, info);

      // 从提取结果中移除该人物
      const updatedExtractedCharacters = { ...extractedCharacters };
      delete updatedExtractedCharacters[name];
      setExtractedCharacters(updatedExtractedCharacters);
    }
  };

  /**
   * 检查人物是否已存在
   * @param name 人物名称
   * @returns 是否已存在
   */
  const characterExists = (name: string): boolean => {
    return existingCharacters.some(
      c => c.name === name || (c.alias && c.alias.includes(name))
    );
  };

  return (
    <div>
      {/* 提取按钮 */}
      <button
        className="ml-2 p-1 text-blue-600 hover:text-blue-800 transition-colors"
        onClick={extractCharacters}
        disabled={isLoading}
        title="从章节提取人物信息"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      </button>

      {/* 加载中状态 */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <div className="flex items-center justify-center mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700"></div>
            </div>
            <p className="text-center">正在提取人物信息...</p>
            <button
              className="mt-4 w-full p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelExtraction}
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 提取结果 */}
      {!isLoading && Object.keys(extractedCharacters).length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-4xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold mb-4 text-purple-700">提取到的人物信息</h3>

            <div className="space-y-4">
              {Object.entries(extractedCharacters).map(([name, info]) => (
                <div key={name} className="border p-4 rounded-lg">
                  <h4 className="font-bold text-lg">{name}</h4>

                  {/* 人物信息预览 */}
                  <div className="mt-2 space-y-1">
                    {Object.entries(info.newInfo).map(([field, value]) => {
                      // 安全的值处理 - 确保value可以被渲染
                      const displayValue = typeof value === 'string'
                        ? value
                        : typeof value === 'object' && value !== null
                          ? JSON.stringify(value, null, 2)
                          : String(value);

                      return (
                        <p key={field} className="text-sm">
                          <span className="font-semibold">{field}:</span> {displayValue.length > 100 ? displayValue.substring(0, 100) + '...' : displayValue}
                        </p>
                      );
                    })}
                  </div>

                  {/* 操作按钮 */}
                  <div className="mt-4 flex justify-end space-x-2">
                    {characterExists(name) ? (
                      <button
                        className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        onClick={() => handleUpdateCharacter(name, info)}
                      >
                        更新现有人物
                      </button>
                    ) : (
                      <button
                        className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                        onClick={() => handleCreateCharacter(name, info)}
                      >
                        创建新人物
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-end">
              <button
                className="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                onClick={() => setExtractedCharacters({})}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <div className="mx-auto mb-4 text-red-500 flex justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <p className="text-center">{error}</p>
            <button
              className="mt-4 w-full p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={() => setError(null)}
            >
              关闭
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChapterCharacterExtractor;
