"use client";

import React from 'react';
import { ButtonType, ButtonSize } from '@/factories/ui/interfaces';

interface ButtonProps {
  text?: string;
  type?: ButtonType;
  size?: ButtonSize;
  disabled?: boolean;
  icon?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 按钮适配器组件
 */
export const Button: React.FC<ButtonProps> = ({
  text = '',
  type = 'primary',
  size = 'medium',
  disabled = false,
  icon,
  onClick,
  className,
  style
}) => {
  // 获取按钮样式
  const getButtonStyles = () => {
    // 基础样式
    let styles: React.CSSProperties = {
      backgroundColor: '',
      color: '',
      borderColor: '',
      padding: '',
      fontSize: '',
      opacity: disabled ? 0.5 : 1,
      cursor: disabled ? 'not-allowed' : 'pointer',
    };

    // 类型样式
    switch (type) {
      case 'primary':
        styles.backgroundColor = 'var(--color-primary)';
        styles.color = 'white';
        styles.borderColor = 'var(--color-primary)';
        break;
      case 'secondary':
        styles.backgroundColor = 'var(--color-secondary)';
        styles.color = 'var(--color-text-primary)';
        styles.borderColor = 'var(--color-secondary)';
        break;
      case 'success':
        styles.backgroundColor = 'var(--color-success)';
        styles.color = 'white';
        styles.borderColor = 'var(--color-success)';
        break;
      case 'danger':
        styles.backgroundColor = 'var(--color-danger)';
        styles.color = 'white';
        styles.borderColor = 'var(--color-danger)';
        break;
      case 'info':
        styles.backgroundColor = 'var(--color-info)';
        styles.color = 'white';
        styles.borderColor = 'var(--color-info)';
        break;
      case 'ghost':
        styles.backgroundColor = 'transparent';
        styles.color = 'var(--color-primary)';
        styles.borderColor = 'var(--color-primary)';
        break;
    }

    // 尺寸样式
    switch (size) {
      case 'small':
        styles.padding = '0.25rem 0.5rem';
        styles.fontSize = '0.875rem';
        break;
      case 'medium':
        styles.padding = '0.5rem 1rem';
        styles.fontSize = '1rem';
        break;
      case 'large':
        styles.padding = '0.75rem 1.5rem';
        styles.fontSize = '1.125rem';
        break;
    }

    return styles;
  };

  // 处理点击事件
  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const buttonStyles = getButtonStyles();
  const combinedClassName = `btn rounded-lg transition-all duration-200 flex items-center justify-center ${className || ''}`;

  return (
    <button
      className={combinedClassName}
      style={{
        ...buttonStyles,
        ...style,
        borderRadius: '0.5rem', // 更大的圆角
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // 添加阴影
      }}
      onClick={handleClick}
      disabled={disabled}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {text}
    </button>
  );
};


