"use client";

import { createAIFactory } from '@/factories/ai/AIFactory';
import { Character } from '@/lib/db/dexie';
import { CharacterRelationshipAnalysis } from '@/factories/ai/interfaces/IAICharacterComponent';
import { createNotificationFactory } from '@/factories/notification';
import { segmentText } from '@/utils/textSegmentation';
import { getAssociatedChapterContent, saveChapterAssociation, getBookChapters } from '@/utils/chapterAssociation';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';

/**
 * 人物AI适配器类
 * 用于连接UI和AI人物组件
 */
class CharacterAIAdapter {
  private aiFactory = createAIFactory();
  private characterAI = this.aiFactory.createAICharacterComponent();
  private notificationFactory = createNotificationFactory();
  private notification = this.notificationFactory.createNotificationComponent();
  private settingsFactory = createSettingsFactory();
  private apiSettings = this.settingsFactory.createAPISettingsDialogComponent();
  private loading = false;
  private error: string | null = null;

  constructor() {
    // 确保从localStorage加载最新设置
    try {
      const savedSettings = localStorage.getItem('api_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        console.log('CharacterAIAdapter从localStorage加载设置:', settings);

        // 如果有保存的提供商设置，确保apiSettings使用它
        if (settings.currentProvider) {
          this.apiSettings.setCurrentProvider(settings.currentProvider);
        }

        // 如果有保存的模型设置，确保apiSettings使用它
        if (settings.currentModel) {
          this.apiSettings.setCurrentModel(settings.currentModel);
        }
      }
    } catch (error) {
      console.error('从localStorage加载API设置失败:', error);
    }

    // 设置状态变更回调
    this.characterAI.onStatusChange((status) => {
      this.loading = status === 'loading' || status === 'streaming';
    });

    // 打印当前API设置
    const currentProvider = this.apiSettings.getCurrentProvider();
    const currentModel = this.apiSettings.getCurrentModel();
    const apiKey = this.apiSettings.getAPIKey(currentProvider);
    const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

    console.log('CharacterAIAdapter初始化完成:', {
      provider: currentProvider,
      model: currentModel,
      hasApiKey: !!apiKey,
      endpoint: apiEndpoint
    });
  }

  /**
   * 从文本中提取人物
   * @param text 文本内容
   * @param bookId 书籍ID
   * @returns 提取的人物列表
   */
  async extractCharacters(text: string, bookId: string): Promise<Character[]> {
    try {
      this.loading = true;
      this.error = null;

      const characters = await this.characterAI.extractCharacters(text, bookId);

      if (characters.length > 0) {
        this.notification.showSuccess(`成功提取出${characters.length}个人物`);
      } else {
        this.notification.showInfo('未能从文本中提取出人物');
      }

      return characters;
    } catch (err: any) {
      this.error = err.message || '提取人物时发生错误';
      this.notification.showError('提取人物失败');
      return [];
    } finally {
      this.loading = false;
    }
  }

  /**
   * 扩展人物信息
   * @param character 人物对象
   * @param fields 要扩展的字段
   * @returns 扩展后的人物对象
   */
  async expandCharacter(character: Character, fields?: any[]): Promise<Character> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}扩展人物信息`);

      const updatedCharacter = await this.characterAI.expandCharacter(character, fields as any);

      this.notification.showSuccess('人物信息扩展成功');

      return updatedCharacter;
    } catch (err: any) {
      this.error = err.message || '扩展人物信息时发生错误';
      this.notification.showError('扩展人物信息失败');
      return character;
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物描述
   * @param character 人物对象
   * @param customPrompt 自定义提示词
   * @returns 生成的描述
   */
  async generateCharacterDescription(character: Character, customPrompt?: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      if (currentProvider === 'custom' && !apiEndpoint) {
        throw new Error('请先在设置中配置自定义API端点');
      }

      // 创建AI发送组件
      const aiSender = this.aiFactory.createAISenderComponent();

      // 创建预设的用户提示词
      const presetUserPrompt = `你是一位文学大师级的人物描写专家，请根据以下信息创造一个立体、生动且内在一致的人物描述。`;

      // 构建提示词，确保包含人物信息和自定义提示词（如果有）
      const defaultPrompt = `
请为以下小说人物生成一段详细的描述，包括外貌、性格、背景等方面。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}

请根据以上信息，生成一段连贯、生动的人物描述，突出这个角色的独特之处和在故事中的作用。
`;

      // 如果有自定义提示词，将其添加到提示词中
      const customPromptText = customPrompt ? `
${customPrompt}

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}
` : '';

      // 最终的提示词，包含自定义提示词（如果有）和默认提示词
      const prompt = customPrompt ? customPromptText : defaultPrompt;

      const systemMessage = `你是一位专业的人物描写专家，擅长清晰准确地描述小说角色。请提供简洁、实用的人物描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 清晰简洁：使用直接、明确的语言描述人物特征
2. 重点突出：优先描述最重要的特征和关键信息
3. 结构合理：按照外貌、性格、背景等逻辑顺序组织内容
4. 实用为主：提供对故事发展和角色理解有实际帮助的信息
5. 避免过度修饰：减少不必要的形容词和华丽词藻

请根据提供的信息，创建一个结构清晰、内容准确的人物描述。`;

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}生成人物描述`);

      // 获取API设置的参数
      const streamingEnabled = this.apiSettings.getStreamingEnabled();

      // 使用API设置的参数
      const response = await aiSender.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        streaming: streamingEnabled,
        messages: [
          { role: 'user', content: presetUserPrompt }
        ]
      });

      if (!response.success) {
        throw new Error(response.error || '生成人物描述失败');
      }

      // 移除成功通知
      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物描述时发生错误';
      this.notification.showError('生成人物描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 分析人物关系
   * @param character 人物对象
   * @param otherCharacters 其他人物列表
   * @returns 人物关系分析结果
   */
  async analyzeCharacterRelationships(
    character: Character,
    otherCharacters: Character[]
  ): Promise<CharacterRelationshipAnalysis[]> {
    try {
      this.loading = true;
      this.error = null;

      const relationships = await this.characterAI.analyzeCharacterRelationships(character, otherCharacters);

      if (relationships.length > 0) {
        this.notification.showSuccess(`成功分析出${relationships.length}个人物关系`);
      } else {
        this.notification.showInfo('未能分析出人物关系');
      }

      return relationships;
    } catch (err: any) {
      this.error = err.message || '分析人物关系时发生错误';
      this.notification.showError('分析人物关系失败');
      return [];
    } finally {
      this.loading = false;
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest() {
    this.characterAI.cancelRequest();
    this.loading = false;
    this.notification.showInfo('已取消AI请求');
  }

  /**
   * 使用关联章节内容生成人物描述
   * @param character 人物对象
   * @param bookId 书籍ID
   * @returns 生成的描述
   */
  async generateDescriptionWithChapters(character: Character, bookId: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取关联章节内容
      const chapterContent = await getAssociatedChapterContent(character.id!, 'character', bookId);

      if (!chapterContent) {
        this.notification.showWarning('没有找到关联章节内容');
        return await this.generateCharacterDescription(character);
      }

      // 构建提示词，包含章节内容
      const prompt = `
请为以下小说人物生成一段详细的描述，包括外貌、性格、背景等方面。
请特别参考提供的章节内容，从中提取关于该人物的信息。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}

章节内容:
${chapterContent}

请根据以上信息，特别是章节内容中的相关描述，生成一段连贯、生动的人物描述，突出这个角色的独特之处和在故事中的作用。
`;

      const systemMessage = `你是一位专业的人物描写专家，擅长清晰准确地描述小说角色。请提供简洁、实用的人物描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 清晰简洁：使用直接、明确的语言描述人物特征
2. 重点突出：优先描述最重要的特征和关键信息
3. 结构合理：按照外貌、性格、背景等逻辑顺序组织内容
4. 实用为主：提供对故事发展和角色理解有实际帮助的信息
5. 避免过度修饰：减少不必要的形容词和华丽词藻

请根据提供的信息，特别是章节内容中的相关描述，创建一个结构清晰、内容准确的人物描述。`;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);
      const streamingEnabled = this.apiSettings.getStreamingEnabled();

      // 创建AI发送组件
      const aiSender = this.aiFactory.createAISenderComponent();

      // 使用API设置的参数
      const response = await aiSender.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        streaming: streamingEnabled
      });

      if (!response.success) {
        throw new Error(response.error || '生成人物描述失败');
      }

      this.notification.showSuccess('基于章节内容的人物描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物描述时发生错误';
      this.notification.showError('生成人物描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 使用文本分段工具分段文本
   * @param text 要分段的文本
   * @param charsPerSegment 每段字符数
   * @param respectSentences 是否保持句子完整性
   * @returns 分段后的文本数组
   */
  segmentTextWithTool(
    text: string,
    charsPerSegment: number = 500,
    respectSentences: boolean = true
  ): string[] {
    return segmentText(text, charsPerSegment, respectSentences);
  }

  /**
   * 保存人物与章节的关联关系
   * @param character 人物对象
   * @param chapterIds 章节ID数组
   * @returns 是否保存成功
   */
  async saveCharacterChapterAssociation(
    character: Character,
    chapterIds: string[]
  ): Promise<boolean> {
    if (!character.id) {
      this.notification.showError('人物ID不存在');
      return false;
    }

    const result = await saveChapterAssociation(character.id, 'character', chapterIds);

    if (result) {
      this.notification.showSuccess('人物与章节关联关系保存成功');
    } else {
      this.notification.showError('保存关联关系失败');
    }

    return result;
  }

  /**
   * 获取书籍章节列表
   * @param bookId 书籍ID
   * @returns 章节列表
   */
  async getChapterList(bookId: string) {
    return await getBookChapters(bookId);
  }

  /**
   * 使用AI整体更新人物信息
   * @param character 人物对象
   * @param chapterContent 章节内容（可选）
   * @param customPrompt 自定义提示词（可选）
   * @returns 更新后的人物对象
   */
  async updateCharacterWithAI(
    character: Character,
    chapterContent?: string,
    customPrompt?: string
  ): Promise<Character> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      if (currentProvider === 'custom' && !apiEndpoint) {
        throw new Error('请先在设置中配置自定义API端点');
      }

      // 创建AI发送组件
      const aiSender = this.aiFactory.createAISenderComponent();

      // 构建人物信息字符串
      const characterInfo = `
人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}
`;

      // 构建提示词
      let prompt = `
请根据以下信息，全面更新这个小说人物的各个方面。

${characterInfo}
`;

      // 如果有章节内容，添加到提示词中
      if (chapterContent && chapterContent.trim()) {
        prompt += `
章节内容:
${chapterContent}

请根据上述章节内容，更新人物的各个方面。特别关注章节中揭示的新信息，并将其整合到人物描述中。
`;
      }

      // 如果有自定义提示词，添加到提示词中
      if (customPrompt && customPrompt.trim()) {
        prompt += `
用户特别要求:
${customPrompt}
`;
      }

      // 添加输出格式要求
      prompt += `
请以JSON格式返回更新后的人物信息，格式如下:
{
  "description": "更新后的人物描述",
  "appearance": "更新后的外貌描述",
  "personality": "更新后的性格描述",
  "background": "更新后的背景描述",
  "goals": "更新后的目标描述",
  "characterArchetype": "更新后的角色原型",
  "growthArc": "更新后的成长弧线",
  "hiddenMotivation": "更新后的隐藏动机",
  "secretHistory": "更新后的秘密历史",
  "innerConflicts": "更新后的内心冲突",
  "symbolism": "更新后的象征意义"
}

请确保返回的是有效的JSON格式，不要添加任何其他解释或说明。如果某个字段没有新信息，请保留原有内容。
`;

      const systemMessage = `你是一位专业的小说人物分析与创作专家，擅长从文本中提取人物信息并进行全面更新。你的任务是根据提供的人物信息和章节内容，更新人物的各个方面，包括描述、外貌、性格、背景等。

请遵循以下原则:
1. 保持连贯性：确保更新后的信息与原有信息保持连贯，不要产生矛盾
2. 整合新信息：将章节中的新信息自然地整合到人物描述中
3. 保持风格一致：更新后的描述应与原有描述风格一致
4. 完整全面：确保更新涵盖人物的所有方面
5. 严格遵循JSON格式：返回的内容必须是有效的JSON格式

请只返回JSON格式的结果，不要添加任何其他解释或说明。`;

      // 使用API设置的参数
      const response = await aiSender.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.3, // 使用较低的温度，确保输出的一致性
        max_tokens: 2000
      });

      if (!response.success) {
        throw new Error(response.error || '更新人物信息失败');
      }

      try {
        // 预处理AI返回的文本，移除可能存在的Markdown格式标记
        let cleanedText = response.text;

        // 移除开头的```json、```、```javascript等标记
        cleanedText = cleanedText.replace(/^```(?:json|javascript|js)?\s*/i, '');

        // 移除结尾的```标记
        cleanedText = cleanedText.replace(/\s*```\s*$/i, '');

        // 移除可能存在的HTML标签
        cleanedText = cleanedText.replace(/<[^>]*>/g, '');

        // 解析JSON结果
        const updatedInfo = JSON.parse(cleanedText);

        // 创建更新后的人物对象
        const updatedCharacter: Character = {
          ...character,
          updatedAt: new Date()
        };

        // 更新字段
        const fieldsToUpdate = [
          'description', 'appearance', 'personality', 'background', 'goals',
          'characterArchetype', 'growthArc', 'hiddenMotivation', 'secretHistory',
          'innerConflicts', 'symbolism'
        ];

        for (const field of fieldsToUpdate) {
          if (updatedInfo[field]) {
            (updatedCharacter as any)[field] = updatedInfo[field];
          }
        }

        this.notification.showSuccess('人物信息更新成功');
        return updatedCharacter;
      } catch (error) {
        console.error('解析AI返回的JSON失败:', error, '原始文本:', response.text);
        throw new Error('解析更新结果失败');
      }
    } catch (err: any) {
      this.error = err.message || '更新人物信息时发生错误';
      this.notification.showError('更新人物信息失败');
      return character;
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物外貌描述
   * @param character 人物对象
   * @param customPrompt 自定义提示词
   * @returns 生成的外貌描述
   */
  async generateCharacterAppearance(character: Character, customPrompt?: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      if (currentProvider === 'custom' && !apiEndpoint) {
        throw new Error('请先在设置中配置自定义API端点');
      }

      // 创建AI发送组件
      const aiSender = this.aiFactory.createAISenderComponent();

      // 创建预设的用户提示词
      const presetUserPrompt = `你是一位文学大师级的人物外貌描写专家，请根据以下信息创造一个立体、生动且内在一致的人物外貌描述。`;

      // 构建默认提示词
      const defaultPrompt = `
请为以下小说人物生成一段详细的外貌描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `已有外貌描述: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}

请生成一段生动、具体的外貌描述，包括但不限于：身高体型、面部特征、发型发色、眼睛、服饰风格、特殊标志等。描述应当与人物的性格和角色原型相符。
`;

      // 如果有自定义提示词，构建自定义提示词文本
      const customPromptText = customPrompt ? `
${customPrompt}

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `已有外貌描述: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
` : '';

      // 最终的提示词，包含自定义提示词（如果有）和默认提示词
      const prompt = customPrompt ? customPromptText : defaultPrompt;

      const systemMessage = `你是一位专业的人物外貌描写专家，擅长清晰准确地描述角色的外在特征。请提供简洁、实用的外貌描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 清晰具体：使用准确的词汇描述身高、体型、面部特征等
2. 重点突出：优先描述最显著和辨识度高的特征
3. 结构合理：按照从整体到细节的顺序组织内容
4. 实用为主：提供有助于读者想象角色形象的关键信息
5. 与角色定位一致：确保外貌描述与角色的性格和定位相符

请根据提供的信息，创建一个清晰、准确的人物外貌描述。`;

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}生成人物外貌描述`);

      // 获取API设置的参数
      const streamingEnabled = this.apiSettings.getStreamingEnabled();

      // 使用API设置的参数
      const response = await aiSender.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        streaming: streamingEnabled,
        messages: [
          { role: 'user', content: presetUserPrompt }
        ]
      });

      if (!response.success) {
        throw new Error(response.error || '生成人物外貌描述失败');
      }

      // 移除成功通知

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物外貌描述时发生错误';
      this.notification.showError('生成人物外貌描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物性格描述
   * @param character 人物对象
   * @param customPrompt 自定义提示词
   * @returns 生成的性格描述
   */
  async generateCharacterPersonality(character: Character, customPrompt?: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      if (currentProvider === 'custom' && !apiEndpoint) {
        throw new Error('请先在设置中配置自定义API端点');
      }

      // 创建AI发送组件
      const aiSender = this.aiFactory.createAISenderComponent();

      // 创建预设的用户提示词
      const presetUserPrompt = `你是一位文学大师级的人物性格描写专家，请根据以下信息创造一个深入、立体且内在一致的人物性格描述。`;

      // 构建默认提示词
      const defaultPrompt = `
请为以下小说人物生成一段详细的性格描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `已有性格描述: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}

请生成一段深入、立体的性格描述，包括但不限于：性格特点、行为模式、思维方式、情感表达、价值观、优缺点、与他人的互动方式等。描述应当与人物的背景和目标相符，并体现出内在的复杂性和矛盾。
`;

      // 如果有自定义提示词，构建自定义提示词文本
      const customPromptText = customPrompt ? `
${customPrompt}

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `已有性格描述: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
` : '';

      // 最终的提示词，包含自定义提示词（如果有）和默认提示词
      const prompt = customPrompt ? customPromptText : defaultPrompt;

      const systemMessage = `你是一位专业的人物性格描写专家，擅长清晰准确地描述角色的性格特点。请提供简洁、实用的性格描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确特点：清晰列出主要性格特征和行为模式
2. 实例说明：用简短的行为例子说明性格特点
3. 逻辑一致：确保性格描述内部逻辑一致，避免矛盾
4. 与背景关联：简要解释性格形成的关键背景因素
5. 与行动相关：说明性格如何影响角色的决策和行动

请根据提供的信息，创建一个清晰、准确、实用的人物性格描述。`;

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}生成人物性格描述`);

      // 获取API设置的参数
      const streamingEnabled = this.apiSettings.getStreamingEnabled();

      // 使用API设置的参数
      const response = await aiSender.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        streaming: streamingEnabled,
        messages: [
          { role: 'user', content: presetUserPrompt }
        ]
      });

      if (!response.success) {
        throw new Error(response.error || '生成人物性格描述失败');
      }

      this.notification.showSuccess('人物性格描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物性格描述时发生错误';
      this.notification.showError('生成人物性格描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物背景描述
   * @param character 人物对象
   * @param customPrompt 自定义提示词
   * @returns 生成的背景描述
   */
  async generateCharacterBackground(character: Character, customPrompt?: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      // 构建提示词
      const prompt = customPrompt ? `
${customPrompt}

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `已有背景描述: ${character.background}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
` : `
请为以下小说人物生成一段详细的背景故事。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `已有背景描述: ${character.background}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}

请生成一段丰富、连贯的背景故事，包括但不限于：出生和成长环境、家庭关系、重要的人生经历和转折点、教育和职业经历、重要的人际关系、塑造性格的关键事件等。背景故事应当与人物的性格和目标相符，并为其行为和动机提供合理的解释。
`;

      const systemMessage = `你是一位专业的人物背景描写专家，擅长清晰准确地描述角色的成长经历和历史。请提供简洁、实用的背景描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 重点事件：突出对角色形成有重大影响的关键事件
2. 时间顺序：按照清晰的时间顺序组织内容
3. 因果关系：简明地解释事件之间的因果关系
4. 与现状关联：说明背景如何导致角色当前的性格和行为
5. 实用信息：提供对理解角色动机和行为有帮助的背景信息

请根据提供的信息，创建一个清晰、准确、实用的人物背景描述。`;

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}生成人物背景描述`);

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: this.apiSettings.getAPIEndpoint(currentProvider)
      });

      this.notification.showSuccess('人物背景描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物背景描述时发生错误';
      this.notification.showError('生成人物背景描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物目标描述
   * @param character 人物对象
   * @returns 生成的目标描述
   */
  async generateCharacterGoals(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的目标和动机描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `已有目标描述: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}

请生成一段深入、具体的目标和动机描述，包括但不限于：短期目标、长期目标、表面目标、深层动机、目标的起源、实现目标的方法、面临的障碍、目标之间的冲突等。目标描述应当与人物的性格和背景相符，并为其行为提供驱动力。
`;

      const systemMessage = `你是一位专业的人物目标与动机描写专家，擅长清晰准确地描述角色的内在驱动力。请提供简洁、实用的目标和动机描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 层次清晰：明确区分短期目标和长期目标，表面目标和深层动机
2. 具体明确：使用具体的目标和行动计划，避免模糊的表述
3. 逻辑关联：确保目标与角色的性格和背景有明确的逻辑关系
4. 重点突出：优先描述最重要和最有影响力的目标
5. 实用为主：提供对理解角色行为有实际帮助的动机信息

请根据提供的信息，创建一个清晰、准确、实用的人物目标和动机描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物目标描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物目标描述时发生错误';
      this.notification.showError('生成人物目标描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物成长弧线描述
   * @param character 人物对象
   * @returns 生成的成长弧线描述
   */
  async generateCharacterGrowthArc(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的成长弧线描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.growthArc ? `已有成长弧线描述: ${character.growthArc}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}

请生成一段深入、具体的成长弧线描述，包括但不限于：人物在故事中的起点状态、面临的挑战和考验、关键的转变点、内在和外在的变化、最终的成长结果、学到的教训等。成长弧线应当与人物的性格、背景和目标相符，并展现出有意义的变化和发展。
`;

      const systemMessage = `你是一位专业的人物成长弧线描写专家，擅长清晰准确地描述角色的发展轨迹。请提供简洁、实用的成长弧线描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确阶段：清晰描述人物发展的关键阶段和转变点
2. 因果关系：简明地解释促使角色成长的原因和结果
3. 具体变化：使用具体的例子说明角色在思想和行为上的变化
4. 结构合理：按照时间或重要性顺序组织内容
5. 实用为主：提供对理解角色发展有实际帮助的信息

请根据提供的信息，创建一个清晰、准确、实用的人物成长弧线描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物成长弧线描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物成长弧线描述时发生错误';
      this.notification.showError('生成人物成长弧线描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物隐藏动机描述
   * @param character 人物对象
   * @returns 生成的隐藏动机描述
   */
  async generateCharacterHiddenMotivation(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的隐藏动机描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `表面目标: ${character.goals}` : ''}
${character.hiddenMotivation ? `已有隐藏动机描述: ${character.hiddenMotivation}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}

请生成一段深入、复杂的隐藏动机描述，包括但不限于：人物表面行为背后的真实驱动力、不为人知的欲望和需求、隐藏动机的起源、隐藏动机与表面目标的冲突、隐藏动机可能导致的行为等。隐藏动机应当与人物的性格、背景和秘密历史相符，并为其行为提供更深层次的解释。
`;

      const systemMessage = `你是一位专业的人物隐藏动机描写专家，擅长清晰准确地描述角色行为背后的隐秘驱动力。请提供简洁、实用的隐藏动机描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确对比：清晰说明表面行为与内在动机的差异
2. 具体原因：具体解释形成隐藏动机的关键原因
3. 逻辑关联：确保隐藏动机与角色的性格和背景有合理的逻辑关系
4. 重点突出：优先描述最重要和最有影响力的隐藏动机
5. 实用为主：提供对理解角色行为有实际帮助的动机信息

请根据提供的信息，创建一个清晰、准确、实用的人物隐藏动机描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物隐藏动机描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物隐藏动机描述时发生错误';
      this.notification.showError('生成人物隐藏动机描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物秘密历史描述
   * @param character 人物对象
   * @returns 生成的秘密历史描述
   */
  async generateCharacterSecretHistory(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的秘密历史描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.secretHistory ? `已有秘密历史描述: ${character.secretHistory}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}

请生成一段深入、神秘的秘密历史描述，包括但不限于：人物不为人知的过去经历、隐藏的身份、秘密的行动、不愿提及的创伤、被掩盖的事件等。秘密历史应当与人物的性格、背景和动机相符，并为其行为提供更深层次的解释，同时为故事发展埋下伏笔。
`;

      const systemMessage = `你是一位专业的人物秘密历史描写专家，擅长清晰准确地描述角色不为人知的过去。请提供简洁、实用的秘密历史描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 合理性：确保秘密历史与角色的整体形象保持一致
2. 因果关系：明确说明秘密历史如何影响角色当前的行为和动机
3. 重点突出：优先描述最重要和最有影响力的秘密事件
4. 结构清晰：按照时间顺序或重要性组织内容
5. 实用为主：提供对理解角色行为有实际帮助的历史信息

请根据提供的信息，创建一个清晰、准确、实用的人物秘密历史描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物秘密历史描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物秘密历史描述时发生错误';
      this.notification.showError('生成人物秘密历史描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物内心冲突描述
   * @param character 人物对象
   * @returns 生成的内心冲突描述
   */
  async generateCharacterInnerConflicts(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的内心冲突描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.innerConflicts ? `已有内心冲突描述: ${character.innerConflicts}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}

请生成一段深入、复杂的内心冲突描述，包括但不限于：人物内在的矛盾和挣扎、价值观的冲突、欲望与责任的拉扯、理想与现实的差距、不同身份角色之间的冲突等。内心冲突应当与人物的性格、背景和目标相符，并为其行为提供更丰富的心理动力。
`;

      const systemMessage = `你是一位专业的人物内心冲突描写专家，擅长清晰准确地描述角色的内在矛盾和挣扎。请提供简洁、实用的内心冲突描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确冲突：清晰描述角色面临的主要内心冲突
2. 具体表现：使用具体的例子说明冲突如何表现在思想和行为中
3. 原因分析：简明地解释冲突产生的原因和背景
4. 影响说明：说明内心冲突如何影响角色的决策和行为
5. 实用为主：提供对理解角色行为有实际帮助的冲突信息

请根据提供的信息，创建一个清晰、准确、实用的人物内心冲突描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物内心冲突描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物内心冲突描述时发生错误';
      this.notification.showError('生成人物内心冲突描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物象征意义描述
   * @param character 人物对象
   * @returns 生成的象征意义描述
   */
  async generateCharacterSymbolism(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的象征意义描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.symbolism ? `已有象征意义描述: ${character.symbolism}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}

请生成一段深入、富有文学性的象征意义描述，包括但不限于：人物在故事中代表的主题或理念、人物名字的寓意、与人物相关的意象和符号、人物与文学传统或神话原型的联系、人物在更广泛文化背景下的象征意义等。象征意义应当与人物的特质和故事角色相符，并为作品增添更深层次的解读维度。
`;

      const systemMessage = `你是一位专业的象征主义分析专家，擅长清晰准确地描述角色的象征意义和寓意。请提供简洁、实用的象征意义描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确象征：清晰说明角色可能代表的主要象征意义
2. 具体依据：使用角色的特质、行为和背景来支持象征解读
3. 主题关联：简明地解释角色象征如何与作品主题相关
4. 重点突出：优先描述最重要和最明显的象征意义
5. 实用为主：提供对理解作品有实际帮助的象征信息

请根据提供的信息，创建一个清晰、准确、实用的人物象征意义描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物象征意义描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物象征意义描述时发生错误';
      this.notification.showError('生成人物象征意义描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成人物角色原型描述
   * @param character 人物对象
   * @returns 生成的角色原型描述
   */
  async generateCharacterArchetype(character: Character): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 构建提示词
      const prompt = `
请为以下小说人物生成一段详细的角色原型描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.characterArchetype ? `已有角色原型描述: ${character.characterArchetype}` : ''}

请生成一段深入、专业的角色原型描述，包括但不限于：人物所属的经典角色原型类别、原型的主要特征、原型在文学传统中的演变、该原型的典型例子、如何在保持原型特征的同时使角色独特化等。角色原型描述应当与人物的特质相符，并为创作提供更清晰的角色定位和发展方向。
`;

      const systemMessage = `你是一位专业的角色原型分析专家，擅长清晰准确地识别和描述角色的原型类别。请提供简洁、实用的角色原型描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 明确分类：准确识别角色所属的主要原型类别（如英雄、导师、欺诈者等）
2. 核心特征：简明地描述该原型的关键特征和叙事功能
3. 应用说明：解释该原型如何应用于当前角色
4. 重点突出：优先描述最重要和最相关的原型特征
5. 实用为主：提供对角色创作有实际帮助的原型信息

请根据提供的信息，创建一个清晰、准确、实用的角色原型描述。`;

      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage
      });

      this.notification.showSuccess('人物角色原型描述生成成功');

      return response.text;
    } catch (err: any) {
      this.error = err.message || '生成人物角色原型描述时发生错误';
      this.notification.showError('生成人物角色原型描述失败');
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 生成通用字段内容
   * @param character 人物对象
   * @param fieldName 字段名称
   * @param customPrompt 自定义提示词
   * @returns 生成的字段内容
   */
  async generateGenericField(character: Character, fieldName: string, customPrompt?: string): Promise<string> {
    try {
      this.loading = true;
      this.error = null;

      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);

      if (!apiKey) {
        throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      }

      // 创建预设的用户提示词
      const presetUserPrompt = `你是一位专业的人物描述专家，请根据以下信息创建一个清晰、准确、实用的人物${fieldName}描述。请避免过度修饰和华丽的表达，专注于提供有实际帮助的内容。`;

      // 构建默认提示词
      const defaultPrompt = `
请为以下小说人物生成一段详细的${fieldName}描述。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${(character as any)[fieldName] ? `已有${fieldName}描述: ${(character as any)[fieldName]}` : ''}

请根据提供的信息，为该人物生成一段详细、生动且内在一致的${fieldName}描述。
`;

      // 如果有自定义提示词，构建自定义提示词文本
      const customPromptText = customPrompt ? `
${customPrompt}

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${(character as any)[fieldName] ? `已有${fieldName}描述: ${(character as any)[fieldName]}` : ''}
` : '';

      // 最终的提示词，包含自定义提示词（如果有）和默认提示词
      const prompt = customPrompt ? customPromptText : defaultPrompt;

      const systemMessage = `你是一位专业的人物描写专家，擅长清晰准确地描述角色的各个方面。请提供简洁、实用的${fieldName}描述，避免过度修饰和华丽的表达。

请遵循以下原则：
1. 清晰具体：使用准确的词汇描述关键特征
2. 重点突出：优先描述最重要和最有特色的内容
3. 结构合理：按照逻辑顺序组织内容
4. 与角色一致：确保描述与角色的整体形象相符
5. 实用为主：提供对理解角色有实际帮助的信息

请根据提供的信息，创建一个清晰、准确、实用的人物${fieldName}描述。`;

      console.log(`使用${currentProvider}提供商，模型: ${currentModel}生成人物${fieldName}描述`);

      // 获取API设置的参数
      const streamingEnabled = this.apiSettings.getStreamingEnabled();

      // 使用API设置的参数
      const response = await this.characterAI.sendRequest(prompt, {
        systemMessage,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: this.apiSettings.getAPIEndpoint(currentProvider),
        streaming: streamingEnabled,
        messages: [
          { role: 'user', content: presetUserPrompt }
        ]
      });

      this.notification.showSuccess(`人物${fieldName}描述生成成功`);

      return response.text;
    } catch (err: any) {
      this.error = err.message || `生成人物${fieldName}描述时发生错误`;
      this.notification.showError(`生成人物${fieldName}描述失败`);
      return '';
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取当前加载状态
   */
  isLoading(): boolean {
    return this.loading;
  }

  /**
   * 获取当前错误信息
   */
  getError(): string | null {
    return this.error;
  }
}

export default CharacterAIAdapter;
