/**
 * 示例管理对话框
 * 用于管理AI生成的JSON示例配置
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ExampleInjectionManager, ExampleConfig } from '../services/ExampleInjectionManager';

interface ExampleManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExampleActivated?: (example: ExampleConfig | null) => void;
}

export const ExampleManagementDialog: React.FC<ExampleManagementDialogProps> = ({
  isOpen,
  onClose,
  onExampleActivated
}) => {
  const [examples, setExamples] = useState<ExampleConfig[]>([]);
  const [activeExample, setActiveExample] = useState<ExampleConfig | null>(null);
  const [selectedExample, setSelectedExample] = useState<ExampleConfig | null>(null);
  const [importData, setImportData] = useState('');
  const [showImport, setShowImport] = useState(false);

  // 加载示例数据
  const loadExamples = () => {
    const allExamples = ExampleInjectionManager.getAllExamples();
    const currentActive = ExampleInjectionManager.getActiveExample();
    setExamples(allExamples);
    setActiveExample(currentActive);
  };

  useEffect(() => {
    if (isOpen) {
      loadExamples();
    }
  }, [isOpen]);

  // 激活示例
  const handleActivateExample = (exampleId: string) => {
    const success = ExampleInjectionManager.activateExample(exampleId);
    if (success) {
      loadExamples();
      const newActive = ExampleInjectionManager.getActiveExample();
      onExampleActivated?.(newActive);
    }
  };

  // 停用示例
  const handleDeactivateExample = () => {
    const success = ExampleInjectionManager.deactivateCurrentExample();
    if (success) {
      loadExamples();
      onExampleActivated?.(null);
    }
  };

  // 删除示例
  const handleDeleteExample = (exampleId: string) => {
    if (confirm('确定要删除这个示例吗？')) {
      const success = ExampleInjectionManager.deleteExample(exampleId);
      if (success) {
        loadExamples();
        if (activeExample?.id === exampleId) {
          onExampleActivated?.(null);
        }
      }
    }
  };

  // 导出示例
  const handleExportExamples = () => {
    const exportData = ExampleInjectionManager.exportExamples();
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai_examples_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 导入示例
  const handleImportExamples = () => {
    try {
      const success = ExampleInjectionManager.importExamples(importData);
      if (success) {
        loadExamples();
        setImportData('');
        setShowImport(false);
        alert('导入成功！');
      } else {
        alert('导入失败，请检查数据格式');
      }
    } catch (error) {
      alert('导入失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">示例管理</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* 操作按钮 */}
          <div className="flex gap-3 mb-6">
            <button
              onClick={handleExportExamples}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              导出示例
            </button>
            <button
              onClick={() => setShowImport(!showImport)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              导入示例
            </button>
            {activeExample && (
              <button
                onClick={handleDeactivateExample}
                className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
              >
                停用当前示例
              </button>
            )}
          </div>

          {/* 导入区域 */}
          {showImport && (
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <h3 className="font-medium mb-2">导入示例数据</h3>
              <textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder="请粘贴导出的JSON数据..."
                className="w-full h-32 p-3 border rounded resize-none"
              />
              <div className="flex gap-2 mt-3">
                <button
                  onClick={handleImportExamples}
                  disabled={!importData.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  确认导入
                </button>
                <button
                  onClick={() => setShowImport(false)}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  取消
                </button>
              </div>
            </div>
          )}

          {/* 当前激活的示例 */}
          {activeExample && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-green-800 mb-2">当前激活示例</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{activeExample.name}</p>
                  <p className="text-sm text-green-600">
                    创建时间：{activeExample.createdAt.toLocaleString()}
                  </p>
                </div>
                <div className="text-sm space-y-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p><strong>剧情点：</strong>{activeExample.plotPoints.length}个</p>
                      <p><strong>冲突等级：</strong>{activeExample.conflictLevel}/5</p>
                      {activeExample.chapterTitle && (
                        <p><strong>章节标题：</strong>{activeExample.chapterTitle}</p>
                      )}
                      {activeExample.plotType && (
                        <p><strong>剧情类型：</strong>{activeExample.plotType}</p>
                      )}
                    </div>
                    <div>
                      <p><strong>整体风格：</strong>{activeExample.overallStyle.slice(0, 50)}...</p>
                      <p><strong>情感基调：</strong>{activeExample.emotionalTone}</p>
                      {activeExample.chapterStyle && (
                        <p><strong>章节风格：</strong>{activeExample.chapterStyle}</p>
                      )}
                      {activeExample.rhythmPhase && (
                        <p><strong>节奏阶段：</strong>{activeExample.rhythmPhase}</p>
                      )}
                    </div>
                  </div>

                  {/* 扩展信息 */}
                  {(activeExample.chapterTechniques?.length || activeExample.themes?.length || activeExample.suspenseElements?.length) && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      {activeExample.chapterTechniques && activeExample.chapterTechniques.length > 0 && (
                        <p><strong>写作技巧：</strong>{activeExample.chapterTechniques.join(', ')}</p>
                      )}
                      {activeExample.themes && activeExample.themes.length > 0 && (
                        <p><strong>主题：</strong>{activeExample.themes.join(', ')}</p>
                      )}
                      {activeExample.suspenseElements && activeExample.suspenseElements.length > 0 && (
                        <p><strong>悬念元素：</strong>{activeExample.suspenseElements.join(', ')}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 示例列表 */}
          <div className="space-y-4">
            <h3 className="font-medium">所有示例 ({examples.length})</h3>
            
            {examples.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无保存的示例
              </div>
            ) : (
              <div className="grid gap-4">
                {examples.map((example) => (
                  <div
                    key={example.id}
                    className={`border rounded-lg p-4 ${
                      example.isActive 
                        ? 'border-green-300 bg-green-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{example.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          创建时间：{example.createdAt.toLocaleString()}
                        </p>
                        
                        <div className="grid grid-cols-2 gap-4 mt-3 text-sm">
                          <div>
                            <p><strong>剧情点：</strong>{example.plotPoints.length}个</p>
                            <p><strong>冲突等级：</strong>{example.conflictLevel}/5</p>
                            {example.chapterTitle && (
                              <p><strong>章节：</strong>{example.chapterTitle}</p>
                            )}
                          </div>
                          <div>
                            <p><strong>整体风格：</strong>{example.overallStyle.slice(0, 30)}...</p>
                            <p><strong>情感基调：</strong>{example.emotionalTone}</p>
                            {example.plotType && (
                              <p><strong>类型：</strong>{example.plotType}</p>
                            )}
                          </div>
                        </div>

                        {/* 额外信息 */}
                        {(example.chapterTechniques?.length || example.themes?.length) && (
                          <div className="mt-2 pt-2 border-t border-gray-100 text-xs text-gray-600">
                            {example.chapterTechniques && example.chapterTechniques.length > 0 && (
                              <p><strong>技巧：</strong>{example.chapterTechniques.slice(0, 2).join(', ')}{example.chapterTechniques.length > 2 ? '...' : ''}</p>
                            )}
                            {example.themes && example.themes.length > 0 && (
                              <p><strong>主题：</strong>{example.themes.slice(0, 2).join(', ')}{example.themes.length > 2 ? '...' : ''}</p>
                            )}
                          </div>
                        )}

                        {example.mainCharacters.length > 0 && (
                          <p className="text-sm mt-2">
                            <strong>主要角色：</strong>{example.mainCharacters.join(', ')}
                          </p>
                        )}
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        {!example.isActive && (
                          <button
                            onClick={() => handleActivateExample(example.id)}
                            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                          >
                            激活
                          </button>
                        )}
                        <button
                          onClick={() => setSelectedExample(example)}
                          className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                        >
                          查看详情
                        </button>
                        <button
                          onClick={() => handleDeleteExample(example.id)}
                          className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 详情对话框 */}
      {selectedExample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">示例详情</h3>
              <button
                onClick={() => setSelectedExample(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
              <h4 className="font-medium mb-2">{selectedExample.name}</h4>
              <div className="space-y-3 text-sm">
                <div>
                  <strong>创建时间：</strong>{selectedExample.createdAt.toLocaleString()}
                </div>
                <div>
                  <strong>整体风格：</strong>{selectedExample.overallStyle}
                </div>
                <div>
                  <strong>情感基调：</strong>{selectedExample.emotionalTone}
                </div>
                <div>
                  <strong>冲突等级：</strong>{selectedExample.conflictLevel}/5
                </div>
                <div>
                  <strong>主要角色：</strong>{selectedExample.mainCharacters.join(', ')}
                </div>
                <div>
                  <strong>剧情点详情：</strong>
                  <div className="mt-2 space-y-2">
                    {selectedExample.plotPoints.map((point, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded">
                        <p><strong>剧情点 {point.order}：</strong>{point.content}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          <strong>避免：</strong>{point.avoidWriting}
                        </p>
                        <p className="text-xs text-gray-600">
                          <strong>推荐：</strong>{point.shouldWriting}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>,
    document.body
  );
};
