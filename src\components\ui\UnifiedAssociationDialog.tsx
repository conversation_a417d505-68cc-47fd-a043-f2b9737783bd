"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';

interface AssociationItem {
  id: string;
  title: string;
  description?: string;
  type: 'chapter' | 'character' | 'terminology' | 'worldBuilding';
  order?: number;
  metadata?: Record<string, any>;
}

interface UnifiedAssociationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  onAssociationsChange: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => void;
}

type ContentType = 'chapter' | 'character' | 'terminology' | 'worldBuilding';

/**
 * 统一关联管理对话框组件
 * 支持管理所有类型的关联内容
 */
export const UnifiedAssociationDialog: React.FC<UnifiedAssociationDialogProps> = ({
  isOpen,
  onClose,
  bookId,
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  onAssociationsChange
}) => {
  const [activeType, setActiveType] = useState<ContentType>('chapter');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [contentItems, setContentItems] = useState<AssociationItem[]>([]);

  // 本地选择状态
  const [localSelectedIds, setLocalSelectedIds] = useState({
    chapterIds: [...selectedChapterIds],
    characterIds: [...selectedCharacterIds],
    terminologyIds: [...selectedTerminologyIds],
    worldBuildingIds: [...selectedWorldBuildingIds]
  });

  // 类型配置
  const typeConfigs = {
    chapter: {
      label: '章节',
      icon: '📖',
      color: 'blue',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-600'
    },
    character: {
      label: '人物',
      icon: '👤',
      color: 'green',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-600'
    },
    terminology: {
      label: '术语',
      icon: '📝',
      color: 'orange',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      textColor: 'text-orange-600'
    },
    worldBuilding: {
      label: '世界观',
      icon: '🌍',
      color: 'purple',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      textColor: 'text-purple-600'
    }
  };

  // 加载内容数据
  const loadContentData = useCallback(async (type: ContentType) => {
    setIsLoading(true);
    try {
      let items: AssociationItem[] = [];

      switch (type) {
        case 'chapter':
          const { chapterRepository } = await import('@/lib/db/repositories');
          const chapters = await chapterRepository.getAllByBookId(bookId);
          items = chapters.map(chapter => ({
            id: chapter.id!,
            title: chapter.title,
            description: `${chapter.wordCount}字 · 第${chapter.order + 1}章`,
            type: 'chapter' as const,
            order: chapter.order
          }));
          break;

        case 'character':
          const { characterRepository } = await import('@/lib/db/repositories');
          const characters = await characterRepository.getAllByBookId(bookId);
          items = characters.map(character => ({
            id: character.id!,
            title: character.name,
            description: character.description,
            type: 'character' as const
          }));
          break;

        case 'terminology':
          const { terminologyRepository } = await import('@/lib/db/repositories');
          const terminologies = await terminologyRepository.getAllByBookId(bookId);
          items = terminologies.map(terminology => ({
            id: terminology.id!,
            title: terminology.name,
            description: terminology.description,
            type: 'terminology' as const
          }));
          break;

        case 'worldBuilding':
          const { worldBuildingRepository } = await import('@/lib/db/repositories');
          const worldBuildings = await worldBuildingRepository.getAllByBookId(bookId);
          items = worldBuildings.map(worldBuilding => ({
            id: worldBuilding.id!,
            title: worldBuilding.name,
            description: worldBuilding.description,
            type: 'worldBuilding' as const
          }));
          break;
      }

      setContentItems(items);
    } catch (error) {
      console.error(`加载${typeConfigs[type].label}数据失败:`, error);
    } finally {
      setIsLoading(false);
    }
  }, [bookId]);

  // 当活动类型改变时加载数据
  useEffect(() => {
    if (isOpen) {
      loadContentData(activeType);
    }
  }, [activeType, isOpen, loadContentData]);

  // 过滤后的内容项
  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) return contentItems;

    const query = searchQuery.toLowerCase();
    return contentItems.filter(item =>
      item.title.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query))
    );
  }, [contentItems, searchQuery]);

  // 获取当前类型的选中ID列表
  const getCurrentSelectedIds = () => {
    switch (activeType) {
      case 'chapter': return localSelectedIds.chapterIds;
      case 'character': return localSelectedIds.characterIds;
      case 'terminology': return localSelectedIds.terminologyIds;
      case 'worldBuilding': return localSelectedIds.worldBuildingIds;
      default: return [];
    }
  };

  // 更新当前类型的选中ID列表
  const updateCurrentSelectedIds = (ids: string[]) => {
    setLocalSelectedIds(prev => ({
      ...prev,
      [`${activeType}Ids`]: ids
    }));
  };

  // 处理项目选择
  const handleItemToggle = (itemId: string) => {
    const currentIds = getCurrentSelectedIds();
    const newIds = currentIds.includes(itemId)
      ? currentIds.filter(id => id !== itemId)
      : [...currentIds, itemId];
    updateCurrentSelectedIds(newIds);
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    const currentIds = getCurrentSelectedIds();
    const allIds = filteredItems.map(item => item.id);
    const newIds = currentIds.length === allIds.length ? [] : allIds;
    updateCurrentSelectedIds(newIds);
  };

  // 处理确认
  const handleConfirm = () => {
    onAssociationsChange(localSelectedIds);
    onClose();
  };

  // 计算统计信息
  const stats = useMemo(() => {
    return {
      chapterCount: localSelectedIds.chapterIds.length,
      characterCount: localSelectedIds.characterIds.length,
      terminologyCount: localSelectedIds.terminologyIds.length,
      worldBuildingCount: localSelectedIds.worldBuildingIds.length,
      total: localSelectedIds.chapterIds.length +
             localSelectedIds.characterIds.length +
             localSelectedIds.terminologyIds.length +
             localSelectedIds.worldBuildingIds.length
    };
  }, [localSelectedIds]);

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50" onClick={onClose}>
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[80vh] max-h-[800px] min-h-[600px] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        {/* 对话框头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h3 className="text-xl font-semibold text-gray-900">管理关联内容</h3>
            <div className="text-sm text-gray-500">
              总计: {stats.total} 项
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧类型选择器 - 优化宽度 */}
          <div className="w-36 border-r border-gray-200 p-3">
            <div className="space-y-2">
              {Object.entries(typeConfigs).map(([type, config]) => {
                const count = stats[`${type}Count` as keyof typeof stats] as number;
                return (
                  <button
                    key={type}
                    onClick={() => setActiveType(type as ContentType)}
                    className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-colors ${
                      activeType === type
                        ? `${config.bgColor} ${config.borderColor} ${config.textColor} border`
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-1.5">
                      <span className="text-sm">{config.icon}</span>
                      <span className="font-medium text-sm">{config.label}</span>
                    </div>
                    {count > 0 && (
                      <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                        activeType === type ? 'bg-white' : 'bg-gray-200'
                      }`}>
                        {count}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* 右侧内容区域 */}
          <div className="flex-1 flex flex-col">
            {/* 搜索和操作栏 */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder={`搜索${typeConfigs[activeType].label}...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  onClick={handleSelectAll}
                  className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors"
                >
                  {getCurrentSelectedIds().length === filteredItems.length ? '取消全选' : '全选'}
                </button>
              </div>
            </div>

            {/* 内容列表 */}
            <div className="flex-1 overflow-y-auto p-4">
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-gray-500">加载中...</div>
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-gray-500">
                    {searchQuery ? '没有找到匹配的内容' : `暂无${typeConfigs[activeType].label}`}
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredItems.map(item => {
                    const isSelected = getCurrentSelectedIds().includes(item.id);
                    return (
                      <div
                        key={item.id}
                        onClick={() => handleItemToggle(item.id)}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          isSelected
                            ? `${typeConfigs[activeType].bgColor} ${typeConfigs[activeType].borderColor} border-2`
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                            isSelected
                              ? `bg-${typeConfigs[activeType].color}-500 border-${typeConfigs[activeType].color}-500`
                              : 'border-gray-300'
                          }`}>
                            {isSelected && (
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">
                              {item.order !== undefined && (
                                <span className="text-gray-500 mr-2">第{item.order + 1}章</span>
                              )}
                              {item.title}
                            </div>
                            {item.description && (
                              <div className="text-sm text-gray-500 mt-1">{item.description}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            已选择 {getCurrentSelectedIds().length} 个{typeConfigs[activeType].label}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              确认 ({stats.total})
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default UnifiedAssociationDialog;
