/**
 * 文本分段工具
 * 将长文本按照指定字数分段，保证句子完整性
 * 
 * @param text 要分段的文本
 * @param charsPerSegment 每段字符数，默认500
 * @param respectSentences 是否保持句子完整性，默认true
 * @returns 分段后的文本数组
 */
export function segmentText(
  text: string,
  charsPerSegment: number = 500,
  respectSentences: boolean = true
): string[] {
  if (!text.trim()) {
    return [];
  }
  
  try {
    // 首先按句子分割文本
    // 匹配中文句号、感叹号、问号，以及英文句号、感叹号、问号后跟空格或行尾的情况
    const sentenceRegex = /([。！？]|[.!?](?:\s|$))/;
    const sentences = text.split(sentenceRegex)
      .reduce((result: string[], part, index, array) => {
        // 将句子和标点符号重新组合
        if (index % 2 === 0 && index < array.length - 1) {
          result.push(part + array[index + 1]);
        } else if (index % 2 === 0 && index === array.length - 1 && part.trim()) {
          // 处理最后一个部分（如果没有以标点符号结尾）
          result.push(part);
        }
        return result;
      }, [])
      .filter(s => s.trim()); // 过滤空句子
    
    const result: string[] = [];
    let currentSegment = '';
    
    for (const sentence of sentences) {
      // 如果当前段落加上这个句子超过了字数限制
      if (respectSentences && currentSegment.length + sentence.length > charsPerSegment && currentSegment.length > 0) {
        // 将当前段落添加到结果中
        result.push(currentSegment);
        // 开始新的段落
        currentSegment = sentence;
      } else {
        // 否则，将句子添加到当前段落
        currentSegment += sentence;
        
        // 如果不需要保持句子完整性，并且当前段落长度超过限制，则强制分段
        if (!respectSentences && currentSegment.length >= charsPerSegment) {
          result.push(currentSegment);
          currentSegment = '';
        }
      }
    }
    
    // 添加最后一个段落（如果有）
    if (currentSegment.length > 0) {
      result.push(currentSegment);
    }
    
    return result;
  } catch (error) {
    console.error('文本分段失败:', error);
    return [text]; // 如果分段失败，返回原文本作为一个段落
  }
}

/**
 * 按段落分段
 * 将文本按照空行分段
 * 
 * @param text 要分段的文本
 * @returns 分段后的文本数组
 */
export function segmentByParagraph(text: string): string[] {
  if (!text.trim()) {
    return [];
  }
  
  // 按空行分割文本
  return text.split(/\n\s*\n/).map(s => s.trim()).filter(s => s);
}

/**
 * 按句子分段
 * 将文本按照句号、问号、感叹号分段
 * 
 * @param text 要分段的文本
 * @returns 分段后的文本数组
 */
export function segmentBySentence(text: string): string[] {
  if (!text.trim()) {
    return [];
  }
  
  // 按句号、问号、感叹号分割文本
  const sentenceRegex = /([。！？]|[.!?](?:\s|$))/;
  return text.split(sentenceRegex)
    .reduce((result: string[], part, index, array) => {
      // 将句子和标点符号重新组合
      if (index % 2 === 0 && index < array.length - 1) {
        result.push(part + array[index + 1]);
      } else if (index % 2 === 0 && index === array.length - 1 && part.trim()) {
        // 处理最后一个部分（如果没有以标点符号结尾）
        result.push(part);
      }
      return result;
    }, [])
    .filter(s => s.trim()); // 过滤空句子
}

/**
 * 按自定义分隔符分段
 * 
 * @param text 要分段的文本
 * @param separator 分隔符（字符串或正则表达式）
 * @returns 分段后的文本数组
 */
export function segmentByCustomSeparator(text: string, separator: string | RegExp): string[] {
  if (!text.trim()) {
    return [];
  }
  
  try {
    // 如果separator是字符串，将其转换为RegExp
    const regex = typeof separator === 'string' ? new RegExp(separator, 'g') : separator;
    return text.split(regex).map(s => s.trim()).filter(s => s);
  } catch (error) {
    console.error('自定义分段失败:', error);
    return [text]; // 如果分段失败，返回原文本作为一个段落
  }
}
