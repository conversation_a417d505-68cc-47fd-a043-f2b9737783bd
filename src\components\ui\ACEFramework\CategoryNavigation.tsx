/**
 * ACE框架分类导航组件
 * 左侧分类列表，支持分类切换和动画效果
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ACEFrameworkCategory, 
  FrameworkCategoryInfo, 
  FRAMEWORK_CATEGORIES,
  getCategoryInfo 
} from '../../../types/ACEFrameworkTypes';

interface CategoryNavigationProps {
  selectedCategory: ACEFrameworkCategory;
  onCategoryChange: (category: ACEFrameworkCategory) => void;
  categoryStats: Record<ACEFrameworkCategory, number>;
  className?: string;
}

export const CategoryNavigation: React.FC<CategoryNavigationProps> = ({
  selectedCategory,
  onCategoryChange,
  categoryStats,
  className = ''
}) => {
  return (
    <div className={`w-48 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 ${className}`}>
      {/* 标题 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          框架分类
        </h3>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          选择框架类型
        </p>
      </div>

      {/* 分类列表 */}
      <div className="p-2">
        {FRAMEWORK_CATEGORIES.map((category) => (
          <CategoryItem
            key={category.id}
            category={category}
            isSelected={selectedCategory === category.id}
            count={categoryStats[category.id] || 0}
            onClick={() => onCategoryChange(category.id)}
          />
        ))}
      </div>
    </div>
  );
};

interface CategoryItemProps {
  category: FrameworkCategoryInfo;
  isSelected: boolean;
  count: number;
  onClick: () => void;
}

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  isSelected,
  count,
  onClick
}) => {
  return (
    <motion.button
      className={`w-full p-3 rounded-lg text-left transition-all duration-200 mb-2 relative overflow-hidden ${
        isSelected
          ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 shadow-sm'
          : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
      }`}
      onClick={onClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      layout
    >
      {/* 选中状态的背景动画 */}
      {isSelected && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20"
          layoutId="selectedBackground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}

      {/* 内容 */}
      <div className="relative z-10">
        {/* 图标和名称 */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center">
            <motion.span 
              className="text-lg mr-2"
              animate={{ 
                scale: isSelected ? 1.1 : 1,
                rotate: isSelected ? 5 : 0 
              }}
              transition={{ duration: 0.2 }}
            >
              {category.icon}
            </motion.span>
            <span className="font-medium text-sm">
              {category.name}
            </span>
          </div>
          
          {/* 数量徽章 */}
          <motion.span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              isSelected
                ? 'bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200'
                : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
            }`}
            animate={{ scale: isSelected ? 1.05 : 1 }}
            transition={{ duration: 0.2 }}
          >
            {count}
          </motion.span>
        </div>

        {/* 描述 */}
        <p className={`text-xs leading-relaxed ${
          isSelected 
            ? 'text-blue-600 dark:text-blue-400' 
            : 'text-gray-500 dark:text-gray-400'
        }`}>
          {category.description}
        </p>
      </div>

      {/* 选中指示器 */}
      {isSelected && (
        <motion.div
          className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500"
          layoutId="selectedIndicator"
          initial={{ scaleY: 0 }}
          animate={{ scaleY: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
      )}
    </motion.button>
  );
};

// 分类图标动画组件
export const CategoryIcon: React.FC<{
  category: ACEFrameworkCategory;
  isSelected: boolean;
  size?: 'sm' | 'md' | 'lg';
}> = ({ category, isSelected, size = 'md' }) => {
  const categoryInfo = getCategoryInfo(category);
  
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <motion.span
      className={`inline-block ${sizeClasses[size]}`}
      animate={{
        scale: isSelected ? 1.2 : 1,
        rotate: isSelected ? 10 : 0,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30
      }}
      style={{ color: categoryInfo.color }}
    >
      {categoryInfo.icon}
    </motion.span>
  );
};

export default CategoryNavigation;
