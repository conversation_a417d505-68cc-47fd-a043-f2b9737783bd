"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BatchSelectButton } from './SentenceCheckbox';

interface SelectionToolbarProps {
  isVisible: boolean;
  selectedCount: number;
  totalCount: number;
  onBatchSelect: (operation: 'all' | 'none' | 'invert') => void;
  onStartAnalysis?: () => void;
  onClearSelection?: () => void;
  onUndoSelection?: () => void;
  canUndo?: boolean;
  isAnalyzing?: boolean;
  className?: string;
}

/**
 * 选择工具栏组件
 * 提供批量选择操作和选择统计显示
 */
export const SelectionToolbar: React.FC<SelectionToolbarProps> = ({
  isVisible,
  selectedCount,
  totalCount,
  onBatchSelect,
  onStartAnalysis,
  onClearSelection,
  onUndoSelection,
  canUndo = false,
  isAnalyzing = false,
  className = ''
}) => {
  const [previousCount, setPreviousCount] = useState(selectedCount);

  // 数字变化动画
  useEffect(() => {
    setPreviousCount(selectedCount);
  }, [selectedCount]);

  if (!isVisible) return null;

  return (
    <motion.div
      className={`bg-emerald-50 border border-emerald-200 rounded-lg p-3 ${className}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* 选择统计 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 0 012 2m-6 9l2 2 4-4" />
          </svg>
          <span className="text-emerald-700 font-medium">已选择</span>
          
          {/* 数字滚动动画 */}
          <div className="relative overflow-hidden">
            <motion.span
              key={selectedCount}
              className="text-emerald-800 font-bold text-lg"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {selectedCount}
            </motion.span>
          </div>
          
          <span className="text-emerald-600">/ {totalCount} 个句子</span>
        </div>

        {/* 进度条 */}
        <div className="flex items-center space-x-2">
          <div className="w-20 h-2 bg-emerald-200 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-emerald-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${totalCount > 0 ? (selectedCount / totalCount) * 100 : 0}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          <span className="text-xs text-emerald-600 font-medium">
            {totalCount > 0 ? Math.round((selectedCount / totalCount) * 100) : 0}%
          </span>
        </div>
      </div>

      {/* 批量操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* 全选按钮 */}
          <BatchSelectButton
            onClick={() => onBatchSelect('all')}
            variant="primary"
            disabled={selectedCount === totalCount}
          >
            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            全选
          </BatchSelectButton>

          {/* 取消全选按钮 */}
          <BatchSelectButton
            onClick={() => onBatchSelect('none')}
            variant="outline"
            disabled={selectedCount === 0}
          >
            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            清空
          </BatchSelectButton>

          {/* 反选按钮 */}
          <BatchSelectButton
            onClick={() => onBatchSelect('invert')}
            variant="secondary"
            disabled={totalCount === 0}
          >
            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            反选
          </BatchSelectButton>

          {/* 撤销按钮 */}
          {canUndo && (
            <BatchSelectButton
              onClick={onUndoSelection}
              variant="outline"
              size="sm"
            >
              <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
              撤销
            </BatchSelectButton>
          )}
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center space-x-2">
          {/* 清除选择按钮 */}
          {onClearSelection && selectedCount > 0 && (
            <BatchSelectButton
              onClick={onClearSelection}
              variant="outline"
              size="sm"
            >
              <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              清除
            </BatchSelectButton>
          )}

          {/* 开始分析按钮 */}
          {onStartAnalysis && (
            <motion.button
              className={`
                inline-flex items-center px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2
                ${selectedCount > 0 && !isAnalyzing
                  ? 'bg-emerald-600 text-white hover:bg-emerald-700 shadow-md'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
              onClick={onStartAnalysis}
              disabled={selectedCount === 0 || isAnalyzing}
              whileHover={selectedCount > 0 && !isAnalyzing ? { scale: 1.02, y: -1 } : {}}
              whileTap={selectedCount > 0 && !isAnalyzing ? { scale: 0.98 } : {}}
            >
              {isAnalyzing ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  分析中...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  开始分析 ({selectedCount})
                </>
              )}
            </motion.button>
          )}
        </div>
      </div>

      {/* 提示信息 */}
      <AnimatePresence>
        {selectedCount === 0 && (
          <motion.div
            className="mt-3 p-2 bg-emerald-100 border border-emerald-200 rounded text-sm text-emerald-700"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              请选择需要修改的句子，然后点击"开始分析"
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

/**
 * 选择统计组件
 * 简化版的统计显示
 */
interface SelectionStatsProps {
  selectedCount: number;
  totalCount: number;
  className?: string;
}

export const SelectionStats: React.FC<SelectionStatsProps> = ({
  selectedCount,
  totalCount,
  className = ''
}) => {
  return (
    <div className={`inline-flex items-center text-sm text-emerald-700 ${className}`}>
      <span className="font-medium">已选择</span>
      <motion.span
        key={selectedCount}
        className="mx-1 font-bold text-emerald-800"
        initial={{ scale: 1.2, color: '#059669' }}
        animate={{ scale: 1, color: '#047857' }}
        transition={{ duration: 0.2 }}
      >
        {selectedCount}
      </motion.span>
      <span>/ {totalCount} 个句子</span>
    </div>
  );
};
