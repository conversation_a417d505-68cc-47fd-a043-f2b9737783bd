/* 文档书写动画 */
@keyframes document-writing {
  0% {
    stroke-dasharray: 0 100;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dasharray: 100 0;
    opacity: 1;
  }
}

.document-line.writing {
  animation: document-writing 0.8s ease-out forwards;
  stroke-dasharray: 0 100;
}

.milestone-glow {
  animation: milestone-pulse 2s ease-in-out infinite;
}

@keyframes milestone-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 靶心射击动画 */
@keyframes target-hit {
  0% {
    stroke-width: 2;
    opacity: 1;
  }
  50% {
    stroke-width: 4;
    opacity: 0.8;
  }
  100% {
    stroke-width: 2;
    opacity: 1;
  }
}

.target-ring.hit {
  animation: target-hit 0.5s ease-out;
}

.target-center.bullseye {
  animation: bullseye-celebration 1.5s ease-out;
}

@keyframes bullseye-celebration {
  0% {
    transform: scale(1);
    fill: currentColor;
  }
  50% {
    transform: scale(1.5);
    fill: #FFD700;
  }
  100% {
    transform: scale(1);
    fill: #FFD700;
  }
}

.celebration-particles .particle {
  animation: particle-burst 1.5s ease-out forwards;
  opacity: 0;
}

@keyframes particle-burst {
  0% {
    opacity: 1;
    transform: rotate(var(--rotation, 0deg)) translateY(0);
  }
  100% {
    opacity: 0;
    transform: rotate(var(--rotation, 0deg)) translateY(-25px);
  }
}

/* 闪电能量动画 */
@keyframes lightning-energy {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 currentColor);
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 8px currentColor);
  }
}

.lightning-bolt.energized {
  animation: lightning-energy 1s ease-in-out infinite;
}

.energy-wave {
  animation: energy-ripple 2s ease-out infinite;
  transform-origin: center;
}

@keyframes energy-ripple {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* 时钟动画 */
.hour-hand, .minute-hand {
  transition: transform 1s ease-in-out;
  transform-origin: 12px 12px;
}

.focus-ring {
  animation: focus-pulse 3s ease-in-out infinite;
}

@keyframes focus-pulse {
  0%, 100% {
    opacity: 0.3;
    stroke-width: 1;
  }
  50% {
    opacity: 0.7;
    stroke-width: 2;
  }
}

/* 番茄钟动画 */
.pomodoro-face.heartbeat {
  animation: pomodoro-heartbeat 2s ease-in-out infinite;
  transform-origin: center;
}

@keyframes pomodoro-heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.pomodoro-hand.ticking {
  animation: hand-tick 1s linear infinite;
  transform-origin: 12px 12px;
}

@keyframes hand-tick {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(6deg);
  }
}

.pomodoro-pulse {
  animation: pomodoro-pulse-wave 2s ease-out infinite;
  transform-origin: center;
}

@keyframes pomodoro-pulse-wave {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 通用动画效果 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  .document-line.writing,
  .target-ring.hit,
  .target-center.bullseye,
  .celebration-particles .particle,
  .lightning-bolt.energized,
  .energy-wave,
  .focus-ring,
  .pomodoro-face.heartbeat,
  .pomodoro-hand.ticking,
  .pomodoro-pulse,
  .milestone-glow {
    animation: none;
  }
  
  .hour-hand, .minute-hand {
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .energy-wave,
  .pomodoro-pulse,
  .focus-ring,
  .milestone-glow {
    opacity: 0.8;
    stroke-width: 2;
  }
}
