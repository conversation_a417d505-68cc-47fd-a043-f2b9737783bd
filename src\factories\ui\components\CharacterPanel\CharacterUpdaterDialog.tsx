"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import CharacterAIAdapter from '@/adapters/ai/CharacterAIAdapter';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { createAIFactory } from '@/factories/ai/AIFactory';
import createMessageBuilder from '@/utils/ai/MessageBuilder';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface CharacterUpdaterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  character: Character;
  onUpdateCharacter: (updatedCharacter: Character) => void;
  bookId: string;
}

/**
 * 人物整体更新对话框组件
 * 用于使用AI整体更新人物信息
 */
const CharacterUpdaterDialog: React.FC<CharacterUpdaterDialogProps> = ({
  isOpen,
  onClose,
  character,
  onUpdateCharacter,
  bookId
}) => {
  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);

  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 章节搜索
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredChapters, setFilteredChapters] = useState<GenericChapter[]>([]);

  // 更新设置
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // 更新状态
  const [isLoading, setIsLoading] = useState(false);
  const [updatedCharacter, setUpdatedCharacter] = useState<Character | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 创建人物AI适配器
  const characterAIAdapter = new CharacterAIAdapter();

  // 当对话框打开时加载章节
  useEffect(() => {
    if (isOpen) {
      loadChapters();
    }
  }, [isOpen, bookId]);

  // 当搜索查询变化时过滤章节
  useEffect(() => {
    if (chapters.length > 0) {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const filtered = chapters.filter(chapter =>
          (chapter.title?.toLowerCase().includes(query) || false) ||
          (chapter.content?.toLowerCase().includes(query) || false)
        );
        setFilteredChapters(filtered);
      } else {
        setFilteredChapters(chapters);
      }
    }
  }, [searchQuery, chapters]);

  /**
   * 加载章节列表
   */
  const loadChapters = async () => {
    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 chapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          setFilteredChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          setFilteredChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
      setFilteredChapters([]);
    } catch (error) {
      console.error('加载章节失败:', error);
      setError('加载章节失败');
      setChapters([]);
      setFilteredChapters([]);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  /**
   * 处理章节选择
   * @param chapterId 章节ID
   */
  const handleChapterSelect = (chapterId: string | undefined) => {
    if (chapterId === undefined) return;

    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  /**
   * 全选/取消全选章节
   */
  const handleSelectAllChapters = () => {
    if (selectedChapterIds.length === filteredChapters.length) {
      setSelectedChapterIds([]);
    } else {
      // 过滤掉undefined的ID
      const validChapterIds = filteredChapters
        .map(chapter => chapter.id)
        .filter((id): id is string => id !== undefined);
      setSelectedChapterIds(validChapterIds);
    }
  };

  /**
   * 选择章节范围
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect') => {
    // 验证输入
    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end)) {
      setError('请输入有效的章节编号');
      return;
    }

    if (start > end) {
      setError('起始章节编号不能大于结束章节编号');
      return;
    }

    if (start < 1 || end > chapters.length) {
      setError(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID，过滤掉undefined的ID
    const chapterIds = chaptersInRange
      .map(chapter => chapter.id)
      .filter((id): id is string => id !== undefined);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  /**
   * 更新人物
   */
  const updateCharacter = async () => {
    setIsLoading(true);
    setError(null);
    setUpdatedCharacter(null);

    try {
      // 获取API设置
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();
      const currentProvider = apiSettings.getCurrentProvider();
      // 获取当前模型（虽然当前未使用，但保留以便将来可能的扩展）
      // const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        setError(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
        setIsLoading(false);
        return;
      }

      // 如果是自定义提供商，还需要检查API端点
      if (currentProvider === 'custom' && !apiEndpoint) {
        setError('请先在设置中配置自定义API端点');
        setIsLoading(false);
        return;
      }

      // 获取选中章节的内容
      let combinedContent = '';
      if (selectedChapterIds.length > 0) {
        console.log('选中的章节ID:', selectedChapterIds);

        // 获取选中章节的内容
        const selectedChapters = chapters.filter(chapter =>
          chapter.id !== undefined && selectedChapterIds.includes(chapter.id)
        );
        console.log('选中的章节数量:', selectedChapters.length);

        for (const chapter of selectedChapters) {
          if (chapter.content) {
            combinedContent += `# ${chapter.title || '无标题章节'}\n\n${chapter.content}\n\n`;
            console.log(`添加章节内容: ${chapter.title || '无标题章节'}, 内容长度: ${chapter.content.length}`);
          } else {
            console.warn(`章节 ${chapter.id} (${chapter.title || '无标题章节'}) 没有内容`);
          }
        }

        console.log('合并后的章节内容长度:', combinedContent.length);
      }

      // 使用AI更新人物
      const result = await characterAIAdapter.updateCharacterWithAI(
        character,
        combinedContent,
        customPrompt
      );

      // 保存更新后的人物
      setUpdatedCharacter(result);
      console.log('更新后的人物:', result);

      // 如果有选中章节，保存章节关联
      if (selectedChapterIds.length > 0 && character.id) {
        try {
          // 保存章节关联
          await characterAIAdapter.saveCharacterChapterAssociation(character, selectedChapterIds);
          console.log('保存章节关联成功');
        } catch (error) {
          console.error('保存章节关联失败:', error);
        }
      }
    } catch (error: any) {
      console.error('更新人物失败:', error);
      setError('更新人物失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消更新
   */
  const cancelUpdate = () => {
    // 取消AI请求
    characterAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  /**
   * 应用更新结果
   */
  const applyUpdateResult = async () => {
    if (!updatedCharacter) {
      setError('没有更新的人物数据');
      return;
    }

    try {
      // 保存更新后的人物
      onUpdateCharacter(updatedCharacter);

      // 关闭对话框
      onClose();
    } catch (error) {
      console.error('保存人物失败:', error);
      setError('保存人物失败');
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-4/5 max-w-4xl max-h-[90vh] flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-blue-700">AI整体更新人物: {character.name}</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 */}
        <div className="flex-1 overflow-auto p-4">
          {/* 更新表单 */}
          {!isLoading && !updatedCharacter && (
            <div className="space-y-6">
              {/* 自定义提示词 */}
              <div>
                <label htmlFor="custom-prompt" className="block text-sm font-medium text-gray-700 mb-1">
                  自定义提示词（可选）
                </label>
                <textarea
                  id="custom-prompt"
                  value={customPrompt}
                  onChange={e => setCustomPrompt(e.target.value)}
                  className="w-full p-2 border rounded-lg"
                  rows={3}
                  placeholder="输入自定义提示词，例如：请更新人物的性格和目标，重点关注其内心冲突的发展"
                />
              </div>

              {/* 章节选择 */}
              <div>
                <div className="mb-2">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">选择关联章节（可选）</h3>
                    <button
                      className="text-sm text-blue-600 hover:text-blue-800"
                      onClick={handleSelectAllChapters}
                    >
                      {selectedChapterIds.length === filteredChapters.length ? '取消全选' : '全选'}
                    </button>
                  </div>

                  {/* 搜索框 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">搜索章节:</span>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="输入章节标题或内容关键词"
                        className="w-full p-1 text-sm border rounded"
                      />
                    </div>
                    {searchQuery && (
                      <button
                        className="px-2 py-1 text-sm text-gray-600 hover:text-gray-800"
                        onClick={() => setSearchQuery('')}
                      >
                        清除
                      </button>
                    )}
                    <div className="text-xs text-gray-500">
                      {searchQuery ? `找到: ${filteredChapters.length}/${chapters.length}` : ''}
                    </div>
                  </div>

                  {/* 范围选择 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">范围选择:</span>
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <button
                      className="px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                      onClick={() => handleRangeSelect('select')}
                    >
                      选择
                    </button>
                    <button
                      className="px-2 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                      onClick={() => handleRangeSelect('deselect')}
                    >
                      取消选择
                    </button>
                  </div>

                  {/* 章节列表 */}
                  <div className="max-h-60 overflow-y-auto border rounded-lg">
                    {isLoadingChapters ? (
                      <div className="p-4 text-center text-gray-500">加载章节中...</div>
                    ) : filteredChapters.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">没有找到章节</div>
                    ) : (
                      filteredChapters.map((chapter) => {
                        // 跳过没有ID的章节
                        if (chapter.id === undefined) return null;

                        return (
                          <div
                            key={chapter.id}
                            className={`p-2 flex items-center ${
                              selectedChapterIds.includes(chapter.id)
                                ? 'bg-blue-100 border border-blue-200'
                                : 'hover:bg-gray-100 border border-transparent'
                            }`}
                          >
                            <input
                              type="checkbox"
                              id={`chapter-${chapter.id}`}
                              checked={selectedChapterIds.includes(chapter.id)}
                              onChange={() => handleChapterSelect(chapter.id)}
                              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                            />
                            <label
                              htmlFor={`chapter-${chapter.id}`}
                              className={`flex-1 cursor-pointer ${
                                selectedChapterIds.includes(chapter.id) ? 'font-medium text-blue-800' : 'text-gray-700'
                              }`}
                            >
                              {chapter.title || (chapter.order !== undefined ? `章节 ${chapter.order + 1}` : '无标题章节')}
                            </label>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 加载中 */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-lg text-gray-700">正在更新人物...</p>
              <p className="text-sm text-gray-500 mt-2">这可能需要一些时间，请耐心等待</p>
            </div>
          )}

          {/* 更新结果 */}
          {!isLoading && updatedCharacter && (
            <div className="space-y-4">
              <h3 className="font-bold text-lg">更新后的人物: {updatedCharacter.name}</h3>

              <div className="border p-4 rounded-lg bg-gray-50">
                {/* 显示更新后的人物信息 */}
                {[
                  { key: 'description', name: '描述' },
                  { key: 'appearance', name: '外貌' },
                  { key: 'personality', name: '性格' },
                  { key: 'background', name: '背景' },
                  { key: 'goals', name: '目标' },
                  { key: 'characterArchetype', name: '角色原型' },
                  { key: 'growthArc', name: '成长弧线' },
                  { key: 'hiddenMotivation', name: '隐藏动机' },
                  { key: 'secretHistory', name: '秘密历史' },
                  { key: 'innerConflicts', name: '内心冲突' },
                  { key: 'symbolism', name: '象征意义' }
                ].map(field => {
                  const originalValue = (character as any)[field.key];
                  const updatedValue = (updatedCharacter as any)[field.key];
                  const hasChanged = originalValue !== updatedValue;

                  return (
                    <div key={field.key} className="mb-4">
                      <h4 className={`font-semibold ${hasChanged ? 'text-blue-700' : 'text-gray-700'}`}>
                        {field.name}
                        {hasChanged && <span className="ml-2 text-xs text-blue-600">(已更新)</span>}
                      </h4>
                      <p className={`whitespace-pre-wrap ${hasChanged ? 'text-blue-600' : 'text-gray-600'}`}>
                        {updatedValue || '无'}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-2">
          {!isLoading && !updatedCharacter ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                onClick={updateCharacter}
              >
                更新人物
              </button>
            </>
          ) : !isLoading && updatedCharacter ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={() => setUpdatedCharacter(null)}
              >
                返回编辑
              </button>
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={applyUpdateResult}
              >
                应用更新
              </button>
            </>
          ) : (
            <button
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelUpdate}
            >
              取消更新
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CharacterUpdaterDialog;
