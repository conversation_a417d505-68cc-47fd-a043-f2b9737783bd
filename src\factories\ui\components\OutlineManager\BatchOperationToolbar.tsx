"use client";

import React from 'react';
import { Node } from 'reactflow';

interface BatchOperationToolbarProps {
  selectedNodes: Node[];
  onBatchDelete: () => void;
  onBatchCopy: () => void;
  onClearSelection: () => void;
  onSelectSimilar?: () => void;
}

const BatchOperationToolbar: React.FC<BatchOperationToolbarProps> = ({
  selectedNodes,
  onBatchDelete,
  onBatchCopy,
  onClearSelection,
  onSelectSimilar
}) => {
  if (selectedNodes.length === 0) return null;

  return (
    <div
      className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-white rounded-lg shadow-xl border border-gray-200 px-4 py-3 flex items-center space-x-4"
      style={{
        animation: 'slideInFromTopBounce 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)',
        backdropFilter: 'blur(12px)',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
      }}
    >
      {/* 选中数量显示 */}
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span className="text-sm font-medium text-gray-700">
          已选中 <span className="text-blue-600 font-semibold">{selectedNodes.length}</span> 个节点
        </span>
      </div>

      {/* 分隔线 */}
      <div className="w-px h-6 bg-gray-300"></div>

      {/* 操作按钮组 */}
      <div className="flex items-center space-x-2">
        {/* 选择相似节点 */}
        {onSelectSimilar && selectedNodes.length === 1 && (
          <button
            className="px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors duration-200 flex items-center space-x-1"
            onClick={onSelectSimilar}
            title="选择相同类型的节点"
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <span>选择相似</span>
          </button>
        )}

        {/* 批量复制 */}
        <button
          className="px-3 py-1.5 text-sm font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-md transition-all duration-200 flex items-center space-x-1 hover:scale-105 hover:shadow-md"
          onClick={onBatchCopy}
          title={`复制选中的 ${selectedNodes.length} 个节点`}
          style={{ animation: 'fadeInLeft 0.3s ease-out 0.1s both' }}
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <span>批量复制</span>
        </button>

        {/* 批量删除 */}
        <button
          className="px-3 py-1.5 text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-all duration-200 flex items-center space-x-1 hover:scale-105 hover:shadow-md"
          onClick={onBatchDelete}
          title={`删除选中的 ${selectedNodes.length} 个节点`}
          style={{ animation: 'fadeInLeft 0.3s ease-out 0.2s both' }}
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          <span>批量删除</span>
        </button>

        {/* 取消选择 */}
        <button
          className="px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-md transition-all duration-200 flex items-center space-x-1 hover:scale-105 hover:shadow-md"
          onClick={onClearSelection}
          title="取消选择"
          style={{ animation: 'fadeInLeft 0.3s ease-out 0.3s both' }}
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span>取消</span>
        </button>
      </div>

      <style jsx>{`
        @keyframes slideInFromTopBounce {
          0% {
            opacity: 0;
            transform: translate(-50%, -30px) scale(0.9);
          }
          60% {
            opacity: 1;
            transform: translate(-50%, 5px) scale(1.02);
          }
          100% {
            opacity: 1;
            transform: translate(-50%, 0) scale(1);
          }
        }

        @keyframes fadeInLeft {
          from {
            opacity: 0;
            transform: translateX(-10px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }

        .pulse-animation {
          animation: pulse 2s infinite;
        }
      `}</style>
    </div>
  );
};

export default BatchOperationToolbar;
