"use client";

import React, { useState, memo, useRef, useEffect, useMemo, useCallback } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { OutlineNodeType } from '../../types/outline';
import NodeMenu from './NodeMenu';
import InlineNodeEditor from './InlineNodeEditor';
import { SynopsisFields } from './assistant/SynopsisAIService';
import '../../../../styles/animated-checkbox.css';

// 高级动画checkbox组件 - 带有SVG路径重绘动画
const AnimatedCheckbox: React.FC<{
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
}> = ({ checked, onChange, className = "" }) => {
  const [animationKey, setAnimationKey] = useState(0);
  const pathRef = useRef<SVGPathElement>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Checkbox clicked, current checked:', checked, 'will change to:', !checked);
    onChange(!checked);
  };

  // 触发路径重绘动画
  useEffect(() => {
    if (checked && pathRef.current) {
      // 重置动画key来触发重新渲染
      setAnimationKey(prev => prev + 1);

      // 获取路径长度并设置动画
      const pathLength = pathRef.current.getTotalLength();
      pathRef.current.style.strokeDasharray = `${pathLength}`;
      pathRef.current.style.strokeDashoffset = `${pathLength}`;

      // 触发动画
      requestAnimationFrame(() => {
        if (pathRef.current) {
          pathRef.current.style.transition = 'stroke-dashoffset 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)';
          pathRef.current.style.strokeDashoffset = '0';
        }
      });
    }
  }, [checked]);

  return (
    <div
      className={`animated-checkbox ${className}`}
      onClick={handleClick}
      onMouseDown={(e) => e.stopPropagation()}
      onMouseUp={(e) => e.stopPropagation()}
    >
      {/* 复选框背景 */}
      <div className={`checkbox-background ${checked ? 'checked' : 'unchecked'}`}>

        {/* 勾选动画背景波纹 */}
        {checked && (
          <div
            key={`ripple-${animationKey}`}
            className="checkbox-ripple"
          />
        )}

        {/* SVG勾选标记 - 带路径重绘动画 */}
        {checked && (
          <svg
            key={`check-${animationKey}`}
            className="w-3 h-3 text-white checkbox-icon"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              ref={pathRef}
              className="checkbox-path"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={3}
              d="M5 13l4 4L19 7"
            />
          </svg>
        )}

        {/* 庆祝粒子效果 */}
        {checked && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <div
                key={`particle-${animationKey}-${i}`}
                className="checkbox-particle"
                style={{
                  transform: `translate(-50%, -50%) rotate(${i * 60}deg) translateY(-8px)`,
                  animationDelay: `${0.4 + i * 0.1}s`
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

interface OutlineCanvasNodeProps extends NodeProps {
  data: OutlineNodeType & {
    onEdit: (nodeId: string) => void;
    onDelete: (nodeId: string) => void;
    onAddChild: (parentId: string, type: string) => void;
    onAIAppend?: (parentNode: OutlineNodeType, customPrompt?: string) => void; // 添加AI追加回调
    onSynopsisAI?: (synopsisData: SynopsisFields) => void; // 新增：Synopsis AI回调
    onSelect?: (nodeId: string) => void;
    onUpdate?: (updatedNode: OutlineNodeType) => void; // 添加更新节点回调
    selected?: boolean; // 使用selected替代isSelected，与ReactFlow保持一致
    // 新增选择模式相关props
    selectionMode?: boolean;
    isNodeSelected?: boolean;
    onNodeSelectionChange?: (nodeId: string, selected: boolean) => void;
    allNodes?: OutlineNodeType[]; // 传入所有节点，用于VolumeEditor的章节选择
    // 🔥 新增：折叠相关props
    isCollapsed?: boolean;
    childrenCount?: number;
    collapsedChildrenTypes?: { [key: string]: number };
    onToggleCollapse?: (nodeId: string) => void;
    // 新增：ACE框架相关props
    availableFrameworks?: any[];
  };
}

/**
 * 自定义大纲节点组件
 * 用于在画布视图中显示大纲节点
 * 支持就地编辑功能，提高交互体验
 */
const OutlineCanvasNode: React.FC<OutlineCanvasNodeProps> = ({ data, isConnectable }) => {

  // 状态管理
  const [showMenu, setShowMenu] = useState(false);
  const [showAddMenu, setShowAddMenu] = useState(false);
  const [isInlineEditing, setIsInlineEditing] = useState(false); // 是否处于就地编辑状态

  // 添加调试日志来跟踪选择状态变化
  useEffect(() => {
    if (data.selectionMode) {
      console.log(`OutlineCanvasNode ${data.id} render:`, {
        isNodeSelected: data.isNodeSelected,
        selectionMode: data.selectionMode,
        hasCallback: !!data.onNodeSelectionChange
      });
    }
  }, [data.id, data.isNodeSelected, data.selectionMode, data.onNodeSelectionChange]);

  // 获取节点类型图标
  const getNodeIcon = () => {
    switch (data.type) {
      case 'volume':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'event':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      case 'phaseGroup':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'chapter':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'plot':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
          </svg>
        );
      case 'dialogue':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'synopsis':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
    }
  };

  // 获取节点类型标签
  const getNodeTypeLabel = () => {
    switch (data.type) {
      case 'volume':
        return '总纲/卷';
      case 'event':
        return '事件刚';
      case 'phaseGroup':
        return '阶段分组';
      case 'chapter':
        return '章节';
      case 'plot':
        return '剧情节点';
      case 'dialogue':
        return '对话节点';
      case 'synopsis':
        return '核心故事梗概';
      default:
        return data.type || '未知';
    }
  };

  // 获取节点类型对应的颜色变量
  const getNodeColorVar = () => {
    switch (data.type) {
      case 'volume':
        return 'info'; // 紫色
      case 'event':
        return 'warning'; // 橙色/金色
      case 'phaseGroup':
        return 'success'; // 绿色
      case 'chapter':
        return 'primary'; // 蓝色
      case 'plot':
        return 'secondary'; // 橙色
      case 'dialogue':
        return 'warning'; // 黄色
      case 'synopsis':
        return 'info'; // 紫色
      default:
        return 'info';
    }
  };

  // 使用useMemo优化节点样式计算
  const nodeStyle = useMemo(() => {
    // 获取节点类型对应的RGB颜色变量（用于阴影和背景）
    const colorVarRgb = data.type === 'chapter'
      ? 'var(--outline-primary-rgb)'
      : data.type === 'plot'
        ? 'var(--outline-secondary-rgb)'
        : 'var(--outline-info-rgb)';

    // 获取节点类型对应的背景色
    const bgColor = data.type === 'chapter'
      ? 'rgba(239, 246, 255, 0.7)' // 浅蓝色背景
      : data.type === 'plot'
        ? 'rgba(255, 247, 237, 0.7)' // 浅橙色背景
        : 'rgba(236, 253, 245, 0.7)'; // 浅绿色背景

    // 获取节点类型对应的纹理背景 - 增加微妙的纹理效果
    const texturePattern = data.type === 'chapter'
      ? 'radial-gradient(circle at 100% 100%, rgba(59, 130, 246, 0.03) 0%, transparent 60%)' // 蓝色纹理
      : data.type === 'plot'
        ? 'radial-gradient(circle at 100% 100%, rgba(249, 115, 22, 0.03) 0%, transparent 60%)' // 橙色纹理
        : 'radial-gradient(circle at 100% 100%, rgba(16, 185, 129, 0.03) 0%, transparent 60%)'; // 绿色纹理

    // 基础样式 - 光晕边框效果，移除传统边框，使用阴影组合创造边界感
    const baseStyle = {
      borderWidth: '0', // 移除传统边框
      backgroundColor: 'white',
      // 使用更温和的过渡效果
      transition: 'box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
      // 移除可能干扰拖拽的属性
      // willChange: 'transform',
      // transform: 'translate3d(0, 0, 0)'
    };

    // 获取节点类型对应的光晕颜色
    const glowColor = data.type === 'chapter'
      ? '59, 130, 246' // 蓝色光晕
      : data.type === 'plot'
        ? '249, 115, 22' // 橙色光晕
        : '16, 185, 129'; // 绿色光晕

    // 基础样式 - 使用阴影组合创造精致的边界感
    const style = {
      ...baseStyle,
      boxShadow: `
        0 4px 12px -2px rgba(0, 0, 0, 0.07),
        0 2px 6px -1px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(${glowColor}, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset,
        0 0 20px rgba(${glowColor}, 0.03) inset
      `,
      backgroundImage: `linear-gradient(to bottom, white, ${bgColor}), ${texturePattern}`,
      borderRadius: '14px', // 增加圆角
    };

    // 如果节点被选中，增强视觉效果 - 使用光晕效果替代传统边框
    if (data.selected) {
      return {
        ...style,
        backgroundColor: `rgba(${colorVarRgb}, 0.03)`,
        backgroundImage: `linear-gradient(to bottom, white, ${bgColor}), ${texturePattern}`,
        // 增强光晕效果，创造焦点感
        boxShadow: `
          0 6px 16px -3px rgba(0, 0, 0, 0.1),
          0 4px 8px -2px rgba(0, 0, 0, 0.06),
          0 0 0 2px rgba(${glowColor}, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.15) inset,
          0 0 25px rgba(${glowColor}, 0.08) inset
        `,
        transform: 'scale(1.03)',
      };
    }

    return style;
  }, [data.type, data.selected]); // 添加依赖项，只在类型或选中状态变化时重新计算

  // 处理编辑点击 - 使用useCallback优化性能
  const handleEditClick = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    console.log(`编辑节点: ${data.id}, 标题: ${data.title}`);

    // 使用就地编辑模式
    setIsInlineEditing(true);

    // 通知父组件节点被选中
    if (data.onSelect) {
      data.onSelect(data.id);
    }

    setShowMenu(false);
  }, [data.id, data.title, data.onSelect, setIsInlineEditing, setShowMenu]);

  // 处理就地编辑保存 - 使用useCallback优化性能
  const handleInlineUpdate = useCallback((updatedNode: OutlineNodeType) => {
    console.log(`就地编辑保存: ${updatedNode.id}, 新标题: ${updatedNode.title}`);

    // 调用父组件的更新回调
    if (data.onUpdate) {
      data.onUpdate(updatedNode);
    }

    // 关闭就地编辑模式
    setIsInlineEditing(false);
  }, [data.onUpdate, setIsInlineEditing]);

  // 处理就地编辑取消 - 使用useCallback优化性能
  const handleInlineCancel = useCallback(() => {
    setIsInlineEditing(false);
  }, [setIsInlineEditing]);

  // 处理删除点击 - 使用useCallback优化性能
  const handleDeleteClick = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    console.log(`准备删除节点: ${data.id}, 标题: ${data.title}`);

    // 如果有子节点，显示警告
    if (data.children && Array.isArray(data.children) && data.children.length > 0) {
      console.warn(`该节点有 ${data.children.length} 个子节点将被删除`);
    }

    data.onDelete(data.id);
    setShowMenu(false);
  }, [data.id, data.title, data.children, data.onDelete, setShowMenu]);

  // 处理添加子节点点击 - 使用useCallback优化性能
  const handleAddChildClick = useCallback((type: string) => (e: React.MouseEvent | Event) => {
    if ('stopPropagation' in e) {
      e.stopPropagation();
    }

    // 确保父节点ID不为空
    if (!data.id) {
      console.error('父节点ID为空，无法添加子节点');
      return;
    }

    console.log(`添加子节点: 父节点ID=${data.id}, 类型=${type}`);

    // 直接添加子节点，无需确认
    data.onAddChild(data.id, type);

    // 直接设置状态
    setShowAddMenu(false);
    setShowMenu(false);
  }, [data.id, data.onAddChild, setShowAddMenu, setShowMenu]);

  // 处理AI追加节点 - 使用useCallback优化性能
  const handleAIAppendClick = useCallback((parentNode: OutlineNodeType, customPrompt?: string) => {
    console.log(`🎯 OutlineCanvasNode: AI追加节点被调用`);
    console.log(`   父节点ID=${parentNode.id}, 类型=${parentNode.type}`);
    console.log(`   自定义提示词=${customPrompt ? '是' : '否'}`);
    console.log(`   onAIAppend函数存在=${!!data.onAIAppend}`);

    if (data.onAIAppend) {
      data.onAIAppend(parentNode, customPrompt);
    } else {
      console.error('❌ onAIAppend函数不存在！');
    }

    setShowMenu(false);
  }, [data.onAIAppend, setShowMenu]);

  // 处理Synopsis AI功能 - 使用useCallback优化性能
  const handleSynopsisAIClick = useCallback((synopsisData: SynopsisFields) => {
    console.log(`🧠 OutlineCanvasNode: Synopsis AI被调用`);
    console.log(`   节点ID=${data.id}, 类型=${data.type}`);
    console.log(`   onSynopsisAI函数存在=${!!data.onSynopsisAI}`);
    console.log(`   Synopsis数据:`, synopsisData);

    if (data.onSynopsisAI) {
      // 添加节点ID到synopsisData中，以便父组件知道要更新哪个节点
      const synopsisDataWithNodeId = {
        ...synopsisData,
        nodeId: data.id
      };
      data.onSynopsisAI(synopsisDataWithNodeId);
    } else {
      console.error('❌ onSynopsisAI函数不存在！');
    }

    setShowMenu(false);
  }, [data.id, data.type, data.onSynopsisAI, setShowMenu]);

  // 创建一个ref用于节点元素
  const nodeRef = useRef<HTMLDivElement>(null);

  // 用于菜单位置计算
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // 如果菜单打开且点击的不是菜单内部元素，则关闭菜单
      if (showMenu &&
          !(e.target as Element).closest('.menu-container') &&
          !(e.target as Element).closest('.add-child-menu')) {
        setShowMenu(false);
        setShowAddMenu(false);
      }
    };

    // 添加全局点击事件监听
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu, showAddMenu]);



  return (
    <>
      {/* 输入连接点 - 改进视觉效果和交互体验 */}
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`transition-all duration-300 ${data.selected ? 'scale-125' : 'hover:scale-125'}`}
        style={{
          background: `var(--outline-${getNodeColorVar()})`,
          width: '14px',
          height: '14px',
          border: '3px solid white',
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.2)',
          zIndex: 10,
          top: '-7px',
          borderRadius: '50%',
          cursor: 'crosshair',
          opacity: data.selected ? 1 : 0.8,
        }}
      >
        {/* 连接点提示 */}
        <div
          className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-1 bg-black bg-opacity-75 text-white text-xs rounded px-2 py-1 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity"
          style={{
            whiteSpace: 'nowrap',
            visibility: 'hidden',
          }}
        >
          连接到此节点
        </div>
      </Handle>

      {/* 选择模式下的选择框 */}
      {data.selectionMode && (
        <div className="absolute top-2 right-2 z-20">
          <AnimatedCheckbox
            key={`${data.id}-${data.isNodeSelected}`} // 添加key强制重渲染
            checked={data.isNodeSelected || false}
            onChange={(checked) => {
              console.log('Node selection change:', data.id, checked, 'callback exists:', !!data.onNodeSelectionChange);
              if (data.onNodeSelectionChange) {
                data.onNodeSelectionChange(data.id, checked);
              } else {
                console.error('onNodeSelectionChange callback not found for node:', data.id);
              }
            }}
            className="shadow-lg"
          />
        </div>
      )}

      {/* 节点内容 */}
      <div
        ref={nodeRef}
        className={`px-5 py-4 w-[300px] transition-all duration-300 relative ${
          data.selected
            ? 'scale-105 node-select'
            : 'hover:translate-y-[-3px]'
        } ${data.isNew ? 'node-create' : ''} ${
          data.selectionMode && data.isNodeSelected
            ? 'ring-2 ring-blue-400 ring-opacity-75 shadow-lg shadow-blue-200'
            : ''
        }`}
        style={{
          ...nodeStyle,
          zIndex: data.selected ? 10 : 1
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault(); // 阻止默认行为

          // 更新选中状态
          if (data.onSelect) {
            data.onSelect(data.id);
          }
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          // 双击使用就地编辑模式
          handleEditClick(e);
        }}
        onContextMenu={(e) => {
          e.stopPropagation();
          e.preventDefault();
          // 右键显示菜单
          if (nodeRef.current) {
            const rect = nodeRef.current.getBoundingClientRect();
            setMenuPosition({
              x: rect.right + 5,
              y: rect.top
            });
          }
          setShowMenu(true);
          setShowAddMenu(false);
        }}
      >
        <div className="flex items-center node-body">
          {/* 🔥 新增：左侧折叠控制区域 */}
          <div className="flex items-center mr-2">
            {/* 折叠/展开按钮 */}
            {data.childrenCount && data.childrenCount > 0 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  console.log('🔍 折叠按钮被点击:', data.id, '当前状态:', data.isCollapsed);
                  if (data.onToggleCollapse) {
                    data.onToggleCollapse(data.id);
                  } else {
                    console.error('❌ onToggleCollapse函数不存在！');
                  }
                }}
                className="flex items-center justify-center w-5 h-5 rounded bg-gray-100 hover:bg-gray-200 transition-all duration-200 mr-1"
                title={data.isCollapsed ? '展开子节点' : '折叠子节点'}
              >
                <svg
                  className={`w-3 h-3 text-gray-600 transition-transform duration-200 ${
                    data.isCollapsed ? 'rotate-0' : 'rotate-90'
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            )}
          </div>

          {/* 节点图标 - 增强视觉效果 */}
          <div
            className="flex-shrink-0 mr-3 flex items-center justify-center w-8 h-8 rounded-lg text-white node-icon relative"
            style={{
              background: `var(--outline-${getNodeColorVar()})`,
              transform: data.selected ? 'scale(1.1)' : 'scale(1)',
              transition: 'all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1)', // 弹性动画效果
              boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset, 0 0 0 1px rgba(0, 0, 0, 0.05)`,
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
          >
            <div className="text-lg" style={{ transform: 'translateY(1px)' }}>
              {getNodeIcon()}
            </div>

            {/* 🔥 新增：子节点数量徽章 */}
            {data.childrenCount && data.childrenCount > 0 && (
              <div
                className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center"
                style={{
                  fontSize: '10px',
                  lineHeight: '1',
                  minWidth: '16px',
                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)',
                }}
              >
                {data.childrenCount}
              </div>
            )}
          </div>

          {/* 节点标题区域 - 增强视觉层次感 */}
          <div className="flex-1 node-title-area">
            <div
              className={`font-semibold truncate text-sm mb-1 ${
                data.type === 'chapter'
                  ? 'text-blue-700'
                  : data.type === 'plot'
                    ? 'text-orange-700'
                    : 'text-green-700'
              }`}
              style={{
                letterSpacing: '0.01em',
                textShadow: '0 1px 1px rgba(255, 255, 255, 0.7)',
                paddingBottom: '8px',
                borderBottom: `1px solid ${
                  data.type === 'chapter'
                    ? 'rgba(59, 130, 246, 0.15)'
                    : data.type === 'plot'
                      ? 'rgba(249, 115, 22, 0.15)'
                      : 'rgba(16, 185, 129, 0.15)'
                }`,
                position: 'relative',
              }}
            >
              {data.title}
            </div>
            {/* 类型特定内容显示 */}
            <div className="mt-2 space-y-2">
              {/* 章节特定内容 */}
              {data.type === 'chapter' && (
                <div className="space-y-1">
                  {data.chapterStyle && (
                    <div className="flex items-center text-xs text-blue-600">
                      <span className="font-medium mr-1">风格：</span>
                      <span className="truncate">{data.chapterStyle}</span>
                    </div>
                  )}
                  {data.chapterTechniques && Array.isArray(data.chapterTechniques) && data.chapterTechniques.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {data.chapterTechniques.slice(0, 3).map((technique, index) => (
                        <span
                          key={index}
                          className="px-1.5 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full"
                        >
                          {technique}
                        </span>
                      ))}
                      {data.chapterTechniques.length > 3 && (
                        <span className="text-xs text-blue-500">+{data.chapterTechniques.length - 3}</span>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* 剧情节点特定内容 */}
              {data.type === 'plot' && (
                <div className="space-y-1">
                  {data.plotPoints && Array.isArray(data.plotPoints) && data.plotPoints.length > 0 && (
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-orange-600 flex items-center justify-between">
                        <span>剧情点：</span>
                        <span className="text-orange-400">({data.plotPoints.length}个)</span>
                      </div>
                      <div className="space-y-1 max-h-20 overflow-hidden relative">
                        {data.plotPoints.slice(0, 2).map((point, index) => (
                          <div key={point.id} className="bg-orange-50 border border-orange-200 rounded p-1.5">
                            <div className="flex items-start text-xs text-orange-700">
                              <span className="font-medium mr-1 text-orange-500 flex-shrink-0">{index + 1}.</span>
                              <div className="flex-1 min-w-0">
                                <div
                                  className="leading-relaxed"
                                  style={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden',
                                    wordBreak: 'break-word',
                                    lineHeight: '1.3'
                                  }}
                                >
                                  {point.content}
                                </div>
                                {point.writingGuidance && (
                                  <div
                                    className="text-xs text-yellow-700 bg-yellow-50 border border-yellow-200 rounded px-1 py-0.5 mt-1"
                                    style={{
                                      display: '-webkit-box',
                                      WebkitLineClamp: 1,
                                      WebkitBoxOrient: 'vertical',
                                      overflow: 'hidden',
                                      wordBreak: 'break-word'
                                    }}
                                  >
                                    <span className="font-medium">💡 </span>{point.writingGuidance}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                        {/* 渐变遮罩效果 */}
                        {data.plotPoints.length > 2 && (
                          <div
                            className="absolute bottom-0 left-0 right-0 h-6 pointer-events-none"
                            style={{
                              background: 'linear-gradient(to top, rgba(255, 247, 237, 0.95), transparent)'
                            }}
                          />
                        )}
                      </div>
                      {data.plotPoints.length > 2 && (
                        <div className="text-xs text-orange-500 text-center py-1">
                          还有 {data.plotPoints.length - 2} 个剧情点...
                        </div>
                      )}
                    </div>
                  )}
                  {data.relatedCharacters && Array.isArray(data.relatedCharacters) && data.relatedCharacters.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {data.relatedCharacters.slice(0, 3).map((character, index) => (
                        <span
                          key={index}
                          className="px-1.5 py-0.5 text-xs bg-orange-100 text-orange-700 rounded-full"
                        >
                          {character}
                        </span>
                      ))}
                      {data.relatedCharacters.length > 3 && (
                        <span className="text-xs text-orange-500">+{data.relatedCharacters.length - 3}</span>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* 对话节点特定内容 */}
              {data.type === 'dialogue' && (
                <div className="space-y-1">
                  {data.dialogueScene && (
                    <div className="flex items-center text-xs text-green-600">
                      <span className="font-medium mr-1">场景：</span>
                      <span className="truncate">{data.dialogueScene}</span>
                    </div>
                  )}
                  {data.participants && Array.isArray(data.participants) && data.participants.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {data.participants.slice(0, 3).map((participant, index) => {
                        // 安全处理participant，确保它是可渲染的字符串
                        const displayParticipant = typeof participant === 'string'
                          ? participant
                          : typeof participant === 'object' && participant !== null
                            ? (participant as any).name || (participant as any).title || JSON.stringify(participant)
                            : String(participant);

                        return (
                          <span
                            key={index}
                            className="px-1.5 py-0.5 text-xs bg-green-100 text-green-700 rounded-full"
                          >
                            {displayParticipant}
                          </span>
                        );
                      })}
                      {data.participants.length > 3 && (
                        <span className="text-xs text-green-500">+{data.participants.length - 3}</span>
                      )}
                    </div>
                  )}
                  {data.dialogueContent && Array.isArray(data.dialogueContent) && data.dialogueContent.length > 0 && (
                    <div className="text-xs text-green-600">
                      <span className="font-medium">{data.dialogueContent.length} 段对话</span>
                    </div>
                  )}
                </div>
              )}

              {/* 核心故事梗概特定内容 */}
              {data.type === 'synopsis' && (
                <div className="space-y-1">
                  {data.synopsisGenre && (
                    <div className="flex items-center text-xs text-indigo-600">
                      <span className="font-medium mr-1">类型：</span>
                      <span className="px-1.5 py-0.5 bg-indigo-100 text-indigo-700 rounded-full">{data.synopsisGenre}</span>
                    </div>
                  )}
                  {data.synopsisBrainhole && (
                    <div className="flex items-center text-xs text-indigo-600">
                      <span className="font-medium mr-1">脑洞：</span>
                      <span className="truncate">{data.synopsisBrainhole}</span>
                    </div>
                  )}
                  {data.synopsisCoreOutline && (
                    <div className="flex items-center text-xs text-indigo-600">
                      <span className="font-medium mr-1">梗概：</span>
                      <span className="truncate">{data.synopsisCoreOutline}</span>
                    </div>
                  )}
                </div>
              )}

              {/* 通用描述 */}
              {data.description && (
                <div className="overflow-hidden transition-all duration-200 group">
                  {/* 描述内容卡片 - 增强视觉边界和层次感 */}
                  <div
                    className={`
                      p-3 rounded-lg text-xs
                      bg-opacity-15 border border-opacity-25
                      hover:bg-opacity-25 transition-all duration-200
                      ${data.type === 'chapter'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : data.type === 'plot'
                          ? 'bg-orange-50 border-orange-200 text-orange-700'
                          : 'bg-green-50 border-green-200 text-green-700'
                      }
                    `}
                    style={{
                      boxShadow: `0 2px 4px rgba(0, 0, 0, 0.03), 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(${
                        data.type === 'chapter'
                          ? '59, 130, 246'
                          : data.type === 'plot'
                            ? '249, 115, 22'
                            : '16, 185, 129'
                      }, 0.05) inset`,
                      maxHeight: '5em',
                      overflow: 'hidden',
                      position: 'relative',
                      backdropFilter: 'blur(0.5px)',
                      transform: 'translateZ(0)', // 启用GPU加速
                    }}
                  >
                    {/* 使用line-clamp样式进行多行截断 */}
                    <div
                      className="leading-relaxed"
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        wordBreak: 'break-word',
                        whiteSpace: 'pre-line'
                      }}
                    >
                      {data.description}
                    </div>

                    {/* 渐变遮罩效果，提示有更多内容 */}
                    {data.description.length > 60 && (
                      <div
                        className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t pointer-events-none"
                        style={{
                          background: `linear-gradient(to top,
                            ${data.type === 'chapter'
                              ? 'rgba(239, 246, 255, 0.95)'
                              : data.type === 'plot'
                                ? 'rgba(255, 247, 237, 0.95)'
                                : 'rgba(236, 253, 245, 0.95)'
                            },
                            transparent)`
                        }}
                      />
                    )}
                  </div>

                  {/* 提示文本 - 悬停时显示 */}
                  <div className="text-xs text-gray-400 text-center mt-1 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    双击查看完整内容
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧区域：选中指示器、类型标签和菜单按钮 */}
          <div className="flex items-center ml-2">
            {/* 选中状态指示器 */}
            {data.selected && (
              <div className="mr-2 w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
            )}

            {/* 节点类型标签 */}
            <div
              className="px-2 py-0.5 text-xs font-medium rounded-full text-white node-type-label"
              style={{
                background: `var(--outline-${getNodeColorVar()})`,
              }}
            >
              {getNodeTypeLabel()}
            </div>

            {/* 菜单按钮 */}
            <button
              className="ml-2 p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                // 计算菜单位置
                if (nodeRef.current) {
                  const rect = nodeRef.current.getBoundingClientRect();
                  setMenuPosition({
                    x: rect.right + 5,
                    y: rect.top
                  });
                }
                setShowMenu(!showMenu);
                if (!showMenu) {
                  setShowAddMenu(false);
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
          </div>
        </div>

        {/* 快速添加按钮 - 仅在节点选中时显示 */}
        {data.selected && (
          <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 flex flex-col space-y-1">
            <button
              className="w-6 h-6 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors shadow-md"
              title="添加子节点"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                // 设置菜单位置并显示菜单，直接定位到添加子节点部分
                if (nodeRef.current) {
                  const rect = nodeRef.current.getBoundingClientRect();
                  setMenuPosition({
                    x: rect.right + 10,
                    y: rect.top
                  });
                  setShowMenu(true);
                  setShowAddMenu(true);
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        )}

        {/* 使用NodeMenu组件替代原来的菜单 */}
        <NodeMenu
          isOpen={showMenu}
          position={menuPosition}
          onClose={() => {
            setShowMenu(false);
            setShowAddMenu(false);
          }}
          onEdit={() => {
            console.log('右键菜单编辑节点被点击，使用就地编辑模式');
            handleEditClick();
          }}
          onInlineEdit={() => handleEditClick()}
          onDelete={() => handleDeleteClick()}
          onAddChild={(type) => handleAddChildClick(type)(new Event('click') as any)}
          onAIAppend={handleAIAppendClick}
          onSynopsisAI={handleSynopsisAIClick}
          nodeType={data.type as 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue' | 'synopsis'}
          nodeData={data}
          availableFrameworks={data.availableFrameworks || []}
        />

        {/* 子节点菜单已经集成到NodeMenu组件中，不再需要单独的菜单 */}
      </div>

      {/* 就地编辑器 */}
      {isInlineEditing && (
        <InlineNodeEditor
          node={data}
          position={{
            x: nodeRef.current ? nodeRef.current.getBoundingClientRect().right + 10 : 0,
            y: nodeRef.current ? nodeRef.current.getBoundingClientRect().top : 0
          }}
          onSave={handleInlineUpdate}
          onCancel={handleInlineCancel}
          containerRef={{ current: document.querySelector('.react-flow') as HTMLElement }}
          allNodes={data.allNodes}
        />
      )}

      {/* 输出连接点 - 改进视觉效果和交互体验 */}
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`transition-all duration-300 ${data.selected ? 'scale-125' : 'hover:scale-125'}`}
        style={{
          background: `var(--outline-${getNodeColorVar()})`,
          width: '14px',
          height: '14px',
          border: '3px solid white',
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.2)',
          zIndex: 10,
          bottom: '-7px',
          borderRadius: '50%',
          cursor: 'crosshair',
          opacity: data.selected ? 1 : 0.8,
        }}
      >
        {/* 添加视觉提示 - 加号图标 */}
        <div
          className="absolute inset-0 flex items-center justify-center text-white font-bold text-xs"
          style={{
            transform: 'translateY(-1px)',
            pointerEvents: 'none'
          }}
        >
          +
        </div>
      </Handle>
    </>
  );
};

export default memo(OutlineCanvasNode);
