"use client";

import React, { useState, useCallback } from 'react';
import { OutlineNodeType } from '../../../types/outline';
import { cycleDistributionService } from '../../../services/CycleDistributionService';

interface ChapterEditorProps {
  node: OutlineNodeType;
  onChange: (updatedNode: OutlineNodeType) => void;
}

/**
 * 章节编辑器组件
 * 专门用于编辑章节类型的节点，提供章节特有的字段编辑功能
 */
const ChapterEditor: React.FC<ChapterEditorProps> = ({ node, onChange }) => {
  // 本地状态管理
  const [title, setTitle] = useState(node.title || '');
  const [chapterStyle, setChapterStyle] = useState(node.chapterStyle || '');
  const [chapterTechniques, setChapterTechniques] = useState<string[]>(node.chapterTechniques || []);
  const [chapterGoals, setChapterGoals] = useState(node.chapterGoals || '');
  const [description, setDescription] = useState(node.description || '');

  // 更新节点数据
  const updateNode = useCallback((updates: Partial<OutlineNodeType>) => {
    const updatedNode = { ...node, ...updates };
    onChange(updatedNode);
  }, [node, onChange]);

  // 处理标题变化
  const handleTitleChange = useCallback((value: string) => {
    setTitle(value);
    updateNode({ title: value });
  }, [updateNode]);

  // 处理风格变化
  const handleStyleChange = useCallback((value: string) => {
    setChapterStyle(value);
    updateNode({ chapterStyle: value });
  }, [updateNode]);

  // 处理目标变化
  const handleGoalsChange = useCallback((value: string) => {
    setChapterGoals(value);
    updateNode({ chapterGoals: value });
  }, [updateNode]);

  // 处理描述变化
  const handleDescriptionChange = useCallback((value: string) => {
    setDescription(value);
    updateNode({ description: value });
  }, [updateNode]);

  // 添加写作手法
  const addTechnique = useCallback((technique: string) => {
    if (technique.trim() && !chapterTechniques.includes(technique.trim())) {
      const newTechniques = [...chapterTechniques, technique.trim()];
      setChapterTechniques(newTechniques);
      updateNode({ chapterTechniques: newTechniques });
    }
  }, [chapterTechniques, updateNode]);

  // 移除写作手法
  const removeTechnique = useCallback((index: number) => {
    const newTechniques = chapterTechniques.filter((_, i) => i !== index);
    setChapterTechniques(newTechniques);
    updateNode({ chapterTechniques: newTechniques });
  }, [chapterTechniques, updateNode]);

  return (
    <div className="space-y-4 chapter-editor">
      {/* 循环法指导信息 */}
      {node.cyclePhase && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={cycleDistributionService.getPhaseIcon(node.cyclePhase)} />
            </svg>
            <h4 className="font-medium text-purple-800">循环法指导</h4>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">循环模板：</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-sm font-medium">
                  {node.cycleTemplate}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">当前阶段：</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium border ${cycleDistributionService.getPhaseColor(node.cyclePhase)}`}>
                  {node.cyclePhase}
                </span>
              </div>

              {node.phaseIndex !== undefined && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">阶段序号：</span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                    第 {node.phaseIndex + 1} 阶段
                  </span>
                </div>
              )}
            </div>

            {node.phaseGuidance && (
              <div className="mt-3 p-3 bg-white rounded border border-purple-100">
                <h5 className="text-sm font-medium text-purple-800 mb-1">写作指导：</h5>
                <p className="text-sm text-gray-700">{node.phaseGuidance}</p>
              </div>
            )}

            {node.phaseRequirements && node.phaseRequirements.length > 0 && (
              <div className="mt-3 p-3 bg-white rounded border border-purple-100">
                <h5 className="text-sm font-medium text-purple-800 mb-2">阶段要求：</h5>
                <ul className="text-sm text-gray-700 space-y-1">
                  {node.phaseRequirements.map((requirement, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>{requirement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 章节标题 */}
      <div>
        <label className="flex items-center text-sm font-medium text-blue-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          章节标题
        </label>
        <input
          type="text"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50"
          placeholder="输入章节标题"
        />
      </div>

      {/* 写作风格 */}
      <div>
        <label className="flex items-center text-sm font-medium text-blue-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5H9a2 2 0 00-2 2v12a4 4 0 004 4h10a2 2 0 002-2V7a2 2 0 00-2-2z" />
          </svg>
          写作风格
        </label>
        <textarea
          value={chapterStyle}
          onChange={(e) => handleStyleChange(e.target.value)}
          className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 min-h-[80px] text-sm bg-gradient-to-r from-blue-50 to-indigo-50"
          placeholder="描述本章节的写作风格，如：悬疑紧张、温馨治愈、激烈战斗等"
        />
      </div>

      {/* 写作手法 */}
      <div>
        <label className="flex items-center text-sm font-medium text-blue-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          写作手法
        </label>
        <div className="space-y-2">
          {/* 手法标签显示 */}
          {chapterTechniques.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {chapterTechniques.map((technique, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"
                >
                  {technique}
                  <button
                    onClick={() => removeTechnique(index)}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          )}
          {/* 添加手法输入 */}
          <input
            type="text"
            className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-sm bg-gradient-to-r from-blue-50 to-indigo-50"
            placeholder="输入写作手法后按回车添加，如：倒叙、插叙、对比、象征等"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const input = e.target as HTMLInputElement;
                addTechnique(input.value);
                input.value = '';
              }
            }}
          />
        </div>
      </div>

      {/* 章节目标 */}
      <div>
        <label className="flex items-center text-sm font-medium text-blue-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
          </svg>
          章节目标
        </label>
        <textarea
          value={chapterGoals}
          onChange={(e) => handleGoalsChange(e.target.value)}
          className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 min-h-[60px] text-sm bg-gradient-to-r from-blue-50 to-indigo-50"
          placeholder="本章节要达成的目标，如：推进主线剧情、展现角色成长、营造氛围等"
        />
      </div>

      {/* 章节描述 */}
      <div>
        <label className="flex items-center text-sm font-medium text-blue-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
          </svg>
          章节描述
        </label>
        <textarea
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 min-h-[80px] text-sm bg-gradient-to-r from-blue-50 to-indigo-50"
          placeholder="输入章节的详细描述"
        />
      </div>
    </div>
  );
};

export default ChapterEditor;
