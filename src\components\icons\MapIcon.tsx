"use client";

import React from 'react';
import IconBase, { IconBaseProps } from './IconBase';

/**
 * 地图图标 - 卷轴地图
 * 支持展开收起动画
 */
const MapIcon: React.FC<Omit<IconBaseProps, 'children'>> = (props) => {
  return (
    <IconBase {...props} className={`map-icon ${props.className || ''}`}>
      {/* 卷轴外框 */}
      <rect x="2" y="4" width="20" height="16" rx="2" />
      {/* 左侧卷轴杆 */}
      <line x1="2" y1="4" x2="2" y2="20" strokeWidth="3" strokeLinecap="round" />
      {/* 右侧卷轴杆 */}
      <line x1="22" y1="4" x2="22" y2="20" strokeWidth="3" strokeLinecap="round" />
      {/* 地图内容 - 山脉 */}
      <path d="M6 14l2-3 3 2 2-4 3 3 2-2" strokeWidth="1.5" opacity="0.7" />
      {/* 地图内容 - 河流 */}
      <path d="M5 16c2-1 4 1 6-1s4 2 6 0" strokeWidth="1" opacity="0.5" />
      {/* 地图内容 - 城市标记 */}
      <circle cx="8" cy="10" r="1" opacity="0.6" />
      <circle cx="16" cy="12" r="1" opacity="0.6" />
      {/* 装饰性边框 */}
      <rect x="4" y="6" width="16" height="12" rx="1" fill="none" strokeWidth="0.5" opacity="0.3" />
    </IconBase>
  );
};

export default MapIcon;
