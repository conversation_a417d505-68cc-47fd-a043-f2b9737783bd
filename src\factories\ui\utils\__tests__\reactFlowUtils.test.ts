import { transformOutlineToFlow } from '../reactFlowUtils';
import { Outline, OutlineNodeType } from '../../types/outline';

describe('transformOutlineToFlow', () => {
  const mockHandlers = {
    onEdit: jest.fn(),
    onDelete: jest.fn(),
    onAddChild: jest.fn(),
    onSelect: jest.fn(),
    onUpdate: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // 清除console.log的输出
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('应该保持节点的手动设置位置', () => {
    const outline: Outline = {
      id: 'test-outline',
      workId: 'test-work',
      title: '测试大纲',
      nodes: [
        {
          id: 'node-1',
          title: '节点1',
          type: 'chapter',
          position: { x: 100, y: 200 }, // 手动设置的位置
          children: []
        },
        {
          id: 'node-2',
          title: '节点2',
          type: 'chapter',
          // 没有position，应该使用计算的默认位置
          children: []
        }
      ],
      lastModified: new Date(),
      version: 1
    };

    const { nodes } = transformOutlineToFlow(outline, null, mockHandlers);

    // 验证第一个节点使用了保存的位置
    const node1 = nodes.find(n => n.id === 'node-1');
    expect(node1?.position).toEqual({ x: 100, y: 200 });

    // 验证第二个节点使用了计算的默认位置
    const node2 = nodes.find(n => n.id === 'node-2');
    expect(node2?.position).toEqual({ x: 300, y: 0 }); // 基于索引计算的位置
  });

  it('应该正确处理无效的位置信息', () => {
    const outline: Outline = {
      id: 'test-outline',
      workId: 'test-work',
      title: '测试大纲',
      nodes: [
        {
          id: 'node-1',
          title: '节点1',
          type: 'chapter',
          position: { x: null as any, y: 200 }, // 无效的x坐标
          children: []
        },
        {
          id: 'node-2',
          title: '节点2',
          type: 'chapter',
          position: { x: 100 }, // 缺少y坐标
          children: []
        } as any
      ],
      lastModified: new Date(),
      version: 1
    };

    const { nodes } = transformOutlineToFlow(outline, null, mockHandlers);

    // 验证无效位置信息的节点使用了计算的默认位置
    const node1 = nodes.find(n => n.id === 'node-1');
    expect(node1?.position).toEqual({ x: 0, y: 0 }); // 默认位置

    const node2 = nodes.find(n => n.id === 'node-2');
    expect(node2?.position).toEqual({ x: 300, y: 0 }); // 基于索引计算的位置
  });

  it('应该正确处理嵌套节点的位置', () => {
    const outline: Outline = {
      id: 'test-outline',
      workId: 'test-work',
      title: '测试大纲',
      nodes: [
        {
          id: 'parent-1',
          title: '父节点1',
          type: 'chapter',
          position: { x: 50, y: 50 },
          children: [
            {
              id: 'child-1',
              title: '子节点1',
              type: 'note',
              position: { x: 150, y: 150 },
              children: []
            },
            {
              id: 'child-2',
              title: '子节点2',
              type: 'note',
              // 没有position
              children: []
            }
          ]
        }
      ],
      lastModified: new Date(),
      version: 1
    };

    const { nodes, edges } = transformOutlineToFlow(outline, null, mockHandlers);

    // 验证父节点位置
    const parentNode = nodes.find(n => n.id === 'parent-1');
    expect(parentNode?.position).toEqual({ x: 50, y: 50 });

    // 验证子节点位置
    const child1 = nodes.find(n => n.id === 'child-1');
    expect(child1?.position).toEqual({ x: 150, y: 150 });

    const child2 = nodes.find(n => n.id === 'child-2');
    expect(child2?.position.y).toBe(150); // 子节点在第二层

    // 验证边的创建
    expect(edges).toHaveLength(2);
    expect(edges.some(e => e.source === 'parent-1' && e.target === 'child-1')).toBe(true);
    expect(edges.some(e => e.source === 'parent-1' && e.target === 'child-2')).toBe(true);
  });
});
