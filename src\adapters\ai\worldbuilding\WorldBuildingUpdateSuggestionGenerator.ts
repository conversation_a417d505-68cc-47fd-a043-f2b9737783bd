"use client";

import { WorldBuilding } from '@/lib/db/dexie';
import { UpdateSuggestion, WorldBuildingExtractionResult } from '../interfaces/WorldBuildingInterfaces';

/**
 * 世界观更新建议生成器
 * 负责生成世界观元素的更新建议
 */
export class WorldBuildingUpdateSuggestionGenerator {
  /**
   * 生成更新建议
   * @param worldBuilding 世界观对象
   * @param extractedInfo 提取的世界观信息
   * @returns 更新建议
   */
  generateUpdateSuggestions(
    worldBuilding: WorldBuilding,
    extractedInfo: WorldBuildingExtractionResult
  ): UpdateSuggestion[] {
    const suggestions: UpdateSuggestion[] = [];

    // 检查类别
    if (extractedInfo.newInfo['类别'] && extractedInfo.newInfo['类别'] !== worldBuilding.category) {
      suggestions.push({
        field: 'category',
        currentValue: worldBuilding.category || null,
        suggestedValue: extractedInfo.newInfo['类别'],
        type: worldBuilding.category ? 'update' : 'add',
        reason: extractedInfo.updateReasons?.['类别'] || '从章节中发现新信息'
      });
    }

    // 检查描述
    if (extractedInfo.newInfo['描述']) {
      const currentDescription = worldBuilding.description || '';
      // 如果当前描述为空，直接使用新描述
      if (!currentDescription) {
        suggestions.push({
          field: 'description',
          currentValue: null,
          suggestedValue: extractedInfo.newInfo['描述'],
          type: 'add',
          reason: extractedInfo.updateReasons?.['描述'] || '从章节中发现新的描述信息'
        });
      }
      // 如果当前描述不为空，始终使用追加模式（如果新描述不是当前描述的子集）
      else if (!currentDescription.includes(extractedInfo.newInfo['描述'])) {
        suggestions.push({
          field: 'description',
          currentValue: currentDescription,
          suggestedValue: extractedInfo.newInfo['描述'],
          type: 'append',
          reason: extractedInfo.updateReasons?.['描述'] || '从章节中发现新的描述信息'
        });
      }
    }

    // 检查其他属性
    // 处理所有从AI提取的属性字段，不限于预定义列表
    for (const [field, value] of Object.entries(extractedInfo.newInfo)) {
      // 跳过已处理的字段（类别和描述）
      if (field === '类别' || field === '描述') {
        continue;
      }

      const currentValue = worldBuilding.attributes?.[field] || null;

      // 如果当前值不存在，直接使用新值
      if (!currentValue) {
        suggestions.push({
          field: `attributes.${field}`,
          currentValue,
          suggestedValue: value as string,
          type: 'add',
          reason: extractedInfo.updateReasons?.[field] || `从章节中发现新的${field}信息`
        });
      }
      // 如果当前值存在，但新值不是当前值的子集，则追加新值
      else if (!currentValue.includes(value as string)) {
        suggestions.push({
          field: `attributes.${field}`,
          currentValue,
          suggestedValue: value as string,
          type: 'append',
          reason: extractedInfo.updateReasons?.[field] || `从章节中发现新的${field}信息`
        });
      }
    }

    return suggestions;
  }
}
