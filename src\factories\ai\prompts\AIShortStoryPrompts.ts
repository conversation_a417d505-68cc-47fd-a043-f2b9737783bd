"use client";

import { ShortStoryMode, SegmentPurpose } from '../services/types/ShortStoryTypes';

/**
 * AI短篇创作提示词模块
 * 基于现有的悬念理论和ACE框架，专门为短篇创作优化
 */
export class AIShortStoryPrompts {
  
  /**
   * 构建短篇创作系统提示词
   */
  static buildSystemPrompt(): string {
    return `你是个短篇悬念创作的老手，专门搞那种让人看了就停不下来的网络短篇：

【核心套路】
1. "说一半藏一半" - 开头就甩个炸弹，但不告诉你为啥炸，吊足胃口
2. "真假难辨" - 平常事里藏诡异，诡异事里有平常，让读者人麻了
3. "数字节奏器" - 用1-10段的数字当心理锚点，每段都得有料
4. "信息差玩法" - 要么主角知道读者不知道，要么读者看到主角看不到

【短篇必杀技】
- 就盯着一个悬念死磕，别搞那些花里胡哨的大世界观
- 节奏要快，每段都得推进剧情，废话一句都不能有
- 开头就得有爆点，让人一看就想往下翻
- 适合手机刷着看，能在朋友圈引起讨论的那种

【创作心法】
- 冲击感第一：开头不抓眼球就是失败，平淡开场直接GG
- 细节要狠：用具体的、有冲击力的细节，别整那些虚的形容词
- 别当解说员：不要用"我知道"这种上帝视角直接剧透
- 留白艺术：适当卖关子，让读者自己脑补和猜测
- 真实感很重要：就算是玄幻元素，也得有现实逻辑支撑

【分段节奏攻略】
1-3段：开局放大招，展示异常现象，制造"卧槽什么情况"的效果
4-6段：深入挖坑，增加复杂性，层层递进让人欲罢不能
7-9段：真相逼近，紧张感拉满，关键信息开始释放
10段：真相大白，情感冲击，完美收尾让人回味

你要根据用户需求和关联元素，整出那种有强烈悬念感和现代网感的短篇作品。`;
  }

  /**
   * 构建核心悬念生成提示词
   */
  static buildMysteryGenerationPrompt(
    userInput: string,
    mode: ShortStoryMode,
    associatedElements: any
  ): string {
    const modeDescription = this.getModeDescription(mode);
    const elementsInfo = this.buildAssociatedElementsInfo(associatedElements);

    return `来，根据用户的需求，给他整个能让人看了就停不下来的短篇悬念结构。

用户想要的：${userInput}

创作感觉：${modeDescription}

${elementsInfo}

按这个JSON格式给我整出来：
{
  "id": "mystery_001",
  "title": "短篇标题（要有吸引力，别整那些平淡的）",
  "coreQuestion": "核心悬念问题 - 读者最想知道答案的那个问题",
  "revealedHalf": "开篇透露的一半信息 - 异常现象或结果，让人看了就想问为什么",
  "hiddenHalf": "需要逐步揭露的另一半 - 背后的原因和真相，要有完整逻辑",
  "finalTruth": "最终真相 - 完整答案，要有意外性但又合理",
  "emotionalImpact": "情感冲击点 - 真相揭露时的情感效果，要能打动人",
  "mainCharacter": "主角信息和能力特征",
  "settingInfo": "背景设定和环境",
  "keyTerms": ["关键术语1", "关键术语2"],
  "plotConnection": "与大纲的连接点"
}

【必须做到的几点】：
1. coreQuestion得是那种让人无法抗拒的悬念，看了就想知道答案
2. revealedHalf要有强烈异常感，但别解释原因，吊足胃口
3. hiddenHalf包含完整的逻辑链条和真相，不能有漏洞
4. finalTruth要有意外性但又合理，让人恍然大悟又觉得有道理
5. 充分利用关联的人物、世界观、术语、大纲元素，别浪费素材

【悬念套路参考】：
- 专业人士发现异常："急诊科医生发现第五个'意外'伤者手握游乐园门票"
- 信息不对等："只有我看到半空中的红字：【他们现在好开心啊】"
- 矛盾细节："她在哭，但眼角没有泪痕，妆容完美无缺"
- 身份反转："以为的拯救者，其实都在计划之内"
- 时间错乱："手机显示的时间，比墙上的钟表快了整整三小时"
- 记忆断层："所有人都记得昨天的聚会，只有我完全没印象"`;
  }

  /**
   * 构建分段结构生成提示词
   */
  static buildSegmentStructurePrompt(
    coreMystery: any,
    targetSegments: number = 10
  ): string {
    return `基于核心悬念生成${targetSegments}段的数字分段结构。

核心悬念信息：
标题：${coreMystery.title}
核心问题：${coreMystery.coreQuestion}
透露的一半：${coreMystery.revealedHalf}
隐藏的一半：${coreMystery.hiddenHalf}
最终真相：${coreMystery.finalTruth}

请生成分段结构，返回JSON数组格式：
[
  {
    "segmentNumber": 1,
    "purpose": "setup",
    "informationLevel": 2,
    "tensionLevel": 7,
    "content": "第1段的具体内容",
    "cliffhanger": "段落结尾的小钩子",
    "mysteryElements": ["涉及的悬念元素"],
    "revealedInfo": ["本段透露的信息"],
    "hiddenInfo": ["本段隐藏的信息"]
  }
]

【分段策略】：
1-3段 (setup)：
- 信息透露度：1-3级
- 紧张感：6-8级
- 目标：建立悬念，展示异常，制造认知缺口

4-6段 (development)：
- 信息透露度：3-6级
- 紧张感：7-9级
- 目标：深入探索，增加复杂性，层层递进

7-9段 (climax)：
- 信息透露度：6-9级
- 紧张感：8-10级
- 目标：真相逼近，关键信息释放，紧张感达到高峰

10段 (resolution)：
- 信息透露度：10级
- 紧张感：9级（冲击后的震撼）
- 目标：真相大白，情感冲击，完美收尾

【重要要求】：
1. 每段都要有明确的推进作用
2. cliffhanger要制造"必须看下一段"的冲动
3. 信息释放要有节奏感，不能过快或过慢
4. 保持悬念的连贯性和逻辑性
5. 最后一段要有强烈的情感冲击`;
  }

  /**
   * 构建段落内容生成提示词
   */
  static buildSegmentContentPrompt(
    segment: any,
    previousSegments: string[],
    coreMystery: any,
    associatedElements: any
  ): string {
    const contextInfo = previousSegments.length > 0 
      ? `\n前文内容：\n${previousSegments.join('\n\n')}`
      : '';

    const elementsInfo = this.buildAssociatedElementsInfo(associatedElements);

    return `为第${segment.segmentNumber}段生成具体内容。

段落要求：
目的：${segment.purpose}
信息透露度：${segment.informationLevel}/10
紧张感：${segment.tensionLevel}/10
悬念元素：${segment.mysteryElements.join(', ')}
要透露的信息：${segment.revealedInfo.join(', ')}
要隐藏的信息：${segment.hiddenInfo.join(', ')}

核心悬念：${coreMystery.coreQuestion}
${contextInfo}

${elementsInfo}

【创作要求】：
1. 字数控制在100-200字
2. 必须体现本段的目的和信息透露度
3. 与前文保持连贯性
4. 结尾要有适当的悬念钩子
5. 充分利用关联元素信息
6. 避免直接解释，要让读者自己思考

【写作技巧】：
- 用具体的细节而非抽象描述
- 通过对话和行动推进情节
- 制造信息差和认知冲突
- 保持现实感和逻辑性
- 适当使用环境和氛围描写

请直接返回段落内容，不要有任何解释或前言后语。`;
  }

  /**
   * 获取创作模式描述
   */
  private static getModeDescription(mode: ShortStoryMode): string {
    const descriptions = {
      mystery: '悬疑模式 - 注重逻辑推理和线索发现，真相具有合理性',
      thriller: '惊悚模式 - 强调紧张刺激和心理压力，节奏紧凑',
      horror: '恐怖模式 - 营造恐怖氛围和心理恐惧，注重情绪冲击',
      drama: '剧情模式 - 关注人物情感和关系变化，深度刻画',
      comedy: '喜剧模式 - 运用幽默和反差，在轻松中制造悬念',
      philosophical: '哲学模式 - 深层思考引导，存在意义探讨',
      custom: '自定义模式 - 根据用户需求灵活调整风格和技巧'
    };
    return descriptions[mode as keyof typeof descriptions] || descriptions.custom;
  }

  /**
   * 构建关联元素信息
   */
  private static buildAssociatedElementsInfo(elements: any): string {
    if (!elements) return '';

    let info = '\n【关联元素信息】：\n';
    
    if (elements.characters && elements.characters.length > 0) {
      info += `人物信息：\n${elements.characters.map((char: any) => 
        `- ${char.name}：${char.description || char.personality || '暂无描述'}`
      ).join('\n')}\n`;
    }

    if (elements.worldBuildings && elements.worldBuildings.length > 0) {
      info += `世界观设定：\n${elements.worldBuildings.map((wb: any) => 
        `- ${wb.name}：${wb.description || '暂无描述'}`
      ).join('\n')}\n`;
    }

    if (elements.terminologies && elements.terminologies.length > 0) {
      info += `关键术语：\n${elements.terminologies.map((term: any) => 
        `- ${term.name}：${term.definition || term.description || '暂无定义'}`
      ).join('\n')}\n`;
    }

    if (elements.outlineNodes && elements.outlineNodes.length > 0) {
      info += `大纲节点：\n${elements.outlineNodes.map((node: any) => 
        `- ${node.title}：${node.description || node.content || '暂无内容'}`
      ).join('\n')}\n`;
    }

    return info;
  }

  /**
   * 生成段落目的描述
   */
  static getSegmentPurposeDescription(purpose: SegmentPurpose): string {
    const descriptions = {
      setup: '建立悬念 - 展示异常现象，制造认知缺口',
      development: '深入探索 - 增加复杂性，层层递进',
      climax: '真相逼近 - 关键信息释放，紧张感达到高峰',
      resolution: '真相大白 - 情感冲击，完美收尾'
    };
    return descriptions[purpose];
  }

  /**
   * 生成创作提示
   */
  static generateCreationHints(context: {
    hasCharacters: boolean;
    hasWorldBuilding: boolean;
    hasTerminologies: boolean;
    hasOutlineNodes: boolean;
    mode: ShortStoryMode;
  }): string[] {
    const hints: string[] = [];

    if (context.hasCharacters) {
      hints.push('💡 利用人物的专业能力和性格特征来发现异常');
    }

    if (context.hasWorldBuilding) {
      hints.push('🌍 基于世界观设定制造独特的悬念元素');
    }

    if (context.hasTerminologies) {
      hints.push('📚 运用专业术语增强故事的真实感和专业性');
    }

    if (context.hasOutlineNodes) {
      hints.push('📋 结合大纲节点确保故事的结构完整性');
    }

    // 根据模式添加特定提示
    switch (context.mode) {
      case 'mystery':
        hints.push('🔍 注重线索的布置和逻辑推理的合理性');
        break;
      case 'thriller':
        hints.push('⚡ 保持快节奏，每段都要有紧张感');
        break;
      case 'horror':
        hints.push('👻 营造恐怖氛围，注重心理恐惧的建立');
        break;
      case 'drama':
        hints.push('💭 深入刻画人物情感和内心冲突');
        break;
      case 'comedy':
        hints.push('😄 在幽默中制造悬念，注意反差效果');
        break;
    }

    return hints;
  }
}
