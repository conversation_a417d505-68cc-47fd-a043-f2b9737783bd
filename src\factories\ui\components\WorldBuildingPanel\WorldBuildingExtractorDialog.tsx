"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorldBuildingExtractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  existingWorldBuildings: WorldBuilding[];
  onCreateWorldBuilding: (worldBuilding: WorldBuilding) => void;
  onUpdateWorldBuilding: (worldBuilding: WorldBuilding) => void;
  bookId: string;
}

/**
 * 世界观提取对话框组件
 * 用于从章节内容中提取世界观信息
 */
export const WorldBuildingExtractorDialog: React.FC<WorldBuildingExtractorDialogProps> = ({
  isOpen,
  onClose,
  existingWorldBuildings,
  onCreateWorldBuilding,
  onUpdateWorldBuilding,
  bookId
}) => {
  // 不再需要UI工厂实例

  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);

  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 章节搜索
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredChapters, setFilteredChapters] = useState<GenericChapter[]>([]);

  // 提取设置
  const [maxWorldBuildings, setMaxWorldBuildings] = useState<number>(0);
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // 关联世界观元素
  const [isLoadingWorldBuildings, setIsLoadingWorldBuildings] = useState(false);
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);
  const [selectedRelatedWorldBuildings, setSelectedRelatedWorldBuildings] = useState<string[]>([]);

  // 提取状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [extractedWorldBuildings, setExtractedWorldBuildings] = useState<Record<string, any>>({});

  // 加载章节数据和世界观元素
  useEffect(() => {
    if (isOpen) {
      loadChapters();
      loadWorldBuildings();
    } else {
      // 重置状态
      setSelectedChapterIds([]);
      setRangeStart('');
      setRangeEnd('');
      setSearchQuery('');
      setMaxWorldBuildings(0);
      setCustomPrompt('');
      setSelectedRelatedWorldBuildings([]);
      setError(null);
      setExtractedWorldBuildings({});
    }
  }, [isOpen, bookId]);

  // 过滤章节
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredChapters(chapters);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredChapters(
        chapters.filter(chapter =>
          chapter.title?.toLowerCase().includes(query) ||
          chapter.content?.toLowerCase().includes(query)
        )
      );
    }
  }, [chapters, searchQuery]);

  // 加载世界观元素
  const loadWorldBuildings = async () => {
    setIsLoadingWorldBuildings(true);
    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 获取当前书籍的所有世界观元素
      const worldBuildingsData = await worldBuildingRepository.getAllByBookId(bookId);

      // 获取所有世界观元素
      const filteredWorldBuildings = worldBuildingsData;

      setWorldBuildings(filteredWorldBuildings);
    } catch (error) {
      console.error('加载世界观元素失败:', error);
    } finally {
      setIsLoadingWorldBuildings(false);
    }
  };

  // 处理世界观元素选择变化
  const handleWorldBuildingSelectionChange = (id: string, name: string) => {
    const worldBuildingInfo = `${id}:${name}`;
    if (selectedRelatedWorldBuildings.includes(worldBuildingInfo)) {
      setSelectedRelatedWorldBuildings(prev => prev.filter(item => item !== worldBuildingInfo));
    } else {
      setSelectedRelatedWorldBuildings(prev => [...prev, worldBuildingInfo]);
    }
  };

  // 加载章节数据
  const loadChapters = async () => {
    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 src/db/chapterRepository.ts
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            setFilteredChapters(chaptersData);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
      setFilteredChapters([]);
      setError('无法获取章节数据，请检查网络连接或联系管理员');
    } catch (error) {
      console.error('加载章节数据失败:', error);
      setError('获取章节数据失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingChapters(false);
    }
  };

  // 处理全选章节
  const handleSelectAllChapters = () => {
    if (selectedChapterIds.length === filteredChapters.length) {
      setSelectedChapterIds([]);
    } else {
      setSelectedChapterIds(filteredChapters.filter(c => c.id).map(c => c.id!));
    }
  };

  // 处理选择章节
  const handleSelectChapter = (chapterId: string) => {
    if (selectedChapterIds.includes(chapterId)) {
      setSelectedChapterIds(prev => prev.filter(id => id !== chapterId));
    } else {
      setSelectedChapterIds(prev => [...prev, chapterId]);
    }
  };

  // 处理范围选择
  const handleRangeSelect = () => {
    if (!rangeStart || !rangeEnd) return;

    const startIndex = chapters.findIndex(c => c.id === rangeStart);
    const endIndex = chapters.findIndex(c => c.id === rangeEnd);

    if (startIndex === -1 || endIndex === -1) return;

    const start = Math.min(startIndex, endIndex);
    const end = Math.max(startIndex, endIndex);

    const rangeIds = chapters.slice(start, end + 1).filter(c => c.id).map(c => c.id!);
    setSelectedChapterIds(rangeIds);
  };

  // 提取世界观信息
  const extractWorldBuildings = async (e?: React.MouseEvent) => {
    // 如果事件对象存在，阻止默认行为和事件冒泡
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (selectedChapterIds.length === 0) {
      setError('请至少选择一个章节');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 获取选中章节的内容
      const selectedChapters = chapters.filter(c => c.id && selectedChapterIds.includes(c.id));
      const chapterContents = selectedChapters.map(c => ({
        id: c.id!,
        title: c.title || '',
        content: c.content || ''
      }));

      // 创建AI适配器
      const { AIWorldBuildingExtractorAdapter } = await import('@/adapters/ai');
      const worldBuildingExtractorAIAdapter = new AIWorldBuildingExtractorAdapter();

      // 提取世界观信息
      const result = await worldBuildingExtractorAIAdapter.extractWorldBuildingsFromChapters(
        chapterContents,
        {
          maxWorldBuildings: maxWorldBuildings > 0 ? maxWorldBuildings : undefined,
          customPrompt: customPrompt.trim() || undefined,
          relatedWorldBuildings: selectedRelatedWorldBuildings.length > 0 ? selectedRelatedWorldBuildings : undefined
        }
      );

      // 设置提取结果
      setExtractedWorldBuildings(result);

      // 如果没有提取到世界观信息
      if (Object.keys(result).length === 0) {
        setError('未从章节中提取到世界观信息');
      }
    } catch (error) {
      console.error('提取世界观信息失败:', error);
      setError('提取世界观信息失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 应用提取结果
  const applyResults = async (e?: React.MouseEvent) => {
    // 如果事件对象存在，阻止默认行为和事件冒泡
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    try {
      // 处理每个提取的世界观元素
      for (const [name, info] of Object.entries(extractedWorldBuildings)) {
        // 检查是否已存在同名世界观元素
        const existingWorldBuilding = existingWorldBuildings.find(wb =>
          wb.name.toLowerCase() === name.toLowerCase()
        );

        if (existingWorldBuilding) {
          // 更新现有世界观元素
          const updatedWorldBuilding = {
            ...existingWorldBuilding,
            updatedAt: new Date()
          };

          // 合并新信息
          if (info.newInfo) {
            // 字段名称映射表
            const fieldNameMap: Record<string, string> = {
              // 类别相关
              '类别': 'category',
              'category': 'category',
              // 描述相关
              '描述': 'description',
              'description': 'description',
              // 重要性相关
              '重要性': 'importance',
              'importance': 'importance',
              // 时间跨度相关
              '时间跨度': 'timeSpan',
              'timeSpan': 'timeSpan',
              'timespan': 'timeSpan',
              // 影响范围相关
              '影响范围': 'scope',
              'scope': 'scope',
              // 起源/历史相关
              '起源': 'origin',
              'origin': 'origin',
              // 规则/法则相关
              '规则': 'rules',
              'rules': 'rules',
              // 象征意义相关
              '象征意义': 'symbolism',
              'symbolism': 'symbolism',
              // 冲突/矛盾相关
              '冲突': 'conflicts',
              'conflicts': 'conflicts'
            };

            // 查找描述字段
            let description = info.newInfo.description || info.newInfo['描述'];
            if (description && (!updatedWorldBuilding.description || updatedWorldBuilding.description.length < description.length)) {
              updatedWorldBuilding.description = description;
            }

            // 查找类别字段
            let category = info.newInfo.category || info.newInfo['类别'];
            if (category && !updatedWorldBuilding.category) {
              updatedWorldBuilding.category = category;
            }

            // 更新属性
            if (!updatedWorldBuilding.attributes) {
              updatedWorldBuilding.attributes = {};
            }

            // 合并其他属性 - 使用用户友好的字段名称
            const displayNameMap: Record<string, string> = {
              '重要性': '重要性',
              'importance': '重要性',
              '时间跨度': '时间跨度',
              'timeSpan': '时间跨度',
              'timespan': '时间跨度',
              '影响范围': '影响范围',
              'scope': '影响范围',
              '起源': '起源',
              'origin': '起源',
              '规则': '规则',
              'rules': '规则',
              '象征意义': '象征意义',
              'symbolism': '象征意义',
              '冲突': '冲突',
              'conflicts': '冲突'
            };

            for (const [key, value] of Object.entries(info.newInfo)) {
              if (key !== 'description' && key !== 'category' && key !== 'name') {
                // 使用映射表获取标准字段名称
                const standardKey = fieldNameMap[key] || key;

                // 使用映射表获取用户友好的显示名称
                const displayKey = displayNameMap[key] || key;

                // 保存到attributes中，使用用户友好的显示名称
                updatedWorldBuilding.attributes[displayKey] = value as string;

                // 对于特定的重要字段，确保使用标准的显示名称
                if (standardKey === 'importance') {
                  updatedWorldBuilding.attributes['重要性'] = value as string;
                } else if (standardKey === 'timeSpan') {
                  updatedWorldBuilding.attributes['时间跨度'] = value as string;
                } else if (standardKey === 'scope') {
                  updatedWorldBuilding.attributes['影响范围'] = value as string;
                }
              }
            }
          }

          // 添加提取章节ID
          const extractedFromChapterIds = new Set([
            ...updatedWorldBuilding.extractedFromChapterIds,
            ...selectedChapterIds
          ]);
          updatedWorldBuilding.extractedFromChapterIds = Array.from(extractedFromChapterIds);

          // 调用更新回调
          onUpdateWorldBuilding(updatedWorldBuilding);
        } else {
          // 创建新的世界观元素
          const now = new Date();

          // 查找类别和描述字段，考虑中文字段名
          const category = info.newInfo?.category || info.newInfo?.['类别'] || '';
          const description = info.newInfo?.description || info.newInfo?.['描述'] || '';

          const newWorldBuilding: WorldBuilding = {
            bookId,
            name,
            category,
            description,
            createdAt: now,
            updatedAt: now,
            extractedFromChapterIds: selectedChapterIds,
            relatedCharacterIds: [],
            relatedTerminologyIds: [],
            relatedWorldBuildingIds: []
          };

          // 添加其他属性 - 使用用户友好的字段名称
          if (info.newInfo) {
            newWorldBuilding.attributes = {};

            // 字段名称映射表 - 用于将各种可能的字段名称映射到标准字段名称
            const fieldNameMap: Record<string, string> = {
              // 类别相关
              '类别': 'category',
              'category': 'category',
              // 描述相关
              '描述': 'description',
              'description': 'description',
              // 重要性相关
              '重要性': 'importance',
              'importance': 'importance',
              // 时间跨度相关
              '时间跨度': 'timeSpan',
              'timeSpan': 'timeSpan',
              'timespan': 'timeSpan',
              // 影响范围相关
              '影响范围': 'scope',
              'scope': 'scope',
              // 起源/历史相关
              '起源': 'origin',
              'origin': 'origin',
              // 规则/法则相关
              '规则': 'rules',
              'rules': 'rules',
              // 象征意义相关
              '象征意义': 'symbolism',
              'symbolism': 'symbolism',
              // 冲突/矛盾相关
              '冲突': 'conflicts',
              'conflicts': 'conflicts'
            };

            // 显示名称映射表 - 用于确保在UI中显示用户友好的字段名称
            const displayNameMap: Record<string, string> = {
              '重要性': '重要性',
              'importance': '重要性',
              '时间跨度': '时间跨度',
              'timeSpan': '时间跨度',
              'timespan': '时间跨度',
              '影响范围': '影响范围',
              'scope': '影响范围',
              '起源': '起源',
              'origin': '起源',
              '规则': '规则',
              'rules': '规则',
              '象征意义': '象征意义',
              'symbolism': '象征意义',
              '冲突': '冲突',
              'conflicts': '冲突'
            };

            for (const [key, value] of Object.entries(info.newInfo)) {
              if (key !== 'description' && key !== 'category' && key !== 'name') {
                // 使用映射表获取标准字段名称
                const standardKey = fieldNameMap[key] || key;

                // 使用映射表获取用户友好的显示名称
                const displayKey = displayNameMap[key] || key;

                // 保存到attributes中，使用用户友好的显示名称
                newWorldBuilding.attributes[displayKey] = value as string;

                // 对于特定的重要字段，确保使用标准的显示名称
                if (standardKey === 'importance') {
                  newWorldBuilding.attributes['重要性'] = value as string;
                } else if (standardKey === 'timeSpan') {
                  newWorldBuilding.attributes['时间跨度'] = value as string;
                } else if (standardKey === 'scope') {
                  newWorldBuilding.attributes['影响范围'] = value as string;
                }
              }
            }
          }

          // 调用创建回调
          onCreateWorldBuilding(newWorldBuilding);
        }
      }

      // 设置成功消息
      setError(`成功应用 ${Object.keys(extractedWorldBuildings).length} 个世界观元素`);

      // 清空提取结果
      setExtractedWorldBuildings({});
    } catch (error) {
      console.error('应用提取结果失败:', error);
      setError('应用提取结果失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 创建按钮组件
  const renderCloseButton = () => (
    <button
      className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
      onClick={onClose}
    >
      关闭
    </button>
  );

  const renderExtractButton = () => (
    <button
      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
      onClick={() => extractWorldBuildings()}
      disabled={selectedChapterIds.length === 0}
    >
      提取世界观
    </button>
  );

  const renderApplyButton = () => (
    <button
      className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
      onClick={() => applyResults()}
    >
      应用结果
    </button>
  );

  const renderCancelButton = () => (
    <button
      className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
      onClick={() => {
        setExtractedWorldBuildings({});
        setError(null);
      }}
    >
      取消
    </button>
  );

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <h2 className="text-xl font-bold mb-4 text-blue-700">从章节提取世界观信息</h2>

        {/* 主体内容 */}
        <div className="flex-1 overflow-auto">
          {/* 章节选择 */}
          {!isLoading && Object.keys(extractedWorldBuildings).length === 0 && (
            <div className="mb-6">
              <div className="mb-2">
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold">选择章节</h3>
                  <button
                    className="text-sm text-blue-600 hover:text-blue-800"
                    onClick={handleSelectAllChapters}
                  >
                    {selectedChapterIds.length === chapters.length ? '取消全选' : '全选'}
                  </button>
                </div>
                <p className="text-sm text-gray-500">选择要从中提取世界观信息的章节</p>
              </div>

              {/* 章节搜索 */}
              <div className="mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  placeholder="搜索章节..."
                  className="w-full p-2 border rounded-lg mb-2"
                />
              </div>

              {/* 范围选择 */}
              <div className="mb-4 flex space-x-2">
                <select
                  className="p-2 border rounded-lg flex-1"
                  value={rangeStart}
                  onChange={e => setRangeStart(e.target.value)}
                >
                  <option value="">选择起始章节</option>
                  {chapters.map(chapter => (
                    <option key={`start-${chapter.id}`} value={chapter.id}>
                      {chapter.title || `章节 ${chapter.order}`}
                    </option>
                  ))}
                </select>
                <select
                  className="p-2 border rounded-lg flex-1"
                  value={rangeEnd}
                  onChange={e => setRangeEnd(e.target.value)}
                >
                  <option value="">选择结束章节</option>
                  {chapters.map(chapter => (
                    <option key={`end-${chapter.id}`} value={chapter.id}>
                      {chapter.title || `章节 ${chapter.order}`}
                    </option>
                  ))}
                </select>
                <button
                  className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                  onClick={handleRangeSelect}
                >
                  选择范围
                </button>
              </div>

              {/* 章节列表 */}
              <div className="max-h-60 overflow-y-auto border rounded-lg p-2 mb-4">
                {isLoadingChapters ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p className="text-gray-500">正在加载章节数据...</p>
                  </div>
                ) : chapters.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    未找到章节数据，请确保已创建章节
                  </div>
                ) : filteredChapters.length === 0 && searchQuery ? (
                  <div className="p-4 text-center text-gray-500">
                    没有找到匹配"{searchQuery}"的章节
                  </div>
                ) : filteredChapters.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    没有找到章节
                  </div>
                ) : (
                  <div>
                    <div className="mb-2 px-2 text-xs text-gray-500 flex justify-between">
                      <span>已选择: {selectedChapterIds.length}/{filteredChapters.length}</span>
                      <span>总章节数: {chapters.length}</span>
                    </div>
                    {filteredChapters.map(chapter => (
                      <div
                        key={chapter.id}
                        className={`p-2 mb-1 rounded-lg cursor-pointer transition-colors ${
                          selectedChapterIds.includes(chapter.id!) ? 'bg-blue-100 border border-blue-300' : 'hover:bg-gray-100 border border-transparent'
                        }`}
                        onClick={() => handleSelectChapter(chapter.id!)}
                      >
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`chapter-${chapter.id}`}
                            checked={selectedChapterIds.includes(chapter.id!)}
                            onChange={() => handleSelectChapter(chapter.id!)}
                            className="mr-2"
                          />
                          <div className="flex-1">
                            <div className={`font-medium ${selectedChapterIds.includes(chapter.id!) ? 'text-blue-800' : 'text-gray-700'}`}>
                              {chapter.title || `章节 ${chapter.order || chapter.chapterNumber || '未编号'}`}
                            </div>
                            <div className="text-xs text-gray-500">
                              {chapter.wordCount || 0} 字 · {new Date(chapter.updatedAt || new Date()).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 提取设置 */}
              <h3 className="font-semibold mb-2">提取设置</h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="max-world-buildings" className="block text-sm font-medium text-gray-700 mb-1">
                    最大世界观元素数量
                  </label>
                  <input
                    type="number"
                    id="max-world-buildings"
                    value={maxWorldBuildings}
                    onChange={e => setMaxWorldBuildings(parseInt(e.target.value) || 0)}
                    min="0"
                    max="20"
                    className="w-full p-2 border rounded-lg"
                    placeholder="0表示不限制"
                  />
                  <p className="text-xs text-gray-500 mt-1">设置为0表示不限制世界观元素数量</p>
                </div>

                <div>
                  <label htmlFor="custom-prompt" className="block text-sm font-medium text-gray-700 mb-1">
                    自定义提示词（可选）
                  </label>
                  <textarea
                    id="custom-prompt"
                    value={customPrompt}
                    onChange={e => setCustomPrompt(e.target.value)}
                    className="w-full p-2 border rounded-lg"
                    rows={3}
                    placeholder="输入自定义提示词，例如：请关注魔法系统的规则和限制"
                  />
                </div>

                {/* 关联世界观元素选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    关联世界观元素（可选）
                  </label>
                  <div className="border rounded-lg p-2 max-h-40 overflow-y-auto">
                    {isLoadingWorldBuildings ? (
                      <div className="flex items-center justify-center p-4">
                        <svg className="animate-spin h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-gray-600">加载世界观元素...</span>
                      </div>
                    ) : worldBuildings.length === 0 ? (
                      <div className="text-center p-4 text-gray-500">
                        没有找到可关联的世界观元素
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {worldBuildings.map((wb) => (
                          <label key={wb.id} className="flex items-center">
                            <input
                              type="checkbox"
                              className="form-checkbox h-4 w-4 text-blue-600"
                              checked={selectedRelatedWorldBuildings.includes(`${wb.id}:${wb.name}`)}
                              onChange={() => handleWorldBuildingSelectionChange(wb.id!, wb.name)}
                              data-id={wb.id}
                              value={wb.name}
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {wb.name} <span className="text-xs text-gray-500">({wb.category || '未分类'})</span>
                            </span>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                  {selectedRelatedWorldBuildings.length > 0 && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-md">
                      <p className="text-sm text-blue-700">已选择 {selectedRelatedWorldBuildings.length} 个关联世界观元素</p>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {selectedRelatedWorldBuildings.map(wb => {
                          const name = wb.split(':')[1] || wb; // 只显示名称部分
                          return (
                            <span key={wb} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {name}
                            </span>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 加载中状态 */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-lg font-medium text-gray-700">正在提取世界观信息...</p>
              <p className="text-sm text-gray-500 mt-2">这可能需要一些时间，请耐心等待</p>
            </div>
          )}

          {/* 提取结果 */}
          {!isLoading && Object.keys(extractedWorldBuildings).length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">提取结果</h3>
              <p className="text-sm text-gray-500 mb-4">
                从选定章节中提取到 {Object.keys(extractedWorldBuildings).length} 个世界观元素
              </p>

              <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                {Object.entries(extractedWorldBuildings).map(([name, info]) => {
                  const isExisting = existingWorldBuildings.some(wb =>
                    wb.name.toLowerCase() === name.toLowerCase()
                  );

                  return (
                    <div
                      key={name}
                      className={`p-4 rounded-lg border ${
                        isExisting ? 'border-yellow-300 bg-yellow-50' : 'border-green-300 bg-green-50'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-800 flex items-center">
                            {name}
                            {isExisting && (
                              <span className="ml-2 px-2 py-0.5 bg-yellow-200 text-yellow-800 rounded-full text-xs">
                                已存在
                              </span>
                            )}
                          </h4>
                          {info.newInfo?.category && (
                            <p className="text-sm text-gray-600 mt-1">
                              类别: {info.newInfo.category}
                            </p>
                          )}
                        </div>
                      </div>

                      {info.newInfo?.description && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-700 bg-white p-2 rounded border border-gray-200">
                            {info.newInfo.description}
                          </p>
                        </div>
                      )}

                      {/* 显示其他属性 */}
                      {info.newInfo && Object.entries(info.newInfo).filter(([key]) =>
                        !['name', 'category', 'description'].includes(key)
                      ).length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs font-medium text-gray-500">其他属性:</p>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-1">
                            {Object.entries(info.newInfo).filter(([key]) =>
                              !['name', 'category', 'description'].includes(key)
                            ).map(([key, value]) => {
                              // 字段名称映射表
                              const fieldNameMap: Record<string, string> = {
                                '重要性': '重要性',
                                'importance': '重要性',
                                '时间跨度': '时间跨度',
                                'timeSpan': '时间跨度',
                                'timespan': '时间跨度',
                                '影响范围': '影响范围',
                                'scope': '影响范围',
                                '起源': '起源',
                                'origin': '起源',
                                '规则': '规则',
                                'rules': '规则',
                                '象征意义': '象征意义',
                                'symbolism': '象征意义',
                                '冲突': '冲突',
                                'conflicts': '冲突'
                              };

                              // 使用映射表获取用户友好的字段名称，如果没有映射则使用原始字段名称
                              const displayKey = fieldNameMap[key] || key;

                              return (
                                <div key={key} className="text-xs">
                                  <span className="font-medium">{displayKey}: </span>
                                  <span>{String(value)}</span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className={`mt-4 p-3 rounded-lg ${error.startsWith('成功') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
              {error}
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="mt-6 flex justify-end space-x-4">
          {/* 根据当前状态显示不同的操作按钮 */}
          {!isLoading && Object.keys(extractedWorldBuildings).length === 0 ? (
            // 提取世界观按钮
            <div className="flex space-x-4">
              {renderCloseButton()}
              <div className={`${selectedChapterIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}>
                {renderExtractButton()}
              </div>
            </div>
          ) : !isLoading && Object.keys(extractedWorldBuildings).length > 0 ? (
            // 如果已经成功应用了结果，显示完成按钮；否则显示应用按钮
            error && error.startsWith('成功') ? (
              <div>{renderCloseButton()}</div>
            ) : (
              <div className="flex space-x-4">
                {renderCancelButton()}
                {renderApplyButton()}
              </div>
            )
          ) : (
            // 加载中状态，不显示按钮
            <div></div>
          )}
        </div>
      </div>
    </div>
  );
};
