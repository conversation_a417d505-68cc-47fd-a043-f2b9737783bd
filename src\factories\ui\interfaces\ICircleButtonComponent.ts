import { IUIComponent } from './IUIComponent';
import React from 'react';

/**
 * 圆形按钮尺寸
 */
export type CircleButtonSize = 'small' | 'medium' | 'large';

/**
 * 圆形按钮组件接口
 */
export interface ICircleButtonComponent extends IUIComponent {
  /**
   * 设置按钮图标
   * @param icon 图标JSX元素
   */
  setIcon(icon: React.ReactNode): void;
  
  /**
   * 设置按钮文本
   * @param text 按钮文本
   */
  setText(text: string): void;
  
  /**
   * 设置按钮颜色
   * @param color 按钮颜色
   */
  setColor(color: string): void;
  
  /**
   * 设置按钮尺寸
   * @param size 按钮尺寸
   */
  setSize(size: CircleButtonSize): void;
  
  /**
   * 设置按钮是否禁用
   * @param disabled 是否禁用
   */
  setDisabled(disabled: boolean): void;
  
  /**
   * 设置点击事件处理函数
   * @param handler 点击事件处理函数
   */
  onClick(handler: () => void): void;
}
