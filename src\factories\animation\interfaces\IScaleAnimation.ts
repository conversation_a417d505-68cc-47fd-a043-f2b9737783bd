import { IAnimationComponent } from './IAnimationComponent';

/**
 * 缩放动画接口
 */
export interface IScaleAnimation extends IAnimationComponent {
  /**
   * 设置起始缩放比例
   * @param scale 缩放比例
   */
  setStartScale(scale: number): void;
  
  /**
   * 设置结束缩放比例
   * @param scale 缩放比例
   */
  setEndScale(scale: number): void;
  
  /**
   * 设置动画持续时间
   * @param duration 持续时间（毫秒）
   */
  setDuration(duration: number): void;
  
  /**
   * 设置动画延迟
   * @param delay 延迟时间（毫秒）
   */
  setDelay(delay: number): void;
  
  /**
   * 设置是否显示
   * @param visible 是否显示
   */
  setVisible(visible: boolean): void;
}
