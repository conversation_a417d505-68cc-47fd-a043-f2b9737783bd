"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BranchInfo } from '../../services/chat/SessionManager';

interface BranchSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  branches: BranchInfo[];
  currentSessionId: string;
  onSwitchBranch: (branchId: string) => void;
  onDeleteBranch: (branchId: string) => void;
  onRenameBranch?: (branchId: string, newName: string) => void;
}

/**
 * 分支管理侧边栏组件
 * 显示所有分支并提供切换、删除、重命名功能
 */
const BranchSidebar: React.FC<BranchSidebarProps> = ({
  isOpen,
  onClose,
  branches,
  currentSessionId,
  onSwitchBranch,
  onDeleteBranch,
  onRenameBranch
}) => {
  const [editingBranchId, setEditingBranchId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');

  // 开始重命名
  const handleStartRename = (branch: BranchInfo) => {
    setEditingBranchId(branch.id);
    setEditingName(branch.name);
  };

  // 确认重命名
  const handleConfirmRename = () => {
    if (editingBranchId && editingName.trim() && onRenameBranch) {
      onRenameBranch(editingBranchId, editingName.trim());
    }
    setEditingBranchId(null);
    setEditingName('');
  };

  // 取消重命名
  const handleCancelRename = () => {
    setEditingBranchId(null);
    setEditingName('');
  };

  // 删除分支确认
  const handleDeleteBranch = (branchId: string, branchName: string) => {
    if (window.confirm(`确定要删除分支"${branchName}"吗？此操作不可撤销。`)) {
      onDeleteBranch(branchId);
    }
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    return new Date(date).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* 侧边栏 */}
          <motion.div
            className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl z-50 flex flex-col"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                分支管理
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 分支列表 */}
            <div className="flex-1 overflow-y-auto p-4">
              {branches.length === 0 ? (
                <div className="text-center text-gray-500 mt-8">
                  <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <p>暂无分支对话</p>
                  <p className="text-sm mt-1">在消息上点击分支按钮创建</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {branches.map((branch) => (
                    <motion.div
                      key={branch.id}
                      className={`
                        p-3 rounded-lg border transition-all duration-200 cursor-pointer
                        ${branch.isActive 
                          ? 'border-blue-300 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                      whileHover={{ scale: 1.02 }}
                      onClick={() => !branch.isActive && onSwitchBranch(branch.id)}
                    >
                      {/* 分支名称 */}
                      <div className="flex items-center justify-between mb-2">
                        {editingBranchId === branch.id ? (
                          <input
                            type="text"
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            autoFocus
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleConfirmRename();
                              } else if (e.key === 'Escape') {
                                handleCancelRename();
                              }
                            }}
                            onBlur={handleConfirmRename}
                            onClick={(e) => e.stopPropagation()}
                          />
                        ) : (
                          <h3 className={`font-medium ${branch.isActive ? 'text-blue-700' : 'text-gray-800'}`}>
                            {branch.name}
                          </h3>
                        )}

                        {/* 操作按钮 */}
                        <div className="flex items-center space-x-1">
                          {branch.isActive && (
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                              当前
                            </span>
                          )}
                          
                          {editingBranchId !== branch.id && (
                            <>
                              {onRenameBranch && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStartRename(branch);
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                                  title="重命名"
                                >
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                              )}
                              
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteBranch(branch.id, branch.name);
                                }}
                                className="p-1 hover:bg-red-100 text-red-600 rounded transition-colors"
                                title="删除分支"
                              >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </>
                          )}
                        </div>
                      </div>

                      {/* 分支信息 */}
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>消息数量: {branch.messageCount}</div>
                        <div>创建时间: {formatTime(branch.createdAt)}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* 底部提示 */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="text-xs text-gray-600">
                💡 提示：点击分支可以切换到该对话线程
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default BranchSidebar;
