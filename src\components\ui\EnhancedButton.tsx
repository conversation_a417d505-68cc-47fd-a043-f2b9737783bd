"use client";

import React, { useState, useRef } from 'react';

// 简单的类名合并工具函数
const cn = (...classes: (string | object | undefined | null | false)[]): string => {
  return classes
    .filter(Boolean)
    .map(cls => {
      if (typeof cls === 'string') return cls;
      if (typeof cls === 'object' && cls !== null) {
        return Object.entries(cls)
          .filter(([, value]) => Boolean(value))
          .map(([key]) => key)
          .join(' ');
      }
      return '';
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'text';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  success?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  ripple?: boolean;
}

/**
 * 增强型按钮组件
 * 基于设计思考的现代化按钮，包含丰富的交互效果和状态管理
 */
export const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  success = false,
  icon,
  children,
  ripple = true,
  className,
  disabled,
  onClick,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const rippleId = useRef(0);

  // 处理点击事件和波纹效果
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);

    // 创建波纹效果
    if (ripple && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const newRipple = { id: rippleId.current++, x, y };
      setRipples(prev => [...prev, newRipple]);

      // 移除波纹
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 400);
    }

    onClick?.(e);
  };

  // 基础样式类 - 基于设计思考优化
  const baseClasses = cn(
    // 基础样式 - 统一的设计语言
    "relative overflow-hidden font-medium transition-all duration-250 ease-out",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:cursor-not-allowed disabled:opacity-50",
    "transform-gpu will-change-transform", // 性能优化

    // 统一尺寸变体 - 保持一致的视觉权重
    {
      'px-3 py-1.5 text-sm h-8 rounded-lg': size === 'small',
      'px-4 py-2 text-base h-10 rounded-lg': size === 'medium',
      'px-6 py-3 text-lg h-12 rounded-xl': size === 'large',
    },

    // 按钮变体样式 - 基于设计系统规范
    {
      // Primary Button - 主要操作按钮（最高视觉权重）
      'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md': variant === 'primary',
      'hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 hover:shadow-lg hover:-translate-y-0.5 hover:scale-105': variant === 'primary' && !disabled && !loading,
      'focus:ring-blue-500 focus:ring-opacity-50': variant === 'primary',

      // Secondary Button - 次要操作按钮（中等视觉权重）
      'bg-transparent border-2 border-blue-500 text-blue-600 shadow-sm backdrop-blur-sm': variant === 'secondary',
      'hover:bg-blue-50 hover:border-blue-600 hover:text-blue-700 hover:-translate-y-0.5': variant === 'secondary' && !disabled && !loading,
      'focus:ring-blue-500 focus:ring-opacity-30': variant === 'secondary',

      // Danger Button - 危险操作按钮（警告视觉权重）
      'bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white shadow-md': variant === 'danger',
      'hover:from-red-600 hover:via-red-700 hover:to-red-800 hover:shadow-lg hover:-translate-y-0.5': variant === 'danger' && !disabled && !loading,
      'focus:ring-red-500 focus:ring-opacity-50': variant === 'danger',

      // Text Button - 文本按钮（最低视觉权重）
      'bg-transparent text-blue-600 rounded-md': variant === 'text',
      'hover:bg-blue-50 hover:text-blue-700': variant === 'text' && !disabled && !loading,
      'focus:ring-blue-500 focus:ring-opacity-30': variant === 'text',
    },

    // 成功状态
    {
      'bg-gradient-to-br from-green-500 to-green-600 text-white': success && !loading,
    },

    // 按压效果 - 即时反馈
    {
      'scale-95 duration-150': isPressed && !disabled && !loading,
    },

    className
  );

  return (
    <button
      ref={buttonRef}
      className={baseClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      {...props}
    >
      {/* 波纹效果 */}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute pointer-events-none animate-ping"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
          }}
        >
          <span className="absolute inline-flex h-full w-full rounded-full bg-white opacity-30" />
        </span>
      ))}

      {/* 按钮内容 */}
      <span className="relative flex items-center justify-center space-x-2">
        {/* 加载状态 - 优化的加载动画 */}
        {loading && (
          <div className="flex items-center">
            <svg
              className="animate-spin h-4 w-4 mr-1"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="3"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            {variant === 'primary' && (
              <span className="text-xs opacity-90 animate-pulse">处理中...</span>
            )}
          </div>
        )}

        {/* 成功状态 - 庆祝动画 */}
        {success && !loading && (
          <div className="flex items-center">
            <svg
              className="h-4 w-4 mr-1 animate-bounce text-green-400"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2.5}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <span className="text-xs opacity-90">完成</span>
          </div>
        )}

        {/* 图标 */}
        {icon && !loading && !success && (
          <span className="flex-shrink-0">{icon}</span>
        )}

        {/* 文字内容 */}
        <span className={cn(
          "transition-opacity duration-200",
          { "opacity-0": loading }
        )}>
          {children}
        </span>
      </span>
    </button>
  );
};

export default EnhancedButton;
