"use client";

import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

/**
 * 核心故事梗概AI服务
 * 两个核心功能：
 * 1. 生成核心梗概选项（ACE框架作为消息前置）
 * 2. 根据选定核心梗填充Synopsis字段
 */
export class SynopsisAIService extends UnifiedAIService {
  constructor() {
    super(AIServiceType.SYNOPSIS);
  }





  /**
   * 构建Synopsis系统提示词
   */
  private buildSynopsisSystemPrompt(): string {
    return `求你帮我作为一个评书人的脑洞人员，深度研究过起点、晋江、纵横、番茄等平台的数万部爆款作品。
    精通读者心理，深谙市场规律，专注于创造有实际商业价值的故事核心。
    的你
作为一个评书人，请直接线拆出这个故事的核心，以什么催动剧情线，660字内讲讲清除，ABCD等如何发生的关系，干了什么，全文 从那里开始核心梗


【核心能力】
- 市场洞察：深度分析读者喜好和消费习惯，避免脱离市场的天马行空创意
- 心理揣摩：精准把握不同读者群体的心理需求和情感触点
- 实用导向：所有创意都基于实际市场表现和商业数据
- ACE深度学习：从优秀ACE框架作品中提炼成功模式和读者共鸣点

【创作原则】
1. 市场导向：基于实际数据和成功案例，避免过于理想化的创意
2. 读者心理：深度揣摩目标受众的心理需求和情感期待
3. 商业价值：确保每个脑洞都有明确的市场定位和盈利潜力
4. 实用落地：创意必须可执行，有具体的应用场景和效果
5. ACE传承：学习优秀作品的成功要素，但结合当前市场环境
6. 充分在深度学习ACE框架优秀作品的成功模式，后进行揣摩读者共鸣点/读者受众心理学，估算心理原型设计基础，策划心理学的掌握，对读者进行完全压制性掌握操控他的客观兴奋感，触及他的心理受众欲望底线

【输出标准】
- 脑洞方向：明确的创意方向和市场定位
- 脑洞内容：具体可执行的核心创意
- 应用效果：预期的市场表现和读者反应
- 心理学：操控心理学的极致运用，通过对ACE框架剧情的理解，进行深层创建合适的脑洞方向，进行面向受众的情感，心理操控。

作为一个评书人，请直接线拆出这个故事的核心，以什么催动剧情线，660字内讲讲清除，ABCD等如何发生的关系，干了什么，全文 从那里开始核心梗

格式请不要以，不是xxx,而是xxxx，你这是内容，不是解释，跟不是说明文，不需要解释性语气，等


然后总体总结全文的走向和他的呈现效果
`;
  }

  /**
   * 第一步：生成核心梗概选项
   * ACE框架作为消息前置，不给用户看
   */
  async generateCoreIdeas(
    userInput: string,
    availableFrameworks: any[] = [],
    onChunk?: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: any[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<{
    success: boolean;
    coreIdeas?: CoreIdea[];
    error?: string;
  }> {
    try {
      // 构建系统提示词
      const systemPrompt = this.buildSynopsisSystemPrompt();

      // 构建用户消息（包含ACE框架学习和用户需求）
      const userMessage = this.buildCoreIdeaUserMessage(userInput);

      // 构建消息数组
      const messages = [
        { role: 'system', content: systemPrompt }
      ];

      // 添加ACE框架学习消息（作为system消息）
      // 优先使用options中的selectedFrameworks，回退到availableFrameworks
      const frameworksToUse = options?.selectedFrameworks || availableFrameworks;
      if (frameworksToUse && frameworksToUse.length > 0) {
        // 🔥 新增：使用分段消息发送和确认机制，参考AI大纲和AI对话的方式
        const aceFrameworkMessages = this.buildSeparatedACEFrameworkMessages(frameworksToUse);
        messages.push(...aceFrameworkMessages);
      }

      // 添加用户消息
      messages.push({ role: 'user', content: userMessage });

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        onChunk || (() => {}),
        {
          maxTokens: options?.maxTokens || 50000,      // 使用传入的参数或默认值
        }
      );

      if (!response.success) {
        return {
          success: false,
          error: response.error || '核心梗生成失败'
        };
      }

      // 解析响应
      const coreIdeas = this.parseCoreIdeasResponse(response.text || '');

      return {
        success: true,
        coreIdeas
      };

    } catch (error: any) {
      console.error('❌ 核心梗生成失败:', error);
      return {
        success: false,
        error: error.message || '核心梗生成失败'
      };
    }
  }

  /**
   * 构建ACE框架学习消息（作为system消息）- 优化版本
   * 支持框架分离学习，避免技巧杂糅
   */
  private buildACEFrameworkMessage(availableFrameworks: any[]): { role: string; content: string } {
    if (!availableFrameworks || availableFrameworks.length === 0) {
      return { role: 'user', content: '' };
    }

    let frameworkContent = '【ACE框架分层学习】\n\n';

    // 多框架分离学习模式
    if (availableFrameworks.length > 1) {
      frameworkContent += `🎯 **多框架分离学习模式**\n`;
      frameworkContent += `您选择了${availableFrameworks.length}个ACE框架作为参考。为避免技巧杂糅，请分别学习每个框架的独特特征：\n\n`;
    }

    availableFrameworks.forEach((framework, index) => {
      const frameworkName = framework.frameworkPattern || framework.frameworkName || `框架${index + 1}`;
      frameworkContent += `=== 框架${index + 1}: ${frameworkName} ===\n`;
      frameworkContent += `【独立学习区域 - 请专门理解此框架】\n`;

      // 1. 剧情分析技巧
      if (framework.plotAnalysis) {
        frameworkContent += `\n【剧情分析技巧】\n`;

        if (framework.plotAnalysis.plotPointsWithGuidance) {
          framework.plotAnalysis.plotPointsWithGuidance.forEach((point: any, pointIndex: number) => {
            frameworkContent += `模仿剧情点${pointIndex + 1}: ${point.content}\n`;
            if (point.specificDescription) {
              frameworkContent += `模仿具体描写技巧: ${point.specificDescription}\n`;
            }
            if (point.avoidanceGuidance) {
              frameworkContent += `严格遵循的避免指导: ${point.avoidanceGuidance}\n`;
            }
          });
        }

        if (framework.plotAnalysis.conflictTypes) {
          frameworkContent += `模仿冲突类型: ${framework.plotAnalysis.conflictTypes.join(', ')}\n`;
        }

        if (framework.plotAnalysis.plotPoints) {
          frameworkContent += `模仿核心剧情点: ${framework.plotAnalysis.plotPoints.join(', ')}\n`;
        }
      }

      // 2. 风格分析技巧
      if (framework.styleAnalysis) {
        frameworkContent += `\n【风格分析技巧】\n`;
        if (framework.styleAnalysis.writingStyle) {
          frameworkContent += `模仿写作风格: ${framework.styleAnalysis.writingStyle}\n`;
        }
        if (framework.styleAnalysis.expressionFeatures) {
          frameworkContent += `模仿表现手法: ${framework.styleAnalysis.expressionFeatures}\n`;
        }
        if (framework.styleAnalysis.practicalMethods) {
          frameworkContent += `实用技巧: ${framework.styleAnalysis.practicalMethods}\n`;
        }
        if (framework.styleAnalysis.rhythmPatterns) {
          frameworkContent += `模仿节奏模式: ${framework.styleAnalysis.rhythmPatterns.join(', ')}\n`;
        }
      }

      // 3. 对话分析技巧
      if (framework.dialogueAnalysis) {
        frameworkContent += `\n【对话分析技巧】\n`;
        if (framework.dialogueAnalysis.dialogueStructure) {
          frameworkContent += `对话结构: ${framework.dialogueAnalysis.dialogueStructure}\n`;
        }
        if (framework.dialogueAnalysis.writingTechniques) {
          frameworkContent += `对话技巧: ${framework.dialogueAnalysis.writingTechniques}\n`;
        }
        if (framework.dialogueAnalysis.styleAnalysis) {
          frameworkContent += `对话风格: ${framework.dialogueAnalysis.styleAnalysis.dialogueStyle}\n`;
          frameworkContent += `角色语言: ${framework.dialogueAnalysis.styleAnalysis.characterVoice}\n`;
        }
      }

      frameworkContent += `\n⚠️ **重要提醒**：请独立理解此框架的技巧特征，避免与其他框架混合使用。\n\n`;
    });

    // 多框架创作指导
    if (availableFrameworks.length > 1) {
      frameworkContent += `\n🎨 **创作指导**：在生成内容时，请选择最适合的框架技巧，避免不同框架技巧的混合使用。确保每个创意都有明确的框架来源和技巧特征。`;
    } else {
      frameworkContent += `请深度学习以上ACE框架的技巧和方法，在生成核心梗概时融入这些技巧特征。`;
    }

    return {
      role: 'user',
      content: frameworkContent
    };
  }

  /**
   * 构建分段ACE框架消息（参考AI大纲和AI对话的方式）
   * 按框架数量进行分段消息发送和确认
   */
  private buildSeparatedACEFrameworkMessages(availableFrameworks: any[]): Array<{ role: string; content: string }> {
    if (!availableFrameworks || availableFrameworks.length === 0) {
      return [];
    }

    // 如果只有一个框架，使用原有的方法
    if (availableFrameworks.length === 1) {
      const singleFrameworkMessage = this.buildACEFrameworkMessage(availableFrameworks);
      const confirmationMessage = {
        role: 'assistant',
        content: `我已理解这个ACE框架的技巧特征。我将在创作时：
1. 深度学习框架的独特技巧
2. 基于框架特征进行深度的读者心理分析和市场定位
3. 确保生成内容的专业性和针对性
4. 运用框架中的成功模式和技巧`
      };
      return [singleFrameworkMessage, confirmationMessage];
    }

    const messages: Array<{ role: string; content: string }> = [];

    // 添加框架分离说明
    messages.push({
      role: 'user',
      content: `【🎯 ACE框架分离学习模式】
您选择了${availableFrameworks.length}个ACE框架作为参考。为避免技巧杂糅和准度混乱，我将为每个框架单独发送消息并分别确认。

📋 **分离原则**：
• 每个框架独立展示，明确标识归属
• 每个框架单独确认，避免混淆
• 专门学习各框架的独特技巧
• 避免不同框架技巧的混合使用

🎨 **学习目标**：深度理解每个框架的特点，在创作时选择最适合的框架技巧，确保效果最佳。`
    });

    // 为每个框架单独构建消息
    availableFrameworks.forEach((framework, index) => {
      const frameworkName = framework.frameworkPattern || framework.frameworkName || `框架${index + 1}`;

      // 添加框架标识消息
      const colorEmojis = ['🔵', '🟢', '🟡', '🔴', '🟠', '🟣', '🔶', '🔷', '🔸'];
      const colorEmoji = colorEmojis[index % colorEmojis.length];

      messages.push({
        role: 'user',
        content: `【${colorEmoji} ACE框架 ${index + 1}/${availableFrameworks.length} - ${frameworkName}】
📖 来源：${framework.patternType || 'ACE框架分析'}
🎯 特征：${framework.frameworkPattern || '具体写作手法'}
💡 重点：专门学习此框架的独特技巧，避免与其他框架混合`
      });

      let frameworkContent = ``;

      // 1. 剧情分析技巧
      if (framework.plotAnalysis) {
        frameworkContent += `\n【剧情分析技巧】\n`;

        if (framework.plotAnalysis.plotPointsWithGuidance) {
          framework.plotAnalysis.plotPointsWithGuidance.forEach((point: any, pointIndex: number) => {
            frameworkContent += `剧情点${pointIndex + 1}: ${point.content}\n`;
            if (point.specificDescription) {
              frameworkContent += `具体描写技巧: ${point.specificDescription}\n`;
            }
            if (point.avoidanceGuidance) {
              frameworkContent += `避免指导: ${point.avoidanceGuidance}\n`;
            }
          });
        }

        if (framework.plotAnalysis.conflictTypes) {
          frameworkContent += `冲突类型: ${framework.plotAnalysis.conflictTypes.join(', ')}\n`;
        }

        if (framework.plotAnalysis.plotPoints) {
          frameworkContent += `核心剧情点: ${framework.plotAnalysis.plotPoints.join(', ')}\n`;
        }
      }

      // 2. 风格分析技巧
      if (framework.styleAnalysis) {
        frameworkContent += `\n【风格分析技巧】\n`;
        if (framework.styleAnalysis.writingStyle) {
          frameworkContent += `写作风格: ${framework.styleAnalysis.writingStyle}\n`;
        }
        if (framework.styleAnalysis.expressionFeatures) {
          frameworkContent += `表现手法: ${framework.styleAnalysis.expressionFeatures}\n`;
        }
        if (framework.styleAnalysis.practicalMethods) {
          frameworkContent += `实用技巧: ${framework.styleAnalysis.practicalMethods}\n`;
        }
        if (framework.styleAnalysis.rhythmPatterns) {
          frameworkContent += `节奏模式: ${framework.styleAnalysis.rhythmPatterns.join(', ')}\n`;
        }
      }

      // 3. 对话分析技巧
      if (framework.dialogueAnalysis) {
        frameworkContent += `\n【对话分析技巧】\n`;
        if (framework.dialogueAnalysis.dialogueStructure) {
          frameworkContent += `对话结构: ${framework.dialogueAnalysis.dialogueStructure}\n`;
        }
        if (framework.dialogueAnalysis.writingTechniques) {
          frameworkContent += `对话技巧: ${framework.dialogueAnalysis.writingTechniques}\n`;
        }
        if (framework.dialogueAnalysis.styleAnalysis) {
          frameworkContent += `对话风格: ${framework.dialogueAnalysis.styleAnalysis.dialogueStyle}\n`;
          frameworkContent += `角色语言: ${framework.dialogueAnalysis.styleAnalysis.characterVoice}\n`;
        }
      }

      frameworkContent += `\n⚠️ **重要提醒**：请独立理解此框架的技巧特征，避免与其他框架混合使用。`;

      messages.push({
        role: 'user',
        content: frameworkContent
      });

      // 添加框架独立确认消息
      messages.push({
        role: 'assistant',
        content: `我已完整理解第${index + 1}个ACE框架"${frameworkName}"的技巧特征。

📚 **学习成果**：
• 独立掌握了此框架的剧情分析技巧
• 理解了风格分析和对话技巧的要点
• 明确了避免与其他框架混合的原则
• 建立了清晰的框架应用边界

🎯 **应用承诺**：
我将在创作时专门运用此框架的独特技巧，确保生成内容体现其专业特征和市场价值。`
      });
    });

    // 添加总体学习确认
    messages.push({
      role: 'assistant',
      content: `🎉 **ACE框架分离学习完成**

我已通过分离模式完整学习了所有${availableFrameworks.length}个ACE框架的独特特点。每个框架都有明确的标识和独立的确认，确保了学习的准确性和深度。

📚 **学习成果**：
• 每个框架的技巧特征都被独立理解
• 避免了不同框架间的技巧杂糅
• 建立了清晰的框架应用边界
• 提升了创作时的选择精准度

🎨 **创作策略**：
在后续创作中，我将根据具体需求选择最适合的单一框架技巧，而不是混合使用，确保创作效果的最佳化。

现在我将基于这些ACE框架的指导，为您生成专业的故事梗概和字段内容。`
    });

    return messages;
  }

  /**
   * 构建用户消息（包含ACE框架学习和用户需求）
   */
  private buildCoreIdeaUserMessage(userInput: string): string {
    return `根据用户需求生成3-5个核心故事梗概选项。

用户需求：${userInput}

生成要求：
1. 基于实际市场数据和成功案例，避免天马行空
2. 深度揣摩读者心理和情感需求
3. 充分学习ACE框架优秀作品的成功模式，揣摩读者共鸣点/读者受众心理学，估算心理原型设计基础，策划心理学的掌握，对读者进行完全压制性掌握操控他的客观兴奋感，触及他的心理受众欲望底线

4. 确保每个脑洞都有明确的商业价值和市场定位
5. 创意必须可执行，有具体的应用场景

请返回JSON格式的核心梗数组：
[
  {
    "id": "idea_001",
    "brainholeDirection": "古风权谋+家族复仇+多方博弈，主打人性贪婪与智慧较量，吸引喜欢权谋斗争和人性探讨的读者群体。",
    "brainhole": "主角林墨本是富商之子，因父亲贪欲抓捕神秘女子炼制长生药而遭受折磨。向县令求助却引狼入室，县令与父亲合谋将罪恶商业化。表面救援者卢岩实则想独吞好处。第16章林墨偷听到真相后，从被动受害者转为主动布局者，编造'伏羲肉成神'诱饵，利用三方贪欲让他们在神秘女子的邪术下自食恶果。核心冲突：贪婪与正义的较量，复仇与救赎的博弈。",
    "applicationEffect": "古风背景增加文化底蕴，权谋博弈满足智斗快感，人性贪婪引发深度思考。多方势力复杂关系制造持续紧张感，主角从弱到强的转变提供逆袭爽感。题材具有传统文化特色，易引发讨论和传播。"
  }
]

【重要】每个字段要求：
- brainholeDirection: 类型+卖点+目标读者，50-80字，要新颖有冲击力，避免老套，强调人性博弈和多方势力
- brainhole: 主角身份+核心能力/优势+设定背景+核心冲突+故事基调，120-200字，必须包含至少3个主要角色和他们的利益冲突，体现角色从被动到主动的转变
- applicationEffect: 爽点+读者反应+商业价值，100-150字，强调人性深度和情感冲击力，突出复杂关系的吸引力

【冲突基调要求】：
- 明确核心矛盾：人与人、人与环境、人与自己、理想与现实等
- 突出故事氛围：紧张刺激、温馨治愈、黑暗压抑、轻松幽默等
- 体现价值冲突：正义与利益、真相与安全、个人与集体等

【创作原则（灵活运用，避免常规错误）】：
- 制造冲击感：开头要有抓眼球的元素，避免平淡开场
- 具体化细节：用具体的、有冲击力的细节，避免空泛的形容
- 避免直接解释：不要用"我知道"等作者视角直接告诉读者结论
- 保持悬念感：适当留白，让读者自己思考和想象
- 注重真实感：即使是奇幻元素，也要有现实基础和逻辑支撑`;
  }

  /**
   * 解析核心梗响应
   */
  private parseCoreIdeasResponse(response: string): CoreIdea[] {
    try {
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed.map((item, index) => ({
            id: item.id || `idea_${Date.now()}_${index}`,
            brainholeDirection: item.brainholeDirection || '未定义方向',
            brainhole: item.brainhole || '未定义脑洞',
            applicationEffect: item.applicationEffect || '未定义效果'
          }));
        }
      }

      // 如果JSON解析失败，返回默认核心梗
      return [{
        id: `idea_${Date.now()}`,
        brainholeDirection: '基于用户需求的创意方向',
        brainhole: '根据用户输入生成的核心脑洞',
        applicationEffect: '预期能够吸引目标读者群体'
      }];

    } catch (error) {
      console.error('解析核心梗响应失败:', error);
      return [];
    }
  }

  /**
   * 第二步：根据选定核心梗生成定制化选项
   */
  async generateCustomizationOptions(
    selectedIdea: CoreIdea,
    availableFrameworks: any[] = [],
    onChunk?: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<{
    success: boolean;
    customizationOptions?: any;
    error?: string;
  }> {
    try {
      // 构建定制化选项生成的系统提示词
      const systemPrompt = `你是一位资深的悬疑小说编辑，有15年的网文行业经验，专门负责悬疑、惊悚、推理类作品的策划和指导。你深谙读者心理，知道什么样的开头能让人欲罢不能，什么样的悬念设置能让人夜不能寐。

你的任务是基于作者提供的核心梗概，从以下4个具体维度为作者提供定制化选择：
1. 【开头冲击技巧】- 如何在前100字内抓住读者
2. 【悬念制造手法】- 如何让读者"分不清真假"
3. 【主角能力定位】- 避免万能人设，突出专业优势
4. 【冲突升级节奏】- 如何层层递进制造紧张感

每个维度必须提供2-3个具体选项，每个选项都要有明确的实现方法和预期效果。

【核心创作理念】：
- "平凡里的诡异，诡异里的平凡"：制造真幻感，让读者分不清真假
- 避免能力过度具象化：不要万能人设定，要有明确局限性
- 针对性悬念：每个诡异元素都要有具体的、不平凡的、冲击性的细节
- 避免作者视角：不要用"我知道"来解释，要让读者自己感受
- 制造扭曲现实感：让读者产生"我分不清到底是真是假"的困惑感

【选择生成要求】：
1. 选择维度要针对当前梗概的特点，体现高级创作技巧
2. 每个选项要具体明确，有实际创作指导意义，避免平庸描述
3. 选项要体现"真幻感"、"扭曲现实"、"点而不破"等高级悬念技巧
4. 选项之间要有明显区别，给用户真正有价值的选择权
5. 避免空泛概念，要有具体的创作方向和技巧应用

返回JSON格式：
{
  "categories": [
    {
      "id": "opening_impact",
      "title": "开头冲击技巧",
      "description": "前100字内如何抓住读者，让人无法放下",
      "options": [
        {
          "id": "professional_anomaly",
          "label": "专业人士发现异常",
          "description": "急诊科医生林小雨：'凌晨三点，第五个车祸伤者。我看着他手心紧握的游乐园门票，日期是今天。妻子哭着说他从不去那种地方。' - 专业敏感+矛盾细节立即制造悬念"
        },
        {
          "id": "absurd_rule_game",
          "label": "荒诞规则游戏",
          "description": "游戏参与者张明：'规则很简单：互扇巴掌，挨一下奖励一万块。旁边女孩兴奋地说要给闺蜜扇成亿万富翁。我看到半空中的红字：【趁现在多笑笑，后面就笑不出来喽】' - 荒诞+恐怖预告"
        }
      ]
    },
    {
      "id": "suspense_method",
      "title": "悬念制造手法",
      "description": "如何让读者产生'我分不清真假'的困惑感",
      "options": [
        {
          "id": "contradiction_details",
          "label": "矛盾细节法",
          "description": "心理咨询师发现：'她在哭诉男友出轨，眼泪很真实。但我注意到她眼角没有泪痕，妆容完美无缺。' - 通过专业观察发现矛盾，让读者质疑表象"
        },
        {
          "id": "information_asymmetry",
          "label": "信息不对等",
          "description": "只有主角知道的真相：'我是唯一看到那条短信的人：【游戏开始，第一个死的会是谁呢？】其他人还在开心地聊天。' - 制造主角与其他人的信息差"
        }
      ]
    },
    {
      "id": "protagonist_ability",
      "title": "主角能力定位",
      "description": "基于现实职业的专业优势，有局限性",
      "options": [
        {
          "id": "medical_professional",
          "label": "医疗专业人士",
          "description": "急诊科医生/法医/护士 - 能从伤口、症状、生理反应判断异常，但在非医疗领域是普通人。优势：专业判断力；局限：只在医疗相关场景有效"
        },
        {
          "id": "psychology_expert",
          "label": "心理学专家",
          "description": "心理咨询师/犯罪心理学家 - 能从微表情、行为模式察觉谎言和异常心理，但无法读心。优势：人性洞察；局限：需要观察时间，对反社会人格效果有限"
        },
        {
          "id": "security_background",
          "label": "安保/军警背景",
          "description": "退伍军人/保安/警察 - 危险嗅觉敏锐，观察力强，但在复杂推理上不如专业人士。优势：危机应对；局限：缺乏专业分析能力"
        }
      ]
    },
    {
      "id": "conflict_escalation",
      "title": "冲突升级节奏",
      "description": "如何层层递进制造紧张感",
      "options": [
        {
          "id": "step_by_step_discovery",
          "label": "逐步发现真相",
          "description": "第一层：发现异常细节 → 第二层：意识到危险 → 第三层：发现自己也在局中 → 第四层：真相颠覆认知。每层都有具体的发现和冲击"
        },
        {
          "id": "time_pressure_increase",
          "label": "时间压力递增",
          "description": "从'有点奇怪'到'必须马上行动'：先是好奇，然后担心，接着恐惧，最后是生死时速。每个阶段都有明确的时间节点和压力来源"
        }
      ]
    }
  ]
}`;

      // 构建用户消息
      const userMessage = `请基于以下核心梗概，为作者提供4个维度的具体定制化选择：

核心梗概信息：
脑洞方向：${selectedIdea.brainholeDirection}
脑洞内容：${selectedIdea.brainhole}
应用效果：${selectedIdea.applicationEffect}

【必须遵循的具体要求】：
1. 每个选项都要有具体的人物身份（如：急诊科医生林小雨、心理咨询师王明等）
2. 每个选项都要有具体的场景和对话（如：'凌晨三点，第五个车祸伤者...'）
3. 主角能力必须基于现实职业，有明确的优势和局限性
4. 避免虚幻的、假大空的描述，要有强烈的现实感和逻辑支撑

【参考具体示例】：
- 开头冲击：急诊科医生发现伤者手握游乐园门票，但妻子说他从不去那种地方
- 悬念制造：心理师注意到她在哭但眼角没有泪痕，妆容完美无缺
- 能力定位：医生能从伤口判断异常，但在非医疗领域是普通人
- 冲突升级：从发现异常细节→意识到危险→发现自己在局中→真相颠覆认知

重要：每个选项都要像上面的示例一样具体，有真实的人物、场景、对话，让读者能立即想象出画面。`;

      // 构建消息数组
      const messages = [
        { role: 'system', content: systemPrompt }
      ];

      // 添加ACE框架学习消息（作为system消息）
      if (availableFrameworks && availableFrameworks.length > 0) {
        // 🔥 使用分段消息发送和确认机制，参考AI大纲和AI对话的方式
        const aceFrameworkMessages = this.buildSeparatedACEFrameworkMessages(availableFrameworks);
        messages.push(...aceFrameworkMessages);
      }

      // 添加用户消息
      messages.push({ role: 'user', content: userMessage });

      // 发送请求 - 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        onChunk || (() => {}),
        {
          maxTokens: options?.maxTokens || 50000,
        }
      );

      if (!response.success) {
        return {
          success: false,
          error: response.error || '生成定制化选项失败'
        };
      }

      // 解析响应
      const customizationOptions = this.parseCustomizationOptionsResponse(response.text || '');

      return {
        success: true,
        customizationOptions
      };

    } catch (error: any) {
      console.error('❌ 生成定制化选项失败:', error);
      return {
        success: false,
        error: error.message || '生成定制化选项失败'
      };
    }
  }

  /**
   * 解析定制化选项响应
   */
  private parseCustomizationOptionsResponse(response: string): any {
    try {
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed;
      }

      // 如果JSON解析失败，返回默认选项
      return {
        categories: [
          {
            id: "story_style",
            title: "故事风格选择",
            description: "选择故事的整体风格和基调",
            options: [
              {
                id: "suspense_thriller",
                label: "悬疑惊悚",
                description: "强调紧张刺激的氛围和悬念设置"
              },
              {
                id: "realistic_drama",
                label: "现实剧情",
                description: "注重真实感和人物情感的深度刻画"
              }
            ]
          }
        ]
      };

    } catch (error) {
      console.error('解析定制化选项响应失败:', error);
      return {
        categories: []
      };
    }
  }

  /**
   * 第三步：根据选定核心梗和定制化选择填充Synopsis字段
   */
  async fillSynopsisFields(
    finalIdea: CoreIdea,
    availableFrameworks: any[] = [],
    onChunk?: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: any[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
      customizations?: any; // 用户的定制化选择
    }
  ): Promise<{
    success: boolean;
    synopsisFields?: SynopsisFields;
    error?: string;
  }> {
    try {
      // 构建系统提示词
      const systemPrompt = this.buildSynopsisSystemPrompt();

      // 构建用户消息
      const userMessage = this.buildFieldsPrompt(finalIdea, options?.customizations);

      // 构建消息数组
      const messages = [
        { role: 'system', content: systemPrompt }
      ];

      // 添加ACE框架学习消息（作为system消息）
      // 优先使用options中的selectedFrameworks，回退到availableFrameworks
      const frameworksToUse = options?.selectedFrameworks || availableFrameworks;
      if (frameworksToUse && frameworksToUse.length > 0) {
        // 🔥 新增：使用分段消息发送和确认机制，参考AI大纲和AI对话的方式
        const aceFrameworkMessages = this.buildSeparatedACEFrameworkMessages(frameworksToUse);
        messages.push(...aceFrameworkMessages);
      }

      // 添加用户消息
      messages.push({ role: 'user', content: userMessage });

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        onChunk || (() => {}),
        {
          maxTokens: options?.maxTokens || 50000,      // 使用传入的参数或默认值
        }
      );

      if (!response.success) {
        return {
          success: false,
          error: response.error || '字段填充失败'
        };
      }

      const synopsisFields = this.parseFieldsResponse(response.text || '');

      return {
        success: true,
        synopsisFields
      };

    } catch (error: any) {
      console.error('❌ 字段填充失败:', error);
      return {
        success: false,
        error: error.message || '字段填充失败'
      };
    }
  }

  /**
   * 构建字段填充提示词
   */
  private buildFieldsPrompt(finalIdea: CoreIdea, customizations?: any): string {
    let customizationText = '';

    if (customizations && Object.keys(customizations).length > 0) {
      customizationText = `

用户定制化选择：
${Object.entries(customizations).map(([categoryId, option]: [string, any]) =>
  `【${categoryId}】选择了：${option.optionLabel}
  具体要求：${option.optionDescription}`
).join('\n\n')}

请严格按照用户的定制化选择来调整生成的内容风格和方向，确保生成的内容体现用户选择的具体要求。`;
    }

    return `根据确定的核心梗概，填充Synopsis节点的所有字段。

核心梗概信息：
脑洞方向：${finalIdea.brainholeDirection}
脑洞内容：${finalIdea.brainhole}
应用效果：${finalIdea.applicationEffect}${customizationText}


注意，你的生成内容，必须避免不过度生硬
作为一个评书人，你喜欢直接线拆出这个故事的核心，以什么催动剧情线，660字内讲讲清除，ABCD等如何发生的关系，干了什么，全文 从那里开始核心梗


然后总体总结全文的走向和他的呈现效果



剧情核心梗概心理学：操控心理学的极致运用，通过对ACE框架剧情的理解，对脑洞方向和效果，进行细化的设定核心梗概设定以完整故事描述，进行面向受众的情感，心理操控。

请基于以上信息，填充以下Synopsis字段，返回JSON格式：
{
  "synopsisBrainhole": "主角林墨是富商之子，因父亲林老爷贪图长生抓捕神秘女子而遭受无尽折磨。向县令求助却引狼入室，县令与父亲合谋将罪恶商业化。表面救援者卢岩实则想独吞神秘女子。林墨在第16章偷听到卢岩真实计划后，从被动受害者转为主动布局者，利用'伏羲肉能成神'的诱饵，让三股贪婪势力汇聚一堂，在神秘女子的邪术下自食恶果。",
  "synopsisGenre": "古风权谋+家族复仇+多方博弈",
  "synopsisOpening": "父亲又在折磨我了。为了熬制那锅汤，他每天都要在我身上割下一块肉。「爹，求您了，放过我吧。」我跪在地上哀求。他看都不看我一眼：「你这不孝子，为了让爹长生不老，这点苦都不愿意吃？」我绝望地看向笼中的女子，她对我摇摇头，眼中满是怜悯。",
  "synopsisCoreOutline": "林墨从被父亲折磨的受害者，通过偷听真相转变为复仇布局者。他利用父亲、县令、卢岩三方的贪欲，编造'伏羲肉成神'的诱饵，最终让三股势力在神秘女子的邪术下自相残杀，完成复仇。",
  "synopsisEnding": "当林墨以为复仇成功时，神秘女子缓缓开口：「孩子，你以为这一切都是巧合吗？从你父亲抓我的那一刻起，这场游戏就开始了。你的痛苦、你的成长、你的复仇，都在我的计划之内。现在，你已经成为了真正的伏羲传人。」原来林墨从一开始就是被选中的继承者。",
  "synopsisStoryDescription": "古风权谋与家族复仇的完美结合，多方势力的利益博弈制造持续紧张感。主角从弱到强的转变提供逆袭爽感，人性贪婪的深度刻画引发读者思考。传统文化背景增加底蕴，复杂的人际关系网络满足智斗需求。",
  "synopsisAceReferences": "运用'以弱胜强'的经典逆袭模式，采用'多方博弈'的复杂权谋设定，借鉴'利用敌人弱点'的智斗技巧，融入'身份反转'的震撼结局设计。"
}

【重要】请严格按照以上示例的格式和详细程度填写，每个字段都要具体明确，避免模糊描述：

【基本要求（避免常规错误，确保70%成功率）】
- synopsisBrainhole: 核心创意设定，100-150字。避免：过于复杂的设定、缺乏冲突的平淡描述
- synopsisGenre: 类型标签，用"+"连接。避免：过于宽泛或过于狭窄的分类
- synopsisOpening: 抓眼球的开头，60-100字。避免：平淡无奇的开场、直接解释的作者视角
- synopsisCoreOutline: 故事主线，80-120字。避免：逻辑混乱、缺乏转折的流水账
- synopsisEnding: 有冲击力的结尾，60-100字。避免：草率收尾、缺乏想象空间
- synopsisStoryDescription: 故事价值描述，120-180字。避免：空洞的形容词堆砌、脱离实际的夸大
- synopsisAceReferences: 创作技巧说明，100-150字。避免：生硬的理论堆砌、与内容不符的技巧

【核心能力多样化示例】：
- 专业技能：医生的诊断能力、律师的逻辑思维、程序员的系统思维、心理师的人性洞察、商人的利益嗅觉
- 天赋异禀：过目不忘、感知敏锐、学习能力强、直觉准确、共情能力强、察言观色
- 特殊经历：军人训练、底层摸爬、海外背景、家族传承、创业失败、宫廷历练、商场沉浮
- 性格优势：坚韧不拔、冷静理性、善于沟通、敢于冒险、细致入微、善于伪装、忍辱负重、复仇决心
- 身份便利：特殊职业、人脉关系、资源渠道、信息优势、地位权力、血缘关系、师承关系
- 智慧型能力：观察力、分析力、心理洞察、策略思维、布局能力、利用敌人弱点、将计就计

【冲突基调多样化示例】：
- 紧张刺激：生死较量、时间紧迫、步步惊心、危机四伏
- 温馨治愈：情感共鸣、成长蜕变、温暖人心、正能量满满
- 黑暗压抑：人性扭曲、社会阴暗、绝望挣扎、现实残酷
- 轻松幽默：诙谐搞笑、轻松愉快、反差萌、日常温馨
- 热血燃烧：激情澎湃、奋斗拼搏、逆袭成长、梦想追求
- 权谋复仇：多方博弈、利益纠葛、智斗较量、复仇快感、人性贪婪、背叛与反击
- 家族恩怨：血缘纠葛、世代仇恨、亲情背叛、传承与颠覆、忠诚与利益的冲突

【参考示例（仅供启发，不必照搬）】：
- 游戏流：「我参加了一场游戏，规则是互扇巴掌赚钱。我突然看到半空中的红字：【他们现在好开心啊，后面就笑不出来喽。】」
- 日常惊悚：「亲热时不小心抓破了男朋友背上文身，他突然大发雷霆。主播问：『你男朋友背上的文身，是不是一个瓶子？』」
- 职场悬疑：「他手心里握着儿童游乐园门票，日期是今天。『我老公不去这种地方的。』妻子哭着说。我看看她，她在哭，但眼角没有眼泪。」
- 权谋复仇：「父亲又在折磨我了。为了熬制那锅汤，他每天都要在我身上割下一块肉。『爹，求您了，放过我吧。』我跪在地上哀求。他看都不看我一眼：『你这不孝子，为了让爹长生不老，这点苦都不愿意吃？」
- 家族博弈：「县令大人，我父亲抓了一个妖女，您看...』我跪在县衙门前。县令眼中闪过贪婪：『哦？妖女？带我去看看。』   」

【避免的常见错误】：
- 开头太平淡：避免"某天早上我醒来"这类无聊开场
- 直接解释：避免"我知道这很危险"这类作者视角
- 空洞形容：避免"诡异的气氛"这类没有具体内容的描述

填充要求：
1. synopsisBrainhole: 将脑洞内容扩展为详细的设定描述
2. synopsisGenre: 根据脑洞方向确定明确的故事类型
3. synopsisCoreOutline: 将脑洞内容转化为精炼的核心梗概
4. synopsisStoryDescription: 基于应用效果描述完整的故事框架
5. synopsisAceReferences: 说明运用了哪些ACE框架的技巧和模式`;
  }

  /**
   * 解析字段填充响应
   */
  private parseFieldsResponse(response: string): SynopsisFields {
    try {
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          synopsisBrainhole: parsed.synopsisBrainhole || '',
          synopsisGenre: parsed.synopsisGenre || '',
          synopsisOpening: parsed.synopsisOpening || '',
          synopsisCoreOutline: parsed.synopsisCoreOutline || '',
          synopsisEnding: parsed.synopsisEnding || '',
          synopsisStoryDescription: parsed.synopsisStoryDescription || '',
          synopsisAceReferences: parsed.synopsisAceReferences || ''
        };
      }

      // 如果JSON解析失败，返回空字段
      return {
        synopsisBrainhole: '',
        synopsisGenre: '',
        synopsisOpening: '',
        synopsisCoreOutline: '',
        synopsisEnding: '',
        synopsisStoryDescription: '',
        synopsisAceReferences: ''
      };

    } catch (error) {
      console.error('解析字段响应失败:', error);
      return {
        synopsisBrainhole: '',
        synopsisGenre: '',
        synopsisOpening: '',
        synopsisCoreOutline: '',
        synopsisEnding: '',
        synopsisStoryDescription: '',
        synopsisAceReferences: ''
      };
    }
  }

}

/**
 * 核心梗概念接口
 */
export interface CoreIdea {
  id: string;
  brainholeDirection: string;  // 脑洞方向
  brainhole: string;          // 脑洞内容
  applicationEffect: string;   // 应用效果
}

/**
 * Synopsis字段接口
 */
export interface SynopsisFields {
  synopsisBrainhole: string;
  synopsisGenre: string;
  synopsisOpening: string;
  synopsisCoreOutline: string;
  synopsisEnding: string;
  synopsisStoryDescription: string;
  synopsisAceReferences: string;
}
