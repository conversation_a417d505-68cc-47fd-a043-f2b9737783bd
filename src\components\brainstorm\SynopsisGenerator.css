/* 简介生成器样式 */

/* 滑块样式 */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #10b981;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #059669;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider::-moz-range-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider::-moz-range-thumb {
  background: #10b981;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: #059669;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 风格按钮动态颜色 */
.style-button-blue {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1d4ed8;
}

.style-button-orange {
  border-color: #f97316;
  background-color: #fff7ed;
  color: #c2410c;
}

.style-button-purple {
  border-color: #8b5cf6;
  background-color: #f3e8ff;
  color: #7c3aed;
}

.style-button-red {
  border-color: #ef4444;
  background-color: #fef2f2;
  color: #dc2626;
}

/* 简介卡片动画 */
.synopsis-card {
  transition: all 0.3s ease;
}

.synopsis-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 关键词高亮动画 */
.keyword-highlight {
  animation: highlightPulse 0.5s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #fef3c7;
  }
  100% {
    background-color: #fef3c7;
  }
}

/* 生成按钮波纹效果 */
.generate-button {
  position: relative;
  overflow: hidden;
}

.generate-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.generate-button:active::before {
  width: 300px;
  height: 300px;
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* 标签页切换动画 */
.tab-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .synopsis-generator {
    flex-direction: column;
  }
  
  .parameter-panel {
    width: 100%;
    max-height: 40vh;
  }
  
  .result-panel {
    width: 100%;
    flex: 1;
  }
  
  .style-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .slider::-webkit-slider-track {
    background: #374151;
  }
  
  .slider::-moz-range-track {
    background: #374151;
  }
  
  .synopsis-card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .keyword-highlight {
    background-color: #92400e;
    color: #fbbf24;
  }
}
