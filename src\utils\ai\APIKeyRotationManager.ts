/**
 * API密钥轮播管理器
 */

import { 
  APIKeyConfig, 
  URLKeyPool, 
  APIKeyRotationConfig, 
  RotationStrategy,
  RequestResult,
  KeyUsageStats,
  RotationManagerOptions,
  HealthCheckResult
} from '../../types/apiKeyRotation';
import { APIKeyRotationConfigManager } from './APIKeyRotationConfig';
import { FailureRecoveryService } from './FailureRecoveryService';

export class APIKeyRotationManager {
  private static instance: APIKeyRotationManager;
  private config: APIKeyRotationConfig;
  private configManager: APIKeyRotationConfigManager;
  private recoveryService: FailureRecoveryService;
  private usageStats: Map<string, KeyUsageStats> = new Map();

  private constructor(options?: RotationManagerOptions) {
    this.configManager = APIKeyRotationConfigManager.getInstance();
    this.recoveryService = FailureRecoveryService.getInstance();
    this.config = this.configManager.load();
    
    // 初始化选项
    if (options?.enableHealthCheck) {
      this.startHealthChecks();
    }
    
    console.log('🔄 API密钥轮播管理器已初始化');
  }

  /**
   * 获取单例实例
   */
  static getInstance(options?: RotationManagerOptions): APIKeyRotationManager {
    if (!APIKeyRotationManager.instance) {
      APIKeyRotationManager.instance = new APIKeyRotationManager(options);
    }
    return APIKeyRotationManager.instance;
  }

  /**
   * 添加API密钥
   */
  addAPIKey(
    url: string, 
    key: string, 
    options?: {
      weight?: number;
      customWaitTime?: number;
      notes?: string;
    }
  ): APIKeyConfig {
    // 确保URL池存在
    if (!this.config.urlPools[url]) {
      this.configManager.addURLPool(this.config, url);
    }

    const keyConfig: APIKeyConfig = {
      id: this.generateKeyId(),
      key,
      url,
      status: 'active',
      lastUsed: new Date(),
      failureCount: 0,
      weight: options?.weight || 1,
      customWaitTime: options?.customWaitTime,
      usageCount: 0,
      successRate: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
      notes: options?.notes
    };

    this.config.urlPools[url].keys.push(keyConfig);
    this.configManager.save(this.config);
    
    // 初始化使用统计
    this.initKeyStats(keyConfig.id);
    
    console.log(`✅ 已添加API密钥: ${keyConfig.id} 到 ${url}`);
    return keyConfig;
  }

  /**
   * 删除API密钥
   */
  removeAPIKey(url: string, keyId: string): boolean {
    const pool = this.config.urlPools[url];
    if (!pool) return false;

    const keyIndex = pool.keys.findIndex(k => k.id === keyId);
    if (keyIndex === -1) return false;

    // 停止相关服务
    this.recoveryService.stopHealthCheck(keyId);
    
    // 删除密钥
    pool.keys.splice(keyIndex, 1);
    
    // 调整当前索引
    if (pool.currentIndex >= pool.keys.length) {
      pool.currentIndex = 0;
    }
    
    this.configManager.save(this.config);
    this.usageStats.delete(keyId);
    
    console.log(`✅ 已删除API密钥: ${keyId}`);
    return true;
  }

  /**
   * 获取下一个可用的API密钥
   */
  getNextAPIKey(url: string): APIKeyConfig | null {
    const pool = this.config.urlPools[url];
    if (!pool || !pool.enabled) {
      console.warn(`⚠️ URL池不存在或已禁用: ${url}`);
      return null;
    }

    // 获取可用密钥
    const availableKeys = this.recoveryService.getAvailableKeys(pool.keys);
    if (availableKeys.length === 0) {
      console.warn(`⚠️ 没有可用的API密钥: ${url}`);
      return null;
    }

    // 根据策略选择密钥
    const selectedKey = this.selectKeyByStrategy(availableKeys, pool.rotationStrategy);
    if (selectedKey) {
      selectedKey.lastUsed = new Date();
      selectedKey.usageCount++;
      this.updateKeyStats(selectedKey.id, 'request');
      this.configManager.save(this.config);
    }

    return selectedKey;
  }

  /**
   * 根据策略选择密钥
   */
  private selectKeyByStrategy(keys: APIKeyConfig[], strategy: RotationStrategy): APIKeyConfig | null {
    if (keys.length === 0) return null;

    switch (strategy) {
      case 'round-robin':
        return this.selectRoundRobin(keys);
      
      case 'weighted':
        return this.selectWeighted(keys);
      
      case 'random':
        return this.selectRandom(keys);
      
      case 'least-used':
        return this.selectLeastUsed(keys);
      
      default:
        return this.selectRoundRobin(keys);
    }
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(keys: APIKeyConfig[]): APIKeyConfig {
    const pool = this.config.urlPools[keys[0].url];
    const key = keys[pool.currentIndex % keys.length];
    pool.currentIndex = (pool.currentIndex + 1) % keys.length;
    return key;
  }

  /**
   * 加权选择
   */
  private selectWeighted(keys: APIKeyConfig[]): APIKeyConfig {
    const totalWeight = keys.reduce((sum, key) => sum + (key.weight || 1), 0);
    let random = Math.random() * totalWeight;
    
    for (const key of keys) {
      random -= (key.weight || 1);
      if (random <= 0) {
        return key;
      }
    }
    
    return keys[0]; // fallback
  }

  /**
   * 随机选择
   */
  private selectRandom(keys: APIKeyConfig[]): APIKeyConfig {
    const randomIndex = Math.floor(Math.random() * keys.length);
    return keys[randomIndex];
  }

  /**
   * 最少使用选择
   */
  private selectLeastUsed(keys: APIKeyConfig[]): APIKeyConfig {
    return keys.reduce((least, current) => 
      current.usageCount < least.usageCount ? current : least
    );
  }

  /**
   * 报告请求结果
   */
  reportRequestResult(result: RequestResult): void {
    const key = this.findKeyById(result.keyId);
    if (!key) return;

    if (result.success) {
      // 成功处理
      key.successRate = key.usageCount > 0 ? 
        ((key.usageCount - key.failureCount) / key.usageCount) * 100 : 100;
      this.updateKeyStats(result.keyId, 'success', result.responseTime);
    } else {
      // 失败处理
      const pool = this.config.urlPools[key.url];
      this.recoveryService.handleKeyFailure(
        key, 
        result.error || 'Unknown error',
        pool?.maxFailures || this.config.globalSettings.maxFailures,
        key.customWaitTime || pool?.defaultWaitTime
      );
      this.updateKeyStats(result.keyId, 'failure');
    }

    this.configManager.save(this.config);
  }

  /**
   * 查找密钥
   */
  private findKeyById(keyId: string): APIKeyConfig | null {
    for (const pool of Object.values(this.config.urlPools)) {
      const key = pool.keys.find(k => k.id === keyId);
      if (key) return key;
    }
    return null;
  }

  /**
   * 生成密钥ID
   */
  private generateKeyId(): string {
    return `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 初始化密钥统计
   */
  private initKeyStats(keyId: string): void {
    this.usageStats.set(keyId, {
      keyId,
      totalRequests: 0,
      successCount: 0,
      failureCount: 0,
      successRate: 100,
      avgResponseTime: 0,
      lastUsed: new Date()
    });
  }

  /**
   * 更新密钥统计
   */
  private updateKeyStats(keyId: string, type: 'request' | 'success' | 'failure', responseTime?: number): void {
    const stats = this.usageStats.get(keyId);
    if (!stats) return;

    switch (type) {
      case 'request':
        stats.totalRequests++;
        stats.lastUsed = new Date();
        break;
      
      case 'success':
        stats.successCount++;
        if (responseTime) {
          stats.avgResponseTime = (stats.avgResponseTime + responseTime) / 2;
        }
        stats.lastSuccess = new Date();
        break;
      
      case 'failure':
        stats.failureCount++;
        stats.lastFailure = new Date();
        break;
    }

    stats.successRate = stats.totalRequests > 0 ? 
      (stats.successCount / stats.totalRequests) * 100 : 100;
  }

  /**
   * 启动健康检查
   */
  private startHealthChecks(): void {
    Object.values(this.config.urlPools).forEach(pool => {
      pool.keys.forEach(key => {
        this.recoveryService.startHealthCheck(
          key,
          pool.healthCheckInterval,
          this.performHealthCheck.bind(this)
        );
      });
    });
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(key: APIKeyConfig): Promise<HealthCheckResult> {
    // 这里应该实现实际的健康检查逻辑
    // 例如发送一个简单的API请求来测试密钥是否有效
    return {
      keyId: key.id,
      healthy: true,
      timestamp: new Date()
    };
  }

  /**
   * 获取配置
   */
  getConfig(): APIKeyRotationConfig {
    return { ...this.config };
  }

  /**
   * 获取使用统计
   */
  getUsageStats(): Map<string, KeyUsageStats> {
    return new Map(this.usageStats);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.recoveryService.cleanup();
    console.log('🧹 API密钥轮播管理器已清理');
  }
}
