"use client";

import React, { useRef, useCallback } from 'react';
import './ChapterAnalysisButton.css';

interface ChapterAnalysisButtonProps {
  onClick: (buttonPosition?: { x: number; y: number }) => void;
  isActive: boolean;
  disabled?: boolean;
}

/**
 * 章节分析按钮组件
 * 用于在画布右侧显示一个浮动的章节分析按钮
 */
const ChapterAnalysisButton: React.FC<ChapterAnalysisButtonProps> = ({
  onClick,
  isActive,
  disabled = false
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = useCallback(() => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const buttonPosition = {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
      onClick(buttonPosition);
    } else {
      onClick();
    }
  }, [onClick]);

  return (
    <button
      ref={buttonRef}
      className={`chapter-analysis-button ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
      onClick={handleClick}
      disabled={disabled}
      title="章节分析 - 分析章节内容，生成JSON示例"
      aria-label="打开章节分析功能"
    >
      <div className="chapter-analysis-icon">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* 章节分析图标 - 文档分析 */}
          <path
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="none"
          />
          <path
            d="M15 13l2 2-2 2"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="none"
          />
          <circle cx="19" cy="15" r="2" fill="currentColor" />
          <circle cx="17" cy="13" r="1" fill="currentColor" />
          <circle cx="17" cy="17" r="1" fill="currentColor" />
        </svg>
      </div>

      {/* 活跃状态指示器 */}
      {isActive && (
        <div className="chapter-analysis-indicator">
          <div className="pulse-dot"></div>
        </div>
      )}

      {/* 悬停提示文本 */}
      <div className="chapter-analysis-tooltip">
        章节分析
      </div>
    </button>
  );
};

export default ChapterAnalysisButton;
