"use client";

import React from 'react';

interface WorldBuildingPanelFooterProps {
  worldBuildingCount: number;
  saveButton?: React.ReactNode;
  cancelButton?: React.ReactNode;
  isEditing?: boolean;
}

/**
 * 世界观面板底部组件
 */
export const WorldBuildingPanelFooter: React.FC<WorldBuildingPanelFooterProps> = ({
  worldBuildingCount,
  saveButton,
  cancelButton,
  isEditing = false
}) => {
  return (
    <div className="flex justify-between items-center px-4 py-3 border-t" style={{ backgroundColor: 'var(--color-primary-bg)' }}>
      <div
        className="text-base rounded-full px-6 py-2 flex items-center"
        style={{
          color: 'var(--color-white)',
          backgroundColor: 'var(--color-primary)',
          boxShadow: '0 3px 6px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.3s ease',
          transform: worldBuildingCount > 0 ? 'scale(1)' : 'scale(0.95)',
          opacity: worldBuildingCount > 0 ? 1 : 0.8
        }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        共 {worldBuildingCount} 个世界观元素
      </div>

      {/* 编辑模式下显示按钮 */}
      {isEditing && (
        <div className="flex space-x-4">
          {cancelButton}
          {saveButton}
        </div>
      )}
    </div>
  );
};
