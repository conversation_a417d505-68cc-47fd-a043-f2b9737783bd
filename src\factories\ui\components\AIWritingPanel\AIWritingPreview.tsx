"use client";

import React from 'react';

interface AIWritingPreviewProps {
  streamResponse: string;
  isLoading: boolean;
  generatedContent: string;
  bookId: string; // 书籍ID
  onApply: () => void;
  onRestart: () => void;
  onInsertBubble?: (content: string) => void; // 插入单个气泡的回调
  onContinue?: () => void; // 继续对话的回调
}

/**
 * AI写作预览组件
 * 用于显示AI生成的内容
 */
const AIWritingPreview: React.FC<AIWritingPreviewProps> = ({
  streamResponse,
  isLoading,
  generatedContent,
  bookId,
  onApply,
  onRestart,
  onInsertBubble,
  onContinue
}) => {
  // 注意：我们不再需要将响应文本分割成多个气泡，而是将整个回复作为一个完整的气泡显示

  // 过滤掉预制消息
  const filterPresetMessages = (messages: Array<{role: string, content: string}>) => {
    if (!messages || messages.length === 0) {
      return [];
    }

    // 预制消息的特征
    const presetMessages = [
      '我已阅读并分析了',
      '我已记住',
      '我将查看并分析',
      '作为您的文学创作顾问',
      '我已完成所有章节内容的分析',
      '我将参考以下章节内容',
      '我将考虑以下人物信息',
      '我将遵循以下世界观设定',
      '我将正确使用以下术语',
      '以下是相关人物信息',
      '以下是相关世界观信息',
      '以下是相关术语信息',
      '我已理解您的要求',
      '我将确保改写后的内容',
      '我将确保当前章节',
      '我将确保与需要改写的内容',
      '我将基于这些内容进行创作',
      '我将根据您提供的要求',
      '我会在创作中考虑这些人物的特点和背景',
      '我会在创作中遵循这些世界设定',
      '我会在创作中正确使用这些术语',
      '作为您的文学创作顾问',
      '我已准备好协助您',
      '我已准备好协助您进行小说创作'
    ];

    // 定义系统和用户预制消息的特征
    const systemPresetMessages = [
      '你是一位专业的文学改写专家',
      '擅长根据要求改写文本内容',
      '你的任务是根据用户提供的要求',
      '请注意以下几点'
    ];

    const userPresetMessages = [
      '我需要您改写一段文本内容',
      '请根据我提供的上下文和要求进行改写',
      '【当前模式】',
      '【改写要求】',
      '【剧情方向】'
    ];

    // 过滤掉所有预制消息（包括系统、用户和助手消息）
    return messages.filter(msg => {
      // 根据消息角色使用不同的过滤列表
      if (msg.role === 'system') {
        return !systemPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'user') {
        return !userPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'assistant') {
        return !presetMessages.some(preset => msg.content.includes(preset));
      }

      // 其他角色的消息保留
      return true;
    });
  };

  // 模拟对话历史，区分不同的回复
  const simulateConversation = (text: string) => {
    if (!text) return [];

    // 检查是否有用户提示标记
    const userPromptRegex = /\n\n【USER_PROMPT】(.*?)【\/USER_PROMPT】\n\n/;
    const userPromptMatches = [];
    let tempText = text;
    let tempIndex = 0;
    let match;

    // 手动查找所有匹配项
    while ((match = tempText.match(userPromptRegex)) !== null) {
      if (match.index !== undefined) {
        const matchIndex = tempIndex + match.index;
        userPromptMatches.push({
          index: matchIndex,
          0: match[0],
          1: match[1]
        });
        tempIndex += match.index + match[0].length;
        tempText = tempText.substring(match.index + match[0].length);
      } else {
        break;
      }
    }

    // 如果找到用户提示标记
    if (userPromptMatches.length > 0) {
      const messages = [];
      let lastIndex = 0;

      // 处理每个用户提示和随后的AI回复
      for (const match of userPromptMatches) {
        const matchIndex = match.index!;
        const userPrompt = match[1];
        const matchLength = match[0].length;

        // 添加用户提示之前的AI回复
        if (matchIndex > lastIndex) {
          const aiContent = text.substring(lastIndex, matchIndex);

          // 将AI内容作为一个完整的回复添加
          if (aiContent.trim()) {
            messages.push({
              role: 'assistant',
              content: aiContent.trim()
            });
          }
        }

        // 添加用户提示
        messages.push({
          role: 'user',
          content: userPrompt.trim()
        });

        // 更新lastIndex
        lastIndex = matchIndex + matchLength;
      }

      // 添加最后一个用户提示之后的AI回复
      if (lastIndex < text.length) {
        const remainingText = text.substring(lastIndex);

        // 检查是否有明确的分隔符（如【重写内容】或【分析】）
        const rewriteMatch = remainingText.match(/^【重写内容】\n/);
        const analyzeMatch = remainingText.match(/^【分析】\n/);

        if ((rewriteMatch && rewriteMatch[0]) || (analyzeMatch && analyzeMatch[0])) {
          const match = rewriteMatch || analyzeMatch;
          if (match && match[0]) {
            const aiContent = remainingText.substring(match[0].length);

            if (aiContent.trim()) {
              messages.push({
                role: 'assistant',
                content: aiContent.trim()
              });
            }
          }
        } else {
          // 将剩余内容作为一个完整的回复添加
          if (remainingText.trim()) {
            messages.push({
              role: 'assistant',
              content: remainingText.trim()
            });
          }
        }
      }

      // 过滤掉预制消息
      return filterPresetMessages(messages);
    }

    // 如果没有用户提示标记，检查是否有明确的分隔符（如【重写内容】或【分析】）
    const rewriteMatch = text.match(/\n\n【重写内容】\n/);
    const analyzeMatch = text.match(/\n\n【分析】\n/);

    if (rewriteMatch || analyzeMatch) {
      // 找到第一个匹配的分隔符
      const match = rewriteMatch || analyzeMatch;
      const index = match!.index!;

      // 分割成两部分：原始内容和新内容
      const firstPart = text.substring(0, index);
      const secondPart = text.substring(index + match![0].length);

      // 将第一部分作为一个完整的回复添加
      const messages = [];
      if (firstPart.trim()) {
        messages.push({
          role: 'assistant',
          content: firstPart.trim()
        });
      }

      // 将第二部分作为一个新的回复添加
      if (secondPart.trim()) {
        messages.push({
          role: 'assistant',
          content: secondPart.trim()
        });
      }

      // 过滤掉预制消息
      return filterPresetMessages(messages);
    }

    // 如果没有明确的分隔符，将整个文本作为一个完整的回复添加
    if (text.trim()) {
      const messages = [{
        role: 'assistant',
        content: text.trim()
      }];
      return filterPresetMessages(messages);
    }

    return [];
  };

  // 处理插入内容
  const handleInsertContent = (content: string) => {
    if (onInsertBubble) {
      onInsertBubble(content);
    }
  };

  // 处理复制内容
  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        // 可以添加一个复制成功的提示，但这里简化处理
        console.log('内容已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };
  return (
    <div className="w-3/5 pl-5 border-l border-gray-200 flex flex-col h-full">

      {/* 标题区域 - 固定在顶部 */}
      <div className="flex justify-between items-center mb-3 py-2">
        <h3 className="text-lg font-medium text-indigo-800 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          AI响应预览
        </h3>

        {/* 对话控制按钮 */}
        {generatedContent && (
          <div className="flex space-x-2">
            <button
              onClick={onApply}
              className="px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors shadow-sm flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              应用内容
            </button>
            <button
              onClick={onRestart}
              className="px-3 py-1 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors shadow-sm flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重新开始
            </button>
          </div>
        )}
      </div>

      {/* 内容区域 - 可滚动 */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {!streamResponse && !isLoading ? (
          <EmptyState />
        ) : (
          <div className="flex-1 flex flex-col">
            <div
              className="p-5 border rounded-2xl flex-1 overflow-y-auto shadow-sm"
              style={{
                borderColor: 'rgba(79, 70, 229, 0.2)',
                backgroundColor: 'rgba(238, 242, 255, 0.7)',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                minHeight: '600px',
                maxHeight: 'calc(100vh - 300px)' // 设置最大高度，确保在视口内可滚动
              }}
            >
              {isLoading && !streamResponse && !generatedContent && !localStorage.getItem(`ai-writing-prefix-${bookId}`) ? (
                <LoadingSpinner />
              ) : (
                <div
                  className="ai-writing-preview prose prose-indigo max-w-none h-full"
                  style={{
                    wordBreak: 'break-word',
                    fontSize: '1.05rem',
                    lineHeight: '1.8'
                  }}
                >
                  {/* 使用气泡样式显示对话 */}
                  <div className="space-y-4">
                    {simulateConversation(streamResponse).map((message, index) => (
                      <div
                        key={index}
                        className={`bubble-container flex items-start mb-4 group ${message.role === 'user' ? 'justify-end' : ''}`}
                      >
                        {/* 用户气泡 */}
                        {message.role === 'user' ? (
                          <>
                            {/* 用户气泡内容 */}
                            <div
                              className="user-bubble flex-grow-0 max-w-[80%] bg-blue-500 text-white rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow"
                            >
                              <div className="whitespace-pre-line">
                                {message.content}
                              </div>
                            </div>

                            {/* 用户头像 */}
                            <div className="flex-shrink-0 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm ml-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          </>
                        ) : (
                          <>
                            {/* AI头像 */}
                            <div className="flex-shrink-0 bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            </div>

                            {/* AI气泡内容 */}
                            <div
                              className="ai-bubble flex-grow max-w-[80%] bg-indigo-50 border border-indigo-100 rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow"
                            >
                              <div
                                className="whitespace-pre-line"
                                dangerouslySetInnerHTML={{
                                  __html: index === simulateConversation(streamResponse).length - 1 && isLoading
                                    ? message.content + '<span class="typing-cursor">|</span>'
                                    : message.content
                                }}
                              />
                            </div>

                            {/* 操作按钮 - 放在气泡旁边 */}
                            <div className="ml-2 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col space-y-2">
                              {/* 插入按钮 */}
                              {onInsertBubble && (
                                <button
                                  className="bg-green-500 text-white rounded-full p-2 shadow-sm hover:bg-green-600 transition-colors"
                                  onClick={() => handleInsertContent(message.content)}
                                  title="插入到编辑器"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </button>
                              )}

                              {/* 复制按钮 */}
                              <button
                                className="bg-blue-500 text-white rounded-full p-2 shadow-sm hover:bg-blue-600 transition-colors"
                                onClick={() => handleCopyContent(message.content)}
                                title="复制内容"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                              </button>

                              {/* 继续对话按钮 - 只在最后一个AI消息上显示 */}
                              {onContinue && index === simulateConversation(streamResponse).length - 1 && !isLoading && (
                                <button
                                  className="bg-indigo-500 text-white rounded-full p-2 shadow-sm hover:bg-indigo-600 transition-colors"
                                  onClick={onContinue}
                                  title="继续对话"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                  </svg>
                                </button>
                              )}
                            </div>
                          </>
                        )}
                      </div>
                    ))}

                    {/* 加载动画 - 只在加载中且有内容时显示在最后一个气泡下方 */}
                    {isLoading && streamResponse && (
                      <div className="loading-gradient h-2 bg-gradient-to-r from-indigo-100 via-indigo-200 to-indigo-100 rounded-full animate-pulse mt-2"></div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 加载中动画组件
 */
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center h-full">
    <div className="relative">
      <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-500 rounded-full animate-spin"></div>
      <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-b-indigo-300 rounded-full animate-spin" style={{ animationDuration: '1.5s' }}></div>
    </div>
  </div>
);

/**
 * 空状态提示组件
 */
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center justify-center h-full border border-dashed border-indigo-200 rounded-2xl bg-indigo-50" style={{ minHeight: '600px' }}>
    <div className="bg-white p-6 rounded-xl shadow-md max-w-md text-center">
      <div className="bg-indigo-100 p-4 rounded-full inline-block mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      </div>
      <h3 className="text-xl font-bold text-gray-800 mb-2">准备好创作了吗？</h3>
      <p className="text-gray-600 mb-6">
        填写左侧的创作要素，然后点击下方的"开始AI创作"按钮，AI将根据您的要求生成内容。
      </p>
      <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
        <p className="font-medium mb-2">提示：</p>
        <ul className="list-disc list-inside space-y-1 text-left">
          <li>选择相关章节可以提高内容的连贯性</li>
          <li>关联人物和术语可以保持设定一致性</li>
          <li>详细的写作风格和要求可以获得更精准的结果</li>
        </ul>
      </div>
    </div>
  </div>
);

export default AIWritingPreview;
