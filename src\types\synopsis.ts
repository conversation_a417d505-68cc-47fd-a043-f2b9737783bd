/**
 * 简介生成相关类型定义
 */

/**
 * 简介段落结构
 */
export interface SynopsisSection {
  name: string;           // 段落名称，如"背景设定"、"冲突介绍"
  description: string;    // 段落说明
  placeholder: string;    // AI生成指导
  wordRange: [number, number]; // 字数范围
}

/**
 * 简介写作技巧接口
 */
export interface SynopsisWritingTechnique {
  id: string;
  name: string;
  category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
  description: string;
  examples: string[];
  applicableScenarios: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
  effectiveness: number; // 0-1
  extractedFrom?: string; // 提取来源简介
  confidence?: number; // 提取置信度
  techniqueType: 'omission' | 'emphasis' | 'contrast' | 'suspense' | 'rhythm' | 'layout' | 'coolpoint';
}

/**
 * 简介写作技巧特征接口
 */
export interface SynopsisStyleCharacteristics {
  layoutTechniques: {
    paragraphStructure: string[];   // 段落结构：单段式、多段式、递进式
    lineBreakStrategy: string[];    // 换行策略：悬念换行、爽点换行、节奏换行
    rhythmControl: string[];        // 节奏控制：快节奏展开、慢节奏铺垫、变节奏起伏
    visualImpact: number;           // 视觉冲击力：0-1
  };
  omissionAndEmphasis: {
    omittedElements: string[];      // 省略要素：背景细节、人物外貌、具体设定
    emphasizedElements: string[];   // 强调要素：身份反差、能力觉醒、势力震惊
    contrastTechniques: string[];   // 对比技巧：前后对比、身份对比、实力对比
    suspensePoints: string[];       // 悬念设置：身份悬念、能力悬念、关系悬念
  };
  coolPointLayout: {
    primaryCoolPoints: string[];    // 主要爽点：装逼打脸、身份暴露、实力碾压
    coolPointTiming: string[];      // 爽点时机：开头爆点、中段高潮、结尾悬念
    coolPointIntensity: number;     // 爽点强度：0-1
    anticipationBuilding: string[]; // 期待营造：预告式、暗示式、对比式
  };
  creativeConcept: {
    coreCreativity: string[];       // 核心创意：设定创新、身份创新、能力创新
    conceptPresentation: string[];  // 概念展现：直接展示、逐步揭示、反转展现
    uniquenessLevel: number;        // 独特性程度：0-1
    marketAppeal: string[];         // 市场吸引力：热门元素、差异化卖点、读者痛点
  };
}

/**
 * 可复用模板接口
 */
export interface ReusableTemplate {
  id: string;
  name: string;
  pattern: string;
  description: string;
  techniques: string[];              // 使用的技巧列表
  applicableGenres: string[];        // 适用类型
  effectiveness: number;             // 效果评分
  examples: string[];                // 应用示例
}

/**
 * 增强的简介分析结果接口
 */
export interface EnhancedSynopsisAnalysis {
  // 原有字段保持不变
  synopsis: string;
  extractedKeywords: string[];
  keywords: Array<{
    text: string;
    category: string;
  }>;
  detectedFramework: string;
  frameworkPattern: string;
  synopsisCategory: string;
  confidence: number;
  structureAnalysis?: {
    opening: string;
    development: string;
    ending: string;
  };

  // 新增技巧分析字段
  writingTechniques: SynopsisWritingTechnique[];
  styleCharacteristics: SynopsisStyleCharacteristics;
  reusableTemplates: ReusableTemplate[];
  techniqueAnalysisConfidence: number;  // 技巧分析置信度
}

/**
 * 简介框架模板
 */
export interface SynopsisFramework {
  id: string;
  name: string;           // 框架名称，如"经典三段式"
  pattern?: string;       // 框架模式，如"{主角身份}+{系统设定}+{马甲暴露}+{势力震惊}"
  structure: SynopsisSection[]; // 多段落结构
  description: string;    // 框架说明
  examples: string[];     // 成功案例
  category: 'classic' | 'modern' | 'genre-specific'; // 框架分类
  usageCount: number;
  effectiveness: number;  // 效果评分 (1-10)
  createdAt: Date;
  lastUsedAt?: Date;
}

/**
 * 简介生成参数（移除style字段，通过customRequirements处理风格需求）
 */
export interface SynopsisParams {
  keywords: string[];
  framework: SynopsisFramework | null;
  length: 'short' | 'medium' | 'long';
  customRequirements?: string;
}

/**
 * 生成的简介
 */
export interface SynopsisResult {
  id: string;
  content: string;
  aiScore: number;
  reason: string;
  parameters: SynopsisParams;
  generatedAt: Date;
  isFavorite?: boolean;
  wordCount: number;
  sections?: Array<{
    name: string;
    content: string;
    wordCount: number;
  }>;
}

/**
 * 简介生成回调函数
 */
export interface SynopsisGenerationCallbacks {
  onStart?: () => void;
  onProgress?: (progress: number, currentSection?: string) => void;
  onSectionGenerated?: (section: string, content: string) => void;
  onComplete?: (synopsis: SynopsisResult) => void;
  onError?: (error: Error) => void;
}

/**
 * 简介生成结果
 */
export interface SynopsisGenerationResult {
  synopsis: SynopsisResult[];
  success: boolean;
  error?: string;
  totalGenerated: number;
  averageScore: number;
}

/**
 * AI简介生成响应
 */
export interface AISynopsisResponse {
  synopsis: Array<{
    content: string;
    score: number;
    reason: string;
    sections?: Array<{
      name: string;
      content: string;
    }>;
  }>;
}

/**
 * 风格选项配置
 */
export interface StyleOption {
  id: 'serious' | 'humorous' | 'mysterious' | 'passionate';
  name: string;
  icon: string;
  color: string;
  description: string;
}

/**
 * 长度选项配置
 */
export interface LengthOption {
  id: 'short' | 'medium' | 'long';
  name: string;
  wordRange: [number, number];
  description: string;
}

/**
 * 预设简介框架
 */
export const PRESET_SYNOPSIS_FRAMEWORKS: Omit<SynopsisFramework, 'usageCount' | 'createdAt' | 'lastUsedAt'>[] = [
  {
    id: 'classic-three-act',
    name: '经典三段式',
    structure: [
      {
        name: '背景设定',
        description: '介绍故事世界观和基础设定',
        placeholder: '在一个...的世界里',
        wordRange: [30, 50]
      },
      {
        name: '冲突介绍',
        description: '引出主要矛盾和挑战',
        placeholder: '然而，当...发生时',
        wordRange: [40, 70]
      },
      {
        name: '悬念结尾',
        description: '留下悬念，激发阅读兴趣',
        placeholder: '面对...，主角将如何...',
        wordRange: [30, 50]
      }
    ],
    description: '适用于大多数类型的小说，结构清晰，易于理解',
    examples: [
      '在修仙界中，天赋平庸的少年意外获得神秘传承。然而强敌环伺，各方势力虎视眈眈。面对重重危机，他能否逆天改命，成就无上仙途？',
      '都市之中，普通青年突然觉醒超能力。但随之而来的是神秘组织的追杀和未知的阴谋。在这个暗流涌动的世界里，他将如何守护心中的正义？'
    ],
    category: 'classic',
    effectiveness: 9
  },
  {
    id: 'character-driven',
    name: '人物驱动式',
    structure: [
      {
        name: '主角介绍',
        description: '突出主角的独特性和魅力',
        placeholder: '他是...',
        wordRange: [40, 60]
      },
      {
        name: '能力展示',
        description: '展现主角的特殊能力或优势',
        placeholder: '拥有...的他',
        wordRange: [30, 50]
      },
      {
        name: '挑战预告',
        description: '预告即将面临的挑战和成长',
        placeholder: '但是...',
        wordRange: [40, 60]
      }
    ],
    description: '以主角为核心，突出人物魅力和成长历程',
    examples: [
      '他是被家族抛弃的废物少爷，却意外觉醒了吞噬万物的神秘血脉。拥有逆天天赋的他，将在这个强者为尊的世界中，一步步登上巅峰，让所有人刮目相看。',
      '她是现代特工之王，一朝穿越成为将军府的废柴小姐。拥有前世记忆和技能的她，将在这个男尊女卑的古代，用智慧和实力改写自己的命运。'
    ],
    category: 'modern',
    effectiveness: 8
  },
  {
    id: 'worldview-showcase',
    name: '世界观展示式',
    structure: [
      {
        name: '世界设定',
        description: '展现独特的世界观和设定',
        placeholder: '这是一个...的世界',
        wordRange: [40, 60]
      },
      {
        name: '规则说明',
        description: '介绍世界的核心规则和体系',
        placeholder: '在这里...',
        wordRange: [30, 50]
      },
      {
        name: '主角登场',
        description: '主角在这个世界中的位置和使命',
        placeholder: '而他...',
        wordRange: [40, 60]
      }
    ],
    description: '适合设定复杂的奇幻、科幻类作品',
    examples: [
      '这是一个被神明遗弃的破碎世界，魔法与科技并存，古老的预言即将应验。在这个混乱的时代，一个拥有双重血脉的少年，将承担起拯救世界的重任。',
      '星际时代，人类已经征服了银河系的大部分星域。但在宇宙深处，古老的威胁正在苏醒。一个普通的星舰驾驶员，将成为人类文明最后的希望。'
    ],
    category: 'genre-specific',
    effectiveness: 7
  },
  {
    id: 'mystery-intro',
    name: '悬疑引入式',
    structure: [
      {
        name: '神秘事件',
        description: '以神秘事件开场，吸引注意',
        placeholder: '一个神秘的...',
        wordRange: [30, 50]
      },
      {
        name: '主角卷入',
        description: '主角被卷入事件的经过',
        placeholder: '意外卷入其中的...',
        wordRange: [40, 60]
      },
      {
        name: '真相暗示',
        description: '暗示更大的真相和阴谋',
        placeholder: '然而这只是...',
        wordRange: [40, 60]
      }
    ],
    description: '适合悬疑、推理、都市异能类作品',
    examples: [
      '一个神秘的连环杀手案件震惊全城，所有受害者都有着相同的诡异死状。意外卷入其中的心理学教授发现，这些案件背后隐藏着一个更加可怕的真相。',
      '深夜的地铁站里，一个不应该存在的神秘乘客反复出现。作为地铁安保的他开始调查这个异常现象，却发现自己陷入了一个超自然的恐怖漩涡。'
    ],
    category: 'genre-specific',
    effectiveness: 8
  }
];

/**
 * 风格选项配置
 */
export const STYLE_OPTIONS: StyleOption[] = [
  {
    id: 'serious',
    name: '严肃',
    icon: '🎭',
    color: 'blue',
    description: '正统严肃的叙述风格，适合正剧类作品'
  },
  {
    id: 'humorous',
    name: '幽默',
    icon: '😄',
    color: 'orange',
    description: '轻松幽默的表达方式，增加趣味性'
  },
  {
    id: 'mysterious',
    name: '神秘',
    icon: '🌙',
    color: 'purple',
    description: '神秘悬疑的氛围营造，制造紧张感'
  },
  {
    id: 'passionate',
    name: '激情',
    icon: '🔥',
    color: 'red',
    description: '热血激昂的情感表达，激发共鸣'
  }
];

/**
 * 长度选项配置
 */
export const LENGTH_OPTIONS: LengthOption[] = [
  {
    id: 'short',
    name: '短',
    wordRange: [100, 150],
    description: '简洁明了，突出核心卖点'
  },
  {
    id: 'medium',
    name: '中',
    wordRange: [200, 300],
    description: '详略得当，平衡信息量'
  },
  {
    id: 'long',
    name: '长',
    wordRange: [400, 500],
    description: '详细丰富，全面展示内容'
  }
];
