"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding, Terminology } from '@/lib/db/dexie';

interface TerminologyRelationManagerProps {
  isOpen: boolean;
  onClose: () => void;
  worldBuilding: WorldBuilding;
  onUpdate: (worldBuilding: WorldBuilding) => void;
  bookId: string;
}

/**
 * 术语关联管理对话框组件
 * 用于管理世界观元素与术语之间的关联
 */
export const TerminologyRelationManager: React.FC<TerminologyRelationManagerProps> = ({
  isOpen,
  onClose,
  worldBuilding,
  onUpdate,
  bookId
}) => {
  // 术语列表
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 选中的术语ID列表
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>(worldBuilding.relatedTerminologyIds || []);
  // 搜索关键词
  const [searchQuery, setSearchQuery] = useState('');
  // 过滤后的术语列表
  const [filteredTerminologies, setFilteredTerminologies] = useState<Terminology[]>([]);

  // 加载术语数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadTerminologies();
    }
  }, [isOpen, bookId]);

  // 当搜索关键词变化时，过滤术语列表
  useEffect(() => {
    if (terminologies.length > 0) {
      const filtered = terminologies.filter(terminology => {
        if (searchQuery.trim() === '') return true;
        return terminology.name.toLowerCase().includes(searchQuery.toLowerCase());
      });
      setFilteredTerminologies(filtered);
    }
  }, [searchQuery, terminologies]);

  // 加载术语列表
  const loadTerminologies = async () => {
    setIsLoading(true);
    try {
      // 导入 terminologyRepository
      const { terminologyRepository } = await import('@/lib/db/repositories');

      // 获取术语列表
      const terminologiesData = await terminologyRepository.getAllByBookId(bookId);
      setTerminologies(terminologiesData);
      setFilteredTerminologies(terminologiesData);
    } catch (error) {
      console.error('加载术语列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理术语选择变化
  const handleTerminologySelectionChange = (terminologyId: string) => {
    setSelectedTerminologyIds(prev => {
      if (prev.includes(terminologyId)) {
        return prev.filter(id => id !== terminologyId);
      } else {
        return [...prev, terminologyId];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAllTerminologies = (checked: boolean) => {
    if (checked) {
      setSelectedTerminologyIds(filteredTerminologies.map(terminology => terminology.id!).filter(Boolean));
    } else {
      setSelectedTerminologyIds([]);
    }
  };

  // 保存关联
  const saveRelationships = async () => {
    try {
      // 更新世界观元素的关联术语
      const updatedWorldBuilding = { 
        ...worldBuilding,
        relatedTerminologyIds: selectedTerminologyIds
      };
      
      // 更新术语的关联世界观元素
      const { terminologyRepository } = await import('@/lib/db/repositories');
      
      // 获取当前关联的术语
      const currentRelatedTerminologyIds = worldBuilding.relatedTerminologyIds || [];
      
      // 需要添加关联的术语
      const terminologyIdsToAdd = selectedTerminologyIds.filter(id => !currentRelatedTerminologyIds.includes(id));
      
      // 需要移除关联的术语
      const terminologyIdsToRemove = currentRelatedTerminologyIds.filter(id => !selectedTerminologyIds.includes(id));
      
      // 为每个新增的术语添加关联
      for (const terminologyId of terminologyIdsToAdd) {
        const terminology = await terminologyRepository.getById(terminologyId);
        if (terminology) {
          const relatedWorldBuildingIds = [...(terminology.relatedWorldBuildingIds || [])];
          if (!relatedWorldBuildingIds.includes(worldBuilding.id!)) {
            relatedWorldBuildingIds.push(worldBuilding.id!);
            await terminologyRepository.update(terminologyId, {
              relatedWorldBuildingIds
            });
          }
        }
      }
      
      // 为每个移除的术语删除关联
      for (const terminologyId of terminologyIdsToRemove) {
        const terminology = await terminologyRepository.getById(terminologyId);
        if (terminology) {
          const relatedWorldBuildingIds = (terminology.relatedWorldBuildingIds || [])
            .filter(id => id !== worldBuilding.id);
          await terminologyRepository.update(terminologyId, {
            relatedWorldBuildingIds
          });
        }
      }
      
      // 更新世界观元素
      onUpdate(updatedWorldBuilding);
      onClose();
    } catch (error) {
      console.error('保存关联失败:', error);
      alert('保存关联失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">管理关联</h2>
          <p className="text-sm text-gray-600">为世界观元素 "{worldBuilding.name}" 管理关联的术语</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-hidden flex flex-col">
          {/* 搜索框 */}
          <div className="mb-4 relative">
            <input
              type="text"
              placeholder="搜索术语..."
              className="w-full px-4 py-2 border rounded-lg"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          {/* 全选/取消全选 */}
          <div className="mb-2 flex items-center">
            <input
              type="checkbox"
              id="select-all"
              checked={selectedTerminologyIds.length > 0 && selectedTerminologyIds.length === filteredTerminologies.length}
              onChange={(e) => handleSelectAllTerminologies(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="select-all" className="text-sm font-medium text-gray-700">
              {selectedTerminologyIds.length > 0 && selectedTerminologyIds.length === filteredTerminologies.length
                ? '取消全选'
                : '全选'}
            </label>
            <span className="ml-auto text-sm text-gray-500">
              已选择 {selectedTerminologyIds.length} / {filteredTerminologies.length} 个术语
            </span>
          </div>

          {/* 术语列表 */}
          <div className="flex-1 overflow-y-auto border rounded-lg">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : filteredTerminologies.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                没有找到匹配的术语
              </div>
            ) : (
              <div className="divide-y">
                {filteredTerminologies.map(terminology => (
                  <div
                    key={terminology.id}
                    className="p-3 hover:bg-gray-50 flex items-center cursor-pointer"
                    onClick={() => terminology.id && handleTerminologySelectionChange(terminology.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedTerminologyIds.includes(terminology.id || '')}
                      onChange={() => {}} // 通过父元素的onClick处理
                      className="mr-3"
                    />
                    <div>
                      <div className="font-medium">{terminology.name}</div>
                      <div className="text-sm text-gray-500 truncate max-w-lg">
                        {terminology.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-3">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            onClick={saveRelationships}
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};
