import { FrameworkInfo } from '../types/SharedTypes';

/**
 * 框架数据增强器
 * 解决框架指导剧情点获取缺失问题
 * 为缺少plotPointsWithGuidance的框架自动生成指导信息
 */
export class FrameworkDataEnhancer {
  private static instance: FrameworkDataEnhancer;

  private constructor() {}

  public static getInstance(): FrameworkDataEnhancer {
    if (!FrameworkDataEnhancer.instance) {
      FrameworkDataEnhancer.instance = new FrameworkDataEnhancer();
    }
    return FrameworkDataEnhancer.instance;
  }

  /**
   * 增强框架数据，补充缺失的plotPointsWithGuidance信息
   */
  enhanceFrameworkData(framework: FrameworkInfo): FrameworkInfo {
    console.log('🔥 FrameworkDataEnhancer: 开始增强框架数据:', framework.frameworkPattern);

    // 如果已经有完整的plotPointsWithGuidance数据，直接返回
    if (framework.plotAnalysis?.plotPointsWithGuidance &&
        framework.plotAnalysis.plotPointsWithGuidance.length > 0) {
      console.log('🔥 FrameworkDataEnhancer: 框架已有plotPointsWithGuidance，数量:',
        framework.plotAnalysis.plotPointsWithGuidance.length);
      return framework;
    }

    // 生成plotPointsWithGuidance
    const generatedPlotPoints = this.generatePlotPointsWithGuidance(framework);
    console.log('🔥 FrameworkDataEnhancer: 生成plotPointsWithGuidance，数量:', generatedPlotPoints.length);

    // 创建增强的框架副本
    const enhancedFramework: FrameworkInfo = {
      ...framework,
      plotAnalysis: {
        ...framework.plotAnalysis,
        plotPointsWithGuidance: generatedPlotPoints
      }
    };

    console.log('🔥 FrameworkDataEnhancer: 框架增强完成，最终plotPointsWithGuidance数量:',
      enhancedFramework.plotAnalysis?.plotPointsWithGuidance?.length || 0);

    return enhancedFramework;
  }

  /**
   * 基于框架模式和变量生成plotPointsWithGuidance
   */
  private generatePlotPointsWithGuidance(framework: FrameworkInfo): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    const plotPoints: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }> = [];

    // 基于frameworkPattern解析剧情点
    if (framework.frameworkPattern) {
      const extractedPoints = this.extractPlotPointsFromPattern(framework.frameworkPattern);
      plotPoints.push(...extractedPoints);
    }

    // 基于frameworkVariables补充剧情点
    if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
      const variablePoints = this.generatePlotPointsFromVariables(framework.frameworkVariables, framework.patternType);
      plotPoints.push(...variablePoints);
    }

    // 基于patternType生成通用剧情点
    if (framework.patternType && plotPoints.length < 3) {
      const typePoints = this.generatePlotPointsByType(framework.patternType);
      plotPoints.push(...typePoints);
    }

    // 确保至少有3个剧情点
    while (plotPoints.length < 3) {
      plotPoints.push(this.generateDefaultPlotPoint(plotPoints.length + 1));
    }

    // 🔥 移除数量限制，返回所有剧情点以完整展示ACE框架功能
    return plotPoints;
  }

  /**
   * 从框架模式中提取剧情点
   */
  private extractPlotPointsFromPattern(pattern: string): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    const plotPoints: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }> = [];

    console.log('🔥 FrameworkDataEnhancer: 解析框架模式:', pattern);

    // 特殊处理参考框架3的模式结构
    if (pattern.includes('环境突变与初始困惑') || pattern.includes('系统规则强制灌输')) {
      plotPoints.push(
        {
          content: '环境突变与初始困惑：主角突然进入陌生环境或面临未知变化，产生困惑和不安',
          specificDescription: '重点描写主角对新环境的具体观察、身体感受、第一反应，以及试图理解现状的具体行为',
          avoidanceGuidance: '避免使用"一丝困惑"、"几分不安"等模糊表达，不要过度描写环境细节'
        },
        {
          content: '系统规则强制灌输：外部力量向主角传达新的规则体系和行为准则',
          specificDescription: '具体描写规则传达的方式、主角接收信息的过程、对规则的理解和质疑',
          avoidanceGuidance: '避免大段说教式的规则介绍，通过具体事例和互动来展现规则'
        },
        {
          content: '核心能力获取与解析：主角发现并理解自己的特殊能力或天赋',
          specificDescription: '详细描写能力觉醒的具体过程、主角的身体变化、能力测试的具体行为',
          avoidanceGuidance: '避免抽象的能力描述，通过具体的行动和效果来展现能力'
        },
        {
          content: '社交平台信息交互与世界观补全：通过与他人的交流完善对世界的认知',
          specificDescription: '具体描写对话内容、信息获取的过程、主角认知更新的具体表现',
          avoidanceGuidance: '避免信息堆砌，通过自然的对话和互动来传递世界观信息'
        },
        {
          content: '潜在对手展示与主角隐藏优势确立：揭示竞争关系，确立主角的独特优势',
          specificDescription: '具体描写对手的能力展示、主角优势的具体表现、双方实力对比的具体细节',
          avoidanceGuidance: '避免直接的实力数值对比，通过具体的行动和结果来体现优势'
        }
      );
    } else {
      // 通用模式解析
      const actions = this.parseActionsFromPattern(pattern);

      actions.forEach((action) => {
        plotPoints.push({
          content: `${action.subject}${action.action}：${action.description}`,
          specificDescription: this.generateSpecificDescription(action),
          avoidanceGuidance: this.generateAvoidanceGuidance(action.type)
        });
      });
    }

    console.log('🔥 FrameworkDataEnhancer: 提取剧情点数量:', plotPoints.length);
    return plotPoints;
  }

  /**
   * 解析模式中的动作
   */
  private parseActionsFromPattern(pattern: string): Array<{
    subject: string;
    action: string;
    description: string;
    type: string;
  }> {
    const actions: Array<{
      subject: string;
      action: string;
      description: string;
      type: string;
    }> = [];

    // 示例：{主角}因{日常小事}求助{神秘主播}，发现{身边人}涉及{灵异事件}
    const segments = pattern.split(/[，。；]/);
    
    segments.forEach((segment, index) => {
      if (segment.trim()) {
        const variables = segment.match(/\{([^}]+)\}/g) || [];
        const subject = variables[0] ? variables[0].replace(/[{}]/g, '') : '主角';
        
        let actionType = 'action';
        if (segment.includes('发现') || segment.includes('揭露')) actionType = 'discovery';
        if (segment.includes('冲突') || segment.includes('对抗')) actionType = 'conflict';
        if (segment.includes('解决') || segment.includes('完成')) actionType = 'resolution';

        actions.push({
          subject,
          action: this.extractActionVerb(segment),
          description: this.cleanDescription(segment),
          type: actionType
        });
      }
    });

    return actions;
  }

  /**
   * 提取动作动词
   */
  private extractActionVerb(segment: string): string {
    const verbs = ['求助', '发现', '揭露', '解决', '对抗', '逃脱', '获得', '失去', '选择', '决定'];
    for (const verb of verbs) {
      if (segment.includes(verb)) {
        return verb;
      }
    }
    return '行动';
  }

  /**
   * 清理描述文本
   */
  private cleanDescription(segment: string): string {
    return segment.replace(/\{[^}]+\}/g, (match) => {
      return match.replace(/[{}]/g, '');
    }).trim();
  }

  /**
   * 生成具体描写特征
   */
  private generateSpecificDescription(action: any): string {
    const descriptions = {
      discovery: '重点描写发现过程中的具体细节，如观察到的异常现象、获得的关键信息、角色的即时反应等',
      conflict: '详细描写冲突双方的具体行动，包括言语交锋、身体动作、使用的手段和策略',
      resolution: '具体描写解决问题的关键步骤，包括采取的具体措施、使用的工具或方法、产生的直接效果',
      action: '专注描写角色的具体行为动作，包括说了什么话、做了什么事、如何移动和操作'
    };

    return descriptions[action.type as keyof typeof descriptions] || descriptions.action;
  }

  /**
   * 生成避免指导
   */
  private generateAvoidanceGuidance(actionType: string): string {
    const baseGuidance = '避免描写"一丝xx"、"几分xx"、"些许xx"等模糊表达。不要使用"像...一样"的比喻样式。';
    
    const specificGuidance = {
      discovery: baseGuidance + '避免过度的心理活动描写，专注于发现的具体过程和客观现象。',
      conflict: baseGuidance + '避免冗长的环境描写，专注于冲突双方的直接交锋和具体行动。',
      resolution: baseGuidance + '避免抽象的情感描述，专注于解决问题的具体步骤和可见结果。',
      action: baseGuidance + '避免无关的场景铺垫，直接描写角色的具体行为和对话内容。'
    };

    return specificGuidance[actionType as keyof typeof specificGuidance] || specificGuidance.action;
  }

  /**
   * 基于框架变量生成剧情点
   */
  private generatePlotPointsFromVariables(variables: string[], patternType?: string): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    const plotPoints: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }> = [];

    // 为每个关键变量生成一个剧情点
    variables.slice(0, 3).forEach((variable, index) => {
      plotPoints.push({
        content: `围绕"${variable}"展开的关键剧情：具体描述与${variable}相关的重要事件和角色行动`,
        specificDescription: `重点描写${variable}在剧情中的具体表现形式，包括相关的人物行为、对话内容、事件发展等`,
        avoidanceGuidance: `避免对${variable}进行抽象化描述，要具体写出与${variable}相关的可见行动和明确结果。避免"一丝xx"等模糊表达。`
      });
    });

    return plotPoints;
  }

  /**
   * 基于模式类型生成剧情点
   */
  private generatePlotPointsByType(patternType: string): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    const typeTemplates = {
      '都市灵异悬疑': [
        {
          content: '主角遭遇超自然现象：发现异常事件的具体过程和初步应对',
          specificDescription: '详细描写超自然现象的具体表现，如异常声音、奇怪现象、不寻常的物理变化等',
          avoidanceGuidance: '避免"一丝恐惧"、"几分诡异"等模糊表达，直接描写现象本身和角色的具体反应'
        },
        {
          content: '深入调查真相：主角采取具体行动探索事件背后的原因',
          specificDescription: '具体描写调查过程，包括查找线索、询问相关人员、实地探访等具体行动',
          avoidanceGuidance: '避免过多的心理活动描写，专注于调查的具体步骤和发现的客观信息'
        }
      ],
      '玄幻修仙': [
        {
          content: '修炼突破：主角在修炼中遇到关键节点或获得重要提升',
          specificDescription: '详细描写修炼过程的具体步骤，包括功法运行、能量变化、身体反应等',
          avoidanceGuidance: '避免"一丝灵气"、"几分玄妙"等模糊描述，具体写出修炼的可见效果和明确变化'
        }
      ]
    };

    return typeTemplates[patternType as keyof typeof typeTemplates] || this.getDefaultPlotPoints();
  }

  /**
   * 获取默认剧情点模板
   */
  private getDefaultPlotPoints(): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    return [
      {
        content: '主角面临关键选择：在重要时刻需要做出影响后续发展的决定',
        specificDescription: '具体描写选择的背景、可选方案、角色的思考过程和最终决定',
        avoidanceGuidance: '避免"一丝犹豫"、"几分纠结"等模糊表达，直接写出选择的具体内容和决定过程'
      },
      {
        content: '遭遇重要转折：剧情发生意外变化，改变原有的发展方向',
        specificDescription: '详细描写转折点的具体事件，包括突发情况、角色反应、局势变化等',
        avoidanceGuidance: '避免抽象的命运描述，专注于转折事件的具体表现和可见影响'
      }
    ];
  }

  /**
   * 生成默认剧情点
   */
  private generateDefaultPlotPoint(index: number): {
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  } {
    return {
      content: `剧情发展点${index}：角色采取具体行动推进剧情发展`,
      specificDescription: '描写角色的具体行为动作，包括说话内容、身体动作、使用的物品等',
      avoidanceGuidance: '避免"一丝xx"、"几分xx"等模糊表达，直接写出角色做了什么、说了什么'
    };
  }

  /**
   * 检查框架是否需要增强
   */
  needsEnhancement(framework: FrameworkInfo): boolean {
    return !framework.plotAnalysis?.plotPointsWithGuidance || 
           framework.plotAnalysis.plotPointsWithGuidance.length === 0;
  }

  /**
   * 批量增强框架数据
   */
  enhanceMultipleFrameworks(frameworks: FrameworkInfo[]): FrameworkInfo[] {
    return frameworks.map(framework => this.enhanceFrameworkData(framework));
  }
}
