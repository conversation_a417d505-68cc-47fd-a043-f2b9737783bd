/* 聊天界面样式 */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
}

/* 消息列表 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 欢迎界面 */
.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.chat-welcome h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #333;
}

.chat-welcome p {
  margin: 0 0 24px 0;
  font-size: 14px;
  line-height: 1.5;
}

.welcome-tips {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.tip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.tip-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 消息样式 */
.chat-message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.chat-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.chat-message.assistant {
  align-self: flex-start;
}

/* 头像 */
.message-avatar {
  flex-shrink: 0;
}

.user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF9A9E 0%, #FECFEF 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  border: 3px solid rgba(255, 255, 255, 0.4);
  animation: userGlow 5s infinite ease-in-out;
}

/* 用户头像悬停效果 */
.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(255, 154, 158, 0.5);
}

/* 用户SVG图标样式 */
.user-avatar svg {
  width: 36px;
  height: 36px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 用户皇冠动画 */
.user-crown {
  transition: all 0.3s ease;
  transform-origin: center bottom;
  animation: crownShine 3s infinite ease-in-out;
}

.crown-jewel {
  animation: jewelTwinkle 2s infinite ease-in-out;
}

.assistant-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  border: 3px solid rgba(255, 255, 255, 0.3);
  animation: aiBreathing 4s infinite ease-in-out;
}

/* AI助手头像悬停效果 */
.assistant-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* AI思考状态 */
.assistant-avatar.thinking {
  animation: aiThinking 2s infinite ease-in-out;
}

.assistant-avatar.thinking::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  animation: thinkingPulse 1.5s infinite ease-in-out;
  z-index: -1;
}

/* AI回复状态 */
.assistant-avatar.responding {
  animation: aiResponding 0.3s ease-out;
}

.assistant-avatar.responding::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.5);
  animation: respondingGlow 2s infinite ease-in-out;
  z-index: -1;
}

/* AI助手SVG图标样式 */
.assistant-avatar svg {
  width: 36px;
  height: 36px;
  color: white;
  fill: currentColor;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* 机器人眼睛动画 - 根据状态变色 */
.robot-eye {
  animation: robotBlink 3s infinite ease-in-out;
  fill: #00E5FF !important;
  transition: all 0.3s ease;
}

.assistant-avatar.thinking .robot-eye {
  animation: robotThinkingBlink 0.8s infinite ease-in-out;
  fill: #9C27B0 !important;
}

.assistant-avatar.responding .robot-eye {
  fill: #4CAF50 !important;
  animation: robotExcited 0.5s ease-in-out;
}

/* 机器人小帽子动画 */
.robot-hat {
  transition: all 0.3s ease;
  transform-origin: center bottom;
}

.assistant-avatar.thinking .robot-hat {
  animation: hatWiggle 2s infinite ease-in-out;
}

.assistant-avatar.responding .robot-hat {
  animation: hatBounce 0.6s ease-out;
}

/* 消息内容 */
.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap; /* 保留换行符和空格 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: messageSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: box-shadow 0.2s ease, transform 0.2s ease;
  position: relative;
}

.message-text:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.chat-message.user .message-text {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  animation: messageSlideInFromRight 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3), 0 1px 3px rgba(76, 175, 80, 0.4);
}

.chat-message.user .message-text:hover {
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4), 0 2px 6px rgba(76, 175, 80, 0.5);
}

.chat-message.assistant .message-text {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #333;
  animation: messageSlideInFromLeft 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 提及的节点 */
.mentioned-nodes {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  line-height: 1.2;
}

.mentioned-node {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 10px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin: 2px 0;
  min-height: 24px;
  white-space: nowrap;

  /* 呼吸效果 */
  animation: mentionBreathe 3s ease-in-out infinite;
}

/* 多个@节点的颜色变化 */
.mentioned-node:nth-child(1) {
  background: rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.4);
  color: #5a6fd8;
}

.mentioned-node:nth-child(2) {
  background: rgba(139, 69, 219, 0.15);
  border-color: rgba(139, 69, 219, 0.4);
  color: #8b45db;
}

.mentioned-node:nth-child(3) {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.4);
  color: #22c55e;
}

.mentioned-node:nth-child(4) {
  background: rgba(249, 115, 22, 0.15);
  border-color: rgba(249, 115, 22, 0.4);
  color: #f97316;
}

.mentioned-node:nth-child(5) {
  background: rgba(236, 72, 153, 0.15);
  border-color: rgba(236, 72, 153, 0.4);
  color: #ec4899;
}

/* 超过5个的节点使用循环颜色 */
.mentioned-node:nth-child(n+6) {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  color: #667eea;
  opacity: 0.8;
}

.mentioned-node:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(1.1) saturate(1.2);
}

/* 特定颜色的悬停效果 */
.mentioned-node:nth-child(1):hover {
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.mentioned-node:nth-child(2):hover {
  box-shadow: 0 4px 8px rgba(139, 69, 219, 0.3);
}

.mentioned-node:nth-child(3):hover {
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
}

.mentioned-node:nth-child(4):hover {
  box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);
}

.mentioned-node:nth-child(5):hover {
  box-shadow: 0 4px 8px rgba(236, 72, 153, 0.3);
}

.mentioned-node::before {
  content: '@';
  font-weight: 600;
  opacity: 0.8;
}

/* 新插入的@节点动画 - 波浪式出现 */
.mentioned-node.new-mention {
  animation: mentionGrow 0.4s ease-out, mentionBreathe 3s ease-in-out infinite 0.4s;
}

.mentioned-node.new-mention:nth-child(1) {
  animation-delay: 0s, 0.4s;
}

.mentioned-node.new-mention:nth-child(2) {
  animation-delay: 0.1s, 0.5s;
}

.mentioned-node.new-mention:nth-child(3) {
  animation-delay: 0.2s, 0.6s;
}

.mentioned-node.new-mention:nth-child(4) {
  animation-delay: 0.3s, 0.7s;
}

.mentioned-node.new-mention:nth-child(5) {
  animation-delay: 0.4s, 0.8s;
}

/* 多节点计数器显示 */
.mentioned-nodes[data-count]:before {
  content: attr(data-count) " 个节点:";
  font-size: 11px;
  color: #666;
  margin-right: 4px;
  font-weight: 500;
}

.mentioned-nodes[data-count="1"]:before {
  display: none;
}

/* 批量高亮按钮 */
.batch-highlight-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  font-size: 10px;
  color: #666;
  transition: all 0.2s ease;
  margin-left: 4px;
  flex-shrink: 0;
}

.batch-highlight-btn:hover {
  background: #f0f0f0;
  border-color: #667eea;
  color: #667eea;
  transform: scale(1.1);
}

.batch-highlight-btn:active {
  transform: scale(0.95);
}

@keyframes mentionBreathe {
  0%, 100% {
    border-color: rgba(102, 126, 234, 0.3);
  }
  50% {
    border-color: rgba(102, 126, 234, 0.6);
  }
}

@keyframes mentionGrow {
  0% {
    transform: scale(0) translateY(-1px);
    opacity: 0;
  }
  60% {
    transform: scale(1.1) translateY(-1px);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* 消息时间 */
.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}

.chat-message.user .message-time {
  text-align: left;
}

/* 输入中指示器 */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-input-container {
  position: relative;
  border-top: 1px solid #e0e0e0;
  background: white;
}

/* 底部工具栏 */
.chat-bottom-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.chat-input {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 16px;
}

.chat-input textarea {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  max-height: 120px;
  min-height: 44px;
  outline: none;
  transition: border-color 0.2s ease;
}

.chat-input textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* @符号激活状态 */
.chat-input textarea.mention-active {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  animation: mentionPulse 0.3s ease-out;
}

/* @符号激活状态的提示 */
.chat-input textarea.mention-active::placeholder {
  color: #667eea;
  opacity: 0.7;
}

.chat-input textarea.mention-active:focus::placeholder {
  opacity: 0;
}

@keyframes mentionPulse {
  0% {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.3);
  }
  100% {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }
}

.chat-input textarea:disabled {
  background: #f5f5f5;
  color: #999;
}

/* @关联按钮 */
.association-button {
  background: white;
  color: #3b82f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  font-size: 10px;
  font-weight: 600;
  gap: 2px;
  margin-right: 8px;
}

.association-button:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  filter: drop-shadow(0 0 8px #3b82f6);
  transform: scale(1.05);
}

.association-button:active {
  transform: scale(0.95);
}

.association-button svg {
  width: 12px;
  height: 12px;
}

.association-button.empty {
  background: #f8fafc;
  color: #6b7280;
  border-color: #d1d5db;
  border-style: dashed;
}

.association-button.empty:hover {
  background: #f1f5f9;
  border-color: #9ca3af;
  color: #374151;
  filter: none;
}

.association-button.has-items {
  background: white;
  color: #3b82f6;
  border-color: #d1d5db;
  border-style: solid;
}

.association-button.has-items:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  filter: drop-shadow(0 0 8px #3b82f6);
  transform: scale(1.05);
}

.association-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 12px;
  text-align: center;
  line-height: 1;
}

.send-button {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.send-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
  transform: scale(1.08) translateY(-1px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.send-button:hover:not(:disabled)::before {
  left: 100%;
}

.send-button:active:not(:disabled) {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-messages {
    padding: 12px;
  }

  .chat-message {
    max-width: 90%;
  }

  .chat-input {
    padding: 12px;
  }

  .welcome-tips {
    align-items: center;
  }

  /* 移动端@节点标签优化 */
  .mentioned-nodes {
    gap: 6px;
    margin-top: 6px;
  }

  .mentioned-node {
    padding: 3px 8px;
    font-size: 11px;
    min-height: 20px;
  }

  /* 移动端多节点计数器 */
  .mentioned-nodes[data-count]:before {
    font-size: 10px;
    margin-right: 3px;
  }

  /* 移动端颜色简化 */
  .mentioned-node:nth-child(n+4) {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    color: #667eea;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .chat-interface {
    background: #1e1e1e;
  }

  .chat-messages {
    background: #1e1e1e;
  }

  .chat-welcome h3 {
    color: #fff;
  }

  .chat-welcome p,
  .tip {
    color: #ccc;
  }

  .tip-icon {
    background: #333;
  }

  .message-text {
    background: #333;
    color: #fff;
  }

  .chat-message.assistant .message-text {
    background: #333;
    color: #fff;
  }

  .chat-input-container {
    background: #1e1e1e;
    border-top-color: #333;
  }

  .chat-bottom-toolbar {
    background: #2a2a2a;
    border-top-color: #444;
  }

  .chat-input textarea {
    background: #333;
    color: #fff;
    border-color: #555;
  }

  .chat-input textarea:focus {
    border-color: #667eea;
  }

  /* 深色主题下的批量高亮按钮 */
  .batch-highlight-btn {
    background: #333;
    border-color: #555;
    color: #ccc;
  }

  .batch-highlight-btn:hover {
    background: #444;
    border-color: #667eea;
    color: #667eea;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 流式响应效果 */
.message-text.streaming {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.typing-cursor {
  color: #667eea;
  font-weight: bold;
  animation: blink 1.2s infinite;
  margin-left: 2px;
  text-shadow: 0 0 4px rgba(102, 126, 234, 0.3);
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 消息进入动画 */
@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 用户消息从右侧滑入 */
@keyframes messageSlideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(30px) translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* 助手消息从左侧滑入 */
@keyframes messageSlideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px) translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* 流式响应时的发送按钮 */
.send-button.streaming {
  background: #667eea;
  cursor: wait;
  animation: streamingPulse 2s infinite ease-in-out;
}

.streaming-indicator {
  display: flex;
  gap: 3px;
  align-items: center;
  justify-content: center;
}

.streaming-indicator .dot {
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  animation: streaming-pulse 1.4s infinite ease-in-out;
}

.streaming-indicator .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.streaming-indicator .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.streaming-indicator .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes streaming-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes streamingPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

/* 智能建议样式 */
.smart-suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
  z-index: 1000;
  animation: slideInUp 0.3s ease-out;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9ff;
  border-radius: 8px 8px 0 0;
}

.suggestions-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.suggestions-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.suggestions-close:hover {
  background: #e0e0e0;
  color: #333;
}

.suggestions-list {
  padding: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  margin-bottom: 4px;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-item:hover {
  background: #f8f9ff;
  transform: translateX(2px);
}

.suggestion-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.suggestion-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 流式响应时的发送按钮增强 */
.send-button.streaming {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: wait;
  animation: streamingPulse 2s infinite ease-in-out;
}

/* AI助手头像动画 */
@keyframes aiThinking {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes thinkingPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes aiResponding {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

@keyframes respondingGlow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes robotBlink {
  0%, 90%, 100% {
    opacity: 1;
  }
  95% {
    opacity: 0.3;
  }
}

@keyframes robotThinkingBlink {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.3;
  }
}

@keyframes robotExcited {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* AI助手呼吸动画 - 让头像更有生命力 */
@keyframes aiBreathing {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.5);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  }
}

/* 小帽子摇摆动画 - 思考时的可爱效果 */
@keyframes hatWiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

/* 小帽子弹跳动画 - 回复时的兴奋效果 */
@keyframes hatBounce {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  30% {
    transform: translateY(-2px) rotate(-2deg);
  }
  60% {
    transform: translateY(-1px) rotate(2deg);
  }
}

/* 用户头像发光动画 - 主角光环效果 */
@keyframes userGlow {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4);
  }
  50% {
    box-shadow: 0 6px 24px rgba(255, 154, 158, 0.6), 0 0 20px rgba(255, 215, 0, 0.3);
  }
}

/* 皇冠闪耀动画 */
@keyframes crownShine {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.2) drop-shadow(0 0 4px rgba(255, 215, 0, 0.6));
  }
}

/* 皇冠宝石闪烁动画 */
@keyframes jewelTwinkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 上下文链路开关按钮样式 */
.context-chain-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 36px;
  height: 36px;
  position: relative;
  margin-right: 8px;
}

.context-chain-toggle:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  color: #374151;
  transform: scale(1.05);
}

.context-chain-toggle.enabled {
  background: #dcfce7;
  border-color: #22c55e;
  color: #15803d;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
}

.context-chain-toggle.enabled:hover {
  background: #bbf7d0;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.15);
}

.context-chain-toggle.disabled {
  background: #fef2f2;
  border-color: #f87171;
  color: #dc2626;
  box-shadow: 0 0 0 2px rgba(248, 113, 113, 0.1);
}

.context-chain-toggle.disabled:hover {
  background: #fee2e2;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.15);
}

.context-chain-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.context-chain-toggle:hover svg {
  transform: scale(1.1);
}

.context-chain-toggle .loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.context-chain-toggle .spinner {
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
