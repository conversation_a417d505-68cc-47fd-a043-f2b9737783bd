"use client";

import React from 'react';
import { Chapter } from '@/db';
import { AnimatedChapterIcon, getChapterIcon } from './EnhancedChapterIcons';

interface ChapterStatusIndicatorProps {
  chapter: Chapter;
  className?: string;
  showIcon?: boolean;
  iconSize?: number;
}

/**
 * 章节状态指示器组件
 * 显示章节的内容状态：空章节、有内容、已完成
 */
export const ChapterStatusIndicator: React.FC<ChapterStatusIndicatorProps> = ({
  chapter,
  className = "",
  showIcon = false,
  iconSize = 14
}) => {
  // 计算章节状态
  const getChapterStatus = () => {
    const contentLength = chapter.content?.replace(/<[^>]*>/g, '').trim().length || 0;

    if (contentLength === 0) {
      return 'empty';
    } else if (contentLength < 500) {
      return 'draft';
    } else {
      return 'completed';
    }
  };

  const status = getChapterStatus();

  // 状态配置
  const statusConfig = {
    empty: {
      color: '#9CA3AF', // gray-400
      bgColor: 'rgba(156, 163, 175, 0.1)',
      label: '空章节',
      icon: null,
      animation: false
    },
    draft: {
      color: '#3B82F6', // blue-500
      bgColor: 'rgba(59, 130, 246, 0.1)',
      label: '有内容',
      icon: null,
      animation: true
    },
    completed: {
      color: '#10B981', // green-500
      bgColor: 'rgba(16, 185, 129, 0.1)',
      label: '已完成',
      icon: (
        <svg
          width="8"
          height="8"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <polyline points="20,6 9,17 4,12" />
        </svg>
      ),
      animation: false
    }
  };

  const config = statusConfig[status];

  return (
    <div
      className={`flex items-center gap-1 transition-all duration-500 ease-out ${className}`}
      title={config.label}
    >
      {/* 章节图标（可选） */}
      {showIcon && (
        <div className="transform transition-all duration-300 ease-out hover:scale-110">
          <AnimatedChapterIcon
            type={getChapterIcon(chapter)}
            size={iconSize}
            color={config.color}
            isActive={config.animation}
            animationType="scale"
            className="transition-all duration-300 ease-out"
          />
        </div>
      )}

      {/* 状态圆点 */}
      <div
        className={`relative w-3 h-3 rounded-full flex items-center justify-center transition-all duration-500 ease-out transform ${
          config.animation ? 'animate-pulse scale-110' : 'scale-100'
        }`}
        style={{
          backgroundColor: config.color,
          boxShadow: `0 0 0 3px ${config.bgColor}`,
          transition: 'all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)'
        }}
      >
        {/* 图标（如勾选） */}
        {config.icon && (
          <div
            className="absolute inset-0 flex items-center justify-center"
            style={{ color: 'white' }}
          >
            {config.icon}
          </div>
        )}

        {/* 脉冲动画（仅对有内容的章节） */}
        {config.animation && (
          <>
            <div
              className="absolute inset-0 rounded-full animate-ping"
              style={{
                backgroundColor: config.color,
                animationDuration: '2s'
              }}
            />
            <div
              className="absolute inset-0 rounded-full"
              style={{
                backgroundColor: config.color,
                animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
              }}
            />
          </>
        )}
      </div>

      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }

        @keyframes ping {
          75%, 100% {
            transform: scale(2);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

/**
 * 计算章节字数的工具函数
 * @param chapter 章节对象
 * @returns 格式化的字数字符串
 */
export const formatChapterWordCount = (chapter: Chapter): string => {
  const content = chapter.content || '';
  const plainText = content.replace(/<[^>]*>/g, '').trim();
  const wordCount = plainText.length;

  if (wordCount === 0) {
    return '空章节';
  } else if (wordCount < 1000) {
    return `${wordCount} 字`;
  } else {
    return `${(wordCount / 1000).toFixed(1)}k 字`;
  }
};

/**
 * 格式化相对时间的工具函数
 * @param date 日期对象
 * @returns 格式化的时间字符串
 */
export const formatRelativeTime = (date: Date): string => {
  // 确保date是有效的Date对象
  const validDate = date instanceof Date ? date : new Date(date);
  if (isNaN(validDate.getTime())) return '无效时间';

  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - validDate.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInMinutes < 1440) { // 24小时
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}小时前`;
  } else if (diffInMinutes < 10080) { // 7天
    const days = Math.floor(diffInMinutes / 1440);
    return `${days}天前`;
  } else {
    return validDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }
};
