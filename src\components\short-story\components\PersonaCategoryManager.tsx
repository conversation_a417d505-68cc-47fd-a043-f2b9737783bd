"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { AIPersonaConfig, PersonaCategory, CategoryPrompt } from '../../../types/ai-persona';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';
import Panel from '../../../factories/ui/components/common/Panel';
import CategoryPromptManager from './CategoryPromptManager';
import PersonaCategoryCreateDialog from './PersonaCategoryCreateDialog';

interface PersonaCategoryManagerProps {
  isOpen: boolean;
  onClose: () => void;
  persona: AIPersonaConfig | null;
  onUsePrompt?: (prompt: CategoryPrompt) => void;
}

const PersonaCategoryManager: React.FC<PersonaCategoryManagerProps> = ({
  isOpen,
  onClose,
  persona,
  onUsePrompt
}) => {
  const [categories, setCategories] = useState<PersonaCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<PersonaCategory | null>(null);
  const [showPromptManager, setShowPromptManager] = useState(false);
  const [personaStorageService] = useState(() => PersonaStorageService.getInstance());

  // 加载分类数据
  const loadCategories = async () => {
    if (!persona) return;

    try {
      setIsLoading(true);
      setError(null);

      const categoryList = await personaStorageService.getPersonaCategories(persona.id);
      setCategories(categoryList);

      console.log('分类数据加载完成:', {
        personaId: persona.id,
        categoryCount: categoryList.length,
        categories: categoryList.map(cat => ({
          id: cat.id,
          name: cat.name,
          promptsCount: cat.prompts.length,
          order: cat.order
        }))
      });
    } catch (err) {
      console.error('加载分类数据失败:', err);
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 当persona或isOpen变化时重新加载数据
  useEffect(() => {
    if (isOpen && persona) {
      loadCategories();
    }
  }, [isOpen, persona]);

  // 创建新分类
  const handleCreateCategory = async (categoryData: { name: string; prompts?: CategoryPrompt[] }) => {
    if (!persona) return;

    try {
      const newCategory = await personaStorageService.createCategory(persona.id, {
        name: categoryData.name,
        prompts: categoryData.prompts || [],
        order: categories.length
      });

      setCategories(prev => [...prev, newCategory]);
      setExpandedCategories(prev => new Set([...prev, newCategory.id]));

      console.log('创建分类成功:', newCategory.id);
    } catch (err) {
      console.error('创建分类失败:', err);
      setError(err instanceof Error ? err.message : '创建分类失败');
    }
  };

  // 更新分类
  const handleUpdateCategory = async (categoryId: string, updates: Partial<PersonaCategory>) => {
    if (!persona) return;

    try {
      await personaStorageService.updateCategory(persona.id, categoryId, updates);

      setCategories(prev => prev.map(cat =>
        cat.id === categoryId ? { ...cat, ...updates } : cat
      ));

      console.log('更新分类成功:', categoryId);
    } catch (err) {
      console.error('更新分类失败:', err);
      setError(err instanceof Error ? err.message : '更新分类失败');
    }
  };

  // 删除分类
  const handleDeleteCategory = async (categoryId: string) => {
    if (!persona) return;

    const category = categories.find(c => c.id === categoryId);
    if (!category) return;

    // 确认删除
    if (!window.confirm(`确定要删除分类"${category.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await personaStorageService.deleteCategory(persona.id, categoryId);

      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
      setExpandedCategories(prev => {
        const newSet = new Set(prev);
        newSet.delete(categoryId);
        return newSet;
      });

      console.log('删除分类成功:', categoryId);
    } catch (err) {
      console.error('删除分类失败:', err);
      setError(err instanceof Error ? err.message : '删除分类失败');
    }
  };

  // 处理分类点击，打开提示词管理器
  const handleCategoryClick = (category: PersonaCategory) => {
    setSelectedCategory(category);
    setShowPromptManager(true);
  };

  // 处理提示词保存
  const handleSavePrompts = async (prompts: CategoryPrompt[]) => {
    if (!selectedCategory || !persona) return;

    try {
      await handleUpdateCategory(selectedCategory.id, { prompts });

      // 更新本地状态
      setCategories(prev => prev.map(cat =>
        cat.id === selectedCategory.id ? { ...cat, prompts } : cat
      ));

      console.log('提示词保存成功:', selectedCategory.id);
    } catch (err) {
      console.error('提示词保存失败:', err);
      setError(err instanceof Error ? err.message : '提示词保存失败');
    }
  };

  // 关闭提示词管理器
  const handleClosePromptManager = () => {
    setShowPromptManager(false);
    setSelectedCategory(null);
  };



  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <motion.div
          className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <p className="text-gray-600">正在加载分类数据...</p>
      </div>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-red-600 mb-2">加载失败</p>
        <p className="text-gray-500 text-sm mb-4">{error}</p>
        <button
          onClick={loadCategories}
          className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
        >
          重试
        </button>
      </div>
    </div>
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <p className="text-gray-500 dark:text-gray-400 mb-2">暂无分类</p>
        <p className="text-sm text-gray-400 dark:text-gray-500 mb-4">创建第一个分类来组织提示词</p>
        <button
          onClick={() => setShowCreateDialog(true)}
          className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
        >
          创建分类
        </button>
      </div>
    </div>
  );

  // 渲染主内容
  const renderContent = () => {
    if (isLoading) return renderLoadingState();
    if (error) return renderErrorState();
    if (categories.length === 0) return renderEmptyState();

    return (
      <div className="space-y-4">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            分类管理 ({categories.length})
          </h3>
          <button
            onClick={() => setShowCreateDialog(true)}
            className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm"
          >
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              新建分类
            </div>
          </button>
        </div>

        {/* 分类文件夹网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatePresence mode="popLayout">
            {categories.map((category, index) => (
              <CategoryFolderCard
                key={category.id}
                category={category}
                onClick={() => handleCategoryClick(category)}
                onEdit={(updates) => handleUpdateCategory(category.id, updates)}
                onDelete={() => handleDeleteCategory(category.id)}
                index={index}
              />
            ))}
          </AnimatePresence>
        </div>
      </div>
    );
  };

  if (!persona) return null;

  // 使用Portal将弹窗渲染到document.body，避免被父级容器限制
  return createPortal(
    <>
      <Panel
        title={`分类管理 - ${persona.phase === 'custom' ? '自定义人设' : String(persona.phase)}`}
        isOpen={isOpen}
        onClose={onClose}
        size="large"
        enhanced={true}
        literaryTheme={true}
        content={renderContent()}
      />

      {/* 创建分类对话框 */}
      <PersonaCategoryCreateDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onCreateCategory={handleCreateCategory}
      />

      {/* 提示词管理弹窗 */}
      <CategoryPromptManager
        isOpen={showPromptManager}
        category={selectedCategory}
        onClose={handleClosePromptManager}
        onSave={handleSavePrompts}
        onUsePrompt={onUsePrompt}
      />
    </>,
    document.body
  );
};

// 分类文件夹卡片组件
interface CategoryFolderCardProps {
  category: PersonaCategory;
  onClick: () => void;
  onEdit: (updates: Partial<PersonaCategory>) => void;
  onDelete: () => void;
  index: number;
}

const CategoryFolderCard: React.FC<CategoryFolderCardProps> = ({
  category,
  onClick,
  onEdit,
  onDelete,
  index
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(category.name);

  // 处理名称编辑保存
  const handleSaveEdit = () => {
    if (editedName.trim() && editedName.trim() !== category.name) {
      onEdit({ name: editedName.trim() });
    }
    setIsEditing(false);
  };

  // 处理编辑取消
  const handleCancelEdit = () => {
    setEditedName(category.name);
    setIsEditing(false);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ delay: index * 0.05 }}
      className="group relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-md transition-all duration-200 cursor-pointer"
      onClick={onClick}
    >
      <div className="p-4">
        {/* 文件夹图标和名称 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
              </svg>
            </div>

            <div className="flex-1 min-w-0">
              {isEditing ? (
                <input
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  onBlur={handleSaveEdit}
                  onKeyDown={handleKeyDown}
                  className="w-full px-2 py-1 text-sm font-medium bg-white dark:bg-gray-700 border border-purple-300 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 dark:text-gray-100"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {category.name}
                </h4>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="p-1 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors"
              title="重命名分类"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
              title="删除分类"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">提示词数量</span>
            <span className="font-medium text-purple-600 dark:text-purple-400">
              {category.prompts.length}
            </span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">创建时间</span>
            <span className="text-gray-500 dark:text-gray-500">
              {new Date(category.createdAt).toLocaleDateString()}
            </span>
          </div>

          {category.updatedAt && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">最后更新</span>
              <span className="text-gray-500 dark:text-gray-500">
                {new Date(category.updatedAt).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {/* 点击提示 */}
        <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            点击打开提示词管理
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default PersonaCategoryManager;
