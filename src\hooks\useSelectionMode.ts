"use client";

import { useState, useCallback, useEffect } from 'react';
import { TextSegment, Sentence } from '@/factories/ai/services/TextProcessingService';

/**
 * 选择状态接口
 */
export interface SelectionState {
  isSelectionMode: boolean;
  selectedSentenceIds: Set<string>;
  totalSentenceCount: number;
  selectionHistory: string[][];
}

/**
 * 选择模式管理Hook
 * 提供句子选择功能的状态管理和操作方法
 */
export const useSelectionMode = (segments: TextSegment[]) => {
  // 选择状态
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedSentenceIds, setSelectedSentenceIds] = useState<Set<string>>(new Set());
  const [selectionHistory, setSelectionHistory] = useState<string[][]>([]);

  // 计算总句子数量
  const totalSentenceCount = segments.reduce((count, segment) => 
    count + segment.sentences.length, 0
  );

  // 计算已选择数量
  const selectedCount = selectedSentenceIds.size;

  // 从localStorage恢复选择状态
  useEffect(() => {
    const savedSelection = localStorage.getItem('annotation-selection-state');
    if (savedSelection) {
      try {
        const parsed = JSON.parse(savedSelection);
        if (parsed.selectedIds && Array.isArray(parsed.selectedIds)) {
          setSelectedSentenceIds(new Set(parsed.selectedIds));
        }
        if (typeof parsed.isSelectionMode === 'boolean') {
          setIsSelectionMode(parsed.isSelectionMode);
        }
      } catch (error) {
        console.warn('Failed to restore selection state:', error);
      }
    }
  }, []);

  // 保存选择状态到localStorage
  const saveSelectionState = useCallback(() => {
    const stateToSave = {
      selectedIds: Array.from(selectedSentenceIds),
      isSelectionMode,
      timestamp: Date.now()
    };
    localStorage.setItem('annotation-selection-state', JSON.stringify(stateToSave));
  }, [selectedSentenceIds, isSelectionMode]);

  // 切换选择模式
  const toggleSelectionMode = useCallback(() => {
    const newMode = !isSelectionMode;
    setIsSelectionMode(newMode);
    
    // 如果退出选择模式，保持选择状态但隐藏UI
    if (!newMode) {
      saveSelectionState();
    }
    
    console.log('🔄 选择模式切换:', { 
      from: isSelectionMode, 
      to: newMode, 
      selectedCount: selectedSentenceIds.size 
    });
  }, [isSelectionMode, selectedSentenceIds.size, saveSelectionState]);

  // 切换单个句子选择状态
  const toggleSentenceSelection = useCallback((sentenceId: string) => {
    setSelectedSentenceIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sentenceId)) {
        newSet.delete(sentenceId);
        console.log('❌ 取消选择句子:', sentenceId);
      } else {
        newSet.add(sentenceId);
        console.log('✅ 选择句子:', sentenceId);
      }
      
      // 保存到历史记录
      setSelectionHistory(history => [...history.slice(-9), Array.from(newSet)]);
      
      return newSet;
    });
  }, []);

  // 批量选择操作
  const batchSelect = useCallback((operation: 'all' | 'none' | 'invert') => {
    const allSentenceIds = segments.flatMap(segment => 
      segment.sentences.map(sentence => sentence.id)
    );

    setSelectedSentenceIds(prev => {
      let newSet: Set<string>;
      
      switch (operation) {
        case 'all':
          newSet = new Set(allSentenceIds);
          console.log('🔄 全选句子:', newSet.size);
          break;
        case 'none':
          newSet = new Set();
          console.log('🔄 取消全选');
          break;
        case 'invert':
          newSet = new Set(allSentenceIds.filter(id => !prev.has(id)));
          console.log('🔄 反选句子:', newSet.size);
          break;
        default:
          return prev;
      }
      
      // 保存到历史记录
      setSelectionHistory(history => [...history.slice(-9), Array.from(newSet)]);
      
      return newSet;
    });
  }, [segments]);

  // 按段落选择
  const selectBySegment = useCallback((segmentId: string, selected: boolean) => {
    const segment = segments.find(s => s.id === segmentId);
    if (!segment) return;

    const segmentSentenceIds = segment.sentences.map(s => s.id);
    
    setSelectedSentenceIds(prev => {
      const newSet = new Set(prev);
      
      if (selected) {
        segmentSentenceIds.forEach(id => newSet.add(id));
        console.log('📝 选择段落:', segmentId, '句子数:', segmentSentenceIds.length);
      } else {
        segmentSentenceIds.forEach(id => newSet.delete(id));
        console.log('📝 取消选择段落:', segmentId);
      }
      
      return newSet;
    });
  }, [segments]);

  // 检查句子是否被选中
  const isSentenceSelected = useCallback((sentenceId: string) => {
    return selectedSentenceIds.has(sentenceId);
  }, [selectedSentenceIds]);

  // 获取选中的句子
  const getSelectedSentences = useCallback(() => {
    const selectedSentences: Sentence[] = [];
    segments.forEach(segment => {
      segment.sentences.forEach(sentence => {
        if (selectedSentenceIds.has(sentence.id)) {
          selectedSentences.push(sentence);
        }
      });
    });
    return selectedSentences;
  }, [segments, selectedSentenceIds]);

  // 清除选择状态
  const clearSelection = useCallback(() => {
    setSelectedSentenceIds(new Set());
    setSelectionHistory([]);
    localStorage.removeItem('annotation-selection-state');
    console.log('🧹 清除选择状态');
  }, []);

  // 撤销上一次选择操作
  const undoSelection = useCallback(() => {
    if (selectionHistory.length > 1) {
      const previousSelection = selectionHistory[selectionHistory.length - 2];
      setSelectedSentenceIds(new Set(previousSelection));
      setSelectionHistory(history => history.slice(0, -1));
      console.log('↩️ 撤销选择操作');
    }
  }, [selectionHistory]);

  // 自动保存选择状态
  useEffect(() => {
    if (selectedSentenceIds.size > 0) {
      saveSelectionState();
    }
  }, [selectedSentenceIds, saveSelectionState]);

  return {
    // 状态
    isSelectionMode,
    selectedSentenceIds,
    selectedCount,
    totalSentenceCount,
    selectionHistory,
    
    // 操作方法
    toggleSelectionMode,
    toggleSentenceSelection,
    batchSelect,
    selectBySegment,
    isSentenceSelected,
    getSelectedSentences,
    clearSelection,
    undoSelection,
    
    // 工具方法
    saveSelectionState
  };
};

/**
 * 句子选择管理Hook
 * 专门用于管理单个句子的选择状态
 */
export const useSentenceSelection = (
  sentenceId: string,
  isSelected: boolean,
  onToggle: (sentenceId: string) => void
) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleToggle = useCallback(() => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    onToggle(sentenceId);
    
    // 动画完成后重置状态
    setTimeout(() => {
      setIsAnimating(false);
    }, 300);
  }, [sentenceId, onToggle, isAnimating]);

  return {
    isSelected,
    isAnimating,
    handleToggle
  };
};
