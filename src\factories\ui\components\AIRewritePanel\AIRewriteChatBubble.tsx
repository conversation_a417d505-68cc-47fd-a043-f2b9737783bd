"use client";

import React from 'react';

interface AIRewriteChatBubbleProps {
  role: 'user' | 'assistant';
  content: string;
  isLoading?: boolean;
  isLast?: boolean;
  onInsert?: (content: string) => void;
  onCopy?: (content: string) => void;
}

/**
 * AI改写聊天气泡组件
 * 用于显示对话中的单个气泡
 */
const AIRewriteChatBubble: React.FC<AIRewriteChatBubbleProps> = ({
  role,
  content,
  isLoading = false,
  isLast = false,
  onInsert,
  onCopy
}) => {
  // 处理插入内容
  const handleInsert = () => {
    if (onInsert) {
      onInsert(content);
    }
  };

  // 处理复制内容
  const handleCopy = () => {
    if (onCopy) {
      onCopy(content);
    } else {
      navigator.clipboard.writeText(content)
        .then(() => {
          console.log('内容已复制到剪贴板');
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  return (
    <div className={`bubble-container flex items-start mb-4 group ${role === 'user' ? 'justify-end' : ''}`}>
      {/* 用户气泡 */}
      {role === 'user' ? (
        <>
          {/* 用户气泡内容 */}
          <div className="user-bubble flex-grow-0 max-w-[80%] bg-blue-500 text-white rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow">
            <div className="whitespace-pre-line">
              {content}
            </div>
          </div>

          {/* 用户头像 */}
          <div className="flex-shrink-0 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm ml-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </>
      ) : (
        <>
          {/* AI头像 */}
          <div className="flex-shrink-0 bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>

          {/* AI气泡内容 */}
          <div className="ai-bubble flex-grow max-w-[80%] bg-indigo-50 border border-indigo-100 rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow">
            <div
              className="whitespace-pre-line"
              dangerouslySetInnerHTML={{
                __html: isLast && isLoading
                  ? content + '<span class="typing-cursor inline-block animate-pulse">▌</span>'
                  : content
              }}
            />
          </div>

          {/* 操作按钮 - 放在气泡旁边 */}
          <div className="ml-2 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col space-y-2">
            {/* 插入按钮 */}
            {onInsert && (
              <button
                className="bg-green-500 text-white rounded-full p-2 shadow-sm hover:bg-green-600 transition-colors"
                onClick={handleInsert}
                title="插入到编辑器"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </button>
            )}

            {/* 复制按钮 */}
            <button
              className="bg-blue-500 text-white rounded-full p-2 shadow-sm hover:bg-blue-600 transition-colors"
              onClick={handleCopy}
              title="复制内容"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default AIRewriteChatBubble;
