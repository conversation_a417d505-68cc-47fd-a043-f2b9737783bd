"use client";

import React from 'react';

interface SelectorItem {
  id: string;
  name: string;
  description?: string;
  disabled?: boolean; // 添加禁用标志，用于标记当前章节
}

interface SelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  items: SelectorItem[];
  selectedIds: string[];
  onSelect: (ids: string[]) => void;
  isLoading?: boolean;
  loadingText?: string;
  emptyText?: string;
  showSearch?: boolean;
  extraContent?: React.ReactNode; // 添加额外内容的属性
}

/**
 * 通用选择器对话框组件
 * 用于选择章节、人物、术语、世界观等元素
 */
const SelectorDialog: React.FC<SelectorDialogProps> = ({
  isOpen,
  onClose,
  title,
  items,
  selectedIds,
  onSelect,
  isLoading = false,
  loadingText = '加载中...',
  emptyText = '没有找到项目',
  showSearch = true,
  extraContent = null
}) => {
  // 搜索状态
  const [searchTerm, setSearchTerm] = React.useState('');

  // 过滤后的项目
  const filteredItems = React.useMemo(() => {
    if (!searchTerm) return items;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return items.filter(item =>
      item.name.toLowerCase().includes(lowerSearchTerm) ||
      (item.description && item.description.toLowerCase().includes(lowerSearchTerm))
    );
  }, [items, searchTerm]);

  // 处理选择变更
  const handleItemSelect = (id: string) => {
    if (selectedIds.includes(id)) {
      onSelect(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      onSelect([...selectedIds, id]);
    }
  };

  // 全选
  const handleSelectAll = () => {
    onSelect(filteredItems.map(item => item.id));
  };

  // 取消全选
  const handleDeselectAll = () => {
    onSelect([]);
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-xl w-[550px] max-h-[85vh] flex flex-col overflow-hidden">
        {/* 头部 */}
        <div className="p-4 border-b flex justify-between items-center bg-gradient-to-r from-indigo-50 to-blue-50">
          <div className="flex items-center">
            <div className="bg-indigo-100 p-2 rounded-lg mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-indigo-800">{title}</h2>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索框 */}
        {showSearch && (
          <div className="p-4 border-b">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索..."
                className="w-full px-4 py-2.5 pl-11 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <div className="absolute left-4 top-3 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        )}

        {/* 选择按钮 */}
        <div className="px-4 py-3 border-b flex justify-between items-center bg-gray-50">
          <div className="text-sm font-medium text-indigo-700 bg-indigo-50 px-3 py-1 rounded-full">
            已选择 {selectedIds.length} / {filteredItems.length} 项
          </div>
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleSelectAll}
              className="px-3 py-1.5 text-xs bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm font-medium"
              disabled={filteredItems.length === 0}
            >
              全选
            </button>
            <button
              type="button"
              onClick={handleDeselectAll}
              className="px-3 py-1.5 text-xs bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-sm font-medium"
              disabled={selectedIds.length === 0}
            >
              取消全选
            </button>
          </div>
        </div>

        {/* 额外内容 */}
        {extraContent && (
          <div className="border-b">
            {extraContent}
          </div>
        )}

        {/* 项目列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="relative">
                <div className="w-10 h-10 border-4 border-indigo-200 border-t-indigo-500 rounded-full animate-spin"></div>
                <div className="absolute top-0 left-0 w-10 h-10 border-4 border-transparent border-b-indigo-300 rounded-full animate-spin" style={{ animationDuration: '1.5s' }}></div>
              </div>
              <p className="text-indigo-700 ml-3 font-medium">{loadingText}</p>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-500">{emptyText}</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredItems.map(item => (
                <div
                  key={item.id}
                  className={`p-3 rounded-xl border transition-all ${
                    item.disabled
                      ? 'bg-gray-100 border-gray-300 cursor-not-allowed'
                      : selectedIds.includes(item.id)
                        ? 'bg-indigo-50 border-indigo-300 shadow-sm cursor-pointer'
                        : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 cursor-pointer'
                  }`}
                  onClick={() => !item.disabled && handleItemSelect(item.id)}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`item-${item.id}`}
                      checked={selectedIds.includes(item.id)}
                      onChange={() => !item.disabled && handleItemSelect(item.id)}
                      disabled={item.disabled}
                      className={`mr-3 h-4 w-4 focus:ring-indigo-500 border-gray-300 rounded ${
                        item.disabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-indigo-600 cursor-pointer'
                      }`}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex-1">
                      <label
                        htmlFor={`item-${item.id}`}
                        className={`block text-sm font-medium ${
                          item.disabled
                            ? 'text-gray-500 cursor-not-allowed'
                            : 'text-gray-800 cursor-pointer'
                        }`}
                      >
                        {item.name}
                        {item.disabled && <span className="ml-2 text-xs text-blue-500">(当前章节)</span>}
                      </label>
                      {item.description && (
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">{item.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-3 bg-gradient-to-r from-gray-50 to-indigo-50">
          <button
            className="px-4 py-2 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-xl hover:from-indigo-600 hover:to-blue-700 transition-colors shadow-md font-medium"
            onClick={onClose}
          >
            确认选择
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectorDialog;
