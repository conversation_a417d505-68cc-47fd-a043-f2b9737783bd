"use client";

import React, { useState } from 'react';
import { UnifiedAssociationDialog } from './UnifiedAssociationDialog';

interface UnifiedAssociationButtonProps {
  bookId: string;
  selectedChapterIds?: string[];
  selectedCharacterIds?: string[];
  selectedTerminologyIds?: string[];
  selectedWorldBuildingIds?: string[];
  onAssociationsChange?: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
}

/**
 * 统一关联管理按钮组件
 * 提供一键管理所有类型关联内容的功能
 */
export const UnifiedAssociationButton: React.FC<UnifiedAssociationButtonProps> = ({
  bookId,
  selectedChapterIds = [],
  selectedCharacterIds = [],
  selectedTerminologyIds = [],
  selectedWorldBuildingIds = [],
  onAssociationsChange,
  variant = 'default',
  className = '',
  disabled = false
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // 计算总关联数量
  const totalCount = selectedChapterIds.length +
                    selectedCharacterIds.length +
                    selectedTerminologyIds.length +
                    selectedWorldBuildingIds.length;

  // 处理关联变化
  const handleAssociationsChange = (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    onAssociationsChange?.(associations);
  };

  // 获取按钮样式
  const getButtonStyles = () => {
    const baseStyles = "relative inline-flex items-center justify-center transition-all duration-200 font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2";

    switch (variant) {
      case 'compact':
        return `${baseStyles} px-3 py-2 text-sm bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700`;
      case 'icon-only':
        return `${baseStyles} w-10 h-10 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700`;
      default:
        return `${baseStyles} px-4 py-2 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700 shadow-sm`;
    }
  };

  // 获取类型指示器
  const getTypeIndicators = () => {
    const indicators = [];

    if (selectedChapterIds.length > 0) {
      indicators.push(
        <div key="chapter" className="w-2 h-2 rounded-full bg-blue-500" title={`${selectedChapterIds.length}个章节`} />
      );
    }

    if (selectedCharacterIds.length > 0) {
      indicators.push(
        <div key="character" className="w-2 h-2 rounded-full bg-green-500" title={`${selectedCharacterIds.length}个人物`} />
      );
    }

    if (selectedTerminologyIds.length > 0) {
      indicators.push(
        <div key="terminology" className="w-2 h-2 rounded-full bg-orange-500" title={`${selectedTerminologyIds.length}个术语`} />
      );
    }

    if (selectedWorldBuildingIds.length > 0) {
      indicators.push(
        <div key="worldBuilding" className="w-2 h-2 rounded-full bg-purple-500" title={`${selectedWorldBuildingIds.length}个世界观`} />
      );
    }

    return indicators;
  };

  return (
    <>
      <button
        className={`${getButtonStyles()} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={() => !disabled && setIsDialogOpen(true)}
        disabled={disabled}
        title="管理关联内容"
      >
        {/* 图标 */}
        <svg
          className={`${variant === 'icon-only' ? 'w-5 h-5' : 'w-4 h-4'} ${variant !== 'icon-only' ? 'mr-2' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
          />
        </svg>

        {/* 文本（非图标模式） */}
        {variant !== 'icon-only' && (
          <span>
            {variant === 'compact' ? '关联' : '管理关联'}
          </span>
        )}

        {/* 数量徽章 */}
        {totalCount > 0 && (
          <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[20px] h-5">
            {totalCount > 99 ? '99+' : totalCount}
          </span>
        )}

        {/* 类型指示器 */}
        {variant === 'default' && getTypeIndicators().length > 0 && (
          <div className="absolute -bottom-1 -right-1 flex space-x-0.5">
            {getTypeIndicators()}
          </div>
        )}
      </button>

      {/* 统一关联管理对话框 */}
      <UnifiedAssociationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        bookId={bookId}
        selectedChapterIds={selectedChapterIds}
        selectedCharacterIds={selectedCharacterIds}
        selectedTerminologyIds={selectedTerminologyIds}
        selectedWorldBuildingIds={selectedWorldBuildingIds}
        onAssociationsChange={handleAssociationsChange}
      />
    </>
  );
};

export default UnifiedAssociationButton;
