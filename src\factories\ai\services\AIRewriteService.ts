"use client";

import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { TextProcessingServiceInterface, createTextProcessingService } from './TextProcessingService';
import { PromptBuilderServiceInterface, createPromptBuilderService } from './PromptBuilderService';
// import { ConversationHistoryServiceInterface, createConversationHistoryService } from './ConversationHistoryService'; // 暂时不使用
import { PromptHelperServiceInterface, createPromptHelperService } from './PromptHelperService';
import { dialogContinuationManager } from '@/utils/ai/DialogContinuationManager';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

// 定义消息类型
interface Message {
  role: string;
  content: string;
  isSystemGenerated?: boolean;
}

/**
 * AI改写服务接口
 */
export interface AIRewriteServiceInterface {
  /**
   * 改写内容
   * @param params 改写参数
   * @param callbacks 回调函数
   */
  rewriteContent(
    params: RewriteContentParams,
    callbacks: RewriteContentCallbacks
  ): Promise<RewriteContentResult>;
}

/**
 * 改写内容参数
 */
export interface RewriteContentParams {
  // API设置
  provider: string;
  model: string;
  apiKey: string;
  apiEndpoint?: string;

  // 内容设置
  selectedText: string;
  rewriteRequirements?: string;
  plot?: string;

  // 上下文
  beforeContext?: string;
  afterContext?: string;

  // 关联元素
  chapters: any[]; // 使用any类型以兼容不同的Chapter接口
  selectedChapterIds: string[];
  characters: Character[];
  selectedCharacterIds: string[];
  terminologies: Terminology[];
  selectedTerminologyIds: string[];
  worldBuildings: WorldBuilding[];
  selectedWorldBuildingIds: string[];

  // 大纲关联元素
  outlines?: any[];
  selectedOutlineIds?: string[];
  selectedOutlineNodeIds?: string[];
  outlineContextMode?: 'selected' | 'hierarchy' | 'full';

  // 书籍ID
  bookId: string;

  // 对话历史
  conversationHistory?: ConversationMessage[];
}

/**
 * 对话消息
 */
export interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  isContextMessage?: boolean; // 标记是否为上下文消息，在气泡中不显示
  isSystemGenerated?: boolean; // 标记是否为系统生成的预设消息，在气泡中不显示
}

/**
 * 改写内容回调函数
 */
export interface RewriteContentCallbacks {
  onStart?: () => void;
  onStreamChunk?: (chunk: string) => void;
  onComplete?: (result: RewriteContentResult) => void;
  onError?: (error: Error) => void;
}

/**
 * 改写内容结果
 */
export interface RewriteContentResult {
  text: string;
  success: boolean;
  error?: string;
  conversationHistory?: ConversationMessage[]; // 返回更新后的对话历史
}

/**
 * AI改写服务实现
 */
export class AIRewriteService extends UnifiedAIService implements AIRewriteServiceInterface {
  private aiSender: DefaultAISenderComponent;
  private apiSettings: any;
  private textProcessingService: TextProcessingServiceInterface;
  private promptBuilderService: PromptBuilderServiceInterface;
  // private conversationHistoryService: ConversationHistoryServiceInterface; // 暂时不使用
  private promptHelperService: PromptHelperServiceInterface;

  constructor() {
    super(AIServiceType.REWRITE);
    this.aiSender = new DefaultAISenderComponent();

    // 获取API设置
    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 初始化服务
    this.textProcessingService = createTextProcessingService();
    this.promptBuilderService = createPromptBuilderService();
    // this.conversationHistoryService = createConversationHistoryService(this.promptBuilderService); // 暂时不使用
    this.promptHelperService = createPromptHelperService();

    // 确保在客户端环境中初始化时加载设置
    if (typeof window !== 'undefined') {
      // 延迟一点时间再获取API设置，确保localStorage已加载
      setTimeout(() => {
        // 重新获取API设置，确保从localStorage加载了最新的设置
        this.apiSettings = settingsFactory.createAPISettingsDialogComponent();
        console.log('AIRewriteService: 已重新加载API设置');
      }, 200);
    }
  }

  /**
   * 改写内容
   * @param params 改写参数
   * @param callbacks 回调函数
   */
  async rewriteContent(
    params: RewriteContentParams,
    callbacks: RewriteContentCallbacks
  ): Promise<RewriteContentResult> {
    try {
      callbacks.onStart?.();

      // 构建提示消息
      const messages = this.buildPromptMessages(params);

      // 确保流式输出已启用
      const streamingEnabled = this.apiSettings.getStreamingEnabled();
      console.log('流式输出状态:', streamingEnabled ? '已启用' : '已禁用');

      // 使用统一的AI流式调用方法
      const result = await this.callAIStreaming(
        messages,
        (chunk: string) => {
          // 直接处理每个流式响应块
          if (callbacks.onStreamChunk) {
            callbacks.onStreamChunk(chunk);
          }
        },
        {
          streaming: true
        }
      );

      // 处理结果
      const rewrittenContent = result.text.trim();

      // 获取当前对话模式
      let continueMode = 'new';
      if (params.bookId) {
        const savedMode = localStorage.getItem(`ai-rewrite-mode-${params.bookId}`);
        if (savedMode) {
          try {
            continueMode = JSON.parse(savedMode);
          } catch (error) {
            console.error('解析对话模式失败:', error);
          }
        }
      }

      // 使用 DialogContinuationService 或 ConversationHistoryService 构建新的对话历史
      let updatedHistory;
      if (params.conversationHistory && params.conversationHistory.length > 0 && continueMode !== 'new') {
        // 获取当前内容（最后一条助手消息）
        const currentContent = params.conversationHistory
          .filter(msg => msg.role === 'assistant')
          .pop()?.content || '';

        // 获取用户提示
        let userPrompt = params.bookId ?
          localStorage.getItem(`ai-rewrite-user-prompt-${params.bookId}`) || '' :
          '';

        // 记录原始用户提示，用于调试
        console.log('原始用户提示:', userPrompt);

        // 构建关联消息
        const relatedMessages: ConversationMessage[] = [];

        // 无论是什么对话模式，都添加关联元素作为上下文消息
        // 注意：只有在对话历史中不存在这些关联消息时才添加，避免重复
          // 检查对话历史中是否已包含章节信息
          const hasChapterInfo = params.conversationHistory.some(msg =>
            msg.role === 'user' && msg.content.includes('请参考以下章节信息：')
          );

          // 添加章节信息（如果尚未添加）
          if (!hasChapterInfo && params.selectedChapterIds.length > 0) {
            const selectedChapters = params.chapters.filter(chapter => {
              return chapter.id && params.selectedChapterIds.includes(chapter.id);
            });

            if (selectedChapters.length > 0) {
              relatedMessages.push({
                role: 'user',
                content: `请参考以下章节信息：\n${selectedChapters.map(chapter =>
                  `章节：${chapter.title || '无标题'}\n${chapter.content || '无内容'}`
                ).join('\n\n')}`
              });

              relatedMessages.push({
                role: 'assistant',
                content: '我已了解相关章节信息，将在继续创作时保持一致性。'
              });
            }
          }

          // 检查对话历史中是否已包含角色信息
          const hasCharacterInfo = params.conversationHistory.some(msg =>
            msg.role === 'user' && msg.content.includes('请参考以下角色信息：')
          );

          // 添加角色信息（如果尚未添加）
          if (!hasCharacterInfo && params.selectedCharacterIds.length > 0) {
            const selectedCharacters = params.characters.filter(character => {
              return character.id && params.selectedCharacterIds.includes(character.id);
            });

            if (selectedCharacters.length > 0) {
              relatedMessages.push({
                role: 'user',
                content: `请参考以下角色信息：\n${selectedCharacters.map(character =>
                  `角色：${character.name || '无名称'}\n${character.description || '无描述'}`
                ).join('\n\n')}`
              });

              relatedMessages.push({
                role: 'assistant',
                content: '我已了解相关角色信息，将在继续创作时保持角色特性的一致性。'
              });
            }
          }

          // 检查对话历史中是否已包含世界观信息
          const hasWorldBuildingInfo = params.conversationHistory.some(msg =>
            msg.role === 'user' && msg.content.includes('请参考以下世界观信息：')
          );

          // 添加世界观信息（如果尚未添加）
          if (!hasWorldBuildingInfo && params.selectedWorldBuildingIds.length > 0) {
            const selectedWorldBuildings = params.worldBuildings.filter(worldBuilding => {
              return worldBuilding.id && params.selectedWorldBuildingIds.includes(worldBuilding.id);
            });

            if (selectedWorldBuildings.length > 0) {
              relatedMessages.push({
                role: 'user',
                content: `请参考以下世界观信息：\n${selectedWorldBuildings.map(worldBuilding =>
                  `世界观：${worldBuilding.name || '无名称'}\n${worldBuilding.description || '无描述'}`
                ).join('\n\n')}`
              });

              relatedMessages.push({
                role: 'assistant',
                content: '我已了解相关世界观信息，将在继续创作时保持世界设定的一致性。'
              });
            }
          }

          // 检查对话历史中是否已包含术语信息
          const hasTerminologyInfo = params.conversationHistory.some(msg =>
            msg.role === 'user' && msg.content.includes('请参考以下术语信息：')
          );

          // 添加术语信息（如果尚未添加）
          if (!hasTerminologyInfo && params.selectedTerminologyIds.length > 0) {
            const selectedTerminologies = params.terminologies.filter(terminology => {
              return terminology.id && params.selectedTerminologyIds.includes(terminology.id);
            });

            if (selectedTerminologies.length > 0) {
              relatedMessages.push({
                role: 'user',
                content: `请参考以下术语信息：\n${selectedTerminologies.map(terminology =>
                  `术语：${terminology.name || '无名称'}\n${terminology.description || '无描述'}`
                ).join('\n\n')}`
              });

              relatedMessages.push({
                role: 'assistant',
                content: '我已了解相关术语信息，将在继续创作时正确使用这些术语。'
              });
            }
          }

        // 使用 DialogContinuationManager 更新对话历史，包含上下文和关联消息
        const result = await dialogContinuationManager.handleContinuation({
          currentContent,
          continuePrompt: userPrompt,
          continueMode: continueMode as 'new' | 'continue' | 'rewrite' | 'analyze',
          bookId: params.bookId || '',
          conversationHistory: params.conversationHistory,
          serviceType: 'rewrite' as const,

          // 关联元素
          chapters: params.chapters,
          selectedChapterIds: params.selectedChapterIds,
          characters: params.characters,
          selectedCharacterIds: params.selectedCharacterIds,
          terminologies: params.terminologies,
          selectedTerminologyIds: params.selectedTerminologyIds,
          worldBuildings: params.worldBuildings,
          selectedWorldBuildingIds: params.selectedWorldBuildingIds,

          // 大纲关联元素
          outlines: params.outlines,
          selectedOutlineIds: params.selectedOutlineIds,
          selectedOutlineNodeIds: params.selectedOutlineNodeIds,
          outlineContextMode: params.outlineContextMode,

          // 上下文信息
          beforeContext: params.beforeContext,
          afterContext: params.afterContext,

          // 选中的文本
          selectedText: params.selectedText,

          // 改写要求 - 确保传递最新的改写要求
          rewriteRequirements: params.rewriteRequirements,

          // 剧情方向 - 确保传递最新的剧情方向
          plot: params.plot
        });
        updatedHistory = result.updatedHistory;

        // 添加助手消息（生成的内容）
        // 注意：只有在updatedHistory的最后一条消息不是助手消息时才添加
        const lastMessage = updatedHistory[updatedHistory.length - 1];
        if (!lastMessage || lastMessage.role !== 'assistant') {
          updatedHistory.push({
            role: 'assistant',
            content: rewrittenContent,
            isSystemGenerated: false // Explicitly mark as not system-generated
          } as ConversationMessage);
        } else {
          // 如果最后一条是助手消息，更新其内容
          lastMessage.content = rewrittenContent;
          (lastMessage as any).isSystemGenerated = false; // Explicitly mark as not system-generated
        }
      } else {
        // 对于首次生成，我们需要保存完整的消息记录，包括所有系统提示、关联信息、上下文和选中文本
        // 直接使用构建API请求时使用的完整消息记录，只需添加AI的响应
        updatedHistory = messages.map(msg => {
          // 确保每条消息都有isSystemGenerated属性
          // 默认情况下，系统消息和助手的初始响应被标记为系统生成的预设消息
          // 用户的实际输入和AI的实质性回复不被标记为系统生成的预设消息
          const isSystemGenerated =
            // 如果消息已经有isSystemGenerated属性，则保留原值
            msg.hasOwnProperty('isSystemGenerated') ? msg.isSystemGenerated :
            // 系统消息默认为系统生成
            msg.role === 'system' ||
            // 助手消息，如果包含特定的确认文本，则为系统生成
            (msg.role === 'assistant' && (
              msg.content.includes('我已阅读并分析了') ||
              msg.content.includes('我已记住') ||
              msg.content.includes('我将查看并分析') ||
              msg.content.includes('我已了解相关') ||
              msg.content.includes('我已完成所有章节的分析')
            )) ||
            // 用户消息，如果包含特定的系统生成文本，则为系统生成
            (msg.role === 'user' && (
              msg.content.includes('请根据以上信息，在【') ||
              msg.content.includes('请参考以下') ||
              msg.content.includes('上文 第') ||
              msg.content.includes('下文 第') ||
              msg.content.includes('【需要改写的文本】') ||
              msg.content.includes('请确保改写后的内容与上文自然衔接')
            ));

          return {
            role: msg.role as 'system' | 'user' | 'assistant',
            content: msg.content,
            isSystemGenerated
          } as ConversationMessage;
        });

        // 添加AI生成的内容作为最后一条消息
        // 检查最后一条消息是否为用户消息，如果是，则添加AI响应；如果不是，则更新最后一条消息
        const lastMsg = updatedHistory[updatedHistory.length - 1];
        if (lastMsg && lastMsg.role === 'user') {
          updatedHistory.push({
            role: 'assistant',
            content: rewrittenContent,
            isSystemGenerated: false // 明确标记为非系统生成的预设消息
          } as ConversationMessage);
        } else if (lastMsg && lastMsg.role === 'assistant') {
          lastMsg.content = rewrittenContent;
          (lastMsg as any).isSystemGenerated = false; // 明确标记为非系统生成的预设消息
        }

        // 记录保存的消息类型，用于调试
        const messageTypes = updatedHistory.map(msg => msg.role);
        console.log('[AIRewriteService] 保存的消息类型:', messageTypes);

        // 记录消息内容的前20个字符，用于调试
        const messageContents = updatedHistory.map(msg => ({
          role: msg.role,
          content: msg.content.substring(0, 20) + '...'
        }));
        console.log('[AIRewriteService] 保存的消息内容预览:', messageContents);

        console.log('[AIRewriteService] 首次生成，保存完整消息记录，长度:', updatedHistory.length);
      }

      const contentResult: RewriteContentResult = {
        text: rewrittenContent,
        success: true,
        conversationHistory: updatedHistory
      };

      callbacks.onComplete?.(contentResult);
      return contentResult;

    } catch (error: any) {
      console.error('改写内容失败:', error);

      const errorResult: RewriteContentResult = {
        text: '',
        success: false,
        error: error instanceof Error ? error.message : '改写内容时发生错误'
      };

      callbacks.onError?.(error);
      return errorResult;
    }
  }

  /**
   * 构建提示消息
   * @param params 改写参数
   * @returns 提示消息
   */
  private buildPromptMessages(params: RewriteContentParams): Message[] {
    const messageBuilder = new MessageBuilder();

    // 获取当前对话模式
    let continueMode = 'new';
    if (params.bookId) {
      const savedMode = localStorage.getItem(`ai-rewrite-mode-${params.bookId}`);
      if (savedMode) {
        try {
          continueMode = JSON.parse(savedMode);
        } catch (error) {
          console.error('解析对话模式失败:', error);
        }
      }
    }

    // 检查是否有对话历史且不是新对话
    if (params.conversationHistory && params.conversationHistory.length > 0 && continueMode !== 'new') {
      console.log('[AIRewriteService] 使用现有对话历史构建消息，历史长度:', params.conversationHistory.length);

      // 使用新的 DialogContinuationManager 处理继续对话
      const userPrompt = params.bookId ? localStorage.getItem(`ai-rewrite-user-prompt-${params.bookId}`) || '' : '';

      // 获取当前内容（最后一条助手消息）- 不使用这个变量，但保留代码以便将来可能的扩展
      // const currentContent = params.conversationHistory
      //   .filter(msg => msg.role === 'assistant')
      //   .pop()?.content || '';

      // 记录原始对话历史中的消息类型，用于调试
      const messageTypes = params.conversationHistory.map(msg => msg.role);
      console.log('[AIRewriteService] 原始对话历史中的消息类型:', messageTypes);

      // 记录消息内容的前20个字符，用于调试
      const messageContents = params.conversationHistory.map(msg => ({
        role: msg.role,
        content: msg.content.substring(0, 20) + '...'
      }));
      console.log('[AIRewriteService] 原始对话历史中的消息内容预览:', messageContents);

      // 直接使用完整的原始消息记录，只添加新的用户提示
      // 构建用户提示
      let formattedUserPrompt = userPrompt;

      // 根据模式添加前缀
      if (continueMode === 'continue' && !userPrompt.includes('请继续上述内容，保持一致的风格和情节走向')) {
        formattedUserPrompt = `请继续上述内容，保持一致的风格和情节走向。${userPrompt}`;
      } else if (continueMode === 'rewrite' && !userPrompt.includes('请重写上述内容，但保持核心情节不变')) {
        formattedUserPrompt = `请重写上述内容，但保持核心情节不变。${userPrompt}`;
      } else if (continueMode === 'analyze' && !userPrompt.includes('请分析上述内容，提供深入的文学分析和建议')) {
        formattedUserPrompt = `请分析上述内容，提供深入的文学分析和建议。${userPrompt}`;
      }

      // 如果有改写要求，添加到用户提示中
      if (params.rewriteRequirements && params.rewriteRequirements.trim() && !formattedUserPrompt.includes('【改写要求】')) {
        formattedUserPrompt += `\n\n【改写要求】\n${params.rewriteRequirements}`;
      }

      // 注意：剧情方向现在通过DialogContinuationManager独立处理，不再添加到用户提示中

      // 确保对话历史中的所有消息都有isSystemGenerated属性
      const processedHistory = params.conversationHistory.map(msg => {
        // 如果消息已经有isSystemGenerated属性，则保留原值
        if (msg.hasOwnProperty('isSystemGenerated')) {
          return msg;
        }

        // 否则，根据消息类型和内容设置默认值
        const isSystemGenerated =
          // 系统消息默认为系统生成
          msg.role === 'system' ||
          // 助手消息，如果包含特定的确认文本，则为系统生成
          (msg.role === 'assistant' && (
            msg.content.includes('我已阅读并分析了') ||
            msg.content.includes('我已记住') ||
            msg.content.includes('我将查看并分析') ||
            msg.content.includes('我已了解相关') ||
            msg.content.includes('我已完成所有章节的分析')
          )) ||
          // 用户消息，如果包含特定的系统生成文本，则为系统生成
          (msg.role === 'user' && (
            msg.content.includes('请根据以上信息，在【') ||
            msg.content.includes('请参考以下') ||
            msg.content.includes('上文 第') ||
            msg.content.includes('下文 第') ||
            msg.content.includes('【需要改写的文本】') ||
            msg.content.includes('请确保改写后的内容与上文自然衔接')
          ));

        return {
          ...msg,
          isSystemGenerated
        };
      });

      const messages = [
        ...processedHistory,
        {
          role: 'user',
          content: formattedUserPrompt,
          isSystemGenerated: false // Explicitly mark as not system-generated
        }
      ];

      console.log('[AIRewriteService] 继续对话，使用完整的原始消息记录，长度:', messages.length);

      return messages;
    } else {
      // 新对话，使用原来的逻辑

      // 添加系统提示，标记为系统生成的预设消息
      messageBuilder.addSystemMessage(this.promptBuilderService.getSystemPrompt(params), true, true);

      // 使用 PromptHelperService 添加上下文信息
      this.promptHelperService.addSelectedChapters(
        messageBuilder,
        params.chapters,
        params.selectedChapterIds
      );
      this.promptHelperService.addSelectedCharacters(
        messageBuilder,
        params.characters,
        params.selectedCharacterIds
      );
      this.promptHelperService.addSelectedWorldBuildings(
        messageBuilder,
        params.worldBuildings,
        params.selectedWorldBuildingIds
      );
      this.promptHelperService.addSelectedTerminologies(
        messageBuilder,
        params.terminologies,
        params.selectedTerminologyIds
      );

      // 添加选中的大纲节点信息
      console.log('🔍 检查大纲数据传递:', {
        hasOutlines: !!params.outlines,
        outlinesLength: params.outlines?.length || 0,
        hasSelectedNodeIds: !!params.selectedOutlineNodeIds,
        selectedNodeIdsLength: params.selectedOutlineNodeIds?.length || 0,
        selectedNodeIds: params.selectedOutlineNodeIds
      });

      if (params.outlines && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0) {
        console.log('✅ 大纲数据条件满足，开始添加大纲节点信息');
        this.promptHelperService.addSelectedOutlineNodes(
          messageBuilder,
          params.outlines,
          params.selectedOutlineNodeIds,
          params.outlineContextMode
        );
      } else {
        console.log('❌ 大纲数据条件不满足，跳过大纲节点信息添加');
      }

      // 添加上文内容
      if (params.beforeContext && this.textProcessingService.calculateActualWordCount(params.beforeContext) > 0) {
        messageBuilder.addAssistantMessage(`我将查看并分析上文内容，以便创作符合上下文的内容：`, true, true);

        const beforeContextSegments = this.textProcessingService.segmentText(params.beforeContext);
        for (let i = 0; i < beforeContextSegments.length; i++) {
          const segment = beforeContextSegments[i];
          const segmentWordCount = this.textProcessingService.calculateActualWordCount(segment);
          messageBuilder.addUserMessage(`上文 第${i+1}/${beforeContextSegments.length}段，${segmentWordCount}字，内容：\n\n${segment}`, undefined, true, true);
          messageBuilder.addAssistantMessage(`我已阅读并分析了上文的第${i+1}段内容。`, true, true);
        }

        messageBuilder.addUserMessage(`请确保改写后的内容与上文自然衔接，保持情节和语气的一致性。`, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已记住上文的最后几句话，将确保与需要改写的内容前部分自然衔接。`, true, true);
      }

      // 添加需要改写的文本
      const selectedTextWordCount = this.textProcessingService.calculateActualWordCount(params.selectedText);
      messageBuilder.addUserMessage(`【需要你写的文本概念】(${selectedTextWordCount}字)\n\n${params.selectedText}`, undefined, true, true);
      messageBuilder.addAssistantMessage(`我已理解需要我写的文本概念内容(${selectedTextWordCount}字)，我开始书写。`, true, true);

      // 添加下文内容
      if (params.afterContext && this.textProcessingService.calculateActualWordCount(params.afterContext) > 0) {
        messageBuilder.addAssistantMessage(`我将查看并分析下文内容，以便创作符合上下文的内容：`, true, true);

        const afterContextSegments = this.textProcessingService.segmentText(params.afterContext);
        for (let i = 0; i < afterContextSegments.length; i++) {
          const segment = afterContextSegments[i];
          const segmentWordCount = this.textProcessingService.calculateActualWordCount(segment);
          messageBuilder.addUserMessage(`下文 第${i+1}/${afterContextSegments.length}段，${segmentWordCount}字，内容：\n\n${segment}`, undefined, true, true);
          messageBuilder.addAssistantMessage(`我已阅读并分析了下文的第${i+1}段内容。`, true, true);
        }

        messageBuilder.addUserMessage(`下文的开头几句话是：\n\n${params.afterContext}\n\n(${this.textProcessingService.calculateActualWordCount(params.afterContext)}字)，请将这几句话作为衔接点，与改写后的内容自然衔接，不要重复这些内容。`, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已记住下文的开头几句话，将确保改写后的内容能够自然引出下文，不会重复下文的内容。`, true, true);
      }

      // 添加剧情方向（独立消息，高权重）
      if (params.plot && params.plot.trim()) {
        console.log('📋 添加剧情方向信息');
        messageBuilder.addUserMessage(`【剧情方向】\n${params.plot.trim()}`, undefined, true, true);
        messageBuilder.addAssistantMessage(`我已理解剧情方向要求，将在改写中体现这些方向性指导。`, true, true);
      }

      // 添加改写要求，标记为系统生成的预设消息，因为这是系统生成的提示
      messageBuilder.addUserMessage(this.promptBuilderService.getRewriteRequirements(params), undefined, true, true);
    }

    return messageBuilder.build();
  }
}

// 导出AIRewriteService实例
export const aiRewriteService = new AIRewriteService();
