"use client";

import React, { useEffect, useState } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { CategoryFieldsView } from './CategoryFields/CategoryFieldsView';
import { getCategoryMainInfo, getCategoryLabel } from './CategoryFieldsUtils';
import { WorldBuildingUpdaterDialog } from './WorldBuildingUpdaterDialog';
import { RelationshipManager } from './RelationshipManager';
import { TerminologyRelationManager } from './TerminologyRelationManager';
import { ChapterManager } from './ChapterManager';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  worldBuildingIds?: string[];
  terminologyIds?: string[];
}

interface WorldBuildingViewProps {
  worldBuilding: WorldBuilding;
  worldBuildings: WorldBuilding[];
  chapters?: GenericChapter[];
  onEdit: () => void;
  onDelete: (worldBuildingId: string) => void;
  onSave: (worldBuilding: WorldBuilding) => void;
  editButton: React.ReactNode;
  deleteButton: React.ReactNode;
}

/**
 * 世界观详情查看组件
 */
export const WorldBuildingView: React.FC<WorldBuildingViewProps> = ({
  worldBuilding,
  worldBuildings,
  chapters = [],
  onEdit,
  onDelete,
  onSave,
  editButton,
  deleteButton
}) => {
  // 主类信息
  const [mainCategoryInfo, setMainCategoryInfo] = useState<{
    mainCategoryId: string;
    mainCategoryLabel: string;
  } | null>(null);

  // 当类别变化时，更新主类信息
  useEffect(() => {
    if (worldBuilding.category) {
      const info = getCategoryMainInfo(worldBuilding.category);
      setMainCategoryInfo(info);
    } else {
      setMainCategoryInfo(null);
    }
  }, [worldBuilding.category]);

  // AI更新对话框状态
  const [isUpdaterDialogOpen, setIsUpdaterDialogOpen] = useState(false);

  // 人物关联管理对话框状态
  const [isRelationshipManagerOpen, setIsRelationshipManagerOpen] = useState(false);

  // 术语关联管理对话框状态
  const [isTerminologyRelationManagerOpen, setIsTerminologyRelationManagerOpen] = useState(false);

  // 章节管理对话框状态
  const [isChapterManagerOpen, setIsChapterManagerOpen] = useState(false);

  // 打开AI更新对话框
  const openUpdaterDialog = () => {
    setIsUpdaterDialogOpen(true);
  };

  // 关闭AI更新对话框
  const closeUpdaterDialog = () => {
    setIsUpdaterDialogOpen(false);
  };

  // 打开人物关联管理对话框
  const openRelationshipManager = () => {
    setIsRelationshipManagerOpen(true);
  };

  // 关闭人物关联管理对话框
  const closeRelationshipManager = () => {
    setIsRelationshipManagerOpen(false);
  };

  // 打开术语关联管理对话框
  const openTerminologyRelationManager = () => {
    setIsTerminologyRelationManagerOpen(true);
  };

  // 关闭术语关联管理对话框
  const closeTerminologyRelationManager = () => {
    setIsTerminologyRelationManagerOpen(false);
  };

  // 打开章节管理对话框
  const openChapterManager = () => {
    setIsChapterManagerOpen(true);
  };

  // 关闭章节管理对话框
  const closeChapterManager = () => {
    setIsChapterManagerOpen(false);
  };

  // 处理AI更新
  const handleAIUpdate = (updatedWorldBuilding: WorldBuilding) => {
    // 直接调用父组件的onSave函数，保存更新后的世界观
    if (onSave) {
      onSave(updatedWorldBuilding);
      console.log('AI更新世界观元素成功:', updatedWorldBuilding.name);
    } else {
      console.error('onSave 函数未定义，无法保存更新后的世界观');
    }
  };

  // 处理关联更新
  const handleRelationshipUpdate = (updatedWorldBuilding: WorldBuilding) => {
    // 调用父组件的onSave函数，保存更新后的世界观
    if (onSave) {
      onSave(updatedWorldBuilding);
      console.log('关联更新世界观元素成功:', updatedWorldBuilding.name);
    } else {
      console.error('onSave 函数未定义，无法保存更新后的世界观');
    }
  };
  return (
    <div className="space-y-6">
      {/* 基本信息 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          基本信息
        </h3>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500">名称</h4>
              <p className="mt-1 font-medium text-gray-800">{worldBuilding.name}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">类别</h4>
              <div className="mt-1 flex flex-wrap gap-2">
                {mainCategoryInfo && (
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    {mainCategoryInfo.mainCategoryLabel}
                  </span>
                )}
                <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  {worldBuilding.category ? getCategoryLabel(worldBuilding.category) : '未分类'}
                </span>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">重要性</h4>
              <p className="mt-1 text-yellow-500">
                {worldBuilding.attributes?.importance ? '⭐'.repeat(parseInt(worldBuilding.attributes.importance)) : '未设置'}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">时间跨度</h4>
              <p className="mt-1">
                {worldBuilding.attributes?.timeSpan ? (
                  <span className="inline-block px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
                    {worldBuilding.attributes.timeSpan}
                  </span>
                ) : '未设置'}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">影响范围</h4>
              <p className="mt-1">
                {worldBuilding.attributes?.scope ? (
                  <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    {worldBuilding.attributes.scope}
                  </span>
                ) : '未设置'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 详细描述 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
          </svg>
          详细描述
        </h3>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <p className="whitespace-pre-wrap text-gray-700 leading-relaxed">{worldBuilding.description || '暂无描述'}</p>
        </div>
      </div>

      {/* 类别特定字段 */}
      {worldBuilding.category && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            {worldBuilding.category ? getCategoryLabel(worldBuilding.category) : ''}特有属性
          </h3>
          <div className="bg-white rounded-lg p-4 border shadow-sm">
            <CategoryFieldsView worldBuilding={worldBuilding} />
          </div>
        </div>
      )}

      {/* 相关元素 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
            </svg>
            相关元素
          </div>
          <div className="flex space-x-2">
            <button
              className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors flex items-center"
              onClick={openRelationshipManager}
              title="管理人物关联"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              人物关联
            </button>
            <button
              className="px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors flex items-center"
              onClick={openTerminologyRelationManager}
              title="管理术语关联"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              术语关联
            </button>
          </div>
        </h3>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          {/* 相关人物 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              相关人物
            </h4>
            {worldBuilding.relatedCharacterIds && worldBuilding.relatedCharacterIds.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {worldBuilding.relatedCharacterIds.map(id => {
                  // 查找关联的人物
                  const character = worldBuildings.find(wb =>
                    wb.id === id || (wb.relatedCharacterIds && wb.relatedCharacterIds.includes(id))
                  );

                  return (
                    <span key={id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      {character ? character.name : id}
                    </span>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">暂无相关人物</p>
            )}
          </div>

          {/* 相关术语 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              相关术语
            </h4>
            {worldBuilding.relatedTerminologyIds && worldBuilding.relatedTerminologyIds.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {worldBuilding.relatedTerminologyIds.map(id => {
                  // 查找关联的术语
                  const terminology = worldBuildings.find(wb => wb.id === id);
                  return (
                    <span key={id} className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      {terminology ? terminology.name : id}
                    </span>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">暂无相关术语</p>
            )}
          </div>

          {/* 相关世界观元素 */}
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              相关世界观元素
            </h4>
            {worldBuilding.relatedWorldBuildingIds && worldBuilding.relatedWorldBuildingIds.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {worldBuilding.relatedWorldBuildingIds.map(id => {
                  const relatedWorldBuilding = worldBuildings.find(wb => wb.id === id);
                  return (
                    <span key={id} className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
                      {relatedWorldBuilding ? relatedWorldBuilding.name : id}
                    </span>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">暂无相关世界观元素</p>
            )}
          </div>
        </div>
      </div>

      {/* 提取自章节 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            提取自章节
          </div>
          <button
            className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors flex items-center"
            onClick={openChapterManager}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            管理章节
          </button>
        </h3>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          {worldBuilding.extractedFromChapterIds && worldBuilding.extractedFromChapterIds.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {worldBuilding.extractedFromChapterIds.map((id, index) => {
                // 查找章节
                const chapter = chapters.find(c => c.id === id);

                // 获取章节标题或序号
                let chapterTitle = "未知章节";
                if (chapter) {
                  // 如果找到章节，使用章节标题
                  chapterTitle = chapter.title || `第${(chapter.order !== undefined ? chapter.order : index) + 1}章`;
                } else {
                  // 如果没有找到章节，尝试从ID中提取信息
                  // 这里假设ID可能包含章节序号信息
                  const match = id.match(/chapter-(\d+)/i);
                  if (match) {
                    chapterTitle = `第${match[1]}章`;
                  } else {
                    // 如果无法从ID中提取信息，使用索引作为章节序号
                    chapterTitle = `第${index + 1}章`;
                  }
                }

                return (
                  <span key={id} className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                    {chapterTitle}
                  </span>
                );
              })}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">暂无提取章节</p>
          )}
        </div>
      </div>

      {/* 备注 */}
      {worldBuilding.notes && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            备注
          </h3>
          <div className="bg-white rounded-lg p-4 border shadow-sm">
            <p className="whitespace-pre-wrap text-gray-700">{worldBuilding.notes}</p>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-4">
        {worldBuilding.id && (
          <>
            {deleteButton}

            {/* AI更新按钮 */}
            <button
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors flex items-center"
              onClick={openUpdaterDialog}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              AI更新
            </button>

            {editButton}
          </>
        )}
      </div>

      {/* AI更新对话框 */}
      {worldBuilding.bookId && (
        <WorldBuildingUpdaterDialog
          worldBuilding={worldBuilding}
          isOpen={isUpdaterDialogOpen}
          onClose={closeUpdaterDialog}
          onUpdate={handleAIUpdate}
          bookId={worldBuilding.bookId}
        />
      )}

      {/* 人物关联管理对话框 */}
      {worldBuilding.bookId && (
        <RelationshipManager
          worldBuilding={worldBuilding}
          isOpen={isRelationshipManagerOpen}
          onClose={closeRelationshipManager}
          onUpdate={handleRelationshipUpdate}
          bookId={worldBuilding.bookId}
        />
      )}

      {/* 术语关联管理对话框 */}
      {worldBuilding.bookId && (
        <TerminologyRelationManager
          worldBuilding={worldBuilding}
          isOpen={isTerminologyRelationManagerOpen}
          onClose={closeTerminologyRelationManager}
          onUpdate={handleRelationshipUpdate}
          bookId={worldBuilding.bookId}
        />
      )}

      {/* 章节管理对话框 */}
      {worldBuilding.bookId && (
        <ChapterManager
          worldBuilding={worldBuilding}
          isOpen={isChapterManagerOpen}
          onClose={closeChapterManager}
          onUpdate={handleRelationshipUpdate}
          bookId={worldBuilding.bookId}
        />
      )}
    </div>
  );
};
