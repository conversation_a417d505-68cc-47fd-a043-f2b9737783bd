"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ExtendedACEFramework,
  ACEFrameworkCategory,
  FRAMEWORK_CATEGORIES
} from '../../types/ACEFrameworkTypes';
import { ACEFrameworkManager } from '../../services/ACEFrameworkManager';
import CategoryNavigation from './ACEFramework/CategoryNavigation';
import FrameworkGrid from './ACEFramework/FrameworkGrid';
import KeywordManagementModal from './ACEFramework/KeywordManagementModal';

interface ACEFrameworkButtonProps {
  selectedFrameworkIds?: string[];
  onFrameworkIdsChange?: (ids: string[]) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
}

/**
 * 新的ACE框架选择弹窗组件 - 支持分类选择
 */
interface ACEFrameworkModalProps {
  isOpen: boolean;
  frameworks: ExtendedACEFramework[];
  selectedFrameworkIds: string[];
  onClose: () => void;
  onSelectionChange: (ids: string[]) => void;
  onManageKeywords?: (framework: ExtendedACEFramework) => void;
  onFrameworksUpdated?: () => void;
  isLoading: boolean;
}

const ACEFrameworkModal: React.FC<ACEFrameworkModalProps> = ({
  isOpen,
  frameworks,
  selectedFrameworkIds,
  onClose,
  onSelectionChange,
  onManageKeywords,
  onFrameworksUpdated,
  isLoading
}) => {
  const [selectedCategory, setSelectedCategory] = useState<ACEFrameworkCategory>('outline-framework');
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelectedIds, setInternalSelectedIds] = useState<string[]>(selectedFrameworkIds);
  const [categoryStats, setCategoryStats] = useState<Record<ACEFrameworkCategory, number>>({
    'synopsis-keywords': 0,
    'synopsis-framework': 0,
    'outline-framework': 0,
    'extracted-elements': 0
  });

  // 同步外部选择状态
  useEffect(() => {
    setInternalSelectedIds(selectedFrameworkIds);
  }, [selectedFrameworkIds]);

  // 计算分类统计
  useEffect(() => {
    const stats = ACEFrameworkManager.getCategoryStats();
    setCategoryStats(stats);
  }, [frameworks]);

  // 获取当前分类的框架
  const currentCategoryFrameworks = frameworks.filter(framework =>
    framework.category === selectedCategory
  );

  // 过滤框架
  const filteredFrameworks = currentCategoryFrameworks.filter(framework =>
    framework.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    framework.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (framework.pattern && framework.pattern.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // 处理框架选择
  const handleFrameworkToggle = (frameworkId: string) => {
    const newSelectedIds = internalSelectedIds.includes(frameworkId)
      ? internalSelectedIds.filter(id => id !== frameworkId)
      : [...internalSelectedIds, frameworkId];

    setInternalSelectedIds(newSelectedIds);
  };

  // 确认选择
  const handleConfirm = () => {
    onSelectionChange(internalSelectedIds);
    onClose();
  };

  // 全选/全不选当前分类
  const handleSelectAll = () => {
    const currentCategoryIds = currentCategoryFrameworks.map(f => f.id);
    const otherSelectedIds = internalSelectedIds.filter(id =>
      !currentCategoryIds.includes(id)
    );
    setInternalSelectedIds([...otherSelectedIds, ...currentCategoryIds]);
  };

  const handleClearAll = () => {
    const currentCategoryIds = currentCategoryFrameworks.map(f => f.id);
    setInternalSelectedIds(internalSelectedIds.filter(id =>
      !currentCategoryIds.includes(id)
    ));
  };



  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <motion.div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />

      {/* 弹窗内容 */}
      <motion.div
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-5xl max-h-[85vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">选择ACE创作框架</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              选择要应用的专业创作框架，支持简介关键词、简介框架、大纲框架、拆解元素四种类型
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和操作栏 */}
        <div className="p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="搜索当前分类的框架..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
              <svg className="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <button
              onClick={handleSelectAll}
              className="px-3 py-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              全选当前
            </button>
            <button
              onClick={handleClearAll}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              清空当前
            </button>
          </div>

          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            已选择 {internalSelectedIds.length} 个框架 | 当前分类: {currentCategoryFrameworks.length} 个
          </div>
        </div>

        {/* 主要内容区域 - 左右分栏 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧分类导航 */}
          <CategoryNavigation
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            categoryStats={categoryStats}
          />

          {/* 右侧框架展示 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedCategory}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex-1 flex flex-col overflow-hidden"
              >
                <FrameworkGrid
                  frameworks={filteredFrameworks}
                  selectedFrameworkIds={internalSelectedIds}
                  onFrameworkToggle={handleFrameworkToggle}
                  onManageKeywords={onManageKeywords}
                  onFrameworksUpdated={onFrameworksUpdated}
                  isLoading={isLoading}
                />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            已选择 {internalSelectedIds.length} 个框架
            {internalSelectedIds.length > 0 && (
              <span className="ml-2 text-blue-600 dark:text-blue-400">
                ({FRAMEWORK_CATEGORIES.map(cat => {
                  const count = internalSelectedIds.filter(id =>
                    frameworks.find(f => f.id === id)?.category === cat.id
                  ).length;
                  return count > 0 ? `${cat.name}${count}个` : null;
                }).filter(Boolean).join(', ')})
              </span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              确认选择
            </button>
          </div>
        </div>
      </motion.div>
    </div>,
    document.body
  );
};

// 关键词管理弹窗独立渲染
const KeywordManagementWrapper: React.FC<{
  showKeywordManagement: boolean;
  managingFramework: ExtendedACEFramework | null;
  onClose: () => void;
  onSave: (keywords: any[]) => void;
}> = ({ showKeywordManagement, managingFramework, onClose, onSave }) => {
  if (!showKeywordManagement) return null;

  return (
    <KeywordManagementModal
      isOpen={showKeywordManagement}
      framework={managingFramework}
      onClose={onClose}
      onSave={onSave}
    />
  );
};

/**
 * ACE框架选择按钮组件
 * 支持简介关键词、简介框架、大纲框架的分类选择
 */
export const ACEFrameworkButton: React.FC<ACEFrameworkButtonProps> = ({
  selectedFrameworkIds = [],
  onFrameworkIdsChange,
  variant = 'default',
  className = '',
  disabled = false
}) => {
  const [frameworks, setFrameworks] = useState<ExtendedACEFramework[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showKeywordManagement, setShowKeywordManagement] = useState(false);
  const [managingFramework, setManagingFramework] = useState<ExtendedACEFramework | null>(null);

  // 加载ACE框架数据
  const loadFrameworks = useCallback(async () => {
    try {
      setIsLoading(true);
      // 使用新的ACEFrameworkManager获取所有框架
      const allFrameworks = ACEFrameworkManager.getAllFrameworks();
      setFrameworks(allFrameworks);
    } catch (error) {
      console.error('加载ACE框架失败:', error);
      setFrameworks([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 组件挂载时加载框架
  useEffect(() => {
    loadFrameworks();
  }, [loadFrameworks]);

  // 处理框架选择变化
  const handleFrameworkIdsChange = (ids: string[]) => {
    if (onFrameworkIdsChange) {
      onFrameworkIdsChange(ids);
    }
  };

  // 处理关键词管理
  const handleManageKeywords = useCallback((framework: ExtendedACEFramework) => {
    setManagingFramework(framework);
    setShowKeywordManagement(true);
  }, []);

  // 保存关键词管理结果
  const handleSaveKeywords = useCallback(async (keywords: any[]) => {
    if (!managingFramework) return;

    try {
      // 根据框架类型保存到对应的localStorage
      if (managingFramework.id === 'keywords-user-saved-title') {
        localStorage.setItem('book-title-keywords', JSON.stringify(keywords));
      } else if (managingFramework.id === 'keywords-user-saved-synopsis') {
        localStorage.setItem('synopsis-keywords', JSON.stringify(keywords));
      }

      // 重新加载框架数据
      await loadFrameworks();

      console.log('关键词已保存:', keywords.length, '个');
    } catch (error) {
      console.error('保存关键词失败:', error);
    }
  }, [managingFramework, loadFrameworks]);

  // 计算选中框架数量
  const selectedCount = selectedFrameworkIds.length;

  // 获取按钮样式
  const getButtonStyles = () => {
    const baseStyles = "inline-flex items-center justify-center border border-gray-300 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-gray-200";

    switch (variant) {
      case 'compact':
        return `${baseStyles} px-3 py-2 text-sm rounded-lg`;
      case 'icon-only':
        return `${baseStyles} p-2 rounded-lg`;
      default:
        return `${baseStyles} px-4 py-2 text-sm rounded-lg`;
    }
  };

  return (
    <>
      <button
        className={`${getButtonStyles()} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={() => !disabled && setIsModalOpen(true)}
        disabled={disabled}
        title="选择ACE创作框架"
      >
        {/* ACE框架图标 */}
        <svg
          className={`${variant === 'icon-only' ? 'w-5 h-5' : 'w-4 h-4'} ${variant !== 'icon-only' ? 'mr-2' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12"
          />
        </svg>

        {/* 文本（非图标模式） */}
        {variant !== 'icon-only' && (
          <span>
            {variant === 'compact' 
              ? (selectedCount > 0 ? `ACE(${selectedCount})` : 'ACE框架')
              : (selectedCount > 0 ? `ACE框架(${selectedCount})` : 'ACE框架管理')
            }
          </span>
        )}

        {/* 选中数量指示器（图标模式） */}
        {variant === 'icon-only' && selectedCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {selectedCount}
          </span>
        )}
      </button>

      {/* ACE框架选择弹窗 */}
      <ACEFrameworkModal
        isOpen={isModalOpen}
        frameworks={frameworks}
        selectedFrameworkIds={selectedFrameworkIds}
        onClose={() => setIsModalOpen(false)}
        onSelectionChange={handleFrameworkIdsChange}
        onManageKeywords={handleManageKeywords}
        onFrameworksUpdated={loadFrameworks}
        isLoading={isLoading}
      />

      {/* 关键词管理弹窗 - 独立渲染 */}
      <KeywordManagementWrapper
        showKeywordManagement={showKeywordManagement}
        managingFramework={managingFramework}
        onClose={() => {
          setShowKeywordManagement(false);
          setManagingFramework(null);
        }}
        onSave={handleSaveKeywords}
      />
    </>
  );
};
