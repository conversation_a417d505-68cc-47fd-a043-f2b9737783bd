"use client";

import React, { useState, useEffect } from 'react';
import { ITerminologyPanelComponent } from '../../interfaces/ITerminologyPanelComponent';
import { Terminology, Character, WorldBuilding } from '@/lib/db/dexie';
import { TerminologyList } from './TerminologyList';
import { TerminologyDetail } from './TerminologyDetail';
import { TerminologyPanelHeader } from './TerminologyPanelHeader';
import { TerminologyPanelFooter } from './TerminologyPanelFooter';
import { DeleteConfirmDialog } from './DeleteConfirmDialog';
import { TerminologyExtractDialog } from './TerminologyExtractDialog';
import { TerminologyCreateDialog } from './TerminologyCreateDialog';
import { TerminologyBatchUpdateDialog } from './TerminologyBatchUpdateDialog';
import { TerminologyFieldUpdateDialog } from './TerminologyFieldUpdateDialog';
import { TerminologyContentEnhanceDialog } from './TerminologyContentEnhanceDialog';
import { useTerminologyData } from './useTerminologyData';
import { createNotificationFactory } from '@/factories/notification/NotificationFactory';
import Panel from '../common/Panel';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * 术语面板组件
 * 用于管理小说中的术语
 */
export class TerminologyPanelComponent implements ITerminologyPanelComponent {
  private bookId: string = '';
  private isOpen: boolean = false;
  private closeHandler: (() => void) | null = null;
  private createHandler: ((terminology: Terminology) => void) | null = null;
  private updateHandler: ((terminology: Terminology) => void) | null = null;
  private deleteHandler: ((terminologyId: string) => void) | null = null;
  private className: string = '';

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this.bookId = bookId;
  }

  /**
   * 设置面板是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 设置关闭回调
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void {
    this.closeHandler = handler;
  }

  /**
   * 设置创建回调
   * @param handler 创建回调函数
   */
  onCreate(handler: (terminology: Terminology) => void): void {
    this.createHandler = handler;
  }

  /**
   * 设置更新回调
   * @param handler 更新回调函数
   */
  onUpdate(handler: (terminology: Terminology) => void): void {
    this.updateHandler = handler;
  }

  /**
   * 设置删除回调
   * @param handler 删除回调函数
   */
  onDelete(handler: (terminologyId: string) => void): void {
    this.deleteHandler = handler;
  }

  /**
   * 设置类名
   * @param className 类名
   */
  setClassName(className: string): void {
    this.className = className;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    return (
      <TerminologyPanelComponentView
        bookId={this.bookId}
        isOpen={this.isOpen}
        onClose={this.closeHandler || (() => {})}
        onTerminologyCreate={this.createHandler || (() => {})}
        onTerminologyUpdate={this.updateHandler || (() => {})}
        onTerminologyDelete={this.deleteHandler || (() => {})}
        className={this.className}
      />
    );
  }
}

interface TerminologyPanelComponentViewProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  onTerminologyCreate: (terminology: Terminology) => void;
  onTerminologyUpdate: (terminology: Terminology) => void;
  onTerminologyDelete: (terminologyId: string) => void;
  className: string;
}

/**
 * 术语面板组件视图
 */
const TerminologyPanelComponentView: React.FC<TerminologyPanelComponentViewProps> = ({
  bookId,
  isOpen,
  onClose,
  onTerminologyCreate,
  onTerminologyUpdate,
  onTerminologyDelete,
  className
}) => {
  // 不再需要UI工厂实例

  // 删除确认对话框状态
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [terminologyToDelete, setTerminologyToDelete] = useState<Terminology | null>(null);

  // AI提取对话框状态
  const [isExtractDialogOpen, setIsExtractDialogOpen] = useState(false);

  // AI创建对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // 批量更新对话框状态
  const [isBatchUpdateDialogOpen, setIsBatchUpdateDialogOpen] = useState(false);

  // 字段更新对话框状态
  const [isFieldUpdateDialogOpen, setIsFieldUpdateDialogOpen] = useState(false);
  const [fieldUpdateInfo, setFieldUpdateInfo] = useState<{field: string, fieldLabel: string, currentValue: string}>({
    field: '',
    fieldLabel: '',
    currentValue: ''
  });

  // 内容增强对话框状态
  const [isContentEnhanceDialogOpen, setIsContentEnhanceDialogOpen] = useState(false);

  // 多选模式状态
  const [multiSelectMode, setMultiSelectMode] = useState(false);

  // 批量选择的术语ID
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);

  // 章节列表状态
  const [chapters, setChapters] = useState<GenericChapter[]>([]);

  // 人物列表状态
  const [characters, setCharacters] = useState<Character[]>([]);

  // 世界观列表状态
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);

  // 使用术语数据钩子
  const {
    terminologies,
    filteredTerminologies,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedTerminology,
    isEditing,
    handleSelectTerminology,
    handleCreateTerminology,
    handleEditTerminology,
    handleSaveTerminology,
    handleCancelEdit,
    handleDeleteTerminology
  } = useTerminologyData(bookId, isOpen);

  // 加载章节数据、人物数据和世界观数据 - 用于 TerminologyDetail 组件和AI提取
  useEffect(() => {
    if (isOpen) {
      loadChaptersData();
      loadCharactersData();
      loadWorldBuildingsData();
    }
  }, [isOpen, bookId]);

  // 加载章节数据的函数
  const loadChaptersData = async () => {
    console.log('开始加载章节数据, bookId =', bookId);
    console.log('当前时间戳:', new Date().toISOString());

    try {
      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 CharacterAIAdapter 的 getChapterList 方法
      try {
        const CharacterAIAdapter = (await import('@/adapters/ai/CharacterAIAdapter')).default;
        const characterAIAdapter = new CharacterAIAdapter();
        const chapterList = await characterAIAdapter.getChapterList(bookId);

        console.log('通过 CharacterAIAdapter.getChapterList 获取到章节数据:', chapterList);

        if (chapterList && chapterList.length > 0) {
          setChapters(chapterList);
          return;
        }
      } catch (error) {
        console.error('通过 CharacterAIAdapter.getChapterList 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果所有方法都失败，设置为空数组
      console.warn('所有获取章节数据的方法都失败，设置为空数组');
      setChapters([]);
    } catch (error) {
      console.error('获取章节数据失败:', error);
      setChapters([]);
    }
  };

  // 加载人物数据的函数
  const loadCharactersData = async () => {
    console.log('开始加载人物数据, bookId =', bookId);

    try {
      // 尝试使用 src/lib/db/repositories/characterRepository.ts
      try {
        const { characterRepository } = await import('@/lib/db/repositories');
        const charactersData = await characterRepository.getAllByBookId(bookId);

        console.log('通过 characterRepository 获取到人物数据:', charactersData);

        if (charactersData && charactersData.length > 0) {
          setCharacters(charactersData);
          return;
        }
      } catch (error) {
        console.error('通过 characterRepository 获取人物数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const charactersData = await novelDb.characters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到人物数据:', charactersData);

        if (charactersData && charactersData.length > 0) {
          setCharacters(charactersData);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取人物数据失败:', error);
      }

      // 如果所有方法都失败，设置为空数组
      console.warn('所有获取人物数据的方法都失败，设置为空数组');
      setCharacters([]);
    } catch (error) {
      console.error('获取人物数据失败:', error);
      setCharacters([]);
    }
  };

  // 加载世界观数据的函数
  const loadWorldBuildingsData = async () => {
    console.log('开始加载世界观数据, bookId =', bookId);

    try {
      // 尝试使用 src/lib/db/repositories/worldBuildingRepository.ts
      try {
        const { worldBuildingRepository } = await import('@/lib/db/repositories');
        const worldBuildingsData = await worldBuildingRepository.getAllByBookId(bookId);

        console.log('通过 worldBuildingRepository 获取到世界观数据:', worldBuildingsData);

        if (worldBuildingsData && worldBuildingsData.length > 0) {
          setWorldBuildings(worldBuildingsData);
          return;
        }
      } catch (error) {
        console.error('通过 worldBuildingRepository 获取世界观数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const worldBuildingsData = await novelDb.worldBuilding.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到世界观数据:', worldBuildingsData);

        if (worldBuildingsData && worldBuildingsData.length > 0) {
          setWorldBuildings(worldBuildingsData);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取世界观数据失败:', error);
      }

      // 如果所有方法都失败，设置为空数组
      console.warn('所有获取世界观数据的方法都失败，设置为空数组');
      setWorldBuildings([]);
    } catch (error) {
      console.error('获取世界观数据失败:', error);
      setWorldBuildings([]);
    }
  };

  // 处理删除确认
  const handleDeleteWithConfirm = (terminology: Terminology) => {
    setTerminologyToDelete(terminology);
    setIsDeleteDialogOpen(true);
  };

  // 确认删除
  const confirmDelete = () => {
    if (terminologyToDelete && terminologyToDelete.id) {
      handleDeleteTerminology(terminologyToDelete.id);
      onTerminologyDelete(terminologyToDelete.id);
    }
    setIsDeleteDialogOpen(false);
    setTerminologyToDelete(null);
  };

  // 取消删除
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setTerminologyToDelete(null);
  };

  // 处理AI提取术语
  const handleExtractTerminologies = async (source: string, options: any) => {
    // 创建通知组件
    const notificationFactory = createNotificationFactory();
    const notification = notificationFactory.createNotificationComponent();

    try {
      // 如果是从AI提取对话框传来的完整术语对象，直接创建
      if (source === 'ai' && options.terminology) {
        const newTerminology: Partial<Terminology> = {
          ...options.terminology,
          bookId,
          extractedFromChapterIds: options.selectedChapterIds || [],
          relatedCharacterIds: [],
          relatedTerminologyIds: options.terminology.relatedTerminologyIds || [],
          relatedWorldBuildingIds: [],
          attributes: {
            ...(options.terminology.attributes || {}),
            importance: options.terminology.attributes?.importance ?
              String(options.terminology.attributes.importance) : '3'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        handleSaveTerminology(newTerminology as Terminology);
        notification.showSuccess(`成功创建术语: ${newTerminology.name}`);
        return;
      }

      // 如果不是从AI提取对话框传来的，这里应该不会执行到
      // 但为了安全起见，我们仍然保留一些代码
      notification.showInfo('正在处理术语提取请求...');

      return;
    } catch (error) {
      console.error('提取术语失败', error);
      notification.showError('提取术语失败: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  };

  // 处理批量更新术语
  const handleBatchUpdateTerminologies = () => {
    if (selectedTerminologyIds.length === 0) {
      alert('请先选择要更新的术语');
      return;
    }

    // 打开批量更新对话框
    setIsBatchUpdateDialogOpen(true);
  };

  // 处理AI创建术语
  const handleAICreateTerminologies = () => {
    setIsCreateDialogOpen(true);
  };

  // 处理字段更新
  const handleFieldUpdate = (field: string, fieldLabel: string, currentValue: string) => {
    if (!selectedTerminology) return;

    setFieldUpdateInfo({
      field,
      fieldLabel,
      currentValue
    });
    setIsFieldUpdateDialogOpen(true);
  };

  // 处理内容增强
  const handleContentEnhance = () => {
    if (!selectedTerminology) return;

    setIsContentEnhanceDialogOpen(true);
  };

  // 切换多选模式
  const toggleMultiSelectMode = () => {
    setMultiSelectMode(!multiSelectMode);
    if (multiSelectMode) {
      // 退出多选模式时清空选择
      setSelectedTerminologyIds([]);
    }
  };

  // 处理保存并回调
  const handleSaveWithCallback = async (terminology: Terminology) => {
    try {
      // 检查是否是创建还是更新
      const isCreate = !terminology.id;

      // 保存术语 - 这里会在内部处理创建或更新
      await handleSaveTerminology(terminology);

      // 只有在更新现有术语时才调用回调
      // 创建新术语时，handleSaveTerminology已经处理了创建逻辑
      if (!isCreate && terminology.id) {
        onTerminologyUpdate(terminology);
      }

      // 注意：我们不再调用onTerminologyCreate，因为handleSaveTerminology已经处理了创建逻辑
    } catch (error) {
      console.error('保存术语失败:', error);
    }
  };

  // 渲染面板内容
  const renderPanelContent = () => {
    return (
      <div className="flex h-full" style={{ backgroundColor: 'var(--color-white)' }}>
        {/* 左侧列表 */}
        <div style={{
          width: '32%',
          borderRight: '1px solid rgba(0, 0, 0, 0.05)',
          boxShadow: 'inset -5px 0 15px -5px rgba(0, 0, 0, 0.02)'
        }}>
          <TerminologyList
            terminologies={filteredTerminologies}
            isLoading={isLoading}
            searchQuery={searchQuery}
            selectedTerminology={selectedTerminology}
            onSelectTerminology={handleSelectTerminology}
            onCreateTerminology={handleCreateTerminology}
            onDeleteTerminology={handleDeleteWithConfirm}
          />
        </div>

        {/* 右侧详情/编辑区域 */}
        <div className="p-5" style={{
          width: '68%',
          backgroundColor: 'rgba(245, 247, 250, 0.5)',
          borderRadius: '0 0 12px 0',
          boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.02)'
        }}>
          <TerminologyDetail
            terminology={selectedTerminology}
            bookId={bookId}
            terminologies={terminologies}
            isEditing={isEditing}
            onEdit={handleEditTerminology}
            onDelete={handleDeleteWithConfirm}
            onSave={handleSaveWithCallback}
            onCancel={handleCancelEdit}
            chapters={chapters} // 传递章节列表
            characters={characters} // 传递人物列表
            worldBuildings={worldBuildings} // 传递世界观列表
            onFieldUpdate={handleFieldUpdate} // 传递字段更新回调
            onContentEnhance={handleContentEnhance} // 传递内容增强回调
          />
        </div>
      </div>
    );
  };

  // 准备面板头部
  const panelHeader = (
    <TerminologyPanelHeader
      searchQuery={searchQuery}
      sortBy={sortBy}
      onSearchChange={setSearchQuery}
      onSortChange={setSortBy}
      onCreateTerminology={handleCreateTerminology}
      onExtractTerminologies={() => setIsExtractDialogOpen(true)}
      onBatchUpdateTerminologies={handleBatchUpdateTerminologies}
      onAICreateTerminologies={handleAICreateTerminologies}
      multiSelectMode={multiSelectMode}
      onToggleMultiSelectMode={toggleMultiSelectMode}
    />
  );

  // 准备面板内容
  const panelContent = renderPanelContent();

  // 准备面板底部
  const panelFooter = (
    <TerminologyPanelFooter
      terminologyCount={terminologies.length}
    />
  );

  return (
    <>
      <Panel
        title="术语管理"
        isOpen={isOpen}
        size="large"
        fixedHeight={true}
        backgroundColor="var(--color-primary-bg)"
        width="70%"
        height="85%"
        enhanced={true}
        literaryTheme={true}
        header={panelHeader}
        content={panelContent}
        footer={panelFooter}
        onClose={onClose}
        className={className}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        terminology={terminologyToDelete}
      />

      {/* AI提取术语对话框 */}
      <TerminologyExtractDialog
        isOpen={isExtractDialogOpen}
        onClose={() => setIsExtractDialogOpen(false)}
        onExtract={handleExtractTerminologies}
        bookId={bookId}
        chapters={chapters}
      />

      {/* AI创建术语对话框 */}
      <TerminologyCreateDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onCreate={async (terminology) => {
          try {
            // 创建术语 - 只需要调用一次保存函数
            // 这里选择调用handleSaveTerminology，它会处理内部状态更新
            await handleSaveTerminology(terminology as Terminology);

            // 不再调用onTerminologyCreate，避免重复创建
            // 关闭对话框
            setIsCreateDialogOpen(false);
          } catch (error) {
            console.error('创建术语失败:', error);
          }
        }}
        bookId={bookId}
        chapters={chapters}
      />

      {/* 批量更新对话框 */}
      <TerminologyBatchUpdateDialog
        isOpen={isBatchUpdateDialogOpen}
        onClose={() => setIsBatchUpdateDialogOpen(false)}
        onUpdate={async (updates) => {
          try {
            // 批量更新术语
            for (const terminologyId of updates.terminologyIds) {
              const terminology = terminologies.find(t => t.id === terminologyId);
              if (!terminology) continue;

              const updatedTerminology: Partial<Terminology> = {
                id: terminologyId
              };

              // 根据字段名设置更新值
              if (updates.field.includes('.')) {
                const [parent, child] = updates.field.split('.');
                if (parent === 'attributes') {
                  updatedTerminology.attributes = {
                    ...(terminology.attributes || {}),
                    [child]: updates.value
                  };
                }
              } else {
                (updatedTerminology as any)[updates.field] = updates.value;
              }

              // 更新术语
              await handleSaveTerminology(updatedTerminology as Terminology);
            }

            // 关闭对话框
            setIsBatchUpdateDialogOpen(false);
          } catch (error) {
            console.error('批量更新术语失败:', error);
          }
        }}
        selectedTerminologyIds={selectedTerminologyIds}
        terminologies={terminologies}
      />

      {/* 字段更新对话框 */}
      <TerminologyFieldUpdateDialog
        isOpen={isFieldUpdateDialogOpen}
        onClose={() => setIsFieldUpdateDialogOpen(false)}
        onUpdate={async (terminologyUpdates) => {
          try {
            // 确保术语名称不为空
            if (!terminologyUpdates.name && (!selectedTerminology || !selectedTerminology.name)) {
              console.error('更新术语字段失败: 术语名称不能为空');
              alert('更新术语字段失败: 术语名称不能为空');
              return;
            }

            // 合并更新，确保包含所有必要字段
            const completeTerminology: Terminology = {
              ...selectedTerminology!,
              ...terminologyUpdates,
              // 确保这些字段存在
              name: terminologyUpdates.name || selectedTerminology!.name,
              bookId: terminologyUpdates.bookId || selectedTerminology!.bookId || bookId,
              updatedAt: new Date()
            };

            console.log('完整的术语更新数据:', completeTerminology);

            // 更新术语
            await handleSaveTerminology(completeTerminology);

            // 关闭对话框
            setIsFieldUpdateDialogOpen(false);
          } catch (error) {
            console.error('更新术语字段失败:', error);
          }
        }}
        terminology={selectedTerminology!}
        field={fieldUpdateInfo.field}
        fieldLabel={fieldUpdateInfo.fieldLabel}
        chapters={chapters}
        terminologies={terminologies}
        bookId={bookId}
      />

      {/* 内容增强对话框 */}
      <TerminologyContentEnhanceDialog
        isOpen={isContentEnhanceDialogOpen}
        onClose={() => setIsContentEnhanceDialogOpen(false)}
        onUpdate={async (terminologyUpdates) => {
          try {
            // 确保术语名称不为空
            if (!terminologyUpdates.name && (!selectedTerminology || !selectedTerminology.name)) {
              console.error('更新术语内容失败: 术语名称不能为空');
              alert('更新术语内容失败: 术语名称不能为空');
              return;
            }

            // 合并更新，确保包含所有必要字段
            const completeTerminology: Terminology = {
              ...selectedTerminology!,
              ...terminologyUpdates,
              // 确保这些字段存在
              name: terminologyUpdates.name || selectedTerminology!.name,
              bookId: terminologyUpdates.bookId || selectedTerminology!.bookId || bookId,
              updatedAt: new Date()
            };

            console.log('完整的术语更新数据:', completeTerminology);

            // 更新术语
            await handleSaveTerminology(completeTerminology);

            // 关闭对话框
            setIsContentEnhanceDialogOpen(false);
          } catch (error) {
            console.error('增强术语内容失败:', error);
          }
        }}
        terminology={selectedTerminology!}
        chapters={chapters}
        terminologies={terminologies}
        bookId={bookId}
      />
    </>
  );
};
