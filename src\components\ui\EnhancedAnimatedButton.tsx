"use client";

import React, { useState, useEffect } from 'react';

interface EnhancedAnimatedButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  animationType?: 'morph' | 'rotate' | 'pulse' | 'wave';
}

/**
 * 增强的动画按钮组件
 * 支持多种SVG图标的复合动画和逐渐变化效果
 */
export const EnhancedAnimatedButton: React.FC<EnhancedAnimatedButtonProps> = ({
  onClick,
  children,
  className = "",
  disabled = false,
  variant = 'primary',
  size = 'md',
  animationType = 'morph'
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);

  // 动画阶段循环
  useEffect(() => {
    if (isHovered) {
      const interval = setInterval(() => {
        setAnimationPhase(prev => (prev + 1) % 4);
      }, 800);
      return () => clearInterval(interval);
    } else {
      setAnimationPhase(0);
    }
  }, [isHovered]);

  // 获取当前动画阶段的SVG图标
  const getCurrentIcon = () => {
    const icons = [
      // 阶段1: 简单加号
      <svg viewBox="0 0 24 24" className="w-5 h-5 transition-all duration-300">
        <path
          d="M12 5V19M5 12H19"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          className="transition-all duration-500"
        />
      </svg>,

      // 阶段2: 圆形加号
      <svg viewBox="0 0 24 24" className="w-5 h-5 transition-all duration-300">
        <circle
          cx="12"
          cy="12"
          r="9"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="animate-pulse"
        />
        <path
          d="M12 8V16M8 12H16"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>,

      // 阶段3: 方形加号
      <svg viewBox="0 0 24 24" className="w-5 h-5 transition-all duration-300">
        <rect
          x="4"
          y="4"
          width="16"
          height="16"
          rx="2"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="animate-bounce"
        />
        <path
          d="M12 8V16M8 12H16"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>,

      // 阶段4: 星形加号
      <svg viewBox="0 0 24 24" className="w-5 h-5 transition-all duration-300">
        <path
          d="M12 2L15 9L22 9L17 14L19 22L12 18L5 22L7 14L2 9L9 9L12 2Z"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="animate-spin"
          style={{ animationDuration: '2s' }}
        />
        <path
          d="M12 8V16M8 12H16"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </svg>
    ];

    return icons[animationPhase];
  };

  // 获取按钮样式
  const getButtonStyles = () => {
    const baseStyles = "btn rounded-lg transition-all duration-300 flex items-center justify-center relative overflow-hidden";

    const variants = {
      primary: "bg-blue-500 hover:bg-blue-600 text-white border-blue-500",
      secondary: "bg-gray-500 hover:bg-gray-600 text-white border-gray-500",
      outline: "bg-transparent border-2 hover:bg-opacity-10"
    };

    const sizes = {
      sm: "px-3 py-2 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-6 py-3 text-lg"
    };

    const animationStyles = isHovered ? "transform scale-105 shadow-lg" : "transform scale-100 shadow-md";
    const pressedStyles = isPressed ? "transform scale-95" : "";

    return `${baseStyles} ${variants[variant]} ${sizes[size]} ${animationStyles} ${pressedStyles} ${className}`;
  };

  // 获取动画特效
  const getAnimationEffects = () => {
    if (!isHovered) return null;

    switch (animationType) {
      case 'wave':
        return (
          <div className="absolute inset-0 overflow-hidden rounded-lg">
            <div
              className="absolute inset-0 bg-white opacity-20 transform -translate-x-full animate-pulse"
              style={{
                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                animation: 'wave 1.5s infinite'
              }}
            />
          </div>
        );

      case 'pulse':
        return (
          <div className="absolute inset-0 rounded-lg animate-ping bg-current opacity-20" />
        );

      case 'rotate':
        return (
          <div className="absolute inset-0 rounded-lg border-2 border-current opacity-30 animate-spin"
               style={{ animationDuration: '2s' }} />
        );

      default:
        return null;
    }
  };

  return (
    <button
      className={getButtonStyles()}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      style={{
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        backgroundImage: isHovered ? 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2))' : 'none'
      }}
    >
      {/* 动画特效层 */}
      {getAnimationEffects()}

      {/* 图标容器 */}
      <span className={`mr-2 transform transition-all duration-300 ${isHovered ? 'rotate-180' : 'rotate-0'}`}>
        {getCurrentIcon()}
      </span>

      {/* 文字内容 */}
      <span className="relative z-10 transition-all duration-300">
        {children}
      </span>

      {/* 粒子效果 */}
      {isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-60 animate-bounce"
              style={{
                left: `${20 + i * 10}%`,
                top: `${30 + (i % 2) * 40}%`,
                animationDelay: `${i * 0.1}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      )}

      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes wave {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        @keyframes sparkle {
          0%, 100% {
            opacity: 0;
            transform: scale(0);
          }
          50% {
            opacity: 1;
            transform: scale(1);
          }
        }

        .btn:hover .sparkle {
          animation: sparkle 1s infinite;
        }
      `}</style>
    </button>
  );
};

/**
 * 小型圆形添加按钮组件
 * 融合SVGRepo风格的精美设计
 */
export const CreateChapterButton: React.FC<{ onClick?: () => void; disabled?: boolean }> = ({
  onClick,
  disabled = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);

  // 动画阶段循环
  useEffect(() => {
    if (isHovered) {
      const interval = setInterval(() => {
        setAnimationPhase(prev => (prev + 1) % 3);
      }, 600);
      return () => clearInterval(interval);
    } else {
      setAnimationPhase(0);
    }
  }, [isHovered]);

  // 获取当前阶段的SVG图标 - 基于SVGRepo优化设计
  const getCurrentIcon = () => {
    const icons = [
      // 阶段1: 文档加号 (基于Document Add风格)
      <svg viewBox="0 0 24 24" className="w-5 h-5 document-icon" fill="none">
        <defs>
          <linearGradient id="docGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="1" />
          </linearGradient>
        </defs>
        {/* 文档外框 */}
        <path
          d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          stroke="url(#docGradient1)"
          strokeWidth="1.5"
          fill="none"
          className="transition-all duration-300"
        />
        <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="1.5" className="document-corner" />
        {/* 加号 */}
        <path
          d="M12 11V17M9 14H15"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          className="plus-icon"
        />
      </svg>,

      // 阶段2: 多层文档 (基于Plus Add Document Create Page风格)
      <svg viewBox="0 0 24 24" className="w-5 h-5 document-icon" fill="none">
        <defs>
          <linearGradient id="docGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.6" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="0.9" />
          </linearGradient>
        </defs>
        {/* 后层文档 */}
        <path
          d="M16 4h-4c-1.1 0-2 0.9-2 2v10c0 1.1 0.9 2 2 2h6c1.1 0 2-0.9 2-2V8l-4-4z"
          stroke="url(#docGradient2)"
          strokeWidth="1.5"
          fill="rgba(59, 130, 246, 0.1)"
          className="multi-document-layer animate-pulse"
          style={{ animationDuration: '1.5s' }}
        />
        {/* 前层文档 */}
        <path
          d="M12 8h-4c-1.1 0-2 0.9-2 2v6c0 1.1 0.9 2 2 2h6c1.1 0 2-0.9 2-2v-4"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="rgba(255, 255, 255, 0.8)"
          className="multi-document-layer"
        />
        {/* 加号 */}
        <path
          d="M10 13h4M12 11v4"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          className="plus-icon"
        />
      </svg>,

      // 阶段3: 星光文档 (创新设计)
      <svg viewBox="0 0 24 24" className="w-5 h-5 document-icon" fill="none">
        <defs>
          <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fbbf24" stopOpacity="0.8" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="1" />
          </linearGradient>
        </defs>
        {/* 文档基础 */}
        <path
          d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="rgba(251, 191, 36, 0.1)"
        />
        <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="1.5" className="document-corner" />
        {/* 星形加号 */}
        <path
          d="M12 9l1 3h3l-2.5 2 1 3-2.5-2-2.5 2 1-3L8 12h3l1-3z"
          stroke="url(#starGradient)"
          strokeWidth="1.5"
          fill="rgba(251, 191, 36, 0.3)"
          className="star-effect"
        />
      </svg>
    ];

    return icons[animationPhase];
  };

  return (
    <button
      className="w-9 h-9 create-button-enhanced flex items-center justify-center relative group"
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
    >
      {/* 闪烁粒子效果 */}
      {isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-70 particle"
              style={{
                left: `${20 + i * 15}%`,
                top: `${25 + (i % 2) * 30}%`,
                animationDelay: `${i * 0.3}s`,
                animationDuration: '1.5s'
              }}
            />
          ))}
        </div>
      )}

      {/* 动态SVG图标 */}
      <div className="icon-container">
        {getCurrentIcon()}
      </div>

      {/* 涟漪效果 */}
      {isPressed && (
        <div className="ripple-effect absolute inset-0" />
      )}
    </button>
  );
};

export const AddContentButton: React.FC<{ onClick?: () => void; disabled?: boolean }> = ({
  onClick,
  disabled = false
}) => (
  <EnhancedAnimatedButton
    onClick={onClick}
    disabled={disabled}
    variant="primary"
    size="sm"
    animationType="wave"
    className="bg-green-500 hover:bg-green-600 border-green-500"
  >
    添加内容
  </EnhancedAnimatedButton>
);

/**
 * 小型删除按钮组件
 * 融合SVGRepo风格的精美设计
 */
export const DeleteChapterButton: React.FC<{
  onClick?: (e: React.MouseEvent) => void;
  disabled?: boolean;
  title?: string;
}> = ({
  onClick,
  disabled = false,
  title = "删除章节"
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  return (
    <button
      className="w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative overflow-hidden"
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        color: isHovered ? '#EF4444' : '#9CA3AF',
        border: '2px solid transparent',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        transform: isPressed ? 'scale(0.92)' : isHovered ? 'scale(1.05)' : 'scale(1)',
        boxShadow: isHovered
          ? '0 4px 12px rgba(239, 68, 68, 0.2), 0 0 0 2px rgba(239, 68, 68, 0.15)'
          : '0 2px 5px rgba(0,0,0,0.05)',
        backdropFilter: 'blur(1px)'
      }}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      title={title}
    >
      {/* 背景警告渐变 */}
      {isHovered && (
        <div
          className="absolute inset-0 rounded-full"
          style={{
            background: 'linear-gradient(45deg, rgba(239, 68, 68, 0.08), rgba(248, 113, 113, 0.08))',
            animation: 'pulse 1.2s infinite'
          }}
        />
      )}

      {/* 删除图标 - 基于SVGRepo风格优化 */}
      <svg
        className={`w-4 h-4 transition-all duration-300 ${isHovered ? 'rotate-6 scale-105' : 'rotate-0 scale-100'}`}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        {/* 垃圾桶盖子 */}
        <path
          d="M3 6h18"
          className={isHovered ? 'animate-pulse' : ''}
          style={{ animationDuration: '0.8s' }}
        />
        {/* 垃圾桶手柄 */}
        <path d="M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2" />
        {/* 垃圾桶主体 */}
        <path
          d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6"
          className={isHovered ? 'animate-pulse' : ''}
          style={{ animationDuration: '1s' }}
        />
        {/* 垃圾桶内部线条 */}
        <path d="M10 11v6" opacity={isHovered ? "1" : "0.7"} />
        <path d="M14 11v6" opacity={isHovered ? "1" : "0.7"} />
      </svg>
    </button>
  );
};

export const SaveButton: React.FC<{ onClick?: () => void; disabled?: boolean }> = ({
  onClick,
  disabled = false
}) => (
  <EnhancedAnimatedButton
    onClick={onClick}
    disabled={disabled}
    variant="secondary"
    size="md"
    animationType="pulse"
    className="bg-purple-500 hover:bg-purple-600 border-purple-500"
  >
    保存
  </EnhancedAnimatedButton>
);
