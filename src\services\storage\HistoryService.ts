import { db } from '@/lib/db/dexie';
import { HistoryRecord } from '@/lib/db/dexie';
import { BaseDataService } from './DataService';

/**
 * 历史记录服务 - 管理各类历史记录
 * 替代localStorage中的书名生成历史、简介生成历史、对话历史等
 */
export class HistoryService extends BaseDataService {
  /**
   * 添加历史记录
   * @param bookId 书籍ID（可选，全局历史时为空）
   * @param type 历史类型 ('title', 'synopsis', 'dialogue', 'writing', 'rewrite')
   * @param content 历史内容
   * @param metadata 额外元数据
   */
  async add(
    bookId: string | null, 
    type: string, 
    content: any, 
    metadata?: Record<string, any>
  ): Promise<string> {
    try {
      const record: HistoryRecord = {
        bookId,
        type,
        content,
        metadata,
        createdAt: new Date()
      };

      const id = await db.historyRecords.add(record);
      
      // 清理缓存中相关的历史列表
      this.clearHistoryCache(bookId, type);
      
      return id as string;
    } catch (error) {
      console.error('添加历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取历史记录列表
   * @param bookId 书籍ID（可选）
   * @param type 历史类型
   * @param limit 限制数量
   * @param offset 偏移量
   */
  async getHistory(
    bookId: string | null, 
    type: string, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<HistoryRecord[]> {
    const cacheKey = this.getCacheKey(bookId || 'global', type, `list:${limit}:${offset}`);
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      let query = db.historyRecords
        .where(['bookId', 'type'])
        .equals([bookId, type])
        .reverse() // 按创建时间倒序
        .offset(offset)
        .limit(limit);

      const records = await query.toArray();
      
      this.cache.set(cacheKey, records);
      this.cleanupCache();
      
      return records;
    } catch (error) {
      console.error('获取历史记录失败:', error);
      return [];
    }
  }

  /**
   * 获取单个历史记录
   * @param id 记录ID
   */
  async getById(id: string): Promise<HistoryRecord | null> {
    const cacheKey = this.getCacheKey('record', id);
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const record = await db.historyRecords.get(id);
      
      if (record) {
        this.cache.set(cacheKey, record);
        this.cleanupCache();
      }
      
      return record || null;
    } catch (error) {
      console.error('获取历史记录失败:', error);
      return null;
    }
  }

  /**
   * 删除历史记录
   * @param id 记录ID
   */
  async delete(id: string): Promise<void> {
    try {
      const record = await this.getById(id);
      if (!record) return;

      await db.historyRecords.delete(id);
      
      // 清理相关缓存
      this.cache.delete(this.getCacheKey('record', id));
      this.clearHistoryCache(record.bookId, record.type);
    } catch (error) {
      console.error('删除历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除历史记录
   * @param bookId 书籍ID
   * @param type 历史类型
   * @param keepCount 保留数量（删除超出部分）
   */
  async cleanup(bookId: string | null, type: string, keepCount: number = 100): Promise<void> {
    try {
      const records = await db.historyRecords
        .where(['bookId', 'type'])
        .equals([bookId, type])
        .reverse()
        .toArray();

      if (records.length > keepCount) {
        const toDelete = records.slice(keepCount);
        const idsToDelete = toDelete.map(r => r.id!);
        
        await db.historyRecords.bulkDelete(idsToDelete);
        
        // 清理缓存
        this.clearHistoryCache(bookId, type);
      }
    } catch (error) {
      console.error('清理历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 搜索历史记录
   * @param bookId 书籍ID
   * @param type 历史类型
   * @param searchText 搜索文本
   * @param limit 限制数量
   */
  async search(
    bookId: string | null, 
    type: string, 
    searchText: string, 
    limit: number = 20
  ): Promise<HistoryRecord[]> {
    try {
      const records = await db.historyRecords
        .where(['bookId', 'type'])
        .equals([bookId, type])
        .reverse()
        .limit(limit * 5) // 扩大搜索范围
        .toArray();

      // 在内存中进行文本搜索
      const filtered = records.filter(record => {
        const contentStr = typeof record.content === 'string' 
          ? record.content 
          : JSON.stringify(record.content);
        return contentStr.toLowerCase().includes(searchText.toLowerCase());
      }).slice(0, limit);

      return filtered;
    } catch (error) {
      console.error('搜索历史记录失败:', error);
      return [];
    }
  }

  /**
   * 获取历史记录统计
   * @param bookId 书籍ID
   * @param type 历史类型
   */
  async getStats(bookId: string | null, type?: string): Promise<{
    total: number;
    byType: Record<string, number>;
    latestDate?: Date;
  }> {
    try {
      let query = db.historyRecords.where('bookId').equals(bookId);
      
      if (type) {
        query = query.and(record => record.type === type);
      }

      const records = await query.toArray();
      
      const stats = {
        total: records.length,
        byType: {} as Record<string, number>,
        latestDate: undefined as Date | undefined
      };

      records.forEach(record => {
        stats.byType[record.type] = (stats.byType[record.type] || 0) + 1;
        
        if (!stats.latestDate || record.createdAt > stats.latestDate) {
          stats.latestDate = record.createdAt;
        }
      });

      return stats;
    } catch (error) {
      console.error('获取历史统计失败:', error);
      return { total: 0, byType: {} };
    }
  }

  /**
   * 导出历史记录
   * @param bookId 书籍ID
   * @param type 历史类型（可选）
   */
  async export(bookId: string | null, type?: string): Promise<HistoryRecord[]> {
    try {
      let query = db.historyRecords.where('bookId').equals(bookId);
      
      if (type) {
        query = query.and(record => record.type === type);
      }

      return await query.reverse().toArray();
    } catch (error) {
      console.error('导出历史记录失败:', error);
      return [];
    }
  }

  /**
   * 导入历史记录
   * @param records 历史记录数组
   */
  async import(records: Omit<HistoryRecord, 'id'>[]): Promise<void> {
    try {
      await db.historyRecords.bulkAdd(records);
      
      // 清理所有相关缓存
      this.cache.clear();
    } catch (error) {
      console.error('导入历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 清理历史记录相关缓存
   */
  private clearHistoryCache(bookId: string | null, type: string): void {
    const prefix = this.getCacheKey(bookId || 'global', type);
    const keysToDelete = Array.from(this.cache.keys()).filter(key => key.startsWith(prefix));
    keysToDelete.forEach(key => this.cache.delete(key));
  }
}

// 导出单例实例
export const historyService = new HistoryService();
