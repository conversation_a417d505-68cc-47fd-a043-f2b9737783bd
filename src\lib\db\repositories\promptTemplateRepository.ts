import { v4 as uuidv4 } from 'uuid';
import { db, PromptTemplate, PromptCategory } from '../dexie';

export interface IPromptTemplateRepository {
  getAll(): Promise<PromptTemplate[]>;
  getById(id: string): Promise<PromptTemplate | undefined>;
  getByCategory(category: PromptCategory): Promise<PromptTemplate[]>;
  create(template: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, template: Partial<PromptTemplate>): Promise<void>;
  delete(id: string): Promise<void>;
}

export class PromptTemplateRepository implements IPromptTemplateRepository {
  async getAll(): Promise<PromptTemplate[]> {
    return await db.promptTemplates.toArray();
  }

  async getById(id: string): Promise<PromptTemplate | undefined> {
    return await db.promptTemplates.get(id);
  }

  async getByCategory(category: PromptCategory): Promise<PromptTemplate[]> {
    // 使用过滤器而不是equals方法
    return await db.promptTemplates
      .filter(template => template.category === category)
      .sortBy('name');
  }

  async create(template: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();

    await db.promptTemplates.add({
      ...template,
      id,
      createdAt: now,
      updatedAt: now
    });

    return id;
  }

  async update(id: string, template: Partial<PromptTemplate>): Promise<void> {
    await db.promptTemplates.update(id, {
      ...template,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    await db.promptTemplates.delete(id);
  }
}

// 创建并导出仓库实例
export const promptTemplateRepository = new PromptTemplateRepository();
