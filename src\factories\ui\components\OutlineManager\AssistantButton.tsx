"use client";

import React, { useRef, useCallback } from 'react';
import './AssistantButton.css';

interface AssistantButtonProps {
  onClick: (buttonPosition?: { x: number; y: number }) => void;
  isActive: boolean;
  disabled?: boolean;
}

/**
 * AI助手触发按钮组件
 * 用于在画布右侧显示一个浮动的助手按钮
 */
const AssistantButton: React.FC<AssistantButtonProps> = ({
  onClick,
  isActive,
  disabled = false
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = useCallback(() => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const buttonPosition = {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
      onClick(buttonPosition);
    } else {
      onClick();
    }
  }, [onClick]);

  return (
    <button
      ref={buttonRef}
      className={`assistant-button ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
      onClick={handleClick}
      disabled={disabled}
      title="AI助手 - 点击打开智能对话"
      aria-label="打开AI助手"
    >
      <div className="assistant-icon">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* AI助手图标 - 机器人头像 */}
          <path
            d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21ZM16 11V13H18V11H16ZM20 15H16V17H20V15ZM20 19H16V21H20V19Z"
            fill="currentColor"
          />
          <circle cx="9" cy="12" r="1.5" fill="currentColor" />
          <circle cx="15" cy="12" r="1.5" fill="currentColor" />
          <path
            d="M18 14.5C18 16.43 16.43 18 14.5 18H9.5C7.57 18 6 16.43 6 14.5V10.5C6 8.57 7.57 7 9.5 7H14.5C16.43 7 18 8.57 18 10.5V14.5Z"
            stroke="currentColor"
            strokeWidth="1.5"
            fill="none"
          />
        </svg>
      </div>

      {/* 活跃状态指示器 */}
      {isActive && (
        <div className="assistant-indicator">
          <div className="pulse-dot"></div>
        </div>
      )}

      {/* 悬停提示文本 */}
      <div className="assistant-tooltip">
        AI助手
      </div>
    </button>
  );
};

export default AssistantButton;
