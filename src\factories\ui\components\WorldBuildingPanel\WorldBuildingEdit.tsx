"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { WorldBuildingEditForm } from './WorldBuildingEditForm';

interface WorldBuildingEditProps {
  editingWorldBuilding: WorldBuilding;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSave: () => void;
  onCancel: () => void;
  saveButton: React.ReactNode;
  cancelButton: React.ReactNode;
}

/**
 * 世界观编辑组件
 */
export const WorldBuildingEdit: React.FC<WorldBuildingEditProps> = ({
  editingWorldBuilding,
  handleInputChange,
  handleSave,
  onCancel,
  saveButton,
  cancelButton
}) => {
  if (!editingWorldBuilding) return null;

  return (
    <div>
      <WorldBuildingEditForm
        editingWorldBuilding={editingWorldBuilding}
        handleInputChange={handleInputChange}
        handleSave={handleSave}
        onCancel={onCancel}
      />
    </div>
  );
};
