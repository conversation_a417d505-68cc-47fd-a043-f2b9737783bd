"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { AIPersonaConfig, PersonaVersion } from '../../../types/ai-persona';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';
import Panel from '../../../factories/ui/components/common/Panel';
import VersionList from './VersionList';
import VersionDetail from './VersionDetail';

interface PersonaVersionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  persona: AIPersonaConfig | null;
  onVersionChange?: (version: PersonaVersion) => void;
  onCategoryManage?: (persona: AIPersonaConfig) => void;
}

const PersonaVersionDialog: React.FC<PersonaVersionDialogProps> = ({
  isOpen,
  onClose,
  persona,
  onVersionChange,
  onCategoryManage
}) => {
  const [versions, setVersions] = useState<PersonaVersion[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<PersonaVersion | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreatingVersion, setIsCreatingVersion] = useState(false);
  const [personaStorageService] = useState(() => PersonaStorageService.getInstance());

  // 加载版本数据
  const loadVersions = async () => {
    if (!persona) return;

    try {
      setIsLoading(true);
      setError(null);

      const versionList = await personaStorageService.getPersonaVersions(persona.id);
      setVersions(versionList);

      // 默认选择最新版本
      if (versionList.length > 0) {
        setSelectedVersion(versionList[versionList.length - 1]);
      } else {
        setSelectedVersion(null);
      }

      console.log('版本数据加载完成:', {
        personaId: persona.id,
        versionCount: versionList.length
      });
    } catch (err) {
      console.error('加载版本数据失败:', err);
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 当persona或isOpen变化时重新加载数据
  useEffect(() => {
    if (isOpen && persona) {
      loadVersions();
    }
  }, [isOpen, persona]);

  // 创建新版本
  const handleCreateVersion = async (description?: string) => {
    if (!persona) return;

    try {
      setIsCreatingVersion(true);

      const newVersion = await personaStorageService.createPersonaVersion(
        persona.id,
        description || `版本 ${versions.length + 1}`
      );

      // 重新加载版本列表
      await loadVersions();

      // 选择新创建的版本
      setSelectedVersion(newVersion);

      console.log('创建版本成功:', newVersion.id);
    } catch (err) {
      console.error('创建版本失败:', err);
      setError(err instanceof Error ? err.message : '创建版本失败');
    } finally {
      setIsCreatingVersion(false);
    }
  };

  // 删除版本
  const handleDeleteVersion = async (versionId: string) => {
    if (!persona) return;

    // 确认删除
    if (!window.confirm('确定要删除这个版本吗？此操作不可撤销。')) {
      return;
    }

    try {
      // 从版本列表中移除
      const updatedVersions = versions.filter(v => v.id !== versionId);

      // 更新存储
      const versionKey = `ai-persona-extended-versions-${persona.id}`;
      localStorage.setItem(versionKey, JSON.stringify(updatedVersions));

      // 更新状态
      setVersions(updatedVersions);

      // 如果删除的是当前选中的版本，选择最新版本
      if (selectedVersion?.id === versionId) {
        if (updatedVersions.length > 0) {
          setSelectedVersion(updatedVersions[updatedVersions.length - 1]);
        } else {
          setSelectedVersion(null);
        }
      }

      console.log('删除版本成功:', versionId);
    } catch (err) {
      console.error('删除版本失败:', err);
      setError(err instanceof Error ? err.message : '删除版本失败');
    }
  };

  // 恢复版本
  const handleRestoreVersion = async (version: PersonaVersion) => {
    if (!persona) return;

    // 确认恢复
    if (!window.confirm(`确定要恢复到版本 ${version.version} 吗？当前配置将被覆盖。`)) {
      return;
    }

    try {
      await personaStorageService.restorePersonaVersion(persona.id, version.id);

      // 通知父组件版本已更改
      onVersionChange?.(version);

      console.log('恢复版本成功:', version.id);

      // 关闭对话框
      onClose();
    } catch (err) {
      console.error('恢复版本失败:', err);
      setError(err instanceof Error ? err.message : '恢复版本失败');
    }
  };

  // 处理版本选择
  const handleVersionSelect = (version: PersonaVersion) => {
    setSelectedVersion(version);
  };

  // 处理分类管理
  const handleCategoryManage = () => {
    if (persona) {
      onCategoryManage?.(persona);
    }
  };

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <motion.div
          className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <p className="text-gray-600">正在加载版本数据...</p>
      </div>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-red-600 mb-2">加载失败</p>
        <p className="text-gray-500 text-sm mb-4">{error}</p>
        <button
          onClick={loadVersions}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          重试
        </button>
      </div>
    </div>
  );

  // 渲染主内容
  const renderContent = () => {
    if (isLoading) return renderLoadingState();
    if (error) return renderErrorState();

    return (
      <div className="flex h-full">
        {/* 左侧：版本列表 */}
        <div className="w-1/3 border-r border-gray-200 dark:border-gray-700">
          <VersionList
            versions={versions}
            selectedVersion={selectedVersion}
            onVersionSelect={handleVersionSelect}
            onCreateVersion={handleCreateVersion}
            onDeleteVersion={handleDeleteVersion}
            onRestoreVersion={handleRestoreVersion}
            isCreatingVersion={isCreatingVersion}
          />
        </div>

        {/* 右侧：版本详情 */}
        <div className="flex-1">
          <VersionDetail
            version={selectedVersion}
            persona={persona}
            onCategoryManage={handleCategoryManage}
          />
        </div>
      </div>
    );
  };

  if (!persona) return null;

  // 使用Portal将弹窗渲染到document.body，避免被父级容器限制
  return createPortal(
    <Panel
      title={`版本管理 - ${persona.phase === 'custom' ? '自定义人设' : String(persona.phase)}`}
      isOpen={isOpen}
      onClose={onClose}
      size="large"
      enhanced={true}
      literaryTheme={true}
      content={renderContent()}
    />,
    document.body
  );
};

export default PersonaVersionDialog;
