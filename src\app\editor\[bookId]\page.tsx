"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { createAIWritingAdapter, createAIRewriteAdapter } from '@/adapters/ai';
import { createAIContinuePanelAdapter } from '@/adapters/ui/AIContinuePanel';
import { AIBookAnalysisAdapter } from '@/adapters/ai/AIBookAnalysisAdapter';
import { CircleButton, ConfirmDialog } from '@/adapters/ui'
import { SettingsDialog, APISettingsDialog } from '@/adapters/settings';
import { CharacterPanelAdapter, ChapterCharacterExtractorAdapter } from '@/adapters/character';
import { WorldBuildingPanelAdapter } from '@/adapters/worldbuilding';
import { TerminologyPanelAdapter } from '@/adapters/terminology';
import { Book, Chapter, BookRepository, ChapterRepository, WritingStatsRepository } from '@/db';
import { chapterRepository } from '@/lib/db/repositories';
import { OutlineManagerDialog } from '@/factories/ui/components/OutlineManager';
import { OutlineManagerProvider } from '@/factories/ui/hooks/useOutlineManager';
import { Character, WorldBuilding, Terminology } from '@/lib/db/dexie';
import { EnhancedChapterEditor, EnhancedChapterEditorRef } from '@/components/ui/EnhancedChapterEditor';
import { AnimatedNumber } from '@/components/ui/AnimatedNumber';
import { AnimatedSVGIcon } from '@/components/ui/AnimatedSVGIcon';
import { ChapterStatusIndicator, formatChapterWordCount, formatRelativeTime } from '@/components/ui/ChapterStatusIndicator';
import { ChapterSearchBar, HighlightText, searchChapters, sortSearchResults } from '@/components/ui/ChapterSearchBar';
import { FloatingDecorativeIcon } from '@/components/ui/DecorativeIcons';
import { AnimatedCompositeIcon } from '@/components/ui/CompositeIcons';
import { DeleteChapterButton } from '@/components/ui/EnhancedAnimatedButton';
import { ChapterBookIcon, useChapterFlipAnimation } from '@/components/ui/ChapterBookIcon';
import { EDITOR_LAYOUT, LayoutUtils } from '@/constants/layout';
import FullTextAnnotationButton from '@/components/ui/FullTextAnnotationButton';
import FullTextAnnotationDialog from '@/components/ui/FullTextAnnotationDialog';
import BrainstormDialog from '@/components/ui/BrainstormDialog';
import { ShortStoryWorkspaceDialog } from '@/components/short-story/ShortStoryWorkspaceDialog';
import { segmentText } from '@/utils/textSegmentation';
import '@/styles/animated-svg-icons.css';
import '@/styles/chapter-book-animations.css';
import '@/styles/enhanced-button.css';

// 添加按钮动画样式
const buttonAnimationStyles = `
  @keyframes gear-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes gear-click {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(0.9) rotate(45deg); }
    100% { transform: scale(1.1) rotate(90deg); }
  }

  @keyframes font-bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
  }

  @keyframes font-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  .gear-icon:hover svg {
    animation: gear-rotate 2s linear infinite;
  }

  .gear-icon:active svg {
    animation: gear-click 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .font-icon:hover svg {
    animation: font-bounce 0.6s ease-in-out infinite;
  }

  .font-icon:active svg {
    animation: font-pulse 0.2s ease-in-out;
  }

  .gear-icon svg, .font-icon svg {
    transition: all 0.3s ease;
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = buttonAnimationStyles;
  document.head.appendChild(styleElement);
}

interface EditorPageProps {
  params: {
    bookId: string;
  };
}

export default function EditorPage({ params }: EditorPageProps) {
  const { bookId } = params;
  const router = useRouter();

  // 创建仓库实例
  const bookRepository = new BookRepository();
  const writingStatsRepository = new WritingStatsRepository();
  // chapterRepository 从 @/lib/db/repositories 导入

  // 字体和设置状态
  const [currentFont, setCurrentFont] = useState<string>('roboto');
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isAPISettingsOpen, setIsAPISettingsOpen] = useState(false);

  // 字体选项映射
  const fontFamilyMap: Record<string, string> = {
    'roboto': 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    'serif': 'Georgia, "Times New Roman", serif',
    'sans': 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    'siyuan': '"Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", sans-serif',
    'mono': '"Courier New", monospace'
  };

  // 书籍数据
  const [book, setBook] = useState<Book | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 章节数据
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [currentChapter, setCurrentChapter] = useState<Chapter | null>(null);

  // 当前章节内容
  const [currentChapterContent, setCurrentChapterContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);

  // 删除确认弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [chapterToDelete, setChapterToDelete] = useState<Chapter | null>(null);

  // 章节切换动画状态
  const [isChapterChanging, setIsChapterChanging] = useState(false);
  const [chapterChangeDirection, setChapterChangeDirection] = useState<'next' | 'prev'>('next');

  // AI功能菜单状态
  const [isAIMenuOpen, setIsAIMenuOpen] = useState(false);

  // 人物面板状态
  const [isCharacterPanelOpen, setIsCharacterPanelOpen] = useState(false);

  // 世界观面板状态
  const [isWorldBuildingPanelOpen, setIsWorldBuildingPanelOpen] = useState(false);

  // 术语面板状态
  const [isTerminologyPanelOpen, setIsTerminologyPanelOpen] = useState(false);

  // 大纲管理面板状态
  const [isOutlineManagerOpen, setIsOutlineManagerOpen] = useState(false);

  // AI写作面板状态
  const [isAIWritingDialogOpen, setIsAIWritingDialogOpen] = useState(false);

  // AI改写面板状态
  const [isAIRewriteDialogOpen, setIsAIRewriteDialogOpen] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [beforeContext, setBeforeContext] = useState('');
  const [afterContext, setAfterContext] = useState('');
  const [showFloatingButton, setShowFloatingButton] = useState(false);
  const [floatingButtonPosition, setFloatingButtonPosition] = useState({ top: 0, left: 0 });

  // AI续写面板状态
  const [isAIContinueDialogOpen, setIsAIContinueDialogOpen] = useState(false);

  // AI拆书面板状态
  const [isAIBookAnalysisDialogOpen, setIsAIBookAnalysisDialogOpen] = useState(false);

  // 全文标注面板状态
  const [isFullTextAnnotationDialogOpen, setIsFullTextAnnotationDialogOpen] = useState(false);

  // AI脑洞面板状态
  const [isBrainstormDialogOpen, setIsBrainstormDialogOpen] = useState(false);

  // AI短篇创作面板状态
  const [isAIShortStoryDialogOpen, setIsAIShortStoryDialogOpen] = useState(false);

  // 人物提取状态
  const [isCharacterExtracting, setIsCharacterExtracting] = useState(false);
  const [characters, setCharacters] = useState<Character[]>([]);

  // 章节创建状态锁（防止重复创建）
  const [isCreatingFirstChapter, setIsCreatingFirstChapter] = useState(false);
  const [isCreatingChapter, setIsCreatingChapter] = useState(false);

  // 搜索状态
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChapters, setFilteredChapters] = useState<Chapter[]>([]);

  // 编辑器引用
  const editorRef = useRef<EnhancedChapterEditorRef>(null);

  // 章节翻页动画
  const { isAnimating: isChapterFlipping, direction: flipDirection, triggerFlip } = useChapterFlipAnimation();

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setFilteredChapters(chapters);
    } else {
      const searchResults = searchChapters(chapters, query);
      const sortedResults = sortSearchResults(searchResults, query);
      setFilteredChapters(sortedResults);
    }
  };

  // 当章节列表变化时更新过滤结果
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredChapters(chapters);
    } else {
      const searchResults = searchChapters(chapters, searchQuery);
      const sortedResults = sortSearchResults(searchResults, searchQuery);
      setFilteredChapters(sortedResults);
    }
  }, [chapters, searchQuery]);

  // 加载书籍和章节数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // 加载书籍
        const bookData = await bookRepository.getBookById(bookId);
        if (!bookData) {
          console.error('书籍不存在');
          router.push('/');
          return;
        }

        setBook(bookData);

        // 加载章节
        const chaptersData = await chapterRepository.getAllByBookId(bookId);
        setChapters(chaptersData);

        // 如果没有章节，创建第一章（使用状态锁防止重复创建）
        if (chaptersData.length === 0 && !isCreatingFirstChapter) {
          setIsCreatingFirstChapter(true);

          try {
            const chapterId = await chapterRepository.create({
              bookId,
              title: '第1章',
              content: '',
              order: 0,
              wordCount: 0,
              characterIds: [],
              terminologyIds: [],
              worldBuildingIds: []
            });

            const newChapter = await chapterRepository.getById(chapterId);
            if (newChapter) {
              setChapters([newChapter]);
              setCurrentChapter(newChapter);
              setCurrentChapterContent(newChapter.content);

              // 保存当前章节ID到localStorage
              localStorage.setItem(`current-editor-chapter-${bookId}`, newChapter.id!);
            }
          } catch (error) {
            console.error('创建第一章失败:', error);
          } finally {
            setIsCreatingFirstChapter(false);
          }
        } else {
          // 尝试从localStorage获取上次编辑的章节ID
          let lastEditedChapterId = null;
          try {
            lastEditedChapterId = localStorage.getItem(`current-editor-chapter-${bookId}`);
          } catch (error) {
            console.error('获取上次编辑章节ID失败:', error);
          }

          // 如果有上次编辑的章节ID，则选择该章节
          let chapterToSelect = chaptersData[0];
          if (lastEditedChapterId) {
            const lastChapter = chaptersData.find(ch => ch.id === lastEditedChapterId);
            if (lastChapter) {
              chapterToSelect = lastChapter;
            }
          }

          // 设置当前章节
          setCurrentChapter(chapterToSelect);
          setCurrentChapterContent(chapterToSelect.content);

          // 保存当前章节ID到localStorage
          if (chapterToSelect.id) {
            localStorage.setItem(`current-editor-chapter-${bookId}`, chapterToSelect.id);
          }
        }

        // 加载人物数据
        try {
          // 导入 characterRepository
          const { characterRepository } = await import('@/lib/db/repositories');

          // 获取人物数据
          const charactersData = await characterRepository.getAllByBookId(bookId);
          setCharacters(charactersData);
        } catch (error) {
          console.error('加载人物数据失败:', error);
          setCharacters([]);
        }
      } catch (error) {
        console.error('加载数据失败', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [bookId]);

  // 仪表盘数据
  const [dashboardData, setDashboardData] = useState({
    totalWords: 0,
    dailyGoal: 2000,
    dailyWords: 0,
    writingTime: 0,
    lastSaved: new Date().toLocaleTimeString('zh-CN'),
    writingSpeed: 0, // 字/分钟
    consecutiveDays: 0, // 连续写作天数
    todayStartTime: null as Date | null // 今日开始写作时间
  });

  // 写作速度追踪
  const [speedTracker, setSpeedTracker] = useState({
    lastWordCount: 0,
    lastUpdateTime: Date.now(),
    recentSpeeds: [] as number[] // 最近的写作速度记录
  });

  // 番茄钟状态
  const [pomodoroTimer, setPomodoroTimer] = useState({
    isActive: false,
    timeLeft: 25 * 60, // 25分钟，以秒为单位
    mode: 'work' as 'work' | 'break', // 工作或休息模式
    completedSessions: 0
  });

  // 计算总字数和更新仪表盘
  useEffect(() => {
    const updateDashboard = async () => {
      try {
        // 计算总字数
        const totalWords = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0);

        // 获取今日写作统计
        const todayStats = await writingStatsRepository.getTodayStats(bookId);
        const dailyWords = todayStats ? todayStats.wordCount : 0;

        // 获取总写作时间
        const totalTimeSpent = await writingStatsRepository.getTotalTimeSpent(bookId);

        setDashboardData(prev => ({
          ...prev,
          totalWords,
          dailyWords,
          writingTime: totalTimeSpent
        }));
      } catch (error) {
        console.error('更新仪表盘失败', error);
      }
    };

    updateDashboard();

    // 写作时间计时器 - 只在用户实际写作时计时
    let lastActivityTime = Date.now();
    const timer = setInterval(() => {
      // 检查是否在写作（通过检查内容是否有变化）
      const now = Date.now();
      if (now - lastActivityTime < 120000) { // 2分钟内有活动才计时
        setDashboardData(prev => ({ ...prev, writingTime: prev.writingTime + 1 }));
      }
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, [chapters, bookId]);

  // 实时更新仪表盘和写作速度
  useEffect(() => {
    if (currentChapter && currentChapterContent) {
      const currentWordCount = calculateRealTimeWordCount();
      const now = Date.now();

      // 计算写作速度
      if (speedTracker.lastWordCount > 0) {
        const wordDiff = currentWordCount - speedTracker.lastWordCount;
        const timeDiff = (now - speedTracker.lastUpdateTime) / 1000 / 60; // 转换为分钟

        if (wordDiff > 0 && timeDiff > 0) {
          const currentSpeed = Math.round(wordDiff / timeDiff);

          // 更新速度追踪器
          setSpeedTracker(prev => {
            const newSpeeds = [...prev.recentSpeeds, currentSpeed].slice(-10); // 只保留最近10次记录
            const avgSpeed = newSpeeds.reduce((sum, speed) => sum + speed, 0) / newSpeeds.length;

            // 更新仪表盘数据
            setDashboardData(prevDash => ({
              ...prevDash,
              writingSpeed: Math.round(avgSpeed)
            }));

            return {
              lastWordCount: currentWordCount,
              lastUpdateTime: now,
              recentSpeeds: newSpeeds
            };
          });
        }
      } else {
        // 初始化速度追踪器
        setSpeedTracker({
          lastWordCount: currentWordCount,
          lastUpdateTime: now,
          recentSpeeds: []
        });
      }
    }
  }, [currentChapterContent, currentChapter]);

  // 番茄钟计时器
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (pomodoroTimer.isActive && pomodoroTimer.timeLeft > 0) {
      interval = setInterval(() => {
        setPomodoroTimer(prev => ({
          ...prev,
          timeLeft: prev.timeLeft - 1
        }));
      }, 1000);
    } else if (pomodoroTimer.timeLeft === 0) {
      // 番茄钟结束
      setPomodoroTimer(prev => ({
        ...prev,
        isActive: false,
        timeLeft: prev.mode === 'work' ? 5 * 60 : 25 * 60, // 工作结束后5分钟休息，休息结束后25分钟工作
        mode: prev.mode === 'work' ? 'break' : 'work',
        completedSessions: prev.mode === 'work' ? prev.completedSessions + 1 : prev.completedSessions
      }));

      // 显示通知
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(pomodoroTimer.mode === 'work' ? '工作时间结束，休息一下！' : '休息结束，开始工作！');
      }
    }

    return () => clearInterval(interval);
  }, [pomodoroTimer.isActive, pomodoroTimer.timeLeft, pomodoroTimer.mode]);

  // 自动保存功能
  useEffect(() => {
    if (!currentChapter || currentChapterContent === currentChapter.content) {
      return;
    }

    const saveTimeout = setTimeout(async () => {
      if (!currentChapter) return;

      try {
        setIsSaving(true);

        // 计算字数变化
        const oldWordCount = currentChapter.wordCount;
        const newWordCount = currentChapterContent.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length;
        const wordCountChange = newWordCount - oldWordCount;

        // 更新章节
        await chapterRepository.update(currentChapter.id!, {
          content: currentChapterContent
        });

        // 获取更新后的章节
        const updatedChapter = await chapterRepository.getById(currentChapter.id!);
        if (updatedChapter) {
          // 更新当前章节
          setCurrentChapter(updatedChapter);

          // 更新章节列表
          setChapters(prev => prev.map(ch => ch.id === updatedChapter.id ? updatedChapter : ch));

          // 记录写作统计
          if (wordCountChange !== 0) {
            await writingStatsRepository.recordStats(bookId, wordCountChange, 1);
          }

          // 更新最后保存时间
          const now = new Date();
          setLastSavedTime(now);
          setDashboardData(prev => ({ ...prev, lastSaved: now.toLocaleTimeString('zh-CN') }));
        }
      } catch (error) {
        console.error('保存章节失败', error);
      } finally {
        setIsSaving(false);
      }
    }, 2000); // 2秒后自动保存

    return () => clearTimeout(saveTimeout);
  }, [currentChapter, currentChapterContent, bookId]);

  // 处理删除章节
  const handleDeleteChapter = async () => {
    if (!chapterToDelete) return;

    try {
      // 删除章节
      await chapterRepository.delete(chapterToDelete.id!);

      // 从章节列表中移除
      setChapters(prev => prev.filter(ch => ch.id !== chapterToDelete.id));

      // 如果删除的是当前章节，切换到第一个章节
      if (currentChapter && currentChapter.id === chapterToDelete.id) {
        const remainingChapters = chapters.filter(ch => ch.id !== chapterToDelete.id);
        if (remainingChapters.length > 0) {
          setCurrentChapter(remainingChapters[0]);
          setCurrentChapterContent(remainingChapters[0].content);
        } else {
          setCurrentChapter(null);
          setCurrentChapterContent('');
        }
      }
    } catch (error) {
      console.error('删除章节失败', error);
      alert('删除章节失败，请重试');
    } finally {
      // 关闭确认弹窗
      setDeleteDialogOpen(false);
      setChapterToDelete(null);
    }
  };

  // 格式化时间显示
  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours === 0) {
      return `${mins}分钟`;
    } else if (mins === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}h${mins}m`;
    }
  };

  // 格式化番茄钟时间（秒转分:秒）
  const formatPomodoroTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 番茄钟控制函数
  const togglePomodoro = () => {
    setPomodoroTimer(prev => ({
      ...prev,
      isActive: !prev.isActive
    }));
  };

  const resetPomodoro = () => {
    setPomodoroTimer(prev => ({
      ...prev,
      isActive: false,
      timeLeft: 25 * 60,
      mode: 'work'
    }));
  };

  // 获取写作速度状态
  const getSpeedStatus = (speed: number): { color: string; label: string } => {
    if (speed === 0) return { color: '#9CA3AF', label: '待机中' };
    if (speed < 20) return { color: '#EF4444', label: '慢速' };
    if (speed < 40) return { color: '#F59E0B', label: '正常' };
    if (speed < 60) return { color: '#10B981', label: '快速' };
    return { color: '#8B5CF6', label: '极速' };
  };

  // 计算实时字数
  const calculateRealTimeWordCount = (): number => {
    // 如果没有当前章节，返回0
    if (!currentChapter) return 0;

    // 计算当前章节的实时字数
    const currentChapterWordCount = currentChapterContent
      .replace(/\s+/g, '')
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
      .length;

    // 计算其他章节的总字数
    const otherChaptersWordCount = chapters
      .filter(ch => ch.id !== currentChapter.id)
      .reduce((sum, ch) => sum + ch.wordCount, 0);

    // 返回总字数
    return currentChapterWordCount + otherChaptersWordCount;
  };

  // 计算今日实时字数（今日新增字数）
  const calculateRealTimeDailyWords = (): number => {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    const todayBaseKey = `today-base-${bookId}-${today}`;

    // 获取当前总字数
    const currentTotalWords = calculateRealTimeWordCount();

    // 获取今日开始时的字数基准
    let todayBaseWordCount = 0;
    try {
      const stored = localStorage.getItem(todayBaseKey);
      if (stored) {
        todayBaseWordCount = parseInt(stored, 10);
      } else {
        // 如果没有今日基准，设置当前字数为基准
        localStorage.setItem(todayBaseKey, currentTotalWords.toString());
        todayBaseWordCount = currentTotalWords;
      }
    } catch (error) {
      console.error('获取今日字数基准失败:', error);
      todayBaseWordCount = currentTotalWords;
    }

    // 计算今日新增字数
    const todayNewWords = Math.max(0, currentTotalWords - todayBaseWordCount);

    // 检查是否需要更新基准（防止字数减少导致负数）
    if (currentTotalWords < todayBaseWordCount) {
      try {
        localStorage.setItem(todayBaseKey, currentTotalWords.toString());
      } catch (error) {
        console.error('更新今日字数基准失败:', error);
      }
      return 0;
    }

    return todayNewWords;
  };

  // 计算特定章节的实时字数
  const calculateChapterWordCount = (chapter: Chapter): number => {
    // 如果是当前正在编辑的章节，返回实时字数
    if (currentChapter && chapter.id === currentChapter.id) {
      return currentChapterContent
        .replace(/\s+/g, '')
        .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
        .length;
    }

    // 否则返回保存的字数
    return chapter.wordCount;
  };

  // 处理设置按钮点击
  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  // 处理设置弹窗关闭
  const handleSettingsClose = () => {
    setIsSettingsOpen(false);
  };

  // 处理API设置按钮点击
  const handleAPISettingsClick = () => {
    setIsAPISettingsOpen(true);
  };

  // 处理API设置弹窗关闭
  const handleAPISettingsClose = () => {
    setIsAPISettingsOpen(false);
  };

  // 处理字体变更
  const handleFontChange = (font: string) => {
    setCurrentFont(font);
  };

  // 处理创建人物
  const handleCreateCharacter = async (characterInfo: any) => {
    try {
      // 导入 characterRepository
      const { characterRepository } = await import('@/lib/db/repositories');

      // 创建完整的人物对象，确保所有字段都有默认值
      const now = new Date();
      const newCharacter = {
        bookId,
        name: characterInfo.newInfo?.name || characterInfo.name || '新人物',
        description: characterInfo.newInfo?.description || '',
        appearance: characterInfo.newInfo?.appearance || '',
        personality: characterInfo.newInfo?.personality || '',
        background: characterInfo.newInfo?.background || '',
        goals: characterInfo.newInfo?.goals || '',
        characterArchetype: characterInfo.newInfo?.characterArchetype || '',
        growthArc: characterInfo.newInfo?.growthArc || '',
        hiddenMotivation: characterInfo.newInfo?.hiddenMotivation || '',
        secretHistory: characterInfo.newInfo?.secretHistory || '',
        innerConflicts: characterInfo.newInfo?.innerConflicts || '',
        symbolism: characterInfo.newInfo?.symbolism || '',
        relationships: characterInfo.newInfo?.relationships ? [{
          targetCharacterId: '',
          relationshipType: '其他',
          description: characterInfo.newInfo.relationships
        }] : [],
        createdAt: now,
        updatedAt: now,
        extractedFromChapterIds: currentChapter ? [currentChapter.id!] : [],
        relatedCharacterIds: [],
        relatedTerminologyIds: [],
        relatedWorldBuildingIds: []
      };

      console.log('创建人物对象:', newCharacter);
      console.log('AI提取的原始数据:', characterInfo.newInfo);

      // 保存人物
      const characterId = await characterRepository.create(newCharacter);

      // 更新人物列表
      if (characterId) {
        const createdCharacter = await characterRepository.getById(characterId);
        if (createdCharacter) {
          setCharacters(prev => [...prev, createdCharacter]);
          console.log('成功创建并获取人物:', createdCharacter);
        }
      }

      // 显示成功消息
      alert(`成功创建人物: ${newCharacter.name}`);
    } catch (error) {
      console.error('创建人物失败:', error);
      console.error('错误详情:', error);
      alert('创建人物失败，请重试');
    }
  };

  // 处理更新人物
  const handleUpdateCharacter = async (character: Character, characterInfo: any) => {
    try {
      // 导入 characterRepository
      const { characterRepository } = await import('@/lib/db/repositories');

      // 更新人物，确保所有字段都正确处理
      const updatedCharacter = { ...character };
      updatedCharacter.updatedAt = new Date();

      // 如果当前章节存在且不在提取章节列表中，添加到列表
      if (currentChapter && currentChapter.id && !updatedCharacter.extractedFromChapterIds.includes(currentChapter.id)) {
        updatedCharacter.extractedFromChapterIds = [...updatedCharacter.extractedFromChapterIds, currentChapter.id];
      }

      // 更新AI提取的信息，确保字段正确映射
      if (characterInfo.newInfo) {
        // 明确处理每个字段，确保数据类型正确
        if (characterInfo.newInfo.name) updatedCharacter.name = characterInfo.newInfo.name;
        if (characterInfo.newInfo.description) updatedCharacter.description = characterInfo.newInfo.description;
        if (characterInfo.newInfo.appearance) updatedCharacter.appearance = characterInfo.newInfo.appearance;
        if (characterInfo.newInfo.personality) updatedCharacter.personality = characterInfo.newInfo.personality;
        if (characterInfo.newInfo.background) updatedCharacter.background = characterInfo.newInfo.background;
        if (characterInfo.newInfo.goals) updatedCharacter.goals = characterInfo.newInfo.goals;
        if (characterInfo.newInfo.characterArchetype) updatedCharacter.characterArchetype = characterInfo.newInfo.characterArchetype;
        if (characterInfo.newInfo.growthArc) updatedCharacter.growthArc = characterInfo.newInfo.growthArc;
        if (characterInfo.newInfo.hiddenMotivation) updatedCharacter.hiddenMotivation = characterInfo.newInfo.hiddenMotivation;
        if (characterInfo.newInfo.secretHistory) updatedCharacter.secretHistory = characterInfo.newInfo.secretHistory;
        if (characterInfo.newInfo.innerConflicts) updatedCharacter.innerConflicts = characterInfo.newInfo.innerConflicts;
        if (characterInfo.newInfo.symbolism) updatedCharacter.symbolism = characterInfo.newInfo.symbolism;

        // 处理关系字段
        if (characterInfo.newInfo.relationships) {
          if (!updatedCharacter.relationships) {
            updatedCharacter.relationships = [];
          }
          // 添加新的关系描述
          updatedCharacter.relationships.push({
            targetCharacterId: '',
            relationshipType: '其他',
            description: characterInfo.newInfo.relationships
          });
        }
      }

      console.log('更新人物对象:', updatedCharacter);
      console.log('AI提取的原始数据:', characterInfo.newInfo);

      // 保存人物
      await characterRepository.update(updatedCharacter.id!, updatedCharacter);

      // 更新人物列表
      setCharacters(prev => prev.map(c => c.id === updatedCharacter.id ? updatedCharacter : c));

      // 显示成功消息
      alert(`成功更新人物: ${updatedCharacter.name}`);
    } catch (error) {
      console.error('更新人物失败:', error);
      console.error('错误详情:', error);
      alert('更新人物失败，请重试');
    }
  };

  // 加载状态显示
  if (isLoading) {
    return (
      <main
        className="min-h-screen flex items-center justify-center"
        style={{
          fontFamily: fontFamilyMap[currentFont] || fontFamilyMap['roboto'],
          backgroundColor: 'var(--color-primary-bg)'
        }}
      >
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-t-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
          <h2 className="text-xl font-medium mb-2" style={{ color: 'var(--color-primary)' }}>加载中...</h2>
          <p className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>正在加载您的作品</p>
        </div>
      </main>
    );
  }

  return (
    <main
      className="h-screen flex flex-col overflow-hidden"
      style={{
        fontFamily: fontFamilyMap[currentFont] || fontFamilyMap['roboto']
      }}
    >
      {/* 设置弹窗 */}
      <SettingsDialog
        isOpen={isSettingsOpen}
        onClose={handleSettingsClose}
        currentFont={currentFont}
        onFontChange={handleFontChange}
      />

      {/* API设置弹窗 */}
      <APISettingsDialog
        isOpen={isAPISettingsOpen}
        onClose={handleAPISettingsClose}
      />

      {/* 删除确认弹窗 */}
      <ConfirmDialog
        isOpen={deleteDialogOpen}
        title="删除章节"
        message={`确定要删除章节"${chapterToDelete?.title || ''}"吗？此操作不可撤销。`}
        confirmText="删除"
        cancelText="取消"
        confirmType="danger"
        onConfirm={handleDeleteChapter}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setChapterToDelete(null);
        }}
      />
      <header className="py-3 px-6 flex flex-col space-y-2"
        style={{
          backgroundColor: 'var(--color-white)',
          borderBottom: `1px solid var(--color-secondary)`
        }}>
        {/* 顶部导航栏 */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link href="/" style={{ color: 'var(--color-primary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            </Link>
            <h1 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>
              编辑：{book ? book.title : '加载中...'}
            </h1>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <div className="flex items-center text-sm transition-all duration-300"
                style={{
                  color: isSaving ? 'var(--color-info)' :
                         (currentChapter && currentChapterContent !== currentChapter.content) ? 'var(--color-warning)' :
                         'var(--color-success)'
                }}
              >
                {isSaving ? (
                  <>
                    <div className="w-3 h-3 mr-1 rounded-full border-2 border-t-2 border-blue-300 border-t-blue-500 animate-spin"></div>
                    <span className="animate-pulse">正在保存...</span>
                  </>
                ) : currentChapter && currentChapterContent !== currentChapter.content ? (
                  <>
                    <div className="w-2 h-2 mr-1 rounded-full bg-yellow-400 animate-pulse"></div>
                    <span>未保存的更改</span>
                  </>
                ) : (
                  <>
                    <div className="w-2 h-2 mr-1 rounded-full bg-green-400"></div>
                    <span className="opacity-80">上次保存: {dashboardData.lastSaved}</span>
                  </>
                )}
              </div>
            </div>
            {/* API设置按钮 - 仅图标 */}
            <button
              className="btn rounded-lg transition-all duration-200 flex items-center justify-center group hover:scale-105 active:scale-95"
              style={{
                backgroundColor: 'var(--color-secondary)',
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-secondary)',
                padding: '8px',
                opacity: 1,
                cursor: 'pointer',
                marginRight: '8px',
                borderRadius: '6px',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                width: '32px',
                height: '32px',
                border: 'none'
              }}
              onClick={handleAPISettingsClick}
              aria-label="API设置"
              title="API设置"
            >
              {/* 专业齿轮SVG图标 */}
              <span className="gear-icon transition-all duration-300">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="transition-colors duration-200"
                  style={{ transformOrigin: 'center' }}
                >
                  {/* 专业8齿齿轮设计 */}
                  <path d="M12 2.5L13.5 5.5L16.5 5.5L18 2.5L21.5 4L21.5 7L23 10L21.5 13L21.5 16L18 17.5L16.5 20.5L13.5 20.5L12 23.5L10.5 20.5L7.5 20.5L6 17.5L2.5 16L2.5 13L1 10L2.5 7L2.5 4L6 2.5L7.5 5.5L10.5 5.5Z"/>
                  {/* 中心圆孔 */}
                  <circle cx="12" cy="12" r="3" fill="none"/>
                </svg>
              </span>
            </button>
            {/* 字体设置按钮 - 仅图标 */}
            <button
              className="btn rounded-lg transition-all duration-200 flex items-center justify-center group hover:scale-105 active:scale-95"
              style={{
                backgroundColor: 'var(--color-secondary)',
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-secondary)',
                padding: '8px',
                opacity: 1,
                cursor: 'pointer',
                borderRadius: '6px',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                width: '32px',
                height: '32px',
                border: 'none'
              }}
              onClick={handleSettingsClick}
              aria-label="字体设置"
              title="字体设置"
            >
              {/* 字体设置SVG图标 */}
              <span className="font-icon transition-all duration-300">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="transition-colors duration-200"
                >
                  {/* 字母A的字体图标设计 */}
                  <path d="M4 20L12 4L20 20"/>
                  <path d="M6.5 16L17.5 16"/>
                  <circle cx="12" cy="4" r="1"/>
                </svg>
              </span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* 左侧章节列表 */}
        <div className="w-64 flex flex-col"
          style={{
            backgroundColor: 'var(--color-sidebar-bg)',
            borderRight: `1px solid var(--color-secondary)`
          }}>
          <div className="p-4 flex flex-col h-full">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-3">
                <h2 className="font-semibold text-lg" style={{ color: 'var(--color-primary)' }}>章节列表</h2>
                <AnimatedCompositeIcon
                  type="starry-chapter"
                  size={32}
                  primaryColor="var(--color-primary)"
                  secondaryColor="var(--color-accent)"
                  animated={true}
                  animationSpeed="slow"
                  hoverEffect={true}
                />
              </div>

              {/* 统一的创建章节按钮 */}
              <button
                className="flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 hover:scale-110 active:scale-95 relative z-50"
                style={{
                  backgroundColor: (isSaving || isCreatingChapter) ? 'rgba(156, 163, 175, 0.6)' : 'var(--color-primary)',
                  color: 'white',
                  border: 'none',
                  cursor: (isSaving || isCreatingChapter) ? 'not-allowed' : 'pointer',
                  opacity: (isSaving || isCreatingChapter) ? 0.6 : 1,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  pointerEvents: 'auto'
                }}
                onClick={async () => {
                  // 防止重复点击
                  if (isSaving || isCreatingChapter) {
                    console.log('⚠️ 正在创建章节中，忽略重复点击');
                    return;
                  }

                  console.log('🔥 创建章节按钮被点击');
                  console.log('当前状态:', {
                    isSaving,
                    isCreatingChapter,
                    chaptersLength: chapters.length,
                    bookId,
                    currentChapter: currentChapter?.title
                  });

                  try {
                    setIsCreatingChapter(true);
                    setIsSaving(true);
                    console.log('📝 开始创建新章节流程');

                    // 如果当前章节有未保存的更改，先保存
                    if (currentChapter && currentChapterContent !== currentChapter.content) {
                      console.log('💾 保存当前章节的未保存更改');

                      // 更新章节
                      await chapterRepository.update(currentChapter.id!, {
                        content: currentChapterContent
                      });
                      console.log('✅ 当前章节保存完成');
                    }

                    // 获取最后一个章节的顺序
                    const lastOrder = chapters.length > 0
                      ? Math.max(...chapters.map(ch => ch.order))
                      : -1;

                    console.log('📊 计算章节顺序:', { lastOrder, newOrder: lastOrder + 1 });

                    // 创建新章节数据
                    const newChapterData = {
                      bookId,
                      title: `第${chapters.length + 1}章`,
                      content: '',
                      order: lastOrder + 1,
                      wordCount: 0,
                      characterIds: [],
                      terminologyIds: [],
                      worldBuildingIds: []
                    };

                    console.log('🆕 准备创建章节:', newChapterData);

                    // 创建新章节
                    const chapterId = await chapterRepository.create(newChapterData);
                    console.log('✨ 章节创建成功，ID:', chapterId);

                    const newChapter = await chapterRepository.getById(chapterId);
                    console.log('📖 获取新创建的章节:', newChapter);

                    if (newChapter) {
                      // 更新章节列表
                      setChapters(prev => {
                        const updated = [...prev, newChapter];
                        console.log('📚 更新章节列表，新长度:', updated.length);
                        return updated;
                      });

                      // 切换到新章节
                      setCurrentChapter(newChapter);
                      setCurrentChapterContent('');

                      // 保存当前章节ID到localStorage
                      if (newChapter.id) {
                        localStorage.setItem(`current-editor-chapter-${bookId}`, newChapter.id);
                      }

                      console.log('🎉 成功切换到新章节:', newChapter.title);
                    } else {
                      throw new Error('无法获取新创建的章节');
                    }
                  } catch (error) {
                    console.error('❌ 创建新章节失败:', error);
                    console.error('错误详情:', error);

                    // 显示错误提示
                    alert(`创建章节失败：${error instanceof Error ? error.message : '未知错误'}`);
                  } finally {
                    setIsSaving(false);
                    setIsCreatingChapter(false);
                    console.log('🏁 创建章节流程结束');
                  }
                }}
                disabled={isSaving || isCreatingChapter}
                title="创建新章节"
              >
                {isSaving ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                )}
              </button>
            </div>

            {/* 搜索栏 */}
            <div className="mb-4">
              <ChapterSearchBar
                onSearch={handleSearch}
                placeholder="搜索章节标题或内容..."
                className="w-full"
              />
            </div>

            <div className="text-sm mb-2 transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }}>
              总字数: {calculateRealTimeWordCount()}
            </div>

            <div className="overflow-y-auto flex-1" style={{ maxHeight: 'calc(100vh - 220px)' }}>
              {chapters.length === 0 ? (
              <div className="py-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 rounded-full"
                  style={{ borderColor: 'var(--color-secondary)', color: 'var(--color-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <p className="text-sm mb-2" style={{ color: 'var(--color-text-secondary)' }}>
                  正在创建第一章...
                </p>
              </div>
            ) : filteredChapters.length === 0 ? (
              <div className="py-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 rounded-full"
                  style={{ borderColor: 'var(--color-secondary)', color: 'var(--color-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <p className="text-sm mb-2" style={{ color: 'var(--color-text-secondary)' }}>
                  没有找到匹配的章节
                </p>
                <p className="text-xs" style={{ color: 'var(--color-text-hint)' }}>
                  尝试使用不同的关键词搜索
                </p>
              </div>
            ) : (
              <div className="space-y-1.5">
                {filteredChapters.map((chapter) => (
                  <div
                    key={chapter.id}
                    className={`p-3 rounded-xl flex items-center gap-3 cursor-pointer transition-all duration-500 ease-out transform hover:scale-105 hover:shadow-lg group`}
                    style={{
                      color: currentChapter && chapter.id === currentChapter.id ? 'var(--color-info)' : 'var(--color-text-primary)',
                      margin: '8px 0',
                      backdropFilter: 'none',
                      backgroundColor: currentChapter && chapter.id === currentChapter.id
                        ? 'rgba(235, 245, 255, 0.98)'
                        : 'rgba(255, 255, 255, 0.95)',
                      transition: 'all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)',
                      transform: currentChapter && chapter.id === currentChapter.id
                        ? 'translateX(8px) scale(1.03) rotateY(2deg)'
                        : isChapterChanging && currentChapter && chapter.id === currentChapter.id
                          ? 'translateX(-8px) scale(0.97) rotateY(-2deg)'
                          : 'translateX(0) scale(1) rotateY(0deg)',
                      boxShadow: currentChapter && chapter.id === currentChapter.id
                        ? '0 8px 25px rgba(66, 153, 225, 0.2), 0 4px 12px rgba(66, 153, 225, 0.15)'
                        : '0 2px 8px rgba(0, 0, 0, 0.05)',
                      borderLeft: currentChapter && chapter.id === currentChapter.id
                        ? '4px solid var(--color-info)'
                        : '4px solid transparent',
                      borderRadius: '12px',
                      perspective: '1000px'
                    }}
                    onClick={async () => {
                      // 防止重复点击当前章节
                      if (currentChapter && chapter.id === currentChapter.id) {
                        return;
                      }

                      try {
                        // 如果当前章节有未保存的更改，先保存
                        if (currentChapter && currentChapterContent !== currentChapter.content) {
                          setIsSaving(true);

                          // 计算字数变化
                          const oldWordCount = currentChapter.wordCount;
                          const newWordCount = currentChapterContent.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length;
                          const wordCountChange = newWordCount - oldWordCount;

                          // 更新章节
                          await chapterRepository.update(currentChapter.id!, {
                            content: currentChapterContent
                          });

                          // 获取更新后的章节
                          const updatedChapter = await chapterRepository.getById(currentChapter.id!);
                          if (updatedChapter) {
                            // 更新章节列表
                            setChapters(prev => prev.map(ch => ch.id === updatedChapter.id ? updatedChapter : ch));

                            // 记录写作统计
                            if (wordCountChange !== 0) {
                              await writingStatsRepository.recordStats(bookId, wordCountChange, 1);
                            }
                          }
                        }

                        // 触发3D翻页动画
                        triggerFlip(chapter.id!, currentChapter?.id || null, chapters);

                        // 确定切换方向
                        const currentIndex = chapters.findIndex(ch => ch.id === (currentChapter?.id || ''));
                        const newIndex = chapters.findIndex(ch => ch.id === chapter.id);
                        setChapterChangeDirection(newIndex > currentIndex ? 'next' : 'prev');

                        // 开始切换动画
                        setIsChapterChanging(true);

                        // 延迟切换章节，让动画有时间显示
                        setTimeout(() => {
                          // 切换到选中的章节
                          console.log('切换到章节:', chapter.title);
                          setCurrentChapter(chapter);
                          setCurrentChapterContent(chapter.content);

                          // 保存当前章节ID到localStorage，供AI改写对话框使用
                          if (chapter.id) {
                            localStorage.setItem(`current-editor-chapter-${bookId}`, chapter.id);
                          }

                          // 结束切换动画
                          setTimeout(() => {
                            setIsChapterChanging(false);
                          }, 300);
                        }, 150);
                      } catch (error) {
                        console.error('章节切换失败', error);
                        setIsChapterChanging(false);
                      } finally {
                        setIsSaving(false);
                      }
                    }}
                  >
                    {/* 章节图标 - 固定宽度 */}
                    <div className="flex-shrink-0">
                      <ChapterBookIcon
                        isActive={!!(currentChapter && chapter.id === currentChapter.id)}
                        isAnimating={!!(isChapterFlipping && currentChapter && chapter.id === currentChapter.id)}
                        animationDirection={flipDirection === 'next' ? 'in' : 'out'}
                        size={28}
                        className="flex-shrink-0"
                      />
                    </div>

                    {/* 章节内容 - 可伸缩区域 */}
                    <div className="flex-1 min-w-0 flex flex-col">
                      <div className="flex items-center gap-2 min-w-0">
                        <div className="font-medium truncate">
                          <HighlightText
                            text={chapter.title}
                            searchQuery={searchQuery}
                          />
                        </div>
                        <div className="flex-shrink-0">
                          <ChapterStatusIndicator
                            chapter={chapter}
                            showIcon={true}
                            iconSize={16}
                          />
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-0.5">
                        <div className="text-xs truncate" style={{ color: 'var(--color-text-hint)' }}>
                          {formatRelativeTime(new Date(chapter.updatedAt))}
                        </div>
                        <div className="text-xs flex-shrink-0 ml-2" style={{ color: 'var(--color-text-hint)' }}>
                          {formatChapterWordCount(chapter)}
                        </div>
                      </div>
                    </div>

                    {/* 删除按钮 - 固定宽度 */}
                    <div className="flex-shrink-0 w-10 flex justify-center">
                      {chapters.length > 1 && (
                        <DeleteChapterButton
                          onClick={(e) => {
                            // 阻止事件冒泡，避免触发章节选择
                            e.stopPropagation();

                            // 打开确认弹窗
                            setChapterToDelete(chapter);
                            setDeleteDialogOpen(true);
                          }}
                          title="删除章节"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
              )}
            </div>

            {/* 写作统计面板 */}
            <div
              className="mt-4 p-3 rounded-lg transition-all duration-200"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(1px)',
                borderTop: '1px solid rgba(0, 0, 0, 0.06)',
                boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.04)'
              }}
            >
              {/* 统计数据 */}
              <div className="space-y-3 mb-3">
                {/* 总字数 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AnimatedSVGIcon
                      type="document"
                      isActive={dashboardData.writingSpeed > 0}
                      progress={calculateRealTimeWordCount() / 100000} // 10万字为满进度
                      style={{ color: 'var(--color-text-secondary)' }}
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      总字数
                    </span>
                    <FloatingDecorativeIcon
                      type="star"
                      size={18}
                      color="var(--color-text-hint)"
                      animated={dashboardData.writingSpeed > 0}
                      floatDirection="up"
                      floatDuration="1.5s"
                      className="opacity-60"
                    />
                  </div>
                  <AnimatedNumber
                    value={calculateRealTimeWordCount()}
                    className="text-sm font-semibold tabular-nums"
                    style={{
                      color: 'var(--color-text-primary)',
                      fontFeatureSettings: '"tnum"'
                    }}
                  />
                </div>

                {/* 今日目标 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AnimatedSVGIcon
                      type="target"
                      isActive={calculateRealTimeDailyWords() > 0}
                      progress={calculateRealTimeDailyWords() / dashboardData.dailyGoal}
                      style={{ color: 'var(--color-text-secondary)' }}
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      今日目标
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1 text-sm font-semibold tabular-nums" style={{ color: 'var(--color-text-primary)' }}>
                      <AnimatedNumber value={calculateRealTimeDailyWords()} />
                      <span>/</span>
                      <span>{dashboardData.dailyGoal.toLocaleString()}</span>
                    </div>
                    <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden mt-1 relative">
                      <div
                        className="h-full rounded-full transition-all duration-1000 ease-out relative"
                        style={{
                          width: `${Math.min(100, Math.max(0, (calculateRealTimeDailyWords() / dashboardData.dailyGoal) * 100))}%`,
                          background: `linear-gradient(90deg,
                            ${(calculateRealTimeDailyWords() / dashboardData.dailyGoal) < 0.3 ? '#EF4444, #F87171' :
                              (calculateRealTimeDailyWords() / dashboardData.dailyGoal) < 0.7 ? '#F59E0B, #FBBF24' :
                              (calculateRealTimeDailyWords() / dashboardData.dailyGoal) < 0.9 ? '#10B981, #34D399' : '#8B5CF6, #A78BFA'})`,
                          boxShadow: '0 0 4px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-shimmer"></div>
                      </div>
                    </div>
                    <div className="text-xs mt-1 opacity-70" style={{ color: 'var(--color-text-secondary)' }}>
                      {Math.round((calculateRealTimeDailyWords() / dashboardData.dailyGoal) * 100)}% 完成
                    </div>
                  </div>
                </div>

                {/* 写作速度 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AnimatedSVGIcon
                      type="lightning"
                      isActive={dashboardData.writingSpeed > 0}
                      speed={dashboardData.writingSpeed}
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      写作速度
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <AnimatedNumber
                        value={dashboardData.writingSpeed}
                        className="text-sm font-semibold tabular-nums"
                        style={{ color: getSpeedStatus(dashboardData.writingSpeed).color }}
                      />
                      <span className="text-xs opacity-70" style={{ color: 'var(--color-text-secondary)' }}>
                        字/分
                      </span>
                    </div>
                    <div className="text-xs opacity-60" style={{ color: getSpeedStatus(dashboardData.writingSpeed).color }}>
                      {getSpeedStatus(dashboardData.writingSpeed).label}
                    </div>
                  </div>
                </div>

                {/* 写作时间 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AnimatedSVGIcon
                      type="clock"
                      isActive={dashboardData.writingSpeed > 0}
                      progress={dashboardData.writingTime > 120 ? 0.9 : 0} // 超过2小时显示专注提醒
                      style={{ color: 'var(--color-text-secondary)' }}
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      写作时间
                    </span>
                  </div>
                  <span
                    className="text-sm font-semibold tabular-nums"
                    style={{
                      color: 'var(--color-text-primary)',
                      fontFeatureSettings: '"tnum"'
                    }}
                  >
                    {formatTime(dashboardData.writingTime)}
                  </span>
                </div>

                {/* 番茄钟 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AnimatedSVGIcon
                      type="pomodoro"
                      isActive={pomodoroTimer.isActive}
                      progress={1 - (pomodoroTimer.timeLeft / (25 * 60))} // 进度从0到1
                      style={{ color: pomodoroTimer.isActive ? '#EF4444' : 'var(--color-text-secondary)' }}
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      番茄钟
                    </span>
                    <FloatingDecorativeIcon
                      type="clock-time"
                      size={18}
                      color={pomodoroTimer.isActive ? '#EF4444' : 'var(--color-text-hint)'}
                      animated={pomodoroTimer.isActive}
                      floatDirection="up"
                      floatDuration="1s"
                      className="opacity-70"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className="text-sm font-mono"
                      style={{
                        color: pomodoroTimer.isActive ? '#EF4444' : 'var(--color-text-primary)',
                        fontFeatureSettings: '"tnum"'
                      }}
                    >
                      {formatPomodoroTime(pomodoroTimer.timeLeft)}
                    </span>
                    <button
                      onClick={togglePomodoro}
                      className="w-6 h-6 rounded-full flex items-center justify-center transition-colors duration-200"
                      style={{
                        backgroundColor: pomodoroTimer.isActive ? '#EF4444' : 'var(--color-primary)',
                        color: 'white'
                      }}
                    >
                      {pomodoroTimer.isActive ? (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                          <rect x="6" y="4" width="4" height="16" />
                          <rect x="14" y="4" width="4" height="16" />
                        </svg>
                      ) : (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                          <polygon points="5,3 19,12 5,21" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* 保存按钮 */}
              <button
                className="w-full py-2 rounded-md text-sm font-medium transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: isSaving || !currentChapter || currentChapterContent === currentChapter.content
                    ? 'rgba(156, 163, 175, 0.6)'
                    : 'var(--color-primary)',
                  color: 'white',
                  border: 'none'
                }}
                disabled={isSaving || !currentChapter || currentChapterContent === currentChapter.content}
                onClick={async () => {
                  if (!currentChapter) return;

                  try {
                    setIsSaving(true);

                    // 计算字数变化
                    const oldWordCount = currentChapter.wordCount;
                    const newWordCount = currentChapterContent.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length;
                    const wordCountChange = newWordCount - oldWordCount;

                    // 更新章节
                    await chapterRepository.update(currentChapter.id!, {
                      content: currentChapterContent
                    });

                    // 获取更新后的章节
                    const updatedChapter = await chapterRepository.getById(currentChapter.id!);
                    if (updatedChapter) {
                      // 更新当前章节
                      setCurrentChapter(updatedChapter);

                      // 更新章节列表
                      setChapters(prev => prev.map(ch => ch.id === updatedChapter.id ? updatedChapter : ch));

                      // 记录写作统计
                      if (wordCountChange !== 0) {
                        await writingStatsRepository.recordStats(bookId, wordCountChange, 1);
                      }

                      // 更新最后保存时间
                      const now = new Date();
                      setLastSavedTime(now);
                      setDashboardData(prev => ({ ...prev, lastSaved: now.toLocaleTimeString('zh-CN') }));
                    }
                  } catch (error) {
                    console.error('保存章节失败', error);
                  } finally {
                    setIsSaving(false);
                  }
                }}
              >
                {isSaving ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>保存中...</span>
                  </div>
                ) : (
                  "保存进度"
                )}
              </button>
            </div>
          </div>
        </div>

        {/* 右侧编辑区 */}
        <div className="flex-1 flex flex-col overflow-hidden relative"
          style={{
            backgroundColor: 'var(--color-white)',
            boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.03)',
            transition: 'all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)',
            transform: isChapterChanging
              ? `translateX(${chapterChangeDirection === 'next' ? '-30px' : '30px'}) scale(0.98)`
              : 'translateX(0) scale(1)',
            opacity: isChapterChanging ? 0.7 : 1,
            filter: isChapterChanging ? 'blur(2px)' : 'blur(0px)'
          }}
        >
          <div className="p-4 rounded-b-lg shadow-sm transition-all duration-300"
            style={{
              borderBottom: `1px solid var(--color-secondary)`,
              backgroundColor: 'rgba(255, 255, 255, 0.95)'
            }}
          >
            <div className="flex items-center">
              <input
                type="text"
                value={currentChapter ? currentChapter.title : ''}
                onChange={async (e) => {
                  if (!currentChapter) return;

                  try {
                    // 更新章节标题
                    await chapterRepository.update(currentChapter.id!, {
                      title: e.target.value
                    });

                    // 获取更新后的章节
                    const updatedChapter = await chapterRepository.getById(currentChapter.id!);
                    if (updatedChapter) {
                      // 更新当前章节
                      setCurrentChapter(updatedChapter);

                      // 更新章节列表
                      setChapters(prev => prev.map(ch => ch.id === updatedChapter.id ? updatedChapter : ch));
                    }
                  } catch (error) {
                    console.error('更新章节标题失败', error);
                  }
                }}
                className="w-full text-xl font-medium border-0 focus:outline-none focus:ring-0 transition-all duration-200"
                placeholder="章节标题"
                style={{ color: 'var(--color-primary)' }}
                disabled={!currentChapter}
              />
              {currentChapter && (
                <div className="text-xs text-gray-400 ml-2 flex items-center opacity-70 hover:opacity-100 transition-opacity duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                  点击编辑
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-6 transition-all duration-300">
            <EnhancedChapterEditor
              ref={editorRef}
              value={currentChapterContent}
              onChange={setCurrentChapterContent}
              onSelect={handleEditorSelection}
              placeholder="开始写作..."
              disabled={!currentChapter || isChapterChanging}
              isChapterChanging={isChapterChanging}
              chapterChangeDirection={chapterChangeDirection}
              style={{
                // 移除硬编码高度，使用组件内部的统一计算
                // height: 'calc(100vh - 250px)' // 已移除
              }}
            />
          </div>

          {/* 当前章节信息 */}
          <div
            className="absolute bottom-4 left-4 p-3 rounded-lg shadow-sm z-10 transition-all duration-300 transform hover:scale-102"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              border: `1px solid var(--color-secondary)`,
              backdropFilter: 'blur(5px)',
              maxWidth: '200px'
            }}
          >
            <div className="flex items-center space-x-2">
              <span className="text-xs opacity-70" style={{ color: 'var(--color-text-secondary)' }}>当前章节:</span>
              <span className="text-sm font-medium truncate" style={{ color: 'var(--color-text-primary)' }}>
                {currentChapter ? currentChapter.title : '无'}
              </span>
            </div>
          </div>

          {/* 选中文本浮动按钮 */}
          {showFloatingButton && (
            <div
              className="fixed z-50 transition-all duration-300"
              style={{
                top: `${floatingButtonPosition.top}px`,
                left: `${floatingButtonPosition.left}px`,
                pointerEvents: 'auto',
                transform: 'translate(-50%, -50%)' // 使按钮居中于计算的位置
              }}
            >
              <button
                className="flex items-center justify-center p-3 rounded-full shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 active:scale-95 transition-all duration-200"
                style={{
                  width: '40px',
                  height: '40px',
                  backdropFilter: 'blur(4px)',
                  border: '2px solid rgba(255, 255, 255, 0.5)'
                }}
                onClick={() => {
                  // 隐藏浮动按钮
                  setShowFloatingButton(false);

                  // 打开AI改写对话框
                  setIsAIRewriteDialogOpen(true);
                }}
                title="AI改写选中内容"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          )}

          {/* AI功能菜单和AI续写按钮 */}
          <div className="absolute bottom-4 right-4 z-20 flex space-x-8">
            {/* AI脑洞按钮 - 位于全文标注按钮旁边 */}
            <div className="relative z-30">
              <CircleButton
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {/* 魔法棒主体 */}
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                          d="M3 21l14-14" />
                    {/* 魔法棒顶端星星 */}
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                          d="M18 3l1.5 1.5L18 6l-1.5-1.5L18 3z"
                          fill="currentColor" />
                    {/* 飞散的星星效果 */}
                    <circle cx="8" cy="16" r="0.5" fill="currentColor" opacity="0.8" />
                    <circle cx="11" cy="13" r="0.5" fill="currentColor" opacity="0.6" />
                    <circle cx="14" cy="10" r="0.5" fill="currentColor" opacity="0.4" />
                    {/* 小星星装饰 */}
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1}
                          d="M6 18l0.5 0.5L6 19l-0.5-0.5L6 18z"
                          opacity="0.7" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1}
                          d="M20 8l0.5 0.5L20 9l-0.5-0.5L20 8z"
                          opacity="0.5" />
                  </svg>
                }
                text="AI脑洞"
                color="#8B5CF6"
                size="medium"
                onClick={() => {
                  // 打开AI脑洞对话框
                  setIsBrainstormDialogOpen(true);
                }}
              />
            </div>

            {/* 全文标注按钮 - 位于AI续写按钮旁边 */}
            <div className="relative z-30">
              <CircleButton
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                }
                text="全文标注"
                color="var(--color-warning)"
                size="medium"
                onClick={() => {
                  // 打开全文标注对话框
                  setIsFullTextAnnotationDialogOpen(true);
                }}
              />
            </div>

            {/* AI续写按钮 - 独立于主AI功能 */}
            <div className="relative z-30">
              <CircleButton
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
                text="AI续写"
                color="var(--color-success)"
                size="medium"
                onClick={() => {
                  // 打开AI续写对话框
                  setIsAIContinueDialogOpen(true);
                }}
              />
            </div>

            {/* AI功能主按钮 */}
            <div
              className={`relative z-30 transition-all duration-500 ${isAIMenuOpen ? 'rotate-45 scale-110' : ''}`}
            >
              <CircleButton
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                }
                color="var(--color-info)"
                size="large"
                onClick={() => setIsAIMenuOpen(!isAIMenuOpen)}
              />
            </div>

            {/* 功能按钮组 - 双层圆弧展开 */}
            <div className="absolute" style={{ bottom: 0, right: 0, width: 350, height: 350, pointerEvents: 'none' }}>
              {/* 内圈：人物、世界观、术语功能 - 使用更大的间距 */}

              {/* 人物功能按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/8) * 120 : 0, // 22.5度位置，内圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/8) * 120 : 0,  // 第一个按钮，内圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.1s' : '0.6s', // 打开时先显示，关闭时后消失
                  transformOrigin: 'bottom right',
                  margin: '20px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  }
                  text="人物"
                  color="var(--color-primary)"
                  size="medium"
                  onClick={() => {
                    setIsAIMenuOpen(false); // 关闭AI菜单
                    setIsCharacterPanelOpen(true); // 打开人物面板
                  }}
                />
              </div>

              {/* 世界观功能按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI*3/8) * 120 : 0, // 67.5度位置，内圈
                  right: isAIMenuOpen ? Math.cos(Math.PI*3/8) * 120 : 0,  // 第三个按钮，内圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.3s' : '0.4s', // 打开时第三个显示，关闭时第三个消失
                  transformOrigin: 'bottom right',
                  margin: '20px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  }
                  text="世界观"
                  color="var(--color-primary)"
                  size="medium"
                  onClick={() => {
                    setIsAIMenuOpen(false); // 关闭AI菜单
                    setIsWorldBuildingPanelOpen(true); // 打开世界观面板
                  }}
                />
              </div>

              {/* 术语功能按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/2) * 120 : 0, // 90度位置，内圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/2) * 120 : 0,  // 第四个按钮，内圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.4s' : '0.3s', // 打开时第四个显示，关闭时第四个消失
                  transformOrigin: 'bottom right',
                  margin: '20px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  }
                  text="术语"
                  color="var(--color-primary)"
                  size="medium"
                  onClick={() => {
                    setIsAIMenuOpen(false); // 关闭AI菜单
                    setIsTerminologyPanelOpen(true); // 打开术语面板
                  }}
                />
              </div>

              {/* 大纲管理按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/4) * 120 : 0, // 45度位置，内圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/4) * 120 : 0,  // 第二个按钮，内圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.2s' : '0.5s', // 打开时第二个显示，关闭时第二个消失
                  transformOrigin: 'bottom right',
                  margin: '20px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  }
                  text="大纲"
                  color="var(--color-primary)"
                  size="medium"
                  onClick={async () => {
                    console.log('点击大纲按钮');
                    setIsAIMenuOpen(false); // 关闭AI菜单

                    // 获取 OutlineManagerProvider 的引用
                    const outlineManagerProvider = document.querySelector('[data-outline-manager-provider="true"]');

                    // 如果找到了 OutlineManagerProvider，触发自定义事件
                    if (outlineManagerProvider) {
                      console.log('找到大纲管理器提供者元素，触发事件');
                      const event = new CustomEvent('openOutlineManager');
                      outlineManagerProvider.dispatchEvent(event);
                    } else {
                      console.error('未找到大纲管理器提供者元素');
                    }

                    // 最后打开大纲管理面板
                    // 这样可以确保在打开面板之前已经触发了事件，初始化了大纲
                    setTimeout(() => {
                      setIsOutlineManagerOpen(true);
                    }, 100);
                  }}
                />
              </div>

              {/* 外圈：AI写作、AI拆书、AI改写功能 */}

              {/* AI写作按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/8) * 180 : 0, // 22.5度位置，外圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/8) * 180 : 0,  // 对应人物位置，外圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.4s' : '0.3s', // 打开时第四个显示，关闭时第四个消失
                  transformOrigin: 'bottom right',
                  margin: '18px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  }
                  text="AI写作"
                  color="var(--color-secondary)"
                  size="medium"
                  onClick={() => {
                    setIsAIMenuOpen(false);
                    setIsAIWritingDialogOpen(true);
                  }}
                />
              </div>

              {/* AI拆书按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/4) * 180 : 0, // 45度位置，外圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/4) * 180 : 0,  // 对应大纲位置，外圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.5s' : '0.2s', // 打开时第五个显示，关闭时第五个消失
                  transformOrigin: 'bottom right',
                  margin: '18px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  }
                  text="AI拆书"
                  color="var(--color-secondary)"
                  size="medium"
                  onClick={() => {
                    // 关闭AI菜单
                    setIsAIMenuOpen(false);

                    // 打开AI拆书对话框
                    setIsAIBookAnalysisDialogOpen(true);
                  }}
                />
              </div>

              {/* AI改写按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI*3/8) * 180 : 0, // 67.5度位置，外圈
                  right: isAIMenuOpen ? Math.cos(Math.PI*3/8) * 180 : 0,  // 对应世界观位置，外圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.6s' : '0.1s', // 打开时最后显示，关闭时最先消失
                  transformOrigin: 'bottom right',
                  margin: '18px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  }
                  text="AI改写"
                  color="var(--color-secondary)"
                  size="medium"
                  onClick={() => {
                    // 关闭AI菜单
                    setIsAIMenuOpen(false);

                    // 获取当前选中的文本
                    const editor = editorRef.current?.getTextarea();
                    if (editor) {
                      const start = editor.selectionStart;
                      const end = editor.selectionEnd;

                      // 如果有选中文本
                      if (start !== end) {
                        const selected = currentChapterContent.substring(start, end);

                        // 保存选中文本
                        setSelectedText(selected);

                        // 获取当前章节内的上下文
                        const localBeforeContext = currentChapterContent.substring(Math.max(0, start - 500), start);
                        const localAfterContext = currentChapterContent.substring(end, Math.min(currentChapterContent.length, end + 500));

                        // 获取跨章节上下文和章节信息
                        const {
                          beforeContext: crossBeforeContext,
                          afterContext: crossAfterContext,
                          previousChapters,
                          nextChapters
                        } = getCrossChapterContext();

                        // 根据章节位置智能处理上下文
                        let combinedBeforeContext = localBeforeContext;
                        let combinedAfterContext = localAfterContext;

                        if (previousChapters.length > 0 && nextChapters.length === 0) {
                          // 当前章节是最后一章，将前章节内容全部放到前文上下文中
                          combinedBeforeContext = crossBeforeContext ?
                            `${crossBeforeContext}\n\n${localBeforeContext}` : localBeforeContext;
                        } else if (previousChapters.length === 0 && nextChapters.length > 0) {
                          // 当前章节是第一章，将后章节内容全部放到后文上下文中
                          combinedAfterContext = crossAfterContext ?
                            `${localAfterContext}\n\n${crossAfterContext}` : localAfterContext;
                        } else if (previousChapters.length > 0 && nextChapters.length > 0) {
                          // 当前章节在中间，前后都有章节
                          combinedBeforeContext = crossBeforeContext ?
                            `${crossBeforeContext}\n\n${localBeforeContext}` : localBeforeContext;

                          combinedAfterContext = crossAfterContext ?
                            `${localAfterContext}\n\n${crossAfterContext}` : localAfterContext;
                        }

                        // 设置合并后的上下文
                        setBeforeContext(combinedBeforeContext);
                        setAfterContext(combinedAfterContext);

                        // 打开AI改写对话框
                        setIsAIRewriteDialogOpen(true);
                      } else {
                        // 如果没有选中文本，提示用户
                        alert('请先选择要改写的文本');
                      }
                    }
                  }}
                />
              </div>

              {/* AI短篇创作按钮 */}
              <div
                className="absolute z-20 transition-all duration-500"
                style={{
                  bottom: isAIMenuOpen ? Math.sin(Math.PI/2) * 180 : 0, // 90度位置，外圈
                  right: isAIMenuOpen ? Math.cos(Math.PI/2) * 180 : 0,  // 对应术语位置，外圈
                  opacity: isAIMenuOpen ? 1 : 0,
                  transform: isAIMenuOpen ? 'scale(1)' : 'scale(0.5)',
                  pointerEvents: isAIMenuOpen ? 'auto' : 'none',
                  transitionDelay: isAIMenuOpen ? '0.7s' : '0.0s', // 打开时最后显示，关闭时最先消失
                  transformOrigin: 'bottom right',
                  margin: '18px'
                }}
              >
                <CircleButton
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                  }
                  text="AI短篇"
                  color="var(--color-warning)"
                  size="medium"
                  onClick={() => {
                    // 关闭AI菜单
                    setIsAIMenuOpen(false);
                    // 打开AI短篇创作对话框
                    setIsAIShortStoryDialogOpen(true);
                  }}
                />
              </div>
            </div>

            {/* 背景遮罩 - 始终存在但透明度变化 */}
            <div
              className="fixed inset-0 z-10 transition-opacity duration-500"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(2px)',
                opacity: isAIMenuOpen ? 1 : 0,
                pointerEvents: isAIMenuOpen ? 'auto' : 'none'
              }}
              onClick={() => setIsAIMenuOpen(false)}
            />
          </div>
        </div>
      </div>

      {/* 人物面板 */}
      <CharacterPanelAdapter
        bookId={bookId}
        isOpen={isCharacterPanelOpen}
        onClose={() => setIsCharacterPanelOpen(false)}
      />

      {/* 世界观面板 */}
      <WorldBuildingPanelAdapter
        bookId={bookId}
        isOpen={isWorldBuildingPanelOpen}
        onClose={() => setIsWorldBuildingPanelOpen(false)}
      />

      {/* 术语面板 */}
      <TerminologyPanelAdapter
        bookId={bookId}
        isOpen={isTerminologyPanelOpen}
        onClose={() => setIsTerminologyPanelOpen(false)}
      />

      {/* 大纲管理面板 */}
      <div
        data-outline-manager-provider="true"
        ref={(el) => {
          // 当元素挂载时，添加自定义事件监听器
          if (el) {
            console.log('大纲管理器提供者元素挂载');

            // 移除旧的事件监听器（如果有）
            el.removeEventListener('openOutlineManager', () => {
              console.log('移除旧的openOutlineManager事件监听器');
            });

            // 添加新的事件监听器
            el.addEventListener('openOutlineManager', () => {
              console.log('触发openOutlineManager事件');
            });
          }
        }}
      >
        <OutlineManagerProvider bookId={bookId}>
          <OutlineManagerDialog
            isOpen={isOutlineManagerOpen}
            onClose={() => setIsOutlineManagerOpen(false)}
            bookId={bookId}
            bookTitle={book?.title}
          />
        </OutlineManagerProvider>
      </div>

      {/* AI写作面板 */}
      {isAIWritingDialogOpen && (
        createAIWritingAdapter().createAIWritingDialog({
          isOpen: isAIWritingDialogOpen,
          onClose: () => setIsAIWritingDialogOpen(false),
          onInsertContent: handleInsertAIWritingContent,
          bookId: bookId
        })
      )}

      {/* AI改写面板 */}
      {isAIRewriteDialogOpen && (
        createAIRewriteAdapter().createAIRewriteDialog({
          isOpen: isAIRewriteDialogOpen,
          onClose: () => setIsAIRewriteDialogOpen(false),
          selectedText: selectedText,
          beforeContext: beforeContext,
          afterContext: afterContext,
          onRewritten: handleReplaceWithRewrittenContent,
          bookId: bookId
        })
      )}

      {/* AI续写面板 */}
      {isAIContinueDialogOpen && (
        createAIContinuePanelAdapter().createAIContinueDialog({
          isOpen: isAIContinueDialogOpen,
          onClose: () => setIsAIContinueDialogOpen(false),
          onInsertContent: handleInsertAIWritingContent,
          initialContext: getContextForContinue().contextString,
          initialContextMessages: getContextForContinue().contextMessages,
          bookId: bookId,
          currentChapterId: currentChapter?.id // 传递当前章节ID，用于禁用当前章节的选择
        })
      )}

      {/* AI拆书面板 - 覆盖式全屏界面 */}
      {isAIBookAnalysisDialogOpen && (
        <div className="fixed inset-0 z-50 bg-white overflow-hidden flex flex-col" style={{ height: '100vh', width: '100vw' }}>
          {/* 顶部导航栏 */}
          <div className="flex justify-between items-center p-4 border-b bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div className="flex items-center">
              <button
                className="mr-4 text-white hover:text-gray-200 transition-colors"
                onClick={() => setIsAIBookAnalysisDialogOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
              </button>
              <h2 className="text-xl font-bold">AI拆书</h2>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm opacity-80">当前书籍: {book?.title || '未选择'}</span>
            </div>
          </div>

          {/* 主体内容区 - 占满剩余空间 */}
          <div className="flex-1 overflow-auto">
            <AIBookAnalysisAdapter
              onAnalysisComplete={(result) => {
                // 可以选择将分析结果插入到编辑器中，或者其他处理方式
                console.log('AI拆书分析完成:', result);
                // 不要自动关闭，让用户查看结果
                // setIsAIBookAnalysisDialogOpen(false);
              }}
            />
          </div>
        </div>
      )}

      {/* 全文标注对话框 */}
      {isFullTextAnnotationDialogOpen && (
        <FullTextAnnotationDialog
          isOpen={isFullTextAnnotationDialogOpen}
          onClose={() => setIsFullTextAnnotationDialogOpen(false)}
          bookId={bookId}
          chapterContent={currentChapterContent}
          chapterTitle={currentChapter?.title || '当前章节'}
          onExport={(modifiedText) => {
            // 将修改后的文本替换到编辑器中
            setCurrentChapterContent(modifiedText);
            console.log('全文标注完成，文本已更新');
          }}
        />
      )}

      {/* AI脑洞对话框 */}
      <BrainstormDialog
        isOpen={isBrainstormDialogOpen}
        onClose={() => setIsBrainstormDialogOpen(false)}
        bookId={bookId}
        chapterContent={currentChapterContent}
        onBrainstormComplete={(result) => {
          console.log('AI脑洞生成完成:', result);
          // 这里可以处理生成的结果，比如插入到编辑器中
        }}
      />

      {/* AI短篇创作工作区对话框 */}
      <ShortStoryWorkspaceDialog
        isOpen={isAIShortStoryDialogOpen}
        onClose={() => setIsAIShortStoryDialogOpen(false)}
        bookId={bookId}
        onShortStoryGenerated={(content) => {
          console.log('AI短篇创作完成:', content);
          // 将生成的短篇内容插入到编辑器中
          handleInsertAIWritingContent(content);
        }}
      />
    </main>
  );



  // 删除旧的段落计算函数，将使用 NovelContextBuilder 替代

  // 获取续写上下文消息（使用新的 NovelContextBuilder）
  function getContextForContinue(): { contextMessages: any[], contextString: string } {
    // 获取编辑器元素
    const editor = editorRef.current?.getTextarea();
    if (!editor || !currentChapter) {
      console.log('getContextForContinue: 编辑器或章节不存在', { hasEditor: !!editor, hasChapter: !!currentChapter });
      return { contextMessages: [], contextString: '' };
    }

    // 获取当前光标位置
    const cursorPosition = editor.selectionStart;

    // 添加调试信息
    console.log('编辑器数据检查:', {
      hasEditor: !!editor,
      cursorPosition,
      contentLength: currentChapterContent.length,
      chapterTitle: currentChapter?.title,
      chapterOrder: currentChapter?.order,
      editorValue: editor.value?.length || 0,
      editorValuePreview: editor.value?.substring(0, 100) || '空'
    });

    // 准备章节数据
    const allChapters = chapters.map(ch => ({
      id: ch.id,
      title: ch.title,
      order: ch.order,
      content: ch.content || ''
    }));

    // 优先使用编辑器的实际值，如果编辑器有内容但状态为空
    const actualContent = editor.value && editor.value.length > 0 ? editor.value : currentChapterContent;

    const currentChapterData = {
      id: currentChapter.id,
      title: currentChapter.title,
      order: currentChapter.order,
      content: actualContent
    };

    console.log('最终使用的内容:', {
      useEditorValue: editor.value && editor.value.length > 0 && currentChapterContent.length === 0,
      finalContentLength: actualContent.length,
      finalContentPreview: actualContent.substring(0, 100) || '空'
    });

    // 使用 NovelContextBuilder 构建上下文
    try {
      const { NovelContextBuilder } = require('@/utils/context/NovelContextBuilder');
      const contextBuilder = new NovelContextBuilder(
        allChapters,
        currentChapterData,
        cursorPosition
      );

      const contextMessages = contextBuilder.buildContextMessages();
      const contextString = contextBuilder.buildContextString();

      return { contextMessages, contextString };
    } catch (error) {
      console.error('构建上下文失败:', error);

      // 降级处理：返回简单的上下文
      const beforeContext = currentChapterContent.substring(0, cursorPosition);
      const afterContext = currentChapterContent.substring(cursorPosition);

      const fallbackMessages = [];
      if (beforeContext) {
        fallbackMessages.push({
          role: 'user',
          content: `【当前章节前文】\n${beforeContext}`
        });
      }
      if (afterContext) {
        fallbackMessages.push({
          role: 'user',
          content: `【当前章节后文】\n${afterContext}`
        });
      }

      return {
        contextMessages: fallbackMessages,
        contextString: fallbackMessages.map(msg => msg.content).join('\n\n')
      };
    }
  }

  // 处理AI写作内容插入
  function handleInsertAIWritingContent(content: string) {
    if (!content || !currentChapter) return;

    // 获取编辑器元素
    const editor = editorRef.current?.getTextarea();
    if (!editor) {
      // 如果找不到编辑器元素，直接在内容末尾添加
      const currentContent = currentChapterContent;
      const newContent = currentContent + (currentContent.endsWith('\n\n') ? '' : '\n\n') + content;
      setCurrentChapterContent(newContent);
      return;
    }

    // 获取当前光标位置
    const start = editor.selectionStart;
    const end = editor.selectionEnd;

    // 使用可撤销的方式插入内容
    editor.focus();
    editor.setSelectionRange(start, end);

    // 准备要插入的内容（如果在段落开头，可能需要添加额外的换行）
    const contentToInsert = content;

    // 使用document.execCommand实现可撤销的插入
    // 这会将操作添加到浏览器的撤销/重做历史栈中
    try {
      if (document.execCommand('insertText', false, contentToInsert)) {
        console.log('使用execCommand插入内容成功');
      } else {
        console.warn('execCommand不可用，回退到直接修改内容');
        // 回退到直接修改内容的方式
        const newContent =
          currentChapterContent.substring(0, start) +
          contentToInsert +
          currentChapterContent.substring(end);

        // 更新编辑器内容
        setCurrentChapterContent(newContent);

        // 更新光标位置
        setTimeout(() => {
          editor.focus();
          editor.selectionStart = start + contentToInsert.length;
          editor.selectionEnd = start + contentToInsert.length;
        }, 0);
      }
    } catch (error) {
      console.error('execCommand执行失败:', error);
      // 回退到直接修改内容的方式
      const newContent =
        currentChapterContent.substring(0, start) +
        contentToInsert +
        currentChapterContent.substring(end);

      // 更新编辑器内容
      setCurrentChapterContent(newContent);

      // 更新光标位置
      setTimeout(() => {
        editor.focus();
        editor.selectionStart = start + contentToInsert.length;
        editor.selectionEnd = start + contentToInsert.length;
      }, 0);
    }
  }

  // 处理AI改写内容替换
  function handleReplaceWithRewrittenContent(content: string) {
    if (!content || !currentChapter) return;

    // 获取编辑器元素
    const editor = editorRef.current?.getTextarea();
    if (!editor) {
      // 如果找不到编辑器元素，无法替换选中内容
      console.error('找不到编辑器元素，无法替换选中内容');
      return;
    }

    // 获取当前选中位置
    const start = editor.selectionStart;
    const end = editor.selectionEnd;

    // 如果没有选中内容，使用之前保存的选中内容位置
    const selectionStart = (start === end && selectedText) ? currentChapterContent.indexOf(selectedText) : start;
    const selectionEnd = (start === end && selectedText) ? selectionStart + selectedText.length : end;

    if (selectionStart === -1 || selectionStart === selectionEnd) {
      console.warn('无法确定要替换的文本位置');

      // 使用可撤销的方式插入内容
      editor.focus();
      editor.setSelectionRange(start, end);

      // 使用document.execCommand实现可撤销的插入
      // 这会将操作添加到浏览器的撤销/重做历史栈中
      if (document.execCommand('insertText', false, content)) {
        console.log('使用execCommand插入内容成功');
      } else {
        console.warn('execCommand不可用，回退到直接修改内容');
        // 回退到直接修改内容的方式
        const newContent =
          currentChapterContent.substring(0, start) +
          content +
          currentChapterContent.substring(end);
        setCurrentChapterContent(newContent);
      }

      return;
    }

    // 使用可撤销的方式替换选中内容
    editor.focus();
    editor.setSelectionRange(selectionStart, selectionEnd);

    // 使用document.execCommand实现可撤销的替换
    // 这会将操作添加到浏览器的撤销/重做历史栈中
    if (document.execCommand('insertText', false, content)) {
      console.log('使用execCommand替换内容成功');
    } else {
      console.warn('execCommand不可用，回退到直接修改内容');
      // 回退到直接修改内容的方式
      const newContent =
        currentChapterContent.substring(0, selectionStart) +
        content +
        currentChapterContent.substring(selectionEnd);
      setCurrentChapterContent(newContent);

      // 更新光标位置
      setTimeout(() => {
        editor.focus();
        editor.selectionStart = selectionStart + content.length;
        editor.selectionEnd = selectionStart + content.length;
      }, 0);
    }
  }

  /**
   * 增强版跨章节上下文获取函数（无限制版本）
   * @param options 配置选项
   * @returns 包含前后章节上下文的对象
   */
  function getCrossChapterContextEnhanced(options: {
    mode: 'none' | 'adjacent' | 'all';
    maxChapters: number;
    segmentSize: number;
  }): {
    beforeContext: string;
    afterContext: string;
  } {
    if (options.mode === 'none' || !currentChapter || !chapters || chapters.length <= 1) {
      return { beforeContext: '', afterContext: '' };
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 0;
      const orderB = b.order !== undefined ? b.order : 0;
      return orderA - orderB;
    });

    const currentIndex = sortedChapters.findIndex(ch => ch.id === currentChapter.id);
    if (currentIndex === -1) {
      return { beforeContext: '', afterContext: '' };
    }

    let beforeContext = '';
    let afterContext = '';

    if (options.mode === 'adjacent') {
      // 只获取相邻章节的完整内容
      if (currentIndex > 0) {
        const prevChapter = sortedChapters[currentIndex - 1];
        const content = prevChapter.content || '';
        if (content) {
          const chapterIndex = prevChapter.order !== undefined ? prevChapter.order + 1 : '?';
          const prevChapterTitle = prevChapter.title || `第${chapterIndex}章`;

          // 简化处理：直接使用章节内容
          beforeContext = `【${prevChapterTitle}的完整内容】\n${content}`;
        }
      }
      if (currentIndex < sortedChapters.length - 1) {
        const nextChapter = sortedChapters[currentIndex + 1];
        const content = nextChapter.content || '';
        if (content) {
          const chapterIndex = nextChapter.order !== undefined ? nextChapter.order + 1 : '?';
          const nextChapterTitle = nextChapter.title || `第${chapterIndex}章`;

          // 简化处理：直接使用章节内容
          afterContext = `【${nextChapterTitle}的完整内容】\n${content}`;
        }
      }
    } else if (options.mode === 'all') {
      // 获取所有章节的完整内容（无限制）
      const beforeChapters = sortedChapters.slice(0, currentIndex);
      const afterChapters = sortedChapters.slice(currentIndex + 1);

      // 构建前文上下文 - 获取所有前面章节的完整内容
      if (beforeChapters.length > 0) {
        const beforeContexts = beforeChapters.map(ch => {
          const content = ch.content || '';
          if (content) {
            const chapterIndex = ch.order !== undefined ? ch.order + 1 : '?';
            const chapterTitle = ch.title || `第${chapterIndex}章`;

            // 简化处理：直接使用章节内容
            return `【${chapterTitle}的完整内容】\n${content}`;
          }
          return '';
        }).filter(c => c);
        beforeContext = beforeContexts.join('\n\n');
      }

      // 构建后文上下文 - 获取所有后面章节的完整内容
      if (afterChapters.length > 0) {
        const afterContexts = afterChapters.map(ch => {
          const content = ch.content || '';
          if (content) {
            const chapterIndex = ch.order !== undefined ? ch.order + 1 : '?';
            const chapterTitle = ch.title || `第${chapterIndex}章`;

            // 简化处理：直接使用章节内容
            return `【${chapterTitle}的完整内容】\n${content}`;
          }
          return '';
        }).filter(c => c);
        afterContext = afterContexts.join('\n\n');
      }
    }

    return { beforeContext, afterContext };
  }

  /**
   * 获取章节结尾的分段内容（用于350字分段）
   * @param content 章节内容
   * @param segmentSize 分段大小
   * @returns 章节结尾的分段内容
   */
  function getChapterEndSegments(content: string, segmentSize: number): string {
    if (!content) return '';

    // 使用现有的 segmentText 函数进行350字分段
    const segments = segmentText(content, segmentSize, true);

    // 返回最后几个段落（总共不超过1000字）
    let result = '';
    let totalChars = 0;

    for (let i = segments.length - 1; i >= 0; i--) {
      if (totalChars + segments[i].length > 1000) break;
      result = segments[i] + '\n\n' + result;
      totalChars += segments[i].length;
    }

    return result.trim();
  }

  /**
   * 获取章节开头的分段内容（用于350字分段）
   * @param content 章节内容
   * @param segmentSize 分段大小
   * @returns 章节开头的分段内容
   */
  function getChapterStartSegments(content: string, segmentSize: number): string {
    if (!content) return '';

    // 使用现有的 segmentText 函数进行350字分段
    const segments = segmentText(content, segmentSize, true);

    // 返回前几个段落（总共不超过1000字）
    let result = '';
    let totalChars = 0;

    for (const segment of segments) {
      if (totalChars + segment.length > 1000) break;
      result += segment + '\n\n';
      totalChars += segment.length;
    }

    return result.trim();
  }

  /**
   * 获取当前章节的前后章节上下文（保留原函数以兼容现有代码）
   * @returns 包含前后章节上下文的对象和章节信息
   */
  function getCrossChapterContext(): {
    beforeContext: string,
    afterContext: string,
    previousChapters: Chapter[],
    nextChapters: Chapter[],
    currentChapterIndex: number,
    totalChapters: number
  } {
    if (!currentChapter || !chapters || chapters.length <= 1) {
      return {
        beforeContext: '',
        afterContext: '',
        previousChapters: [],
        nextChapters: [],
        currentChapterIndex: 0,
        totalChapters: chapters?.length || 0
      };
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 0;
      const orderB = b.order !== undefined ? b.order : 0;
      return orderA - orderB;
    });

    // 找出当前章节在所有章节中的位置
    const currentIndex = sortedChapters.findIndex(chapter => chapter.id === currentChapter.id);
    if (currentIndex === -1) {
      return {
        beforeContext: '',
        afterContext: '',
        previousChapters: [],
        nextChapters: [],
        currentChapterIndex: 0,
        totalChapters: sortedChapters.length
      };
    }

    // 获取前后章节
    const previousChapters = currentIndex > 0 ? sortedChapters.slice(0, currentIndex) : [];
    const nextChapters = currentIndex < sortedChapters.length - 1 ? sortedChapters.slice(currentIndex + 1) : [];

    let crossBeforeContext = '';
    let crossAfterContext = '';

    // 根据章节位置智能处理上下文
    if (previousChapters.length > 0 && nextChapters.length === 0) {
      // 当前章节是最后一章，将前章节内容全部放到前文上下文中
      const previousChapter = previousChapters[previousChapters.length - 1];
      const previousChapterText = previousChapter.content || '';

      if (previousChapterText) {
        const chapterIndex = previousChapter.order !== undefined ? previousChapter.order + 1 : '?';
        const previousChapterTitle = previousChapter.title || `第${chapterIndex}章`;
        crossBeforeContext = `【${previousChapterTitle}的全部内容】\n${previousChapterText}`;
      }
    } else if (previousChapters.length === 0 && nextChapters.length > 0) {
      // 当前章节是第一章，将后章节内容全部放到后文上下文中
      const nextChapter = nextChapters[0];
      const nextChapterText = nextChapter.content || '';

      if (nextChapterText) {
        const chapterIndex = nextChapter.order !== undefined ? nextChapter.order + 1 : '?';
        const nextChapterTitle = nextChapter.title || `第${chapterIndex}章`;
        crossAfterContext = `【${nextChapterTitle}的全部内容】\n${nextChapterText}`;
      }
    } else if (previousChapters.length > 0 && nextChapters.length > 0) {
      // 当前章节在中间，提取前后章节的关键部分
      const previousChapter = previousChapters[previousChapters.length - 1];
      const previousChapterText = previousChapter.content || '';

      if (previousChapterText) {
        const lastSegments = extractLastSentences(previousChapterText, 1); // 提取最后1段（10句话）
        const chapterIndex = previousChapter.order !== undefined ? previousChapter.order + 1 : '?';
        const previousChapterTitle = previousChapter.title || `第${chapterIndex}章`;
        crossBeforeContext = `【${previousChapterTitle}的结尾】\n${lastSegments}`;
      }

      const nextChapter = nextChapters[0];
      const nextChapterText = nextChapter.content || '';

      if (nextChapterText) {
        const firstSegments = extractFirstSentences(nextChapterText, 1); // 提取前1段（10句话）
        const chapterIndex = nextChapter.order !== undefined ? nextChapter.order + 1 : '?';
        const nextChapterTitle = nextChapter.title || `第${chapterIndex}章`;
        crossAfterContext = `【${nextChapterTitle}的开头】\n${firstSegments}`;
      }
    }

    return {
      beforeContext: crossBeforeContext,
      afterContext: crossAfterContext,
      previousChapters,
      nextChapters,
      currentChapterIndex: currentIndex,
      totalChapters: sortedChapters.length
    };
  }

  /**
   * 提取文本的最后几段（改为10句一段的分割方式）
   * @param text 要提取的文本
   * @param segmentCount 要提取的段落数量
   * @returns 提取的段落内容
   */
  function extractLastSentences(text: string, segmentCount: number = 1): string {
    if (!text) return '';

    // 按句子分割文本
    const sentences = text.match(/[^.!?。！？]+[.!?。！？]+/g) || [text];

    // 如果句子数量小于10句，返回整个文本
    if (sentences.length <= 10) return text;

    // 每10句组成一段
    const sentencesPerSegment = 10;
    const segments: string[] = [];

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      const segmentText = segmentSentences.join('').trim();

      if (segmentText) {
        segments.push(segmentText);
      }
    }

    // 提取最后segmentCount个段落
    return segments.slice(-segmentCount).join('\n\n');
  }

  /**
   * 提取文本的前几段（改为10句一段的分割方式）
   * @param text 要提取的文本
   * @param segmentCount 要提取的段落数量
   * @returns 提取的段落内容
   */
  function extractFirstSentences(text: string, segmentCount: number = 1): string {
    if (!text) return '';

    // 按句子分割文本
    const sentences = text.match(/[^.!?。！？]+[.!?。！？]+/g) || [text];

    // 如果句子数量小于10句，返回整个文本
    if (sentences.length <= 10) return text;

    // 每10句组成一段
    const sentencesPerSegment = 10;
    const segments: string[] = [];

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      const segmentText = segmentSentences.join('').trim();

      if (segmentText) {
        segments.push(segmentText);
      }
    }

    // 提取前segmentCount个段落
    return segments.slice(0, segmentCount).join('\n\n');
  }

  // 处理编辑器选中文本
  function handleEditorSelection() {
    const editor = editorRef.current?.getTextarea();
    if (!editor) return;

    const start = editor.selectionStart;
    const end = editor.selectionEnd;

    // 如果有选中文本
    if (start !== end) {
      const selected = currentChapterContent.substring(start, end);

      // 保存选中文本和上下文
      setSelectedText(selected);

      // 获取当前章节内的上下文
      const localBeforeContext = currentChapterContent.substring(Math.max(0, start - 500), start);
      const localAfterContext = currentChapterContent.substring(end, Math.min(currentChapterContent.length, end + 500));

      // 获取跨章节上下文和章节信息
      const {
        beforeContext: crossBeforeContext,
        afterContext: crossAfterContext,
        previousChapters,
        nextChapters
      } = getCrossChapterContext();

      // 根据章节位置智能处理上下文
      let combinedBeforeContext = localBeforeContext;
      let combinedAfterContext = localAfterContext;

      if (previousChapters.length > 0 && nextChapters.length === 0) {
        // 当前章节是最后一章，将前章节内容全部放到前文上下文中
        combinedBeforeContext = crossBeforeContext ?
          `${crossBeforeContext}\n\n${localBeforeContext}` : localBeforeContext;
      } else if (previousChapters.length === 0 && nextChapters.length > 0) {
        // 当前章节是第一章，将后章节内容全部放到后文上下文中
        combinedAfterContext = crossAfterContext ?
          `${localAfterContext}\n\n${crossAfterContext}` : localAfterContext;
      } else if (previousChapters.length > 0 && nextChapters.length > 0) {
        // 当前章节在中间，前后都有章节
        combinedBeforeContext = crossBeforeContext ?
          `${crossBeforeContext}\n\n${localBeforeContext}` : localBeforeContext;

        combinedAfterContext = crossAfterContext ?
          `${localAfterContext}\n\n${crossAfterContext}` : localAfterContext;
      }

      // 设置合并后的上下文
      setBeforeContext(combinedBeforeContext);
      setAfterContext(combinedAfterContext);

      // 获取编辑器的位置和尺寸
      const editorRect = editor.getBoundingClientRect();

      // 使用更简单的方法：将按钮放在编辑器的中心位置
      // 这样用户总能看到按钮，而不必担心精确定位
      const verticalPosition = editorRect.top + (editorRect.height / 2);
      const horizontalPosition = editorRect.left + (editorRect.width / 2);

      // 设置浮动按钮位置
      setFloatingButtonPosition({
        top: verticalPosition,
        left: horizontalPosition
      });

      // 显示浮动按钮
      setShowFloatingButton(true);
    } else {
      // 如果没有选中文本，隐藏浮动按钮
      setShowFloatingButton(false);
    }
  }
}
