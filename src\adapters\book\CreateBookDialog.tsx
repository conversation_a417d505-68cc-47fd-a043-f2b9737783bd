"use client";

import React, { useEffect } from 'react';
import { createBookFactory } from '@/factories/book/BookFactory';

interface CreateBookDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
  onCreateBook?: (title: string, description: string) => void;
}

/**
 * 创建书籍对话框适配器组件
 */
const CreateBookDialog: React.FC<CreateBookDialogProps> = ({
  isOpen = false,
  onClose,
  onCreateBook
}) => {
  // 创建书籍工厂和组件
  const bookFactory = createBookFactory();
  const createBookDialog = bookFactory.createBookDialogComponent();
  
  // 设置初始状态
  useEffect(() => {
    createBookDialog.setIsOpen(isOpen);
  }, [isOpen]);
  
  // 设置回调
  useEffect(() => {
    if (onCreateBook) {
      createBookDialog.onCreateBook(onCreateBook);
    }
    
    if (onClose) {
      createBookDialog.onCancel(onClose);
    }
  }, [onCreateBook, onClose]);
  
  return <>{createBookDialog.render()}</>;
};

export default CreateBookDialog;
