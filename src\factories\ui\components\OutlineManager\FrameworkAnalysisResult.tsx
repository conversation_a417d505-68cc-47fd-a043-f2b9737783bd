"use client";

import React, { useState } from 'react';
import EnhancedMarkdown from '@/components/EnhancedMarkdown';

interface FrameworkAnalysisResultProps {
  result: any; // OutlineFrameworkResult 类型
  onSave: (framework: any) => void;
  onExport: (format: 'json' | 'markdown' | 'pdf') => void;
  onClose: () => void;
  onStartNew: () => void;
}

type TabType = 'plot' | 'dialogue' | 'style';

const FrameworkAnalysisResult: React.FC<FrameworkAnalysisResultProps> = ({
  result,
  onSave,
  onExport,
  onClose,
  onStartNew
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('plot');

  const tabs = [
    { id: 'plot', label: '情节分析', icon: '📊' },
    { id: 'dialogue', label: '对话分析', icon: '💬' },
    { id: 'style', label: '风格分析', icon: '🎨' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'plot':
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-3">故事结构</h4>
              <div className="text-blue-700">
                <EnhancedMarkdown
                  content={result.plotAnalysis?.storyStructure || '分析中...'}
                  className="prose prose-sm max-w-none prose-blue"
                />
              </div>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-3">冲突设计</h4>
              <div className="text-green-700">
                <EnhancedMarkdown
                  content={result.plotAnalysis?.conflictDesign || '分析中...'}
                  className="prose prose-sm max-w-none prose-green"
                />
              </div>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-semibold text-purple-800 mb-3">节奏控制</h4>
              <div className="text-purple-700">
                <EnhancedMarkdown
                  content={result.plotAnalysis?.rhythmControl || '分析中...'}
                  className="prose prose-sm max-w-none prose-purple"
                />
              </div>
            </div>

            {/* 新增：具体剧情点 */}
            {result.plotAnalysis?.plotPoints && result.plotAnalysis.plotPoints.length > 0 && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <h4 className="font-semibold text-amber-800 mb-3">🎯 具体剧情点</h4>
                <div className="space-y-2">
                  {result.plotAnalysis.plotPoints.map((point: string, index: number) => (
                    <div key={index} className="flex items-start space-x-2">
                      <span className="text-amber-600 font-medium min-w-[20px]">{index + 1}.</span>
                      <span className="text-amber-700">{point}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：剧情点写作指导 */}
            {result.plotAnalysis?.plotPointsWithGuidance && result.plotAnalysis.plotPointsWithGuidance.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-800 mb-3">📝 剧情点写作指导</h4>
                <div className="space-y-4">
                  {result.plotAnalysis.plotPointsWithGuidance.map((guidancePoint: any, index: number) => (
                    <div key={index} className="border border-yellow-300 rounded-lg p-3 bg-white">
                      <div className="flex items-start space-x-2 mb-2">
                        <span className="flex-shrink-0 w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </span>
                        <div className="flex-1">
                          <div className="text-sm text-gray-800 font-medium mb-1">
                            {guidancePoint.content}
                          </div>
                        </div>
                      </div>

                      {guidancePoint.specificDescription && (
                        <div className="ml-8 mb-2">
                          <div className="text-xs font-medium text-blue-700 mb-1">📖 具体描写特征：</div>
                          <div className="text-xs text-blue-600 bg-blue-50 border border-blue-200 rounded px-2 py-1">
                            {guidancePoint.specificDescription}
                          </div>
                        </div>
                      )}

                      {guidancePoint.avoidanceGuidance && (
                        <div className="ml-8">
                          <div className="text-xs font-medium text-red-700 mb-1">⚠️ 写作指导：</div>
                          <div className="text-xs text-red-600 bg-red-50 border border-red-200 rounded px-2 py-1">
                            {guidancePoint.avoidanceGuidance}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：行为框架模式 */}
            {result.plotAnalysis?.behaviorFrameworks && result.plotAnalysis.behaviorFrameworks.length > 0 && (
              <div className="bg-rose-50 border border-rose-200 rounded-lg p-4">
                <h4 className="font-semibold text-rose-800 mb-3">🎭 行为框架模式</h4>
                <div className="flex flex-wrap gap-2">
                  {result.plotAnalysis.behaviorFrameworks.map((framework: string, index: number) => (
                    <span key={index} className="bg-rose-100 text-rose-700 px-3 py-1 rounded-full text-sm">
                      {framework}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'dialogue':
        return (
          <div className="space-y-6">
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <h4 className="font-semibold text-indigo-800 mb-3">对话结构</h4>
              <div className="text-indigo-700">
                <EnhancedMarkdown
                  content={result.dialogueAnalysis?.dialogueStructure || '分析中...'}
                  className="prose prose-sm max-w-none prose-indigo"
                />
              </div>
            </div>
            <div className="bg-pink-50 border border-pink-200 rounded-lg p-4">
              <h4 className="font-semibold text-pink-800 mb-3">推进方式</h4>
              <div className="text-pink-700">
                <EnhancedMarkdown
                  content={result.dialogueAnalysis?.plotAdvancement || '分析中...'}
                  className="prose prose-sm max-w-none prose-pink"
                />
              </div>
            </div>
            <div className="bg-cyan-50 border border-cyan-200 rounded-lg p-4">
              <h4 className="font-semibold text-cyan-800 mb-3">写作技巧</h4>
              <div className="text-cyan-700">
                <EnhancedMarkdown
                  content={result.dialogueAnalysis?.writingTechniques || '分析中...'}
                  className="prose prose-sm max-w-none prose-cyan"
                />
              </div>
            </div>

            {/* 新增：语气特征分析 */}
            {result.dialogueAnalysis?.toneCharacteristics && result.dialogueAnalysis.toneCharacteristics.length > 0 && (
              <div className="bg-sky-50 border border-sky-200 rounded-lg p-4">
                <h4 className="font-semibold text-sky-800 mb-3">🎵 语气特征</h4>
                <div className="grid grid-cols-2 gap-2">
                  {result.dialogueAnalysis.toneCharacteristics.map((tone: string, index: number) => (
                    <div key={index} className="bg-sky-100 text-sky-700 px-3 py-2 rounded text-sm">
                      {tone}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：行文框架模式 */}
            {result.dialogueAnalysis?.stylePatterns && result.dialogueAnalysis.stylePatterns.length > 0 && (
              <div className="bg-lime-50 border border-lime-200 rounded-lg p-4">
                <h4 className="font-semibold text-lime-800 mb-3">📝 行文框架</h4>
                <div className="space-y-2">
                  {result.dialogueAnalysis.stylePatterns.map((pattern: string, index: number) => (
                    <div key={index} className="bg-lime-100 text-lime-700 px-3 py-2 rounded">
                      {pattern}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：文学化分析 */}
            {result.dialogueAnalysis?.literaryAnalysis && (
              <div className="bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4">
                <h4 className="font-semibold text-fuchsia-800 mb-3">📚 文学化分析</h4>
                <div className="text-fuchsia-700">
                  <EnhancedMarkdown
                    content={result.dialogueAnalysis.literaryAnalysis}
                    className="prose prose-sm max-w-none prose-fuchsia"
                  />
                </div>
              </div>
            )}

            {/* 新增：完整对话列表 */}
            {result.dialogueAnalysis?.completeDialogues && result.dialogueAnalysis.completeDialogues.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-3">💬 完整对话列表</h4>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {result.dialogueAnalysis.completeDialogues.map((dialogue: any, index: number) => (
                    <div key={index} className="bg-white border border-blue-200 rounded p-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {dialogue.speaker && (
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              {dialogue.speaker}
                            </span>
                          )}
                          <span className="text-xs text-gray-500">位置: {dialogue.position}</span>
                        </div>
                      </div>
                      <div className="text-blue-800 font-medium mb-2">
                        「{dialogue.content}」
                      </div>
                      {dialogue.context && (
                        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                          上下文: {dialogue.context}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：对话风格分析 */}
            {result.dialogueAnalysis?.styleAnalysis && (
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h4 className="font-semibold text-purple-800 mb-3">🎨 对话风格分析</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white border border-purple-200 rounded p-3">
                    <h5 className="font-medium text-purple-700 mb-2">整体风格</h5>
                    <div className="text-sm text-purple-600">
                      {result.dialogueAnalysis.styleAnalysis.dialogueStyle || '分析中...'}
                    </div>
                  </div>
                  <div className="bg-white border border-purple-200 rounded p-3">
                    <h5 className="font-medium text-purple-700 mb-2">角色语言特色</h5>
                    <div className="text-sm text-purple-600">
                      {result.dialogueAnalysis.styleAnalysis.characterVoice || '分析中...'}
                    </div>
                  </div>
                  <div className="bg-white border border-purple-200 rounded p-3">
                    <h5 className="font-medium text-purple-700 mb-2">情感基调</h5>
                    <div className="text-sm text-purple-600">
                      {result.dialogueAnalysis.styleAnalysis.emotionalTone || '分析中...'}
                    </div>
                  </div>
                  <div className="bg-white border border-purple-200 rounded p-3">
                    <h5 className="font-medium text-purple-700 mb-2">技巧特征</h5>
                    <div className="text-sm text-purple-600">
                      {result.dialogueAnalysis.styleAnalysis.technicalFeatures || '分析中...'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'style':
        return (
          <div className="space-y-6">
            <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
              <h4 className="font-semibold text-emerald-800 mb-3">写作风格</h4>
              <div className="text-emerald-700">
                <EnhancedMarkdown
                  content={result.styleAnalysis?.writingStyle || '分析中...'}
                  className="prose prose-sm max-w-none prose-emerald"
                />
              </div>
            </div>
            <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
              <h4 className="font-semibold text-teal-800 mb-3">表现特色</h4>
              <div className="text-teal-700">
                <EnhancedMarkdown
                  content={result.styleAnalysis?.expressionFeatures || '分析中...'}
                  className="prose prose-sm max-w-none prose-teal"
                />
              </div>
            </div>
            <div className="bg-violet-50 border border-violet-200 rounded-lg p-4">
              <h4 className="font-semibold text-violet-800 mb-3">实用方法</h4>
              <div className="text-violet-700">
                <EnhancedMarkdown
                  content={result.styleAnalysis?.practicalMethods || '分析中...'}
                  className="prose prose-sm max-w-none prose-violet"
                />
              </div>
            </div>

            {/* 新增：节奏模式 */}
            {result.styleAnalysis?.rhythmPatterns && result.styleAnalysis.rhythmPatterns.length > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <h4 className="font-semibold text-orange-800 mb-3">🎼 节奏模式</h4>
                <div className="space-y-2">
                  {result.styleAnalysis.rhythmPatterns.map((pattern: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></span>
                      <span className="text-orange-700">{pattern}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 新增：节奏控制框架 */}
            {result.styleAnalysis?.pacingFramework && (
              <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
                <h4 className="font-semibold text-slate-800 mb-3">⚡ 节奏控制框架</h4>
                <div className="text-slate-700">
                  <EnhancedMarkdown
                    content={result.styleAnalysis.pacingFramework}
                    className="prose prose-sm max-w-none prose-slate"
                  />
                </div>
              </div>
            )}

            {/* 新增：大纲参照指导 */}
            {result.styleAnalysis?.outlineGuidance && (
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                <h4 className="font-semibold text-emerald-800 mb-3">🎯 大纲参照指导</h4>
                <div className="text-emerald-700 bg-emerald-100 p-3 rounded border-l-4 border-emerald-500">
                  <EnhancedMarkdown
                    content={result.styleAnalysis.outlineGuidance}
                    className="prose prose-sm max-w-none prose-emerald"
                  />
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* 结果头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900">{result.frameworkName}</h3>
            <div className="mt-3 space-y-2">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-purple-600">框架模式：</span>
                <code className="text-sm bg-purple-100 text-purple-700 px-2 py-1 rounded">
                  {result.frameworkPattern}
                </code>
              </div>
              {result.patternType && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-orange-600">模式类型：</span>
                  <span className="text-sm bg-orange-100 text-orange-700 px-2 py-1 rounded">
                    {result.patternType}
                  </span>
                </div>
              )}
              {result.frameworkVariables && result.frameworkVariables.length > 0 && (
                <div className="flex items-start space-x-2">
                  <span className="text-sm font-medium text-green-600">变量列表：</span>
                  <div className="flex flex-wrap gap-1">
                    {result.frameworkVariables.map((variable: string, index: number) => (
                      <span key={index} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        {variable}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>分析章节: {result.extractedFrom?.chapterTitles?.join('、') || '未知'}</span>
                <span>置信度: {Math.round((result.metadata?.confidence || 0) * 100)}%</span>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => onExport('json')}
              className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              导出JSON
            </button>
            <button
              onClick={() => onExport('markdown')}
              className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              导出MD
            </button>
            <button
              onClick={() => onSave(result)}
              className="px-4 py-2 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              保存框架
            </button>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="flex-1 p-6 overflow-y-auto">
        {renderTabContent()}
      </div>

      {/* 底部操作栏 */}
      <div className="p-6 border-t border-gray-200 bg-gray-50">
        <div className="flex justify-between">
          <button
            onClick={onStartNew}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            重新分析
          </button>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              关闭
            </button>
            <button
              onClick={() => onSave(result)}
              className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              保存到框架库
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FrameworkAnalysisResult;
