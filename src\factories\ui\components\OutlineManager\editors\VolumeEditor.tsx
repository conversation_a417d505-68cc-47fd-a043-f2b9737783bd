import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { OutlineNodeType } from '../../../types/outline';
import { cycleDistributionService } from '../../../services/CycleDistributionService';

interface VolumeEditorProps {
  node: OutlineNodeType;
  onChange?: (updatedNode: OutlineNodeType) => void;
  onSave?: (data: Partial<OutlineNodeType>) => void; // 保持向后兼容
  onCancel?: () => void;
  allNodes?: OutlineNodeType[]; // 传入所有节点，用于查找章节节点
}

// 暴露给父组件的方法
export interface VolumeEditorRef {
  triggerSave: () => void;
}

// 章节接口定义
interface Chapter {
  id?: string;
  title: string;
  order: number;
  wordCount?: number;
}

export const VolumeEditor = forwardRef<VolumeEditorRef, VolumeEditorProps>(({
  node,
  onChange,
  onSave,
  onCancel,
  allNodes
}, ref) => {
  // 章节数据状态
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  const [formData, setFormData] = useState({
    title: node.title || '',
    description: node.description || '',
    creativeNotes: node.creativeNotes || '',
    volumeTheme: node.volumeTheme || '',
    volumeArc: node.volumeArc || '',
    chapterCount: node.chapterCount || 30,
    cycleTemplate: node.cycleTemplate || '4章循环',
    targetWordCount: node.targetWordCount || 500000,
    rhythmStrategy: node.rhythmStrategy || '',
    cyclePhases: node.cyclePhases || ['开篇建立', '冲突发展', '高潮爽点', '过渡转折'],
    cycleChapterMappings: node.cycleChapterMappings || [] // 新增：循环阶段与章节的映射
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 加载章节节点数据
  useEffect(() => {
    if (allNodes) {
      loadChapterNodes();
    }
  }, [allNodes]);

  const loadChapterNodes = () => {
    if (!allNodes) return;

    setIsLoadingChapters(true);
    try {
      // 从传入的所有节点中筛选出章节类型的节点
      const chapterNodes: Chapter[] = allNodes
        .filter(node => node.type === 'chapter')
        .map((node, index) => ({
          id: node.id,
          title: node.title || `第${index + 1}章`,
          order: index,
          wordCount: 0
        }));

      // 按标题中的章节号排序
      const sortedChapters = chapterNodes.sort((a, b) => {
        const aNum = parseInt(a.title.match(/第(\d+)章/)?.[1] || '0');
        const bNum = parseInt(b.title.match(/第(\d+)章/)?.[1] || '0');
        return aNum - bNum;
      });

      setChapters(sortedChapters);
      console.log('📚 加载到章节节点:', sortedChapters.length, '个');
    } catch (error) {
      console.error('加载章节节点失败:', error);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '总纲标题不能为空';
    }

    if (!formData.description.trim()) {
      newErrors.description = '总纲描述不能为空';
    }

    if (!formData.volumeTheme.trim()) {
      newErrors.volumeTheme = '卷主题不能为空';
    }

    if (formData.chapterCount < 1 || formData.chapterCount > 100) {
      newErrors.chapterCount = '章节数量应在1-100之间';
    }

    if (formData.targetWordCount < 10000 || formData.targetWordCount > 10000000) {
      newErrors.targetWordCount = '目标字数应在1万-1000万之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理保存
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    // 过滤空的循环阶段
    const filteredPhases = formData.cyclePhases.filter(phase => phase.trim());

    // 过滤有效的章节映射
    const validMappings = formData.cycleChapterMappings.filter(
      mapping => mapping.phase && mapping.chapterIds.length > 0
    );

    const updatedData = {
      ...formData,
      type: 'volume' as const,
      cyclePhases: filteredPhases.length > 0 ? filteredPhases : undefined,
      cycleChapterMappings: validMappings.length > 0 ? validMappings : undefined,
      // 标记需要生成阶段分组
      shouldGeneratePhaseGroups: validMappings.length > 0
    };

    // 支持新的onChange模式和旧的onSave模式
    if (onChange) {
      // 新模式：通过onChange更新节点数据
      const updatedNode = { ...node, ...updatedData };
      onChange(updatedNode);
    } else if (onSave) {
      // 旧模式：通过onSave保存数据
      onSave(updatedData);
    }
  };

  // 暴露给父组件的方法（只在有ref时才执行）
  useImperativeHandle(ref, () => ({
    triggerSave: handleSave
  }), [handleSave]);

  // 处理输入变化
  const handleInputChange = (field: string, value: any) => {
    const newFormData = {
      ...formData,
      [field]: value
    };

    setFormData(newFormData);

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // 在onChange模式下，实时更新节点数据
    if (onChange) {
      const updatedNode = { ...node, ...newFormData };
      onChange(updatedNode);
    }
  };

  // 处理循环阶段变化
  const handleCyclePhasesChange = (index: number, value: string) => {
    const newPhases = [...formData.cyclePhases];
    newPhases[index] = value;
    handleInputChange('cyclePhases', newPhases);
  };

  // 添加循环阶段
  const addCyclePhase = () => {
    handleInputChange('cyclePhases', [...formData.cyclePhases, '新阶段']);
  };

  // 删除循环阶段
  const removeCyclePhase = (index: number) => {
    const newPhases = formData.cyclePhases.filter((_, i) => i !== index);
    const newMappings = formData.cycleChapterMappings.filter((_, i) => i !== index);
    handleInputChange('cyclePhases', newPhases);
    handleInputChange('cycleChapterMappings', newMappings);
  };

  // 处理章节映射变化
  const handleChapterMappingChange = (phaseIndex: number, chapterIds: string[]) => {
    const newMappings = [...formData.cycleChapterMappings];

    // 确保映射数组有足够的元素
    while (newMappings.length <= phaseIndex) {
      newMappings.push({ phase: '', chapterIds: [] });
    }

    newMappings[phaseIndex] = {
      phase: formData.cyclePhases[phaseIndex] || '',
      chapterIds: chapterIds
    };

    handleInputChange('cycleChapterMappings', newMappings);
  };

  // 获取当前阶段已选择的章节ID
  const getSelectedChapterIds = (phaseIndex: number): string[] => {
    return formData.cycleChapterMappings[phaseIndex]?.chapterIds || [];
  };

  return (
    <div className="space-y-6">
      {/* 基础信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">基础信息</h3>
        
        {/* 标题 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            总纲标题 *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请输入总纲标题"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* 描述 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            总纲描述 *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请描述总纲的整体规划和核心内容"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description}</p>
          )}
        </div>
      </div>

      {/* 卷设定 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">卷设定</h3>
        
        {/* 卷主题 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            卷主题 *
          </label>
          <input
            type="text"
            value={formData.volumeTheme}
            onChange={(e) => handleInputChange('volumeTheme', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.volumeTheme ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请输入本卷的核心主题"
          />
          {errors.volumeTheme && (
            <p className="mt-1 text-sm text-red-600">{errors.volumeTheme}</p>
          )}
        </div>

        {/* 卷弧线 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            卷弧线
          </label>
          <textarea
            value={formData.volumeArc}
            onChange={(e) => handleInputChange('volumeArc', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请描述本卷的整体故事发展轨迹和关键转折点"
          />
        </div>
      </div>

      {/* 规划设定 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">规划设定</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 预期章节数 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              预期章节数 *
            </label>
            <input
              type="number"
              value={formData.chapterCount}
              onChange={(e) => handleInputChange('chapterCount', parseInt(e.target.value) || 0)}
              min="1"
              max="100"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.chapterCount ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.chapterCount && (
              <p className="mt-1 text-sm text-red-600">{errors.chapterCount}</p>
            )}
          </div>

          {/* 目标字数 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              目标字数 *
            </label>
            <input
              type="number"
              value={formData.targetWordCount}
              onChange={(e) => handleInputChange('targetWordCount', parseInt(e.target.value) || 0)}
              min="10000"
              max="10000000"
              step="10000"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.targetWordCount ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.targetWordCount && (
              <p className="mt-1 text-sm text-red-600">{errors.targetWordCount}</p>
            )}
          </div>
        </div>

        {/* 循环法模板 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            循环法模板
          </label>
          <select
            value={formData.cycleTemplate}
            onChange={(e) => handleInputChange('cycleTemplate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="4章循环">4章循环（铺垫→冲突→爽点→过渡）</option>
            <option value="3章循环">3章循环（起→承→转）</option>
            <option value="5章循环">5章循环（起→承→转→合→续）</option>
            <option value="自定义循环">自定义循环</option>
          </select>
        </div>
      </div>

      {/* 节奏策略 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">节奏策略</h3>
        
        {/* 节奏策略 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            整体节奏策略
          </label>
          <textarea
            value={formData.rhythmStrategy}
            onChange={(e) => handleInputChange('rhythmStrategy', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请描述整体的节奏控制规划，包括张弛有度的安排"
          />
        </div>

        {/* 循环阶段规划 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            循环阶段规划
          </label>
          <div className="space-y-3">
            {formData.cyclePhases.map((phase, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium text-gray-700 w-16 flex-shrink-0">阶段{index + 1}:</span>

                  {/* 阶段选择下拉框 */}
                  <div className="flex-1">
                    <select
                      value={phase}
                      onChange={(e) => handleCyclePhasesChange(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                    >
                      <option value="">请选择循环阶段</option>
                      <optgroup label="经典4章循环">
                        <option value="开篇建立">开篇建立 - 建立场景、介绍角色、埋下伏笔</option>
                        <option value="冲突发展">冲突发展 - 引入矛盾、推进情节、加强张力</option>
                        <option value="高潮爽点">高潮爽点 - 解决冲突、释放张力、给予满足感</option>
                        <option value="过渡转折">过渡转折 - 承上启下、调整节奏、为下一循环做准备</option>
                      </optgroup>
                      <optgroup label="起承转合">
                        <option value="起">起 - 开始阶段，建立基础，引入要素</option>
                        <option value="承">承 - 承接发展，深化内容，推进情节</option>
                        <option value="转">转 - 转折变化，制造冲突，产生张力</option>
                        <option value="合">合 - 收束整合，解决问题，达成目标</option>
                      </optgroup>
                      <optgroup label="扩展循环">
                        <option value="续">续 - 延续发展，为下一阶段做准备</option>
                        <option value="铺垫">铺垫 - 为后续情节做准备</option>
                        <option value="爆发">爆发 - 情节高潮，矛盾激化</option>
                        <option value="收尾">收尾 - 情节收束，问题解决</option>
                      </optgroup>
                    </select>
                  </div>

                  {/* 自定义输入框 */}
                  <div className="flex-1">
                    <input
                      type="text"
                      value={phase}
                      onChange={(e) => handleCyclePhasesChange(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="或自定义阶段名称"
                    />
                  </div>

                  {/* 阶段预览 */}
                  {phase && (
                    <div className="flex-shrink-0">
                      <span className={`px-2 py-1 rounded text-xs border ${cycleDistributionService.getPhaseColor(phase)}`}>
                        {phase}
                      </span>
                    </div>
                  )}

                  {/* 删除按钮 */}
                  {formData.cyclePhases.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeCyclePhase(index)}
                      className="px-2 py-1 text-red-600 hover:text-red-800 flex-shrink-0"
                      title="删除此阶段"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* 章节选择区域 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">
                    选择对应章节：
                  </label>

                  {isLoadingChapters ? (
                    <div className="text-sm text-gray-500">加载章节中...</div>
                  ) : chapters.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {chapters.map((chapter) => {
                        const selectedIds = getSelectedChapterIds(index);
                        const isSelected = selectedIds.includes(chapter.id || '');

                        return (
                          <label
                            key={chapter.id}
                            className={`flex items-center space-x-2 p-2 rounded border cursor-pointer transition-colors ${
                              isSelected
                                ? 'bg-blue-50 border-blue-300 text-blue-700'
                                : 'bg-white border-gray-200 hover:bg-gray-50'
                            }`}
                          >
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={(e) => {
                                const currentIds = getSelectedChapterIds(index);
                                const newIds = e.target.checked
                                  ? [...currentIds, chapter.id || '']
                                  : currentIds.filter(id => id !== chapter.id);
                                handleChapterMappingChange(index, newIds);
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm truncate">
                              第{(chapter.order || 0) + 1}章: {chapter.title}
                            </span>
                          </label>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      {allNodes ? '暂无章节节点' : '请先加载大纲数据'}
                    </div>
                  )}

                  {/* 已选章节统计 */}
                  {getSelectedChapterIds(index).length > 0 && (
                    <div className="text-xs text-blue-600">
                      已选择 {getSelectedChapterIds(index).length} 个章节
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* 添加阶段按钮 */}
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={addCyclePhase}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>添加阶段</span>
              </button>

              {/* 快速设置按钮 */}
              <div className="flex space-x-2 ml-4">
                <button
                  type="button"
                  onClick={() => {
                    handleInputChange('cyclePhases', ['开篇建立', '冲突发展', '高潮爽点', '过渡转折']);
                  }}
                  className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                >
                  4章循环
                </button>
                <button
                  type="button"
                  onClick={() => {
                    handleInputChange('cyclePhases', ['起', '承', '转', '合']);
                  }}
                  className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                >
                  起承转合
                </button>
              </div>
            </div>

            {/* 阶段分组生成提示 */}
            {formData.cycleChapterMappings.some(mapping => mapping.chapterIds.length > 0) && (
              <div className="mt-4 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <svg className="w-5 h-5 text-emerald-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-emerald-800">阶段分组功能</h4>
                    <p className="text-sm text-emerald-700 mt-1">
                      保存后将自动为每个循环阶段创建分组节点，章节将按阶段归类显示，形成更清晰的层级结构：
                    </p>
                    <div className="text-xs text-emerald-600 mt-2 space-y-1">
                      <div>📁 卷纲 → 🔄 阶段分组 → 📖 章节</div>
                      <div>例如：第一卷 → 开篇建立 → 第1-2章</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创作建议 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          创作建议
        </label>
        <textarea
          value={formData.creativeNotes}
          onChange={(e) => handleInputChange('creativeNotes', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="请输入总纲的创作指导和整体规划要点"
        />
      </div>

      {/* 循环预览 */}
      {formData.cycleTemplate && formData.cyclePhases.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
            <h4 className="font-medium text-blue-800">循环设置预览</h4>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">循环模板：</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm font-medium">
                {formData.cycleTemplate}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">循环阶段：</span>
              <div className="flex flex-wrap gap-1">
                {formData.cyclePhases.map((phase, index) => (
                  <span
                    key={index}
                    className={`px-2 py-1 rounded text-xs border ${cycleDistributionService.getPhaseColor(phase)}`}
                  >
                    {index + 1}. {phase}
                  </span>
                ))}
              </div>
            </div>
            <div className="text-sm text-blue-600 mt-2">
              💡 保存后，新建章节时将自动按此循环分配阶段指导
            </div>
          </div>
        </div>
      )}

      {/* 在onChange模式下不显示操作按钮，使用InlineNodeEditor统一的保存按钮 */}
      {!onChange && onSave && (
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            保存
          </button>
        </div>
      )}
    </div>
  );
});

// 设置displayName用于调试
VolumeEditor.displayName = 'VolumeEditor';
