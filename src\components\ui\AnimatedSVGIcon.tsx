"use client";

import React, { useEffect, useState } from 'react';

interface AnimatedSVGIconProps {
  type: 'document' | 'target' | 'lightning' | 'clock' | 'pomodoro';
  isActive?: boolean;
  progress?: number; // 0-1
  speed?: number; // for lightning
  className?: string;
  style?: React.CSSProperties;
}

export const AnimatedSVGIcon: React.FC<AnimatedSVGIconProps> = ({
  type,
  isActive = false,
  progress = 0,
  speed = 0,
  className = '',
  style = {}
}) => {
  const [animationKey, setAnimationKey] = useState(0);

  // 触发动画重新开始
  useEffect(() => {
    if (isActive) {
      setAnimationKey(prev => prev + 1);
    }
  }, [isActive, progress, speed]);

  const getSpeedColor = (speed: number): string => {
    if (speed === 0) return '#9CA3AF';
    if (speed < 20) return '#EF4444';
    if (speed < 40) return '#F59E0B';
    if (speed < 60) return '#10B981';
    return '#8B5CF6';
  };

  const renderDocumentIcon = () => (
    <svg
      className={`w-4 h-4 ${className}`}
      style={style}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
    >
      {/* 文档外框 */}
      <path
        d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
        className="document-frame"
      />
      <polyline points="14,2 14,8 20,8" className="document-corner" />
      
      {/* 文档内容线条 - 支持书写动画 */}
      <g className="document-content">
        <line
          x1="9" y1="12" x2="15" y2="12"
          className={`document-line ${isActive ? 'writing' : ''}`}
          style={{
            animationDelay: '0ms',
            animationDuration: '800ms'
          }}
        />
        <line
          x1="9" y1="16" x2="13" y2="16"
          className={`document-line ${isActive ? 'writing' : ''}`}
          style={{
            animationDelay: '200ms',
            animationDuration: '600ms'
          }}
        />
      </g>
      
      {/* 里程碑光环 */}
      {progress > 0.8 && (
        <circle
          cx="12" cy="12" r="11"
          fill="none"
          stroke="#FFD700"
          strokeWidth="1"
          opacity="0.6"
          className="milestone-glow"
        />
      )}
    </svg>
  );

  const renderTargetIcon = () => (
    <svg
      className={`w-4 h-4 ${className}`}
      style={style}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
    >
      {/* 靶心圆环 */}
      <circle
        cx="12" cy="12" r="10"
        className={`target-ring ${progress > 0.2 ? 'hit' : ''}`}
        style={{ animationDelay: '0ms' }}
      />
      <circle
        cx="12" cy="12" r="6"
        className={`target-ring ${progress > 0.6 ? 'hit' : ''}`}
        style={{ animationDelay: '200ms' }}
      />
      <circle
        cx="12" cy="12" r="2"
        fill="currentColor"
        className={`target-center ${progress > 0.9 ? 'bullseye' : ''}`}
      />
      
      {/* 完成时的粒子效果 */}
      {progress >= 1 && (
        <g className="celebration-particles">
          {[...Array(6)].map((_, i) => (
            <circle
              key={i}
              cx="12" cy="12" r="1"
              fill="#FFD700"
              className="particle"
              style={{
                animationDelay: `${i * 100}ms`,
                transform: `rotate(${i * 60}deg) translateY(-15px)`
              }}
            />
          ))}
        </g>
      )}
    </svg>
  );

  const renderLightningIcon = () => (
    <svg
      className={`w-4 h-4 ${className}`}
      style={{ ...style, color: getSpeedColor(speed) }}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
    >
      {/* 闪电主体 */}
      <path
        d="M13 10V3L4 14h7v7l9-11h-7z"
        className={`lightning-bolt ${speed > 40 ? 'energized' : ''}`}
        style={{
          animationDuration: speed > 60 ? '0.3s' : speed > 20 ? '1s' : '2s'
        }}
      />
      
      {/* 能量波纹 */}
      {speed > 0 && (
        <g className="energy-waves">
          <circle
            cx="12" cy="12" r="8"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.3"
            className="energy-wave"
            style={{ animationDelay: '0ms' }}
          />
          <circle
            cx="12" cy="12" r="12"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.2"
            className="energy-wave"
            style={{ animationDelay: '300ms' }}
          />
        </g>
      )}
    </svg>
  );

  const renderClockIcon = () => {
    const currentTime = new Date();
    const hours = currentTime.getHours() % 12;
    const minutes = currentTime.getMinutes();
    
    const hourAngle = (hours * 30) + (minutes * 0.5); // 时针角度
    const minuteAngle = minutes * 6; // 分针角度

    return (
      <svg
        className={`w-4 h-4 ${className}`}
        style={style}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth={2}
      >
        {/* 表盘 */}
        <circle cx="12" cy="12" r="10" />
        
        {/* 时钟刻度 */}
        <g className="clock-marks">
          {[...Array(12)].map((_, i) => (
            <line
              key={i}
              x1="12" y1="3" x2="12" y2="5"
              strokeWidth={i % 3 === 0 ? 2 : 1}
              transform={`rotate(${i * 30} 12 12)`}
              opacity="0.6"
            />
          ))}
        </g>
        
        {/* 时针 */}
        <line
          x1="12" y1="12" x2="12" y2="8"
          strokeWidth="3"
          strokeLinecap="round"
          className="hour-hand"
          transform={`rotate(${hourAngle} 12 12)`}
        />
        
        {/* 分针 */}
        <line
          x1="12" y1="12" x2="12" y2="6"
          strokeWidth="2"
          strokeLinecap="round"
          className="minute-hand"
          transform={`rotate(${minuteAngle} 12 12)`}
        />
        
        {/* 中心点 */}
        <circle cx="12" cy="12" r="1.5" fill="currentColor" />
        
        {/* 专注提醒光环 */}
        {progress > 0.8 && (
          <circle
            cx="12" cy="12" r="11"
            fill="none"
            stroke="#F59E0B"
            strokeWidth="1"
            opacity="0.5"
            className="focus-ring"
          />
        )}
      </svg>
    );
  };

  const renderPomodoroIcon = () => (
    <svg
      className={`w-4 h-4 ${className}`}
      style={{ ...style, color: isActive ? '#EF4444' : 'currentColor' }}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
    >
      {/* 番茄钟表盘 */}
      <circle
        cx="12" cy="12" r="10"
        className={`pomodoro-face ${isActive ? 'heartbeat' : ''}`}
      />
      
      {/* 指针 */}
      <polyline
        points="12,6 12,12 16,14"
        className={`pomodoro-hand ${isActive ? 'ticking' : ''}`}
      />
      
      {/* 活跃时的脉冲效果 */}
      {isActive && (
        <circle
          cx="12" cy="12" r="8"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          opacity="0.3"
          className="pomodoro-pulse"
        />
      )}
    </svg>
  );

  const iconRenderers = {
    document: renderDocumentIcon,
    target: renderTargetIcon,
    lightning: renderLightningIcon,
    clock: renderClockIcon,
    pomodoro: renderPomodoroIcon
  };

  return iconRenderers[type]();
};
