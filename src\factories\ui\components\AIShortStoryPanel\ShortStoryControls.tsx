"use client";

import React from 'react';

interface ShortStoryControlsProps {
  isLoading: boolean;
  hasResult: boolean;
  creationStep: 'input' | 'phase-strategy' | 'structure-preview' | 'manual-generation' | 'generating';
  onGenerate: () => void;
  onApply: () => void;
  onCancel: () => void;
}

/**
 * 短篇创作控制按钮组件
 * 包含底部控制按钮
 */
export const ShortStoryControls: React.FC<ShortStoryControlsProps> = ({
  isLoading,
  hasResult,
  creationStep,
  onGenerate,
  onApply,
  onCancel
}) => {
  return (
    <div className="p-5 border-t flex justify-between items-center bg-gradient-to-r from-gray-50 to-amber-50">
      <div>
        <button
          className="px-5 py-2.5 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
          onClick={onCancel}
          disabled={isLoading}
        >
          取消
        </button>
      </div>

      <div className="flex items-center space-x-3">
        {/* 生成状态指示器 */}
        {isLoading && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span>
              {creationStep === 'input' ? '正在生成结构...' :
               creationStep === 'generating' ? '正在生成内容...' : '处理中...'}
            </span>
          </div>
        )}

        {/* 生成内容按钮或应用按钮组 */}
        {hasResult ? (
          <div className="flex space-x-3">
            {/* 重新生成按钮 */}
            <button
              onClick={onGenerate}
              className="px-4 py-2.5 bg-amber-500 text-white rounded-xl hover:bg-amber-600 transition-colors shadow-sm text-sm flex items-center"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重新生成
            </button>
            
            {/* 应用结果按钮 */}
            <button
              onClick={onApply}
              className="px-6 py-2.5 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors shadow-sm text-sm font-medium flex items-center"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              应用结果
            </button>
          </div>
        ) : (
          /* 生成按钮 - 根据步骤显示不同文本 */
          <button
            onClick={onGenerate}
            className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all shadow-sm text-sm font-medium flex items-center"
            disabled={isLoading}
          >
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            {creationStep === 'input' ? '生成结构预览' :
             creationStep === 'structure-preview' ? '确认结构' : '生成短篇'}
          </button>
        )}
      </div>
    </div>
  );
};
