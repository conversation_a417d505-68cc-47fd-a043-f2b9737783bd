"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { OutlineTreeSelector } from './OutlineTreeSelector';
import OutlineCanvas from '@/factories/ui/components/OutlineManager/OutlineCanvas';
import OutlineCanvasErrorBoundary from '@/factories/ui/components/OutlineManager/OutlineCanvasErrorBoundary';
import NodeEditPanel from '@/factories/ui/components/OutlineManager/NodeEditPanel';
import { OutlineNodeType, PlotType } from '@/factories/ui/types/outline';

// 内联编辑表单组件
interface NodeEditFormInlineProps {
  node: OutlineNodeType;
  onSave: (updatedNode: OutlineNodeType) => void;
  isDarkTheme: boolean;
}

const NodeEditFormInline: React.FC<NodeEditFormInlineProps> = ({ node, onSave, isDarkTheme }) => {
  const [title, setTitle] = useState(node.title || '');
  const [description, setDescription] = useState(node.description || '');
  const [creativeNotes, setCreativeNotes] = useState(node.creativeNotes || '');
  const [type, setType] = useState<'chapter' | 'plot' | 'dialogue'>(node.type || 'chapter');

  // 章节专有字段
  const [chapterStyle, setChapterStyle] = useState(node.chapterStyle || '');
  const [chapterTechniques, setChapterTechniques] = useState<string[]>(node.chapterTechniques || []);
  const [chapterGoals, setChapterGoals] = useState(node.chapterGoals || '');

  // 剧情节点专有字段
  const [plotType, setPlotType] = useState<PlotType | ''>(node.plotType || '');
  const [relatedCharacters, setRelatedCharacters] = useState<string[]>(node.relatedCharacters || []);

  // 对话设计专有字段
  const [dialogueScene, setDialogueScene] = useState(node.dialogueScene || '');
  const [participants, setParticipants] = useState<string[]>(node.participants || []);
  const [dialoguePurpose, setDialoguePurpose] = useState(node.dialoguePurpose || '');

  // 当节点变化时更新表单
  useEffect(() => {
    setTitle(node.title || '');
    setDescription(node.description || '');
    setCreativeNotes(node.creativeNotes || '');
    setType(node.type || 'chapter');

    // 更新章节字段
    setChapterStyle(node.chapterStyle || '');
    setChapterTechniques(node.chapterTechniques || []);
    setChapterGoals(node.chapterGoals || '');

    // 更新剧情节点字段
    setPlotType(node.plotType || '');
    setRelatedCharacters(node.relatedCharacters || []);

    // 更新对话设计字段
    setDialogueScene(node.dialogueScene || '');
    setParticipants(node.participants || []);
    setDialoguePurpose(node.dialoguePurpose || '');
  }, [node]);

  const handleSave = () => {
    const updatedNode: OutlineNodeType = {
      ...node,
      title: title.trim() || node.title,
      description: description.trim(),
      creativeNotes: creativeNotes.trim(),
      type,
      // 章节专有字段
      chapterStyle: type === 'chapter' ? chapterStyle.trim() : undefined,
      chapterTechniques: type === 'chapter' ? chapterTechniques.filter(t => t.trim()) : undefined,
      chapterGoals: type === 'chapter' ? chapterGoals.trim() : undefined,
      // 剧情节点专有字段
      plotType: type === 'plot' && plotType ? plotType as PlotType : undefined,
      relatedCharacters: type === 'plot' ? relatedCharacters.filter(c => c.trim()) : undefined,
      // 对话设计专有字段
      dialogueScene: type === 'dialogue' ? dialogueScene.trim() : undefined,
      participants: type === 'dialogue' ? participants.filter(p => p.trim()) : undefined,
      dialoguePurpose: type === 'dialogue' ? dialoguePurpose.trim() : undefined,
    };
    onSave(updatedNode);
  };

  return (
    <div className="space-y-4">
      {/* 节点类型选择 */}
      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isDarkTheme ? 'text-gray-300' : 'text-gray-700'
        }`}>
          节点类型
        </label>
        <div className="flex space-x-2">
          {[
            { value: 'chapter', label: '章节', color: 'blue' },
            { value: 'plot', label: '剧情节点', color: 'green' },
            { value: 'dialogue', label: '对话设计', color: 'purple' }
          ].map(({ value, label, color }) => (
            <button
              key={value}
              className={`flex-1 py-2 px-3 rounded-md border text-sm transition-colors ${
                type === value
                  ? `bg-${color}-500 text-white border-${color}-500`
                  : isDarkTheme
                    ? 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType(value as 'chapter' | 'plot' | 'dialogue')}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* 标题输入 */}
      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isDarkTheme ? 'text-gray-300' : 'text-gray-700'
        }`}>
          标题
        </label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className={`w-full px-3 py-2 border rounded-md transition-colors ${
            isDarkTheme
              ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
              : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
          } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
          placeholder="输入节点标题"
        />
      </div>

      {/* 描述输入 */}
      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isDarkTheme ? 'text-gray-300' : 'text-gray-700'
        }`}>
          描述
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-md transition-colors resize-none ${
            isDarkTheme
              ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
              : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
          } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
          placeholder="输入节点描述"
        />
      </div>

      {/* 创作建议输入 */}
      <div>
        <label className={`block text-sm font-medium mb-2 flex items-center gap-2 ${
          isDarkTheme ? 'text-gray-300' : 'text-gray-700'
        }`}>
          <span>💡</span>
          <span>创作建议</span>
          <span className="text-xs text-blue-500">(重要：影响AI生成质量)</span>
        </label>
        <textarea
          value={creativeNotes}
          onChange={(e) => setCreativeNotes(e.target.value)}
          rows={4}
          className={`w-full px-3 py-2 border-2 border-blue-200 rounded-md transition-colors resize-none ${
            isDarkTheme
              ? 'bg-gray-700 text-gray-100 focus:border-blue-400'
              : 'bg-gradient-to-r from-blue-50 to-indigo-50 text-gray-900 focus:border-blue-400'
          } focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50`}
          placeholder="输入创作建议，如台词设计、心理描写、节奏控制等..."
        />
      </div>

      {/* 节点类型专有字段 */}
      {type === 'chapter' && (
        <div className="space-y-4 border-t pt-4">
          <h5 className={`font-medium text-sm ${isDarkTheme ? 'text-gray-300' : 'text-gray-700'}`}>
            📖 章节专有字段
          </h5>

          {/* 写作风格 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              写作风格
            </label>
            <input
              type="text"
              value={chapterStyle}
              onChange={(e) => setChapterStyle(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md transition-colors ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="如：悬疑紧张、温馨治愈、激烈战斗等"
            />
          </div>

          {/* 写作手法 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              写作手法
            </label>
            <input
              type="text"
              value={chapterTechniques.join(', ')}
              onChange={(e) => setChapterTechniques(e.target.value.split(',').map(t => t.trim()).filter(t => t))}
              className={`w-full px-3 py-2 border rounded-md transition-colors ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="如：倒叙、插叙、对比、象征等（用逗号分隔）"
            />
          </div>

          {/* 章节目标 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              章节目标
            </label>
            <textarea
              value={chapterGoals}
              onChange={(e) => setChapterGoals(e.target.value)}
              rows={2}
              className={`w-full px-3 py-2 border rounded-md transition-colors resize-none ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="如：推进主线剧情、展现角色成长、营造氛围等"
            />
          </div>
        </div>
      )}

      {type === 'plot' && (
        <div className="space-y-4 border-t pt-4">
          <h5 className={`font-medium text-sm ${isDarkTheme ? 'text-gray-300' : 'text-gray-700'}`}>
            🎭 剧情节点专有字段
          </h5>

          {/* 剧情类型 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              剧情类型
            </label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { value: 'conflict', label: '冲突' },
                { value: 'twist', label: '转折' },
                { value: 'climax', label: '高潮' },
                { value: 'resolution', label: '解决' }
              ].map(({ value, label }) => (
                <button
                  key={value}
                  className={`py-2 px-3 rounded-md border text-sm transition-colors ${
                    plotType === value
                      ? 'bg-green-500 text-white border-green-500'
                      : isDarkTheme
                        ? 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setPlotType(value as PlotType)}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* 关联角色 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              关联角色
            </label>
            <input
              type="text"
              value={relatedCharacters.join(', ')}
              onChange={(e) => setRelatedCharacters(e.target.value.split(',').map(c => c.trim()).filter(c => c))}
              className={`w-full px-3 py-2 border rounded-md transition-colors ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="参与此剧情的角色（用逗号分隔）"
            />
          </div>
        </div>
      )}

      {type === 'dialogue' && (
        <div className="space-y-4 border-t pt-4">
          <h5 className={`font-medium text-sm ${isDarkTheme ? 'text-gray-300' : 'text-gray-700'}`}>
            💬 对话设计专有字段
          </h5>

          {/* 对话场景 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              对话场景
            </label>
            <input
              type="text"
              value={dialogueScene}
              onChange={(e) => setDialogueScene(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md transition-colors ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="对话发生的场景描述"
            />
          </div>

          {/* 参与角色 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              参与角色
            </label>
            <input
              type="text"
              value={participants.join(', ')}
              onChange={(e) => setParticipants(e.target.value.split(',').map(p => p.trim()).filter(p => p))}
              className={`w-full px-3 py-2 border rounded-md transition-colors ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="参与对话的角色（用逗号分隔）"
            />
          </div>

          {/* 对话目的 */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isDarkTheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              对话目的
            </label>
            <textarea
              value={dialoguePurpose}
              onChange={(e) => setDialoguePurpose(e.target.value)}
              rows={2}
              className={`w-full px-3 py-2 border rounded-md transition-colors resize-none ${
                isDarkTheme
                  ? 'bg-gray-700 border-gray-600 text-gray-100 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              placeholder="如：信息传递、情感表达、冲突升级等"
            />
          </div>
        </div>
      )}

      {/* 保存按钮 */}
      <div className="pt-4">
        <button
          onClick={handleSave}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
        >
          保存修改
        </button>
      </div>
    </div>
  );
};

interface OutlineManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  selectedOutlineNodeIds: string[];
  onOutlineNodesChange: (nodeIds: string[]) => void;
  onOutlineDataChange?: (outline: any) => void; // 新增：通知大纲数据变更
}

/**
 * 全屏切换图标组件
 */
const FullscreenIcon: React.FC<{ isFullscreen: boolean }> = ({ isFullscreen }) => (
  <svg
    className="w-5 h-5 transition-all duration-300"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
  >
    {isFullscreen ? (
      // 收缩图标：四个向内箭头
      <>
        <path d="M8 3v3H5V3h3z" />
        <path d="M16 3v3h3V3h-3z" />
        <path d="M8 21v-3H5v3h3z" />
        <path d="M16 21v-3h3v3h-3z" />
      </>
    ) : (
      // 展开图标：四个向外箭头
      <>
        <path d="M3 8V5h3V3H3a2 2 0 00-2 2v3h2z" />
        <path d="M21 8V5a2 2 0 00-2-2h-3v2h3v3h2z" />
        <path d="M3 16v3a2 2 0 002 2h3v-2H5v-3H3z" />
        <path d="M21 16v3h-3v2h3a2 2 0 002-2v-3h-2z" />
      </>
    )}
  </svg>
);

/**
 * 主题切换图标组件
 */
const ThemeIcon: React.FC<{ isDark: boolean }> = ({ isDark }) => (
  <svg
    className="w-5 h-5 transition-all duration-300"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
  >
    {isDark ? (
      // 太阳图标
      <>
        <circle cx="12" cy="12" r="5" />
        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
      </>
    ) : (
      // 月亮图标
      <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z" />
    )}
  </svg>
);

/**
 * 独立大纲管理对话框组件
 * 提供大尺寸弹窗，左右分栏布局：左侧树级选择，右侧画布预览
 * 支持全屏展开和现代化配色主题
 */
export const OutlineManagementDialog: React.FC<OutlineManagementDialogProps> = ({
  isOpen,
  onClose,
  bookId,
  selectedOutlineNodeIds,
  onOutlineNodesChange,
  onOutlineDataChange
}) => {
  const [outlineData, setOutlineData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [localSelectedIds, setLocalSelectedIds] = useState<string[]>([...selectedOutlineNodeIds]);

  // 编辑模式状态
  const [editMode, setEditMode] = useState(false);
  const [selectedNodeForEdit, setSelectedNodeForEdit] = useState<OutlineNodeType | null>(null);

  // 全屏状态管理
  const [isFullscreen, setIsFullscreen] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('outline-dialog-fullscreen') === 'true';
    }
    return false;
  });

  // 主题状态管理
  const [isDarkTheme, setIsDarkTheme] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('outline-dialog-theme');
      if (saved) return saved === 'dark';
      // 检测系统主题偏好
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });

  // 持久化全屏状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('outline-dialog-fullscreen', isFullscreen.toString());
    }
  }, [isFullscreen]);

  // 持久化主题状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('outline-dialog-theme', isDarkTheme ? 'dark' : 'light');
    }
  }, [isDarkTheme]);

  // 键盘事件监听
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F11') {
        e.preventDefault();
        setIsFullscreen(!isFullscreen);
      } else if (e.key === 'Escape') {
        if (isFullscreen) {
          setIsFullscreen(false);
        } else {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, isFullscreen, onClose]);

  // 加载大纲数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadOutlineData();
    }
  }, [isOpen, bookId]);

  // 同步外部选择状态
  useEffect(() => {
    setLocalSelectedIds([...selectedOutlineNodeIds]);
  }, [selectedOutlineNodeIds]);

  const loadOutlineData = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 OutlineManagementDialog 开始加载大纲数据:', { bookId });

      // 改用OutlineRepository，与AI服务保持一致
      const { outlineRepository } = await import('@/lib/db/repositories');
      const outlines = await outlineRepository.getAllByBookId(bookId);

      console.log('📋 OutlineRepository 返回的大纲数据:', {
        outlinesCount: outlines.length,
        outlines: outlines.map(o => ({ id: o.id, title: o.title, nodesCount: o.nodes?.length || 0 }))
      });

      // 取第一个大纲作为当前大纲（保持向后兼容）
      const outline = outlines.length > 0 ? outlines[0] : null;
      setOutlineData(outline);

      console.log('✅ OutlineManagementDialog 大纲数据加载完成:', {
        bookId,
        outlinesCount: outlines.length,
        selectedOutline: outline ? { id: outline.id, title: outline.title, nodesCount: outline.nodes?.length || 0 } : null
      });
    } catch (error) {
      console.error('❌ 加载大纲数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理节点选择变化
  const handleSelectionChange = useCallback((nodeIds: string[]) => {
    setLocalSelectedIds(nodeIds);

    // 在编辑模式下，如果选择了单个节点，自动加载到编辑面板
    if (editMode && nodeIds.length === 1 && outlineData) {
      const findNodeById = (nodes: OutlineNodeType[], id: string): OutlineNodeType | null => {
        for (const node of nodes) {
          if (node.id === id) return node;
          if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const selectedNode = findNodeById(outlineData.nodes, nodeIds[0]);
      if (selectedNode) {
        setSelectedNodeForEdit(selectedNode);
      }
    }
  }, [editMode, outlineData]);

  // 处理模式切换
  const handleModeToggle = () => {
    setEditMode(!editMode);
    if (!editMode) {
      setSelectedNodeForEdit(null);
    }
  };



  // 处理节点保存
  const handleNodeSave = async (updatedNode: OutlineNodeType) => {
    try {
      console.log('💾 保存节点:', updatedNode);

      // 更新本地大纲数据
      if (outlineData && outlineData.nodes) {
        const updateNodeInOutline = (nodes: OutlineNodeType[], targetNode: OutlineNodeType): OutlineNodeType[] => {
          return nodes.map(node => {
            if (node.id === targetNode.id) {
              return { ...targetNode };
            }
            if (node.children && node.children.length > 0) {
              return {
                ...node,
                children: updateNodeInOutline(node.children, targetNode)
              };
            }
            return node;
          });
        };

        const updatedOutline = {
          ...outlineData,
          nodes: updateNodeInOutline(outlineData.nodes, updatedNode)
        };
        setOutlineData(updatedOutline);

        // 保存到数据库
        const { outlineRepository } = await import('@/lib/db/repositories');
        await outlineRepository.update(updatedOutline.id, updatedOutline);

        // 通知父组件大纲数据已更新
        if (onOutlineDataChange) {
          onOutlineDataChange(updatedOutline);
        }

        // 重新加载大纲数据以确保同步
        await loadOutlineData();

        console.log('✅ 节点保存成功，数据已同步');
      }
    } catch (error) {
      console.error('❌ 节点保存失败:', error);
    }
  };

  // 处理编辑面板关闭
  const handleEditPanelClose = () => {
    setSelectedNodeForEdit(null);
  };

  // 处理全屏切换
  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理主题切换
  const handleThemeToggle = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  // 处理确认
  const handleConfirm = () => {
    console.log('🔍 OutlineManagementDialog 确认选择:', {
      localSelectedIds,
      outlineData: outlineData ? { id: outlineData.id, title: outlineData.title, nodesCount: outlineData.nodes?.length || 0 } : null,
      bookId,
      timestamp: new Date().toISOString()
    });

    onOutlineNodesChange(localSelectedIds);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setLocalSelectedIds([...selectedOutlineNodeIds]); // 恢复原始状态
    onClose();
  };

  if (!isOpen) return null;

  // 动态样式类名
  const overlayClasses = `fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ${
    isDarkTheme ? 'bg-black bg-opacity-70' : 'bg-black bg-opacity-50'
  }`;

  const dialogClasses = `
    ${isFullscreen
      ? "fixed inset-0 w-screen h-screen rounded-none"
      : "w-full max-w-7xl h-[85vh] max-h-[900px] min-h-[700px] rounded-xl"
    }
    ${isDarkTheme
      ? "bg-gradient-to-br from-gray-900 to-gray-800 text-gray-100"
      : "bg-gradient-to-br from-gray-50 to-white text-gray-900"
    }
    shadow-2xl transition-all duration-300 ease-out flex flex-col
  `.trim();

  return createPortal(
    <div className={overlayClasses} onClick={handleCancel}>
      <div
        className={dialogClasses}
        onClick={e => e.stopPropagation()}
      >
        {/* 对话框头部 */}
        <div className={`flex items-center justify-between p-6 border-b ${
          isDarkTheme ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center space-x-4">
            <h3 className={`text-xl font-semibold ${
              isDarkTheme ? 'text-gray-100' : 'text-gray-900'
            }`}>
              大纲管理
            </h3>

            {/* 模式切换按钮 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleModeToggle}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${
                  editMode
                    ? isDarkTheme
                      ? 'bg-blue-600 text-white hover:bg-blue-500'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                    : isDarkTheme
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={editMode ? '切换到选择模式' : '切换到编辑模式'}
              >
                {editMode ? (
                  <span className="flex items-center gap-1">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                    编辑模式
                  </span>
                ) : (
                  <span className="flex items-center gap-1">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    选择模式
                  </span>
                )}
              </button>
            </div>

            <div className={`text-sm ${
              isDarkTheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {editMode ? (
                selectedNodeForEdit ? `编辑: ${selectedNodeForEdit.title}` : '点击节点进行编辑'
              ) : (
                `已选择: ${localSelectedIds.length} 个节点`
              )}
            </div>
            {!isFullscreen && (
              <div className={`text-xs px-2 py-1 rounded ${
                isDarkTheme ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
              }`}>
                F11 全屏
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* 主题切换按钮 */}
            <button
              onClick={handleThemeToggle}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isDarkTheme
                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
              title={isDarkTheme ? '切换到浅色主题' : '切换到深色主题'}
            >
              <ThemeIcon isDark={isDarkTheme} />
            </button>

            {/* 全屏切换按钮 */}
            <button
              onClick={handleFullscreenToggle}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isDarkTheme
                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
              title={isFullscreen ? '退出全屏 (F11)' : '全屏显示 (F11)'}
            >
              <FullscreenIcon isFullscreen={isFullscreen} />
            </button>

            {/* 关闭按钮 */}
            <button
              onClick={handleCancel}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isDarkTheme
                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
              title="关闭 (Esc)"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 主体内容 - 左右分栏布局 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 大纲树级选择器 */}
          <div className={`flex flex-col transition-all duration-300 ${
            editMode && selectedNodeForEdit ? 'w-2/3' : 'w-full'
          }`}>
            <div className="flex-1 overflow-hidden">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  {/* 骨架屏加载动画 */}
                  <div className="w-full max-w-sm space-y-3 p-4">
                    <div className={`h-4 rounded animate-pulse ${
                      isDarkTheme ? 'bg-gray-700' : 'bg-gray-200'
                    }`}></div>
                    <div className={`h-4 rounded animate-pulse w-3/4 ${
                      isDarkTheme ? 'bg-gray-700' : 'bg-gray-200'
                    }`}></div>
                    <div className={`h-4 rounded animate-pulse w-1/2 ${
                      isDarkTheme ? 'bg-gray-700' : 'bg-gray-200'
                    }`}></div>
                    <div className={`h-4 rounded animate-pulse w-5/6 ${
                      isDarkTheme ? 'bg-gray-700' : 'bg-gray-200'
                    }`}></div>
                  </div>
                </div>
              ) : outlineData ? (
                <div className="h-full">
                  <OutlineTreeSelector
                    outline={outlineData}
                    selectedNodeIds={localSelectedIds}
                    onSelectionChange={handleSelectionChange}
                    showCanvasPreview={false}
                  />
                  {editMode && (
                    <div className={`mt-4 p-3 rounded-lg border ${
                      isDarkTheme ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
                    }`}>
                      <div className={`text-sm ${isDarkTheme ? 'text-blue-300' : 'text-blue-700'}`}>
                        💡 编辑模式：选择一个节点后，在右侧编辑面板中修改其专有字段
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className={`text-center ${
                    isDarkTheme ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    <div className="text-4xl mb-4">📋</div>
                    <div className={`text-lg font-medium mb-2 ${
                      isDarkTheme ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      暂无大纲数据
                    </div>
                    <div className="text-sm">请先创建大纲内容</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 编辑面板 */}
          {editMode && selectedNodeForEdit && (
            <div className={`w-1/3 border-l transition-all duration-300 ${
              isDarkTheme ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            }`}>
              <div className="h-full flex flex-col">
                {/* 编辑面板头部 */}
                <div className={`px-4 py-3 border-b ${
                  isDarkTheme ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${
                      isDarkTheme ? 'text-gray-100' : 'text-gray-900'
                    }`}>
                      编辑节点
                    </h4>
                    <button
                      onClick={handleEditPanelClose}
                      className={`p-1 rounded-full transition-colors ${
                        isDarkTheme
                          ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* 编辑表单 */}
                <div className="flex-1 overflow-auto p-4">
                  <NodeEditFormInline
                    node={selectedNodeForEdit}
                    onSave={handleNodeSave}
                    isDarkTheme={isDarkTheme}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 对话框底部 */}
        <div className={`flex items-center justify-between p-6 border-t ${
          isDarkTheme ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className={`text-sm ${
            isDarkTheme ? 'text-gray-400' : 'text-gray-500'
          }`}>
            {editMode ? (
              <span>💡 提示：点击节点进行编辑，编辑后的新字段将影响AI生成质量</span>
            ) : (
              <span>💡 提示：选择需要关联的大纲节点</span>
            )}
            {isFullscreen && (
              <span className="ml-4">按 Esc 退出全屏</span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 ${
                isDarkTheme
                  ? 'text-gray-300 bg-gray-700 hover:bg-gray-600 hover:text-white'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200 hover:shadow-md'
              }`}
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className={`px-6 py-2 rounded-lg font-medium text-white transition-all duration-200 transform hover:scale-105 hover:shadow-lg ${
                isDarkTheme
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500'
                  : 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700'
              }`}
            >
              确认选择 ({localSelectedIds.length})
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default OutlineManagementDialog;
