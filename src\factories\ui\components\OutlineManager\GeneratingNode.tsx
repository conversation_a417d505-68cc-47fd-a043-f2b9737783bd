import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';

interface GeneratingNodeData {
  id: string;
  parentNodeTitle: string;
  generatingType: 'chapter' | 'plot' | 'dialogue';
  customPrompt?: string;
  progress?: number;
  currentText?: string;
}

interface GeneratingNodeProps extends NodeProps {
  data: GeneratingNodeData;
}

const GeneratingNode: React.FC<GeneratingNodeProps> = ({ data, isConnectable }) => {
  const [animationPhase, setAnimationPhase] = useState(0);
  const [dots, setDots] = useState('');

  // 获取生成类型的中文名称
  const getTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      'chapter': '章节',
      'plot': '剧情点',
      'dialogue': '对话'
    };
    return labels[type] || '节点';
  };

  // 获取生成类型的图标
  const getTypeIcon = (type: string): JSX.Element => {
    switch (type) {
      case 'chapter':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'plot':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      case 'dialogue':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        );
    }
  };

  // 动画效果
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // 点点点动画
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 400);

    return () => clearInterval(interval);
  }, []);

  const typeLabel = getTypeLabel(data.generatingType);

  return (
    <div className="relative" style={{ pointerEvents: 'auto' }}>
      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-3 h-3 border-2 border-purple-400 bg-white"
        style={{
          top: '-6px',
          borderRadius: '50%',
          opacity: 0.8
        }}
      />

      {/* 主节点容器 */}
      <div className="relative min-w-[200px] max-w-[280px]" style={{ pointerEvents: 'auto' }}>
        {/* 渐变背景动画 */}
        <div 
          className="absolute inset-0 rounded-lg opacity-20"
          style={{
            background: `linear-gradient(45deg, 
              hsl(${240 + animationPhase * 10}, 70%, 60%), 
              hsl(${280 + animationPhase * 10}, 70%, 60%), 
              hsl(${320 + animationPhase * 10}, 70%, 60%)
            )`,
            animation: 'gradient-flow 3s ease-in-out infinite'
          }}
        />

        {/* 主内容区域 */}
        <div className="relative bg-white border-2 border-dashed border-purple-300 rounded-lg p-4 shadow-lg">
          {/* 头部区域 */}
          <div className="flex items-center space-x-3 mb-3">
            {/* AI图标 */}
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-4 w-4 text-white animate-spin" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>

            {/* 类型图标和标题 */}
            <div className="flex items-center space-x-2 text-purple-600">
              {getTypeIcon(data.generatingType)}
              <span className="font-medium text-sm">AI生成{typeLabel}中{dots}</span>
            </div>
          </div>

          {/* 生成信息 */}
          <div className="space-y-2">
            <div className="text-xs text-gray-600">
              <span className="font-medium">父节点：</span>
              <span className="text-gray-800">{data.parentNodeTitle}</span>
            </div>

            {data.customPrompt && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">自定义要求：</span>
                <div className="mt-1 p-2 bg-gray-50 rounded text-gray-700 italic">
                  "{data.customPrompt.length > 60 ? data.customPrompt.substring(0, 60) + '...' : data.customPrompt}"
                </div>
              </div>
            )}

            {/* 进度条 */}
            {data.progress !== undefined && (
              <div className="mt-3">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-600">生成进度</span>
                  <span className="text-xs text-purple-600 font-medium">{Math.round(data.progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${data.progress}%` }}
                  />
                </div>
              </div>
            )}

            {/* 当前生成文本预览 */}
            {data.currentText && (
              <div className="mt-3 p-2 bg-blue-50 rounded border-l-2 border-blue-400">
                <div className="text-xs text-blue-600 font-medium mb-1">实时生成预览</div>
                <div className="text-xs text-gray-700 italic">
                  {data.currentText.length > 80 ? data.currentText.substring(0, 80) + '...' : data.currentText}
                </div>
              </div>
            )}
          </div>

          {/* 状态指示器 */}
          <div className="absolute top-2 right-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          </div>
        </div>

        {/* 流动线条效果 */}
        <div className="absolute inset-0 rounded-lg overflow-hidden pointer-events-none">
          <div 
            className="absolute inset-0 opacity-30"
            style={{
              background: `linear-gradient(90deg, 
                transparent 0%, 
                rgba(139, 92, 246, 0.3) 50%, 
                transparent 100%
              )`,
              animation: 'flow-line 2s linear infinite',
              transform: `translateX(${-100 + (animationPhase * 50)}%)`
            }}
          />
        </div>
      </div>

      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-3 h-3 border-2 border-purple-400 bg-white"
        style={{
          bottom: '-6px',
          borderRadius: '50%',
          opacity: 0.8
        }}
      />

      {/* CSS动画样式 */}
      <style jsx>{`
        @keyframes gradient-flow {
          0%, 100% { opacity: 0.2; }
          50% { opacity: 0.4; }
        }
        
        @keyframes flow-line {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
};

export default GeneratingNode;
