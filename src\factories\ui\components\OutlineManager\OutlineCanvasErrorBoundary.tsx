import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

/**
 * OutlineCanvas错误边界组件
 * 用于捕获和处理React Flow相关的错误，防止整个应用崩溃
 */
class OutlineCanvasErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 生成错误ID用于追踪
    const errorId = `outline-canvas-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 OutlineCanvas错误边界捕获到错误:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      retryCount: this.retryCount
    });

    // 更新状态包含错误信息
    this.setState({
      errorInfo
    });

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告（可以集成到错误监控服务）
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 这里可以集成到错误监控服务，如Sentry、LogRocket等
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      errorId: this.state.errorId,
      retryCount: this.retryCount
    };

    // 暂时只记录到控制台，实际项目中应该发送到错误监控服务
    console.error('📊 错误报告:', errorReport);
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      console.log(`🔄 尝试重新加载OutlineCanvas (第${this.retryCount}次)`);
      
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: ''
      });
    } else {
      console.warn('⚠️ 已达到最大重试次数，建议刷新页面');
    }
  };

  private handleReset = () => {
    this.retryCount = 0;
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      const canRetry = this.retryCount < this.maxRetries;

      return (
        <div className="outline-canvas-error-boundary">
          <div className="error-container">
            <div className="error-header">
              <div className="error-icon">⚠️</div>
              <h2>大纲画布遇到问题</h2>
              <p className="error-subtitle">
                {canRetry ? '我们正在尝试恢复，请稍候...' : '多次尝试后仍无法恢复'}
              </p>
            </div>

            <div className="error-details">
              <details className="error-details-toggle">
                <summary>查看错误详情</summary>
                <div className="error-content">
                  <div className="error-section">
                    <h4>错误信息:</h4>
                    <pre className="error-message">{error?.message}</pre>
                  </div>
                  
                  {error?.stack && (
                    <div className="error-section">
                      <h4>错误堆栈:</h4>
                      <pre className="error-stack">{error.stack}</pre>
                    </div>
                  )}
                  
                  {errorInfo?.componentStack && (
                    <div className="error-section">
                      <h4>组件堆栈:</h4>
                      <pre className="error-component-stack">{errorInfo.componentStack}</pre>
                    </div>
                  )}
                  
                  <div className="error-section">
                    <h4>错误ID:</h4>
                    <code className="error-id">{this.state.errorId}</code>
                  </div>
                </div>
              </details>
            </div>

            <div className="error-actions">
              {canRetry ? (
                <button 
                  className="btn btn-primary"
                  onClick={this.handleRetry}
                >
                  🔄 重新加载 ({this.maxRetries - this.retryCount} 次机会)
                </button>
              ) : (
                <button 
                  className="btn btn-secondary"
                  onClick={this.handleReset}
                >
                  🔄 重置状态
                </button>
              )}
              
              <button 
                className="btn btn-outline"
                onClick={this.handleReload}
              >
                🔃 刷新页面
              </button>
            </div>

            <div className="error-tips">
              <h4>💡 可能的解决方案:</h4>
              <ul>
                <li>检查网络连接是否正常</li>
                <li>尝试刷新页面</li>
                <li>清除浏览器缓存</li>
                <li>如果问题持续存在，请联系技术支持</li>
              </ul>
            </div>
          </div>

          <style jsx>{`
            .outline-canvas-error-boundary {
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 400px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 8px;
              border: 1px solid #e9ecef;
            }

            .error-container {
              max-width: 600px;
              background: white;
              border-radius: 12px;
              padding: 32px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              text-align: center;
            }

            .error-header {
              margin-bottom: 24px;
            }

            .error-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }

            .error-header h2 {
              color: #dc3545;
              margin: 0 0 8px 0;
              font-size: 24px;
            }

            .error-subtitle {
              color: #6c757d;
              margin: 0;
              font-size: 16px;
            }

            .error-details {
              margin: 24px 0;
              text-align: left;
            }

            .error-details-toggle {
              cursor: pointer;
              padding: 12px;
              background: #f8f9fa;
              border-radius: 6px;
              border: 1px solid #e9ecef;
            }

            .error-details-toggle summary {
              font-weight: 500;
              color: #495057;
            }

            .error-content {
              margin-top: 16px;
              padding-top: 16px;
              border-top: 1px solid #e9ecef;
            }

            .error-section {
              margin-bottom: 16px;
            }

            .error-section h4 {
              margin: 0 0 8px 0;
              font-size: 14px;
              color: #495057;
            }

            .error-message,
            .error-stack,
            .error-component-stack {
              background: #f8f9fa;
              border: 1px solid #e9ecef;
              border-radius: 4px;
              padding: 12px;
              font-size: 12px;
              color: #dc3545;
              overflow-x: auto;
              white-space: pre-wrap;
              word-break: break-word;
            }

            .error-id {
              background: #e9ecef;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              color: #495057;
            }

            .error-actions {
              display: flex;
              gap: 12px;
              justify-content: center;
              margin: 24px 0;
              flex-wrap: wrap;
            }

            .btn {
              padding: 12px 24px;
              border-radius: 6px;
              border: none;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s;
            }

            .btn-primary {
              background: #007bff;
              color: white;
            }

            .btn-primary:hover {
              background: #0056b3;
            }

            .btn-secondary {
              background: #6c757d;
              color: white;
            }

            .btn-secondary:hover {
              background: #545b62;
            }

            .btn-outline {
              background: transparent;
              color: #007bff;
              border: 1px solid #007bff;
            }

            .btn-outline:hover {
              background: #007bff;
              color: white;
            }

            .error-tips {
              text-align: left;
              background: #f8f9fa;
              padding: 16px;
              border-radius: 6px;
              border-left: 4px solid #17a2b8;
            }

            .error-tips h4 {
              margin: 0 0 12px 0;
              color: #17a2b8;
              font-size: 16px;
            }

            .error-tips ul {
              margin: 0;
              padding-left: 20px;
            }

            .error-tips li {
              margin-bottom: 4px;
              color: #495057;
            }
          `}</style>
        </div>
      );
    }

    return this.props.children;
  }
}

export default OutlineCanvasErrorBoundary;
