"use client";

import React, { useState } from 'react';
import { CircleButton } from '@/adapters/ui';
import { useOutlineManager } from '../../hooks/useOutlineManager';

interface OutlineManagerButtonProps {
  onClick?: () => void;
  position?: 'bottom-right' | 'custom' | 'wheel';
  angle?: number; // 角度，用于在圆形菜单中定位
  distance?: number; // 距离中心点的距离
  wheelIndex?: number; // 在轮盘中的索引位置
  wheelTotal?: number; // 轮盘中的总按钮数
  wheelStartAngle?: number; // 轮盘起始角度
  wheelEndAngle?: number; // 轮盘结束角度
}

/**
 * 大纲管理按钮组件
 * 支持轮盘式布局，可以在右下角以轮盘形式排列
 */
export const OutlineManagerButton: React.FC<OutlineManagerButtonProps> = ({
  onClick,
  position = 'bottom-right',
  angle = 135, // 默认在右下方，135度位置
  distance = 60,
  wheelIndex = 0,
  wheelTotal = 1,
  wheelStartAngle = 90, // 默认从90度开始（左上方）
  wheelEndAngle = 180 // 默认到180度结束（右上方）
}) => {
  const { openOutlineManager } = useOutlineManager();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      openOutlineManager();
    }
  };

  // 计算按钮位置样式
  const getPositionStyle = () => {
    if (position === 'custom' && angle !== undefined) {
      // 将角度转换为弧度
      const radians = (angle * Math.PI) / 180;
      // 计算x和y坐标
      const x = Math.cos(radians) * distance;
      const y = Math.sin(radians) * distance;

      return {
        transform: `translate(${x}px, ${y}px)`,
      };
    }

    if (position === 'wheel' && wheelTotal > 0) {
      // 计算在轮盘中的角度
      const angleRange = wheelEndAngle - wheelStartAngle;
      const buttonAngle = wheelStartAngle + (angleRange * wheelIndex / (wheelTotal - 1));

      // 将角度转换为弧度
      const radians = (buttonAngle * Math.PI) / 180;

      // 计算x和y坐标
      const x = Math.cos(radians) * distance;
      const y = Math.sin(radians) * distance;

      return {
        transform: `translate(${x}px, ${y}px)`,
      };
    }

    // 默认底部右侧位置
    return {};
  };

  return (
    <div
      className={`${position === 'bottom-right' ? 'fixed bottom-8 right-8 z-40' : 'relative'}`}
      style={position === 'custom' || position === 'wheel' ? getPositionStyle() : {}}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CircleButton
        icon={
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        }
        text="大纲"
        color="var(--color-primary)"
        size="medium"
        onClick={handleClick}
        className={`shadow-lg transition-all duration-300 ${isHovered ? 'shadow-xl scale-110 animate-pulse' : ''}`}
      />
    </div>
  );
};
