"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  CoreMystery, 
  SegmentStructure, 
  ShortStoryMode 
} from '@/factories/ai/services/types/ShortStoryTypes';

// 创作流程步骤类型（简化版）
export type CreationStep = 'editing';

// 布局状态接口
interface LayoutState {
  leftPanelWidth: number;
  rightPanelWidth: number;
  rightPanelCollapsed: boolean;
  isMobile: boolean;
  focusMode: boolean; // 专注模式，隐藏左右面板
}

// 创作参数接口
interface CreationParams {
  userInput: string;
  storyMode: ShortStoryMode;
  targetSegments: number;
  customPhilosophy: string;
  selectedCharacterIds: string[];
  selectedWorldBuildingIds: string[];
  selectedTerminologyIds: string[];
  selectedOutlineNodeIds: string[];
  selectedACEFrameworkIds: string[];
}

// 编辑状态接口
interface EditingState {
  currentEditingSegment: number;
  isGenerating: boolean;
  streamingContent: string;
  lastSavedAt: Date | null;
  hasUnsavedChanges: boolean;
}

// 节奏控制状态接口
interface RhythmState {
  currentPhase: 'setup' | 'compression' | 'climax' | 'resolution';
  emotionCurve: number[]; // 情绪强度曲线
  phaseStrategy: any; // 阶段策略数据
}

// 完整的store状态接口
interface ShortStoryState {
  // 基础数据
  bookId: string;
  
  // 创作流程状态
  currentStep: CreationStep;
  
  // 创作数据
  coreMystery: CoreMystery | null;
  segments: SegmentStructure[];
  fullText: string;
  
  // 创作参数
  params: CreationParams;
  
  // UI状态
  layoutState: LayoutState;
  isPersonaPanelOpen: boolean; // 新增：人设面板开关
  
  // 编辑状态
  editingState: EditingState;
  
  // 节奏控制状态
  rhythmState: RhythmState;
  
  // 错误状态
  error: string | null;
  
  // 操作方法
  setBookId: (bookId: string) => void;
  setCurrentStep: (step: CreationStep) => void;
  setCoreMystery: (mystery: CoreMystery | null) => void;
  setSegments: (segments: SegmentStructure[]) => void;
  updateSegment: (index: number, content: string) => void;
  setFullText: (text: string) => void;
  updateParams: (params: Partial<CreationParams>) => void;
  setLayoutState: (state: Partial<LayoutState>) => void;
  setEditingState: (state: Partial<EditingState>) => void;
  setRhythmState: (state: Partial<RhythmState>) => void;
  setError: (error: string | null) => void;
  togglePersonaPanel: (isOpen?: boolean) => void; // 新增：切换人设面板
  
  // 复合操作
  resetCreation: () => void;
  saveProgress: () => void;
  loadProgress: (bookId: string) => void;
  toggleFocusMode: () => void;
}

// 默认状态
const defaultLayoutState: LayoutState = {
  leftPanelWidth: 30,
  rightPanelWidth: 25,
  rightPanelCollapsed: false,
  isMobile: false,
  focusMode: false
};

const defaultCreationParams: CreationParams = {
  userInput: '',
  storyMode: 'mystery',
  targetSegments: 20,
  customPhilosophy: '',
  selectedCharacterIds: [],
  selectedWorldBuildingIds: [],
  selectedTerminologyIds: [],
  selectedOutlineNodeIds: [],
  selectedACEFrameworkIds: []
};

const defaultEditingState: EditingState = {
  currentEditingSegment: -1,
  isGenerating: false,
  streamingContent: '',
  lastSavedAt: null,
  hasUnsavedChanges: false
};

const defaultRhythmState: RhythmState = {
  currentPhase: 'setup',
  emotionCurve: [],
  phaseStrategy: null
};

// 创建store
export const useShortStoryStore = create<ShortStoryState>()(
  persist(
    (set, get) => ({
      // 初始状态
      bookId: '',
      currentStep: 'editing',
      coreMystery: null,
      segments: [],
      fullText: '',
      params: defaultCreationParams,
      layoutState: defaultLayoutState,
      isPersonaPanelOpen: false, // 新增
      editingState: defaultEditingState,
      rhythmState: defaultRhythmState,
      error: null,

      // 基础设置方法
      setBookId: (bookId: string) => set({ bookId }),
      
      setCurrentStep: (step: CreationStep) => set({ currentStep: step }),
      
      setCoreMystery: (mystery: CoreMystery | null) => set({ coreMystery: mystery }),
      
      setSegments: (segments: SegmentStructure[]) => {
        set({ segments });
        // 自动更新情绪曲线
        const emotionCurve = segments.map(seg => seg.tensionLevel || 0);
        get().setRhythmState({ emotionCurve });
      },
      
      updateSegment: (index: number, content: string) => {
        const segments = [...get().segments];
        if (segments[index]) {
          // 计算真正的字数（去除空格和标点符号）
          const wordCount = content.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length;

          segments[index] = {
            ...segments[index],
            content,
            wordCount
          };
          set({
            segments,
            editingState: {
              ...get().editingState,
              hasUnsavedChanges: true
            }
          });
        }
      },
      
      setFullText: (text: string) => set({ fullText: text }),
      
      updateParams: (newParams: Partial<CreationParams>) => 
        set({ params: { ...get().params, ...newParams } }),
      
      setLayoutState: (newState: Partial<LayoutState>) => 
        set({ layoutState: { ...get().layoutState, ...newState } }),
      
      setEditingState: (newState: Partial<EditingState>) => 
        set({ editingState: { ...get().editingState, ...newState } }),
      
      setRhythmState: (newState: Partial<RhythmState>) =>
        set({ rhythmState: { ...get().rhythmState, ...newState } }),
      
      setError: (error: string | null) => set({ error }),

      togglePersonaPanel: (isOpen?: boolean) => {
        set(state => ({
          isPersonaPanelOpen: typeof isOpen === 'boolean' ? isOpen : !state.isPersonaPanelOpen
        }));
      },

      // 复合操作
      resetCreation: () => set({
        currentStep: 'editing',
        coreMystery: null,
        segments: [],
        fullText: '',
        params: defaultCreationParams,
        editingState: defaultEditingState,
        rhythmState: defaultRhythmState,
        error: null
      }),

      saveProgress: () => {
        const state = get();
        const progressData = {
          bookId: state.bookId,
          currentStep: state.currentStep,
          coreMystery: state.coreMystery,
          segments: state.segments,
          fullText: state.fullText,
          params: state.params,
          rhythmState: state.rhythmState,
          savedAt: new Date().toISOString()
        };
        
        localStorage.setItem(`short-story-progress-${state.bookId}`, JSON.stringify(progressData));
        
        set({
          editingState: {
            ...state.editingState,
            lastSavedAt: new Date(),
            hasUnsavedChanges: false
          }
        });
      },

      loadProgress: (bookId: string) => {
        try {
          const savedData = localStorage.getItem(`short-story-progress-${bookId}`);
          if (savedData) {
            const progressData = JSON.parse(savedData);
            set({
              bookId: progressData.bookId,
              currentStep: progressData.currentStep,
              coreMystery: progressData.coreMystery,
              segments: progressData.segments || [],
              fullText: progressData.fullText || '',
              params: { ...defaultCreationParams, ...progressData.params },
              rhythmState: { ...defaultRhythmState, ...progressData.rhythmState },
              editingState: {
                ...defaultEditingState,
                lastSavedAt: progressData.savedAt ? new Date(progressData.savedAt) : null
              }
            });
          }
        } catch (error) {
          console.error('加载创作进度失败:', error);
        }
      },

      toggleFocusMode: () => {
        const currentFocusMode = get().layoutState.focusMode;
        set({
          layoutState: {
            ...get().layoutState,
            focusMode: !currentFocusMode
          }
        });
      }
    }),
    {
      name: 'short-story-workspace',
      partialize: (state) => ({
        layoutState: state.layoutState,
        // 只持久化布局状态，创作数据通过saveProgress单独保存
      })
    }
  )
);

// 导出类型供其他组件使用
export type { LayoutState, CreationParams, EditingState, RhythmState };
