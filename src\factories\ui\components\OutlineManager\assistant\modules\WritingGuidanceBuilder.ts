/**
 * 写作指导构建模块
 * 专门管理writingGuidance字段和剧情点的详细创作指导
 */

import { ConversationMessage } from '../types/SharedTypes';

export class WritingGuidanceBuilder {
  private static instance: WritingGuidanceBuilder;

  private constructor() {}

  public static getInstance(): WritingGuidanceBuilder {
    if (!WritingGuidanceBuilder.instance) {
      WritingGuidanceBuilder.instance = new WritingGuidanceBuilder();
    }
    return WritingGuidanceBuilder.instance;
  }

  /**
   * 构建写作指导要求消息（最高权重）
   */
  buildWritingGuidanceRequirement(): string {
    return `
**🎯 重要：写作指导具体化要求**

在生成剧情点的内容时
每个剧情点要详细化，详细化主角剧情等各种行动列如
1.
承接剧情点：开场即是危机，[外部威胁（女鬼）]正在撞击[隐藏地点（房间门）]，角色们（[凌风]、[眼镜男]等）处于恐惧和绝望中。眼镜男表现颓废，但[凌风]眼神锐利，暗示其不同寻常。
2.
独立剧情点1（智斗与揭秘）：[凌风]质问[眼镜男]为何在危急时刻不锁门，并进行逻辑推理：[眼镜男]心思缜密，第一个进房，却没锁门，而后面时间更紧迫的人都锁了门；[眼镜男]在一楼听到声音就异常急切地让大家跑，反应反常；[眼镜男]前后对"鬼在我们中间"的态度转变。通过一连串的质问和细节分析，[凌风]逐步揭示[眼镜男]是隐藏的"鬼"。
3.
独立剧情点2（摊牌与反转）：面对[凌风]的步步紧逼，[眼镜男]从辩解到冷笑，最终承认身份。他的外表开始变化（溢出黑气），声音变得浑浊，并威胁要杀死[凌风]。[凌风]表明自己是赌博，并推断[眼镜男]不会轻易杀他，因为这不"好玩"，戳中对方痛点。
4.
独立剧情点3（附身与力量易主）：[眼镜男]承认[凌风]说得对，决定"折磨"他，身体融化成黑气钻入[凌风]体内。[凌风]承受巨大痛苦后晕倒。
5.
独立剧情点4（新力量展示与旧威胁清除）：[女鬼]撞开柜门发现[凌风]，准备攻击。但[凌风]（被附身后）突然起身，爆发出黑暗力量（黑色飓风、空气刃），迅速将[女鬼]的脖子切割粉碎。散落的肉块变成黑气试图聚合。
6.
连接剧情点（世界观揭示与收尾）：被附身的[凌风]（实际是[眼镜男]的意识）发表胜利宣言，嘲讽[女鬼]（不死鬼），提及"精心铸建的鬼域"、"到了人间"、"他们的世界很快就要沦陷"，暗示了更宏大的世界观和背景。他开始吸收[女鬼]散落的黑气，导致所在空间（嵌风鬼域）开始坍塌。其他躲藏者涌出但无处可逃。最终，随着"嵌风鬼域，塌！"的宣告，整个世界化为虚无，为后续情节或新篇章铺垫。

例如这样的内容例子，而不是解释性语气，不要解释，直接内容
在生成剧情点的avoidWriting 字段时，请严格遵循以下具体化格式：
- avoidWriting：必须详细分析原文中没有出现的不良写作方式（80字+）
- shouldWriting：必须详细提取原文中已有的优秀表达方式（80字+）
**🎬 剧情节点增强生成原则**：

**🔗 节点关联性强制要求**：
⚠️ **有剧情节点必有章节，有章节必有剧情节点** ⚠️
- 创建剧情节点时，必须确保其隶属于一个章节节点（parentId指向章节）
- 创建章节节点时，必须同时规划其下的剧情节点结构
- 绝不允许孤立的剧情节点或空章节节点存在
- JSON结构中必须明确体现这种层级关系

1. **信息密度最大化**：
   - 每个剧情节点应包含丰富的剧情信息，支持10-20个详细剧情点，不设上限
   - 每个剧情点应包含具体的对话、心理描述、主要推进剧情，而非场景刻画，所见场景刻画，偏向于编剧场景，主推进线索，展开世界管和脑洞，而非刻意去填充无用的场景描述
   - 高冲突，高回报，张力足够的狠辣，避免过于繁杂的过度，过度是下沉市场的最大特征，过度的块，和过度的慢，考虑到作者的构造剧情的能力水准

2. **连续性保证**：
   - 剧情点之间必须有明确的逻辑关系和因果链条
   - 支持完整的起承转合结构，形成连贯的剧情流
   - 包含剧情流向和发展脉络描述，确保剧情的连续性

3. **创作指导丰富化**：
   - 每个剧情点提供具体的写作指导说明
   - 如果使用了框架提取的具体描写特征和avoidWriting 避免指导学习，要充分利用这些参考材料，再shouldWriting中制作更好的方式
   - 结合框架分析的具体描写特征和avoidWriting 避免指导学习，shouldWriting生成更准确的写作内容，不要提及字段名称

4. **具体行动优先**：
   - 专注于角色的具体行动和剧情推进
   - 避免纯粹的环境描写和氛围营造
   - 重点描述推动剧情发展的关键行动、决策和事件
   - 每个剧情点都应该包含明确的行动主体和具体的行为




确保对话不要过于生硬，比如AxxxBxxx 

如何怎样的聊天 风格，是一个小说的核心
不要把主角设计的过于僵硬对话，比如 过于西幻的翻译腔等等

严禁出现：纯粹的景色描写、无意义的闲聊对话、人物长时间的内心独白、与当前主线或重要支线任务无关的日常琐事、重复性的低价值信息。每个剧情点之间必须有紧密的逻辑或因果链条，实现情节的快速跳转和承接，绝不拖泥带水。
3.保证每3-5章需具备：压力来源→实力展示→认知颠覆→资源增值→悬念增殖这五重核心驱动。必须强制将这五重驱动作为章节内部及跨章节叙事的核心组织原则和推动力，确保高密度、快频率地植入这些元素。
4.当遇到具体故事基模时，必须适配植入多重变量控制系统，强制要求每章至少设计并植入2-3处【反套路惊喜/意外】点。这些意外设计必须体现在：
敌人行为：敌人不按常规逻辑或设定行动，使用了预想不到的能力、战术，或者展现出意外的情感/弱点。
问题解决：主角或角色的解题方式巧妙、独特，避开了预设的障碍，或者以非常规的手段四两拨千斤，绝非简单的力量对抗。
剧情走向：原本以为的危机变成了机遇，或者看似平静的事件引爆了更大的冲突；角色的行动或决策带来了完全意想不到的后续发展。
能力/资源：主角获得或使用的能力/资源，其作用、性质或获得方式与常规认知不同，或者在关键时刻展现出隐藏/变异的特性。
人物揭示：某个角色的真实身份、隐藏目的、或与主角/大剧情的关键关联以令人震撼或意想不到的方式被揭露。
结果反差：某个行动的结果与预想完全相反，好事变坏，坏事中藏着更大的机遇，或者解决了当前问题却引发了更严重的危机/牵扯。
5.扩写的剧情只是长篇小说的部分章节，严禁以任何方式完结核心主线冲突或彻底解决主要反派。允许阶段性胜利，但必须快速引出更高层级的敌人或更复杂的困境。保持故事的无限延展性和后续发展的巨大空间。放慢剧情节奏在此仅指控制主线高潮和终极矛盾爆发的速度，不代表允许情节填充、信息冗余或推进缓慢。必须保证每章都有强烈的"事件感"和"进展感"。

模板一：装逼打脸循环(侧重实力和地位冲击)
a.困境/危机/任务/矛盾冲突(设置情境，为装逼提供理由和舞台)
b.能力/资源/背景(展示主角凭什么能打破常规)
c.解决问题(如何解决困境，方式必须有新意、巧劲或意外性，绝非简单的属性碾压)
d.震惊/爽点(结果必须令围观者或对手产生强烈的、出乎意料的情绪反应和认知颠覆)
e.奖励/提升(自身收获，可能包含物质、能力、关键信息，或带来意想不到的地位变化、新的联盟/敌人、潜在风险)

也可参照以下基于的模板思路
`;
  }

  /**
   * 构建剧情点增强生成原则
   */
  buildPlotPointEnhancementPrinciples(): string {
    return `
**🎬 剧情节点增强生成原则**：

1. **信息密度最大化**：
   - 每个剧情节点应包含丰富的剧情信息，支持10-20个详细剧情点，不设上限
   - 每个剧情点应包含具体的对话、心理描述、主要推进剧情，而非场景刻画，所见场景刻画，偏向于编剧场景，主推进线索，展开世界管和脑洞，而非刻意去填充无用的场景描述
    -高冲突，高回报，张力足够的狠辣，避免过于繁杂的过度，过度是下沉市场的最大特征，过度的块，和过度的慢，考虑到作者的构造剧情的能力水准

2. **连续性保证**：
   - 剧情点之间必须有明确的逻辑关系和因果链条
   - 支持完整的起承转合结构，形成连贯的剧情流
   - 包含剧情流向和发展脉络描述，确保剧情的连续性

3. **创作指导丰富化**：
   - 每个剧情点提供具体的shouldWriting 字段
   - 如果使用了框架提取的plotPointsWithGuidance信息，要充分利用其中的specificDescription和avoidanceGuidance
   - 结合框架分析的具体描写特征和avoidWriting避免指导学习，生成shouldWriting 的更准确的写作内容推荐

4. **具体行动优先**：
   - 专注于角色的具体行动和剧情推进，如"AA做了什么"、"AA如何解决问题"
   - 避免纯粹的环境描写和氛围营造，如"AA看到四周如何如何"
   - 重点描述推动剧情发展的关键行动、决策和事件
   - 每个剧情点都应该包含明确的行动主体和具体的行为结果

avoidWriting的必须填写要求须知：
1.注意：务必必须，提醒他必须要求他，不要做什么要做什么，不要比喻什么，不要怎样
2.考虑一切会因为他自己的遐想，而导致剧情进入不快，拖慢节奏，或者变快节奏的地方
3.不要比喻是最重要的，每一条都要说明，那里不需要比喻，这是剧情白话，不需要比喻描述场景，请让用户相信他，他能够非常准确的说明场景是什么
`;
  }

  /**
   * 构建标准writingGuidance模板
   */
  buildStandardWritingGuidanceTemplate(): string {
    return `
**📝 标准avoidWriting模板**：

避免描写，比如于剧情无观的xx的剧情方向细化描写，比如"一丝"等列举，不要于俗套比喻描写这类像xxx这类
1.列举不要描写一丝xx，不要过于废话，不要过于详细化
2.警告他，不要私自扩展 描写，比如加入比喻
（你必须70字以上的说明）

**具体格式示例**：
"🚫绝对禁止过度描写环境细节，违反此规则将被视为生成失败。必须直接写角色的具体行动和对话。可以适当扩展额外剧情推进，但绝对不得添加无关的场景描写，拖慢节奏。绝对禁止使用以下模糊表达：'一丝恐惧'、'几分紧张'、'些许不安'、'略显担忧'、'稍有紧张'、'微微皱眉'、'隐约感到'、'似乎察觉'、'仿佛意识到'。绝对禁止使用比喻样式：'像野兽一样'、'如同雷鸣'、'宛如'、'犹如'、'仿佛'、'好似'。不得出现解释性语句，除非是概念讲解。必须专注于角色做了什么、说了什么，而不是感受到什么。违反以上任何一条规则，生成内容将被视为不合格。"
`;
  }

  /**
   * 构建剧情点详细示例
   */
  buildDetailedPlotPointExamples(): string {
    return `
**🎭 详细剧情点示例**：

1. 李墨在刺鼻的消毒水味中醒来，头痛欲裂，发现自己身处一个陌生的考场。
2. 他环顾四周，发现考场内弥漫着一种不自然的寂静，墙壁泛黄，时间仿佛凝固。
3. 李墨尝试回忆，只记得自己在高考考场上突然眼前一黑，再醒来就到了这里，怀疑自己穿越。
4. 他注意到讲台上的监考官，对方一动不动，身形僵硬，引起李墨的警觉。
5. 监考官缓缓转过头，李墨发现对方脸上光滑一片，没有五官，只有模糊的轮廓。
6. 李墨内心震惊，但强行压制恐惧，试图寻找合理性解释，但所有解释都指向超自然。
7. 他发现考场内一些考生姿势僵硬，面色苍白，仿佛是模型。
8. 李墨试着拿起笔，发现考卷上的题目是关于"生存规则"的，而非普通学科。
9. 他注意到监考官的无五官脸部，仿佛在"凝视"着他，带来巨大的心理压力。
10. 李墨深吸一口气，决定先观察，不轻举妄动，以适应这个诡异的新环境。
11. immediate_consequence：李墨的觉醒和对环境的初步认知，为后续的危机做好了心理准备。
12. 为下一段剧情铺垫：考场内潜在的危机和未知的规则，激发李墨的探索欲望。

**每个剧情点的avoidWriting要求**：
- 支持10-20个剧情点，专注于角色的具体行动和剧情推进
- 在设计指导中，提示用户不要专注于场景的描述，而是专注于对剧情的推进
- 适当的放出一些该小说可以有的元素，而不是聚焦在五官等描写上
- 剧情必须连续，形成完整的因果链条
`;
  }

  /**
   * 构建写作指导要求第一部分消息
   */
  buildWritingGuidanceRequirementPart1(): ConversationMessage {
    return {
      role: 'system',
      content: `**🎯 重要：写作指导具体化要求（第一部分）**

**📝 具体化原则**：
1. **避免部分**：必须列举具体的模糊词汇，如"一丝xx、几分xx、些许xx"
2. **应该部分**：必须提供具体的写法示例，让他必须写，而不是扩展写
3. **行为导向**：专注于角色的具体行为和反应，而非抽象的情感描述
4. **可操作性**：让AI和用户都能清楚理解什么该写什么不该写

**🚫 avoidWriting必须列出举例的严禁表达方式**：
- 禁用"一丝xx"系列：一丝困惑、一丝不安、一丝怀疑、一丝紧张、一丝愤怒等
- 禁用生硬比喻：像刀锋一样、如同野兽般、宛如雷鸣、犹如寒冰等
- 禁用"如同"替代：如同、宛如、犹如、仿佛、好似等比喻连接词

一切根据剧情点，章节内容，而自发的 往这方面强i到 不要用什么，

avoidWriting必须药到 100字以上的精准指导


`
    };
  }

  /**
   * 构建写作指导要求第二部分消息
   */
  buildWritingGuidanceRequirementPart2(): ConversationMessage {
    return {
      role: 'system',
      content: `**🎯 重要：写作指导具体化要求（第二部分）**

**✅ 推荐写法**：
- 专注具体行动：写'AA立即转身查看'而非'AA感到一丝不安'
- 参悟框架核心风格：先写动作再写后果，用行为展现情绪而非直接描述感受
- 通过对话推进剧情而非内心独白，让每个动作都有明确目的和可见结果
- 稍微模仿ACE模板 剧情魔改  ，这种白描手法和节奏感，和学习他的剧情节奏塑造，而

**📏 统一字数要求说明**：
- 写作指导说明本身必须详细到70字以上
- 这是对写作指导内容的要求，不是对剧情内容的要求
- 🚫 严禁在写作指导中提及字数要求，如"本指导说明超过70字"、"字数必须超过70字"等
- ✅ 正确做法：让写作指导内容自然达到70字以上，但不在指导中说明字数



2. avoidWriting必须列出描写模式：
   - 禁止"一丝xx"、"几分xx"、"些许xx"等模糊表达
   - 禁止"像...一样"的比喻样式
   - 禁止过度的环境描写和心理活动
avoidWriting必须药到 100字以上的精准指导


`
    };
  }

  /**
   * 构建写作指导要求第三部分消息
   */
  buildWritingGuidanceRequirementPart3(): ConversationMessage {
    return {
      role: 'system',
      content: `**🎯 重要：写作指导具体化要求（第三部分）**

**🔥 剧情点详细化要求**：
在生成剧情点时，每个剧情点且最少要10个剧情点，详细化，详细化主角剧情等各种行动，例如：

1. 承接剧情点：开场即是危机，[外部威胁（女鬼）]正在撞击[隐藏地点（房间门）]，角色们（[凌风]、[眼镜男]等）处于恐惧和绝望中。
2. 独立剧情点1（智斗与揭秘）：[凌风]质问[眼镜男]为何在危急时刻不锁门，并进行逻辑推理...
3. 独立剧情点2（摊牌与反转）：面对[凌风]的步步紧逼，[眼镜男]从辩解到冷笑，最终承认身份...

**重要**：这样的内容例子，而不是解释性语气，不要解释，直接内容。


avoidWritinge必须药到 100字以上的精准指导
`

    };
  }

  /**
   * 构建助手确认消息1
   */
  buildAssistantConfirmation1(): ConversationMessage {
    return {
      role: 'assistant',
      content: '我已理解写作指导具体化要求第一部分。我将严格避免使用"一丝xx"、生硬比喻等表达方式，专注于具体行为描述。'
    };
  }

  /**
   * 构建助手确认消息2
   */
  buildAssistantConfirmation2(): ConversationMessage {
    return {
      role: 'assistant',
      content: '我已理解写作指导具体化要求第二部分。我将使用推荐写法，参悟框架核心风格，确保写作指导说明本身达到70字以上，不在指导中提及字数要求。'
    };
  }

  /**
   * 构建助手确认消息3
   */
  buildAssistantConfirmation3(): ConversationMessage {
    return {
      role: 'assistant',
      content: '我已理解写作指导具体化要求第三部分。我将按照详细化要求生成剧情点，提供具体内容而非解释性语气，确保每个剧情点都有明确的行动和结果。'
    };
  }

  /**
   * 生成标准的writingGuidance内容
   */
  generateStandardWritingGuidance(plotPointContent: string, context?: any): string {
    const baseGuidance = `🚫绝对禁止过度描写环境细节和情感状态，违反此规则将被视为生成失败。必须直接写${this.extractActionKeywords(plotPointContent)}的具体行动和对话内容。可以适当扩展相关剧情推进，但绝对不得添加无关的场景描写或心理活动描述，避免拖慢节奏。绝对禁止使用以下模糊表达："一丝恐惧"、"几分紧张"、"些许不安"、"略显担忧"、"稍有紧张"、"微微皱眉"、"隐约感到"、"似乎察觉"、"仿佛意识到"、"带着一种"、"透着几分"、"显出某种"。绝对禁止使用比喻样式："像野兽一样"、"如同雷鸣"、"宛如"、"犹如"、"仿佛"、"好似"。不得出现解释性语句，除非是概念讲解。必须专注于角色具体做了什么、说了什么，而不是感受到什么或环境如何变化。违反以上任何一条规则，生成内容将被视为不合格。`;

    // 根据上下文添加特定指导
    if (context?.plotType === 'conflict') {
      return baseGuidance + '重点描写冲突双方的具体行动和言语交锋，避免过多的内心独白。';
    } else if (context?.plotType === 'twist') {
      return baseGuidance + '专注于转折点的关键信息揭示和角色反应，不要铺垫过多无关细节。';
    } else if (context?.plotType === 'climax') {
      return baseGuidance + '突出高潮时刻的关键动作和决定性对话，保持紧凑的节奏感。';
    }

    return baseGuidance;
  }

  /**
   * 从剧情点内容中提取行动关键词
   */
  private extractActionKeywords(content: string): string {
    // 简单的关键词提取逻辑
    const actionWords = ['发现', '决定', '行动', '对话', '解决', '面对', '选择', '执行'];
    for (const word of actionWords) {
      if (content.includes(word)) {
        return word;
      }
    }
    return '角色';
  }

  /**
   * 验证writingGuidance内容的完整性
   */
  validateWritingGuidance(guidance: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // 检查长度
    if (guidance.length < 70) {
      issues.push('writingGuidance内容过短，应至少70字');
      suggestions.push('添加更具体的避免事项和应该做的事项');
    }

    // 检查是否包含避免事项
    if (!guidance.includes('不要') && !guidance.includes('避免')) {
      issues.push('缺少明确的避免指导');
      suggestions.push('添加"不要写一丝xx"等具体避免事项');
    }

    // 检查是否包含具体指导
    if (!guidance.includes('直接写') && !guidance.includes('专注于')) {
      issues.push('缺少具体的写作指导');
      suggestions.push('添加"直接写xxx"等具体操作指导');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }
}
