"use client";

import React from 'react';

interface CompositeIconProps {
  type: 'writing-night' | 'creative-cloud' | 'magical-book' | 'dream-writer' | 'starry-chapter';
  className?: string;
  size?: number;
  primaryColor?: string;
  secondaryColor?: string;
  animated?: boolean;
}

/**
 * 复合SVG图标组件
 * 将多个SVG元素组合成独特的设计
 */
export const CompositeIcon: React.FC<CompositeIconProps> = ({
  type,
  className = "",
  size = 32,
  primaryColor = "#3B82F6",
  secondaryColor = "#F59E0B",
  animated = false
}) => {
  const compositeIcons = {
    'writing-night': (
      <svg
        width={size}
        height={size}
        viewBox="0 0 48 48"
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        {/* 月亮背景 */}
        <path
          d="M20 8C20 12.4183 16.4183 16 12 16C7.58172 16 4 12.4183 4 8C4 3.58172 7.58172 0 12 0C16.4183 0 20 3.58172 20 8Z"
          fill={secondaryColor}
          opacity="0.3"
          className={animated ? 'animate-bounce' : ''}
          style={{ animationDuration: '3s' }}
        />

        {/* 羽毛笔 */}
        <g transform="translate(16, 12) scale(0.8)">
          <path
            d="M2 20L8 14L20 2C21.1046 0.895431 22.8954 0.895431 24 2C25.1046 3.10457 25.1046 4.89543 24 6L12 18L6 24L2 20Z"
            stroke={primaryColor}
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={animated ? 'animate-pulse' : ''}
          />
          <path
            d="M16 6L18 8"
            stroke={primaryColor}
            strokeWidth="2"
            strokeLinecap="round"
          />
        </g>

        {/* 星星装饰 */}
        <g className={animated ? 'animate-spin' : ''} style={{ animationDuration: '8s' }}>
          <path
            d="M36 12L37 15L40 16L37 17L36 20L35 17L32 16L35 15L36 12Z"
            fill={secondaryColor}
            opacity="0.6"
          />
          <path
            d="M8 32L9 34L11 35L9 36L8 38L7 36L5 35L7 34L8 32Z"
            fill={secondaryColor}
            opacity="0.4"
          />
        </g>
      </svg>
    ),

    'creative-cloud': (
      <svg
        width={size}
        height={size}
        viewBox="0 0 48 48"
        className={`${className} ${animated ? 'animate-bounce' : ''}`}
      >
        {/* 云朵基础 */}
        <path
          d="M36 20C36 14.4772 31.5228 10 26 10C22.6863 10 19.7863 11.7909 18.1863 14.4545C17.4727 14.1636 16.6909 14 15.8636 14C12.6136 14 10 16.6136 10 19.8636C10 20.5909 10.1364 21.2909 10.3818 21.9364C7.8636 22.8636 6 25.2727 6 28.0909C6 31.8636 9.1364 35 12.9091 35H34.0909C38.4091 35 42 31.4091 42 27.0909C42 23.2727 39.2727 20.0909 36 20Z"
          fill={primaryColor}
          opacity="0.2"
          className={animated ? 'animate-pulse' : ''}
        />

        {/* 加号创意符号 */}
        <g transform="translate(20, 18)">
          <path
            d="M4 0V8M0 4H8"
            stroke={primaryColor}
            strokeWidth="2.5"
            strokeLinecap="round"
            className={animated ? 'animate-spin' : ''}
            style={{ animationDuration: '4s' }}
          />
        </g>

        {/* 闪烁星星 */}
        <g className={animated ? 'animate-ping' : ''} style={{ animationDuration: '2s' }}>
          <circle cx="32" cy="12" r="1.5" fill={secondaryColor} opacity="0.8" />
          <circle cx="38" cy="18" r="1" fill={secondaryColor} opacity="0.6" />
          <circle cx="12" cy="16" r="1" fill={secondaryColor} opacity="0.7" />
        </g>
      </svg>
    ),

    'magical-book': (
      <svg
        width={size}
        height={size}
        viewBox="0 0 48 48"
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        {/* 书本基础 */}
        <path
          d="M8 6C8 4.89543 8.89543 4 10 4H36C37.1046 4 38 4.89543 38 6V38C38 39.1046 37.1046 40 36 40H10C8.89543 40 8 39.1046 8 38V6Z"
          fill={primaryColor}
          opacity="0.1"
        />
        <path
          d="M8 6C8 4.89543 8.89543 4 10 4H36C37.1046 4 38 4.89543 38 6V38C38 39.1046 37.1046 40 36 40H10C8.89543 40 8 39.1046 8 38V6Z"
          stroke={primaryColor}
          strokeWidth="2"
          fill="none"
        />

        {/* 书脊线 */}
        <path
          d="M12 4V40"
          stroke={primaryColor}
          strokeWidth="2"
        />

        {/* 魔法星星 */}
        <g className={animated ? 'animate-spin' : ''} style={{ animationDuration: '6s' }}>
          <path
            d="M24 12L25.5 16.5L30 18L25.5 19.5L24 24L22.5 19.5L18 18L22.5 16.5L24 12Z"
            fill={secondaryColor}
            opacity="0.8"
          />
        </g>

        {/* 魔法粒子 */}
        <g className={animated ? 'animate-bounce' : ''} style={{ animationDuration: '2s' }}>
          <circle cx="20" cy="28" r="1" fill={secondaryColor} opacity="0.6" />
          <circle cx="28" cy="26" r="1.5" fill={secondaryColor} opacity="0.7" />
          <circle cx="26" cy="32" r="1" fill={secondaryColor} opacity="0.5" />
        </g>
      </svg>
    ),

    'dream-writer': (
      <svg
        width={size}
        height={size}
        viewBox="0 0 48 48"
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        {/* 睡眠月亮 */}
        <path
          d="M20 8C20 12.4183 16.4183 16 12 16C7.58172 16 4 12.4183 4 8C4 3.58172 7.58172 0 12 0C16.4183 0 20 3.58172 20 8Z"
          fill={primaryColor}
          opacity="0.3"
        />

        {/* 睡眠符号 */}
        <g transform="translate(8, 4)" className={animated ? 'animate-bounce' : ''}>
          <path
            d="M2 2H6L2 6H6"
            stroke={primaryColor}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>

        {/* 羽毛笔写作 */}
        <g transform="translate(20, 20) scale(0.7)">
          <path
            d="M2 20L8 14L20 2C21.1046 0.895431 22.8954 0.895431 24 2C25.1046 3.10457 25.1046 4.89543 24 6L12 18L6 24L2 20Z"
            stroke={secondaryColor}
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>

        {/* 梦境云朵 */}
        <g transform="translate(28, 8)" className={animated ? 'animate-pulse' : ''}>
          <ellipse cx="6" cy="4" rx="4" ry="2" fill={primaryColor} opacity="0.2" />
          <ellipse cx="8" cy="6" rx="3" ry="1.5" fill={primaryColor} opacity="0.15" />
        </g>
      </svg>
    ),

    'starry-chapter': (
      <svg
        width={size}
        height={size}
        viewBox="0 0 48 48"
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        {/* 章节页面 - 基于Plus Add Document Create Page风格 */}
        <rect
          x="14"
          y="10"
          width="20"
          height="28"
          rx="3"
          fill="none"
          stroke={primaryColor}
          strokeWidth="2"
          className={animated ? 'animate-pulse' : ''}
          style={{ animationDuration: '2s' }}
        />

        {/* 页面折角 */}
        <path
          d="M28 10L28 16L34 16"
          fill="none"
          stroke={primaryColor}
          strokeWidth="2"
          strokeLinejoin="round"
        />

        {/* 页面内容线条 - 基于SVGRepo简洁风格 */}
        <g opacity="0.7">
          <line x1="18" y1="20" x2="28" y2="20" stroke={primaryColor} strokeWidth="1.5" strokeLinecap="round" />
          <line x1="18" y1="24" x2="26" y2="24" stroke={primaryColor} strokeWidth="1.5" strokeLinecap="round" />
          <line x1="18" y1="28" x2="30" y2="28" stroke={primaryColor} strokeWidth="1.5" strokeLinecap="round" />
          <line x1="18" y1="32" x2="24" y2="32" stroke={primaryColor} strokeWidth="1.5" strokeLinecap="round" />
        </g>

        {/* 星星装饰 - 基于Star SVG风格 */}
        <g className={animated ? 'animate-spin' : ''} style={{ animationDuration: '8s' }}>
          <path
            d="M8 16L9 19L12 20L9 21L8 24L7 21L4 20L7 19L8 16Z"
            fill={secondaryColor}
            opacity="0.8"
            className={animated ? 'animate-pulse' : ''}
            style={{ animationDuration: '1.5s' }}
          />
          <path
            d="M38 14L39 16L41 17L39 18L38 20L37 18L35 17L37 16L38 14Z"
            fill={secondaryColor}
            opacity="0.6"
            className={animated ? 'animate-pulse' : ''}
            style={{ animationDuration: '1.8s' }}
          />
          <path
            d="M6 34L7 36L9 37L7 38L6 40L5 38L3 37L5 36L6 34Z"
            fill={secondaryColor}
            opacity="0.7"
            className={animated ? 'animate-pulse' : ''}
            style={{ animationDuration: '1.2s' }}
          />
        </g>

        {/* 加号图标 - 基于Plus Add风格 */}
        <g transform="translate(36, 30)" className={animated ? 'animate-bounce' : ''} style={{ animationDuration: '2s' }}>
          <circle
            cx="0"
            cy="0"
            r="6"
            fill={secondaryColor}
            opacity="0.9"
          />
          <path
            d="M-3 0H3M0 -3V3"
            stroke="white"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </g>
      </svg>
    )
  };

  return compositeIcons[type] || null;
};

/**
 * 动态复合图标组件
 * 支持更复杂的动画效果
 */
interface AnimatedCompositeIconProps extends CompositeIconProps {
  animationSpeed?: 'slow' | 'normal' | 'fast';
  hoverEffect?: boolean;
}

export const AnimatedCompositeIcon: React.FC<AnimatedCompositeIconProps> = ({
  type,
  className = "",
  size = 32,
  primaryColor = "#3B82F6",
  secondaryColor = "#F59E0B",
  animated = true,
  animationSpeed = 'normal',
  hoverEffect = true
}) => {
  const getSpeedClass = () => {
    switch (animationSpeed) {
      case 'slow': return 'duration-[4s]';
      case 'fast': return 'duration-[1s]';
      default: return 'duration-[2s]';
    }
  };

  return (
    <div
      className={`inline-block transition-transform duration-300 ${
        hoverEffect ? 'hover:scale-110 hover:rotate-3' : ''
      } ${getSpeedClass()}`}
    >
      <CompositeIcon
        type={type}
        className={className}
        size={size}
        primaryColor={primaryColor}
        secondaryColor={secondaryColor}
        animated={animated}
      />
    </div>
  );
};
