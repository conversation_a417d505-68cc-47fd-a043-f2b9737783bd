"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { aiAssistantDataService } from '@/services/aiAssistantDataService';
import { AIAssistantContextType } from '@/lib/db/dexie';
import './AssociationManagerDialog.css';

interface AssociationManagerDialogProps {
  isOpen: boolean;
  bookId: string;
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  onClose: () => void;
  onUpdateAssociations: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => void;
}

type ContentType = 'chapter' | 'character' | 'terminology' | 'worldBuilding';

interface ContentItem {
  id: string;
  title: string;
  description?: string;
  order?: number;
  metadata?: any;
}

/**
 * 关联管理对话框组件
 * 支持分类管理、搜索过滤、批量操作、范围选择
 */
const AssociationManagerDialog: React.FC<AssociationManagerDialogProps> = ({
  isOpen,
  bookId,
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  onClose,
  onUpdateAssociations
}) => {
  const [activeType, setActiveType] = useState<ContentType>('chapter');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);

  // 范围选择状态（仅用于章节）
  const [rangeStart, setRangeStart] = useState('');
  const [rangeEnd, setRangeEnd] = useState('');
  const [isRangeMode, setIsRangeMode] = useState(false);

  // 本地选择状态
  const [localSelectedIds, setLocalSelectedIds] = useState<{
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }>({
    chapterIds: selectedChapterIds,
    characterIds: selectedCharacterIds,
    terminologyIds: selectedTerminologyIds,
    worldBuildingIds: selectedWorldBuildingIds,
  });

  // 内容类型配置
  const contentTypes = [
    { key: 'chapter' as ContentType, label: '章节', icon: '📚' },
    { key: 'character' as ContentType, label: '人物', icon: '👤' },
    { key: 'terminology' as ContentType, label: '术语', icon: '📖' },
    { key: 'worldBuilding' as ContentType, label: '世界观', icon: '🌍' },
  ];

  // 获取当前类型的选中ID列表
  const getCurrentSelectedIds = useCallback(() => {
    switch (activeType) {
      case 'chapter': return localSelectedIds.chapterIds;
      case 'character': return localSelectedIds.characterIds;
      case 'terminology': return localSelectedIds.terminologyIds;
      case 'worldBuilding': return localSelectedIds.worldBuildingIds;
      default: return [];
    }
  }, [activeType, localSelectedIds]);

  // 更新当前类型的选中ID列表
  const updateCurrentSelectedIds = useCallback((ids: string[]) => {
    setLocalSelectedIds(prev => ({
      ...prev,
      [`${activeType}Ids`]: ids
    }));
  }, [activeType]);

  // 将ContentType转换为AIAssistantContextType
  const mapContentTypeToAIType = useCallback((contentType: ContentType): AIAssistantContextType => {
    switch (contentType) {
      case 'chapter': return AIAssistantContextType.CHAPTER;
      case 'character': return AIAssistantContextType.CHARACTER;
      case 'terminology': return AIAssistantContextType.TERMINOLOGY;
      case 'worldBuilding': return AIAssistantContextType.WORLD_BUILDING;
      default: return AIAssistantContextType.CHAPTER;
    }
  }, []);

  // 加载内容列表
  const loadContentItems = useCallback(async () => {
    if (!bookId || !isOpen) return;

    setIsLoading(true);
    try {
      const results = await aiAssistantDataService.searchMentionItems(
        bookId, searchQuery, [mapContentTypeToAIType(activeType)], 100
      );

      const items: ContentItem[] = results.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description,
        order: item.metadata?.order,
        metadata: item.metadata
      }));

      // 章节按order排序
      if (activeType === 'chapter') {
        items.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      setContentItems(items);
    } catch (error) {
      console.error('Failed to load content items:', error);
    } finally {
      setIsLoading(false);
    }
  }, [bookId, activeType, searchQuery, isOpen, mapContentTypeToAIType]);

  // 当对话框打开、类型切换或搜索查询变化时重新加载
  useEffect(() => {
    loadContentItems();
  }, [loadContentItems]);

  // 重置本地状态
  useEffect(() => {
    if (isOpen) {
      setLocalSelectedIds({
        chapterIds: selectedChapterIds,
        characterIds: selectedCharacterIds,
        terminologyIds: selectedTerminologyIds,
        worldBuildingIds: selectedWorldBuildingIds,
      });
      setSearchQuery('');
      setRangeStart('');
      setRangeEnd('');
      setIsRangeMode(false);
    }
  }, [isOpen, selectedChapterIds, selectedCharacterIds, selectedTerminologyIds, selectedWorldBuildingIds]);

  // 处理单个项目选择
  const handleItemToggle = useCallback((itemId: string) => {
    const currentSelected = getCurrentSelectedIds();
    const newSelected = currentSelected.includes(itemId)
      ? currentSelected.filter(id => id !== itemId)
      : [...currentSelected, itemId];

    updateCurrentSelectedIds(newSelected);
  }, [getCurrentSelectedIds, updateCurrentSelectedIds]);

  // 处理全选
  const handleSelectAll = useCallback(() => {
    const allIds = contentItems.map(item => item.id);
    updateCurrentSelectedIds(allIds);
  }, [contentItems, updateCurrentSelectedIds]);

  // 处理反选
  const handleInvertSelection = useCallback(() => {
    const currentSelected = getCurrentSelectedIds();
    const allIds = contentItems.map(item => item.id);
    const newSelected = allIds.filter(id => !currentSelected.includes(id));
    updateCurrentSelectedIds(newSelected);
  }, [contentItems, getCurrentSelectedIds, updateCurrentSelectedIds]);

  // 处理清空
  const handleClearSelection = useCallback(() => {
    updateCurrentSelectedIds([]);
  }, [updateCurrentSelectedIds]);

  // 处理范围选择（仅章节）
  const handleRangeSelect = useCallback(() => {
    if (activeType !== 'chapter' || !rangeStart || !rangeEnd) return;

    const startOrder = parseInt(rangeStart);
    const endOrder = parseInt(rangeEnd);

    if (isNaN(startOrder) || isNaN(endOrder) || startOrder > endOrder) return;

    const rangeIds = contentItems
      .filter(item => {
        const order = item.order || 0;
        return order >= startOrder && order <= endOrder;
      })
      .map(item => item.id);

    const currentSelected = getCurrentSelectedIds();
    const newSelected = [...new Set([...currentSelected, ...rangeIds])];
    updateCurrentSelectedIds(newSelected);

    setRangeStart('');
    setRangeEnd('');
  }, [activeType, rangeStart, rangeEnd, contentItems, getCurrentSelectedIds, updateCurrentSelectedIds]);

  // 处理确认
  const handleConfirm = useCallback(() => {
    onUpdateAssociations(localSelectedIds);
    onClose();
  }, [localSelectedIds, onUpdateAssociations, onClose]);

  // 计算统计信息
  const stats = useMemo(() => {
    return {
      chapterCount: localSelectedIds.chapterIds.length,
      characterCount: localSelectedIds.characterIds.length,
      terminologyCount: localSelectedIds.terminologyIds.length,
      worldBuildingCount: localSelectedIds.worldBuildingIds.length,
    };
  }, [localSelectedIds]);

  if (!isOpen) return null;

  return createPortal(
    <div className="association-manager-overlay" onClick={onClose}>
      <div className="association-manager-dialog" onClick={e => e.stopPropagation()}>
        {/* 对话框头部 */}
        <div className="dialog-header">
          <h3>关联管理</h3>
          <div className="dialog-stats">
            总计: {stats.chapterCount + stats.characterCount + stats.terminologyCount + stats.worldBuildingCount} 项
          </div>
          <button className="dialog-close" onClick={onClose}>×</button>
        </div>

        {/* 对话框内容 */}
        <div className="dialog-content">
          {/* 左侧类型选择器 */}
          <div className="type-selector">
            {contentTypes.map(type => (
              <button
                key={type.key}
                className={`type-button ${activeType === type.key ? 'active' : ''}`}
                onClick={() => setActiveType(type.key)}
              >
                <span className="type-icon">{type.icon}</span>
                <span className="type-label">{type.label}</span>
                <span className="type-count">
                  ({localSelectedIds[`${type.key}Ids`].length})
                </span>
              </button>
            ))}
          </div>

          {/* 右侧内容区域 */}
          <div className="content-area">
            {/* 搜索和操作栏 */}
            <div className="content-toolbar">
              <input
                type="text"
                placeholder={`搜索${contentTypes.find(t => t.key === activeType)?.label}...`}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="search-input"
              />

              <div className="toolbar-actions">
                <button onClick={handleSelectAll} className="toolbar-btn">全选</button>
                <button onClick={handleInvertSelection} className="toolbar-btn">反选</button>
                <button onClick={handleClearSelection} className="toolbar-btn">清空</button>
              </div>
            </div>

            {/* 章节范围选择 */}
            {activeType === 'chapter' && (
              <div className="range-selector">
                <button
                  className={`range-toggle ${isRangeMode ? 'active' : ''}`}
                  onClick={() => setIsRangeMode(!isRangeMode)}
                >
                  范围选择
                </button>

                {isRangeMode && (
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="起始章节"
                      value={rangeStart}
                      onChange={e => setRangeStart(e.target.value)}
                      className="range-input"
                    />
                    <span>-</span>
                    <input
                      type="number"
                      placeholder="结束章节"
                      value={rangeEnd}
                      onChange={e => setRangeEnd(e.target.value)}
                      className="range-input"
                    />
                    <button
                      onClick={handleRangeSelect}
                      disabled={!rangeStart || !rangeEnd}
                      className="range-apply"
                    >
                      应用
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* 内容列表 */}
            <div className="content-list">
              {isLoading ? (
                <div className="loading-state">
                  <div className="loading-spinner"></div>
                  <span>加载中...</span>
                </div>
              ) : contentItems.length === 0 ? (
                <div className="empty-state">
                  <span>暂无{contentTypes.find(t => t.key === activeType)?.label}内容</span>
                </div>
              ) : (
                contentItems.map((item, index) => {
                  const isSelected = getCurrentSelectedIds().includes(item.id);
                  return (
                    <div
                      key={item.id}
                      className={`content-item ${isSelected ? 'selected' : ''}`}
                      onClick={() => handleItemToggle(item.id)}
                      style={{ animationDelay: `${index * 0.02}s` }}
                    >
                      <div className="item-checkbox">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => {}}
                          tabIndex={-1}
                        />
                      </div>
                      <div className="item-content">
                        <div className="item-title">
                          {activeType === 'chapter' && item.order && (
                            <span className="item-order">第{item.order}章 </span>
                          )}
                          {item.title}
                        </div>
                        {item.description && (
                          <div className="item-description">{item.description}</div>
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="dialog-footer">
          <button className="dialog-btn secondary" onClick={onClose}>
            取消
          </button>
          <button className="dialog-btn primary" onClick={handleConfirm}>
            确认 ({stats.chapterCount + stats.characterCount + stats.terminologyCount + stats.worldBuildingCount})
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default AssociationManagerDialog;
