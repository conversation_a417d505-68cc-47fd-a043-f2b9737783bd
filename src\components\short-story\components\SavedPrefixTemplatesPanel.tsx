import React, { useState, useEffect } from 'react';
import { AIGeneratedPrefixStorageService, SavedAIPrefix } from '../../../services/ai/AIGeneratedPrefixStorageService';
import { PhaseType } from '../../../types/ai-persona';
import { WorldBookPrefix } from '../../../types/worldbook';
import { CustomPrefixDialog } from './CustomPrefixDialog';
import { WorldBookPrefixDialog } from './WorldBookPrefixDialog';
import { CategoryPrefixDialog } from './CategoryPrefixDialog';
import { PrefixEditDialog } from './PrefixEditDialog';
import { FolderCreateDialog } from './FolderCreateDialog';
import { WorldBookImportDialog } from './WorldBookImportDialog';
import { ImportResult } from '../../../types/worldbook';
import {
  getCategoryIcon,
  WorldBookIcon,
  EditIcon,
  DeleteIcon,
  StarIcon,
  StarFilledIcon,
  PlusIcon,
  ChevronRightIcon,
  XIcon,
  FolderIcon
} from '../../common/icons/PrefixIcons';

interface SavedPrefixTemplatesPanelProps {
  currentPhase?: PhaseType;
  onPrefixesSelected: (prefixes: string[]) => void;
  onClose: () => void;
}

export const SavedPrefixTemplatesPanel: React.FC<SavedPrefixTemplatesPanelProps> = ({
  currentPhase,
  onPrefixesSelected,
  onClose
}) => {
  const [storageService] = useState(() => AIGeneratedPrefixStorageService.getInstance());
  const [savedPrefixes, setSavedPrefixes] = useState<SavedAIPrefix[]>([]);
  const [selectedPrefixes, setSelectedPrefixes] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterPhase, setFilterPhase] = useState<string>('all');
  const [filterWorldBook, setFilterWorldBook] = useState<string>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [worldBookSources, setWorldBookSources] = useState<string[]>([]);
  const [expandedWorldBooks, setExpandedWorldBooks] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'folder' | 'list'>('folder');
  const [showCustomPrefixDialog, setShowCustomPrefixDialog] = useState(false);
  const [showWorldBookDialog, setShowWorldBookDialog] = useState<string | null>(null);
  const [showCategoryDialog, setShowCategoryDialog] = useState<string | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false);
  const [showWorldBookImportDialog, setShowWorldBookImportDialog] = useState(false);
  const [editingPrefix, setEditingPrefix] = useState<SavedAIPrefix | null>(null);
  const [worldBookStats, setWorldBookStats] = useState<any>({
    totalWorldBookEntries: 0,
    totalSources: 0,
    bySource: {},
    mostUsedWorldBook: [],
    constantEntries: 0,
    selectiveEntries: 0
  });

  useEffect(() => {
    loadSavedPrefixes();
  }, []);

  const loadSavedPrefixes = async () => {
    const prefixes = storageService.getAllSavedPrefixes();
    setSavedPrefixes(prefixes);

    // 加载世界书来源
    try {
      const sources = await storageService.getWorldBookSources();
      setWorldBookSources(sources);
    } catch (error) {
      console.error('加载世界书来源失败:', error);
      setWorldBookSources([]);
    }

    // 加载世界书统计信息
    try {
      const stats = await storageService.getWorldBookStatistics();
      setWorldBookStats(stats);
    } catch (error) {
      console.error('加载世界书统计信息失败:', error);
      setWorldBookStats({
        totalWorldBookEntries: 0,
        totalSources: 0,
        bySource: {},
        mostUsedWorldBook: [],
        constantEntries: 0,
        selectiveEntries: 0
      });
    }
  };

  const getFilteredPrefixes = (): SavedAIPrefix[] => {
    let filtered = savedPrefixes;

    // 搜索过滤
    if (searchQuery.trim()) {
      // 如果有世界书过滤，使用专门的世界书搜索
      if (filterWorldBook !== 'all' && filterWorldBook !== 'ai_generated') {
        filtered = storageService.searchWorldBookPrefixes(searchQuery.trim(), filterWorldBook);
      } else {
        filtered = storageService.searchPrefixes(searchQuery.trim());
      }
    }

    // 类别过滤
    if (filterCategory !== 'all') {
      filtered = filtered.filter(prefix => prefix.category === filterCategory);
    }

    // 阶段过滤
    if (filterPhase !== 'all') {
      if (filterPhase === 'current' && currentPhase) {
        filtered = filtered.filter(prefix => prefix.phase === currentPhase || !prefix.phase);
      } else if (filterPhase !== 'current') {
        filtered = filtered.filter(prefix => prefix.phase === filterPhase);
      }
    }

    // 世界书来源过滤
    if (filterWorldBook !== 'all') {
      if (filterWorldBook === 'ai_generated') {
        // 显示AI生成的（非世界书）
        filtered = filtered.filter(prefix => {
          const worldBookPrefix = prefix as WorldBookPrefix;
          return !worldBookPrefix.worldBookSource;
        });
      } else {
        // 显示特定世界书来源
        filtered = filtered.filter(prefix => {
          const worldBookPrefix = prefix as WorldBookPrefix;
          return worldBookPrefix.worldBookSource === filterWorldBook;
        });
      }
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(prefix => prefix.isFavorite);
    }

    return filtered;
  };

  // 获取按类别和来源分组的数据
  const getGroupedData = () => {
    const filtered = getFilteredPrefixes();

    // 分离不同类型的条目
    const customPrefixes: SavedAIPrefix[] = [];
    const worldBookGroups: Record<string, SavedAIPrefix[]> = {};
    const categoryGroups: Record<string, SavedAIPrefix[]> = {};

    filtered.forEach(prefix => {
      const worldBookPrefix = prefix as WorldBookPrefix;
      if (worldBookPrefix.worldBookSource) {
        // 世界书条目
        if (!worldBookGroups[worldBookPrefix.worldBookSource]) {
          worldBookGroups[worldBookPrefix.worldBookSource] = [];
        }
        worldBookGroups[worldBookPrefix.worldBookSource].push(prefix);
      } else if (prefix.category === 'custom') {
        // 自定义条目
        customPrefixes.push(prefix);
      } else {
        // AI生成条目按类别分组
        if (!categoryGroups[prefix.category]) {
          categoryGroups[prefix.category] = [];
        }
        categoryGroups[prefix.category].push(prefix);
      }
    });

    // 对每个世界书组内的条目按原始顺序排序
    Object.keys(worldBookGroups).forEach(source => {
      worldBookGroups[source].sort((a, b) => {
        const aWorldBook = a as WorldBookPrefix;
        const bWorldBook = b as WorldBookPrefix;
        return aWorldBook.originalOrder - bWorldBook.originalOrder;
      });
    });

    // 自定义条目按创建时间排序
    customPrefixes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // 类别条目按创建时间排序
    Object.keys(categoryGroups).forEach(category => {
      categoryGroups[category].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    });

    return { customPrefixes, worldBookGroups, categoryGroups };
  };

  const handlePrefixToggle = (prefixId: string) => {
    const newSelected = new Set(selectedPrefixes);
    if (newSelected.has(prefixId)) {
      newSelected.delete(prefixId);
    } else {
      newSelected.add(prefixId);
    }
    setSelectedPrefixes(newSelected);
  };

  // 批量选择处理函数
  const handleBatchSelect = (ids: string[]) => {
    setSelectedPrefixes(new Set(ids));
  };

  const handleSelectAll = () => {
    const filteredPrefixes = getFilteredPrefixes();
    const allIds = new Set(filteredPrefixes.map(p => p.id));
    setSelectedPrefixes(allIds);
  };

  const handleClearSelection = () => {
    setSelectedPrefixes(new Set());
  };

  const handleApplySelected = async () => {
    const selectedPrefixObjects = savedPrefixes.filter(p => selectedPrefixes.has(p.id));
    const prefixContents = selectedPrefixObjects.map(p => p.content);

    // 记录使用
    try {
      await Promise.all(selectedPrefixObjects.map(prefix =>
        storageService.recordUsage(prefix.id)
      ));
    } catch (error) {
      console.error('记录使用失败:', error);
    }

    onPrefixesSelected(prefixContents);
    onClose();
  };

  const handleToggleFavorite = async (prefixId: string) => {
    try {
      await storageService.toggleFavorite(prefixId);
      await loadSavedPrefixes();
    } catch (error) {
      console.error('切换收藏状态失败:', error);
    }
  };

  const handleDeletePrefix = async (prefixId: string) => {
    if (confirm('确定要删除这个前置消息吗？')) {
      try {
        await storageService.deletePrefix(prefixId);
        await loadSavedPrefixes();
        // 从选择中移除
        const newSelected = new Set(selectedPrefixes);
        newSelected.delete(prefixId);
        setSelectedPrefixes(newSelected);
      } catch (error) {
        console.error('删除前置消息失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  const getCategoryDisplayName = (category: string) => {
    const categoryNames = {
      'ace_framework': 'ACE框架',
      'format': '格式规范',
      'context': '上下文增强',
      'persona': '人设强化'
    };
    return categoryNames[category as keyof typeof categoryNames] || category;
  };

  const getPhaseDisplayName = (phase?: PhaseType) => {
    if (!phase) return '通用';
    const phaseNames = {
      'intro': '导语',
      'setup': '铺垫期',
      'compression': '爆发情绪',
      'climax': '反转',
      'resolution': '解气',
      'ending': '结局',
      'buildup': '铺垫',
      'custom': '自定义'
    };
    return phaseNames[phase] || phase;
  };

  const isWorldBookPrefix = (prefix: SavedAIPrefix): prefix is WorldBookPrefix => {
    const worldBookPrefix = prefix as WorldBookPrefix;
    return worldBookPrefix.worldBookSource !== undefined;
  };

  const getWorldBookInfo = (prefix: SavedAIPrefix) => {
    if (!isWorldBookPrefix(prefix)) return null;

    return {
      source: prefix.worldBookSource,
      originalUid: prefix.originalUid,
      isConstant: prefix.isConstant,
      isSelective: prefix.isSelective,
      originalOrder: prefix.originalOrder
    };
  };

  // 切换世界书文件夹展开状态
  const toggleWorldBookExpansion = (source: string) => {
    const newExpanded = new Set(expandedWorldBooks);
    if (newExpanded.has(source)) {
      newExpanded.delete(source);
    } else {
      newExpanded.add(source);
    }
    setExpandedWorldBooks(newExpanded);
  };

  // 删除整个世界书
  const handleDeleteWorldBook = async (source: string, e: React.MouseEvent) => {
    e.stopPropagation();

    try {
      const worldBookPrefixes = await storageService.getPrefixesByWorldBook(source);
      const confirmMessage = `确定要删除世界书 "${source}" 吗？\n\n这将删除该世界书的所有 ${worldBookPrefixes.length} 个条目，此操作不可撤销。`;

      if (window.confirm(confirmMessage)) {
        const deletedCount = await storageService.deleteWorldBookSource(source);
        console.log(`✅ 删除了世界书 "${source}" 的 ${deletedCount} 个条目`);

        // 刷新数据
        await loadSavedPrefixes();

        // 从展开状态中移除
        const newExpanded = new Set(expandedWorldBooks);
        newExpanded.delete(source);
        setExpandedWorldBooks(newExpanded);

        alert(`✅ 成功删除世界书 "${source}" 及其 ${deletedCount} 个条目`);
      }
    } catch (error) {
      console.error('删除世界书失败:', error);
      alert('删除世界书失败，请重试');
    }
  };

  // 获取世界书统计信息
  const getWorldBookStats = (source: string) => {
    // 从内存中的savedPrefixes获取统计信息，避免异步调用
    const prefixes = savedPrefixes.filter(prefix => {
      const worldBookPrefix = prefix as WorldBookPrefix;
      return worldBookPrefix.worldBookSource === source;
    }) as WorldBookPrefix[];

    return {
      total: prefixes.length,
      constant: prefixes.filter(p => p.isConstant).length,
      selective: prefixes.filter(p => p.isSelective).length,
      favorites: prefixes.filter(p => p.isFavorite).length,
      totalUsage: prefixes.reduce((sum, p) => sum + p.usageCount, 0)
    };
  };

  // 处理编辑前置消息
  const handleEditPrefix = (prefix: SavedAIPrefix, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingPrefix(prefix);
    setShowEditDialog(true);
  };

  // 处理创建新前置消息
  const handleCreatePrefix = (category?: string) => {
    setEditingPrefix(null);
    setShowEditDialog(true);
  };

  // 保存编辑的前置消息
  const handleSavePrefix = async (updatedPrefix: SavedAIPrefix) => {
    try {
      if (editingPrefix) {
        // 更新现有前置消息
        await storageService.updatePrefix(editingPrefix.id, updatedPrefix);
      } else {
        // 创建新前置消息
        // 注意：savePrefix方法可能不存在，需要检查
        console.warn('savePrefix方法可能需要实现');
      }
      await loadSavedPrefixes();
      setEditingPrefix(null);
    } catch (error) {
      console.error('保存前置消息失败:', error);
      alert('保存失败，请重试');
    }
  };

  // 处理创建文件夹
  const handleCreateFolder = (folderName: string, category: string) => {
    // 这里可以实现创建文件夹的逻辑
    // 目前只是创建一个新的前置消息作为文件夹的第一个条目
    const newPrefix: SavedAIPrefix = {
      id: `folder_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: `这是 ${folderName} 文件夹的示例前置消息`,
      description: `${folderName} 文件夹`,
      category: category as any, // 临时类型转换
      phase: undefined,
      tags: ['文件夹', folderName],
      isFavorite: false,
      usageCount: 0,
      createdAt: new Date(),
      useCase: '文件夹示例',
      reasoning: '用于组织前置消息',
      confidence: 1.0
    };

    storageService.savePrefix(newPrefix);
    loadSavedPrefixes();
    console.log(`✅ 创建了新文件夹: ${folderName} (${category})`);
  };

  // 处理世界书导入完成
  const handleWorldBookImportComplete = (result: ImportResult) => {
    console.log(`✅ 世界书导入完成: ${result.fileName}，导入了 ${result.imported} 个条目`);

    // 刷新数据
    loadSavedPrefixes();

    // 显示导入结果
    if (result.imported > 0) {
      alert(`✅ 成功从世界书 "${result.fileName}" 导入了 ${result.imported} 个前置消息模板！\n\n您可以在下方查看和使用这些模板。`);
    } else {
      alert(`⚠️ 世界书 "${result.fileName}" 导入完成，但没有有效的条目被导入。\n\n跳过了 ${result.skipped} 个条目。`);
    }

    // 关闭导入对话框
    setShowWorldBookImportDialog(false);

    // 如果有错误，显示错误信息
    if (result.errors.length > 0) {
      console.error('导入过程中的错误:', result.errors);
    }
  };

  // 处理世界书条目更新
  const handleWorldBookPrefixUpdate = async (updatedPrefix: WorldBookPrefix) => {
    try {
      console.log(`🔄 正在更新世界书条目: ${updatedPrefix.id}`);

      // 使用存储服务更新条目到数据库
      const success = await storageService.updatePrefix(updatedPrefix.id, updatedPrefix);

      if (success) {
        console.log(`✅ 世界书条目已成功更新到数据库: ${updatedPrefix.id}`);
        // 刷新数据以反映更新
        loadSavedPrefixes();
      } else {
        console.error(`❌ 更新世界书条目失败: ${updatedPrefix.id}`);
        alert('❌ 更新失败，请重试');
      }
    } catch (error) {
      console.error('更新世界书条目时发生错误:', error);
      alert('❌ 更新时发生错误，请重试');
    }
  };

  const filteredPrefixes = getFilteredPrefixes();
  const statistics = storageService.getStatistics();

  return (
    <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
      {/* 头部 */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200">
        <div>
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <FolderIcon size={24} className="mr-2 text-blue-600" />
            保存的前置消息模板
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            共 {statistics.total} 个模板，收藏 {statistics.favorites} 个
            {worldBookStats.totalWorldBookEntries > 0 && (
              <span className="ml-2 text-purple-600">
                • 世界书 {worldBookStats.totalWorldBookEntries} 个 ({worldBookStats.totalSources} 个来源)
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowWorldBookImportDialog(true)}
            className="flex items-center px-3 py-2 text-sm text-purple-600 border border-purple-300 rounded-lg hover:bg-purple-50"
            title="导入世界书文件"
          >
            <WorldBookIcon size={16} className="mr-1" />
            导入世界书
          </button>
          <button
            onClick={() => setShowCreateFolderDialog(true)}
            className="flex items-center px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50"
            title="创建新文件夹"
          >
            <PlusIcon size={16} className="mr-1" />
            新建文件夹
          </button>
          <button
            onClick={() => handleCreatePrefix()}
            className="flex items-center px-3 py-2 text-sm text-green-600 border border-green-300 rounded-lg hover:bg-green-50"
            title="创建新前置消息"
          >
            <PlusIcon size={16} className="mr-1" />
            新建模板
          </button>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XIcon size={24} />
          </button>
        </div>
      </div>

      {/* 过滤和搜索 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-wrap gap-4 items-center">
          {/* 搜索框 */}
          <div className="flex-1 min-w-64">
            <input
              type="text"
              placeholder="搜索前置消息内容、描述、用途..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 类别过滤 */}
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">所有类别</option>
            <option value="ace_framework">ACE框架</option>
            <option value="format">格式规范</option>
            <option value="context">上下文增强</option>
            <option value="persona">人设强化</option>
          </select>

          {/* 阶段过滤 */}
          <select
            value={filterPhase}
            onChange={(e) => setFilterPhase(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">所有阶段</option>
            {currentPhase && <option value="current">当前阶段({getPhaseDisplayName(currentPhase)})</option>}
            <option value="intro">导语</option>
            <option value="setup">铺垫期</option>
            <option value="compression">爆发情绪</option>
            <option value="climax">反转</option>
            <option value="resolution">解气</option>
            <option value="ending">结局</option>
            <option value="buildup">铺垫</option>
            <option value="custom">自定义</option>
          </select>

          {/* 世界书来源过滤 */}
          <select
            value={filterWorldBook}
            onChange={(e) => setFilterWorldBook(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">所有来源</option>
            <option value="ai_generated">AI生成</option>
            {worldBookSources.map(source => (
              <option key={source} value={source}>📚 {source}</option>
            ))}
          </select>

          {/* 收藏过滤 */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showFavoritesOnly}
              onChange={(e) => setShowFavoritesOnly(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">仅显示收藏</span>
          </label>

          {/* 视图模式切换 */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('folder')}
              className={`flex items-center px-3 py-2 text-sm ${
                viewMode === 'folder'
                  ? 'bg-purple-500 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
              title="文件夹视图"
            >
              <FolderIcon size={16} className="mr-1" />
              文件夹
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center px-3 py-2 text-sm ${
                viewMode === 'list'
                  ? 'bg-purple-500 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
              title="列表视图"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="mr-1">
                <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              列表
            </button>
          </div>
        </div>
      </div>

      {/* 操作栏 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="flex space-x-2">
            <button
              onClick={handleSelectAll}
              disabled={filteredPrefixes.length === 0}
              className="px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50 disabled:text-gray-400 disabled:border-gray-300"
            >
              全选 ({filteredPrefixes.length})
            </button>
            <button
              onClick={handleClearSelection}
              disabled={selectedPrefixes.size === 0}
              className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:text-gray-400"
            >
              清空选择
            </button>
          </div>

          <div className="text-sm text-gray-600">
            已选择 {selectedPrefixes.size} 个模板
          </div>
        </div>
      </div>

      {/* 前置消息列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        {(() => {
          const { customPrefixes, worldBookGroups, categoryGroups } = getGroupedData();
          const totalItems = customPrefixes.length + Object.values(worldBookGroups).flat().length + Object.values(categoryGroups).flat().length;

          if (totalItems === 0) {
            return (
              <div className="text-center py-12 text-gray-500">
                <div className="text-4xl mb-4">📝</div>
                <p>没有找到匹配的前置消息模板</p>
                <p className="text-sm mt-2">尝试调整搜索条件或生成新的前置消息</p>
              </div>
            );
          }

          if (viewMode === 'list') {
            // 列表视图：传统的网格布局
            const allPrefixes = [...customPrefixes, ...Object.values(worldBookGroups).flat(), ...Object.values(categoryGroups).flat()];
            return (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {allPrefixes.map((prefix) => (
                  <div
                    key={prefix.id}
                    onClick={() => handlePrefixToggle(prefix.id)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPrefixes.has(prefix.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {/* 传统卡片内容 */}
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2 flex-wrap">
                        <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                          {getCategoryDisplayName(prefix.category)}
                        </span>
                        {prefix.phase && (
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded">
                            {getPhaseDisplayName(prefix.phase)}
                          </span>
                        )}
                        {(() => {
                          const worldBookPrefix = prefix as WorldBookPrefix;
                          return worldBookPrefix.worldBookSource && (
                            <span className="text-xs px-2 py-1 bg-purple-100 text-purple-600 rounded">
                              📚 {worldBookPrefix.worldBookSource}
                            </span>
                          );
                        })()}
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={(e) => handleEditPrefix(prefix, e)}
                          className="text-gray-400 hover:text-blue-500 transition-colors"
                          title="编辑"
                        >
                          <EditIcon size={16} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleFavorite(prefix.id);
                          }}
                          className={`${prefix.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-500 transition-colors`}
                          title="收藏"
                        >
                          {prefix.isFavorite ? <StarFilledIcon size={16} /> : <StarIcon size={16} />}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeletePrefix(prefix.id);
                          }}
                          className="text-gray-400 hover:text-red-500 transition-colors"
                          title="删除"
                        >
                          <DeleteIcon size={16} />
                        </button>
                      </div>
                    </div>
                    <div className="mb-3">
                      <p className="text-sm text-gray-800 line-clamp-3">{prefix.content}</p>
                    </div>
                    <div className="mb-2">
                      <p className="text-xs text-gray-600 line-clamp-2">{prefix.description}</p>
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <div className="flex items-center space-x-2">
                        <span>使用 {prefix.usageCount} 次</span>
                        {(() => {
                          const worldBookInfo = getWorldBookInfo(prefix);
                          return worldBookInfo && (
                            <div className="flex items-center space-x-1">
                              <span>•</span>
                              <span>UID: {worldBookInfo.originalUid}</span>
                              {worldBookInfo.isConstant && (
                                <span className="px-1 py-0.5 bg-green-100 text-green-600 rounded text-xs">常量</span>
                              )}
                              {worldBookInfo.isSelective && (
                                <span className="px-1 py-0.5 bg-blue-100 text-blue-600 rounded text-xs">选择性</span>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                      <span>{new Date(prefix.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            );
          }

          // 文件夹视图：卡片式显示
          return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* 自定义前置消息文件夹卡片 */}
              {customPrefixes.length > 0 && (
                <div
                  onClick={() => setShowCustomPrefixDialog(true)}
                  className="p-4 border-2 border-green-200 rounded-lg cursor-pointer transition-all hover:border-green-300 hover:bg-green-50 bg-gradient-to-br from-green-25 to-green-50"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon('custom', 24, 'text-green-600')}
                      <span className="text-xs px-2 py-1 bg-green-100 text-green-600 rounded">
                        自定义
                      </span>
                    </div>
                    <ChevronRightIcon size={16} className="text-green-600" />
                  </div>

                  <div className="mb-3">
                    <h3 className="text-sm font-semibold text-green-800 line-clamp-2">
                      自定义前置消息
                    </h3>
                  </div>

                  <div className="mb-2">
                    <p className="text-xs text-green-600">
                      包含 {customPrefixes.length} 个条目
                    </p>
                  </div>

                  <div className="flex justify-between items-center text-xs text-green-500">
                    <div className="flex space-x-2">
                      <span>{customPrefixes.filter(p => p.isFavorite).length} 收藏</span>
                      <span>{customPrefixes.filter(p => p.phase).length} 有阶段</span>
                    </div>
                    <span>使用 {customPrefixes.reduce((sum, p) => sum + p.usageCount, 0)} 次</span>
                  </div>
                </div>
              )}

              {/* 类别文件夹卡片 */}
              {Object.entries(categoryGroups).map(([category, prefixes]) => {
                const categoryDisplayName = getCategoryDisplayName(category);
                const getCategoryTheme = (cat: string) => {
                  switch (cat) {
                    case 'context_enhancement': return {
                      bg: 'from-teal-25 to-teal-50',
                      border: 'border-teal-200',
                      hover: 'hover:border-teal-300 hover:bg-teal-50',
                      text: 'text-teal-800',
                      textSecondary: 'text-teal-600',
                      tag: 'bg-teal-100 text-teal-600'
                    };
                    case 'persona_enhancement': return {
                      bg: 'from-orange-25 to-orange-50',
                      border: 'border-orange-200',
                      hover: 'hover:border-orange-300 hover:bg-orange-50',
                      text: 'text-orange-800',
                      textSecondary: 'text-orange-600',
                      tag: 'bg-orange-100 text-orange-600'
                    };
                    case 'format_specification': return {
                      bg: 'from-blue-25 to-blue-50',
                      border: 'border-blue-200',
                      hover: 'hover:border-blue-300 hover:bg-blue-50',
                      text: 'text-blue-800',
                      textSecondary: 'text-blue-600',
                      tag: 'bg-blue-100 text-blue-600'
                    };
                    case 'ace_framework': return {
                      bg: 'from-indigo-25 to-indigo-50',
                      border: 'border-indigo-200',
                      hover: 'hover:border-indigo-300 hover:bg-indigo-50',
                      text: 'text-indigo-800',
                      textSecondary: 'text-indigo-600',
                      tag: 'bg-indigo-100 text-indigo-600'
                    };
                    default: return {
                      bg: 'from-gray-25 to-gray-50',
                      border: 'border-gray-200',
                      hover: 'hover:border-gray-300 hover:bg-gray-50',
                      text: 'text-gray-800',
                      textSecondary: 'text-gray-600',
                      tag: 'bg-gray-100 text-gray-600'
                    };
                  }
                };

                const theme = getCategoryTheme(category);

                return (
                  <div
                    key={category}
                    onClick={() => setShowCategoryDialog(category)}
                    className={`p-4 border-2 ${theme.border} rounded-lg cursor-pointer transition-all ${theme.hover} bg-gradient-to-br ${theme.bg}`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(category, 24, theme.text)}
                        <span className={`text-xs px-2 py-1 ${theme.tag} rounded`}>
                          {categoryDisplayName}
                        </span>
                      </div>
                      <ChevronRightIcon size={16} className={theme.textSecondary} />
                    </div>

                    <div className="mb-3">
                      <h3 className={`text-sm font-semibold ${theme.text} line-clamp-2`}>
                        {categoryDisplayName}
                      </h3>
                    </div>

                    <div className="mb-2">
                      <p className={`text-xs ${theme.textSecondary}`}>
                        包含 {prefixes.length} 个条目
                      </p>
                    </div>

                    <div className={`flex justify-between items-center text-xs ${theme.textSecondary}`}>
                      <div className="flex space-x-2">
                        <span>{prefixes.filter(p => p.isFavorite).length} 收藏</span>
                        <span>{prefixes.filter(p => p.phase).length} 有阶段</span>
                      </div>
                      <span>使用 {prefixes.reduce((sum, p) => sum + p.usageCount, 0)} 次</span>
                    </div>
                  </div>
                );
              })}

              {/* 世界书文件夹卡片 */}
              {Object.entries(worldBookGroups).map(([source, prefixes]) => {
                const stats = getWorldBookStats(source);

                return (
                  <div
                    key={source}
                    onClick={() => setShowWorldBookDialog(source)}
                    className="p-4 border-2 border-purple-200 rounded-lg cursor-pointer transition-all hover:border-purple-300 hover:bg-purple-50 bg-gradient-to-br from-purple-25 to-purple-50"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        <WorldBookIcon size={24} className="text-purple-600" />
                        <span className="text-xs px-2 py-1 bg-purple-100 text-purple-600 rounded">
                          世界书
                        </span>
                      </div>
                      <ChevronRightIcon size={16} className="text-purple-600" />
                    </div>

                    <div className="mb-3">
                      <h3 className="text-sm font-semibold text-purple-800 line-clamp-2">
                        {source}
                      </h3>
                    </div>

                    <div className="mb-2">
                      <p className="text-xs text-purple-600">
                        包含 {stats.total} 个条目
                      </p>
                    </div>

                    <div className="flex justify-between items-center text-xs text-purple-500">
                      <div className="flex space-x-2">
                        <span>{stats.constant} 常量</span>
                        <span>{stats.selective} 选择性</span>
                      </div>
                      <span>使用 {stats.totalUsage} 次</span>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })()}
      </div>

      {/* 底部操作 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            显示 {filteredPrefixes.length} / {statistics.total} 个模板
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleApplySelected}
              disabled={selectedPrefixes.size === 0}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              应用选择 ({selectedPrefixes.size}个)
            </button>
          </div>
        </div>
      </div>

      {/* 自定义前置消息弹窗 */}
      <CustomPrefixDialog
        isOpen={showCustomPrefixDialog}
        onClose={() => setShowCustomPrefixDialog(false)}
        customPrefixes={getGroupedData().customPrefixes}
        selectedPrefixes={selectedPrefixes}
        onPrefixToggle={handlePrefixToggle}
        onDeletePrefix={handleDeletePrefix}
        onToggleFavorite={handleToggleFavorite}
        onEditPrefix={(prefix) => {
          setEditingPrefix(prefix);
          setShowEditDialog(true);
        }}
        onCreatePrefix={() => handleCreatePrefix('custom')}
        onApplySelected={handleApplySelected}
      />

      {/* 世界书弹窗 */}
      {showWorldBookDialog && (
        <WorldBookPrefixDialog
          isOpen={!!showWorldBookDialog}
          onClose={() => setShowWorldBookDialog(null)}
          worldBookSource={showWorldBookDialog}
          worldBookPrefixes={getGroupedData().worldBookGroups[showWorldBookDialog] as WorldBookPrefix[] || []}
          selectedPrefixes={selectedPrefixes}
          onPrefixToggle={handlePrefixToggle}
          onBatchSelect={handleBatchSelect}
          onDeletePrefix={handleDeletePrefix}
          onToggleFavorite={handleToggleFavorite}
          onEditPrefix={(prefix) => {
            setEditingPrefix(prefix);
            setShowEditDialog(true);
          }}
          onCreatePrefix={(worldBookSource) => {
            // 创建世界书条目的逻辑
            handleCreatePrefix(worldBookSource);
          }}
          onDeleteWorldBook={(source) => {
            const deletedCount = storageService.deleteWorldBookSource(source);
            console.log(`✅ 删除了世界书 "${source}" 的 ${deletedCount} 个条目`);
            loadSavedPrefixes();
            setShowWorldBookDialog(null);
            alert(`✅ 成功删除世界书 "${source}" 及其 ${deletedCount} 个条目`);
          }}
          onApplySelected={handleApplySelected}
          onPrefixUpdate={handleWorldBookPrefixUpdate}
        />
      )}

      {/* 类别弹窗 */}
      {showCategoryDialog && (
        <CategoryPrefixDialog
          isOpen={!!showCategoryDialog}
          onClose={() => setShowCategoryDialog(null)}
          category={showCategoryDialog}
          categoryDisplayName={getCategoryDisplayName(showCategoryDialog)}
          categoryPrefixes={getGroupedData().categoryGroups[showCategoryDialog] || []}
          selectedPrefixes={selectedPrefixes}
          onPrefixToggle={handlePrefixToggle}
          onDeletePrefix={handleDeletePrefix}
          onToggleFavorite={handleToggleFavorite}
          onEditPrefix={(prefix) => {
            setEditingPrefix(prefix);
            setShowEditDialog(true);
          }}
          onCreatePrefix={(category) => handleCreatePrefix(category)}
          onApplySelected={handleApplySelected}
          themeColor={(() => {
            switch (showCategoryDialog) {
              case 'context_enhancement': return 'teal';
              case 'persona_enhancement': return 'orange';
              case 'format_specification': return 'blue';
              case 'ace_framework': return 'indigo';
              default: return 'blue';
            }
          })()}
        />
      )}

      {/* 前置消息编辑弹窗 */}
      <PrefixEditDialog
        isOpen={showEditDialog}
        onClose={() => {
          setShowEditDialog(false);
          setEditingPrefix(null);
        }}
        prefix={editingPrefix}
        onSave={handleSavePrefix}
        currentPhase={currentPhase}
      />

      {/* 文件夹创建弹窗 */}
      <FolderCreateDialog
        isOpen={showCreateFolderDialog}
        onClose={() => setShowCreateFolderDialog(false)}
        onCreateFolder={handleCreateFolder}
      />

      {/* 世界书导入弹窗 */}
      <WorldBookImportDialog
        isOpen={showWorldBookImportDialog}
        onClose={() => setShowWorldBookImportDialog(false)}
        onImportComplete={handleWorldBookImportComplete}
      />
    </div>
  );
};
