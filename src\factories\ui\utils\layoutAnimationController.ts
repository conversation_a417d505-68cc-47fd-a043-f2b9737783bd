import { LayoutResult } from './layoutAlgorithms';

/**
 * 动画配置接口
 */
export interface AnimationConfig {
  /**
   * 动画持续时间（毫秒）
   */
  duration: number;
  
  /**
   * 缓动函数类型
   * - linear: 线性
   * - easeIn: 缓入
   * - easeOut: 缓出
   * - easeInOut: 缓入缓出
   * - elastic: 弹性
   */
  easing: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'elastic';
  
  /**
   * 节点动画错开延迟（毫秒）
   * 用于创建节点按顺序移动的效果
   */
  staggerDelay: number;
  
  /**
   * 是否启用动画
   * 如果为false，则直接应用最终位置
   */
  enableAnimation: boolean;
}

/**
 * 节点位置类型
 */
export type NodePositions = { [nodeId: string]: { x: number, y: number } };

/**
 * 动画布局变化
 * 处理节点位置的平滑过渡
 * 
 * @param currentPositions 当前节点位置
 * @param newLayout 新布局结果
 * @param animationConfig 动画配置
 * @param onAnimationFrame 动画帧回调函数
 * @param onAnimationComplete 动画完成回调函数
 */
export function animateLayoutChange(
  currentPositions: NodePositions,
  newLayout: LayoutResult,
  animationConfig: AnimationConfig,
  onAnimationFrame: (positions: NodePositions) => void,
  onAnimationComplete: () => void
): void {
  // 如果禁用动画，直接应用新布局
  if (!animationConfig.enableAnimation) {
    const finalPositions = newLayout.nodes.reduce((acc, node) => {
      acc[node.id] = { x: node.x, y: node.y };
      return acc;
    }, {} as NodePositions);
    
    onAnimationFrame(finalPositions);
    onAnimationComplete();
    return;
  }
  
  // 创建节点ID到索引的映射
  const nodeIndices = newLayout.nodes.reduce((acc, node, index) => {
    acc[node.id] = index;
    return acc;
  }, {} as { [nodeId: string]: number });
  
  // 记录动画开始时间
  const startTime = performance.now();
  const duration = animationConfig.duration;
  
  // 动画帧函数
  const animate = (timestamp: number) => {
    const elapsed = timestamp - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // 计算当前帧的位置
    const currentFramePositions = Object.keys(currentPositions).reduce((acc, nodeId) => {
      const startPos = currentPositions[nodeId];
      const nodeIndex = nodeIndices[nodeId];
      
      if (nodeIndex !== undefined) {
        const endPos = { 
          x: newLayout.nodes[nodeIndex].x, 
          y: newLayout.nodes[nodeIndex].y 
        };
        
        // 计算节点的延迟
        const nodeDelay = nodeIndex * animationConfig.staggerDelay;
        const nodeProgress = Math.max(0, Math.min(1, (elapsed - nodeDelay) / duration));
        const nodeEasedProgress = applyEasing(nodeProgress, animationConfig.easing);
        
        // 插值计算当前位置
        acc[nodeId] = {
          x: startPos.x + (endPos.x - startPos.x) * nodeEasedProgress,
          y: startPos.y + (endPos.y - startPos.y) * nodeEasedProgress
        };
      } else {
        // 如果节点在新布局中不存在，保持原位置
        acc[nodeId] = startPos;
      }
      
      return acc;
    }, {} as NodePositions);
    
    // 更新动画帧
    onAnimationFrame(currentFramePositions);
    
    // 继续动画或完成
    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      onAnimationComplete();
    }
  };
  
  // 开始动画
  requestAnimationFrame(animate);
}

/**
 * 应用缓动函数
 * @param progress 进度 (0-1)
 * @param easing 缓动类型
 * @returns 应用缓动后的进度值
 */
function applyEasing(progress: number, easing: string): number {
  switch (easing) {
    case 'easeIn':
      // 缓入 (加速)
      return progress * progress;
      
    case 'easeOut':
      // 缓出 (减速)
      return 1 - Math.pow(1 - progress, 2);
      
    case 'easeInOut':
      // 缓入缓出 (先加速后减速)
      return progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      
    case 'elastic':
      // 弹性 (超出后回弹)
      const c4 = (2 * Math.PI) / 3;
      
      return progress === 0
        ? 0
        : progress === 1
        ? 1
        : -Math.pow(2, 10 * progress - 10) * Math.sin((progress * 10 - 10.75) * c4);
      
    case 'linear':
    default:
      // 线性 (匀速)
      return progress;
  }
}

/**
 * 创建WebWorker进行布局计算
 * 用于大型图表的性能优化
 * 
 * @param workerFunction 工作函数
 * @returns WebWorker实例
 */
export function createLayoutWorker(workerFunction: Function): Worker {
  // 创建一个包含工作函数的Blob
  const blob = new Blob(
    [`(${workerFunction.toString()})()`],
    { type: 'application/javascript' }
  );
  
  // 从Blob创建URL
  const url = URL.createObjectURL(blob);
  
  // 创建Worker
  const worker = new Worker(url);
  
  // 清理URL
  URL.revokeObjectURL(url);
  
  return worker;
}

/**
 * 使用WebWorker计算布局
 * 
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns Promise，解析为布局结果
 */
export function computeLayoutInWorker(
  nodes: any[],
  edges: any[],
  config: any
): Promise<LayoutResult> {
  return new Promise((resolve, reject) => {
    // 创建Worker函数
    const workerFunction = () => {
      // 在Worker中监听消息
      self.addEventListener('message', (event) => {
        const { nodes, edges, config } = event.data;
        
        try {
          // 这里应该是布局算法的实现
          // 由于Worker中无法直接访问外部函数，需要在这里复制实现
          // 或者使用importScripts加载外部脚本
          
          // 简化的布局计算示例
          const result = {
            nodes: nodes.map((node: any, index: number) => ({
              id: node.id,
              x: index * 100,
              y: index * 100
            }))
          };
          
          // 发送结果回主线程
          self.postMessage({ result });
        } catch (error) {
          self.postMessage({ error: error.message });
        }
      });
    };
    
    // 创建Worker
    const worker = createLayoutWorker(workerFunction);
    
    // 设置消息处理
    worker.onmessage = (event) => {
      const { result, error } = event.data;
      
      if (error) {
        reject(new Error(error));
      } else {
        resolve(result);
      }
      
      // 终止Worker
      worker.terminate();
    };
    
    // 设置错误处理
    worker.onerror = (error) => {
      reject(new Error('Worker error: ' + error.message));
      worker.terminate();
    };
    
    // 发送数据到Worker
    worker.postMessage({ nodes, edges, config });
  });
}

/**
 * 优化大型画布的布局计算
 * 对于大型图表，使用分批处理和WebWorker
 * 
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @param threshold 使用优化的节点数阈值
 * @returns Promise，解析为布局结果
 */
export function optimizedLayoutComputation(
  nodes: any[],
  edges: any[],
  config: any,
  threshold: number = 100
): Promise<LayoutResult> {
  // 如果节点数量小于阈值，直接在主线程计算
  if (nodes.length < threshold) {
    // 假设computeLayout是同步函数
    // const result = computeLayout(nodes, edges, config);
    // return Promise.resolve(result);
    
    // 由于这里无法直接调用computeLayout，返回一个模拟结果
    return Promise.resolve({
      nodes: nodes.map((node, index) => ({
        id: node.id,
        x: node.position.x,
        y: node.position.y
      }))
    });
  }
  
  // 否则，使用WebWorker进行计算
  return computeLayoutInWorker(nodes, edges, config);
}
