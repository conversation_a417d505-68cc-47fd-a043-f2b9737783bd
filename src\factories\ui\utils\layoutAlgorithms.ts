import { Node, Edge } from 'reactflow';
import { LayoutConfig } from '../components/OutlineManager/tools/LayoutControlPanel';

/**
 * 节点数据接口
 */
export interface NodeData {
  id: string;
  width: number;
  height: number;
  parentId?: string;
  level?: number;
  // 其他节点属性
}

/**
 * 边数据接口
 */
export interface EdgeData {
  id: string;
  source: string;
  target: string;
  // 其他边属性
}

/**
 * 布局结果接口
 */
export interface LayoutResult {
  nodes: {
    id: string;
    x: number;
    y: number;
  }[];
  // 可能包含优化后的边信息
  edges?: {
    id: string;
    points: { x: number, y: number }[];
  }[];
}

/**
 * 计算动态间距
 * @param nodes 节点数组
 * @param userConfig 用户配置（可选）
 * @returns 动态间距配置
 */
function calculateDynamicSpacing(nodes: Node[], userConfig?: LayoutConfig): { nodeSpacing: number, levelSpacing: number } {
  // 优先使用用户设置的值
  if (userConfig?.nodeSpacing && userConfig?.levelSpacing) {
    console.log('🎯 使用用户设置的间距:', {
      nodeSpacing: userConfig.nodeSpacing,
      levelSpacing: userConfig.levelSpacing
    });
    return {
      nodeSpacing: userConfig.nodeSpacing,
      levelSpacing: userConfig.levelSpacing
    };
  }

  if (nodes.length === 0) {
    return { nodeSpacing: 300, levelSpacing: 200 };
  }

  // 计算平均节点尺寸
  const avgNodeWidth = nodes.reduce((sum, node) => sum + (node.width || 300), 0) / nodes.length;
  const avgNodeHeight = nodes.reduce((sum, node) => sum + (node.height || 200), 0) / nodes.length;

  const dynamicSpacing = {
    nodeSpacing: userConfig?.nodeSpacing || Math.max(avgNodeWidth * 1.5, 350), // 1.5倍节点宽度作为水平间距，最小350px
    levelSpacing: userConfig?.levelSpacing || Math.max(avgNodeHeight * 1.8, 250) // 1.8倍节点高度作为垂直间距，最小250px
  };

  console.log('📏 计算的动态间距:', dynamicSpacing);
  return dynamicSpacing;
}

/**
 * 碰撞检测
 * @param nodeA 节点A
 * @param nodeB 节点B
 * @returns 是否碰撞
 */
function isColliding(nodeA: { id: string, x: number, y: number, width?: number, height?: number },
                    nodeB: { id: string, x: number, y: number, width?: number, height?: number }): boolean {
  const widthA = nodeA.width || 300;
  const heightA = nodeA.height || 200;
  const widthB = nodeB.width || 300;
  const heightB = nodeB.height || 200;

  const padding = 20; // 节点间最小间距

  return !(nodeA.x + widthA + padding < nodeB.x ||
           nodeB.x + widthB + padding < nodeA.x ||
           nodeA.y + heightA + padding < nodeB.y ||
           nodeB.y + heightB + padding < nodeA.y);
}

/**
 * 调整节点位置以避免碰撞
 * @param nodeToAdjust 要调整的节点
 * @param fixedNode 固定节点
 * @returns 调整后的节点
 */
function adjustPosition(nodeToAdjust: { id: string, x: number, y: number, width?: number, height?: number },
                       fixedNode: { id: string, x: number, y: number, width?: number, height?: number }):
                       { id: string, x: number, y: number, width?: number, height?: number } {
  const widthA = nodeToAdjust.width || 300;
  const heightA = nodeToAdjust.height || 200;
  const widthB = fixedNode.width || 300;
  const heightB = fixedNode.height || 200;
  const padding = 30;

  // 计算调整方向
  const dx = nodeToAdjust.x - fixedNode.x;
  const dy = nodeToAdjust.y - fixedNode.y;

  if (Math.abs(dx) > Math.abs(dy)) {
    // 水平调整
    if (dx > 0) {
      nodeToAdjust.x = fixedNode.x + widthB + padding;
    } else {
      nodeToAdjust.x = fixedNode.x - widthA - padding;
    }
  } else {
    // 垂直调整
    if (dy > 0) {
      nodeToAdjust.y = fixedNode.y + heightB + padding;
    } else {
      nodeToAdjust.y = fixedNode.y - heightA - padding;
    }
  }

  return nodeToAdjust;
}

/**
 * 检测并解决碰撞
 * @param layoutNodes 布局节点数组
 * @returns 解决碰撞后的节点数组
 */
function detectAndResolveCollisions(layoutNodes: { id: string, x: number, y: number }[]): { id: string, x: number, y: number }[] {
  const resolvedNodes = [...layoutNodes];

  for (let i = 0; i < resolvedNodes.length; i++) {
    for (let j = i + 1; j < resolvedNodes.length; j++) {
      if (isColliding(resolvedNodes[i], resolvedNodes[j])) {
        resolvedNodes[j] = adjustPosition(resolvedNodes[j], resolvedNodes[i]);
      }
    }
  }

  return resolvedNodes;
}

/**
 * 计算布局
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
export function computeLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  console.log('🔄 开始计算布局:', {
    nodeCount: nodes.length,
    edgeCount: edges.length,
    config
  });

  if (nodes.length === 0) {
    return { nodes: [], edges: [] };
  }

  // 计算动态间距，传递用户配置以确保优先级正确
  const dynamicSpacing = calculateDynamicSpacing(nodes, config);

  // 更新配置中的间距，优先使用用户设置的值
  const enhancedConfig = {
    ...config,
    nodeSpacing: config.nodeSpacing || dynamicSpacing.nodeSpacing,
    levelSpacing: config.levelSpacing || dynamicSpacing.levelSpacing
  };

  console.log('⚙️ 最终使用的配置:', enhancedConfig);

  // 根据布局类型选择不同的布局算法
  let result: LayoutResult;
  switch (enhancedConfig.type) {
    case 'tree':
      result = computeTreeLayout(nodes, edges, enhancedConfig);
      break;
    case 'force':
      result = computeForceLayout(nodes, edges, enhancedConfig);
      break;
    case 'radial':
      result = computeRadialLayout(nodes, edges, enhancedConfig);
      break;
    case 'grid':
      result = computeGridLayout(nodes, edges, enhancedConfig);
      break;
    case 'horizontal':
      result = computeDirectionalLayout(nodes, edges, { ...enhancedConfig, direction: 'LR' });
      break;
    case 'vertical':
      result = computeDirectionalLayout(nodes, edges, { ...enhancedConfig, direction: 'TB' });
      break;
    default:
      result = computeTreeLayout(nodes, edges, enhancedConfig);
  }

  // 应用碰撞检测和解决
  result.nodes = detectAndResolveCollisions(result.nodes);

  return result;
}

/**
 * 计算树形布局
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
function computeTreeLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  // 构建节点关系图
  const { nodeMap, rootNodes, childrenMap } = buildNodeRelationships(nodes, edges);
  
  // 计算节点的层级和索引
  calculateLevelsAndIndices(rootNodes, nodeMap, childrenMap);
  
  // 应用布局配置
  const direction = config.direction || 'TB';
  const nodeSpacing = config.nodeSpacing;
  const levelSpacing = config.levelSpacing;
  const compactness = config.compactness;
  
  // 计算节点位置
  const result: LayoutResult = {
    nodes: []
  };
  
  // 根据方向和配置计算每个节点的位置
  nodes.forEach(node => {
    const nodeInfo = nodeMap.get(node.id);
    if (nodeInfo) {
      const { level, index } = nodeInfo;
      
      // 根据方向计算x和y坐标
      let x = 0;
      let y = 0;
      
      // 修复紧凑度计算公式
      // 原公式 (2 - compactness) 存在问题：compactness越大，间距反而越大
      // 新公式：compactness越大，间距越小，范围在0.5-2.0之间
      const compactnessMultiplier = 0.5 + (1 - compactness) * 1.5;

      switch (direction) {
        case 'LR': // 从左到右
          x = level * levelSpacing;
          y = index * nodeSpacing * compactnessMultiplier;
          break;
        case 'RL': // 从右到左
          x = -level * levelSpacing;
          y = index * nodeSpacing * compactnessMultiplier;
          break;
        case 'BT': // 从下到上
          x = index * nodeSpacing * compactnessMultiplier;
          y = -level * levelSpacing;
          break;
        case 'TB': // 从上到下
        default:
          x = index * nodeSpacing * compactnessMultiplier;
          y = level * levelSpacing;
          break;
      }
      
      result.nodes.push({
        id: node.id,
        x,
        y
      });
    }
  });
  
  // 如果需要居中内容
  if (config.centerContent) {
    centerLayoutResult(result);
  }
  
  return result;
}

/**
 * 计算力导向布局
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
function computeForceLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  // 构建节点关系图
  const { nodeMap, rootNodes, childrenMap } = buildNodeRelationships(nodes, edges);
  
  // 初始化节点位置（随机分布或使用现有位置）
  const positions: { [id: string]: { x: number, y: number } } = {};
  nodes.forEach(node => {
    positions[node.id] = {
      x: node.position.x + Math.random() * 10,
      y: node.position.y + Math.random() * 10
    };
  });
  
  // 力导向参数
  const iterations = 50; // 迭代次数
  const k = config.nodeSpacing; // 理想边长
  const gravity = 0.1 * config.compactness; // 引力系数
  const centerX = 0;
  const centerY = 0;
  
  // 力导向算法迭代
  for (let i = 0; i < iterations; i++) {
    // 计算斥力（节点之间）
    const forces: { [id: string]: { x: number, y: number } } = {};
    nodes.forEach(node => {
      forces[node.id] = { x: 0, y: 0 };
    });
    
    // 节点间斥力
    for (let a = 0; a < nodes.length; a++) {
      for (let b = a + 1; b < nodes.length; b++) {
        const nodeA = nodes[a];
        const nodeB = nodes[b];
        const posA = positions[nodeA.id];
        const posB = positions[nodeB.id];
        
        const dx = posB.x - posA.x;
        const dy = posB.y - posA.y;
        const distance = Math.sqrt(dx * dx + dy * dy) || 0.1;
        
        // 斥力计算 (反比于距离的平方)
        const force = k * k / distance;
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;
        
        forces[nodeA.id].x -= fx;
        forces[nodeA.id].y -= fy;
        forces[nodeB.id].x += fx;
        forces[nodeB.id].y += fy;
      }
    }
    
    // 边的引力
    edges.forEach(edge => {
      const source = positions[edge.source];
      const target = positions[edge.target];
      
      if (source && target) {
        const dx = target.x - source.x;
        const dy = target.y - source.y;
        const distance = Math.sqrt(dx * dx + dy * dy) || 0.1;
        
        // 引力计算 (正比于距离)
        const force = distance / k;
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;
        
        forces[edge.source].x += fx;
        forces[edge.source].y += fy;
        forces[edge.target].x -= fx;
        forces[edge.target].y -= fy;
      }
    });
    
    // 中心引力
    nodes.forEach(node => {
      const pos = positions[node.id];
      const dx = centerX - pos.x;
      const dy = centerY - pos.y;
      const distance = Math.sqrt(dx * dx + dy * dy) || 0.1;
      
      forces[node.id].x += dx * gravity;
      forces[node.id].y += dy * gravity;
    });
    
    // 更新位置
    nodes.forEach(node => {
      const force = forces[node.id];
      positions[node.id].x += Math.min(Math.max(force.x, -10), 10);
      positions[node.id].y += Math.min(Math.max(force.y, -10), 10);
    });
  }
  
  // 构建结果
  const result: LayoutResult = {
    nodes: nodes.map(node => ({
      id: node.id,
      x: positions[node.id].x,
      y: positions[node.id].y
    }))
  };
  
  // 如果需要居中内容
  if (config.centerContent) {
    centerLayoutResult(result);
  }
  
  return result;
}

/**
 * 计算放射状布局
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
function computeRadialLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  // 构建节点关系图
  const { nodeMap, rootNodes, childrenMap } = buildNodeRelationships(nodes, edges);
  
  // 计算节点的层级
  calculateLevelsAndIndices(rootNodes, nodeMap, childrenMap);
  
  // 应用布局配置
  const nodeSpacing = config.nodeSpacing;
  const levelSpacing = config.levelSpacing;
  const compactness = config.compactness;
  
  // 计算节点位置
  const result: LayoutResult = {
    nodes: []
  };
  
  // 获取最大层级
  let maxLevel = 0;
  nodeMap.forEach(info => {
    maxLevel = Math.max(maxLevel, info.level);
  });
  
  // 计算每个层级的节点数量
  const levelCounts: number[] = Array(maxLevel + 1).fill(0);
  nodeMap.forEach(info => {
    levelCounts[info.level]++;
  });
  
  // 计算每个节点在其层级中的角度
  const levelAngles: { [id: string]: number } = {};
  nodeMap.forEach((info, id) => {
    const level = info.level;
    const count = levelCounts[level];
    const index = info.index % count; // 确保索引在范围内
    
    // 计算角度 (0 到 2π)
    levelAngles[id] = (index / count) * 2 * Math.PI;
  });
  
  // 根据层级和角度计算每个节点的位置
  nodes.forEach(node => {
    const nodeInfo = nodeMap.get(node.id);
    if (nodeInfo) {
      const level = nodeInfo.level;
      const angle = levelAngles[node.id];
      
      // 计算半径 (根据层级)
      const radius = level * levelSpacing * compactness;
      
      // 计算坐标
      const x = Math.cos(angle) * radius;
      const y = Math.sin(angle) * radius;
      
      result.nodes.push({
        id: node.id,
        x,
        y
      });
    }
  });
  
  // 如果需要居中内容
  if (config.centerContent) {
    centerLayoutResult(result);
  }
  
  return result;
}

/**
 * 计算网格布局
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
function computeGridLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  // 应用布局配置
  const nodeSpacing = config.nodeSpacing;
  
  // 计算网格尺寸
  const nodeCount = nodes.length;
  const gridSize = Math.ceil(Math.sqrt(nodeCount));
  
  // 计算节点位置
  const result: LayoutResult = {
    nodes: []
  };
  
  nodes.forEach((node, index) => {
    const row = Math.floor(index / gridSize);
    const col = index % gridSize;
    
    result.nodes.push({
      id: node.id,
      x: col * nodeSpacing,
      y: row * nodeSpacing
    });
  });
  
  // 如果需要居中内容
  if (config.centerContent) {
    centerLayoutResult(result);
  }
  
  return result;
}

/**
 * 计算方向性布局 (水平或垂直)
 * @param nodes 节点数组
 * @param edges 边数组
 * @param config 布局配置
 * @returns 布局结果
 */
function computeDirectionalLayout(
  nodes: Node[],
  edges: Edge[],
  config: LayoutConfig
): LayoutResult {
  // 构建节点关系图
  const { nodeMap, rootNodes, childrenMap } = buildNodeRelationships(nodes, edges);
  
  // 计算节点的层级和索引
  calculateLevelsAndIndices(rootNodes, nodeMap, childrenMap);
  
  // 应用布局配置
  const direction = config.direction || 'TB';
  const nodeSpacing = config.nodeSpacing;
  const levelSpacing = config.levelSpacing;
  
  // 计算节点位置
  const result: LayoutResult = {
    nodes: []
  };
  
  // 根据方向计算每个节点的位置
  nodes.forEach(node => {
    const nodeInfo = nodeMap.get(node.id);
    if (nodeInfo) {
      const { level, index } = nodeInfo;
      
      // 根据方向计算x和y坐标
      let x = 0;
      let y = 0;
      
      if (direction === 'LR' || direction === 'RL') {
        // 水平方向布局
        x = (direction === 'LR' ? 1 : -1) * level * levelSpacing;
        y = index * nodeSpacing;
      } else {
        // 垂直方向布局
        x = index * nodeSpacing;
        y = (direction === 'TB' ? 1 : -1) * level * levelSpacing;
      }
      
      result.nodes.push({
        id: node.id,
        x,
        y
      });
    }
  });
  
  // 如果需要居中内容
  if (config.centerContent) {
    centerLayoutResult(result);
  }
  
  return result;
}

/**
 * 构建节点关系图
 * @param nodes 节点数组
 * @param edges 边数组
 * @returns 节点关系图
 */
function buildNodeRelationships(nodes: Node[], edges: Edge[]) {
  const nodeMap = new Map<string, { level: number, index: number }>();
  const rootNodes: string[] = [];
  const childrenMap = new Map<string, string[]>();
  
  // 初始化
  nodes.forEach(node => {
    nodeMap.set(node.id, { level: 0, index: 0 });
  });
  
  // 构建父子关系
  edges.forEach(edge => {
    const sourceId = edge.source;
    const targetId = edge.target;
    
    // 记录父节点的子节点
    if (!childrenMap.has(sourceId)) {
      childrenMap.set(sourceId, []);
    }
    childrenMap.get(sourceId)?.push(targetId);
  });
  
  // 找出根节点
  nodes.forEach(node => {
    let isChild = false;
    edges.forEach(edge => {
      if (edge.target === node.id) {
        isChild = true;
      }
    });
    
    if (!isChild) {
      rootNodes.push(node.id);
    }
  });
  
  return { nodeMap, rootNodes, childrenMap };
}

/**
 * 计算节点的层级和索引
 * @param rootNodes 根节点数组
 * @param nodeMap 节点映射
 * @param childrenMap 子节点映射
 */
function calculateLevelsAndIndices(
  rootNodes: string[],
  nodeMap: Map<string, { level: number, index: number }>,
  childrenMap: Map<string, string[]>
) {
  let currentIndex = 0;
  
  // 递归计算层级和索引
  function traverse(nodeId: string, level: number) {
    nodeMap.set(nodeId, { level, index: currentIndex++ });
    
    const children = childrenMap.get(nodeId) || [];
    children.forEach(childId => {
      traverse(childId, level + 1);
    });
  }
  
  // 从根节点开始遍历
  rootNodes.forEach((rootId, rootIndex) => {
    traverse(rootId, 0);
  });
}

/**
 * 居中布局结果
 * @param result 布局结果
 */
function centerLayoutResult(result: LayoutResult) {
  if (result.nodes.length === 0) return;
  
  // 计算边界
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;
  
  result.nodes.forEach(node => {
    minX = Math.min(minX, node.x);
    minY = Math.min(minY, node.y);
    maxX = Math.max(maxX, node.x);
    maxY = Math.max(maxY, node.y);
  });
  
  // 计算中心偏移
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  
  // 应用偏移
  result.nodes.forEach(node => {
    node.x -= centerX;
    node.y -= centerY;
  });
}
