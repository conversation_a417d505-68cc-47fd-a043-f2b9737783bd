import { v4 as uuidv4 } from 'uuid';
import { db, Book } from './database';

/**
 * 书籍仓库
 */
export class BookRepository {
  /**
   * 获取所有书籍
   * @returns 书籍列表
   */
  async getAllBooks(): Promise<Book[]> {
    return await db.books.orderBy('updatedAt').reverse().toArray();
  }
  
  /**
   * 根据ID获取书籍
   * @param id 书籍ID
   * @returns 书籍对象，如果不存在则返回undefined
   */
  async getBookById(id: string): Promise<Book | undefined> {
    return await db.books.get(id);
  }
  
  /**
   * 创建新书籍
   * @param title 书籍标题
   * @param description 书籍描述
   * @param coverImage 封面图片URL（可选）
   * @returns 创建的书籍对象
   */
  async createBook(title: string, description: string, coverImage?: string): Promise<Book> {
    const now = new Date();
    const book: Book = {
      id: uuidv4(),
      title,
      description,
      coverImage,
      createdAt: now,
      updatedAt: now
    };
    
    await db.books.add(book);
    return book;
  }
  
  /**
   * 更新书籍
   * @param id 书籍ID
   * @param updates 要更新的字段
   * @returns 更新后的书籍对象
   */
  async updateBook(id: string, updates: Partial<Omit<Book, 'id' | 'createdAt'>>): Promise<Book | undefined> {
    const book = await this.getBookById(id);
    if (!book) {
      return undefined;
    }
    
    const updatedBook = {
      ...book,
      ...updates,
      updatedAt: new Date()
    };
    
    await db.books.update(id, updatedBook);
    return updatedBook;
  }
  
  /**
   * 删除书籍
   * @param id 书籍ID
   * @returns 是否删除成功
   */
  async deleteBook(id: string): Promise<boolean> {
    try {
      // 删除书籍相关的章节
      await db.chapters.where('bookId').equals(id).delete();
      
      // 删除书籍相关的写作统计
      await db.writingStats.where('bookId').equals(id).delete();
      
      // 删除书籍
      await db.books.delete(id);
      
      return true;
    } catch (error) {
      console.error('删除书籍失败', error);
      return false;
    }
  }
}
