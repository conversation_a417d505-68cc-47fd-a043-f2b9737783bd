import { db } from '@/lib/db/dexie';

/**
 * 获取实体关联的章节内容
 *
 * @param entityId 实体ID
 * @param entityType 实体类型：'character'（人物）、'terminology'（术语）或'worldbuilding'（世界观）
 * @param bookId 书籍ID
 * @returns 关联章节的内容
 */
export async function getAssociatedChapterContent(
  entityId: string,
  entityType: 'character' | 'terminology' | 'worldbuilding',
  bookId: string
): Promise<string> {
  try {
    // 获取实体关联的章节ID
    let associatedChapterIds: string[] = [];

    switch (entityType) {
      case 'character':
        const character = await db.characters.get(entityId);
        associatedChapterIds = character?.associatedChapters || [];
        break;

      case 'terminology':
        const terminology = await db.terminologies.get(entityId);
        associatedChapterIds = terminology?.associatedChapters || [];
        break;

      case 'worldbuilding':
        const worldbuilding = await db.worldbuildings.get(entityId);
        associatedChapterIds = worldbuilding?.extractedFromChapterIds || [];
        break;
    }

    if (associatedChapterIds.length === 0) {
      return '';
    }

    // 获取关联章节的内容
    const chapters = await db.chapters
      .where('id')
      .anyOf(associatedChapterIds)
      .and(chapter => chapter.bookId === bookId)
      .toArray();

    // 按章节顺序排序
    chapters.sort((a, b) => (a.order || 0) - (b.order || 0));

    // 组合章节内容
    const content = chapters
      .map(chapter => `# ${chapter.title || `第${chapter.order}章`}\n\n${chapter.content || ''}`)
      .join('\n\n');

    return content;
  } catch (error) {
    console.error('获取关联章节内容失败:', error);
    return '';
  }
}

/**
 * 保存实体与章节的关联关系
 *
 * @param entityId 实体ID
 * @param entityType 实体类型：'character'（人物）、'terminology'（术语）或'worldbuilding'（世界观）
 * @param chapterIds 章节ID数组
 * @returns 是否保存成功
 */
export async function saveChapterAssociation(
  entityId: string,
  entityType: 'character' | 'terminology' | 'worldbuilding',
  chapterIds: string[]
): Promise<boolean> {
  try {
    switch (entityType) {
      case 'character':
        await db.characters.update(entityId, {
          associatedChapters: chapterIds
        });
        break;

      case 'terminology':
        await db.terminologies.update(entityId, {
          associatedChapters: chapterIds
        });
        break;

      case 'worldbuilding':
        await db.worldbuildings.update(entityId, {
          extractedFromChapterIds: chapterIds
        });
        break;
    }

    return true;
  } catch (error) {
    console.error('保存章节关联关系失败:', error);
    return false;
  }
}

/**
 * 获取书籍的所有章节
 *
 * @param bookId 书籍ID
 * @returns 章节数组，包含id、title和content
 */
export async function getBookChapters(bookId: string): Promise<{ id: string; title: string; content: string; order: number }[]> {
  try {
    const chapters = await db.chapters.where('bookId').equals(bookId).toArray();

    return chapters.map(chapter => ({
      id: chapter.id || '',
      title: chapter.title || `第${chapter.order}章`,
      content: chapter.content || '',
      order: chapter.order || 0
    })).sort((a, b) => a.order - b.order);
  } catch (error) {
    console.error('获取书籍章节失败:', error);
    return [];
  }
}

/**
 * 获取实体关联的章节ID
 *
 * @param entityId 实体ID
 * @param entityType 实体类型：'character'（人物）、'terminology'（术语）或'worldbuilding'（世界观）
 * @returns 关联章节ID数组
 */
export async function getAssociatedChapterIds(
  entityId: string,
  entityType: 'character' | 'terminology' | 'worldbuilding'
): Promise<string[]> {
  try {
    switch (entityType) {
      case 'character':
        const character = await db.characters.get(entityId);
        return character?.associatedChapters || [];

      case 'terminology':
        const terminology = await db.terminologies.get(entityId);
        return terminology?.associatedChapters || [];

      case 'worldbuilding':
        const worldbuilding = await db.worldbuildings.get(entityId);
        return worldbuilding?.extractedFromChapterIds || [];

      default:
        return [];
    }
  } catch (error) {
    console.error('获取关联章节ID失败:', error);
    return [];
  }
}
