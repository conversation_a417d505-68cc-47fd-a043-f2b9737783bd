/**
 * 双AI协同系统配置类型定义
 */

/**
 * AI模型类型
 */
export type AIModelType = 'outline' | 'dialogue';

/**
 * AI任务类型
 */
export type AITaskType = 'structure' | 'dialogue' | 'mixed';

/**
 * AI功能类型
 */
export type AIFunctionType =
  | 'outline_creation'      // 大纲创建
  | 'framework_extraction'  // 框架提取
  | 'rhythm_analysis'       // 节奏分析
  | 'dialogue_creation'     // 对话创建
  | 'dialogue_enhancement'  // 对话增强
  | 'character_dialogue'    // 人物对话
  | 'general_writing'       // 通用写作
  | 'content_continuation'  // 内容续写
  | 'style_adaptation';     // 风格适配

/**
 * 单个AI模型配置
 */
export interface ModelConfig {
  /** API端点URL */
  url: string;
  /** API密钥 */
  apiKey: string;
  /** 模型名称 */
  modelName: string;
  /** 系统提示词 */
  systemPrompt: string;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 共享参数配置
 */
export interface SharedConfig {
  /** 温度参数 */
  temperature: number;
  /** 最大token数 */
  maxTokens: number;
  /** Top P参数 - 控制核心采样的概率质量 */
  topP: number;
  /** Top K参数 - 控制候选词汇数量，范围通常为1-100 */
  topK?: number;
  /** 频率惩罚 */
  frequencyPenalty: number;
  /** 存在惩罚 */
  presencePenalty: number;
  /** 是否启用流式输出 */
  streaming?: boolean;
}

/**
 * 双AI配置
 */
export interface DualAIConfig {
  /** 配置模式：单AI或双AI */
  mode: 'single' | 'dual';
  /** 共享参数 */
  shared: SharedConfig;
  /** AI模型配置 */
  models: {
    /** 大纲AI配置 */
    outline: ModelConfig;
    /** 对话AI配置 */
    dialogue: ModelConfig;
  };
  /** 配置版本 */
  version?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

/**
 * AI调用选项
 */
export interface CallOptions {
  /** 覆盖温度参数 */
  temperature?: number;
  /** 覆盖最大token数 */
  maxTokens?: number;
  /** 覆盖Top P参数 */
  topP?: number;
  /** 覆盖Top K参数 */
  topK?: number;
  /** 覆盖频率惩罚 */
  frequencyPenalty?: number;
  /** 覆盖存在惩罚 */
  presencePenalty?: number;
  /** 是否启用流式输出 */
  streaming?: boolean;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 流式响应回调函数 */
  onChunk?: (chunk: string) => void;
}

/**
 * AI响应结果
 */
export interface AIResponse {
  /** 是否成功 */
  success: boolean;
  /** 响应文本 */
  text?: string;
  /** 错误信息 */
  error?: string;
  /** 使用的模型类型 */
  modelType?: AIModelType;
  /** 使用的token数 */
  tokensUsed?: number;
  /** 响应时间（毫秒） */
  responseTime?: number;
  /** 原始响应数据 */
  rawResponse?: any;
}

/**
 * 连接状态
 */
export type ConnectionStatus = 'connected' | 'disconnected' | 'testing' | 'error';

/**
 * AI配置状态
 */
export interface AIConfigState {
  /** 当前模式 */
  currentMode: 'single' | 'dual';
  /** 活动配置 */
  activeConfig: DualAIConfig;
  /** 连接状态 */
  connectionStatus: {
    outline: ConnectionStatus;
    dialogue: ConnectionStatus;
  };
  /** 使用统计 */
  usage: {
    outline: {
      calls: number;
      tokens: number;
      cost: number;
    };
    dialogue: {
      calls: number;
      tokens: number;
      cost: number;
    };
  };
  /** 最后更新时间 */
  lastUpdated?: string;
}

/**
 * 任务模型映射配置
 */
export const TASK_MODEL_MAPPING: Record<AITaskType, AIModelType> = {
  'structure': 'outline',
  'dialogue': 'dialogue',
  'mixed': 'outline' // 默认使用大纲AI
};

/**
 * 功能模型映射配置
 */
export const FUNCTION_MODEL_MAPPING: Record<AIFunctionType, AIModelType> = {
  'outline_creation': 'outline',
  'framework_extraction': 'outline',
  'rhythm_analysis': 'outline',
  'dialogue_creation': 'dialogue',
  'dialogue_enhancement': 'dialogue',
  'character_dialogue': 'dialogue',
  'general_writing': 'outline',
  'content_continuation': 'outline',
  'style_adaptation': 'dialogue'
};

/**
 * 默认系统提示词
 */
export const DEFAULT_SYSTEM_PROMPTS = {
  outline: `你是专业的小说大纲规划师，擅长：
- 章节结构设计和情节规划
- 框架提取和内容分析
- 节奏控制和发展建议
- 故事逻辑和情节连贯性

注意：专注于结构性内容创作，不涉及具体对话创作。`,

  dialogue: `你是专业的对话创作师，擅长：
- 人物对话的真实感塑造
- 对话节点的情感表达
- 人物性格通过对话的体现
- 对话的节奏和韵律控制

注意：专注于对话创作，让每个角色都有独特的说话方式和性格特征。`
};

/**
 * 获取默认配置（动态从API设置获取）
 */
export function getDefaultDualAIConfig(): DualAIConfig {
  // 从API设置获取配置
  let apiSettings: any = {};
  try {
    if (typeof window !== 'undefined') {
      const savedSettings = localStorage.getItem('api_settings');
      if (savedSettings) {
        apiSettings = JSON.parse(savedSettings);
      }
    }
  } catch (error) {
    console.warn('无法获取API设置，使用默认值', error);
  }

  return {
    mode: 'single',
    shared: {
      temperature: 0.7,
      maxTokens: apiSettings.maxTokens || 80000, // 直接从API设置获取
      topP: 1,
      topK: apiSettings.topK || 40, // 新增topK参数，默认值40
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: apiSettings.streamingEnabled ?? true // 直接从API设置获取
    },
    models: {
      outline: {
        url: apiSettings.apiEndpoints?.openai || '',
        apiKey: apiSettings.apiKeys?.openai || '',
        modelName: apiSettings.currentModel || 'gemini-2.5-pro-exp-03-25',
        systemPrompt: DEFAULT_SYSTEM_PROMPTS.outline,
        enabled: true
      },
      dialogue: {
        url: apiSettings.apiEndpoints?.openai || '',
        apiKey: apiSettings.apiKeys?.openai || '',
        modelName: apiSettings.currentModel || 'gemini-2.5-pro-exp-03-25',
        systemPrompt: DEFAULT_SYSTEM_PROMPTS.dialogue,
        enabled: true
      }
    },
    version: '1.0.0'
  };
}

/**
 * 默认配置（为了向后兼容保留，但建议使用getDefaultDualAIConfig()）
 */
export const DEFAULT_DUAL_AI_CONFIG: DualAIConfig = getDefaultDualAIConfig();
