"use client";

import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { BorderFlowAnimation } from './components/BorderFlowAnimation';
import { PaperTextureBackground } from './components/PaperTextureBackground';
import { FocusGlowEffect } from './components/FocusGlowEffect';
import { InkRippleEffect } from './components/InkRippleEffect';
import { ClickBackgroundAnimation } from './components/ClickBackgroundAnimation';
import { useAnimationPreference } from './hooks/useAnimationPreference';
import { useFocusState } from './hooks/useFocusState';
import { useInputTracking } from './hooks/useInputTracking';
import { useClickAnimation } from './hooks/useClickAnimation';
import { WritingPet } from '../WritingPet';
import { EDITOR_LAYOUT, LayoutUtils } from '@/constants/layout';
import './styles/animations.css';

export interface EnhancedChapterEditorRef {
  getTextarea: () => HTMLTextAreaElement | null;
  focus: () => void;
  getSelectionStart: () => number;
  getSelectionEnd: () => number;
  setSelectionRange: (start: number, end: number) => void;
}

interface EnhancedChapterEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSelect?: () => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
  isChapterChanging?: boolean;
  chapterChangeDirection?: 'next' | 'prev';
}

/**
 * 增强版章节编辑器组件
 * 支持SVG动画效果，包括边框流光、纸张纹理、焦点光晕和输入反馈
 */
export const EnhancedChapterEditor = forwardRef<EnhancedChapterEditorRef, EnhancedChapterEditorProps>(({
  value,
  onChange,
  onSelect,
  placeholder = "开始写作...",
  disabled = false,
  className = "",
  style = {},
  isChapterChanging = false,
  chapterChangeDirection = 'next'
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 动画偏好设置
  const { animationsEnabled, reducedMotion } = useAnimationPreference();

  // 焦点状态管理
  const { isFocused, isHovered, handleFocus, handleBlur, handleMouseEnter, handleMouseLeave } = useFocusState();

  // 宠物引用
  const petRef = useRef<{ triggerWakeUp: () => void; triggerSleep: () => void } | null>(null);

  // 输入追踪
  const { ripples, metrics, handleInput } = useInputTracking(textareaRef);

  // 点击背景动画
  const { clickAnimations, handleTextareaClick } = useClickAnimation();

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getTextarea: () => textareaRef.current,
    focus: () => textareaRef.current?.focus(),
    getSelectionStart: () => textareaRef.current?.selectionStart || 0,
    getSelectionEnd: () => textareaRef.current?.selectionEnd || 0,
    setSelectionRange: (start: number, end: number) => {
      textareaRef.current?.setSelectionRange(start, end);
    }
  }), []);

  // 处理文本变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    handleInput(e);
  };

  // 处理选择事件
  const handleTextSelect = () => {
    onSelect?.();
  };

  // 增强的焦点处理 - 集成宠物唤醒
  const handleEnhancedFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    handleFocus();
    // 这里会通过WritingPet组件的props传递焦点状态，触发唤醒
  };

  // 增强的失焦处理 - 集成宠物入睡
  const handleEnhancedBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    handleBlur();
    // 这里会通过WritingPet组件的props传递失焦状态，触发入睡
  };

  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    isolation: 'isolate', // 创建新的层叠上下文，避免外部影响
    contain: 'layout', // 限制布局影响范围
    borderColor: 'var(--color-secondary)',
    borderWidth: '1px',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    boxShadow: 'inset 0 0 10px rgba(0, 0, 0, 0.02)',
    padding: '16px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // 更平滑的过渡
    transform: isChapterChanging
      ? `translateY(${chapterChangeDirection === 'next' ? '10px' : '-10px'})`
      : 'translateY(0)',
    opacity: isChapterChanging ? 0.5 : 1,
    height: EDITOR_LAYOUT.CONTAINER_HEIGHT, // 使用统一的高度计算
    minHeight: EDITOR_LAYOUT.MIN_HEIGHT, // 设置最小高度
    maxHeight: LayoutUtils.isMobile() ? '70vh' : '80vh', // 响应式最大高度
    ...style
  };

  // 文本区域样式
  const textareaStyle: React.CSSProperties = {
    color: 'var(--color-text-primary)',
    lineHeight: '1.8',
    fontSize: '1.05rem',
    transition: 'all 0.3s ease-in-out', // 统一过渡时间
    opacity: isChapterChanging ? 0.3 : 1,
    height: EDITOR_LAYOUT.TEXTAREA_HEIGHT, // 使用统一的高度计算
    overflowY: 'auto',
    resize: 'none' // 禁止用户调整大小，保持布局稳定
  };

  return (
    <div
      ref={containerRef}
      className={`enhanced-chapter-editor w-full rounded-xl border ${className}`}
      style={containerStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 纸张纹理背景 */}
      {animationsEnabled && !reducedMotion && (
        <PaperTextureBackground
          isActive={isFocused}
          intensity={isFocused ? 'high' : 'low'}
        />
      )}

      {/* 边框流光动画 */}
      {animationsEnabled && !reducedMotion && (
        <BorderFlowAnimation
          isHovered={isHovered}
          isFocused={isFocused}
          speed={isHovered ? 'fast' : 'normal'}
        />
      )}

      {/* 焦点光晕效果 */}
      {animationsEnabled && !reducedMotion && isFocused && (
        <FocusGlowEffect
          intensity={isFocused ? 'high' : 'low'}
        />
      )}

      {/* 文本输入区域 */}
      <textarea
        ref={textareaRef}
        id="enhanced-chapter-editor"
        className="w-full h-full p-0 border-0 focus:outline-none focus:ring-0 resize-none bg-transparent overflow-auto"
        value={value}
        onChange={handleTextChange}
        onSelect={handleTextSelect}
        onFocus={handleEnhancedFocus}
        onBlur={handleEnhancedBlur}
        onClick={handleTextareaClick}
        placeholder={placeholder}
        style={textareaStyle}
        disabled={disabled || isChapterChanging}
      />

      {/* 墨水扩散效果 */}
      {animationsEnabled && !reducedMotion && ripples.map((ripple) => (
        <InkRippleEffect
          key={ripple.id}
          x={ripple.x}
          y={ripple.y}
          onComplete={() => {
            // 清理完成的涟漪效果
          }}
        />
      ))}

      {/* 点击背景动画效果 */}
      {animationsEnabled && !reducedMotion && clickAnimations.map((animation) => (
        <ClickBackgroundAnimation
          key={animation.id}
          x={animation.x}
          y={animation.y}
          onComplete={() => {
            // 清理完成的背景动画
          }}
        />
      ))}

      {/* 写作宠物 */}
      <WritingPet
        isWriting={isFocused && !disabled}
        inputFrequency={metrics.inputFrequency}
        lastInputTime={metrics.lastInputTime}
        editorFocused={isFocused}
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px'
        }}
      />
    </div>
  );
});

EnhancedChapterEditor.displayName = 'EnhancedChapterEditor';

export default EnhancedChapterEditor;
