"use client";

import { EnhancedSentenceSuggestion } from '@/factories/ai/services/TextProcessingService';

/**
 * 句子上下文信息
 */
export interface SentenceContext {
  previousSentence?: string;
  nextSentence?: string;
  paragraphTheme?: string;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  hitRate: number;
  cacheSize: number;
  hitCount: number;
  missCount: number;
  totalRequests: number;
}

/**
 * 建议缓存管理器
 * 提供智能缓存机制，提高AI批注性能
 */
export class SuggestionCacheManager {
  private cache = new Map<string, EnhancedSentenceSuggestion>();
  private maxSize: number;
  private hitCount = 0;
  private missCount = 0;
  
  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }

  /**
   * 生成缓存键
   * @param sentence 句子文本
   * @param context 上下文信息
   * @returns 缓存键
   */
  generateKey(sentence: string, context?: SentenceContext): string {
    try {
      const contextStr = context ? 
        `${context.previousSentence || ''}_${context.nextSentence || ''}_${context.paragraphTheme || ''}` : '';
      
      const combinedText = sentence + contextStr;
      
      // 使用简单的哈希算法生成键
      return this.simpleHash(combinedText);
    } catch (error) {
      console.error('生成缓存键失败:', error);
      return this.simpleHash(sentence);
    }
  }

  /**
   * 简单哈希函数
   * @param text 文本
   * @returns 哈希值
   */
  private simpleHash(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存的建议
   * @param sentence 句子文本
   * @param context 上下文信息
   * @returns 缓存的建议或null
   */
  get(sentence: string, context?: SentenceContext): EnhancedSentenceSuggestion | null {
    const key = this.generateKey(sentence, context);
    const cached = this.cache.get(key);
    
    if (cached) {
      this.hitCount++;
      // 更新访问时间
      cached.lastAccessed = Date.now();
      
      console.log('🎯 缓存命中:', {
        sentence: sentence.substring(0, 30) + '...',
        cacheKey: key,
        hitRate: this.getHitRate()
      });
      
      return cached;
    } else {
      this.missCount++;
      
      console.log('❌ 缓存未命中:', {
        sentence: sentence.substring(0, 30) + '...',
        cacheKey: key,
        hitRate: this.getHitRate()
      });
      
      return null;
    }
  }

  /**
   * 设置缓存
   * @param sentence 句子文本
   * @param suggestion 建议对象
   * @param context 上下文信息
   */
  set(sentence: string, suggestion: EnhancedSentenceSuggestion, context?: SentenceContext): void {
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }
    
    const key = this.generateKey(sentence, context);
    suggestion.lastAccessed = Date.now();
    this.cache.set(key, suggestion);
    
    console.log('💾 缓存已保存:', {
      sentence: sentence.substring(0, 30) + '...',
      cacheKey: key,
      cacheSize: this.cache.size,
      maxSize: this.maxSize
    });
  }

  /**
   * 清除最近最少使用的缓存项
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, value] of this.cache.entries()) {
      if ((value.lastAccessed || 0) < oldestTime) {
        oldestTime = value.lastAccessed || 0;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log('🗑️ 清除最旧缓存项:', oldestKey);
    }
  }

  /**
   * 获取缓存命中率
   * @returns 命中率 (0-1)
   */
  getHitRate(): number {
    const total = this.hitCount + this.missCount;
    return total > 0 ? this.hitCount / total : 0;
  }

  /**
   * 获取缓存统计信息
   * @returns 统计信息
   */
  getStats(): CacheStats {
    return {
      hitRate: this.getHitRate(),
      cacheSize: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      totalRequests: this.hitCount + this.missCount
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    console.log('🧹 缓存已清空');
  }

  /**
   * 删除特定的缓存项
   * @param sentence 句子文本
   * @param context 上下文信息
   * @returns 是否删除成功
   */
  delete(sentence: string, context?: SentenceContext): boolean {
    const key = this.generateKey(sentence, context);
    const deleted = this.cache.delete(key);
    
    if (deleted) {
      console.log('🗑️ 删除缓存项:', key);
    }
    
    return deleted;
  }

  /**
   * 检查缓存是否存在
   * @param sentence 句子文本
   * @param context 上下文信息
   * @returns 是否存在
   */
  has(sentence: string, context?: SentenceContext): boolean {
    const key = this.generateKey(sentence, context);
    return this.cache.has(key);
  }

  /**
   * 获取缓存大小
   * @returns 缓存项数量
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 设置最大缓存大小
   * @param maxSize 最大大小
   */
  setMaxSize(maxSize: number): void {
    this.maxSize = maxSize;
    
    // 如果当前缓存超过新的最大大小，清理多余项
    while (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed();
    }
  }

  /**
   * 导出缓存数据（用于持久化）
   * @returns 缓存数据
   */
  export(): { [key: string]: EnhancedSentenceSuggestion } {
    const exported: { [key: string]: EnhancedSentenceSuggestion } = {};
    
    for (const [key, value] of this.cache.entries()) {
      exported[key] = value;
    }
    
    return exported;
  }

  /**
   * 导入缓存数据（用于恢复）
   * @param data 缓存数据
   */
  import(data: { [key: string]: EnhancedSentenceSuggestion }): void {
    this.clear();
    
    for (const [key, value] of Object.entries(data)) {
      if (this.cache.size < this.maxSize) {
        this.cache.set(key, value);
      }
    }
    
    console.log('📥 导入缓存数据:', this.cache.size, '项');
  }
}

// 创建全局缓存管理器实例
export const globalSuggestionCache = new SuggestionCacheManager(1000);
