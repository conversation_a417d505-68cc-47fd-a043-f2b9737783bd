"use client";

import React, { useState } from 'react';
import { createNotificationFactory } from '@/factories/notification/NotificationFactory';

interface TextSegmentationToolProps {
  onSegmented?: (segments: string[]) => void;
  onClose?: () => void;
  initialText?: string;
}

/**
 * 文本分段工具
 * 用于将长文本按照指定字数分段，保证句子完整性
 */
export const TextSegmentationTool: React.FC<TextSegmentationToolProps> = ({
  onSegmented,
  onClose,
  initialText = ''
}) => {
  const [text, setText] = useState(initialText);
  const [segments, setSegments] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [charsPerSegment, setCharsPerSegment] = useState(500);
  const [respectSentences, setRespectSentences] = useState(true);

  // 创建通知工厂
  const notificationFactory = createNotificationFactory();
  const notification = notificationFactory.createNotificationComponent();

  // 按照指定字数分段，保证句子完整性
  const segmentByCharCount = () => {
    if (!text.trim()) {
      notification.showWarning('请输入要分段的文本');
      return;
    }

    setLoading(true);

    try {
      // 首先按句子分割文本
      // 匹配中文句号、感叹号、问号，以及英文句号、感叹号、问号后跟空格或行尾的情况
      const sentenceRegex = /([。！？]|[.!?](?:\s|$))/;
      const sentences = text.split(sentenceRegex)
        .reduce((result: string[], part, index, array) => {
          // 将句子和标点符号重新组合
          if (index % 2 === 0 && index < array.length - 1) {
            result.push(part + array[index + 1]);
          } else if (index % 2 === 0 && index === array.length - 1 && part.trim()) {
            // 处理最后一个部分（如果没有以标点符号结尾）
            result.push(part);
          }
          return result;
        }, [])
        .filter(s => s.trim()); // 过滤空句子

      const result: string[] = [];
      let currentSegment = '';

      for (const sentence of sentences) {
        // 如果当前段落加上这个句子超过了字数限制
        if (respectSentences && currentSegment.length + sentence.length > charsPerSegment && currentSegment.length > 0) {
          // 将当前段落添加到结果中
          result.push(currentSegment);
          // 开始新的段落
          currentSegment = sentence;
        } else {
          // 否则，将句子添加到当前段落
          currentSegment += sentence;

          // 如果不需要保持句子完整性，并且当前段落长度超过限制，则强制分段
          if (!respectSentences && currentSegment.length >= charsPerSegment) {
            result.push(currentSegment);
            currentSegment = '';
          }
        }
      }

      // 添加最后一个段落（如果有）
      if (currentSegment.length > 0) {
        result.push(currentSegment);
      }

      setSegments(result);

      if (onSegmented) {
        onSegmented(result);
      }

      notification.showSuccess(`分段完成，共 ${result.length} 段`);
    } catch (error) {
      console.error('分段失败:', error);
      notification.showError('分段失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 复制分段结果
  const copySegments = () => {
    if (segments.length === 0) {
      notification.showWarning('没有可复制的分段结果');
      return;
    }

    const segmentsText = segments.join('\n\n');
    navigator.clipboard.writeText(segmentsText)
      .then(() => notification.showSuccess('分段结果已复制到剪贴板'))
      .catch(() => notification.showError('复制失败，请手动复制'));
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-blue-600">文本分段工具</h2>
        {onClose && (
          <button
            className="p-1 rounded-full hover:bg-gray-200"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">输入要分段的文本</label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="粘贴需要分段的长文本..."
          className="w-full p-2 border rounded h-40 focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">每段字符数</label>
          <input
            type="number"
            value={charsPerSegment}
            onChange={(e) => setCharsPerSegment(Math.max(100, parseInt(e.target.value) || 500))}
            min="100"
            className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">建议值: 500-1000</p>
        </div>

        <div className="flex items-center">
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              checked={respectSentences}
              onChange={(e) => setRespectSentences(e.target.checked)}
              className="form-checkbox text-blue-600"
            />
            <span className="ml-2">保持句子完整性</span>
          </label>
          <div className="ml-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor" title="选中此项时，分段会在句子结束处进行，确保句子不会被截断">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="flex space-x-2 mb-4">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          onClick={segmentByCharCount}
          disabled={loading || !text.trim()}
        >
          {loading ? '处理中...' : '开始分段'}
        </button>

        <button
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          onClick={copySegments}
          disabled={segments.length === 0}
        >
          复制分段结果
        </button>
      </div>

      {segments.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">分段结果 ({segments.length} 段):</h3>
          <div className="border rounded bg-gray-50 max-h-80 overflow-y-auto">
            {segments.map((segment, index) => (
              <div key={index} className="p-3 border-b last:border-b-0 hover:bg-gray-100">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-medium text-gray-500">段落 {index + 1}</span>
                  <span className="text-xs text-gray-400">{segment.length} 字符</span>
                </div>
                <p className="whitespace-pre-wrap text-sm">{segment}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-center">分段处理中...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextSegmentationTool;
