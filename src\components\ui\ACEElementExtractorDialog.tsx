"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { elementExtractionService, ExtractionOptions } from '@/services/ElementExtractionService';
import { ACEFrameworkManager } from '@/services/ACEFrameworkManager';
import { ExtractedElement } from '@/types/ACEFrameworkTypes';

interface ACEElementExtractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExtracted?: (elements: ExtractedElement[]) => void;
}

/**
 * ACE元素拆解对话框组件
 * 提供文本输入、AI分析、结果预览和保存确认等功能
 */
export const ACEElementExtractorDialog: React.FC<ACEElementExtractorDialogProps> = ({
  isOpen,
  onClose,
  onExtracted
}) => {
  // 基础状态
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 提取选项
  const [extractionOptions, setExtractionOptions] = useState<ExtractionOptions>({
    extractionType: 'ace-elements',
    minImportance: 3
  });
  
  // 提取结果
  const [extractedElements, setExtractedElements] = useState<ExtractedElement[]>([]);
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  
  // 流式响应
  const [streamResponse, setStreamResponse] = useState('');
  
  // 已有元素（用于去重）
  const [existingElements, setExistingElements] = useState<ExtractedElement[]>([]);

  // 重置状态
  const resetState = () => {
    setInputText('');
    setError(null);
    setExtractedElements([]);
    setSelectedElements([]);
    setStreamResponse('');
  };

  // 对话框打开时重置状态并加载已有元素
  useEffect(() => {
    if (isOpen) {
      resetState();
      loadExistingElements();
    }
  }, [isOpen]);

  // 加载已有元素
  const loadExistingElements = () => {
    try {
      const elements = ACEFrameworkManager.getExtractedElements();
      console.log('📦 加载已有元素:', elements.length, '个');
      setExistingElements(elements);
    } catch (error) {
      console.warn('加载已有元素失败:', error);
    }
  };

  // 组件挂载时加载已有元素
  React.useEffect(() => {
    if (isOpen) {
      loadExistingElements();
    }
  }, [isOpen]);

  // 处理文本输入
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
    setError(null);
  };

  // 处理选项变更
  const handleOptionChange = (key: keyof ExtractionOptions, value: any) => {
    setExtractionOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 流式响应回调
  const onStreamChunk = (chunk: string) => {
    setStreamResponse(prev => prev + chunk);
  };

  // 处理提取
  const handleExtract = async () => {
    if (!inputText.trim()) {
      setError('请输入要分析的文本内容');
      return;
    }

    setIsLoading(true);
    setError(null);
    setStreamResponse('');
    setExtractedElements([]);

    try {
      setStreamResponse('开始分析文本内容...\n');

      const options: ExtractionOptions = {
        ...extractionOptions,
        onStreamChunk,
        existingElements
      };

      const result = await elementExtractionService.extractElements(inputText, options);

      if (result.success) {
        setExtractedElements(result.elements);
        setSelectedElements(result.elements.map(el => el.id));
        setStreamResponse(prev => prev + `\n✅ 分析完成！共提取到 ${result.elements.length} 个元素\n`);
      } else {
        setError(result.error || '提取失败');
        setStreamResponse(prev => prev + `\n❌ 提取失败: ${result.error}\n`);
      }
    } catch (error) {
      console.error('提取元素失败:', error);
      setError(error instanceof Error ? error.message : '提取过程中发生未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理元素选择
  const handleElementSelection = (elementId: string) => {
    setSelectedElements(prev => {
      if (prev.includes(elementId)) {
        return prev.filter(id => id !== elementId);
      } else {
        return [...prev, elementId];
      }
    });
  };

  // 全选/取消全选（只选择非重复元素）
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 只选择没有重复警告的元素
      const nonDuplicateElements = extractedElements.filter(el => !isElementDuplicate(el));
      setSelectedElements(nonDuplicateElements.map(el => el.id));
    } else {
      setSelectedElements([]);
    }
  };

  // 保存选中的元素
  const handleSave = async () => {
    if (selectedElements.length === 0) {
      setError('请至少选择一个元素进行保存');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const elementsToSave = extractedElements.filter(el => selectedElements.includes(el.id));
      const success = ACEFrameworkManager.saveExtractedElements(elementsToSave);

      if (success) {
        setStreamResponse(prev => prev + `\n✅ 成功保存 ${elementsToSave.length} 个元素到ACE框架管理系统\n`);
        
        // 通知父组件
        if (onExtracted) {
          onExtracted(elementsToSave);
        }

        // 延迟关闭对话框
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError('保存元素失败，请重试');
      }
    } catch (error) {
      console.error('保存元素失败:', error);
      setError(error instanceof Error ? error.message : '保存过程中发生未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 按分类分组元素
  const groupElementsByCategory = (elements: ExtractedElement[]): Record<string, ExtractedElement[]> => {
    const groups: Record<string, ExtractedElement[]> = {};

    elements.forEach(element => {
      const category = element.category || '未分类';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(element);
    });

    return groups;
  };

  // 获取分类显示名称
  const getCategoryDisplayName = (category: string): string => {
    const categoryNames: Record<string, string> = {
      '关键词': '🔥 关键词',
      '技巧': '✨ 写作技巧',
      '结构': '🏗️ 故事结构',
      '风格': '🎨 文体风格',
      '创意': '💡 创意元素',
      '场景': '🌍 场景设定',
      '名词': '📝 专业名词',
      '未分类': '❓ 未分类'
    };

    return categoryNames[category] || `📋 ${category}`;
  };

  // 处理分类选择（只选择非重复元素）
  const handleCategorySelection = (category: string, checked: boolean) => {
    const categoryElements = extractedElements.filter(el => el.category === category);

    setSelectedElements(prev => {
      if (checked) {
        // 只添加该分类下没有重复警告的元素
        const nonDuplicateElements = categoryElements.filter(el => !isElementDuplicate(el));
        const nonDuplicateElementIds = nonDuplicateElements.map(el => el.id);

        const newSelected = [...prev];
        nonDuplicateElementIds.forEach(id => {
          if (!newSelected.includes(id)) {
            newSelected.push(id);
          }
        });
        return newSelected;
      } else {
        // 移除该分类下的所有元素（包括重复的）
        const categoryElementIds = categoryElements.map(el => el.id);
        return prev.filter(id => !categoryElementIds.includes(id));
      }
    });
  };

  // 计算字符串相似度（使用编辑距离算法）
  const calculateSimilarity = (str1: string, str2: string): number => {
    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 1.0;
    if (s1.length === 0 || s2.length === 0) return 0.0;

    // 使用编辑距离算法计算相似度
    const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));

    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= s2.length; j++) {
      for (let i = 1; i <= s1.length; i++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j - 1][i] + 1,     // 删除
          matrix[j][i - 1] + 1,     // 插入
          matrix[j - 1][i - 1] + cost // 替换
        );
      }
    }

    const maxLength = Math.max(s1.length, s2.length);
    const editDistance = matrix[s2.length][s1.length];
    return 1 - (editDistance / maxLength);
  };

  // 检查元素是否重复（优化的多维度匹配策略）
  const isElementDuplicate = (element: ExtractedElement): boolean => {
    if (!existingElements || existingElements.length === 0) {
      console.log('🔍 重复检测: 没有已有元素，跳过检测');
      return false;
    }

    const NAME_THRESHOLD = 0.3; // 名称相似度阈值
    const DESCRIPTION_THRESHOLD = 0.2; // 描述相似度阈值

    for (const existingElement of existingElements) {
      // 计算名称相似度
      let maxNameSimilarity = 0;
      let bestNameMatch = '';
      for (const currentName of element.elements) {
        for (const existingName of existingElement.elements) {
          const nameSimilarity = calculateSimilarity(currentName, existingName);
          if (nameSimilarity > maxNameSimilarity) {
            maxNameSimilarity = nameSimilarity;
            bestNameMatch = existingName;
          }
        }
      }

      // 计算描述相似度
      let descriptionSimilarity = 0;
      if (element.sourceText && existingElement.sourceText) {
        descriptionSimilarity = calculateSimilarity(element.sourceText, existingElement.sourceText);
      }

      // 多重判断策略：
      // 1. 名称高度相似（≥0.7）直接判定为重复
      // 2. 名称中等相似（≥0.5）且描述高度相似（≥0.7）判定为重复
      // 3. 描述极高相似（≥0.8）且名称有一定相似（≥0.3）判定为重复

      const isNameHighSimilar = maxNameSimilarity >= NAME_THRESHOLD;
      const isNameMediumSimilar = maxNameSimilarity >= 0.5;
      const isNameLowSimilar = maxNameSimilarity >= 0.3;
      const isDescriptionHighSimilar = descriptionSimilarity >= DESCRIPTION_THRESHOLD;
      const isDescriptionVeryHighSimilar = descriptionSimilarity >= 0.8;

      let isDuplicate = false;
      let reason = '';

      if (isNameHighSimilar) {
        isDuplicate = true;
        reason = '名称高度相似';
      } else if (isNameMediumSimilar && isDescriptionHighSimilar) {
        isDuplicate = true;
        reason = '名称中等相似且描述高度相似';
      } else if (isNameLowSimilar && isDescriptionVeryHighSimilar) {
        isDuplicate = true;
        reason = '描述极高相似且名称有关联';
      }

      if (isDuplicate) {
        console.log('🔍 重复检测: 发现相似元素', {
          currentElement: element.elements.join(', '),
          existingElement: existingElement.elements.join(', '),
          nameSimilarity: maxNameSimilarity.toFixed(3),
          descriptionSimilarity: descriptionSimilarity.toFixed(3),
          reason: reason,
          bestNameMatch: bestNameMatch,
          isDuplicate: true
        });
        return true;
      }
    }

    console.log('🔍 重复检测: 未发现重复', {
      currentElement: element.elements,
      existingElementsCount: existingElements.length,
      isDuplicate: false
    });

    return false;
  };

  // 获取元素的最佳相似度信息（找到最相似的重复元素）
  const getElementSimilarityInfo = (element: ExtractedElement): {
    similarElement: string;
    nameSimilarity: number;
    descriptionSimilarity: number;
    matchReason: string;
  } | null => {
    if (!existingElements || existingElements.length === 0) return null;

    const NAME_THRESHOLD = 0.3;
    const DESCRIPTION_THRESHOLD = 0.2;

    let bestMatch: {
      similarElement: string;
      nameSimilarity: number;
      descriptionSimilarity: number;
      matchReason: string;
      priority: number; // 优先级：1=名称高度相似，2=名称+描述相似，3=描述极度相似
      score: number; // 综合评分，用于同优先级内的排序
    } | null = null;

    for (const existingElement of existingElements) {
      // 计算名称相似度
      let maxNameSimilarity = 0;
      for (const currentName of element.elements) {
        for (const existingName of existingElement.elements) {
          const nameSimilarity = calculateSimilarity(currentName, existingName);
          maxNameSimilarity = Math.max(maxNameSimilarity, nameSimilarity);
        }
      }

      // 计算描述相似度
      let descriptionSimilarity = 0;
      if (element.sourceText && existingElement.sourceText) {
        descriptionSimilarity = calculateSimilarity(element.sourceText, existingElement.sourceText);
      }

      // 应用相同的多重判断策略
      const isNameHighSimilar = maxNameSimilarity >= NAME_THRESHOLD;
      const isNameMediumSimilar = maxNameSimilarity >= 0.5;
      const isNameLowSimilar = maxNameSimilarity >= 0.3;
      const isDescriptionHighSimilar = descriptionSimilarity >= DESCRIPTION_THRESHOLD;
      const isDescriptionVeryHighSimilar = descriptionSimilarity >= 0.8;

      let matchReason = '';
      let priority = 0;
      let score = 0;

      if (isNameHighSimilar) {
        matchReason = '名称高度相似';
        priority = 1;
        score = maxNameSimilarity; // 名称相似度作为评分
      } else if (isNameMediumSimilar && isDescriptionHighSimilar) {
        matchReason = '名称+描述相似';
        priority = 2;
        score = maxNameSimilarity * 0.6 + descriptionSimilarity * 0.4; // 综合评分
      } else if (isNameLowSimilar && isDescriptionVeryHighSimilar) {
        matchReason = '描述极度相似';
        priority = 3;
        score = descriptionSimilarity; // 描述相似度作为评分
      }

      if (matchReason) {
        // 选择最佳匹配：优先级高的优先，同优先级内评分高的优先
        if (!bestMatch || priority < bestMatch.priority || (priority === bestMatch.priority && score > bestMatch.score)) {
          bestMatch = {
            similarElement: existingElement.elements.join(', '),
            nameSimilarity: maxNameSimilarity,
            descriptionSimilarity: descriptionSimilarity,
            matchReason: matchReason,
            priority: priority,
            score: score
          };
        }
      }
    }

    return bestMatch ? {
      similarElement: bestMatch.similarElement,
      nameSimilarity: bestMatch.nameSimilarity,
      descriptionSimilarity: bestMatch.descriptionSimilarity,
      matchReason: bestMatch.matchReason
    } : null;
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-[800px] max-h-[85vh] overflow-hidden flex flex-col"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* 对话框头部 */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">ACE元素拆解</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                智能分析文本内容，提取可复用的创作元素
              </p>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              onClick={onClose}
              disabled={isLoading}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 对话框内容 */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* 文本输入区域 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  输入文本内容
                </label>
                <textarea
                  value={inputText}
                  onChange={handleTextChange}
                  placeholder="请输入要分析的文本内容，可以是小说片段、创作技巧、优秀范例等..."
                  className="w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-gray-100"
                  disabled={isLoading}
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {inputText.length} 字符
                  </span>
                  {inputText.length > 5000 && (
                    <span className="text-xs text-amber-600 dark:text-amber-400">
                      建议控制在5000字符以内以获得更好的分析效果
                    </span>
                  )}
                </div>
              </div>

              {/* 提取选项 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  提取选项
                </label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-4">
                    <label className="text-sm text-gray-600 dark:text-gray-400">最低重要性：</label>
                    <select
                      value={extractionOptions.minImportance}
                      onChange={(e) => handleOptionChange('minImportance', parseInt(e.target.value))}
                      className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-100"
                      disabled={isLoading}
                    >
                      <option value={1}>1星 - 所有元素</option>
                      <option value={2}>2星 - 较重要</option>
                      <option value={3}>3星 - 重要</option>
                      <option value={4}>4星 - 很重要</option>
                      <option value={5}>5星 - 极重要</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* 错误信息 */}
              {error && (
                <motion.div
                  className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                </motion.div>
              )}

              {/* 流式响应显示 */}
              {streamResponse && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    分析过程
                  </label>
                  <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3 max-h-32 overflow-y-auto">
                    <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap font-mono">
                      {streamResponse}
                    </pre>
                  </div>
                </div>
              )}

              {/* 提取结果显示 */}
              {extractedElements.length > 0 && (
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        提取结果 ({extractedElements.length} 个元素)
                      </label>
                      {(() => {
                        const duplicateCount = extractedElements.filter(el => isElementDuplicate(el)).length;
                        return duplicateCount > 0 && (
                          <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                            ⚠️ 检测到 {duplicateCount} 个重复元素，已用红色边框标记
                          </p>
                        );
                      })()}
                    </div>
                    <div className="flex items-center space-x-2">
                      {(() => {
                        const nonDuplicateElements = extractedElements.filter(el => !isElementDuplicate(el));
                        const nonDuplicateSelected = selectedElements.filter(id =>
                          nonDuplicateElements.some(el => el.id === id)
                        );
                        return (
                          <>
                            <input
                              type="checkbox"
                              id="selectAll"
                              checked={nonDuplicateElements.length > 0 && nonDuplicateSelected.length === nonDuplicateElements.length}
                              onChange={(e) => handleSelectAll(e.target.checked)}
                              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                              disabled={isLoading}
                            />
                            <label htmlFor="selectAll" className="text-sm text-gray-600 dark:text-gray-400">
                              全选 ({nonDuplicateElements.length} 个非重复)
                            </label>
                          </>
                        );
                      })()}
                    </div>
                  </div>

                  <div className="max-h-80 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                    {/* 按分类分组显示 */}
                    {Object.entries(groupElementsByCategory(extractedElements)).map(([category, elements]) => (
                      <div key={category} className="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                        {/* 分类标题 */}
                        <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {getCategoryDisplayName(category)}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              ({elements.length} 个)
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {(() => {
                              const nonDuplicateElements = elements.filter(el => !isElementDuplicate(el));
                              const nonDuplicateSelected = selectedElements.filter(id =>
                                nonDuplicateElements.some(el => el.id === id)
                              );
                              return (
                                <>
                                  <input
                                    type="checkbox"
                                    checked={nonDuplicateElements.length > 0 && nonDuplicateSelected.length === nonDuplicateElements.length}
                                    onChange={(e) => handleCategorySelection(category, e.target.checked)}
                                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                    disabled={isLoading}
                                  />
                                  <label className="text-xs text-gray-600 dark:text-gray-400">
                                    全选分类 ({nonDuplicateElements.length}/{elements.length})
                                  </label>
                                </>
                              );
                            })()}
                          </div>
                        </div>

                        {/* 分类下的元素 */}
                        <div className="p-3 space-y-3">
                          {elements.map((element) => {
                            const isDuplicate = isElementDuplicate(element);
                            const similarityInfo = getElementSimilarityInfo(element);
                            return (
                              <motion.div
                                key={element.id}
                                className={`flex items-start space-x-3 p-3 bg-white dark:bg-gray-900 rounded-lg border ${
                                  isDuplicate
                                    ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/10'
                                    : 'border-gray-100 dark:border-gray-700'
                                }`}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.2 }}
                              >
                              <input
                                type="checkbox"
                                checked={selectedElements.includes(element.id)}
                                onChange={() => handleElementSelection(element.id)}
                                className="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                disabled={isLoading}
                              />
                              <div className="flex-1 min-w-0">
                                {/* 重复警告 */}
                                {isDuplicate && similarityInfo && (
                                  <div className="mb-2">
                                    <div className="flex items-center space-x-1 mb-1">
                                      <svg className="w-4 h-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                                      </svg>
                                      <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                                        与"{similarityInfo.similarElement}"重复 ({similarityInfo.matchReason})
                                      </span>
                                    </div>
                                    <div className="text-xs text-red-500 dark:text-red-400 ml-5">
                                      名称相似度: {Math.round(similarityInfo.nameSimilarity * 100)}% |
                                      描述相似度: {Math.round(similarityInfo.descriptionSimilarity * 100)}%
                                    </div>
                                  </div>
                                )}

                                <div className="flex items-center space-x-2 mb-2">
                                  <span className={`text-sm font-medium ${isDuplicate ? 'text-red-700 dark:text-red-300' : 'text-gray-900 dark:text-gray-100'}`}>
                                    {element.elements.join(', ')}
                                  </span>
                                  {element.confidence && (
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {Math.round(element.confidence * 100)}%
                                    </span>
                                  )}
                                </div>

                                {/* 显示description信息 */}
                                {element.sourceText && (
                                  <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 leading-relaxed">
                                    {element.sourceText}
                                  </div>
                                )}

                                {element.tags && element.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1">
                                    {element.tags.map((tag, index) => (
                                      <span
                                        key={index}
                                        className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </motion.div>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 底部按钮区域 */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {extractedElements.length > 0 && (
                  <span>已选择 {selectedElements.length} / {extractedElements.length} 个元素</span>
                )}
              </div>

              <div className="flex space-x-3">
                <button
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  {extractedElements.length > 0 ? '关闭' : '取消'}
                </button>

                {extractedElements.length === 0 ? (
                  <button
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-lg transition-colors flex items-center"
                    onClick={handleExtract}
                    disabled={isLoading || !inputText.trim()}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        分析中...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                        开始分析
                      </>
                    )}
                  </button>
                ) : (
                  <button
                    className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 rounded-lg transition-colors flex items-center"
                    onClick={handleSave}
                    disabled={isLoading || selectedElements.length === 0}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        保存中...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        保存选中元素
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ACEElementExtractorDialog;
