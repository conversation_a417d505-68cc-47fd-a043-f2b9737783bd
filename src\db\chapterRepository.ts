import { v4 as uuidv4 } from 'uuid';
import { db, Chapter } from './database';

/**
 * 章节仓库
 */
export class ChapterRepository {
  /**
   * 获取书籍的所有章节
   * @param bookId 书籍ID
   * @returns 章节列表，按顺序排序
   */
  async getChaptersByBookId(bookId: string): Promise<Chapter[]> {
    return await db.chapters
      .where('bookId')
      .equals(bookId)
      .sortBy('order');
  }

  /**
   * 根据ID获取章节
   * @param id 章节ID
   * @returns 章节对象，如果不存在则返回undefined
   */
  async getChapterById(id: string): Promise<Chapter | undefined> {
    return await db.chapters.get(id);
  }

  /**
   * 创建新章节
   * @param bookId 书籍ID
   * @param title 章节标题
   * @param content 章节内容
   * @param order 章节顺序（可选，默认为最后一个）
   * @returns 创建的章节对象
   */
  async createChapter(bookId: string, title: string, content: string, order?: number): Promise<Chapter> {
    // 如果没有指定顺序，则获取最后一个章节的顺序并加1
    if (order === undefined) {
      const chapters = await this.getChaptersByBookId(bookId);
      order = chapters.length > 0 ? chapters[chapters.length - 1].order + 1 : 0;
    }

    // 检查是否已存在相同order的章节（防重复创建）
    const existingChapter = await db.chapters
      .where('bookId')
      .equals(bookId)
      .and(ch => ch.order === order)
      .first();

    if (existingChapter) {
      console.warn(`章节已存在: bookId=${bookId}, order=${order}, 返回现有章节`);
      return existingChapter;
    }

    const now = new Date();
    const wordCount = this.countWords(content);

    const chapter: Chapter = {
      id: uuidv4(),
      bookId,
      title,
      content,
      order,
      wordCount,
      createdAt: now,
      updatedAt: now
    };

    await db.chapters.add(chapter);

    // 更新书籍的最后修改时间
    await db.books.update(bookId, { updatedAt: now });

    return chapter;
  }

  /**
   * 更新章节
   * @param id 章节ID
   * @param updates 要更新的字段
   * @returns 更新后的章节对象
   */
  async updateChapter(id: string, updates: Partial<Omit<Chapter, 'id' | 'bookId' | 'createdAt'>>): Promise<Chapter | undefined> {
    const chapter = await this.getChapterById(id);
    if (!chapter) {
      return undefined;
    }

    // 如果更新了内容，重新计算字数
    let wordCount = chapter.wordCount;
    if (updates.content !== undefined) {
      wordCount = this.countWords(updates.content);
    }

    const now = new Date();
    const updatedChapter = {
      ...chapter,
      ...updates,
      wordCount,
      updatedAt: now
    };

    await db.chapters.update(id, updatedChapter);

    // 更新书籍的最后修改时间
    await db.books.update(chapter.bookId, { updatedAt: now });

    return updatedChapter;
  }

  /**
   * 删除章节
   * @param id 章节ID
   * @returns 是否删除成功
   */
  async deleteChapter(id: string): Promise<boolean> {
    try {
      const chapter = await this.getChapterById(id);
      if (!chapter) {
        return false;
      }

      await db.chapters.delete(id);

      // 更新书籍的最后修改时间
      await db.books.update(chapter.bookId, { updatedAt: new Date() });

      return true;
    } catch (error) {
      console.error('删除章节失败', error);
      return false;
    }
  }

  /**
   * 重新排序章节
   * @param bookId 书籍ID
   * @param chapterIds 章节ID列表，按新顺序排列
   * @returns 是否重新排序成功
   */
  async reorderChapters(bookId: string, chapterIds: string[]): Promise<boolean> {
    try {
      // 获取所有章节
      const chapters = await this.getChaptersByBookId(bookId);

      // 检查章节ID列表是否包含所有章节
      if (chapters.length !== chapterIds.length) {
        return false;
      }

      // 更新每个章节的顺序
      await db.transaction('rw', db.chapters, async () => {
        for (let i = 0; i < chapterIds.length; i++) {
          await db.chapters.update(chapterIds[i], { order: i });
        }
      });

      // 更新书籍的最后修改时间
      await db.books.update(bookId, { updatedAt: new Date() });

      return true;
    } catch (error) {
      console.error('重新排序章节失败', error);
      return false;
    }
  }

  /**
   * 计算文本中的字数
   * @param text 文本内容
   * @returns 字数
   */
  private countWords(text: string): number {
    // 移除空白字符和标点符号，然后计算字数
    return text.replace(/\\s+/g, '').replace(/[.,\\/#!$%\\^&\\*;:{}=\\-_`~()]/g, '').length;
  }
}
