"use client";

import React, { useState, useEffect } from 'react';
import { IWorldBuildingPanelComponent } from '../../interfaces/IWorldBuildingPanelComponent';
import { WorldBuilding } from '@/lib/db/dexie';
import { WorldBuildingList } from './WorldBuildingList';
import { WorldBuildingDetail } from './WorldBuildingDetail';
import { WorldBuildingPanelHeader } from './WorldBuildingPanelHeader';
import { WorldBuildingPanelFooter } from './WorldBuildingPanelFooter';
import { DeleteConfirmDialog } from './DeleteConfirmDialog';
import { WorldBuildingExtractorDialog } from './WorldBuildingExtractorDialog';
import { WorldBuildingBatchUpdaterDialog } from './WorldBuildingBatchUpdaterDialog';
import { WorldBuildingCreatorDialog } from './WorldBuildingCreatorDialog';
import { useWorldBuildingData } from './useWorldBuildingData';
import Panel from '../common/Panel';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * 世界观面板组件实现
 */
export class WorldBuildingPanelComponent implements IWorldBuildingPanelComponent {
  private bookId: string = '';
  private isOpen: boolean = false;
  private closeHandler: (() => void) | null = null;
  private createHandler: ((worldBuilding: WorldBuilding) => void) | null = null;
  private updateHandler: ((worldBuilding: WorldBuilding) => void) | null = null;
  private deleteHandler: ((worldBuildingId: string) => void) | null = null;
  private className: string = '';

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this.bookId = bookId;
  }

  /**
   * 设置是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void {
    this.closeHandler = handler;
  }

  /**
   * 设置创建世界观回调函数
   * @param handler 创建世界观回调函数
   */
  onCreate(handler: (worldBuilding: WorldBuilding) => void): void {
    this.createHandler = handler;
  }

  /**
   * 设置更新世界观回调函数
   * @param handler 更新世界观回调函数
   */
  onUpdate(handler: (worldBuilding: WorldBuilding) => void): void {
    this.updateHandler = handler;
  }

  /**
   * 设置删除世界观回调函数
   * @param handler 删除世界观回调函数
   */
  onDelete(handler: (worldBuildingId: string) => void): void {
    this.deleteHandler = handler;
  }

  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void {
    this.className = className;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    return (
      <WorldBuildingPanelComponentView
        bookId={this.bookId}
        isOpen={this.isOpen}
        onClose={this.closeHandler || (() => {})}
        onWorldBuildingCreate={this.createHandler || (() => {})}
        onWorldBuildingUpdate={this.updateHandler || (() => {})}
        onWorldBuildingDelete={this.deleteHandler || (() => {})}
        className={this.className}
      />
    );
  }
}

interface WorldBuildingPanelComponentViewProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  onWorldBuildingCreate: (worldBuilding: WorldBuilding) => void;
  onWorldBuildingUpdate: (worldBuilding: WorldBuilding) => void;
  onWorldBuildingDelete: (worldBuildingId: string) => void;
  className: string;
}

/**
 * 世界观面板组件视图
 */
const WorldBuildingPanelComponentView: React.FC<WorldBuildingPanelComponentViewProps> = ({
  bookId,
  isOpen,
  onClose,
  onWorldBuildingCreate,
  onWorldBuildingUpdate,
  onWorldBuildingDelete,
  className
}) => {
  // 不再需要UI工厂实例

  // 删除确认对话框状态
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [worldBuildingToDelete, setWorldBuildingToDelete] = useState<WorldBuilding | null>(null);

  // 世界观提取对话框状态
  const [isExtractorDialogOpen, setIsExtractorDialogOpen] = useState(false);

  // 批量更新对话框状态
  const [isBatchUpdaterDialogOpen, setIsBatchUpdaterDialogOpen] = useState(false);

  // AI创建对话框状态
  const [isCreatorDialogOpen, setIsCreatorDialogOpen] = useState(false);

  // 多选模式状态
  const [multiSelectMode, setMultiSelectMode] = useState(false);

  // 选中的世界观元素ID列表
  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>([]);

  // 章节列表状态
  const [chapters, setChapters] = useState<GenericChapter[]>([]);

  // 使用世界观数据钩子
  const {
    worldBuildings,
    filteredWorldBuildings,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedWorldBuilding,
    isEditing,
    handleSelectWorldBuilding,
    handleCreateWorldBuilding,
    handleEditWorldBuilding,
    handleSaveWorldBuilding,
    handleCancelEdit,
    handleDeleteWorldBuilding,
    loadWorldBuildings
  } = useWorldBuildingData(bookId, isOpen);

  // 加载章节数据 - 用于 WorldBuildingDetail 组件和章节管理
  useEffect(() => {
    if (isOpen) {
      loadChaptersData();
    }
  }, [isOpen, bookId]);

  // 加载章节数据的函数
  const loadChaptersData = async () => {
    console.log('开始加载章节数据, bookId =', bookId);
    console.log('当前时间戳:', new Date().toISOString());

    try {
      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 src/db/chapterRepository.ts
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果所有方法都失败，设置为空数组
      console.warn('所有获取章节数据的方法都失败，设置为空数组');
      setChapters([]);
    } catch (error) {
      console.error('获取章节数据失败:', error);
      setChapters([]);
    }
  };

  // 处理删除世界观（带确认）
  const handleDeleteWithConfirm = (worldBuildingId: string) => {
    const worldBuilding = worldBuildings.find(wb => wb.id === worldBuildingId);
    if (worldBuilding) {
      setWorldBuildingToDelete(worldBuilding);
      setIsDeleteDialogOpen(true);
    }
  };

  // 处理确认删除
  const handleConfirmDelete = () => {
    if (worldBuildingToDelete && worldBuildingToDelete.id) {
      handleDeleteWorldBuilding(worldBuildingToDelete.id);
      onWorldBuildingDelete(worldBuildingToDelete.id);
    }
    setIsDeleteDialogOpen(false);
    setWorldBuildingToDelete(null);
  };

  // 处理取消删除
  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setWorldBuildingToDelete(null);
  };

  // 处理保存世界观（带回调）
  const handleSaveWithCallback = (worldBuilding: WorldBuilding) => {
    handleSaveWorldBuilding(worldBuilding);

    // 判断是新建还是更新
    if (worldBuilding.id) {
      onWorldBuildingUpdate(worldBuilding);
    } else {
      onWorldBuildingCreate(worldBuilding);
    }
  };

  // 处理打开世界观提取对话框
  const handleOpenExtractorDialog = () => {
    setIsExtractorDialogOpen(true);
  };

  // 处理关闭世界观提取对话框
  const handleCloseExtractorDialog = () => {
    setIsExtractorDialogOpen(false);
    // 关闭对话框后重新加载世界观数据，确保显示最新的提取结果
    loadWorldBuildings();
  };

  // 处理打开批量更新对话框
  const handleOpenBatchUpdaterDialog = () => {
    if (selectedWorldBuildingIds.length === 0) {
      alert('请先选择要更新的世界观元素');
      return;
    }
    setIsBatchUpdaterDialogOpen(true);
  };

  // 处理关闭批量更新对话框
  const handleCloseBatchUpdaterDialog = () => {
    setIsBatchUpdaterDialogOpen(false);
    // 关闭对话框后重新加载世界观数据，确保显示最新的更新结果
    loadWorldBuildings();
  };

  // 处理打开AI创建对话框
  const handleOpenCreatorDialog = () => {
    setIsCreatorDialogOpen(true);
  };

  // 处理关闭AI创建对话框
  const handleCloseCreatorDialog = () => {
    setIsCreatorDialogOpen(false);
    // 关闭对话框后重新加载世界观数据，确保显示最新的创建结果
    loadWorldBuildings();
  };

  // 处理切换多选模式
  const handleToggleMultiSelectMode = () => {
    setMultiSelectMode(!multiSelectMode);
    // 清空选中的世界观元素ID列表
    setSelectedWorldBuildingIds([]);
  };

  // 处理多选变化
  const handleMultiSelectChange = (worldBuildingIds: string[]) => {
    setSelectedWorldBuildingIds(worldBuildingIds);
  };

  // 处理世界观创建（带刷新）
  const handleWorldBuildingCreateWithRefresh = (worldBuilding: WorldBuilding) => {
    onWorldBuildingCreate(worldBuilding);
    // 创建后重新加载世界观数据
    loadWorldBuildings();
  };

  // 处理批量世界观创建（带刷新）
  const handleWorldBuildingBatchCreateWithRefresh = (worldBuildings: WorldBuilding[]) => {
    // 逐个创建世界观元素
    worldBuildings.forEach(wb => {
      onWorldBuildingCreate(wb);
    });
    // 创建后重新加载世界观数据
    loadWorldBuildings();
  };

  // 处理世界观更新（带刷新）
  const handleWorldBuildingUpdateWithRefresh = (worldBuilding: WorldBuilding) => {
    onWorldBuildingUpdate(worldBuilding);
    // 更新后重新加载世界观数据
    loadWorldBuildings();
  };

  // 处理批量世界观更新（带刷新）
  const handleWorldBuildingBatchUpdateWithRefresh = (worldBuildings: WorldBuilding[]) => {
    // 逐个更新世界观元素
    worldBuildings.forEach(wb => {
      onWorldBuildingUpdate(wb);
    });
    // 更新后重新加载世界观数据
    loadWorldBuildings();
    // 关闭多选模式
    setMultiSelectMode(false);
    // 清空选中的世界观元素ID列表
    setSelectedWorldBuildingIds([]);
  };

  // 渲染面板内容
  const renderPanelContent = () => {
    return (
      <div className="flex h-full overflow-hidden">
        {/* 左侧列表 */}
        <div style={{ width: '35%' }} className="border-r border-gray-200">
          <WorldBuildingList
            worldBuildings={filteredWorldBuildings}
            isLoading={isLoading}
            searchQuery={searchQuery}
            selectedWorldBuilding={selectedWorldBuilding}
            onSelectWorldBuilding={handleSelectWorldBuilding}
            onCreateWorldBuilding={handleCreateWorldBuilding}
            onDeleteWorldBuilding={handleDeleteWithConfirm}
            multiSelectMode={multiSelectMode}
            selectedWorldBuildingIds={selectedWorldBuildingIds}
            onMultiSelectChange={handleMultiSelectChange}
          />
        </div>

        {/* 右侧详情/编辑区域 */}
        <div style={{ width: '65%' }}>
          {multiSelectMode ? (
            <div className="h-full p-6 bg-gray-50 rounded-r-lg">
              <div className="bg-white p-6 rounded-lg shadow-sm h-full">
                <h2 className="text-xl font-semibold mb-4">批量操作模式</h2>
                <p className="mb-4">已选择 {selectedWorldBuildingIds.length} 个世界观元素</p>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">选中的世界观元素</h3>
                    <div className="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg">
                      {selectedWorldBuildingIds.map(id => {
                        const wb = worldBuildings.find(wb => wb.id === id);
                        return (
                          <div key={id} className="mb-2 p-2 bg-white rounded-lg shadow-sm">
                            <p className="font-medium">{wb?.name || id}</p>
                            {wb?.category && <p className="text-sm text-gray-500">类别: {wb.category}</p>}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <button
                      className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                      onClick={handleOpenBatchUpdaterDialog}
                      disabled={selectedWorldBuildingIds.length === 0}
                    >
                      批量AI更新
                    </button>
                    <button
                      className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                      onClick={handleToggleMultiSelectMode}
                    >
                      取消多选
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <WorldBuildingDetail
              worldBuilding={selectedWorldBuilding}
              bookId={bookId}
              worldBuildings={worldBuildings}
              isEditing={isEditing}
              onEdit={handleEditWorldBuilding}
              onDelete={handleDeleteWithConfirm}
              onSave={handleSaveWithCallback}
              onCancel={handleCancelEdit}
              chapters={chapters} // 传递章节列表
            />
          )}
        </div>
      </div>
    );
  };

  // 准备面板头部
  const panelHeader = (
    <WorldBuildingPanelHeader
      searchQuery={searchQuery}
      sortBy={sortBy}
      onSearchChange={setSearchQuery}
      onSortChange={setSortBy}
      onCreateWorldBuilding={handleCreateWorldBuilding}
      onExtractWorldBuildings={handleOpenExtractorDialog}
      onBatchUpdateWorldBuildings={handleOpenBatchUpdaterDialog}
      onAICreateWorldBuildings={handleOpenCreatorDialog}
      multiSelectMode={multiSelectMode}
      onToggleMultiSelectMode={handleToggleMultiSelectMode}
    />
  );

  // 准备面板内容
  const panelContent = renderPanelContent();

  // 创建按钮组件
  let saveButton = null;
  let cancelButton = null;

  if (isEditing) {
    saveButton = (
      <button
        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
        onClick={() => {
          if (selectedWorldBuilding) {
            handleSaveWithCallback(selectedWorldBuilding);
          }
        }}
      >
        保存
      </button>
    );

    cancelButton = (
      <button
        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm"
        onClick={handleCancelEdit}
      >
        取消
      </button>
    );
  }

  // 准备面板底部
  const panelFooter = (
    <WorldBuildingPanelFooter
      worldBuildingCount={worldBuildings.length}
      saveButton={saveButton}
      cancelButton={cancelButton}
      isEditing={isEditing}
    />
  );

  return (
    <>
      <Panel
        title="世界观管理"
        isOpen={isOpen}
        size="large"
        fixedHeight={true}
        backgroundColor="var(--color-white)"
        width="70%"
        height="85%"
        enhanced={true}
        literaryTheme={true}
        header={panelHeader}
        content={panelContent}
        footer={panelFooter}
        onClose={onClose}
        className={className}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        worldBuilding={worldBuildingToDelete}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      {/* 世界观提取对话框 */}
      <WorldBuildingExtractorDialog
        isOpen={isExtractorDialogOpen}
        onClose={handleCloseExtractorDialog}
        existingWorldBuildings={worldBuildings}
        onCreateWorldBuilding={handleWorldBuildingCreateWithRefresh}
        onUpdateWorldBuilding={handleWorldBuildingUpdateWithRefresh}
        bookId={bookId}
      />

      {/* 批量更新对话框 */}
      {isBatchUpdaterDialogOpen && (
        <div className="z-50">
          <WorldBuildingBatchUpdaterDialog
            isOpen={isBatchUpdaterDialogOpen}
            onClose={handleCloseBatchUpdaterDialog}
            worldBuildings={worldBuildings.filter(wb => selectedWorldBuildingIds.includes(wb.id!))}
            onUpdate={handleWorldBuildingBatchUpdateWithRefresh}
            bookId={bookId}
          />
        </div>
      )}

      {/* AI创建对话框 */}
      {isCreatorDialogOpen && (
        <div className="z-50">
          <WorldBuildingCreatorDialog
            isOpen={isCreatorDialogOpen}
            onClose={handleCloseCreatorDialog}
            onCreate={handleWorldBuildingBatchCreateWithRefresh}
            bookId={bookId}
          />
        </div>
      )}
    </>
  );
};

// 导出组件
export default WorldBuildingPanelComponent;
