/**
 * 通知组件接口
 * 用于显示各种类型的通知
 */
export interface INotificationComponent {
  /**
   * 显示成功通知
   * @param message 通知消息
   */
  showSuccess(message: string): void;
  
  /**
   * 显示错误通知
   * @param message 通知消息
   */
  showError(message: string): void;
  
  /**
   * 显示信息通知
   * @param message 通知消息
   */
  showInfo(message: string): void;
  
  /**
   * 显示警告通知
   * @param message 通知消息
   */
  showWarning(message: string): void;
}
