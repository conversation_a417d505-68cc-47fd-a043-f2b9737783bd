/**
 * 关键词触发服务
 * 负责检测用户输入中的关键词并触发相应的世界书条目
 */

import { AIGeneratedPrefixStorageService, SavedAIPrefix } from '../ai/AIGeneratedPrefixStorageService';
import { WorldBookPrefix } from '../../types/worldbook';

export interface KeywordMatch {
  /** 匹配的关键词 */
  keyword: string;
  /** 匹配的世界书条目 */
  prefix: WorldBookPrefix;
  /** 匹配位置 */
  position: number;
  /** 匹配长度 */
  length: number;
}

export interface TriggerResult {
  /** 匹配到的条目 */
  matches: KeywordMatch[];
  /** 作为独立消息发送的条目 */
  independentMessages: WorldBookPrefix[];
  /** 添加到用户消息中的条目 */
  appendToUserMessage: WorldBookPrefix[];
}

export class KeywordTriggerService {
  private storageService: AIGeneratedPrefixStorageService;

  constructor() {
    this.storageService = AIGeneratedPrefixStorageService.getInstance();
  }

  /**
   * 检测用户输入中的关键词并返回匹配的世界书条目
   * @param userInput 用户输入文本
   * @returns 触发结果
   */
  detectKeywords(userInput: string): TriggerResult {
    const matches: KeywordMatch[] = [];
    const independentMessages: WorldBookPrefix[] = [];
    const appendToUserMessage: WorldBookPrefix[] = [];
    const triggeredPrefixes = new Set<string>(); // 记录已触发的条目ID，避免重复触发

    // 获取所有启用了关键词触发的世界书条目
    const allPrefixes = this.storageService.getAllSavedPrefixes();
    const worldBookPrefixes = allPrefixes.filter(prefix =>
      this.isWorldBookPrefix(prefix) &&
      (prefix as WorldBookPrefix).enableKeywordTrigger === true
    ) as WorldBookPrefix[];

    // 对每个条目检查关键词匹配
    for (const prefix of worldBookPrefixes) {
      // 如果这个条目已经被触发过，跳过
      if (triggeredPrefixes.has(prefix.id)) {
        continue;
      }

      const keywordMatches = this.findKeywordMatches(userInput, prefix);

      if (keywordMatches.length > 0) {
        // 添加到匹配列表
        matches.push(...keywordMatches);

        // 记录已触发的条目
        triggeredPrefixes.add(prefix.id);

        // 根据设置决定发送方式
        if (prefix.sendAsIndependentMessage) {
          independentMessages.push(prefix);
        } else {
          appendToUserMessage.push(prefix);
        }

        // 记录使用统计
        this.storageService.recordWorldBookUsage(prefix.id, prefix.worldBookSource);

        console.log(`🔍 触发世界书条目: ${prefix.description}，关键词: ${keywordMatches.map(m => m.keyword).join(', ')}`);
      }
    }

    return {
      matches,
      independentMessages,
      appendToUserMessage
    };
  }

  /**
   * 在文本中查找关键词匹配
   * @param text 要搜索的文本
   * @param prefix 世界书条目
   * @returns 匹配结果数组
   */
  private findKeywordMatches(text: string, prefix: WorldBookPrefix): KeywordMatch[] {
    const matches: KeywordMatch[] = [];
    const searchText = text.toLowerCase();

    // 合并所有关键词
    const allKeywords = [
      ...prefix.originalKeys,
      ...prefix.originalKeysSecondary,
      ...prefix.tags.filter(tag => tag && tag.trim().length > 0)
    ].filter(keyword => keyword && typeof keyword === 'string');

    // 去重并过滤掉太短的关键词
    const uniqueKeywords = [...new Set(allKeywords)]
      .filter(keyword => keyword.trim().length >= 2);

    for (const keyword of uniqueKeywords) {
      const keywordLower = keyword.toLowerCase();
      let startIndex = 0;

      // 查找所有匹配位置
      while (true) {
        const index = searchText.indexOf(keywordLower, startIndex);
        if (index === -1) break;

        // 检查是否为完整单词匹配（避免部分匹配）
        if (this.isCompleteWordMatch(searchText, keywordLower, index)) {
          matches.push({
            keyword,
            prefix,
            position: index,
            length: keyword.length
          });
        }

        startIndex = index + 1;
      }
    }

    return matches;
  }

  /**
   * 检查是否为完整单词匹配
   * @param text 文本
   * @param keyword 关键词
   * @param index 匹配位置
   * @returns 是否为完整单词
   */
  private isCompleteWordMatch(text: string, keyword: string, index: number): boolean {
    // 检查前一个字符
    const prevChar = index > 0 ? text[index - 1] : ' ';
    const nextChar = index + keyword.length < text.length ? text[index + keyword.length] : ' ';

    // 如果关键词是中文，不需要单词边界检查
    if (/[\u4e00-\u9fff]/.test(keyword)) {
      return true;
    }

    // 对于英文关键词，检查单词边界
    const isWordChar = (char: string) => /[a-zA-Z0-9_]/.test(char);
    
    return !isWordChar(prevChar) && !isWordChar(nextChar);
  }

  /**
   * 检查是否为世界书前置消息
   * @param prefix 前置消息
   * @returns 是否为世界书前置消息
   */
  private isWorldBookPrefix(prefix: SavedAIPrefix): prefix is WorldBookPrefix {
    return 'worldBookSource' in prefix;
  }

  /**
   * 更新世界书条目的关键词触发设置
   * @param prefixId 条目ID
   * @param enableKeywordTrigger 是否启用关键词触发
   * @param sendAsIndependentMessage 是否作为独立消息发送
   */
  updateKeywordTriggerSettings(
    prefixId: string, 
    enableKeywordTrigger: boolean, 
    sendAsIndependentMessage: boolean
  ): boolean {
    try {
      const allPrefixes = this.storageService.getAllSavedPrefixes();
      const prefixIndex = allPrefixes.findIndex(p => p.id === prefixId);
      
      if (prefixIndex === -1) {
        console.error(`未找到ID为 ${prefixId} 的前置消息`);
        return false;
      }

      const prefix = allPrefixes[prefixIndex];
      if (!this.isWorldBookPrefix(prefix)) {
        console.error(`ID为 ${prefixId} 的前置消息不是世界书条目`);
        return false;
      }

      // 更新设置
      (prefix as WorldBookPrefix).enableKeywordTrigger = enableKeywordTrigger;
      (prefix as WorldBookPrefix).sendAsIndependentMessage = sendAsIndependentMessage;

      // 保存到存储
      this.storageService.updatePrefix(prefixId, prefix);
      
      console.log(`✅ 更新世界书条目 ${prefixId} 的关键词触发设置: 启用=${enableKeywordTrigger}, 独立消息=${sendAsIndependentMessage}`);
      return true;
    } catch (error) {
      console.error('更新关键词触发设置失败:', error);
      return false;
    }
  }

  /**
   * 获取所有启用了关键词触发的世界书条目统计
   * @returns 统计信息
   */
  getKeywordTriggerStats(): {
    totalWorldBookEntries: number;
    enabledKeywordTrigger: number;
    independentMessageCount: number;
    appendToMessageCount: number;
  } {
    const allPrefixes = this.storageService.getAllSavedPrefixes();
    const worldBookPrefixes = allPrefixes.filter(prefix =>
      this.isWorldBookPrefix(prefix)
    ) as WorldBookPrefix[];

    const enabledTrigger = worldBookPrefixes.filter(p => p.enableKeywordTrigger === true);
    const independentMessage = enabledTrigger.filter(p => p.sendAsIndependentMessage === true);
    const appendToMessage = enabledTrigger.filter(p => p.sendAsIndependentMessage !== true);

    return {
      totalWorldBookEntries: worldBookPrefixes.length,
      enabledKeywordTrigger: enabledTrigger.length,
      independentMessageCount: independentMessage.length,
      appendToMessageCount: appendToMessage.length
    };
  }

  /**
   * 更新触发内容变成关键词的设置
   * @param prefixId 前置消息ID
   * @param enableContentAsKeyword 是否启用触发内容变成关键词
   * @returns 是否更新成功
   */
  updateContentAsKeywordSetting(prefixId: string, enableContentAsKeyword: boolean): boolean {
    try {
      const allPrefixes = this.storageService.getAllSavedPrefixes();
      const prefixIndex = allPrefixes.findIndex(p => p.id === prefixId);

      if (prefixIndex === -1) {
        console.error(`未找到ID为 ${prefixId} 的前置消息`);
        return false;
      }

      const prefix = allPrefixes[prefixIndex];
      if (!this.isWorldBookPrefix(prefix)) {
        console.error(`ID为 ${prefixId} 的前置消息不是世界书条目`);
        return false;
      }

      // 更新设置
      (prefix as WorldBookPrefix).enableContentAsKeyword = enableContentAsKeyword;

      // 保存到存储
      this.storageService.updatePrefix(prefixId, prefix);

      console.log(`✅ 更新世界书条目 ${prefixId} 的触发内容变成关键词设置: ${enableContentAsKeyword}`);
      return true;
    } catch (error) {
      console.error('更新触发内容变成关键词设置失败:', error);
      return false;
    }
  }

  /**
   * 批量更新触发内容变成关键词的设置
   * @param prefixIds 前置消息ID数组
   * @param enableContentAsKeyword 是否启用触发内容变成关键词
   * @returns 更新成功的数量
   */
  batchUpdateContentAsKeywordSetting(prefixIds: string[], enableContentAsKeyword: boolean): number {
    let successCount = 0;
    for (const prefixId of prefixIds) {
      if (this.updateContentAsKeywordSetting(prefixId, enableContentAsKeyword)) {
        successCount++;
      }
    }
    console.log(`✅ 批量更新触发内容变成关键词设置: ${successCount}/${prefixIds.length} 个条目`);
    return successCount;
  }
}
