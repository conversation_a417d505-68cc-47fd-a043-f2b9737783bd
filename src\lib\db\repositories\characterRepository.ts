import { v4 as uuidv4 } from 'uuid';
import { db, Character } from '../dexie';

export interface ICharacterRepository {
  getAllByBookId(bookId: string): Promise<Character[]>;
  getById(id: string): Promise<Character | undefined>;
  create(character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, character: Partial<Character>): Promise<void>;
  delete(id: string): Promise<void>;
  getByChapterId(chapterId: string): Promise<Character[]>;
  linkToChapter(characterId: string, chapterId: string): Promise<void>;
  unlinkFromChapter(characterId: string, chapterId: string): Promise<void>;
}

export class CharacterRepository implements ICharacterRepository {
  async getAllByBookId(bookId: string): Promise<Character[]> {
    return await db.characters
      .where('bookId')
      .equals(bookId)
      .sortBy('name');
  }

  async getById(id: string): Promise<Character | undefined> {
    return await db.characters.get(id);
  }

  async create(character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();
    
    await db.characters.add({
      ...character,
      id,
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: character.extractedFromChapterIds || [],
      relatedCharacterIds: character.relatedCharacterIds || [],
      relatedTerminologyIds: character.relatedTerminologyIds || [],
      relatedWorldBuildingIds: character.relatedWorldBuildingIds || []
    });
    
    return id;
  }

  async update(id: string, character: Partial<Character>): Promise<void> {
    await db.characters.update(id, {
      ...character,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    // 获取角色信息，用于更新相关章节
    const character = await db.characters.get(id);
    if (!character) {
      throw new Error(`Character with id ${id} not found`);
    }
    
    await db.transaction('rw', [db.characters, db.chapters], async () => {
      // 从所有相关章节中移除该角色的引用
      for (const chapterId of character.extractedFromChapterIds) {
        const chapter = await db.chapters.get(chapterId);
        if (chapter) {
          const updatedCharacterIds = chapter.characterIds.filter(cid => cid !== id);
          await db.chapters.update(chapterId, { characterIds: updatedCharacterIds });
        }
      }
      
      // 从所有相关角色中移除该角色的引用
      const relatedCharacters = await db.characters
        .where('relatedCharacterIds')
        .anyOf([id])
        .toArray();
      
      for (const relatedCharacter of relatedCharacters) {
        const updatedRelatedCharacterIds = relatedCharacter.relatedCharacterIds.filter(cid => cid !== id);
        await db.characters.update(relatedCharacter.id!, { relatedCharacterIds: updatedRelatedCharacterIds });
      }
      
      // 删除角色
      await db.characters.delete(id);
    });
  }

  async getByChapterId(chapterId: string): Promise<Character[]> {
    const chapter = await db.chapters.get(chapterId);
    if (!chapter) {
      throw new Error(`Chapter with id ${chapterId} not found`);
    }
    
    if (!chapter.characterIds || chapter.characterIds.length === 0) {
      return [];
    }
    
    return await db.characters
      .where('id')
      .anyOf(chapter.characterIds)
      .toArray();
  }

  async linkToChapter(characterId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.characters, db.chapters], async () => {
      // 更新角色的提取章节列表
      const character = await db.characters.get(characterId);
      if (character) {
        const extractedFromChapterIds = [...new Set([...character.extractedFromChapterIds, chapterId])];
        await db.characters.update(characterId, { extractedFromChapterIds });
      }
      
      // 更新章节的角色列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const characterIds = [...new Set([...chapter.characterIds, characterId])];
        await db.chapters.update(chapterId, { characterIds });
      }
    });
  }

  async unlinkFromChapter(characterId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.characters, db.chapters], async () => {
      // 更新角色的提取章节列表
      const character = await db.characters.get(characterId);
      if (character) {
        const extractedFromChapterIds = character.extractedFromChapterIds.filter(id => id !== chapterId);
        await db.characters.update(characterId, { extractedFromChapterIds });
      }
      
      // 更新章节的角色列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const characterIds = chapter.characterIds.filter(id => id !== characterId);
        await db.chapters.update(chapterId, { characterIds });
      }
    });
  }
}

// 创建并导出仓库实例
export const characterRepository = new CharacterRepository();
