import React, { useState, useEffect } from 'react';
import { BannedWordCategory } from '../../../services/storage/BannedWordsStorageService';

interface CreateCategoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateCategory: (category: Omit<BannedWordCategory, 'id' | 'isCustom'>) => void;
  onUpdateCategory?: (categoryId: string, category: Omit<BannedWordCategory, 'id' | 'isCustom'>) => void;
  editingCategory?: BannedWordCategory | null;
}

// 预设的颜色选项
const COLOR_OPTIONS = [
  { name: '红色', value: 'bg-red-50 border-red-200 text-red-800' },
  { name: '橙色', value: 'bg-orange-50 border-orange-200 text-orange-800' },
  { name: '黄色', value: 'bg-yellow-50 border-yellow-200 text-yellow-800' },
  { name: '绿色', value: 'bg-green-50 border-green-200 text-green-800' },
  { name: '蓝色', value: 'bg-blue-50 border-blue-200 text-blue-800' },
  { name: '紫色', value: 'bg-purple-50 border-purple-200 text-purple-800' },
  { name: '粉色', value: 'bg-pink-50 border-pink-200 text-pink-800' },
  { name: '灰色', value: 'bg-gray-50 border-gray-200 text-gray-800' }
];

const CreateCategoryDialog: React.FC<CreateCategoryDialogProps> = ({
  isOpen,
  onClose,
  onCreateCategory,
  onUpdateCategory,
  editingCategory
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLOR_OPTIONS[0].value);
  const [initialWords, setInitialWords] = useState('');

  const isEditMode = !!editingCategory;

  // 编辑模式时初始化表单数据
  useEffect(() => {
    if (isEditMode && editingCategory) {
      setName(editingCategory.name);
      setDescription(editingCategory.description);
      setSelectedColor(editingCategory.color);
      setInitialWords(editingCategory.words.join(', '));
    } else {
      // 创建模式时重置表单
      setName('');
      setDescription('');
      setSelectedColor(COLOR_OPTIONS[0].value);
      setInitialWords('');
    }
  }, [isEditMode, editingCategory, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      alert('请输入分类名称');
      return;
    }

    // 处理词汇
    const words = initialWords
      .split(/[,，\n]/)
      .map(word => word.trim())
      .filter(word => word.length > 0);

    const categoryData: Omit<BannedWordCategory, 'id' | 'isCustom'> = {
      name: name.trim(),
      description: description.trim() || `自定义的${name.trim()}分类`,
      words,
      color: selectedColor
    };

    if (isEditMode && editingCategory && onUpdateCategory) {
      onUpdateCategory(editingCategory.id, categoryData);
    } else {
      onCreateCategory(categoryData);
    }

    // 重置表单
    setName('');
    setDescription('');
    setSelectedColor(COLOR_OPTIONS[0].value);
    setInitialWords('');
    onClose();
  };

  const handleCancel = () => {
    // 重置表单
    setName('');
    setDescription('');
    setSelectedColor(COLOR_OPTIONS[0].value);
    setInitialWords('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10001] p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">
            {isEditMode ? '编辑自定义分类' : '创建自定义分类'}
          </h3>
          <button
            onClick={handleCancel}
            className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* 分类名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              分类名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="例如：网络用语、方言词汇..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
              maxLength={20}
            />
          </div>

          {/* 分类描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              分类描述
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="简单描述这个分类的用途..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm resize-none"
              rows={2}
              maxLength={100}
            />
          </div>

          {/* 颜色选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              分类颜色
            </label>
            <div className="grid grid-cols-4 gap-2">
              {COLOR_OPTIONS.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => setSelectedColor(color.value)}
                  className={`p-2 rounded-lg border-2 text-xs transition-all ${
                    selectedColor === color.value
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : 'border-transparent hover:border-gray-300'
                  } ${color.value}`}
                >
                  {color.name}
                </button>
              ))}
            </div>
          </div>

          {/* 初始词汇 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              初始词汇（可选）
            </label>
            <textarea
              value={initialWords}
              onChange={(e) => setInitialWords(e.target.value)}
              placeholder="输入一些初始的禁用词汇，用逗号或换行分隔..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm resize-none"
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              💡 可以用逗号、中文逗号或换行来分隔多个词汇
            </p>
          </div>

          {/* 预览 */}
          {name && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                预览效果
              </label>
              <div className={`p-3 rounded-lg border ${selectedColor}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">{name}</h4>
                    <p className="text-xs mt-1">
                      {description || `自定义的${name}分类`}
                    </p>
                  </div>
                  <div className="text-xs">
                    ({initialWords.split(/[,，\n]/).filter(w => w.trim()).length}个)
                  </div>
                </div>
              </div>
            </div>
          )}
        </form>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-2 p-4 border-t border-gray-200">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={!name.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
          >
            {isEditMode ? '保存修改' : '创建分类'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateCategoryDialog;
