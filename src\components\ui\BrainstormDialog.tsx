"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import BookTitleGenerator from '../brainstorm/BookTitleGenerator';
import SynopsisGenerator from '../brainstorm/SynopsisGenerator';
import RobotAvatar, { RobotEmotion } from './RobotAvatar';
import { AssociationData, BrainstormResult } from '../brainstorm/types';
import { db, WorldBuilding } from '@/lib/db/dexie';
import { BrainstormAIService } from '@/factories/ai/services/BrainstormAIService';
import { ToolCallResultBubble, ToolCallResult } from './ToolCallResultBubble';
import ToolCallStatusIndicator, { ToolCallStatus } from './ToolCallStatusIndicator';
import { RecommendationEnhancer } from '@/utils/ai/RecommendationEnhancer';
import SessionSidebar from '../chat/SessionSidebar';
import { sessionManager, ChatSession, ChatMessage as SessionChatMessage } from '@/services/chat/SessionManager';
import WorldBuildingPanelComponent from '@/factories/ui/components/WorldBuildingPanel/WorldBuildingPanelComponent';

// 🔧 修复：创建唯一ID生成器，避免重复ID导致的渲染问题
let messageIdCounter = 0;
const generateUniqueMessageId = (): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  const counter = ++messageIdCounter; // 使用递增计数器确保唯一性
  const microTime = performance.now().toString().replace('.', ''); // 添加高精度时间戳
  return `msg_${timestamp}_${random}_${counter}_${microTime.slice(-6)}`;
};

// Markdown渲染组件
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  // 增强的Markdown渲染逻辑，专门优化AI创意内容
  const renderMarkdown = (text: string) => {
    return text
      // 处理emoji和特殊符号
      .replace(/🎯/g, '<span class="text-orange-500">🎯</span>')
      .replace(/✨/g, '<span class="text-yellow-500">✨</span>')
      .replace(/💡/g, '<span class="text-yellow-400">💡</span>')
      .replace(/🔥/g, '<span class="text-red-500">🔥</span>')
      .replace(/📚/g, '<span class="text-blue-500">📚</span>')
      .replace(/🎭/g, '<span class="text-purple-500">🎭</span>')

      // 处理标题 ## text
      .replace(/^## (.*$)/gm, '<h3 class="text-lg font-bold text-gray-900 mt-4 mb-3 pb-2 border-b border-gray-200">$1</h3>')

      // 处理标题 ### text
      .replace(/^### (.*$)/gm, '<h4 class="text-base font-semibold text-purple-700 mt-3 mb-2">$1</h4>')

      // 处理特殊格式的创意要点 🎯 **text**:
      .replace(/^🎯 \*\*(.*?)\*\*[:：](.*$)/gm, '<div class="bg-orange-50 border-l-4 border-orange-400 p-3 my-2 rounded-r"><div class="flex items-start"><span class="text-orange-500 mr-2">🎯</span><div><strong class="text-orange-800 font-semibold">$1</strong><p class="text-gray-700 mt-1">$2</p></div></div></div>')

      // 处理方案标题格式
      .replace(/^方案(\d+)[:：](.*$)/gm, '<div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4 my-3"><h4 class="text-purple-800 font-bold text-lg mb-2">💡 方案$1：$2</h4>')

      // 处理列表项 - text（先处理列表，再处理内部格式）
      .replace(/^- (.*$)/gm, '<li class="flex items-start mb-2"><span class="text-purple-500 mr-2 mt-1">•</span><span class="text-gray-700 leading-relaxed">$1</span></li>')

      // 处理数字列表 1. text（先处理列表，再处理内部格式）
      .replace(/^(\d+)\. (.*$)/gm, '<li class="flex items-start mb-2"><span class="bg-purple-100 text-purple-700 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5 flex-shrink-0">$1</span><span class="text-gray-700 leading-relaxed">$2</span></li>')

      // 处理粗体 **text**（在列表处理之后）
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900 bg-yellow-50 px-1 rounded">$1</strong>')

      // 处理斜体 *text*（在粗体处理之后，避免冲突）
      .replace(/\*([^*]+)\*/g, '<em class="italic text-purple-700 font-medium">$1</em>')

      // 处理代码块 `code`
      .replace(/`([^`]+)`/g, '<code class="bg-purple-100 text-purple-700 px-2 py-1 rounded text-sm font-mono border">$1</code>')

      // 处理引用块 > text
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-blue-400 bg-blue-50 pl-4 py-2 my-2 italic text-blue-800">$1</blockquote>')

      // 处理分隔线
      .replace(/^---$/gm, '<hr class="border-gray-300 my-4" />')

      // 处理换行，保持段落结构
      .replace(/\n\n/g, '</p><p class="mb-3">')
      .replace(/\n/g, '<br />');
  };

  return (
    <div className="markdown-content leading-relaxed">
      <p className="mb-3" dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }} />
    </div>
  );
};

interface BrainstormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  chapterContent?: string;
  onBrainstormComplete?: (result: any) => void;
}

// 功能类型
type FunctionType = 'bookTitle' | 'synopsis' | 'aiChat';

// 消息类型
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'suggestion' | 'result' | 'tool_call_result' | 'welcome';
  metadata?: any;
  toolCallResult?: ToolCallResult;
}

// 统计卡片组件接口
interface StatCardProps {
  icon: string;
  label: string;
  count: number;
}

// 统计卡片组件 - 浮动胶囊设计
const StatCard: React.FC<StatCardProps> = ({ icon, label, count }) => (
  <motion.div
    className="flex items-center space-x-1 text-xs text-gray-700"
    whileHover={{
      scale: 1.05
    }}
    transition={{ duration: 0.15, ease: "easeOut" }}
  >
    <span className="text-sm">{icon}</span>
    <span className="font-medium">{label}:</span>
    <span className="font-bold text-purple-600">{count}</span>
  </motion.div>
);

/**
 * AI脑洞生成对话框组件 - 多功能选择界面
 * 支持书名生成、简介生成、AI创意聊天等功能
 */
const BrainstormDialog: React.FC<BrainstormDialogProps> = ({
  isOpen,
  onClose,
  bookId,
  chapterContent,
  onBrainstormComplete
}) => {
  // 功能选择状态
  const [selectedFunction, setSelectedFunction] = useState<FunctionType | null>(null);
  const [currentView, setCurrentView] = useState<'select' | 'generate'>('select');

  // 原有的脑洞生成状态
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedAssociations, setSelectedAssociations] = useState<any>({});

  // 会话管理状态
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isSessionSidebarCollapsed, setIsSessionSidebarCollapsed] = useState(false);

  // 聊天状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [robotEmotion, setRobotEmotion] = useState<RobotEmotion>('default');

  // 工具调用状态管理
  const [toolCallStatus, setToolCallStatus] = useState<ToolCallStatus | null>(null);
  const [toolCallMessage, setToolCallMessage] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // � 添加chunk去重状态管理
  const [lastChunk, setLastChunk] = useState('');
  const [chunkHistory, setChunkHistory] = useState<string[]>([]);

  // �🔥 推荐增强开关状态
  const [isRecommendationEnhanced, setIsRecommendationEnhanced] = useState(false);

  // 数据状态
  const [associationData, setAssociationData] = useState<AssociationData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);

  // 世界观管理面板状态
  const [isWorldBuildingPanelOpen, setIsWorldBuildingPanelOpen] = useState(false);

  // AI服务实例
  const [brainstormAIService] = useState(() => new BrainstormAIService());

  /**
   * 将工具调用结果格式化为文本内容，用于消息历史传递
   */
  const formatToolCallResultForHistory = (toolCallResult: ToolCallResult): string => {
    if (toolCallResult.toolType === 'generate_book_titles' && toolCallResult.titles) {
      const titles = toolCallResult.titles
        .map((title: any, index: number) => `${index + 1}. **${title.title}** (评分: ${title.score}/5)${title.reason ? ` - ${title.reason}` : ''}`)
        .join('\n');

      return `📚 **书名生成结果**：\n${titles}\n\n关键词：${toolCallResult.metadata?.keywords?.join(', ') || '无'}`;
    }

    if (toolCallResult.toolType === 'generate_synopsis' && toolCallResult.synopsis) {
      const synopsisContent = typeof toolCallResult.synopsis === 'string'
        ? toolCallResult.synopsis
        : toolCallResult.synopsis.content || JSON.stringify(toolCallResult.synopsis);
      return `📖 **简介生成结果**：\n${synopsisContent}\n\n字数：${toolCallResult.metadata?.length || '未知'}`;
    }

    return `🔧 **工具调用结果**：${JSON.stringify(toolCallResult, null, 2)}`;
  };

  // 重置状态
  useEffect(() => {
    if (isOpen) {
      setSelectedFunction(null);
      setCurrentView('select');
      setMessages([]);
      setInputText('');
      setIsGenerating(false);
    }
  }, [isOpen]);

  // 🔥 初始化推荐增强状态
  useEffect(() => {
    const enhancerEnabled = RecommendationEnhancer.isEnabled();
    setIsRecommendationEnhanced(enhancerEnabled);
    console.log('🔍 推荐增强器状态:', enhancerEnabled);
  }, []);

  // 🔥 初始化会话管理
  useEffect(() => {
    if (isOpen && bookId) {
      console.log('📂 初始化会话管理，bookId:', bookId);
      const loadedSessions = sessionManager.loadSessions(bookId);
      setSessions(loadedSessions);

      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        setCurrentSessionId(currentSession.id);
        setMessages(currentSession.messages);
        console.log('✅ 加载当前会话:', currentSession.name, '消息数量:', currentSession.messages.length);
      }
    }
  }, [isOpen, bookId]);

  // 当切换到AI聊天功能时，确保有当前会话
  useEffect(() => {
    if (selectedFunction === 'aiChat' && currentSessionId) {
      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        setMessages(currentSession.messages);
      }
    }
  }, [selectedFunction, currentSessionId]);

  // 🔧 修复：组件关闭时清理状态
  useEffect(() => {
    return () => {
      // 组件卸载时清理状态
      if (!isOpen) {
        setMessages([]);
        setInputText('');
        setIsGenerating(false);
        setRobotEmotion('default');
        setCurrentSessionId(null);
        console.log('🧹 组件关闭，清理状态');
      }
    };
  }, [isOpen]);

  // 数据加载逻辑
  useEffect(() => {
    const loadAssociationData = async () => {
      if (!bookId) return;

      try {
        setIsLoadingData(true);
        setDataError(null);

        // 直接从数据库获取数据
        const [characters, worldBuildings, terminologies, chapters] = await Promise.all([
          db.characters.where('bookId').equals(bookId).toArray(),
          db.worldBuilding.where('bookId').equals(bookId).toArray(),
          db.terminology.where('bookId').equals(bookId).toArray(),
          db.chapters.where('bookId').equals(bookId).toArray()
        ]);

        setAssociationData({
          characters: characters.map(c => ({
            id: c.id!,
            name: c.name,
            description: c.description,
            traits: [c.personality, c.characterArchetype]
              .filter(Boolean)
              .map(trait => {
                // 安全处理trait，确保它是字符串
                if (typeof trait === 'string') {
                  return trait;
                } else if (typeof trait === 'object' && trait !== null) {
                  // 如果是对象，尝试提取name或title字段，否则转为JSON字符串
                  return (trait as any).name || (trait as any).title || JSON.stringify(trait);
                } else {
                  // 其他情况转为字符串
                  return String(trait);
                }
              })
              .filter(Boolean) as string[]
          })),
          worldSettings: worldBuildings.map(w => ({
            id: w.id!,
            name: w.name,
            description: w.description,
            category: w.category
          })),
          glossary: terminologies.map(t => ({
            id: t.id!,
            term: t.name,
            definition: t.description,
            category: t.category
          })),
          outline: chapters.map(c => ({
            id: c.id!,
            title: c.title,
            content: c.content,
            level: 1
          }))
        });
      } catch (error) {
        console.error('加载关联数据失败:', error);
        setDataError('加载数据失败，请重试');
        setAssociationData(null);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadAssociationData();
  }, [bookId]);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 🔥 会话管理处理函数
  const handleSessionSelect = (sessionId: string) => {
    console.log('🔄 切换会话:', sessionId);

    // 🔧 修复：切换前先清理状态，避免闪烁
    setIsGenerating(false);
    setRobotEmotion('default');

    const session = sessionManager.switchSession(sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
      console.log('✅ 会话切换完成:', session.name);
    }
  };

  const handleNewSession = () => {
    console.log('➕ 创建新会话');

    // 🔧 修复：创建新会话前先清理当前状态
    setMessages([]); // 立即清空消息，避免闪烁
    setInputText(''); // 清空输入框
    setIsGenerating(false); // 重置生成状态
    setRobotEmotion('default'); // 重置机器人表情

    const newSession = sessionManager.createSession(bookId);
    setSessions(sessionManager.getSessionsByBookId(bookId));
    setCurrentSessionId(newSession.id);
    setMessages(newSession.messages); // 设置新会话的消息（包含欢迎消息）

    console.log('✅ 新会话创建完成:', newSession.name);
  };

  const handleDeleteSession = (sessionId: string) => {
    console.log('🗑️ 删除会话:', sessionId);
    const success = sessionManager.deleteSession(sessionId);
    if (success) {
      setSessions(sessionManager.getSessionsByBookId(bookId));

      // 🔧 修复：删除会话后总是创建新会话，而不是切换到其他会话
      if (sessionId === currentSessionId) {
        // 如果删除的是当前会话，创建新会话
        console.log('🔄 删除的是当前会话，创建新会话');
        handleNewSession();
      } else {
        // 如果删除的不是当前会话，保持当前会话不变
        const currentSession = sessionManager.getCurrentSession();
        if (currentSession) {
          setCurrentSessionId(currentSession.id);
          setMessages(currentSession.messages);
        } else {
          // 如果当前会话也不存在了，创建新会话
          handleNewSession();
        }
      }
    }
  };

  const handleRenameSession = (sessionId: string, newName: string) => {
    console.log('📝 重命名会话:', sessionId, newName);
    sessionManager.updateSession(sessionId, { name: newName });
    setSessions(sessionManager.getSessionsByBookId(bookId));
  };

  const handleClearSession = (sessionId: string) => {
    console.log('🧹 清空会话:', sessionId);
    sessionManager.clearSession(sessionId);
    setSessions(sessionManager.getSessionsByBookId(bookId));
    if (sessionId === currentSessionId) {
      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        setMessages(currentSession.messages);
      }
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputText.trim() || isGenerating) return;

    const userMessage: ChatMessage = {
      id: generateUniqueMessageId(),
      role: 'user',
      content: inputText.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsGenerating(true);
    setRobotEmotion('thinking'); // 切换到思考状态

    // � 重置chunk去重状态
    setLastChunk('');
    setChunkHistory([]);

    // �🔥 保存用户消息到会话管理器
    if (currentSessionId) {
      sessionManager.addMessage(currentSessionId, userMessage);
    }

    try {
      console.log('🤖 开始AI聊天...');

      // 检查是否包含工具调用指令
      const hasToolCall = userMessage.content.includes('generate_book_titles') ||
                         userMessage.content.includes('analyze_framework') ||
                         userMessage.content.includes('generate_synopsis') ||
                         userMessage.content.includes('书名') ||
                         userMessage.content.includes('标题') ||
                         userMessage.content.includes('简介') ||
                         userMessage.content.includes('梗概') ||
                         userMessage.content.includes('内容简介');

      if (hasToolCall) {
        setToolCallStatus('executing');
        setToolCallMessage('正在执行工具调用...');
      }

      // 创建AI响应消息
      const aiResponseId = generateUniqueMessageId();
      const aiMessage: ChatMessage = {
        id: aiResponseId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        type: 'result',
        metadata: {
          relatedData: associationData ? {
            characters: associationData.characters.length,
            worldSettings: associationData.worldSettings.length,
            glossary: associationData.glossary.length
          } : null
        }
      };

      // 先添加空的AI消息
      setMessages(prev => [...prev, aiMessage]);
      setRobotEmotion('excited'); // 切换到兴奋状态

      // 🔥 保存AI消息到会话管理器
      if (currentSessionId) {
        sessionManager.addMessage(currentSessionId, aiMessage);
      }

      // 准备消息历史（排除当前用户消息和即将创建的AI消息）
      const messageHistory = messages
        .filter(msg => msg.id !== userMessage.id && msg.id !== aiResponseId)
        .map(msg => {
          if (msg.type === 'tool_call_result' && msg.toolCallResult) {
            // 将工具调用结果转换为文本内容
            return {
              role: msg.role,
              content: formatToolCallResultForHistory(msg.toolCallResult)
            };
          }
          return {
            role: msg.role,
            content: msg.content
          };
        });

      console.log('📜 准备发送消息历史:', {
        totalMessages: messages.length,
        historyMessages: messageHistory.length,
        userMessageId: userMessage.id,
        aiResponseId: aiResponseId
      });

      // 调用真实的AI服务（流式响应）
      const response = await brainstormAIService.sendChatMessage(
        userMessage.content,
        associationData || undefined,
        (chunk: string) => {
          // 🔧 防重复chunk处理
          console.log('📥 收到chunk:', {
            length: chunk.length,
            preview: chunk.substring(0, 30) + (chunk.length > 30 ? '...' : ''),
            timestamp: Date.now()
          });

          // 检测重复chunk
          if (chunk === lastChunk && chunk.length > 5) {
            console.warn('⚠️ 检测到重复chunk，跳过:', chunk.substring(0, 20) + '...');
            return;
          }

          // 检测是否在历史中出现过（防止更复杂的重复模式）
          if (chunk.length > 10 && chunkHistory.includes(chunk)) {
            console.warn('⚠️ 检测到历史重复chunk，跳过:', chunk.substring(0, 20) + '...');
            return;
          }

          // 更新chunk历史（只保留最近的10个chunk）
          setChunkHistory(prev => {
            const newHistory = [...prev, chunk];
            return newHistory.slice(-10);
          });
          setLastChunk(chunk);

          // 流式更新AI消息内容
          setMessages(prev => prev.map(msg => {
            if (msg.id === aiResponseId) {
              // 🔧 额外检查：确保不会重复添加相同内容
              if (msg.content.endsWith(chunk)) {
                console.warn('⚠️ 消息内容已包含此chunk，跳过重复添加');
                return msg;
              }

              const newContent = msg.content + chunk;

              // 检测世界观管理面板打开标记
              if (newContent.includes('<!-- WORLDBUILDING_PANEL_OPEN -->')) {
                console.log('🌍 检测到世界观管理面板打开标记');
                setIsWorldBuildingPanelOpen(true);
              }

              const updatedMsg = { ...msg, content: newContent };
              // 🔥 同步更新到会话管理器
              if (currentSessionId) {
                sessionManager.updateMessage(currentSessionId, aiResponseId, updatedMsg.content);
              }
              return updatedMsg;
            }
            return msg;
          }));
        },
        messageHistory // 传递消息历史
      );

      if (!response.success) {
        throw new Error(response.error || 'AI响应失败');
      }

      console.log('✅ AI聊天完成');

      // 如果有工具调用结果，添加工具调用结果消息
      if (response.toolCallResult) {
        console.log('📋 添加工具调用结果消息:', response.toolCallResult);

        setToolCallStatus('completed');
        setToolCallMessage('工具调用执行完成');

        const toolCallMessage: ChatMessage = {
          id: generateUniqueMessageId(),
          role: 'assistant',
          content: formatToolCallResultForHistory(response.toolCallResult), // 填充文本内容
          timestamp: new Date(),
          type: 'tool_call_result',
          toolCallResult: response.toolCallResult
        };

        setMessages(prev => {
          const newMessages = [...prev, toolCallMessage];
          console.log('✅ 工具调用结果消息已添加，当前消息总数:', newMessages.length);
          return newMessages;
        });

        // 3秒后清除工具调用状态
        setTimeout(() => {
          setToolCallStatus(null);
          setToolCallMessage('');
        }, 3000);
      } else {
        console.log('⚠️ 没有工具调用结果');
        if (hasToolCall) {
          setToolCallStatus(null);
          setToolCallMessage('');
        }
      }

      // 延迟切换回默认状态
      setTimeout(() => {
        setRobotEmotion('default');
      }, 1000);
    } catch (error) {
      console.error('❌ AI响应失败:', error);
      setRobotEmotion('confused'); // 切换到困惑状态

      // 如果是工具调用失败
      if (toolCallStatus === 'executing') {
        setToolCallStatus('error');
        setToolCallMessage('工具调用执行失败');

        setTimeout(() => {
          setToolCallStatus(null);
          setToolCallMessage('');
        }, 3000);
      }

      const errorMessage: ChatMessage = {
        id: generateUniqueMessageId(),
        role: 'assistant',
        content: '抱歉，生成创意时出现了问题，请稍后重试。',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages(prev => [...prev, errorMessage]);

      // 延迟切换回默认状态
      setTimeout(() => {
        setRobotEmotion('default');
      }, 2000);
    } finally {
      setIsGenerating(false);
    }
  };

  // 功能选择处理
  const handleFunctionSelect = (functionType: FunctionType) => {
    setSelectedFunction(functionType);
    setCurrentView('generate');
  };

  // 返回功能选择
  const handleBackToFunctionSelect = () => {
    setSelectedFunction(null);
    setCurrentView('select');
    setSelectedType(null);
    setMessages([]);
    setInputText('');
  };

  // 原有的脑洞生成处理函数
  const handleTypeSelect = (typeId: string) => {
    setSelectedType(typeId);
    setCurrentView('generate');
  };

  const handleBackToSelect = () => {
    setCurrentView('select');
    setSelectedType(null);
  };

  const handleGenerate = async (request: any): Promise<BrainstormResult[]> => {
    try {
      console.log('🎨 开始生成脑洞内容...');

      // 调用真实的AI服务
      const results = await brainstormAIService.generateBrainstorm({
        ...request,
        associationData
      });

      console.log('✅ 脑洞内容生成完成');
      return results;
    } catch (error) {
      console.error('❌ 脑洞生成失败:', error);

      // 返回默认内容作为降级方案
      return [
        {
          id: '1',
          type: request.type,
          content: `基于"${request.input}"生成的创意内容`,
          score: 4.5,
          metadata: {
            keywords: ['创意', '灵感', '构思'],
            createdAt: new Date(),
          },
        }
      ];
    }
  };

  const handleResultSelect = (result: BrainstormResult) => {
    onBrainstormComplete?.(result);
    // 可以选择是否关闭对话框
    // onClose();
  };

  const handleAssociationSelect = (dataType: string, items: any[]) => {
    setSelectedAssociations((prev: any) => ({
      ...prev,
      [dataType]: items
    }));
  };

  // 🔥 推荐增强开关处理函数
  const handleRecommendationEnhancerToggle = () => {
    const newState = !isRecommendationEnhanced;
    setIsRecommendationEnhanced(newState);
    RecommendationEnhancer.setEnabled(newState);

    console.log('🔄 推荐增强器状态切换:', newState);

    // 🔥 显示Toast通知而不是聊天消息
    showToastNotification(newState);
  };

  // 🔥 Toast通知状态
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'info'>('info');

  // 🔥 显示Toast通知
  const showToastNotification = (enabled: boolean) => {
    setToastMessage(enabled ? 'AI推荐增强已启用' : 'AI推荐增强已关闭');
    setToastType(enabled ? 'success' : 'info');
    setToastVisible(true);

    // 3秒后自动隐藏
    setTimeout(() => {
      setToastVisible(false);
    }, 3000);
  };

  // 处理世界观选择和发送
  const handleWorldBuildingSelect = async (selectedWorldBuildings: WorldBuilding[]) => {
    if (selectedWorldBuildings.length === 0) return;

    try {
      // 使用现有的PromptHelperService发送世界观
      const { PromptHelperService } = await import('@/factories/ai/services/PromptHelperService');
      const { MessageBuilder } = await import('@/utils/ai/MessageBuilder');

      const messageBuilder = MessageBuilder.create();
      const promptHelper = new PromptHelperService();

      promptHelper.addSelectedWorldBuildings(
        messageBuilder,
        selectedWorldBuildings,
        selectedWorldBuildings.map(wb => wb.id!)
      );

      // 添加确认消息到对话
      const confirmMessage: ChatMessage = {
        id: generateUniqueMessageId(),
        role: 'assistant',
        content: `🌍 **已接收${selectedWorldBuildings.length}个世界观设定**\n\n${selectedWorldBuildings.map(wb => `• **${wb.name}**：${wb.description || '暂无描述'}`).join('\n')}\n\n现在我可以基于这些世界观设定来回答您的问题或协助创作。`,
        timestamp: new Date(),
        type: 'result'
      };

      setMessages(prev => [...prev, confirmMessage]);

      // 保存到会话
      if (currentSessionId) {
        sessionManager.addMessage(currentSessionId, confirmMessage);
      }

      console.log('✅ 世界观发送完成:', selectedWorldBuildings.map(wb => wb.name));
    } catch (error) {
      console.error('❌ 世界观发送失败:', error);

      // 添加错误消息
      const errorMessage: ChatMessage = {
        id: generateUniqueMessageId(),
        role: 'assistant',
        content: '❌ **世界观发送失败**\n\n请稍后重试。',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      // 关闭世界观管理面板
      setIsWorldBuildingPanelOpen(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
          {/* 背景遮罩 */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* 对话框主体 - 优化尺寸多功能界面 */}
          <motion.div
            className="relative w-full max-w-[95vw] h-[95vh] max-h-[1200px] min-h-[900px] bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200"
            style={{
              maxWidth: window.innerWidth >= 2560 ? '1800px' :
                       window.innerWidth >= 1920 ? '1400px' :
                       window.innerWidth >= 1440 ? '1200px' : '95vw'
            }}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            {/* 头部 - 单行布局 + 浮动统计信息 */}
            <div className="relative flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-blue-600">
              <div className="flex items-center space-x-3">
                {selectedFunction && (
                  <button
                    onClick={handleBackToFunctionSelect}
                    className="p-1 hover:bg-white/10 rounded-lg transition-colors mr-2"
                  >
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                )}
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  {selectedFunction === 'aiChat' ? (
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  ) : selectedFunction === 'bookTitle' ? (
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  ) : selectedFunction === 'synopsis' ? (
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21l14-14" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 3l1.5 1.5L18 6l-1.5-1.5L18 3z" fill="currentColor" />
                      <circle cx="8" cy="16" r="0.5" fill="currentColor" opacity="0.8" />
                      <circle cx="11" cy="13" r="0.5" fill="currentColor" opacity="0.6" />
                      <circle cx="14" cy="10" r="0.5" fill="currentColor" opacity="0.4" />
                    </svg>
                  )}
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-white">
                    {selectedFunction === 'aiChat' ? 'AI创意助手'
                     : selectedFunction === 'bookTitle' ? '书名生成器'
                     : selectedFunction === 'synopsis' ? '简介生成器'
                     : 'AI脑洞生成器'}
                  </h2>
                  <p className="text-white/80 text-sm">
                    {selectedFunction === 'aiChat' ? '智能创意生成与脑洞拓展'
                     : selectedFunction === 'bookTitle' ? '基于项目数据生成吸引人的书名'
                     : selectedFunction === 'synopsis' ? '创建引人入胜的作品简介'
                     : '选择你需要的创意生成功能'}
                  </p>
                </div>
              </div>

              {/* 浮动统计信息 - 不影响布局 */}
              {associationData && selectedFunction && (
                <motion.div
                  className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-sm rounded-full px-3 py-1.5 shadow-lg border border-gray-200/50">
                    <StatCard icon="👥" label="角色" count={associationData.characters.length} />
                    <div className="w-px h-4 bg-gray-300"></div>
                    <StatCard icon="🌍" label="世界观" count={associationData.worldSettings.length} />
                    <div className="w-px h-4 bg-gray-300"></div>
                    <StatCard icon="📚" label="术语" count={associationData.glossary.length} />
                  </div>
                </motion.div>
              )}

              <button
                onClick={onClose}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* 内容区域 - 增加顶部间距避免被浮动胶囊覆盖 */}
            <div className="flex flex-col h-[calc(95vh-80px)] max-h-[1120px] pt-6">
              {isLoadingData ? (
                // 数据加载中
                <div className="flex items-center justify-center flex-1">
                  <div className="text-center">
                    <motion.div
                      className="w-12 h-12 border-4 border-purple-200 border-t-purple-600 rounded-full mx-auto mb-4"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    <p className="text-gray-600">正在加载项目数据...</p>
                  </div>
                </div>
              ) : dataError ? (
                // 数据加载错误
                <div className="flex items-center justify-center flex-1">
                  <div className="text-center">
                    <div className="text-4xl mb-4">⚠️</div>
                    <p className="text-gray-600 mb-4">{dataError}</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                    >
                      重新加载
                    </button>
                  </div>
                </div>
              ) : !selectedFunction ? (
                // 功能选择界面
                <div className="flex items-center justify-center flex-1 p-8">
                  <div className="max-w-2xl w-full">
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">选择创意生成功能</h3>
                      <p className="text-gray-600">基于你的项目数据，为你提供专业的创意生成服务</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* 书名生成 */}
                      <motion.button
                        onClick={() => handleFunctionSelect('bookTitle')}
                        className="p-6 bg-white border-2 border-gray-200 rounded-xl hover:border-purple-400 hover:shadow-lg transition-all duration-300 text-left group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors">
                          <svg className="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">书名生成</h4>
                        <p className="text-gray-600 text-sm">基于项目内容生成吸引人的书名，支持多种风格和类型</p>
                      </motion.button>

                      {/* 简介生成 */}
                      <motion.button
                        onClick={() => handleFunctionSelect('synopsis')}
                        className="p-6 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-400 hover:shadow-lg transition-all duration-300 text-left group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                          <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">简介生成</h4>
                        <p className="text-gray-600 text-sm">创建引人入胜的作品简介，突出核心卖点和吸引力</p>
                      </motion.button>

                      {/* AI创意聊天 */}
                      <motion.button
                        onClick={() => handleFunctionSelect('aiChat')}
                        className="p-6 bg-white border-2 border-gray-200 rounded-xl hover:border-emerald-400 hover:shadow-lg transition-all duration-300 text-left group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-200 transition-colors">
                          <svg className="w-6 h-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">AI创意助手</h4>
                        <p className="text-gray-600 text-sm">智能对话生成题材、剧情、人物、世界观等创意内容</p>
                      </motion.button>
                    </div>
                  </div>
                </div>
              ) : selectedFunction === 'aiChat' ? (
                <div className="flex h-full">
                  {/* 左侧会话列表 */}
                  <SessionSidebar
                    sessions={sessions}
                    currentSessionId={currentSessionId}
                    onSessionSelect={handleSessionSelect}
                    onNewSession={handleNewSession}
                    onDeleteSession={handleDeleteSession}
                    onRenameSession={handleRenameSession}
                    onClearSession={handleClearSession}
                    isCollapsed={isSessionSidebarCollapsed}
                    onToggleCollapse={() => setIsSessionSidebarCollapsed(!isSessionSidebarCollapsed)}
                  />

                  {/* 右侧聊天区域 */}
                  <div className="flex-1 flex flex-col">
                    {/* 消息列表 */}
                    <div className="flex-1 overflow-y-auto p-6 space-y-6 min-h-0">
                    {messages.map((message) => (
                      <motion.div
                        key={message.id}
                        className={`flex items-start gap-3 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
                        initial={{
                          opacity: 0,
                          y: 20,
                          scale: message.type === 'welcome' ? 0.95 : 1
                        }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          scale: 1
                        }}
                        transition={{
                          duration: message.type === 'welcome' ? 0.5 : 0.3,
                          type: message.type === 'welcome' ? "spring" : "tween",
                          stiffness: message.type === 'welcome' ? 300 : undefined,
                          damping: message.type === 'welcome' ? 20 : undefined
                        }}
                      >
                        {/* 头像/图标 */}
                        {message.role === 'user' ? (
                          // 用户头像
                          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 text-white flex items-center justify-center">
                            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        ) : (
                          // AI机器人头像
                          <RobotAvatar
                            emotion={robotEmotion}
                            isAnimating={isGenerating}
                          />
                        )}

                        {/* 消息气泡 */}
                        {message.type === 'tool_call_result' && message.toolCallResult ? (
                          // 工具调用结果气泡
                          <div>
                            <ToolCallStatusIndicator
                              status="completed"
                              message="工具调用执行完成"
                              className="mb-3"
                            />
                            <ToolCallResultBubble
                              result={message.toolCallResult}
                              onAction={async (action, data) => {
                                console.log('工具调用操作:', action, data);
                                // 处理工具调用结果的操作
                                if (action === 'copy') {
                                  // 复制操作已在组件内部处理
                                } else if (action === 'favorite') {
                                  // TODO: 实现收藏功能
                                  console.log('收藏书名:', data);
                                } else if (action === 'apply') {
                                  // TODO: 实现应用功能
                                  console.log('应用书名:', data);
                                } else if (action === 'save' && data) {
                                  // 处理世界观保存
                                  try {
                                    console.log('🌍 开始保存世界观到数据库:', data);

                                    // 转换为WorldBuilding格式
                                    const worldBuilding = {
                                      id: generateUniqueMessageId(), // 🔥 修复：使用项目中已有的ID生成方法
                                      name: data.name || '未命名世界观',
                                      category: 'worldview', // 使用worldview作为类别
                                      description: data.description || '',
                                      bookId: bookId,
                                      attributes: {
                                        type: data.type || '',
                                        complexity: data.complexity || '',
                                        score: String(data.score || 0),
                                        elements: JSON.stringify(data.elements || {}),
                                        tags: JSON.stringify(data.tags || []),
                                        summary: data.summary || ''
                                      },
                                      createdAt: new Date(),
                                      updatedAt: new Date(),
                                      extractedFromChapterIds: [], // AI创建的世界观不来自章节
                                      relatedCharacterIds: [], // 暂时没有关联人物
                                      relatedTerminologyIds: [], // 暂时没有关联术语
                                      relatedWorldBuildingIds: [], // 暂时没有关联其他世界观
                                      notes: `由AI创意助手生成的世界观设定。类型：${data.type}，复杂度：${data.complexity}`
                                    };

                                    // 保存到数据库
                                    const savedId = await db.worldBuilding.add(worldBuilding);
                                    console.log('✅ 世界观保存成功，ID:', savedId);

                                    // 添加成功消息到对话
                                    const successMessage: ChatMessage = {
                                      id: generateUniqueMessageId(),
                                      role: 'assistant',
                                      content: `🌍 **世界观保存成功**\n\n"${worldBuilding.name}" 已成功保存到世界观管理系统中。\n\n您可以在世界观管理面板中查看和编辑这个世界观设定。`,
                                      timestamp: new Date(),
                                      type: 'result'
                                    };

                                    setMessages(prev => [...prev, successMessage]);

                                    // 保存到会话
                                    if (currentSessionId) {
                                      sessionManager.addMessage(currentSessionId, successMessage);
                                    }

                                  } catch (error) {
                                    console.error('❌ 世界观保存失败:', error);

                                    // 添加错误消息
                                    const errorMessage: ChatMessage = {
                                      id: generateUniqueMessageId(),
                                      role: 'assistant',
                                      content: `❌ **世界观保存失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请稍后重试。`,
                                      timestamp: new Date(),
                                      type: 'text'
                                    };

                                    setMessages(prev => [...prev, errorMessage]);
                                  }
                                }
                              }}
                            />
                          </div>
                        ) : (
                          // 普通消息气泡
                          <div className={`max-w-[80%] ${
                            message.role === 'user'
                              ? 'bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg'
                              : message.type === 'welcome'
                                ? 'bg-gradient-to-br from-emerald-50 to-teal-50 border-2 border-emerald-200 text-gray-800 shadow-md hover:shadow-lg transition-all duration-300'
                                : 'bg-white border border-gray-200 text-gray-800 shadow-md hover:shadow-lg transition-shadow duration-200'
                          } rounded-2xl px-5 py-4 relative`}>
                            {/* 气泡尖角 */}
                            <div className={`absolute top-5 w-3 h-3 transform rotate-45 ${
                              message.role === 'user'
                                ? 'bg-purple-500 -right-1.5'
                                : message.type === 'welcome'
                                  ? 'bg-emerald-50 border-l border-b border-emerald-200 -left-1.5'
                                  : 'bg-white border-l border-b border-gray-200 -left-1.5'
                            }`} />

                            {/* 消息内容 */}
                            <div className={`text-sm ${message.role === 'user' ? 'text-white' : 'text-gray-800'}`}>
                              {message.role === 'user' ? (
                                <div className="whitespace-pre-wrap leading-relaxed">
                                  {message.content}
                                </div>
                              ) : (
                                <MarkdownRenderer content={message.content} />
                              )}
                            </div>

                            {/* 消息元数据 */}
                            <div className={`text-xs mt-2 flex items-center gap-2 ${
                              message.role === 'user' ? 'text-purple-200' : 'text-gray-500'
                            }`}>
                              <span>{message.timestamp instanceof Date ? message.timestamp.toLocaleTimeString() : new Date(message.timestamp).toLocaleTimeString()}</span>
                              {message.metadata?.relatedData && (
                                <>
                                  <span>•</span>
                                  <span className="flex items-center gap-1">
                                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                    基于 {message.metadata.relatedData.characters + message.metadata.relatedData.worldSettings + message.metadata.relatedData.glossary} 项数据
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}

                    {/* AI正在输入指示器 */}
                    {isGenerating && (
                      <motion.div
                        className="flex items-start gap-3"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        {/* AI机器人头像 - 思考状态 */}
                        <RobotAvatar
                          emotion="loading"
                          isAnimating={true}
                        />

                        {/* 思考气泡 */}
                        <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-2xl px-5 py-4 shadow-md relative max-w-[80%]">
                          {/* 气泡尖角 */}
                          <div className="absolute top-5 w-3 h-3 transform rotate-45 bg-gray-50 border-l border-b border-gray-200 -left-1.5" />

                          <div className="flex items-center space-x-3">
                            <div className="flex space-x-1">
                              <motion.div
                                className="w-2.5 h-2.5 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full"
                                animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                                transition={{ duration: 1.2, repeat: Infinity, delay: 0 }}
                              />
                              <motion.div
                                className="w-2.5 h-2.5 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full"
                                animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                                transition={{ duration: 1.2, repeat: Infinity, delay: 0.3 }}
                              />
                              <motion.div
                                className="w-2.5 h-2.5 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full"
                                animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                                transition={{ duration: 1.2, repeat: Infinity, delay: 0.6 }}
                              />
                            </div>
                            <span className="text-gray-600 text-sm font-medium">AI正在创意构思中...</span>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* 滚动锚点 */}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* 工具调用状态指示器 */}
                  {toolCallStatus && (
                    <div className="border-t border-gray-200 px-4 py-3 bg-gray-50">
                      <ToolCallStatusIndicator
                        status={toolCallStatus}
                        message={toolCallMessage}
                      />
                    </div>
                  )}

                  {/* 🔥 推荐增强控制区域 */}
                  <div className="border-t border-gray-200 px-4 py-3 bg-gradient-to-r from-emerald-50 to-teal-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <svg
                            className={`w-5 h-5 transition-colors ${isRecommendationEnhanced ? 'text-emerald-600' : 'text-gray-400'}`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                          <span className="text-sm font-medium text-gray-700">AI推荐增强</span>
                        </div>
                        <motion.button
                          onClick={handleRecommendationEnhancerToggle}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 ${
                            isRecommendationEnhanced ? 'bg-emerald-600' : 'bg-gray-200'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <motion.span
                            className="inline-block h-4 w-4 rounded-full bg-white shadow-lg"
                            style={{
                              transform: isRecommendationEnhanced ? 'translateX(20px)' : 'translateX(4px)'
                            }}
                            animate={{
                              x: isRecommendationEnhanced ? 20 : 4
                            }}
                            transition={{ type: "spring", stiffness: 700, damping: 30 }}
                          />
                        </motion.button>
                      </div>

                      <div className="text-xs text-gray-500">
                        {isRecommendationEnhanced ? (
                          <span className="flex items-center space-x-1">
                            <svg className="w-3 h-3 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span>AI可访问完整创作资源库</span>
                          </span>
                        ) : (
                          <span className="flex items-center space-x-1">
                            <svg className="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                            <span>基础模式，关键词触发推荐</span>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 输入区域 */}
                  <div className="border-t border-gray-200 p-4">
                    <div className="flex items-end space-x-3">
                      <div className="flex-1">
                        <textarea
                          value={inputText}
                          onChange={(e) => setInputText(e.target.value)}
                          onKeyDown={handleKeyDown}
                          placeholder="描述你想要的创意内容，比如：帮我想一个玄幻小说的题材..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none max-h-32"
                          rows={1}
                          disabled={isGenerating}
                        />
                      </div>
                      <motion.button
                        onClick={handleSendMessage}
                        disabled={!inputText.trim() || isGenerating}
                        className="px-6 py-3 bg-purple-600 text-white rounded-2xl hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {isGenerating ? (
                          <motion.div
                            className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                        ) : (
                          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                        )}
                      </motion.button>
                    </div>
                  </div>
                  </div>
                </div>
              ) : selectedFunction === 'bookTitle' ? (
                // 书名生成器
                <div className="w-full h-full">
                  <BookTitleGenerator
                    associationData={associationData || undefined}
                    selectedAssociations={selectedAssociations}
                    onGenerate={handleGenerate}
                    onResultSelect={handleResultSelect}
                  />
                </div>
              ) : selectedFunction === 'synopsis' ? (
                // 简介生成器
                <div className="w-full h-full">
                  <SynopsisGenerator
                    onClose={() => {
                      // 可以选择是否关闭对话框
                      // onClose();
                    }}
                  />
                </div>
              ) : null}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>

    {/* 🔥 Toast通知组件 - 使用SVG重绘动画 */}
    <AnimatePresence>
      {toastVisible && (
        <motion.div
          className="fixed top-4 right-4 z-50 flex items-center space-x-3 bg-white border border-gray-200 rounded-lg shadow-lg px-4 py-3 max-w-sm"
          initial={{ opacity: 0, y: -50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -50, scale: 0.9 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        >
          {/* SVG动画图标 */}
          <div className="flex-shrink-0">
            {toastType === 'success' ? (
              // 成功状态 - 绿色勾选动画
              <motion.svg
                className="w-6 h-6 text-emerald-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 600, damping: 25 }}
              >
                <motion.circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                />
                <motion.path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ delay: 0.3, duration: 0.4, ease: "easeInOut" }}
                />
              </motion.svg>
            ) : (
              // 信息状态 - 蓝色信息图标动画
              <motion.svg
                className="w-6 h-6 text-blue-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 600, damping: 25 }}
              >
                <motion.circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                />
                <motion.path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 16v-4M12 8h.01"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ delay: 0.3, duration: 0.4, ease: "easeInOut" }}
                />
              </motion.svg>
            )}
          </div>

          {/* 消息文本 */}
          <div className="flex-1">
            <motion.p
              className="text-sm font-medium text-gray-900"
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              {toastMessage}
            </motion.p>
            <motion.p
              className="text-xs text-gray-500 mt-1"
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              {toastType === 'success'
                ? 'AI现在可以访问完整的创作资源库'
                : '已切换到基础推荐模式'
              }
            </motion.p>
          </div>

          {/* 关闭按钮 */}
          <motion.button
            onClick={() => setToastVisible(false)}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.2 }}
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </motion.button>

          {/* 进度条 */}
          <motion.div
            className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-b-lg"
            initial={{ width: "100%" }}
            animate={{ width: "0%" }}
            transition={{ duration: 3, ease: "linear" }}
          />
        </motion.div>
      )}
    </AnimatePresence>

    {/* 世界观管理面板 */}
    {isWorldBuildingPanelOpen && (() => {
      const panelComponent = new WorldBuildingPanelComponent();
      panelComponent.setBookId(bookId);
      panelComponent.setIsOpen(isWorldBuildingPanelOpen);
      panelComponent.onClose(() => setIsWorldBuildingPanelOpen(false));
      panelComponent.onCreate((worldBuilding) => {
        console.log('世界观创建:', worldBuilding);
      });
      panelComponent.onUpdate((worldBuilding) => {
        console.log('世界观更新:', worldBuilding);
      });
      panelComponent.onDelete((worldBuildingId) => {
        console.log('世界观删除:', worldBuildingId);
      });
      return panelComponent.render();
    })()}
    </>
  );
};

export default BrainstormDialog;
