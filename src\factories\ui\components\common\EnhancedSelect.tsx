"use client";

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import './EnhancedSelect.css';

// 选项接口
export interface SelectOption {
  value: string;
  label: string;
  preview?: ReactNode;
  disabled?: boolean;
  description?: string;
}

// 增强型下拉框属性
export interface EnhancedSelectProps {
  options: SelectOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  showOptionIcon?: boolean;
  maxHeight?: number;
  onBlur?: () => void;
  onFocus?: () => void;
}

// 增强型下拉框组件
const EnhancedSelect: React.FC<EnhancedSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  className = '',
  disabled = false,
  error,
  label,
  required = false,
  searchable = false,
  clearable = false,
  showOptionIcon = true,
  maxHeight = 250,
  onBlur,
  onFocus,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 获取当前选中选项
  const selectedOption = options.find(option => option.value === value);
  
  // 过滤选项
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;
  
  // 处理选择
  const handleSelect = (optionValue: string) => {
    if (disabled) return;
    
    const option = options.find(opt => opt.value === optionValue);
    if (option && !option.disabled) {
      onChange(optionValue);
      setIsOpen(false);
      setSearchTerm('');
    }
  };
  
  // 处理清除
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled) return;
    
    onChange('');
    setSearchTerm('');
  };
  
  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : prev
          );
        }
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : prev
        );
        break;
        
      case 'Enter':
        e.preventDefault();
        if (isOpen && highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex].value);
        } else if (!isOpen) {
          setIsOpen(true);
        }
        break;
        
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchTerm('');
        break;
        
      case 'Tab':
        if (isOpen) {
          setIsOpen(false);
        }
        break;
    }
  };
  
  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        if (onBlur) onBlur();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onBlur]);
  
  // 打开下拉框时聚焦搜索框
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);
  
  // 调整下拉框位置
  useEffect(() => {
    if (isOpen && dropdownRef.current && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      
      // 检查下方空间是否足够
      const spaceBelow = viewportHeight - containerRect.bottom;
      const spaceAbove = containerRect.top;
      const dropdownHeight = dropdownRect.height;
      
      // 如果下方空间不足，且上方空间足够，则向上展开
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        dropdownRef.current.style.bottom = '100%';
        dropdownRef.current.style.top = 'auto';
        dropdownRef.current.style.maxHeight = `${Math.min(maxHeight, spaceAbove - 10)}px`;
      } else {
        dropdownRef.current.style.top = '100%';
        dropdownRef.current.style.bottom = 'auto';
        dropdownRef.current.style.maxHeight = `${Math.min(maxHeight, spaceBelow - 10)}px`;
      }
    }
  }, [isOpen, maxHeight, filteredOptions]);
  
  // 渲染选项
  const renderOption = (option: SelectOption, index: number) => {
    const isSelected = option.value === value;
    const isHighlighted = index === highlightedIndex;
    const isDisabled = option.disabled;
    
    return (
      <div
        key={option.value}
        className={`enhanced-select-option ${isSelected ? 'is-selected' : ''} ${isHighlighted ? 'is-highlighted' : ''} ${isDisabled ? 'is-disabled' : ''}`}
        onClick={() => !isDisabled && handleSelect(option.value)}
        onMouseEnter={() => !isDisabled && setHighlightedIndex(index)}
        role="option"
        aria-selected={isSelected}
        aria-disabled={isDisabled}
      >
        <div className="enhanced-select-option-content">
          {showOptionIcon && (
            <div className="enhanced-select-option-icon">
              {isSelected && (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              )}
            </div>
          )}
          
          <div className="enhanced-select-option-text">
            <div className="enhanced-select-option-label">{option.label}</div>
            {option.description && (
              <div className="enhanced-select-option-description">{option.description}</div>
            )}
          </div>
          
          {option.preview && (
            <div className="enhanced-select-option-preview">
              {option.preview}
            </div>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className={`enhanced-select-container ${className}`}>
      {label && (
        <label className="enhanced-select-label">
          {label}
          {required && <span className="enhanced-select-required">*</span>}
        </label>
      )}
      
      <div 
        className={`enhanced-select ${isOpen ? 'is-open' : ''} ${disabled ? 'is-disabled' : ''} ${error ? 'has-error' : ''}`}
        ref={containerRef}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
        onClick={() => {
          if (!disabled) {
            setIsOpen(!isOpen);
            if (onFocus && !isOpen) onFocus();
          }
        }}
      >
        <div className="enhanced-select-trigger">
          {searchable && isOpen ? (
            <input
              ref={searchInputRef}
              className="enhanced-select-search"
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              placeholder={placeholder}
              disabled={disabled}
            />
          ) : (
            <span className={`enhanced-select-value ${!selectedOption ? 'is-placeholder' : ''}`}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          )}
          
          <div className="enhanced-select-actions">
            {clearable && value && !disabled && (
              <button
                type="button"
                className="enhanced-select-clear"
                onClick={handleClear}
                aria-label="Clear selection"
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            )}
            
            <span className="enhanced-select-arrow">
              <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L5 5L9 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
          </div>
        </div>
        
        {isOpen && (
          <div 
            className="enhanced-select-dropdown"
            ref={dropdownRef}
            role="listbox"
            aria-multiselectable="false"
          >
            {filteredOptions.length > 0 ? (
              filteredOptions.map(renderOption)
            ) : (
              <div className="enhanced-select-no-options">
                {searchTerm ? '没有匹配的选项' : '没有可用选项'}
              </div>
            )}
          </div>
        )}
      </div>
      
      {error && <div className="enhanced-select-error">{error}</div>}
    </div>
  );
};

export default EnhancedSelect;
