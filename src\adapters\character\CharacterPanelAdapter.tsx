"use client";

import React, { useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import { characterRepository } from '@/lib/db/repositories';
import { CharacterPanelComponent } from '@/factories/ui/components/CharacterPanel';

interface CharacterPanelAdapterProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

/**
 * 人物面板适配器组件
 * 用于将人物面板工厂组件集成到React应用中
 */
export const CharacterPanelAdapter: React.FC<CharacterPanelAdapterProps> = ({
  bookId,
  isOpen,
  onClose,
  className
}) => {
  // 创建人物面板组件实例
  const characterPanelComponent = new CharacterPanelComponent();

  // 设置属性
  characterPanelComponent.setBookId(bookId);
  characterPanelComponent.setIsOpen(isOpen);
  characterPanelComponent.onClose(onClose);
  if (className) characterPanelComponent.setClassName(className);

  // 设置回调函数
  characterPanelComponent.onCreate(async (character: Character) => {
    try {
      console.log('CharacterPanelAdapter - 创建人物:', character);

      // 确保所有必要字段都存在
      const characterToCreate = {
        ...character,
        bookId: character.bookId || bookId,
        createdAt: character.createdAt || new Date(),
        updatedAt: character.updatedAt || new Date(),
        extractedFromChapterIds: character.extractedFromChapterIds || [],
        relatedCharacterIds: character.relatedCharacterIds || [],
        relatedTerminologyIds: character.relatedTerminologyIds || [],
        relatedWorldBuildingIds: character.relatedWorldBuildingIds || []
      };

      // 创建人物
      const id = await characterRepository.create(characterToCreate);
      console.log('人物创建成功:', character.name, '新ID:', id);

      // 不再刷新页面，依赖组件内部状态更新
    } catch (error) {
      console.error('创建人物失败:', error);
      alert('创建人物失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  characterPanelComponent.onUpdate(async (character: Character) => {
    try {
      console.log('CharacterPanelAdapter - 更新人物:', character);

      // 更新人物
      if (character.id) {
        // 确保更新时间
        const characterToUpdate = {
          ...character,
          updatedAt: new Date()
        };

        await characterRepository.update(character.id, characterToUpdate);
        console.log('人物更新成功:', character.name);

        // 不再刷新页面，依赖组件内部状态更新
      } else {
        console.error('更新人物失败: 缺少ID');
        alert('更新人物失败: 缺少ID');
      }
    } catch (error) {
      console.error('更新人物失败:', error);
      alert('更新人物失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  characterPanelComponent.onDelete(async (characterId: string) => {
    try {
      // 删除人物
      await characterRepository.delete(characterId);
      console.log('人物删除成功:', characterId);
    } catch (error) {
      console.error('删除人物失败:', error);
    }
  });

  return <>{characterPanelComponent.render()}</>;
};
