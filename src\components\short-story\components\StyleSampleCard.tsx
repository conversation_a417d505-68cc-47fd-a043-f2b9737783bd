"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { StyleSample } from '../../../services/ai/StyleSampleService';

interface StyleSampleCardProps {
  sample: StyleSample;
  onEdit: (sample: StyleSample) => void;
  onDelete: (sample: StyleSample) => void;
  onToggleActive: (sample: StyleSample) => void;
  index: number;
}

/**
 * 风格样本卡片组件
 * 显示单个风格样本的信息和操作按钮
 */
const StyleSampleCard: React.FC<StyleSampleCardProps> = ({
  sample,
  onEdit,
  onDelete,
  onToggleActive,
  index
}) => {
  // 计算段落数量
  const segmentCount = Math.ceil(sample.content.length / 500);
  
  // 格式化日期
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    });
  };

  // 处理删除确认
  const handleDelete = () => {
    if (window.confirm(`确定要删除风格样本"${sample.name}"吗？此操作不可撤销。`)) {
      onDelete(sample);
    }
  };

  return (
    <motion.div
      className={`p-4 rounded-lg border transition-all duration-200 relative ${
        sample.isActive 
          ? 'border-green-200 bg-green-50 dark:border-green-600 dark:bg-green-900/20' 
          : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gray-50 dark:hover:bg-gray-750'
      }`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
    >
      {/* 卡片头部 */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-medium text-gray-800 dark:text-gray-100 text-lg">
              {sample.name}
            </h3>
            {sample.isActive && (
              <span className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 px-2 py-1 rounded-full text-xs">
                激活中
              </span>
            )}
          </div>
          
          {sample.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {sample.description}
            </p>
          )}
          
          {/* 统计信息 */}
          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-2">
            <span>{sample.content.length} 字</span>
            <span>{segmentCount} 段</span>
            <span>{formatDate(sample.createdAt)}</span>
          </div>
          
          {/* 标签 */}
          {sample.tags && sample.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {sample.tags.map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
        
        {/* 操作按钮 */}
        <div className="flex items-center space-x-2 ml-4">
          {/* 激活/停用按钮 */}
          <button
            onClick={() => onToggleActive(sample)}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              sample.isActive
                ? 'bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:hover:bg-orange-900/50'
                : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50'
            }`}
            title={sample.isActive ? '停用样本' : '激活样本'}
          >
            {sample.isActive ? '停用' : '激活'}
          </button>
          
          {/* 编辑按钮 */}
          <button
            onClick={() => onEdit(sample)}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            title="编辑样本"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
          
          {/* 删除按钮 */}
          <button
            onClick={handleDelete}
            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
            title="删除样本"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* 内容预览 */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <div className="font-medium mb-1">内容预览：</div>
          <div className="text-xs leading-relaxed">
            {sample.content.length > 150 
              ? `${sample.content.substring(0, 150)}...` 
              : sample.content
            }
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default StyleSampleCard;
