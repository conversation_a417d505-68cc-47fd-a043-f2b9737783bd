"use client";

import React, { useState } from 'react';
import { AnimatedButton } from '@/components/animations/PremiumAnimations';
import { Book } from '@/db';

interface EnhancedDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  book: Book | null;
  isDeleting?: boolean;
}

export const EnhancedDeleteDialog: React.FC<EnhancedDeleteDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  book,
  isDeleting = false
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!isOpen || !book) return null;

  const handleConfirm = () => {
    if (!isDeleting) {
      onConfirm();
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setShowDetails(false);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        onClick={handleClose}
      />

      {/* 对话框 */}
      <div
        className="relative premium-card max-w-md w-full mx-4 p-0 overflow-hidden"
        style={{
          animation: 'modalEnter 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)'
        }}
      >
        {/* 危险警告条 */}
        <div className="h-1 bg-gradient-to-r from-red-500 to-red-600" />

        <div className="p-6">
          {/* 警告图标和标题 */}
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">确认删除作品</h3>
              <p className="text-sm text-gray-600">此操作无法撤销</p>
            </div>
          </div>

          {/* 书籍信息 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{book.title}</h4>
                <p className="text-sm text-gray-600 line-clamp-2">{book.description}</p>
                <p className="text-xs text-gray-500 mt-1">
                  最后编辑: {new Date(book.updatedAt).toLocaleDateString('zh-CN')}
                </p>
              </div>
            </div>

            {/* 详细信息切换 */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="mt-3 text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
            >
              {showDetails ? '隐藏' : '显示'}详细信息
              <svg
                className={`w-4 h-4 transition-transform ${showDetails ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* 详细信息 */}
            {showDetails && (
              <div className="mt-3 pt-3 border-t border-gray-200 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">创建时间:</span>
                  <span className="text-gray-900">{new Date(book.createdAt).toLocaleString('zh-CN')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">更新时间:</span>
                  <span className="text-gray-900">{new Date(book.updatedAt).toLocaleString('zh-CN')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">作品ID:</span>
                  <span className="text-gray-900 font-mono text-xs">{book.id}</span>
                </div>
              </div>
            )}
          </div>

          {/* 删除警告 */}
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>警告：</strong>删除后无法恢复，请确认您真的要删除《{book.title}》。
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <AnimatedButton
              variant="secondary"
              size="md"
              onClick={handleClose}
              disabled={isDeleting}
              className="flex-1"
            >
              取消
            </AnimatedButton>
            <AnimatedButton
              variant="danger"
              size="md"
              onClick={handleConfirm}
              disabled={isDeleting}
              loading={isDeleting}
              className="flex-1"
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </AnimatedButton>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes modalEnter {
          from {
            opacity: 0;
            transform: scale(0.9) translateY(20px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
    </div>
  );
};
