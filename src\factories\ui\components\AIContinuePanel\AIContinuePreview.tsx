"use client";

import React, { useState, useEffect, useRef } from 'react';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import '@/styles/enhanced-button.css';

// 简单的类名合并工具函数
const cn = (...classes: (string | object | undefined | null | false)[]): string => {
  return classes
    .filter(Boolean)
    .map(cls => {
      if (typeof cls === 'string') return cls;
      if (typeof cls === 'object' && cls !== null) {
        return Object.entries(cls)
          .filter(([, value]) => Boolean(value))
          .map(([key]) => key)
          .join(' ');
      }
      return '';
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

interface Message {
  role: string;
  content: string;
  timestamp?: number; // 可选的时间戳，用于排序
  status?: 'generating' | 'completed' | 'error'; // 续写状态
  id?: string; // 续写记录ID
  isActive?: boolean; // 是否为当前活跃的续写
}

interface AIContinuePreviewProps {
  streamResponse: string;
  isLoading: boolean;
  generatedContent: string;
  bookId: string;
  onApply: () => void;
  onRestart: () => void; // 重新生成当前内容
  onStartNew?: () => void; // 开始新的续写过程
  onInsertBubble?: (content: string) => void; // 插入单个气泡的回调
  onContinue?: (userInput: string) => void; // 继续对话的回调，接收用户输入
  onDeleteContinue?: (continueId: string) => void; // 删除续写记录的回调
  onClearAll?: () => void; // 清空所有续写记录的回调
  onRegenerateContinue?: (continueId: string) => void; // 重新生成指定续写记录的回调
  messages?: Message[]; // 对话消息列表
  // 新增：上下文信息，用于智能提示
  hasBeforeContext?: boolean; // 是否有前文
  hasAfterContext?: boolean; // 是否有后文
  beforeContextLength?: number; // 前文字数
  afterContextLength?: number; // 后文字数
}

/**
 * AI续写预览组件
 * 用于显示AI生成的内容
 */
const AIContinuePreview: React.FC<AIContinuePreviewProps> = ({
  streamResponse,
  isLoading,
  generatedContent,
  bookId,
  onApply,
  onRestart,
  onStartNew,
  onInsertBubble,
  onContinue,
  onDeleteContinue,
  onClearAll,
  onRegenerateContinue,
  messages = [], // 默认为空数组
  hasBeforeContext = false,
  hasAfterContext = false,
  beforeContextLength = 0,
  afterContextLength = 0
}) => {
  const previewRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [showInputDialog, setShowInputDialog] = useState(false);
  const [userInput, setUserInput] = useState('');

  // 确认对话框状态
  const [showClearAllConfirm, setShowClearAllConfirm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showStartNewConfirm, setShowStartNewConfirm] = useState(false);
  const [deleteTargetId, setDeleteTargetId] = useState<string | null>(null);

  // 按钮状态管理
  const [applySuccess, setApplySuccess] = useState(false);

  // 获取要显示的消息
  const getMessagesToDisplay = () => {
    // 如果有传入的消息列表，优先使用
    if (messages && messages.length > 0) {
      return messages;
    }

    // 否则，使用streamResponse生成一条消息
    if (!streamResponse) return [];

    return [
      {
        role: 'assistant',
        content: streamResponse
      }
    ];
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (previewRef.current) {
      previewRef.current.scrollTop = previewRef.current.scrollHeight;
    }
  };

  // 检查是否需要显示滚动到底部按钮
  const checkScroll = () => {
    if (previewRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = previewRef.current;
      const isScrolledToBottom = scrollTop + clientHeight >= scrollHeight - 50;
      setShowScrollToBottom(!isScrolledToBottom);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const previewElement = previewRef.current;
    if (previewElement) {
      previewElement.addEventListener('scroll', checkScroll);
      return () => previewElement.removeEventListener('scroll', checkScroll);
    }
  }, []);

  // 当内容更新时，自动滚动到底部
  useEffect(() => {
    scrollToBottom();
    checkScroll();
  }, [streamResponse, messages]);

  // 处理插入气泡内容
  const handleInsertBubble = (content: string) => {
    if (onInsertBubble) {
      onInsertBubble(content);
    }
  };

  // 获取当前续写模式
  const getContinueMode = () => {
    if (hasBeforeContext && hasAfterContext) {
      return {
        mode: 'bridge',
        label: '衔接模式',
        description: `连接前文(${beforeContextLength}字)和后文(${afterContextLength}字)`,
        color: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
          </svg>
        )
      };
    } else if (hasBeforeContext && !hasAfterContext) {
      return {
        mode: 'continue',
        label: '续写模式',
        description: `基于前文(${beforeContextLength}字)继续创作`,
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
          </svg>
        )
      };
    } else if (!hasBeforeContext && hasAfterContext) {
      return {
        mode: 'prelude',
        label: '前置模式',
        description: `为后文(${afterContextLength}字)创作前置内容`,
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 17l-5-5m0 0l5-5m-5 5h12" />
          </svg>
        )
      };
    } else {
      return {
        mode: 'free',
        label: '自由创作',
        description: '无特定上下文约束',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
        )
      };
    }
  };

  const currentMode = getContinueMode();

  return (
    <div className="w-full h-full flex flex-col bg-white rounded-lg shadow-sm overflow-hidden">
      {/* 预览标题 */}
      <div className="bg-indigo-50 p-3 border-b border-indigo-100">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-indigo-800">AI续写预览</h3>
          <div className="flex space-x-2">
            {/* 应用最新按钮 */}
            {generatedContent && (
              <EnhancedButton
                variant="primary"
                size="small"
                onClick={async () => {
                  if (onApply) {
                    await onApply();
                    // 显示成功状态
                    setApplySuccess(true);
                    setTimeout(() => setApplySuccess(false), 2000);
                  }
                }}
                disabled={isLoading}
                loading={isLoading}
                success={applySuccess}
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                }
              >
                {applySuccess ? '已应用' : '应用最新'}
              </EnhancedButton>
            )}
          </div>
        </div>

        {/* 智能模式提示条 */}
        {(hasBeforeContext || hasAfterContext) && (
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border mb-3 ${currentMode.color}`}>
            {currentMode.icon}
            <span className="ml-1 mr-2">{currentMode.label}</span>
            <span className="text-xs opacity-75">{currentMode.description}</span>
          </div>
        )}

        {/* 工具栏 - 优化后的简化布局 */}
        <div className="toolbar-container">
          {/* 按钮组 */}
          <div className="button-group">
            {/* 开始新续写按钮 - 主要操作（最高视觉权重） */}
            {onStartNew && (
              <EnhancedButton
                variant="primary"
                size="small"
                onClick={() => {
                  if (messages && messages.length > 0) {
                    setShowStartNewConfirm(true);
                  } else {
                    onStartNew();
                  }
                }}
                disabled={isLoading}
                loading={isLoading}
                className={cn(
                  // 当没有内容时添加呼吸动画引导用户
                  (!messages || messages.length === 0) && !isLoading && "breathing"
                )}
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                }
              >
                <span className="hidden sm:inline">开始新续写</span>
                <span className="sm:hidden">新续写</span>
              </EnhancedButton>
            )}

            {/* 继续当前内容按钮 */}
            {onContinue && generatedContent && (
              <EnhancedButton
                variant="secondary"
                size="small"
                onClick={() => setShowInputDialog(true)}
                disabled={isLoading}
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                }
              >
                <span className="hidden md:inline">继续当前内容</span>
                <span className="md:hidden">继续</span>
              </EnhancedButton>
            )}

            {/* 重新生成按钮 */}
            <EnhancedButton
              variant="secondary"
              size="small"
              onClick={onRestart}
              disabled={isLoading}
              loading={isLoading}
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              <span className="hidden md:inline">重新生成</span>
              <span className="md:hidden">重新</span>
            </EnhancedButton>

            {/* 清空所有按钮 - 危险操作 */}
            {messages && messages.length > 0 && (
              <>
                <div className="button-separator"></div>
                <EnhancedButton
                  variant="danger"
                  size="small"
                  onClick={() => setShowClearAllConfirm(true)}
                  disabled={isLoading}
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  }
                >
                  <span className="hidden md:inline">清空所有</span>
                  <span className="md:hidden">清空</span>
                </EnhancedButton>
              </>
            )}
          </div>

          {/* 统计信息 */}
          {messages && messages.length > 0 && (
            <div className="record-count">
              共 {messages.length} 条续写记录
            </div>
          )}
        </div>
      </div>

      {/* 预览内容 */}
      <div
        ref={previewRef}
        className="flex-1 overflow-y-auto p-4 relative"
        onScroll={checkScroll}
      >
        {(streamResponse || (messages && messages.length > 0)) ? (
          <div className="space-y-4">
            {getMessagesToDisplay().map((message, index) => {
              const isGenerating = message.status === 'generating';
              const isError = message.status === 'error';
              const isCompleted = message.status === 'completed';
              const isLastMessage = index === getMessagesToDisplay().length - 1;
              const showTypingCursor = isGenerating && isLastMessage && isLoading;

              return (
                <div
                  key={message.id || index}
                  className="bubble-container flex items-start mb-4 group"
                >
                  {/* AI头像 */}
                  <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                    isError ? 'bg-red-100' : isGenerating ? 'bg-orange-100' : 'bg-indigo-100'
                  }`}>
                    {isError ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ) : isGenerating ? (
                      <svg className="animate-spin h-6 w-6 text-orange-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    )}
                  </div>

                  {/* AI气泡内容 */}
                  <div
                    className={`ai-bubble flex-grow max-w-[80%] rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow cursor-pointer ${
                      isError
                        ? 'bg-red-50 border border-red-100'
                        : isGenerating
                          ? 'bg-orange-50 border border-orange-100'
                          : 'bg-indigo-50 border border-indigo-100'
                    }`}
                    onClick={() => handleInsertBubble(message.content)}
                  >
                    {/* 续写序号和时间戳 */}
                    {message.timestamp && (
                      <div className="text-xs text-gray-500 mb-2 flex items-center justify-between">
                        <span>续写 #{index + 1}</span>
                        <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                      </div>
                    )}

                    <div
                      className="whitespace-pre-line"
                      dangerouslySetInnerHTML={{
                        __html: showTypingCursor
                          ? message.content + '<span class="typing-cursor inline-block animate-pulse">▌</span>'
                          : message.content
                      }}
                    />

                    {/* 状态指示器 */}
                    {message.status && (
                      <div className="absolute top-2 right-2">
                        {isGenerating && (
                          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                        )}
                        {isCompleted && (
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        )}
                        {isError && (
                          <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 操作按钮组 */}
                  <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col space-y-2">
                    {/* 插入按钮 */}
                    <button
                      className="bg-green-500 text-white rounded-full p-2 shadow-sm hover:bg-green-600 transition-colors"
                      onClick={() => handleInsertBubble(message.content)}
                      title="插入内容到编辑器"
                      disabled={isGenerating}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>

                    {/* 复制按钮 */}
                    <button
                      className="bg-blue-500 text-white rounded-full p-2 shadow-sm hover:bg-blue-600 transition-colors"
                      onClick={() => navigator.clipboard.writeText(message.content)}
                      title="复制内容"
                      disabled={isGenerating}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                      </svg>
                    </button>

                    {/* 删除按钮 */}
                    {message.id && onDeleteContinue && (
                      <button
                        className="bg-red-500 text-white rounded-full p-2 shadow-sm hover:bg-red-600 transition-colors"
                        onClick={() => {
                          if (message.id) {
                            onDeleteContinue(message.id);
                          }
                        }}
                        title="删除此次续写"
                        disabled={isGenerating}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            {isLoading ? (
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"></div>
                <p>正在生成内容...</p>
              </div>
            ) : (
              <p>AI生成的内容将显示在这里</p>
            )}
          </div>
        )}

        {/* 滚动到底部按钮 */}
        {showScrollToBottom && (
          <button
            className="absolute bottom-4 right-4 bg-indigo-500 text-white rounded-full p-2 shadow-md hover:bg-indigo-600 transition-colors"
            onClick={scrollToBottom}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        )}
      </div>

      {/* 对话输入框 */}
      {showInputDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">继续对话</h3>
            <p className="text-sm text-gray-500 mb-4">
              请输入您想要继续的内容或提示，AI将基于之前的对话继续生成。
            </p>
            <textarea
              className="w-full p-3 border border-gray-300 rounded-lg mb-4"
              rows={4}
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              placeholder="请输入您的提示或指导..."
              autoFocus
            />
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
                onClick={() => {
                  setShowInputDialog(false);
                  setUserInput('');
                }}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
                onClick={() => {
                  if (onContinue && userInput.trim()) {
                    onContinue(userInput.trim());
                    setShowInputDialog(false);
                    setUserInput('');
                  }
                }}
                disabled={!userInput.trim()}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 开始新续写确认对话框 */}
      {showStartNewConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">开始新续写</h3>
            <p className="text-sm text-gray-500 mb-6">
              您当前有 {messages?.length || 0} 条续写记录。开始新续写将清空所有现有内容和历史记录，此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
                onClick={() => setShowStartNewConfirm(false)}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                onClick={() => {
                  setShowStartNewConfirm(false);
                  if (onStartNew) {
                    onStartNew();
                  }
                }}
              >
                确认开始新续写
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 清空所有确认对话框 */}
      {showClearAllConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">清空所有续写记录</h3>
            <p className="text-sm text-gray-500 mb-6">
              此操作将删除所有续写记录，此操作不可撤销。您确定要继续吗？
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
                onClick={() => setShowClearAllConfirm(false)}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                onClick={() => {
                  setShowClearAllConfirm(false);
                  if (onClearAll) {
                    onClearAll();
                  }
                }}
              >
                确认清空
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIContinuePreview;
