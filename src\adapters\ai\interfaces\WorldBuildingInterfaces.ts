"use client";

import { WorldBuilding } from '@/lib/db/dexie';

/**
 * 章节内容接口
 */
export interface ChapterContent {
  /**
   * 章节ID
   */
  id: string;

  /**
   * 章节标题
   */
  title: string;

  /**
   * 章节内容
   */
  content: string;
}

/**
 * 提取选项接口
 */
export interface ExtractionOptions {
  /**
   * 最大世界观元素数量
   */
  maxWorldBuildings?: number;

  /**
   * 自定义提示词
   */
  customPrompt?: string;

  /**
   * 关联的世界观元素
   */
  relatedWorldBuildings?: string[];
}

/**
 * 世界观提取结果接口
 */
export interface WorldBuildingExtractionResult {
  /**
   * 新信息
   */
  newInfo: Record<string, string>;

  /**
   * 更新原因
   */
  updateReasons?: Record<string, string>;
}

/**
 * 更新建议接口
 */
export interface UpdateSuggestion {
  /**
   * 字段名
   */
  field: string;

  /**
   * 当前值
   */
  currentValue: string | null;

  /**
   * 建议值
   */
  suggestedValue: string;

  /**
   * 更新类型：'add'（添加）、'update'（更新）或'append'（追加）
   */
  type: 'add' | 'update' | 'append';

  /**
   * 更新原因
   */
  reason: string;
}

/**
 * 世界观提取器接口
 */
export interface IWorldBuildingExtractor {
  /**
   * 从章节内容中提取世界观信息
   * @param chapters 章节内容列表
   * @param options 提取选项
   * @returns 提取的世界观信息
   */
  extractWorldBuildingsFromChapters(
    chapters: ChapterContent[],
    options?: ExtractionOptions
  ): Promise<Record<string, any>>;

  /**
   * 取消当前请求
   */
  cancelRequest(): void;
}

/**
 * 世界观更新器接口
 */
export interface IWorldBuildingUpdater {
  /**
   * 从章节内容中更新特定世界观元素的信息
   * @param worldBuilding 世界观对象
   * @param chapters 章节内容列表
   * @param options 提取选项
   * @returns 更新建议
   */
  updateWorldBuildingFromChapters(
    worldBuilding: WorldBuilding,
    chapters: ChapterContent[],
    options?: ExtractionOptions
  ): Promise<UpdateSuggestion[]>;

  /**
   * 使用关联章节内容更新世界观元素
   * @param worldBuilding 世界观对象
   * @param bookId 书籍ID
   * @param options 更新选项
   * @returns 更新建议
   */
  updateWorldBuildingWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    bookId: string,
    options?: ExtractionOptions
  ): Promise<UpdateSuggestion[]>;

  /**
   * 取消当前请求
   */
  cancelRequest(): void;
}

/**
 * 世界观字段生成器接口
 */
export interface IWorldBuildingFieldGenerator {
  /**
   * 使用关联章节内容生成世界观元素的特定字段
   * @param worldBuilding 世界观对象
   * @param fieldName 字段名称
   * @param bookId 书籍ID
   * @param relatedWorldBuildings 关联的世界观元素
   * @param customPrompt 自定义提示词
   * @returns 生成的字段内容
   */
  generateFieldWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    fieldName: string,
    bookId: string,
    relatedWorldBuildings?: string[],
    customPrompt?: string
  ): Promise<string>;

  /**
   * 取消当前请求
   */
  cancelRequest(): void;
}

/**
 * 世界观创建器接口
 */
export interface IWorldBuildingCreator {
  /**
   * 从章节内容中创建世界观元素
   * @param chapters 章节内容列表
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  createWorldBuildingsFromChapters(
    chapters: ChapterContent[],
    options?: ExtractionOptions
  ): Promise<Record<string, any>>;

  /**
   * 使用关联章节内容创建世界观元素
   * @param bookId 书籍ID
   * @param chapterId 章节ID
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  createWorldBuildingsFromAssociatedChapter(
    bookId: string,
    chapterId: string,
    options?: ExtractionOptions
  ): Promise<Record<string, any>>;

  /**
   * 取消当前请求
   */
  cancelRequest(): void;
}
