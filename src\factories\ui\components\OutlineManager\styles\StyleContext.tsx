"use client";

import React, { createContext, useState, useContext, ReactNode } from 'react';
import { EdgeStyle } from '../edges/CustomEdge';

// 基础设计令牌
export interface DesignTokens {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    neutral: string[];
  };
  typography: {
    fontFamily: string;
    fontWeights: Record<string, number>;
    lineHeights: Record<string, number>;
  };
  spacing: Record<string, string>;
  animation: {
    durations: Record<string, number>;
    easings: Record<string, string>;
  };
}

// 节点样式接口
export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  boxShadow: string;
  padding: string;
  fontFamily: string;
  fontSize: string;
  fontWeight: number;
  color: string;
}

// 完整样式接口
export interface StylePreset {
  id: string;
  name: string;
  description: string;
  tokens: DesignTokens;
  nodeStyles: Record<string, NodeStyle>;
  edgeStyles: Record<string, EdgeStyle>;
}

// 默认设计令牌
const defaultTokens: DesignTokens = {
  colors: {
    primary: '#3b82f6',
    secondary: '#6b7280',
    accent: '#f59e0b',
    neutral: ['#f9fafb', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af', '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827'],
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    fontWeights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      none: 1,
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  spacing: {
    '0': '0',
    '1': '0.25rem',
    '2': '0.5rem',
    '3': '0.75rem',
    '4': '1rem',
    '6': '1.5rem',
    '8': '2rem',
    '12': '3rem',
    '16': '4rem',
  },
  animation: {
    durations: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
    easings: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    },
  },
};

// 预设样式
const stylePresets: StylePreset[] = [
  {
    id: 'classic',
    name: '经典',
    description: '简洁清晰的线条和节点',
    tokens: defaultTokens,
    nodeStyles: {
      default: {
        backgroundColor: '#ffffff',
        borderColor: '#d1d5db',
        borderWidth: 1,
        borderRadius: 4,
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '12px',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSize: '14px',
        fontWeight: 400,
        color: '#374151',
      },
    },
    edgeStyles: {
      'parent-child': {
        type: 'bezier',
        strokeWidth: 1.5,
        strokeColor: '#9ca3af',
        strokeOpacity: 0.8,
        arrowHeadType: 'none',
      },
      'association': {
        type: 'straight',
        strokeWidth: 1,
        strokeColor: '#9ca3af',
        strokeOpacity: 0.6,
        strokeDasharray: '5,5',
        arrowHeadType: 'none',
      },
      'sequence': {
        type: 'smoothstep',
        strokeWidth: 1.5,
        strokeColor: '#9ca3af',
        strokeOpacity: 0.8,
        arrowHeadType: 'arrow',
      },
    },
  },
  {
    id: 'modern',
    name: '现代',
    description: '圆润流畅的曲线和节点',
    tokens: {
      ...defaultTokens,
      colors: {
        ...defaultTokens.colors,
        primary: '#8b5cf6',
        accent: '#ec4899',
      },
    },
    nodeStyles: {
      default: {
        backgroundColor: '#ffffff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        borderRadius: 12,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        padding: '16px',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSize: '14px',
        fontWeight: 500,
        color: '#1f2937',
      },
    },
    edgeStyles: {
      'parent-child': {
        type: 'bezier',
        strokeWidth: 2,
        strokeColor: '#8b5cf6',
        strokeOpacity: 0.7,
        arrowHeadType: 'none',
        animated: true,
        animationSpeed: 0.5,
      },
      'association': {
        type: 'bezier',
        strokeWidth: 1.5,
        strokeColor: '#8b5cf6',
        strokeOpacity: 0.5,
        strokeDasharray: '5,5',
        arrowHeadType: 'none',
      },
      'sequence': {
        type: 'smoothstep',
        strokeWidth: 2,
        strokeColor: '#8b5cf6',
        strokeOpacity: 0.7,
        arrowHeadType: 'circle',
      },
    },
  },
  {
    id: 'professional',
    name: '专业',
    description: '强调层次的复杂样式',
    tokens: {
      ...defaultTokens,
      colors: {
        ...defaultTokens.colors,
        primary: '#0ea5e9',
        secondary: '#64748b',
        accent: '#f97316',
      },
    },
    nodeStyles: {
      default: {
        backgroundColor: '#ffffff',
        borderColor: '#cbd5e1',
        borderWidth: 1,
        borderRadius: 6,
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        padding: '14px',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSize: '14px',
        fontWeight: 400,
        color: '#334155',
      },
    },
    edgeStyles: {
      'parent-child': {
        type: 'smoothstep',
        strokeWidth: 1.5,
        strokeColor: '#0ea5e9',
        strokeOpacity: 0.8,
        arrowHeadType: 'arrow',
      },
      'association': {
        type: 'bezier',
        strokeWidth: 1,
        strokeColor: '#64748b',
        strokeOpacity: 0.6,
        strokeDasharray: '3,3',
        arrowHeadType: 'none',
      },
      'sequence': {
        type: 'step',
        strokeWidth: 1.5,
        strokeColor: '#f97316',
        strokeOpacity: 0.8,
        arrowHeadType: 'diamond',
      },
    },
  },
  {
    id: 'creative',
    name: '创意',
    description: '独特艺术感的线条和节点',
    tokens: {
      ...defaultTokens,
      colors: {
        ...defaultTokens.colors,
        primary: '#f43f5e',
        secondary: '#8b5cf6',
        accent: '#fbbf24',
      },
    },
    nodeStyles: {
      default: {
        backgroundColor: '#fffbeb',
        borderColor: '#f43f5e',
        borderWidth: 2,
        borderRadius: 16,
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        padding: '16px',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSize: '15px',
        fontWeight: 500,
        color: '#18181b',
      },
    },
    edgeStyles: {
      'parent-child': {
        type: 'bezier',
        strokeWidth: 3,
        strokeColor: '#f43f5e',
        strokeOpacity: 0.7,
        arrowHeadType: 'none',
        animated: true,
        animationSpeed: 1,
      },
      'association': {
        type: 'bezier',
        strokeWidth: 2,
        strokeColor: '#8b5cf6',
        strokeOpacity: 0.6,
        strokeDasharray: '5,10',
        arrowHeadType: 'none',
        animated: true,
        animationSpeed: 0.5,
      },
      'sequence': {
        type: 'bezier',
        strokeWidth: 2.5,
        strokeColor: '#fbbf24',
        strokeOpacity: 0.8,
        arrowHeadType: 'arrowclosed',
      },
    },
  },
];

// 样式上下文接口
interface StyleContextType {
  currentStyle: string;
  setCurrentStyle: (styleId: string) => void;
  stylePresets: StylePreset[];
  getCurrentPreset: () => StylePreset;
  getEdgeStyle: (relationshipType: string) => EdgeStyle;
  getNodeStyle: (nodeType: string) => NodeStyle;
  updateEdgeStyle: (relationshipType: string, updates: Partial<EdgeStyle>) => void;
  updateNodeStyle: (nodeType: string, updates: Partial<NodeStyle>) => void;
  // 添加序列化方法，用于调试
  toJSON?: () => any;
}

// 创建上下文
const StyleContext = createContext<StyleContextType | undefined>(undefined);

// 样式提供器组件
interface StyleProviderProps {
  children: ReactNode;
}

// localStorage键名
const STORAGE_KEYS = {
  CURRENT_STYLE: 'outline-canvas-current-style',
  CUSTOM_PRESETS: 'outline-canvas-custom-presets',
  STYLE_MODIFICATIONS: 'outline-canvas-style-modifications'
};

// 从localStorage加载样式设置
const loadStyleSettings = () => {
  try {
    const savedCurrentStyle = localStorage.getItem(STORAGE_KEYS.CURRENT_STYLE);
    const savedModifications = localStorage.getItem(STORAGE_KEYS.STYLE_MODIFICATIONS);

    let loadedPresets = [...stylePresets];

    // 如果有保存的样式修改，应用到预设中
    if (savedModifications) {
      const modifications = JSON.parse(savedModifications);
      loadedPresets = loadedPresets.map(preset => {
        const presetModifications = modifications[preset.id];
        if (presetModifications) {
          return {
            ...preset,
            edgeStyles: { ...preset.edgeStyles, ...presetModifications.edgeStyles },
            nodeStyles: { ...preset.nodeStyles, ...presetModifications.nodeStyles }
          };
        }
        return preset;
      });
    }

    return {
      currentStyle: savedCurrentStyle || 'classic',
      presets: loadedPresets
    };
  } catch (error) {
    console.warn('加载样式设置失败:', error);
    return {
      currentStyle: 'classic',
      presets: stylePresets
    };
  }
};

// 保存样式设置到localStorage
const saveStyleSettings = (currentStyle: string, presets: StylePreset[]) => {
  try {
    // 保存当前选中的样式
    localStorage.setItem(STORAGE_KEYS.CURRENT_STYLE, currentStyle);

    // 保存样式修改（只保存与原始预设的差异）
    const modifications: Record<string, any> = {};
    presets.forEach(preset => {
      const originalPreset = stylePresets.find(p => p.id === preset.id);
      if (originalPreset) {
        const edgeStyleChanges: Record<string, any> = {};
        const nodeStyleChanges: Record<string, any> = {};

        // 检查边样式变化
        Object.keys(preset.edgeStyles).forEach(key => {
          if (JSON.stringify(preset.edgeStyles[key]) !== JSON.stringify(originalPreset.edgeStyles[key])) {
            edgeStyleChanges[key] = preset.edgeStyles[key];
          }
        });

        // 检查节点样式变化
        Object.keys(preset.nodeStyles).forEach(key => {
          if (JSON.stringify(preset.nodeStyles[key]) !== JSON.stringify(originalPreset.nodeStyles[key])) {
            nodeStyleChanges[key] = preset.nodeStyles[key];
          }
        });

        // 如果有变化，保存修改
        if (Object.keys(edgeStyleChanges).length > 0 || Object.keys(nodeStyleChanges).length > 0) {
          modifications[preset.id] = {
            edgeStyles: edgeStyleChanges,
            nodeStyles: nodeStyleChanges
          };
        }
      }
    });

    localStorage.setItem(STORAGE_KEYS.STYLE_MODIFICATIONS, JSON.stringify(modifications));
    console.log('✅ 样式设置已保存到localStorage');
  } catch (error) {
    console.error('保存样式设置失败:', error);
  }
};

export const StyleProvider: React.FC<StyleProviderProps> = ({ children }) => {
  // 从localStorage加载初始状态
  const initialSettings = loadStyleSettings();
  const [currentStyle, setCurrentStyle] = useState(initialSettings.currentStyle);
  const [presets, setPresets] = useState<StylePreset[]>(initialSettings.presets);

  // 获取当前预设
  const getCurrentPreset = () => {
    return presets.find(preset => preset.id === currentStyle) || presets[0];
  };

  // 获取边样式
  const getEdgeStyle = (relationshipType: string) => {
    const preset = getCurrentPreset();
    return preset.edgeStyles[relationshipType] || preset.edgeStyles['parent-child'];
  };

  // 获取节点样式
  const getNodeStyle = (nodeType: string) => {
    const preset = getCurrentPreset();
    return preset.nodeStyles[nodeType] || preset.nodeStyles['default'];
  };

  // 更新边样式
  const updateEdgeStyle = (relationshipType: string, updates: Partial<EdgeStyle>) => {
    setPresets(prevPresets => {
      const newPresets = prevPresets.map(preset => {
        if (preset.id === currentStyle) {
          return {
            ...preset,
            edgeStyles: {
              ...preset.edgeStyles,
              [relationshipType]: {
                ...preset.edgeStyles[relationshipType],
                ...updates,
              },
            },
          };
        }
        return preset;
      });

      // 自动保存到localStorage
      saveStyleSettings(currentStyle, newPresets);
      return newPresets;
    });
  };

  // 更新节点样式
  const updateNodeStyle = (nodeType: string, updates: Partial<NodeStyle>) => {
    setPresets(prevPresets => {
      const newPresets = prevPresets.map(preset => {
        if (preset.id === currentStyle) {
          return {
            ...preset,
            nodeStyles: {
              ...preset.nodeStyles,
              [nodeType]: {
                ...preset.nodeStyles[nodeType],
                ...updates,
              },
            },
          };
        }
        return preset;
      });

      // 自动保存到localStorage
      saveStyleSettings(currentStyle, newPresets);
      return newPresets;
    });
  };

  // 增强的setCurrentStyle函数，包含自动保存
  const setCurrentStyleWithSave = (styleId: string) => {
    setCurrentStyle(styleId);
    // 保存新的当前样式到localStorage
    saveStyleSettings(styleId, presets);
    console.log('🎨 样式预设已切换并保存:', styleId);
  };

  // 创建上下文值对象，包含toJSON方法
  const contextValue: StyleContextType = {
    currentStyle,
    setCurrentStyle: setCurrentStyleWithSave,
    stylePresets: presets,
    getCurrentPreset,
    getEdgeStyle,
    getNodeStyle,
    updateEdgeStyle,
    updateNodeStyle,
    // 添加toJSON方法，用于序列化
    toJSON: () => {
      // 返回可序列化的对象
      const currentPreset = getCurrentPreset();
      return {
        currentStyle,
        currentPreset: {
          id: currentPreset.id,
          name: currentPreset.name,
          description: currentPreset.description
        },
        // 添加样式获取函数的序列化版本
        getEdgeStyle: (type: string) => getEdgeStyle(type),
        getNodeStyle: (type: string) => getNodeStyle(type),
        // 添加一些常用样式的预计算值
        edgeStyles: {
          'parent-child': getEdgeStyle('parent-child'),
          'association': getEdgeStyle('association'),
          'sequence': getEdgeStyle('sequence')
        },
        nodeStyles: {
          'default': getNodeStyle('default'),
          'chapter': getNodeStyle('chapter'),
          'scene': getNodeStyle('scene')
        }
      };
    }
  };

  return (
    <StyleContext.Provider value={contextValue}>
      {children}
    </StyleContext.Provider>
  );
};

// 自定义钩子，用于访问样式上下文
export const useStyle = () => {
  const context = useContext(StyleContext);
  if (context === undefined) {
    throw new Error('useStyle must be used within a StyleProvider');
  }
  return context;
};
