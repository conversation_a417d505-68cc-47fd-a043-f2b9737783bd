"use client";

import { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

/**
 * 世界观数据钩子
 * 用于管理世界观数据的状态和操作
 */
export const useWorldBuildingData = (bookId: string, isOpen: boolean) => {
  // 世界观列表状态
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);

  // 过滤后的世界观列表状态
  const [filteredWorldBuildings, setFilteredWorldBuildings] = useState<WorldBuilding[]>([]);

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 搜索查询状态
  const [searchQuery, setSearchQuery] = useState('');

  // 排序方式状态
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'importance'>('name');

  // 选中的世界观状态
  const [selectedWorldBuilding, setSelectedWorldBuilding] = useState<WorldBuilding | null>(null);

  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);

  // 加载世界观数据
  useEffect(() => {
    if (isOpen) {
      loadWorldBuildings();
    }
  }, [isOpen, bookId]);

  // 根据搜索查询和排序方式过滤和排序世界观列表
  useEffect(() => {
    if (!worldBuildings.length) {
      setFilteredWorldBuildings([]);
      return;
    }

    let filtered = [...worldBuildings];

    // 根据搜索查询过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        wb =>
          wb.name.toLowerCase().includes(query) ||
          wb.description.toLowerCase().includes(query) ||
          (wb.category && wb.category.toLowerCase().includes(query))
      );
    }

    // 根据排序方式排序
    filtered.sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else if (sortBy === 'category') {
        return (a.category || '').localeCompare(b.category || '');
      } else if (sortBy === 'importance') {
        const aImportance = a.attributes?.importance ? parseInt(a.attributes.importance) : 0;
        const bImportance = b.attributes?.importance ? parseInt(b.attributes.importance) : 0;
        return bImportance - aImportance; // 重要性从高到低排序
      }
      return 0;
    });

    setFilteredWorldBuildings(filtered);
  }, [worldBuildings, searchQuery, sortBy]);

  /**
   * 加载世界观数据
   */
  const loadWorldBuildings = async () => {
    setIsLoading(true);
    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 获取世界观数据
      const data = await worldBuildingRepository.getAllByBookId(bookId);
      setWorldBuildings(data);

      // 如果有世界观，选中第一个
      if (data.length > 0) {
        setSelectedWorldBuilding(data[0]);
      } else {
        setSelectedWorldBuilding(null);
      }
    } catch (error) {
      console.error('加载世界观数据失败:', error);
      setWorldBuildings([]);
      setSelectedWorldBuilding(null);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 选择世界观
   * @param worldBuilding 世界观
   */
  const handleSelectWorldBuilding = (worldBuilding: WorldBuilding) => {
    if (isEditing) {
      // 如果正在编辑，提示用户保存或取消
      if (window.confirm('您有未保存的更改，是否放弃这些更改？')) {
        setIsEditing(false);
        setSelectedWorldBuilding(worldBuilding);
      }
    } else {
      setSelectedWorldBuilding(worldBuilding);
    }
  };

  /**
   * 创建世界观
   */
  const handleCreateWorldBuilding = () => {
    // 创建新的世界观对象
    const now = new Date();
    const newWorldBuilding: WorldBuilding = {
      bookId,
      name: '新世界观元素',
      category: '地理', // 默认类别
      description: '',
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: [],
      relatedCharacterIds: [],
      relatedTerminologyIds: [],
      relatedWorldBuildingIds: []
    };

    // 设置为选中的世界观并进入编辑模式
    setSelectedWorldBuilding(newWorldBuilding);
    setIsEditing(true);
  };

  /**
   * 编辑世界观
   */
  const handleEditWorldBuilding = () => {
    if (selectedWorldBuilding) {
      setIsEditing(true);
    }
  };

  /**
   * 保存世界观
   * @param worldBuilding 世界观
   */
  const handleSaveWorldBuilding = async (worldBuilding: WorldBuilding) => {
    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 判断是新建还是更新
      const isNew = !worldBuilding.id;

      if (isNew) {
        // 创建世界观
        const id = await worldBuildingRepository.create(worldBuilding);
        console.log('世界观创建成功:', id);

        // 更新世界观对象
        const createdWorldBuilding = { ...worldBuilding, id };

        // 更新世界观列表
        setWorldBuildings(prev => [...prev, createdWorldBuilding]);
        setSelectedWorldBuilding(createdWorldBuilding);
      } else {
        // 更新世界观
        await worldBuildingRepository.update(worldBuilding.id!, worldBuilding);
        console.log('世界观更新成功:', worldBuilding.id);

        // 更新世界观列表
        setWorldBuildings(prev =>
          prev.map(wb => wb.id === worldBuilding.id ? worldBuilding : wb)
        );
        setSelectedWorldBuilding(worldBuilding);
      }

      // 退出编辑模式
      setIsEditing(false);
    } catch (error) {
      console.error('保存世界观失败:', error);
      alert('保存世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  /**
   * 取消编辑
   */
  const handleCancelEdit = () => {
    // 如果是新建的世界观，取消编辑时清空选中的世界观
    if (selectedWorldBuilding && !selectedWorldBuilding.id) {
      setSelectedWorldBuilding(worldBuildings.length > 0 ? worldBuildings[0] : null);
    }

    // 退出编辑模式
    setIsEditing(false);
  };

  /**
   * 删除世界观
   * @param worldBuildingId 世界观ID
   */
  const handleDeleteWorldBuilding = async (worldBuildingId: string) => {
    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 删除世界观
      await worldBuildingRepository.delete(worldBuildingId);
      console.log('世界观删除成功:', worldBuildingId);

      // 更新世界观列表
      const updatedWorldBuildings = worldBuildings.filter(wb => wb.id !== worldBuildingId);
      setWorldBuildings(updatedWorldBuildings);

      // 如果删除的是当前选中的世界观，选中列表中的第一个世界观
      if (selectedWorldBuilding && selectedWorldBuilding.id === worldBuildingId) {
        setSelectedWorldBuilding(updatedWorldBuildings.length > 0 ? updatedWorldBuildings[0] : null);
      }
    } catch (error) {
      console.error('删除世界观失败:', error);
      alert('删除世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  return {
    worldBuildings,
    filteredWorldBuildings,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedWorldBuilding,
    isEditing,
    handleSelectWorldBuilding,
    handleCreateWorldBuilding,
    handleEditWorldBuilding,
    handleSaveWorldBuilding,
    handleCancelEdit,
    handleDeleteWorldBuilding,
    loadWorldBuildings
  };
};
