# iOS风格设计规范

## 1. 设计原则

### 1.1 清晰性
- 文本在所有尺寸下都应清晰可读
- 图标应简洁明了，功能一目了然
- 装饰元素不应干扰内容和功能

### 1.2 尊重
- 界面应辅助用户完成任务，而不是抢占注意力
- 全屏沉浸式体验，减少不必要的干扰
- 内容为王，UI元素应该为内容服务

### 1.3 深度
- 使用分层的视觉层次结构传达层级关系
- 通过半透明、模糊和微妙阴影创造深度感
- 动画应该有意义，帮助用户理解界面变化

## 2. 视觉设计

### 2.1 色彩
- **主色调**：使用柔和的米色和棕色作为基础
- **强调色**：使用鲜明但不刺眼的颜色作为点缀
- **透明度**：大量使用半透明效果，创造层次感
- **暗色模式**：支持明暗两种模式无缝切换

### 2.2 排版
- **字体**：使用系统字体或无衬线字体
- **字重**：标题使用中等(Medium)或半粗体(Semibold)，正文使用常规(Regular)
- **行高**：正文使用1.5-1.8的行高，提高可读性
- **字号层级**：建立清晰的字号层级，通常为4-5级

### 2.3 图标与图像
- **图标**：简洁、一致、轻量级的线条图标
- **图标状态**：提供正常、按下、禁用等状态
- **图像**：使用圆角矩形，保持一致性

## 3. 组件设计

### 3.1 卡片
- 轻微圆角（12-16px）
- 微妙的阴影效果
- 内容内边距一致（16-20px）
- 悬停/点击时有轻微的上浮动画

### 3.2 按钮
- **形状**：圆角矩形（8-12px圆角）
- **状态**：正常、按下、禁用状态有明显区分
- **反馈**：点击时有轻微缩放或颜色变化
- **类型**：主要、次要、文本、图标按钮样式统一

### 3.3 输入框
- 简洁的边框或底部线条
- 聚焦时有明显的状态变化
- 错误状态清晰可见
- 配合适当的输入辅助功能

### 3.4 对话框/弹窗
- 居中显示，带有半透明背景遮罩
- 明显的圆角（16-20px）
- 优雅的进入/退出动画
- 清晰的操作按钮，通常底部对齐

### 3.5 列表
- 条目之间有微妙的分隔线或间距
- 可点击项有明确的视觉反馈
- 支持左右滑动操作
- 列表项高度一致，内容对齐

## 4. 动画与交互

### 4.1 动画原则
- **连续性**：元素状态变化应流畅自然
- **明确性**：动画应帮助用户理解界面变化
- **效率**：动画不应无故延迟用户操作
- **精致**：微妙而不浮夸

### 4.2 常用动画类型
- **转场**：页面切换使用自然的推入/推出或淡入/淡出
- **反馈**：点击操作有即时的视觉反馈
- **状态变化**：组件状态变化平滑过渡
- **强调**：重要元素可使用注意力引导动画

### 4.3 手势交互
- 支持常见的滑动、点按、长按等手势
- 手势应有适当的视觉反馈
- 关键操作应有确认机制

## 5. 布局

### 5.1 网格系统
- 使用8px基础网格系统
- 内容区块间距一致（16px或24px）
- 响应式设计，适应不同屏幕尺寸

### 5.2 空间利用
- 合理使用留白，不过度拥挤
- 内容分区明确，层次清晰
- 关注点突出，次要信息弱化

### 5.3 导航模式
- 导航元素位置固定，易于访问
- 层级结构清晰，避免过深嵌套
- 提供返回路径和位置指示

## 6. 可访问性

### 6.1 颜色对比
- 文本与背景的对比度符合WCAG标准
- 不仅依靠颜色传达信息
- 支持高对比度模式

### 6.2 触摸目标
- 按钮和可交互元素尺寸足够大（至少44×44px）
- 交互元素间有足够间距，避免误触
- 提供明确的焦点状态

### 6.3 文本可读性
- 文本大小适中，避免过小字体
- 支持动态字体大小调整
- 重要信息不依赖颜色区分

## 7. 实现注意事项

### 7.1 CSS技巧
- 使用CSS变量管理主题色和关键尺寸
- 使用`backdrop-filter`实现iOS风格模糊效果
- 使用`box-shadow`创建微妙的阴影效果
- 使用`border-radius`创建一致的圆角

### 7.2 动画实现
- 使用CSS transitions实现简单状态变化
- 使用CSS animations实现复杂动画
- 动画持续时间控制在200-400ms之间
- 使用`cubic-bezier`曲线实现自然的动画效果

### 7.3 响应式设计
- 使用相对单位（rem, em, %）而非固定像素
- 使用媒体查询适配不同设备
- 考虑触摸和鼠标两种交互模式
