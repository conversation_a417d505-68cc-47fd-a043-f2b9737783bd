"use client";

import React, { useState } from 'react';
import { Terminology, Character, WorldBuilding } from '@/lib/db/dexie';
import { TerminologyForm } from './TerminologyForm';
import { TerminologyView } from './TerminologyView';
import { TerminologyRelationDialog } from './TerminologyRelationDialog';

interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TerminologyDetailProps {
  terminology: Terminology | null;
  bookId: string;
  terminologies: Terminology[];
  isEditing: boolean;
  onEdit: () => void;
  onDelete: (terminology: Terminology) => void;
  onSave: (terminology: Terminology) => void;
  onCancel: () => void;
  chapters?: GenericChapter[];
  characters?: Character[];
  worldBuildings?: WorldBuilding[];
  onFieldUpdate?: (field: string, fieldLabel: string, currentValue: string) => void;
  onContentEnhance?: () => void;
}

/**
 * 空状态组件
 */
const EmptyState: React.FC = () => (
  <div className="h-full flex flex-col justify-center items-center text-gray-500">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
    <p className="text-lg">选择或创建一个术语</p>
    <p className="text-sm mt-2">在左侧列表中选择一个术语查看详情，或点击"创建术语"按钮添加新术语</p>
  </div>
);

/**
 * 术语详情/编辑组件
 */
export const TerminologyDetail: React.FC<TerminologyDetailProps> = ({
  terminology,
  bookId,
  terminologies,
  isEditing,
  onEdit,
  onDelete,
  onSave,
  onCancel,
  chapters = [],
  characters = [],
  worldBuildings = [],
  onFieldUpdate,
  onContentEnhance
}) => {
  // 不再需要UI工厂实例

  // 关联术语对话框状态
  const [isRelationDialogOpen, setIsRelationDialogOpen] = useState(false);

  // 章节范围选择状态
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(
    terminology?.extractedFromChapterIds || []
  );

  // 如果没有选中的术语，显示空状态
  if (!terminology) {
    return <EmptyState />;
  }

  // 处理范围选择
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    if (!rangeStart || !rangeEnd || !chapters || chapters.length === 0) return;

    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      alert('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      alert('起始章节不能大于结束章节');
      return;
    }

    if (start > chapters.length || end > chapters.length) {
      alert(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      alert('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  // 处理章节选择
  const handleChapterSelect = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 保存关联章节
  const saveRelatedChapters = () => {
    const updatedTerminology = {
      ...terminology,
      extractedFromChapterIds: selectedChapterIds
    };
    onSave(updatedTerminology);
  };

  // 创建按钮组件
  const renderEditButton = () => (
    <button
      className="px-3 py-1.5 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md text-sm font-medium transition-colors"
      onClick={onEdit}
    >
      编辑
    </button>
  );

  const renderDeleteButton = () => (
    <button
      className="px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-md text-sm font-medium transition-colors"
      onClick={() => onDelete(terminology)}
    >
      删除
    </button>
  );

  // 处理关联术语保存
  const handleRelationSave = (relatedTerminologyIds: string[]) => {
    const updatedTerminology = {
      ...terminology,
      relatedTerminologyIds
    };
    onSave(updatedTerminology);
  };

  if (isEditing) {
    // 渲染编辑表单
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 overflow-auto">
          <TerminologyForm
            terminology={terminology}
            bookId={bookId}
            isNew={!terminologies.some(t => t.id === terminology.id)}
            onSave={onSave}
            onCancel={onCancel}
            hideButtons={true}
            terminologies={terminologies}
            characters={characters}
            worldBuildings={worldBuildings}
            onFieldUpdate={onFieldUpdate}
            onContentEnhance={onContentEnhance}
          />
        </div>
        <div className="mt-4 flex justify-end space-x-3 border-t border-gray-200 pt-4">
          <button
            className="px-5 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onCancel}
          >
            取消
          </button>
          <button
            className="px-5 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
            }}
            onClick={() => {
              // 获取表单元素并触发提交事件
              const form = document.querySelector('form');
              if (form) {
                // 创建并分发提交事件
                const submitEvent = new Event('submit', { cancelable: true, bubbles: true });
                form.dispatchEvent(submitEvent);
              } else {
                // 如果找不到表单，则使用当前术语数据
                console.warn('找不到术语表单，使用当前术语数据保存');
                onSave(terminology);
              }
            }}
          >
            保存
          </button>
        </div>
      </div>
    );
  } else {
    // 渲染查看视图
    return (
      <div className="h-full flex flex-col">
        <div className="mb-4 flex justify-end space-x-3">
          <button
            className="px-4 py-1.5 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(70, 130, 180, 0.1)',
              color: 'var(--color-info)',
              border: '1px solid rgba(70, 130, 180, 0.3)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={() => setIsRelationDialogOpen(true)}
            title="管理关联术语"
          >
            关联术语
          </button>
          <button
            className="px-4 py-1.5 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(70, 130, 180, 0.1)',
              color: 'var(--color-info)',
              border: '1px solid rgba(70, 130, 180, 0.3)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={() => {
              // 显示章节范围选择对话框
              const chaptersDialog = document.createElement('div');
              chaptersDialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
              chaptersDialog.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-auto">
                  <h2 class="text-xl font-bold mb-4">章节范围选择</h2>

                  <div class="mb-4">
                    <div class="flex items-center space-x-2 mb-3">
                      <input type="number" id="range-start" placeholder="起始" class="w-20 p-2 border rounded" min="1" max="${chapters?.length || 1}" />
                      <span>-</span>
                      <input type="number" id="range-end" placeholder="结束" class="w-20 p-2 border rounded" min="1" max="${chapters?.length || 1}" />
                      <button id="select-range" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">选择</button>
                      <button id="deselect-range" class="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">取消选择</button>
                    </div>
                  </div>

                  <div class="max-h-60 overflow-y-auto border rounded-lg p-2 mb-4">
                    <div class="space-y-1">
                      ${chapters?.map(chapter => `
                        <div class="flex items-center p-2 hover:bg-gray-100 rounded">
                          <input type="checkbox" id="chapter-${chapter.id}"
                            ${selectedChapterIds.includes(chapter.id!) ? 'checked' : ''}
                            data-chapter-id="${chapter.id}"
                            class="chapter-checkbox mr-2 h-4 w-4" />
                          <label for="chapter-${chapter.id}" class="cursor-pointer">
                            ${chapter.title || `第${chapter.order !== undefined ? chapter.order + 1 : '?'}章`}
                          </label>
                        </div>
                      `).join('') || '没有可选择的章节'}
                    </div>
                  </div>

                  <div class="flex justify-end space-x-3">
                    <button id="cancel-dialog" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">取消</button>
                    <button id="save-chapters" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">保存关联章节</button>
                  </div>
                </div>
              `;

              document.body.appendChild(chaptersDialog);

              // 添加事件监听
              document.getElementById('select-range')?.addEventListener('click', () => {
                const start = parseInt((document.getElementById('range-start') as HTMLInputElement).value);
                const end = parseInt((document.getElementById('range-end') as HTMLInputElement).value);

                if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
                  alert('请输入有效的章节范围');
                  return;
                }

                if (start > end) {
                  alert('起始章节不能大于结束章节');
                  return;
                }

                if (start > chapters!.length || end > chapters!.length) {
                  alert(`章节编号必须在1到${chapters!.length}之间`);
                  return;
                }

                // 获取排序后的章节
                const sortedChapters = [...chapters!].sort((a, b) => {
                  const orderA = a.order !== undefined ? a.order : 999999;
                  const orderB = b.order !== undefined ? b.order : 999999;
                  return orderA - orderB;
                });

                // 选择范围内的章节
                const chaptersInRange = sortedChapters.slice(start - 1, end);

                // 选中对应的复选框
                chaptersInRange.forEach(chapter => {
                  const checkbox = document.querySelector(`[data-chapter-id="${chapter.id}"]`) as HTMLInputElement;
                  if (checkbox) checkbox.checked = true;
                });
              });

              document.getElementById('deselect-range')?.addEventListener('click', () => {
                const start = parseInt((document.getElementById('range-start') as HTMLInputElement).value);
                const end = parseInt((document.getElementById('range-end') as HTMLInputElement).value);

                if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
                  alert('请输入有效的章节范围');
                  return;
                }

                if (start > end) {
                  alert('起始章节不能大于结束章节');
                  return;
                }

                if (start > chapters!.length || end > chapters!.length) {
                  alert(`章节编号必须在1到${chapters!.length}之间`);
                  return;
                }

                // 获取排序后的章节
                const sortedChapters = [...chapters!].sort((a, b) => {
                  const orderA = a.order !== undefined ? a.order : 999999;
                  const orderB = b.order !== undefined ? b.order : 999999;
                  return orderA - orderB;
                });

                // 选择范围内的章节
                const chaptersInRange = sortedChapters.slice(start - 1, end);

                // 取消选中对应的复选框
                chaptersInRange.forEach(chapter => {
                  const checkbox = document.querySelector(`[data-chapter-id="${chapter.id}"]`) as HTMLInputElement;
                  if (checkbox) checkbox.checked = false;
                });
              });

              document.getElementById('save-chapters')?.addEventListener('click', () => {
                // 获取所有选中的章节ID
                const selectedIds: string[] = [];
                document.querySelectorAll('.chapter-checkbox:checked').forEach(checkbox => {
                  const chapterId = (checkbox as HTMLInputElement).dataset.chapterId;
                  if (chapterId) selectedIds.push(chapterId);
                });

                // 更新术语的关联章节
                const updatedTerminology = {
                  ...terminology,
                  extractedFromChapterIds: selectedIds
                };
                onSave(updatedTerminology);

                // 关闭对话框
                chaptersDialog.remove();
              });

              document.getElementById('cancel-dialog')?.addEventListener('click', () => {
                chaptersDialog.remove();
              });
            }}
            title="管理关联章节"
          >
            关联章节
          </button>
          <button
            className="px-4 py-1.5 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onEdit}
          >
            编辑
          </button>
          <button
            className="px-4 py-1.5 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(178, 34, 34, 0.1)',
              color: 'var(--color-danger)',
              border: '1px solid rgba(178, 34, 34, 0.3)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={() => onDelete(terminology)}
          >
            删除
          </button>
        </div>
        <div className="flex-1 overflow-auto">
          <TerminologyView
            terminology={terminology}
            terminologies={terminologies}
            chapters={chapters}
            onEdit={onEdit}
            onDelete={() => onDelete(terminology)}
            editButton={renderEditButton()}
            deleteButton={renderDeleteButton()}
          />
        </div>

        {/* 关联术语对话框 */}
        <TerminologyRelationDialog
          isOpen={isRelationDialogOpen}
          onClose={() => setIsRelationDialogOpen(false)}
          onSave={handleRelationSave}
          terminology={terminology}
          allTerminologies={terminologies}
        />
      </div>
    );
  }
};
