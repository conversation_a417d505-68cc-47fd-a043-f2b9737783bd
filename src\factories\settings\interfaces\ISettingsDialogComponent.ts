import { ISettingsComponent } from './ISettingsComponent';

/**
 * 设置弹窗组件接口
 */
export interface ISettingsDialogComponent extends ISettingsComponent {
  /**
   * 设置是否显示弹窗
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 设置当前字体
   * @param font 字体ID
   */
  setCurrentFont(font: string): void;
  
  /**
   * 获取当前字体
   * @returns 当前字体ID
   */
  getCurrentFont(): string;
  
  /**
   * 设置字体变更回调
   * @param callback 回调函数
   */
  onFontChange(callback: (font: string) => void): void;
}
