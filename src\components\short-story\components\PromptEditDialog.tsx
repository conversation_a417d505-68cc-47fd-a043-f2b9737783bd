"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { CategoryPrompt } from '../../../types/ai-persona';

interface PromptEditDialogProps {
  isOpen: boolean;
  prompt: CategoryPrompt | null;
  onClose: () => void;
  onSave: (updatedPrompt: CategoryPrompt) => void;
}

/**
 * 提示词编辑弹窗组件
 * 提供独立的弹窗界面来编辑提示词的名称和内容
 */
const PromptEditDialog: React.FC<PromptEditDialogProps> = ({
  isOpen,
  prompt,
  onClose,
  onSave
}) => {
  const [editedName, setEditedName] = useState('');
  const [editedContent, setEditedContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化编辑内容
  useEffect(() => {
    if (prompt) {
      setEditedName(prompt.name || '');
      setEditedContent(prompt.content || '');
    }
  }, [prompt]);

  // 处理保存
  const handleSave = async () => {
    if (!prompt || !editedContent.trim()) return;

    setIsSubmitting(true);
    try {
      const updatedPrompt: CategoryPrompt = {
        ...prompt,
        name: editedName.trim() || undefined,
        content: editedContent.trim(),
        updatedAt: new Date()
      };

      onSave(updatedPrompt);
      onClose();
    } catch (error) {
      console.error('保存提示词失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    if (prompt) {
      setEditedName(prompt.name || '');
      setEditedContent(prompt.content || '');
    }
    onClose();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    }
  };

  if (!isOpen || !prompt) return null;

  return createPortal(
    <div className="fixed inset-0 z-[10001] flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <motion.div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleCancel}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />
      
      {/* 弹窗内容 */}
      <motion.div 
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-2xl flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              编辑提示词
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              修改提示词的名称和内容
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 编辑表单 */}
        <div className="p-6 space-y-6">
          {/* 提示词名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              提示词名称（可选）
            </label>
            <input
              type="text"
              value={editedName}
              onChange={(e) => setEditedName(e.target.value)}
              placeholder="输入提示词名称，便于识别和管理..."
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
              autoFocus
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              如果不填写名称，将使用内容的前20个字符作为显示名称
            </p>
          </div>

          {/* 提示词内容 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              提示词内容 *
            </label>
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              placeholder="输入提示词的具体内容..."
              rows={8}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 resize-none"
            />
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                支持 Ctrl+Enter 快速保存，Esc 取消编辑
              </p>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {editedContent.length} 字符
              </span>
            </div>
          </div>

          {/* 预览区域 */}
          {editedContent.trim() && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                显示预览
              </label>
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                    {editedName.trim() || (editedContent.length > 20 ? editedContent.substring(0, 20) + '...' : editedContent)}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                    {editedContent.length > 100 ? editedContent.substring(0, 100) + '...' : editedContent}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            创建时间: {new Date(prompt.createdAt).toLocaleString()}
            {prompt.updatedAt && (
              <span className="ml-4">
                最后更新: {new Date(prompt.updatedAt).toLocaleString()}
              </span>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 disabled:opacity-50 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={!editedContent.trim() || isSubmitting}
              className="px-6 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isSubmitting && (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
              <span>{isSubmitting ? '保存中...' : '保存更改'}</span>
            </button>
          </div>
        </div>
      </motion.div>
    </div>,
    document.body
  );
};

export default PromptEditDialog;
