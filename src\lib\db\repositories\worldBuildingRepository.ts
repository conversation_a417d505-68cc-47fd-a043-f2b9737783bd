import { v4 as uuidv4 } from 'uuid';
import { db, WorldBuilding } from '../dexie';

export interface IWorldBuildingRepository {
  getAllByBookId(bookId: string): Promise<WorldBuilding[]>;
  getById(id: string): Promise<WorldBuilding | undefined>;
  create(worldBuilding: Omit<WorldBuilding, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, worldBuilding: Partial<WorldBuilding>): Promise<void>;
  delete(id: string): Promise<void>;
  getByChapterId(chapterId: string): Promise<WorldBuilding[]>;
  linkToChapter(worldBuildingId: string, chapterId: string): Promise<void>;
  unlinkFromChapter(worldBuildingId: string, chapterId: string): Promise<void>;
}

export class WorldBuildingRepository implements IWorldBuildingRepository {
  async getAllByBookId(bookId: string): Promise<WorldBuilding[]> {
    try {
      return await db.worldBuilding
        .where('bookId')
        .equals(bookId)
        .toArray();
    } catch (error) {
      console.error('获取世界观数据失败:', error);
      return []; // 出错时返回空数组
    }
  }

  async getById(id: string): Promise<WorldBuilding | undefined> {
    return await db.worldBuilding.get(id);
  }

  async create(worldBuilding: Omit<WorldBuilding, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();

    await db.worldBuilding.add({
      ...worldBuilding,
      id,
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: worldBuilding.extractedFromChapterIds || [],
      relatedCharacterIds: worldBuilding.relatedCharacterIds || [],
      relatedTerminologyIds: worldBuilding.relatedTerminologyIds || [],
      relatedWorldBuildingIds: worldBuilding.relatedWorldBuildingIds || []
    });

    return id;
  }

  async update(id: string, worldBuilding: Partial<WorldBuilding>): Promise<void> {
    await db.worldBuilding.update(id, {
      ...worldBuilding,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    const worldBuilding = await db.worldBuilding.get(id);
    if (!worldBuilding) {
      throw new Error(`WorldBuilding with id ${id} not found`);
    }

    await db.transaction('rw', [db.worldBuilding, db.chapters, db.characters, db.terminology], async () => {
      // 从所有相关章节中移除该世界观元素的引用
      for (const chapterId of worldBuilding.extractedFromChapterIds) {
        const chapter = await db.chapters.get(chapterId);
        if (chapter) {
          const updatedWorldBuildingIds = chapter.worldBuildingIds.filter(wid => wid !== id);
          await db.chapters.update(chapterId, { worldBuildingIds: updatedWorldBuildingIds });
        }
      }

      // 从所有相关人物中移除该世界观元素的引用
      const relatedCharacters = await db.characters
        .where('relatedWorldBuildingIds')
        .anyOf([id])
        .toArray();

      for (const relatedCharacter of relatedCharacters) {
        const updatedRelatedWorldBuildingIds = relatedCharacter.relatedWorldBuildingIds.filter(wid => wid !== id);
        await db.characters.update(relatedCharacter.id!, { relatedWorldBuildingIds: updatedRelatedWorldBuildingIds });
      }

      // 从所有相关术语中移除该世界观元素的引用
      const relatedTerminologies = await db.terminology
        .where('relatedWorldBuildingIds')
        .anyOf([id])
        .toArray();

      for (const relatedTerminology of relatedTerminologies) {
        const updatedRelatedWorldBuildingIds = relatedTerminology.relatedWorldBuildingIds.filter(wid => wid !== id);
        await db.terminology.update(relatedTerminology.id!, { relatedWorldBuildingIds: updatedRelatedWorldBuildingIds });
      }

      // 从所有相关世界观元素中移除该世界观元素的引用
      const relatedWorldBuildings = await db.worldBuilding
        .where('relatedWorldBuildingIds')
        .anyOf([id])
        .toArray();

      for (const relatedWorldBuilding of relatedWorldBuildings) {
        const updatedRelatedWorldBuildingIds = relatedWorldBuilding.relatedWorldBuildingIds.filter(wid => wid !== id);
        await db.worldBuilding.update(relatedWorldBuilding.id!, { relatedWorldBuildingIds: updatedRelatedWorldBuildingIds });
      }

      // 删除世界观元素
      await db.worldBuilding.delete(id);
    });
  }

  async getByChapterId(chapterId: string): Promise<WorldBuilding[]> {
    const chapter = await db.chapters.get(chapterId);
    if (!chapter || !chapter.worldBuildingIds || chapter.worldBuildingIds.length === 0) {
      return [];
    }

    return await db.worldBuilding
      .where('id')
      .anyOf(chapter.worldBuildingIds)
      .toArray();
  }

  async linkToChapter(worldBuildingId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.worldBuilding, db.chapters], async () => {
      // 更新世界观元素的提取章节列表
      const worldBuilding = await db.worldBuilding.get(worldBuildingId);
      if (worldBuilding) {
        const extractedFromChapterIds = [...new Set([...worldBuilding.extractedFromChapterIds, chapterId])];
        await db.worldBuilding.update(worldBuildingId, { extractedFromChapterIds });
      }

      // 更新章节的世界观元素列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const worldBuildingIds = [...new Set([...chapter.worldBuildingIds, worldBuildingId])];
        await db.chapters.update(chapterId, { worldBuildingIds });
      }
    });
  }

  async unlinkFromChapter(worldBuildingId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.worldBuilding, db.chapters], async () => {
      // 更新世界观元素的提取章节列表
      const worldBuilding = await db.worldBuilding.get(worldBuildingId);
      if (worldBuilding) {
        const extractedFromChapterIds = worldBuilding.extractedFromChapterIds.filter(id => id !== chapterId);
        await db.worldBuilding.update(worldBuildingId, { extractedFromChapterIds });
      }

      // 更新章节的世界观元素列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const worldBuildingIds = chapter.worldBuildingIds.filter(id => id !== worldBuildingId);
        await db.chapters.update(chapterId, { worldBuildingIds });
      }
    });
  }
}

// 创建并导出仓库实例
export const worldBuildingRepository = new WorldBuildingRepository();
