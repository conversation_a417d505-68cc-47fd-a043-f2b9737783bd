"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { AssociationItem } from '@/hooks/useAssociationState';
import NodeMentionSelector from './NodeMentionSelector';
import ThinkingCanvasButton from '@/components/thinking-canvas/ThinkingCanvasButton';
import ThinkingCanvasDialog from '@/components/thinking-canvas/ThinkingCanvasDialog';
import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { ThinkingMode } from '@/components/outline/ThinkingModeSelector';
import ThinkingModeDialog from '@/components/outline/ThinkingModeDialog';
import WorkflowIndicator from '@/components/outline/WorkflowIndicator';
import ThinkingCanvasCard from '@/components/outline/ThinkingCanvasCard';
import StreamingThinkingCanvasCard from '@/components/outline/StreamingThinkingCanvasCard';
import { thinkingCanvasWorkflow, WorkflowState } from '@/services/thinking-canvas/ThinkingCanvasWorkflow';

import { ContextChain } from './ContextChainService';
import '../assistant/ChatInterface.css';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  mentionedNodes?: string[];
}

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (content: string, mentionedNodes?: string[], contextChains?: ContextChain[]) => void;
  isLoading: boolean;
  isStreaming?: boolean;
  outline: any;
  bookId: string; // 新增书籍ID参数
  selectedNodeId?: string | null;
  onNodeHighlight?: (nodeIds: string[]) => void;
  onTestConnection?: () => void;
  // 关联功能相关
  associationItems?: AssociationItem[];
  onOpenAssociationManager?: () => void;
  // 思考画布相关
  onSendThinkingMessage?: (content: string, mentionedNodes?: string[], contextChains?: ContextChain[]) => void;
}

/**
 * 聊天界面组件
 * 提供与AI助手的对话功能，支持@节点功能
 */
const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  isLoading,
  isStreaming = false,
  outline,
  bookId,
  selectedNodeId,
  onNodeHighlight,
  onTestConnection,
  associationItems = [],
  onOpenAssociationManager,
  onSendThinkingMessage
}) => {
  const [inputValue, setInputValue] = useState('');
  const [mentionedNodes, setMentionedNodes] = useState<string[]>([]);
  const [showMentionSelector, setShowMentionSelector] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [showSmartSuggestions, setShowSmartSuggestions] = useState(false);
  const [selectedNodeForSuggestions, setSelectedNodeForSuggestions] = useState<string | null>(null);
  const [selectedNodeTitle, setSelectedNodeTitle] = useState<string>(''); // 新增：缓存选中节点的标题
  const [cursorPosition, setCursorPosition] = useState(0);
  const [isMentionActive, setIsMentionActive] = useState(false);
  const [nodeTitleCache, setNodeTitleCache] = useState<Record<string, string>>({});


  // 上下文链路自动模式状态
  const [isContextChainEnabled, setIsContextChainEnabled] = useState(true); // 默认开启
  const [isLoadingContextChains, setIsLoadingContextChains] = useState(false);

  // 思考画布状态
  const [isThinkingCanvasOpen, setIsThinkingCanvasOpen] = useState(false);
  const [currentThinkingCanvas, setCurrentThinkingCanvas] = useState<ThinkingCanvasData | undefined>();

  // 思考模式状态
  const [thinkingMode, setThinkingMode] = useState<ThinkingMode>('standard');
  const [workflowState, setWorkflowState] = useState<WorkflowState | null>(null);
  const [showThinkingCanvas, setShowThinkingCanvas] = useState(false);
  const [showThinkingModeDialog, setShowThinkingModeDialog] = useState(false);

  // 功能开关状态
  const [isRhythmAnalysisEnabled, setIsRhythmAnalysisEnabled] = useState(() => {
    try {
      const saved = localStorage.getItem('rhythmAnalysisEnabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  });
  const [isDualAIEnabled, setIsDualAIEnabled] = useState(() => {
    try {
      const saved = localStorage.getItem('dualAIEnabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // 智能建议数据
  const smartSuggestions = [
    {
      id: 'expand',
      icon: '🌱',
      title: '扩展内容',
      description: '为这个节点添加更多详细内容',
      template: '请帮我扩展 @{node} 的内容，添加更多细节和描述',
      color: '#4caf50'
    },
    {
      id: 'optimize',
      icon: '⚙️',
      title: '优化结构',
      description: '改进节点的组织结构和逻辑',
      template: '请帮我优化 @{node} 的结构和逻辑安排',
      color: '#2196f3'
    },
    {
      id: 'relate',
      icon: '🔗',
      title: '关联分析',
      description: '分析与其他节点的关系',
      template: '请分析 @{node} 与其他章节的关联性，并提出改进建议',
      color: '#9c27b0'
    },
    {
      id: 'subdivide',
      icon: '📝',
      title: '细分章节',
      description: '将节点拆分为更小的部分',
      template: '请帮我将 @{node} 细分为几个子章节或场景',
      color: '#ff9800'
    }
  ];

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 监听工作流程状态变化
  useEffect(() => {
    const handleStateChange = (state: WorkflowState) => {
      setWorkflowState(state);
      setShowThinkingCanvas(!!state.thinkingCanvas);
    };

    thinkingCanvasWorkflow.addStateListener(handleStateChange);

    // 获取初始状态
    setWorkflowState(thinkingCanvasWorkflow.getState());

    return () => {
      thinkingCanvasWorkflow.removeStateListener(handleStateChange);
    };
  }, []);

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;

    setInputValue(value);
    setCursorPosition(cursorPos);

    // 检查是否输入了@符号 - 改进的检测逻辑
    const beforeCursor = value.substring(0, cursorPos);
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (atMatch && atMatch[0].length > 0) {
      // 只有在@符号后且光标在@区域内时才显示选择器
      setMentionQuery(atMatch[1]);
      setShowMentionSelector(true);
      setIsMentionActive(true);
    } else {
      // 用户删除了@符号或移动光标离开@区域，关闭选择器
      setShowMentionSelector(false);
      setMentionQuery('');
      setIsMentionActive(false);
    }
  }, []);

  // 处理光标位置变化（点击、键盘导航等）
  const handleSelectionChange = useCallback((e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    const cursorPos = target.selectionStart;
    const value = target.value;

    setCursorPosition(cursorPos);

    // 检查光标是否在@区域内
    const beforeCursor = value.substring(0, cursorPos);
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (atMatch && atMatch[0].length > 0) {
      // 光标在@区域内，显示选择器
      setMentionQuery(atMatch[1]);
      setShowMentionSelector(true);
      setIsMentionActive(true);
    } else {
      // 光标不在@区域内，关闭选择器
      setShowMentionSelector(false);
      setMentionQuery('');
      setIsMentionActive(false);
    }
  }, []);

  // 处理节点选择
  const handleNodeSelect = useCallback((nodeId: string, nodeTitle: string) => {
    const beforeCursor = inputValue.substring(0, cursorPosition);
    const afterCursor = inputValue.substring(cursorPosition);

    // 找到@符号的位置
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);
    if (atMatch) {
      const atIndex = beforeCursor.lastIndexOf('@');
      const newValue =
        inputValue.substring(0, atIndex) +
        `@${nodeTitle} ` +
        afterCursor;

      setInputValue(newValue);
      setMentionedNodes(prev => [...prev, nodeId]);
      setShowMentionSelector(false);
      setMentionQuery('');
      setIsMentionActive(false);

      // 预加载节点标题到缓存
      preloadNodeTitle(nodeId);

      // 触发节点高亮
      if (onNodeHighlight) {
        onNodeHighlight([nodeId]);
      }

      // 显示智能建议并缓存节点标题
      setSelectedNodeForSuggestions(nodeId);
      setSelectedNodeTitle(nodeTitle); // 新增：缓存选中的节点标题
      setShowSmartSuggestions(true);

      // 重新聚焦输入框
      setTimeout(() => {
        inputRef.current?.focus();
        const newCursorPos = atIndex + nodeTitle.length + 2;
        inputRef.current?.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    }
  }, [inputValue, cursorPosition]);

  // 处理智能建议选择
  const handleSuggestionSelect = useCallback((suggestion: any) => {
    if (!selectedNodeForSuggestions) return;

    // 使用缓存的标题，而不是查找大纲节点
    const nodeTitle = selectedNodeTitle || '未知节点';
    const message = suggestion.template.replace('{node}', nodeTitle);

    setInputValue(message);

    // 重要：更新mentionedNodes状态，确保AI能收到正确的节点ID
    setMentionedNodes([selectedNodeForSuggestions]);

    // 预加载节点标题到缓存
    preloadNodeTitle(selectedNodeForSuggestions);

    setShowSmartSuggestions(false);
    setSelectedNodeForSuggestions(null);
    setSelectedNodeTitle(''); // 清理缓存的标题

    // 聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  }, [selectedNodeForSuggestions, selectedNodeTitle]);

  // 处理发送消息
  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading || isStreaming) return;

    // 自动添加关联内容到@节点
    let finalMentionedNodes = [...mentionedNodes];

    // 如果有关联内容，自动添加到消息中
    if (associationItems && associationItems.length > 0) {
      const associationNodeIds = associationItems.map(item => item.id);
      // 合并现有的@节点和关联内容，去重
      finalMentionedNodes = [...new Set([...mentionedNodes, ...associationNodeIds])];
      console.log('🔗 自动添加关联内容到消息:', {
        原有节点: mentionedNodes,
        关联内容: associationNodeIds,
        最终节点: finalMentionedNodes
      });
    }

    // 根据思考模式选择不同的处理方式
    if (thinkingMode === 'thinking') {
      // 思考模式：使用思考画布工作流程
      if (onSendThinkingMessage) {
        onSendThinkingMessage(inputValue.trim(), finalMentionedNodes);
      } else {
        console.warn('思考模式需要 onSendThinkingMessage 回调');
        // 降级到标准模式
        await handleStandardMessage(finalMentionedNodes);
      }
    } else {
      // 标准模式：直接发送消息
      await handleStandardMessage(finalMentionedNodes);
    }

    // 清理状态
    setInputValue('');
    setMentionedNodes([]);
    setShowMentionSelector(false);
    setMentionQuery('');
    setShowSmartSuggestions(false);
    setSelectedNodeForSuggestions(null);
    setSelectedNodeTitle(''); // 清理缓存的标题
  }, [inputValue, mentionedNodes, isLoading, isStreaming, thinkingMode, onSendThinkingMessage, associationItems]);

  // 处理标准模式消息发送
  const handleStandardMessage = useCallback(async (finalMentionedNodes: string[]) => {
    // 如果有@节点且启用了上下文链路，自动构建链路
    if (finalMentionedNodes.length > 0 && isContextChainEnabled) {
      console.log('🔗 检测到@节点，自动构建上下文链路');
      setIsLoadingContextChains(true);

      try {
        // 导入ContextChainService
        const { ContextChainService } = await import('./ContextChainService');
        const contextService = new ContextChainService(outline);

        // 自动构建上下文链路（使用第一个@节点作为主节点）
        const contextChains = await contextService.getNodeContextChains(finalMentionedNodes[0], {
          includeHierarchy: true,
          includeSequence: true,
          maxNodesPerChain: 10,
          priorityThreshold: 0.5
        });

        console.log('🔗 自动构建的上下文链路:', contextChains);

        // 发送消息，包含上下文链路
        onSendMessage(inputValue.trim(), finalMentionedNodes, contextChains);
      } catch (error) {
        console.error('❌ 构建上下文链路失败:', error);
        // 失败时发送普通消息
        onSendMessage(inputValue.trim(), finalMentionedNodes);
      } finally {
        setIsLoadingContextChains(false);
      }
    } else {
      // 没有@节点或未启用链路，直接发送消息
      onSendMessage(inputValue.trim(), finalMentionedNodes);
    }
  }, [inputValue, isContextChainEnabled, outline, onSendMessage]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }

    if (e.key === 'Escape') {
      setShowMentionSelector(false);
      setMentionQuery('');
      setIsMentionActive(false);
    }

    // 处理Backspace键 - 如果删除了@符号，立即关闭选择器
    if (e.key === 'Backspace' && showMentionSelector) {
      const target = e.target as HTMLTextAreaElement;
      const cursorPos = target.selectionStart;
      const value = target.value;

      // 检查是否会删除@符号
      const beforeCursor = value.substring(0, cursorPos);
      const atMatch = beforeCursor.match(/@([^@\s]*)$/);

      if (atMatch && cursorPos === beforeCursor.length && atMatch[1] === '') {
        // 用户在@符号后按Backspace，会删除@符号
        setTimeout(() => {
          setShowMentionSelector(false);
          setMentionQuery('');
          setIsMentionActive(false);
        }, 0);
      }
    }

    // 处理空格键 - 输入空格后关闭选择器
    if (e.key === ' ' && showMentionSelector) {
      setTimeout(() => {
        setShowMentionSelector(false);
        setMentionQuery('');
        setIsMentionActive(false);
      }, 0);
    }
  }, [handleSendMessage, showMentionSelector]);

  // 格式化时间
  const formatTime = useCallback((date: Date) => {
    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效时间';

    return validDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  // 获取节点标题 - 增强版本，支持大纲节点和数据库内容
  const getNodeTitle = useCallback(async (nodeId: string): Promise<string> => {
    // 首先在大纲节点中查找
    const findNode = (nodes: any[]): string | null => {
      for (const node of nodes) {
        if (node.id === nodeId) return node.title;
        if (node.children) {
          const found = findNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const outlineTitle = findNode(outline?.nodes || []);
    if (outlineTitle) return outlineTitle;

    // 如果在大纲中找不到，尝试在数据库中查找
    try {
      const { db } = await import('../../../../../lib/db/dexie');

      // 查找章节
      const chapter = await db.chapters.get(nodeId);
      if (chapter) return chapter.title;

      // 查找人物
      const character = await db.characters.get(nodeId);
      if (character) return character.name;

      // 查找术语
      const terminology = await db.terminology.get(nodeId);
      if (terminology) return terminology.name;

      // 查找世界观
      const worldBuilding = await db.worldBuilding.get(nodeId);
      if (worldBuilding) return worldBuilding.name;

    } catch (error) {
      console.error('查询节点标题失败:', error);
    }

    return '未知节点';
  }, [outline]);

  // 同步版本的getNodeTitle，用于渲染，优先使用缓存
  const getNodeTitleSync = useCallback((nodeId: string) => {
    // 首先检查缓存
    if (nodeTitleCache[nodeId]) {
      return nodeTitleCache[nodeId];
    }

    // 然后在大纲节点中查找
    const findNode = (nodes: any[]): string | null => {
      for (const node of nodes) {
        if (node.id === nodeId) return node.title;
        if (node.children) {
          const found = findNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const title = findNode(outline?.nodes || []);
    if (title) {
      // 缓存找到的标题
      setNodeTitleCache(prev => ({ ...prev, [nodeId]: title }));
      return title;
    }

    return '未知节点';
  }, [outline, nodeTitleCache]);

  // 预加载节点标题到缓存
  const preloadNodeTitle = useCallback(async (nodeId: string) => {
    if (nodeTitleCache[nodeId]) return; // 已经缓存了

    try {
      const title = await getNodeTitle(nodeId);
      setNodeTitleCache(prev => ({ ...prev, [nodeId]: title }));
    } catch (error) {
      console.error('预加载节点标题失败:', error);
    }
  }, [nodeTitleCache, getNodeTitle]);

  // 处理@节点标签点击
  const handleMentionClick = useCallback((nodeId: string, event?: React.MouseEvent) => {
    if (onNodeHighlight) {
      // 如果按住Ctrl键，高亮所有@的节点
      if (event?.ctrlKey || event?.metaKey) {
        const allNodeIds = Array.from(new Set(messages.flatMap(msg => msg.mentionedNodes || [])));
        onNodeHighlight(allNodeIds);
      } else {
        onNodeHighlight([nodeId]);
      }
    }
  }, [onNodeHighlight, messages]);

  // 处理批量高亮
  const handleBatchHighlight = useCallback((nodeIds: string[]) => {
    if (onNodeHighlight) {
      onNodeHighlight(nodeIds);
    }
  }, [onNodeHighlight]);



  // 处理上下文链路开关切换
  const handleContextChainToggle = useCallback(() => {
    setIsContextChainEnabled(prev => {
      const newValue = !prev;
      console.log('🔗 上下文链路开关:', newValue ? '开启' : '关闭');
      return newValue;
    });
  }, []);

  // 思考画布处理函数
  const handleOpenThinkingCanvas = useCallback(() => {
    console.log('🎨 打开思考画布');
    setIsThinkingCanvasOpen(true);
  }, []);

  const handleCloseThinkingCanvas = useCallback(() => {
    setIsThinkingCanvasOpen(false);
    setCurrentThinkingCanvas(undefined);
  }, []);

  const handleSaveThinkingCanvas = useCallback((data: ThinkingCanvasData) => {
    setCurrentThinkingCanvas(data);
    console.log('思考画布已保存:', data);
  }, []);

  // 处理思考画布编辑
  const handleEditThinkingCanvas = useCallback((canvas: ThinkingCanvasData) => {
    // 设置当前思考画布数据并打开弹窗
    setCurrentThinkingCanvas(canvas);
    setIsThinkingCanvasOpen(true);
  }, []);

  // 处理思考画布删除
  const handleDeleteThinkingCanvas = useCallback((canvasId: string) => {
    if (workflowState?.thinkingCanvas?.id === canvasId) {
      thinkingCanvasWorkflow.reset();
    }
  }, [workflowState]);

  // 处理模式切换
  const handleModeChange = useCallback((mode: ThinkingMode) => {
    setThinkingMode(mode);
    if (mode === 'standard') {
      // 切换到标准模式时重置工作流程
      thinkingCanvasWorkflow.reset();
    }
  }, []);

  // 处理思考模式按钮点击
  const handleThinkingModeButtonClick = useCallback(() => {
    setShowThinkingModeDialog(true);
  }, []);

  // 处理思考模式弹窗关闭
  const handleThinkingModeDialogClose = useCallback(() => {
    setShowThinkingModeDialog(false);
  }, []);

  // 处理思考模式选择
  const handleThinkingModeSelect = useCallback((mode: ThinkingMode) => {
    handleModeChange(mode);
    setShowThinkingModeDialog(false);
  }, [handleModeChange]);

  // 处理节奏分析开关
  const handleRhythmAnalysisToggle = useCallback(() => {
    const newState = !isRhythmAnalysisEnabled;
    setIsRhythmAnalysisEnabled(newState);
    console.log('节奏分析模式:', newState ? '已启用' : '已关闭');

    // TODO: 这里可以添加保存到localStorage的逻辑
    localStorage.setItem('rhythmAnalysisEnabled', JSON.stringify(newState));
  }, [isRhythmAnalysisEnabled]);

  // 处理双AI开关
  const handleDualAIToggle = useCallback(() => {
    const newState = !isDualAIEnabled;
    setIsDualAIEnabled(newState);
    console.log('双AI模式:', newState ? '已启用' : '已关闭');

    // TODO: 这里可以添加保存到localStorage的逻辑
    localStorage.setItem('dualAIEnabled', JSON.stringify(newState));
  }, [isDualAIEnabled]);

  return (
    <div className="chat-interface">
      {/* 功能控制按钮组 */}
      <div className="mb-4 space-y-3">
        {/* 思考模式按钮 */}
        <div className="flex items-center justify-between">
          <button
            onClick={handleThinkingModeButtonClick}
            disabled={isLoading || isStreaming}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
              thinkingMode === 'thinking'
                ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'
                : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
            } ${isLoading || isStreaming ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <span className="text-lg">
              {thinkingMode === 'thinking' ? '💭' : '⚡'}
            </span>
            <span className="font-medium">
              {thinkingMode === 'thinking' ? '思考模式' : '标准模式'}
            </span>
            <span className="text-sm opacity-75">
              {thinkingMode === 'thinking' ? '深度分析创作' : '快速直接生成'}
            </span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="opacity-50">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>
        </div>

        {/* 功能开关按钮组 */}
        <div className="flex items-center gap-3">
          {/* 节奏分析开关 */}
          <button
            onClick={handleRhythmAnalysisToggle}
            disabled={isLoading || isStreaming}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-md border text-sm transition-colors ${
              isRhythmAnalysisEnabled
                ? 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
                : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
            } ${isLoading || isStreaming ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            title={isRhythmAnalysisEnabled ? '点击关闭节奏分析' : '点击启用节奏分析'}
          >
            <span className="text-sm">🎵</span>
            <span className="font-medium">节奏分析</span>
            {isRhythmAnalysisEnabled && (
              <span className="text-xs bg-orange-200 text-orange-800 px-1 rounded">ON</span>
            )}
          </button>

          {/* 双AI开关 */}
          <button
            onClick={handleDualAIToggle}
            disabled={isLoading || isStreaming}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-md border text-sm transition-colors ${
              isDualAIEnabled
                ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100'
                : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
            } ${isLoading || isStreaming ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            title={isDualAIEnabled ? '点击关闭双AI模式' : '点击启用双AI模式'}
          >
            <span className="text-sm">🤖</span>
            <span className="font-medium">双AI</span>
            {isDualAIEnabled && (
              <span className="text-xs bg-purple-200 text-purple-800 px-1 rounded">ON</span>
            )}
          </button>
        </div>
      </div>

      {/* 工作流程指示器 - 仅在思考模式下显示 */}
      {thinkingMode === 'thinking' && workflowState && (
        <div className="mb-4">
          <WorkflowIndicator
            currentStep={workflowState.currentStep}
            isProcessing={workflowState.isProcessing}
            onStepClick={(step) => {
              // 可以添加步骤点击处理逻辑
              console.log('点击工作流程步骤:', step);
            }}
          />
        </div>
      )}

      {/* 流式思考画布卡片 - 在流式生成时显示 */}
      {thinkingMode === 'thinking' && workflowState?.isStreaming && (
        <div className="mb-4">
          <StreamingThinkingCanvasCard
            content={workflowState.streamingContent || ''}
            isStreaming={workflowState.isStreaming}
            title="AI深度思考中"
            onEdit={() => {
              // 流式完成后才能编辑
              if (workflowState.thinkingCanvas) {
                handleEditThinkingCanvas(workflowState.thinkingCanvas);
              }
            }}
          />
        </div>
      )}

      {/* 思考画布卡片 - 仅在有完成的思考画布时显示 */}
      {showThinkingCanvas && workflowState?.thinkingCanvas && !workflowState?.isStreaming && (
        <div className="mb-4">
          <ThinkingCanvasCard
            canvas={workflowState.thinkingCanvas}
            isExpanded={true}
            onEdit={handleEditThinkingCanvas}
            onSave={handleSaveThinkingCanvas}
            onDelete={handleDeleteThinkingCanvas}
          />
        </div>
      )}

      {/* 消息列表 */}
      <div className="chat-messages">
        {messages.length === 0 && (
          <div className="chat-welcome">
            <div className="welcome-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
              </svg>
            </div>
            <h3>欢迎使用AI助手</h3>
            <p>我可以帮助您编辑大纲、扩展章节、优化结构。</p>
            <div className="welcome-tips">
              <div className="tip">
                <span className="tip-icon">@</span>
                <span>使用 @ 符号提及节点</span>
              </div>
              <div className="tip">
                <span className="tip-icon">💡</span>
                <span>尝试说"扩展这个章节"</span>
              </div>
              {onTestConnection && (
                <div className="tip">
                  <span className="tip-icon">🔧</span>
                  <span>
                    遇到问题？
                    <button
                      onClick={onTestConnection}
                      className="test-connection-btn"
                      style={{
                        marginLeft: '8px',
                        padding: '4px 8px',
                        fontSize: '12px',
                        background: '#667eea',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                      }}
                    >
                      测试AI连接
                    </button>
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className={`chat-message ${message.type}`}>
            <div className="message-avatar">
              {message.type === 'user' ? (
                <div className="user-avatar">
                  <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                    {/* 用户头像背景 - 温暖的渐变 */}
                    <circle cx="12" cy="12" r="11" fill="url(#userGradient)" stroke="rgba(255,255,255,0.4)" strokeWidth="0.5"/>

                    {/* 可爱的小皇冠装饰 */}
                    <path d="M8 6 L10 4 L12 6 L14 4 L16 6 L16 8 L8 8 Z" fill="url(#crownGradient)" className="user-crown"/>
                    <circle cx="12" cy="5" r="1" fill="#FFD700" className="crown-jewel"/>

                    {/* 用户头像 - 友好的人物形象 */}
                    <circle cx="12" cy="11" r="3" fill="#FFF" opacity="0.9"/>
                    <circle cx="10.5" cy="10" r="0.8" fill="#FF6B9D"/>
                    <circle cx="13.5" cy="10" r="0.8" fill="#FF6B9D"/>
                    <path d="M10 12.5 Q12 14 14 12.5" stroke="#FF6B9D" strokeWidth="1.2" strokeLinecap="round" fill="none"/>

                    {/* 用户身体 */}
                    <rect x="9" y="15" width="6" height="5" rx="3" fill="#FFF" opacity="0.8"/>

                    {/* 渐变定义 */}
                    <defs>
                      <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#FF9A9E"/>
                        <stop offset="50%" stopColor="#FECFEF"/>
                        <stop offset="100%" stopColor="#FECFEF"/>
                      </linearGradient>
                      <linearGradient id="crownGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#FFD700"/>
                        <stop offset="100%" stopColor="#FFA500"/>
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              ) : (
                <div className={`assistant-avatar ${isLoading ? 'thinking' : ''} ${isStreaming ? 'responding' : ''}`}>
                  <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                    {/* 机器人头部 - 使用渐变填充 */}
                    <circle cx="12" cy="13" r="9" fill="url(#robotGradient)" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5"/>

                    {/* 可爱的小帽子 */}
                    <ellipse cx="12" cy="5" rx="4" ry="1.5" fill="url(#hatGradient)" className="robot-hat"/>
                    <rect x="10" y="3.5" width="4" height="2" rx="2" fill="url(#hatGradient)" className="robot-hat"/>

                    {/* 机器人眼睛 - 使用亮色 */}
                    <circle cx="9" cy="11" r="1.8" fill="#00E5FF" className="robot-eye"/>
                    <circle cx="15" cy="11" r="1.8" fill="#00E5FF" className="robot-eye"/>

                    {/* 机器人嘴巴 - 友好的微笑 */}
                    <path d="M9 15.5 Q12 17.5 15 15.5" stroke="#4FC3F7" strokeWidth="1.5" strokeLinecap="round" fill="none"/>

                    {/* 渐变定义 */}
                    <defs>
                      <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#667eea"/>
                        <stop offset="50%" stopColor="#764ba2"/>
                        <stop offset="100%" stopColor="#5a67d8"/>
                      </linearGradient>
                      <linearGradient id="hatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#FF6B6B"/>
                        <stop offset="100%" stopColor="#FF8E53"/>
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              )}
            </div>

            <div className="message-content">
              <div className={`message-text ${message.type === 'assistant' && isStreaming && message.content ? 'streaming' : ''}`}>
                {message.content}
                {message.type === 'assistant' && isStreaming && message.content && (
                  <span className="typing-cursor">|</span>
                )}
              </div>

              {message.mentionedNodes && message.mentionedNodes.length > 0 && (
                <div
                  className="mentioned-nodes"
                  data-count={message.mentionedNodes.length > 1 ? message.mentionedNodes.length : undefined}
                >
                  {message.mentionedNodes.map((nodeId, index) => (
                    <span
                      key={nodeId}
                      className="mentioned-node"
                      onClick={(e) => handleMentionClick(nodeId, e)}
                      title={`点击高亮节点 (Ctrl+点击高亮所有节点)`}
                      style={{
                        animationDelay: `${index * 0.1}s`,
                      }}
                    >
                      @{getNodeTitleSync(nodeId)}
                    </span>
                  ))}
                  {message.mentionedNodes.length > 1 && (
                    <button
                      className="batch-highlight-btn"
                      onClick={() => handleBatchHighlight(message.mentionedNodes!)}
                      title="高亮所有节点"
                      aria-label="高亮所有节点"
                    >
                      ⚡
                    </button>
                  )}
                </div>
              )}

              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="chat-message assistant">
            <div className="message-avatar">
              <div className="assistant-avatar thinking">
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                  {/* 机器人头部 - 使用渐变填充 */}
                  <circle cx="12" cy="13" r="9" fill="url(#robotGradient)" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5"/>

                  {/* 可爱的小帽子 - 思考时会摇摆 */}
                  <ellipse cx="12" cy="5" rx="4" ry="1.5" fill="url(#hatGradient)" className="robot-hat"/>
                  <rect x="10" y="3.5" width="4" height="2" rx="2" fill="url(#hatGradient)" className="robot-hat"/>

                  {/* 机器人眼睛 - 思考状态使用紫色 */}
                  <circle cx="9" cy="11" r="1.8" fill="#9C27B0" className="robot-eye"/>
                  <circle cx="15" cy="11" r="1.8" fill="#9C27B0" className="robot-eye"/>

                  {/* 机器人嘴巴 - 思考时的表情 */}
                  <path d="M9 15.5 Q12 16 15 15.5" stroke="#BA68C8" strokeWidth="1.5" strokeLinecap="round" fill="none"/>

                  {/* 渐变定义 */}
                  <defs>
                    <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#667eea"/>
                      <stop offset="50%" stopColor="#764ba2"/>
                      <stop offset="100%" stopColor="#5a67d8"/>
                    </linearGradient>
                    <linearGradient id="hatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#FF6B6B"/>
                      <stop offset="100%" stopColor="#FF8E53"/>
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="chat-input-container">
        {showMentionSelector && (
          <NodeMentionSelector
            outline={outline}
            bookId={bookId}
            query={mentionQuery}
            onSelect={handleNodeSelect}
            onClose={() => setShowMentionSelector(false)}
          />
        )}

        {/* 智能建议 */}
        {showSmartSuggestions && selectedNodeForSuggestions && (
          <div className="smart-suggestions">
            <div className="suggestions-header">
              <span className="suggestions-title">💡 智能建议</span>
              <button
                className="suggestions-close"
                onClick={() => {
                  setShowSmartSuggestions(false);
                  setSelectedNodeForSuggestions(null);
                  setSelectedNodeTitle(''); // 清理缓存的标题
                }}
              >
                ×
              </button>
            </div>
            <div className="suggestions-list">
              {smartSuggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className="suggestion-item"
                  onClick={() => handleSuggestionSelect(suggestion)}
                  style={{ borderLeftColor: suggestion.color }}
                >
                  <div className="suggestion-icon">{suggestion.icon}</div>
                  <div className="suggestion-content">
                    <div className="suggestion-title">{suggestion.title}</div>
                    <div className="suggestion-description">{suggestion.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="chat-input">
          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onSelect={handleSelectionChange}
            onClick={handleSelectionChange}
            onKeyUp={handleSelectionChange}
            placeholder="输入消息... (使用 @ 提及节点)"
            disabled={isLoading}
            rows={1}
            className={isMentionActive ? 'mention-active' : ''}
          />

          {/* 上下文链路开关按钮 */}
          <button
            className={`context-chain-toggle ${isContextChainEnabled ? 'enabled' : 'disabled'}`}
            onClick={handleContextChainToggle}
            title={isContextChainEnabled
              ? "上下文链路已开启 - 点击关闭"
              : "上下文链路已关闭 - 点击开启"}
            aria-label={isContextChainEnabled ? "关闭上下文链路" : "开启上下文链路"}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              {isContextChainEnabled ? (
                // 开启状态：链条图标
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              ) : (
                // 关闭状态：断开的链条图标
                <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
              )}
            </svg>
            {isLoadingContextChains && (
              <div className="loading-indicator">
                <div className="spinner"></div>
              </div>
            )}
          </button>



          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading || isStreaming}
            className={`send-button ${isStreaming ? 'streaming' : ''}`}
            aria-label={isStreaming ? "AI正在思考..." : "发送消息"}
          >
            {isStreaming ? (
              <div className="streaming-indicator">
                <div className="dot"></div>
                <div className="dot"></div>
                <div className="dot"></div>
              </div>
            ) : (
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            )}
          </button>
        </div>

        {/* 底部工具栏 - 思考画布按钮 */}
        <div className="chat-bottom-toolbar">
          <ThinkingCanvasButton
            onClick={handleOpenThinkingCanvas}
            variant="compact"
            className="ml-auto"
          />
        </div>
      </div>

      {/* 思考画布弹窗 */}
      <ThinkingCanvasDialog
        isOpen={isThinkingCanvasOpen}
        onClose={handleCloseThinkingCanvas}
        initialData={currentThinkingCanvas}
        onSave={handleSaveThinkingCanvas}
      />

      {/* 思考模式选择弹窗 */}
      <ThinkingModeDialog
        isOpen={showThinkingModeDialog}
        onClose={handleThinkingModeDialogClose}
        onModeSelect={handleThinkingModeSelect}
        currentMode={thinkingMode}
      />
    </div>
  );
};

export default ChatInterface;
