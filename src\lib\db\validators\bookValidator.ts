import { Book } from '../dexie';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateBook(book: Partial<Book>): ValidationResult {
  const errors: Record<string, string> = {};
  
  // 验证标题
  if (!book.title) {
    errors.title = '标题不能为空';
  } else if (book.title.length > 100) {
    errors.title = '标题不能超过100个字符';
  }
  
  // 验证描述
  if (!book.description) {
    errors.description = '描述不能为空';
  }
  
  // 验证作者
  if (!book.author) {
    errors.author = '作者不能为空';
  }
  
  // 验证设置
  if (book.settings) {
    // 验证字体大小
    if (book.settings.fontSize && (book.settings.fontSize < 8 || book.settings.fontSize > 36)) {
      errors['settings.fontSize'] = '字体大小必须在8到36之间';
    }
    
    // 验证行高
    if (book.settings.lineHeight && (book.settings.lineHeight < 1 || book.settings.lineHeight > 3)) {
      errors['settings.lineHeight'] = '行高必须在1到3之间';
    }
    
    // 验证自动保存间隔
    if (book.settings.autoSave && book.settings.autoSaveInterval < 1000) {
      errors['settings.autoSaveInterval'] = '自动保存间隔不能小于1000毫秒';
    }
  } else if (book.id === undefined) {
    // 只在创建新书籍时验证设置是否存在
    errors.settings = '设置不能为空';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
