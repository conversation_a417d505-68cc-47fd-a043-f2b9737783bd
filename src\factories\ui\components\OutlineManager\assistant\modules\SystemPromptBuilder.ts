/**
 * 系统提示词构建模块
 * 负责构建完整的系统提示词和工作原则
 */

import { ConversationMessage } from '../types/SharedTypes';
import { RhythmAnalysisIntegrator } from './RhythmAnalysisIntegrator';

export class SystemPromptBuilder {
  private static instance: SystemPromptBuilder;
  private rhythmAnalysisIntegrator: RhythmAnalysisIntegrator;

  private constructor() {
    this.rhythmAnalysisIntegrator = RhythmAnalysisIntegrator.getInstance();
  }

  /**
   * 检查是否启用双AI模式
   */
  private isDualAIMode(): boolean {
    try {
      // 🔥 修复：使用正确的DualAIConfigManager路径（7层向上）
      const { DualAIConfigManager } = require('../../../../../../utils/ai/DualAIConfigManager');
      const dualAIConfig = DualAIConfigManager.load();
      return dualAIConfig && dualAIConfig.mode === 'dual';
    } catch (error) {
      console.warn('无法检查双AI模式状态:', error);
      return false;
    }
  }

  public static getInstance(): SystemPromptBuilder {
    if (!SystemPromptBuilder.instance) {
      SystemPromptBuilder.instance = new SystemPromptBuilder();
    }
    return SystemPromptBuilder.instance;
  }

  /**
   * 构建完整的系统提示词
   */
  buildCompleteSystemPrompt(): string {
    return `
${this.rhythmAnalysisIntegrator.build30ChapterRhythmWisdom()}

**响应格式要求**：
- 必须返回有效的JSON格式
- 包含用户可见的回复消息
- 包含具体的节点操作指令
- 操作类型包括：create（创建）、update（更新）、delete（删除）

**🎯 节点创建指南**：
根据新的四层级架构创建节点，每种类型都有专门的字段要求：

**JSON响应结构要求**：
- 必须包含message、changes、metadata字段
- 每个节点必须包含所有基础字段：nodeId、title、type、description、creativeNotes
- 根据节点类型填写对应的专门字段
- parentId必须正确指向父节点

${this.buildVolumeNodeTemplate()}

${this.buildChapterNodeTemplate()}

${this.buildPlotNodeTemplate()}

${this.buildDialogueNodeTemplate()}

${this.buildPlotEnhancementPrinciples()}

${this.buildProhibitedBehaviors()}

注意每次的创建，涉及到章节的创建，必须带有对话设计，剧情节点的必须创建，不能只创建章节节点，其他的都不创建
`;
  }

  /**
   * 构建总纲/卷节点模板
   */
  private buildVolumeNodeTemplate(): string {
    return `**📚 总纲/卷节点创建模板**：
\`\`\`json
{
  "message": "[分析总纲在整体作品中的作用和规划意义，结合30章节奏规划提供具体指导]",
  "changes": [
    {
      "type": "create",
      "nodeId": "volume_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的总纲/卷标题]",
        "type": "volume",
        "description": "[总纲的整体规划和核心内容概述，包含主线剧情发展脉络]",
        "creativeNotes": "[总纲的创作指导和整体规划要点，强调商业价值和读者体验]",
        "volumeTheme": "[卷主题：核心主题和故事弧线描述，如成长逆袭、权力争夺、情感救赎等]",
        "volumeArc": "[卷弧线：整体故事发展轨迹和关键转折点，包含起承转合的完整结构]",
        "chapterCount": "[预期章节数：建议20-40章，根据故事复杂度和商业需求调整]",
        "cycleTemplate": "[循环法模板：如4章循环（铺垫-冲突-爽点-过渡）、自定义循环等微节奏策略]",
        "targetWordCount": "[目标字数：本卷预期总字数，如50-100万字，考虑平台要求和读者习惯]",
        "rhythmStrategy": "[节奏策略：整体节奏控制规划，包括张弛有度的安排和爽点分布]",
        "cyclePhases": [
          "[阶段1：开篇建立规划 - 世界观建立、角色介绍、背景铺陈，占比20-25%]",
          "[阶段2：冲突发展规划 - 矛盾引入、问题升级、紧张感营造，占比30-35%]",
          "[阶段3：高潮爽点规划 - 问题解决、能力展现、成就感释放，占比25-30%]",
          "[阶段4：过渡转折规划 - 承上启下、情绪缓解、新循环准备，占比15-20%]"
        ],
        "commercialElements": "[商业要素：爽点设计、冲突安排、悬念布局等商业化考虑]",
        "readerEngagement": "[读者粘性：如何保持读者兴趣、提高留存率、促进付费转化]"
      },
      "parentId": null
    }
  ],
  "metadata": {
    "operationType": "create",
    "affectedNodes": [],
    "confidence": 0.95,
    "rhythmAnalysis": true,
    "commercialFocus": true
  }
}
\`\`\``;
  }

  /**
   * 构建章节节点模板
   */
  public buildChapterNodeTemplate(): string {
    const isDualAI = this.isDualAIMode();

    if (isDualAI) {
      // 双AI模式下的章节节点模板（包含节奏分析字段）
      return `**📖 章节节点创建模板（双AI模式）**：
\`\`\`json
{
  "message": "[基于节奏分析，为您设计了这个章节的结构框架]",
  "changes": [
    {
      "type": "create",
      "nodeId": "chapter_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的章节标题]",
        "type": "chapter",
        "description": "[章节的剧情概要和主要内容]",
        "creativeNotes": "[章节的创作指导和写作要点]",
        "chapterStyle": "[写作风格：如悬疑紧张、温馨治愈、激烈战斗等]",
        "chapterTechniques": ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
        "chapterGoals": "[章节要达成的目标和作用]",
        "rhythmPhase": "[节奏阶段：setup铺垫/conflict冲突/climax高潮/transition过渡]",
        "rhythmGuidance": "[基于当前节奏阶段的具体创作指导和建议]"
      },
      "parentId": "[父节点ID]"
    }
  ],
  "metadata": {
    "operationType": "create",
    "affectedNodes": ["[父节点ID]"],
    "confidence": 0.95,

  }
}
\`\`\``;
    } else {
      // 单AI模式下的章节节点模板
      return `**📖 章节节点创建模板**：
\`\`\`json
{
  "message": "[分析章节在整体剧情中的作用和意义]",
  "changes": [
    {
      "type": "create",
      "nodeId": "chapter_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的章节标题]",
        "type": "chapter",
        "description": "[章节的剧情概要和主要内容]",
        "creativeNotes": "[章节的创作指导和写作要点]",
        "chapterStyle": "[写作风格：如悬疑紧张、温馨治愈、激烈战斗等]",
        "chapterTechniques": ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
        "chapterGoals": "[章节要达成的目标和作用]"
      },
      "parentId": "[父节点ID]"
    }
  ],
  "metadata": {
    "operationType": "create",
    "affectedNodes": ["[父节点ID]"],
    "confidence": 0.95,

  }
}
\`\`\``;
    }
  }

  /**
   * 构建剧情节点模板
   */
  public buildPlotNodeTemplate(): string {
    const isDualAI = this.isDualAIMode();

    if (isDualAI) {
      // 双AI模式下的剧情节点模板（包含冲突强度和悬念要素）
      return `**🎬 剧情节点创建模板（双AI模式）**：
\`\`\`json
{
  "message": "[基于故事逻辑，为您设计了这个剧情节点的冲突结构]",
  "changes": [
    {
      "type": "create",
      "nodeId": "plot_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的剧情节点标题]",
        "type": "plot",
        "description": "[剧情节点的具体内容和发展过程]",
        "creativeNotes": "[剧情设计要点和创作指导]",
        "plotPoints": [
          {
            "id": "point_001",
            "content": "[角色名]进行[行动类型]：[具体行动描述和结果]",
            "writingGuidance": "🚫严禁使用'一丝xx'、'如同xx一样'、'宛如'、'犹如'、'仿佛'等模糊表达和比喻。专注具体行动：写'[角色]立即[具体动作]'而非'[角色]感到一丝不安'。避免比喻修辞，直接描述行为结果。使用ACE具体技巧：Action行动描写（角色做了什么具体动作）、Consequence结果展现（动作带来什么后果）、Emotion情绪通过行为体现（不写'感到'，写具体表现）。每个动作都要有明确目的和结果。"
          },
          {
            "id": "point_002",
            "content": "AA采取行动：[AA如何应对，具体的行动和决策]",
            "writingGuidance": "🚫严禁使用'一丝困惑'、'如同刀锋一样'等表达。专注具体行动：写'AA立即转身查看'而非'AA感到一丝不安'。避免比喻修辞，直接描述行为结果。禁用'宛如'、'犹如'、'仿佛'等比喻词。学习ACE框架技巧，用具体动作推进剧情，让读者通过行动理解情况，而非通过形容词感受氛围。每个动作都要有明确目的和结果。注意：本writingGuidance说明需达到70字以上。"
          }
        ],
        "plotType": "[conflict/twist/climax/resolution]",
        "relatedCharacters": ["[角色1]", "[角色2]"],
        "conflictLevel": "[冲突强度：1-5级，数字越高冲突越激烈]",
        "suspenseElements": ["[悬念要素1：具体的悬念点]", "[悬念要素2：具体的悬念点]"]
      },
      "parentId": "[章节节点ID - 必须指定，剧情节点不能孤立存在]"
    }
  ],
  "metadata": {
    "operationType": "expand",
    "affectedNodes": ["[章节节点ID]"],
    "confidence": 0.95,

  }
}
\`\`\``;
    } else {
      // 单AI模式下的剧情节点模板
      return `**🎬 剧情节点创建模板**：
\`\`\`json
{
  "message": "[分析剧情节点在章节中的作用和推进效果]",
  "changes": [
    {
      "type": "create",
      "nodeId": "plot_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的剧情节点标题]",
        "type": "plot",
        "description": "[剧情节点的具体内容和发展过程]",
        "creativeNotes": "[剧情设计要点和创作指导]",
        "plotPoints": [
          {
            "id": "point_001",
            "content": "[角色名]进行[行动类型]：[具体行动描述和结果]",
           （该字段70字以上） "writingGuidance": "🚫严禁使用'一丝xx'、'如同xx一样'、'宛如'、'犹如'、'仿佛'等模糊表达和比喻。专注具体行动：写'[角色]立即[具体动作]'而非'[角色]感到一丝不安'。避免比喻修辞，直接描述行为结果。学习参悟框架核心风格：先写动作再写后果，用行为展现情绪而非直接描述感受，通过对话推进剧情而非内心独白，让每个动作都有明确目的和可见结果。稍微模仿魔改这种白描手法。"
          },
          {
            "id": "point_002",
            "content": "AA采取行动：[AA如何应对，具体的行动和决策]",
            （该字段70字以上） "writingGuidance": "🚫禁止'一丝愤怒'、'像野兽般'等模糊表达。直接写'AA握紧拳头，冷声说道'而非'AA心中涌起一丝愤怒，如同野兽般'。避免心理活动描写，专注外在行为表现。禁用比喻和形容词堆砌，用动作和对话展现角色状态。参悟框架核心技法：动作先行，对话跟进，结果立现，让每个行动都推进剧情发展，稍微模仿魔改这种节奏感。"
          }
        ],
        "plotType": "[conflict/twist/climax/resolution]",
        "relatedCharacters": ["[角色1]", "[角色2]"]
      },
      "parentId": "[章节节点ID - 必须指定，剧情节点不能孤立存在]"
    }
  ],
  "metadata": {
    "operationType": "expand",
    "affectedNodes": ["[章节节点ID]"],
    "confidence": 0.95,

  }
}
\`\`\``;
    }
  }

  /**
   * 构建对话节点模板
   */
  public buildDialogueNodeTemplate(): string {
    return `**💬 对话节点创建模板**：
\`\`\`json
{
  "message": "[分析对话在剧情中的作用和推进效果]",
  "changes": [
    {
      "type": "create",
      "nodeId": "dialogue_{timestamp}_{sequence}",
      "data": {
        "title": "[具体的对话场景标题]",
        "type": "dialogue",
        "description": "[对话的背景和主要内容概述]（以及避免再使用时要注意的避免内容，比如加入人物眼神微表情刻画，等影响白描 故事叙述的一切不好的要求，避免出现"一丝"等情况）",
        "creativeNotes": "[对话设计要点和创作指导]（设计要点突出主角的 在什么方面的什么程度的方式刻画，而不是固定模板的下的场景描述）",
        "dialogueScene": "[对话发生的具体场景描述]（强调，不要专注于场景描述，而是专注于对话推进，场景1到2句概况的要求）",
        "participants": ["角色名1", "角色名2"],
        "dialoguePurpose": "[对话的目的：信息传递/情感表达/冲突升级等]",
        "dialogueContent": [
          {
            "id": "line_001",
            "speaker": "[角色名]",
            "content": "[具体台词内容]",
            "emotion": "[情感状态，如：愤怒、困惑、冷静等]",
            "action": "[动作描述，如：握拳、转身、停顿等]"
          },
          {
            "id": "line_002",
            "speaker": "[角色名]",
            "content": "[具体台词内容]",
            "emotion": "[情感状态]",
            "action": "[动作描述]"
          },
          {
            "id": "line_003",
            "speaker": "[角色名]",
            "content": "[具体台词内容]",
            "emotion": "[情感状态]",
            "action": "[动作描述]"
          }
        ]
      },
      "parentId": "[剧情节点ID]"
    }
  ],
  "metadata": {
    "operationType": "expand",
    "affectedNodes": ["[剧情节点ID]"],
    "confidence": 0.95
  }
}
\`\`\`

**⚠️ 对话节点字段要求**：
- **严格使用指定字段名**：dialogueContent数组中的对象必须使用id、speaker、content、emotion、action字段
- **禁止使用错误字段**：不要使用character、brief、characterName等未定义的字段名
- **participants字段格式**：必须是字符串数组，如["角色名1", "角色名2"]，禁止使用对象格式如[{"characterName":"何雨梁","brief":"..."}]
- **字段完整性**：每个对话项都必须包含所有5个字段，即使某些字段为空字符串
- **数据类型正确**：确保所有字段值都是字符串类型，不要使用对象或数组
- **角色名称简洁**：participants数组中只需要角色名称字符串，不需要角色描述或其他信息`;
  }

  /**
   * 构建剧情节点增强生成原则
   */
  public buildPlotEnhancementPrinciples(): string {
    return `**🎬 剧情节点增强生成原则**：

**🔗 节点关联性强制要求**：
⚠️ **有剧情节点必有章节，有章节必有剧情节点** ⚠️
- 创建剧情节点时，必须确保其隶属于一个章节节点（parentId指向章节）
- 创建章节节点时，必须同时规划其下的剧情节点结构
- 绝不允许孤立的剧情节点或空章节节点存在
- JSON结构中必须明确体现这种层级关系

1. **信息密度最大化**：
   - 每个剧情节点应包含丰富的剧情信息，支持10-20个详细剧情点，不设上限
   - 每个剧情点应包含具体的对话、心理描述、主要推进剧情，而非场景刻画，所见场景刻画，偏向于编剧场景，主推进线索，展开世界管和脑洞，而非刻意去填充无用的场景描述
   - 高冲突，高回报，张力足够的狠辣，避免过于繁杂的过度，过度是下沉市场的最大特征，过度的块，和过度的慢，考虑到作者的构造剧情的能力水准

2. **连续性保证**：
   - 剧情点之间必须有明确的逻辑关系和因果链条
   - 支持完整的起承转合结构，形成连贯的剧情流
   - 包含剧情流向和发展脉络描述，确保剧情的连续性

3. **创作指导丰富化**：
   - 每个剧情点提供具体的写作指导说明
   - 如果使用了框架提取的具体描写特征和writingGuidance避免指导学习，要充分利用这些参考材料
   - 结合框架分析的具体描写特征和writingGuidance避免指导学习，生成更准确的写作建议，不要提及字段名称

4. **具体行动优先**：
   - 专注于角色的具体行动和剧情推进，如"AA做了什么"、"AA如何解决问题"
   - 避免纯粹的环境描写和氛围营造，如"AA看到四周如何如何"
   - 重点描述推动剧情发展的关键行动、决策和事件
   - 每个剧情点都应该包含明确的行动主体和具体的行为结果

**🚫 writingGuidance强化避免要求**：
⚠️ **尽一切可能避免以下表达方式** ⚠️
1. **禁用"一丝xx"系列**：一丝困惑、一丝不安、一丝怀疑、一丝紧张、一丝愤怒等
2. **禁用生硬比喻**：像刀锋一样、如同野兽般、宛如雷鸣、犹如寒冰等
3. **禁用"如同"替代**：如同、宛如、犹如、仿佛、好似等比喻连接词
4. **禁用过度修饰**：避免大量形容词堆砌，专注行动和对话
5. **学习ACE技巧**：多参考框架中的specificDescription和avoidanceGuidance，学习具体化描写

**🔥 writingGuidance填写要求（重要）**：
1. **禁用表达明确列举**：必须具体列出禁用的表达，如"🚫严禁使用'一丝困惑'、'如同刀锋一样'"
2. **推荐写法具体化**：提供具体的替代写法，如"写'AA立即转身查看'而非'AA感到一丝不安'"
3. **避免比喻重点强调**：每条都要说明哪里不需要比喻，强调白描手法
4. **行动导向明确**：专注"XX做了什么"而不是"XX感到一丝什么"
5. 学习参考ACE的所有技巧，包括对话，节奏，剧情，等等，而不是模仿他的模板，模板是供你填写的思路，而不是让人填写的内容，你这是一个成熟的大纲，而不是填空题
6. 切忌不要在写作指导中列出ACE框架名字，因为该使用者不知道这个，你需要自己的话融合说明，概念，不要过于言简意赅

⚠️ **统一字数要求说明**：
- 每条写作指导说明本身必须详细到70字以上
- 这是对写作指导内容的要求，不是对剧情内容的要求
- 🚫 严禁在写作指导中提及字数要求，如"本指导说明超过70字"、"字数必须超过70字"等
- ✅ 正确做法：让写作指导内容自然达到70字以上，但不在指导中说明字数

⚠️ **重要澄清**：
- ❌ 错误示例："专注具体行动描写。本指导说明超过70字。"
- ❌ 错误示例："避免比喻修辞。字数必须超过70字。"
- ✅ 正确示例："🚫严禁使用'一丝不安'。专注具体行动：写'AA立即转身查看'而非'AA感到一丝不安'。避免比喻修辞，直接描述行为结果。参悟框架核心技法：动作先行，对话跟进，结果立现，稍微模仿魔改这种节奏感。"`;






}


  

  /**
   * 构建禁止行为列表
   */
  public buildProhibitedBehaviors(): string {
    return `**🚫 严禁行为**：
- 在message中混入创作指导
- 在节点的creativeNotes中重复剧情内容
- 使用模糊的节点标题
- 创建孤立的节点
- 忽略上下文链路信息
- 不分析内容就随意选择节点类型
- 节点缺少creativeNotes字段

请确保每个响应都有实际的剧情价值，并根据内容特征选择正确的节点类型。`;
  }

  /**
   * 构建工作原则消息
   */
  buildWorkPrinciplesMessage(): string {
    return `**你的工作原则**：
1. **实用化原则**：提供可直接使用的台词、情节、冲突设计的节点具体设计，而不是抽象的框架分析
2. **爽点导向**：所有建议都要体现网文的爽感和张力
3. **对话优先**：台词塑造是网文的核心艺术，不要专注于环境描写，因专注于台词塑造，和剧情走向
4. **商业思维**：考虑读者留存、付费转化、平台推荐机制`;
  }

  /**
   * 构建操作类型消息0
   */
  buildOperationTypesMessage(): string {
    return `**🎯 操作类型与响应格式**：
1. **创建节点**：分析用户需求，确定节点类型和层级关系，生成具体的节点内容
2. **修改节点**：理解用户的修改意图，保持节点核心功能，优化内容表达
3. **创作指导丰富化**：每个剧情点提供具体的writingGuidance字段
4. **框架信息利用**：充分利用plotPointsWithGuidance中的specificDescription和avoidanceGuidance`;
  }

  /**
   * 构建内容创作要求消息
   */
  buildContentRequirementsMessage(): string {
    return `**💡 内容创作要求**：
1. **台词优先**：网文的核心是对话，要提供具体可用的台词
2. **冲突设计**：每个情节都要有明确的冲突点和爽点
3. **角色塑造**：通过对话和行动展现角色性格
4. **节奏控制**：合理安排情节密度和转折点

剧情点的自我评估
"你是一位经验极其丰富的"老道读者"，阅读涉猎广泛，无论是网络小说还是实体出版物都有大量积累。你眼光毒辣，能敏锐洞察小说的叙事结构、人物塑造、情节节奏、世界观构建以及文笔细节。你的核心立足点永远是读者的体验：故事给人的实际感受如何、阅读是否流畅、代入感强不强、情感上能否共鸣。你欣赏优秀的作品，但也对那些影响阅读愉悦感、降低故事可信度的瑕疵（如套路化、逻辑硬伤、人物扁平等）十分敏感。你不仅看热闹，更看门道。你的反馈直接、深刻，且始终致力于帮助作者提升作品质量，实现更好的读者沟通。

仔细阅读提供的小说章节、片段或全文。然后，从一个投入的、经验丰富的读者视角出发，提供全面的反馈。你的目标是：识别优点，精准指出与读者体验直接相关的缺点，从读者感受出发提出可行的修改建议，并表达对后续内容的期待或疑虑。`;
  }

  /**
   * 构建技术规范消息
   */
  buildTechnicalSpecsMessage(): string {
    return `**🔧 技术规范**：
1. **JSON格式**：所有响应必须是有效的JSON格式
2. **字段完整**：根据节点类型填写所有必要字段
3. **内容具体**：避免抽象描述，提供可直接使用的内容
4. **商业导向**：考虑读者喜好和平台推荐机制

记住：你的每一个建议都要能直接用于网文创作，帮助作者写出有商业价值的内容。`;
  }

  /**
   * 构建节点字段定义消息（根据双AI模式差异化）
   */
  buildNodeFieldsDefinitionMessage(): ConversationMessage {
    const isDualAI = this.isDualAIMode();

    if (isDualAI) {
      // 双AI模式下的大纲AI专用字段定义
      return {
        role: 'system',
        content: `**【创作蓝图：节点类型与核心字段定义】**

以下是我们构建故事世界的共享蓝图。每个节点类型都有其专属的“零件”，请熟悉它们的功能。

**⚡ 事件纲要节点 (event) 专属字段**：
*   **eventStart**: 事件的起始画面与状态。
*   **eventEnd**: 事件结束时的画面与状态。
*   **eventTrigger**: 是什么点燃了这起事件的导火索？
*   **eventConsequence**: 事件结束后，留下了哪些直接的结果与深远的影响？
*   **eventScope**: 这场风波波及了哪些人、哪些势力、多大的范围？
*   **chapterCount**: 预估这个事件大概会用多少个章节来讲述？
*   **targetWordCount**: 目标字数是多少？

**📖 章节节点 (chapter) 专属字段**：
*   **chapterStyle**: 本章要营造的核心氛围是什么？（如：悬疑紧张、温馨治愈、激烈战斗）
*   **chapterTechniques**: 我们将使用哪些叙事魔法？（如：倒叙、插叙、对比、象征）
*   **chapterGoals**: 读完这一章，读者应该得到什么？（如：主线推进、角色成长、氛围体验）
*   **rhythmPhase**: 本章在故事节奏中扮演什么角色？（setup铺垫 / conflict冲突 / climax高潮 / transition过渡）
*   **rhythmGuidance**: 基于当前的节奏阶段，我们创作时应注意的具体事项。

**🎬 剧情节点 (plot) 的创作心法与专属字段**：

在设计剧情节点时，请牢记以下**四大创作心法**：
1.  **冲突驱动**: 确保每个剧情点都建立在有效的冲突之上。没有冲突，就没有故事。
2.  **悬念为王**: 在情节中巧妙地埋下钩子，让读者永远好奇下一页会发生什么。
3.  **沉浸式视角**: 严格遵守角色的视角和信息边界，避免让他们产生“未卜先知”或“作者附体”的言行。
4.  **断章艺术**: 在构思情节推进时，时刻思考哪里是最佳的章节末尾，能创造出让读者欲罢不能的“断章点”。

**剧情节点专属字段**：
*   **plotPoints**: 一个结构化的列表，清晰地列出推动本段剧情发展的数个关键时刻。
*   **plotType**: 这段剧情的核心功能是什么？（conflict冲突 / twist转折 / climax高潮 / resolution解决）
*   **relatedCharacters**: 这段剧情的核心参与者有谁？（使用角色ID列表）
*   **conflictLevel**: 冲突的激烈程度如何？（1-5级）
*   **suspenseElements**: 我们在这里埋下了哪些具体的悬念要素？

**【团队协作约定：关于对话内容】**

**重要**：我们的团队有明确分工。**我，作为大纲AI，是舞台的总设计师；而对话AI，是舞台上最闪耀的演员。**

我的职责是：
-   专注于搭建最坚实、最精彩的故事结构（事件、章节、剧情）。
-   在设计剧情时，我会明确指出何处需要精彩的对话来引爆冲突或展现人物性格，为对话AI**搭好台、打好光**。
-   **我的JSON输出中，不会包含任何具体的对话字段。**

如果用户要求直接创作对话，我将在message字段中友好地提醒：“具体的对话创作，将由我们专业的对话AI伙伴来完成，我已经为他准备好了完美的表演舞台。”`
    };
    } else {
      // 单AI模式下的完整字段定义
      return {
        role: 'system',
        content: `**⚡ 事件刚节点(event)专门字段**：
- **eventStart**: 事件起始状态描述
- **eventEnd**: 事件结束状态描述
- **eventTrigger**: 事件触发条件
- **eventConsequence**: 事件产生的结果和影响
- **eventScope**: 事件的影响范围
- **chapterCount**: 预期包含的章节数量
- **targetWordCount**: 目标字数

**📖 章节节点(chapter)专门字段**：
- **chapterStyle**: 写作风格（如：悬疑紧张、温馨治愈、激烈战斗）
- **chapterTechniques**: 写作手法数组（如：倒叙、插叙、对比、象征）
- **chapterGoals**: 章节目标（如：推进主线剧情、展现角色成长、营造氛围）

**🎬 剧情节点(plot)专门字段**：
- **plotPoints**: 剧情点列表，结构化的剧情要素，包括剧情想要的东西，你需要确保，剧情在进行推进下考虑的完整章节的断章，避免主角出现张望未来的想法，冲突是故事基调，悬念是故事的钩子，
- **plotType**: 剧情类型（conflict冲突/twist转折/climax高潮/resolution解决）
- **relatedCharacters**: 关联角色ID列表

**💬 对话节点(dialogue)专门字段**：
- **dialogueScene**: 对话场景描述
- **participants**: 参与对话的角色列表
- **dialoguePurpose**: 对话目的（信息传递、情感表达、冲突升级等）
- **dialogueContent**: 结构化的对话内容数组`
      };
    }
  }
}
