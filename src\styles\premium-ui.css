/* AI小说平台 - 高端UI样式系统 - 基于米黄色系 */

/* 全局变量 - 米黄色系现代化设计 */
:root {
  /* 主色调 - 基于原有的深棕色系 */
  --premium-primary: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  --premium-primary-solid: #8B4513;
  --premium-primary-dark: #6B3100;
  --premium-primary-light: #A05A2C;

  /* 辅助色 - 基于原有的淡棕色系 */
  --premium-accent: linear-gradient(135deg, #D2B48C 0%, #DEB887 100%);
  --premium-accent-solid: #D2B48C;
  --premium-accent-light: #E0C9A6;

  /* 背景色系 - 保持米黄色调 */
  --premium-bg-primary: #F5F2E9;
  --premium-bg-secondary: #F0EBE0;
  --premium-bg-glass: rgba(245, 242, 233, 0.85);
  --premium-text-primary: #333333;
  --premium-text-secondary: #666666;
  --premium-text-hint: #999999;

  /* 阴影系统 - 温暖色调 */
  --premium-shadow-sm: 0 2px 4px rgba(139, 69, 19, 0.08);
  --premium-shadow-md: 0 4px 12px rgba(139, 69, 19, 0.12);
  --premium-shadow-lg: 0 8px 25px rgba(139, 69, 19, 0.15);
  --premium-shadow-xl: 0 20px 40px rgba(139, 69, 19, 0.12);

  /* 动画缓动 */
  --premium-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --premium-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 动态背景系统 - 米黄色调 */
.premium-background {
  position: relative;
  background: var(--premium-bg-primary);
  overflow: hidden;
}

.premium-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(210, 180, 140, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(160, 82, 45, 0.06) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* 现代化卡片系统 */
.premium-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: var(--premium-shadow-lg);
  transition: all 0.3s var(--premium-ease);
  position: relative;
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.premium-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--premium-shadow-xl);
  border-color: rgba(255, 255, 255, 0.4);
}

.premium-card:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  pointer-events: none;
}

/* 高端按钮系统 */
.premium-button {
  position: relative;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s var(--premium-ease);
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.5px;
}

.premium-button-primary {
  background: var(--premium-primary);
  color: white;
  box-shadow: var(--premium-shadow-md);
}

.premium-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--premium-shadow-lg);
  background: linear-gradient(135deg, #A05A2C 0%, #B8860B 100%);
}

.premium-button-primary:active {
  transform: translateY(0);
  transition: all 0.1s var(--premium-ease);
}

/* 波纹效果 */
.premium-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.premium-button:active::after {
  width: 300px;
  height: 300px;
}

/* 现代化标题系统 */
.premium-title {
  background: var(--premium-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.5px;
}

/* 加载动画 */
.premium-loading {
  display: inline-flex;
  gap: 4px;
}

.premium-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--premium-primary-solid);
  animation: premiumBounce 1.4s ease-in-out infinite both;
}

.premium-loading-dot:nth-child(1) { animation-delay: -0.32s; }
.premium-loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes premiumBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 输入框系统 */
.premium-input {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 16px;
  transition: all 0.3s var(--premium-ease);
  width: 100%;
}

.premium-input:focus {
  outline: none;
  border-color: var(--premium-primary-solid);
  box-shadow: 0 0 0 4px rgba(139, 69, 19, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 成功提示动画 */
.premium-success {
  background: linear-gradient(135deg, #556B2F 0%, #6B8545 100%);
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: var(--premium-shadow-lg);
  animation: premiumSlideIn 0.5s var(--premium-ease-bounce);
}

@keyframes premiumSlideIn {
  from {
    transform: translateY(100px) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 拖拽区域 */
.premium-drag-area {
  background: rgba(139, 69, 19, 0.05);
  border: 3px dashed var(--premium-primary-solid);
  border-radius: 16px;
  animation: premiumPulse 2s ease-in-out infinite;
}

@keyframes premiumPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .premium-card {
    border-radius: 12px;
  }

  .premium-button {
    padding: 10px 20px;
    font-size: 14px;
  }

  .premium-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --premium-bg-primary: #1a202c;
    --premium-bg-secondary: #2d3748;
    --premium-bg-glass: rgba(45, 55, 72, 0.8);
    --premium-text-primary: #f7fafc;
    --premium-text-secondary: #e2e8f0;
    --premium-text-hint: #a0aec0;
  }

  .premium-card {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .premium-input {
    background: rgba(45, 55, 72, 0.9);
    color: var(--premium-text-primary);
  }
}
