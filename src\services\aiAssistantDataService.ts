import { db, AIAssistantContextType } from '@/lib/db/dexie';
import { aiAssistantContextRepository } from '@/lib/db/repositories/aiAssistantContextRepository';
import { aiAssistantTemplateRepository } from '@/lib/db/repositories/aiAssistantTemplateRepository';

/**
 * @功能的搜索结果项
 */
export interface MentionSearchItem {
  id: string;
  title: string;
  type: AIAssistantContextType | 'outlineNode';
  description?: string;
  category?: string;
  level?: number; // 用于大纲节点的层级
  path?: string[]; // 用于大纲节点的路径
  usageCount?: number; // 使用次数，用于排序
  lastUsedAt?: Date; // 最后使用时间
  metadata?: Record<string, any>; // 额外元数据
}

/**
 * AI助手数据服务
 * 提供@功能的统一数据查询接口
 */
export class AIAssistantDataService {

  /**
   * 搜索可@的内容
   * @param bookId 书籍ID
   * @param query 搜索关键词
   * @param types 可选的类型过滤
   * @param limit 返回数量限制
   * @returns 搜索结果列表
   */
  async searchMentionItems(
    bookId: string,
    query: string = '',
    types?: (AIAssistantContextType | 'outlineNode')[],
    limit: number = 20
  ): Promise<MentionSearchItem[]> {
    console.log('🔍 AI助手搜索开始:', { bookId, query, types, limit });

    const results: MentionSearchItem[] = [];
    const lowerQuery = query.toLowerCase();

    // 如果没有指定类型，搜索所有类型
    const searchTypes = types || [
      AIAssistantContextType.CHAPTER,
      AIAssistantContextType.CHARACTER,
      AIAssistantContextType.TERMINOLOGY,
      AIAssistantContextType.WORLD_BUILDING,
      'outlineNode' as const
    ];

    console.log('📋 搜索类型:', searchTypes);

    // 并行搜索各种类型的数据
    const searchPromises = searchTypes.map(async (type) => {
      console.log(`🔎 开始搜索类型: ${type}`);
      let typeResults: MentionSearchItem[] = [];

      switch (type) {
        case AIAssistantContextType.CHAPTER:
          typeResults = await this.searchChapters(bookId, lowerQuery);
          break;
        case AIAssistantContextType.CHARACTER:
          typeResults = await this.searchCharacters(bookId, lowerQuery);
          break;
        case AIAssistantContextType.TERMINOLOGY:
          typeResults = await this.searchTerminology(bookId, lowerQuery);
          break;
        case AIAssistantContextType.WORLD_BUILDING:
          typeResults = await this.searchWorldBuilding(bookId, lowerQuery);
          break;
        case 'outlineNode':
          typeResults = await this.searchOutlineNodes(bookId, lowerQuery);
          break;
        default:
          typeResults = [];
      }

      console.log(`✅ ${type} 搜索完成，找到 ${typeResults.length} 个结果:`, typeResults);
      return typeResults;
    });

    const searchResults = await Promise.all(searchPromises);

    // 合并所有结果
    searchResults.forEach(typeResults => {
      results.push(...typeResults);
    });

    console.log('🎯 搜索结果汇总:', results);

    // 排序和限制结果
    const finalResults = this.sortAndLimitResults(results, query, limit);
    console.log('📊 最终返回结果:', finalResults);

    return finalResults;
  }

  /**
   * 搜索章节
   */
  private async searchChapters(bookId: string, query: string): Promise<MentionSearchItem[]> {
    console.log(`📖 搜索章节: bookId=${bookId}, query="${query}"`);

    const chapters = await db.chapters
      .where('bookId')
      .equals(bookId)
      .toArray();

    console.log(`📖 数据库中找到 ${chapters.length} 个章节:`, chapters.map(c => ({ id: c.id, title: c.title })));

    const results = chapters
      .filter(chapter =>
        !query || chapter.title.toLowerCase().includes(query)
      )
      .map(chapter => ({
        id: chapter.id!,
        title: chapter.title,
        type: AIAssistantContextType.CHAPTER,
        description: chapter.summary || `第${chapter.order}章 • ${chapter.wordCount}字`,
        metadata: {
          order: chapter.order,
          wordCount: chapter.wordCount
        }
      }));

    console.log(`📖 章节搜索结果:`, results);
    return results;
  }

  /**
   * 搜索人物
   */
  private async searchCharacters(bookId: string, query: string): Promise<MentionSearchItem[]> {
    console.log(`👤 搜索人物: bookId=${bookId}, query="${query}"`);

    const characters = await db.characters
      .where('bookId')
      .equals(bookId)
      .toArray();

    console.log(`👤 数据库中找到 ${characters.length} 个人物:`, characters.map(c => ({ id: c.id, name: c.name })));

    const results = characters
      .filter(character =>
        !query ||
        character.name.toLowerCase().includes(query) ||
        (character.alias && character.alias.some(alias => alias.toLowerCase().includes(query)))
      )
      .map(character => ({
        id: character.id!,
        title: character.name,
        type: AIAssistantContextType.CHARACTER,
        description: character.description || '人物角色',
        metadata: {
          alias: character.alias,
          personality: character.personality,
          background: character.background,
          appearance: character.appearance,
          goals: character.goals,
          characterArchetype: character.characterArchetype,
          growthArc: character.growthArc
        }
      }));

    console.log(`👤 人物搜索结果:`, results);
    return results;
  }

  /**
   * 搜索术语
   */
  private async searchTerminology(bookId: string, query: string): Promise<MentionSearchItem[]> {
    console.log(`📚 搜索术语: bookId=${bookId}, query="${query}"`);

    const terminology = await db.terminology
      .where('bookId')
      .equals(bookId)
      .toArray();

    console.log(`📚 数据库中找到 ${terminology.length} 个术语:`, terminology.map(t => ({ id: t.id, name: t.name })));

    const results = terminology
      .filter(term =>
        !query ||
        term.name.toLowerCase().includes(query) ||
        (term.alias && term.alias.some(alias => alias.toLowerCase().includes(query)))
      )
      .map(term => ({
        id: term.id!,
        title: term.name,
        type: AIAssistantContextType.TERMINOLOGY,
        description: term.description || term.category,
        category: term.category,
        metadata: {
          alias: term.alias,
          category: term.category
        }
      }));

    console.log(`📚 术语搜索结果:`, results);
    return results;
  }

  /**
   * 搜索世界观
   */
  private async searchWorldBuilding(bookId: string, query: string): Promise<MentionSearchItem[]> {
    console.log(`🌍 搜索世界观: bookId=${bookId}, query="${query}"`);

    const worldBuilding = await db.worldBuilding
      .where('bookId')
      .equals(bookId)
      .toArray();

    console.log(`🌍 数据库中找到 ${worldBuilding.length} 个世界观:`, worldBuilding.map(w => ({ id: w.id, name: w.name })));

    const results = worldBuilding
      .filter(world =>
        !query || world.name.toLowerCase().includes(query)
      )
      .map(world => ({
        id: world.id!,
        title: world.name,
        type: AIAssistantContextType.WORLD_BUILDING,
        description: world.description || world.category,
        category: world.category,
        metadata: {
          category: world.category
        }
      }));

    console.log(`🌍 世界观搜索结果:`, results);
    return results;
  }

  /**
   * 搜索大纲节点
   */
  private async searchOutlineNodes(bookId: string, query: string): Promise<MentionSearchItem[]> {
    // 这里需要根据实际的大纲数据结构来实现
    // 暂时返回空数组，后续会在扩展时实现
    return [];
  }

  /**
   * 排序和限制搜索结果
   */
  private sortAndLimitResults(
    results: MentionSearchItem[],
    query: string,
    limit: number
  ): MentionSearchItem[] {
    const lowerQuery = query.toLowerCase();

    return results
      .sort((a, b) => {
        // 如果有查询词，优先显示标题开头匹配的
        if (query) {
          const aStartsWith = a.title.toLowerCase().startsWith(lowerQuery);
          const bStartsWith = b.title.toLowerCase().startsWith(lowerQuery);

          if (aStartsWith && !bStartsWith) return -1;
          if (!aStartsWith && bStartsWith) return 1;
        }

        // 然后按使用频率排序（如果有的话）
        if (a.usageCount !== undefined && b.usageCount !== undefined) {
          if (a.usageCount !== b.usageCount) {
            return b.usageCount - a.usageCount;
          }
        }

        // 最后按标题字母顺序排序
        return a.title.localeCompare(b.title);
      })
      .slice(0, limit);
  }

  /**
   * 获取最近使用的@内容
   * @param bookId 书籍ID
   * @param limit 返回数量限制
   * @returns 最近使用的内容列表
   */
  async getRecentlyUsedItems(bookId: string, limit: number = 10): Promise<MentionSearchItem[]> {
    const recentContexts = await aiAssistantContextRepository.getRecentlyUsedContexts(bookId, limit);

    const results: MentionSearchItem[] = [];

    for (const context of recentContexts) {
      results.push({
        id: context.contextId,
        title: context.contextTitle,
        type: context.contextType,
        usageCount: context.usageCount,
        lastUsedAt: context.lastUsedAt,
        metadata: context.metadata
      });
    }

    return results;
  }

  /**
   * 获取最常用的@内容
   * @param bookId 书籍ID
   * @param limit 返回数量限制
   * @returns 最常用的内容列表
   */
  async getMostUsedItems(bookId: string, limit: number = 10): Promise<MentionSearchItem[]> {
    const popularContexts = await aiAssistantContextRepository.getMostUsedContexts(bookId, limit);

    const results: MentionSearchItem[] = [];

    for (const context of popularContexts) {
      results.push({
        id: context.contextId,
        title: context.contextTitle,
        type: context.contextType,
        usageCount: context.usageCount,
        lastUsedAt: context.lastUsedAt,
        metadata: context.metadata
      });
    }

    return results;
  }

  /**
   * 记录@内容的使用
   * @param bookId 书籍ID
   * @param contextType 内容类型
   * @param contextId 内容ID
   * @param contextTitle 内容标题
   */
  async recordUsage(
    bookId: string,
    contextType: AIAssistantContextType,
    contextId: string,
    contextTitle: string
  ): Promise<void> {
    await aiAssistantContextRepository.saveContext({
      bookId,
      contextType,
      contextId,
      contextTitle,
      isActive: true,
      priority: 0,
      usageCount: 1,
      lastUsedAt: new Date()
    });
  }
}

// 导出单例实例
export const aiAssistantDataService = new AIAssistantDataService();

// 在浏览器环境中添加调试工具
if (typeof window !== 'undefined') {
  (window as any).debugAIAssistant = {
    // 测试搜索功能
    async testSearch(bookId: string, query: string = '') {
      console.log('🧪 开始测试AI助手搜索功能...');
      const results = await aiAssistantDataService.searchMentionItems(bookId, query);
      console.log('🎯 测试结果:', results);
      return results;
    },

    // 检查数据库中的数据
    async checkData(bookId: string) {
      console.log('🔍 检查数据库中的数据...');

      const chapters = await db.chapters.where('bookId').equals(bookId).toArray();
      const characters = await db.characters.where('bookId').equals(bookId).toArray();
      const terminology = await db.terminology.where('bookId').equals(bookId).toArray();
      const worldBuilding = await db.worldBuilding.where('bookId').equals(bookId).toArray();

      console.log('📊 数据统计:');
      console.log(`📖 章节: ${chapters.length} 个`, chapters.map(c => c.title));
      console.log(`👤 人物: ${characters.length} 个`, characters.map(c => c.name));
      console.log(`📚 术语: ${terminology.length} 个`, terminology.map(t => t.name));
      console.log(`🌍 世界观: ${worldBuilding.length} 个`, worldBuilding.map(w => w.name));

      return { chapters, characters, terminology, worldBuilding };
    },

    // 获取当前书籍ID
    getCurrentBookId() {
      // 尝试从URL或其他地方获取当前书籍ID
      const url = window.location.href;
      const match = url.match(/\/books\/([^\/]+)/);
      if (match) {
        console.log('📚 当前书籍ID:', match[1]);
        return match[1];
      }
      console.log('❌ 无法获取当前书籍ID');
      return null;
    }
  };

  console.log('🔧 AI助手调试工具已加载到 window.debugAIAssistant');
  console.log('💡 使用方法:');
  console.log('  - window.debugAIAssistant.getCurrentBookId() // 获取当前书籍ID');
  console.log('  - window.debugAIAssistant.checkData(bookId) // 检查数据');
  console.log('  - window.debugAIAssistant.testSearch(bookId, query) // 测试搜索');
}
