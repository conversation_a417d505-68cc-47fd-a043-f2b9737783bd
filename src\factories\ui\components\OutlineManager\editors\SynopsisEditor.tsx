"use client";

import React from 'react';
import { OutlineNodeType } from '../../../types/outline';

interface SynopsisEditorProps {
  node: OutlineNodeType;
  onChange: (node: OutlineNodeType) => void;
}

/**
 * 核心故事梗概编辑器
 * 提供专门的字段编辑界面：脑洞、类型、开头、梗概、结尾、故事、引用
 */
const SynopsisEditor: React.FC<SynopsisEditorProps> = ({ node, onChange }) => {
  const handleFieldChange = (field: string, value: string) => {
    onChange({
      ...node,
      [field]: value
    });
  };

  return (
    <div className="space-y-4">
      {/* 标题输入 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          标题
        </label>
        <input
          type="text"
          value={node.title || ''}
          onChange={(e) => handleFieldChange('title', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
          placeholder="输入核心故事梗概标题"
        />
      </div>

      {/* 描述输入 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          描述
        </label>
        <textarea
          value={node.description || ''}
          onChange={(e) => handleFieldChange('description', e.target.value)}
          rows={2}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="简要描述这个核心故事梗概"
        />
      </div>

      {/* 脑洞 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          脑洞
        </label>
        <textarea
          value={node.synopsisBrainhole || ''}
          onChange={(e) => handleFieldChange('synopsisBrainhole', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="描述故事的核心创意和独特想法"
        />
      </div>

      {/* 类型 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          类型
        </label>
        <input
          type="text"
          value={node.synopsisGenre || ''}
          onChange={(e) => handleFieldChange('synopsisGenre', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
          placeholder="如：悬疑、言情、科幻、历史等"
        />
      </div>

      {/* 开头 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          开头
        </label>
        <textarea
          value={node.synopsisOpening || ''}
          onChange={(e) => handleFieldChange('synopsisOpening', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="故事的开头设计，如何抓住读者注意力，制造初始冲击和悬念"
        />
      </div>

      {/* 梗概 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          梗概
        </label>
        <textarea
          value={node.synopsisCoreOutline || ''}
          onChange={(e) => handleFieldChange('synopsisCoreOutline', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="对核心梗的使用，对核心的概况，核心极致"
        />
      </div>

      {/* 结尾 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          结尾
        </label>
        <textarea
          value={node.synopsisEnding || ''}
          onChange={(e) => handleFieldChange('synopsisEnding', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="故事的结尾设计，如何收束剧情，给读者留下深刻印象（可选，除非用户要求）"
        />
      </div>

      {/* 故事 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          故事
        </label>
        <textarea
          value={node.synopsisStoryDescription || ''}
          onChange={(e) => handleFieldChange('synopsisStoryDescription', e.target.value)}
          rows={5}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="准备讲一个完整的什么故事"
        />
      </div>

      {/* 引用 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          引用
        </label>
        <textarea
          value={node.synopsisAceReferences || ''}
          onChange={(e) => handleFieldChange('synopsisAceReferences', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-colors"
          placeholder="引用了那些ACE框架的灵感"
        />
      </div>
    </div>
  );
};

export default SynopsisEditor;
