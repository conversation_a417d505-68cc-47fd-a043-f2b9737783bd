import { IAnimationFactory } from './interfaces/IAnimationFactory';
import { IFadeAnimation, FadeDirection } from './interfaces/IFadeAnimation';
import { IScaleAnimation } from './interfaces/IScaleAnimation';
import { DefaultFadeAnimation } from './components/DefaultFadeAnimation';
import { DefaultScaleAnimation } from './components/DefaultScaleAnimation';

/**
 * 默认动画工厂实现
 */
class DefaultAnimationFactory implements IAnimationFactory {
  /**
   * 创建淡入淡出动画
   * @param direction 方向
   * @param duration 持续时间（毫秒）
   * @param delay 延迟时间（毫秒）
   * @param visible 是否显示
   */
  createFadeAnimation(
    direction: FadeDirection = 'none',
    duration: number = 300,
    delay: number = 0,
    visible: boolean = true
  ): IFadeAnimation {
    const animation = new DefaultFadeAnimation();
    animation.setDirection(direction);
    animation.setDuration(duration);
    animation.setDelay(delay);
    animation.setVisible(visible);
    return animation;
  }
  
  /**
   * 创建缩放动画
   * @param startScale 起始缩放比例
   * @param endScale 结束缩放比例
   * @param duration 持续时间（毫秒）
   * @param delay 延迟时间（毫秒）
   * @param visible 是否显示
   */
  createScaleAnimation(
    startScale: number = 0.95,
    endScale: number = 1,
    duration: number = 300,
    delay: number = 0,
    visible: boolean = true
  ): IScaleAnimation {
    const animation = new DefaultScaleAnimation();
    animation.setStartScale(startScale);
    animation.setEndScale(endScale);
    animation.setDuration(duration);
    animation.setDelay(delay);
    animation.setVisible(visible);
    return animation;
  }
}

/**
 * 创建动画工厂
 * @param style 样式，默认为'default'
 * @returns 动画工厂实例
 */
export function createAnimationFactory(style: 'default' | 'fancy' = 'default'): IAnimationFactory {
  switch (style) {
    case 'default':
    default:
      return new DefaultAnimationFactory();
  }
}
