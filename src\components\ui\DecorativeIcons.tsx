"use client";

import React from 'react';

interface DecorativeIconProps {
  type: 'star' | 'star-sparkle' | 'star-rings' | 'star-fall' | 'star-rainbow' | 'clock-time';
  className?: string;
  size?: number;
  color?: string;
  animated?: boolean;
}

/**
 * 装饰性图标组件
 * 用于美化界面的各种精美SVG图标
 */
export const DecorativeIcon: React.FC<DecorativeIconProps> = ({
  type,
  className = "",
  size = 16,
  color = "currentColor",
  animated = false
}) => {
  const iconSvgs = {
    'star': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        <path 
          d="M11.2691 4.41115C11.5006 3.89177 11.6164 3.63208 11.7776 3.55211C11.9176 3.48263 12.082 3.48263 12.222 3.55211C12.3832 3.63208 12.499 3.89177 12.7305 4.41115L14.5745 8.54808C14.643 8.70162 14.6772 8.77839 14.7302 8.83718C14.777 8.8892 14.8343 8.93081 14.8982 8.95929C14.9705 8.99149 15.0541 9.00031 15.2213 9.01795L19.7256 9.49336C20.2911 9.55304 20.5738 9.58288 20.6997 9.71147C20.809 9.82316 20.8598 9.97956 20.837 10.1342C20.8108 10.3122 20.5996 10.5025 20.1772 10.8832L16.8125 13.9154C16.6877 14.0279 16.6252 14.0842 16.5857 14.1527C16.5507 14.2134 16.5288 14.2807 16.5215 14.3503C16.5132 14.429 16.5306 14.5112 16.5655 14.6757L17.5053 19.1064C17.6233 19.6627 17.6823 19.9408 17.5989 20.1002C17.5264 20.2388 17.3934 20.3354 17.2393 20.3615C17.0619 20.3915 16.8156 20.2495 16.323 19.9654L12.3995 17.7024C12.2539 17.6184 12.1811 17.5765 12.1037 17.56C12.0352 17.5455 11.9644 17.5455 11.8959 17.56C11.8185 17.5765 11.7457 17.6184 11.6001 17.7024L7.67662 19.9654C7.18404 20.2495 6.93775 20.3915 6.76034 20.3615C6.60623 20.3354 6.47319 20.2388 6.40075 20.1002C6.31736 19.9408 6.37635 19.6627 6.49434 19.1064L7.4341 14.6757C7.46898 14.5112 7.48642 14.429 7.47814 14.3503C7.47081 14.2807 7.44894 14.2134 7.41394 14.1527C7.37439 14.0842 7.31195 14.0279 7.18708 13.9154L3.82246 10.8832C3.40005 10.5025 3.18884 10.3122 3.16258 10.1342C3.13978 9.97956 3.19059 9.82316 3.29993 9.71147C3.42581 9.58288 3.70856 9.55304 4.27406 9.49336L8.77835 9.01795C8.94553 9.00031 9.02911 8.99149 9.10139 8.95929C9.16534 8.93081 9.2226 8.8892 9.26946 8.83718C9.32241 8.77839 9.35663 8.70162 9.42508 8.54808L11.2691 4.41115Z" 
          stroke={color} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'star-sparkle': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={`${className} ${animated ? 'animate-spin' : ''}`}
        style={{ animationDuration: animated ? '3s' : undefined }}
      >
        <path 
          d="M12 3L13.4302 8.31181C13.6047 8.96 13.692 9.28409 13.8642 9.54905C14.0166 9.78349 14.2165 9.98336 14.451 10.1358C14.7159 10.308 15.04 10.3953 15.6882 10.5698L21 12L15.6882 13.4302C15.04 13.6047 14.7159 13.692 14.451 13.8642C14.2165 14.0166 14.0166 14.2165 13.8642 14.451C13.692 14.7159 13.6047 15.04 13.4302 15.6882L12 21L10.5698 15.6882C10.3953 15.04 10.308 14.7159 10.1358 14.451C9.98336 14.2165 9.78349 14.0166 9.54905 13.8642C9.28409 13.692 8.96 13.6047 8.31181 13.4302L3 12L8.31181 10.5698C8.96 10.3953 9.28409 10.308 9.54905 10.1358C9.78349 9.98336 9.98336 9.78349 10.1358 9.54905C10.308 9.28409 10.3953 8.96 10.5698 8.31181L12 3Z" 
          stroke={color} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'star-rings': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={`${className} ${animated ? 'animate-bounce' : ''}`}
      >
        <path 
          d="M11.7647 14.2813C11.7509 13.3709 11.7441 12.9157 12.0311 12.75C12.3181 12.5843 12.7089 12.8178 13.4905 13.2849L13.6927 13.4058C13.9148 13.5385 14.0258 13.6048 14.1481 13.6203C14.2705 13.6358 14.3902 13.5986 14.6298 13.5242L14.8479 13.4565C15.6908 13.1947 16.1123 13.0638 16.3656 13.2978C16.619 13.5318 16.5364 13.9757 16.3713 14.8635L16.3286 15.0932C16.2817 15.3455 16.2582 15.4716 16.286 15.5949C16.3139 15.7181 16.3898 15.8245 16.5418 16.0372L16.6801 16.2309C17.2148 16.9795 17.4821 17.3538 17.3517 17.6641C17.2213 17.9745 16.7794 18.0153 15.8958 18.0969L15.6672 18.118C15.4161 18.1412 15.2906 18.1528 15.1854 18.2135C15.0803 18.2742 15.0075 18.3771 14.8619 18.583L14.7293 18.7704C14.2168 19.4948 13.9605 19.8571 13.6266 19.8148C13.2926 19.7726 13.1021 19.354 12.7211 18.5166L12.6226 18.3C12.5143 18.062 12.4602 17.943 12.3674 17.8573C12.2745 17.7716 12.1536 17.7288 11.9116 17.6433L11.6914 17.5655C10.8399 17.2646 10.4142 17.1141 10.3383 16.7777C10.2623 16.4413 10.5864 16.1418 11.2345 15.5426L11.4022 15.3876C11.5864 15.2174 11.6785 15.1322 11.7263 15.0186C11.774 14.9049 11.7721 14.7755 11.7682 14.5168L11.7647 14.2813Z" 
          stroke={color} 
          strokeWidth="1.5"
        />
      </svg>
    ),
    
    'star-fall': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={`${className} ${animated ? 'animate-pulse' : ''}`}
      >
        <path 
          d="M9.23163 8.61762C7.26389 9.06284 6.28001 9.28545 6.04594 10.0382C5.81186 10.7909 6.4826 11.5753 7.82408 13.1439L8.17113 13.5498C8.55234 13.9955 8.74294 14.2184 8.82869 14.4942C8.91444 14.7699 8.88562 15.0673 8.82799 15.662L8.77552 16.2035C8.5727 18.2965 8.4713 19.343 9.08412 19.8082C9.69694 20.2734 10.6181 19.8492 12.4605 19.0009L12.9372 18.7815C13.4607 18.5404 13.7225 18.4199 14 18.4199C14.2775 18.4199 14.5393 18.5404 15.0628 18.7815L15.5395 19.0009C17.3819 19.8492 18.3031 20.2734 18.9159 19.8082C19.5287 19.343 19.4273 18.2965 19.2245 16.2035M20.1759 13.1439C21.5174 11.5753 22.1881 10.7909 21.9541 10.0382C21.72 9.28545 20.7361 9.06284 18.7684 8.61762L18.2593 8.50244C17.7001 8.37592 17.4205 8.31266 17.196 8.14225C16.9716 7.97183 16.8276 7.71355 16.5396 7.19699L16.2775 6.7267C15.2641 4.9089 14.7575 4 14 4C13.2425 4 12.7359 4.9089 11.7225 6.7267" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M2.08887 16C3.20445 15.121 4.68639 14.7971 6.08887 15.1257" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M2.08887 10.5C3.08887 10 3.37862 10.0605 4.08887 10" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M2 5.60867L2.20816 5.48676C4.41383 4.19506 6.75032 3.84687 8.95304 4.48161L9.16092 4.54152" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
      </svg>
    ),
    
    'clock-time': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        className={`${className} ${animated ? 'animate-spin' : ''}`}
        style={{ animationDuration: animated ? '60s' : undefined }}
      >
        <circle 
          fill="none" 
          stroke={color} 
          strokeMiterlimit="10" 
          strokeWidth="1.91" 
          cx="12" 
          cy="12" 
          r="10.5"
        />
        <circle 
          fill="none" 
          stroke={color} 
          strokeMiterlimit="10" 
          strokeWidth="1.91" 
          cx="12" 
          cy="12" 
          r="0.95"
        />
        <polyline 
          fill="none" 
          stroke={color} 
          strokeMiterlimit="10" 
          strokeWidth="1.91" 
          points="12 4.36 12 12 16.77 16.77"
        />
      </svg>
    )
  };

  return iconSvgs[type] || null;
};

/**
 * 浮动装饰图标组件
 * 带有浮动动画效果的装饰图标
 */
interface FloatingDecorativeIconProps extends DecorativeIconProps {
  floatDirection?: 'up' | 'down' | 'left' | 'right';
  floatDuration?: string;
}

export const FloatingDecorativeIcon: React.FC<FloatingDecorativeIconProps> = ({
  type,
  className = "",
  size = 16,
  color = "currentColor",
  animated = true,
  floatDirection = 'up',
  floatDuration = '3s'
}) => {
  const getFloatAnimation = () => {
    const directions = {
      up: 'animate-bounce',
      down: 'animate-bounce',
      left: 'animate-pulse',
      right: 'animate-pulse'
    };
    return directions[floatDirection];
  };

  return (
    <div 
      className={`inline-block ${getFloatAnimation()}`}
      style={{ 
        animationDuration: floatDuration,
        animationIterationCount: 'infinite'
      }}
    >
      <DecorativeIcon
        type={type}
        className={className}
        size={size}
        color={color}
        animated={animated}
      />
    </div>
  );
};
