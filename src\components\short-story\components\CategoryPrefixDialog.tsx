import React, { useState, useEffect } from 'react';
import { SavedAIPrefix } from '../../../services/ai/AIGeneratedPrefixStorageService';
import {
  getCategoryIcon,
  EditIcon,
  DeleteIcon,
  StarIcon,
  StarFilledIcon,
  PlusIcon,
  XIcon
} from '../../common/icons/PrefixIcons';

interface CategoryPrefixDialogProps {
  isOpen: boolean;
  onClose: () => void;
  category: string;
  categoryDisplayName: string;
  categoryPrefixes: SavedAIPrefix[];
  selectedPrefixes: Set<string>;
  onPrefixToggle: (id: string) => void;
  onDeletePrefix: (id: string) => void;
  onToggleFavorite: (id: string) => void;
  onEditPrefix: (prefix: SavedAIPrefix) => void;
  onCreatePrefix: (category: string) => void;
  onApplySelected: () => void;
  themeColor?: 'blue' | 'orange' | 'teal' | 'indigo';
}

export const CategoryPrefixDialog: React.FC<CategoryPrefixDialogProps> = ({
  isOpen,
  onClose,
  category,
  categoryDisplayName,
  categoryPrefixes,
  selectedPrefixes,
  onPrefixToggle,
  onDeletePrefix,
  onToggleFavorite,
  onEditPrefix,
  onCreatePrefix,
  onApplySelected,
  themeColor = 'blue'
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // 重置搜索状态
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setShowFavoritesOnly(false);
    }
  }, [isOpen]);

  // 过滤前置消息
  const getFilteredPrefixes = (): SavedAIPrefix[] => {
    let filtered = categoryPrefixes;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(prefix =>
        prefix.content.toLowerCase().includes(query) ||
        prefix.description.toLowerCase().includes(query) ||
        prefix.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(prefix => prefix.isFavorite);
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const filteredPrefixes = getFilteredPrefixes();

  const handlePrefixClick = (prefix: SavedAIPrefix, e: React.MouseEvent) => {
    e.stopPropagation();
    onPrefixToggle(prefix.id);
  };

  const handleDeleteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('确定要删除这个前置消息吗？')) {
      onDeletePrefix(prefixId);
    }
  };

  const handleFavoriteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(prefixId);
  };

  const handleSelectAll = () => {
    filteredPrefixes.forEach(prefix => {
      if (!selectedPrefixes.has(prefix.id)) {
        onPrefixToggle(prefix.id);
      }
    });
  };

  const handleClearSelection = () => {
    filteredPrefixes.forEach(prefix => {
      if (selectedPrefixes.has(prefix.id)) {
        onPrefixToggle(prefix.id);
      }
    });
  };

  if (!isOpen) return null;

  // 主题色配置
  const themeConfig = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-800',
      textSecondary: 'text-blue-600',
      button: 'bg-blue-500 hover:bg-blue-600',
      buttonBorder: 'border-blue-300 text-blue-600',
      focus: 'focus:ring-blue-500',
      cardBorder: 'border-blue-500 bg-blue-50',
      cardHover: 'border-blue-300 hover:bg-blue-25',
      tag: 'bg-blue-100 text-blue-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      text: 'text-orange-800',
      textSecondary: 'text-orange-600',
      button: 'bg-orange-500 hover:bg-orange-600',
      buttonBorder: 'border-orange-300 text-orange-600',
      focus: 'focus:ring-orange-500',
      cardBorder: 'border-orange-500 bg-orange-50',
      cardHover: 'border-orange-300 hover:bg-orange-25',
      tag: 'bg-orange-100 text-orange-600'
    },
    teal: {
      bg: 'bg-teal-50',
      border: 'border-teal-200',
      text: 'text-teal-800',
      textSecondary: 'text-teal-600',
      button: 'bg-teal-500 hover:bg-teal-600',
      buttonBorder: 'border-teal-300 text-teal-600',
      focus: 'focus:ring-teal-500',
      cardBorder: 'border-teal-500 bg-teal-50',
      cardHover: 'border-teal-300 hover:bg-teal-25',
      tag: 'bg-teal-100 text-teal-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-800',
      textSecondary: 'text-indigo-600',
      button: 'bg-indigo-500 hover:bg-indigo-600',
      buttonBorder: 'border-indigo-300 text-indigo-600',
      focus: 'focus:ring-indigo-500',
      cardBorder: 'border-indigo-500 bg-indigo-50',
      cardHover: 'border-indigo-300 hover:bg-indigo-25',
      tag: 'bg-indigo-100 text-indigo-600'
    }
  };

  const theme = themeConfig[themeColor];



  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10002] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className={`text-xl font-bold ${theme.text} flex items-center`}>
              {getCategoryIcon(category, 24, `${theme.text} mr-2`)}
              {categoryDisplayName}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {categoryDisplayName}类别前置消息管理 - 共 {categoryPrefixes.length} 个条目
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onCreatePrefix(category)}
              className={`flex items-center px-3 py-2 text-sm ${theme.buttonBorder} border rounded-lg hover:${theme.bg}`}
              title={`创建新的${categoryDisplayName}前置消息`}
            >
              <PlusIcon size={16} className="mr-1" />
              新建
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XIcon size={24} />
            </button>
          </div>
        </div>

        {/* 过滤器区域 */}
        <div className={`p-4 border-b border-gray-200 ${theme.bg}`}>
          <div className="flex flex-wrap gap-4 items-center">
            {/* 搜索框 */}
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder={`搜索${categoryDisplayName}前置消息...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 rounded-lg ${theme.focus}`}
              />
            </div>

            {/* 收藏过滤 */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showFavoritesOnly}
                onChange={(e) => setShowFavoritesOnly(e.target.checked)}
                className={`rounded border-gray-300 ${theme.textSecondary} ${theme.focus}`}
              />
              <span className="text-sm text-gray-700">仅显示收藏</span>
            </label>

            {/* 统计信息 */}
            <div className={`text-sm ${theme.textSecondary}`}>
              显示 {filteredPrefixes.length} / {categoryPrefixes.length} 个条目
            </div>
          </div>
        </div>

        {/* 操作栏 */}
        <div className={`p-4 border-b border-gray-200 ${theme.bg}`}>
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                disabled={filteredPrefixes.length === 0}
                className={`px-3 py-1 text-sm ${theme.buttonBorder} border rounded hover:${theme.bg} disabled:text-gray-400 disabled:border-gray-300`}
              >
                全选 ({filteredPrefixes.length})
              </button>
              <button
                onClick={handleClearSelection}
                disabled={selectedPrefixes.size === 0}
                className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:text-gray-400"
              >
                清空选择
              </button>
            </div>

            <div className="text-sm text-gray-600">
              已选择 {Array.from(selectedPrefixes).filter(id => 
                categoryPrefixes.some(p => p.id === id)
              ).length} 个条目
            </div>
          </div>
        </div>

        {/* 前置消息列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredPrefixes.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">{getCategoryIcon(category)}</div>
              <p>没有找到匹配的{categoryDisplayName}前置消息</p>
              <p className="text-sm mt-2">尝试调整搜索条件</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPrefixes.map((prefix) => (
                <div
                  key={prefix.id}
                  onClick={(e) => handlePrefixClick(prefix, e)}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedPrefixes.has(prefix.id)
                      ? theme.cardBorder
                      : `border-gray-200 ${theme.cardHover}`
                  }`}
                >
                  {/* 头部信息 */}
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2 flex-wrap">
                      <span className={`text-xs px-2 py-1 ${theme.tag} rounded`}>
                        {categoryDisplayName}
                      </span>
                      {prefix.phase && (
                        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded">
                          {prefix.phase}
                        </span>
                      )}
                    </div>

                    <div className="flex space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditPrefix(prefix);
                        }}
                        className="text-gray-400 hover:text-blue-500 transition-colors"
                        title="编辑"
                      >
                        <EditIcon size={16} />
                      </button>
                      <button
                        onClick={(e) => handleFavoriteClick(prefix.id, e)}
                        className={`${prefix.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-500 transition-colors`}
                        title="收藏"
                      >
                        {prefix.isFavorite ? <StarFilledIcon size={16} /> : <StarIcon size={16} />}
                      </button>
                      <button
                        onClick={(e) => handleDeleteClick(prefix.id, e)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        title="删除"
                      >
                        <DeleteIcon size={16} />
                      </button>
                    </div>
                  </div>

                  {/* 内容 */}
                  <div className="mb-3">
                    <p className="text-sm text-gray-800 line-clamp-3">
                      {prefix.content}
                    </p>
                  </div>

                  {/* 描述 */}
                  <div className="mb-2">
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {prefix.description}
                    </p>
                  </div>

                  {/* 底部信息 */}
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>使用 {prefix.usageCount} 次</span>
                    <span>{new Date(prefix.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部操作 */}
        <div className={`p-4 border-t border-gray-200 ${theme.bg}`}>
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              显示 {filteredPrefixes.length} / {categoryPrefixes.length} 个{categoryDisplayName}条目
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => {
                  onApplySelected();
                  onClose();
                }}
                disabled={Array.from(selectedPrefixes).filter(id => 
                  categoryPrefixes.some(p => p.id === id)
                ).length === 0}
                className={`px-4 py-2 ${theme.button} text-white rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed`}
              >
                应用选择 ({Array.from(selectedPrefixes).filter(id => 
                  categoryPrefixes.some(p => p.id === id)
                ).length}个)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
