"use client";

import React, { useState, useMemo } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';
import { PhaseAIPanel } from '../components/PhaseAIPanel';

/**
 * 右侧节奏控制面板
 * 显示创作节奏、情绪曲线、阶段控制等功能
 */
export const RhythmControlPanel: React.FC = () => {
  const {
    segments,
    rhythmState,
    editingState,
    setRhythmState
  } = useShortStoryStore();

  // 本地状态
  const [selectedPhase, setSelectedPhase] = useState<string | null>(null);
  const [showAIMode, setShowAIMode] = useState(false);
  const [currentAIPhase, setCurrentAIPhase] = useState<any>(null);

  // 基于您提供的短篇结构定义阶段
  const phases = [
    {
      key: 'intro',
      name: '导语',
      range: '50-150字',
      color: '#3B82F6',
      description: '悬念/反转/爽点，写出爆点',
      formula: '受辱 + 反击 + 打脸'
    },
    {
      key: 'setup',
      name: '铺垫期',
      range: '0-2000字',
      color: '#10B981',
      description: '拉情绪，主角受辱/嘲讽/贬低'
    },
    {
      key: 'compression',
      name: '爆发情绪',
      range: '2000-5000字',
      color: '#F59E0B',
      description: '铺垫反击，把最气人的片段放上，然后主角打脸，拉小高潮'
    },
    {
      key: 'climax',
      name: '反转',
      range: '5000-8000字',
      color: '#EF4444',
      description: '可以多来点反转，如反派各种陷害/阻挠，主角见招拆招，打脸反派，爽点密集'
    },
    {
      key: 'resolution',
      name: '让读者解气',
      range: '8000-10000字',
      color: '#8B5CF6',
      description: '反派作死不能，开始低声下气装可怜，主角可假意和好/信任'
    },
    {
      key: 'ending',
      name: '大结局',
      range: '10000-12000字',
      color: '#EC4899',
      description: '再次反转，主角怎么可能让反派顺心，大仇小仇一起报，怎么爽怎么来，读者看的开心'
    },
    {
      key: 'custom',
      name: '自定义',
      range: '灵活字数',
      color: '#6B7280',
      description: '通用创作阶段，可根据具体需求灵活调整',
      formula: '需求分析 + 灵活应用 + 个性化指导'
    }
  ];

  // 计算当前阶段
  const currentPhase = useMemo(() => {
    const totalWords = segments.reduce((sum, seg) => sum + (seg.wordCount || 0), 0);

    // 调试信息 - 在开发环境中显示
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 RhythmControlPanel 阶段判断调试:', {
        totalWords,
        segments: segments.map(seg => ({
          content: seg.content ? `${seg.content.substring(0, 20)}...` : '无内容',
          wordCount: seg.wordCount,
          contentLength: seg.content?.length || 0
        })),
        当前阶段: totalWords < 150 ? phases[0].name :
                 totalWords < 2000 ? phases[1].name :
                 totalWords < 5000 ? phases[2].name :
                 totalWords < 8000 ? phases[3].name :
                 totalWords < 10000 ? phases[4].name : phases[5].name
      });
    }

    if (totalWords < 150) return phases[0];
    if (totalWords < 2000) return phases[1];
    if (totalWords < 5000) return phases[2];
    if (totalWords < 8000) return phases[3];
    if (totalWords < 10000) return phases[4];
    return phases[5];
  }, [segments]);

  // 计算情绪曲线数据
  const emotionCurveData = useMemo(() => {
    if (segments.length === 0) return [];

    return segments.map((segment, index) => ({
      x: (index / (segments.length - 1)) * 100,
      y: 100 - (segment.tensionLevel || 0) * 10,
      segment: segment,
      index: index
    }));
  }, [segments]);

  // 生成SVG路径
  const generateCurvePath = (data: any[]) => {
    if (data.length === 0) return '';

    let path = `M ${data[0].x} ${data[0].y}`;

    for (let i = 1; i < data.length; i++) {
      const prev = data[i - 1];
      const curr = data[i];
      const cp1x = prev.x + (curr.x - prev.x) * 0.3;
      const cp1y = prev.y;
      const cp2x = curr.x - (curr.x - prev.x) * 0.3;
      const cp2y = curr.y;

      path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
    }

    return path;
  };

  // 处理当前阶段点击 - 切换到AI交互模式
  const handleCurrentPhaseClick = () => {
    setCurrentAIPhase(currentPhase);
    setShowAIMode(true);
  };

  // 处理阶段卡片点击 - 展开/收起详情
  const handlePhaseCardClick = (phase: any) => {
    setSelectedPhase(selectedPhase === phase.key ? null : phase.key);
  };

  // 处理阶段AI助手切换
  const handlePhaseAIClick = (phase: any, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡
    setCurrentAIPhase(phase);
    setShowAIMode(true);
    setSelectedPhase(null); // 清除选中状态
  };

  // 返回节奏控制模式
  const handleBackToRhythm = () => {
    setShowAIMode(false);
    setCurrentAIPhase(null);
  };

  // 处理AI内容更新
  const handleAIContentUpdate = (content: string, position?: number) => {
    // 这里后续实现内容更新逻辑
    console.log('AI内容更新:', { content, position });
  };

  // 如果在AI模式，显示AI面板
  if (showAIMode && currentAIPhase) {
    return (
      <div
        className="h-full bg-gray-50 transition-all duration-300 ease-in-out"
        style={{
          maxHeight: '100%',
          overflow: 'hidden',
          contain: 'layout'
        }}
      >
        <PhaseAIPanel
          phase={currentAIPhase}
          onBack={handleBackToRhythm}
          onContentUpdate={handleAIContentUpdate}
          selectedACEFrameworkIds={undefined} // 从store获取
        />
      </div>
    );
  }

  return (
    <div
      className="h-full flex flex-col bg-gray-50 transition-all duration-300 ease-in-out"
      style={{
        maxHeight: '100%',
        overflow: 'hidden',
        contain: 'layout'
      }}
    >
      {/* 面板标题 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-white">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <span className="mr-2">📊</span>
          节奏控制
        </h2>
        <p className="text-sm text-gray-600 mt-1">监控创作节奏和情绪变化</p>
      </div>

      {/* 滚动内容区域 */}
      <div
        className="flex-1 overflow-y-auto p-4 space-y-6"
        style={{
          minHeight: 0,
          maxHeight: 'calc(100% - 100px)' // 为标题栏预留空间
        }}
      >
        {/* 当前阶段指示器 */}
        <div className="bg-white rounded-xl p-4 border border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-800">当前阶段</h3>
            {!showAIMode && (
              <span className="text-xs text-blue-500">点击进入AI助手</span>
            )}
          </div>

          <div
            className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
              showAIMode ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
            }`}
            style={{
              borderColor: currentPhase.color,
              backgroundColor: `${currentPhase.color}10`
            }}
            onClick={handleCurrentPhaseClick}
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium" style={{ color: currentPhase.color }}>
                {currentPhase.name}
                {showAIMode && (
                  <span className="ml-2 text-xs bg-blue-500 text-white px-2 py-1 rounded">
                    AI助手模式
                  </span>
                )}
              </h4>
              <span className="text-sm text-gray-500">{currentPhase.range}</span>
            </div>

            <p className="text-sm text-gray-700 leading-relaxed">
              {currentPhase.description}
            </p>

            {currentPhase.formula && (
              <div className="mt-2 text-xs text-gray-600 font-mono">
                公式: {currentPhase.formula}
              </div>
            )}
          </div>
        </div>

        {/* 情绪曲线图 */}
        <div className="bg-white rounded-xl p-4 border border-gray-200">
          <h3 className="font-semibold text-gray-800 mb-3">情绪曲线</h3>

          {segments.length > 0 ? (
            <div className="relative">
              <svg viewBox="0 0 100 100" className="w-full h-32 border border-gray-200 rounded">
                {/* 网格线 */}
                <defs>
                  <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f3f4f6" strokeWidth="0.5"/>
                  </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />

                {/* 情绪曲线 */}
                {emotionCurveData.length > 1 && (
                  <path
                    d={generateCurvePath(emotionCurveData)}
                    stroke="#3B82F6"
                    strokeWidth="2"
                    fill="none"
                  />
                )}

                {/* 数据点 */}
                {emotionCurveData.map((point, index) => (
                  <circle
                    key={index}
                    cx={point.x}
                    cy={point.y}
                    r="3"
                    fill={point.segment.content ? "#10B981" : "#D1D5DB"}
                    stroke="#fff"
                    strokeWidth="1"
                    className="cursor-pointer"
                    title={`第${index + 1}段 - 紧张度: ${point.segment.tensionLevel || 0}`}
                  />
                ))}
              </svg>

              {/* 图例 */}
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span>开始</span>
                <span>情绪强度</span>
                <span>结束</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📈</div>
              <p>生成分段结构后将显示情绪曲线</p>
            </div>
          )}
        </div>

        {/* 阶段进度 */}
        <div className="bg-white rounded-xl p-4 border border-gray-200">
          <h3 className="font-semibold text-gray-800 mb-3">创作进度</h3>

          <div className="space-y-3">
            {phases.map((phase, index) => {
              const isActive = phase.key === currentPhase.key;
              const isCompleted = phases.indexOf(currentPhase) > index;

              return (
                <div
                  key={phase.key}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all
                    ${isActive
                      ? 'border-blue-500 bg-blue-50'
                      : isCompleted
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                  onClick={() => handlePhaseCardClick(phase)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: phase.color }}
                      />
                      <span className="font-medium text-sm">{phase.name}</span>
                      {isCompleted && (
                        <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => handlePhaseAIClick(phase, e)}
                        className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                        title={`进入${phase.name}AI助手`}
                      >
                        🤖 AI助手
                      </button>
                      <span className="text-xs text-gray-500">{phase.range}</span>
                    </div>
                  </div>

                  {selectedPhase === phase.key && (
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {phase.description}
                      </p>
                      {phase.formula && (
                        <div className="mt-1 text-xs text-gray-500 font-mono">
                          {phase.formula}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-xl p-4 border border-gray-200">
          <h3 className="font-semibold text-gray-800 mb-3">创作统计</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {segments.reduce((sum, seg) => sum + (seg.wordCount || 0), 0)}
              </div>
              <div className="text-xs text-gray-500">总字数</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {segments.filter(seg => seg.content).length}
              </div>
              <div className="text-xs text-gray-500">已完成段落</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round((segments.filter(seg => seg.content).length / Math.max(segments.length, 1)) * 100)}%
              </div>
              <div className="text-xs text-gray-500">完成进度</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {segments.length > 0 ? Math.ceil(segments.reduce((sum, seg) => sum + (seg.wordCount || 0), 0) / 300) : 0}
              </div>
              <div className="text-xs text-gray-500">预计阅读(分钟)</div>
            </div>
          </div>
        </div>

        {/* 节奏调整工具 */}
        {segments.length > 0 && (
          <div className="bg-white rounded-xl p-4 border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-3">节奏调整</h3>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  整体节奏强度
                </label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={rhythmState.emotionCurve.length > 0 ?
                    Math.round(rhythmState.emotionCurve.reduce((a, b) => a + b, 0) / rhythmState.emotionCurve.length) : 5
                  }
                  onChange={(e) => {
                    // 这里可以添加节奏调整逻辑
                  }}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>平缓</span>
                  <span>适中</span>
                  <span>激烈</span>
                </div>
              </div>

              <button className="w-full py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                应用节奏调整
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
