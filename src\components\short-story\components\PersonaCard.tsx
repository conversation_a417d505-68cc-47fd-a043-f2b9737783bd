"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AIPersonaConfig, PhaseType } from '../../../types/ai-persona';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';

interface PersonaCardProps {
  persona: AIPersonaConfig;
  onSelect: () => void;
  onVersionManage: () => void;
  onCategoryManage?: () => void;
  getPhaseDisplayName: (phase: PhaseType) => string;
  className?: string;
}

const PersonaCard: React.FC<PersonaCardProps> = ({
  persona,
  onSelect,
  onVersionManage,
  onCategoryManage,
  getPhaseDisplayName,
  className = ''
}) => {
  const [versionCount, setVersionCount] = useState(0);
  const [categoryCount, setCategoryCount] = useState(0);
  const [usageStats, setUsageStats] = useState({ usageCount: 0, lastUsedAt: new Date() });
  const [personaStorageService] = useState(() => PersonaStorageService.getInstance());

  // 加载额外信息
  useEffect(() => {
    const loadExtraInfo = async () => {
      try {
        // 获取版本数量
        const versions = await personaStorageService.getPersonaVersions(persona.id);
        setVersionCount(versions.length);

        // 获取分类数量
        const categories = await personaStorageService.getPersonaCategories(persona.id);
        setCategoryCount(categories.length);

        // 获取使用统计
        const stats = await personaStorageService.getUsageStats(persona.id);
        setUsageStats(stats);
      } catch (error) {
        console.error('加载人设额外信息失败:', error);
      }
    };

    loadExtraInfo();
  }, [persona.id, personaStorageService]);

  // 获取阶段颜色
  const getPhaseColor = (phase: PhaseType): string => {
    const colors: Record<PhaseType, string> = {
      'intro': 'bg-blue-100 text-blue-700 border-blue-200',
      'setup': 'bg-orange-100 text-orange-700 border-orange-200',
      'compression': 'bg-red-100 text-red-700 border-red-200',
      'climax': 'bg-purple-100 text-purple-700 border-purple-200',
      'resolution': 'bg-green-100 text-green-700 border-green-200',
      'ending': 'bg-gray-100 text-gray-700 border-gray-200',
      'buildup': 'bg-yellow-100 text-yellow-700 border-yellow-200',
      'custom': 'bg-indigo-100 text-indigo-700 border-indigo-200'
    };
    return colors[phase] || colors.custom;
  };

  // 获取阶段图标
  const getPhaseIcon = (phase: PhaseType): string => {
    const icons: Record<PhaseType, string> = {
      'intro': '🚀',
      'setup': '🏗️',
      'compression': '💥',
      'climax': '⚡',
      'resolution': '🎯',
      'ending': '🏁',
      'buildup': '📈',
      'custom': '⚙️'
    };
    return icons[phase] || icons.custom;
  };

  // 格式化最后使用时间
  const formatLastUsed = (date?: Date): string => {
    if (!date) return '从未使用';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    const now = new Date();
    const diffMs = now.getTime() - validDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    return `${Math.floor(diffDays / 30)}月前`;
  };

  return (
    <motion.div
      layout
      whileHover={{
        y: -4,
        scale: 1.02,
        transition: { type: "spring", stiffness: 400, damping: 25 }
      }}
      whileTap={{ scale: 0.98 }}
      className={`min-h-[200px] p-5 rounded-xl border cursor-pointer relative overflow-hidden bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-sm hover:shadow-lg transition-all duration-300 ${className}`}
      onClick={onSelect}
    >
      {/* 内容 */}
      <div className="relative z-10">
        {/* 头部：阶段标签和操作按钮 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <span className="text-lg mr-2">{getPhaseIcon(persona.phase)}</span>
            <span className={`text-xs px-2 py-1 rounded-full border ${getPhaseColor(persona.phase)}`}>
              {getPhaseDisplayName(persona.phase)}
            </span>
          </div>

          {/* 操作按钮组 */}
          <div className="flex items-center space-x-1">
            {/* 分类管理按钮 */}
            {onCategoryManage && (
              <motion.button
                className="p-1 rounded-md text-gray-400 hover:text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onCategoryManage();
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="分类管理"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </motion.button>
            )}

            {/* 版本管理按钮 */}
            <motion.button
              className="p-1 rounded-md text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onVersionManage();
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title="版本管理"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
              </svg>
            </motion.button>
          </div>
        </div>

        {/* 人设名称 */}
        <h4 className="font-semibold text-base mb-3 line-clamp-2 text-gray-900 dark:text-gray-100">
          {persona.phase === 'custom' ? '自定义人设' : getPhaseDisplayName(persona.phase)}
        </h4>

        {/* 人设描述（系统提示词预览） */}
        <p className="text-sm leading-relaxed mb-4 line-clamp-3 text-gray-600 dark:text-gray-400">
          {persona.systemPrompt.length > 100
            ? `${persona.systemPrompt.substring(0, 100)}...`
            : persona.systemPrompt}
        </p>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">{versionCount}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">版本</div>
          </div>
          <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">{categoryCount}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">分类</div>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="flex items-center justify-between mt-auto">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{formatLastUsed(usageStats?.lastUsedAt)}</span>
          </div>

          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>{usageStats?.usageCount || 0} 次使用</span>
          </div>
        </div>

        {/* 默认人设标识 */}
        {persona.metadata.isDefault && (
          <div className="absolute top-2 right-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800 border border-yellow-200">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              默认
            </span>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default PersonaCard;
