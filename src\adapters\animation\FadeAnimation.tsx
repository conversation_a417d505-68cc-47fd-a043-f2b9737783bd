"use client";

import React, { useEffect, useState } from 'react';
import { createAnimationFactory } from '@/factories/animation/AnimationFactory';
import { FadeDirection } from '@/factories/animation/interfaces';

interface FadeAnimationProps {
  children: React.ReactNode;
  direction?: FadeDirection;
  duration?: number;
  delay?: number;
  visible?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 淡入淡出动画适配器组件
 */
const FadeAnimation: React.FC<FadeAnimationProps> = ({
  children,
  direction = 'none',
  duration = 300,
  delay = 0,
  visible = true,
  className = '',
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  
  // 创建动画工厂和组件
  const animationFactory = createAnimationFactory();
  const fadeAnimation = animationFactory.createFadeAnimation(direction, duration, delay, isVisible);
  
  // 设置动画属性
  useEffect(() => {
    fadeAnimation.setDirection(direction);
  }, [direction]);
  
  useEffect(() => {
    fadeAnimation.setDuration(duration);
  }, [duration]);
  
  useEffect(() => {
    fadeAnimation.setDelay(delay);
  }, [delay]);
  
  // 控制可见性
  useEffect(() => {
    if (visible) {
      // 延迟一帧，确保DOM已经渲染
      requestAnimationFrame(() => {
        setIsVisible(true);
      });
    } else {
      setIsVisible(false);
    }
  }, [visible]);
  
  // 如果不可见且没有动画，则不渲染
  if (!visible && !isVisible) {
    return null;
  }
  
  // 合并样式和类名
  const animationClassName = fadeAnimation.getClassName();
  const animationStyle = fadeAnimation.getStyle();
  
  const mergedClassName = `${animationClassName} ${className}`.trim();
  const mergedStyle = { ...animationStyle, ...style };
  
  return (
    <div className={mergedClassName} style={mergedStyle}>
      {children}
    </div>
  );
};

export default FadeAnimation;
