"use client";

import React from 'react';
import { Chapter, Character } from '@/lib/db/dexie';
import ChapterCharacterExtractor from '@/factories/ui/components/ChapterEditor/ChapterCharacterExtractor';
import { CharacterExtractionResult } from '@/adapters/ai/CharacterExtractorAIAdapter';

interface ChapterCharacterExtractorAdapterProps {
  chapter: Chapter;
  existingCharacters: Character[];
  onCreateCharacter: (characterInfo: any) => Promise<void>;
  onUpdateCharacter: (character: Character, characterInfo: any) => Promise<void>;
}

/**
 * 章节人物提取适配器
 * 用于在章节编辑器中集成人物提取功能
 */
export const ChapterCharacterExtractorAdapter: React.FC<ChapterCharacterExtractorAdapterProps> = ({
  chapter,
  existingCharacters,
  onCreateCharacter,
  onUpdateCharacter
}) => {
  /**
   * 处理创建人物
   * @param characterInfo 人物信息
   */
  const handleCreateCharacter = async (characterInfo: CharacterExtractionResult) => {
    try {
      await on<PERSON><PERSON><PERSON>haracter(characterInfo);
    } catch (error) {
      console.error('创建人物失败:', error);
    }
  };

  /**
   * 处理更新人物
   * @param character 人物对象
   * @param characterInfo 人物信息
   */
  const handleUpdateCharacter = async (character: Character, characterInfo: CharacterExtractionResult) => {
    try {
      await onUpdateCharacter(character, characterInfo);
    } catch (error) {
      console.error('更新人物失败:', error);
    }
  };

  return (
    <ChapterCharacterExtractor
      chapter={chapter}
      existingCharacters={existingCharacters}
      onCreateCharacter={handleCreateCharacter}
      onUpdateCharacter={handleUpdateCharacter}
    />
  );
};

export default ChapterCharacterExtractorAdapter;
