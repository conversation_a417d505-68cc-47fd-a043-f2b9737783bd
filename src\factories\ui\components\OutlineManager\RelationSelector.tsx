"use client";

import React, { useState, useEffect } from 'react';
import { useOutlineManager } from '../../hooks/useOutlineManager';
import { OutlineNodeType } from '../../types/outline';
import { db } from '@/lib/db/dexie';

interface RelationSelectorProps {
  node: OutlineNodeType;
  bookId: string;
  onClose: () => void;
}

/**
 * 关联选择器组件
 * 用于选择要关联的元素
 */
export const RelationSelector: React.FC<RelationSelectorProps> = ({ node, bookId, onClose }) => {
  const { addNodeRelation } = useOutlineManager();
  
  const [activeTab, setActiveTab] = useState<'character' | 'worldBuilding' | 'terminology'>('character');
  const [characters, setCharacters] = useState<any[]>([]);
  const [worldBuildings, setWorldBuildings] = useState<any[]>([]);
  const [terminologies, setTerminologies] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // 加载可选元素
  useEffect(() => {
    const loadElements = async () => {
      setIsLoading(true);
      try {
        switch (activeTab) {
          case 'character':
            const characters = await db.characters.where('bookId').equals(bookId).toArray();
            setCharacters(characters);
            break;
          case 'worldBuilding':
            const worldBuildings = await db.worldBuilding.where('bookId').equals(bookId).toArray();
            setWorldBuildings(worldBuildings);
            break;
          case 'terminology':
            const terminologies = await db.terminology.where('bookId').equals(bookId).toArray();
            setTerminologies(terminologies);
            break;
        }
      } catch (error) {
        console.error('加载元素失败:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadElements();
  }, [activeTab, bookId]);
  
  // 处理添加关联
  const handleAddRelation = async (relationType: 'character' | 'worldBuilding' | 'terminology', relationId: string) => {
    try {
      await addNodeRelation(node.id, relationType, relationId);
      onClose();
    } catch (error) {
      console.error('添加关联失败:', error);
    }
  };
  
  // 过滤元素
  const getFilteredElements = () => {
    const term = searchTerm.toLowerCase();
    
    switch (activeTab) {
      case 'character':
        return characters.filter(item => 
          item.name.toLowerCase().includes(term) || 
          (item.alias && item.alias.some((alias: string) => alias.toLowerCase().includes(term)))
        );
      case 'worldBuilding':
        return worldBuildings.filter(item => 
          item.name.toLowerCase().includes(term) || 
          item.category.toLowerCase().includes(term)
        );
      case 'terminology':
        return terminologies.filter(item => 
          item.name.toLowerCase().includes(term) || 
          item.category.toLowerCase().includes(term) ||
          (item.alias && item.alias.some((alias: string) => alias.toLowerCase().includes(term)))
        );
      default:
        return [];
    }
  };
  
  // 检查元素是否已关联
  const isElementRelated = (relationType: 'character' | 'worldBuilding' | 'terminology', relationId: string) => {
    switch (relationType) {
      case 'character':
        return node.relatedCharacterIds?.includes(relationId) || false;
      case 'worldBuilding':
        return node.relatedWorldBuildingIds?.includes(relationId) || false;
      case 'terminology':
        return node.relatedTerminologyIds?.includes(relationId) || false;
      default:
        return false;
    }
  };
  
  const filteredElements = getFilteredElements();
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">添加关联</h3>
      
      {/* 标签页 */}
      <div className="flex border-b mb-4">
        <button
          className={`px-4 py-2 ${activeTab === 'character' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('character')}
        >
          人物
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'worldBuilding' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('worldBuilding')}
        >
          世界观
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'terminology' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('terminology')}
        >
          术语
        </button>
      </div>
      
      {/* 搜索框 */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="搜索..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      
      {/* 内容区域 */}
      <div className="mt-4 max-h-60 overflow-y-auto">
        {isLoading ? (
          <div className="text-center py-4">加载中...</div>
        ) : (
          <>
            {filteredElements.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {searchTerm ? '没有找到匹配的元素' : '暂无可选元素'}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredElements.map(item => {
                  const isRelated = isElementRelated(activeTab, item.id);
                  
                  return (
                    <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div>
                        <div className="font-medium">{item.name}</div>
                        {activeTab === 'character' && item.alias && item.alias.length > 0 && (
                          <div className="text-xs text-gray-500">别名: {item.alias.join(', ')}</div>
                        )}
                        {(activeTab === 'worldBuilding' || activeTab === 'terminology') && (
                          <div className="text-xs text-gray-500">{item.category}</div>
                        )}
                      </div>
                      <button
                        className={`px-3 py-1 rounded text-sm ${
                          isRelated 
                            ? 'bg-gray-200 text-gray-500 cursor-not-allowed' 
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                        onClick={() => !isRelated && handleAddRelation(activeTab, item.id)}
                        disabled={isRelated}
                      >
                        {isRelated ? '已关联' : '添加'}
                      </button>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </div>
      
      {/* 底部按钮 */}
      <div className="mt-4 flex justify-end">
        <button
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          onClick={onClose}
        >
          关闭
        </button>
      </div>
    </div>
  );
};
