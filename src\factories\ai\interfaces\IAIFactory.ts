import { IAIWritingComponent } from './IAIWritingComponent';
import { IAIBookAnalysisComponent } from './IAIBookAnalysisComponent';
import { IAIRewriteComponent } from './IAIRewriteComponent';
import { IAIContinueComponent } from './IAIContinueComponent';
import { IAISenderComponent } from './IAISenderComponent';
import { IAICharacterComponent } from './IAICharacterComponent';

/**
 * AI工厂接口
 * 用于创建各种AI功能组件
 */
export interface IAIFactory {
  /**
   * 创建AI写作组件
   * @returns AI写作组件
   */
  createAIWritingComponent(): IAIWritingComponent;

  /**
   * 创建AI拆书组件
   * @returns AI拆书组件
   */
  createAIBookAnalysisComponent(): IAIBookAnalysisComponent;

  /**
   * 创建AI选中改写组件
   * @returns AI选中改写组件
   */
  createAIRewriteComponent(): IAIRewriteComponent;

  /**
   * 创建AI续写组件
   * @returns AI续写组件
   */
  createAIContinueComponent(): IAIContinueComponent;

  /**
   * 创建AI发送组件
   * @returns AI发送组件
   */
  createAISenderComponent(): IAISenderComponent;

  /**
   * 创建AI人物组件
   * @returns AI人物组件
   */
  createAICharacterComponent(): IAICharacterComponent;
}
