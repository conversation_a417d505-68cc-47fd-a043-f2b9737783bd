"use client";

import React, { useEffect, useState, useMemo } from 'react';

interface InkRippleEffectProps {
  x: number;
  y: number;
  onComplete?: () => void;
}

/**
 * 增强版彩虹波纹效果组件
 * 在用户输入时在光标位置创建多层彩虹波纹和星光粒子动画
 */
export const InkRippleEffect: React.FC<InkRippleEffectProps> = ({
  x,
  y,
  onComplete
}) => {
  const [isVisible, setIsVisible] = useState(true);

  // 生成动态色彩
  const colorScheme = useMemo(() => {
    const timestamp = Date.now();
    const baseHue = (timestamp % 360);

    return {
      primary: `hsl(${baseHue}, 85%, 60%)`,
      secondary: `hsl(${(baseHue + 30) % 360}, 80%, 65%)`,
      tertiary: `hsl(${(baseHue + 60) % 360}, 75%, 70%)`,
      particles: `hsl(${(baseHue + 90) % 360}, 90%, 75%)`
    };
  }, [x, y]);

  // 生成随机粒子数据
  const particles = useMemo(() => {
    const count = 8 + Math.floor(Math.random() * 5); // 8-12个粒子
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      angle: (Math.PI * 2 * i) / count + Math.random() * 0.5,
      distance: 15 + Math.random() * 10,
      size: 0.3 + Math.random() * 0.4,
      delay: Math.random() * 0.3,
      duration: 0.8 + Math.random() * 0.4
    }));
  }, [x, y]);

  useEffect(() => {
    // 动画完成后隐藏组件
    const timer = setTimeout(() => {
      setIsVisible(false);
      onComplete?.();
    }, 1200); // 增加动画持续时间

    return () => clearTimeout(timer);
  }, [onComplete]);

  if (!isVisible) return null;

  return (
    <div
      className="absolute pointer-events-none"
      style={{
        left: x - 25, // 扩大中心偏移
        top: y - 25,
        width: 50,
        height: 50,
        zIndex: 10
      }}
    >
      <svg
        width="50"
        height="50"
        viewBox="0 0 50 50"
        style={{
          overflow: 'visible'
        }}
      >
        <defs>
          {/* 主波纹渐变 */}
          <radialGradient id={`primaryRipple-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.primary} stopOpacity="0.8" />
            <stop offset="30%" stopColor={colorScheme.primary} stopOpacity="0.6" />
            <stop offset="60%" stopColor={colorScheme.secondary} stopOpacity="0.4" />
            <stop offset="80%" stopColor={colorScheme.tertiary} stopOpacity="0.2" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 次波纹渐变 */}
          <radialGradient id={`secondaryRipple-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.secondary} stopOpacity="0.7" />
            <stop offset="40%" stopColor={colorScheme.tertiary} stopOpacity="0.5" />
            <stop offset="70%" stopColor={colorScheme.primary} stopOpacity="0.3" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 微波纹渐变 */}
          <radialGradient id={`tertiaryRipple-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.tertiary} stopOpacity="0.6" />
            <stop offset="50%" stopColor={colorScheme.particles} stopOpacity="0.4" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 背景光晕渐变 */}
          <radialGradient id={`glowHalo-${x}-${y}`} cx="50%" cy="50%" r="70%">
            <stop offset="0%" stopColor={colorScheme.primary} stopOpacity="0.1" />
            <stop offset="50%" stopColor={colorScheme.secondary} stopOpacity="0.05" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 高斯模糊滤镜 */}
          <filter id={`glow-${x}-${y}`}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* 背景光晕 */}
        <circle
          cx="25"
          cy="25"
          r="5"
          fill={`url(#glowHalo-${x}-${y})`}
          filter={`url(#glow-${x}-${y})`}
          style={{
            animation: 'glowHalo 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards'
          }}
        />

        {/* 主波纹 */}
        <circle
          cx="25"
          cy="25"
          r="3"
          fill={`url(#primaryRipple-${x}-${y})`}
          style={{
            animation: 'primaryRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards'
          }}
        />

        {/* 次波纹 */}
        <circle
          cx="25"
          cy="25"
          r="2"
          fill={`url(#secondaryRipple-${x}-${y})`}
          style={{
            animation: 'secondaryRipple 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards',
            animationDelay: '0.1s'
          }}
        />

        {/* 微波纹 */}
        <circle
          cx="25"
          cy="25"
          r="1.5"
          fill={`url(#tertiaryRipple-${x}-${y})`}
          style={{
            animation: 'tertiaryRipple 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards',
            animationDelay: '0.2s'
          }}
        />

        {/* 星光粒子系统 */}
        <g className="star-particles">
          {particles.map((particle) => {
            const particleX = 25 + Math.cos(particle.angle) * particle.distance;
            const particleY = 25 + Math.sin(particle.angle) * particle.distance;

            return (
              <g key={particle.id}>
                {/* 主粒子 */}
                <circle
                  cx="25"
                  cy="25"
                  r={particle.size}
                  fill={colorScheme.particles}
                  opacity="0.9"
                  style={{
                    animation: `starParticle ${particle.duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`,
                    animationDelay: `${particle.delay}s`,
                    transformOrigin: '25px 25px'
                  }}
                />
                {/* 粒子光晕 */}
                <circle
                  cx="25"
                  cy="25"
                  r={particle.size * 1.5}
                  fill={colorScheme.particles}
                  opacity="0.3"
                  filter={`url(#glow-${x}-${y})`}
                  style={{
                    animation: `starGlow ${particle.duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`,
                    animationDelay: `${particle.delay}s`,
                    transformOrigin: '25px 25px'
                  }}
                />
              </g>
            );
          })}
        </g>

        <style>
          {`
            @keyframes glowHalo {
              0% {
                r: 5;
                opacity: 0.1;
                transform: scale(1);
              }
              50% {
                r: 20;
                opacity: 0.05;
                transform: scale(1.2);
              }
              100% {
                r: 35;
                opacity: 0;
                transform: scale(0.8);
              }
            }

            @keyframes primaryRipple {
              0% {
                r: 3;
                opacity: 0.8;
              }
              50% {
                r: 12;
                opacity: 0.5;
              }
              100% {
                r: 20;
                opacity: 0;
              }
            }

            @keyframes secondaryRipple {
              0% {
                r: 2;
                opacity: 0.7;
              }
              50% {
                r: 8;
                opacity: 0.4;
              }
              100% {
                r: 15;
                opacity: 0;
              }
            }

            @keyframes tertiaryRipple {
              0% {
                r: 1.5;
                opacity: 0.6;
              }
              50% {
                r: 6;
                opacity: 0.3;
              }
              100% {
                r: 12;
                opacity: 0;
              }
            }

            @keyframes starParticle {
              0% {
                opacity: 0;
                transform: scale(0) translate(0, 0) rotate(0deg);
              }
              20% {
                opacity: 1;
                transform: scale(1.2) translate(0, 0) rotate(90deg);
              }
              80% {
                opacity: 0.8;
                transform: scale(0.8) translate(var(--particle-x, 15px), var(--particle-y, 15px)) rotate(360deg);
              }
              100% {
                opacity: 0;
                transform: scale(0.3) translate(var(--particle-x, 20px), var(--particle-y, 25px)) rotate(450deg);
              }
            }

            @keyframes starGlow {
              0% {
                opacity: 0;
                transform: scale(0);
              }
              30% {
                opacity: 0.6;
                transform: scale(1.5);
              }
              70% {
                opacity: 0.3;
                transform: scale(1);
              }
              100% {
                opacity: 0;
                transform: scale(0.5);
              }
            }

            /* 为星光粒子添加随机轨迹变量 */
            .star-particles g:nth-child(1) { --particle-x: -8px; --particle-y: -12px; }
            .star-particles g:nth-child(2) { --particle-x: 15px; --particle-y: -8px; }
            .star-particles g:nth-child(3) { --particle-x: -5px; --particle-y: 18px; }
            .star-particles g:nth-child(4) { --particle-x: 12px; --particle-y: -15px; }
            .star-particles g:nth-child(5) { --particle-x: -18px; --particle-y: 5px; }
            .star-particles g:nth-child(6) { --particle-x: 8px; --particle-y: 20px; }
            .star-particles g:nth-child(7) { --particle-x: -12px; --particle-y: -5px; }
            .star-particles g:nth-child(8) { --particle-x: 20px; --particle-y: 8px; }
            .star-particles g:nth-child(9) { --particle-x: -15px; --particle-y: -18px; }
            .star-particles g:nth-child(10) { --particle-x: 5px; --particle-y: -20px; }
            .star-particles g:nth-child(11) { --particle-x: 18px; --particle-y: 12px; }
            .star-particles g:nth-child(12) { --particle-x: -10px; --particle-y: 15px; }

            @media (prefers-reduced-motion: reduce) {
              circle {
                animation: none !important;
                opacity: 0.1 !important;
                r: 3 !important;
              }
              .star-particles {
                display: none;
              }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default InkRippleEffect;
