"use client";

import React from 'react';
import { UnifiedAssociationButton } from '@/components/ui/UnifiedAssociationButton';
import { OutlineManagementButton } from '@/components/ui/OutlineManagementButton';
import { AssociationOverview } from '@/components/ui/AssociationOverview';
import FullTextAnnotationButton from '@/components/ui/FullTextAnnotationButton';

interface AIRewriteLeftPanelProps {
  bookId: string; // 新增bookId属性
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  selectedOutlineNodeIds?: string[]; // 新增大纲节点选择
  rewriteRequirements: string;
  plot: string;
  error: string | null;
  chapterContent?: string; // 新增章节内容，用于全文标注

  onRewriteRequirementsChange: (value: string) => void;
  onPlotChange: (value: string) => void;
  onOpenRewriteRequirementsTemplates: () => void;
  onOpenPlotTemplates: () => void;
  // 新增统一关联管理回调
  onAssociationsChange?: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
    outlineNodeIds: string[];
  }) => void;
  // 新增大纲节点管理回调
  onOutlineNodesChange?: (nodeIds: string[]) => void;
  // 新增全文标注回调
  onFullTextAnnotation?: () => void;
}

/**
 * AI改写左侧面板组件
 * 包含关联元素和改写要求输入
 */
const AIRewriteLeftPanel: React.FC<AIRewriteLeftPanelProps> = ({
  bookId,
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  selectedOutlineNodeIds = [],
  rewriteRequirements,
  plot,
  error,
  chapterContent,

  onRewriteRequirementsChange,
  onPlotChange,
  onOpenRewriteRequirementsTemplates,
  onOpenPlotTemplates,
  onAssociationsChange,
  onOutlineNodesChange,
  onFullTextAnnotation
}) => {
  // 处理统一关联管理的回调适配
  const handleUnifiedAssociationsChange = (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    // 保持原有的大纲节点选择，只更新其他关联
    onAssociationsChange?.({
      ...associations,
      outlineNodeIds: selectedOutlineNodeIds
    });
  };
  return (
    <div className="w-1/3 pr-4 overflow-y-auto">
      <div className="space-y-4">
        {/* 关联元素选择区域 */}
        <div className="bg-gray-50 p-4 rounded-xl border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-gray-800">关联元素</h3>
            {/* 统一关联管理按钮 */}
            <UnifiedAssociationButton
              bookId={bookId}
              selectedChapterIds={selectedChapterIds}
              selectedCharacterIds={selectedCharacterIds}
              selectedTerminologyIds={selectedTerminologyIds}
              selectedWorldBuildingIds={selectedWorldBuildingIds}
              onAssociationsChange={handleUnifiedAssociationsChange}
              variant="compact"
              className="text-xs"
            />
          </div>
          {/* 关联内容概览 */}
          <AssociationOverview
            chapterCount={selectedChapterIds.length}
            characterCount={selectedCharacterIds.length}
            terminologyCount={selectedTerminologyIds.length}
            worldBuildingCount={selectedWorldBuildingIds.length}
          />

          {/* 操作提示 */}
          <div className="mt-3 text-xs text-gray-500 text-center">
            💡 点击"统一管理"按钮来选择和管理所有类型的关联内容
          </div>
        </div>

        {/* 改写要求 */}
        <div className="bg-purple-50 p-4 rounded-xl border border-purple-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-purple-800">改写要求</h3>
            <button
              type="button"
              onClick={onOpenRewriteRequirementsTemplates}
              className="px-3 py-1 text-xs bg-purple-600 text-white rounded-lg hover:bg-purple-700 shadow-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              选择模板
            </button>
          </div>
          <textarea
            value={rewriteRequirements}
            onChange={(e) => onRewriteRequirementsChange(e.target.value)}
            className="w-full px-3 py-2 border border-purple-200 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white"
            rows={4}
            placeholder="描述您对改写内容的具体要求，例如：风格转变、情感基调、表达方式等..."
          />
        </div>

        {/* 剧情方向 */}
        <div className="bg-amber-50 p-4 rounded-xl border border-amber-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-amber-800">剧情方向</h3>
            <div className="flex items-center space-x-2">
              {/* 独立大纲管理按钮 */}
              <OutlineManagementButton
                bookId={bookId}
                selectedOutlineNodeIds={selectedOutlineNodeIds}
                onOutlineNodesChange={onOutlineNodesChange}
                variant="compact"
                className="text-xs"
              />
              {/* 全文标注按钮 */}
              <FullTextAnnotationButton
                bookId={bookId}
                chapterContent={chapterContent}
                onAnnotationStart={onFullTextAnnotation}
                variant="compact"
                className="text-xs"
              />
              <button
                type="button"
                onClick={onOpenPlotTemplates}
                className="px-3 py-1 text-xs bg-amber-600 text-white rounded-lg hover:bg-amber-700 shadow-sm transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                选择模板
              </button>
            </div>
          </div>
          <textarea
            value={plot}
            onChange={(e) => onPlotChange(e.target.value)}
            className="w-full px-3 py-2 border border-amber-200 rounded-lg shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 bg-white"
            rows={4}
            placeholder="描述您希望的剧情发展方向，例如：角色行为变化、情节转折、结局调整等..."
          />
          {/* 大纲节点选择提示 */}
          {selectedOutlineNodeIds.length > 0 && (
            <div className="mt-2 text-xs text-amber-600">
              已选择 {selectedOutlineNodeIds.length} 个大纲节点作为剧情参考
            </div>
          )}
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm shadow-sm">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIRewriteLeftPanel;
