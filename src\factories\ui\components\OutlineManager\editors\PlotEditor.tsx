"use client";

import React, { useState, useCallback } from 'react';
import { OutlineNodeType, PlotPoint, PlotType } from '../../../types/outline';

interface PlotEditorProps {
  node: OutlineNodeType;
  onChange: (updatedNode: OutlineNodeType) => void;
}

/**
 * 剧情编辑器组件
 * 专门用于编辑剧情类型的节点，提供剧情特有的字段编辑功能
 */
const PlotEditor: React.FC<PlotEditorProps> = ({ node, onChange }) => {
  // 本地状态管理
  const [title, setTitle] = useState(node.title || '');
  const [plotPoints, setPlotPoints] = useState<PlotPoint[]>(node.plotPoints || []);
  const [plotType, setPlotType] = useState<PlotType>(node.plotType || 'conflict');
  const [relatedCharacters, setRelatedCharacters] = useState<string[]>(node.relatedCharacters || []);
  const [description, setDescription] = useState(node.description || '');

  // 更新节点数据
  const updateNode = useCallback((updates: Partial<OutlineNodeType>) => {
    const updatedNode = { ...node, ...updates };
    onChange(updatedNode);
  }, [node, onChange]);

  // 处理标题变化
  const handleTitleChange = useCallback((value: string) => {
    setTitle(value);
    updateNode({ title: value });
  }, [updateNode]);

  // 处理剧情类型变化
  const handlePlotTypeChange = useCallback((value: PlotType) => {
    setPlotType(value);
    updateNode({ plotType: value });
  }, [updateNode]);

  // 处理描述变化
  const handleDescriptionChange = useCallback((value: string) => {
    setDescription(value);
    updateNode({ description: value });
  }, [updateNode]);

  // 添加剧情点
  const addPlotPoint = useCallback(() => {
    const newPlotPoint: PlotPoint = {
      id: `plot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      order: plotPoints.length + 1,
      content: '',
      avoidWriting: '避免描写：一丝xx、几分xx、些许xx。应该描写：AA具体的动作、AA明确的反应、AA直接的表现',
      type: 'setup',
      styleMethod: {
        technique: '直接描写',
        style: '客观叙述',
        tone: '中性',
        perspective: '第三人称',
        emphasis: '行动和对话并重'
      },
      formatSpecs: {
        wordCount: {
          min: 150,
          max: 400,
          target: 250
        },
        paragraphRules: {
          maxSentencesPerParagraph: 3,
          paragraphBreakRules: '对话必须独立成段，行动描写紧跟对话同段不换段，冲突场面强制换行突出戏剧张力',
          conflictHandling: '冲突升级时必须换行强调，禁止用段落分隔弱化冲突感',
          actionDialogueFlow: '严格执行对话→行动→对话节奏，行动不独立成段',
          mandatoryBreaks: '情绪转折、场景切换、说话人变化必须换行'
        },
        punctuationRules: {
          dialogueFormat: '使用「」标记对话，内层对话用\'单引号\'，避免引号嵌套混乱',
          emphasisFormat: '通过换行+具体行动强调情绪，严禁感叹号堆积和省略号滥用',
          pauseFormat: '用具体行动描写表现停顿：\'他停下脚步\'而非\'他...停下了脚步\'',
          conflictPunctuation: '冲突场面用短句+换行制造紧张感，不依赖标点符号强调',
          naturalFlow: '标点服务于阅读节奏，避免为了强调而强调'
        },
        lineBreakRules: {
          sceneTransition: '场景切换必须换行+具体行动标记转换：\'他推开门，走进办公室\'',
          timeTransition: '时间流逝用具体行动标记：\'十分钟后，鱼竿开始颤动\'',
          speakerChange: '说话人变化立即换行，保持对话清晰度和节奏感',
          conflictEscalation: '冲突升级强制换行：每个冲突动作独立行，增强视觉冲击力',
          actionEmphasis: '关键行动前后换行：重要动作要有视觉突出效果',
          emotionShift: '情绪转折点必须换行：从平静到愤怒要有明显视觉分割',
          prohibitedMerging: '严禁将冲突行动与平常描写合并在同一行'
        }
      }
    };
    const newPlotPoints = [...plotPoints, newPlotPoint];
    setPlotPoints(newPlotPoints);
    updateNode({ plotPoints: newPlotPoints });
  }, [plotPoints, updateNode]);

  // 更新剧情点
  const updatePlotPoint = useCallback((index: number, updates: Partial<PlotPoint>) => {
    const newPlotPoints = plotPoints.map((point, i) => 
      i === index ? { ...point, ...updates } : point
    );
    setPlotPoints(newPlotPoints);
    updateNode({ plotPoints: newPlotPoints });
  }, [plotPoints, updateNode]);

  // 删除剧情点
  const removePlotPoint = useCallback((index: number) => {
    const newPlotPoints = plotPoints.filter((_, i) => i !== index)
      .map((point, i) => ({ ...point, order: i + 1 })); // 重新排序
    setPlotPoints(newPlotPoints);
    updateNode({ plotPoints: newPlotPoints });
  }, [plotPoints, updateNode]);

  // 移动剧情点
  const movePlotPoint = useCallback((fromIndex: number, toIndex: number) => {
    const newPlotPoints = [...plotPoints];
    const [movedItem] = newPlotPoints.splice(fromIndex, 1);
    newPlotPoints.splice(toIndex, 0, movedItem);
    // 重新排序
    const reorderedPoints = newPlotPoints.map((point, i) => ({ ...point, order: i + 1 }));
    setPlotPoints(reorderedPoints);
    updateNode({ plotPoints: reorderedPoints });
  }, [plotPoints, updateNode]);

  // 添加关联角色
  const addCharacter = useCallback((character: string) => {
    if (character.trim() && !relatedCharacters.includes(character.trim())) {
      const newCharacters = [...relatedCharacters, character.trim()];
      setRelatedCharacters(newCharacters);
      updateNode({ relatedCharacters: newCharacters });
    }
  }, [relatedCharacters, updateNode]);

  // 移除关联角色
  const removeCharacter = useCallback((index: number) => {
    const newCharacters = relatedCharacters.filter((_, i) => i !== index);
    setRelatedCharacters(newCharacters);
    updateNode({ relatedCharacters: newCharacters });
  }, [relatedCharacters, updateNode]);

  // 获取剧情类型标签
  const getPlotTypeLabel = (type: PlotType) => {
    const labels = {
      conflict: '冲突',
      twist: '转折',
      climax: '高潮',
      resolution: '解决'
    };
    return labels[type];
  };

  return (
    <div className="space-y-4 plot-editor">
      {/* 剧情标题 */}
      <div>
        <label className="flex items-center text-sm font-medium text-orange-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
          </svg>
          剧情标题
        </label>
        <input
          type="text"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          className="w-full px-3 py-2 border border-orange-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-gradient-to-r from-orange-50 to-red-50"
          placeholder="输入剧情节点标题"
        />
      </div>

      {/* 剧情类型 */}
      <div>
        <label className="flex items-center text-sm font-medium text-orange-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          剧情类型
        </label>
        <select
          value={plotType}
          onChange={(e) => handlePlotTypeChange(e.target.value as PlotType)}
          className="w-full px-3 py-2 border border-orange-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-gradient-to-r from-orange-50 to-red-50"
        >
          <option value="conflict">⚡ 冲突</option>
          <option value="twist">🔄 转折</option>
          <option value="climax">🎯 高潮</option>
          <option value="resolution">✅ 解决</option>
        </select>
      </div>

      {/* 剧情点列表 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="flex items-center text-sm font-medium text-orange-700">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            剧情点列表
          </label>
          <button
            onClick={addPlotPoint}
            className="px-3 py-1 text-xs bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
          >
            + 添加剧情点
          </button>
        </div>
        
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {plotPoints.map((point, index) => (
            <div key={point.id} className="flex items-start space-x-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
              {/* 编号 */}
              <div className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                {index + 1}
              </div>

              {/* 内容 */}
              <div className="flex-1 space-y-2">
                <textarea
                  value={point.content}
                  onChange={(e) => updatePlotPoint(index, { content: e.target.value })}
                  className="w-full px-2 py-1 text-sm border border-orange-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-400"
                  placeholder="输入剧情点内容"
                  rows={2}
                />

                {/* 写作指导字段 */}
                <div className="space-y-1">
                  <label className="text-xs font-medium text-orange-700">写作指导（必填）：</label>
                  <textarea
                    value={point.avoidWriting || ''}
                    onChange={(e) => updatePlotPoint(index, { avoidWriting: e.target.value })}
                    className="w-full px-2 py-1 text-xs border border-yellow-200 rounded focus:outline-none focus:ring-1 focus:ring-yellow-400 bg-yellow-50"
                    placeholder="避免描写：一丝xx、几分xx、些许xx。应该描写：AA具体的动作、AA明确的反应、AA直接的表现"
                    rows={2}
                  />
                </div>

                {/* 写作风格方法指导 */}
                <div className="space-y-1 bg-blue-50 p-2 rounded border border-blue-200">
                  <label className="text-xs font-medium text-blue-700">🎨 写作风格方法：</label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-blue-600">技巧：</label>
                      <input
                        type="text"
                        value={point.styleMethod?.technique || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          styleMethod: { ...point.styleMethod, technique: e.target.value } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                        placeholder="对比手法"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-blue-600">风格：</label>
                      <input
                        type="text"
                        value={point.styleMethod?.style || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          styleMethod: { ...point.styleMethod, style: e.target.value } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                        placeholder="简洁明快"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-blue-600">语调：</label>
                      <input
                        type="text"
                        value={point.styleMethod?.tone || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          styleMethod: { ...point.styleMethod, tone: e.target.value } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                        placeholder="紧张"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-blue-600">视角：</label>
                      <input
                        type="text"
                        value={point.styleMethod?.perspective || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          styleMethod: { ...point.styleMethod, perspective: e.target.value } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                        placeholder="第三人称"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-xs text-blue-600">重点：</label>
                    <input
                      type="text"
                      value={point.styleMethod?.emphasis || ''}
                      onChange={(e) => updatePlotPoint(index, {
                        styleMethod: { ...point.styleMethod, emphasis: e.target.value } as any
                      })}
                      className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                      placeholder="动作描写为主"
                    />
                  </div>
                </div>

                {/* 格式规范 */}
                <div className="space-y-1 bg-green-50 p-2 rounded border border-green-200">
                  <label className="text-xs font-medium text-green-700">📏 格式规范：</label>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <label className="text-xs text-green-600">最少字数：</label>
                      <input
                        type="number"
                        value={point.formatSpecs?.wordCount?.min || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            wordCount: {
                              ...point.formatSpecs?.wordCount,
                              min: parseInt(e.target.value) || 150
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="150"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-green-600">最多字数：</label>
                      <input
                        type="number"
                        value={point.formatSpecs?.wordCount?.max || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            wordCount: {
                              ...point.formatSpecs?.wordCount,
                              max: parseInt(e.target.value) || 400
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="400"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-green-600">目标字数：</label>
                      <input
                        type="number"
                        value={point.formatSpecs?.wordCount?.target || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            wordCount: {
                              ...point.formatSpecs?.wordCount,
                              target: parseInt(e.target.value) || 250
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="250"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-green-600">对话格式：</label>
                      <input
                        type="text"
                        value={point.formatSpecs?.punctuationRules?.dialogueFormat || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            punctuationRules: {
                              ...point.formatSpecs?.punctuationRules,
                              dialogueFormat: e.target.value
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="「」"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-green-600">分段规则：</label>
                      <input
                        type="text"
                        value={point.formatSpecs?.paragraphRules?.paragraphBreakRules || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            paragraphRules: {
                              ...point.formatSpecs?.paragraphRules,
                              paragraphBreakRules: e.target.value
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="对话必须独立成段"
                      />
                    </div>
                  </div>

                  {/* 新增的冲突处理规范 */}
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <label className="text-xs text-green-600">冲突处理：</label>
                      <input
                        type="text"
                        value={point.formatSpecs?.paragraphRules?.conflictHandling || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            paragraphRules: {
                              ...point.formatSpecs?.paragraphRules,
                              conflictHandling: e.target.value
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="冲突升级时必须换行强调"
                      />
                    </div>
                  </div>

                  {/* 对话行动流程 */}
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <label className="text-xs text-green-600">对话行动流程：</label>
                      <input
                        type="text"
                        value={point.formatSpecs?.paragraphRules?.actionDialogueFlow || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            paragraphRules: {
                              ...point.formatSpecs?.paragraphRules,
                              actionDialogueFlow: e.target.value
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="严格执行对话→行动→对话节奏"
                      />
                    </div>
                  </div>

                  {/* 强制换行要求 */}
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <label className="text-xs text-green-600">强制换行：</label>
                      <input
                        type="text"
                        value={point.formatSpecs?.paragraphRules?.mandatoryBreaks || ''}
                        onChange={(e) => updatePlotPoint(index, {
                          formatSpecs: {
                            ...point.formatSpecs,
                            paragraphRules: {
                              ...point.formatSpecs?.paragraphRules,
                              mandatoryBreaks: e.target.value
                            }
                          } as any
                        })}
                        className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                        placeholder="情绪转折、场景切换必须换行"
                      />
                    </div>
                  </div>
                </div>

                <select
                  value={point.type || 'setup'}
                  onChange={(e) => updatePlotPoint(index, { type: e.target.value as PlotPoint['type'] })}
                  className="text-xs border border-orange-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-orange-400"
                >
                  <option value="setup">铺垫</option>
                  <option value="conflict">冲突</option>
                  <option value="resolution">解决</option>
                  <option value="twist">转折</option>
                </select>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex flex-col space-y-1">
                {index > 0 && (
                  <button
                    onClick={() => movePlotPoint(index, index - 1)}
                    className="text-orange-600 hover:text-orange-800"
                    title="上移"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                )}
                {index < plotPoints.length - 1 && (
                  <button
                    onClick={() => movePlotPoint(index, index + 1)}
                    className="text-orange-600 hover:text-orange-800"
                    title="下移"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                )}
                <button
                  onClick={() => removePlotPoint(index)}
                  className="text-red-600 hover:text-red-800"
                  title="删除"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
          
          {plotPoints.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p className="text-sm">暂无剧情点，点击上方按钮添加</p>
            </div>
          )}
        </div>
      </div>

      {/* 关联角色 */}
      <div>
        <label className="flex items-center text-sm font-medium text-orange-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          关联角色
        </label>
        <div className="space-y-2">
          {/* 角色标签显示 */}
          {relatedCharacters.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {relatedCharacters.map((character, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200"
                >
                  {character}
                  <button
                    onClick={() => removeCharacter(index)}
                    className="ml-1 text-orange-600 hover:text-orange-800"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          )}
          {/* 添加角色输入 */}
          <input
            type="text"
            className="w-full px-3 py-2 border border-orange-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 text-sm bg-gradient-to-r from-orange-50 to-red-50"
            placeholder="输入角色名称后按回车添加"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const input = e.target as HTMLInputElement;
                addCharacter(input.value);
                input.value = '';
              }
            }}
          />
        </div>
      </div>

      {/* 剧情描述 */}
      <div>
        <label className="flex items-center text-sm font-medium text-orange-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
          </svg>
          剧情描述
        </label>
        <textarea
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          className="w-full px-3 py-2 border border-orange-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 min-h-[80px] text-sm bg-gradient-to-r from-orange-50 to-red-50"
          placeholder="输入剧情的详细描述"
        />
      </div>
    </div>
  );
};

export default PlotEditor;
