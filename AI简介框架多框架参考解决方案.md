# AI简介框架多框架参考分层构造解决方案

## 问题描述

AI简介框架无法在一次框架中参考多个已分析存储的框架结构进行创作，需要解决AI创意助手生成简介工具的框架消息分层构造发送机制。

## 问题分析

### 原有问题
1. **框架技巧杂糅**：所有框架信息混合在一条消息中发送给AI
2. **理解深度不足**：AI无法深度理解每个框架的独特特征
3. **缺少保护机制**：没有"避免技巧杂糅"的明确指导

### 对比其他AI服务
- **OutlineAIService**：已实现`buildSeparatedFrameworkMessages`方法
- **DialogueAIService**：已实现分层框架构造机制
- **BrainstormAIService**：缺少多框架参考的分层构造

## 解决方案

### 核心实现

#### 1. buildSeparatedFrameworkMessages方法
```typescript
private static buildSeparatedFrameworkMessages(
  mainFramework: SynopsisFramework, 
  referenceFrameworks: SynopsisFramework[]
): Array<{ role: string; content: string }>
```

**功能特点：**
- 为主框架和每个参考框架构建独立的system消息
- 添加"避免技巧杂糅"的明确警告
- AI对每个框架单独确认理解
- 提供明确的使用指导

#### 2. buildSingleFrameworkMessage方法
```typescript
private static buildSingleFrameworkMessage(
  messageBuilder: any, 
  framework: SynopsisFramework
): void
```

**功能特点：**
- 封装原有的单框架处理逻辑
- 保持向后兼容性
- 当没有参考框架时自动回退

#### 3. 智能检测机制
```typescript
// 在buildSynopsisGenerationPrompt中
if (referenceFrameworks.length > 0) {
  // 使用分层框架构造
  const separatedMessages = this.buildSeparatedFrameworkMessages(params.framework, referenceFrameworks);
} else {
  // 使用原有的单框架逻辑
  this.buildSingleFrameworkMessage(messageBuilder, params.framework);
}
```

### 消息构建流程

```mermaid
graph TD
    A[用户请求简介生成] --> B[检测框架数量]
    B -->|单框架| C[使用原有逻辑]
    B -->|多框架| D[分层框架构造]
    
    D --> E[多框架说明消息]
    D --> F[主框架独立消息]
    D --> G[参考框架1独立消息]
    D --> H[参考框架2独立消息]
    D --> I[AI确认理解消息]
    
    E --> J[完整消息序列]
    F --> J
    G --> J
    H --> J
    I --> J
    
    C --> K[单框架消息序列]
    J --> L[发送给AI]
    K --> L
    L --> M[AI生成高质量简介]
```

### 技术特点

1. **类型安全**：使用TypeScript确保类型安全
2. **向后兼容**：保持原有单框架功能不变
3. **错误处理**：添加回退机制，失败时使用单框架模式
4. **代码复用**：提取公共方法，避免重复代码

### 预期效果

1. **深度理解**：AI能深度理解每个框架的独特特征
2. **避免杂糅**：防止框架技巧的无序混合
3. **智能选择**：根据具体需求选择合适的框架元素
4. **质量提升**：提供更高质量和更有针对性的简介内容

## 实现文件

- **主要文件**：`src/factories/ai/services/brainstorm/builders/SynopsisPromptBuilder.ts`
- **新增方法**：
  - `buildSeparatedFrameworkMessages`
  - `buildSingleFrameworkMessage`
  - `buildWritingTechniquesContent`
  - `buildStyleCharacteristicsContent`

## 测试验证

1. **单框架模式**：确保原有功能正常工作
2. **多框架模式**：验证分层构造机制生效
3. **回退机制**：测试错误情况下的回退逻辑
4. **消息格式**：确认AI能正确理解分层消息

## 后续优化

1. **框架权重机制**：允许用户指定不同框架的影响程度
2. **智能推荐**：基于简介类型自动推荐合适的参考框架
3. **效果评估**：收集用户反馈，持续优化框架选择算法

## 总结

通过实现分层框架构造机制，成功解决了AI简介生成中的框架技巧杂糅问题。该方案参考了其他AI服务的成功模式，确保了一致性和可维护性，同时针对简介生成的特殊需求进行了优化。
