"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../../types/outline';
import { VolumeEditor } from './editors/VolumeEditor';
import { EventEditor } from './editors/EventEditor';
import { PhaseGroupEditor } from './editors/PhaseGroupEditor';
import ChapterEditor from './editors/ChapterEditor';
import PlotEditor from './editors/PlotEditor';
import DialogueEditor from './editors/DialogueEditor';
import SynopsisEditor from './editors/SynopsisEditor';
import { useDebounce } from '@/hooks/useDebounce';
import { Button } from '@/adapters/ui';

interface InlineNodeEditorProps {
  node: OutlineNodeType;
  position: { x: number, y: number };
  onSave: (updatedNode: OutlineNodeType) => void;
  onCancel: () => void;
  containerRef: React.RefObject<HTMLElement>;
  allNodes?: OutlineNodeType[]; // 传入所有节点，用于VolumeEditor的章节选择
}

/**
 * 节点内联编辑器组件
 * 直接在节点位置上方显示编辑界面，提供更直观的编辑体验
 * 支持自动保存、键盘快捷键和丰富的编辑选项
 */
const InlineNodeEditor: React.FC<InlineNodeEditorProps> = ({
  node,
  position,
  onSave,
  onCancel,
  containerRef,
  allNodes
}) => {
  // 状态管理
  const [title, setTitle] = useState(node.title || '');
  const [description, setDescription] = useState(node.description || '');
  const [creativeNotes, setCreativeNotes] = useState(node.creativeNotes || '');
  const [type, setType] = useState<'volume' | 'phaseGroup' | 'chapter' | 'plot' | 'dialogue' | 'synopsis'>(node.type || 'chapter');
  const [isMounted, setIsMounted] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);
  const [currentNode, setCurrentNode] = useState<OutlineNodeType>(node);

  // 编辑器引用
  const editorRef = useRef<HTMLDivElement>(null);
  const titleInputRef = useRef<HTMLInputElement>(null);

  // 处理节点更新
  const handleNodeChange = useCallback((updatedNode: OutlineNodeType) => {
    setCurrentNode(updatedNode);
    setTitle(updatedNode.title || '');
    setDescription(updatedNode.description || '');
    setCreativeNotes(updatedNode.creativeNotes || '');
    setType(updatedNode.type);
  }, []);

  // 防抖处理，用于自动保存
  const debouncedTitle = useDebounce(title, 800); // 减少延迟，提高响应速度
  const debouncedDescription = useDebounce(description, 1000);
  // 注意：类型变更不使用防抖，避免触发自动保存导致弹窗关闭

  // 计算编辑器位置
  const calculateEditorPosition = () => {
    if (!containerRef.current || !editorRef.current) return { top: position.y, left: position.x };

    const containerRect = containerRef.current.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    // 确保编辑器不超出容器边界
    let top = position.y - 10; // 稍微上移，避免遮挡节点
    let left = position.x;

    // 如果编辑器右侧超出容器
    if (left + editorRect.width > containerRect.width) {
      left = containerRect.width - editorRect.width - 10;
    }

    // 如果编辑器底部超出容器
    if (top + editorRect.height > containerRect.height) {
      top = containerRect.height - editorRect.height - 10;
    }

    // 确保不小于0
    top = Math.max(10, top);
    left = Math.max(10, left);

    return { top, left };
  };

  // 自动保存功能 - 只对标题和描述进行自动保存，类型变更需要手动保存
  useEffect(() => {
    // 只有在组件挂载后且值发生变化时才触发自动保存
    if (isMounted &&
        (debouncedTitle !== node.title ||
         debouncedDescription !== node.description)) {

      handleAutoSave();
    }
  }, [debouncedTitle, debouncedDescription]);

  // 监听类型变更，更新内部状态但不自动保存
  useEffect(() => {
    if (isMounted && type !== node.type) {
      setCurrentNode(prev => ({
        ...prev,
        type: type
      }));
    }
  }, [type, isMounted]);

  // 组件挂载后设置状态
  useEffect(() => {
    setIsMounted(true);
    // 自动聚焦标题输入框
    if (titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }

    // 添加点击外部关闭事件
    const handleClickOutside = (e: MouseEvent) => {
      if (editorRef.current && !editorRef.current.contains(e.target as Node)) {
        handleSave();
      }
    };

    // 添加ESC键关闭事件
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel();
      } else if (e.key === 'Enter' && e.ctrlKey) {
        handleSave();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // 自动保存处理 - 只更新内部状态，不关闭弹窗
  const handleAutoSave = () => {
    if (currentNode.title?.trim() === '') return; // 不保存空标题

    setIsSaving(true);

    // 更新内部节点状态，但不调用onSave（避免关闭弹窗）
    const updatedNode: OutlineNodeType = {
      ...currentNode,
      title: title,
      description: description,
      type: type,
      // 只对创作建议进行首尾空白修剪，保留内部的空行和换行
      creativeNotes: creativeNotes.replace(/^\s+|\s+$/g, ''),
      // 显式保留位置信息，确保不会丢失
      position: node.position
    };

    // 更新内部状态
    setCurrentNode(updatedNode);

    // 显示保存指示器，提供视觉反馈
    setShowSaveIndicator(true);

    // 添加动画效果
    if (editorRef.current) {
      editorRef.current.classList.add('pulse-save');
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.classList.remove('pulse-save');
        }
      }, 500);
    }

    // 延迟隐藏保存指示器
    setTimeout(() => {
      setShowSaveIndicator(false);
      setIsSaving(false);
    }, 1500);
  };

  // 保存处理 - 保存最终状态并关闭弹窗
  const handleSave = async () => {
    if (title.trim() === '') {
      // 如果标题为空，使用原标题
      setTitle(node.title || '');
      return;
    }

    setIsSaving(true);

    try {
      const updatedNode: OutlineNodeType = {
        ...currentNode,
        title: title,
        description: description,
        type: type,
        // 只对创作建议进行首尾空白修剪，保留内部的空行和换行
        creativeNotes: creativeNotes.replace(/^\s+|\s+$/g, ''),
        // 显式保留位置信息，确保不会丢失
        position: node.position
      };

      // 调用保存回调，这会关闭弹窗
      await onSave(updatedNode);

      // 显示成功反馈
      console.log('节点保存成功');
    } catch (error) {
      console.error('保存节点失败:', error);
      // 保存失败时不关闭弹窗，让用户可以重试
      setIsSaving(false);
      return;
    }
  };

  // 取消处理
  const handleCancel = () => {
    onCancel();
  };

  // 计算位置
  const { top, left } = calculateEditorPosition();

  // 使用Portal渲染到容器中
  return createPortal(
    <div
      ref={editorRef}
      className="absolute z-50 bg-white rounded-lg shadow-xl border border-gray-200 transition-all duration-200 transform flex flex-col"
      style={{
        top: `${top}px`,
        left: `${left}px`,
        opacity: isMounted ? 1 : 0,
        transform: isMounted ? 'translateY(0) scale(1)' : 'translateY(-10px) scale(0.98)',
        maxHeight: 'min(75vh, 600px)',
        width: 'min(90vw, 800px)',
        minWidth: '400px'
      }}
    >
      {/* 编辑器标题 - 固定头部 */}
      <div className="flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-700">编辑节点</h3>

        {/* 保存指示器 */}
        {showSaveIndicator && (
          <span className="text-xs text-green-600 flex items-center animate-fade-in">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            已自动保存
          </span>
        )}

        <button
          className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
          onClick={handleCancel}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 可滚动内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 min-h-0">
        {/* 节点类型选择 */}
        <div className="mb-3">
          <div className="grid grid-cols-3 gap-1 mb-2">
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'volume'
                  ? 'bg-purple-500 text-white border-purple-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('volume')}
            >
              总纲/卷
            </button>
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'event'
                  ? 'bg-indigo-500 text-white border-indigo-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('event')}
            >
              事件刚
            </button>
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'phaseGroup'
                  ? 'bg-emerald-500 text-white border-emerald-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('phaseGroup')}
            >
              阶段分组
            </button>
          </div>
          <div className="grid grid-cols-2 gap-1 mb-2">
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'chapter'
                  ? 'bg-[var(--outline-primary)] text-white border-[var(--outline-primary)]'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('chapter')}
            >
              章节
            </button>
          </div>
          <div className="grid grid-cols-3 gap-1">
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'plot'
                  ? 'bg-orange-500 text-white border-orange-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('plot')}
            >
              剧情节点
            </button>
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'dialogue'
                  ? 'bg-green-500 text-white border-green-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('dialogue')}
            >
              对话设计
            </button>
            <button
              className={`py-1 px-2 text-xs rounded-md border ${
                type === 'synopsis'
                  ? 'bg-indigo-500 text-white border-indigo-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('synopsis')}
            >
              核心故事梗概
            </button>
          </div>
        </div>

        {/* 类型特定的编辑器 */}
        <div className="mb-4">
          {type === 'volume' && (
            <VolumeEditor
              node={currentNode}
              onChange={handleNodeChange}
              allNodes={allNodes}
            />
          )}
          {type === 'event' && (
            <EventEditor
              node={currentNode}
              onChange={handleNodeChange}
              allNodes={allNodes}
            />
          )}
          {type === 'phaseGroup' && (
            <PhaseGroupEditor
              node={currentNode}
              onChange={handleNodeChange}
            />
          )}
          {type === 'chapter' && (
            <ChapterEditor
              node={currentNode}
              onChange={handleNodeChange}
            />
          )}
          {type === 'plot' && (
            <PlotEditor
              node={currentNode}
              onChange={handleNodeChange}
            />
          )}
          {type === 'dialogue' && (
            <DialogueEditor
              node={currentNode}
              onChange={handleNodeChange}
            />
          )}
          {type === 'synopsis' && (
            <SynopsisEditor
              node={currentNode}
              onChange={handleNodeChange}
            />
          )}
        </div>

        {/* 创作建议输入 */}
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium text-gray-600">💡</span>
            <label className="text-xs font-medium text-gray-600">创作建议</label>
          </div>
          <textarea
            value={creativeNotes}
            onChange={(e) => setCreativeNotes(e.target.value)}
            className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 min-h-[60px] text-sm bg-gradient-to-r from-blue-50 to-indigo-50"
            placeholder="输入创作建议，如台词设计、心理描写、节奏控制等..."
          />
        </div>
      </div>

      {/* 固定底部 */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4">
        {/* 底部按钮 */}
        <div className="flex justify-end space-x-2 mb-2">
          <button
            className="px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            onClick={handleCancel}
          >
            取消
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
              isSaving
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-[var(--outline-primary)] text-white hover:bg-[var(--outline-primary-dark)]'
            }`}
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                保存中...
              </span>
            ) : '保存'}
          </button>
        </div>

        {/* 快捷键提示 */}
        <div className="text-xs text-gray-500 flex justify-center">
          <span className="mr-2">Esc: 取消</span>
          <span>Ctrl+Enter: 保存</span>
        </div>
      </div>
    </div>,
    containerRef.current || document.body
  );
};

export default InlineNodeEditor;
