"use client";

import React from 'react';
import { PetState, PetType } from '../index';

interface PetSVGProps {
  petType: PetType;
  state: PetState;
  isAnimating: boolean;
  animationLevel: 'low' | 'medium' | 'high';
}

/**
 * 宠物SVG图形组件
 * 根据宠物类型和状态渲染不同的SVG图形
 */
export const PetSVG: React.FC<PetSVGProps> = ({
  petType,
  state,
  isAnimating,
  animationLevel
}) => {
  // 根据状态获取动画类名
  const getAnimationClass = () => {
    const baseClass = `pet-${petType}`;
    const stateClass = `pet-state-${state}`;
    const levelClass = `pet-level-${animationLevel}`;
    const animatingClass = isAnimating ? 'pet-animating' : '';

    return `${baseClass} ${stateClass} ${levelClass} ${animatingClass}`.trim();
  };

  // 渲染简化的Q版小猫头部
  const renderCat = () => (
    <svg
      viewBox="0 0 32 32"
      className={getAnimationClass()}
      style={{ width: '100%', height: '100%' }}
    >
      <defs>
        {/* 头部奶茶色渐变 */}
        <radialGradient id="catHeadGradient" cx="50%" cy="35%" r="65%">
          <stop offset="0%" stopColor="#F5DEB3" />
          <stop offset="70%" stopColor="#DEB887" />
          <stop offset="100%" stopColor="#D2B48C" />
        </radialGradient>

        {/* 耳朵渐变 */}
        <linearGradient id="catEarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#DEB887" />
          <stop offset="100%" stopColor="#CD853F" />
        </linearGradient>

        {/* 眼睛深棕色渐变 */}
        <radialGradient id="eyeGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="#8B4513" />
          <stop offset="100%" stopColor="#654321" />
        </radialGradient>

        {/* 眼睛高光 */}
        <radialGradient id="eyeHighlight" cx="30%" cy="30%" r="40%">
          <stop offset="0%" stopColor="white" stopOpacity="0.9" />
          <stop offset="100%" stopColor="white" stopOpacity="0" />
        </radialGradient>

        {/* 眼睑遮罩 */}
        <mask id="eyeMask">
          <rect width="100%" height="100%" fill="white"/>
          <ellipse className="eye-lid-top" cx="13" cy="16" rx="2.5" ry="3" fill="black"/>
          <ellipse className="eye-lid-top" cx="19" cy="16" rx="2.5" ry="3" fill="black"/>
        </mask>
      </defs>

      {/* 头部主体 */}
      <circle
        className="pet-head"
        cx="16"
        cy="18"
        r="12"
        fill="url(#catHeadGradient)"
      />

      {/* 左耳 */}
      <path
        className="pet-ear pet-ear-left"
        d="M 10 12 L 8 6 L 14 8 Z"
        fill="url(#catEarGradient)"
      />

      {/* 右耳 */}
      <path
        className="pet-ear pet-ear-right"
        d="M 22 12 L 18 8 L 24 6 Z"
        fill="url(#catEarGradient)"
      />

      {/* 左耳内侧 */}
      <path
        className="pet-ear-inner"
        d="M 10.5 10 L 9.5 8 L 12 9 Z"
        fill="#FFB6C1"
        opacity="0.7"
      />

      {/* 右耳内侧 */}
      <path
        className="pet-ear-inner"
        d="M 21.5 10 L 20 9 L 22.5 8 Z"
        fill="#FFB6C1"
        opacity="0.7"
      />

      {/* 左眼 - 更大更生动 */}
      <ellipse
        className="pet-eye pet-eye-left"
        cx="12"
        cy="15"
        rx="3"
        ry="3.5"
        fill="url(#eyeGradient)"
      />

      {/* 右眼 - 更大更生动 */}
      <ellipse
        className="pet-eye pet-eye-right"
        cx="20"
        cy="15"
        rx="3"
        ry="3.5"
        fill="url(#eyeGradient)"
      />

      {/* 左眼高光 */}
      <circle
        className="pet-eye-highlight"
        cx="12.8"
        cy="14.2"
        r="1.2"
        fill="url(#eyeHighlight)"
      />

      {/* 右眼高光 */}
      <circle
        className="pet-eye-highlight"
        cx="20.8"
        cy="14.2"
        r="1.2"
        fill="url(#eyeHighlight)"
      />

      {/* 左眼珠（用于眼睛跟随效果） */}
      <circle
        className="pet-pupil pet-pupil-left"
        cx="12"
        cy="15"
        r="1"
        fill="#000"
      />

      {/* 右眼珠 */}
      <circle
        className="pet-pupil pet-pupil-right"
        cx="20"
        cy="15"
        r="1"
        fill="#000"
      />

      {/* 眼睑（用于眨眼动画） */}
      <ellipse
        className="pet-eyelid pet-eyelid-left"
        cx="12"
        cy="13"
        rx="3"
        ry="0"
        fill="url(#catHeadGradient)"
      />

      <ellipse
        className="pet-eyelid pet-eyelid-right"
        cx="20"
        cy="13"
        rx="3"
        ry="0"
        fill="url(#catHeadGradient)"
      />

      {/* 鼻子 */}
      <path
        className="pet-nose"
        d="M 16 19 L 15 20.5 L 17 20.5 Z"
        fill="#FF1493"
      />

      {/* 嘴巴 */}
      <path
        className="pet-mouth"
        d="M 16 20.5 Q 14 22 12 21.5 M 16 20.5 Q 18 22 20 21.5"
        stroke="#FF1493"
        strokeWidth="0.8"
        fill="none"
        strokeLinecap="round"
      />

      {/* 胡须 */}
      <g className="pet-whiskers">
        <line x1="6" y1="17" x2="10" y2="17" stroke="#666" strokeWidth="0.5" />
        <line x1="6" y1="19" x2="10" y2="18.5" stroke="#666" strokeWidth="0.5" />
        <line x1="22" y1="17" x2="26" y2="17" stroke="#666" strokeWidth="0.5" />
        <line x1="22" y1="18.5" x2="26" y2="19" stroke="#666" strokeWidth="0.5" />
      </g>

      {/* 腮红（兴奋状态时显示） */}
      <circle
        className="pet-blush pet-blush-left"
        cx="9"
        cy="19"
        r="2"
        fill="#FF69B4"
        opacity="0"
      />
      <circle
        className="pet-blush pet-blush-right"
        cx="23"
        cy="19"
        r="2"
        fill="#FF69B4"
        opacity="0"
      />
    </svg>
  );

  // 根据宠物类型渲染对应的SVG
  const renderPet = () => {
    switch (petType) {
      case 'cat':
        return renderCat();
      case 'bird':
        // TODO: 实现小鸟SVG
        return renderCat(); // 暂时使用小猫
      case 'dragon':
        // TODO: 实现小龙SVG
        return renderCat(); // 暂时使用小猫
      case 'rabbit':
        // TODO: 实现小兔SVG
        return renderCat(); // 暂时使用小猫
      default:
        return renderCat();
    }
  };

  return (
    <div className="pet-svg-container">
      {renderPet()}
    </div>
  );
};

export default PetSVG;
