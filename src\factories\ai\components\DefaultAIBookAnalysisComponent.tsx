"use client";

import React, { useState, useEffect } from 'react';
import { IAIBookAnalysisComponent, Book } from '../interfaces';
import { AIMessage } from '../interfaces/IAISenderComponent';
import { AIResponse, AIRequestOptions } from '../interfaces/IAIComponent';
import { createChapterSegmenter, ChapterSegmenter } from '@/utils/ai/ChapterSegmenter';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { aiServiceProvider } from '@/services/ai/AIServiceProvider';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';
import {
  singleBookBreakdownPrompts,
  mergedBookBreakdownPrompts,
  mixedBookBreakdownPrompts,
  syncBookBreakdownPrompts,
  getAllBookBreakdownPrompts
} from '@/utils/ai/prompts/AIBookBreakdownPrompts';

/**
 * 默认AI拆书组件实现
 */
export class DefaultAIBookAnalysisComponent extends UnifiedAIService implements IAIBookAnalysisComponent {
  private books: Book[] = [];
  private analysisMode: 'single' | 'merged' | 'mixed' | 'sync' = 'single';
  private promptTemplate: string = '';
  private isAnalyzing: boolean = false;
  private onAnalysisComplete?: (result: string) => void;
  private abortController: AbortController | null = null;
  private segmenter: ChapterSegmenter;
  // 存储书籍ID，用于关联分析结果
  private _bookId: string = '';
  // 标记是否正在流式处理中，用于区分流式更新和最终完成
  private isStreaming: boolean = false;

  /**
   * 发送请求
   * 实现IAIBookAnalysisComponent接口要求的方法
   */
  async sendRequest(prompt: string, options?: AIRequestOptions): Promise<AIResponse> {
    try {
      // 使用options中的参数（如果有）
      if (options) {
        // 如果提供了温度参数，使用它
        if (options.temperature !== undefined) {
          console.log(`使用自定义温度参数: ${options.temperature}`);
        }

        // 如果提供了最大令牌数，使用它
        if (options.maxTokens !== undefined) {
          console.log(`使用自定义最大令牌数: ${options.maxTokens}`);
        }
      }

      // 执行分析
      const text = await this.analyze();

      return {
        text,
        success: true
      };
    } catch (error: any) {
      return {
        text: error.message || '分析失败',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  constructor() {
    super(AIServiceType.BOOK_ANALYSIS);
    // 初始化章节分段器
    this.segmenter = createChapterSegmenter({
      maxSegmentLength: 2000,
      minSegmentLength: 500,
      addSegmentNumber: true,
      preserveCharacterDescriptions: true
    });
  }

  /**
   * 设置要分析的书籍
   * @param books 书籍列表
   */
  setBooks(books: Book[]): void {
    this.books = books;
  }

  /**
   * 设置分析模式
   * @param mode 分析模式
   */
  setAnalysisMode(mode: 'single' | 'merged' | 'mixed' | 'sync'): void {
    this.analysisMode = mode;
  }

  /**
   * 设置提示词模板
   * @param template 提示词模板
   */
  setPromptTemplate(template: string): void {
    this.promptTemplate = template;
  }

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this._bookId = bookId;
  }

  /**
   * 设置分析完成回调
   * @param callback 回调函数
   */
  setOnAnalysisComplete(callback: (result: string) => void): void {
    this.onAnalysisComplete = callback;
  }

  /**
   * 取消分析
   */
  cancelAnalysis(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.isAnalyzing = false;
      this.isStreaming = false;
    }
  }

  /**
   * 更新分析文本，但不触发成功通知
   * 用于流式处理过程中的实时文本更新
   * @param text 更新的文本内容
   * @private
   */
  private updateAnalysisText(text: string): void {
    // 如果有回调函数，调用它更新文本内容
    if (this.onAnalysisComplete && this.isStreaming) {
      this.onAnalysisComplete(text);
    }
  }

  /**
   * 继续对话分析
   * @param messages 消息数组
   * @returns 分析结果
   */
  public async continueAnalysis(messages: { role: string; content: string }[]): Promise<string> {
    try {
      // 设置分析中状态
      this.isAnalyzing = true;
      this.isStreaming = true;

      // 使用流式输出
      let fullText = '';

      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        (chunk) => {
          // 每次收到数据块时更新全文
          fullText += chunk;

          // 使用updateAnalysisText方法更新文本，但不触发成功通知
          this.updateAnalysisText(fullText);
        },
        {
          streaming: true,
          signal: this.abortController?.signal
        }
      );

      // 流式处理结束
      this.isStreaming = false;
      this.isAnalyzing = false;

      // 分析完成，触发一次成功通知
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(response.text);
      }

      return response.text;
    } catch (error: any) {
      console.error('继续对话分析失败:', error);

      // 流式处理结束
      this.isStreaming = false;
      this.isAnalyzing = false;

      // 如果有回调函数，通知错误
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(`继续对话分析过程中出现错误: ${error.message || '未知错误'}`);
      }

      return `分析失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 分析书籍
   * @returns 分析结果
   */
  async analyze(): Promise<string> {
    if (this.isAnalyzing) {
      throw new Error('已有分析任务正在进行中');
    }

    if (this.books.length === 0) {
      throw new Error('未设置要分析的书籍');
    }

    this.isAnalyzing = true;
    this.isStreaming = true; // 设置流式处理标志
    this.abortController = new AbortController();

    try {
      console.log('开始分析书籍', {
        books: this.books.map(book => book.title),
        analysisMode: this.analysisMode,
        promptTemplate: this.promptTemplate
      });

      // 根据分析模式选择不同的分析方法
      let result: string;
      switch (this.analysisMode) {
        case 'single':
          result = await this.analyzeSingleBookWithStreaming();
          break;
        case 'merged':
          result = await this.analyzeMergedBooksWithStreaming();
          break;
        case 'mixed':
          result = await this.analyzeMixedBooksWithStreaming();
          break;
        case 'sync':
          result = await this.analyzeSyncBooksWithStreaming();
          break;
        default:
          result = await this.analyzeSingleBookWithStreaming();
      }

      // 调用完成回调
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(result);
      }

      // 流式处理结束
      this.isStreaming = false;

      return result;
    } catch (error: any) {
      console.error('分析书籍失败:', error);

      // 如果是取消请求，返回特定消息
      if (error.name === 'AbortError') {
        return '分析已取消';
      }

      throw error;
    } finally {
      this.isAnalyzing = false;
      this.isStreaming = false;
      this.abortController = null;
    }
  }

  /**
   * 智能分段章节内容
   * 将长文本分成有意义的段落，避免简单截断
   * @param content 章节内容
   * @param maxLength 最大长度限制
   * @returns 分段后的内容
   * @deprecated 使用ChapterSegmenter代替
   */
  // 此方法保留作为参考，实际使用ChapterSegmenter
  // @ts-ignore - 保留作为参考方法，不直接使用
  private _intelligentSegmentation(content: string, maxLength: number = 1000): string {
    if (content.length <= maxLength) {
      return content;
    }

    // 尝试在段落处分割
    const paragraphs = content.split(/\n\n+/);
    let result = '';
    let currentLength = 0;

    for (const paragraph of paragraphs) {
      if (currentLength + paragraph.length + 2 <= maxLength) { // +2 for newlines
        result += paragraph + '\n\n';
        currentLength += paragraph.length + 2;
      } else {
        // 如果单个段落超过限制，尝试在句子处分割
        if (result === '') {
          const sentences = paragraph.split(/(?<=[.!?。！？])\s+/);
          for (const sentence of sentences) {
            if (currentLength + sentence.length + 1 <= maxLength) { // +1 for space
              result += sentence + ' ';
              currentLength += sentence.length + 1;
            } else {
              break;
            }
          }
        }
        break;
      }
    }

    // 如果没有找到合适的分割点，则直接截断
    if (result === '') {
      result = content.substring(0, maxLength);
    }

    return result.trim() + '...(内容省略)';
  }

  /**
   * 分析单本书籍（流式输出）
   * @returns 分析结果
   */
  private async analyzeSingleBookWithStreaming(): Promise<string> {
    if (this.books.length === 0) {
      throw new Error('未设置要分析的书籍');
    }

    // 获取第一本书
    const book = this.books[0];

    // 从提示词模板中获取系统提示词
    // 默认使用第一个单本拆解提示词模板的内容作为基础
    const defaultPrompt = singleBookBreakdownPrompts[0].content;

    // 构建系统提示词 - 如果有自定义提示词模板，则使用自定义模板
    const systemPrompt = this.promptTemplate ?
      `你是一位专业的文学分析专家，擅长深入分析小说作品。
请根据以下要求分析提供的小说内容：

${this.promptTemplate}

请以Markdown格式输出分析结果，使用标题、列表等元素组织内容，确保分析深入、专业且有洞见。` :
      // 否则使用默认提示词模板
      defaultPrompt;

    // 构建用户提示（仅用于非分段模式，实际上不会使用）
    let userPrompt = `请分析以下小说作品：《${book.title}》\n\n`;

    // 构建消息数组，使用更复杂的消息结构和章节分段
    const messages: AIMessage[] = this.constructMessages(systemPrompt, userPrompt);

    // 使用流式输出
    let fullText = '';

    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        (chunk) => {
          // 每次收到数据块时更新全文
          fullText += chunk;

          // 使用updateAnalysisText方法更新文本，但不触发成功通知
          this.updateAnalysisText(fullText);
        },
        {
          streaming: true,
          signal: this.abortController?.signal
        }
      );

      // 分析完成，触发一次成功通知
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(response.text);
      }

      return response.text;
    } catch (error: any) {
      console.error('AI分析失败:', error);

      // 如果有回调函数，通知错误
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(`分析过程中出现错误: ${error.message || '未知错误'}`);
      }

      return `分析失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 获取API配置
   * 从环境变量或配置中获取API参数，而不是硬编码
   */
  private async getApiConfig() {
    // 尝试从API设置组件直接获取参数
    try {
      // 首先尝试从API设置组件获取配置
      const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();

      // 获取当前提供商和模型
      const provider = apiSettings.getCurrentProvider();
      const model = apiSettings.getCurrentModel();

      // 尝试获取maxTokens，如果方法不存在则使用默认值
      let maxTokens;
      try {
        // 使用any类型绕过类型检查，因为接口中可能没有定义此方法
        const apiSettingsAny = apiSettings as any;
        if (typeof apiSettingsAny.getMaxTokens === 'function') {
          maxTokens = apiSettingsAny.getMaxTokens();
        }
      } catch (error) {
        console.warn('无法获取maxTokens，将使用默认值', error);
      }

      // 使用默认温度，避免覆盖API设置
      const temperature = 0.7;

      console.log('使用API设置:', { provider, model, maxTokens, temperature });

      return {
        temperature,
        maxTokens,
        model
      };
    } catch (apiError) {
      console.warn('无法从API设置获取配置，尝试从configService获取', apiError);

      // 如果无法从API设置获取，尝试从configService获取
      try {
        // 使用configService获取API配置，但不保存回API设置
        const { configService } = await import('@/services/configService');
        // 使用getAIConfig但不修改API设置
        const config = await configService.getAIConfig();

        // 获取当前配置
        const temperature = config.temperature || 0.7;
        const maxTokens = config.maxTokens; // 不设置默认值，使用API服务的默认值
        const model = config.model || 'gemini-2.5-pro-exp-03-25';

        console.log('使用configService配置:', { model, temperature, maxTokens });

        return {
          temperature,
          maxTokens,
          model
        };
      } catch (configError) {
        console.warn('无法获取任何配置，使用默认值', configError);
        // 返回默认值，但不硬编码maxTokens
        return {
          temperature: 0.7,
          model: 'gemini-2.5-pro-exp-03-25'
          // 不设置maxTokens，使用API服务的默认值
        };
      }
    }
  }

  /**
   * 构造消息数组
   * 创建更复杂的消息结构，类似于其他AI功能
   * 使用章节分段器将长章节分成多个消息
   */
  private constructMessages(systemPrompt: string, userPrompt: string): AIMessage[] {
    // 创建消息构建器
    const messageBuilder = new MessageBuilder();

    // 添加系统消息
    messageBuilder.addSystemMessage(systemPrompt);

    // 如果是简单的用户提示，直接添加
    if (!this.books || this.books.length === 0) {
      messageBuilder.addUserMessage(userPrompt);
      // 将通用消息格式转换为AIMessage格式
    return messageBuilder.build().map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content
    }));
    }

    // 根据分析模式处理不同的书籍
    if (this.analysisMode === 'single' && this.books.length > 0) {
      // 单本模式 - 只处理第一本书
      const book = this.books[0];
      messageBuilder.addUserMessage(`请分析以下小说作品：《${book.title}》\n小说包含${book.chapters.length}个章节。`);

      // 添加自定义提示词模板（如果有）
      if (this.promptTemplate) {
        messageBuilder.addUserMessage(`分析要求：\n${this.promptTemplate}`);
      }

      // 为每个章节添加分段内容
      // 处理所有章节，不限制数量
      for (const chapter of book.chapters) {
        // 添加章节标题
        messageBuilder.addUserMessage(`## ${chapter.title}`);

        // 使用分段器处理章节内容
        const segments = this.segmenter.segmentChapter(chapter.content);

        // 添加章节分段消息
        messageBuilder.addAssistantMessage(`我将阅读"${chapter.title}"章节的内容，共${segments.length}个段落。`);

        // 为每个段落添加单独的消息
        // 处理所有段落，不限制数量
        for (let i = 0; i < segments.length; i++) {
          const segment = segments[i];
          const wordCount = segment.replace(/\s+/g, '').length;

          // 每个段落作为单独的消息，包含段落编号和字数信息
          messageBuilder.addUserMessage(`【${chapter.title}】\n段落：${i+1}/${segments.length}\n字数：${wordCount}\n\n${segment}`);

          // 每个段落后添加确认消息
          messageBuilder.addAssistantMessage(`我已阅读"${chapter.title}"章节的第${i+1}段内容。`);
        }

        // 章节结束消息
        messageBuilder.addAssistantMessage(`我已完成"${chapter.title}"章节的阅读。`);
      }
    } else if (this.books.length > 0) {
      // 多本模式（合并、混合、同步）
      messageBuilder.addUserMessage(`请分析以下${this.books.length}部小说作品：`);

      // 添加自定义提示词模板（如果有）
      if (this.promptTemplate) {
        messageBuilder.addUserMessage(`分析要求：\n${this.promptTemplate}`);
      }

      // 添加分析模式说明
      let modeDescription = '';
      switch (this.analysisMode) {
        case 'merged':
          modeDescription = '合并分析模式：请找出这些作品的共性和差异，进行整合分析。';
          break;
        case 'mixed':
          modeDescription = '混合分析模式：请首先仔细寻找这些作品的共同元素和模式，然后创造性地混合这些作品的元素，想象它们在同一个世界中的可能性。寻找共同点是混合分析的关键基础。';
          break;
        case 'sync':
          modeDescription = '同步分析模式：请为每部作品提供独立的分析，保持每部作品的独立性。在完成独立分析后，请添加一个专门的章节来分析这些作品的共同点和模式，这有助于理解创作规律。';
          break;
      }
      messageBuilder.addUserMessage(modeDescription);

      // 根据分析模式处理多本书籍
      if (this.analysisMode === 'mixed') {
        // 混合拆解模式：交替处理不同作品的对应章节
        messageBuilder.addUserMessage(`## 混合拆解模式：将交替处理不同作品的对应章节和段落`);

        // 确定最大章节数 - 不限制章节数，处理所有章节
        // 找出所有书籍中最大的章节数
        const maxChaptersCount = Math.max(...this.books.map(book => book.chapters.length));

        // 对于每个章节位置，处理所有书籍的对应章节
        for (let chapterIndex = 0; chapterIndex < maxChaptersCount; chapterIndex++) {
          messageBuilder.addUserMessage(`### 第${chapterIndex + 1}章节组对比`);

          // 收集所有书籍当前章节的段落
          const bookChapters: Array<{
            bookIndex: number;
            bookTitle: string;
            chapter: any;
            segments: string[];
          }> = [];

          // 对于每本书，收集当前章节的段落
          for (let bookIndex = 0; bookIndex < this.books.length; bookIndex++) {
            const book = this.books[bookIndex];

            // 检查该书是否有此章节
            if (chapterIndex < book.chapters.length) {
              const chapter = book.chapters[chapterIndex];

              // 使用分段器处理章节内容，不限制段落数量
              const segments = this.segmenter.segmentChapter(chapter.content);

              // 添加到收集的段落中
              bookChapters.push({
                bookIndex,
                bookTitle: book.title,
                chapter,
                segments
              });
            }
          }

          // 找出最大段落数
          const maxSegmentsCount = Math.max(...bookChapters.map(item => item.segments.length));

          // 真正的交叉发送：先发送所有书籍的第1段，然后是所有书籍的第2段，以此类推
          for (let segmentIndex = 0; segmentIndex < maxSegmentsCount; segmentIndex++) {
            // 添加段落组标题
            messageBuilder.addUserMessage(`#### 第${segmentIndex + 1}段落组`);

            // 对于每本书，发送当前段落索引的段落
            for (const bookChapter of bookChapters) {
              // 检查该书的当前章节是否有此段落
              if (segmentIndex < bookChapter.segments.length) {
                const segment = bookChapter.segments[segmentIndex];
                const wordCount = segment.replace(/\s+/g, '').length;

                // 每个段落作为单独的消息，包含段落编号和字数信息
                messageBuilder.addUserMessage(`【${bookChapter.bookTitle} - ${bookChapter.chapter.title}】\n段落：${segmentIndex+1}/${bookChapter.segments.length}\n字数：${wordCount}\n\n${segment}`);

                // 每个段落后添加确认消息
                messageBuilder.addAssistantMessage(`我已阅读"${bookChapter.bookTitle}"的"${bookChapter.chapter.title}"章节的第${segmentIndex+1}段内容。`);
              }
            }

            // 每个段落组后添加小结
            messageBuilder.addUserMessage(`请对以上第${segmentIndex + 1}段落组的内容进行简要对比。`);
            messageBuilder.addAssistantMessage(`我已对第${segmentIndex + 1}段落组的内容进行了对比，发现了一些相似点和差异。`);
          }

          // 每组章节后添加对比提示，强调寻找共同点
          messageBuilder.addUserMessage(`请对以上第${chapterIndex + 1}章节组的内容进行对比分析，特别注意寻找这些章节在人物、情节、风格、主题等方面的共同点和相似模式。这些共同点将是混合创作的重要基础。`);
          messageBuilder.addAssistantMessage(`我将对第${chapterIndex + 1}章节组的内容进行对比分析，重点寻找共同点和相似模式。`);
        }
      } else {
        // 其他模式（同步、合并）：按书籍顺序处理
        // 为每本书添加基本信息
        for (let bookIndex = 0; bookIndex < this.books.length; bookIndex++) {
          const book = this.books[bookIndex];
          messageBuilder.addUserMessage(`## 作品${bookIndex + 1}：《${book.title}》\n小说包含${book.chapters.length}个章节。`);

          // 处理所有章节，不限制数量
          const chaptersToProcess = book.chapters;

          // 为每个章节添加分段内容
          for (const chapter of chaptersToProcess) {
            // 添加章节标题
            messageBuilder.addUserMessage(`### ${chapter.title}`);

            // 使用分段器处理章节内容，不限制段落数量
            const segments = this.segmenter.segmentChapter(chapter.content); // 处理所有段落

            // 添加章节分段消息
            messageBuilder.addAssistantMessage(`我将阅读"${book.title}"的"${chapter.title}"章节，共${segments.length}个段落。`);

            // 为每个段落添加单独的消息
            for (let i = 0; i < segments.length; i++) {
              const segment = segments[i];
              const wordCount = segment.replace(/\s+/g, '').length;

              // 每个段落作为单独的消息，包含段落编号和字数信息
              messageBuilder.addUserMessage(`【${book.title} - ${chapter.title}】\n段落：${i+1}/${segments.length}\n字数：${wordCount}\n\n${segment}`);

              // 每个段落后添加确认消息
              messageBuilder.addAssistantMessage(`我已阅读"${book.title}"的"${chapter.title}"章节的第${i+1}段内容。`);
            }

            // 章节结束消息
            messageBuilder.addAssistantMessage(`我已完成"${book.title}"的"${chapter.title}"章节的阅读。`);
          }

          // 所有章节都已处理，不需要添加提示
          // 移除原来的提示代码，因为我们现在处理所有章节
        }
      }
    }

    // 最后添加分析指令，根据不同模式添加不同的共同点分析要求
    let finalInstruction = `请根据以上章节内容，进行全面分析，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考`;

    // 根据分析模式添加共同点分析的特定指令
    if (this.analysisMode === 'mixed') {
      finalInstruction += `\n\n特别重要：请专门创建一个"共同元素分析"章节，详细分析这些作品在人物、情节、风格、世界观和主题等方面的共同点和相似模式。这些共同点是混合创作的基础，也是理解作者创作倾向的关键。`;
    } else if (this.analysisMode === 'sync') {
      finalInstruction += `\n\n特别重要：在完成每部作品的独立分析后，请添加一个"作品共性分析"章节，深入探讨这些作品在各个维度上的共同特征和模式，这有助于理解创作规律和作者风格。`;
    } else if (this.analysisMode === 'merged') {
      finalInstruction += `\n\n特别重要：在合并分析中，请特别关注并详细阐述这些作品的共同点和相似模式，这是整合分析的核心部分。`;
    }

    finalInstruction += `\n\n请以Markdown格式输出分析结果，使用标题、列表等元素组织内容，确保分析深入、专业且有洞见。`;

    messageBuilder.addUserMessage(finalInstruction);

    // 将通用消息格式转换为AIMessage格式
    return messageBuilder.build().map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content
    }));
  }

  /**
   * 分析单本书籍（非流式，保留作为备用）
   * @returns 分析结果
   * @deprecated 使用analyzeSingleBookWithStreaming代替
   */
  // @ts-ignore - 保留作为备用方法，不直接使用
  private async analyzeSingleBook(): Promise<string> {
    // 此方法保留作为备用，实际使用analyzeSingleBookWithStreaming
    return this.analyzeSingleBookWithStreaming();
  }

  /**
   * 分析合并书籍（流式输出）
   * @returns 分析结果
   */
  private async analyzeMergedBooksWithStreaming(): Promise<string> {
    if (this.books.length === 0) {
      throw new Error('未设置要分析的书籍');
    }

    // 从提示词模板中获取系统提示词
    // 默认使用第一个合并拆解提示词模板的内容作为基础
    const defaultPrompt = mergedBookBreakdownPrompts[0].content;

    // 构建系统提示词 - 如果有自定义提示词模板，则使用自定义模板
    const systemPrompt = this.promptTemplate ?
      `你是一位专业的文学比较分析专家，擅长对多部作品进行整合分析。
请根据以下要求对提供的多部小说内容进行合并分析：

${this.promptTemplate}

请以Markdown格式输出分析结果，使用标题、列表等元素组织内容，确保分析深入、专业且有洞见。` :
      // 否则使用默认提示词模板
      defaultPrompt;

    // 构建用户提示（仅用于非分段模式，实际上不会使用）
    let userPrompt = `请对以下${this.books.length}部小说作品进行合并分析：\n\n`;

    // 获取API配置
    const apiConfig = await this.getApiConfig();

    // 构建消息数组，使用更复杂的消息结构和章节分段
    const messages: AIMessage[] = this.constructMessages(systemPrompt, userPrompt);

    // 使用流式输出
    let fullText = '';

    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        (chunk) => {
          // 每次收到数据块时更新全文
          fullText += chunk;

          // 使用updateAnalysisText方法更新文本，但不触发成功通知
          this.updateAnalysisText(fullText);
        },
        {
          streaming: true,
          signal: this.abortController?.signal
        }
      );

      // 分析完成，触发一次成功通知
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(response.text);
      }

      return response.text;
    } catch (error: any) {
      console.error('AI合并分析失败:', error);

      // 如果有回调函数，通知错误
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(`合并分析过程中出现错误: ${error.message || '未知错误'}`);
      }

      return `分析失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 分析合并书籍（非流式，保留作为备用）
   * @returns 分析结果
   * @deprecated 使用analyzeMergedBooksWithStreaming代替
   */
  // @ts-ignore - 保留作为备用方法，不直接使用
  private async analyzeMergedBooks(): Promise<string> {
    // 此方法保留作为备用，实际使用analyzeMergedBooksWithStreaming
    return this.analyzeMergedBooksWithStreaming();
  }

  /**
   * 分析混合书籍（流式输出）
   * @returns 分析结果
   */
  private async analyzeMixedBooksWithStreaming(): Promise<string> {
    if (this.books.length === 0) {
      throw new Error('未设置要分析的书籍');
    }

    // 从提示词模板中获取系统提示词
    // 默认使用第一个混合拆解提示词模板的内容作为基础
    const defaultPrompt = mixedBookBreakdownPrompts[0].content;

    // 构建系统提示词 - 如果有自定义提示词模板，则使用自定义模板
    const systemPrompt = this.promptTemplate ?
      `你是一位专业的文学混合分析专家，擅长从多部作品中提取元素并创造性地混合分析。
请根据以下要求对提供的多部小说内容进行混合分析：

${this.promptTemplate}

请以Markdown格式输出分析结果，使用标题、列表等元素组织内容，确保分析有创意、专业且有洞见。` :
      // 否则使用默认提示词模板
      defaultPrompt;

    // 构建用户提示（仅用于非分段模式，实际上不会使用）
    let userPrompt = `请对以下${this.books.length}部小说作品进行混合分析，创造性地整合它们的元素：\n\n`;

    // 获取API配置
    const apiConfig = await this.getApiConfig();

    // 混合分析需要更高的创造性
    const temperature = Math.min(apiConfig.temperature! * 1.15, 0.9);

    // 构建消息数组，使用更复杂的消息结构和章节分段
    const messages: AIMessage[] = this.constructMessages(systemPrompt, userPrompt);

    // 使用流式输出
    let fullText = '';

    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        (chunk) => {
          // 每次收到数据块时更新全文
          fullText += chunk;

          // 使用updateAnalysisText方法更新文本，但不触发成功通知
          this.updateAnalysisText(fullText);
        },
        {
          temperature: temperature, // 混合分析需要更高的创造性
          streaming: true,
          signal: this.abortController?.signal
        }
      );

      // 分析完成，触发一次成功通知
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(response.text);
      }

      return response.text;
    } catch (error: any) {
      console.error('AI混合分析失败:', error);

      // 如果有回调函数，通知错误
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(`混合分析过程中出现错误: ${error.message || '未知错误'}`);
      }

      return `分析失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 分析混合书籍（非流式，保留作为备用）
   * @returns 分析结果
   * @deprecated 使用analyzeMixedBooksWithStreaming代替
   */
  // @ts-ignore - 保留作为备用方法，不直接使用
  private async analyzeMixedBooks(): Promise<string> {
    // 此方法保留作为备用，实际使用analyzeMixedBooksWithStreaming
    return this.analyzeMixedBooksWithStreaming();
  }

  /**
   * 同步分析多本书籍（流式输出）
   * @returns 分析结果
   */
  private async analyzeSyncBooksWithStreaming(): Promise<string> {
    if (this.books.length === 0) {
      throw new Error('未设置要分析的书籍');
    }

    // 从提示词模板中获取系统提示词
    // 默认使用第一个同步拆解提示词模板的内容作为基础
    // 注意：这里我们可以使用模板内容，但目前直接使用硬编码的提示词，后续可以改进

    // 构建系统提示词 - 如果有自定义提示词模板，则使用自定义模板
    const systemPrompt = this.promptTemplate ?
      `你是一位专业的文学分析专家，擅长对多部作品进行并行分析，保持每部作品的独立性。
请根据以下要求对提供的多部小说内容进行同步分析，为每部作品提供独立的分析：

${this.promptTemplate}

请以Markdown格式输出分析结果，为每部作品创建独立的章节，使用标题、列表等元素组织内容，确保分析深入、专业且有洞见。` :
      // 否则使用默认提示词
      `你是一位专业的文学分析专家，擅长对多部作品进行并行分析，保持每部作品的独立性。
请对提供的多部小说内容进行同步分析，为每部作品提供独立的分析，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹
2. 情节分析：主要情节线、冲突设置、高潮转折
3. 写作风格分析：语言特点、叙事视角、修辞手法
4. 世界观设定分析：背景设定、规则体系、文化元素
5. 主题分析：核心主题、象征意义、哲学思考

请以Markdown格式输出分析结果，为每部作品创建独立的章节，使用标题、列表等元素组织内容，确保分析深入、专业且有洞见。`;

    // 构建用户提示（仅用于非分段模式，实际上不会使用）
    let userPrompt = `请对以下${this.books.length}部小说作品进行同步分析，为每部作品提供独立的分析：\n\n`;

    // 获取API配置
    const apiConfig = await this.getApiConfig();

    // 构建消息数组，使用更复杂的消息结构和章节分段
    const messages: AIMessage[] = this.constructMessages(systemPrompt, userPrompt);

    // 使用流式输出
    let fullText = '';

    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        (chunk) => {
          // 每次收到数据块时更新全文
          fullText += chunk;

          // 使用updateAnalysisText方法更新文本，但不触发成功通知
          this.updateAnalysisText(fullText);
        },
        {
          streaming: true,
          signal: this.abortController?.signal
        }
      );

      // 分析完成，触发一次成功通知
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(response.text);
      }

      return response.text;
    } catch (error: any) {
      console.error('AI同步分析失败:', error);

      // 如果有回调函数，通知错误
      if (this.onAnalysisComplete) {
        this.onAnalysisComplete(`同步分析过程中出现错误: ${error.message || '未知错误'}`);
      }

      return `分析失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 同步分析多本书籍（非流式，保留作为备用）
   * @returns 分析结果
   * @deprecated 使用analyzeSyncBooksWithStreaming代替
   */
  // @ts-ignore - 保留作为备用方法，不直接使用
  private async analyzeSyncBooks(): Promise<string> {
    // 此方法保留作为备用，实际使用analyzeSyncBooksWithStreaming
    return this.analyzeSyncBooksWithStreaming();
  }

  /**
   * 渲染组件UI
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const AIBookAnalysisComponent = () => {
      const [selectedBooks, setSelectedBooks] = useState<string[]>([]);
      const [analysisMode, setAnalysisMode] = useState<'single' | 'merged' | 'mixed' | 'sync'>(this.analysisMode);
      const [promptTemplate, setPromptTemplate] = useState(this.promptTemplate);
      const [analysisResult, setAnalysisResult] = useState('');
      const [isAnalyzing, setIsAnalyzing] = useState(false);
      const [availableBooks, setAvailableBooks] = useState<Book[]>([]);
      const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);
      const [successMessage, setSuccessMessage] = useState<string | null>(null);
      const [errorMessage, setErrorMessage] = useState<string | null>(null);
      // 添加状态变量，用于跟踪是否已经显示过通知
      const [hasShownCompletionNotice, setHasShownCompletionNotice] = useState(false);

      // 加载书籍列表
      useEffect(() => {
        const loadBooks = async () => {
          try {
            // 从数据库加载书籍列表
            const { bookRepository, chapterRepository } = await import('@/lib/db/repositories');

            // 获取所有书籍
            const userBooks = await bookRepository.getAll();

            // 对于每本书，加载其章节
            const booksWithChapters = await Promise.all(
              userBooks.map(async (book) => {
                const chapters = await chapterRepository.getAllByBookId(book.id!);

                // 转换为AI拆书组件需要的格式
                return {
                  id: book.id!,
                  title: book.title,
                  chapters: chapters.map(chapter => ({
                    id: chapter.id!,
                    title: chapter.title,
                    content: chapter.content
                  }))
                };
              })
            );

            setAvailableBooks(booksWithChapters);
          } catch (error) {
            console.error('加载书籍失败', error);
            setErrorMessage('加载书籍失败');
          }
        };

        loadBooks();
      }, []);

      // 处理分析
      const handleAnalyze = async () => {
        if (selectedBooks.length === 0) {
          setErrorMessage('请至少选择一本书籍');
          return;
        }

        const selectedBooksData = availableBooks.filter(book =>
          selectedBooks.includes(book.id)
        );

        this.setBooks(selectedBooksData);
        this.setAnalysisMode(analysisMode);
        this.setPromptTemplate(promptTemplate);

        // 重置通知状态
        setHasShownCompletionNotice(false);

        // 设置回调函数
        this.setOnAnalysisComplete((result) => {
          setAnalysisResult(result);

          // 只有在分析过程结束且尚未显示过通知时才显示成功通知
          if (!isAnalyzing && !hasShownCompletionNotice) {
            setSuccessMessage('分析完成');
            setHasShownCompletionNotice(true);

            // 3秒后清除成功消息
            setTimeout(() => {
              setSuccessMessage(null);
            }, 3000);
          }
        });

        setIsAnalyzing(true);
        setErrorMessage(null);

        try {
          const result = await this.analyze();
          setAnalysisResult(result);

          // 分析完成后，设置状态以允许显示成功消息
          setIsAnalyzing(false);

          // 如果尚未显示过通知，则显示一次
          if (!hasShownCompletionNotice) {
            setSuccessMessage('分析完成');
            setHasShownCompletionNotice(true);

            // 3秒后清除成功消息
            setTimeout(() => {
              setSuccessMessage(null);
            }, 3000);
          }
        } catch (error: any) {
          console.error('分析书籍失败', error);
          setErrorMessage(`分析失败: ${error.message || '未知错误'}`);
          setIsAnalyzing(false);
        }
      };

      // 处理取消分析
      const handleCancelAnalysis = () => {
        this.cancelAnalysis();
        setIsAnalyzing(false);
        // 重置通知状态
        setHasShownCompletionNotice(false);
      };

      // 切换书籍选择
      const toggleBookSelection = (bookId: string) => {
        setSelectedBooks(prev =>
          prev.includes(bookId)
            ? prev.filter(id => id !== bookId)
            : [...prev, bookId]
        );
      };

      // 处理选择提示词模板
      const handleSelectTemplate = (template: any) => {
        setPromptTemplate(template.content);
        setSuccessMessage('已应用提示词模板');

        // 3秒后清除成功消息
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      };

      // 直接在JSX中使用handleSelectTemplate，不需要额外的变量

      // 导出分析结果
      const exportAnalysisResult = () => {
        if (!analysisResult) return;

        try {
          // 创建Blob对象
          const blob = new Blob([analysisResult], { type: 'text/markdown' });

          // 创建下载链接
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `书籍分析结果_${new Date().toISOString().slice(0, 10)}.md`;

          // 触发下载
          document.body.appendChild(a);
          a.click();

          // 清理
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          setSuccessMessage('分析结果已导出');

          // 3秒后清除成功消息
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        } catch (error) {
          console.error('导出分析结果失败', error);
          setErrorMessage('导出分析结果失败');
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">AI拆书</h2>

          {/* 成功消息 */}
          {successMessage && (
            <div className="mb-4 p-2 bg-green-100 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          {/* 错误消息 */}
          {errorMessage && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
              {errorMessage}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  选择书籍
                </label>
                <div className="border border-gray-300 rounded p-2 max-h-40 overflow-y-auto">
                  {availableBooks.map(book => (
                    <div key={book.id} className="flex items-center mb-1">
                      <input
                        type="checkbox"
                        id={`book-${book.id}`}
                        checked={selectedBooks.includes(book.id)}
                        onChange={() => toggleBookSelection(book.id)}
                        className="mr-2"
                      />
                      <label htmlFor={`book-${book.id}`}>{book.title}</label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分析模式
                </label>
                <select
                  className="w-full p-2 border border-gray-300 rounded"
                  value={analysisMode}
                  onChange={(e) => setAnalysisMode(e.target.value as any)}
                >
                  <option value="single">单本拆解</option>
                  <option value="merged">合并拆解</option>
                  <option value="mixed">混合拆解</option>
                  <option value="sync">同步拆解</option>
                </select>
              </div>

              <div className="mb-4">
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    提示词模板
                  </label>
                  <button
                    className="text-sm text-blue-600 hover:text-blue-800"
                    onClick={() => setIsPromptManagerOpen(true)}
                  >
                    选择模板
                  </button>
                </div>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={4}
                  value={promptTemplate}
                  onChange={(e) => setPromptTemplate(e.target.value)}
                  placeholder="请输入提示词模板或点击'选择模板'使用已保存的模板..."
                />
              </div>

              {isAnalyzing ? (
                <button
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  onClick={handleCancelAnalysis}
                >
                  取消分析
                </button>
              ) : (
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
                  onClick={handleAnalyze}
                  disabled={selectedBooks.length === 0}
                >
                  分析书籍
                </button>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分析结果
              </label>
              <div className="p-3 bg-gray-50 rounded border border-gray-200 h-96 overflow-auto whitespace-pre-wrap">
                {isAnalyzing ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
                    <span className="ml-2">分析中...</span>
                  </div>
                ) : (
                  analysisResult || '分析结果将显示在这里...'
                )}
              </div>

              {analysisResult && !isAnalyzing && (
                <button
                  className="mt-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                  onClick={exportAnalysisResult}
                >
                  导出分析结果
                </button>
              )}
            </div>
          </div>

          {/* 提示词模板管理器 */}
          {isPromptManagerOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-4 rounded-lg w-full max-w-lg">
                <h3 className="text-lg font-bold mb-4">选择提示词模板</h3>
                <div className="mb-4">
                  <p className="text-sm text-gray-600">
                    请从下面选择一个提示词模板，或者关闭此窗口继续使用当前模板。
                  </p>
                </div>
                <div className="flex justify-end">
                  <button
                    className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2"
                    onClick={() => handleSelectTemplate({ content: promptTemplate })}
                  >
                    应用当前模板
                  </button>
                  <button
                    className="px-3 py-1 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 mr-2"
                    onClick={() => setIsPromptManagerOpen(false)}
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      );
    };

    return <AIBookAnalysisComponent />;
  }
}
