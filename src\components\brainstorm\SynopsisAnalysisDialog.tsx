import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  KeywordElement as FactoryKeywordElement,
  TitleFramework as FactoryTitleFramework
} from '@/factories/ai/services/brainstorm';
import { configService } from '@/services/configService';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
// 简单的SVG图标组件
const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

const Loader2Icon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M21 12a9 9 0 11-6.219-8.56"></path>
  </svg>
);

const CheckCircleIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M22 11.08V12a10 10 0 11-5.93-9.14"></path>
    <polyline points="22,4 12,14.01 9,11.01"></polyline>
  </svg>
);

const AlertCircleIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="8" x2="12" y2="12"></line>
    <line x1="12" y1="16" x2="12.01" y2="16"></line>
  </svg>
);

// 简介分析结果接口
interface SynopsisAnalysisResult {
  synopsis: string;
  extractedKeywords: string[];
  keywords: Array<{
    text: string;
    category: string;
  }>;
  detectedFramework: string;
  frameworkPattern: string;
  synopsisCategory: string;
  confidence: number;
  structureAnalysis?: {
    opening: string;
    development: string;
    ending: string;
  };
  // 新增技巧分析字段
  writingTechniques?: Array<{
    id: string;
    name: string;
    category: string;
    description: string;
    examples: string[];
    techniqueType: string;
    effectiveness: number;
  }>;
  styleCharacteristics?: {
    layoutTechniques: any;
    omissionAndEmphasis: any;
    coolPointLayout: any;
    creativeConcept: any;
  };
  reusableTemplates?: Array<{
    id: string;
    name: string;
    pattern: string;
    description: string;
    techniques: string[];
    effectiveness: number;
  }>;
  techniqueAnalysisConfidence?: number;
}

interface SynopsisAnalysisDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAnalysisComplete: (keywords: FactoryKeywordElement[], frameworks: FactoryTitleFramework[]) => void;
  existingKeywords: FactoryKeywordElement[];
  existingFrameworks: FactoryTitleFramework[];
}

/**
 * 简介拆解分析弹窗
 * 用于分析现有简介，提取关键词和框架模式
 */
const SynopsisAnalysisDialog: React.FC<SynopsisAnalysisDialogProps> = ({
  isOpen,
  onClose,
  onAnalysisComplete,
  existingKeywords,
  existingFrameworks
}) => {
  const [inputSynopsis, setInputSynopsis] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<SynopsisAnalysisResult[]>([]);
  const [selectedResults, setSelectedResults] = useState<Set<number>>(new Set());

  // 分析简介 - 使用真实AI服务
  const analyzeSynopsis = useCallback(async () => {
    if (!inputSynopsis.trim()) return;

    setIsAnalyzing(true);
    try {
      const synopsisList = inputSynopsis.split('\n\n').filter(s => s.trim());

      // 构建现有框架参考信息
      let existingFrameworksInfo = '';
      if (existingFrameworks && existingFrameworks.length > 0) {
        existingFrameworksInfo = `

【现有框架库参考】
以下是当前已有的框架模式，请优先匹配这些框架，如果简介符合某个现有框架，请使用相同的框架名称和模式：

${existingFrameworks.map((framework, index) =>
  `${index + 1}. 框架名称：${framework.name}
   框架模式：${framework.pattern}
   框架说明：${framework.description}
   参考示例：${framework.examples.slice(0, 2).join('、')}${framework.examples.length > 2 ? '等' : ''}`
).join('\n\n')}

分析指导：
- 如果简介符合上述任一框架模式，请使用相同的框架名称和模式
- 只有在简介确实展现了全新的结构模式时，才创建新的框架
- 优先考虑框架的核心结构特征，而非表面词汇`;
      }

      // 构建AI分析请求
      const analysisPrompt = `你是一位资深的网文简介分析专家，专门研究简介的写作技巧和吸引力机制。你的任务是深入分析简介，提取关键词、框架模式，并分析写作技巧。

【写作技巧分析维度】

1. 排版技巧分析：
   - 段落结构：单段式（一气呵成）、多段式（层次递进）、递进式（逐步揭示）
   - 换行策略：悬念换行（关键信息前换行）、爽点换行（高潮前换行）、节奏换行（调节阅读节奏）
   - 节奏控制：快节奏展开（信息密集）、慢节奏铺垫（逐步建立）、变节奏起伏（张弛有度）
   - 视觉冲击：排版的视觉效果和阅读体验

2. 省略与强调分析：
   - 省略要素：故意不写的信息（背景细节、人物外貌、具体设定等）
   - 强调要素：重点突出的信息（身份反差、能力觉醒、势力震惊等）
   - 对比技巧：前后对比、身份对比、实力对比等制造反差
   - 悬念设置：身份悬念、能力悬念、关系悬念等吸引读者

3. 爽点布局分析：
   - 主要爽点：装逼打脸、身份暴露、实力碾压、马甲掉落等
   - 爽点时机：开头爆点（立即吸引）、中段高潮（维持兴趣）、结尾悬念（期待后续）
   - 爽点强度：爽点的冲击力和吸引力程度
   - 期待营造：预告式（明确预告）、暗示式（隐晦暗示）、对比式（通过对比营造）

4. 脑洞展现分析：
   - 核心创意：设定创新、身份创新、能力创新、世界观创新
   - 概念展现：直接展示（明确说明）、逐步揭示（层层递进）、反转展现（意外揭示）
   - 独特性程度：创意的新颖性和差异化程度
   - 市场吸引力：热门元素、差异化卖点、读者痛点

网文类别包括：
- fantasy（玄幻）：修仙、法宝、境界、天才、废材、逆天、神通、仙界、灵气、丹药
- urban（都市）：都市、总裁、豪门、校园、职场、商战、医生、律师、重生、系统
- history（历史）：穿越、古代、皇帝、朝廷、战争、谋士、将军、王朝、宫廷、江湖
- scifi（科幻）：未来、科技、机甲、星际、外星、基因、虚拟、时空、末世、进化
- xianxia（仙侠）：仙侠、修真、飞升、渡劫、元婴、金丹、筑基、炼气、仙门、道法
- military（军事）：军事、战争、特种兵、军官、战场、武器、战略、部队、任务、敌人
- mystery（悬疑）：悬疑、推理、犯罪、侦探、谋杀、真相、线索、破案、秘密、阴谋
- romance（言情）：言情、恋爱、霸道、甜宠、虐恋、婚姻、情感、浪漫、暖文、追妻
- game（游戏）：游戏、网游、竞技、电竞、副本、装备、技能、公会、升级、PK
- sports（体育）：体育、足球、篮球、运动、比赛、冠军、训练、球员、教练、梦想
- anime（二次元）：二次元、动漫、萝莉、御姐、后宫、魔法、学园、异世界、召唤、冒险
- other（其他）：不属于以上任何类别

简介列表：
${synopsisList.map((synopsis, index) => `${index + 1}. ${synopsis}`).join('\n\n')}

请对每个简介进行分析，提取：
1. 关键词列表（提取简介中的核心词汇，如"重生"、"系统"、"逆袭"等，并为每个关键词标注所属网文类别）
2. 框架模式（简介的结构模式，用{}表示变量，如"{主角}重生{时间}，{目标}"）
3. 框架名称（给这种模式起个简洁的名字，如"重生逆袭模式"）
4. 简介类别（判断整个简介所属的主要网文类别）
5. 置信度（0-1之间的数值，表示分析的准确性）
6. 结构分析（必须包含具体的分析内容，不能使用"未分析"等占位符）
7. 写作技巧分析（从排版、省略强调、爽点布局、脑洞展现四个维度分析）

请严格按照以下JSON格式返回，不要添加任何其他内容：
{
  "analysis": [
    {
      "synopsis": "简介内容",
      "keywords": [
        {
          "text": "关键词",
          "category": "网文类别代码"
        }
      ],
      "frameworkPattern": "框架模式",
      "frameworkName": "框架名称",
      "synopsisCategory": "网文类别代码",
      "confidence": 0.9,
      "structureAnalysis": {
        "opening": "具体描述开头的模式，如：主角身份介绍+背景设定+初始困境",
        "development": "具体描述发展的模式，如：冲突升级+能力觉醒+关键转折",
        "ending": "具体描述结尾的模式，如：目标达成+新挑战预告+悬念设置"
      },
      "writingTechniques": [
        {
          "id": "technique_1",
          "name": "技巧名称",
          "category": "layout|emphasis|coolpoint|creativity",
          "description": "技巧描述",
          "examples": ["具体应用示例"],
          "applicableScenarios": ["适用场景"],
          "difficulty": "basic|intermediate|advanced",
          "effectiveness": 0.8,
          "techniqueType": "omission|emphasis|contrast|suspense|rhythm|layout|coolpoint"
        }
      ],
      "styleCharacteristics": {
        "layoutTechniques": {
          "paragraphStructure": ["段落结构特征"],
          "lineBreakStrategy": ["换行策略"],
          "rhythmControl": ["节奏控制方式"],
          "visualImpact": 0.8
        },
        "omissionAndEmphasis": {
          "omittedElements": ["省略的要素"],
          "emphasizedElements": ["强调的要素"],
          "contrastTechniques": ["对比技巧"],
          "suspensePoints": ["悬念设置"]
        },
        "coolPointLayout": {
          "primaryCoolPoints": ["主要爽点"],
          "coolPointTiming": ["爽点时机"],
          "coolPointIntensity": 0.8,
          "anticipationBuilding": ["期待营造方式"]
        },
        "creativeConcept": {
          "coreCreativity": ["核心创意"],
          "conceptPresentation": ["概念展现方式"],
          "uniquenessLevel": 0.8,
          "marketAppeal": ["市场吸引力要素"]
        }
      },
      "reusableTemplates": [
        {
          "id": "template_1",
          "name": "模板名称",
          "pattern": "模板模式",
          "description": "模板描述",
          "techniques": ["使用的技巧"],
          "applicableGenres": ["适用类型"],
          "effectiveness": 0.8,
          "examples": ["应用示例"]
        }
      ],
      "techniqueAnalysisConfidence": 0.85
    }
  ]
}

重要要求：
- 优先匹配现有框架库中的框架，使用相同的框架名称和模式
- structureAnalysis字段必须包含具体的分析内容，不能使用"未分析"等占位符
- opening、development、ending每个字段都必须是具体的模式描述
- 如果无法分析结构，请省略structureAnalysis字段，不要填入空值或占位符
- 只有在确实发现新的结构模式时才创建新框架`;

      // 获取API配置
      console.log('🔧 获取API配置...');
      const aiConfig = await configService.getAIConfig();
      console.log('📋 API配置详情:', {
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        model: aiConfig.model,
        hasApiKey: !!aiConfig.apiKey
      });

      console.log('🔍 开始AI简介分析...');

      // 构建多组对话预设的消息
      const messages = [
        // 系统消息：直接使用analysisPrompt作为系统提示词
        {
          role: 'system',
          content: analysisPrompt
        },

        // 助手确认消息：确认理解任务并包含现有框架信息
        {
          role: 'assistant',
          content: `我已准备好进行专业的简介分析。我将从实际简介内容中提取关键词、识别结构框架，并分析写作技巧，将分析结果转化为可复用的知识资产。${existingFrameworksInfo}`
        },

        // 用户消息：提供待分析的简介
        {
          role: 'user',
          content: `请分析以下网文简介：

${synopsisList.map((synopsis, index) => `${index + 1}. ${synopsis}`).join('\n\n')}`
        },

        // 助手确认消息：确认分析方向
        {
          role: 'assistant',
          content: '我将深入分析这些简介的实际内容，提取真实使用的关键词和结构模式，为您的框架库提供可复用的知识资产。'
        },

        // 用户消息：要求开始分析
        {
          role: 'user',
          content: '请开始分析，严格按照JSON格式返回结果。'
        }
      ];

      // 使用统一AI服务
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      const aiService = AIServiceFactory.getService(AIServiceType.TOOL_CALL);

      const response = await aiService.callAI(messages, {
        streaming: false // 分析不需要流式输出
      });

      if (!response.success || !response.text) {
        throw new Error(response.error || 'AI分析失败');
      }

      console.log('📝 AI原始响应:', response.text);

      // 使用AIResponseParser解析响应
      const defaultValue = { analysis: [] };
      const parsedResponse = AIResponseParser.parseJSON(response.text, defaultValue);

      console.log('🔍 解析后的响应:', parsedResponse);

      if (!parsedResponse.analysis || !Array.isArray(parsedResponse.analysis)) {
        throw new Error('AI响应格式不正确：缺少analysis数组');
      }

      // 转换为内部格式
      const results: SynopsisAnalysisResult[] = parsedResponse.analysis.map((item: any) => {
        const extractedKeywords = (item.keywords || []).map((k: any) => k.text || '').filter(Boolean);
        const keywords = (item.keywords || []).map((k: any) => ({
          text: k.text || '',
          category: k.category || 'other'
        }));

        // 增强结构分析数据检查
        console.log('🔍 结构分析数据检查:', {
          hasStructureAnalysis: !!item.structureAnalysis,
          structureData: item.structureAnalysis,
          opening: item.structureAnalysis?.opening,
          development: item.structureAnalysis?.development,
          ending: item.structureAnalysis?.ending
        });

        // 验证结构分析数据的有效性
        const hasValidStructureAnalysis = item.structureAnalysis &&
          item.structureAnalysis.opening &&
          item.structureAnalysis.development &&
          item.structureAnalysis.ending &&
          item.structureAnalysis.opening !== '未分析' &&
          item.structureAnalysis.development !== '未分析' &&
          item.structureAnalysis.ending !== '未分析' &&
          item.structureAnalysis.opening.trim().length > 0 &&
          item.structureAnalysis.development.trim().length > 0 &&
          item.structureAnalysis.ending.trim().length > 0;

        return {
          synopsis: item.synopsis || '',
          extractedKeywords,
          keywords,
          detectedFramework: item.frameworkName || '未知模式',
          frameworkPattern: item.frameworkPattern || '{未知模式}',
          synopsisCategory: item.synopsisCategory || 'other',
          confidence: typeof item.confidence === 'number' ? item.confidence : 0.5,
          structureAnalysis: hasValidStructureAnalysis ? {
            opening: item.structureAnalysis.opening,
            development: item.structureAnalysis.development,
            ending: item.structureAnalysis.ending
          } : undefined,
          // 新增写作技巧分析字段
          writingTechniques: item.writingTechniques || [],
          styleCharacteristics: item.styleCharacteristics || {},
          reusableTemplates: item.reusableTemplates || [],
          techniqueAnalysisConfidence: typeof item.techniqueAnalysisConfidence === 'number' ? item.techniqueAnalysisConfidence : undefined
        };
      });

      console.log('✅ 分析完成，共解析', results.length, '个简介');

      setAnalysisResults(results);
      // 默认选中所有结果
      setSelectedResults(new Set(results.map((_, index) => index)));
    } catch (error) {
      console.error('❌ 简介分析失败:', error);

      // 详细的错误信息
      let errorMessage = '未知错误';
      let troubleshootingTips = '';

      if (error instanceof Error) {
        errorMessage = error.message;

        // 根据错误类型提供具体的解决建议
        if (errorMessage.includes('API密钥') || errorMessage.includes('API key')) {
          troubleshootingTips = '\n💡 解决建议：\n1. 检查API设置中是否正确配置了API密钥\n2. 确认API密钥格式正确且有效\n3. 检查API密钥是否有足够的使用额度';
        } else if (errorMessage.includes('网络') || errorMessage.includes('network')) {
          troubleshootingTips = '\n💡 解决建议：\n1. 检查网络连接是否正常\n2. 尝试刷新页面重试\n3. 检查防火墙或代理设置';
        } else if (errorMessage.includes('JSON') || errorMessage.includes('格式')) {
          troubleshootingTips = '\n💡 解决建议：\n1. AI响应格式异常，请重试\n2. 如果问题持续，请检查输入的简介格式\n3. 尝试减少一次分析的简介数量';
        } else {
          troubleshootingTips = '\n💡 解决建议：\n1. 检查网络连接是否正常\n2. 确认API密钥配置正确\n3. 尝试减少输入内容后重试\n4. 如果问题持续，请联系技术支持';
        }
      }

      // 显示用户友好的错误信息
      alert(`❌ 简介分析失败\n\n错误详情：${errorMessage}${troubleshootingTips}`);
    } finally {
      setIsAnalyzing(false);
    }
  }, [inputSynopsis, existingFrameworks]);

  // 保存选中的分析结果
  const saveSelectedResults = useCallback(() => {
    const selectedAnalysis = analysisResults.filter((_, index) => selectedResults.has(index));
    
    if (selectedAnalysis.length === 0) {
      alert('请至少选择一个分析结果');
      return;
    }

    // 处理关键词：相同的合并，不同的创建
    const newKeywords: FactoryKeywordElement[] = [];
    const keywordMap: Map<string, {
      frequency: number;
      categories: Set<string>;
    }> = new Map();

    selectedAnalysis.forEach(result => {
      result.keywords.forEach(keyword => {
        if (!keywordMap.has(keyword.text)) {
          keywordMap.set(keyword.text, {
            frequency: 0,
            categories: new Set()
          });
        }
        const data = keywordMap.get(keyword.text)!;
        data.frequency++;
        data.categories.add(keyword.category);
      });
    });

    // 处理关键词：相同的合并，不同的创建
    keywordMap.forEach((data, text) => {
      const existingKeyword = existingKeywords.find(k => k.text === text);
      if (existingKeyword) {
        // 相同的关键词：合并频次
        existingKeyword.frequency += data.frequency;
        existingKeyword.lastUsedAt = new Date();
        // 如果是分析提取的，添加标签
        if (!existingKeyword.tags) {
          existingKeyword.tags = [];
        }
        if (!existingKeyword.tags.includes('简介分析')) {
          existingKeyword.tags.push('简介分析');
        }
      } else {
        // 不同的关键词：创建新的
        const categoryTags = Array.from(data.categories);
        newKeywords.push({
          id: `synopsis_analyzed_${Date.now()}_${Math.random()}`,
          text,
          frequency: data.frequency,
          hotness: Math.min(10, 5 + data.frequency), // 根据频次计算热度
          tags: ['简介分析', ...categoryTags],
          createdAt: new Date(),
          lastUsedAt: new Date()
        });
      }
    });

    // 处理框架
    const newFrameworks: FactoryTitleFramework[] = [];
    const frameworkMap: Map<string, {
      pattern: string;
      examples: string[];
      count: number;
      categories: Set<string>;
    }> = new Map();

    selectedAnalysis.forEach(result => {
      const key = result.detectedFramework;
      if (!frameworkMap.has(key)) {
        frameworkMap.set(key, {
          pattern: result.frameworkPattern,
          examples: [],
          count: 0,
          categories: new Set()
        });
      }
      const framework = frameworkMap.get(key)!;
      framework.examples.push(result.synopsis); // 保存完整的简介内容作为参考示例
      framework.categories.add(result.synopsisCategory);
      framework.count++;
    });

    frameworkMap.forEach((data, name) => {
      const existingFramework = existingFrameworks.find(f => f.name === name);
      if (!existingFramework) {
        // 查找第一个有有效结构分析的结果作为框架结构参考
        const resultWithStructure = selectedAnalysis.find(result =>
          result.detectedFramework === name && result.structureAnalysis
        );

        // 查找第一个有写作技巧分析的结果
        const resultWithTechniques = selectedAnalysis.find(result =>
          result.detectedFramework === name && (
            (result.writingTechniques && result.writingTechniques.length > 0) ||
            result.styleCharacteristics ||
            (result.reusableTemplates && result.reusableTemplates.length > 0)
          )
        );

        // 创建新框架，包含完整的分析数据
        const newFramework: FactoryTitleFramework = {
          id: `synopsis_framework_${Date.now()}_${Math.random()}`,
          name,
          pattern: data.pattern,
          description: `从${data.count}个简介中分析提取的框架模式`,
          examples: data.examples.slice(0, 3),
          variables: [],
          usageCount: data.count,
          effectiveness: Math.min(10, 5 + data.count),
          createdAt: new Date(),
          lastUsedAt: new Date(),
          // 新增：包含写作技巧分析数据
          writingTechniques: resultWithTechniques?.writingTechniques?.map(technique => ({
            ...technique,
            category: technique.category as 'layout' | 'emphasis' | 'coolpoint' | 'creativity'
          })) || [],
          styleCharacteristics: resultWithTechniques?.styleCharacteristics || {},
          reusableTemplates: resultWithTechniques?.reusableTemplates || [],
          techniqueAnalysisConfidence: resultWithTechniques?.techniqueAnalysisConfidence
        };

        // 如果有结构分析数据，添加到框架中
        if (resultWithStructure?.structureAnalysis) {
          (newFramework as any).structureAnalysis = {
            opening: resultWithStructure.structureAnalysis.opening,
            development: resultWithStructure.structureAnalysis.development,
            ending: resultWithStructure.structureAnalysis.ending
          };

          console.log('🔍 保存框架结构分析:', {
            frameworkName: name,
            structureAnalysis: (newFramework as any).structureAnalysis
          });
        }

        newFrameworks.push(newFramework);
      }
    });

    console.log('💾 保存分析结果:', {
      newKeywords: newKeywords.length,
      newFrameworks: newFrameworks.length
    });

    onAnalysisComplete(newKeywords, newFrameworks);
    onClose();
  }, [analysisResults, selectedResults, existingKeywords, existingFrameworks, onAnalysisComplete, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="bg-white rounded-xl shadow-2xl w-full max-w-5xl h-[85vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">🔍 简介关键词框架拆解</h2>
              <p className="text-sm text-gray-500 mt-1">分析现有简介，提取关键词和框架模式</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <XIcon className="w-5 h-5" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 p-6 overflow-hidden">
            <div className="h-full flex gap-6">
              {/* 左侧：输入区域 */}
              <div className="w-1/2 flex flex-col">
                <h3 className="text-sm font-medium text-gray-700 mb-3">输入简介（用空行分隔多个简介）</h3>
                <textarea
                  value={inputSynopsis}
                  onChange={(e) => setInputSynopsis(e.target.value)}
                  placeholder="他是废材，却意外获得了神秘系统。从此，修炼如喝水，突破如吃饭。看他如何从废材逆袭，成为至强者！

重生回到十八岁，这一世，我要改写命运！前世的仇人，这一世我要让你们付出代价。前世的遗憾，这一世我要一一弥补。

穿越异世界，获得最强召唤系统。召唤神兽，征服天下！看少年如何在异世界中崛起，成为传说中的召唤师！"
                  className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />

                {/* 分析说明 */}
                <div className="mt-3 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
                  <div className="text-sm font-medium text-gray-700">🎯 智能分析功能</div>
                  <div className="text-xs text-gray-500 mt-1">自动分析关键词、框架模式、结构特征和写作技巧</div>
                </div>

                <motion.button
                  onClick={analyzeSynopsis}
                  disabled={isAnalyzing || !inputSynopsis.trim()}
                  className="mt-4 py-3 bg-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  whileHover={{ scale: isAnalyzing ? 1 : 1.02 }}
                  whileTap={{ scale: isAnalyzing ? 1 : 0.98 }}
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2Icon className="w-4 h-4 animate-spin" />
                      分析中...
                    </>
                  ) : (
                    '🔍 开始分析'
                  )}
                </motion.button>
              </div>

              {/* 右侧：分析结果 */}
              <div className="w-1/2 flex flex-col">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-700">分析结果</h3>
                  {analysisResults.length > 0 && (
                    <button
                      onClick={saveSelectedResults}
                      className="px-4 py-2 bg-green-500 text-white rounded-lg text-sm font-medium hover:bg-green-600 transition-colors"
                    >
                      保存选中结果
                    </button>
                  )}
                </div>
                
                <div className="flex-1 overflow-y-auto space-y-4">
                  {analysisResults.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-400">
                      <div className="text-center">
                        <AlertCircleIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>暂无分析结果</p>
                        <p className="text-xs mt-1">请在左侧输入简介并点击分析</p>
                      </div>
                    </div>
                  ) : (
                    analysisResults.map((result, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedResults.has(index)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          const newSelected = new Set(selectedResults);
                          if (newSelected.has(index)) {
                            newSelected.delete(index);
                          } else {
                            newSelected.add(index);
                          }
                          setSelectedResults(newSelected);
                        }}
                      >
                        <div className="flex items-start gap-2 mb-2">
                          <CheckCircleIcon
                            className={`w-4 h-4 mt-0.5 ${
                              selectedResults.has(index) ? 'text-blue-500' : 'text-gray-300'
                            }`}
                          />
                          <div className="flex-1">
                            <p className="text-xs text-gray-600 mb-1">简介片段:</p>
                            <p className="text-sm text-gray-800 mb-2 line-clamp-2">
                              {result.synopsis.substring(0, 100)}...
                            </p>
                          </div>
                        </div>
                        
                        <div className="space-y-2 text-xs">
                          <div>
                            <span className="font-medium text-gray-600">关键词:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {result.extractedKeywords.map((keyword, i) => (
                                <span key={i} className="px-2 py-1 bg-blue-100 text-blue-700 rounded">
                                  {keyword}
                                </span>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <span className="font-medium text-gray-600">框架:</span>
                            <span className="ml-2 text-gray-800">{result.detectedFramework}</span>
                          </div>
                          
                          <div>
                            <span className="font-medium text-gray-600">模式:</span>
                            <span className="ml-2 text-gray-800 font-mono text-xs">{result.frameworkPattern}</span>
                          </div>

                          {/* 结构分析 */}
                          {result.structureAnalysis ? (
                            <div>
                              <span className="font-medium text-gray-600">结构分析:</span>
                              <div className="ml-2 mt-1 space-y-1">
                                <div className="text-xs">
                                  <span className="font-medium text-blue-600">开局:</span>
                                  <span className="ml-1 text-gray-700">{result.structureAnalysis.opening}</span>
                                </div>
                                <div className="text-xs">
                                  <span className="font-medium text-green-600">发展:</span>
                                  <span className="ml-1 text-gray-700">{result.structureAnalysis.development}</span>
                                </div>
                                <div className="text-xs">
                                  <span className="font-medium text-purple-600">结尾:</span>
                                  <span className="ml-1 text-gray-700">{result.structureAnalysis.ending}</span>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                              <div className="flex items-center gap-1">
                                <span>💡</span>
                                <span>AI正在学习如何分析此类简介的结构，建议手动补充框架信息</span>
                              </div>
                            </div>
                          )}

                          {/* 写作技巧分析 */}
                          {result.writingTechniques && result.writingTechniques.length > 0 && (
                            <div>
                              <span className="font-medium text-gray-600">🎯 写作技巧:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.writingTechniques.slice(0, 3).map((technique, i) => (
                                  <span
                                    key={i}
                                    className={`px-2 py-1 text-xs rounded ${
                                      technique.category === 'layout' ? 'bg-green-100 text-green-700' :
                                      technique.category === 'emphasis' ? 'bg-orange-100 text-orange-700' :
                                      technique.category === 'coolpoint' ? 'bg-red-100 text-red-700' :
                                      'bg-purple-100 text-purple-700'
                                    }`}
                                    title={technique.description}
                                  >
                                    {technique.name}
                                  </span>
                                ))}
                                {result.writingTechniques.length > 3 && (
                                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                    +{result.writingTechniques.length - 3}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* 爽点分析 */}
                          {result.styleCharacteristics?.coolPointLayout?.primaryCoolPoints && (
                            <div>
                              <span className="font-medium text-gray-600">💥 主要爽点:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.styleCharacteristics.coolPointLayout.primaryCoolPoints.slice(0, 2).map((coolPoint: string, i: number) => (
                                  <span key={i} className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                                    {coolPoint}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 排版技巧 */}
                          {result.styleCharacteristics?.layoutTechniques?.paragraphStructure && (
                            <div>
                              <span className="font-medium text-gray-600">📝 排版技巧:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.styleCharacteristics.layoutTechniques.paragraphStructure.slice(0, 2).map((technique: string, i: number) => (
                                  <span key={i} className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                                    {technique}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 省略强调技巧 */}
                          {result.styleCharacteristics?.omissionAndEmphasis?.emphasizedElements && (
                            <div>
                              <span className="font-medium text-gray-600">🎯 强调要素:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.styleCharacteristics.omissionAndEmphasis.emphasizedElements.slice(0, 2).map((element: string, i: number) => (
                                  <span key={i} className="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">
                                    {element}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 脑洞创意 */}
                          {result.styleCharacteristics?.creativeConcept?.coreCreativity && (
                            <div>
                              <span className="font-medium text-gray-600">💡 核心创意:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.styleCharacteristics.creativeConcept.coreCreativity.slice(0, 2).map((creativity: string, i: number) => (
                                  <span key={i} className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                                    {creativity}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 可复用模板 */}
                          {result.reusableTemplates && result.reusableTemplates.length > 0 && (
                            <div>
                              <span className="font-medium text-gray-600">🔄 复用模板:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.reusableTemplates.slice(0, 2).map((template, i) => (
                                  <span key={i} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs" title={template.description}>
                                    {template.name}
                                  </span>
                                ))}
                                {result.reusableTemplates.length > 2 && (
                                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                    +{result.reusableTemplates.length - 2}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* 技巧分析置信度 */}
                          {result.techniqueAnalysisConfidence && (
                            <div className="text-xs text-purple-600 bg-purple-50 p-2 rounded border border-purple-200">
                              <div className="flex items-center gap-1">
                                <span>🎯</span>
                                <span>技巧分析置信度: {(result.techniqueAnalysisConfidence * 100).toFixed(0)}%</span>
                              </div>
                            </div>
                          )}

                          <div className="flex justify-between">
                            <span>
                              <span className="font-medium text-gray-600">类别:</span>
                              <span className="ml-2 text-gray-800">{result.synopsisCategory}</span>
                            </span>
                            <span>
                              <span className="font-medium text-gray-600">置信度:</span>
                              <span className="ml-2 text-gray-800">{(result.confidence * 100).toFixed(0)}%</span>
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SynopsisAnalysisDialog;
