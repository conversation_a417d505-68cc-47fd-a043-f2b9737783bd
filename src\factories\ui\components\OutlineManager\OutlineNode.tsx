"use client";

import React, { useState, useRef, useEffect } from 'react';
import { OutlineNodeType } from '../../types/outline';
import { NodeRelationsPanel } from './NodeRelationsPanel';
import { RelationSelector } from './RelationSelector';
import Portal from '../common/Portal';
import ConfirmDialog from '../common/ConfirmDialog';
import NodeEditDialog from './NodeEditDialog';

interface OutlineNodeProps {
  node: OutlineNodeType;
  level: number;
  bookId?: string;
  onNodeChange: (nodeId: string, updates: Partial<OutlineNodeType>) => void;
  onNodeDelete: (nodeId: string) => void;
  onAddChild: (nodeId: string) => void;
  allNodes?: OutlineNodeType[]; // 传入所有节点，用于VolumeEditor的章节选择
}

/**
 * 大纲节点组件
 */
export const OutlineNode: React.FC<OutlineNodeProps> = ({
  node,
  level,
  bookId,
  onNodeChange,
  onNodeDelete,
  onAddChild,
  allNodes
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showRelations, setShowRelations] = useState(false);
  const [showRelationSelector, setShowRelationSelector] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const nodeRef = useRef<HTMLLIElement>(null);
  const hasChildren = node.children && node.children.length > 0;

  // 当菜单打开时，计算菜单位置
  useEffect(() => {
    if (isMenuOpen && menuButtonRef.current) {
      const rect = menuButtonRef.current.getBoundingClientRect();
      setMenuPosition({
        top: rect.bottom + window.scrollY,
        left: rect.right + window.scrollX - 48 // 菜单宽度为48，右对齐
      });

      // 添加点击外部关闭菜单的事件监听
      const handleClickOutside = (event: MouseEvent) => {
        // 如果点击的不是菜单内部元素，则关闭菜单
        const portalElement = document.getElementById('outline-menu-portal');
        if (portalElement && !portalElement.contains(event.target as Node) &&
            menuButtonRef.current && !menuButtonRef.current.contains(event.target as Node)) {
          setIsMenuOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);

      // 清理函数
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isMenuOpen]);

  // 检查是否有关联元素
  const hasRelations =
    (node.relatedCharacterIds && node.relatedCharacterIds.length > 0) ||
    (node.relatedWorldBuildingIds && node.relatedWorldBuildingIds.length > 0) ||
    (node.relatedTerminologyIds && node.relatedTerminologyIds.length > 0);

  // 这个函数已移至下方，使用可选参数版本

  // 处理编辑点击
  const handleEditClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    console.log('编辑按钮被点击，打开编辑对话框...');
    setShowEditDialog(true);
    setIsMenuOpen(false);

    // 检查Portal容器是否存在
    const portalContainer = document.getElementById('node-edit-dialog-portal');
    console.log('Portal容器状态:', portalContainer ? '存在' : '不存在');
  };

  // 处理节点更新
  const handleNodeUpdate = (updatedNode: OutlineNodeType) => {
    // 传递完整的节点数据，确保所有字段都被保存
    onNodeChange(node.id, {
      ...updatedNode,
      // 显式保留位置信息，确保不会丢失
      position: updatedNode.position || node.position
    });
  };

  // 处理删除点击
  const handleDeleteClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setShowDeleteConfirm(true);
    setIsMenuOpen(false);
  };

  // 确认删除
  const confirmDelete = () => {
    setShowDeleteConfirm(false);
    setIsDeleting(true);

    // 添加延迟，等待动画完成后再实际删除
    setTimeout(() => {
      onNodeDelete(node.id);
    }, 300); // 动画持续时间
  };

  // 取消删除
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // 处理添加子节点
  const handleAddChildClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    onAddChild(node.id);
    setIsMenuOpen(false);
  };

  // 处理菜单打开/关闭
  const toggleMenu = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setIsMenuOpen(!isMenuOpen);
  };

  // 处理展开/折叠点击
  const handleExpandClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    onNodeChange(node.id, { expanded: !node.expanded });
  };

  // 获取节点类型标签和颜色
  const getNodeTypeInfo = () => {
    // 根据层级设置缩进类名
    const indentClass = `level-${Math.min(level, 5)}`;

    // 根据节点类型返回相应信息
    switch (node.type) {
      case 'volume':
        return {
          label: '总纲/卷',
          color: 'var(--outline-volume)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          )
        };
      case 'event':
        return {
          label: '事件刚',
          color: 'var(--outline-warning)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          )
        };
      case 'chapter':
        return {
          label: '章节',
          color: 'var(--outline-primary)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          )
        };
      case 'plot':
        return {
          label: '剧情节点',
          color: 'var(--outline-secondary)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
            </svg>
          )
        };
      case 'dialogue':
        return {
          label: '对话节点',
          color: 'var(--outline-info)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          )
        };
      default:
        return {
          label: node.type || '未知',
          color: 'var(--outline-info)',
          indentClass,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          )
        };
    }
  };

  const { label: nodeTypeLabel, color: nodeColor, icon: nodeIcon } = getNodeTypeInfo();

  return (
    <li className="list-none" ref={nodeRef} data-node-id={node.id}>
      <div
        className={`group flex items-center py-3 px-4 rounded-lg relative transition-all duration-300 ease-out
          ${isDeleting ? 'animate-[nodeDelete_0.3s_ease-out_forwards]' : ''}
          ${node.type === 'volume'
            ? node.expanded
              ? 'bg-white border-[var(--outline-volume)] shadow-md'
              : 'bg-white border-transparent hover:border-[var(--outline-volume-light)] hover:shadow-md'
            : node.type === 'event'
              ? node.expanded
                ? 'bg-white border-[var(--outline-warning)] shadow-md'
                : 'bg-white border-transparent hover:border-[var(--outline-warning-light)] hover:shadow-md'
              : node.type === 'chapter'
                ? node.expanded
                  ? 'bg-white border-[var(--outline-primary)] shadow-md'
                  : 'bg-white border-transparent hover:border-[var(--outline-primary-light)] hover:shadow-md'
                : node.type === 'plot'
                  ? node.expanded
                    ? 'bg-white border-[var(--outline-secondary)] shadow-md'
                    : 'bg-white border-transparent hover:border-[var(--outline-secondary-light)] hover:shadow-md'
                  : node.expanded
                    ? 'bg-white border-[var(--outline-info)] shadow-md'
                    : 'bg-white border-transparent hover:border-[var(--outline-info-light)] hover:shadow-md'
          }`}
        style={{
          marginBottom: '12px',
          animation: isDeleting ? 'none' : 'outlineNodeAppear 0.3s ease-out',
          borderWidth: '1px',
          borderLeftWidth: '3px',
          transform: 'translateZ(0)', // 启用GPU加速
          boxShadow: node.expanded
            ? `0 4px 6px -1px rgba(var(--outline-${node.type === 'volume' ? 'volume' : node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'}-rgb), 0.1), 0 2px 4px -1px rgba(var(--outline-${node.type === 'volume' ? 'volume' : node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'}-rgb), 0.06)`
            : 'none'
        }}
      >
        <div className="flex-shrink-0 w-8">
          {hasChildren ? (
            <button
              className={`w-6 h-6 flex items-center justify-center rounded-full transition-all duration-300 ease-out
                        ${node.type === 'volume'
                          ? node.expanded
                            ? `bg-[var(--outline-volume)] bg-opacity-20 text-[var(--outline-volume)]`
                            : `text-gray-400 hover:text-[var(--outline-volume)] hover:bg-[var(--outline-volume)] hover:bg-opacity-10`
                          : node.type === 'chapter'
                            ? node.expanded
                              ? `bg-[var(--outline-primary)] bg-opacity-20 text-[var(--outline-primary)]`
                              : `text-gray-400 hover:text-[var(--outline-primary)] hover:bg-[var(--outline-primary)] hover:bg-opacity-10`
                            : node.type === 'plot'
                              ? node.expanded
                                ? `bg-[var(--outline-secondary)] bg-opacity-20 text-[var(--outline-secondary)]`
                                : `text-gray-400 hover:text-[var(--outline-secondary)] hover:bg-[var(--outline-secondary)] hover:bg-opacity-10`
                              : node.expanded
                                ? `bg-[var(--outline-info)] bg-opacity-20 text-[var(--outline-info)]`
                                : `text-gray-400 hover:text-[var(--outline-info)] hover:bg-[var(--outline-info)] hover:bg-opacity-10`
                        } hover:scale-110`}
              onClick={handleExpandClick}
              aria-label={node.expanded ? "折叠" : "展开"}
              style={{
                transform: 'translateZ(0)' // 启用GPU加速
              }}
            >
              {node.expanded ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
            </button>
          ) : (
            <div className="w-6 h-6 flex items-center justify-center">
              <div
                className={`w-2 h-2 rounded-full transition-all duration-300
                  ${node.type === 'volume'
                    ? 'bg-[var(--outline-volume)]'
                    : node.type === 'chapter'
                      ? 'bg-[var(--outline-primary)]'
                      : node.type === 'plot'
                        ? 'bg-[var(--outline-secondary)]'
                        : 'bg-[var(--outline-info)]'
                  } group-hover:scale-150 group-hover:opacity-80`}
              ></div>
            </div>
          )}
        </div>

        {/* 节点类型图标 */}
        <div
          className={`flex-shrink-0 mr-2 flex items-center justify-center w-6 h-6 rounded-md transition-all duration-300
            ${node.type === 'volume'
              ? 'bg-gradient-to-br from-[var(--outline-volume-light)] to-[var(--outline-volume)] text-white'
              : node.type === 'chapter'
                ? 'bg-gradient-to-br from-[var(--outline-primary-light)] to-[var(--outline-primary)] text-white'
                : node.type === 'plot'
                  ? 'bg-gradient-to-br from-[var(--outline-secondary-light)] to-[var(--outline-secondary)] text-white'
                  : 'bg-gradient-to-br from-[var(--outline-info-light)] to-[var(--outline-info)] text-white'
            } group-hover:scale-110`}
          style={{
            boxShadow: `0 2px 4px rgba(var(--outline-${
              node.type === 'volume' ? 'volume' : node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'
            }-rgb), 0.2)`,
            transform: 'translateZ(0)' // 启用GPU加速
          }}
        >
          {nodeIcon}
        </div>

        <div className="flex-grow">
          <div className="cursor-pointer flex-1" onClick={(e) => {
            e.stopPropagation();
            handleEditClick();
          }}>
            <div className="flex items-center">
              <div
                className={`font-medium flex-1 transition-all duration-300
                  ${node.type === 'volume'
                    ? 'text-[var(--outline-volume-dark)] group-hover:text-[var(--outline-volume)]'
                    : node.type === 'chapter'
                      ? 'text-[var(--outline-primary-dark)] group-hover:text-[var(--outline-primary)]'
                      : node.type === 'plot'
                        ? 'text-[var(--outline-secondary-dark)] group-hover:text-[var(--outline-secondary)]'
                        : 'text-[var(--outline-info-dark)] group-hover:text-[var(--outline-info)]'
                  }`}
                style={{
                  textShadow: '0 0.5px 0 rgba(0,0,0,0.05)',
                  transform: 'translateZ(0)' // 启用GPU加速
                }}
              >
                {node.title}
              </div>
              <div
                className={`ml-2 px-2.5 py-0.5 text-xs font-medium rounded-full
                  transition-all duration-300 ease-out group-hover:shadow-sm
                  ${node.type === 'volume'
                    ? 'bg-gradient-to-r from-[var(--outline-volume-light)] to-[var(--outline-volume)] text-white'
                    : node.type === 'chapter'
                      ? 'bg-gradient-to-r from-[var(--outline-primary-light)] to-[var(--outline-primary)] text-white'
                      : node.type === 'plot'
                        ? 'bg-gradient-to-r from-[var(--outline-secondary-light)] to-[var(--outline-secondary)] text-white'
                        : 'bg-gradient-to-r from-[var(--outline-info-light)] to-[var(--outline-info)] text-white'
                  } group-hover:scale-105`}
                style={{
                  boxShadow: `0 1px 2px rgba(var(--outline-${
                    node.type === 'volume' ? 'volume' : node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'
                  }-rgb), 0.2)`,
                  transform: 'translateZ(0)', // 启用GPU加速
                  animation: node.expanded ? 'outlineBadgePulse 1s ease-out' : 'none'
                }}
              >
                {nodeTypeLabel}
              </div>
            </div>
            <div className="text-xs text-gray-500 flex items-center mt-1">
              {node.description && (
                <span className="mr-3 text-gray-500 truncate max-w-[200px]">{node.description}</span>
              )}
              {hasRelations && (
                <span className={`text-xs text-[${nodeColor}] flex items-center`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  关联元素
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex-shrink-0 relative opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex space-x-1">
            <button
              className={`w-7 h-7 flex items-center justify-center rounded-full hover:bg-[${nodeColor}] hover:bg-opacity-10 text-gray-500 hover:text-[${nodeColor}] transition-colors duration-200`}
              onClick={handleEditClick}
              title="编辑"
              aria-label="编辑节点"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>

            <button
              className={`w-7 h-7 flex items-center justify-center rounded-full hover:bg-[${nodeColor}] hover:bg-opacity-10 text-gray-500 hover:text-[${nodeColor}] transition-colors duration-200`}
              onClick={handleAddChildClick}
              title="添加子节点"
              aria-label="添加子节点"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>

            <button
              className={`w-7 h-7 flex items-center justify-center rounded-full hover:bg-red-100 text-gray-500 hover:text-red-600 transition-colors duration-200`}
              onClick={handleDeleteClick}
              title="删除"
              aria-label="删除节点"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>

            <button
              ref={menuButtonRef}
              className={`w-7 h-7 flex items-center justify-center rounded-full hover:bg-[${nodeColor}] hover:bg-opacity-10 text-gray-500 hover:text-[${nodeColor}] transition-colors duration-200`}
              onClick={toggleMenu}
              title="更多选项"
              aria-label="更多选项"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
          </div>

          {isMenuOpen && (
            <Portal containerId="outline-menu-portal">
              <div
                className="fixed w-48 bg-white rounded-lg shadow-xl z-50 border border-gray-200 overflow-hidden"
                style={{
                  top: `${menuPosition.top}px`,
                  left: `${menuPosition.left}px`
                }}
              >
                <div className="py-1">
                  <button
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[var(--color-primary-bg)] flex items-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsMenuOpen(false);
                      setShowRelationSelector(true);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-[var(--color-primary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101" />
                    </svg>
                    添加关联
                  </button>

                  {hasRelations && (
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[var(--color-primary-bg)] flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsMenuOpen(false);
                        setShowRelations(!showRelations);
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-[var(--color-primary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      {showRelations ? '隐藏关联' : '查看关联'}
                    </button>
                  )}

                  <button
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[var(--color-primary-bg)] flex items-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsMenuOpen(false);
                      // 切换节点类型的逻辑
                      const newType = node.type === 'volume' ? 'event' :
                                     node.type === 'event' ? 'chapter' :
                                     node.type === 'chapter' ? 'plot' :
                                     node.type === 'plot' ? 'dialogue' : 'volume';
                      onNodeChange(node.id, { type: newType });
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-[var(--color-primary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                    切换类型
                  </button>
                </div>
              </div>
            </Portal>
          )}
        </div>
      </div>

      {/* 关联面板 */}
      {showRelations && (
        <div className="mt-2 ml-8 border border-gray-200 rounded-md bg-white shadow-md overflow-hidden animate-fadeIn">
          <div className="p-2 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h4 className="text-sm font-medium text-gray-700">关联元素</h4>
            <button
              className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
              onClick={() => setShowRelations(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="p-3">
            <NodeRelationsPanel node={node} />
          </div>
        </div>
      )}

      {/* 关联选择器 - 使用Portal渲染 */}
      {showRelationSelector && bookId && (
        <Portal containerId="outline-relation-portal">
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden animate-fadeIn">
              <div className="p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                <h4 className="text-lg font-medium text-gray-700">添加关联</h4>
                <button
                  className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
                  onClick={() => setShowRelationSelector(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="p-4 overflow-auto max-h-[calc(80vh-4rem)]">
                <RelationSelector
                  node={node}
                  bookId={bookId}
                  onClose={() => setShowRelationSelector(false)}
                />
              </div>
            </div>
          </div>
        </Portal>
      )}

      {/* 子节点通过OutlineTree组件的递归渲染处理 */}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        title="确认删除"
        message={`确定要删除"${node.title}"${hasChildren ? '及其所有子节点' : ''}吗？此操作不可撤销。`}
        confirmText="删除"
        cancelText="取消"
        confirmButtonClass="bg-red-500 hover:bg-red-600"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
      />

      {/* 节点编辑对话框 */}
      <NodeEditDialog
        node={node}
        isOpen={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        onSave={handleNodeUpdate}
        allNodes={allNodes}
      />
    </li>
  );
};
