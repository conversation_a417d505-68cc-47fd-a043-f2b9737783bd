import { Book, Chapter } from '@/db/database';

/**
 * 书籍导出格式
 */
export type ExportFormat = 'md' | 'txt';

/**
 * 书籍导入导出工具
 */
export class BookImportExport {
  /**
   * 导出书籍为文件
   * @param book 书籍对象
   * @param chapters 章节列表
   * @param format 导出格式
   */
  static exportBook(book: Book, chapters: Chapter[], format: ExportFormat): void {
    let content = '';
    const filename = `${book.title}.${format}`;

    if (format === 'md') {
      content = this.generateMarkdownContent(book, chapters);
    } else {
      content = this.generateTextContent(book, chapters);
    }

    // 创建并下载文件
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 生成Markdown格式内容
   * @param book 书籍对象
   * @param chapters 章节列表
   * @returns Markdown格式内容
   */
  private static generateMarkdownContent(book: Book, chapters: Chapter[]): string {
    let content = `# ${book.title}\n\n`;

    if (book.description) {
      content += `${book.description}\n\n`;
    }

    chapters.forEach(chapter => {
      content += `## ${chapter.title}\n\n${chapter.content}\n\n`;
    });

    return content;
  }

  /**
   * 生成纯文本格式内容
   * @param book 书籍对象
   * @param chapters 章节列表
   * @returns 纯文本格式内容
   */
  private static generateTextContent(book: Book, chapters: Chapter[]): string {
    let content = `${book.title}\n\n`;

    if (book.description) {
      content += `${book.description}\n\n`;
    }

    chapters.forEach(chapter => {
      content += `${chapter.title}\n\n${chapter.content}\n\n`;
    });

    return content;
  }

  /**
   * 解析导入的文件内容
   * @param content 文件内容
   * @param filename 文件名
   * @returns 解析后的书籍信息和章节列表
   */
  static parseImportedContent(content: string, filename: string): { title: string, description: string, chapters: { title: string, content: string }[] } {
    // 默认书籍标题（从文件名中提取）
    const title = filename.replace(/\.(md|txt)$/, '');
    let description = '';
    const chapters: { title: string, content: string }[] = [];

    // 检测文件类型并解析
    let result;
    if (filename.endsWith('.md')) {
      result = this.parseMarkdownContent(content, title);
    } else {
      result = this.parseTextContent(content, title);
    }

    // 对章节进行排序
    result.chapters = this.sortChapters(result.chapters);

    return result;
  }

  /**
   * 对章节进行排序
   * @param chapters 章节列表
   * @returns 排序后的章节列表
   */
  private static sortChapters(chapters: { title: string, content: string }[]): { title: string, content: string }[] {
    // 定义包含order属性的章节类型
    type ChapterWithOrder = { title: string, content: string, order: number };

    // 提取章节编号
    const chaptersWithOrder: ChapterWithOrder[] = chapters.map(chapter => {
      const title = chapter.title;
      let order = 0;

      // 尝试提取章节编号
      const chapterNumMatch = title.match(/第([0-9一二三四五六七八九十百千]+)章/);
      if (chapterNumMatch) {
        // 处理中文数字
        const numStr = chapterNumMatch[1];
        if (/^[0-9]+$/.test(numStr)) {
          order = parseInt(numStr, 10);
        } else {
          // 简单的中文数字转换（仅支持简单数字，复杂的需要更完整的转换函数）
          const chineseNums: Record<string, number> = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '百': 100, '千': 1000
          };
          if (numStr.length === 1) {
            order = chineseNums[numStr] || 0;
          } else if (numStr === '十') {
            order = 10;
          } else if (numStr.startsWith('十')) {
            order = 10 + (chineseNums[numStr.charAt(1)] || 0);
          } else if (numStr.endsWith('十')) {
            order = (chineseNums[numStr.charAt(0)] || 0) * 10;
          }
        }
      } else {
        // 尝试从纯数字或数字开头的标题中提取
        const numMatch = title.match(/^([0-9]+)/);
        if (numMatch) {
          order = parseInt(numMatch[1], 10);
        }
      }

      return {
        ...chapter,
        order
      };
    });

    // 按编号排序
    chaptersWithOrder.sort((a, b) => a.order - b.order);

    // 返回排序后的章节（不包含order属性）
    return chaptersWithOrder.map(({ title, content }) => ({ title, content }));
  }

  /**
   * 解析Markdown格式内容
   * @param content Markdown内容
   * @param defaultTitle 默认标题
   * @returns 解析后的书籍信息和章节列表
   */
  private static parseMarkdownContent(content: string, defaultTitle: string): { title: string, description: string, chapters: { title: string, content: string }[] } {
    const lines = content.split('\n');
    let title = defaultTitle;
    let description = '';
    const chapters: { title: string, content: string }[] = [];

    let currentChapter: { title: string, content: string } | null = null;

    // 查找标题（# 开头的行）
    const titleMatch = content.match(/^# (.+)$/m);
    if (titleMatch) {
      title = titleMatch[1].trim();
    }

    // 逐行解析内容
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检测章节标题（## 开头的行）
      if (line.startsWith('## ')) {
        // 如果已有当前章节，保存它
        if (currentChapter) {
          chapters.push(currentChapter);
        }

        // 创建新章节
        currentChapter = {
          title: line.substring(3).trim(),
          content: ''
        };
      }
      // 检测章节标题（各种格式）
      else if (
        // 第X章 格式（支持阿拉伯数字和中文数字）
        (line.match(/^第[0-9一二三四五六七八九十百千]+章/) ||
         // 第X章 标题 格式
         line.match(/^第[0-9一二三四五六七八九十百千]+章\s+.+/) ||
         // 数字编号如001、1、1.
         line.match(/^[0-9]{1,3}([\.、]|\s)/) ||
         // 纯数字编号如001、01、1
         line.match(/^[0-9]{1,3}$/) ||
         // Chapter X 格式（英文）
         line.match(/^Chapter\s+[0-9]+/i) ||
         // 章节 X 格式
         line.match(/^章节\s*[0-9]+/) ||
         // 卷X 章X 格式
         line.match(/^卷[0-9一二三四五六七八九十]+\s*章[0-9一二三四五六七八九十]+/)) &&
        // 确保是段落开始（前一行为空或者是文件开头）
        (i === 0 || lines[i-1].trim() === '')
      ) {
        // 如果已有当前章节，保存它
        if (currentChapter) {
          chapters.push(currentChapter);
        }

        // 创建新章节
        currentChapter = {
          title: line.trim(),
          content: ''
        };
      }
      // 如果有当前章节，添加内容
      else if (currentChapter) {
        currentChapter.content += line + '\n';
      }
      // 如果还没有章节，且不是标题行，则视为描述
      else if (!line.startsWith('# ') && line.trim() !== '') {
        description += line + '\n';
      }
    }

    // 保存最后一个章节
    if (currentChapter) {
      chapters.push(currentChapter);
    }

    // 清理章节内容（移除多余的空行）
    chapters.forEach(chapter => {
      chapter.content = chapter.content.trim();
    });

    return {
      title,
      description: description.trim(),
      chapters
    };
  }

  /**
   * 解析纯文本格式内容
   * @param content 纯文本内容
   * @param defaultTitle 默认标题
   * @returns 解析后的书籍信息和章节列表
   */
  private static parseTextContent(content: string, defaultTitle: string): { title: string, description: string, chapters: { title: string, content: string }[] } {
    const lines = content.split('\n');
    let title = defaultTitle;
    let description = '';
    const chapters: { title: string, content: string }[] = [];

    let currentChapter: { title: string, content: string } | null = null;

    // 如果第一行不为空，将其视为标题
    if (lines.length > 0 && lines[0].trim() !== '') {
      title = lines[0].trim();
      lines.shift();
    }

    // 逐行解析内容
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检测章节标题（各种格式）
      if (
        // 第X章 格式（支持阿拉伯数字和中文数字）
        (line.match(/^第[0-9一二三四五六七八九十百千]+章/) ||
         // 第X章 标题 格式
         line.match(/^第[0-9一二三四五六七八九十百千]+章\s+.+/) ||
         // 数字编号如001、1、1.
         line.match(/^[0-9]{1,3}([\.、]|\s)/) ||
         // 纯数字编号如001、01、1
         line.match(/^[0-9]{1,3}$/) ||
         // Chapter X 格式（英文）
         line.match(/^Chapter\s+[0-9]+/i) ||
         // 章节 X 格式
         line.match(/^章节\s*[0-9]+/) ||
         // 卷X 章X 格式
         line.match(/^卷[0-9一二三四五六七八九十]+\s*章[0-9一二三四五六七八九十]+/)) &&
        // 确保是段落开始（前一行为空或者是文件开头）
        (i === 0 || lines[i-1].trim() === '')
      ) {
        // 如果已有当前章节，保存它
        if (currentChapter) {
          chapters.push(currentChapter);
        }

        // 创建新章节
        currentChapter = {
          title: line.trim(),
          content: ''
        };
      }
      // 如果有当前章节，添加内容
      else if (currentChapter) {
        currentChapter.content += line + '\n';
      }
      // 如果还没有章节，则视为描述
      else {
        description += line + '\n';
      }
    }

    // 保存最后一个章节
    if (currentChapter) {
      chapters.push(currentChapter);
    }

    // 清理章节内容（移除多余的空行）
    chapters.forEach(chapter => {
      chapter.content = chapter.content.trim();
    });

    return {
      title,
      description: description.trim(),
      chapters
    };
  }
}
