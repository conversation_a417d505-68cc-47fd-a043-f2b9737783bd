"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface ACEElementExtractorButtonProps {
  onExtract?: (text: string) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
}

/**
 * ACE元素拆解按钮组件
 * 用于触发文本元素拆解功能，采用与ACEFrameworkButton相同的设计风格
 */
export const ACEElementExtractorButton: React.FC<ACEElementExtractorButtonProps> = ({
  onExtract,
  variant = 'default',
  className = '',
  disabled = false,
  isLoading = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // 获取按钮样式
  const getButtonStyles = () => {
    const baseStyles = "relative inline-flex items-center justify-center border border-purple-300 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors dark:bg-purple-900/20 dark:border-purple-600 dark:hover:bg-purple-800/30 dark:text-purple-200 text-purple-700";

    switch (variant) {
      case 'compact':
        return `${baseStyles} px-3 py-2 text-sm rounded-lg font-medium`;
      case 'icon-only':
        return `${baseStyles} p-2 rounded-lg`;
      default:
        return `${baseStyles} px-4 py-2 text-sm rounded-lg font-medium`;
    }
  };

  // 处理点击事件
  const handleClick = () => {
    if (!disabled && !isLoading && onExtract) {
      onExtract('');
    }
  };

  return (
    <motion.button
      className={`${getButtonStyles()} ${className} ${disabled || isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={handleClick}
      disabled={disabled || isLoading}
      title="ACE元素拆解"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: disabled || isLoading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || isLoading ? 1 : 0.98 }}
      transition={{ duration: 0.2 }}
    >
      {/* 拆解图标 */}
      <motion.div
        animate={{ rotate: isLoading ? 360 : 0 }}
        transition={{ duration: 1, repeat: isLoading ? Infinity : 0, ease: "linear" }}
      >
        {isLoading ? (
          // 加载状态图标
          <svg
            className={`${variant === 'icon-only' ? 'w-5 h-5' : 'w-4 h-4'} ${variant !== 'icon-only' ? 'mr-2' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        ) : (
          // 拆解分析图标
          <svg
            className={`${variant === 'icon-only' ? 'w-5 h-5' : 'w-4 h-4'} ${variant !== 'icon-only' ? 'mr-2' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
            />
          </svg>
        )}
      </motion.div>

      {/* 文本（非图标模式） */}
      {variant !== 'icon-only' && (
        <span>
          {isLoading 
            ? '拆解中...'
            : (variant === 'compact' ? 'ACE拆解' : 'ACE元素拆解')
          }
        </span>
      )}

      {/* 悬浮效果指示器 */}
      {variant === 'icon-only' && isHovered && !disabled && !isLoading && (
        <motion.div
          className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10"
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 5 }}
          transition={{ duration: 0.2 }}
        >
          ACE元素拆解
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
        </motion.div>
      )}

      {/* 加载状态覆盖层 */}
      {isLoading && (
        <motion.div
          className="absolute inset-0 bg-white bg-opacity-50 dark:bg-gray-800 dark:bg-opacity-50 rounded-lg flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </motion.div>
      )}
    </motion.button>
  );
};

/**
 * ACE元素拆解按钮的紧凑版本
 * 专门用于工具栏或空间受限的场景
 */
export const CompactACEElementExtractorButton: React.FC<Omit<ACEElementExtractorButtonProps, 'variant'>> = (props) => {
  return <ACEElementExtractorButton {...props} variant="compact" />;
};

/**
 * ACE元素拆解按钮的图标版本
 * 专门用于图标工具栏或最小化界面
 */
export const IconACEElementExtractorButton: React.FC<Omit<ACEElementExtractorButtonProps, 'variant'>> = (props) => {
  return <ACEElementExtractorButton {...props} variant="icon-only" />;
};

export default ACEElementExtractorButton;
