"use client";

import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { 
  outlineAIService, 
  OutlineAIResponseExtended, 
  ThinkingCanvasOptions 
} from '@/factories/ui/components/OutlineManager/assistant/OutlineAIService';
import { thinkingCanvasProcessor } from './ThinkingCanvasProcessor';
import { ContextChain } from '@/factories/ui/components/OutlineManager/assistant/ContextChainService';

/**
 * 工作流程步骤类型
 */
export type WorkflowStep = 'thinking' | 'generating' | 'completed';

/**
 * 工作流程状态
 */
export interface WorkflowState {
  currentStep: WorkflowStep;
  thinkingCanvas: ThinkingCanvasData | null;
  generatedChanges: any[] | null;
  error: string | null;
  isProcessing: boolean;
  isStreaming?: boolean;
  streamingContent?: string;
}

/**
 * 思考画布工作流程管理器
 * 管理"先思考再写大纲"的完整工作流程
 */
export class ThinkingCanvasWorkflow {
  private static instance: ThinkingCanvasWorkflow;
  private workflowState: WorkflowState;
  private listeners: ((state: WorkflowState) => void)[] = [];

  private constructor() {
    this.workflowState = {
      currentStep: 'thinking',
      thinkingCanvas: null,
      generatedChanges: null,
      error: null,
      isProcessing: false
    };
  }

  public static getInstance(): ThinkingCanvasWorkflow {
    if (!ThinkingCanvasWorkflow.instance) {
      ThinkingCanvasWorkflow.instance = new ThinkingCanvasWorkflow();
    }
    return ThinkingCanvasWorkflow.instance;
  }

  /**
   * 添加状态监听器
   */
  public addStateListener(listener: (state: WorkflowState) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除状态监听器
   */
  public removeStateListener(listener: (state: WorkflowState) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 更新工作流程状态
   */
  private updateState(updates: Partial<WorkflowState>): void {
    this.workflowState = { ...this.workflowState, ...updates };
    this.listeners.forEach(listener => listener(this.workflowState));
  }

  /**
   * 获取当前工作流程状态
   */
  public getState(): WorkflowState {
    return { ...this.workflowState };
  }

  /**
   * 重置工作流程
   */
  public reset(): void {
    this.updateState({
      currentStep: 'thinking',
      thinkingCanvas: null,
      generatedChanges: null,
      error: null,
      isProcessing: false
    });
  }

  /**
   * 步骤1：生成思考画布（支持流式输出）
   */
  public async generateThinkingCanvas(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    options?: ThinkingCanvasOptions & {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: ContextChain[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
      onProgress?: (chunk: string, fullContent: string) => void;
    }
  ): Promise<ThinkingCanvasData | null> {
    try {
      console.log('🎯 开始流式生成思考画布工作流程');

      this.updateState({
        currentStep: 'thinking',
        isProcessing: true,
        isStreaming: true,
        streamingContent: '',
        error: null
      });

      // 调用AI服务生成思考画布（流式）
      const response = await outlineAIService.generateThinkingCanvasStreaming(
        userMessage,
        mentionedNodes,
        outline,
        {
          mode: options?.mode || 'detailed',
          temperature: options?.temperature,
          maxTokens: options?.maxTokens,
          bookId: options?.bookId,
          contextChains: options?.contextChains,
          selectedFramework: options?.selectedFramework,
          selectedFrameworks: options?.selectedFrameworks,
          onProgress: (chunk: string, fullContent: string) => {
            // 更新流式内容状态
            this.updateState({
              streamingContent: fullContent
            });

            // 调用外部进度回调
            if (options?.onProgress) {
              options.onProgress(chunk, fullContent);
            }
          }
        }
      );

      if (!response.success) {
        throw new Error(response.error || '思考画布生成失败');
      }

      // 提取思考画布数据
      const thinkingCanvas = thinkingCanvasProcessor.extractThinkingCanvas(response);

      if (!thinkingCanvas) {
        throw new Error('无法从AI响应中提取思考画布数据');
      }

      // 保存思考画布
      await thinkingCanvasProcessor.saveThinkingCanvas(thinkingCanvas);

      // 更新状态
      this.updateState({
        currentStep: 'generating',
        thinkingCanvas,
        generatedChanges: response.changes || [],
        isProcessing: false,
        isStreaming: false,
        streamingContent: ''
      });

      console.log('✅ 流式思考画布生成完成:', thinkingCanvas.id);
      return thinkingCanvas;

    } catch (error: any) {
      console.error('❌ 思考画布生成失败:', error);

      this.updateState({
        error: error.message || '思考画布生成失败',
        isProcessing: false,
        isStreaming: false,
        streamingContent: ''
      });

      return null;
    }
  }

  /**
   * 步骤2：基于思考画布生成大纲节点
   */
  public async generateOutlineFromThinking(
    editedCanvas?: ThinkingCanvasData,
    additionalRequirements?: string
  ): Promise<any[] | null> {
    try {
      console.log('🎯 基于思考画布生成大纲节点');

      const canvas = editedCanvas || this.workflowState.thinkingCanvas;
      if (!canvas) {
        throw new Error('没有可用的思考画布');
      }

      this.updateState({
        currentStep: 'generating',
        isProcessing: true,
        error: null
      });

      // 如果用户编辑了思考画布，保存更新
      if (editedCanvas && editedCanvas.id === this.workflowState.thinkingCanvas?.id) {
        await thinkingCanvasProcessor.saveThinkingCanvas(editedCanvas);
        this.updateState({ thinkingCanvas: editedCanvas });
      }

      // 构建基于思考画布的生成提示词
      const prompt = this.buildOutlineGenerationPrompt(canvas, additionalRequirements);

      // 调用AI服务生成大纲节点
      const response = await outlineAIService.sendRequest(
        prompt,
        [], // mentionedNodes
        null, // outline
        {
          temperature: 0.6,
          maxTokens: 2000
        }
      );

      if (!response.success) {
        throw new Error(response.error || '大纲节点生成失败');
      }

      const changes = response.changes || [];

      // 更新状态
      this.updateState({
        currentStep: 'completed',
        generatedChanges: changes,
        isProcessing: false
      });

      console.log('✅ 大纲节点生成完成，共', changes.length, '个操作');
      return changes;

    } catch (error: any) {
      console.error('❌ 大纲节点生成失败:', error);
      
      this.updateState({
        error: error.message || '大纲节点生成失败',
        isProcessing: false
      });

      return null;
    }
  }

  /**
   * 步骤3：应用生成的大纲节点
   */
  public async applyOutlineChanges(changes: any[]): Promise<boolean> {
    try {
      console.log('🎯 应用大纲节点变更');

      this.updateState({
        isProcessing: true,
        error: null
      });

      // 这里应该调用大纲管理器的应用变更方法
      // 由于我们还没有实现UI部分，这里先记录日志
      console.log('📝 应用大纲变更:', changes);

      // 记录思考画布的使用效果
      if (this.workflowState.thinkingCanvas) {
        await this.recordUsageStats(this.workflowState.thinkingCanvas, changes.length);
      }

      this.updateState({
        isProcessing: false
      });

      console.log('✅ 大纲节点应用完成');
      return true;

    } catch (error: any) {
      console.error('❌ 大纲节点应用失败:', error);
      
      this.updateState({
        error: error.message || '大纲节点应用失败',
        isProcessing: false
      });

      return false;
    }
  }

  /**
   * 构建基于思考画布的大纲生成提示词
   */
  private buildOutlineGenerationPrompt(canvas: ThinkingCanvasData, additionalRequirements?: string): string {
    const canvasContent = `
**对话设计**：
- 角色列表: ${canvas.dialogueDesign.characters.join(', ')}
- 对话推进逻辑: ${canvas.dialogueDesign.dialogueFlow}
- 情感基调: ${canvas.dialogueDesign.emotionalTone}
- 对话节奏: ${canvas.dialogueDesign.pacing}
- 对话技巧: ${canvas.dialogueDesign.techniques.join(', ')}

**剧情节奏**：
- 紧张点设计: ${canvas.plotRhythm.tensionPoints.join(', ')}
- 高潮时刻: ${canvas.plotRhythm.climaxMoments.join(', ')}
- 节奏策略: ${canvas.plotRhythm.pacingStrategy}
- 读者爽感设计: ${canvas.plotRhythm.readerEngagement}

**AI建议**：
${canvas.aiSuggestions.map(suggestion => `- ${suggestion}`).join('\n')}

**用户备注**：
${canvas.userNotes}`;

    return `
**基于思考画布生成大纲节点**

我已经完成了深度思考，现在需要基于思考内容生成具体的大纲节点操作。

**思考画布内容**：
${canvasContent}

**任务要求**：
1. 基于上述思考内容，生成具体的大纲节点操作
2. 确保生成的节点与思考内容保持一致
3. 注重实用性和可操作性

${additionalRequirements ? `**额外要求**：${additionalRequirements}` : ''}

请严格按照JSON格式返回：
{
  "message": "基于思考画布生成的大纲建议",
  "changes": [
    // 具体的大纲节点操作
  ],
  "metadata": {
    "operationType": "generate_from_thinking",
    "basedOnThinkingCanvas": "${canvas.id}",
    "confidence": 0.9
  }
}`;
  }

  /**
   * 记录思考画布使用统计
   */
  private async recordUsageStats(canvas: ThinkingCanvasData, generatedNodesCount: number): Promise<void> {
    try {
      // 更新思考画布的使用统计
      const updatedCanvas: ThinkingCanvasData = {
        ...canvas,
        lastModified: new Date().toISOString(),
        // 在用户备注中记录使用统计
        userNotes: canvas.userNotes + `\n\n[使用记录] ${new Date().toLocaleString()}: 生成了 ${generatedNodesCount} 个大纲节点`
      };

      await thinkingCanvasProcessor.saveThinkingCanvas(updatedCanvas);
      console.log('📊 思考画布使用统计已更新');

    } catch (error: any) {
      console.error('记录使用统计失败:', error);
    }
  }

  /**
   * 跳过思考直接生成（降级到标准模式）
   */
  public async generateDirectly(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    options?: any
  ): Promise<any[] | null> {
    try {
      console.log('🎯 跳过思考，直接生成大纲节点');

      this.updateState({
        currentStep: 'generating',
        isProcessing: true,
        error: null
      });

      // 调用标准的AI服务
      const response = await outlineAIService.sendRequest(
        userMessage,
        mentionedNodes,
        outline,
        options
      );

      if (!response.success) {
        throw new Error(response.error || '大纲节点生成失败');
      }

      const changes = response.changes || [];

      this.updateState({
        currentStep: 'completed',
        generatedChanges: changes,
        isProcessing: false
      });

      console.log('✅ 直接生成完成，共', changes.length, '个操作');
      return changes;

    } catch (error: any) {
      console.error('❌ 直接生成失败:', error);
      
      this.updateState({
        error: error.message || '大纲节点生成失败',
        isProcessing: false
      });

      return null;
    }
  }

  /**
   * 获取工作流程进度百分比
   */
  public getProgress(): number {
    switch (this.workflowState.currentStep) {
      case 'thinking':
        return this.workflowState.isProcessing ? 30 : 0;
      case 'generating':
        return this.workflowState.isProcessing ? 70 : 50;
      case 'completed':
        return 100;
      default:
        return 0;
    }
  }

  /**
   * 检查是否可以进入下一步
   */
  public canProceedToNext(): boolean {
    switch (this.workflowState.currentStep) {
      case 'thinking':
        return !!this.workflowState.thinkingCanvas && !this.workflowState.isProcessing;
      case 'generating':
        return !!this.workflowState.generatedChanges && !this.workflowState.isProcessing;
      case 'completed':
        return false;
      default:
        return false;
    }
  }
}

// 导出单例实例
export const thinkingCanvasWorkflow = ThinkingCanvasWorkflow.getInstance();
