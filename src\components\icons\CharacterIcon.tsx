"use client";

import React from 'react';
import IconBase, { IconBaseProps } from './IconBase';

/**
 * 人物图标 - 人物剪影轮廓
 * 支持悬停点头动画
 */
const CharacterIcon: React.FC<Omit<IconBaseProps, 'children'>> = (props) => {
  return (
    <IconBase {...props} className={`character-icon ${props.className || ''}`}>
      {/* 人物头部轮廓 */}
      <circle cx="12" cy="8" r="4" />
      {/* 人物身体轮廓 */}
      <path d="M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" />
      {/* 思考状态装饰 - 小气泡 */}
      <circle cx="16" cy="6" r="1" opacity="0.5" />
      <circle cx="17.5" cy="4.5" r="0.5" opacity="0.3" />
    </IconBase>
  );
};

export default CharacterIcon;
