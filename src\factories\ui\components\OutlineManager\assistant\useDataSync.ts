import { useEffect, useCallback, useRef } from 'react';
import { db } from '@/lib/db/dexie';

/**
 * 数据同步Hook
 * 监听数据库变化，自动刷新@功能的数据
 */
export const useDataSync = (bookId: string, onDataChange?: () => void) => {
  const lastUpdateRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // 防抖的数据变化处理
  const handleDataChange = useCallback(() => {
    const now = Date.now();
    
    // 防抖：500ms内只触发一次
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      if (now - lastUpdateRef.current > 500) {
        lastUpdateRef.current = now;
        console.log('📡 检测到数据变化，刷新@功能数据');
        onDataChange?.();
      }
    }, 500);
  }, [onDataChange]);

  // 监听数据库变化
  useEffect(() => {
    if (!bookId) return;

    console.log('🔄 开始监听数据变化, bookId:', bookId);

    // 创建数据库变化监听器
    const observers: (() => void)[] = [];

    // 监听人物数据变化
    const characterObserver = db.characters
      .where('bookId')
      .equals(bookId)
      .toArray()
      .then(() => {
        // 初始化后开始监听变化
        const subscription = db.characters.hook('creating', () => handleDataChange());
        const subscription2 = db.characters.hook('updating', () => handleDataChange());
        const subscription3 = db.characters.hook('deleting', () => handleDataChange());
        
        observers.push(() => {
          subscription.unsubscribe();
          subscription2.unsubscribe();
          subscription3.unsubscribe();
        });
      });

    // 监听术语数据变化
    const terminologyObserver = db.terminology
      .where('bookId')
      .equals(bookId)
      .toArray()
      .then(() => {
        const subscription = db.terminology.hook('creating', () => handleDataChange());
        const subscription2 = db.terminology.hook('updating', () => handleDataChange());
        const subscription3 = db.terminology.hook('deleting', () => handleDataChange());
        
        observers.push(() => {
          subscription.unsubscribe();
          subscription2.unsubscribe();
          subscription3.unsubscribe();
        });
      });

    // 监听世界观数据变化
    const worldBuildingObserver = db.worldBuilding
      .where('bookId')
      .equals(bookId)
      .toArray()
      .then(() => {
        const subscription = db.worldBuilding.hook('creating', () => handleDataChange());
        const subscription2 = db.worldBuilding.hook('updating', () => handleDataChange());
        const subscription3 = db.worldBuilding.hook('deleting', () => handleDataChange());
        
        observers.push(() => {
          subscription.unsubscribe();
          subscription2.unsubscribe();
          subscription3.unsubscribe();
        });
      });

    // 监听章节数据变化
    const chapterObserver = db.chapters
      .where('bookId')
      .equals(bookId)
      .toArray()
      .then(() => {
        const subscription = db.chapters.hook('creating', () => handleDataChange());
        const subscription2 = db.chapters.hook('updating', () => handleDataChange());
        const subscription3 = db.chapters.hook('deleting', () => handleDataChange());
        
        observers.push(() => {
          subscription.unsubscribe();
          subscription2.unsubscribe();
          subscription3.unsubscribe();
        });
      });

    // 清理函数
    return () => {
      console.log('🔄 停止监听数据变化');
      
      // 清理定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // 取消所有订阅
      observers.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.warn('取消数据监听订阅失败:', error);
        }
      });
    };
  }, [bookId, handleDataChange]);

  // 手动刷新数据
  const refreshData = useCallback(() => {
    console.log('🔄 手动刷新@功能数据');
    handleDataChange();
  }, [handleDataChange]);

  return {
    refreshData
  };
};

/**
 * 数据验证Hook
 * 验证bookId的有效性和数据完整性
 */
export const useDataValidation = (bookId: string) => {
  const validateBookId = useCallback(async () => {
    if (!bookId) {
      console.warn('⚠️ bookId为空，无法验证数据');
      return false;
    }

    try {
      // 检查书籍是否存在
      const book = await db.books.get(bookId);
      if (!book) {
        console.warn('⚠️ 书籍不存在:', bookId);
        return false;
      }

      console.log('✅ bookId验证通过:', bookId, book.title);
      return true;
    } catch (error) {
      console.error('❌ bookId验证失败:', error);
      return false;
    }
  }, [bookId]);

  const checkDataIntegrity = useCallback(async () => {
    if (!bookId) return null;

    try {
      const [chapters, characters, terminology, worldBuilding] = await Promise.all([
        db.chapters.where('bookId').equals(bookId).count(),
        db.characters.where('bookId').equals(bookId).count(),
        db.terminology.where('bookId').equals(bookId).count(),
        db.worldBuilding.where('bookId').equals(bookId).count()
      ]);

      const integrity = {
        chapters,
        characters,
        terminology,
        worldBuilding,
        total: chapters + characters + terminology + worldBuilding,
        hasData: chapters > 0 || characters > 0 || terminology > 0 || worldBuilding > 0
      };

      console.log('📊 数据完整性检查结果:', integrity);
      return integrity;
    } catch (error) {
      console.error('❌ 数据完整性检查失败:', error);
      return null;
    }
  }, [bookId]);

  return {
    validateBookId,
    checkDataIntegrity
  };
};

/**
 * 搜索性能优化Hook
 * 提供搜索缓存和性能优化
 */
export const useSearchOptimization = () => {
  const searchCacheRef = useRef<Map<string, any>>(new Map());
  const lastSearchRef = useRef<string>('');

  const getCachedSearch = useCallback((query: string, bookId: string) => {
    const cacheKey = `${bookId}:${query}`;
    return searchCacheRef.current.get(cacheKey);
  }, []);

  const setCachedSearch = useCallback((query: string, bookId: string, results: any) => {
    const cacheKey = `${bookId}:${query}`;
    searchCacheRef.current.set(cacheKey, {
      results,
      timestamp: Date.now()
    });

    // 限制缓存大小
    if (searchCacheRef.current.size > 50) {
      const firstKey = searchCacheRef.current.keys().next().value;
      searchCacheRef.current.delete(firstKey);
    }
  }, []);

  const clearSearchCache = useCallback(() => {
    searchCacheRef.current.clear();
    console.log('🗑️ 清空搜索缓存');
  }, []);

  const isRepeatedSearch = useCallback((query: string) => {
    const isRepeated = lastSearchRef.current === query;
    lastSearchRef.current = query;
    return isRepeated;
  }, []);

  return {
    getCachedSearch,
    setCachedSearch,
    clearSearchCache,
    isRepeatedSearch
  };
};
