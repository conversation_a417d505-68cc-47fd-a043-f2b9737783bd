import React, { useState, useRef, useEffect } from 'react';
import { WorldBookImportService } from '../../../services/worldbook/WorldBookImportService';
import { AIGeneratedPrefixStorageService } from '../../../services/ai/AIGeneratedPrefixStorageService';
import { ImportResult, ImportOptions, WorldBookEntry } from '../../../types/worldbook';

interface WorldBookImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: (result: ImportResult) => void;
}

interface PreviewData {
  fileName: string;
  totalEntries: number;
  validEntries: number;
  emptyEntries: number;
  disabledEntries: number;
  sampleEntries: WorldBookEntry[];
}

export const WorldBookImportDialog: React.FC<WorldBookImportDialogProps> = ({
  isOpen,
  onClose,
  onImportComplete
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [importStatus, setImportStatus] = useState('');
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    skipEmptyContent: true,
    skipDisabled: false,
    preserveOrder: true,
    minContentLength: 10
  });
  const [dragActive, setDragActive] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const importService = WorldBookImportService.getInstance();
  const storageService = AIGeneratedPrefixStorageService.getInstance();

  // 重置状态
  useEffect(() => {
    if (!isOpen) {
      setSelectedFile(null);
      setPreviewData(null);
      setImportStatus('');
      setImportProgress(0);
      setIsImporting(false);
      setIsPreviewLoading(false);
    }
  }, [isOpen]);

  // 处理文件选择
  const handleFileSelect = async (file: File) => {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.json')) {
      setImportStatus('错误: 仅支持 .json 格式的世界书文件');
      return;
    }

    setSelectedFile(file);
    setImportStatus('');
    setIsPreviewLoading(true);

    try {
      // 预览文件内容
      const preview = await importService.previewWorldBook(file);
      setPreviewData(preview);
      setImportStatus(`已选择文件: ${file.name}`);
    } catch (error) {
      setImportStatus(`预览失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setPreviewData(null);
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // 处理文件输入变化
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // 处理拖拽
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  // 处理导入
  const handleImport = async () => {
    if (!selectedFile) {
      setImportStatus('请先选择文件');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportStatus('正在导入世界书...');

    try {
      // 步骤1: 解析和转换
      setImportProgress(20);
      setImportStatus('正在解析世界书文件...');

      const { result, prefixes } = await importService.importWorldBook(selectedFile, importOptions);

      setImportProgress(60);
      setImportStatus('正在保存到存储...');

      // 步骤2: 保存到存储服务
      if (prefixes.length > 0) {
        const saveResult = await storageService.saveWorldBookPrefixes(prefixes);

        setImportProgress(100);
        setImportStatus(`导入完成! 成功保存了 ${saveResult.imported} 个前置消息模板`);

        // 合并结果信息
        const finalResult: ImportResult = {
          ...result,
          imported: saveResult.imported,
          skipped: saveResult.skipped,
          errors: [...result.errors, ...saveResult.errors]
        };

        setTimeout(() => {
          onImportComplete(finalResult);
          onClose();
        }, 1500);
      } else {
        setImportStatus(`导入完成，但没有有效条目。跳过了 ${result.skipped} 个条目。`);

        setTimeout(() => {
          onImportComplete(result);
        }, 2000);
      }

    } catch (error) {
      setImportStatus(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsImporting(false);
    }
  };

  // 处理选择文件按钮点击
  const handleSelectFileClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10001] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-4/5 flex flex-col">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-800">📚 导入世界书</h2>
            <p className="text-sm text-gray-600 mt-1">
              将JSON格式的世界书文件导入为前置消息模板
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            disabled={isImporting}
          >
            ×
          </button>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* 文件选择区域 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">选择世界书文件</h3>
            
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center">
                <svg className="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p className="text-lg text-gray-600 mb-2">
                  拖拽世界书文件到此处，或
                  <button
                    onClick={handleSelectFileClick}
                    className="text-blue-500 hover:text-blue-600 underline ml-1"
                    disabled={isImporting}
                  >
                    点击选择文件
                  </button>
                </p>
                <p className="text-sm text-gray-500">支持 .json 格式的世界书文件</p>
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".json"
                onChange={handleFileChange}
                disabled={isImporting}
              />
            </div>

            {/* 状态显示 */}
            {importStatus && (
              <div className="mt-4">
                <p className={`text-sm ${importStatus.includes('错误') || importStatus.includes('失败') ? 'text-red-500' : 'text-gray-600'}`}>
                  {importStatus}
                </p>
                
                {isImporting && (
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${importProgress}%` }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 预览区域 */}
          {(isPreviewLoading || previewData) && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">文件预览</h3>
              
              {isPreviewLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-3 text-gray-600">正在分析文件...</span>
                </div>
              ) : previewData && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{previewData.totalEntries}</div>
                      <div className="text-sm text-gray-600">总条目</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{previewData.validEntries}</div>
                      <div className="text-sm text-gray-600">有效条目</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{previewData.emptyEntries}</div>
                      <div className="text-sm text-gray-600">空内容</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{previewData.disabledEntries}</div>
                      <div className="text-sm text-gray-600">已禁用</div>
                    </div>
                  </div>

                  {/* 样本条目 */}
                  {previewData.sampleEntries.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2">样本条目预览:</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {previewData.sampleEntries.slice(0, 3).map((entry, index) => (
                          <div key={index} className="bg-white p-3 rounded border">
                            <div className="font-medium text-sm text-gray-800">{entry.comment}</div>
                            <div className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {entry.content.substring(0, 100)}...
                            </div>
                            <div className="flex gap-1 mt-2">
                              {entry.key.slice(0, 3).map((keyword, i) => (
                                <span key={i} className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded">
                                  {keyword}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 导入选项 */}
          {previewData && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">导入选项</h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={importOptions.skipEmptyContent}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, skipEmptyContent: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isImporting}
                  />
                  <span className="text-sm text-gray-700">跳过空内容条目</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={importOptions.skipDisabled}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, skipDisabled: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isImporting}
                  />
                  <span className="text-sm text-gray-700">跳过已禁用条目</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={importOptions.preserveOrder}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, preserveOrder: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isImporting}
                  />
                  <span className="text-sm text-gray-700">保留原始排序</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {previewData && `将导入 ${previewData.validEntries} 个有效条目`}
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={isImporting}
              >
                取消
              </button>
              <button
                onClick={handleImport}
                disabled={!selectedFile || !previewData || isImporting || previewData.validEntries === 0}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {isImporting ? '导入中...' : `导入世界书 (${previewData?.validEntries || 0}个条目)`}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
