"use client";

import React from 'react';

interface TerminologyPanelFooterProps {
  terminologyCount: number;
}

/**
 * 术语面板底部组件
 */
export const TerminologyPanelFooter: React.FC<TerminologyPanelFooterProps> = ({
  terminologyCount
}) => {
  return (
    <div className="flex items-center justify-between p-4" style={{
      borderTop: '1px solid rgba(139, 69, 19, 0.1)',
      backgroundColor: 'var(--color-primary-bg)',
      borderBottomLeftRadius: '16px',
      borderBottomRightRadius: '16px'
    }}>
      <div className="text-sm" style={{ color: 'var(--color-primary)' }}>
        共 <span className="font-semibold">{terminologyCount}</span> 个术语
      </div>
      <div className="text-xs" style={{ color: 'var(--color-text-secondary)' }}>
        提示：术语管理可以帮助您保持小说世界观的一致性
      </div>
    </div>
  );
};
