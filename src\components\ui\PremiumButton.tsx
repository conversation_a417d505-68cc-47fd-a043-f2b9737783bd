"use client";

import React, { useState } from 'react';

interface PremiumButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  className?: string;
}

export const PremiumButton: React.FC<PremiumButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  className = ''
}) => {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    if (disabled || loading) return;
    
    setIsClicked(true);
    setTimeout(() => setIsClicked(false), 150);
    
    if (onClick) {
      onClick();
    }
  };

  const baseClasses = `
    premium-button
    relative
    inline-flex
    items-center
    justify-center
    gap-2
    font-semibold
    rounded-xl
    transition-all
    duration-300
    ease-out
    overflow-hidden
    focus:outline-none
    focus:ring-4
    focus:ring-blue-500
    focus:ring-opacity-20
    ${disabled || loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
    ${isClicked ? 'scale-95' : 'hover:scale-105'}
  `;

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-blue-500 to-purple-600
      hover:from-blue-600 hover:to-purple-700
      text-white
      shadow-lg
      hover:shadow-xl
      hover:-translate-y-1
    `,
    secondary: `
      bg-white
      border-2 border-gray-200
      hover:border-blue-300
      text-gray-700
      hover:text-blue-600
      shadow-md
      hover:shadow-lg
      backdrop-blur-sm
    `,
    ghost: `
      bg-transparent
      hover:bg-gray-100
      text-gray-600
      hover:text-gray-800
    `
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  return (
    <button
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      {/* 波纹效果 */}
      <span className="absolute inset-0 overflow-hidden rounded-xl">
        <span 
          className={`
            absolute inset-0 
            bg-white 
            opacity-0 
            transition-opacity 
            duration-300
            ${isClicked ? 'opacity-20' : ''}
          `}
        />
      </span>

      {/* 内容 */}
      <span className="relative flex items-center gap-2">
        {loading ? (
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>加载中...</span>
          </div>
        ) : (
          <>
            {icon && <span className="flex-shrink-0">{icon}</span>}
            {children}
          </>
        )}
      </span>
    </button>
  );
};

export default PremiumButton;
