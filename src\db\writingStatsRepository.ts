import { v4 as uuidv4 } from 'uuid';
import { db, WritingStats } from './database';

/**
 * 写作统计仓库
 */
export class WritingStatsRepository {
  /**
   * 获取书籍的所有写作统计
   * @param bookId 书籍ID
   * @returns 写作统计列表，按日期排序
   */
  async getStatsByBookId(bookId: string): Promise<WritingStats[]> {
    return await db.writingStats
      .where('bookId')
      .equals(bookId)
      .sortBy('date');
  }

  /**
   * 获取书籍的特定日期的写作统计
   * @param bookId 书籍ID
   * @param date 日期字符串，格式为YYYY-MM-DD
   * @returns 写作统计对象，如果不存在则返回undefined
   */
  async getStatsByDate(bookId: string, date: string): Promise<WritingStats | undefined> {
    try {
      // 尝试使用复合索引查询
      return await db.writingStats
        .where('[bookId+date]')
        .equals([bookId, date])
        .first();
    } catch (error) {
      console.warn('复合索引查询失败，使用备用查询方法', error);

      // 备用查询方法：先按bookId过滤，再按date过滤
      return await db.writingStats
        .where('bookId')
        .equals(bookId)
        .filter(stats => stats.date === date)
        .first();
    }
  }

  /**
   * 获取书籍的今日写作统计
   * @param bookId 书籍ID
   * @returns 今日写作统计对象，如果不存在则返回undefined
   */
  async getTodayStats(bookId: string): Promise<WritingStats | undefined> {
    const today = this.getFormattedDate(new Date());
    return await this.getStatsByDate(bookId, today);
  }

  /**
   * 记录写作统计
   * @param bookId 书籍ID
   * @param wordCount 字数变化（可以是正数或负数）
   * @param timeSpent 花费时间（分钟）
   * @returns 更新后的写作统计对象
   */
  async recordStats(bookId: string, wordCount: number, timeSpent: number): Promise<WritingStats> {
    const today = this.getFormattedDate(new Date());

    // 查找今日统计
    let stats = await this.getStatsByDate(bookId, today);

    if (stats) {
      // 更新现有统计
      // 确保字数不会变成负数
      stats.wordCount = Math.max(0, stats.wordCount + wordCount);
      stats.timeSpent += timeSpent;

      await db.writingStats.update(stats.id!, stats);
    } else {
      // 创建新统计
      stats = {
        id: uuidv4(),
        bookId,
        date: today,
        wordCount: Math.max(0, wordCount), // 确保字数不会是负数
        timeSpent
      };

      await db.writingStats.add(stats);
    }

    return stats;
  }

  /**
   * 获取书籍的总字数
   * @param bookId 书籍ID
   * @returns 总字数
   */
  async getTotalWordCount(bookId: string): Promise<number> {
    const stats = await this.getStatsByBookId(bookId);
    return stats.reduce((total, stat) => total + stat.wordCount, 0);
  }

  /**
   * 获取书籍的总写作时间
   * @param bookId 书籍ID
   * @returns 总写作时间（分钟）
   */
  async getTotalTimeSpent(bookId: string): Promise<number> {
    const stats = await this.getStatsByBookId(bookId);
    return stats.reduce((total, stat) => total + stat.timeSpent, 0);
  }

  /**
   * 获取格式化的日期字符串
   * @param date 日期对象
   * @returns 格式化的日期字符串，格式为YYYY-MM-DD
   */
  private getFormattedDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
}
