"use client";

import React from 'react';

interface ConfirmContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  content: string;
  fieldLabel: string;
}

/**
 * 确认内容对话框组件
 */
export const ConfirmContentDialog: React.FC<ConfirmContentDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  content,
  fieldLabel
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[500px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>{title}</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-4">
            <p className="text-gray-700">是否应用以下生成的{fieldLabel}？</p>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-[300px] overflow-y-auto"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                borderColor: 'rgba(139, 69, 19, 0.1)'
              }}
            >
              <pre className="whitespace-pre-wrap text-gray-800 text-sm">{content}</pre>
            </div>

            <p className="text-sm text-gray-500 mt-2">
              注意：应用后，生成的内容将会追加到现有内容之后，而不是替换现有内容。
            </p>
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onClose}
          >
            取消
          </button>

          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
            }}
            onClick={onConfirm}
          >
            应用
          </button>
        </div>
      </div>
    </div>
  );
};
