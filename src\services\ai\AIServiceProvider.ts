"use client";

import { AIResponse, AIRequestOptions } from '@/factories/ai/interfaces/IAIComponent';
// 移除未使用的导入
// import { ConversationMessage } from '@/factories/ai/services/AIWritingService';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { createChapterSegmenter, ChapterSegmenter } from '@/utils/ai/ChapterSegmenter';
import { AIContinuePrompts } from '@/utils/ai/prompts/AIContinuePrompts';
import { UnifiedAIService, AIServiceType } from './BaseAIService';

/**
 * AI服务提供者
 * 集中管理所有AI方法调用，避免代码重复
 */
export class AIServiceProvider extends UnifiedAIService {
  private static instance: AIServiceProvider;
  private apiSettings: any;
  private segmenter: ChapterSegmenter;
  private abortController: AbortController | null = null;

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    super(AIServiceType.WRITING); // 使用写作类型作为默认
    // 创建章节分段器，设置适当的分段选项
    this.segmenter = createChapterSegmenter({
      maxSegmentLength: 2000,
      minSegmentLength: 500,
      addSegmentNumber: true,
      preserveCharacterDescriptions: true
    });

    // 获取API设置
    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 确保在客户端环境中初始化时加载设置
    if (typeof window !== 'undefined') {
      // 延迟一点时间再获取API设置，确保localStorage已加载
      setTimeout(() => {
        // 重新获取API设置，确保从localStorage加载了最新的设置
        // 保存当前设置的引用，而不是创建新实例
        const newSettings = settingsFactory.createAPISettingsDialogComponent();

        // 检查是否有有效的API设置
        if (newSettings && typeof newSettings === 'object') {
          try {
            // 尝试调用方法验证对象是否有效
            const provider = newSettings.getCurrentProvider();
            const model = newSettings.getCurrentModel();

            // 如果能成功获取这些值，说明对象有效
            if (provider && model) {
              // 只更新引用，不修改原始设置
              this.apiSettings = newSettings;
              console.log('AIServiceProvider: 已重新加载API设置', { provider, model });
            } else {
              console.warn('AIServiceProvider: API设置无效，保留现有设置');
            }
          } catch (error) {
            console.warn('AIServiceProvider: 验证API设置时出错，保留现有设置', error);
          }
        } else {
          console.warn('AIServiceProvider: 无法加载有效的API设置，保留现有设置');
        }
      }, 200);
    }
  }

  /**
   * 获取单例实例
   * @returns AIServiceProvider实例
   */
  public static getInstance(): AIServiceProvider {
    if (!AIServiceProvider.instance) {
      AIServiceProvider.instance = new AIServiceProvider();
    }
    return AIServiceProvider.instance;
  }

  /**
   * 发送AI请求
   * @param messages 消息数组
   * @param options 请求选项
   * @returns AI响应
   */
  public async sendRequest(
    messages: Array<{ role: string; content: string }>,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI服务架构（现在AIServiceProvider继承了UnifiedAIService）
      // 如果启用了流式输出，使用流式方法
      if (options?.stream) {
        let fullResponse = '';

        await this.callAIStreaming(
          messages,
          (chunk: string) => {
            fullResponse += chunk;
            // 这里可以添加流式数据的处理逻辑
          },
          {
            streaming: true
          }
        );

        return {
          text: fullResponse,
          success: true
        };
      } else {
        // 非流式请求
        const response = await this.callAI(messages, {
          streaming: false
        });
        return response;
      }
    } catch (error: any) {
      console.error('AI请求失败:', error);
      return {
        text: '',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 发送流式AI请求
   * @param messages 消息数组
   * @param onChunk 接收数据块的回调函数
   * @param options 请求选项
   * @returns 完整的响应内容
   */
  public async sendStreamingRequest(
    messages: Array<{ role: string; content: string }>,
    onChunk: (chunk: string) => void,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      // 创建AbortController用于取消请求
      this.abortController = new AbortController();

      // 使用统一的AI流式调用方法
      let fullResponse = '';

      await this.callAIStreaming(
        messages,
        (chunk: string) => {
          fullResponse += chunk;
          onChunk(chunk);
        },
        {
          streaming: true
        }
      );

      return {
        text: fullResponse,
        success: true
      };
    } catch (error: any) {
      console.error('AI流式请求失败:', error);
      return {
        text: '',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 取消当前请求
   */
  public cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * 构建续写消息 - 优化版本，调整消息权重顺序
   * @param params 续写参数
   * @returns 构建的消息数组
   */
  public async buildContinueMessages(params: {
    continueRequirements?: string;
    continueStyle?: string;
    futurePlot?: string;
    context?: string;
    currentChapterName?: string;
    currentChapterId?: string;
    chapters?: Array<{id?: string, name: string, description?: string, order?: number, content?: string}>;
    characters?: Array<{id?: string, name: string, description?: string}>;
    terminologies?: Array<{id?: string, name: string, description?: string}>;
    worldBuildings?: Array<{id?: string, name: string, description?: string}>;
    selectedCharacterIds?: string[];
    selectedTerminologyIds?: string[];
    selectedWorldBuildingIds?: string[];
    selectedChapterIds?: string[];
    // 新增：大纲关联参数
    outlines?: any[];
    selectedOutlineIds?: string[];
    selectedOutlineNodeIds?: string[];
    outlineContextMode?: 'selected' | 'hierarchy' | 'full';
  }): Promise<Array<{ role: string; content: string }>> {
    // 创建消息构建器
    const messageBuilder = new MessageBuilder();

    // 解析上下文，分离前文和后文（先解析，再用于生成系统消息）
    let beforeContext = '';
    let afterContext = '';
    let beforeContextEnd = '';
    let afterContextStart = '';

    if (params.context) {
      const contextParts = params.context.split(/【当前章节后文】/);

      if (contextParts.length > 1) {
        // 如果找到了【当前章节后文】标记
        beforeContext = contextParts[0].replace(/【当前章节前文】/, '').trim();
        afterContext = contextParts[1].trim();
      } else {
        // 如果没有找到【当前章节后文】标记，尝试查找【章节前文】
        const beforeParts = params.context.split(/【章节前文】/);
        if (beforeParts.length > 1) {
          beforeContext = beforeParts[1].trim();
        } else {
          // 如果都没找到，就使用整个上下文作为前文
          beforeContext = params.context.trim();
        }
      }
    }

    // 智能生成系统消息（在解析上下文之后）
    const smartSystemPrompt = AIContinuePrompts.generateSmartSystemPrompt(
      !!beforeContext,
      !!afterContext
    );
    messageBuilder.addSystemMessage(smartSystemPrompt);

    // 添加助手消息（保持第二）
    messageBuilder.addAssistantMessage(AIContinuePrompts.assistantRolePrompt);

    // 处理关联人物
    if (params.characters && params.selectedCharacterIds && params.selectedCharacterIds.length > 0) {
      const selectedCharacters = params.characters.filter(char =>
        params.selectedCharacterIds!.includes(char.id || '') && char.id
      );

      if (selectedCharacters.length > 0) {
        // 添加人物列表标题
        messageBuilder.addUserMessage(`【关联人物列表】\n共${selectedCharacters.length}个人物`);
        messageBuilder.addAssistantMessage(`我将逐一了解这些与当前章节相关的人物。`);

        // 为每个人物添加单独的消息
        for (const char of selectedCharacters) {
          messageBuilder.addUserMessage(`【人物】\n名称：${char.name}${char.description ? `\n描述：${char.description}` : ''}`);
          messageBuilder.addAssistantMessage(`我已了解了"${char.name}"的信息。`);
        }

        // 添加人物总结
        messageBuilder.addAssistantMessage(`我已了解了所有${selectedCharacters.length}个关联人物的信息。`);
      }
    }

    // 处理关联术语
    if (params.terminologies && params.selectedTerminologyIds && params.selectedTerminologyIds.length > 0) {
      const selectedTerms = params.terminologies.filter(term =>
        params.selectedTerminologyIds!.includes(term.id || '') && term.id
      );

      if (selectedTerms.length > 0) {
        // 添加术语列表标题
        messageBuilder.addUserMessage(`【关联术语列表】\n共${selectedTerms.length}个术语`);
        messageBuilder.addAssistantMessage(`我将逐一了解这些与当前章节相关的术语。`);

        // 为每个术语添加单独的消息
        for (const term of selectedTerms) {
          messageBuilder.addUserMessage(`【术语】\n名称：${term.name}${term.description ? `\n描述：${term.description}` : ''}`);
          messageBuilder.addAssistantMessage(`我已了解了"${term.name}"的含义。`);
        }

        // 添加术语总结
        messageBuilder.addAssistantMessage(`我已了解了所有${selectedTerms.length}个关联术语的信息。`);
      }
    }

    // 处理关联世界观
    if (params.worldBuildings && params.selectedWorldBuildingIds && params.selectedWorldBuildingIds.length > 0) {
      const selectedWorldViews = params.worldBuildings.filter(wb =>
        params.selectedWorldBuildingIds!.includes(wb.id || '') && wb.id
      );

      if (selectedWorldViews.length > 0) {
        // 添加世界观列表标题
        messageBuilder.addUserMessage(`【关联世界观列表】\n共${selectedWorldViews.length}个世界观元素`);
        messageBuilder.addAssistantMessage(`我将逐一了解这些与当前章节相关的世界观元素。`);

        // 为每个世界观添加单独的消息
        for (const wb of selectedWorldViews) {
          messageBuilder.addUserMessage(`【世界观】\n名称：${wb.name}${wb.description ? `\n描述：${wb.description}` : ''}`);
          messageBuilder.addAssistantMessage(`我已了解了"${wb.name}"的世界观设定。`);
        }

        // 添加世界观总结
        messageBuilder.addAssistantMessage(`我已了解了所有${selectedWorldViews.length}个关联世界观元素的信息。`);
      }
    }

    // 处理关联大纲节点
    if (params.outlines && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0) {
      console.log('🔍 AIServiceProvider.buildContinueMessages 处理大纲数据:', {
        hasOutlines: !!params.outlines,
        outlinesLength: params.outlines?.length || 0,
        selectedNodeIds: params.selectedOutlineNodeIds
      });

      // 导入PromptHelperService来处理大纲数据
      try {
        const { createPromptHelperService } = await import('../../factories/ai/services/PromptHelperService');
        const promptHelper = createPromptHelperService();

        // 使用统一的大纲添加方法，与AI写作功能保持一致（单节点单条目模式）
        promptHelper.addSelectedOutlineNodes(
          messageBuilder,
          params.outlines,
          params.selectedOutlineNodeIds,
          'hierarchy' // 使用层级模式，与AI写作保持一致
        );

        console.log('✅ 大纲数据已添加到续写消息构建器（统一模式）');
      } catch (error) {
        console.error('❌ 导入PromptHelperService失败:', error);
      }
    }

    // 处理关联章节
    if (params.chapters && params.selectedChapterIds && params.selectedChapterIds.length > 0 && params.currentChapterId) {
      // 获取所有章节并按顺序排序
      const allChapters = [...params.chapters].sort((a, b) => {
        const orderA = a.order !== undefined ? a.order : 0;
        const orderB = b.order !== undefined ? b.order : 0;
        return orderA - orderB;
      });

      // 找到当前章节的索引
      const currentChapterIndex = allChapters.findIndex(chapter => chapter.id === params.currentChapterId);

      // 获取前一章节和后一章节
      const prevChapter = currentChapterIndex > 0 ? allChapters[currentChapterIndex - 1] : null;
      const nextChapter = currentChapterIndex < allChapters.length - 1 ? allChapters[currentChapterIndex + 1] : null;

      // 获取关联章节数据（排除当前章节、前一章节和后一章节）
      let selectedChapters = params.chapters.filter(chapter =>
        params.selectedChapterIds!.includes(chapter.id || '') &&
        chapter.id &&
        chapter.id !== params.currentChapterId && // 排除当前章节
        (!prevChapter || chapter.id !== prevChapter.id) && // 排除前一章节
        (!nextChapter || chapter.id !== nextChapter.id) // 排除后一章节
      );

      // 根据章节顺序排序，并按照与当前章节的相对位置分组
      if (selectedChapters.length > 0 && params.currentChapterId) {
        // 获取当前章节
        const currentChapter = params.chapters.find(chapter => chapter.id === params.currentChapterId);
        // 获取当前章节的order
        const currentOrder = currentChapter?.order || 0;

        // 按照order排序
        selectedChapters.sort((a, b) => {
          const orderA = a.order !== undefined ? a.order : 0;
          const orderB = b.order !== undefined ? b.order : 0;

          // 计算与当前章节的距离
          const distanceA = Math.abs(orderA - currentOrder);
          const distanceB = Math.abs(orderB - currentOrder);

          // 优先按距离排序，距离相同则按顺序排序
          if (distanceA !== distanceB) {
            return distanceA - distanceB; // 距离近的排在前面
          } else {
            return orderA - orderB; // 距离相同则按章节顺序排序
          }
        });
      }
// ... （您代码的其他部分）

      // 添加关联章节（如果有）- 使用智能分段处理 (导演版)
      if (selectedChapters.length > 0) {
        // 添加关联章节列表标题
        messageBuilder.addUserMessage(`【相关场景参考列表】`);

        // 添加章节列表，每个章节名称作为单独的消息
        for (let i = 0; i < selectedChapters.length; i++) {
          messageBuilder.addUserMessage(`${i + 1}. ${selectedChapters[i].name}`);
        }

        messageBuilder.addAssistantMessage(`收到。我将逐一拉片（审阅）这些参考场景。`);

        // 为每个章节单独处理
        for (const chapter of selectedChapters) {
          // 添加章节标题作为单独的消息
          messageBuilder.addUserMessage(`【参考场景】${chapter.name}`);

          if (chapter.description) {
            messageBuilder.addUserMessage(`【场景梗概】\n${chapter.description}`);
            messageBuilder.addAssistantMessage(`我已了解场景"${chapter.name}"的梗概。`);
          }

          // 如果没有内容，添加提示并继续下一个章节
          if (!chapter.content) {
            messageBuilder.addAssistantMessage(`场景"${chapter.name}"无内容脚本，将跳过审阅。`);
            continue;
          }

          // 使用分段器处理章节内容
          const segments = this.segmenter.segmentChapter(chapter.content);

          // 添加章节分段消息
          messageBuilder.addAssistantMessage(`开始审阅场景"${chapter.name}"的剧本，共${segments.length}个分镜。`);

          // 为每个段落添加单独的消息
          for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            const wordCount = segment.replace(/\s+/g, '').length;

            // 每个段落作为单独的消息，包含段落编号和字数信息
            messageBuilder.addUserMessage(`【${chapter.name}】\n分镜：${i+1}/${segments.length}\n字数：${wordCount}\n\n${segment}`);

            // 每个段落后添加确认消息
            messageBuilder.addAssistantMessage(`我已审阅完场景"${chapter.name}"的第${i+1}个分镜。`);
          }

          // 章节结束消息
          messageBuilder.addAssistantMessage(`我已完成对场景"${chapter.name}"的审阅。`);
        }

        // 所有章节处理完毕的总结消息
        messageBuilder.addAssistantMessage(`所有参考场景已审阅完毕，将作为本次拍摄的重要参考。`);
      }
    }

    // 【新增】添加用户指令消息组（在关联元素之后，前文后文分析之前，确保高权重）(导演版)
    // 1. 续写要求 -> 导演备忘录
    if (params.continueRequirements && params.continueRequirements.trim()) {
      messageBuilder.addUserMessage(`【导演备忘录】\n${params.continueRequirements}`);
      messageBuilder.addAssistantMessage(`收到导演备忘录，将严格按照这些要求执行拍摄。`);
    }

    // 2. 续写风格 -> 影片风格
    if (params.continueStyle && params.continueStyle.trim()) {
      messageBuilder.addUserMessage(`【影片风格】\n${params.continueStyle}`);
      messageBuilder.addAssistantMessage(`已明确影片风格，拍摄时将保持这种调性。`);
    }

    // 3. 剧情方向 -> 剧本走向
    if (params.futurePlot && params.futurePlot.trim()) {
      messageBuilder.addUserMessage(`【剧本走向】\n${params.futurePlot}`);
      messageBuilder.addAssistantMessage(`已明确剧本走向，将朝着这个方向推进情节。`);
    }

    // 添加关联元素总结 (导演版)
    if (
      (params.characters && params.selectedCharacterIds && params.selectedCharacterIds.length > 0) ||
      (params.terminologies && params.selectedTerminologyIds && params.selectedTerminologyIds.length > 0) ||
      (params.worldBuildings && params.selectedWorldBuildingIds && params.selectedWorldBuildingIds.length > 0) ||
      (params.chapters && params.selectedChapterIds && params.selectedChapterIds.length > 0) ||
      (params.outlines && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0)
    ) {
      let summaryMessage = `【拍摄资料总结】\n所有资料已收到并分析完毕，总结如下：\n`;

      // 添加人物总结 -> 演员
      if (params.characters && params.selectedCharacterIds && params.selectedCharacterIds.length > 0) {
        const selectedCharacters = params.characters.filter(char =>
          params.selectedCharacterIds!.includes(char.id || '') && char.id
        );
        if (selectedCharacters.length > 0) {
          summaryMessage += `- 演员表：${selectedCharacters.map(char => char.name).join('、')}\n`;
        }
      }

      // 添加术语总结 -> 关键道具
      if (params.terminologies && params.selectedTerminologyIds && params.selectedTerminologyIds.length > 0) {
        const selectedTerms = params.terminologies.filter(term =>
          params.selectedTerminologyIds!.includes(term.id || '') && term.id
        );
        if (selectedTerms.length > 0) {
          summaryMessage += `- 关键道具/术语：${selectedTerms.map(term => term.name).join('、')}\n`;
        }
      }

      // 添加世界观总结
      if (params.worldBuildings && params.selectedWorldBuildingIds && params.selectedWorldBuildingIds.length > 0) {
        const selectedWorldViews = params.worldBuildings.filter(wb =>
          params.selectedWorldBuildingIds!.includes(wb.id || '') && wb.id
        );
        if (selectedWorldViews.length > 0) {
          summaryMessage += `- 世界观设定：${selectedWorldViews.map(wb => wb.name).join('、')}\n`;
        }
      }

      // 添加章节总结 -> 参考场景
      if (params.chapters && params.selectedChapterIds && params.selectedChapterIds.length > 0) {
        const selectedChapters = params.chapters.filter(chapter =>
          params.selectedChapterIds!.includes(chapter.id || '') && chapter.id
        );
        if (selectedChapters.length > 0) {
          summaryMessage += `- 参考场景：${selectedChapters.map(chapter => chapter.name).join('、')}\n`;
        }
      }

      // 添加大纲总结 -> 分镜脚本
      if (params.outlines && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0) {
        summaryMessage += `- 分镜脚本：已选定${params.selectedOutlineNodeIds.length}个分镜\n`;
      }

      messageBuilder.addAssistantMessage(summaryMessage);
    }

    // 添加续写风格（中间位置，作为创作指导）(导演版)
    if (params.continueStyle) {
      messageBuilder.addUserMessage(`【影片风格】\n\n${params.continueStyle}`);
    }

    // 添加后续剧情（中间位置，作为情节指导）(导演版)
    if (params.futurePlot) {
      messageBuilder.addUserMessage(`【剧本走向】\n\n${params.futurePlot}`);
    }

    // 添加续写要求（较后位置，作为具体要求）(导演版)
    if (params.continueRequirements) {
      messageBuilder.addUserMessage(`【导演备忘录】\n\n${params.continueRequirements}`);
    }

    // 【重要】添加当前章节前文后文（最后位置，获得最高权重）(导演版)
    if (beforeContext) {
      messageBuilder.addUserMessage(`【${params.currentChapterName || '当前场景'}的已拍板内容】\n${beforeContext}`);

      // 生成智能的前文理解确认消息
      const beforeContextResult = this.generateBeforeContextAnalysis(beforeContext, params.currentChapterName || '当前场景');
      messageBuilder.addAssistantMessage(beforeContextResult.analysis); // 假设此函数返回的analysis文本也已影视化
      beforeContextEnd = beforeContextResult.endingContent;
    }

    if (afterContext) {
      messageBuilder.addUserMessage(`【${params.currentChapterName || '当前场景'}的待拍板内容】\n${afterContext}`);

      // 生成智能的后文理解确认消息
      const afterContextResult = this.generateAfterContextAnalysis(afterContext, params.currentChapterName || '当前场景');
      messageBuilder.addAssistantMessage(afterContextResult.analysis); // 假设此函数返回的analysis文本也已影视化
      afterContextStart = afterContextResult.startingContent;
    }
    // 智能生成最终的续写指令（保持最后）
    const smartFinalInstruction = AIContinuePrompts.generateSmartFinalInstruction(
      !!beforeContext,
      !!afterContext,
      beforeContext ? beforeContext.length : 0,
      afterContext ? afterContext.length : 0,
      beforeContextEnd,
      afterContextStart
    );
    messageBuilder.addUserMessage(smartFinalInstruction);

    // 构建消息数组
    return messageBuilder.build();
  }

  /**
   * 生成前文理解分析消息
   * @param beforeContext 前文内容
   * @param chapterName 章节名称
   * @returns 智能分析的助手消息和结尾内容
   */
  private generateBeforeContextAnalysis(beforeContext: string, chapterName: string): { analysis: string; endingContent: string } {
    // 提取前文的结尾部分进行分析（后150字左右）
    const analysisText = beforeContext.length > 150
      ? beforeContext.substring(beforeContext.length - 150)
      : beforeContext;

    // 分析前文的结尾特征
    const sentences = analysisText.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
    const lastSentence = sentences[sentences.length - 1]?.trim() || '';

    // 提取前文的最后几句具体内容（用于展示理解）
    const lastFewSentences = sentences.slice(-2).join('。') + (sentences.length > 0 ? '。' : '');

    // 检测前文结尾的关键元素
    const hasDialogue = /["「『"]/.test(analysisText);
    const hasAction = /走|跑|看|说|想|做|来|去|站|坐/.test(analysisText);
    const hasThought = /想|思考|觉得|认为|心里/.test(analysisText);
    const hasDescription = /的|着|了|在|是|有/.test(analysisText);

    // 生成智能分析消息
    let analysisMessage = `我已阅读了"${chapterName}"的前文内容。`;

    // 展示对前文结尾的具体理解
    if (lastFewSentences && lastFewSentences.trim()) {
      analysisMessage += `\n\n前文结尾内容：「${lastFewSentences.trim()}」\n`;
    }

    if (lastSentence) {
      // 分析结尾句的特点
      if (hasDialogue) {
        analysisMessage += `前文以对话结尾，我将从对话的后续反应或影响开始续写。`;
      } else if (hasAction) {
        analysisMessage += `前文描述了具体行动，我将承接这些行动的后续发展。`;
      } else if (hasThought) {
        analysisMessage += `前文涉及人物内心活动，我将延续这种心理状态的发展。`;
      } else if (hasDescription) {
        analysisMessage += `前文进行了场景描述，我将在此基础上推进情节发展。`;
      } else {
        analysisMessage += `前文建立了明确的情节基础，我将自然地延续故事发展。`;
      }
    }

    analysisMessage += `\n\n我将确保续写与前文在以下方面保持连贯：`;

    const continuityPoints = [];
    if (hasDialogue) {
      continuityPoints.push('对话的语气和情感延续');
    }
    if (hasAction) {
      continuityPoints.push('行动的逻辑性和连续性');
    }
    if (hasThought) {
      continuityPoints.push('人物心理状态的自然发展');
    }
    if (hasDescription) {
      continuityPoints.push('场景氛围的一致性');
    }

    // 添加通用连贯性要点
    continuityPoints.push('叙事节奏的平稳过渡');
    continuityPoints.push('人物性格的一致表现');
    continuityPoints.push('语言风格的统一性');

    // 格式化连贯性要点
    continuityPoints.forEach((point, index) => {
      analysisMessage += `\n${index + 1}. ${point}`;
    });

    // 提取前文结尾内容用于衔接指令
    const endingContent = lastSentence || (beforeContext.length > 30
      ? beforeContext.substring(beforeContext.length - 30).trim()
      : beforeContext.trim());

    return {
      analysis: analysisMessage,
      endingContent: endingContent
    };
  }

  /**
   * 生成后文理解分析消息
   * @param afterContext 后文内容
   * @param chapterName 章节名称
   * @returns 智能分析的助手消息和开头内容
   */
  private generateAfterContextAnalysis(afterContext: string, chapterName: string): { analysis: string; startingContent: string } {
    // 提取后文的开头部分进行分析（前150字左右）
    const analysisText = afterContext.substring(0, 150);

    // 分析后文的基本特征
    const sentences = analysisText.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
    const firstSentence = sentences[0]?.trim() || '';

    // 提取后文的开头几句具体内容（用于展示理解）
    const firstFewSentences = sentences.slice(0, 2).join('。') + (sentences.length > 0 ? '。' : '');

    // 检测可能的关键元素
    const hasDialogue = /["「『"]/.test(analysisText);
    const hasAction = /走|跑|看|说|想|做|来|去|站|坐/.test(analysisText);
    const hasDescription = /的|着|了|在|是|有/.test(analysisText);

  // ... (您的其他代码)

  // 生成智能分析消息 (导演版)
  let analysisMessage = `我已审阅了场景"${chapterName}"的待拍板内容。`;

  // 展示对后文开头的具体理解 -> 镜头预览
  if (firstFewSentences && firstFewSentences.trim()) {
    analysisMessage += `\n\n预览镜头：「${firstFewSentences.trim()}」\n`;
  }

  if (firstSentence) {
    // 分析开头句的特点 -> 分析镜头类型
    if (hasDialogue) {
      analysisMessage += `后续镜头以对话为主，我们需要为这场戏的发生创造合适的情境和氛围。`;
    } else if (hasAction) {
      analysisMessage += `后续镜头是动作戏，我们需要确保当前的拍摄能够自然过渡到这些动作场面。`;
    } else if (hasDescription) {
      analysisMessage += `后续镜头是场景或状态的定场镜头，我们需要在当前的拍摄中做好铺垫。`;
    } else {
      analysisMessage += `后续镜头有明确的情节发展，我们需要确保当前的拍摄能够顺畅衔接。`;
    }
  }

  // 添加衔接策略说明 -> 拍摄方案
  analysisMessage += `\n\n为确保剪辑流畅，我将重点关注以下拍摄方案：`;

  const connectionPoints = []; // 影视化术语替换
  if (hasDialogue) {
    connectionPoints.push('为对话创造自然的引入时机和场面');
  }
  if (hasAction) {
    connectionPoints.push('确保演员（人物）行为的连贯性和动机合理性');
  }
  if (hasDescription) {
    connectionPoints.push('保持美术风格和场景氛围的一致性');
  }

  // 如果没有检测到特定元素，添加通用衔接点 -> 通用拍摄要点
  if (connectionPoints.length === 0) {
    connectionPoints.push('保持故事发展的自然节奏');
    connectionPoints.push('确保演员（人物）状态的合理过渡');
  }

  // 添加节奏和风格考虑
  connectionPoints.push('维持影片叙事节奏的连贯');
  connectionPoints.push('保持与样片一致的镜头语言和风格');

  // 格式化衔接要点
  connectionPoints.forEach((point, index) => {
    analysisMessage += `\n${index + 1}. ${point}`;
  });

  analysisMessage += `\n\n我将确保拍摄内容能够与后续镜头完美衔接，实现无缝转场。`;

  // 提取后文开头内容用于衔接指令 -> 提取关键镜头内容
  const startingContent = firstSentence || (afterContext.length > 30
    ? afterContext.substring(0, 30).trim()
    // Fallback for very short content
    : afterContext.trim());

  return {
    analysis: analysisMessage,
    startingContent: startingContent
  };
}

  /**
   * 获取章节分段器
   * @returns 章节分段器实例
   */
  public getSegmenter(): ChapterSegmenter {
    return this.segmenter;
  }

  /**
   * 构建对话继续消息
   * @param context 上下文内容
   * @param userInput 用户输入
   * @returns 构建的消息数组
   */
  public buildDialogueContinueMessages(
    context: string,
    userInput: string
  ): Array<{ role: string; content: string }> {
    // 创建消息构建器
    const messageBuilder = new MessageBuilder();

    // 添加系统消息
    messageBuilder.addSystemMessage('你是一位专业的小说创作助手，擅长提供有创意的写作建议和内容。请根据用户的输入继续对话。');

    // 添加上下文
    if (context) {
      messageBuilder.addUserMessage(`【上下文】\n${context}`);
      messageBuilder.addAssistantMessage('我已理解上下文内容，请告诉我您想要继续的方向。');
    }

    // 添加用户输入
    if (userInput) {
      messageBuilder.addUserMessage(`【用户输入】\n${userInput}`);
    }

    // 添加最终指令
    messageBuilder.addUserMessage('请根据以上内容继续对话，保持风格一致，直接回复内容，不要有任何前言或解释。');

    // 构建消息数组
    return messageBuilder.build();
  }

  /**
   * 继续对话
   * @param history 对话历史
   * @param options 请求选项
   * @returns AI响应
   */
  public async continueDialogue(
    history: Array<{ role: string; content: string }>,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      // 确保历史记录不为空
      if (!history || history.length === 0) {
        throw new Error('对话历史不能为空');
      }

      // 发送请求
      return await this.sendRequest(history, options);
    } catch (error: any) {
      console.error('继续对话失败:', error);
      return {
        text: '',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 流式继续对话
   * @param history 对话历史
   * @param onChunk 接收数据块的回调函数
   * @param options 请求选项
   * @returns 完整的响应内容
   */
  public async continueDialogueWithStreaming(
    history: Array<{ role: string; content: string }>,
    onChunk: (chunk: string) => void,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      // 确保历史记录不为空
      if (!history || history.length === 0) {
        throw new Error('对话历史不能为空');
      }

      // 发送流式请求
      return await this.sendStreamingRequest(history, onChunk, options);
    } catch (error: any) {
      console.error('流式继续对话失败:', error);
      return {
        text: '',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }
}

// 导出单例实例
export const aiServiceProvider = AIServiceProvider.getInstance();

export default aiServiceProvider;
