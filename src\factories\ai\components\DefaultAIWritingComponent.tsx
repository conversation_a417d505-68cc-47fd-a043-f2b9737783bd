"use client";

import React, { useState } from 'react';
import { IAIWritingComponent } from '../interfaces';

/**
 * 默认AI写作组件实现
 */
export class DefaultAIWritingComponent implements IAIWritingComponent {
  private requirements: string = '';
  private writingStyle: string = '';
  private corePlot: string = '';

  /**
   * 设置写作要求
   * @param requirements 写作要求
   */
  setRequirements(requirements: string): void {
    this.requirements = requirements;
  }

  /**
   * 设置写作风格
   * @param style 写作风格
   */
  setWritingStyle(style: string): void {
    this.writingStyle = style;
  }

  /**
   * 设置核心剧情
   * @param plot 核心剧情
   */
  setCorePlot(plot: string): void {
    this.corePlot = plot;
  }

  /**
   * 生成内容
   * @returns 生成的内容
   */
  async generate(): Promise<string> {
    // 这里应该调用AI服务，目前返回模拟数据
    console.log('生成内容', {
      requirements: this.requirements,
      writingStyle: this.writingStyle,
      corePlot: this.corePlot
    });

    return new Promise(resolve => {
      setTimeout(() => {
        resolve(`基于以下要求生成的内容：

要求：${this.requirements}
风格：${this.writingStyle}
剧情：${this.corePlot}

这里是AI生成的内容示例。在实际实现中，这里会调用OpenAI或其他AI服务来生成内容。`);
      }, 1000);
    });
  }

  /**
   * 渲染组件UI
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const AIWritingComponent = () => {
      const [requirements, setRequirements] = useState(this.requirements);
      const [writingStyle, setWritingStyle] = useState(this.writingStyle);
      const [corePlot, setCorePlot] = useState(this.corePlot);
      const [generatedContent, setGeneratedContent] = useState('');
      const [isGenerating, setIsGenerating] = useState(false);

      const handleGenerate = async () => {
        this.setRequirements(requirements);
        this.setWritingStyle(writingStyle);
        this.setCorePlot(corePlot);

        setIsGenerating(true);
        try {
          const content = await this.generate();
          setGeneratedContent(content);
        } catch (error) {
          console.error('生成内容失败', error);
        } finally {
          setIsGenerating(false);
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">AI写作</h2>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              写作要求
            </label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded"
              rows={3}
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder="请输入写作要求..."
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              写作风格
            </label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded"
              rows={2}
              value={writingStyle}
              onChange={(e) => setWritingStyle(e.target.value)}
              placeholder="请输入写作风格..."
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              核心剧情
            </label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded"
              rows={3}
              value={corePlot}
              onChange={(e) => setCorePlot(e.target.value)}
              placeholder="请输入核心剧情..."
            />
          </div>

          <button
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
            onClick={handleGenerate}
            disabled={isGenerating || !requirements}
          >
            {isGenerating ? '生成中...' : '生成内容'}
          </button>

          {generatedContent && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">生成结果</h3>
              <div className="p-3 bg-gray-50 rounded border border-gray-200 whitespace-pre-wrap">
                {generatedContent}
              </div>
              <button
                className="mt-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                onClick={() => {
                  // 这里应该实现插入到编辑器的功能
                  alert('内容已插入到编辑器（模拟）');
                }}
              >
                插入到编辑器
              </button>
            </div>
          )}
        </div>
      );
    };

    return <AIWritingComponent />;
  }
}
