"use client";

import { SuggestionOption, SuggestionType, SuggestionCategory } from '@/factories/ai/services/TextProcessingService';

/**
 * 用户偏好数据
 */
export interface UserPreferences {
  preferredSuggestionTypes: Map<SuggestionType, number>;
  preferredStyles: Map<string, number>;
  confidenceThreshold: number;
  categoryWeights: Map<SuggestionCategory, number>;
  lastUpdated: number;
}

/**
 * 用户行为记录
 */
export interface UserAction {
  action: 'accept' | 'reject' | 'edit';
  suggestion: SuggestionOption;
  timestamp: number;
  editedText?: string; // 如果是编辑操作，记录编辑后的文本
}

/**
 * 偏好分析结果
 */
export interface PreferenceAnalysis {
  topSuggestionTypes: Array<{ type: SuggestionType; score: number }>;
  topStyles: Array<{ style: string; score: number }>;
  confidenceThreshold: number;
  categoryPreferences: Array<{ category: SuggestionCategory; weight: number }>;
  totalActions: number;
  acceptanceRate: number;
}

/**
 * 用户偏好分析器
 * 根据用户的接受/拒绝行为学习和调整AI建议
 */
export class UserPreferenceAnalyzer {
  private preferences: UserPreferences;
  private actionHistory: UserAction[] = [];
  private maxHistorySize = 1000;

  constructor() {
    this.preferences = this.getDefaultPreferences();
    this.loadPreferences();
  }

  /**
   * 获取默认偏好设置
   * @returns 默认偏好
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      preferredSuggestionTypes: new Map([
        ['modify', 1.0],
        ['enhance', 0.8],
        ['simplify', 0.6],
        ['delete', 0.4],
        ['split', 0.3],
        ['merge', 0.3],
        ['reorder', 0.2],
        ['keep', 0.1]
      ]),
      preferredStyles: new Map(),
      confidenceThreshold: 0.7,
      categoryWeights: new Map([
        ['grammar', 1.0],
        ['expression', 0.9],
        ['style', 0.8],
        ['clarity', 0.7],
        ['logic', 0.6],
        ['structure', 0.5]
      ]),
      lastUpdated: Date.now()
    };
  }

  /**
   * 记录用户行为
   * @param action 行为类型
   * @param suggestion 建议对象
   * @param editedText 编辑后的文本（可选）
   */
  recordUserAction(action: 'accept' | 'reject' | 'edit', suggestion: SuggestionOption, editedText?: string): void {
    const userAction: UserAction = {
      action,
      suggestion,
      timestamp: Date.now(),
      editedText
    };

    // 添加到历史记录
    this.actionHistory.push(userAction);

    // 限制历史记录大小
    if (this.actionHistory.length > this.maxHistorySize) {
      this.actionHistory = this.actionHistory.slice(-this.maxHistorySize);
    }

    // 更新偏好
    this.updatePreferences(userAction);

    // 保存偏好
    this.savePreferences();

    console.log('📊 记录用户行为:', {
      action,
      suggestionType: suggestion.type,
      confidence: suggestion.confidence,
      newThreshold: this.preferences.confidenceThreshold
    });
  }

  /**
   * 更新用户偏好
   * @param userAction 用户行为
   */
  private updatePreferences(userAction: UserAction): void {
    const { action, suggestion } = userAction;

    if (action === 'accept') {
      // 增加偏好权重
      this.preferences.preferredSuggestionTypes.set(
        suggestion.type,
        (this.preferences.preferredSuggestionTypes.get(suggestion.type) || 0) + 0.1
      );

      if (suggestion.style) {
        this.preferences.preferredStyles.set(
          suggestion.style,
          (this.preferences.preferredStyles.get(suggestion.style) || 0) + 0.1
        );
      }

      // 调整置信度阈值
      if (suggestion.confidence < this.preferences.confidenceThreshold) {
        this.preferences.confidenceThreshold = Math.max(
          0.3,
          this.preferences.confidenceThreshold - 0.02
        );
      }

    } else if (action === 'reject') {
      // 降低偏好权重
      this.preferences.preferredSuggestionTypes.set(
        suggestion.type,
        Math.max(0, (this.preferences.preferredSuggestionTypes.get(suggestion.type) || 0) - 0.05)
      );

      if (suggestion.style) {
        this.preferences.preferredStyles.set(
          suggestion.style,
          Math.max(0, (this.preferences.preferredStyles.get(suggestion.style) || 0) - 0.05)
        );
      }

      // 提高置信度阈值
      if (suggestion.confidence >= this.preferences.confidenceThreshold) {
        this.preferences.confidenceThreshold = Math.min(
          0.95,
          this.preferences.confidenceThreshold + 0.01
        );
      }

    } else if (action === 'edit') {
      // 编辑行为表示部分接受，适度调整
      this.preferences.preferredSuggestionTypes.set(
        suggestion.type,
        (this.preferences.preferredSuggestionTypes.get(suggestion.type) || 0) + 0.05
      );
    }

    this.preferences.lastUpdated = Date.now();
  }

  /**
   * 根据偏好对建议进行排序
   * @param suggestions 建议数组
   * @returns 排序后的建议数组
   */
  rankSuggestions(suggestions: SuggestionOption[]): SuggestionOption[] {
    return suggestions.sort((a, b) => {
      const aScore = this.calculatePreferenceScore(a);
      const bScore = this.calculatePreferenceScore(b);
      return bScore - aScore;
    });
  }

  /**
   * 计算建议的偏好分数
   * @param suggestion 建议对象
   * @returns 偏好分数
   */
  private calculatePreferenceScore(suggestion: SuggestionOption): number {
    let score = suggestion.confidence;

    // 类型偏好加权
    const typeWeight = this.preferences.preferredSuggestionTypes.get(suggestion.type) || 0;
    score += typeWeight * 0.2;

    // 风格偏好加权
    if (suggestion.style) {
      const styleWeight = this.preferences.preferredStyles.get(suggestion.style) || 0;
      score += styleWeight * 0.1;
    }

    // 置信度阈值调整
    if (suggestion.confidence < this.preferences.confidenceThreshold) {
      score *= 0.8;
    }

    // 影响程度调整
    if (suggestion.estimatedImpact) {
      const impactMultiplier = {
        'minor': 0.9,
        'moderate': 1.0,
        'significant': 1.1
      };
      score *= impactMultiplier[suggestion.estimatedImpact];
    }

    return score;
  }

  /**
   * 获取偏好分析结果
   * @returns 分析结果
   */
  getPreferenceAnalysis(): PreferenceAnalysis {
    const totalActions = this.actionHistory.length;
    const acceptedActions = this.actionHistory.filter(a => a.action === 'accept').length;
    const acceptanceRate = totalActions > 0 ? acceptedActions / totalActions : 0;

    // 排序建议类型偏好
    const topSuggestionTypes = Array.from(this.preferences.preferredSuggestionTypes.entries())
      .map(([type, score]) => ({ type, score }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);

    // 排序风格偏好
    const topStyles = Array.from(this.preferences.preferredStyles.entries())
      .map(([style, score]) => ({ style, score }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);

    // 排序类别权重
    const categoryPreferences = Array.from(this.preferences.categoryWeights.entries())
      .map(([category, weight]) => ({ category, weight }))
      .sort((a, b) => b.weight - a.weight);

    return {
      topSuggestionTypes,
      topStyles,
      confidenceThreshold: this.preferences.confidenceThreshold,
      categoryPreferences,
      totalActions,
      acceptanceRate
    };
  }

  /**
   * 重置偏好设置
   */
  resetPreferences(): void {
    this.preferences = this.getDefaultPreferences();
    this.actionHistory = [];
    this.savePreferences();
    console.log('🔄 用户偏好已重置');
  }

  /**
   * 保存偏好到本地存储
   */
  private savePreferences(): void {
    try {
      const preferencesData = {
        preferredSuggestionTypes: Array.from(this.preferences.preferredSuggestionTypes.entries()),
        preferredStyles: Array.from(this.preferences.preferredStyles.entries()),
        confidenceThreshold: this.preferences.confidenceThreshold,
        categoryWeights: Array.from(this.preferences.categoryWeights.entries()),
        lastUpdated: this.preferences.lastUpdated
      };

      localStorage.setItem('ai-annotation-preferences', JSON.stringify(preferencesData));
      localStorage.setItem('ai-annotation-history', JSON.stringify(this.actionHistory.slice(-100))); // 只保存最近100条
    } catch (error) {
      console.error('保存用户偏好失败:', error);
    }
  }

  /**
   * 从本地存储加载偏好
   */
  private loadPreferences(): void {
    try {
      const preferencesData = localStorage.getItem('ai-annotation-preferences');
      const historyData = localStorage.getItem('ai-annotation-history');

      if (preferencesData) {
        const parsed = JSON.parse(preferencesData);
        this.preferences = {
          preferredSuggestionTypes: new Map(parsed.preferredSuggestionTypes || []),
          preferredStyles: new Map(parsed.preferredStyles || []),
          confidenceThreshold: parsed.confidenceThreshold || 0.7,
          categoryWeights: new Map(parsed.categoryWeights || []),
          lastUpdated: parsed.lastUpdated || Date.now()
        };
      }

      if (historyData) {
        this.actionHistory = JSON.parse(historyData) || [];
      }

      console.log('📥 用户偏好已加载:', {
        typePreferences: this.preferences.preferredSuggestionTypes.size,
        stylePreferences: this.preferences.preferredStyles.size,
        historySize: this.actionHistory.length
      });
    } catch (error) {
      console.error('加载用户偏好失败:', error);
      this.preferences = this.getDefaultPreferences();
    }
  }
}

// 创建全局用户偏好分析器实例
export const globalUserPreferenceAnalyzer = new UserPreferenceAnalyzer();
