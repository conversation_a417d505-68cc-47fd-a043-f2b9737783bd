"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding, Character } from '@/lib/db/dexie';

interface RelationshipManagerProps {
  isOpen: boolean;
  onClose: () => void;
  worldBuilding: WorldBuilding;
  onUpdate: (worldBuilding: WorldBuilding) => void;
  bookId: string;
}

/**
 * 关联管理对话框组件
 * 用于管理世界观元素与人物之间的关联
 */
export const RelationshipManager: React.FC<RelationshipManagerProps> = ({
  isOpen,
  onClose,
  worldBuilding,
  onUpdate,
  bookId
}) => {
  // 人物列表
  const [characters, setCharacters] = useState<Character[]>([]);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 选中的人物ID列表
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>(worldBuilding.relatedCharacterIds || []);
  // 搜索关键词
  const [searchQuery, setSearchQuery] = useState('');
  // 过滤后的人物列表
  const [filteredCharacters, setFilteredCharacters] = useState<Character[]>([]);

  // 加载人物数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadCharacters();
    }
  }, [isOpen, bookId]);

  // 过滤人物列表
  useEffect(() => {
    if (characters.length > 0) {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const filtered = characters.filter(character => 
          character.name.toLowerCase().includes(query) || 
          character.description?.toLowerCase().includes(query)
        );
        setFilteredCharacters(filtered);
      } else {
        setFilteredCharacters(characters);
      }
    }
  }, [characters, searchQuery]);

  // 加载人物列表
  const loadCharacters = async () => {
    setIsLoading(true);
    try {
      // 导入 characterRepository
      const { characterRepository } = await import('@/lib/db/repositories');

      // 获取人物列表
      const charactersData = await characterRepository.getAllByBookId(bookId);
      setCharacters(charactersData);
      setFilteredCharacters(charactersData);
    } catch (error) {
      console.error('加载人物列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理人物选择变化
  const handleCharacterSelectionChange = (characterId: string) => {
    setSelectedCharacterIds(prev => {
      if (prev.includes(characterId)) {
        return prev.filter(id => id !== characterId);
      } else {
        return [...prev, characterId];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAllCharacters = (checked: boolean) => {
    if (checked) {
      setSelectedCharacterIds(filteredCharacters.map(character => character.id!).filter(Boolean));
    } else {
      setSelectedCharacterIds([]);
    }
  };

  // 保存关联
  const saveRelationships = async () => {
    try {
      // 更新世界观元素的关联人物
      const updatedWorldBuilding = { 
        ...worldBuilding,
        relatedCharacterIds: selectedCharacterIds
      };
      
      // 更新人物的关联世界观元素
      const { characterRepository } = await import('@/lib/db/repositories');
      
      // 获取当前关联的人物
      const currentRelatedCharacterIds = worldBuilding.relatedCharacterIds || [];
      
      // 需要添加关联的人物
      const characterIdsToAdd = selectedCharacterIds.filter(id => !currentRelatedCharacterIds.includes(id));
      
      // 需要移除关联的人物
      const characterIdsToRemove = currentRelatedCharacterIds.filter(id => !selectedCharacterIds.includes(id));
      
      // 为每个新增的人物添加关联
      for (const characterId of characterIdsToAdd) {
        const character = await characterRepository.getById(characterId);
        if (character) {
          const relatedWorldBuildingIds = [...(character.relatedWorldBuildingIds || [])];
          if (!relatedWorldBuildingIds.includes(worldBuilding.id!)) {
            relatedWorldBuildingIds.push(worldBuilding.id!);
            await characterRepository.update({
              ...character,
              relatedWorldBuildingIds
            });
          }
        }
      }
      
      // 为每个移除的人物删除关联
      for (const characterId of characterIdsToRemove) {
        const character = await characterRepository.getById(characterId);
        if (character) {
          const relatedWorldBuildingIds = (character.relatedWorldBuildingIds || [])
            .filter(id => id !== worldBuilding.id);
          await characterRepository.update({
            ...character,
            relatedWorldBuildingIds
          });
        }
      }
      
      // 更新世界观元素
      onUpdate(updatedWorldBuilding);
      onClose();
    } catch (error) {
      console.error('保存关联失败:', error);
      alert('保存关联失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">管理关联</h2>
          <p className="text-sm text-gray-600">为世界观元素 "{worldBuilding.name}" 管理关联的人物</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-y-auto">
          {/* 搜索框 */}
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索人物..."
                className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* 人物列表 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredCharacters.length === 0 ? (
              <p className="text-center text-gray-500 py-4">没有找到人物</p>
            ) : (
              <div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-blue-600"
                      checked={selectedCharacterIds.length === filteredCharacters.length && filteredCharacters.length > 0}
                      onChange={(e) => handleSelectAllCharacters(e.target.checked)}
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700">全选</span>
                  </label>
                </div>
                <div className="space-y-1">
                  {filteredCharacters.map((character) => (
                    <label key={character.id} className="flex items-center p-2 hover:bg-gray-100 rounded-lg">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedCharacterIds.includes(character.id!)}
                        onChange={() => handleCharacterSelectionChange(character.id!)}
                      />
                      <div className="ml-2">
                        <span className="text-sm font-medium text-gray-700">{character.name}</span>
                        {character.description && (
                          <p className="text-xs text-gray-500 line-clamp-1">{character.description}</p>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 选中的人物 */}
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">已选择 {selectedCharacterIds.length} 个人物</h3>
            <div className="flex flex-wrap gap-2">
              {selectedCharacterIds.map(id => {
                const character = characters.find(c => c.id === id);
                return (
                  <div key={id} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm flex items-center">
                    <span>{character?.name || id}</span>
                    <button
                      className="ml-2 text-blue-500 hover:text-blue-700"
                      onClick={() => handleCharacterSelectionChange(id)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-2">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            onClick={saveRelationships}
          >
            保存关联
          </button>
        </div>
      </div>
    </div>
  );
};
