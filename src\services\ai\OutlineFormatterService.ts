"use client";

import { Outline, OutlineNodeType } from '@/factories/ui/types/outline';

/**
 * 大纲上下文模式
 */
export type OutlineContextMode = 'selected' | 'hierarchy' | 'full';

/**
 * 大纲上下文信息
 */
export interface OutlineContext {
  selectedNodes: OutlineNodeType[];
  hierarchyChains: OutlineNodeType[][];
  contextMode: OutlineContextMode;
  maxDepth?: number;
  totalNodes: number;
  lastModified?: Date;
}

/**
 * 大纲格式化服务
 * 负责将大纲数据格式化为AI可理解的文本格式
 */
export class OutlineFormatterService {

  /**
   * 根据模式格式化大纲数据为AI可理解的文本
   * @param outlines 大纲数组
   * @param selectedNodeIds 选中的节点ID数组
   * @param mode 格式化模式
   * @returns 格式化后的文本
   */
  formatForAI(
    outlines: Outline[],
    selectedNodeIds: string[],
    mode: OutlineContextMode = 'hierarchy'
  ): string {
    console.log('🔍 OutlineFormatterService.formatForAI 详细调用信息:', {
      outlinesInput: outlines?.map(o => ({ id: o.id, title: o.title, nodesCount: o.nodes?.length || 0 })),
      selectedNodeIds,
      mode,
      timestamp: new Date().toISOString()
    });

    // 数据验证
    if (!Array.isArray(outlines)) {
      console.error('❌ outlines参数不是数组:', typeof outlines, outlines);
      return '';
    }

    if (!Array.isArray(selectedNodeIds)) {
      console.error('❌ selectedNodeIds参数不是数组:', typeof selectedNodeIds, selectedNodeIds);
      return '';
    }

    // 验证大纲数据结构
    const validOutlines = outlines.filter(outline => {
      const isValid = outline && outline.id && Array.isArray(outline.nodes);
      if (!isValid) {
        console.warn('⚠️ 无效的大纲数据:', outline);
      }
      return isValid;
    });

    console.log('✅ 数据验证通过:', {
      originalCount: outlines.length,
      validCount: validOutlines.length,
      selectedNodeIds
    });

    if (!validOutlines.length || !selectedNodeIds.length) {
      console.log('⚠️ 有效大纲数据为空或未选择节点，返回空字符串');
      return '';
    }

    const selectedNodes = this.getSelectedNodes(validOutlines, selectedNodeIds);
    console.log('📋 找到的选中节点:', selectedNodes.map(n => ({ id: n.id, title: n.title, type: n.type })));

    let result = '';
    switch (mode) {
      case 'selected':
        result = this.formatSelectedNodesOnly(selectedNodes);
        break;
      case 'hierarchy':
        result = this.formatNodesWithHierarchy(validOutlines, selectedNodes);
        break;
      case 'full':
        result = this.formatFullOutlineStructure(validOutlines);
        break;
      default:
        result = this.formatNodesWithHierarchy(validOutlines, selectedNodes);
    }

    console.log('✅ 格式化结果长度:', result.length, '字符');
    console.log('📝 格式化结果预览:', result.substring(0, 200) + (result.length > 200 ? '...' : ''));

    return result;
  }

  /**
   * 构建节点的完整层级链
   * @param nodeId 节点ID
   * @param outlines 大纲数组
   * @returns 从根节点到目标节点的完整路径
   */
  buildHierarchyChain(nodeId: string, outlines: Outline[]): OutlineNodeType[] {
    const chain: OutlineNodeType[] = [];

    for (const outline of outlines) {
      const node = this.findNodeById(outline.nodes, nodeId);
      if (node) {
        this.buildChainRecursive(outline.nodes, nodeId, chain);
        break;
      }
    }

    return chain;
  }

  /**
   * 智能提取相关上下文，控制token数量
   * @param nodeIds 节点ID数组
   * @param outlines 大纲数组
   * @param maxTokens 最大token数量
   * @returns 压缩后的上下文文本
   */
  extractRelevantContext(nodeIds: string[], outlines: Outline[], maxTokens: number = 2000): string {
    const selectedNodes = this.getSelectedNodes(outlines, nodeIds);
    let context = this.formatNodesWithHierarchy(outlines, selectedNodes);

    // 如果内容过长，进行智能压缩
    if (this.estimateTokens(context) > maxTokens) {
      context = this.compressContent(context, maxTokens);
    }

    return context;
  }

  /**
   * 获取选中的节点
   */
  private getSelectedNodes(outlines: Outline[], nodeIds: string[]): OutlineNodeType[] {
    const nodes: OutlineNodeType[] = [];

    for (const outline of outlines) {
      for (const nodeId of nodeIds) {
        const node = this.findNodeById(outline.nodes, nodeId);
        if (node) {
          // 🔥 修复：允许Synopsis节点参与AI写作关联
          // Synopsis节点包含重要的开头设计、结尾设计等信息，对AI写作很有价值
          console.log(`✅ OutlineFormatterService: 添加节点: ${node.title} (${node.type}) (${node.id})`);
          nodes.push(node);
        }
      }
    }

    return nodes;
  }

  /**
   * 在节点树中查找指定ID的节点
   */
  private findNodeById(nodes: OutlineNodeType[], nodeId: string): OutlineNodeType | null {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children) {
        const found = this.findNodeById(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 递归构建层级链
   */
  private buildChainRecursive(nodes: OutlineNodeType[], targetId: string, chain: OutlineNodeType[]): boolean {
    for (const node of nodes) {
      chain.push(node);

      if (node.id === targetId) {
        return true;
      }

      if (node.children && this.buildChainRecursive(node.children, targetId, chain)) {
        return true;
      }

      chain.pop();
    }
    return false;
  }

  /**
   * 格式化选中节点（仅节点本身）
   */
  private formatSelectedNodesOnly(nodes: OutlineNodeType[]): string {
    if (!nodes.length) return '';

    const sections = nodes.map(node => {
      let content = `【${this.getNodeTypeLabel(node.type)}】${node.title}\n`;

      if (node.description) {
        content += `描述：${node.description}\n`;
      }

      if (node.wordCount) {
        content += `字数：${node.wordCount}\n`;
      }

      // 添加专有字段
      content += this.formatNodeSpecificFields(node);

      // 添加创作建议
      if ((node as any).creativeNotes && (node as any).creativeNotes.trim()) {
        content += `💡 创作建议：${(node as any).creativeNotes}\n`;
      }

      return content;
    });

    return sections.join('\n');
  }

  /**
   * 格式化节点专有字段
   * @param node 节点数据
   * @returns 格式化后的专有字段内容
   */
  private formatNodeSpecificFields(node: any): string {
    let content = '';

    // 章节专有字段
    if (node.type === 'chapter') {
      if (node.chapterStyle) {
        content += `🎨 写作风格：${node.chapterStyle}\n`;
      }
      if (node.chapterTechniques && Array.isArray(node.chapterTechniques) && node.chapterTechniques.length > 0) {
        content += `✍️ 写作手法：${node.chapterTechniques.join('、')}\n`;
      }
      if (node.chapterGoals) {
        content += `🎯 章节目标：${node.chapterGoals}\n`;
      }
    }

    // 🔥 Synopsis节点专有字段
    if (node.type === 'synopsis') {
      if (node.synopsisBrainhole) {
        content += `🧠 脑洞设定：${node.synopsisBrainhole}\n`;
      }
      if (node.synopsisGenre) {
        content += `🎭 故事类型：${node.synopsisGenre}\n`;
      }
      if (node.synopsisOpening) {
        content += `🚀 开头设计：${node.synopsisOpening}\n`;
      }
      if (node.synopsisCoreOutline) {
        content += `📖 核心梗概：${node.synopsisCoreOutline}\n`;
      }
      if (node.synopsisEnding) {
        content += `🎯 结尾设计：${node.synopsisEnding}\n`;
      }
      if (node.synopsisStoryDescription) {
        content += `📝 故事描述：${node.synopsisStoryDescription}\n`;
      }
      if (node.synopsisAceReferences) {
        content += `🎨 ACE引用：${node.synopsisAceReferences}\n`;
      }
    }

    // 剧情节点专有字段
    if (node.type === 'plot') {
      if (node.plotType) {
        const plotTypeLabel = this.getPlotTypeLabel(node.plotType);
        content += `🎭 剧情类型：${plotTypeLabel}\n`;
      }
      if (node.relatedCharacters && Array.isArray(node.relatedCharacters) && node.relatedCharacters.length > 0) {
        content += `👥 关联角色：${node.relatedCharacters.join('、')}\n`;
      }
      if (node.plotPoints && Array.isArray(node.plotPoints) && node.plotPoints.length > 0) {
        content += `📋 剧情点：\n`;
        node.plotPoints.forEach((point: any, index: number) => {
          const pointText = typeof point === 'string' ? point :
                           typeof point === 'object' && point?.content ? point.content :
                           typeof point === 'object' ? JSON.stringify(point) : String(point);
          content += `  ${index + 1}. ${pointText}\n`;

          // 添加写作指导信息
          if (typeof point === 'object' && point?.writingGuidance) {
            content += `     💡 写作指导：${point.writingGuidance}\n`;
          }

          // 添加写作风格方法指导
          if (typeof point === 'object' && point?.styleMethod) {
            content += `     🎨 写作风格：${point.styleMethod.technique} | ${point.styleMethod.style} | ${point.styleMethod.tone}\n`;
            content += `     📐 视角重点：${point.styleMethod.perspective} | ${point.styleMethod.emphasis}\n`;
          }

          // 添加格式规范
          if (typeof point === 'object' && point?.formatSpecs) {
            content += `     📏 字数要求：${point.formatSpecs.wordCount?.min}-${point.formatSpecs.wordCount?.max}字（目标${point.formatSpecs.wordCount?.target}字）\n`;
            content += `     📝 格式规范：${point.formatSpecs.paragraphRules?.paragraphBreakRules} | ${point.formatSpecs.punctuationRules?.dialogueFormat}\n`;

            // 添加新增的段落规范字段
            if (point.formatSpecs.paragraphRules?.conflictHandling) {
              content += `     ⚔️ 冲突处理：${point.formatSpecs.paragraphRules.conflictHandling}\n`;
            }
            if (point.formatSpecs.paragraphRules?.actionDialogueFlow) {
              content += `     🎭 对话行动流程：${point.formatSpecs.paragraphRules.actionDialogueFlow}\n`;
            }
            if (point.formatSpecs.paragraphRules?.mandatoryBreaks) {
              content += `     📄 强制换行：${point.formatSpecs.paragraphRules.mandatoryBreaks}\n`;
            }

            // 添加新增的标点规范字段
            if (point.formatSpecs.punctuationRules?.conflictPunctuation) {
              content += `     ⚡ 冲突标点：${point.formatSpecs.punctuationRules.conflictPunctuation}\n`;
            }
            if (point.formatSpecs.punctuationRules?.naturalFlow) {
              content += `     🌊 自然流畅：${point.formatSpecs.punctuationRules.naturalFlow}\n`;
            }

            // 添加新增的换行规范字段
            if (point.formatSpecs.lineBreakRules?.conflictEscalation) {
              content += `     🔥 冲突升级换行：${point.formatSpecs.lineBreakRules.conflictEscalation}\n`;
            }
            if (point.formatSpecs.lineBreakRules?.actionEmphasis) {
              content += `     💪 行动强调换行：${point.formatSpecs.lineBreakRules.actionEmphasis}\n`;
            }
            if (point.formatSpecs.lineBreakRules?.emotionShift) {
              content += `     💭 情绪转折换行：${point.formatSpecs.lineBreakRules.emotionShift}\n`;
            }
            if (point.formatSpecs.lineBreakRules?.prohibitedMerging) {
              content += `     🚫 禁止合并：${point.formatSpecs.lineBreakRules.prohibitedMerging}\n`;
            }
          }
        });
      }
    }

    // 对话设计专有字段
    if (node.type === 'dialogue') {
      if (node.dialogueScene) {
        content += `🎬 对话场景：${node.dialogueScene}\n`;
      }
      if (node.participants && Array.isArray(node.participants) && node.participants.length > 0) {
        content += `👥 参与角色：${node.participants.join('、')}\n`;
      }
      if (node.dialoguePurpose) {
        content += `🎯 对话目的：${node.dialoguePurpose}\n`;
      }
      if (node.dialogueContent && Array.isArray(node.dialogueContent) && node.dialogueContent.length > 0) {
        content += `💬 对话内容：\n`;
        node.dialogueContent.forEach((dialogue: any, index: number) => {
          if (typeof dialogue === 'string') {
            content += `  ${index + 1}. ${dialogue}\n`;
          } else if (typeof dialogue === 'object' && dialogue?.speaker && dialogue?.text) {
            content += `  ${index + 1}. ${dialogue.speaker}: ${dialogue.text}\n`;
          } else if (typeof dialogue === 'object' && dialogue?.content) {
            content += `  ${index + 1}. ${dialogue.content}\n`;
          } else {
            content += `  ${index + 1}. ${JSON.stringify(dialogue)}\n`;
          }
        });
      }
    }

    return content;
  }

  /**
   * 获取剧情类型标签
   * @param plotType 剧情类型
   * @returns 中文标签
   */
  private getPlotTypeLabel(plotType: string): string {
    const labels: { [key: string]: string } = {
      'conflict': '冲突',
      'twist': '转折',
      'climax': '高潮',
      'resolution': '解决'
    };
    return labels[plotType] || plotType;
  }

  /**
   * 格式化节点及其层级关系
   */
  private formatNodesWithHierarchy(outlines: Outline[], selectedNodes: OutlineNodeType[]): string {
    if (!selectedNodes.length) return '';

    let content = '【大纲层级结构】\n';

    // 为每个选中节点构建层级链
    for (const node of selectedNodes) {
      const chain = this.buildHierarchyChain(node.id, outlines);
      if (chain.length > 0) {
        content += this.formatHierarchyChain(chain) + '\n\n';
      }
    }

    return content;
  }

  /**
   * 格式化完整大纲结构
   */
  private formatFullOutlineStructure(outlines: Outline[]): string {
    let content = '【完整大纲结构】\n';

    for (const outline of outlines) {
      content += `\n大纲：${outline.title}\n`;
      content += this.formatNodeTree(outline.nodes, 0);
    }

    return content;
  }

  /**
   * 格式化层级链
   */
  private formatHierarchyChain(chain: OutlineNodeType[]): string {
    return chain.map((node, index) => {
      const indent = '  '.repeat(index);
      const icon = this.getNodeIcon(node.type);
      let content = `${indent}${icon} ${node.title}`;

      if (node.description && index === chain.length - 1) {
        content += `\n${indent}  描述：${node.description}`;
      }

      return content;
    }).join('\n');
  }

  /**
   * 格式化节点树
   */
  private formatNodeTree(nodes: OutlineNodeType[], level: number): string {
    const indent = '  '.repeat(level);

    return nodes.map(node => {
      const icon = this.getNodeIcon(node.type);
      let content = `${indent}${icon} ${node.title}`;

      if (node.description) {
        content += ` - ${node.description.substring(0, 100)}${node.description.length > 100 ? '...' : ''}`;
      }

      if (node.children && node.children.length > 0) {
        content += '\n' + this.formatNodeTree(node.children, level + 1);
      }

      return content;
    }).join('\n');
  }

  /**
   * 获取节点类型图标
   */
  private getNodeIcon(type: string): string {
    const icons: Record<string, string> = {
      'volume': '📚',
      'event': '⚡',
      'chapter': '📖',
      'plot': '🎬',
      'dialogue': '💬',
      'synopsis': '📋',
      'section': '📝',
      'character': '👤',
      // 保留旧类型的兼容性
      'scene': '🎬'
    };
    return icons[type] || '💬';
  }

  /**
   * 获取节点类型标签
   */
  private getNodeTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'volume': '总纲/卷',
      'event': '事件刚',
      'chapter': '章节',
      'scene': '场景',
      'section': '段落',
      'character': '人物',
      'plot': '情节',
      'dialogue': '对话节点',
      'synopsis': '核心故事梗概'
    };
    return labels[type] || '对话节点';
  }

  /**
   * 估算文本的token数量（粗略估算）
   */
  private estimateTokens(text: string): number {
    // 中文字符按1.5个token计算，英文单词按1个token计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return Math.ceil(chineseChars * 1.5 + englishWords);
  }

  /**
   * 压缩内容以控制token数量
   */
  private compressContent(content: string, maxTokens: number): string {
    const lines = content.split('\n');
    let compressedContent = '';
    let currentTokens = 0;

    for (const line of lines) {
      const lineTokens = this.estimateTokens(line);
      if (currentTokens + lineTokens > maxTokens) {
        compressedContent += '\n[内容过长，已省略部分内容...]';
        break;
      }
      compressedContent += line + '\n';
      currentTokens += lineTokens;
    }

    return compressedContent;
  }
}

// 导出服务实例
export const outlineFormatterService = new OutlineFormatterService();
