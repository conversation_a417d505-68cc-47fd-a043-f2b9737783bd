import { LayoutConfig } from '../components/OutlineManager/tools/LayoutControlPanel';
import { NodePositions } from './layoutAnimationController';

/**
 * 布局历史条目接口
 */
export interface LayoutHistoryEntry {
  /**
   * 时间戳
   */
  timestamp: number;
  
  /**
   * 布局配置
   */
  layoutConfig: LayoutConfig;
  
  /**
   * 节点位置
   */
  nodePositions: NodePositions;
  
  /**
   * 可选的描述
   */
  description?: string;
}

/**
 * 布局历史管理器
 * 管理布局历史记录，支持撤销/重做功能
 */
export class LayoutHistoryManager {
  /**
   * 历史记录数组
   */
  private history: LayoutHistoryEntry[] = [];
  
  /**
   * 当前历史索引
   */
  private currentIndex: number = -1;
  
  /**
   * 最大历史记录数
   */
  private maxHistorySize: number;
  
  /**
   * 构造函数
   * @param maxHistorySize 最大历史记录数，默认为10
   */
  constructor(maxHistorySize: number = 10) {
    this.maxHistorySize = maxHistorySize;
  }
  
  /**
   * 添加历史记录
   * @param entry 历史记录条目
   */
  public addEntry(entry: LayoutHistoryEntry): void {
    // 如果当前不是最新记录，删除当前索引之后的所有记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }
    
    // 添加新记录
    this.history.push(entry);
    this.currentIndex = this.history.length - 1;
    
    // 如果超出最大历史记录数，删除最早的记录
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.currentIndex--;
    }
  }
  
  /**
   * 撤销到上一个布局
   * @returns 上一个布局记录，如果没有则返回null
   */
  public undo(): LayoutHistoryEntry | null {
    // 如果没有历史记录或已经是第一个记录，返回null
    if (this.currentIndex <= 0 || this.history.length === 0) {
      return null;
    }
    
    // 移动到上一个记录
    this.currentIndex--;
    return this.history[this.currentIndex];
  }
  
  /**
   * 重做到下一个布局
   * @returns 下一个布局记录，如果没有则返回null
   */
  public redo(): LayoutHistoryEntry | null {
    // 如果没有历史记录或已经是最后一个记录，返回null
    if (this.currentIndex >= this.history.length - 1 || this.history.length === 0) {
      return null;
    }
    
    // 移动到下一个记录
    this.currentIndex++;
    return this.history[this.currentIndex];
  }
  
  /**
   * 获取当前布局记录
   * @returns 当前布局记录，如果没有则返回null
   */
  public getCurrentEntry(): LayoutHistoryEntry | null {
    if (this.currentIndex < 0 || this.history.length === 0) {
      return null;
    }
    
    return this.history[this.currentIndex];
  }
  
  /**
   * 获取所有历史记录
   * @returns 历史记录数组
   */
  public getHistory(): LayoutHistoryEntry[] {
    return [...this.history];
  }
  
  /**
   * 获取历史记录数量
   * @returns 历史记录数量
   */
  public getHistoryLength(): number {
    return this.history.length;
  }
  
  /**
   * 获取当前历史索引
   * @returns 当前历史索引
   */
  public getCurrentIndex(): number {
    return this.currentIndex;
  }
  
  /**
   * 清空历史记录
   */
  public clearHistory(): void {
    this.history = [];
    this.currentIndex = -1;
  }
  
  /**
   * 检查是否可以撤销
   * @returns 是否可以撤销
   */
  public canUndo(): boolean {
    return this.currentIndex > 0;
  }
  
  /**
   * 检查是否可以重做
   * @returns 是否可以重做
   */
  public canRedo(): boolean {
    return this.currentIndex < this.history.length - 1;
  }
  
  /**
   * 获取历史记录缩略图数据
   * 用于在UI中显示历史记录预览
   * 
   * @param count 要获取的记录数量，默认为5
   * @returns 最近的历史记录数组
   */
  public getHistoryThumbnails(count: number = 5): LayoutHistoryEntry[] {
    // 计算起始索引，确保不会越界
    const startIndex = Math.max(0, this.history.length - count);
    
    // 返回最近的记录
    return this.history.slice(startIndex);
  }
  
  /**
   * 跳转到指定索引的历史记录
   * @param index 历史记录索引
   * @returns 指定索引的历史记录，如果索引无效则返回null
   */
  public jumpToIndex(index: number): LayoutHistoryEntry | null {
    // 检查索引是否有效
    if (index < 0 || index >= this.history.length) {
      return null;
    }
    
    // 更新当前索引
    this.currentIndex = index;
    return this.history[this.currentIndex];
  }
  
  /**
   * 保存历史记录到本地存储
   * @param key 存储键名
   */
  public saveToLocalStorage(key: string = 'outlineLayoutHistory'): void {
    try {
      localStorage.setItem(key, JSON.stringify({
        history: this.history,
        currentIndex: this.currentIndex
      }));
    } catch (error) {
      console.error('保存布局历史记录失败:', error);
    }
  }
  
  /**
   * 从本地存储加载历史记录
   * @param key 存储键名
   * @returns 是否成功加载
   */
  public loadFromLocalStorage(key: string = 'outlineLayoutHistory'): boolean {
    try {
      const data = localStorage.getItem(key);
      if (!data) return false;
      
      const parsed = JSON.parse(data);
      this.history = parsed.history;
      this.currentIndex = parsed.currentIndex;
      
      return true;
    } catch (error) {
      console.error('加载布局历史记录失败:', error);
      return false;
    }
  }
}
