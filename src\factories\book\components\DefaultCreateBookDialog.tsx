"use client";

import React, { useState, useEffect, useRef } from 'react';
import { ICreateBookDialogComponent } from '../interfaces';
import { createAnimationFactory } from '@/factories/animation';

/**
 * 默认创建书籍对话框组件实现
 */
export class DefaultCreateBookDialogComponent implements ICreateBookDialogComponent {
  private isOpen: boolean = false;
  private createBookCallback: ((title: string, description: string) => void) | null = null;
  private cancelCallback: (() => void) | null = null;
  
  /**
   * 设置是否显示对话框
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }
  
  /**
   * 设置创建书籍回调函数
   * @param callback 回调函数，参数为书籍标题和描述
   */
  onCreateBook(callback: (title: string, description: string) => void): void {
    this.createBookCallback = callback;
  }
  
  /**
   * 设置取消回调函数
   * @param callback 回调函数
   */
  onCancel(callback: () => void): void {
    this.cancelCallback = callback;
  }
  
  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const CreateBookDialog = () => {
      const [isOpen, setIsOpen] = useState(this.isOpen);
      const [title, setTitle] = useState('');
      const [description, setDescription] = useState('');
      const [isSubmitting, setIsSubmitting] = useState(false);
      const [titleError, setTitleError] = useState('');
      const dialogRef = useRef<HTMLDivElement>(null);
      
      // 创建动画工厂
      const animationFactory = createAnimationFactory();
      const fadeAnimation = animationFactory.createFadeAnimation('none', 300, 0, isOpen);
      const scaleAnimation = animationFactory.createScaleAnimation(0.9, 1, 300, 0, isOpen);
      
      // 监听isOpen变化
      useEffect(() => {
        setIsOpen(this.isOpen);
      }, [this.isOpen]);
      
      // 点击外部关闭弹窗
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
            handleCancel();
          }
        };
        
        if (isOpen) {
          document.addEventListener('mousedown', handleClickOutside);
        }
        
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isOpen]);
      
      // 处理取消
      const handleCancel = () => {
        setIsOpen(false);
        this.setIsOpen(false);
        
        if (this.cancelCallback) {
          this.cancelCallback();
        }
      };
      
      // 处理创建书籍
      const handleCreateBook = () => {
        // 验证标题
        if (!title.trim()) {
          setTitleError('请输入作品标题');
          return;
        }
        
        setIsSubmitting(true);
        
        // 模拟创建书籍
        setTimeout(() => {
          if (this.createBookCallback) {
            this.createBookCallback(title, description);
          }
          
          setIsSubmitting(false);
          setIsOpen(false);
          this.setIsOpen(false);
          
          // 重置表单
          setTitle('');
          setDescription('');
          setTitleError('');
        }, 500);
      };
      
      if (!isOpen) return null;
      
      // 获取动画样式
      const fadeStyle = fadeAnimation.getStyle();
      const scaleStyle = scaleAnimation.getStyle();
      
      return (
        <div 
          className="fixed inset-0 flex items-center justify-center z-50 transition-opacity duration-300"
          style={{ 
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            opacity: isOpen ? 1 : 0,
            ...fadeStyle
          }}
        >
          <div 
            ref={dialogRef}
            className="bg-white rounded-lg shadow-xl w-96 max-w-full transition-transform duration-300"
            style={{ 
              backgroundColor: 'var(--color-white)',
              borderColor: 'var(--color-secondary)',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              ...scaleStyle
            }}
          >
            <div className="flex justify-between items-center p-4 border-b" style={{ borderColor: 'var(--color-secondary)' }}>
              <h2 className="text-xl font-medium" style={{ color: 'var(--color-primary)' }}>创建新作品</h2>
              <button 
                onClick={handleCancel}
                className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                disabled={isSubmitting}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text-primary)' }}>
                  作品标题 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => {
                    setTitle(e.target.value);
                    if (e.target.value.trim()) {
                      setTitleError('');
                    }
                  }}
                  className={`w-full p-2 border rounded ${titleError ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="请输入作品标题"
                  disabled={isSubmitting}
                />
                {titleError && (
                  <p className="mt-1 text-sm text-red-500">{titleError}</p>
                )}
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text-primary)' }}>
                  作品简介
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={4}
                  placeholder="请输入作品简介（选填）"
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 border border-gray-300 rounded transition-colors duration-200"
                  style={{ color: 'var(--color-text-primary)' }}
                  disabled={isSubmitting}
                >
                  取消
                </button>
                <button
                  onClick={handleCreateBook}
                  className="px-4 py-2 rounded transition-colors duration-200"
                  style={{ 
                    backgroundColor: 'var(--color-primary)',
                    color: 'white',
                    opacity: isSubmitting ? 0.7 : 1
                  }}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '创建中...' : '创建作品'}
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    };
    
    return <CreateBookDialog />;
  }
}
