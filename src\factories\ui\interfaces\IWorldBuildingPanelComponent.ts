import { IUIComponent } from './IUIComponent';
import { WorldBuilding } from '@/lib/db/dexie';

/**
 * 世界观面板组件接口
 */
export interface IWorldBuildingPanelComponent extends IUIComponent {
  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void;
  
  /**
   * 设置是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void;
  
  /**
   * 设置创建世界观回调函数
   * @param handler 创建世界观回调函数
   */
  onCreate(handler: (worldBuilding: WorldBuilding) => void): void;
  
  /**
   * 设置更新世界观回调函数
   * @param handler 更新世界观回调函数
   */
  onUpdate(handler: (worldBuilding: WorldBuilding) => void): void;
  
  /**
   * 设置删除世界观回调函数
   * @param handler 删除世界观回调函数
   */
  onDelete(handler: (worldBuildingId: string) => void): void;
  
  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void;
}
