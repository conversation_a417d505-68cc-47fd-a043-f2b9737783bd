"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { CustomDropdown } from './CustomDropdown';
import { CategoryFieldsEdit } from './CategoryFields/CategoryFieldsEdit';
import { getCategoryOptions, getCategoryMainInfo, getCategoryLabel } from './CategoryFieldsUtils';
import { WorldBuildingFieldAIButton } from './WorldBuildingFieldAIButton';

interface WorldBuildingEditFormProps {
  editingWorldBuilding: WorldBuilding;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSave: () => void;
  onCancel: () => void;
}

/**
 * 世界观编辑表单组件 - 分步骤实现
 */
export const WorldBuildingEditForm: React.FC<WorldBuildingEditFormProps> = ({
  editingWorldBuilding,
  handleInputChange,
  handleSave,
  onCancel
}) => {
  // 当前步骤
  const [currentStep, setCurrentStep] = useState(1);
  // 总步骤数
  const totalSteps = 3;
  // 主类信息
  const [mainCategoryInfo, setMainCategoryInfo] = useState<{
    mainCategoryId: string;
    mainCategoryLabel: string;
  } | null>(null);

  // 获取类别选项
  const categoryOptions = getCategoryOptions();

  // 当类别变化时，更新主类信息
  useEffect(() => {
    if (editingWorldBuilding.category) {
      const info = getCategoryMainInfo(editingWorldBuilding.category);
      setMainCategoryInfo(info);
    } else {
      setMainCategoryInfo(null);
    }
  }, [editingWorldBuilding.category]);

  // 自动调整所有文本区域的高度
  useEffect(() => {
    // 在组件加载和步骤切换时调整所有文本区域的高度
    const adjustTextareaHeights = () => {
      setTimeout(() => {
        document.querySelectorAll('textarea').forEach((textarea) => {
          textarea.style.height = 'auto';
          textarea.style.height = `${textarea.scrollHeight}px`;
        });
      }, 100);
    };

    adjustTextareaHeights();

    // 监听窗口大小变化，重新调整高度
    window.addEventListener('resize', adjustTextareaHeights);

    return () => {
      window.removeEventListener('resize', adjustTextareaHeights);
    };
  }, [currentStep, editingWorldBuilding]);

  // 处理下一步
  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSave();
    }
  };

  // 处理上一步
  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onCancel();
    }
  };

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-between mb-8">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;

          return (
            <div key={stepNumber} className="flex items-center">
              {/* 连接线 */}
              {index > 0 && (
                <div
                  className={`h-1 w-16 mx-2 ${
                    isCompleted ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              )}

              {/* 步骤圆圈 */}
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  isActive
                    ? 'bg-blue-500 text-white'
                    : isCompleted
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                }`}
              >
                {isCompleted ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  stepNumber
                )}
              </div>

              {/* 步骤标题 */}
              <span className={`ml-2 text-sm ${isActive ? 'font-medium text-blue-500' : 'text-gray-500'}`}>
                {stepNumber === 1 && '基本信息'}
                {stepNumber === 2 && '详细属性'}
                {stepNumber === 3 && '关联信息'}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  // 渲染步骤1：基本信息
  const renderStep1 = () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
          <div className="bg-white rounded-lg p-4 border space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={editingWorldBuilding.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                为世界观元素起一个简洁明了的名称，例如"精灵王国"、"魔法学院"等
              </p>
            </div>
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                类别 <span className="text-red-500">*</span>
              </label>
              <CustomDropdown
                options={categoryOptions}
                value={editingWorldBuilding.category || ''}
                onChange={(value) => {
                  handleInputChange({
                    target: { name: 'category', value }
                  } as React.ChangeEvent<HTMLSelectElement>);
                }}
                maxHeight={250}
                enableSearch={true}
                placeholder="选择类别..."
              />
              <p className="mt-1 text-xs text-gray-500">
                选择一个最适合的类别，这将决定后续需要填写的详细属性
              </p>
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                简要描述
                <div className="flex ml-auto">
                  {/* AI生成按钮 */}
                  <WorldBuildingFieldAIButton
                    worldBuilding={editingWorldBuilding}
                    fieldName="description"
                    fieldDisplayName="简要描述"
                    bookId={editingWorldBuilding.bookId}
                    mode="generate"
                    onSave={(updatedWorldBuilding) => {
                      // 更新表单数据
                      const event = {
                        target: {
                          name: 'description',
                          value: updatedWorldBuilding.description
                        }
                      } as React.ChangeEvent<HTMLTextAreaElement>;
                      handleInputChange(event);

                      // 在下一个渲染周期调整所有文本区域的高度
                      setTimeout(() => {
                        document.querySelectorAll('textarea').forEach((textarea) => {
                          textarea.style.height = 'auto';
                          textarea.style.height = `${textarea.scrollHeight}px`;
                        });
                      }, 0);
                    }}
                  />

                  {/* AI更新按钮 */}
                  <WorldBuildingFieldAIButton
                    worldBuilding={editingWorldBuilding}
                    fieldName="description"
                    fieldDisplayName="简要描述"
                    bookId={editingWorldBuilding.bookId}
                    mode="update"
                    onSave={(updatedWorldBuilding) => {
                      // 更新表单数据
                      const event = {
                        target: {
                          name: 'description',
                          value: updatedWorldBuilding.description
                        }
                      } as React.ChangeEvent<HTMLTextAreaElement>;
                      handleInputChange(event);

                      // 在下一个渲染周期调整所有文本区域的高度
                      setTimeout(() => {
                        document.querySelectorAll('textarea').forEach((textarea) => {
                          textarea.style.height = 'auto';
                          textarea.style.height = `${textarea.scrollHeight}px`;
                        });
                      }, 0);
                    }}
                  />
                </div>
              </label>
              <textarea
                id="description"
                name="description"
                value={editingWorldBuilding.description}
                onChange={(e) => {
                  handleInputChange(e);
                  // 自动调整高度
                  e.target.style.height = 'auto';
                  e.target.style.height = `${e.target.scrollHeight}px`;
                }}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 min-h-[150px] resize-none overflow-hidden"
                placeholder="输入简要描述..."
                ref={(el) => {
                  // 初始化时调整高度
                  if (el) {
                    setTimeout(() => {
                      el.style.height = 'auto';
                      el.style.height = `${el.scrollHeight}px`;
                    }, 0);
                  }
                }}
              />
              <p className="mt-1 text-xs text-gray-500">
                简要描述这个世界观元素的主要特点和在故事中的作用
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染步骤2：详细属性
  const renderStep2 = () => {
    return (
      <div className="space-y-6">
        {/* 类别特定字段 */}
        {editingWorldBuilding.category ? (
          <div>
            {/* 显示主类和子类信息 */}
            {mainCategoryInfo && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <span className="text-sm font-medium text-gray-500 mr-2">主类别:</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    {mainCategoryInfo.mainCategoryLabel}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-500 mr-2">子类别:</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    {getCategoryLabel(editingWorldBuilding.category)}
                  </span>
                </div>
              </div>
            )}

            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {getCategoryLabel(editingWorldBuilding.category)}特有属性
            </h3>
            <div className="bg-white rounded-lg p-4 border">
              <CategoryFieldsEdit
                editingWorldBuilding={editingWorldBuilding}
                handleInputChange={handleInputChange}
              />
            </div>
          </div>
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-yellow-700">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>请先在上一步选择类别</span>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 渲染步骤3：关联信息
  const renderStep3 = () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">关联信息</h3>
          <div className="bg-white rounded-lg p-4 border space-y-4">
            {/* 这里可以添加与其他世界观元素的关联，如相关人物、相关地点等 */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                备注
              </label>
              <textarea
                id="notes"
                name="notes"
                value={editingWorldBuilding.notes || ''}
                onChange={(e) => {
                  handleInputChange(e);
                  // 自动调整高度
                  e.target.style.height = 'auto';
                  e.target.style.height = `${e.target.scrollHeight}px`;
                }}
                rows={5}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 min-h-[120px] resize-none overflow-hidden"
                placeholder="输入备注..."
                ref={(el) => {
                  // 初始化时调整高度
                  if (el) {
                    setTimeout(() => {
                      el.style.height = 'auto';
                      el.style.height = `${el.scrollHeight}px`;
                    }, 0);
                  }
                }}
              />
              <p className="mt-1 text-xs text-gray-500">
                添加任何其他相关信息或备注
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      default:
        return null;
    }
  };

  // 渲染导航按钮
  const renderNavButtons = () => {
    return (
      <div className="flex justify-between mt-8">
        <button
          type="button"
          onClick={handlePrevStep}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {currentStep === 1 ? '取消' : '上一步'}
        </button>
        <button
          type="button"
          onClick={handleNextStep}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {currentStep === totalSteps ? '保存' : '下一步'}
        </button>
      </div>
    );
  };

  return (
    <div className="max-w-3xl mx-auto">
      {renderStepIndicator()}
      {renderStepContent()}
      {renderNavButtons()}
    </div>
  );
};
