"use client";

import React, { ReactNode } from 'react';
import { FeatherPenIcon } from '../icons';

export type PanelSize = 'small' | 'medium' | 'large';

interface EnhancedPanelProps {
  title: string;
  isOpen: boolean;
  size?: PanelSize;
  content?: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
  onClose?: () => void;
  className?: string;
  fixedHeight?: boolean;
  backgroundColor?: string;
  width?: string;
  height?: string;
  enhanced?: boolean; // 是否启用增强效果
  literaryTheme?: boolean; // 是否使用文学主题
}

/**
 * 增强版面板组件
 * 支持文学创作主题的装饰元素和微交互动画
 */
const EnhancedPanel: React.FC<EnhancedPanelProps> = ({
  title,
  isOpen,
  size = 'medium',
  content,
  header,
  footer,
  onClose,
  className = '',
  fixedHeight = false,
  backgroundColor = 'var(--color-white, #FFFFFF)',
  width = '80%',
  height = '70%',
  enhanced = true,
  literaryTheme = true
}) => {
  if (!isOpen) return null;

  // 获取面板尺寸样式
  const sizeStyles = {
    small: 'max-w-md',
    medium: 'max-w-2xl',
    large: 'max-w-4xl',
  };

  const enhancedClass = enhanced ? 'panel-enhanced' : '';
  const literaryClass = literaryTheme ? 'panel-literary' : '';

  return (
    <div 
      className={`panel-overlay ${enhancedClass} ${literaryClass}`} 
      onClick={onClose}
    >
      <div
        className={`panel-container ${sizeStyles[size]} ${enhancedClass} ${literaryClass} ${className}`}
        style={{
          backgroundColor,
          width,
          height: fixedHeight ? height : 'auto',
          maxHeight: fixedHeight ? height : '90vh',
          animation: enhanced ? 'panelEnter 400ms cubic-bezier(0.34, 1.56, 0.64, 1)' : 'none'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 装饰元素 - 四角花纹 */}
        {enhanced && literaryTheme && (
          <>
            <div className="panel-corner-decoration top-left" />
            <div className="panel-corner-decoration top-right" />
            <div className="panel-corner-decoration bottom-left" />
            <div className="panel-corner-decoration bottom-right" />
          </>
        )}

        {/* 面板头部 */}
        <div className="panel-header">
          {header || (
            <>
              <div className="panel-title-container">
                {literaryTheme && (
                  <FeatherPenIcon 
                    size="md" 
                    animated={enhanced} 
                    className="panel-title-icon" 
                  />
                )}
                <h2 className="panel-title">{title}</h2>
              </div>
              <button
                className="panel-close-button"
                onClick={onClose}
                aria-label="关闭面板"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </>
          )}
        </div>

        {/* 装饰分割线 */}
        {enhanced && literaryTheme && (
          <div className="panel-divider-decorative" />
        )}

        {/* 面板内容 */}
        <div className="panel-content">
          {content}
        </div>

        {/* 面板底部 */}
        {footer && (
          <>
            {enhanced && literaryTheme && (
              <div className="panel-divider-decorative" />
            )}
            <div className="panel-footer">
              {footer}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default EnhancedPanel;
