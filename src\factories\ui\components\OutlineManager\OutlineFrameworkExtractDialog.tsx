"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Outline } from '../../types/outline';
import { Chapter } from '@/lib/db/dexie';
import { chapterRepository } from '@/lib/db/repositories';
import ChapterSelector from './ChapterSelector';
import FrameworkAnalysisResult from './FrameworkAnalysisResult';
import ExtractionModeSelector from './ExtractionModeSelector';
import ProgressIndicator from './ProgressIndicator';
import { OutlineFrameworkExtractService, ExtractionMode, ExtractionConfig } from './OutlineFrameworkExtractService';

interface OutlineFrameworkExtractDialogProps {
  isOpen: boolean;
  onClose: () => void;
  outline: Outline | null;
  bookId: string;
  buttonPosition?: { x: number; y: number };
}

interface OutlineFrameworkResult {
  id: string;
  frameworkName: string;
  frameworkPattern: string;  // 变量格式，如："{主角} {穿越}后获得{系统}"
  frameworkVariables: string[]; // 提取的变量列表，如：["主角", "穿越", "系统"]
  patternType: string;       // 模式类型，如：穿越系统流
  extractedFrom: {
    chapterIds: string[];
    chapterTitles: string[];
    extractDate: Date;
  };
  // 简化为三个核心分析维度
  plotAnalysis: {
    storyStructure: string;    // 整合：章节安排 + 情节推进 + 场景构建
    conflictDesign: string;    // 整合：冲突类型 + 升级方法 + 解决技巧
    rhythmControl: string;     // 整合：转场技巧 + 节奏控制 + 连接方法
    // 新增写作指导相关字段
    plotPointsWithGuidance?: Array<{
      content: string;              // 剧情点内容
      specificDescription: string;  // 该章节具体描写的内容
      avoidanceGuidance: string;    // 预测应该避免的描写
    }>;
  };
  dialogueAnalysis: {
    // 保留核心旧字段（标记为可选，向后兼容）
    dialogueStructure?: string; // 对话的具体结构和格式
    plotAdvancement?: string;   // 对话推进情节的具体方法
    writingTechniques?: string; // 对话写作的实际技巧
    toneCharacteristics?: string[]; // 语气特征分析
    stylePatterns?: string[];       // 行文框架模式
    literaryAnalysis?: string;      // 文学化分析

    // 新增：完整对话提取
    completeDialogues: Array<{
      content: string;      // 对话内容
      speaker?: string;     // 说话人
      context: string;      // 上下文
      position: number;     // 位置
    }>;

    // 新增：风格分析
    styleAnalysis: {
      dialogueStyle: string;        // 对话风格特征
      characterVoice: string;       // 角色语言特色
      emotionalTone: string;        // 情感基调
      technicalFeatures: string;   // 写作技巧特征
    };
  };
  styleAnalysis: {
    writingStyle: string;      // 整合：写作风格 + 技巧手法 + 设计逻辑
    expressionFeatures: string; // 整合：表现特色 + 技术手法运用
    practicalMethods: string;   // 整合：实用方法 + 应用技巧
  };
  reusablePatterns: string[];
  applicationMethods: string[];
  metadata: {
    analysisDepth: 'basic' | 'detailed' | 'comprehensive';
    confidence: number;
    tags: string[];
    category: string;
  };
}

type DialogStep = 'selection' | 'analyzing' | 'result';

const OutlineFrameworkExtractDialog: React.FC<OutlineFrameworkExtractDialogProps> = ({
  isOpen,
  onClose,
  outline,
  bookId,
  buttonPosition
}) => {
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<DialogStep>('selection');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<OutlineFrameworkResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  // 新增状态：提取模式和进度跟踪
  const [extractionMode, setExtractionMode] = useState<ExtractionMode>(ExtractionMode.SUMMARY);
  const [currentProcessingChapter, setCurrentProcessingChapter] = useState<Chapter | null>(null);
  const [processedChapterIds, setProcessedChapterIds] = useState<string[]>([]);
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0);

  // 章节数据状态
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  const extractService = new OutlineFrameworkExtractService();

  // 加载章节数据
  useEffect(() => {
    const loadChapters = async () => {
      if (!isOpen || !bookId) {
        console.log('🚫 跳过章节加载:', { isOpen, bookId });
        return;
      }

      console.log('🔍 开始加载章节数据:', { bookId, isOpen });
      setIsLoadingChapters(true);
      try {
        // 详细的诊断日志
        console.log('📊 数据库连接状态检查...');

        // 检查数据库是否可用
        const { db } = await import('@/lib/db/dexie');
        console.log('📊 数据库实例:', db);

        // 检查章节表是否存在
        const tableExists = await db.chapters.count();
        console.log('📊 章节表记录总数:', tableExists);

        // 执行查询
        const chaptersData = await chapterRepository.getAllByBookId(bookId);
        console.log('📊 查询结果详情:', {
          bookId,
          chaptersCount: chaptersData.length,
          chapters: chaptersData.map(ch => ({
            id: ch.id,
            title: ch.title,
            bookId: ch.bookId,
            wordCount: ch.wordCount,
            order: ch.order
          }))
        });

        // 如果没有章节，检查是否有其他书籍的章节
        if (chaptersData.length === 0) {
          const allChapters = await db.chapters.toArray();
          console.log('📊 数据库中所有章节:', allChapters.map(ch => ({
            id: ch.id,
            title: ch.title,
            bookId: ch.bookId
          })));

          // 检查是否有匹配的书籍
          const { bookRepository } = await import('@/lib/db/repositories');
          const currentBook = await bookRepository.getById(bookId);
          console.log('📊 当前书籍信息:', currentBook);
        }

        setChapters(chaptersData);
      } catch (error) {
        console.error('❌ 加载章节数据失败:', error);
        console.error('❌ 错误详情:', {
          name: (error as Error).name,
          message: (error as Error).message,
          stack: (error as Error).stack
        });
        setError(`加载章节数据失败: ${(error as Error).message}`);
      } finally {
        setIsLoadingChapters(false);
      }
    };

    loadChapters();
  }, [isOpen, bookId]);

  // 重置状态
  const resetDialog = useCallback(() => {
    setSelectedChapterIds([]);
    setCurrentStep('selection');
    setIsAnalyzing(false);
    setAnalysisResult(null);
    setError(null);
    setAnalysisProgress(0);
    setExtractionMode(ExtractionMode.SUMMARY);
    setCurrentProcessingChapter(null);
    setProcessedChapterIds([]);
    setCurrentChapterIndex(0);
  }, []);

  // 关闭弹窗
  const handleClose = useCallback(() => {
    resetDialog();
    onClose();
  }, [resetDialog, onClose]);

  // 章节选择变化
  const handleChapterSelectionChange = useCallback((chapterIds: string[]) => {
    setSelectedChapterIds(chapterIds);
  }, []);

  // 进度回调函数
  const handleProgress = useCallback((current: number, total: number, currentChapter?: Chapter) => {
    setCurrentChapterIndex(current);
    setCurrentProcessingChapter(currentChapter || null);

    // 更新进度百分比
    const progress = Math.round((current / total) * 100);
    setAnalysisProgress(progress);

    // 更新已处理的章节列表
    if (currentChapter && current > 1) {
      setProcessedChapterIds(prev => {
        const newProcessed = [...prev];
        if (!newProcessed.includes(currentChapter.id!)) {
          newProcessed.push(currentChapter.id!);
        }
        return newProcessed;
      });
    }
  }, []);

  // 开始分析
  const handleStartAnalysis = useCallback(async () => {
    if (!outline || selectedChapterIds.length === 0) {
      setError('请选择要分析的章节');
      return;
    }

    // 单章节模式验证
    if (extractionMode === ExtractionMode.SINGLE && selectedChapterIds.length !== 1) {
      setError('单章节模式只能选择一个章节');
      return;
    }

    setIsAnalyzing(true);
    setCurrentStep('analyzing');
    setError(null);
    setAnalysisProgress(0);
    setProcessedChapterIds([]);
    setCurrentProcessingChapter(null);
    setCurrentChapterIndex(0);

    try {
      // 获取选中的章节
      const selectedChapters = selectedChapterIds
        .map(id => chapters.find(chapter => chapter.id === id))
        .filter(chapter => chapter !== undefined) as Chapter[];

      if (selectedChapters.length === 0) {
        throw new Error('未找到选中的章节');
      }

      // 构建提取配置
      const config: ExtractionConfig = {
        mode: extractionMode,
        selectedChapters,
        analysisDepth: 'detailed',
        showProgress: true,
        allChapters: chapters, // 传递所有章节用于上下文分析
        onProgress: handleProgress
      };

      console.log(`🚀 开始${extractionMode}模式分析，章节数量：${selectedChapters.length}`);

      // 调用新的多模式分析服务
      const result = await extractService.extractFrameworkWithMode(config);

      setAnalysisProgress(100);
      setAnalysisResult(result);
      setCurrentStep('result');
      setIsAnalyzing(false);

      console.log('✅ 分析完成');

    } catch (err) {
      console.error('❌ 分析失败:', err);
      setError(err instanceof Error ? err.message : '分析失败，请重试');
      setIsAnalyzing(false);
      setCurrentStep('selection');
      setProcessedChapterIds([]);
      setCurrentProcessingChapter(null);
    }
  }, [outline, selectedChapterIds, extractionMode, chapters, extractService, handleProgress]);

  // 保存框架
  const handleSaveFramework = useCallback((framework: OutlineFrameworkResult) => {
    try {
      // 读取现有框架
      const existingFrameworks = JSON.parse(
        localStorage.getItem('outline-frameworks') || '[]'
      );

      // 构建新框架对象，添加必要的元数据
      const newFramework = {
        ...framework,
        usageCount: 0,
        createdAt: new Date().toISOString(),
        lastUsedAt: null
      };

      // 检查是否已存在相同的框架（基于frameworkName）
      const existingIndex = existingFrameworks.findIndex(
        (f: any) => f.frameworkName === framework.frameworkName
      );

      let updatedFrameworks;
      if (existingIndex >= 0) {
        // 更新现有框架
        updatedFrameworks = existingFrameworks.map((f: any, index: number) =>
          index === existingIndex ? { ...newFramework, usageCount: f.usageCount } : f
        );
        console.log('✅ 框架已更新:', framework.frameworkName);
      } else {
        // 添加新框架
        updatedFrameworks = [...existingFrameworks, newFramework];
        console.log('✅ 新框架已添加:', framework.frameworkName);
      }

      // 保存到localStorage
      localStorage.setItem('outline-frameworks', JSON.stringify(updatedFrameworks));

      // 用户反馈 - 这里可以添加toast通知
      alert(`框架"${framework.frameworkName}"已成功保存到框架库！`);

    } catch (error) {
      console.error('❌ 保存框架失败:', error);
      alert('保存框架失败，请重试');
    }
  }, []);

  // 导出框架
  const handleExportFramework = useCallback((format: 'json' | 'markdown' | 'pdf') => {
    // TODO: 实现导出功能
    console.log('导出框架:', format);
  }, []);

  // 获取可选择的章节
  const getSelectableChapters = (): Chapter[] => {
    console.log('获取章节数据:', chapters);
    return chapters;
  };

  if (!isOpen) return null;

  const dialogContent = (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* 弹窗内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[800px] flex flex-col mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">剧情大纲框架提取</h2>
                <p className="text-gray-600 mt-1">深度分析大纲结构，提取创作技巧和设计思路</p>
              </div>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容区域 */}
            <div className="flex-1 flex overflow-hidden">
              {currentStep === 'selection' && (
                <>
                  {/* 左侧：提取模式选择器 */}
                  <div className="w-2/5 border-r border-gray-200 p-6 overflow-y-auto">
                    <ExtractionModeSelector
                      selectedMode={extractionMode}
                      onModeChange={setExtractionMode}
                      disabled={isAnalyzing}
                    />
                  </div>

                  {/* 右侧：章节选择器 */}
                  <div className="flex-1 p-6 overflow-y-auto">
                    <h3 className="text-lg font-semibold mb-4">选择要分析的章节</h3>
                    {isLoadingChapters ? (
                      <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
                        <span className="ml-3 text-gray-600">加载章节数据中...</span>
                      </div>
                    ) : chapters.length === 0 ? (
                      <div className="text-center py-12 text-gray-500">
                        <p>暂无章节数据</p>
                        <p className="text-sm mt-2">请先在编辑器中创建章节内容</p>
                      </div>
                    ) : (
                      <ChapterSelector
                        chapters={getSelectableChapters()}
                        selectedIds={selectedChapterIds}
                        onSelectionChange={handleChapterSelectionChange}
                        maxSelection={extractionMode === ExtractionMode.SINGLE ? 1 : 10}
                        mode={extractionMode}
                        currentProcessingId={currentProcessingChapter?.id}
                        processedIds={processedChapterIds}
                        showRangeSelection={extractionMode === ExtractionMode.RANGE}
                      />
                    )}
                  </div>
                </>
              )}

              {currentStep === 'analyzing' && (
                <ProgressIndicator
                  mode={extractionMode}
                  current={currentChapterIndex}
                  total={selectedChapterIds.length}
                  currentChapter={currentProcessingChapter}
                  isAnalyzing={isAnalyzing}
                  progress={analysisProgress}
                />
              )}

              {currentStep === 'result' && analysisResult && (
                <FrameworkAnalysisResult
                  result={analysisResult}
                  onSave={handleSaveFramework}
                  onExport={handleExportFramework}
                  onClose={handleClose}
                  onStartNew={() => {
                    resetDialog();
                    setCurrentStep('selection');
                  }}
                />
              )}
            </div>

            {/* 底部操作栏 */}
            {currentStep === 'selection' && (
              <div className="border-t border-gray-200 p-6 bg-gray-50">
                <div className="flex items-center justify-between">
                  {/* 左侧：分析信息 */}
                  <div className="flex items-center space-x-4">
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <h4 className="font-medium text-orange-800 mb-1">分析维度</h4>
                      <p className="text-xs text-orange-700">
                        情节分析 • 对话分析 • 风格分析
                      </p>
                    </div>

                    {selectedChapterIds.length > 0 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h4 className="font-medium text-blue-800 mb-1">已选择章节</h4>
                        <p className="text-xs text-blue-700">
                          共 {selectedChapterIds.length} 个章节
                          {extractionMode === ExtractionMode.SINGLE && ' (单章节深度分析)'}
                        </p>
                      </div>
                    )}

                    {error && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                        <p className="text-xs text-red-700">{error}</p>
                      </div>
                    )}
                  </div>

                  {/* 右侧：操作按钮 */}
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={handleClose}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      取消
                    </button>
                    <button
                      onClick={handleStartAnalysis}
                      disabled={selectedChapterIds.length === 0 || (extractionMode === ExtractionMode.SINGLE && selectedChapterIds.length !== 1)}
                      className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <span>开始分析</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );

  return createPortal(dialogContent, document.body);
};

export default OutlineFrameworkExtractDialog;
