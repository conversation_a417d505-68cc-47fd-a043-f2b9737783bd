import { Chapter } from '@/lib/db/dexie';
import { configService } from '@/services/configService';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

// 提取模式枚举
export enum ExtractionMode {
  SUMMARY = 'summary',      // 模式1：总结提取
  SINGLE = 'single',        // 模式2：单章节提取
  RANGE = 'range'           // 模式3：范围提取
}

// 提取配置接口
export interface ExtractionConfig {
  mode: ExtractionMode;
  selectedChapters: Chapter[];
  analysisDepth: 'basic' | 'detailed' | 'comprehensive';
  showProgress: boolean;
  allChapters?: Chapter[]; // 所有章节列表，用于提供上下文
  onProgress?: (current: number, total: number, currentChapter?: Chapter) => void;
}

interface AnalysisOptions {
  analysisDepth: 'basic' | 'detailed' | 'comprehensive';
  includeDialogue?: boolean;
  includeConflict?: boolean;
  includeTechniques?: boolean;
}

interface OutlineFrameworkResult {
  id: string;
  frameworkName: string;
  frameworkPattern: string;  // 变量格式，如："{主角} {穿越}后获得{系统}"
  frameworkVariables: string[]; // 提取的变量列表，如：["主角", "穿越", "系统"]
  patternType: string;       // 模式类型，如：穿越系统流
  extractedFrom: {
    chapterIds: string[];
    chapterTitles: string[];
    extractDate: Date;
  };
  // 简化为三个核心分析维度，增加深度分析字段
  plotAnalysis: {
    storyStructure: string;    // 整合：章节安排 + 情节推进 + 场景构建
    conflictDesign: string;    // 整合：冲突类型 + 升级方法 + 解决技巧
    rhythmControl: string;     // 整合：转场技巧 + 节奏控制 + 连接方法
    // 新增深度分析字段
    plotPoints?: string[];        // 具体剧情点列表
    behaviorFrameworks?: string[]; // 行为框架模式
    // 新增写作指导相关字段
    plotPointsWithGuidance?: Array<{
      content: string;              // 剧情点内容
      specificDescription: string;  // 该章节具体描写的内容
      avoidanceGuidance: string;    // 预测应该避免的描写
    }>;
  };
  dialogueAnalysis: {
    // 保留核心旧字段（标记为可选，向后兼容）
    dialogueStructure?: string; // 对话的具体结构和格式
    plotAdvancement?: string;   // 对话推进情节的具体方法
    writingTechniques?: string; // 对话写作的实际技巧
    toneCharacteristics?: string[]; // 语气特征分析
    stylePatterns?: string[];       // 行文框架模式
    literaryAnalysis?: string;      // 文学化分析

    // 新增：完整对话提取
    completeDialogues: Array<{
      content: string;      // 对话内容
      speaker?: string;     // 说话人
      context: string;      // 上下文
      position: number;     // 位置
    }>;

    // 新增：风格分析
    styleAnalysis: {
      dialogueStyle: string;        // 对话风格特征
      characterVoice: string;       // 角色语言特色
      emotionalTone: string;        // 情感基调
      technicalFeatures: string;   // 写作技巧特征
    };
  };
  styleAnalysis: {
    writingStyle: string;      // 整合：写作风格 + 技巧手法 + 设计逻辑
    expressionFeatures: string; // 整合：表现特色 + 技术手法运用
    practicalMethods: string;   // 整合：实用方法 + 应用技巧
    // 新增深度分析字段
    rhythmPatterns?: string[];      // 节奏模式
    pacingFramework?: string;       // 节奏控制框架
    outlineGuidance?: string;       // 大纲参照指导
  };
  reusablePatterns: string[];
  applicationMethods: string[];
  metadata: {
    analysisDepth: 'basic' | 'detailed' | 'comprehensive';
    confidence: number;
    tags: string[];
    category: string;
  };
}

export class OutlineFrameworkExtractService {
  private aiSender: DefaultAISenderComponent | null = null;

  /**
   * 确保AI发送器已初始化
   */
  private async ensureAISender(): Promise<void> {
    if (!this.aiSender) {
      // 使用统一的AI服务架构
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      this.aiSender = AIServiceFactory.getService(AIServiceType.TOOL_CALL) as any;
    }
  }

  /**
   * 提取大纲框架（新版本，支持多种模式）
   */
  async extractFrameworkWithMode(
    config: ExtractionConfig
  ): Promise<OutlineFrameworkResult> {
    try {
      await this.ensureAISender();

      console.log(`🔍 开始${config.mode}模式框架提取分析...`);

      // 根据模式选择不同的处理策略
      switch (config.mode) {
        case ExtractionMode.SUMMARY:
          return this.extractSummaryFramework(config);
        case ExtractionMode.SINGLE:
          return this.extractSingleChapterFramework(config);
        case ExtractionMode.RANGE:
          return this.extractRangeFramework(config);
        default:
          throw new Error(`不支持的提取模式: ${config.mode}`);
      }
    } catch (error) {
      console.error('❌ 框架提取失败:', error);
      throw error;
    }
  }



  /**
   * 提取大纲框架（保持向后兼容）
   */
  async extractFramework(
    selectedChapters: Chapter[],
    options: AnalysisOptions
  ): Promise<OutlineFrameworkResult> {
    try {
      await this.ensureAISender();

      console.log('🔍 开始框架提取分析...');

      // 获取API配置
      const aiConfig = await configService.getAIConfig();
      console.log('📋 API配置详情:', {
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        model: aiConfig.model,
        hasApiKey: !!aiConfig.apiKey
      });

      // 构建分析消息
      const messages = this.buildAnalysisMessages(selectedChapters, options);

      console.log('🔍 发送AI框架分析请求...');

      // 调用AI服务 - 改为流式请求
      let fullContent = '';
      const response = await this.aiSender!.sendStreamingRequest(
        '',
        (chunk: string) => {
          fullContent += chunk;
          // 如果有进度回调，可以在这里传递流式内容
          // 这里暂时保持原有的进度回调逻辑
        },
        {
          messages: messages,
          temperature: aiConfig.temperature || 0.7,
          maxTokens: aiConfig.maxTokens || 4000,
          streaming: true
        }
      );

      if (!response.success) {
        throw new Error(response.error || 'AI请求失败');
      }

      console.log('✅ AI框架分析完成');

      // 解析AI响应 - 使用流式累积的完整内容
      const result = this.parseAnalysisResponse(fullContent, selectedChapters, options);
      return result;

    } catch (error) {
      console.error('❌ 框架提取失败:', error);
      throw error; // 直接抛出错误，不使用模拟结果
    }
  }

  /**
   * 模式1：总结提取 - 分析多个章节的共同模式
   */
  private async extractSummaryFramework(config: ExtractionConfig): Promise<OutlineFrameworkResult> {
    const options: AnalysisOptions = {
      analysisDepth: config.analysisDepth,
      includeDialogue: true,
      includeConflict: true,
      includeTechniques: true
    };

    // 使用现有的提取逻辑，但添加章节标记
    return this.extractFrameworkWithProgress(config.selectedChapters, options, config.onProgress);
  }



  /**
   * 模式2：单章节提取 - 深度分析单个章节
   */
  private async extractSingleChapterFramework(config: ExtractionConfig): Promise<OutlineFrameworkResult> {
    if (config.selectedChapters.length !== 1) {
      throw new Error('单章节模式只能选择一个章节');
    }

    const chapter = config.selectedChapters[0];
    config.onProgress?.(1, 1, chapter);

    // 获取API配置
    const aiConfig = await configService.getAIConfig();

    // 构建包含上下文的单章节深度分析消息
    const messages = this.buildSingleChapterWithContextMessages(chapter, config.analysisDepth, config.allChapters);

    console.log(`🔍 发送单章节深度分析请求: ${chapter.title}`);

    // 调用AI服务 - 改为流式请求
    let fullContent = '';
    const response = await this.aiSender!.sendStreamingRequest(
      '',
      (chunk: string) => {
        fullContent += chunk;
        // 传递流式内容给进度回调
        config.onProgress?.(1, 1, chapter);
      },
      {
        messages: messages,
        temperature: aiConfig.temperature || 0.7,
        maxTokens: aiConfig.maxTokens || 6000, // 单章节分析使用更多token
        topP: aiConfig.topP,
        topK: aiConfig.topK,
        streaming: true
      }
    );

    if (!response.success) {
      throw new Error(response.error || 'AI请求失败');
    }

    console.log('✅ 单章节AI分析完成');

    // 解析AI响应 - 使用流式累积的完整内容
    const options: AnalysisOptions = {
      analysisDepth: config.analysisDepth,
      includeDialogue: true,
      includeConflict: true,
      includeTechniques: true
    };

    return this.parseAnalysisResponse(fullContent, [chapter], options);
  }



  /**
   * 模式3：范围提取 - 灵活的章节范围分析
   */
  private async extractRangeFramework(config: ExtractionConfig): Promise<OutlineFrameworkResult> {
    const selectedChapters = config.selectedChapters;

    // 根据章节数量选择不同的处理策略
    if (selectedChapters.length === 1) {
      // 单章节：使用深度分析模式
      console.log('🔍 范围提取：单章节，使用深度分析模式');
      return this.extractSingleChapterFramework({
        ...config,
        mode: ExtractionMode.SINGLE
      });
    } else if (selectedChapters.length <= 3) {
      // 少量章节：使用批量单章节分析模式
      console.log('📋 范围提取：少量章节，使用批量单章节分析模式');
      return this.extractBatchSingleChapterFramework(config);
    } else {
      // 多章节：使用总结提取模式
      console.log('📊 范围提取：多章节，使用总结提取模式');
      return this.extractSummaryFramework(config);
    }
  }

  /**
   * 批量单章节分析模式 - 对每个章节进行独立深度分析后合并
   */
  private async extractBatchSingleChapterFramework(config: ExtractionConfig): Promise<OutlineFrameworkResult> {
    const selectedChapters = config.selectedChapters;
    const chapterResults: OutlineFrameworkResult[] = [];

    console.log(`🔄 开始批量单章节分析，共${selectedChapters.length}个章节`);

    // 逐个分析每个章节
    for (let i = 0; i < selectedChapters.length; i++) {
      const chapter = selectedChapters[i];

      // 调用进度回调
      config.onProgress?.(i + 1, selectedChapters.length, chapter);

      console.log(`📖 分析第${i + 1}章：${chapter.title}`);

      try {
        // 对单个章节进行深度分析（包含上下文）
        const singleResult = await this.extractSingleChapterFramework({
          ...config,
          selectedChapters: [chapter],
          mode: ExtractionMode.SINGLE,
          allChapters: config.allChapters, // 传递所有章节用于上下文分析
          onProgress: undefined // 避免重复调用进度回调
        });

        chapterResults.push(singleResult);
        console.log(`✅ 第${i + 1}章分析完成`);

      } catch (error) {
        console.error(`❌ 第${i + 1}章分析失败:`, error);
        // 继续分析下一章，不中断整个流程
      }
    }

    // 合并所有章节的分析结果
    console.log('🔗 合并批量分析结果');
    return this.mergeBatchResults(chapterResults, selectedChapters);
  }

  /**
   * 合并批量分析结果
   */
  private mergeBatchResults(results: OutlineFrameworkResult[], chapters: Chapter[]): OutlineFrameworkResult {
    if (results.length === 0) {
      throw new Error('没有成功分析的章节结果');
    }

    // 如果只有一个结果，直接返回
    if (results.length === 1) {
      return results[0];
    }

    // 合并多个结果
    const mergedResult: OutlineFrameworkResult = {
      id: `batch_${Date.now()}`,
      frameworkName: `批量分析框架 - ${chapters.map(c => c.title).join('、')}`,
      frameworkPattern: this.mergePatternsFromResults(results),
      frameworkVariables: this.mergeVariablesFromResults(results),
      patternType: '批量范围分析',
      extractedFrom: {
        chapterIds: chapters.map(c => c.id!),
        chapterTitles: chapters.map(c => c.title),
        extractDate: new Date()
      },
      plotAnalysis: this.mergePlotAnalysis(results),
      dialogueAnalysis: this.mergeDialogueAnalysis(results),
      styleAnalysis: this.mergeStyleAnalysis(results),
      reusablePatterns: this.mergeArrayFields(results, 'reusablePatterns'),
      applicationMethods: this.mergeArrayFields(results, 'applicationMethods'),
      metadata: {
        analysisDepth: 'comprehensive',
        confidence: this.calculateAverageConfidence(results),
        tags: this.mergeArrayFields(results, 'metadata.tags').flat(),
        category: '批量范围分析'
      }
    };

    return mergedResult;
  }

  /**
   * 带进度回调的框架提取
   */
  private async extractFrameworkWithProgress(
    selectedChapters: Chapter[],
    options: AnalysisOptions,
    onProgress?: (current: number, total: number, currentChapter?: Chapter) => void
  ): Promise<OutlineFrameworkResult> {
    try {
      // 获取API配置
      const aiConfig = await configService.getAIConfig();
      console.log('📋 API配置详情:', {
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        model: aiConfig.model,
        hasApiKey: !!aiConfig.apiKey
      });

      // 构建分析消息（带进度回调）
      const messages = this.buildAnalysisMessagesWithProgress(selectedChapters, options, onProgress);

      console.log('🔍 发送AI框架分析请求...');

      // 调用AI服务 - 改为流式请求
      let fullContent = '';
      const response = await this.aiSender!.sendStreamingRequest(
        '',
        (chunk: string) => {
          fullContent += chunk;
          // 传递流式内容给进度回调
          if (onProgress && selectedChapters.length > 0) {
            onProgress(selectedChapters.length, selectedChapters.length, selectedChapters[0]);
          }
        },
        {
          messages: messages,
          temperature: aiConfig.temperature || 0.7,
          maxTokens: aiConfig.maxTokens || 4000,
          streaming: true
        }
      );

      if (!response.success) {
        throw new Error(response.error || 'AI请求失败');
      }

      console.log('✅ AI框架分析完成');

      // 解析AI响应 - 使用流式累积的完整内容
      const result = this.parseAnalysisResponse(fullContent, selectedChapters, options);
      return result;

    } catch (error) {
      console.error('❌ 框架提取失败:', error);
      throw error;
    }
  }

  /**
   * 计算中文字数（正确的字数统计）
   */
  private getChineseWordCount(text: string): number {
    // 移除空白字符，然后计算实际字符数
    // 对于中文，每个字符就是一个字
    return text.replace(/\s/g, '').length;
  }

  /**
   * 提取章节中的对话内容
   */
  private extractDialogues(content: string): Array<{
    content: string;
    speaker?: string;
    context: string;
    position: number;
  }> {
    const dialogues: Array<{
      content: string;
      speaker?: string;
      context: string;
      position: number;
    }> = [];

    // 对话标记模式
    const dialoguePatterns = [
      /「([^」]*)」/g,           // 中文书名号
      /"([^"]*)"/g,             // 英文双引号
      /'([^']*)'/g,             // 英文单引号
      /『([^』]*)』/g,           // 中文双书名号
    ];

    dialoguePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const dialogueContent = match[1].trim();
        if (dialogueContent.length > 0) {
          const position = match.index;

          // 提取说话人
          const beforeText = content.substring(Math.max(0, position - 50), position);
          const speakerMatch = beforeText.match(/([^，。！？：\s]{1,10})[说道讲问答叫喊][:：]?\s*$/);
          const speaker = speakerMatch ? speakerMatch[1] : undefined;

          // 提取上下文
          const contextStart = Math.max(0, position - 30);
          const contextEnd = Math.min(content.length, position + match[0].length + 30);
          const context = content.substring(contextStart, contextEnd)
            .replace(/[「」"'『』]/g, '')
            .trim();

          dialogues.push({
            content: dialogueContent,
            speaker,
            context,
            position
          });
        }
      }
    });

    // 按位置排序并去重
    return dialogues
      .sort((a, b) => a.position - b.position)
      .filter((dialogue, index, arr) =>
        index === 0 || dialogue.content !== arr[index - 1].content
      );
  }

  /**
   * 将对话分组（每四句一组）
   */
  private groupDialogues(dialogues: Array<{
    content: string;
    speaker?: string;
    context: string;
    position: number;
  }>): Array<{
    groupIndex: number;
    dialogues: Array<{
      content: string;
      speaker?: string;
      context: string;
      position: number;
    }>;
  }> {
    const groups: Array<{
      groupIndex: number;
      dialogues: Array<{
        content: string;
        speaker?: string;
        context: string;
        position: number;
      }>;
    }> = [];

    for (let i = 0; i < dialogues.length; i += 4) {
      const group = {
        groupIndex: Math.floor(i / 4) + 1,
        dialogues: dialogues.slice(i, i + 4)
      };
      groups.push(group);
    }

    return groups;
  }



  /**
   * 智能分段章节内容
   */
  private segmentChapterContent(content: string, maxSegmentLength: number = 600): string[] {
    const segments: string[] = [];

    console.log(`🔍 开始分段章节内容，目标长度：${maxSegmentLength}字`);
    console.log(`📄 原始内容总字数：${this.getChineseWordCount(content)}字`);

    // 按段落分割
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    console.log(`📝 找到${paragraphs.length}个段落`);

    let currentSegment = '';
    for (const paragraph of paragraphs) {
      const currentWordCount = this.getChineseWordCount(currentSegment);
      const paragraphWordCount = this.getChineseWordCount(paragraph);

      console.log(`📊 当前段落字数：${paragraphWordCount}，累计字数：${currentWordCount}，合计：${currentWordCount + paragraphWordCount}`);

      if (currentWordCount + paragraphWordCount > maxSegmentLength && currentSegment) {
        segments.push(currentSegment.trim());
        console.log(`✂️ 分段完成，段落${segments.length}字数：${this.getChineseWordCount(currentSegment.trim())}`);
        currentSegment = paragraph;
      } else {
        currentSegment += (currentSegment ? '\n\n' : '') + paragraph;
        console.log(`📝 累积段落，当前累积字数：${this.getChineseWordCount(currentSegment)}`);
      }
    }

    if (currentSegment) {
      segments.push(currentSegment.trim());
      console.log(`✂️ 最后段落字数：${this.getChineseWordCount(currentSegment.trim())}`);
    }

    // 如果段落分割后仍然有超长段落，进行二次分割
    const finalSegments: string[] = [];
    for (const segment of segments) {
      const segmentWordCount = this.getChineseWordCount(segment);
      if (segmentWordCount > maxSegmentLength) {
        console.log(`⚠️ 发现超长段落（${segmentWordCount}字），进行二次分割`);
        // 按句子分割超长段落
        const sentences = segment.split(/([。！？])/).filter(s => s.trim());
        let currentSubSegment = '';

        for (let i = 0; i < sentences.length; i += 2) {
          const sentence = sentences[i] + (sentences[i + 1] || '');
          const currentSubWordCount = this.getChineseWordCount(currentSubSegment);
          const sentenceWordCount = this.getChineseWordCount(sentence);

          if (currentSubWordCount + sentenceWordCount > maxSegmentLength && currentSubSegment) {
            finalSegments.push(currentSubSegment.trim());
            console.log(`✂️ 二次分段完成，段落${finalSegments.length}字数：${this.getChineseWordCount(currentSubSegment.trim())}`);
            currentSubSegment = sentence;
          } else {
            currentSubSegment += sentence;
          }
        }

        if (currentSubSegment) {
          finalSegments.push(currentSubSegment.trim());
          console.log(`✂️ 二次分段最后段落字数：${this.getChineseWordCount(currentSubSegment.trim())}`);
        }
      } else {
        finalSegments.push(segment);
      }
    }

    // 如果还是没有分段，按句子分割
    if (finalSegments.length === 0) {
      console.log(`⚠️ 段落分割失败，尝试按句子分割`);
      const sentences = content.split(/([。！？])/).filter(s => s.trim());
      let currentSegment = '';

      for (let i = 0; i < sentences.length; i += 2) {
        const sentence = sentences[i] + (sentences[i + 1] || '');
        const currentWordCount = this.getChineseWordCount(currentSegment);
        const sentenceWordCount = this.getChineseWordCount(sentence);

        if (currentWordCount + sentenceWordCount > maxSegmentLength && currentSegment) {
          finalSegments.push(currentSegment.trim());
          console.log(`✂️ 句子分段完成，段落${finalSegments.length}字数：${this.getChineseWordCount(currentSegment.trim())}`);
          currentSegment = sentence;
        } else {
          currentSegment += sentence;
        }
      }

      if (currentSegment) {
        finalSegments.push(currentSegment.trim());
        console.log(`✂️ 句子分段最后段落字数：${this.getChineseWordCount(currentSegment.trim())}`);
      }
    }

    // 确保至少有一个段落
    if (finalSegments.length === 0) {
      console.log(`⚠️ 所有分割方式失败，使用字符截取`);
      // 使用字数而不是字符数进行截取
      let truncatedContent = '';
      let wordCount = 0;
      for (const char of content) {
        if (!/\s/.test(char)) {
          wordCount++;
        }
        truncatedContent += char;
        if (wordCount >= maxSegmentLength) {
          break;
        }
      }
      finalSegments.push(truncatedContent);
    }

    console.log(`✅ 分段完成，共${finalSegments.length}个段落，字数分布：${finalSegments.map(s => this.getChineseWordCount(s)).join(', ')}`);

    return finalSegments;
  }

  /**
   * 构建单章节深度分析消息
   */
  private buildSingleChapterMessages(chapter: Chapter, analysisDepth: string): Array<{ role: string; content: string }> {
    const messageBuilder = new MessageBuilder();

    // 系统消息：定义AI角色（单章节写作特征分析）
    messageBuilder.addSystemMessage(`你是一位专业的写作特征分析师，专门从章节中提取作者独特的写作手法和风格特征。
你的核心任务是深度分析该章节中作者实际使用的具体写作技巧，提取可学习的实际手法和风格特色。

【单章节深度特征分析专业要求】

🎯 **写作特征提取目标**：
- 深度分析该章节的实际写作手法和技巧运用
- 提取作者独特的表达方式和语言风格特征
- 分析该章节在情节推进中的具体技巧和方法
- 识别作者的个性化写作特色和创作手法

📊 **三维写作特征分析**：

**1. 情节技巧分析（Plot Techniques）**：
   🔍 **故事结构特征**：
   - 该章节的开头手法：作者实际使用的开场方式、引入要素、基调设定技巧
   - 发展技巧：具体的推进方式、转折设置手法、冲突升级技巧
   - 高潮处理：实际的爆发点营造、解决方式、结果呈现技巧
   - 结尾特色：具体的收尾方式、伏笔设置手法、过渡技巧

   ⚔️ **冲突设计手法**：
   - 冲突引入手法：作者如何触发冲突、设置对立双方、聚焦争议点
   - 冲突升级技巧：实际的矛盾层次递进、加剧手法、爆发技巧
   - 冲突解决手法：具体的解决方式、结果影响处理、后续铺垫技巧

   🎵 **节奏控制技巧**：
   - 快节奏手法：作者使用的短句技巧、动作描写、对话节奏、转场手法
   - 慢节奏手法：具体的描写技巧、内心刻画、回忆处理、环境渲染
   - 节奏转换技巧：实际的缓冲段落处理、转折信号设置、新节奏建立

   🎯 **剧情点完整拆解**：
   - 详细列出该章节的每个具体剧情点（完整描述实际内容）
   - 每个剧情点必须包含完整的事件描述、角色行为、对话要点
   - 剧情点之间的具体连接方式和过渡处理
   - 重要剧情点的前因后果和影响分析

   📝 **写作指导增强**：
   - 为每个剧情点分析该章节具体描写的内容特征
   - 预测如果出现这种剧情，应该避免什么样的描写方式
   - 提供具体的"避免描写一丝xx"和"应该描写xx"的指导
   - 提升框架使用后avoidWriting 与shouldWriting 的准确性和审美价值

**2. 对话技巧分析（Dialogue Techniques）**：
   💬 **对话结构特征**：
   - 对话开启手法：作者如何开场、话题引入方式、语气设定技巧
   - 对话推进技巧：信息交换方式、情绪变化处理、冲突升级手法
   - 对话结束技巧：总结决定方式、情感表达手法、行动指向技巧
   - 穿插技巧：对话与动作描写、内心独白、环境反应的结合方式

   🚀 **情节推进技巧**：
   - 信息揭示手法：作者的铺垫方式、关键信息呈现、反应后果处理
   - 冲突爆发技巧：分歧点设置、立场对立表现、情绪激化手法
   - 伏笔设置技巧：暗示性对话运用、模糊表达技巧、后续呼应处理

   ✍️ **对话风格特征**：
   - 角色语言个性：各角色的语言特色、常用句式、情感表达方式
   - 群体对话特色：发言顺序安排、互动方式设计、氛围营造技巧
   - 语言层次感：不同角色的语言水平、表达习惯、说话风格

   🎭 **语气表现技巧**：
   - 愤怒语气特征：作者表现愤怒的语言特征、句式结构、表达强度
   - 温柔语气特征：温柔表达的语言特征、句式结构、表达强度
   - 紧张语气特征：紧张情绪的语言特征、句式结构、表达强度
   - 幽默语气特征：幽默表达的语言特征、句式结构、表达强度

   📝 **行文组织技巧**：
   - 对话动作结合：对话内容与动作描写的穿插技巧和节奏控制
   - 对话心理结合：对话与内心想法、情感反应的结合方式
   - 多人对话处理：发言顺序、标识方式、节奏控制的具体技巧

   📚 **对话功能运用**：
   - 推进剧情功能：关键信息对话、决策对话、行动对话的运用技巧
   - 塑造角色功能：性格展示对话、背景揭示对话、关系定义对话的处理
   - 营造氛围功能：环境对话、情绪对话、张力对话的营造手法

**3. 风格特征分析（Style Features）**：
   🖋️ **写作风格特征**：
   - 语言风格特色：作者的词汇类型偏好、句式长度特点、表达方式特征
   - 句式结构特点：主句模式、从句搭配方式、修饰成分运用特色
   - 词汇选择特征：形容词类型偏好、动词风格特点、名词使用特色

   🎨 **表现手法特征**：
   - 描写技巧特色：感官描写运用、细节选择偏好、层次安排方式
   - 修辞手法特征：比喻句式运用、排比结构使用、对比手法特点
   - 情感渲染特色：情绪词汇选择、氛围营造方式、感染力构建技巧

   🛠️ **实用写作特征**：
   - 开头技巧特色：引入方式偏好、背景设定手法、悬念设置技巧
   - 过渡技巧特征：承接句式运用、转换方法特点、衔接技巧特色
   - 结尾技巧特色：收束方式偏好、余韵留存手法、后续铺垫技巧

   🎼 **节奏特征分析**：
   - 紧张节奏特色：短句运用、快速动作描写、急促对话处理
   - 舒缓节奏特征：长句使用、详细描写风格、内心独白处理
   - 变化节奏特色：节奏转换点设置、过渡段落处理、新节奏建立方式

   ⚡ **节奏控制特征**：
   - 加速技巧特色：触发条件设置、加速手法运用、高潮构建方式
   - 减速技巧特征：缓冲设置方式、减速手法特点、平稳过渡技巧
   - 停顿技巧特色：停顿点选择、停顿方式特点、重启方法特征

   🎯 **大纲应用特征**：
   - 章节功能特色：开篇作用发挥、发展功能体现、承接作用处理
   - 结构定位特征：前章承接方式、本章核心突出、后章铺垫技巧
   - 应用指导特征：适用场景识别、运用方法特点、调整要点总结

🔬 **特征分析深度要求**：
- 每个特征都要具体到实际运用的技巧层面
- 提供具体的手法描述和运用示例
- 提取该章节的独特写作特色和个性化手法
- 生成可直接学习借鉴的写作技巧
- **剧情点必须完整详细拆解**：不使用抽象描述，完整列出每个具体剧情点的详细内容

📋 **特征输出标准**：
- 所有特征内容必须基于该章节的实际写作手法
- 避免理论分析，专注实际技巧的提取
- 提供可直接学习、可具体借鉴的写作特色
- 确保特征结果的学习价值和参考意义

🎯 **特征描述要求**：
- 使用具体的技巧描述而非抽象概念
- 重点描述作者的实际操作方法
- 突出该章节的独特性和个性化特征
- 提供具体的运用示例和技巧说明

⚠️ **剧情点拆解特别要求**：
- plotPoints字段必须完整详细拆解每个具体剧情点
- 不使用模板格式，直接描述具体事件内容
- 每个剧情点包含：事件全过程、角色具体行为、关键对话内容、情感变化
- 剧情点描述要足够详细，让读者能完全理解事件的来龙去脉
- 示例格式："主角在书房发现父亲留下的密信，信中揭露了家族的秘密，主角震惊之余决定深入调查，与管家进行了一番试探性对话，最终确认了信件的真实性"

请严格按照指定的JSON格式返回模板提取结果，确保每个字段都包含可直接使用的模板内容。`);

    // 助手确认消息
    messageBuilder.addAssistantMessage(`我将对"${chapter.title}"进行写作特征分析，深度提取该章节的具体写作手法和风格特色。`);

    // 章节信息
    messageBuilder.addUserMessage(`【单章节写作特征分析】
章节标题：${chapter.title}
章节字数：${chapter.wordCount}字
章节顺序：第${chapter.order}章
分析深度：${analysisDepth}
章节摘要：${chapter.summary || '无'}

请对以下章节内容进行写作特征分析：

${chapter.content}`);

    // 助手确认阅读
    messageBuilder.addAssistantMessage(`我已仔细阅读"${chapter.title}"的完整内容，现在开始进行写作特征分析，专注于提取作者的具体写作手法和风格特色。`);

    // 特征分析指令
    messageBuilder.addUserMessage(`请对该章节进行写作特征分析，按照以下JSON格式返回结果：

{
  "frameworkName": "该章节的写作特征名称（基于实际内容特色）",
  "frameworkPattern": "该章节的写作结构特征，如：细腻环境描写 → 人物心理刻画 → 对话推进情节 → 情感升华收尾",
  "frameworkVariables": ["核心写作元素1", "核心写作元素2", "核心写作元素3"],
  "patternType": "该章节的写作风格类型（基于实际表现）",
  "plotAnalysis": {
    "storyStructure": "故事结构的具体构造方式：开头技巧 + 发展手法 + 高潮处理 + 结尾特色",
    "conflictDesign": "冲突设计的实际手法：触发方式 + 对立表现 + 升级技巧 + 解决手法",
    "rhythmControl": "节奏控制的具体技巧：快节奏手法 + 慢节奏技巧 + 转换方式",
    "plotPoints": ["详细剧情点1：完整描述该剧情点的事件全过程、角色具体行为、关键对话内容", "详细剧情点2：具体事件描述...", "详细剧情点3：具体事件描述..."],
    "behaviorFrameworks": ["行为表现特征1：触发情况 → 行为表现 → 结果后果", "行为表现特征2", "行为表现特征3"],
    "plotPointsWithGuidance": [
      {
        "content": "详细剧情点1的完整描述",
        "specificDescription": "该章节在这个剧情点具体描写了什么内容，如：主角的具体动作、环境细节、心理活动等",
        "avoidanceGuidance": "避免描写：一丝xx、几分xx、些许xx。应该描写：具体的动作、明确的反应、直接的表现"
      },
      {
        "content": "详细剧情点2的完整描述",
        "specificDescription": "该章节的具体描写特征",
        "avoidanceGuidance": "具体的避免和应该描写的指导"
      }
    ]
  },
  "dialogueAnalysis": {
    "dialogueStructure": "对话结构特征：开启手法 + 推进技巧 + 结束方式",
    "plotAdvancement": "对话推进手法：信息揭示方式 → 冲突升级技巧 → 决策引导方法",
    "writingTechniques": "对话写作特色：角色A语言特色 + 角色B语言特色 + 互动模式特征",
    "toneCharacteristics": ["语气特征1：语言特征 + 句式结构特点", "语气特征2", "语气特征3"],
    "stylePatterns": ["行文特色1：对话 + 动作 + 心理的结合方式", "行文特色2", "行文特色3"],
    "literaryAnalysis": "对话功能运用：推进剧情手法 + 塑造角色技巧 + 营造氛围方式"
  },
  "styleAnalysis": {
    "writingStyle": "写作风格特征：词汇类型偏好 + 句式长度特点 + 表达方式特色",
    "expressionFeatures": "表现手法特征：描写技巧 + 修辞手法 + 情感渲染方式",
    "practicalMethods": "实用写作特色：开头技巧 + 过渡手法 + 结尾特色",
    "rhythmPatterns": ["节奏特征1：短句运用 + 动作描写 + 对话节奏", "节奏特征2", "节奏特征3"],
    "pacingFramework": "节奏控制特征：加速技巧 + 减速手法 + 停顿方式",
    "outlineGuidance": "大纲应用特征：章节功能体现 + 结构定位特点 + 运用指导"
  },
  "reusablePatterns": ["可借鉴特征1：结构特点 + 运用方式", "可借鉴特征2"],
  "applicationMethods": ["运用方法1：适用场景 + 调整要点", "运用方法2"]
}`);

    return messageBuilder.build();
  }

  /**
   * 构建包含上下文的单章节深度分析消息
   */
  private buildSingleChapterWithContextMessages(
    targetChapter: Chapter,
    analysisDepth: string,
    allChapters?: Chapter[]
  ): Array<{ role: string; content: string }> {
    const messageBuilder = new MessageBuilder();

    // 系统消息：定义AI角色（单章节写作特征分析 - 包含上下文）
    messageBuilder.addSystemMessage(`你是一位专业的写作特征分析师，专门从章节中提取作者独特的写作手法和风格特征。
你的核心任务是从目标章节中深度分析作者实际使用的具体写作技巧，结合前文上下文进行综合分析。

【包含上下文的单章节写作特征分析】

🎯 **写作特征分析目标**：
- 深度分析目标章节的实际写作手法和技巧运用
- 结合前文上下文，提取该章节的具体写作特色和表达方式
- 分析该章节在整体故事发展中的写作功能和技巧作用
- 识别作者在承接和发展中的个性化写作特色

📊 **三维写作特征分析**：

**1. 情节技巧分析（Plot Techniques）**：
   🔍 **故事结构特征**：
   - 承接技巧：作者如何承接前文、本章开场手法、情节推进方式
   - 发展技巧：具体的推进方式、转折设置手法、冲突升级技巧
   - 高潮处理：爆发点营造方式、解决手法、结果呈现技巧
   - 过渡技巧：本章收尾方式、伏笔设置手法、下章铺垫技巧

   ⚔️ **冲突设计手法**：
   - 承接冲突手法：前文冲突延续方式、本章新冲突引入、冲突交织技巧
   - 冲突升级技巧：矛盾层次递进、加剧手法、爆发技巧
   - 冲突解决手法：具体的解决方式、结果影响处理、后续铺垫技巧

   🎵 **节奏控制技巧**：
   - 承接节奏技巧：前文节奏延续方式、本章节奏调整、新节奏建立
   - 快节奏手法：短句运用、动作描写、对话节奏、快速转场技巧
   - 慢节奏手法：描写技巧、内心刻画、回忆处理、环境渲染方式

   🎯 **剧情点完整拆解**：
   - 详细列出该章节的每个具体剧情点（完整描述实际内容）
   - 承接剧情点：完整描述与前文的呼应关系和本章的具体展开
   - 独立剧情点：详细描述事件全过程、角色具体行为、对话内容要点
   - 连接处理：具体的过渡方式、承接技巧、铺垫内容

**2. 对话技巧分析（Dialogue Techniques）**：
   💬 **完整对话提取**：
   - 提取章节中的完整对话列表，包含说话人、对话内容、上下文和位置信息
   - 对话分类：承接对话、推进对话、结束对话
   - 对话上下文分析：前文关联、情境描述、后续影响

   💬 **对话结构特征**：
   - 承接对话技巧：前文话题延续方式、新话题引入手法、对话推进技巧
   - 对话推进技巧：信息交换方式、情绪变化处理、冲突升级手法
   - 对话结束技巧：总结决定方式、情感表达手法、行动指向技巧

   🎨 **对话风格分析**：
   - 整体对话风格特征：语言风格、表达方式、节奏感
   - 角色语言特色：个性化表达、语言习惯、说话方式
   - 情感基调分析：对话氛围、情绪色彩、感情表达
   - 技巧特征提取：写作手法、对话技巧、表现方式

   🚀 **情节推进技巧**：
   - 信息承接技巧：前文信息回顾方式、新信息揭示手法、信息整合技巧
   - 冲突对话技巧：分歧点设置、立场对立表现、情绪激化手法
   - 伏笔呼应技巧：前文伏笔回应方式、新伏笔设置手法、后续呼应点处理

**3. 风格特征分析（Style Features）**：
   🖋️ **写作风格特征**：
   - 风格承接特征：前文风格延续方式、本章风格特色、风格变化处理
   - 语言风格特征：词汇类型偏好、句式长度特点、表达方式特色
   - 文体统一特征：整体风格把握、局部变化处理、风格协调技巧

🔬 **特征分析深度要求**：
- 每个特征都要具体到实际运用的技巧层面
- 特别关注与前文的承接关系和上下文连贯性
- 提取该章节在整体故事中的写作功能和技巧定位
- 生成可直接学习借鉴的写作特色和手法
- **剧情点必须完整详细拆解**：不使用抽象描述，完整列出每个具体剧情点的详细内容，包括事件全过程、角色行为、对话要点

📋 **特征输出标准**：
- 所有特征内容必须基于该章节的实际写作手法和上下文关系
- 避免理论分析，专注实际技巧的提取
- 提供可直接学习、可具体借鉴的写作特色
- 确保特征结果的学习价值和参考意义

🎯 **特征描述要求**：
- 使用具体的技巧描述而非抽象概念
- 重点描述作者的实际操作方法和承接技巧
- 突出该章节的独特性和个性化特征
- 提供具体的运用示例和技巧说明

⚠️ **剧情点拆解特别要求**：
- plotPoints字段必须完整详细拆解每个具体剧情点
- 不使用模板格式，直接描述具体事件内容
- 每个剧情点包含：事件全过程、角色具体行为、关键对话内容、情感变化、与前文的承接关系
- 剧情点描述要足够详细，让读者能完全理解事件的来龙去脉和上下文关系
- 示例格式："承接前文的线索，主角在书房发现父亲留下的密信，信中揭露了家族的秘密，主角震惊之余决定深入调查，与管家进行了一番试探性对话，最终确认了信件的真实性，为下章的行动做好铺垫"

请严格按照指定的JSON格式返回模板提取结果，确保每个字段都包含可直接使用的模板内容。`);

    // 助手确认消息
    messageBuilder.addAssistantMessage(`我将对"${targetChapter.title}"进行包含上下文的写作特征分析，结合前文内容深度提取该章节的具体写作手法和风格特色。`);

    // 如果有前文章节，先提供上下文
    if (allChapters && allChapters.length > 0) {
      const targetIndex = allChapters.findIndex(ch => ch.id === targetChapter.id);
      const previousChapters = targetIndex > 0 ? allChapters.slice(0, targetIndex) : [];

      if (previousChapters.length > 0) {
        messageBuilder.addUserMessage(`【前文上下文信息】
为了更好地分析目标章节的写作特征，先提供前文章节的基本信息：

${previousChapters.map((ch) => `
第${ch.order}章：${ch.title}
字数：${ch.wordCount}字
摘要：${ch.summary || '无'}
内容概要：${ch.content ? ch.content.substring(0, 200) + '...' : '无'}
`).join('\n')}`);

        messageBuilder.addAssistantMessage(`我已了解前文${previousChapters.length}个章节的基本信息和发展脉络，这将帮助我更好地分析目标章节的承接关系和写作特征。`);
      }
    }

    // 目标章节信息
    messageBuilder.addUserMessage(`【目标章节写作特征分析】
章节标题：${targetChapter.title}
章节字数：${targetChapter.wordCount}字
章节顺序：第${targetChapter.order}章
分析深度：${analysisDepth}
章节摘要：${targetChapter.summary || '无'}

请对以下目标章节内容进行写作特征分析：`);

    // 智能分段发送目标章节内容
    const segments = this.segmentChapterContent(targetChapter.content);
    segments.forEach((segment, segIndex) => {
      const segmentInfo = `【目标章节 - 段落 ${segIndex + 1}/${segments.length}】
${segment}`;

      messageBuilder.addSystemMessage(segmentInfo);
      messageBuilder.addAssistantMessage(`我已阅读目标章节第${segIndex + 1}段内容，正在分析其写作特征和与前文的承接关系。`);
    });

    // 提取并分析对话（对话超细提取）
    const dialogues = this.extractDialogues(targetChapter.content);
    if (dialogues.length > 0) {
      console.log(`📝 目标章节提取到${dialogues.length}句对话`);

      // 将对话分组（每四句一组）
      const dialogueGroups = this.groupDialogues(dialogues);

      // 发送对话分析消息
      messageBuilder.addSystemMessage(`【目标章节对话超细提取】
提取到${dialogues.length}句对话，分为${dialogueGroups.length}组进行分析：`);

      dialogueGroups.forEach((group, groupIndex) => {
        const groupInfo = `【对话组${groupIndex + 1}/${dialogueGroups.length}】
${group.dialogues.map((d, i) => `${i + 1}. ${d.speaker ? `${d.speaker}：` : ''}「${d.content}」`).join('\n')}`;

        messageBuilder.addSystemMessage(groupInfo);
        messageBuilder.addAssistantMessage(`我已分析对话组${groupIndex + 1}，包含${group.dialogues.length}句对话，正在提取对话技巧和梗句模式。`);
      });
    }

    // 助手确认阅读
    messageBuilder.addAssistantMessage(`我已仔细阅读"${targetChapter.title}"的完整内容，结合前文上下文，现在开始进行写作特征分析，专注于提取作者的具体写作手法和承接技巧。`);

    // 模板提取指令
    messageBuilder.addUserMessage(`请对该章节进行包含上下文的写作特征分析，按照以下JSON格式返回结果：

{
  "frameworkName": "该章节的写作特征名称（体现与前文的关系和实际内容特色）",
  "frameworkPattern": "该章节的写作结构特征，如：前文情感承接 → 本章环境渲染 → 人物心理深化 → 情节自然推进",
  "frameworkVariables": ["核心写作元素1", "核心写作元素2", "核心写作元素3"],
  "patternType": "该章节的写作风格类型（体现在整体故事中的功能和特色）",
  "plotAnalysis": {
    "storyStructure": "故事结构的具体构造方式：承接技巧 + 发展手法 + 高潮处理 + 过渡技巧",
    "conflictDesign": "冲突设计的实际手法：承接冲突方式 + 新冲突引入 + 升级技巧 + 解决手法",
    "rhythmControl": "节奏控制的具体技巧：承接节奏方式 + 调整手法 + 新节奏建立",
    "plotPoints": ["承接剧情点：详细描述与前文的具体呼应关系和本章的完整展开过程", "独立剧情点：完整描述该剧情点的事件全过程、角色具体行为、关键对话内容", "连接剧情点：详细描述过渡方式和铺垫内容"],
    "behaviorFrameworks": ["承接行为特征：前文延续方式 → 本章发展表现", "独立行为特征：触发情况 → 行为表现 → 结果后果", "连接行为特征：承接方式 → 转换技巧 → 新建立"],
    "plotPointsWithGuidance": [
      {
        "content": "承接剧情点的完整描述",
        "specificDescription": "该章节在承接剧情时具体描写了什么，如：与前文的呼应方式、角色状态变化、环境延续等",
        "avoidanceGuidance": "避免描写：一丝回忆、几分怀念、些许联想。应该描写：直接的承接动作、明确的状态变化、具体的呼应表现"
      },
      {
        "content": "独立剧情点的完整描述",
        "specificDescription": "该章节的独立剧情具体描写特征",
        "avoidanceGuidance": "具体的避免和应该描写的指导"
      }
    ]
  },
  "dialogueAnalysis": {
    "dialogueStructure": "对话结构特征：承接对话技巧 + 推进对话手法 + 结束对话方式",
    "plotAdvancement": "对话推进手法：信息承接方式 → 新信息揭示技巧 → 信息整合手法",
    "writingTechniques": "对话写作特色：角色A语言特色 + 角色B语言特色 + 互动模式特征",
    "toneCharacteristics": ["承接语气特征：前文延续方式 + 本章变化表现", "独立语气特征：语言特征 + 句式结构特点", "过渡语气特征：转换方式 + 新建立手法"],
    "stylePatterns": ["承接行文特色：前文延续方式 + 本章特色表现", "独立行文特色：对话 + 动作 + 心理的结合方式", "过渡行文特色：承接技巧 + 转换手法 + 新建立"],
    "literaryAnalysis": "对话功能运用：承接功能体现 + 推进剧情手法 + 塑造角色技巧 + 营造氛围方式",
    "completeDialogues": [
      {
        "content": "对话内容1",
        "speaker": "说话人1",
        "context": "对话上下文描述",
        "position": 100
      },
      {
        "content": "对话内容2",
        "speaker": "说话人2",
        "context": "对话上下文描述",
        "position": 200
      }
    ],
    "styleAnalysis": {
      "dialogueStyle": "整体对话风格特征描述",
      "characterVoice": "角色语言特色和个性化表达",
      "emotionalTone": "对话的情感基调和氛围",
      "technicalFeatures": "对话写作的技巧特征和手法"
    }
  },
  "styleAnalysis": {
    "writingStyle": "写作风格特征：风格承接方式 + 本章特色表现 + 风格协调技巧",
    "expressionFeatures": "表现手法特征：承接手法运用 + 新手法引入 + 手法融合技巧",
    "practicalMethods": "实用写作特色：承接技巧 + 过渡手法 + 新建立方式",
    "rhythmPatterns": ["承接节奏特征：前文延续方式 + 本章调整手法", "独立节奏特征：短句运用 + 动作描写 + 对话节奏", "过渡节奏特征：节奏转换技巧 + 新节奏建立"],
    "pacingFramework": "节奏控制特征：承接控制方式 + 调整手法 + 新节奏建立技巧",
    "outlineGuidance": "大纲应用特征：承接功能体现 + 本章功能发挥 + 过渡功能处理 + 运用指导"
  },
  "reusablePatterns": ["承接特征：前文关系处理 + 本章结构特点", "独立特征：结构特点 + 运用方式", "过渡特征：承接技巧 + 转换手法 + 新建立"],
  "applicationMethods": ["承接运用：前文分析 + 本章运用 + 调整要点", "独立运用：适用场景 + 调整要点", "过渡运用：承接处理 + 转换技巧"]
}`);

    return messageBuilder.build();
  }

  /**
   * 构建带进度回调的分析消息
   */
  private buildAnalysisMessagesWithProgress(
    chapters: Chapter[],
    options: AnalysisOptions,
    onProgress?: (current: number, total: number, currentChapter?: Chapter) => void
  ): Array<{ role: string; content: string }> {
    const messageBuilder = new MessageBuilder();

    // 系统消息：定义AI角色
    messageBuilder.addSystemMessage(`你是一位专业的写作技巧分析师，专门提取实际文本中的具体写作方法。
你的任务是分析提供的章节内容，提取作者实际使用的写作技巧和框架模式。

核心分析重点（三个维度深度分析）：
1. **情节分析**：
   - 故事结构的具体构造、冲突设计的实际手法、节奏控制的具体方法
   - 提取具体剧情点，每个剧情点要拆解出行为框架，供融合模仿
   - 分析剧情推进的具体模式和可复用的行为框架

2. **对话分析**：
   - 提取章节中的完整对话列表，包含说话人、对话内容、上下文和位置信息
   - 分析对话的具体写法、推进情节的实际手法、对话写作技巧
   - 深入分析对话风格特征：整体风格、角色语言特色、情感基调、技巧特征
   - 分析对话的语气特征和风格偏向，进行文学化分析
   - 提取抽象的行文框架模式，便于复刻对话塑造的语气等

3. **风格分析**：
   - 写作风格特征、表现手法运用、实用技巧方法
   - 提取具体的节奏控制模式和节奏框架
   - 提供可作为核心大纲参照的节奏控制指导

分析原则：
- 只分析文本中实际存在的技巧，严禁推测作者意图
- 提取可以直接模仿和缝合的具体方法
- 关注实际的写作手法，而非理论分析
- 重点是"如何写的"，而不是"为什么这样写"
- 专注于框架模式的识别和技巧的提取

输出要求：
- 具体的写作手法描述
- 可直接复用的技巧模式
- 实际的操作方法
- 能够直接应用的框架结构

请按照指定的JSON格式返回分析结果。`);

    // 助手确认消息
    messageBuilder.addAssistantMessage('我将分析这些章节的实际写作手法，提取具体的框架结构、情节安排和对话设计技巧，专注于可直接应用的写作方法。');

    // 章节信息概述
    messageBuilder.addUserMessage(`【章节分析任务】
分析深度：${options.analysisDepth}
章节数量：${chapters.length}个
包含对话分析：${options.includeDialogue ? '是' : '否'}`);

    // 智能分段发送每个章节的内容（带进度回调）
    chapters.forEach((chapter, index) => {
      // 调用进度回调
      onProgress?.(index + 1, chapters.length, chapter);

      // 章节基本信息
      const chapterHeader = `【正在分析第${index + 1}章：${chapter.title}】
标题：${chapter.title}
字数：${chapter.wordCount}字
顺序：第${chapter.order}章
摘要：${chapter.summary || '无'}`;

      messageBuilder.addUserMessage(chapterHeader);
      messageBuilder.addAssistantMessage(`我正在分析第${index + 1}章"${chapter.title}"，已了解其基本信息。`);

      // 智能分段发送章节内容
      const segments = this.segmentChapterContent(chapter.content);
      segments.forEach((segment, segIndex) => {
        const segmentInfo = `【第${index + 1}章 - 段落 ${segIndex + 1}/${segments.length}】
${segment}`;

        messageBuilder.addSystemMessage(segmentInfo);
        messageBuilder.addAssistantMessage(`我已阅读第${index + 1}章第${segIndex + 1}段内容，正在分析其写作技巧。`);
      });

      // 提取并分析对话（对话超细提取）
      const dialogues = this.extractDialogues(chapter.content);
      if (dialogues.length > 0) {
        console.log(`📝 第${index + 1}章提取到${dialogues.length}句对话`);

        // 将对话分组（每四句一组）
        const dialogueGroups = this.groupDialogues(dialogues);

        // 发送对话分析消息
        messageBuilder.addSystemMessage(`【第${index + 1}章对话超细提取】
提取到${dialogues.length}句对话，分为${dialogueGroups.length}组进行分析：`);

        dialogueGroups.forEach((group, groupIndex) => {
          const groupInfo = `【对话组${groupIndex + 1}/${dialogueGroups.length}】
${group.dialogues.map((d, i) => `${i + 1}. ${d.speaker ? `${d.speaker}：` : ''}「${d.content}」`).join('\n')}`;

          messageBuilder.addSystemMessage(groupInfo);
          messageBuilder.addAssistantMessage(`我已分析对话组${groupIndex + 1}，包含${group.dialogues.length}句对话，正在提取对话技巧和梗句模式。`);
        });
      }

      messageBuilder.addUserMessage(`【第${index + 1}章"${chapter.title}"分析完成】请继续分析下一章节。`);
      messageBuilder.addAssistantMessage(`第${index + 1}章"${chapter.title}"的内容分析完成，已提取其创作技巧和框架模式。`);
    });

    // 最终分析指令
    messageBuilder.addUserMessage(`请基于以上章节内容，按照以下JSON格式返回实际的写作技巧分析：

{
  "frameworkName": "提取的写作特征名称（基于章节实际内容）",
  "frameworkPattern": "具体的写作结构特征，如：细腻环境描写 → 人物心理刻画 → 对话推进情节 → 情感升华收尾",
  "frameworkVariables": ["核心写作元素1", "核心写作元素2", "核心写作元素3"],
  "patternType": "写作风格类型（如：心理描写流、对话推进流、环境渲染流等）",
  "plotAnalysis": {
    "storyStructure": "故事结构的具体构造方式（章节安排+情节推进+场景构建）",
    "conflictDesign": "冲突设计的实际手法（冲突类型+升级方法+解决技巧）",
    "rhythmControl": "节奏控制的具体方法（转场技巧+节奏控制+连接方法）",
    "plotPoints": ["具体剧情点1", "具体剧情点2", "具体剧情点3"],
    "behaviorFrameworks": ["行为表现特征1", "行为表现特征2", "行为表现特征3"]
  },
  "dialogueAnalysis": {
    "dialogueStructure": "对话的具体结构和格式",
    "plotAdvancement": "对话推进情节的具体方法",
    "writingTechniques": "对话写作的实际技巧",
    "toneCharacteristics": ["语气特征1", "语气特征2", "语气特征3"],
    "stylePatterns": ["行文框架模式1", "行文框架模式2", "行文框架模式3"],
    "literaryAnalysis": "文学化的对话塑造分析，包括语气偏向和风格特点",
    "completeDialogues": [
      {
        "content": "对话内容1",
        "speaker": "说话人1",
        "context": "对话上下文描述",
        "position": 100
      },
      {
        "content": "对话内容2",
        "speaker": "说话人2",
        "context": "对话上下文描述",
        "position": 200
      }
    ],
    "styleAnalysis": {
      "dialogueStyle": "整体对话风格特征描述",
      "characterVoice": "角色语言特色和个性化表达",
      "emotionalTone": "对话的情感基调和氛围",
      "technicalFeatures": "对话写作的技巧特征和手法"
    }
  },
  "styleAnalysis": {
    "writingStyle": "写作风格特征（风格特色+技巧手法+表达特点）",
    "expressionFeatures": "表现手法运用（表现特色+技术手法运用）",
    "practicalMethods": "实用技巧方法（实用方法+应用技巧）",
    "rhythmPatterns": ["节奏特征1", "节奏特征2", "节奏特征3"],
    "pacingFramework": "节奏控制特征的具体描述",
    "outlineGuidance": "可作为核心大纲参照的节奏控制指导"
  },
  "reusablePatterns": ["可借鉴的写作特征1", "可借鉴的写作特征2"],
  "applicationMethods": ["具体运用方法1", "具体运用方法2"]
}`);

    return messageBuilder.build();
  }

  /**
   * 构建分析消息
   */
  private buildAnalysisMessages(chapters: Chapter[], options: AnalysisOptions): Array<{ role: string; content: string }> {
    const messageBuilder = new MessageBuilder();

    // 系统消息：定义AI角色
    messageBuilder.addSystemMessage(`你是一位专业的写作技巧分析师，专门提取实际文本中的具体写作方法。
你的任务是分析提供的章节内容，提取作者实际使用的写作技巧和框架模式。

核心分析重点（三个维度深度分析）：
1. **情节分析**：
   - 故事结构的具体构造、冲突设计的实际手法、节奏控制的具体方法
   - 提取具体剧情点，每个剧情点要拆解出行为框架，供融合模仿
   - 分析剧情推进的具体模式和可复用的行为框架

2. **对话分析**：
   - 提取章节中的完整对话列表，包含说话人、对话内容、上下文和位置信息
   - 分析对话的具体写法、推进情节的实际手法、对话写作技巧
   - 深入分析对话风格特征：整体风格、角色语言特色、情感基调、技巧特征
   - 分析对话的语气特征和风格偏向，进行文学化分析
   - 提取抽象的行文框架模式，便于复刻对话塑造的语气等

3. **风格分析**：
   - 写作风格特征、表现手法运用、实用技巧方法
   - 提取具体的节奏控制模式和节奏框架
   - 提供可作为核心大纲参照的节奏控制指导

分析原则：
- 只分析文本中实际存在的技巧，严禁推测作者意图
- 提取可以直接模仿和缝合的具体方法
- 关注实际的写作手法，而非理论分析
- 重点是"如何写的"，而不是"为什么这样写"
- 专注于框架模式的识别和技巧的提取

输出要求：
- 具体的写作手法描述
- 可直接复用的技巧模式
- 实际的操作方法
- 能够直接应用的框架结构

请按照指定的JSON格式返回分析结果。`);

    // 助手确认消息
    messageBuilder.addAssistantMessage('我将分析这些章节的实际写作手法，提取具体的框架结构、情节安排和对话设计技巧，专注于可直接应用的写作方法。');

    // 章节信息概述
    messageBuilder.addUserMessage(`【章节分析任务】
分析深度：${options.analysisDepth}
章节数量：${chapters.length}个
包含对话分析：${options.includeDialogue ? '是' : '否'}`);

    // 智能分段发送每个章节的内容
    chapters.forEach((chapter, index) => {
      // 章节基本信息
      const chapterHeader = `【章节 ${index + 1}】
标题：${chapter.title}
字数：${chapter.wordCount}字
顺序：第${chapter.order}章
摘要：${chapter.summary || '无'}`;

      messageBuilder.addUserMessage(chapterHeader);
      messageBuilder.addAssistantMessage(`我已了解"${chapter.title}"的基本信息，准备分析其内容。`);

      // 智能分段发送章节内容
      const segments = this.segmentChapterContent(chapter.content);
      segments.forEach((segment, segIndex) => {
        const segmentInfo = `【章节 ${index + 1} - 段落 ${segIndex + 1}/${segments.length}】
${segment}`;

        messageBuilder.addSystemMessage(segmentInfo);
        messageBuilder.addAssistantMessage(`我已阅读第${segIndex + 1}段内容，正在分析其写作技巧和框架特点。`);
      });

      // 提取并分析对话（对话超细提取）
      const dialogues = this.extractDialogues(chapter.content);
      if (dialogues.length > 0) {
        console.log(`📝 章节${index + 1}提取到${dialogues.length}句对话`);

        // 将对话分组（每四句一组）
        const dialogueGroups = this.groupDialogues(dialogues);

        // 发送对话分析消息
        messageBuilder.addSystemMessage(`【章节${index + 1}对话超细提取】
提取到${dialogues.length}句对话，分为${dialogueGroups.length}组进行分析：`);

        dialogueGroups.forEach((group, groupIndex) => {
          const groupInfo = `【对话组${groupIndex + 1}/${dialogueGroups.length}】
${group.dialogues.map((d, i) => `${i + 1}. ${d.speaker ? `${d.speaker}：` : ''}「${d.content}」`).join('\n')}`;

          messageBuilder.addSystemMessage(groupInfo);
          messageBuilder.addAssistantMessage(`我已分析对话组${groupIndex + 1}，包含${group.dialogues.length}句对话，正在提取对话技巧和梗句模式。`);
        });
      }

      messageBuilder.addUserMessage(`【章节 ${index + 1} 分析完成】请继续分析下一章节。`);
      messageBuilder.addAssistantMessage(`"${chapter.title}"的内容分析完成，已提取其创作技巧和框架模式。`);
    });

    // 最终分析指令
    messageBuilder.addUserMessage(`请基于以上章节内容，按照以下JSON格式返回实际的写作技巧分析：

{
  "frameworkName": "提取的写作特征名称（基于章节实际内容）",
  "frameworkPattern": "具体的写作结构特征，如：细腻环境描写 → 人物心理刻画 → 对话推进情节 → 情感升华收尾",
  "frameworkVariables": ["核心写作元素1", "核心写作元素2", "核心写作元素3"],
  "patternType": "写作风格类型（如：心理描写流、对话推进流、环境渲染流等）",
  "plotAnalysis": {
    "storyStructure": "故事结构的具体构造方式（章节安排+情节推进+场景构建）",
    "conflictDesign": "冲突设计的实际手法（冲突类型+升级方法+解决技巧）",
    "rhythmControl": "节奏控制的具体方法（转场技巧+节奏控制+连接方法）",
    "plotPoints": ["具体剧情点1", "具体剧情点2", "具体剧情点3"],
    "behaviorFrameworks": ["行为表现特征1", "行为表现特征2", "行为表现特征3"]
  },
  "dialogueAnalysis": {
    "dialogueStructure": "对话的具体结构和格式",
    "plotAdvancement": "对话推进情节的具体方法",
    "writingTechniques": "对话写作的实际技巧",
    "toneCharacteristics": ["语气特征1", "语气特征2", "语气特征3"],
    "stylePatterns": ["行文框架模式1", "行文框架模式2", "行文框架模式3"],
    "literaryAnalysis": "文学化的对话塑造分析，包括语气偏向和风格特点",
    "completeDialogues": [
      {
        "content": "对话内容1",
        "speaker": "说话人1",
        "context": "对话上下文描述",
        "position": 100
      },
      {
        "content": "对话内容2",
        "speaker": "说话人2",
        "context": "对话上下文描述",
        "position": 200
      }
    ],
    "styleAnalysis": {
      "dialogueStyle": "整体对话风格特征描述",
      "characterVoice": "角色语言特色和个性化表达",
      "emotionalTone": "对话的情感基调和氛围",
      "technicalFeatures": "对话写作的技巧特征和手法"
    }
  },
  "styleAnalysis": {
    "writingStyle": "写作风格特征（风格特色+技巧手法+表达特点）",
    "expressionFeatures": "表现手法运用（表现特色+技术手法运用）",
    "practicalMethods": "实用技巧方法（实用方法+应用技巧）",
    "rhythmPatterns": ["节奏特征1", "节奏特征2", "节奏特征3"],
    "pacingFramework": "节奏控制特征的具体描述",
    "outlineGuidance": "可作为核心大纲参照的节奏控制指导"
  },
  "reusablePatterns": ["可借鉴的写作特征1", "可借鉴的写作特征2"],
  "applicationMethods": ["具体运用方法1", "具体运用方法2"]
}`);

    return messageBuilder.build();
  }

  /**
   * 解析AI分析响应
   */
  private parseAnalysisResponse(
    responseText: string,
    chapters: Chapter[],
    options: AnalysisOptions
  ): OutlineFrameworkResult {
    try {
      console.log('🔍 解析AI响应:', responseText.substring(0, 500) + '...');

      // 使用AIResponseParser解析JSON
      const parsedData = AIResponseParser.parseJSON(responseText, {
        frameworkName: `${chapters[0]?.title || '章节'}实际框架`,
        frameworkPattern: "{主角} {事件}后{结果}",
        frameworkVariables: ["主角", "事件", "结果"],
        patternType: "通用创作流",
        plotAnalysis: {
          storyStructure: "故事结构的具体构造方式",
          conflictDesign: "冲突设计的实际手法",
          rhythmControl: "节奏控制的具体方法",
          plotPoints: ["剧情点分析中..."],
          behaviorFrameworks: ["行为框架分析中..."],
          plotPointsWithGuidance: [
            {
              content: "剧情点分析中...",
              specificDescription: "具体描写分析中...",
              avoidanceGuidance: "写作指导生成中..."
            }
          ]
        },
        dialogueAnalysis: {
          dialogueStructure: "对话的具体结构和格式",
          plotAdvancement: "对话推进情节的具体方法",
          writingTechniques: "对话写作的实际技巧",
          toneCharacteristics: ["语气特征分析中..."],
          stylePatterns: ["行文框架分析中..."],
          literaryAnalysis: "文学化对话分析中...",
          // 新增字段的默认值
          completeDialogues: [
            {
              content: "对话内容提取中...",
              speaker: "角色名",
              context: "对话上下文分析中...",
              position: 0
            }
          ],
          styleAnalysis: {
            dialogueStyle: "对话风格特征分析中...",
            characterVoice: "角色语言特色分析中...",
            emotionalTone: "情感基调分析中...",
            technicalFeatures: "写作技巧特征分析中..."
          }
        },
        styleAnalysis: {
          writingStyle: "写作风格特征",
          expressionFeatures: "表现手法运用",
          practicalMethods: "实用技巧方法",
          rhythmPatterns: ["节奏模式分析中..."],
          pacingFramework: "节奏控制框架分析中...",
          outlineGuidance: "大纲参照指导分析中..."
        },
        reusablePatterns: ["可直接复用的写作模式"],
        applicationMethods: ["具体应用方法"]
      });

      console.log('✅ AI响应解析成功');

      // 构建完整的结果对象
      const result: OutlineFrameworkResult = {
        id: `framework_${Date.now()}`,
        frameworkName: parsedData.frameworkName,
        frameworkPattern: parsedData.frameworkPattern,
        frameworkVariables: parsedData.frameworkVariables || [],
        patternType: parsedData.patternType,
        extractedFrom: {
          chapterIds: chapters.map(c => c.id!).filter(id => id),
          chapterTitles: chapters.map(c => c.title),
          extractDate: new Date()
        },
        plotAnalysis: parsedData.plotAnalysis,
        dialogueAnalysis: parsedData.dialogueAnalysis,
        styleAnalysis: parsedData.styleAnalysis,
        reusablePatterns: parsedData.reusablePatterns || [],
        applicationMethods: parsedData.applicationMethods || [],
        metadata: {
          analysisDepth: options.analysisDepth,
          confidence: 0.9, // AI分析的置信度较高
          tags: ["AI分析", "框架提取", "情节分析", "对话分析", "风格分析", parsedData.patternType || "通用流"],
          category: "AI生成框架"
        }
      };

      return result;

    } catch (error) {
      console.error('❌ AI响应解析失败:', error);
      throw error; // 直接抛出错误，不使用模拟结果
    }
  }

  /**
   * 合并模式字符串
   */
  private mergePatternsFromResults(results: OutlineFrameworkResult[]): string {
    const patterns = results.map(r => r.frameworkPattern).filter(p => p);
    if (patterns.length === 0) return '批量分析模式';

    // 提取共同的模式元素
    const commonElements = this.extractCommonPatternElements(patterns);
    return commonElements.length > 0 ? commonElements.join(' + ') : patterns[0];
  }

  /**
   * 合并变量列表
   */
  private mergeVariablesFromResults(results: OutlineFrameworkResult[]): string[] {
    const allVariables = results.flatMap(r => r.frameworkVariables || []);
    return Array.from(new Set(allVariables)); // 去重
  }

  /**
   * 合并情节分析
   */
  private mergePlotAnalysis(results: OutlineFrameworkResult[]): any {
    const plotAnalyses = results.map(r => r.plotAnalysis).filter(p => p);
    if (plotAnalyses.length === 0) return {};

    return {
      storyStructure: this.mergeTextFields(plotAnalyses, 'storyStructure', '故事结构'),
      conflictDesign: this.mergeTextFields(plotAnalyses, 'conflictDesign', '冲突设计'),
      rhythmControl: this.mergeTextFields(plotAnalyses, 'rhythmControl', '节奏控制'),
      plotPoints: this.mergeArrayFieldsFromObjects(plotAnalyses, 'plotPoints'),
      behaviorFrameworks: this.mergeArrayFieldsFromObjects(plotAnalyses, 'behaviorFrameworks'),
      plotPointsWithGuidance: this.mergePlotPointsWithGuidance(plotAnalyses)
    };
  }

  /**
   * 合并剧情点写作指导
   */
  private mergePlotPointsWithGuidance(plotAnalyses: any[]): Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }> {
    const allGuidancePoints: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }> = [];

    plotAnalyses.forEach(analysis => {
      if (analysis.plotPointsWithGuidance && Array.isArray(analysis.plotPointsWithGuidance)) {
        allGuidancePoints.push(...analysis.plotPointsWithGuidance);
      }
    });

    return allGuidancePoints;
  }

  /**
   * 合并对话分析
   */
  private mergeDialogueAnalysis(results: OutlineFrameworkResult[]): any {
    const dialogueAnalyses = results.map(r => r.dialogueAnalysis).filter(d => d);
    if (dialogueAnalyses.length === 0) return {};

    return {
      dialogueStructure: this.mergeTextFields(dialogueAnalyses, 'dialogueStructure', '对话结构'),
      plotAdvancement: this.mergeTextFields(dialogueAnalyses, 'plotAdvancement', '推进方式'),
      writingTechniques: this.mergeTextFields(dialogueAnalyses, 'writingTechniques', '写作技巧'),
      toneCharacteristics: this.mergeArrayFieldsFromObjects(dialogueAnalyses, 'toneCharacteristics'),
      stylePatterns: this.mergeArrayFieldsFromObjects(dialogueAnalyses, 'stylePatterns'),
      literaryAnalysis: this.mergeTextFields(dialogueAnalyses, 'literaryAnalysis', '文学化分析'),
      // 合并新字段
      completeDialogues: this.mergeCompleteDialogues(dialogueAnalyses),
      styleAnalysis: this.mergeDialogueStyleAnalysis(dialogueAnalyses)
    };
  }

  /**
   * 合并完整对话列表
   */
  private mergeCompleteDialogues(dialogueAnalyses: any[]): Array<{
    content: string;
    speaker?: string;
    context: string;
    position: number;
  }> {
    const allDialogues: Array<{
      content: string;
      speaker?: string;
      context: string;
      position: number;
    }> = [];

    dialogueAnalyses.forEach(analysis => {
      if (analysis.completeDialogues && Array.isArray(analysis.completeDialogues)) {
        allDialogues.push(...analysis.completeDialogues);
      }
    });

    // 按位置排序并去重
    return allDialogues
      .sort((a, b) => a.position - b.position)
      .filter((dialogue, index, arr) =>
        index === 0 || dialogue.content !== arr[index - 1].content
      );
  }

  /**
   * 合并对话风格分析
   */
  private mergeDialogueStyleAnalysis(dialogueAnalyses: any[]): any {
    const styleAnalyses = dialogueAnalyses.map(a => a.styleAnalysis).filter(s => s);
    if (styleAnalyses.length === 0) {
      return {
        dialogueStyle: "批量分析的对话风格",
        characterVoice: "批量分析的角色语言特色",
        emotionalTone: "批量分析的情感基调",
        technicalFeatures: "批量分析的技巧特征"
      };
    }

    return {
      dialogueStyle: this.mergeTextFields(styleAnalyses, 'dialogueStyle', '对话风格'),
      characterVoice: this.mergeTextFields(styleAnalyses, 'characterVoice', '角色语言特色'),
      emotionalTone: this.mergeTextFields(styleAnalyses, 'emotionalTone', '情感基调'),
      technicalFeatures: this.mergeTextFields(styleAnalyses, 'technicalFeatures', '技巧特征')
    };
  }

  /**
   * 合并风格分析
   */
  private mergeStyleAnalysis(results: OutlineFrameworkResult[]): any {
    const styleAnalyses = results.map(r => r.styleAnalysis).filter(s => s);
    if (styleAnalyses.length === 0) return {};

    return {
      writingStyle: this.mergeTextFields(styleAnalyses, 'writingStyle', '写作风格'),
      expressionFeatures: this.mergeTextFields(styleAnalyses, 'expressionFeatures', '表现特色'),
      practicalMethods: this.mergeTextFields(styleAnalyses, 'practicalMethods', '实用方法'),
      rhythmPatterns: this.mergeArrayFieldsFromObjects(styleAnalyses, 'rhythmPatterns'),
      pacingFramework: this.mergeTextFields(styleAnalyses, 'pacingFramework', '节奏控制框架'),
      outlineGuidance: this.mergeTextFields(styleAnalyses, 'outlineGuidance', '大纲参照指导')
    };
  }

  /**
   * 合并数组字段
   */
  private mergeArrayFields(results: OutlineFrameworkResult[], fieldPath: string): string[] {
    const allItems: string[] = [];

    results.forEach(result => {
      const value = this.getNestedValue(result, fieldPath);
      if (Array.isArray(value)) {
        allItems.push(...value);
      }
    });

    return Array.from(new Set(allItems)); // 去重
  }

  /**
   * 从对象数组中合并数组字段
   */
  private mergeArrayFieldsFromObjects(objects: any[], fieldName: string): string[] {
    const allItems: string[] = [];

    objects.forEach(obj => {
      const value = obj[fieldName];
      if (Array.isArray(value)) {
        allItems.push(...value);
      }
    });

    return Array.from(new Set(allItems)); // 去重
  }

  /**
   * 合并文本字段
   */
  private mergeTextFields(objects: any[], fieldName: string, fieldLabel: string): string {
    const texts = objects.map(obj => obj[fieldName]).filter(text => text && text.trim());
    if (texts.length === 0) return `批量分析的${fieldLabel}`;

    if (texts.length === 1) return texts[0];

    // 多个文本合并
    return texts.map((text, index) => `**章节${index + 1}${fieldLabel}**：\n${text}`).join('\n\n');
  }

  /**
   * 计算平均置信度
   */
  private calculateAverageConfidence(results: OutlineFrameworkResult[]): number {
    const confidences = results.map(r => r.metadata?.confidence || 0.8).filter(c => c > 0);
    if (confidences.length === 0) return 0.8;

    return confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 提取共同的模式元素
   */
  private extractCommonPatternElements(patterns: string[]): string[] {
    // 简单实现：提取包含{}的变量部分
    const allElements = patterns.flatMap(pattern => {
      const matches = pattern.match(/\{[^}]+\}/g) || [];
      return matches;
    });

    // 统计频次，返回出现频率高的元素
    const elementCounts = allElements.reduce((counts, element) => {
      counts[element] = (counts[element] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    // 返回出现次数大于1的元素
    return Object.entries(elementCounts)
      .filter(([_, count]) => count > 1)
      .map(([element, _]) => element);
  }

}
