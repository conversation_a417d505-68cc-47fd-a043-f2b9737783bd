"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';

interface ChapterSearchBarProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  className?: string;
}

/**
 * 章节搜索栏组件
 * 支持实时搜索和防抖优化
 */
export const ChapterSearchBar: React.FC<ChapterSearchBarProps> = ({
  onSearch,
  placeholder = "搜索章节...",
  className = ""
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖搜索函数
  const debouncedSearch = useCallback((query: string) => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的定时器
    debounceTimerRef.current = setTimeout(() => {
      onSearch(query);
    }, 300);
  }, [onSearch]);

  // 当搜索词变化时触发防抖搜索
  useEffect(() => {
    debouncedSearch(searchQuery);

    // 清理函数
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [searchQuery, debouncedSearch]);

  // 清空搜索
  const clearSearch = () => {
    setSearchQuery('');
    onSearch('');
  };

  return (
    <div className={`relative ${className}`}>
      {/* 搜索框 */}
      <div
        className={`relative flex items-center transition-all duration-300 ease-out transform ${
          isFocused ? 'ring-2 ring-blue-500 ring-opacity-50 scale-105 shadow-lg' : 'scale-100 shadow-sm'
        }`}
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(2px)',
          borderRadius: '8px',
          border: `1px solid ${isFocused ? 'rgba(59, 130, 246, 0.3)' : 'rgba(0, 0, 0, 0.1)'}`,
          height: '40px',
          transition: 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)'
        }}
      >
        {/* 搜索图标 */}
        <div className="absolute left-3 flex items-center pointer-events-none">
          <svg
            className={`w-4 h-4 transition-all duration-300 ease-out transform ${
              isFocused ? 'text-blue-500 scale-110' : 'text-gray-400 scale-100'
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        {/* 输入框 */}
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 bg-transparent border-0 focus:outline-none focus:ring-0 text-sm"
          style={{
            color: 'var(--color-text-primary)',
            fontSize: '14px'
          }}
        />

        {/* 清空按钮 */}
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute right-3 flex items-center justify-center w-5 h-5 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-200 transform hover:scale-110 active:scale-95 animate-in fade-in slide-in-from-right-2"
            title="清空搜索"
          >
            <svg
              className="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>

      {/* 搜索结果提示 */}
      {searchQuery && (
        <div
          className="absolute top-full left-0 right-0 mt-1 px-3 py-1 text-xs rounded"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(1px)',
            border: '1px solid rgba(0, 0, 0, 0.1)',
            color: 'var(--color-text-secondary)',
            zIndex: 10
          }}
        >
          搜索: "{searchQuery}"
        </div>
      )}
    </div>
  );
};

/**
 * 搜索匹配高亮组件
 */
interface HighlightTextProps {
  text: string;
  searchQuery: string;
  className?: string;
}

export const HighlightText: React.FC<HighlightTextProps> = ({
  text,
  searchQuery,
  className = ""
}) => {
  if (!searchQuery) {
    return <span className={className}>{text}</span>;
  }

  const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'));

  return (
    <span className={className}>
      {parts.map((part, index) =>
        part.toLowerCase() === searchQuery.toLowerCase() ? (
          <mark
            key={index}
            className="bg-yellow-200 text-yellow-900 px-0.5 rounded"
            style={{ backgroundColor: 'rgba(255, 235, 59, 0.3)' }}
          >
            {part}
          </mark>
        ) : (
          part
        )
      )}
    </span>
  );
};

/**
 * 搜索工具函数
 */
export const searchChapters = (chapters: any[], query: string) => {
  if (!query.trim()) {
    return chapters;
  }

  const searchTerm = query.toLowerCase();

  return chapters.filter(chapter => {
    // 搜索章节标题
    const titleMatch = chapter.title?.toLowerCase().includes(searchTerm);

    // 搜索章节内容（去除HTML标签）
    const content = chapter.content?.replace(/<[^>]*>/g, '') || '';
    const contentMatch = content.toLowerCase().includes(searchTerm);

    return titleMatch || contentMatch;
  });
};

/**
 * 搜索结果排序函数
 */
export const sortSearchResults = (chapters: any[], query: string) => {
  if (!query.trim()) {
    return chapters.sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  const searchTerm = query.toLowerCase();

  return chapters.sort((a, b) => {
    // 标题完全匹配优先
    const aTitleExact = a.title?.toLowerCase() === searchTerm;
    const bTitleExact = b.title?.toLowerCase() === searchTerm;
    if (aTitleExact && !bTitleExact) return -1;
    if (!aTitleExact && bTitleExact) return 1;

    // 标题包含搜索词优先
    const aTitleMatch = a.title?.toLowerCase().includes(searchTerm);
    const bTitleMatch = b.title?.toLowerCase().includes(searchTerm);
    if (aTitleMatch && !bTitleMatch) return -1;
    if (!aTitleMatch && bTitleMatch) return 1;

    // 按原始顺序排序
    return (a.order || 0) - (b.order || 0);
  });
};
