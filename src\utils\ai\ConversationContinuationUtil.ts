"use client";

import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { ConversationMessage } from '@/factories/ai/services/AIWritingService';

/**
 * 对话继续模式
 */
export type ContinueMode = 'new' | 'continue' | 'rewrite' | 'analyze';

/**
 * 对话继续参数
 */
export interface ConversationContinuationParams {
  // 当前内容
  currentContent: string;

  // 用户的继续提示
  continuePrompt: string;

  // 继续模式
  continueMode: ContinueMode;

  // 书籍ID（用于本地存储）
  bookId: string;

  // 现有对话历史
  conversationHistory?: ConversationMessage[];

  // 服务类型（writing 或 rewrite）
  serviceType?: 'writing' | 'rewrite';

  // 注意：始终保留完整上下文，不再支持简化上下文模式

  // 关联消息（如章节、角色等信息）
  relatedMessages?: ConversationMessage[];
}

/**
 * 对话继续结果
 */
export interface ConversationContinuationResult {
  // 用于显示的前缀（包含原内容和用户提示）
  prefix: string;

  // 用户提示
  userPrompt: string;

  // 更新后的对话历史
  updatedHistory: ConversationMessage[];
}

/**
 * 构建用户提示
 * @param continuePrompt 继续提示
 * @param continueMode 继续模式
 * @returns 构建后的用户提示
 */
export function buildUserPrompt(continuePrompt: string, continueMode: ContinueMode): string {
  // 如果用户提示已经包含模式前缀，则不再添加
  const hasContinuePrefix = continuePrompt.includes('请继续上述内容，保持一致的风格和情节走向');
  const hasRewritePrefix = continuePrompt.includes('请重写上述内容，但保持核心情节不变');
  const hasAnalyzePrefix = continuePrompt.includes('请分析上述内容，提供深入的文学分析和建议');

  switch (continueMode) {
    case 'continue':
      return hasContinuePrefix ? continuePrompt : `请继续上述内容，保持一致的风格和情节走向。${continuePrompt}`;
    case 'rewrite':
      return hasRewritePrefix ? continuePrompt : `请重写上述内容，但保持核心情节不变。${continuePrompt}`;
    case 'analyze':
      return hasAnalyzePrefix ? continuePrompt : `请分析上述内容，提供深入的文学分析和建议。${continuePrompt}`;
    default:
      return continuePrompt;
  }
}

/**
 * 构建前缀
 * @param currentContent 当前内容
 * @param userPrompt 用户提示
 * @param continueMode 继续模式
 * @returns 构建后的前缀
 */
export function buildPrefix(currentContent: string, userPrompt: string, continueMode: ContinueMode): string {
  switch (continueMode) {
    case 'continue':
      // 继续对话模式，直接接续原内容
      // 添加一个换行符，确保新内容在新段落中开始
      return currentContent + '\n\n【USER_PROMPT】' + userPrompt + '【/USER_PROMPT】\n\n';
    case 'rewrite':
      // 重写模式，添加一个分隔符
      return `${currentContent}\n\n【USER_PROMPT】${userPrompt}【/USER_PROMPT】\n\n【重写内容】\n`;
    case 'analyze':
      // 分析模式，添加一个分隔符
      return `${currentContent}\n\n【USER_PROMPT】${userPrompt}【/USER_PROMPT】\n\n【分析】\n`;
    default:
      return currentContent + '\n\n【USER_PROMPT】' + userPrompt + '【/USER_PROMPT】\n\n';
  }
}

/**
 * 处理对话继续
 *
 * 这个函数处理对话的继续，包括保留完整的上下文消息和关联消息。
 *
 * @param params 对话继续参数
 * @returns 对话继续结果
 */
export function handleConversationContinuation(params: ConversationContinuationParams): ConversationContinuationResult {
  const {
    currentContent,
    continuePrompt,
    continueMode,
    bookId,
    conversationHistory = [],
    serviceType = 'writing',
    relatedMessages = []
  } = params;

  // 获取用户的继续提示
  const userPrompt = buildUserPrompt(continuePrompt, continueMode);

  // 保存用户提示，用于在UI中显示
  if (typeof localStorage !== 'undefined') {
    // 根据服务类型选择不同的localStorage键
    const promptKey = serviceType === 'writing'
      ? `ai-writing-user-prompt-${bookId}`
      : `ai-rewrite-user-prompt-${bookId}`;
    localStorage.setItem(promptKey, userPrompt);
  }

  // 构建前缀，包含原内容和用户提示的标记
  const prefix = buildPrefix(currentContent, userPrompt, continueMode);

  // 保存前缀，用于后续拼接
  if (typeof localStorage !== 'undefined') {
    // 根据服务类型选择不同的localStorage键
    const prefixKey = serviceType === 'writing'
      ? `ai-writing-prefix-${bookId}`
      : `ai-rewrite-prefix-${bookId}`;
    localStorage.setItem(prefixKey, prefix);
  }

  // 更新对话历史
  let updatedHistory: ConversationMessage[] = [];

  if (conversationHistory.length > 0) {
    // 始终保留完整上下文：保留所有现有消息，添加当前内容和用户的继续提示
    console.log(`保留完整${serviceType}对话上下文，历史长度:`, conversationHistory.length);

    // 复制现有对话历史
    updatedHistory = [...conversationHistory];

    // 添加当前内容作为助手消息（如果最后一条不是助手消息）
    const lastMessage = updatedHistory[updatedHistory.length - 1];
    if (lastMessage && lastMessage.role !== 'assistant') {
      updatedHistory.push({
        role: 'assistant',
        content: currentContent
      });
    } else if (lastMessage && lastMessage.role === 'assistant') {
      // 如果最后一条是助手消息，更新其内容
      lastMessage.content = currentContent;
    } else {
      // 如果没有消息，添加当前内容
      updatedHistory.push({
        role: 'assistant',
        content: currentContent
      });
    }

    // 添加用户的继续提示
    updatedHistory.push({
      role: 'user',
      content: userPrompt
    });

    // 添加关联消息（如果有）
    if (relatedMessages.length > 0) {
      // 先移除用户提示消息
      updatedHistory.pop();

      // 添加关联消息
      updatedHistory = [
        ...updatedHistory,
        ...relatedMessages
      ];

      // 最后添加用户提示消息，确保它在历史的最后面
      updatedHistory.push({
        role: 'user',
        content: userPrompt
      });

      console.log(`已添加${relatedMessages.length}条关联消息，并将用户提示放在最后`);
    }
  } else {
    console.log('对话历史为空，创建新的对话历史');
    // 如果对话历史为空，创建一个新的对话历史
    updatedHistory = [];

    // 添加关联消息（如果有）
    if (relatedMessages.length > 0) {
      updatedHistory.push(...relatedMessages);
      console.log(`已添加${relatedMessages.length}条关联消息`);
    }

    // 最后添加用户提示消息，确保它在历史的最后面
    updatedHistory.push({
      role: 'user',
      content: userPrompt
    });
  }

  // 更新本地存储
  if (typeof localStorage !== 'undefined') {
    // 根据服务类型选择不同的localStorage键
    const historyKey = serviceType === 'writing'
      ? `ai-writing-history-${bookId}`
      : `ai-rewrite-history-${bookId}`;
    localStorage.setItem(historyKey, JSON.stringify(updatedHistory));
  }

  console.log(`已更新${serviceType}对话历史，长度:`, updatedHistory.length);

  return {
    prefix,
    userPrompt,
    updatedHistory
  };
}

/**
 * 构建消息
 * @param messageBuilder 消息构建器
 * @param conversationHistory 对话历史
 * @param continueMode 继续模式
 * @param continuePrompt 继续提示
 * @param serviceType 服务类型
 */
export function buildContinuationMessages(
  messageBuilder: MessageBuilder,
  conversationHistory: ConversationMessage[],
  continueMode: ContinueMode,
  continuePrompt?: string,
  serviceType: 'writing' | 'rewrite' = 'writing'
): void {
  if (conversationHistory.length > 0 && continueMode !== 'new') {
    console.log(`使用现有${serviceType}对话历史继续对话，历史长度:`, conversationHistory.length);

    // 不再过滤对话历史，保留所有消息，包括关联消息
    // 将完整对话历史添加到消息构建器
    console.log('添加完整对话历史，包括关联消息');

    // 记录所有消息类型，用于调试
    const messageTypes = conversationHistory.map(msg => msg.role);
    console.log('对话历史中的消息类型:', messageTypes);

    // 记录消息内容的前20个字符，用于调试
    const messageContents = conversationHistory.map(msg => ({
      role: msg.role,
      content: msg.content.substring(0, 20) + '...'
    }));
    console.log('对话历史中的消息内容预览:', messageContents);

    conversationHistory.forEach(msg => {
      if (msg.role === 'system') {
        messageBuilder.addSystemMessage(msg.content);
      } else if (msg.role === 'user') {
        messageBuilder.addUserMessage(msg.content);
      } else if (msg.role === 'assistant') {
        messageBuilder.addAssistantMessage(msg.content);
      }
    });

    // 如果提供了继续提示，则添加到消息构建器
    if (continuePrompt) {
      // 检查最后一条消息是否已经是用户提示
      const lastMessage = conversationHistory[conversationHistory.length - 1];
      const isLastMessageUserPrompt = lastMessage && lastMessage.role === 'user';

      // 如果最后一条不是用户消息，或者用户明确要求添加新的提示，则添加
      if (!isLastMessageUserPrompt) {
        // 构建用户提示
        const userPrompt = buildUserPrompt(continuePrompt, continueMode);
        messageBuilder.addUserMessage(userPrompt);
        console.log('已添加继续提示:', userPrompt);
      } else {
        console.log('最后一条消息已经是用户提示，不再重复添加');
      }
    }

    console.log('已添加完整对话历史，长度:', conversationHistory.length);
  }
}
