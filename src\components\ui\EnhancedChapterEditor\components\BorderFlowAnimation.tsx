"use client";

import React from 'react';

interface BorderFlowAnimationProps {
  isHovered: boolean;
  isFocused: boolean;
  speed?: 'slow' | 'normal' | 'fast';
}

/**
 * 边框流光动画组件
 * 在编辑器边框周围创建优雅的金色流光效果
 */
export const BorderFlowAnimation: React.FC<BorderFlowAnimationProps> = ({
  isHovered,
  isFocused,
  speed = 'normal'
}) => {
  // 根据状态调整动画速度
  const getAnimationDuration = () => {
    if (speed === 'fast' || isHovered) return '8s';
    if (speed === 'slow') return '15s';
    return '12s';
  };

  // 根据焦点状态调整透明度
  const getOpacity = () => {
    if (isFocused) return 0.6;
    if (isHovered) return 0.4;
    return 0.3;
  };

  return (
    <div className="absolute inset-0 pointer-events-none rounded-xl overflow-hidden">
      <svg
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{
          opacity: getOpacity(),
          transition: 'opacity 0.3s ease-in-out'
        }}
      >
        {/* 定义渐变 */}
        <defs>
          <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="transparent" />
            <stop offset="30%" stopColor="#D4AF37" stopOpacity="0.8" />
            <stop offset="50%" stopColor="#FFD700" stopOpacity="1" />
            <stop offset="70%" stopColor="#D4AF37" stopOpacity="0.8" />
            <stop offset="100%" stopColor="transparent" />
          </linearGradient>

          {/* 定义路径动画 */}
          <style>
            {`
              .border-flow-path {
                stroke: url(#borderGradient);
                stroke-width: 0.5;
                fill: none;
                stroke-dasharray: 20 80;
                stroke-dashoffset: 100;
                animation-name: borderFlow;
                animation-duration: ${getAnimationDuration()};
                animation-timing-function: ease-in-out;
                animation-iteration-count: infinite;
              }

              .border-flow-path-top {
                animation-delay: 0s;
              }

              .border-flow-path-right {
                animation-delay: 0.25s;
              }

              .border-flow-path-bottom {
                animation-delay: 0.5s;
              }

              .border-flow-path-left {
                animation-delay: 0.75s;
              }

              @keyframes borderFlow {
                0% {
                  stroke-dashoffset: 100;
                }
                100% {
                  stroke-dashoffset: 0;
                }
              }

              @media (prefers-reduced-motion: reduce) {
                .border-flow-path {
                  animation: none;
                  stroke-dasharray: none;
                  stroke-dashoffset: 0;
                  opacity: 0.2;
                }
              }
            `}
          </style>
        </defs>

        {/* 边框路径 - 顶部 */}
        <path
          className="border-flow-path border-flow-path-top"
          d="M 2 2 L 98 2"
        />

        {/* 边框路径 - 右侧 */}
        <path
          className="border-flow-path border-flow-path-right"
          d="M 98 2 L 98 98"
        />

        {/* 边框路径 - 底部 */}
        <path
          className="border-flow-path border-flow-path-bottom"
          d="M 98 98 L 2 98"
        />

        {/* 边框路径 - 左侧 */}
        <path
          className="border-flow-path border-flow-path-left"
          d="M 2 98 L 2 2"
        />

        {/* 角落装饰 */}
        <g className="corner-decorations" style={{ opacity: isFocused ? 0.8 : 0.4 }}>
          {/* 左上角 */}
          <circle
            cx="2"
            cy="2"
            r="1"
            fill="#D4AF37"
            style={{
              animationName: 'cornerPulse',
              animationDuration: getAnimationDuration(),
              animationTimingFunction: 'ease-in-out',
              animationIterationCount: 'infinite',
              animationDelay: '0s'
            }}
          />

          {/* 右上角 */}
          <circle
            cx="98"
            cy="2"
            r="1"
            fill="#D4AF37"
            style={{
              animationName: 'cornerPulse',
              animationDuration: getAnimationDuration(),
              animationTimingFunction: 'ease-in-out',
              animationIterationCount: 'infinite',
              animationDelay: '0.25s'
            }}
          />

          {/* 右下角 */}
          <circle
            cx="98"
            cy="98"
            r="1"
            fill="#D4AF37"
            style={{
              animationName: 'cornerPulse',
              animationDuration: getAnimationDuration(),
              animationTimingFunction: 'ease-in-out',
              animationIterationCount: 'infinite',
              animationDelay: '0.5s'
            }}
          />

          {/* 左下角 */}
          <circle
            cx="2"
            cy="98"
            r="1"
            fill="#D4AF37"
            style={{
              animationName: 'cornerPulse',
              animationDuration: getAnimationDuration(),
              animationTimingFunction: 'ease-in-out',
              animationIterationCount: 'infinite',
              animationDelay: '0.75s'
            }}
          />
        </g>

        <style>
          {`
            @keyframes cornerPulse {
              0%, 100% {
                opacity: 0.3;
                transform: scale(1);
              }
              50% {
                opacity: 1;
                transform: scale(1.5);
              }
            }

            @media (prefers-reduced-motion: reduce) {
              .corner-decorations circle {
                animation: none !important;
                opacity: 0.3 !important;
                transform: scale(1) !important;
              }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default BorderFlowAnimation;
