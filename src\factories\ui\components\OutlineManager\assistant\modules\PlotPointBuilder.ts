/**
 * 剧情点构建模块
 * 专门管理plotPoints的创建、验证和优化
 */

import { WritingGuidanceBuilder } from './WritingGuidanceBuilder';

export interface PlotPoint {
  id: string;
  order?: number;
  content: string;
  avoidWriting?: string;
  shouldWriting?: string;
  type?: 'action' | 'dialogue' | 'revelation' | 'conflict' | 'transition' | 'setup' | 'resolution' | 'twist';
  characters?: string[];
  consequences?: string;
  nextPlotHint?: string;

  /**
   * 写作风格方法指导（可选）
   */
  styleMethod?: {
    technique: string;
    style: string;
    tone: string;
    perspective: string;
    emphasis: string;
  };

  /**
   * 格式规范（可选）
   */
  formatSpecs?: {
    wordCount: {
      min: number;
      max: number;
      target: number;
    };
    paragraphRules: {
      maxSentencesPerParagraph: number;
      paragraphBreakRules: string;
      conflictHandling?: string;        // 冲突处理规范
      actionDialogueFlow?: string;      // 对话行动流程规范
      mandatoryBreaks?: string;         // 强制换行要求
    };
    punctuationRules: {
      dialogueFormat: string;
      emphasisFormat: string;
      pauseFormat: string;
      conflictPunctuation?: string;     // 冲突标点规范
      naturalFlow?: string;             // 自然流畅要求
    };
    lineBreakRules: {
      sceneTransition: string;
      timeTransition: string;
      speakerChange: string;
      conflictEscalation?: string;      // 冲突升级换行规范
      actionEmphasis?: string;          // 行动强调换行规范
      emotionShift?: string;            // 情绪转折换行规范
      prohibitedMerging?: string;       // 禁止合并规范
    };
  };
}

export interface PlotPointTemplate {
  name: string;
  description: string;
  structure: string[];
  examples: PlotPoint[];
}

export class PlotPointBuilder {
  private static instance: PlotPointBuilder;
  private writingGuidanceBuilder: WritingGuidanceBuilder;

  private constructor() {
    this.writingGuidanceBuilder = WritingGuidanceBuilder.getInstance();
  }

  public static getInstance(): PlotPointBuilder {
    if (!PlotPointBuilder.instance) {
      PlotPointBuilder.instance = new PlotPointBuilder();
    }
    return PlotPointBuilder.instance;
  }

  /**
   * 构建标准剧情点结构
   */
  buildStandardPlotPoints(
    plotType: 'conflict' | 'twist' | 'climax' | 'resolution',
    characterNames: string[] = [],
    plotContext?: any
  ): PlotPoint[] {
    const templates = this.getPlotPointTemplates();
    const template = templates[plotType];
    
    if (!template) {
      return this.buildBasicPlotPoints(characterNames, plotContext);
    }

    return template.examples.map((example, index) => ({
      ...example,
      id: `point_${Date.now()}_${index + 1}`,
      characters: characterNames.length > 0 ? characterNames : example.characters
    }));
  }

  /**
   * 获取剧情点模板库
   */
  private getPlotPointTemplates(): Record<string, PlotPointTemplate> {
    return {
      conflict: {
        name: '冲突剧情点模板',
        description: '用于构建冲突类型的剧情节点',
        structure: ['矛盾引入', '冲突升级', '对抗行动', '结果展现'],
        examples: [
          {
            id: 'conflict_001',
            content: '[主角]发现[对手]的真实意图，双方立场彻底对立',
            avoidWriting: '避免描写"一丝愤怒"、"几分震惊"等模糊表达；避免"像火山爆发一样"的比喻；避免环境描写拖慢节奏；避免"他愤怒地说"等主观描述',
            shouldWriting: '眼神瞬间锐利，直视对方："原来如此。"\n对方冷笑："现在知道也不晚。"\n空气中弥漫着剑拔弩张的气息，两人的立场已经不可调和。',
            type: 'conflict',
            characters: ['主角', '对手']
          },
          {
            id: 'conflict_002',
            content: '[主角]采取直接行动对抗[对手]，展现决心和能力',
            avoidWriting: '避免"一丝犹豫"等心理活动描写；避免"如同闪电"等比喻；避免过多情感渲染和场景铺垫；避免"他迅速地行动"等主观描述',
            shouldWriting: '身体前倾，双手迅速移动，三个动作一气呵成。\n对方立即后退两步，举起防护姿态。\n整个过程不到五秒，胜负已见分晓。',
            type: 'action',
            characters: ['主角', '对手']
          }
        ]
      },
      twist: {
        name: '转折剧情点模板',
        description: '用于构建转折类型的剧情节点',
        structure: ['铺垫暗示', '关键信息', '认知颠覆', '新局面'],
        examples: [
          {
            id: 'twist_001',
            content: '[关键角色]揭示隐藏的真相，彻底改变[主角]对局势的认知',
            avoidWriting: '避免"一丝困惑"、"几分震撼"等模糊表达；避免"如雷贯耳"等比喻；避免过多心理描写；避免"他震惊地发现"等主观描述',
            shouldWriting: '"事实是这样的..."关键角色缓缓开口，说出了三个关键信息。\n主角的手停在半空中，眼神从疑惑转为清明。\n"原来如此。"声音平静，但整个人的姿态已经完全不同。',
            type: 'revelation',
            characters: ['主角', '关键角色']
          },
          {
            id: 'twist_002',
            content: '[主角]基于新信息重新制定计划，展现适应能力',
            avoidWriting: '避免"一丝迟疑"等思考过程描写；避免"灵光一闪"等比喻；避免冗长的内心独白；避免"他思考着"等主观描述',
            shouldWriting: '立即调整策略，改变原定的三个步骤。\n新的计划更加直接：先确保安全，再收集信息，最后采取行动。\n整个调整过程不到一分钟。',
            type: 'action',
            characters: ['主角']
          }
        ]
      },
      climax: {
        name: '高潮剧情点模板',
        description: '用于构建高潮类型的剧情节点',
        structure: ['最终对决', '关键选择', '决定性行动', '结果确定'],
        examples: [
          {
            id: 'climax_001',
            content: '[主角]在关键时刻做出决定性选择，承担所有后果',
            avoidWriting: '避免"一丝犹豫"、"几分坚定"等模糊表达；避免"破釜沉舟"等成语比喻；避免过多情感铺垫；避免"他坚定地决定"等主观描述',
            shouldWriting: '深吸一口气，做出了选择："我来承担。"\n没有回头，没有犹豫，直接走向那扇门。\n身后的声音渐渐远去，前方是未知的后果。',
            type: 'action',
            characters: ['主角']
          },
          {
            id: 'climax_002',
            content: '[主角]展现最强能力，与[最终对手]进行决定性对抗',
            avoidWriting: '避免"一丝保留"等模糊表达；避免"势如破竹"等比喻；避免过多技能解释；避免"他全力以赴"等主观描述',
            shouldWriting: '双手同时发力，释放出最强的攻击。\n对手立即反击，两股力量在空中碰撞。\n胜负在这一瞬间决定。',
            type: 'conflict',
            characters: ['主角', '最终对手']
          }
        ]
      },
      resolution: {
        name: '解决剧情点模板',
        description: '用于构建解决类型的剧情节点',
        structure: ['问题解决', '后果处理', '关系调整', '新平衡'],
        examples: [
          {
            id: 'resolution_001',
            content: '[主角]处理冲突的后续影响，建立新的秩序',
            avoidWriting: '避免"一丝疲惫"、"几分满足"等模糊表达；避免"尘埃落定"等比喻；避免过多情感描写；避免"他满意地看着"等主观描述',
            shouldWriting: '制定了三条新规则，并逐一向相关人员说明。\n每个人都明确了自己的职责和权限。\n新的秩序开始运转，一切井然有序。',
            type: 'action',
            characters: ['主角']
          },
          {
            id: 'resolution_002',
            content: '[主角]与相关角色确认新的关系和合作模式',
            avoidWriting: '避免"一丝和解"等模糊表达；避免"握手言和"等比喻；避免复杂的情感变化描写；避免"他们友好地"等主观描述',
            shouldWriting: '"从今天开始，我们按照新的合作方式进行。"\n各方代表逐一确认了具体的合作条款。\n新的关系正式建立，未来的合作有了明确的框架。',
            type: 'dialogue',
            characters: ['主角', '相关角色']
          }
        ]
      }
    };
  }

  /**
   * 构建基础剧情点（当没有特定模板时）
   */
  private buildBasicPlotPoints(characterNames: string[], plotContext?: any): PlotPoint[] {
    const basicPoints: PlotPoint[] = [
      {
        id: `point_${Date.now()}_1`,
        content: `${characterNames[0] || '[主角]'}发现异常情况：具体做了什么发现了什么`,
        avoidWriting: '避免使用"一丝困惑"、"几分紧张"等模糊表达；避免过多环境描写',
        shouldWriting: '直接描写角色的具体动作和发现的具体内容',
        type: 'action',
        characters: characterNames
      },
      {
        id: `point_${Date.now()}_2`,
        content: `${characterNames[0] || '[主角]'}采取行动：如何应对，具体的行动和决策`,
        avoidWriting: '避免使用"迅速地"、"立即"等副词；避免心理活动描写',
        shouldWriting: '直接描写角色的具体行动和决策过程',
        type: 'action',
        characters: characterNames
      },
      {
        id: `point_${Date.now()}_3`,
        content: `${characterNames[0] || '[主角]'}遭遇阻碍：遇到什么困难，如何尝试解决`,
        avoidWriting: '避免"一丝xx"、"几分xx"等模糊表达；避免比喻；避免主观描述',
        shouldWriting: `${characterNames[0] || '[主角]'}在[具体场景]中[具体行动]。[环境细节描写]。[对话内容]："[具体对话]"。[角色的具体动作和反应]。`,
        type: 'conflict',
        characters: characterNames
      }
    ];

    return basicPoints;
  }

  /**
   * 验证剧情点的完整性和质量
   */
  validatePlotPoints(plotPoints: PlotPoint[]): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    if (plotPoints.length === 0) {
      issues.push('剧情点数量为0');
      suggestions.push('至少需要3-5个剧情点来构成完整的剧情节点');
      return { isValid: false, issues, suggestions };
    }

    if (plotPoints.length < 3) {
      issues.push('剧情点数量过少');
      suggestions.push('建议增加到3-10个剧情点以丰富剧情内容');
    }

    // 检查每个剧情点
    plotPoints.forEach((point, index) => {
      if (!point.content || point.content.trim().length < 10) {
        issues.push(`剧情点${index + 1}内容过短或为空`);
        suggestions.push(`为剧情点${index + 1}添加具体的行动和情节描述`);
      }

      // 检查新的字段结构
      if (!point.avoidWriting || point.avoidWriting.trim().length < 20) {
        issues.push(`剧情点${index + 1}缺少充分的禁用指导`);
        suggestions.push(`为剧情点${index + 1}添加至少20字的具体禁用指导`);
      }

      if (!point.shouldWriting || point.shouldWriting.trim().length < 30) {
        issues.push(`剧情点${index + 1}缺少充分的推荐写法`);
        suggestions.push(`为剧情点${index + 1}添加至少30字的具体推荐写法`);
      }

      // 检查是否包含具体行动
      if (!this.containsSpecificAction(point.content)) {
        issues.push(`剧情点${index + 1}缺少具体的角色行动`);
        suggestions.push(`在剧情点${index + 1}中明确描述角色做了什么具体的事情`);
      }
    });

    // 检查剧情点之间的连续性
    if (!this.checkPlotContinuity(plotPoints)) {
      issues.push('剧情点之间缺乏逻辑连续性');
      suggestions.push('确保每个剧情点都与前后剧情点有明确的因果关系');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * 检查内容是否包含具体行动
   */
  private containsSpecificAction(content: string): boolean {
    const actionKeywords = [
      '做了', '说了', '决定', '选择', '发现', '采取', '执行', '完成',
      '开始', '停止', '拒绝', '接受', '攻击', '防御', '逃跑', '追击'
    ];
    
    return actionKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 检查剧情点的连续性
   */
  private checkPlotContinuity(plotPoints: PlotPoint[]): boolean {
    if (plotPoints.length < 2) return true;

    // 简单的连续性检查：确保有逻辑词汇连接
    const continuityKeywords = [
      '然后', '接着', '随后', '因此', '所以', '但是', '然而', '于是',
      '结果', '导致', '引发', '促使', '迫使', '最终', '最后'
    ];

    let hasContinuity = false;
    for (let i = 1; i < plotPoints.length; i++) {
      const currentContent = plotPoints[i].content;
      if (continuityKeywords.some(keyword => currentContent.includes(keyword))) {
        hasContinuity = true;
        break;
      }
    }

    return hasContinuity;
  }

  /**
   * 优化剧情点结构
   */
  optimizePlotPoints(plotPoints: PlotPoint[]): PlotPoint[] {
    return plotPoints.map((point, index) => {
      // 确保每个剧情点都有唯一ID
      if (!point.id) {
        point.id = `point_${Date.now()}_${index + 1}`;
      }

      // 优化avoidWriting和shouldWriting
      if (!point.avoidWriting || point.avoidWriting.length < 30) {
        point.avoidWriting = '避免使用模糊表达和套路化比喻';
      }
      if (!point.shouldWriting || point.shouldWriting.length < 30) {
        point.shouldWriting = '使用具体动作和客观叙述';
      }

      // 添加后果描述（如果没有）
      if (!point.consequences && index < plotPoints.length - 1) {
        point.consequences = `为下一个剧情点铺垫：${this.generateConsequenceHint(point.content)}`;
      }

      return point;
    });
  }

  /**
   * 生成后果提示
   */
  private generateConsequenceHint(content: string): string {
    if (content.includes('发现')) {
      return '基于新发现的信息，角色需要做出相应的反应和决策';
    } else if (content.includes('行动') || content.includes('采取')) {
      return '行动的结果将影响后续的剧情发展和角色关系';
    } else if (content.includes('冲突') || content.includes('对抗')) {
      return '冲突的结果将决定力量对比和下一步的策略选择';
    } else {
      return '当前情况的变化将推动剧情向新的方向发展';
    }
  }

  /**
   * 获取剧情点统计信息
   */
  getPlotPointStats(plotPoints: PlotPoint[]): {
    totalCount: number;
    typeDistribution: Record<string, number>;
    averageContentLength: number;
    averageAvoidWritingLength: number;
    averageShouldWritingLength: number;
    hasCharacters: number;
    hasConsequences: number;
  } {
    const typeDistribution: Record<string, number> = {};
    let totalContentLength = 0;
    let totalAvoidWritingLength = 0;
    let totalShouldWritingLength = 0;
    let hasCharacters = 0;
    let hasConsequences = 0;

    plotPoints.forEach(point => {
      // 统计类型分布
      const type = point.type || 'unknown';
      typeDistribution[type] = (typeDistribution[type] || 0) + 1;

      // 统计长度
      totalContentLength += point.content.length;
      totalAvoidWritingLength += (point.avoidWriting || '').length;
      totalShouldWritingLength += (point.shouldWriting || '').length;

      // 统计字段完整性
      if (point.characters && point.characters.length > 0) hasCharacters++;
      if (point.consequences) hasConsequences++;
    });

    return {
      totalCount: plotPoints.length,
      typeDistribution,
      averageContentLength: plotPoints.length > 0 ? Math.round(totalContentLength / plotPoints.length) : 0,
      averageAvoidWritingLength: plotPoints.length > 0 ? Math.round(totalAvoidWritingLength / plotPoints.length) : 0,
      averageShouldWritingLength: plotPoints.length > 0 ? Math.round(totalShouldWritingLength / plotPoints.length) : 0,
      hasCharacters,
      hasConsequences
    };
  }
}
