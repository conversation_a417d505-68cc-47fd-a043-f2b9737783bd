"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EnhancedAssociationData, EnhancedCharacter, EnhancedWorldBuilding, EnhancedTerminology } from './types';

interface EnhancedAssociationPanelProps {
  enhancedData: EnhancedAssociationData;
  selectedType: string | null;
  onDataSelect?: (dataType: string, items: any[]) => void;
}

/**
 * 增强的关联数据面板组件
 * 显示完整的角色、世界观、术语数据
 */
const EnhancedAssociationPanel: React.FC<EnhancedAssociationPanelProps> = ({
  enhancedData,
  selectedType,
  onDataSelect
}) => {
  const [activeTab, setActiveTab] = useState<'characters' | 'worldSettings' | 'glossary'>('characters');
  const [selectedItems, setSelectedItems] = useState<{[key: string]: any[]}>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // 处理项目选择
  const handleItemSelect = (dataType: string, item: any) => {
    const currentSelected = selectedItems[dataType] || [];
    const isSelected = currentSelected.some(selected => selected.id === item.id);
    
    let newSelected;
    if (isSelected) {
      newSelected = currentSelected.filter(selected => selected.id !== item.id);
    } else {
      newSelected = [...currentSelected, item];
    }
    
    setSelectedItems(prev => ({
      ...prev,
      [dataType]: newSelected
    }));
    
    onDataSelect?.(dataType, newSelected);
  };

  // 切换项目展开状态
  const toggleItemExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // 过滤数据
  const filterData = (data: any[], searchTerm: string) => {
    if (!searchTerm) return data;
    return data.filter(item => 
      item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  // 渲染角色卡片
  const renderCharacterCard = (character: EnhancedCharacter) => {
    const isSelected = selectedItems.characters?.some(item => item.id === character.id) || false;
    const isExpanded = expandedItems.has(character.id);

    return (
      <motion.div
        key={character.id}
        className={`border rounded-lg p-3 cursor-pointer transition-all ${
          isSelected ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => handleItemSelect('characters', character)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium text-gray-900">{character.name}</h4>
            {character.alias && character.alias.length > 0 && (
              <p className="text-xs text-gray-500 mt-1">别名: {character.alias.join(', ')}</p>
            )}
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{character.description}</p>
            
            {/* 基础信息预览 */}
            <div className="flex flex-wrap gap-1 mt-2">
              {character.personality && (
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                  {character.personality}
                </span>
              )}
              {character.characterArchetype && (
                <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                  {character.characterArchetype}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2 ml-2">
            {isSelected && (
              <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleItemExpanded(character.id);
              }}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <svg 
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* 展开的详细信息 */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="mt-3 pt-3 border-t border-gray-200 overflow-hidden"
            >
              <div className="space-y-2 text-sm">
                {character.appearance && (
                  <div><strong>外貌:</strong> {character.appearance}</div>
                )}
                {character.background && (
                  <div><strong>背景:</strong> {character.background}</div>
                )}
                {character.goals && (
                  <div><strong>目标:</strong> {character.goals}</div>
                )}
                {character.hiddenMotivation && (
                  <div><strong>隐藏动机:</strong> {character.hiddenMotivation}</div>
                )}
                {character.secretHistory && (
                  <div><strong>秘密历史:</strong> {character.secretHistory}</div>
                )}
                {character.innerConflicts && (
                  <div><strong>内心冲突:</strong> {character.innerConflicts}</div>
                )}
                {character.growthArc && (
                  <div><strong>成长弧线:</strong> {character.growthArc}</div>
                )}
                {character.symbolism && (
                  <div><strong>象征意义:</strong> {character.symbolism}</div>
                )}
                
                {/* 关系信息 */}
                {character.relationships && character.relationships.length > 0 && (
                  <div>
                    <strong>人物关系:</strong>
                    <ul className="mt-1 space-y-1">
                      {character.relationships.map((rel, index) => (
                        <li key={index} className="text-xs bg-gray-50 p-2 rounded">
                          <span className="font-medium">{rel.relationshipType}:</span> {rel.description}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {/* 关联数据统计 */}
                <div className="flex space-x-4 text-xs text-gray-500">
                  <span>关联角色: {character.relatedCharacterIds.length}</span>
                  <span>关联术语: {character.relatedTerminologyIds.length}</span>
                  <span>关联世界观: {character.relatedWorldBuildingIds.length}</span>
                </div>
                
                {/* 元数据 */}
                <div className="text-xs text-gray-400">
                  更新时间: {new Date(character.updatedAt).toLocaleDateString()}
                  {character.notes && <div className="mt-1">备注: {character.notes}</div>}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  // 渲染世界观卡片
  const renderWorldBuildingCard = (worldBuilding: EnhancedWorldBuilding) => {
    const isSelected = selectedItems.worldSettings?.some(item => item.id === worldBuilding.id) || false;
    const isExpanded = expandedItems.has(worldBuilding.id);

    return (
      <motion.div
        key={worldBuilding.id}
        className={`border rounded-lg p-3 cursor-pointer transition-all ${
          isSelected ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => handleItemSelect('worldSettings', worldBuilding)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="font-medium text-gray-900">{worldBuilding.name}</h4>
              <span className="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded">
                {worldBuilding.category}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{worldBuilding.description}</p>
          </div>
          
          <div className="flex items-center space-x-2 ml-2">
            {isSelected && (
              <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleItemExpanded(worldBuilding.id);
              }}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <svg 
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* 展开的详细信息 */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="mt-3 pt-3 border-t border-gray-200 overflow-hidden"
            >
              <div className="space-y-2 text-sm">
                {/* 自定义属性 */}
                {worldBuilding.attributes && Object.keys(worldBuilding.attributes).length > 0 && (
                  <div>
                    <strong>属性:</strong>
                    <div className="mt-1 space-y-1">
                      {Object.entries(worldBuilding.attributes).map(([key, value]) => (
                        <div key={key} className="text-xs bg-gray-50 p-2 rounded">
                          <span className="font-medium">{key}:</span> {value}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* 关联数据统计 */}
                <div className="flex space-x-4 text-xs text-gray-500">
                  <span>关联角色: {worldBuilding.relatedCharacterIds.length}</span>
                  <span>关联术语: {worldBuilding.relatedTerminologyIds.length}</span>
                  <span>关联世界观: {worldBuilding.relatedWorldBuildingIds.length}</span>
                </div>
                
                {/* 元数据 */}
                <div className="text-xs text-gray-400">
                  更新时间: {new Date(worldBuilding.updatedAt).toLocaleDateString()}
                  {worldBuilding.notes && <div className="mt-1">备注: {worldBuilding.notes}</div>}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  // 渲染术语卡片
  const renderTerminologyCard = (terminology: EnhancedTerminology) => {
    const isSelected = selectedItems.glossary?.some(item => item.id === terminology.id) || false;
    const isExpanded = expandedItems.has(terminology.id);

    return (
      <motion.div
        key={terminology.id}
        className={`border rounded-lg p-3 cursor-pointer transition-all ${
          isSelected ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => handleItemSelect('glossary', terminology)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="font-medium text-gray-900">{terminology.name}</h4>
              <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">
                {terminology.category}
              </span>
            </div>
            {terminology.alias && terminology.alias.length > 0 && (
              <p className="text-xs text-gray-500 mt-1">别名: {terminology.alias.join(', ')}</p>
            )}
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{terminology.description}</p>
          </div>
          
          <div className="flex items-center space-x-2 ml-2">
            {isSelected && (
              <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleItemExpanded(terminology.id);
              }}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <svg 
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* 展开的详细信息 */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="mt-3 pt-3 border-t border-gray-200 overflow-hidden"
            >
              <div className="space-y-2 text-sm">
                {/* 自定义属性 */}
                {terminology.attributes && Object.keys(terminology.attributes).length > 0 && (
                  <div>
                    <strong>属性:</strong>
                    <div className="mt-1 space-y-1">
                      {Object.entries(terminology.attributes).map(([key, value]) => (
                        <div key={key} className="text-xs bg-gray-50 p-2 rounded">
                          <span className="font-medium">{key}:</span> {value}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* 关联数据统计 */}
                <div className="flex space-x-4 text-xs text-gray-500">
                  <span>关联角色: {terminology.relatedCharacterIds.length}</span>
                  <span>关联术语: {terminology.relatedTerminologyIds.length}</span>
                  <span>关联世界观: {terminology.relatedWorldBuildingIds.length}</span>
                </div>
                
                {/* 元数据 */}
                <div className="text-xs text-gray-400">
                  更新时间: {new Date(terminology.updatedAt).toLocaleDateString()}
                  {terminology.notes && <div className="mt-1">备注: {terminology.notes}</div>}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部：标签切换和搜索 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">关联数据 (增强模式)</h3>
          <div className="text-sm text-gray-500">
            已选择: {Object.values(selectedItems).flat().length} 项
          </div>
        </div>
        
        {/* 标签切换 */}
        <div className="flex space-x-1 mb-3">
          {[
            { key: 'characters', label: '角色', count: enhancedData.characters.length },
            { key: 'worldSettings', label: '世界观', count: enhancedData.worldSettings.length },
            { key: 'glossary', label: '术语', count: enhancedData.glossary.length }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-emerald-100 text-emerald-800'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
        
        {/* 搜索框 */}
        <div className="relative">
          <input
            type="text"
            placeholder={`搜索${activeTab === 'characters' ? '角色' : activeTab === 'worldSettings' ? '世界观' : '术语'}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
          />
          <svg
            className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {activeTab === 'characters' && 
            filterData(enhancedData.characters, searchTerm).map(renderCharacterCard)
          }
          {activeTab === 'worldSettings' && 
            filterData(enhancedData.worldSettings, searchTerm).map(renderWorldBuildingCard)
          }
          {activeTab === 'glossary' && 
            filterData(enhancedData.glossary, searchTerm).map(renderTerminologyCard)
          }
        </div>
        
        {/* 空状态 */}
        {((activeTab === 'characters' && filterData(enhancedData.characters, searchTerm).length === 0) ||
          (activeTab === 'worldSettings' && filterData(enhancedData.worldSettings, searchTerm).length === 0) ||
          (activeTab === 'glossary' && filterData(enhancedData.glossary, searchTerm).length === 0)) && (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-gray-500">
              {searchTerm ? '没有找到匹配的数据' : `暂无${activeTab === 'characters' ? '角色' : activeTab === 'worldSettings' ? '世界观' : '术语'}数据`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedAssociationPanel;
