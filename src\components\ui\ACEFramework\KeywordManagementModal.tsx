/**
 * 关键词管理弹窗组件
 * 用于管理用户保存的关键词，支持逐条筛选、编辑、删除
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ExtendedACEFramework } from '../../../types/ACEFrameworkTypes';

interface KeywordItem {
  text: string;
  hotness: number;
  tags: string[];
  frequency?: number;
  id?: string;
  createdAt?: Date;
  lastUsedAt?: Date;
}

interface KeywordManagementModalProps {
  isOpen: boolean;
  framework: ExtendedACEFramework | null;
  onClose: () => void;
  onSave: (keywords: KeywordItem[]) => void;
}

export const KeywordManagementModal: React.FC<KeywordManagementModalProps> = ({
  isOpen,
  framework,
  onClose,
  onSave
}) => {
  const [keywords, setKeywords] = useState<KeywordItem[]>([]);
  const [selectedKeywords, setSelectedKeywords] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'text' | 'hotness' | 'frequency'>('hotness');

  // 加载关键词数据
  useEffect(() => {
    if (framework && framework.keywordElements) {
      setKeywords(framework.keywordElements);
      // 默认全选
      setSelectedKeywords(new Set(framework.keywordElements.map(k => k.text)));
    }
  }, [framework]);

  // 过滤和排序关键词
  const filteredAndSortedKeywords = keywords
    .filter(keyword => 
      keyword.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
      keyword.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'text':
          return a.text.localeCompare(b.text);
        case 'hotness':
          return (b.hotness || 0) - (a.hotness || 0);
        case 'frequency':
          return (b.frequency || 0) - (a.frequency || 0);
        default:
          return 0;
      }
    });

  // 切换关键词选择
  const toggleKeyword = (text: string) => {
    const newSelected = new Set(selectedKeywords);
    if (newSelected.has(text)) {
      newSelected.delete(text);
    } else {
      newSelected.add(text);
    }
    setSelectedKeywords(newSelected);
  };

  // 全选/全不选
  const toggleSelectAll = () => {
    if (selectedKeywords.size === filteredAndSortedKeywords.length) {
      setSelectedKeywords(new Set());
    } else {
      setSelectedKeywords(new Set(filteredAndSortedKeywords.map(k => k.text)));
    }
  };

  // 保存选择
  const handleSave = () => {
    const selectedKeywordItems = keywords.filter(k => selectedKeywords.has(k.text));
    onSave(selectedKeywordItems);
    onClose();
  };

  // 删除关键词
  const deleteKeyword = (text: string) => {
    setKeywords(prev => prev.filter(k => k.text !== text));
    setSelectedKeywords(prev => {
      const newSet = new Set(prev);
      newSet.delete(text);
      return newSet;
    });
  };

  if (!isOpen || !framework) return null;

  return createPortal(
    <div className="fixed inset-0 z-[60] flex items-center justify-center">
      {/* 背景遮罩 */}
      <motion.div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />
      
      {/* 弹窗内容 */}
      <motion.div 
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              管理关键词 - {framework.name}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              选择要保留的关键词，取消选择的关键词将被移除
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和操作栏 */}
        <div className="p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-3 mb-3">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="搜索关键词或标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
              <svg className="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            >
              <option value="hotness">按热度排序</option>
              <option value="frequency">按频次排序</option>
              <option value="text">按名称排序</option>
            </select>
            
            <button
              onClick={toggleSelectAll}
              className="px-3 py-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              {selectedKeywords.size === filteredAndSortedKeywords.length ? '全不选' : '全选'}
            </button>
          </div>
          
          <div className="text-xs text-gray-500 dark:text-gray-400">
            已选择 {selectedKeywords.size} / {keywords.length} 个关键词
          </div>
        </div>

        {/* 关键词列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <AnimatePresence>
              {filteredAndSortedKeywords.map((keyword) => (
                <KeywordCard
                  key={keyword.text}
                  keyword={keyword}
                  isSelected={selectedKeywords.has(keyword.text)}
                  onToggle={() => toggleKeyword(keyword.text)}
                  onDelete={() => deleteKeyword(keyword.text)}
                />
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            将保存 {selectedKeywords.size} 个关键词
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              保存选择
            </button>
          </div>
        </div>
      </motion.div>
    </div>,
    document.body
  );
};

// 关键词卡片组件
interface KeywordCardProps {
  keyword: KeywordItem;
  isSelected: boolean;
  onToggle: () => void;
  onDelete: () => void;
}

const KeywordCard: React.FC<KeywordCardProps> = ({
  keyword,
  isSelected,
  onToggle,
  onDelete
}) => {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 relative ${
        isSelected
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
      }`}
      onClick={onToggle}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center mb-1">
            <span className={`font-medium text-sm ${
              isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-900 dark:text-gray-100'
            }`}>
              {keyword.text}
            </span>
            <span className="ml-2 text-xs text-yellow-600 dark:text-yellow-400">
              🔥{keyword.hotness}
            </span>
          </div>
          
          {keyword.tags && keyword.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-1">
              {keyword.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="px-1.5 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
          
          {keyword.frequency && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              使用 {keyword.frequency} 次
            </div>
          )}
        </div>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
          title="删除关键词"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </motion.div>
  );
};

export default KeywordManagementModal;
