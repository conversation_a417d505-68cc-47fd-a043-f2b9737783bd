import { db } from '../db/dexie';
import { ChapterRepository } from '../../db/chapterRepository';

/**
 * 数据修复工具类
 * 用于检测和修复数据一致性问题
 */
export class DataRepairUtils {
  private chapterRepository = new ChapterRepository();

  /**
   * 检测重复章节
   * @param bookId 书籍ID
   * @returns 重复章节的信息
   */
  async detectDuplicateChapters(bookId: string) {
    try {
      const chapters = await this.chapterRepository.getChaptersByBookId(bookId);
      
      // 按order分组，找出重复的
      const orderGroups: { [order: number]: any[] } = {};
      
      chapters.forEach(chapter => {
        if (!orderGroups[chapter.order]) {
          orderGroups[chapter.order] = [];
        }
        orderGroups[chapter.order].push(chapter);
      });

      // 找出有重复的order
      const duplicates: { order: number; chapters: any[] }[] = [];
      Object.entries(orderGroups).forEach(([order, chapterList]) => {
        if (chapterList.length > 1) {
          duplicates.push({
            order: parseInt(order),
            chapters: chapterList
          });
        }
      });

      return {
        hasDuplicates: duplicates.length > 0,
        duplicates,
        totalChapters: chapters.length
      };
    } catch (error) {
      console.error('检测重复章节失败:', error);
      return {
        hasDuplicates: false,
        duplicates: [],
        totalChapters: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 修复重复章节
   * @param bookId 书籍ID
   * @param strategy 修复策略：'keep-first' | 'keep-latest' | 'merge-content'
   * @returns 修复结果
   */
  async repairDuplicateChapters(
    bookId: string, 
    strategy: 'keep-first' | 'keep-latest' | 'merge-content' = 'keep-latest'
  ) {
    try {
      const detection = await this.detectDuplicateChapters(bookId);
      
      if (!detection.hasDuplicates) {
        return {
          success: true,
          message: '没有发现重复章节',
          repaired: 0
        };
      }

      let repairedCount = 0;

      for (const duplicate of detection.duplicates) {
        const { order, chapters } = duplicate;
        
        let chapterToKeep;
        let chaptersToDelete;

        switch (strategy) {
          case 'keep-first':
            // 保留最早创建的章节
            chapters.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            chapterToKeep = chapters[0];
            chaptersToDelete = chapters.slice(1);
            break;
            
          case 'keep-latest':
            // 保留最新创建的章节
            chapters.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            chapterToKeep = chapters[0];
            chaptersToDelete = chapters.slice(1);
            break;
            
          case 'merge-content':
            // 合并内容到最新的章节
            chapters.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            chapterToKeep = chapters[0];
            chaptersToDelete = chapters.slice(1);
            
            // 合并所有章节的内容
            const mergedContent = chapters
              .map(ch => ch.content)
              .filter(content => content && content.trim())
              .join('\n\n---\n\n');
            
            if (mergedContent !== chapterToKeep.content) {
              await this.chapterRepository.updateChapter(chapterToKeep.id!, {
                content: mergedContent
              });
            }
            break;
        }

        // 删除重复的章节
        for (const chapterToDelete of chaptersToDelete) {
          await this.chapterRepository.deleteChapter(chapterToDelete.id!);
          repairedCount++;
        }
      }

      return {
        success: true,
        message: `成功修复 ${repairedCount} 个重复章节`,
        repaired: repairedCount,
        strategy
      };
    } catch (error) {
      console.error('修复重复章节失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '修复失败',
        repaired: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 检查数据完整性
   * @param bookId 书籍ID
   * @returns 完整性检查结果
   */
  async checkDataIntegrity(bookId: string) {
    try {
      const chapters = await this.chapterRepository.getChaptersByBookId(bookId);
      
      const issues: string[] = [];
      
      // 检查章节顺序是否连续
      const orders = chapters.map(ch => ch.order).sort((a, b) => a - b);
      for (let i = 0; i < orders.length; i++) {
        if (orders[i] !== i) {
          issues.push(`章节顺序不连续：期望 ${i}，实际 ${orders[i]}`);
        }
      }

      // 检查是否有重复章节
      const duplicateCheck = await this.detectDuplicateChapters(bookId);
      if (duplicateCheck.hasDuplicates) {
        issues.push(`发现 ${duplicateCheck.duplicates.length} 组重复章节`);
      }

      // 检查章节是否有无效数据
      chapters.forEach((chapter, index) => {
        if (!chapter.id) {
          issues.push(`章节 ${index} 缺少ID`);
        }
        if (!chapter.title || chapter.title.trim() === '') {
          issues.push(`章节 ${index} 缺少标题`);
        }
        if (chapter.order < 0) {
          issues.push(`章节 ${index} 顺序值无效：${chapter.order}`);
        }
      });

      return {
        isHealthy: issues.length === 0,
        issues,
        totalChapters: chapters.length,
        duplicateInfo: duplicateCheck
      };
    } catch (error) {
      console.error('数据完整性检查失败:', error);
      return {
        isHealthy: false,
        issues: [error instanceof Error ? error.message : '检查失败'],
        totalChapters: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 自动修复数据问题
   * @param bookId 书籍ID
   * @returns 修复结果
   */
  async autoRepairData(bookId: string) {
    try {
      const results = [];

      // 1. 修复重复章节
      const duplicateRepair = await this.repairDuplicateChapters(bookId, 'keep-latest');
      results.push(`重复章节修复: ${duplicateRepair.message}`);

      // 2. 重新排序章节
      const chapters = await this.chapterRepository.getChaptersByBookId(bookId);
      const sortedChapters = chapters.sort((a, b) => a.order - b.order);
      
      let reorderCount = 0;
      for (let i = 0; i < sortedChapters.length; i++) {
        if (sortedChapters[i].order !== i) {
          await this.chapterRepository.updateChapter(sortedChapters[i].id!, {
            order: i
          });
          reorderCount++;
        }
      }
      
      if (reorderCount > 0) {
        results.push(`重新排序了 ${reorderCount} 个章节`);
      }

      return {
        success: true,
        message: '数据修复完成',
        results
      };
    } catch (error) {
      console.error('自动修复数据失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '修复失败',
        results: [],
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }
}

// 创建并导出实例
export const dataRepairUtils = new DataRepairUtils();
