"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

import { SettingsDialog } from '@/adapters/settings';
import { CreateBookDialog, ImportBookDialog, DeleteConfirmDialog } from '@/adapters/book';
import { FadeAnimation, ScaleAnimation } from '@/adapters/animation';
import { Book, BookRepository } from '@/db';
import { chapterRepository } from '@/lib/db/repositories';
import { BookImportExport } from '@/utils/bookImportExport';
import { AnimatedCard, AnimatedButton, AnimatedCounter } from '@/components/animations/PremiumAnimations';
import { LoadingState, BookCardSkeleton } from '@/components/animations/PremiumLoaders';
import { useNotification, createNotificationHelpers } from '@/components/ui/NotificationSystem';
import { EnhancedDeleteDialog } from '@/components/ui/EnhancedDeleteDialog';
import '../styles/premium-ui.css';
import '../styles/premium-animations.css';

export default function Home() {
  const router = useRouter();
  const bookRepository = new BookRepository();

  // 用户书籍数据状态
  const [books, setBooks] = useState<Book[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 通知系统
  const { addNotification } = useNotification();
  const notify = createNotificationHelpers(addNotification);

  // 拖放状态
  const [isDragging, setIsDragging] = useState(false);
  const [draggedFile, setDraggedFile] = useState<File | null>(null);
  const [importSuccess, setImportSuccess] = useState(false);
  const [importedBookTitle, setImportedBookTitle] = useState('');
  const [importedChapterCount, setImportedChapterCount] = useState(0);

  // 加载书籍数据
  useEffect(() => {
    const loadBooks = async () => {
      try {
        const allBooks = await bookRepository.getAllBooks();
        setBooks(allBooks);
      } catch (error) {
        console.error('加载书籍失败', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadBooks();
  }, []);

  // 字体和设置状态
  const [currentFont, setCurrentFont] = useState<string>('roboto');
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // 对话框状态
  const [isCreateBookDialogOpen, setIsCreateBookDialogOpen] = useState(false);
  const [isImportBookDialogOpen, setIsImportBookDialogOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isEnhancedDeleteOpen, setIsEnhancedDeleteOpen] = useState(false);
  const [selectedBook, setSelectedBook] = useState<Book | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 字体选项映射
  const fontFamilyMap: Record<string, string> = {
    'roboto': 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    'serif': 'Georgia, "Times New Roman", serif',
    'sans': 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    'mono': '"Courier New", monospace'
  };

  // 创建新书函数
  const handleCreateNewBook = () => {
    // 打开创建书籍对话框
    setIsCreateBookDialogOpen(true);
  };

  // 处理创建书籍
  const handleBookCreated = async (title: string, description: string) => {
    try {
      // 创建新书籍
      const newBook = await bookRepository.createBook(
        title,
        description || '点击编辑开始您的创作之旅...'
      );

      // 添加新书到列表
      setBooks(prev => [...prev, newBook]);

      // 关闭对话框
      setIsCreateBookDialogOpen(false);

      // 显示创建成功通知
      notify.success('创建成功', `《${title}》已创建，即将跳转到编辑页面...`);

      // 导航到编辑页面
      setTimeout(() => {
        router.push(`/editor/${newBook.id}`);
      }, 1000);
    } catch (error) {
      console.error('创建书籍失败', error);
      notify.error('创建失败', '创建新书籍时发生错误，请稍后重试');
    }
  };

  // 处理设置按钮点击
  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  // 处理设置弹窗关闭
  const handleSettingsClose = () => {
    setIsSettingsOpen(false);
  };

  // 处理字体变更
  const handleFontChange = (font: string) => {
    setCurrentFont(font);
  };

  // 处理删除按钮点击
  const handleDeleteClick = (book: Book) => {
    setSelectedBook(book);
    setIsEnhancedDeleteOpen(true);
  };

  // 处理删除确认
  const handleDeleteConfirm = async () => {
    if (!selectedBook) return;

    setIsDeleting(true);

    try {
      const success = await bookRepository.deleteBook(selectedBook.id!);
      if (success) {
        // 从列表中移除已删除的书籍
        setBooks(prev => prev.filter(book => book.id !== selectedBook.id));

        // 显示删除成功通知（带撤销功能）
        notify.deleteSuccess(selectedBook.title, () => {
          // 撤销删除的逻辑（这里可以实现软删除和恢复）
          notify.info('撤销功能', '撤销删除功能正在开发中...');
        });
      } else {
        notify.error('删除失败', '无法删除该作品，请稍后重试');
      }
    } catch (error) {
      console.error('删除书籍失败', error);
      notify.error('删除失败', '删除过程中发生错误，请稍后重试');
    } finally {
      setIsDeleting(false);
      setIsEnhancedDeleteOpen(false);
      setSelectedBook(null);
    }
  };

  // 处理导出按钮点击
  const handleExportClick = async (book: Book, format: 'md' | 'txt') => {
    try {
      // 显示导出开始通知
      notify.info('开始导出', `正在导出《${book.title}》为 ${format.toUpperCase()} 格式...`);

      // 获取书籍的所有章节
      const chapters = await chapterRepository.getAllByBookId(book.id!);

      // 导出书籍
      BookImportExport.exportBook(book, chapters, format);

      // 显示导出成功通知
      notify.success('导出成功', `《${book.title}》已成功导出为 ${format.toUpperCase()} 格式`);
    } catch (error) {
      console.error('导出书籍失败', error);
      notify.error('导出失败', `导出《${book.title}》时发生错误，请稍后重试`);
    }
  };

  // 处理导入按钮点击
  const handleImportClick = () => {
    setIsImportBookDialogOpen(true);
  };

  // 处理导入完成
  const handleImportComplete = () => {
    // 关闭对话框
    setIsImportBookDialogOpen(false);

    // 重新加载书籍列表
    const loadBooks = async () => {
      try {
        const allBooks = await bookRepository.getAllBooks();
        setBooks(allBooks);
      } catch (error) {
        console.error('加载书籍失败', error);
      }
    };

    loadBooks();
  };

  // 处理拖放事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 检查是否有文件被拖动
    const hasFiles = e.dataTransfer.types.includes('Files');

    if (hasFiles && !isDragging) {
      setIsDragging(true);
    }
  };

  // 使用计数器来处理嵌套元素的拖拽事件
  const dragCounter = useRef(0);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 检查是否有文件被拖动
    const hasFiles = e.dataTransfer.types.includes('Files');

    if (hasFiles) {
      // 增加计数器
      dragCounter.current++;

      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 减少计数器
    dragCounter.current--;

    // 只有当计数器为0时才设置isDragging为false
    if (dragCounter.current === 0) {
      setIsDragging(false);
    }
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 重置拖放状态和计数器
    setIsDragging(false);
    dragCounter.current = 0;

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];

      // 检查文件类型
      if (file.name.endsWith('.md') || file.name.endsWith('.txt')) {
        setDraggedFile(file);

        try {
          // 读取文件内容
          const content = await readFileContent(file);

          // 解析文件内容
          const { title, description, chapters } = BookImportExport.parseImportedContent(
            content,
            file.name
          );

          // 创建新书籍
          const newBook = await bookRepository.createBook(title, description);

          // 导入前检查是否有冲突章节
          const existingChapters = await chapterRepository.getAllByBookId(newBook.id!);
          if (existingChapters.length > 0) {
            console.warn('⚠️ 新创建的书籍已有章节，可能存在数据问题:', {
              bookId: newBook.id,
              existingCount: existingChapters.length,
              existingChapters: existingChapters.map(ch => ({ id: ch.id, title: ch.title, order: ch.order }))
            });

            // 清理可能的冲突章节
            for (const existingChapter of existingChapters) {
              await chapterRepository.delete(existingChapter.id!);
              console.log('🗑️ 清理冲突章节:', { id: existingChapter.id, title: existingChapter.title });
            }
          }

          console.log('📚 开始拖拽导入章节:', {
            bookId: newBook.id,
            chapterCount: chapters.length,
            bookTitle: title
          });

          // 创建章节 - 使用动态order值
          for (let i = 0; i < chapters.length; i++) {
            const chapter = chapters[i];

            // 获取正确的order值
            const currentOrder = await chapterRepository.getNextOrder(newBook.id!);

            console.log('📝 准备创建章节:', {
              index: i,
              title: chapter.title,
              order: currentOrder, // 使用动态order值
              contentLength: chapter.content?.length || 0,
              contentPreview: chapter.content?.substring(0, 100) || '无内容'
            });

            const chapterId = await chapterRepository.create({
              bookId: newBook.id!,
              title: chapter.title,
              content: chapter.content || '', // 确保content不为undefined
              order: currentOrder, // 使用动态获取的order值
              wordCount: 0, // 将由Repository自动计算
              characterIds: [],
              terminologyIds: [],
              worldBuildingIds: []
            });

            // 验证创建结果
            const verifyChapter = await chapterRepository.getById(chapterId);
            console.log('✅ 章节创建验证:', {
              id: chapterId,
              title: verifyChapter?.title,
              order: verifyChapter?.order,
              hasContent: !!verifyChapter?.content,
              contentLength: verifyChapter?.content?.length || 0
            });

            if (!verifyChapter?.content) {
              console.error('❌ 拖拽导入章节内容丢失:', { chapterId, title: chapter.title, order: currentOrder });
              throw new Error(`章节"${chapter.title}"内容导入失败`);
            }
          }

          // 导入完成后特别验证第一章
          console.log('🔍 验证拖拽导入结果...');
          const allChapters = await chapterRepository.getAllByBookId(newBook.id!);
          const sortedChapters = allChapters.sort((a, b) => a.order - b.order);

          if (sortedChapters.length === 0) {
            throw new Error('拖拽导入失败：没有创建任何章节');
          }

          const firstChapter = sortedChapters[0];
          if (!firstChapter.content || firstChapter.content.trim() === '') {
            console.error('❌ 拖拽导入第一章验证失败:', {
              id: firstChapter.id,
              title: firstChapter.title,
              order: firstChapter.order,
              contentLength: firstChapter.content?.length || 0
            });
            throw new Error(`第一章"${firstChapter.title}"内容为空，拖拽导入失败`);
          }

          console.log('✅ 拖拽导入第一章验证通过:', {
            id: firstChapter.id,
            title: firstChapter.title,
            order: firstChapter.order,
            contentLength: firstChapter.content.length
          });

          console.log('📊 拖拽导入统计:', {
            totalChapters: sortedChapters.length,
            chaptersWithContent: sortedChapters.filter(ch => ch.content && ch.content.trim()).length,
            emptyChapters: sortedChapters.filter(ch => !ch.content || !ch.content.trim()).length
          });

          // 重新加载书籍列表
          const allBooks = await bookRepository.getAllBooks();
          setBooks(allBooks);

          // 清除拖放状态
          setDraggedFile(null);

          // 显示导入成功提示
          setImportedBookTitle(title);
          setImportedChapterCount(chapters.length);
          setImportSuccess(true);

          // 显示章节数量信息
          console.log(`成功导入《${title}》，共 ${chapters.length} 个章节`);

          // 3秒后隐藏提示
          setTimeout(() => {
            setImportSuccess(false);
          }, 3000);
        } catch (error) {
          console.error('导入失败', error);
        }
      }
    }
  };

  // 读取文件内容
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('读取文件失败'));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  };

  // 添加动画样式
  const animationStyle = `
    @keyframes slideInFromBottom {
      from {
        transform: translateY(100px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  `;

  return (
    <main
      className={`min-h-screen premium-background ${isDragging ? 'premium-drag-area' : ''}`}
      style={{
        fontFamily: fontFamilyMap[currentFont] || fontFamilyMap['roboto'],
        position: 'relative',
        padding: '2rem'
      }}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* 添加动画样式 */}
      <style jsx global>{animationStyle}</style>
      {/* 拖放提示层 */}
      {isDragging && (
        <div className="absolute inset-0 flex items-center justify-center z-50 premium-drag-area">
          <div className="premium-card p-8 text-center max-w-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4" style={{ color: 'var(--color-primary)' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
            <h3 className="text-xl font-bold mb-2 premium-title">释放鼠标导入文件</h3>
            <p className="text-sm text-gray-600">
              支持 Markdown (.md) 和文本 (.txt) 格式
            </p>
          </div>
        </div>
      )}

      {/* 导入成功提示 */}
      {importSuccess && (
        <div className="fixed bottom-8 right-8 premium-success z-50 flex items-center max-w-md">
          <div className="bg-white bg-opacity-20 rounded-full p-2 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-white">导入成功</h4>
            <p className="text-sm text-white text-opacity-90">
              已成功导入《{importedBookTitle}》，共 {importedChapterCount} 个章节
            </p>
          </div>
        </div>
      )}
      {/* 设置弹窗 */}
      <SettingsDialog
        isOpen={isSettingsOpen}
        onClose={handleSettingsClose}
        currentFont={currentFont}
        onFontChange={handleFontChange}
      />

      {/* 创建书籍对话框 */}
      <CreateBookDialog
        isOpen={isCreateBookDialogOpen}
        onClose={() => setIsCreateBookDialogOpen(false)}
        onCreateBook={handleBookCreated}
      />

      {/* 导入书籍对话框 */}
      <ImportBookDialog
        isOpen={isImportBookDialogOpen}
        onClose={() => setIsImportBookDialogOpen(false)}
        onImportComplete={handleImportComplete}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        isOpen={isDeleteConfirmOpen}
        book={selectedBook}
        onClose={() => setIsDeleteConfirmOpen(false)}
        onConfirm={handleDeleteConfirm}
      />

      {/* 增强删除对话框 */}
      <EnhancedDeleteDialog
        isOpen={isEnhancedDeleteOpen}
        book={selectedBook}
        onClose={() => {
          if (!isDeleting) {
            setIsEnhancedDeleteOpen(false);
            setSelectedBook(null);
          }
        }}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />

      <div className="max-w-6xl mx-auto">
        <header className="mb-12 flex justify-between items-center">
          <h1 className="text-4xl font-bold premium-title">AI小说平台</h1>
          <div className="flex items-center space-x-4">
            <AnimatedButton
              variant="primary"
              size="md"
              onClick={handleImportClick}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              导入作品
            </AnimatedButton>
            <AnimatedButton
              variant="secondary"
              size="md"
              onClick={handleSettingsClick}
            >
              设置
            </AnimatedButton>
          </div>
        </header>

        <section className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold premium-title">我的书籍</h2>
            <AnimatedButton
              variant="primary"
              size="md"
              onClick={handleCreateNewBook}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              创建新书
            </AnimatedButton>
          </div>

          {isLoading ? (
            <div className="premium-card p-12 text-center">
              <LoadingState
                type="dots"
                size="lg"
                text="正在加载您的作品..."
                color="var(--color-primary)"
              />
            </div>
          ) : books.length === 0 ? (
            <div className="premium-card p-12 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="w-20 h-20 mb-6 flex items-center justify-center rounded-full" style={{ background: 'linear-gradient(135deg, rgba(210, 180, 140, 0.2) 0%, rgba(139, 69, 19, 0.1) 100%)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" style={{ color: 'var(--color-primary)' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <h3 className="text-2xl font-medium mb-3 premium-title">开始您的创作之旅</h3>
                <p className="text-base mb-8 text-gray-600 max-w-md">
                  您还没有创建任何小说，点击下方按钮开始您的第一部作品吧！
                </p>
                <AnimatedButton
                  variant="primary"
                  size="lg"
                  onClick={handleCreateNewBook}
                >
                  创建我的第一本小说
                </AnimatedButton>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {books.map((book, index) => (
                <AnimatedCard
                  key={book.id}
                  delay={index * 100}
                  className="cursor-pointer"
                >
                  <div className="premium-card overflow-hidden">
                      <Link
                        href={`/editor/${book.id}`}
                        className="block group"
                      >
                        <div className="h-40 relative" style={{ background: 'linear-gradient(135deg, rgba(210, 180, 140, 0.1) 0%, rgba(139, 69, 19, 0.05) 100%)' }}>
                          {book.coverImage ? (
                            <Image
                              src={book.coverImage}
                              alt={book.title}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Image
                                src="/book-cover-placeholder.svg"
                                alt={book.title}
                                width={80}
                                height={80}
                                className="opacity-40"
                              />
                            </div>
                          )}
                        </div>
                        <div className="p-6">
                          <h3 className="text-lg font-semibold premium-title group-hover:scale-105 transition-transform duration-200">
                            {book.title}
                          </h3>
                          <p className="text-sm mt-2 text-gray-600 line-clamp-2">
                            {book.description}
                          </p>
                          <p className="text-xs mt-3 text-gray-500">
                            最后编辑: {new Date(book.updatedAt).toLocaleDateString('zh-CN')}
                          </p>
                        </div>
                      </Link>

                      {/* 操作按钮 */}
                      <div className="px-6 pb-6 pt-2 flex justify-between items-center border-t border-gray-100 border-opacity-50">
                        <div className="flex space-x-1">
                          <button
                            className="p-2 text-gray-400 rounded-lg transition-all duration-200"
                            style={{
                              ':hover': {
                                color: 'var(--color-primary)',
                                backgroundColor: 'rgba(139, 69, 19, 0.1)'
                              }
                            } as React.CSSProperties}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = 'var(--color-primary)';
                              e.currentTarget.style.backgroundColor = 'rgba(139, 69, 19, 0.1)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = '';
                              e.currentTarget.style.backgroundColor = '';
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleExportClick(book, 'md');
                            }}
                            title="导出为Markdown"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                          <button
                            className="p-2 text-gray-400 rounded-lg transition-all duration-200"
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = 'var(--color-primary)';
                              e.currentTarget.style.backgroundColor = 'rgba(139, 69, 19, 0.1)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = '';
                              e.currentTarget.style.backgroundColor = '';
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleExportClick(book, 'txt');
                            }}
                            title="导出为TXT"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                        <button
                          className="p-2 text-gray-400 rounded-lg transition-all duration-200"
                          onMouseEnter={(e) => {
                            e.currentTarget.style.color = 'var(--color-danger)';
                            e.currentTarget.style.backgroundColor = 'rgba(178, 34, 34, 0.1)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.color = '';
                            e.currentTarget.style.backgroundColor = '';
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(book);
                          }}
                          title="删除作品"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                </AnimatedCard>
              ))}
            </div>
          )}
        </section>
      </div>
    </main>
  );
}
