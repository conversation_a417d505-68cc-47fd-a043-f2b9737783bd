"use client";

import React, { useEffect, useRef } from 'react';
import { createAIFactory } from '@/factories/ai';
import { IAIContinueComponent } from '@/factories/ai/interfaces';
import { ConversationMessage } from '@/factories/ai/services/AIWritingService';

interface AIContinueAdapterProps {
  context?: string;
  continueRequirements?: string;
  continueStyle?: string;
  futurePlot?: string;
  bookId?: string;
  conversationHistory?: ConversationMessage[];
  onContinued?: (continuedText: string) => void;
}

/**
 * AI续写适配器类
 * 提供API方式调用AI续写功能
 */
export class AIContinueAdapter {
  private aiFactory = createAIFactory();
  private aiContinueComponent: IAIContinueComponent;

  constructor() {
    this.aiContinueComponent = this.aiFactory.createAIContinueComponent();
  }

  /**
   * 设置上下文
   * @param context 上下文内容
   */
  setContext(context: string): void {
    this.aiContinueComponent.setContext(context);
  }

  /**
   * 设置续写要求
   * @param requirements 续写要求
   */
  setContinueRequirements(requirements: string): void {
    this.aiContinueComponent.setContinueRequirements(requirements);
  }

  /**
   * 设置续写风格
   * @param style 续写风格
   */
  setContinueStyle(style: string): void {
    this.aiContinueComponent.setContinueStyle(style);
  }

  /**
   * 设置后续剧情
   * @param plot 后续剧情
   */
  setFuturePlot(plot: string): void {
    this.aiContinueComponent.setFuturePlot(plot);
  }

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    if ('setBookId' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setBookId(bookId);
    }
  }

  /**
   * 设置对话历史
   * @param history 对话历史
   */
  setConversationHistory(history: ConversationMessage[]): void {
    if ('setConversationHistory' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setConversationHistory(history);
    }
  }

  /**
   * 设置人物数据
   * @param characters 人物数据
   */
  setCharacters(characters: Array<{id?: string, name: string, description?: string}>): void {
    console.log('AIContinueAdapter.setCharacters 被调用:', characters);
    try {
      // 检查方法是否存在
      if (typeof this.aiContinueComponent.setCharacters === 'function') {
        this.aiContinueComponent.setCharacters(characters);
        console.log('成功调用 aiContinueComponent.setCharacters');
      } else if ('setCharacters' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setCharacters(characters);
        console.log('成功调用 (aiContinueComponent as any).setCharacters');
      } else {
        console.error('aiContinueComponent 没有 setCharacters 方法');
      }
    } catch (error) {
      console.error('调用 setCharacters 方法失败:', error);
    }
  }

  /**
   * 设置术语数据
   * @param terminologies 术语数据
   */
  setTerminologies(terminologies: Array<{id?: string, name: string, description?: string}>): void {
    console.log('AIContinueAdapter.setTerminologies 被调用:', terminologies);
    try {
      // 检查方法是否存在
      if (typeof this.aiContinueComponent.setTerminologies === 'function') {
        this.aiContinueComponent.setTerminologies(terminologies);
      } else if ('setTerminologies' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setTerminologies(terminologies);
      } else {
        console.error('aiContinueComponent 没有 setTerminologies 方法');
      }
    } catch (error) {
      console.error('调用 setTerminologies 方法失败:', error);
    }
  }

  /**
   * 设置世界观数据
   * @param worldBuildings 世界观数据
   */
  setWorldBuildings(worldBuildings: Array<{id?: string, name: string, description?: string}>): void {
    console.log('AIContinueAdapter.setWorldBuildings 被调用:', worldBuildings);
    try {
      // 检查方法是否存在
      if (typeof this.aiContinueComponent.setWorldBuildings === 'function') {
        this.aiContinueComponent.setWorldBuildings(worldBuildings);
      } else if ('setWorldBuildings' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setWorldBuildings(worldBuildings);
      } else {
        console.error('aiContinueComponent 没有 setWorldBuildings 方法');
      }
    } catch (error) {
      console.error('调用 setWorldBuildings 方法失败:', error);
    }
  }

  /**
   * 设置章节数据
   * @param chapters 章节数据
   */
  setChapters(chapters: Array<{id?: string, name: string, description?: string, order?: number}>): void {
    console.log('AIContinueAdapter.setChapters 被调用:', chapters);
    try {
      // 检查方法是否存在
      if (typeof this.aiContinueComponent.setChapters === 'function') {
        this.aiContinueComponent.setChapters(chapters);
      } else if ('setChapters' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setChapters(chapters);
      } else {
        console.error('aiContinueComponent 没有 setChapters 方法');
      }
    } catch (error) {
      console.error('调用 setChapters 方法失败:', error);
    }
  }

  /**
   * 设置选中的人物ID
   * @param ids 选中的人物ID
   */
  setSelectedCharacterIds(ids: string[]): void {
    if ('setSelectedCharacterIds' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setSelectedCharacterIds(ids);
    }
  }

  /**
   * 设置选中的术语ID
   * @param ids 选中的术语ID
   */
  setSelectedTerminologyIds(ids: string[]): void {
    if ('setSelectedTerminologyIds' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setSelectedTerminologyIds(ids);
    }
  }

  /**
   * 设置选中的世界观ID
   * @param ids 选中的世界观ID
   */
  setSelectedWorldBuildingIds(ids: string[]): void {
    if ('setSelectedWorldBuildingIds' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setSelectedWorldBuildingIds(ids);
    }
  }

  /**
   * 设置选中的章节ID
   * @param ids 选中的章节ID
   */
  setSelectedChapterIds(ids: string[]): void {
    if ('setSelectedChapterIds' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setSelectedChapterIds(ids);
    }
  }

  /**
   * 设置当前章节ID
   * @param id 当前章节ID
   */
  setCurrentChapterId(id: string): void {
    if ('setCurrentChapterId' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).setCurrentChapterId(id);
    }
  }

  /**
   * 设置当前章节信息
   * @param chapter 当前章节信息
   */
  setCurrentChapter(chapter: {id: string, name: string, description?: string, content?: string, order?: number}): void {
    console.log('AIContinueAdapter.setCurrentChapter 被调用:', chapter);
    try {
      // 检查方法是否存在
      if (typeof this.aiContinueComponent.setCurrentChapter === 'function') {
        this.aiContinueComponent.setCurrentChapter(chapter);
        console.log('成功调用 aiContinueComponent.setCurrentChapter');
      } else if ('setCurrentChapter' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setCurrentChapter(chapter);
        console.log('成功调用 (aiContinueComponent as any).setCurrentChapter');
      } else {
        console.error('aiContinueComponent 没有 setCurrentChapter 方法，尝试使用替代方法');

        // 设置当前章节ID
        this.setCurrentChapterId(chapter.id);

        // 如果组件有setCurrentChapterName方法，设置当前章节名称
        if ('setCurrentChapterName' in this.aiContinueComponent) {
          (this.aiContinueComponent as any).setCurrentChapterName(chapter.name);
          console.log('成功设置当前章节名称:', chapter.name);
        }

        // 如果组件有setCurrentChapterContent方法，设置当前章节内容
        if ('setCurrentChapterContent' in this.aiContinueComponent) {
          (this.aiContinueComponent as any).setCurrentChapterContent(chapter.content || '');
          console.log('成功设置当前章节内容');
        }
      }
    } catch (error) {
      console.error('调用 setCurrentChapter 方法失败:', error);
    }
  }

  /**
   * 续写内容
   * @param onContinued 续写完成回调
   * @returns 续写的内容
   */
  async continue(onContinued?: (text: string) => void): Promise<string> {
    if (onContinued) {
      if ('setOnContinuedCallback' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setOnContinuedCallback(onContinued);
      }
    }
    return await this.aiContinueComponent.continue();
  }

  /**
   * 流式续写
   * @param onChunk 数据块回调
   * @param onContinued 续写完成回调
   * @returns 完整的续写内容
   */
  async continueWithStreaming(
    onChunk: (chunk: string) => void,
    onContinued?: (text: string) => void
  ): Promise<string> {
    if (onContinued) {
      if ('setOnContinuedCallback' in this.aiContinueComponent) {
        (this.aiContinueComponent as any).setOnContinuedCallback(onContinued);
      }
    }

    if ('continueWithStreaming' in this.aiContinueComponent) {
      return await (this.aiContinueComponent as any).continueWithStreaming(onChunk);
    } else {
      // 如果组件不支持流式输出，则使用普通输出模拟
      const result = await this.aiContinueComponent.continue();
      onChunk(result);
      return result;
    }
  }

  /**
   * 取消续写
   */
  cancelContinue(): void {
    if ('cancelContinue' in this.aiContinueComponent) {
      (this.aiContinueComponent as any).cancelContinue();
    }
  }

  /**
   * 渲染组件
   * @returns React节点
   */
  render(): React.ReactNode {
    return this.aiContinueComponent.render();
  }
}

/**
 * AI续写适配器组件
 * 用于将AI续写工厂组件集成到React应用中
 */
export const AIContinueComponent: React.FC<AIContinueAdapterProps> = ({
  context,
  continueRequirements,
  continueStyle,
  futurePlot,
  bookId,
  conversationHistory,
  onContinued
}) => {
  const adapterRef = useRef<AIContinueAdapter | null>(null);

  useEffect(() => {
    // 创建适配器
    if (!adapterRef.current) {
      adapterRef.current = new AIContinueAdapter();
    }

    const adapter = adapterRef.current;

    // 设置初始值
    if (context) {
      adapter.setContext(context);
    }

    if (continueRequirements) {
      adapter.setContinueRequirements(continueRequirements);
    }

    if (continueStyle) {
      adapter.setContinueStyle(continueStyle);
    }

    if (futurePlot) {
      adapter.setFuturePlot(futurePlot);
    }

    if (bookId) {
      adapter.setBookId(bookId);
    }

    if (conversationHistory) {
      adapter.setConversationHistory(conversationHistory);
    }

    if (onContinued) {
      // 设置回调
      const component = adapter['aiContinueComponent'] as any;
      if (component && 'setOnContinuedCallback' in component) {
        component.setOnContinuedCallback(onContinued);
      }
    }
  }, [
    context,
    continueRequirements,
    continueStyle,
    futurePlot,
    bookId,
    conversationHistory,
    onContinued
  ]);

  // 如果适配器尚未创建，返回空元素
  if (!adapterRef.current) {
    return null;
  }

  return <>{adapterRef.current.render()}</>;
};
