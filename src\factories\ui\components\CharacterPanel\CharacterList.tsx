"use client";

import React from 'react';
import { Character } from '@/lib/db/dexie';
import { CharacterIcon } from '@/components/icons';

import { AddIcon, DeleteIcon } from './icons';

interface CharacterListProps {
  characters: Character[];
  isLoading: boolean;
  searchQuery: string;
  selectedCharacter: Character | null;
  onSelectCharacter: (character: Character) => void;
  onCreateCharacter: () => void;
  onDeleteCharacter?: (character: Character) => void;
}

/**
 * 人物列表组件
 */
export const CharacterList: React.FC<CharacterListProps> = ({
  characters,
  isLoading,
  searchQuery,
  selectedCharacter,
  onSelectCharacter,
  onCreateCharacter,
  onDeleteCharacter
}) => {
  // 不再需要UI工厂实例

  // 渲染人物列表项
  const renderCharacterListItem = (character: Character) => {
    const isSelected = selectedCharacter?.id === character.id;

    // 处理删除按钮点击
    const handleDelete = (e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，避免触发选择事件
      e.preventDefault(); // 阻止默认行为
      console.log('删除按钮被点击', character);
      if (onDeleteCharacter && character) {
        // 直接调用删除函数
        onDeleteCharacter(character);
      }
    };

    return (
      <div
        id={`character-item-${character.id}`}
        className={`panel-list-item py-3 px-4 border-b cursor-pointer transition-all duration-200 ${
          isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'
        }`}
        style={{
          borderRadius: isSelected ? '0.5rem' : '0',
          margin: '4px 8px',
          borderLeft: isSelected ? '3px solid var(--color-primary)' : '3px solid transparent',
          backgroundColor: isSelected ? 'rgba(235, 245, 255, 0.9)' : 'transparent',
          boxShadow: isSelected ? '0 2px 4px rgba(0, 0, 0, 0.05)' : 'none',
          transform: isSelected ? 'translateX(3px)' : 'translateX(0)',
          transition: 'all 0.2s ease-out',
        }}
        onClick={() => onSelectCharacter(character)}
      >
        <style jsx>{`
          @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.2); }
            70% { box-shadow: 0 0 0 5px rgba(var(--color-primary-rgb), 0); }
            100% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0); }
          }

          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
          }

          @keyframes fadeOut {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(-20px); }
          }

          .delete-btn {
            opacity: 0;
            transition: all 0.2s ease;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
          }

          .character-item:hover .delete-btn {
            opacity: 1;
            animation: fadeIn 0.2s ease forwards;
          }
        `}</style>

        <div className="flex justify-between items-start character-item" style={{ position: 'relative' }}>
          <div className="flex items-start gap-3 flex-1" style={{ maxWidth: 'calc(100% - 30px)', paddingRight: '30px' }}>
            <CharacterIcon
              size="sm"
              animated={true}
              className="mt-0.5 text-amber-700 flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm truncate" style={{
                color: isSelected ? 'var(--color-primary)' : 'var(--color-text-primary)',
                transition: 'color 0.3s ease'
              }}>
                {character.name}
              </div>
              {character.description && (
                <div className="text-xs truncate mt-1" style={{
                  color: 'var(--color-text-secondary)',
                  opacity: isSelected ? 1 : 0.8,
                  transition: 'opacity 0.3s ease',
                  maxWidth: '200px', // 限制最大宽度
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {character.description.length > 50 ? `${character.description.substring(0, 50)}...` : character.description}
                </div>
              )}
            </div>
          </div>

          {onDeleteCharacter && (
            <button
              className="delete-btn p-1 rounded-full bg-red-100 text-red-500 hover:bg-red-200 transition-colors"
              onClick={handleDelete}
              title="删除人物"
              style={{
                minWidth: '24px',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0
              }}
            >
              <DeleteIcon className="w-3.5 h-3.5" />
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="border-r flex flex-col h-full" style={{
      backgroundColor: 'var(--color-sidebar-bg)',
      width: '100%',
      boxShadow: 'inset -2px 0 5px rgba(0, 0, 0, 0.05)',
      position: 'relative'
    }}>
      {/* 创建新人物按钮 */}
      <div className="p-3 flex justify-start">
        <button
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          onClick={onCreateCharacter}
        >
          <AddIcon className="w-4 h-4 mr-2" />
          <span>创建新人物</span>
        </button>
      </div>

      {/* 人物列表 */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <svg
                className="animate-spin h-6 w-6 mx-auto mb-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-sm">加载中...</p>
            </div>
          </div>
        ) : characters.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <p className="text-sm">
                {searchQuery ? '没有找到匹配的人物' : '暂无人物数据'}
              </p>
            </div>
          </div>
        ) : (
          <div>
            {characters.map(character => (
              <React.Fragment key={character.id}>
                {renderCharacterListItem(character)}
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
