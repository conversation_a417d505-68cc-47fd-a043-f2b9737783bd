import { Character } from '../dexie';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateCharacter(character: Partial<Character>): ValidationResult {
  const errors: Record<string, string> = {};

  // 验证名称
  // 移除名称必填的限制
  if (character.name && character.name.length > 100) {
    errors.name = '名称不能超过100个字符';
  }

  // 验证书籍ID
  if (!character.bookId) {
    errors.bookId = '书籍ID不能为空';
  }

  // 验证描述
  if (!character.description) {
    errors.description = '描述不能为空';
  }

  // 验证关系
  if (character.relationships) {
    for (let i = 0; i < character.relationships.length; i++) {
      const relationship = character.relationships[i];

      if (!relationship.targetCharacterId) {
        errors[`relationships[${i}].targetCharacterId`] = '目标角色ID不能为空';
      }

      if (!relationship.relationshipType) {
        errors[`relationships[${i}].relationshipType`] = '关系类型不能为空';
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
