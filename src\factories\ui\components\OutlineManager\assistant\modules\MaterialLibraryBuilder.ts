/**
 * 素材库构建模块
 * 负责管理素材库相关的消息构建和内容管理
 */

import { ConversationMessage } from '../types/SharedTypes';

export class MaterialLibraryBuilder {
  private static instance: MaterialLibraryBuilder;

  private constructor() {}

  public static getInstance(): MaterialLibraryBuilder {
    if (!MaterialLibraryBuilder.instance) {
      MaterialLibraryBuilder.instance = new MaterialLibraryBuilder();
    }
    return MaterialLibraryBuilder.instance;
  }

  /**
   * 检查素材库是否启用
   */
  isMaterialLibraryEnabled(): boolean {
    try {
      const saved = localStorage.getItem('materialLibraryEnabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  }

  /**
   * 构建素材库信息消息
   */
  buildMaterialLibraryMessage(): ConversationMessage | null {
    // 检查是否启用素材库
    const isEnabled = this.isMaterialLibraryEnabled();
    if (!isEnabled) {
      return null;
    }

    return {
      role: 'system',
      content: `**📚 素材库信息**

**角色模板库**：
- 经典主角原型：天才型、成长型、复仇型、救世型
- 配角设定：导师型、对手型、盟友型、工具型
- 反派设计：智慧型、力量型、疯狂型、理念型

**情节模板库**：
- 开局模板：废材逆袭、穿越重生、意外获得、身份揭秘
- 冲突模板：实力碾压、智斗博弈、情感纠葛、道德选择
- 转折模板：身份反转、力量觉醒、背叛揭露、真相大白
- 高潮模板：最终决战、生死抉择、牺牲拯救、完美复仇

**世界观模板库**：
- 修仙世界：境界体系、门派势力、法宝丹药、天材地宝
- 都市异能：觉醒体系、组织架构、隐秘世界、现代融合
- 玄幻大陆：等级制度、种族分布、魔法体系、大陆格局
- 历史架空：朝代背景、政治格局、文化特色、技术水平

**对话模板库**：
- 装逼台词：霸气宣言、实力展示、身份揭露、威胁警告
- 情感对话：告白表达、友情深化、师徒传承、亲情温暖
- 冲突对话：挑衅激怒、谈判博弈、威胁恐吓、最后通牒
- 信息传递：背景介绍、规则说明、任务布置、线索提供

**写作技巧库**：
- 节奏控制：快慢结合、张弛有度、悬念设置、爽点分布
- 人物塑造：性格特征、成长轨迹、关系网络、对话风格
- 环境描写：氛围营造、场景转换、细节点缀、感官调动
- 情感渲染：心理描写、情绪变化、共鸣触发、代入感强化

您可以在创作过程中引用这些素材，我会根据需要为您提供相关的模板和建议。`
    };
  }

  /**
   * 获取角色模板
   */
  getCharacterTemplates(): {
    protagonists: string[];
    supporting: string[];
    antagonists: string[];
  } {
    return {
      protagonists: [
        '天才型主角：天赋异禀，学习能力超强，但需要经历挫折成长',
        '成长型主角：起点普通，通过努力和机遇逐步变强',
        '复仇型主角：背负血海深仇，以复仇为动力不断前进',
        '救世型主角：肩负拯救世界的使命，在责任中成长'
      ],
      supporting: [
        '导师型配角：传授知识技能，引导主角成长的智者',
        '对手型配角：实力相当的竞争者，推动主角进步',
        '盟友型配角：忠诚可靠的伙伴，与主角并肩作战',
        '工具型配角：提供特定帮助后退场的功能性角色'
      ],
      antagonists: [
        '智慧型反派：智商超群，善于谋略和心理战',
        '力量型反派：实力强大，以绝对力量压制主角',
        '疯狂型反派：行为不可预测，为达目的不择手段',
        '理念型反派：有自己的信念和理想，与主角理念冲突'
      ]
    };
  }

  /**
   * 获取情节模板
   */
  getPlotTemplates(): {
    opening: string[];
    conflict: string[];
    twist: string[];
    climax: string[];
  } {
    return {
      opening: [
        '废材逆袭：被人看不起的废材突然获得机遇开始崛起',
        '穿越重生：现代人穿越到异世界或重生到过去',
        '意外获得：偶然得到强大的力量、宝物或传承',
        '身份揭秘：隐藏的身份或血统被揭露，改变命运'
      ],
      conflict: [
        '实力碾压：主角展现强大实力，震撼所有人',
        '智斗博弈：通过智慧和策略战胜强敌',
        '情感纠葛：复杂的感情关系带来的冲突',
        '道德选择：面临道德和利益的艰难抉择'
      ],
      twist: [
        '身份反转：敌人变盟友，盟友变敌人',
        '力量觉醒：关键时刻觉醒新的强大力量',
        '背叛揭露：信任的人背叛，真相大白',
        '真相大白：隐藏的真相被揭露，改变一切'
      ],
      climax: [
        '最终决战：与最强敌人的生死对决',
        '生死抉择：为了重要的人或事做出牺牲',
        '牺牲拯救：通过自我牺牲拯救他人或世界',
        '完美复仇：经过长期准备的复仇计划成功'
      ]
    };
  }

  /**
   * 获取对话模板
   */
  getDialogueTemplates(): {
    pretentious: string[];
    emotional: string[];
    conflict: string[];
    informative: string[];
  } {
    return {
      pretentious: [
        '"你们这些蝼蚁，根本不知道真正的力量是什么。"',
        '"我只是认真了三分，你们就承受不住了吗？"',
        '"在绝对的实力面前，一切技巧都是徒劳。"',
        '"你们的眼界，限制了你们的想象力。"'
      ],
      emotional: [
        '"无论发生什么，我都会保护你。"',
        '"这些年来，你是我唯一的光。"',
        '"师父的恩情，弟子永生难忘。"',
        '"家人的安全，比我的生命更重要。"'
      ],
      conflict: [
        '"今天，我们之间必须有个了断。"',
        '"你以为这样就能威胁到我吗？"',
        '"既然你选择了这条路，就别怪我无情。"',
        '"最后给你一次机会，现在投降还来得及。"'
      ],
      informative: [
        '"事情的真相比你想象的更复杂。"',
        '"这个世界有你不知道的规则。"',
        '"你的任务是找到那个传说中的宝物。"',
        '"线索就隐藏在这些看似无关的细节中。"'
      ]
    };
  }

  /**
   * 获取世界观模板
   */
  getWorldBuildingTemplates(): {
    cultivation: any;
    urban: any;
    fantasy: any;
    historical: any;
  } {
    return {
      cultivation: {
        realms: ['练气', '筑基', '金丹', '元婴', '化神', '炼虚', '合体', '大乘', '渡劫', '大罗'],
        sects: ['天剑宗', '万药谷', '血魔教', '无极宫', '五行门'],
        treasures: ['灵石', '丹药', '法宝', '功法', '灵草'],
        locations: ['修仙界', '秘境', '洞府', '灵山', '禁地']
      },
      urban: {
        abilities: ['异能觉醒', '血脉传承', '系统绑定', '修炼体系'],
        organizations: ['异能局', '隐世家族', '国际组织', '秘密结社'],
        technology: ['黑科技', '生物改造', '虚拟现实', '人工智能'],
        society: ['普通世界', '隐秘世界', '平行空间', '未来科技']
      },
      fantasy: {
        races: ['人族', '精灵族', '兽人族', '龙族', '恶魔族'],
        magic: ['元素魔法', '召唤魔法', '诅咒魔法', '治疗魔法'],
        kingdoms: ['光明帝国', '黑暗王国', '精灵王庭', '兽人部落'],
        artifacts: ['神器', '魔器', '圣器', '古代遗物']
      },
      historical: {
        dynasties: ['架空朝代', '历史改编', '平行历史', '穿越历史'],
        politics: ['朝堂争斗', '权力游戏', '改革变法', '外敌入侵'],
        culture: ['诗词歌赋', '琴棋书画', '武学传承', '商业贸易'],
        technology: ['古代科技', '超前发明', '失传技术', '神秘力量']
      }
    };
  }

  /**
   * 根据节点类型推荐素材
   */
  recommendMaterials(nodeType: string, context?: any): {
    templates: string[];
    suggestions: string[];
  } {
    const templates: string[] = [];
    const suggestions: string[] = [];

    switch (nodeType) {
      case 'chapter':
        templates.push(...this.getPlotTemplates().opening);
        suggestions.push(
          '建议使用开局模板建立章节基调',
          '考虑加入角色介绍和世界观展示',
          '设置悬念和冲突点吸引读者'
        );
        break;

      case 'plot':
        templates.push(...this.getPlotTemplates().conflict);
        suggestions.push(
          '重点设计冲突和转折点',
          '确保剧情推进有逻辑性',
          '平衡动作和对话的比例'
        );
        break;

      case 'dialogue':
        templates.push(...this.getDialogueTemplates().emotional);
        suggestions.push(
          '对话要符合角色性格',
          '通过对话推进剧情发展',
          '避免过长的独白和说教'
        );
        break;

      default:
        templates.push('通用创作模板');
        suggestions.push('根据具体需求选择合适的素材');
    }

    return { templates, suggestions };
  }

  /**
   * 验证素材库配置
   */
  validateMaterialLibrary(): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查素材库是否启用
    if (!this.isMaterialLibraryEnabled()) {
      issues.push('素材库功能未启用');
      recommendations.push('在设置中启用素材库功能以获得更丰富的创作支持');
    }

    // 检查素材完整性
    try {
      const characterTemplates = this.getCharacterTemplates();
      const plotTemplates = this.getPlotTemplates();
      const dialogueTemplates = this.getDialogueTemplates();

      if (characterTemplates.protagonists.length === 0) {
        issues.push('角色模板库为空');
        recommendations.push('添加更多角色模板以丰富创作选择');
      }

      if (plotTemplates.opening.length === 0) {
        issues.push('情节模板库为空');
        recommendations.push('添加更多情节模板以提供创作灵感');
      }

      if (dialogueTemplates.pretentious.length === 0) {
        issues.push('对话模板库为空');
        recommendations.push('添加更多对话模板以改善对话质量');
      }
    } catch (error) {
      issues.push('素材库数据加载失败');
      recommendations.push('检查素材库数据完整性');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }
}
