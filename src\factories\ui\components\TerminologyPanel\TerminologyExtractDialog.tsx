"use client";

import React, { useState, useEffect } from 'react';
import { Terminology, PromptTemplate, PromptCategory } from '@/lib/db/dexie';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { TerminologyPrompts } from '@/utils/ai/prompts/TerminologyPrompts';
import { createChapterSegmenter } from '@/utils/ai/ChapterSegmenter';

interface TerminologyExtractDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExtract: (source: string, options: TerminologyExtractOptions) => Promise<void>;
  bookId: string;
  chapters: any[]; // 使用实际的章节类型
}

export interface TerminologyExtractOptions {
  extractFromChapters: boolean;
  selectedChapterIds: string[];
  autoCreate: boolean;
  minImportance: number;
  terminology?: Partial<Terminology>; // 用于从AI提取对话框传递术语对象
}

/**
 * 术语提取对话框组件
 */
export const TerminologyExtractDialog: React.FC<TerminologyExtractDialogProps> = ({
  isOpen,
  onClose,
  onExtract,
  bookId,
  chapters
}) => {
  // 提取选项状态
  const [options, setOptions] = useState<TerminologyExtractOptions>({
    extractFromChapters: true,
    selectedChapterIds: [],
    autoCreate: false,
    minImportance: 2
  });

  // 关联术语
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);

  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 错误信息
  const [error, setError] = useState<string | null>(null);

  // AI相关状态
  const [aiSender] = useState(() => new DefaultAISenderComponent());
  const [apiSettings] = useState(() => {
    const settingsFactory = createSettingsFactory();
    return settingsFactory.createAPISettingsDialogComponent();
  });
  // 移除未使用的notification变量

  // 提取结果
  const [extractedTerminologies, setExtractedTerminologies] = useState<any[]>([]);

  // 流式响应状态
  const [streamResponse, setStreamResponse] = useState('');

  // 自定义提示词状态（合并了原来的自定义提示词和重要请求）
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);

  // 当对话框打开时，重置状态并加载术语
  useEffect(() => {
    if (isOpen) {
      setStreamResponse('');
      setExtractedTerminologies([]);
      setError(null);
      loadTerminologies();
    }
  }, [isOpen]);

  // 加载术语
  const loadTerminologies = async () => {
    try {
      const { db } = await import('@/lib/db/dexie');
      const terminologiesData = await db.terminology.where('bookId').equals(bookId).toArray();
      setTerminologies(terminologiesData);
    } catch (err) {
      console.error('加载术语失败:', err);
    }
  };

  // 处理术语选择
  const handleTerminologySelection = (terminologyId: string) => {
    setSelectedTerminologyIds(prev => {
      if (prev.includes(terminologyId)) {
        return prev.filter(id => id !== terminologyId);
      } else {
        return [...prev, terminologyId];
      }
    });
  };

  // 处理提示词模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    setCustomPrompt(template.content);
    setIsPromptManagerOpen(false);
  };

  // 处理选项变更
  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setOptions(prev => ({ ...prev, [name]: checked }));
    } else {
      setOptions(prev => ({ ...prev, [name]: value }));
    }
  };

  // 处理章节选择
  const handleChapterSelection = (chapterId: string, checked: boolean) => {
    setOptions(prev => {
      if (checked) {
        return { ...prev, selectedChapterIds: [...prev.selectedChapterIds, chapterId] };
      } else {
        return { ...prev, selectedChapterIds: prev.selectedChapterIds.filter(id => id !== chapterId) };
      }
    });
  };

  // 全选/取消全选章节
  const handleSelectAllChapters = (checked: boolean) => {
    setOptions(prev => {
      if (checked) {
        return { ...prev, selectedChapterIds: chapters.map(chapter => chapter.id) };
      } else {
        return { ...prev, selectedChapterIds: [] };
      }
    });
  };

  // 处理范围选择
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    if (!rangeStart || !rangeEnd) {
      setError('请输入有效的章节范围');
      return;
    }

    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      setError('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      setError('起始章节不能大于结束章节');
      return;
    }

    if (start > chapters.length || end > chapters.length) {
      setError(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order :
                   (a.chapterNumber !== undefined ? a.chapterNumber : 999999);
      const orderB = b.order !== undefined ? b.order :
                   (b.chapterNumber !== undefined ? b.chapterNumber : 999999);
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setOptions(prev => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return {
          ...prev,
          selectedChapterIds: [...new Set([...prev.selectedChapterIds, ...chapterIds])]
        };
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return {
          ...prev,
          selectedChapterIds: prev.selectedChapterIds.filter(id => !chapterIds.includes(id))
        };
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  // 流式响应回调
  const onStreamChunk = (chunk: string) => {
    setStreamResponse(prevResponse => {
      const updatedResponse = prevResponse + chunk;

      try {
        // 尝试解析JSON
        const jsonMatch = updatedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonStr = jsonMatch[0];
          const data = JSON.parse(jsonStr);

          // 如果解析成功，更新提取的术语
          if (data && typeof data === 'object') {
            const terminologies = Object.entries(data).map(([name, info]: [string, any]) => ({
              name,
              ...(info.newInfo || info)
            }));

            setExtractedTerminologies(terminologies);
          }
        }
      } catch (e) {
        // 解析失败，继续等待更多数据
        console.log('JSON解析失败，继续等待更多数据');
      }

      return updatedResponse;
    });
  };

  // 处理提取
  const handleExtract = async () => {
    setIsLoading(true);
    setError(null);
    setStreamResponse('');
    setExtractedTerminologies([]);

    try {
      // 获取章节内容
      let chapterContents: { id: string; title: string; content: string }[] = [];

      if (options.extractFromChapters && options.selectedChapterIds.length > 0) {
        // 获取选中章节的内容
        const selectedChapters = chapters.filter(chapter =>
          options.selectedChapterIds.includes(chapter.id || '')
        );

        // 准备章节内容
        chapterContents = selectedChapters.map(chapter => ({
          id: chapter.id || '',
          title: chapter.title || `第${chapter.chapterNumber || '?'}章`,
          content: chapter.content || ''
        }));
      }

      if (chapterContents.length === 0) {
        setError('没有可提取的内容，请选择章节');
        setIsLoading(false);
        return;
      }

      // 获取API设置
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      // 创建章节分段器
      const segmenter = createChapterSegmenter({
        maxSegmentLength: 2000,
        minSegmentLength: 500,
        addSegmentNumber: true
      });

      // 计算总段落数
      let totalSegmentsCount = 0;
      for (const chapter of chapterContents) {
        const chapterText = `# ${chapter.title}\n\n${chapter.content}`;
        const segments = segmenter.segmentChapter(chapterText);
        totalSegmentsCount += segments.length;
      }

      // 发送请求
      setStreamResponse(prev => prev + `开始从${chapterContents.length}个章节（共${totalSegmentsCount}个段落）中提取术语...\n`);

      // 存储提取的术语结果
      const allTerminologies: Record<string, any> = {};

      // 调试信息
      console.log('选中的章节IDs:', options.selectedChapterIds);
      console.log('处理的章节内容:', chapterContents);

      // 构建提示词
      const messageBuilder = new MessageBuilder();

      // 系统消息 - 使用默认提示词
      messageBuilder.addSystemMessage(TerminologyPrompts.extractSystemRolePrompt);

      // 添加助手确认消息
      messageBuilder.addAssistantMessage(`我将分析提供的内容，提取其中的术语信息。`);

      // 如果有关联术语，添加到消息中（每个术语一条消息）
      if (selectedTerminologyIds.length > 0) {
        // 先添加一条说明消息
        messageBuilder.addAssistantMessage(`以下是已有的关联术语，我会在提取时考虑这些术语的关联性：`);

        // 为每个关联术语添加单独的消息
        const relatedTerms = terminologies.filter(term => selectedTerminologyIds.includes(term.id || ''));
        for (const term of relatedTerms) {
          messageBuilder.addAssistantMessage(`术语: ${term.name}\n描述: ${term.description || '无描述'}`);
        }
      }

      // 用户消息 - 使用基础提示词模板
      const userPrompt = TerminologyPrompts.extractBasePrompt(options.minImportance);
      messageBuilder.addUserMessage(userPrompt);

      // 添加一条说明消息，表示将要处理多个章节
      messageBuilder.addUserMessage(`以下是多个章节的内容，请逐一分析：`);

      // 为每个章节进行智能分段并添加单独的消息
      let globalSegmentIndex = 0;

      // 为每个章节的每个段落添加单独的消息
      for (const chapter of chapterContents) {
        const chapterText = `# ${chapter.title}\n\n${chapter.content}`;
        const segments = segmenter.segmentChapter(chapterText);

        for (let i = 0; i < segments.length; i++) {
          globalSegmentIndex++;
          const segment = segments[i];
          const segmentWordCount = segment.split(/\s+/).length;

          // 按照要求的格式构建消息：第x章，第几段，多少字，内容
          const segmentPrompt = `第${chapter.title}，第${i+1}/${segments.length}段，${segmentWordCount}字，内容：\n\n${segment}`;
          messageBuilder.addUserMessage(segmentPrompt);
          messageBuilder.addAssistantMessage(`我已阅读并分析了"${chapter.title}"章节的第${i+1}段内容（总进度：${globalSegmentIndex}/${totalSegmentsCount}）。`);
        }
      }

      // 添加一条总结消息
      messageBuilder.addAssistantMessage(`我已完成所有章节的分析。`);

      // 添加输出格式指令
      messageBuilder.addUserMessage(`请以JSON格式输出提取结果，格式如下：
{
  "术语名称1": {
    "category": "术语类别",
    "description": "术语描述",
    "importance": 数字(1-5)
  },
  "术语名称2": {
    "category": "术语类别",
    "description": "术语描述",
    "importance": 数字(1-5)
  }
}

重要说明：
1. 只提取文本中明确描述或合理推断的术语
2. 每个术语必须包含类别、描述和重要性
3. 重要性使用1-5的数字表示，5表示最重要
4. 直接返回JSON对象，不要使用Markdown代码块
5. 不要在JSON前后添加任何额外的文本`);

      // 如果有自定义提示词，添加到最后一条消息（确保是真正的最后一条）
      if (customPrompt) {
        messageBuilder.addUserMessage(`${customPrompt}`);
      }

      // 发送请求
      setStreamResponse(prev => prev + `\n正在发送请求处理所有章节内容...\n`);

      const result = await aiSender.sendRequest('', {
        messages: messageBuilder.build(),
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true,
        onStreamChunk: (chunk: string) => {
          // 为响应添加前缀
          onStreamChunk(`[处理所有章节] ${chunk}`);
        }
      });

      // 解析结果
      const jsonMatch = result.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        const data = JSON.parse(jsonStr);

        // 更新状态
        const termCount = Object.keys(data).length;
        setStreamResponse(prev => prev + `\n提取完成，共发现${termCount}个术语。\n`);

        // 将提取的术语添加到结果中
        for (const [name, info] of Object.entries(data)) {
          const typedInfo = info as any;
          allTerminologies[name] = {
            ...typedInfo,
            name,
            extractedFromChapterIds: chapterContents.map(chapter => chapter.id)
          };
        }
      } else {
        setStreamResponse(prev => prev + `\n未能提取到有效的术语信息。\n`);
      }



      // 更新提取的术语列表
      const mergedTerminologies: Array<{
        name: string;
        category?: string;
        description?: string;
        importance?: string | number;
        extractedFromChapterIds?: string[];
        [key: string]: any;
      }> = Object.values(allTerminologies);

      setExtractedTerminologies(mergedTerminologies);

      // 为每个术语单独显示确认消息
      setStreamResponse(prev => prev + `\n提取完成，共发现${mergedTerminologies.length}个术语：\n`);
      for (const term of mergedTerminologies) {
        setStreamResponse(prev => prev + `\n术语: ${term.name} (重要性: ${term.importance || '未知'})\n描述: ${term.description || '无描述'}\n`);
      }

      // 如果启用了自动创建，则创建提取的术语
      if (options.autoCreate && mergedTerminologies.length > 0) {
        try {
          setIsLoading(true);
          const createdTerms = [];

          for (const term of mergedTerminologies) {
            const newTerminology: Partial<Terminology> = {
              name: term.name,
              bookId,
              category: term.category,
              description: term.description,
              attributes: {
                importance: term.importance ? String(term.importance) : '3'
              },
              relatedTerminologyIds: selectedTerminologyIds,
              extractedFromChapterIds: term.extractedFromChapterIds || [],
              createdAt: new Date(),
              updatedAt: new Date()
            };

            await onExtract('ai', {
              ...options,
              terminology: newTerminology
            });

            createdTerms.push(term.name);
            // 显示创建成功消息
            setStreamResponse(prev => prev + `\n成功创建术语: ${term.name}\n`);
          }

          // 不显示通知，直接关闭对话框
          onClose();
        } catch (err) {
          setError(err instanceof Error ? err.message : '创建术语时发生错误');
        } finally {
          setIsLoading(false);
        }
      } else {
        setStreamResponse(prev => prev + `\n\n提取完成，共发现${mergedTerminologies.length}个术语。请在下方查看结果，并选择要创建的术语。\n`);
      }
    } catch (err) {
      console.error('提取术语失败:', err);
      setError(err instanceof Error ? err.message : '提取术语时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[700px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>AI提取术语</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-6">
            {/* 提取来源选项 */}
            <div>
              <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>提取来源</h3>

              {/* 从章节提取 */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id="extractFromChapters"
                    name="extractFromChapters"
                    checked={options.extractFromChapters}
                    onChange={handleOptionChange}
                    className="mr-2 h-4 w-4"
                  />
                  <label htmlFor="extractFromChapters" className="text-gray-700">从章节提取术语</label>
                </div>

                {options.extractFromChapters && (
                  <div className="ml-6 mt-2 border border-gray-200 rounded-lg p-3 max-h-[200px] overflow-y-auto"
                    style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
                  >
                    {/* 章节范围选择 */}
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                      <h4 className="font-medium text-gray-700 mb-2">章节范围选择</h4>
                      <div className="flex items-center space-x-2 mb-3">
                        <input
                          type="number"
                          value={rangeStart}
                          onChange={(e) => setRangeStart(e.target.value)}
                          placeholder="起始"
                          className="w-20 p-2 border rounded"
                          min="1"
                          max={chapters?.length || 1}
                        />
                        <span>-</span>
                        <input
                          type="number"
                          value={rangeEnd}
                          onChange={(e) => setRangeEnd(e.target.value)}
                          placeholder="结束"
                          className="w-20 p-2 border rounded"
                          min="1"
                          max={chapters?.length || 1}
                        />
                        <button
                          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                          onClick={() => handleRangeSelect('select')}
                        >
                          选择
                        </button>
                        <button
                          className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
                          onClick={() => handleRangeSelect('deselect')}
                        >
                          取消选择
                        </button>
                      </div>
                    </div>

                    {/* 章节列表 */}
                    <div className="flex items-center mb-2 pb-2 border-b border-gray-100">
                      <input
                        type="checkbox"
                        id="selectAllChapters"
                        checked={options.selectedChapterIds.length === chapters.length}
                        onChange={(e) => handleSelectAllChapters(e.target.checked)}
                        className="mr-2 h-4 w-4"
                      />
                      <label htmlFor="selectAllChapters" className="text-gray-700 font-medium">全选</label>
                    </div>

                    <div className="space-y-1">
                      {chapters.map(chapter => (
                        <div key={chapter.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`chapter-${chapter.id}`}
                            checked={options.selectedChapterIds.includes(chapter.id)}
                            onChange={(e) => handleChapterSelection(chapter.id, e.target.checked)}
                            className="mr-2 h-4 w-4"
                          />
                          <label htmlFor={`chapter-${chapter.id}`} className="text-gray-700 truncate">
                            {chapter.title || `第${chapter.chapterNumber}章`}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 关联术语 */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <label className="text-gray-700 font-medium">关联术语</label>
                </div>
                <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[200px] overflow-y-auto"
                  style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
                >
                  {terminologies.length === 0 ? (
                    <div className="text-gray-500 text-center py-2">
                      没有可关联的术语
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {terminologies.map(terminology => (
                        <div key={terminology.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`terminology-${terminology.id}`}
                            checked={selectedTerminologyIds.includes(terminology.id!)}
                            onChange={() => handleTerminologySelection(terminology.id!)}
                            className="mr-2 h-4 w-4"
                          />
                          <label htmlFor={`terminology-${terminology.id}`} className="text-gray-700 truncate">
                            {terminology.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>


            </div>

            {/* 提取选项 */}
            <div>
              <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>提取选项</h3>

              {/* 自定义提示词和重要请求（合并） */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <label className="text-gray-700 font-medium">自定义提示词</label>
                  <button
                    type="button"
                    onClick={() => setIsPromptManagerOpen(true)}
                    className="px-3 py-1 text-sm rounded-md transition-colors"
                    style={{
                      backgroundColor: 'var(--color-primary)',
                      color: 'white',
                      boxShadow: '0 2px 4px rgba(139, 69, 19, 0.2)'
                    }}
                  >
                    选择模板
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {customPrompt ? '已选择自定义提示词' : '使用默认提示词'}
                </p>

                {/* 自定义提示词输入区域 */}
                <div className="mt-2">
                  <textarea
                    id="custom-prompt-input"
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入自定义提示词和重要请求，或从模板中选择..."
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      borderColor: 'rgba(139, 69, 19, 0.2)'
                    }}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    在这里输入自定义提示词和重要请求，用于指导AI如何提取术语
                  </p>
                </div>
              </div>

              {/* 自动创建 */}
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoCreate"
                    name="autoCreate"
                    checked={options.autoCreate}
                    onChange={handleOptionChange}
                    className="mr-2 h-4 w-4"
                  />
                  <label htmlFor="autoCreate" className="text-gray-700">自动创建提取的术语</label>
                </div>
                <p className="text-sm text-gray-500 ml-6 mt-1">
                  启用后，AI会自动创建提取的术语。禁用则仅显示提取结果，由您手动选择创建。
                </p>
              </div>

              {/* 最低重要性 */}
              <div>
                <label htmlFor="minImportance" className="block text-gray-700 mb-1">最低重要性</label>
                <select
                  id="minImportance"
                  name="minImportance"
                  value={options.minImportance}
                  onChange={handleOptionChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                >
                  <option value="1">⭐ 次要术语 - 背景补充</option>
                  <option value="2">⭐⭐ 支持术语 - 丰富世界观</option>
                  <option value="3">⭐⭐⭐ 重要术语 - 影响情节</option>
                  <option value="4">⭐⭐⭐⭐ 核心术语 - 关键设定</option>
                  <option value="5">⭐⭐⭐⭐⭐ 关键术语 - 世界基石</option>
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  AI只会提取重要性等于或高于此级别的术语。
                </p>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
          </div>
        </div>

        {/* 提取结果 */}
        {extractedTerminologies.length > 0 && !options.autoCreate && (
          <div className="p-4 border-t border-gray-200" style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}>
            <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>提取结果</h3>
            <div className="max-h-[200px] overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {extractedTerminologies.map((term, index) => (
                  <div
                    key={index}
                    className="p-3 border rounded-lg"
                    style={{
                      borderColor: 'rgba(139, 69, 19, 0.2)',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium" style={{ color: 'var(--color-primary)' }}>{term.name}</h4>
                      <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                        {term.category}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{term.description}</p>
                    <div className="mt-1 text-xs text-yellow-500">
                      {'⭐'.repeat(parseInt(term.importance) || 0)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-between"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <div>
            {extractedTerminologies.length > 0 && !options.autoCreate && (
              <button
                className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
                style={{
                  backgroundColor: 'var(--color-success)',
                  color: 'white',
                  boxShadow: '0 2px 8px rgba(85, 107, 47, 0.2)'
                }}
                onClick={async () => {
                  setIsLoading(true);
                  try {
                    for (const term of extractedTerminologies) {
                      const newTerminology: Partial<Terminology> = {
                        name: term.name,
                        bookId,
                        category: term.category,
                        description: term.description,
                        attributes: {
                          importance: term.importance ? String(term.importance) : '3'
                        },
                        relatedTerminologyIds: selectedTerminologyIds,
                        extractedFromChapterIds: term.extractedFromChapterIds || [],
                        createdAt: new Date(),
                        updatedAt: new Date()
                      };

                      await onExtract('ai', {
                        ...options,
                        terminology: newTerminology
                      });
                    }

                    // 不显示通知，直接关闭对话框
                    onClose();
                  } catch (err) {
                    setError(err instanceof Error ? err.message : '创建术语时发生错误');
                  } finally {
                    setIsLoading(false);
                  }
                }}
                disabled={isLoading}
              >
                创建所有术语
              </button>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
              style={{
                backgroundColor: 'rgba(210, 180, 140, 0.2)',
                color: 'var(--color-primary)',
                border: '1px solid var(--color-secondary)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
              }}
              onClick={onClose}
              disabled={isLoading}
            >
              {extractedTerminologies.length > 0 ? '关闭' : '取消'}
            </button>

            {extractedTerminologies.length === 0 && (
              <button
                className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center"
                style={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'white',
                  boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
                }}
                onClick={handleExtract}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    处理中...
                  </>
                ) : '开始提取'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.TERMINOLOGY} // 使用术语专用分类
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />
    </div>
  );
};
