import { Node, Edge } from 'reactflow';
import { Outline, OutlineNodeType } from '../types/outline';

/**
 * 将大纲数据转换为ReactFlow节点和边
 * @param outline 大纲数据
 * @param selectedNode 选中的节点ID
 * @param handlers 事件处理函数
 * @returns ReactFlow节点和边
 */
export const transformOutlineToFlow = (
  outline: Outline,
  selectedNode: string | null,
  handlers = {
    onEdit: (id: string) => {},
    onDelete: (id: string) => {},
    onAddChild: (id: string, type: string) => {},
    onSelect: (id: string | null) => {}
  }
) => {
  const flowNodes: Node[] = [];
  const flowEdges: Edge[] = [];

  // 节点ID到索引的映射，用于稳定布局
  const nodeIndexMap = new Map<string, { level: number, index: number }>();

  // 第一遍：计算所有节点的层级和索引位置
  const calculateNodePositions = (nodes: OutlineNodeType[], level: number = 0, startIndex: number = 0) => {
    let currentIndex = startIndex;

    nodes.forEach(node => {
      // 保存节点的层级和索引
      nodeIndexMap.set(node.id, { level, index: currentIndex });

      // 递增索引值，确保节点水平分布
      currentIndex += 1;

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        calculateNodePositions(node.children, level + 1, currentIndex - 0.5);
      }
    });

    return currentIndex;
  };

  // 计算所有节点位置
  if (outline.nodes && outline.nodes.length > 0) {
    calculateNodePositions(outline.nodes);
  }

  // 第二遍：创建节点和边
  const processNode = (node: OutlineNodeType, parentId: string | null = null) => {
    // 获取节点的层级和索引
    const nodePosition = nodeIndexMap.get(node.id) || { level: 0, index: 0 };

    // 优先使用保存的位置信息，确保手动调整的位置不被重置
    // 只有在节点没有位置信息时才使用计算的默认位置
    let position;

    if (node.position && typeof node.position.x === 'number' && typeof node.position.y === 'number') {
      // 使用保存的位置信息（手动拖拽或自动布局设置的位置）
      position = { ...node.position };
      console.log(`节点 ${node.id} 使用保存的位置:`, position);
    } else {
      // 计算默认位置（仅在没有保存位置时使用）
      position = {
        x: nodePosition.index * 300,
        y: nodePosition.level * 150
      };
      console.log(`节点 ${node.id} 使用计算的默认位置:`, position);
    }

    // 检查节点是否被选中
    const isSelected = node.id === selectedNode;

    // 创建React Flow节点
    flowNodes.push({
      id: node.id,
      type: 'outlineNode',
      position,
      // 添加节点类型相关的样式
      style: {
        zIndex: isSelected ? 10 : 1, // 选中的节点显示在最上层
      },
      // 添加节点的数据
      data: {
        ...node,
        onEdit: handlers.onEdit,
        onDelete: handlers.onDelete,
        onAddChild: handlers.onAddChild,
        onSelect: handlers.onSelect,
        selected: isSelected,
      },
    });

    // 如果有父节点，创建父子关系边
    if (parentId) {
      flowEdges.push({
        id: `e-${parentId}-${node.id}`,
        source: parentId,
        target: node.id,
        type: 'outlineEdge',
        data: {
          nodeType: node.type
        }
      });
    }

    // 处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        processNode(child, node.id);
      });
    }
  };

  // 处理根节点
  if (outline.nodes && outline.nodes.length > 0) {
    outline.nodes.forEach(node => {
      processNode(node);
    });
  }

  return { nodes: flowNodes, edges: flowEdges };
};

/**
 * 应用自动布局
 * @param nodes ReactFlow节点
 * @param edges ReactFlow边
 * @param mode 布局模式
 * @returns 布局后的节点
 */
export const applyLayout = (
  nodes: Node[],
  edges: Edge[],
  mode: 'tree' | 'force' | 'dagre'
) => {
  if (!nodes.length) return nodes;

  // 计算节点的层级关系
  const nodeMap = new Map<string, { level: number, index: number }>();
  const rootNodes: string[] = [];
  const childrenMap = new Map<string, string[]>();

  // 构建节点关系图
  nodes.forEach(node => {
    // 初始化
    if (!nodeMap.has(node.id)) {
      nodeMap.set(node.id, { level: 0, index: 0 });
    }

    // 查找节点的父节点
    let hasParent = false;
    edges.forEach(edge => {
      if (edge.target === node.id) {
        hasParent = true;
        const parentId = edge.source;

        // 记录父节点的子节点
        if (!childrenMap.has(parentId)) {
          childrenMap.set(parentId, []);
        }
        childrenMap.get(parentId)?.push(node.id);
      }
    });

    // 如果没有父节点，则为根节点
    if (!hasParent) {
      rootNodes.push(node.id);
    }
  });

  // 计算节点的层级和索引
  const calculateLevelAndIndex = (nodeId: string, level: number, index: number) => {
    nodeMap.set(nodeId, { level, index });

    const children = childrenMap.get(nodeId) || [];
    children.forEach((childId, childIndex) => {
      calculateLevelAndIndex(childId, level + 1, index + childIndex);
    });
  };

  // 从根节点开始计算
  rootNodes.forEach((rootId, rootIndex) => {
    calculateLevelAndIndex(rootId, 0, rootIndex * 5);
  });

  // 根据布局模式应用不同的布局算法
  switch (mode) {
    case 'tree':
      // 树形布局 - 水平方向
      return nodes.map(node => {
        const nodeInfo = nodeMap.get(node.id);
        if (nodeInfo) {
          const { level, index } = nodeInfo;
          return {
            ...node,
            position: {
              x: level * 300,
              y: index * 150
            }
          };
        }
        return node;
      });

    case 'force':
      // 力导向布局 - 放射状
      return nodes.map(node => {
        const nodeInfo = nodeMap.get(node.id);
        if (nodeInfo) {
          const { level, index } = nodeInfo;
          // 计算角度和半径
          const angle = (index / (rootNodes.length || 1)) * Math.PI * 2;
          const radius = level * 200 + 100;
          return {
            ...node,
            position: {
              x: Math.cos(angle) * radius + 500, // 中心点偏移
              y: Math.sin(angle) * radius + 400  // 中心点偏移
            }
          };
        }
        return node;
      });

    case 'dagre':
    default:
      // 层次布局 - 自上而下
      return nodes.map(node => {
        const nodeInfo = nodeMap.get(node.id);
        if (nodeInfo) {
          const { level, index } = nodeInfo;
          return {
            ...node,
            position: {
              x: index * 250,
              y: level * 200
            }
          };
        }
        return node;
      });
  }
};
