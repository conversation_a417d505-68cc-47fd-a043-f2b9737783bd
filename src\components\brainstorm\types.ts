// AI脑洞系统的类型定义

export interface BrainstormType {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  category: 'content' | 'structure' | 'character' | 'world';
}

export interface BrainstormRequest {
  type: string;
  input: string;
  context: {
    bookId: string;
    chapterContent?: string;
    characters?: any[];
    worldSettings?: any[];
    glossary?: any[];
    outline?: any[];
  };
}

export interface BrainstormResult {
  id: string;
  type: string;
  content: string;
  score: number;
  metadata: {
    keywords: string[];
    relatedCharacters?: string[];
    relatedWorldSettings?: string[];
    relatedGlossary?: string[];
    createdAt: Date;
  };
}

export interface BrainstormSession {
  id: string;
  bookId: string;
  type: string;
  request: BrainstormRequest;
  results: BrainstormResult[];
  selectedResult?: BrainstormResult;
  status: 'pending' | 'generating' | 'completed' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

// 脑洞类型配置
export const BRAINSTORM_TYPES: BrainstormType[] = [
  {
    id: 'theme',
    name: '题材生成',
    description: '基于现有角色和世界观生成新颖的故事题材',
    color: 'from-orange-500 to-orange-600',
    icon: '📚',
    category: 'content',
  },
  {
    id: 'title',
    name: '书名生成',
    description: '结合作品内容和风格创造吸引人的书名',
    color: 'from-blue-500 to-blue-600',
    icon: '✨',
    category: 'content',
  },
  {
    id: 'synopsis',
    name: '简介生成',
    description: '基于大纲和角色撰写引人入胜的作品简介',
    color: 'from-green-500 to-green-600',
    icon: '📝',
    category: 'content',
  },
  {
    id: 'plot',
    name: '剧情脑洞',
    description: '根据当前剧情和角色关系生成创新发展方向',
    color: 'from-purple-500 to-purple-600',
    icon: '🎭',
    category: 'structure',
  },
  {
    id: 'character',
    name: '人物设定',
    description: '基于世界观和现有角色创造新的角色设定',
    color: 'from-red-500 to-red-600',
    icon: '👤',
    category: 'character',
  },
  {
    id: 'worldview',
    name: '世界观构建',
    description: '扩展和完善现有世界观设定',
    color: 'from-cyan-500 to-cyan-600',
    icon: '🌍',
    category: 'world',
  },
];

// 增强的角色关系类型
export interface CharacterRelationship {
  targetCharacterId: string;
  relationshipType: string; // 如"朋友"、"敌人"、"家人"等
  description: string;
}

// 增强的角色数据类型
export interface EnhancedCharacter {
  id: string;
  name: string;
  alias?: string[];
  description: string;
  appearance?: string;
  personality?: string;
  background?: string;
  goals?: string;
  // 深度信息
  hiddenMotivation?: string;
  secretHistory?: string;
  innerConflicts?: string;
  characterArchetype?: string;
  growthArc?: string;
  symbolism?: string;
  // 关系信息
  relationships?: CharacterRelationship[];
  relatedCharacterIds: string[];
  relatedTerminologyIds: string[];
  relatedWorldBuildingIds: string[];
  // 元数据
  attributes?: Record<string, string>;
  notes?: string;
  extractedFromChapterIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 增强的世界观数据类型
export interface EnhancedWorldBuilding {
  id: string;
  name: string;
  category: string;
  description: string;
  attributes?: Record<string, string>;
  notes?: string;
  relatedCharacterIds: string[];
  relatedTerminologyIds: string[];
  relatedWorldBuildingIds: string[];
  extractedFromChapterIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 增强的术语数据类型
export interface EnhancedTerminology {
  id: string;
  name: string;
  alias?: string[];
  category: string;
  description: string;
  attributes?: Record<string, string>;
  notes?: string;
  relatedCharacterIds: string[];
  relatedTerminologyIds: string[];
  relatedWorldBuildingIds: string[];
  extractedFromChapterIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 增强的章节数据类型
export interface EnhancedChapter {
  id: string;
  title: string;
  content: string;
  summary?: string;
  order: number;
  wordCount: number;
  characterIds: string[];
  terminologyIds: string[];
  worldBuildingIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 关联数据类型（保持向后兼容）
export interface AssociationData {
  characters: Array<{
    id: string;
    name: string;
    description: string;
    traits: string[];
  }>;
  worldSettings: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
  }>;
  glossary: Array<{
    id: string;
    term: string;
    definition: string;
    category: string;
  }>;
  outline: Array<{
    id: string;
    title: string;
    content: string;
    level: number;
  }>;
}

// 增强的关联数据类型
export interface EnhancedAssociationData {
  characters: EnhancedCharacter[];
  worldSettings: EnhancedWorldBuilding[];
  glossary: EnhancedTerminology[];
  outline: EnhancedChapter[];
}

// 生成配置
export interface GenerationConfig {
  maxResults: number;
  temperature: number;
  includeContext: boolean;
  associationWeight: number;
  creativityLevel: 'conservative' | 'balanced' | 'creative' | 'experimental';
}

export const DEFAULT_GENERATION_CONFIG: GenerationConfig = {
  maxResults: 3,
  temperature: 0.8,
  includeContext: true,
  associationWeight: 0.7,
  creativityLevel: 'balanced',
};
