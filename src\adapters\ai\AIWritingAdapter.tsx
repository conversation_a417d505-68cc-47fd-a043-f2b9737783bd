"use client";

import React from 'react';
import { createAIWritingPanelFactory } from '@/factories/ui';

/**
 * AI写作适配器
 * 用于在编辑器中集成AI写作功能
 */
export class AIWritingAdapter {
  /**
   * 创建AI写作对话框组件
   * @param props 组件属性
   * @returns AI写作对话框组件
   */
  createAIWritingDialog(props: {
    isOpen: boolean;
    onClose: () => void;
    onInsertContent: (content: string) => void;
    bookId: string;
  }) {
    const aiWritingPanelFactory = createAIWritingPanelFactory();
    return aiWritingPanelFactory.createAIWritingDialog(props);
  }
}

/**
 * 创建AI写作适配器实例
 * @returns AI写作适配器实例
 */
export function createAIWritingAdapter() {
  return new AIWritingAdapter();
}

export default createAIWritingAdapter;
