"use client";

import React from 'react';

interface ContinuePromptInputProps {
  continuePrompt: string;
  onContinuePromptChange: (value: string) => void;
  onCancel: () => void;
  onSubmit: () => void;
  mode?: 'continue' | 'rewrite' | 'analyze' | 'new';
}

/**
 * 继续对话输入组件
 * 用于输入继续对话的提示词
 */
const ContinuePromptInput: React.FC<ContinuePromptInputProps> = ({
  continuePrompt,
  onContinuePromptChange,
  onCancel,
  onSubmit,
  mode = 'continue'
}) => {
  // 根据不同的模式显示不同的标题和提示文本
  const getTitle = () => {
    switch (mode) {
      case 'continue':
        return '继续创作';
      case 'rewrite':
        return '重写内容';
      case 'analyze':
        return '分析内容';
      default:
        return '继续对话';
    }
  };

  const getPlaceholder = () => {
    switch (mode) {
      case 'continue':
        return '请输入继续创作的要求，AI将在已有内容的基础上继续创作...';
      case 'rewrite':
        return '请输入重写的要求，AI将重写已有内容，但保持核心情节不变...';
      case 'analyze':
        return '请输入分析的要求，AI将对已有内容进行分析和评价...';
      default:
        return '请输入继续对话的提示词...';
    }
  };

  return (
    <div className="p-4 border rounded-xl bg-white shadow-sm mt-3">
      <div className="flex flex-col space-y-3">
        <h3 className="text-lg font-medium text-gray-800">{getTitle()}</h3>
        <p className="text-sm text-gray-500">
          {mode === 'continue' && '内容将直接接续已生成的内容'}
          {mode === 'rewrite' && '重写的内容将显示在已生成内容之后'}
          {mode === 'analyze' && '分析结果将显示在已生成内容之后'}
        </p>
        <textarea
          value={continuePrompt}
          onChange={(e) => onContinuePromptChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          rows={3}
          placeholder={getPlaceholder()}
        />
        <div className="flex justify-between">
          <button
            onClick={onCancel}
            className="px-3 py-1.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            取消
          </button>
          <button
            onClick={onSubmit}
            className="px-3 py-1.5 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
            disabled={!continuePrompt.trim()}
          >
            提交
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContinuePromptInput;
