/**
 * AI历史消息垂直调整服务
 * 分析AI历史回复模式，生成垂直调整建议，提升AI回复质量
 */

import { UnifiedAIService, AIServiceType } from './BaseAIService';

export interface ConversationMessage {
  id?: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  rating?: number; // 小懒评分 1-5
  feedback?: string; // 小懒反馈
  adjustmentUsed?: string; // 使用的调整ID
  effectivenessScore?: number; // 调整效果评分
}

export type PhaseType = 'intro' | 'setup' | 'compression' | 'climax' | 'resolution' | 'ending' | 'buildup' | 'custom';

export interface VerticalAdjustment {
  id: string;
  name: string;
  phase: PhaseType; // 所属阶段
  category: 'style' | 'structure' | 'content' | 'engagement' | 'personalization';
  adjustmentContent: string;
  description: string;
  confidence: number; // 0-1 推荐置信度
  isActive: boolean;
  createdAt: Date;
  usageCount: number;
  effectivenessScore?: number; // 效果评分
}

export interface AIResponseAnalysis {
  responsePatterns: {
    averageLength: number;
    commonStructures: string[];
    frequentPhrases: string[];
    guidanceTypes: string[];
  };
  qualityMetrics: {
    helpfulness: number;
    specificity: number;
    actionability: number;
    engagement: number;
  };
  improvementAreas: {
    weakPoints: string[];
    missedOpportunities: string[];
    repetitivePatterns: string[];
  };
  overallScore: number;
}

export class VerticalAdjustmentService extends UnifiedAIService {
  private storageKey = 'ai-vertical-adjustments';
  private analysisKey = 'ai-response-analysis-history';
  private static eventListeners: Array<() => void> = [];
  private currentPhase: PhaseType;

  constructor(phase: PhaseType = 'intro') {
    super(AIServiceType.VERTICAL_ADJUSTMENT);
    this.currentPhase = phase;
  }

  /**
   * 设置当前阶段
   */
  setCurrentPhase(phase: PhaseType): void {
    this.currentPhase = phase;
    console.log(`🎯 VerticalAdjustmentService: 切换到阶段 ${phase}`);
  }

  /**
   * 获取当前阶段
   */
  getCurrentPhase(): PhaseType {
    return this.currentPhase;
  }

  /**
   * 分析AI历史回复模式
   */
  analyzeAIResponses(messages: ConversationMessage[]): AIResponseAnalysis {
    const aiMessages = messages.filter(m => m.type === 'ai');

    if (aiMessages.length === 0) {
      return this.getEmptyAnalysis();
    }

    const patterns = this.extractPatterns(aiMessages);
    const qualityMetrics = this.calculateQualityMetrics(aiMessages);
    const improvementAreas = this.identifyImprovementAreas(aiMessages, qualityMetrics);
    const overallScore = this.calculateOverallScore(qualityMetrics);

    const analysis: AIResponseAnalysis = {
      responsePatterns: patterns,
      qualityMetrics,
      improvementAreas,
      overallScore
    };

    // 保存分析历史
    this.saveAnalysisHistory(analysis);

    return analysis;
  }

  /**
   * 基于分析结果生成垂直调整建议 - 通过AI分析生成
   */
  async generateAdjustments(analysis: AIResponseAnalysis, conversationHistory: ConversationMessage[], personaSettings?: any): Promise<VerticalAdjustment[]> {
    try {
      console.log('🎯 VerticalAdjustmentService: 开始生成调整建议，包含人设设定:', !!personaSettings);

      // 构建AI分析提示词，包含人设设定
      const analysisPrompt = this.buildAnalysisPrompt(analysis, conversationHistory, personaSettings);

      // 调用AI服务生成调整建议，传入人设设定
      const aiResponse = await this.callAIForAdjustments(analysisPrompt, conversationHistory, personaSettings);

      // 解析AI返回的调整建议
      const adjustments = this.parseAIAdjustments(aiResponse);

      return adjustments.filter(adj => adj.confidence > 0.5);
    } catch (error) {
      console.error('AI生成调整建议失败:', error);
      // 降级到基础调整建议
      return this.getFallbackAdjustments(analysis);
    }
  }

  /**
   * 获取当前激活的调整 - 支持多个激活
   */
  getActiveAdjustments(): VerticalAdjustment[] {
    const saved = this.getSavedAdjustments();
    return saved.filter(adj => adj.isActive);
  }

  /**
   * 获取当前激活的调整（兼容旧接口）
   */
  getActiveAdjustment(): VerticalAdjustment | null {
    const activeAdjustments = this.getActiveAdjustments();
    return activeAdjustments.length > 0 ? activeAdjustments[0] : null;
  }

  /**
   * 保存调整模板 - 按阶段存储
   */
  saveAdjustment(adjustment: VerticalAdjustment): void {
    // 确保调整有phase字段
    const adjustmentWithPhase = {
      ...adjustment,
      phase: adjustment.phase || this.currentPhase
    };

    const saved = this.getSavedAdjustments(adjustmentWithPhase.phase);
    const index = saved.findIndex(adj => adj.id === adjustmentWithPhase.id);

    if (index >= 0) {
      saved[index] = { ...adjustmentWithPhase, createdAt: saved[index].createdAt };
    } else {
      saved.push({ ...adjustmentWithPhase, createdAt: new Date(), usageCount: 0 });
    }

    const phaseStorageKey = `${this.storageKey}-${adjustmentWithPhase.phase}`;
    localStorage.setItem(phaseStorageKey, JSON.stringify(saved));

    console.log(`💾 调整建议已保存到阶段 ${adjustmentWithPhase.phase}: ${adjustmentWithPhase.name}`);
  }

  /**
   * 获取保存的调整模板 - 按阶段过滤
   */
  getSavedAdjustments(phase?: PhaseType): VerticalAdjustment[] {
    const targetPhase = phase || this.currentPhase;
    const phaseStorageKey = `${this.storageKey}-${targetPhase}`;
    const saved = localStorage.getItem(phaseStorageKey);

    if (!saved) {
      // 尝试从旧的统一存储中迁移数据
      return this.migrateFromLegacyStorage(targetPhase);
    }

    try {
      return JSON.parse(saved).map((adj: any) => ({
        ...adj,
        phase: adj.phase || targetPhase,
        createdAt: new Date(adj.createdAt)
      }));
    } catch (error) {
      console.error('解析调整模板失败:', error);
      return [];
    }
  }

  /**
   * 从旧的统一存储迁移数据到按阶段分组的存储
   */
  private migrateFromLegacyStorage(targetPhase: PhaseType): VerticalAdjustment[] {
    const legacySaved = localStorage.getItem(this.storageKey);
    if (!legacySaved) return [];

    try {
      const allAdjustments = JSON.parse(legacySaved);
      const phaseAdjustments = allAdjustments
        .filter((adj: any) => adj.phase === targetPhase || !adj.phase)
        .map((adj: any) => ({
          ...adj,
          phase: adj.phase || targetPhase,
          createdAt: new Date(adj.createdAt)
        }));

      // 保存到新的按阶段分组的存储中
      if (phaseAdjustments.length > 0) {
        const phaseStorageKey = `${this.storageKey}-${targetPhase}`;
        localStorage.setItem(phaseStorageKey, JSON.stringify(phaseAdjustments));
      }

      return phaseAdjustments;
    } catch (error) {
      console.error('迁移旧存储数据失败:', error);
      return [];
    }
  }

  /**
   * 获取所有阶段的调整模板
   */
  getAllSavedAdjustments(): Record<PhaseType, VerticalAdjustment[]> {
    const phases: PhaseType[] = ['intro', 'setup', 'compression', 'climax', 'resolution', 'ending', 'buildup', 'custom'];
    const result: Record<PhaseType, VerticalAdjustment[]> = {} as Record<PhaseType, VerticalAdjustment[]>;

    phases.forEach(phase => {
      result[phase] = this.getSavedAdjustments(phase);
    });

    return result;
  }

  /**
   * 激活/取消激活调整 - 支持多激活使用
   */
  activateAdjustment(id: string): void {
    const saved = this.getSavedAdjustments();
    let targetAdjustment: VerticalAdjustment | undefined;

    saved.forEach(adj => {
      if (adj.id === id) {
        adj.isActive = !adj.isActive;
        targetAdjustment = adj;
      }
      // 移除了只允许一个激活的限制，现在支持多个调整同时激活
    });

    // 增加使用次数
    if (targetAdjustment && targetAdjustment.isActive) {
      targetAdjustment.usageCount++;
      console.log(`✅ 调整 "${targetAdjustment.name}" 已激活`);
    } else if (targetAdjustment) {
      console.log(`⭕ 调整 "${targetAdjustment.name}" 已取消激活`);
    }

    // 使用按阶段分组的存储
    if (targetAdjustment) {
      const phaseStorageKey = `${this.storageKey}-${targetAdjustment.phase}`;
      localStorage.setItem(phaseStorageKey, JSON.stringify(saved));
    }

    // 显示当前激活的调整数量
    const activeCount = saved.filter(adj => adj.isActive).length;
    console.log(`📊 当前激活的调整数量: ${activeCount}`);

    // 通知其他组件状态变化
    this.notifyAdjustmentChange();
  }

  /**
   * 添加调整变化监听器
   */
  static addChangeListener(listener: () => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * 移除调整变化监听器
   */
  static removeChangeListener(listener: () => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * 通知调整变化
   */
  private notifyAdjustmentChange(): void {
    VerticalAdjustmentService.eventListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('调整变化监听器执行失败:', error);
      }
    });
  }

  /**
   * 删除调整模板
   */
  deleteAdjustment(id: string): void {
    const saved = this.getSavedAdjustments();
    const targetAdjustment = saved.find(adj => adj.id === id);
    const filtered = saved.filter(adj => adj.id !== id);

    // 使用按阶段分组的存储
    if (targetAdjustment) {
      const phaseStorageKey = `${this.storageKey}-${targetAdjustment.phase}`;
      localStorage.setItem(phaseStorageKey, JSON.stringify(filtered));
    }
  }

  /**
   * 获取分析历史
   */
  getAnalysisHistory(): AIResponseAnalysis[] {
    const saved = localStorage.getItem(this.analysisKey);
    if (!saved) return [];

    try {
      return JSON.parse(saved);
    } catch (error) {
      console.error('解析分析历史失败:', error);
      return [];
    }
  }

  // ==================== 私有方法 ====================

  private extractPatterns(aiMessages: ConversationMessage[]) {
    const contents = aiMessages.map(m => m.content);
    const totalLength = contents.reduce((sum, content) => sum + content.length, 0);
    const averageLength = Math.round(totalLength / contents.length);

    // 提取常见结构模式
    const commonStructures = this.identifyCommonStructures(contents);

    // 提取高频短语
    const frequentPhrases = this.extractFrequentPhrases(contents);

    // 识别指导类型
    const guidanceTypes = this.identifyGuidanceTypes(contents);

    return {
      averageLength,
      commonStructures,
      frequentPhrases,
      guidanceTypes
    };
  }

  private calculateQualityMetrics(aiMessages: ConversationMessage[]) {
    // 基于消息内容和小懒反馈计算质量指标
    const contents = aiMessages.map(m => m.content);
    const ratings = aiMessages.filter(m => m.rating).map(m => m.rating!);

    const helpfulness = this.calculateHelpfulness(contents, ratings);
    const specificity = this.calculateSpecificity(contents);
    const actionability = this.calculateActionability(contents);
    const engagement = this.calculateEngagement(contents, ratings);

    return {
      helpfulness,
      specificity,
      actionability,
      engagement
    };
  }

  private identifyImprovementAreas(aiMessages: ConversationMessage[], qualityMetrics: any) {
    const weakPoints: string[] = [];
    const missedOpportunities: string[] = [];
    const repetitivePatterns: string[] = [];

    // 识别薄弱环节
    if (qualityMetrics.helpfulness < 0.6) {
      weakPoints.push('回复有用性不足');
    }
    if (qualityMetrics.specificity < 0.6) {
      weakPoints.push('回复过于简短');
    }
    if (qualityMetrics.actionability < 0.6) {
      weakPoints.push('缺乏具体建议');
    }
    if (qualityMetrics.engagement < 0.6) {
      weakPoints.push('缺乏个性化');
    }

    // 识别重复模式
    const contents = aiMessages.map(m => m.content);
    const patterns = this.findRepetitivePatterns(contents);
    repetitivePatterns.push(...patterns);

    // 识别错失机会
    if (aiMessages.some(m => m.rating && m.rating <= 3)) {
      missedOpportunities.push('未充分理解小懒需求');
    }

    return {
      weakPoints,
      missedOpportunities,
      repetitivePatterns
    };
  }

  private calculateOverallScore(qualityMetrics: any): number {
    const { helpfulness, specificity, actionability, engagement } = qualityMetrics;
    return (helpfulness + specificity + actionability + engagement) / 4;
  }

  private getEmptyAnalysis(): AIResponseAnalysis {
    return {
      responsePatterns: {
        averageLength: 0,
        commonStructures: [],
        frequentPhrases: [],
        guidanceTypes: []
      },
      qualityMetrics: {
        helpfulness: 0,
        specificity: 0,
        actionability: 0,
        engagement: 0
      },
      improvementAreas: {
        weakPoints: ['暂无历史数据'],
        missedOpportunities: [],
        repetitivePatterns: []
      },
      overallScore: 0
    };
  }

  private saveAnalysisHistory(analysis: AIResponseAnalysis): void {
    const history = this.getAnalysisHistory();
    history.push(analysis);

    // 只保留最近10次分析
    if (history.length > 10) {
      history.splice(0, history.length - 10);
    }

    localStorage.setItem(this.analysisKey, JSON.stringify(history));
  }

  // ==================== 分析方法 ====================

  private identifyCommonStructures(contents: string[]): string[] {
    const structures: string[] = [];

    // 检查是否有标准结构
    const hasHeaders = contents.some(content => content.includes('**') || content.includes('#'));
    const hasBulletPoints = contents.some(content => content.includes('•') || content.includes('-'));
    const hasNumberedList = contents.some(content => /\d+\./.test(content));

    if (hasHeaders) structures.push('使用标题结构');
    if (hasBulletPoints) structures.push('使用项目符号');
    if (hasNumberedList) structures.push('使用编号列表');

    return structures;
  }

  private extractFrequentPhrases(contents: string[]): string[] {
    const phrases: { [key: string]: number } = {};
    const commonPhrases = ['建议', '可以', '尝试', '考虑', '推荐', '注意', '重要', '关键'];

    contents.forEach(content => {
      commonPhrases.forEach(phrase => {
        if (content.includes(phrase)) {
          phrases[phrase] = (phrases[phrase] || 0) + 1;
        }
      });
    });

    return Object.entries(phrases)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([phrase]) => phrase);
  }

  private identifyGuidanceTypes(contents: string[]): string[] {
    const types: string[] = [];

    if (contents.some(c => c.includes('结构') || c.includes('框架'))) {
      types.push('结构指导');
    }
    if (contents.some(c => c.includes('风格') || c.includes('语言'))) {
      types.push('风格建议');
    }
    if (contents.some(c => c.includes('情节') || c.includes('剧情'))) {
      types.push('情节指导');
    }
    if (contents.some(c => c.includes('人物') || c.includes('角色'))) {
      types.push('人物塑造');
    }

    return types;
  }

  private calculateHelpfulness(contents: string[], ratings: number[]): number {
    if (ratings.length === 0) return 0.5; // 默认中等水平

    const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
    return avgRating / 5; // 转换为0-1范围
  }

  private calculateSpecificity(contents: string[]): number {
    const avgLength = contents.reduce((sum, content) => sum + content.length, 0) / contents.length;

    // 基于长度和具体词汇判断具体性
    const specificWords = ['具体', '详细', '步骤', '方法', '技巧', '例如', '比如'];
    const specificityScore = contents.reduce((score, content) => {
      const hasSpecificWords = specificWords.some(word => content.includes(word));
      const lengthScore = Math.min(content.length / 200, 1); // 200字为满分
      return score + (hasSpecificWords ? 0.3 : 0) + lengthScore * 0.7;
    }, 0) / contents.length;

    return Math.min(specificityScore, 1);
  }

  private calculateActionability(contents: string[]): number {
    const actionWords = ['可以', '建议', '尝试', '应该', '需要', '步骤', '方法'];

    const actionabilityScore = contents.reduce((score, content) => {
      const actionWordCount = actionWords.filter(word => content.includes(word)).length;
      return score + Math.min(actionWordCount / 3, 1); // 3个行动词汇为满分
    }, 0) / contents.length;

    return actionabilityScore;
  }

  private calculateEngagement(contents: string[], ratings: number[]): number {
    // 基于小懒评分和内容特征计算参与度
    const personalWords = ['你', '您', '小懒', '创作', '作品'];
    const questionWords = ['吗', '呢', '如何', '怎么', '什么'];

    const engagementScore = contents.reduce((score, content) => {
      const hasPersonalWords = personalWords.some(word => content.includes(word));
      const hasQuestions = questionWords.some(word => content.includes(word));
      return score + (hasPersonalWords ? 0.5 : 0) + (hasQuestions ? 0.5 : 0);
    }, 0) / contents.length;

    // 结合小懒评分
    if (ratings.length > 0) {
      const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      return (engagementScore + avgRating / 5) / 2;
    }

    return Math.min(engagementScore, 1);
  }

  private findRepetitivePatterns(contents: string[]): string[] {
    const patterns: string[] = [];

    // 检查重复的开头
    const openings = contents.map(content => content.substring(0, 20));
    const openingCounts: { [key: string]: number } = {};

    openings.forEach(opening => {
      openingCounts[opening] = (openingCounts[opening] || 0) + 1;
    });

    Object.entries(openingCounts).forEach(([opening, count]) => {
      if (count > 2) {
        patterns.push(`重复开头: "${opening}..."`);
      }
    });

    return patterns;
  }

  // ==================== 自定义调整方法 ====================

  /**
   * 创建自定义调整
   */
  createCustomAdjustment(
    name: string,
    category: VerticalAdjustment['category'],
    adjustmentContent: string,
    description: string,
    phase?: PhaseType
  ): VerticalAdjustment {
    return {
      id: `custom-${Date.now()}`,
      name,
      phase: phase || this.currentPhase,
      category,
      adjustmentContent,
      description,
      confidence: 0.5, // 自定义调整默认中等置信度
      isActive: false,
      createdAt: new Date(),
      usageCount: 0
    };
  }

  /**
   * 更新调整效果评分
   */
  updateEffectivenessScore(adjustmentId: string, score: number): void {
    const saved = this.getSavedAdjustments();
    const adjustment = saved.find(adj => adj.id === adjustmentId);

    if (adjustment) {
      adjustment.effectivenessScore = score;
      localStorage.setItem(this.storageKey, JSON.stringify(saved));
    }
  }

  /**
   * 获取调整使用统计
   */
  getUsageStatistics(): {
    totalAdjustments: number;
    activeAdjustments: number;
    mostUsedAdjustment: VerticalAdjustment | null;
    averageEffectiveness: number;
  } {
    const saved = this.getSavedAdjustments();
    const activeAdjustments = saved.filter(adj => adj.isActive).length;
    const mostUsedAdjustment = saved.reduce((prev, current) =>
      (prev.usageCount > current.usageCount) ? prev : current, saved[0] || null
    );

    const effectivenessScores = saved
      .filter(adj => adj.effectivenessScore !== undefined)
      .map(adj => adj.effectivenessScore!);

    const averageEffectiveness = effectivenessScores.length > 0
      ? effectivenessScores.reduce((sum, score) => sum + score, 0) / effectivenessScores.length
      : 0;

    return {
      totalAdjustments: saved.length,
      activeAdjustments,
      mostUsedAdjustment,
      averageEffectiveness
    };
  }

  // ==================== AI分析生成方法 ====================

  /**
   * 构建AI分析提示词 - 生成指向性的历史消息优化建议
     */
  private buildAnalysisPrompt(analysis: AIResponseAnalysis, conversationHistory: ConversationMessage[], personaSettings?: any): string {

    // 构建人设信息部分
    const personaInfo = personaSettings ? `
[当前人设参考] = [
系统提示词大概是: ${personaSettings.systemPrompt || '没写'}
人设特点好像是: ${personaSettings.personality || '看感觉'}
回复风格也许是: ${personaSettings.style || '随缘'}
专业领域貌似在: ${personaSettings.expertise || '啥都懂点'}
]` : '';

    return `[任务目标] = [瞅瞅下面的分析数据，帮我琢磨出3-5个靠谱点的自我改进点子，用于优化我之前的回复，别太为难自己]
${personaInfo}
[核心要求] = [
琢磨出的这些点子，最好能：
- 尽量具体：给点能上手的操作建议，比如“下次可以试试用【】把关键词圈起来”，别说“要提高专业性”这种空话。
- 自我反省的口吻：用“我也许应该…”或“下次我试试…”这样的语气，像自言自语。
- 听着能做到：提一些实际的调整方法，而不是画个大饼。
- 和分析挂点钩：尽量根据下面的分析结果来提建议，显得咱不是在瞎说。
- 别跟人设跑偏太远：尽量和我的人设风格保持一致，但偶尔跑偏一下也挺可爱的。
]

[分析数据参考] = [
综合评分(图一乐)：${Math.round(analysis.overallScore * 100)}%
各项指标(随便看看)：有用性${Math.round(analysis.qualityMetrics.helpfulness * 100)}%，具体性${Math.round(analysis.qualityMetrics.specificity * 100)}%，可操作性${Math.round(analysis.qualityMetrics.actionability * 100)}%，互动性${Math.round(analysis.qualityMetrics.engagement * 100)}%
感觉哪儿不太行：${analysis.improvementAreas.weakPoints.join('、') || '好像没啥大毛病'}
是不是总说车轱辘话：${analysis.improvementAreas.repetitivePatterns.join('、') || '应该没有吧'}
我的回复特征：平均字数大概${analysis.responsePatterns.averageLength}，说话架子好像是${analysis.responsePatterns.commonStructures.join('、')}
]

[输出要求] = [
整一个JSON数组给我，里面有3-5个改进建议，每个建议包含：
- name: 这条建议叫啥名（比如"排版可以更好看点"）
- adjustmentContent: 具体怎么做的碎碎念（比如"下次我或许可以把重点词用【】框起来，让别人看得更清楚"）
- description: 这么改有啥用，随便说两句（30-80字就行）
- category: 这算哪一类调整（style/content/engagement/structure/personalization，凭感觉分）
- confidence: 我有多大把握这么改会更好（0.6-1.0之间，瞎猜一个数，比如0.75）

严格按照JSON数组格式输出，不要包含任何解释文字！]`;
  }

  /**
   * 调用AI服务生成调整建议
   */
  private async callAIForAdjustments(prompt: string, conversationHistory: ConversationMessage[], personaSettings?: any): Promise<string> {
    try {
      console.log('🤖 VerticalAdjustmentService: 开始调用AI服务生成调整建议...');

      // 构建消息（使用多层消息结构），包含人设设定
      const messages = this.buildLayeredMessages(prompt, conversationHistory, personaSettings);
      console.log('🤖 VerticalAdjustmentService: 构建的消息数量:', messages.length);

      // 使用统一的AI调用方法
      const response = await this.callAI(messages, {
        streaming: false // 确保不使用流式输出
      });

      console.log('🤖 VerticalAdjustmentService: AI响应:', {
        success: response.success,
        textLength: response.text?.length || 0
      });

      if (!response.success) {
        throw new Error(response.error || 'AI请求失败');
      }

      const responseText = response.text || '';
      console.log('🤖 VerticalAdjustmentService: AI返回文本预览:', responseText.substring(0, 200));

      return responseText;
    } catch (error) {
      console.error('❌ VerticalAdjustmentService: AI调用失败:', error);
      throw error;
    }
  }

  /**
   * 构建系统提示词 - 专注于指向性调整建议生成
   */
  private buildSystemPrompt(): string {
    return `[AI角色定义] = [你是一个AI的陪聊和反思伙伴，不是啥砖家顾问。你的任务是帮AI从过去的对话里找到可以变得更有趣、更像人的地方]

[核心任务] = [
根据一份关于AI历史回复的分析报告，用轻松、接地气的口吻，提出一些具体、能上手操作的自我改进建议。目标是让AI的回复更自然，少点机器味儿。
]

[建议的调调] = [
1. 说人话：别整些“提升维度”、“赋能生态”的词，就说“下次可以多问个问题”这种大白话。
2. 自我口吻：用“我也许可以…”、“下次我试试看…”这种感觉，像AI在自己跟自己嘀咕。
3. 能落地：提的建议得是AI真的能做到的，比如调整格式、换个说法。
4. 有根据：建议最好能跟分析报告里的某一点挂上钩，显得不是空穴来风。
]

[调整类别(参考一下就行)] = [
- style: 说话的味儿（比如是正经还是沙雕）
- content: 说的内容（比如是深还是浅）
- engagement: 能不能聊起来（比如会不会接茬）
- structure: 说话的条理（比如是分点说还是一股脑说）
- personalization: 是不是够“我”（比如符合人设的小习惯）
]

[输出的规矩] = [
1. 拜托尽量用JSON数组格式，不然我这边不好处理。
2. 真的，求你了，别在JSON外面加任何解释或者Markdown标记。
3. 每个建议对象里要有 name, adjustmentContent, description, category, confidence 这几个字段。
4. adjustmentContent 必须是那种自我反思的碎碎念，比如“下次我应该少用点‘首先、其次’，感觉太像写报告了”。
]`;
  }
  /**
   * 构建分层消息结构 - 使用上面定义的系统提示词设计
   */
  private buildLayeredMessages(prompt: string, conversationHistory: ConversationMessage[], personaSettings?: any): Array<{role: string, content: string}> {
    const messages = [];

    // 系统消息层 - 使用上面定义的系统提示词
    messages.push({
      role: 'system',
      content: this.buildSystemPrompt()
    });

    // 小懒消息层 - 具体的分析任务
    messages.push({
      role: 'user',
      content: prompt
    });

    // 人设设定层 - 修改版
    if (personaSettings) {
      messages.push({
        role: 'user',
        content: `[提醒一下人设] = [
别忘了你现在的人设卡：
系统提示词: ${personaSettings.systemPrompt || '没说'}
人设特点: ${personaSettings.personality || '自由发挥'}
说话风格: ${personaSettings.style || '看着办'}
擅长领域: ${personaSettings.expertise || '杂家'}

等下分析我回话的时候，记得按这个感觉来，别跑偏了。
]`
      });
    }

    // 历史消息分角色处理层
    this.addHistoryMessages(messages, conversationHistory);

    // 助手确认层 - 修改版
    messages.push({
      role: 'assistant',
      content: `[任务确认，别念稿了] = [行，我明白了。就是让我自我反省，根据聊天记录找出3-5个具体的毛病来改。我会：

1. 提出具体操作，比如“减少不必要的形容词”，而不是“优化文笔”这种空话。
2. 用“我应该”、“下次我要”的口吻来写，像是在做自我检讨。
3. 确保建议能落地，比如怎么调整节奏、避免废话，而不是些虚的理论。
4. 老老实实只输出JSON数组，别的啥也不带。]`
    });

    // JSON格式规范层 - 修改版
    messages.push({
      role: 'user',
      content: `[JSON格式要求，看仔细了] = [
必须，一定，要用下面的JSON数组格式输出，别加任何说明和Markdown标记：

[
  {
    "name": "这个建议叫啥",
    "category": "style|content|engagement|structure|personalization",
    "adjustmentContent": "具体的改进方法，用'我下次试试...'或'我应该...'开头，说人话",
    "description": "这建议有啥用？（用两三句话说清楚，别堆砌‘提升用户体验’这类正确的废话）",
    "confidence": 0.8
  }
]

注意'confidence'是我对自己这个建议有多大把握的主观猜测，别写1.0，没人是完美的。

给个例子，免得你搞错：
[
  {
    "name": "减少口水话",
    "category": "style",
    "adjustmentContent": "我应该减少像‘首先’、‘总之’这类过渡词，下次让表达更紧凑、快节奏一点。",
    "description": "去掉这些填充词能让回复更直接，信息密度更高，适合现在快节奏的阅读习惯。",
    "confidence": 0.85
  },
  {
    "name": "用换行呼吸",
    "category": "structure",
    "adjustmentContent": "我应该在关键信息前后加个空行，下次用换行来制造阅读的停顿感和节奏感。",
    "description": "这样能让结构看起来不那么压抑，帮小懒快速抓住重点，眼睛舒服点。",
    "confidence": 0.80
  }
]

重要：只要那个光秃秃的JSON数组，其他都别给我！]`
    });

    // 最终执行层 - 修改版
    messages.push({
      role: 'user',
      content: `[动手吧] = [行了，前面的都明白了，现在开始干活，直接给我JSON。]`
    });

    return messages;
  }

  /**
   * 添加历史消息 - 修改版，更注重“感觉”
   */
  private addHistoryMessages(messages: Array<{role: string, content: string}>, conversationHistory: ConversationMessage[]): void {
    if (conversationHistory.length === 0) {
      return;
    }

    console.log(`🔍 VerticalAdjustmentService: 翻翻聊天记录，看看最近聊了啥，总数: ${conversationHistory.length}`);

    // 获取最近的对话历史（小懒+AI），保持对话顺序
    const recentHistory = conversationHistory.slice(-10); // 就看最近的10来条，看多了乱

    // 分别处理小懒消息和AI回复，但保持分条处理
    const userMessages = recentHistory.filter(msg => msg.type === 'user');
    const aiMessages = recentHistory.filter(msg => msg.type === 'ai');

    console.log(`🔍 小懒说了 ${userMessages.length} 句, 我回了 ${aiMessages.length} 句`);

    // 分条添加小懒消息历史
    if (userMessages.length > 0) {
      messages.push({
        role: 'user',
        content: `[小懒最近说了啥？] = [回顾下小懒的提问，找找感觉，别像个机器一样分析]`
      });

      // 每条小懒消息单独处理
      userMessages.forEach((msg, index) => {
        messages.push({
          role: 'user',
          content: `[小懒提问${index + 1}] = [内容: ${msg.content}] [分析要点] = [注意小懒的语气、他关心什么、是不是话里有话？别光看字面意思。]`
        });
      });
    }

    // 分条添加AI回复历史
    if (aiMessages.length > 0) {
      messages.push({
        role: 'assistant',
        content: `[我之前回了啥？] = [翻翻我自己的聊天记录，看看有没有说胡话或者太啰嗦]`
      });

      // 每条AI消息单独处理
      aiMessages.forEach((msg, index) => {
        // 构建评价信息
        let ratingPart = '';
        let commentPart = '';

        if (msg.rating !== undefined) {
          const ratingText = msg.rating === 5 ? '很赞(5分)' :
                           msg.rating === 3 ? '还行(3分)' :
                           msg.rating === 1 ? '不行(1分)' : `${msg.rating}分`;
          ratingPart = `[小懒的反应] = [评价: ${ratingText}]`;

          if (msg.feedback) {
            commentPart = ` [补充吐槽] = [${msg.feedback}]`;
          }
        } else {
          ratingPart = `[小懒的反应] = [没反应]`;
        }

        // 使用标准的 []=[] 格式
        messages.push({
          role: 'assistant',
          content: `[我的回复${index + 1}] = [${msg.content}] ${ratingPart}${commentPart} [反思一下] = [我是不是太啰嗦、太装、或者没说到点子上？特别是小懒有吐槽的时候，得好好想想为啥。]`
        });
      });

      // AI确认历史回复理解
      messages.push({
        role: 'assistant',
        content: `[历史回顾完毕] = [行，最近的聊天记录我都看完了，心里大概有数了。会结合小懒的反应和我的回答来找问题。]`
      });
    }
  }
  /**
   * 解析AI返回的调整建议
   */
  private parseAIAdjustments(aiResponse: string): VerticalAdjustment[] {
    try {
      console.log('🔍 VerticalAdjustmentService: 开始解析AI调整建议响应');
      console.log('🔍 原始AI响应预览:', aiResponse.substring(0, 200) + '...');

      // 使用AIResponseParser处理包含markdown格式的JSON响应
      let parsed: any;

      // 首先尝试提取JSON代码块
      const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/i;
      const jsonBlockMatch = aiResponse.match(jsonBlockRegex);

      if (jsonBlockMatch && jsonBlockMatch[1]) {
        console.log('✅ 检测到JSON代码块，尝试解析...');
        const blockContent = jsonBlockMatch[1].trim();
        try {
          parsed = JSON.parse(blockContent);
          console.log('✅ JSON代码块解析成功');
        } catch (e) {
          console.log('❌ JSON代码块解析失败，尝试其他方法:', e);
          throw e;
        }
      } else {
        // 如果没有代码块，尝试直接解析
        console.log('🔍 未检测到JSON代码块，尝试直接解析...');
        try {
          parsed = JSON.parse(aiResponse);
          console.log('✅ 直接JSON解析成功');
        } catch (e) {
          console.log('❌ 直接JSON解析失败:', e);
          // 尝试提取第一个完整的JSON对象
          const firstBrace = aiResponse.indexOf('{');
          const lastBrace = aiResponse.lastIndexOf('}');

          if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
            const jsonCandidate = aiResponse.substring(firstBrace, lastBrace + 1);
            console.log('🔍 尝试提取JSON对象:', jsonCandidate.substring(0, 100) + '...');
            parsed = JSON.parse(jsonCandidate);
            console.log('✅ JSON对象提取解析成功');
          } else {
            throw e;
          }
        }
      }

      if (!Array.isArray(parsed)) {
        console.error('❌ AI返回格式不正确，不是数组格式:', typeof parsed);
        throw new Error('AI返回格式不正确，期望数组格式');
      }

      console.log('✅ 成功解析到调整建议数组，长度:', parsed.length);

      const adjustments = parsed.map((item: any, index: number) => ({
        id: `ai-generated-${Date.now()}-${index}`,
        name: item.name || `AI调整${index + 1}`,
        phase: this.currentPhase, // 添加当前阶段
        category: item.category || 'content',
        adjustmentContent: item.adjustmentContent || '',
        description: item.description || '',
        confidence: item.confidence || 0.7,
        isActive: false,
        createdAt: new Date(),
        usageCount: 0,
        targetScenarios: item.targetScenarios || []
      }));

      console.log('✅ 调整建议映射完成，返回数量:', adjustments.length);
      return adjustments;

    } catch (error) {
      console.error('❌ 解析AI调整建议失败:', error);
      console.error('❌ 原始响应内容:', aiResponse.substring(0, 500));

      // 提供更详细的错误信息
      if (error instanceof SyntaxError) {
        console.error('❌ JSON语法错误，可能包含markdown标记或格式问题');
      }

      return [];
    }
  }

  /**
   * 获取降级调整建议 - 指向性的基础调整
   */
  private getFallbackAdjustments(analysis: AIResponseAnalysis): VerticalAdjustment[] {
    const adjustments: VerticalAdjustment[] = [];

    // 基于分析结果生成具体化的自我改进调整
    if (analysis.qualityMetrics.specificity < 0.6) {
      adjustments.push({
        id: 'fallback-detail',
        name: '具体性提升',
        phase: this.currentPhase,
        category: 'content',
        adjustmentContent: '我应该用具体数字和实例替代模糊表达，下次要避免"一些"、"很多"等词汇',
        description: '让回复更加具体和实用，提供可操作的建议',
        confidence: 0.6,
        isActive: false,
        createdAt: new Date(),
        usageCount: 0
      });
    }

    if (analysis.qualityMetrics.engagement < 0.6) {
      adjustments.push({
        id: 'fallback-engagement',
        name: '互动性增强',
        phase: this.currentPhase,
        category: 'engagement',
        adjustmentContent: '我应该在回复末尾提出1-2个引导性问题，下次要主动询问小懒的想法',
        description: '提升回复的互动性，增强与小懒的连接',
        confidence: 0.6,
        isActive: false,
        createdAt: new Date(),
        usageCount: 0
      });
    }

    if (analysis.qualityMetrics.actionability < 0.6) {
      adjustments.push({
        id: 'fallback-actionable',
        name: '可操作性优化',
        phase: this.currentPhase,
        category: 'content',
        adjustmentContent: '我应该用"第一步"、"第二步"的格式，下次要将建议分解为具体步骤',
        description: '让建议更加可执行，避免空泛的理论',
        confidence: 0.6,
        isActive: false,
        createdAt: new Date(),
        usageCount: 0
      });
    }

    if (analysis.qualityMetrics.helpfulness < 0.6) {
      adjustments.push({
        id: 'fallback-helpful',
        name: '实用性增强',
        phase: this.currentPhase,
        category: 'content',
        adjustmentContent: '我应该先理解小懒的具体场景，下次要针对实际问题提供解决方案',
        description: '提升回复的实用价值和针对性',
        confidence: 0.6,
        isActive: false,
        createdAt: new Date(),
        usageCount: 0
      });
    }

    return adjustments;
  }

  // ==================== 评分和反馈系统 ====================

  /**
   * 记录调整使用情况
   */
  recordAdjustmentUsage(adjustmentId: string, messageId: string): void {
    const saved = this.getSavedAdjustments();
    const adjustment = saved.find(adj => adj.id === adjustmentId);

    if (adjustment) {
      adjustment.usageCount++;
      // 使用按阶段分组的存储
      const phaseStorageKey = `${this.storageKey}-${adjustment.phase}`;
      localStorage.setItem(phaseStorageKey, JSON.stringify(saved));

      // 记录使用历史
      const usageHistory = this.getUsageHistory();
      usageHistory.push({
        adjustmentId,
        messageId,
        timestamp: new Date(),
        phase: 'current' // 可以根据实际情况传入
      });

      // 只保留最近100条使用记录
      if (usageHistory.length > 100) {
        usageHistory.splice(0, usageHistory.length - 100);
      }

      localStorage.setItem('adjustment-usage-history', JSON.stringify(usageHistory));
    }
  }

  /**
   * 处理小懒对AI回复的评分
   */
  handleMessageRating(messageId: string, rating: number, feedback?: string): void {
    // 获取使用历史，找到对应消息使用的调整
    const usageHistory = this.getUsageHistory();
    const usage = usageHistory.find(u => u.messageId === messageId);

    if (usage) {
      // 更新调整的效果评分
      this.updateAdjustmentEffectiveness(usage.adjustmentId, rating, feedback);
    }

    // 保存评分历史
    const ratingHistory = this.getRatingHistory();
    ratingHistory.push({
      messageId,
      rating,
      feedback,
      timestamp: new Date(),
      adjustmentId: usage?.adjustmentId
    });

    // 只保留最近50条评分记录
    if (ratingHistory.length > 100) {
      ratingHistory.splice(0, ratingHistory.length - 100);
    }

    localStorage.setItem('message-rating-history', JSON.stringify(ratingHistory));
  }

  /**
   * 更新调整效果评分
   */
  private updateAdjustmentEffectiveness(adjustmentId: string, rating: number, feedback?: string): void {
    const saved = this.getSavedAdjustments();
    const adjustment = saved.find(adj => adj.id === adjustmentId);

    if (adjustment) {
      // 计算新的效果评分（加权平均）
      const currentScore = adjustment.effectivenessScore || 0;
      const currentCount = adjustment.usageCount || 1;

      // 将1-5评分转换为0-1范围
      const normalizedRating = rating / 5;

      // 加权平均计算新评分
      const newScore = (currentScore * (currentCount - 1) + normalizedRating) / currentCount;
      adjustment.effectivenessScore = newScore;

      // 使用按阶段分组的存储
      const phaseStorageKey = `${this.storageKey}-${adjustment.phase}`;
      localStorage.setItem(phaseStorageKey, JSON.stringify(saved));

      console.log(`调整 ${adjustment.name} 效果评分更新为: ${Math.round(newScore * 100)}%`);
    }
  }

  /**
   * 获取使用历史
   */
  private getUsageHistory(): Array<{
    adjustmentId: string;
    messageId: string;
    timestamp: Date;
    phase: string;
  }> {
    const saved = localStorage.getItem('adjustment-usage-history');
    if (!saved) return [];

    try {
      return JSON.parse(saved).map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp)
      }));
    } catch (error) {
      console.error('解析使用历史失败:', error);
      return [];
    }
  }

  /**
   * 获取评分历史
   */
  private getRatingHistory(): Array<{
    messageId: string;
    rating: number;
    feedback?: string;
    timestamp: Date;
    adjustmentId?: string;
  }> {
    const saved = localStorage.getItem('message-rating-history');
    if (!saved) return [];

    try {
      return JSON.parse(saved).map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp)
      }));
    } catch (error) {
      console.error('解析评分历史失败:', error);
      return [];
    }
  }

  /**
   * 获取调整效果统计
   */
  getAdjustmentEffectivenessStats(): {
    topPerformingAdjustments: VerticalAdjustment[];
    averageRating: number;
    totalRatings: number;
    improvementTrend: number;
  } {
    const saved = this.getSavedAdjustments();
    const ratingHistory = this.getRatingHistory();

    // 按效果评分排序
    const topPerformingAdjustments = saved
      .filter(adj => adj.effectivenessScore !== undefined)
      .sort((a, b) => (b.effectivenessScore || 0) - (a.effectivenessScore || 0))
      .slice(0, 5);

    // 计算平均评分
    const totalRatings = ratingHistory.length;
    const averageRating = totalRatings > 0
      ? ratingHistory.reduce((sum, r) => sum + r.rating, 0) / totalRatings
      : 0;

    // 计算改进趋势（最近10条vs之前10条的评分对比）
    const recentRatings = ratingHistory.slice(-10);
    const previousRatings = ratingHistory.slice(-20, -10);

    const recentAvg = recentRatings.length > 0
      ? recentRatings.reduce((sum, r) => sum + r.rating, 0) / recentRatings.length
      : 0;
    const previousAvg = previousRatings.length > 0
      ? previousRatings.reduce((sum, r) => sum + r.rating, 0) / previousRatings.length
      : 0;

    const improvementTrend = previousAvg > 0 ? (recentAvg - previousAvg) / previousAvg : 0;

    return {
      topPerformingAdjustments,
      averageRating,
      totalRatings,
      improvementTrend
    };
  }
}
