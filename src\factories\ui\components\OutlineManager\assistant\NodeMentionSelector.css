/* 节点提及选择器样式 */
.node-mention-selector {
  position: absolute;
  bottom: 100%;
  left: 16px;
  right: 16px;
  margin-bottom: 8px;

  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  max-height: 320px;
  overflow: hidden;

  z-index: 1000;

  animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 从@符号位置展开的动画 */
@keyframes expandFromMention {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
    transform-origin: left bottom;
  }
  60% {
    transform: scale(1.02) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    transform-origin: left bottom;
  }
}

.node-mention-selector.expand-from-mention {
  animation: expandFromMention 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 选择器头部 */
.mention-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 12px 12px 0 0;
}

.mention-header span {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-button {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.debug-button:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.mention-close {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.mention-close:hover {
  background: #e0e0e0;
  color: #333;
}

/* 节点列表 */
.mention-list {
  max-height: 240px;
  overflow-y: auto;
}

/* 节点项 */
.mention-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.mention-item:hover,
.mention-item:focus {
  background: #f5f5f5;
  outline: none;
  transform: translateX(2px);
}

.mention-item:hover .mention-item-icon,
.mention-item:focus .mention-item-icon {
  color: #667eea;
  transform: translateX(2px);
}

.mention-item:hover .mention-item-title,
.mention-item:focus .mention-item-title {
  position: relative;
}

.mention-item:hover .mention-item-title::after,
.mention-item:focus .mention-item-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #667eea;
  animation: underlineSlide 0.2s ease-out;
}

.mention-item:hover .mention-item-type,
.mention-item:focus .mention-item-type {
  box-shadow: 0 0 8px rgba(25, 118, 210, 0.3);
}

.mention-item:active {
  background: #e8f4fd;
  transform: scale(0.98);
}

/* 键盘导航选中状态 */
.mention-item.keyboard-selected {
  background: #e3f2fd;
  border-left: 3px solid #667eea;
  padding-left: 13px;
  animation: keyboardSelect 0.2s ease-out;
}

@keyframes underlineSlide {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes keyboardSelect {
  0% {
    background: #f5f5f5;
    border-left-width: 0;
    padding-left: 16px;
  }
  100% {
    background: #e3f2fd;
    border-left-width: 3px;
    padding-left: 13px;
  }
}

/* 节点图标 */
.mention-item-icon {
  flex-shrink: 0;
  color: #666;
  display: flex;
  align-items: center;
  transition: margin-left 0.2s ease;
}

/* 节点内容 */
.mention-item-content {
  flex: 1;
  min-width: 0;
}

.mention-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;

  /* 文本截断 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mention-item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.mention-item-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 不同数据类型的标签颜色 */
.mention-item-type.type-chapter {
  background: #e3f2fd;
  color: #1976d2;
}

.mention-item-type.type-character {
  background: #e8f5e8;
  color: #2e7d32;
}

.mention-item-type.type-terminology {
  background: #fff3e0;
  color: #f57c00;
}

.mention-item-type.type-worldBuilding {
  background: #f3e5f5;
  color: #7b1fa2;
}

.mention-item-type.type-outlineNode {
  background: #fce4ec;
  color: #c2185b;
}

.mention-item-description {
  opacity: 0.8;
  font-style: italic;

  /* 文本截断 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.mention-item-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 6px;
  padding: 6px 8px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 4px;
  border-left: 2px solid rgba(102, 126, 234, 0.2);
}

.detail-item {
  font-size: 11px;
  color: #555;
  line-height: 1.3;
}

.detail-item strong {
  color: #333;
  font-weight: 600;
  margin-right: 4px;
}

/* 章节详细信息样式 */
.chapter-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.chapter-order {
  color: #2563eb;
  font-weight: 600;
}

.chapter-wordcount {
  color: #059669;
  font-weight: 600;
}

.chapter-summary {
  color: #7c3aed;
  font-style: italic;
}

.chapter-preview {
  color: #6b7280;
  font-size: 10px;
  line-height: 1.2;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 人物详细信息样式 */
.character-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.character-personality {
  color: #dc2626;
}

.character-background {
  color: #7c2d12;
}

.character-goals {
  color: #9333ea;
}

/* 术语详细信息样式 */
.terminology-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.terminology-category {
  color: #ea580c;
}

/* 世界观详细信息样式 */
.worldbuilding-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.worldbuilding-category {
  color: #0891b2;
}

.mention-item-usage {
  background: #f5f5f5;
  color: #666;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.mention-item-path {
  opacity: 0.7;

  /* 文本截断 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 加载状态 */
.mention-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mention-loading p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* 空状态 */
.mention-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: #666;
}

.empty-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: #999;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.empty-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.empty-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.create-data-btn,
.debug-data-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-data-btn {
  background: #667eea;
  color: white;
}

.create-data-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.debug-data-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.debug-data-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 选择器底部 */
.mention-footer {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 12px 12px;
}

.mention-footer small {
  font-size: 11px;
  color: #999;
}

/* 取消提示 */
.mention-cancel-hint {
  padding: 6px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  font-size: 11px;
  color: #666;
  text-align: center;
  border-radius: 0 0 12px 12px;
}

.mention-cancel-hint kbd {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 1px 4px;
  font-family: monospace;
  font-size: 10px;
  margin: 0 2px;
  color: #333;
}

/* 滚动条样式 */
.mention-list::-webkit-scrollbar {
  width: 6px;
}

.mention-list::-webkit-scrollbar-track {
  background: transparent;
}

.mention-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.mention-list::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-mention-selector {
    left: 12px;
    right: 12px;
    max-height: 280px;
  }

  .mention-item {
    padding: 10px 12px;
  }

  .mention-item-path {
    max-width: 120px;
  }

  .mention-footer {
    display: none; /* 移动端隐藏提示 */
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .node-mention-selector {
    background: #2d2d2d;
    border-color: #444;
  }

  .mention-header {
    background: #333;
    border-bottom-color: #444;
  }

  .mention-header span {
    color: #fff;
  }

  .mention-close {
    color: #ccc;
  }

  .mention-close:hover {
    background: #444;
    color: #fff;
  }

  .mention-item:hover,
  .mention-item:focus {
    background: #3a3a3a;
  }

  .mention-item:active {
    background: #1e3a5f;
  }

  .mention-item-title {
    color: #fff;
  }

  .mention-item-meta {
    color: #ccc;
  }

  .mention-item-type {
    background: #1e3a5f;
    color: #64b5f6;
  }

  .mention-empty {
    color: #ccc;
  }

  .empty-icon {
    background: #444;
    color: #666;
  }

  .mention-footer {
    background: #333;
    border-top-color: #444;
  }

  .mention-footer small {
    color: #666;
  }

  .mention-cancel-hint {
    background: #333;
    border-top-color: #444;
    color: #666;
  }

  .mention-cancel-hint kbd {
    background: #444;
    border-color: #555;
    color: #ccc;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .node-mention-selector {
    border: 2px solid #000;
  }

  .mention-item:focus {
    outline: 2px solid #000;
    outline-offset: -2px;
  }

  .mention-item-type {
    border: 1px solid currentColor;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .node-mention-selector {
    animation: none;
  }

  .mention-item {
    transition: none;
  }

  .mention-item-icon {
    transition: none;
  }
}
