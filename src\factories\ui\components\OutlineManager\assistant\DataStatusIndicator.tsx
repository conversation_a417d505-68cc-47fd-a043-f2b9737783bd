"use client";

import React, { useState, useEffect } from 'react';
import { aiAssistantDataService } from '@/services/aiAssistantDataService';
import { db, AIAssistantContextType } from '@/lib/db/dexie';


interface DataStatusIndicatorProps {
  bookId: string;
  onCreateData?: (type: AIAssistantContextType) => void;
}

interface DataStats {
  chapters: number;
  characters: number;
  terminology: number;
  worldBuilding: number;
  total: number;
}

/**
 * 数据状态指示器组件
 * 显示各类型数据的数量，并提供快速创建入口
 */
const DataStatusIndicator: React.FC<DataStatusIndicatorProps> = ({
  bookId,
  onCreateData
}) => {
  const [dataStats, setDataStats] = useState<DataStats>({
    chapters: 0,
    characters: 0,
    terminology: 0,
    worldBuilding: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);


  // 获取数据统计
  const fetchDataStats = async () => {
    if (!bookId) return;

    setIsLoading(true);
    try {
      const [chapters, characters, terminology, worldBuilding] = await Promise.all([
        db.chapters.where('bookId').equals(bookId).count(),
        db.characters.where('bookId').equals(bookId).count(),
        db.terminology.where('bookId').equals(bookId).count(),
        db.worldBuilding.where('bookId').equals(bookId).count()
      ]);

      const stats = {
        chapters,
        characters,
        terminology,
        worldBuilding,
        total: chapters + characters + terminology + worldBuilding
      };

      setDataStats(stats);
      console.log('📊 数据统计更新:', stats);
    } catch (error) {
      console.error('获取数据统计失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDataStats();
  }, [bookId]);

  // 获取类型的中文名称
  const getTypeName = (type: AIAssistantContextType) => {
    switch (type) {
      case AIAssistantContextType.CHAPTER: return '章节';
      case AIAssistantContextType.CHARACTER: return '人物';
      case AIAssistantContextType.TERMINOLOGY: return '术语';
      case AIAssistantContextType.WORLD_BUILDING: return '世界观';
      default: return type;
    }
  };

  // 获取类型的图标
  const getTypeIcon = (type: AIAssistantContextType) => {
    switch (type) {
      case AIAssistantContextType.CHAPTER:
        return '📖';
      case AIAssistantContextType.CHARACTER:
        return '👤';
      case AIAssistantContextType.TERMINOLOGY:
        return '📚';
      case AIAssistantContextType.WORLD_BUILDING:
        return '🌍';
      default:
        return '📄';
    }
  };

  // 获取类型的数量
  const getTypeCount = (type: AIAssistantContextType) => {
    switch (type) {
      case AIAssistantContextType.CHAPTER: return dataStats.chapters;
      case AIAssistantContextType.CHARACTER: return dataStats.characters;
      case AIAssistantContextType.TERMINOLOGY: return dataStats.terminology;
      case AIAssistantContextType.WORLD_BUILDING: return dataStats.worldBuilding;
      default: return 0;
    }
  };

  // 处理创建数据
  const handleCreateData = (type: AIAssistantContextType) => {
    if (onCreateData) {
      onCreateData(type);
    } else {
      // 提示用户去相应的管理面板创建数据
      const typeName = getTypeName(type);
      alert(`请在${typeName}管理面板中创建数据，创建后即可在@功能中使用`);
    }
  };

  if (isLoading) {
    return (
      <div className="data-status-indicator loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
        </div>
        <span>检查数据...</span>
      </div>
    );
  }

  const dataTypes = [
    AIAssistantContextType.CHARACTER,
    AIAssistantContextType.TERMINOLOGY,
    AIAssistantContextType.WORLD_BUILDING,
    AIAssistantContextType.CHAPTER
  ];

  return (
    <div className="data-status-indicator">
      <div
        className="data-summary"
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="summary-info">
          <span className="total-count">{dataStats.total}</span>
          <span className="summary-text">个可@内容</span>
        </div>
        <button className="toggle-details">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="currentColor"
            style={{
              transform: showDetails ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease'
            }}
          >
            <path d="M7 10l5 5 5-5z"/>
          </svg>
        </button>
      </div>

      {showDetails && (
        <div className="data-details">
          {dataTypes.map(type => {
            const count = getTypeCount(type);
            const isEmpty = count === 0;

            return (
              <div key={type} className={`data-type-item ${isEmpty ? 'empty' : ''}`}>
                <div className="type-info">
                  <span className="type-icon">{getTypeIcon(type)}</span>
                  <span className="type-name">{getTypeName(type)}</span>
                  <span className="type-count">({count})</span>
                </div>

                {isEmpty && (
                  <button
                    className="create-button"
                    onClick={() => handleCreateData(type)}
                    title={`创建第一个${getTypeName(type)}`}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    创建
                  </button>
                )}
              </div>
            );
          })}
        </div>
      )}

      {dataStats.total === 0 && (
        <div className="empty-state">
          <div className="empty-icon">💡</div>
          <p>还没有可@的内容</p>
          <small>创建人物、术语、世界观等数据后，就可以在AI对话中使用@功能了</small>
        </div>
      )}
    </div>
  );
};

export default DataStatusIndicator;
