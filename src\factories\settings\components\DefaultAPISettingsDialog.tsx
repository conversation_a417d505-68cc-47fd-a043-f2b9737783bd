"use client";

import React, { useState, useEffect, useRef } from 'react';
import { IAPISettingsDialogComponent } from '../interfaces';
import {
  DualAIConfig,
  getDefaultDualAIConfig,
  DEFAULT_SYSTEM_PROMPTS
} from '@/types/DualAIConfig';
import { DualAIConfigManager } from '@/utils/ai/DualAIConfigManager';
import {
  APIKeyConfig,
  URLKeyPool,
  RotationStrategy,
  RequestResult
} from '@/types/apiKeyRotation';
import { APIKeyRotationManager } from '@/utils/ai/APIKeyRotationManager';

/**
 * 默认API设置弹窗组件实现
 */
export class DefaultAPISettingsDialogComponent implements IAPISettingsDialogComponent {
  private isOpen: boolean = false;
  private currentProvider: string = 'openai';
  private currentModel: string = 'gemini-2.5-pro-exp-03-25';
  private availableModels: string[] = ['gemini-2.5-pro-exp-03-25', 'gpt-4', 'gpt-4-turbo'];
  private streamingEnabled: boolean = true;
  private maxTokens: number = 80000; // 默认最大token数
  private topK: number = 40; // 默认Top K值
  private topP: number = 1.0; // 默认Top P值
  private providerChangeCallback: ((provider: string) => void) | null = null;
  private modelChangeCallback: ((model: string) => void) | null = null;

  // 双AI配置相关属性
  private dualAIEnabled: boolean = false;
  private dualAIConfig: DualAIConfig = getDefaultDualAIConfig();

  // API密钥轮播相关属性
  private rotationEnabled: boolean = false;
  private rotationManager: APIKeyRotationManager;

  // API密钥和端点存储
  private apiKeys: Record<string, string> = {
    'openai': '',
    'google': '',
    'custom': ''
  };

  private apiEndpoints: Record<string, string> = {
    'openai': 'https://api.openai.com/v1',
    'google': 'https://generativelanguage.googleapis.com/v1',
    'custom': ''
  };

  constructor() {
    // 初始化轮播管理器
    this.rotationManager = APIKeyRotationManager.getInstance({
      enableHealthCheck: true,
      enableEventLogging: true,
      enableStats: true
    });

    // 尝试从localStorage加载设置
    // 注意：这在服务器端渲染时不会执行，但在客户端实例化时会执行
    if (typeof window !== 'undefined') {
      this.loadSettingsFromLocalStorage();
    }
  }

  /**
   * 从localStorage加载设置
   */
  private loadSettingsFromLocalStorage(): void {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      console.log('非浏览器环境，跳过localStorage加载');
      return;
    }

    try {
      const savedSettings = localStorage.getItem('api_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);

        // 加载保存的设置
        if (settings.currentProvider) {
          this.currentProvider = settings.currentProvider;
        }

        if (settings.currentModel) {
          this.currentModel = settings.currentModel;
        }

        if (settings.apiKeys) {
          Object.keys(settings.apiKeys).forEach(provider => {
            this.apiKeys[provider] = settings.apiKeys[provider];
          });
        }

        if (settings.apiEndpoints) {
          Object.keys(settings.apiEndpoints).forEach(provider => {
            this.apiEndpoints[provider] = settings.apiEndpoints[provider];
          });
        }

        if (settings.streamingEnabled !== undefined) {
          this.streamingEnabled = settings.streamingEnabled;
        }

        // 加载最大token数
        if (settings.maxTokens !== undefined) {
          this.maxTokens = settings.maxTokens;
        }

        // 加载Top K参数
        if (settings.topK !== undefined) {
          this.topK = settings.topK;
        }

        // 加载Top P参数
        if (settings.topP !== undefined) {
          this.topP = settings.topP;
        }

        // 加载供应商特定的模型列表
        if (settings.providerModels && settings.providerModels[this.currentProvider]) {
          const providerModels = settings.providerModels[this.currentProvider];
          if (Array.isArray(providerModels) && providerModels.length > 0) {
            this.availableModels = providerModels;
          }
        }

        // 加载双AI配置
        if (settings.dualAIEnabled !== undefined) {
          this.dualAIEnabled = settings.dualAIEnabled;
        }
      }

      // 尝试加载双AI配置
      const dualAIConfig = DualAIConfigManager.load();
      if (dualAIConfig) {
        this.dualAIConfig = dualAIConfig;
        this.dualAIEnabled = dualAIConfig.mode === 'dual';
      }
    } catch (error) {
      console.error('从localStorage加载API设置失败:', error);
    }
  }

  /**
   * 设置是否显示弹窗
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 获取当前API提供商
   * @returns 当前API提供商ID
   */
  getCurrentProvider(): string {
    return this.currentProvider;
  }

  /**
   * 设置当前API提供商
   * @param provider 提供商ID
   */
  setCurrentProvider(provider: string): void {
    this.currentProvider = provider;
    if (this.providerChangeCallback) {
      this.providerChangeCallback(provider);
    }
  }

  /**
   * 设置API提供商变更回调
   * @param callback 回调函数
   */
  onProviderChange(callback: (provider: string) => void): void {
    this.providerChangeCallback = callback;
  }

  /**
   * 获取当前API密钥
   * @param provider 提供商ID，如果不提供则返回当前提供商的密钥
   * @returns API密钥
   */
  getAPIKey(provider?: string): string {
    const targetProvider = provider || this.currentProvider;
    return this.apiKeys[targetProvider] || '';
  }

  /**
   * 设置API密钥
   * @param key API密钥
   * @param provider 提供商ID，如果不提供则设置当前提供商的密钥
   */
  setAPIKey(key: string, provider?: string): void {
    const targetProvider = provider || this.currentProvider;
    this.apiKeys[targetProvider] = key;
  }

  /**
   * 获取当前模型
   * @returns 当前模型ID
   */
  getCurrentModel(): string {
    return this.currentModel;
  }

  /**
   * 设置当前模型
   * @param model 模型ID
   */
  setCurrentModel(model: string): void {
    this.currentModel = model;
    if (this.modelChangeCallback) {
      this.modelChangeCallback(model);
    }
  }

  /**
   * 设置模型变更回调
   * @param callback 回调函数
   */
  onModelChange(callback: (model: string) => void): void {
    this.modelChangeCallback = callback;
  }

  /**
   * 获取API端点URL
   * @param provider 提供商ID，如果不提供则返回当前提供商的URL
   * @returns API端点URL
   */
  getAPIEndpoint(provider?: string): string {
    const targetProvider = provider || this.currentProvider;
    return this.apiEndpoints[targetProvider] || '';
  }

  /**
   * 设置API端点URL
   * @param url API端点URL
   * @param provider 提供商ID，如果不提供则设置当前提供商的URL
   */
  setAPIEndpoint(url: string, provider?: string): void {
    const targetProvider = provider || this.currentProvider;
    this.apiEndpoints[targetProvider] = url;
  }

  /**
   * 获取可用模型列表
   * @returns 模型列表
   */
  getAvailableModels(): string[] {
    return this.availableModels;
  }

  /**
   * 设置可用模型列表
   * @param models 模型列表
   */
  setAvailableModels(models: string[]): void {
    this.availableModels = models;
  }

  /**
   * 获取是否启用流式输出
   * @returns 是否启用流式输出
   */
  getStreamingEnabled(): boolean {
    return this.streamingEnabled;
  }

  /**
   * 设置是否启用流式输出
   * @param enabled 是否启用
   */
  setStreamingEnabled(enabled: boolean): void {
    this.streamingEnabled = enabled;
  }

  /**
   * 获取最大token数
   * @returns 最大token数
   */
  getMaxTokens(): number {
    return this.maxTokens;
  }

  /**
   * 设置最大token数
   * @param tokens 最大token数
   */
  setMaxTokens(tokens: number): void {
    this.maxTokens = tokens;
  }

  /**
   * 获取Top K参数
   * @returns Top K参数
   */
  getTopK(): number {
    return this.topK;
  }

  /**
   * 设置Top K参数
   * @param topK Top K参数
   */
  setTopK(topK: number): void {
    this.topK = topK;
  }

  /**
   * 获取Top P参数
   * @returns Top P参数
   */
  getTopP(): number {
    return this.topP;
  }

  /**
   * 设置Top P参数
   * @param topP Top P参数
   */
  setTopP(topP: number): void {
    this.topP = topP;
  }

  /**
   * 获取是否启用双AI模式
   * @returns 是否启用双AI模式
   */
  getDualAIEnabled(): boolean {
    return this.dualAIEnabled;
  }

  /**
   * 设置是否启用双AI模式
   * @param enabled 是否启用
   */
  setDualAIEnabled(enabled: boolean): void {
    this.dualAIEnabled = enabled;
    // 更新双AI配置的模式
    this.dualAIConfig.mode = enabled ? 'dual' : 'single';
  }

  /**
   * 获取双AI配置
   * @returns 双AI配置
   */
  getDualAIConfig(): DualAIConfig {
    return this.dualAIConfig;
  }

  /**
   * 设置双AI配置
   * @param config 双AI配置
   */
  setDualAIConfig(config: DualAIConfig): void {
    this.dualAIConfig = config;
    this.dualAIEnabled = config.mode === 'dual';
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const APISettingsDialog = () => {
      const [isOpen, setIsOpen] = useState(this.isOpen);
      const [currentProvider, setCurrentProvider] = useState(this.currentProvider);
      const [currentModel, setCurrentModel] = useState(this.currentModel);
      const [availableModels, setAvailableModels] = useState(this.availableModels);
      const [streamingEnabled, setStreamingEnabled] = useState(this.streamingEnabled);
      const [topK, setTopKState] = useState(this.topK);
      const [topP, setTopPState] = useState(this.topP);
      const [apiKeys, setApiKeys] = useState(this.apiKeys);
      const [apiEndpoints, setApiEndpoints] = useState(this.apiEndpoints);
      const [activeTab, setActiveTab] = useState('general');
      const [isLoading, setIsLoading] = useState(false);
      const [errorMessage, setErrorMessage] = useState('');
      const dialogRef = useRef<HTMLDivElement>(null);

      // 双AI配置状态
      const [dualAIEnabled, setDualAIEnabledState] = useState(this.dualAIEnabled);
      const [dualAIConfig, setDualAIConfigState] = useState(this.dualAIConfig);

      // 提供商选项
      const providerOptions = [
        {
          id: 'openai',
          name: 'OpenAI',
          logo: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
              <line x1="12" y1="22.08" x2="12" y2="12"></line>
            </svg>
          )
        },
        {
          id: 'google',
          name: 'Google AI',
          logo: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="4"></circle>
              <line x1="21.17" y1="8" x2="12" y2="8"></line>
              <line x1="3.95" y1="6.06" x2="8.54" y2="14"></line>
              <line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>
            </svg>
          )
        },
        {
          id: 'custom',
          name: '自定义',
          logo: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
            </svg>
          )
        }
      ];

      // 监听isOpen变化
      useEffect(() => {
        setIsOpen(this.isOpen);

        // 当弹窗打开时，尝试从本地存储加载设置
        if (this.isOpen && typeof window !== 'undefined') {
          try {
            const savedSettings = localStorage.getItem('api_settings');
            if (savedSettings) {
              const settings = JSON.parse(savedSettings);

              // 加载保存的设置
              if (settings.currentProvider) {
                setCurrentProvider(settings.currentProvider);
                this.setCurrentProvider(settings.currentProvider);
              }

              if (settings.currentModel) {
                setCurrentModel(settings.currentModel);
                this.setCurrentModel(settings.currentModel);
              }

              if (settings.apiKeys) {
                setApiKeys(settings.apiKeys);
                Object.keys(settings.apiKeys).forEach(provider => {
                  this.setAPIKey(settings.apiKeys[provider], provider);
                });
              }

              if (settings.apiEndpoints) {
                setApiEndpoints(settings.apiEndpoints);
                Object.keys(settings.apiEndpoints).forEach(provider => {
                  this.setAPIEndpoint(settings.apiEndpoints[provider], provider);
                });
              }

              if (settings.streamingEnabled !== undefined) {
                setStreamingEnabled(settings.streamingEnabled);
                this.setStreamingEnabled(settings.streamingEnabled);
              }

              // 加载Top K参数
              if (settings.topK !== undefined) {
                setTopKState(settings.topK);
                this.setTopK(settings.topK);
              }

              // 加载Top P参数
              if (settings.topP !== undefined) {
                setTopPState(settings.topP);
                this.setTopP(settings.topP);
              }

              // 加载供应商特定的模型列表
              if (settings.providerModels && settings.providerModels[settings.currentProvider]) {
                const providerModels = settings.providerModels[settings.currentProvider];
                if (Array.isArray(providerModels) && providerModels.length > 0) {
                  setAvailableModels(providerModels);
                  this.setAvailableModels(providerModels);
                  console.log(`已加载${settings.currentProvider}的模型列表:`, providerModels);
                }
              }

              // 加载双AI配置
              if (settings.dualAIEnabled !== undefined) {
                setDualAIEnabledState(settings.dualAIEnabled);
                this.setDualAIEnabled(settings.dualAIEnabled);
              }

              console.log('已加载保存的API设置');
            }

            // 尝试加载双AI配置
            const dualAIConfig = DualAIConfigManager.load();
            if (dualAIConfig) {
              setDualAIConfigState(dualAIConfig);
              setDualAIEnabledState(dualAIConfig.mode === 'dual');
              this.setDualAIConfig(dualAIConfig);
              console.log('已加载双AI配置:', dualAIConfig.mode);
            }
          } catch (error) {
            console.error('加载API设置失败:', error);
          }
        }
      }, [this.isOpen]);

      // 监听currentProvider变化
      useEffect(() => {
        setCurrentProvider(this.currentProvider);
      }, [this.currentProvider]);

      // 监听currentModel变化
      useEffect(() => {
        setCurrentModel(this.currentModel);
      }, [this.currentModel]);

      // 监听availableModels变化
      useEffect(() => {
        setAvailableModels(this.availableModels);
      }, [this.availableModels]);

      // 监听streamingEnabled变化
      useEffect(() => {
        setStreamingEnabled(this.streamingEnabled);
      }, [this.streamingEnabled]);

      // 监听topK变化
      useEffect(() => {
        setTopKState(this.topK);
      }, [this.topK]);

      // 监听topP变化
      useEffect(() => {
        setTopPState(this.topP);
      }, [this.topP]);

      // 点击外部关闭弹窗
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
            handleClose();
          }
        };

        if (isOpen) {
          document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isOpen]);

      // 点击外部关闭下拉菜单
      useEffect(() => {
        const handleClickOutsideDropdown = (event: MouseEvent) => {
          const dropdown = document.getElementById('model-dropdown');
          const trigger = document.getElementById('model-dropdown-trigger');

          if (dropdown &&
              !dropdown.classList.contains('hidden') &&
              trigger &&
              !dropdown.contains(event.target as Node) &&
              !trigger.contains(event.target as Node)) {

            // 关闭下拉菜单
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'translateY(-10px)';
            setTimeout(() => {
              dropdown.classList.add('hidden');
            }, 200);
          }
        };

        // 使用捕获阶段以确保在其他事件处理程序之前处理
        document.addEventListener('mousedown', handleClickOutsideDropdown, true);

        // 添加ESC键关闭下拉菜单
        const handleEscKey = (event: KeyboardEvent) => {
          if (event.key === 'Escape') {
            const dropdown = document.getElementById('model-dropdown');
            if (dropdown && !dropdown.classList.contains('hidden')) {
              dropdown.style.opacity = '0';
              dropdown.style.transform = 'translateY(-10px)';
              setTimeout(() => {
                dropdown.classList.add('hidden');
              }, 200);
            }
          }
        };

        document.addEventListener('keydown', handleEscKey);

        return () => {
          document.removeEventListener('mousedown', handleClickOutsideDropdown, true);
          document.removeEventListener('keydown', handleEscKey);
        };
      }, []);

      // 处理关闭弹窗
      const handleClose = () => {
        // 保存所有设置到本地存储
        if (typeof window !== 'undefined') {
          try {
            const settings = {
              currentProvider,
              currentModel,
              apiKeys,
              apiEndpoints,
              streamingEnabled,
              maxTokens: this.maxTokens,
              topK: this.topK,
              topP: this.topP,
              // 为每个供应商保存模型列表
              providerModels: {
                openai: currentProvider === 'openai' ? availableModels : [],
                google: currentProvider === 'google' ? availableModels : [],
                custom: currentProvider === 'custom' ? availableModels : []
              },
              // 保存双AI配置状态
              dualAIEnabled,
              lastUpdated: new Date().toISOString()
            };
            localStorage.setItem('api_settings', JSON.stringify(settings));
            console.log('API设置已保存');

            // 保存双AI配置
            if (dualAIEnabled) {
              DualAIConfigManager.save(dualAIConfig);
              console.log('双AI配置已保存');
            }
          } catch (error) {
            console.error('保存设置失败:', error);
          }
        }

        setIsOpen(false);
        this.setIsOpen(false);
      };

      // 处理提供商变更
      const handleProviderChange = (providerId: string) => {
        // 保存当前提供商的设置和模型列表
        if (typeof window !== 'undefined') {
          try {
            // 获取已保存的设置
            const savedSettingsStr = localStorage.getItem('api_settings');
            let savedSettings = savedSettingsStr ? JSON.parse(savedSettingsStr) : {};

            // 确保providerModels对象存在
            if (!savedSettings.providerModels) {
              savedSettings.providerModels = {};
            }

            // 保存当前提供商的模型列表
            savedSettings.providerModels[currentProvider] = availableModels;

            // 保存更新后的设置
            localStorage.setItem('api_settings', JSON.stringify(savedSettings));
          } catch (error) {
            console.error('保存当前提供商设置失败:', error);
          }
        }

        // 切换提供商
        setCurrentProvider(providerId);
        this.setCurrentProvider(providerId);

        // 加载新提供商的设置
        // 如果是自定义提供商，可能需要加载自定义端点
        if (providerId === 'custom' && apiEndpoints.custom) {
          // 自定义端点已配置，无需额外操作
        } else {
          // 使用默认端点
          const defaultEndpoints = {
            'openai': 'https://api.openai.com/v1',
            'google': 'https://generativelanguage.googleapis.com/v1',
            'custom': ''
          };

          // 如果当前提供商没有配置端点，使用默认端点
          if (!apiEndpoints[providerId]) {
            const newApiEndpoints = { ...apiEndpoints };
            newApiEndpoints[providerId] = defaultEndpoints[providerId];
            setApiEndpoints(newApiEndpoints);
            this.apiEndpoints = newApiEndpoints;
          }
        }

        // 尝试加载新提供商的模型列表
        if (typeof window !== 'undefined') {
          try {
            const savedSettingsStr = localStorage.getItem('api_settings');
            if (savedSettingsStr) {
              const savedSettings = JSON.parse(savedSettingsStr);
              if (savedSettings.providerModels && savedSettings.providerModels[providerId] &&
                  Array.isArray(savedSettings.providerModels[providerId]) &&
                  savedSettings.providerModels[providerId].length > 0) {
                // 加载保存的模型列表
                setAvailableModels(savedSettings.providerModels[providerId]);
                this.setAvailableModels(savedSettings.providerModels[providerId]);
                console.log(`已加载${providerId}的保存模型列表:`, savedSettings.providerModels[providerId]);

                // 如果当前模型不在新的模型列表中，选择第一个模型
                if (!savedSettings.providerModels[providerId].includes(currentModel)) {
                  setCurrentModel(savedSettings.providerModels[providerId][0]);
                  this.setCurrentModel(savedSettings.providerModels[providerId][0]);
                }

                return; // 已加载保存的模型列表，不需要再获取
              }
            }
          } catch (error) {
            console.error('加载提供商模型列表失败:', error);
          }
        }

        // 如果没有保存的模型列表或加载失败，且新提供商有配置的API密钥，尝试获取模型列表
        if (apiKeys[providerId]) {
          // 延迟一点时间再获取模型列表，确保UI已更新
          setTimeout(() => {
            fetchAvailableModels();
          }, 500);
        } else {
          // 如果没有API密钥，使用默认模型列表
          const defaultModels = {
            'openai': ['gemini-2.5-pro-exp-03-25', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'],
            'google': ['gemini-2.5-pro-thinking', 'gemini-ultra', 'palm-2'],
            'custom': ['gemini-2.5-pro-exp-03-25', 'gpt-4', 'claude-2', 'llama-2', 'mistral-7b', 'mixtral-8x7b']
          };

          setAvailableModels(defaultModels[providerId] || []);
          this.setAvailableModels(defaultModels[providerId] || []);

          // 如果当前模型不在新的模型列表中，选择第一个模型
          if (defaultModels[providerId] && !defaultModels[providerId].includes(currentModel) && defaultModels[providerId].length > 0) {
            setCurrentModel(defaultModels[providerId][0]);
            this.setCurrentModel(defaultModels[providerId][0]);
          }
        }
      };

      // 处理模型变更
      const handleModelChange = (model: string) => {
        setCurrentModel(model);
        this.setCurrentModel(model);
      };

      // 处理API密钥变更
      const handleApiKeyChange = (key: string, provider: string) => {
        const newApiKeys = { ...apiKeys, [provider]: key };
        setApiKeys(newApiKeys);
        this.setAPIKey(key, provider);
      };

      // 处理API端点变更
      const handleApiEndpointChange = (url: string, provider: string) => {
        const newApiEndpoints = { ...apiEndpoints, [provider]: url };
        setApiEndpoints(newApiEndpoints);
        this.setAPIEndpoint(url, provider);
      };

      // 处理流式输出变更
      const handleStreamingChange = (enabled: boolean) => {
        setStreamingEnabled(enabled);
        this.setStreamingEnabled(enabled);
      };

      // 处理Top K参数变更
      const handleTopKChange = (value: number) => {
        setTopKState(value);
        this.setTopK(value);
      };

      // 处理Top P参数变更
      const handleTopPChange = (value: number) => {
        setTopPState(value);
        this.setTopP(value);
      };

      // 处理双AI模式切换
      const handleDualAIModeToggle = (enabled: boolean) => {
        setDualAIEnabledState(enabled);
        this.setDualAIEnabled(enabled);

        // 如果启用双AI模式，初始化配置
        if (enabled && (!dualAIConfig.models.outline.url || !dualAIConfig.models.dialogue.url)) {
          const newConfig = {
            ...dualAIConfig,
            mode: 'dual' as const,
            models: {
              outline: {
                ...dualAIConfig.models.outline,
                url: apiEndpoints[currentProvider] || '',
                apiKey: apiKeys[currentProvider] || '',
                modelName: currentModel
              },
              dialogue: {
                ...dualAIConfig.models.dialogue,
                url: apiEndpoints[currentProvider] || '',
                apiKey: apiKeys[currentProvider] || '',
                modelName: currentModel
              }
            }
          };
          setDualAIConfigState(newConfig);
          this.setDualAIConfig(newConfig);
        }
      };

      // 处理双AI配置更新
      const handleDualAIConfigUpdate = (modelType: 'outline' | 'dialogue', field: string, value: string) => {
        const newConfig = {
          ...dualAIConfig,
          models: {
            ...dualAIConfig.models,
            [modelType]: {
              ...dualAIConfig.models[modelType],
              [field]: value
            }
          }
        };
        setDualAIConfigState(newConfig);
        this.setDualAIConfig(newConfig);
      };

      // 实际获取可用模型
      const fetchAvailableModels = async () => {
        setIsLoading(true);
        setErrorMessage('');

        try {
          // 根据不同提供商获取模型列表
          let models: string[] = [];
          const apiKey = apiKeys[currentProvider];

          if (!apiKey && currentProvider !== 'custom') {
            setErrorMessage('请先输入API密钥');
            setIsLoading(false);
            return;
          }

          switch (currentProvider) {
            case 'openai':
              // 实际调用OpenAI API获取模型列表
              try {
                const response = await fetch('https://api.openai.com/v1/models', {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                  }
                });

                if (!response.ok) {
                  throw new Error(`API请求失败: ${response.status}`);
                }

                const data = await response.json();
                // 过滤出GPT模型
                models = data.data
                  .filter((model: any) =>
                    model.id.includes('gpt') &&
                    !model.id.includes('instruct') &&
                    !model.id.includes('-vision-')
                  )
                  .map((model: any) => model.id)
                  .sort();

                // 如果没有找到模型，使用默认列表
                if (models.length === 0) {
                  models = ['gemini-2.5-pro-exp-03-25', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'];
                }
              } catch (error) {
                console.error('OpenAI API请求失败:', error);
                // 使用默认模型列表
                models = ['gemini-2.5-pro-exp-03-25', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'];
                setErrorMessage('无法从OpenAI获取模型列表，使用默认列表');
              }
              break;

            case 'google':
              // Google API通常有固定的模型列表
              models = ['gemini-2.5-pro-thinking', 'gemini-ultra', 'palm-2'];
              break;

            case 'custom':
              // 对于自定义API，尝试获取模型列表，如果失败则使用通用列表
              try {
                const customEndpoint = apiEndpoints.custom;
                if (!customEndpoint) {
                  throw new Error('未设置自定义API端点');
                }

                // 尝试从自定义端点获取模型列表
                // 这里假设自定义端点遵循OpenAI兼容的API格式
                const response = await fetch(`${customEndpoint}/models`, {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${apiKey || ''}`,
                    'Content-Type': 'application/json'
                  }
                });

                if (!response.ok) {
                  throw new Error(`自定义API请求失败: ${response.status}`);
                }

                const data = await response.json();
                if (data.data && Array.isArray(data.data)) {
                  models = data.data.map((model: any) => model.id || model.name || model);
                } else {
                  throw new Error('自定义API返回格式不正确');
                }
              } catch (error) {
                console.error('自定义API请求失败:', error);
                // 使用通用模型列表
                models = ['gemini-2.5-pro-thinking', 'gpt-4', 'claude-2', 'llama-2', 'mistral-7b', 'mixtral-8x7b'];
                setErrorMessage('无法从自定义API获取模型列表，使用通用列表');
              }
              break;

            default:
              models = ['gemini-2.5-pro-thinking'];
          }

          setAvailableModels(models);
          this.setAvailableModels(models);

          // 如果当前模型不在新的模型列表中，选择第一个模型
          if (!models.includes(currentModel) && models.length > 0) {
            setCurrentModel(models[0]);
            this.setCurrentModel(models[0]);
          }

          // 持久化当前提供商的模型列表
          try {
            // 获取已保存的设置
            const savedSettingsStr = localStorage.getItem('api_settings');
            let savedSettings = savedSettingsStr ? JSON.parse(savedSettingsStr) : {};

            // 确保providerModels对象存在
            if (!savedSettings.providerModels) {
              savedSettings.providerModels = {};
            }

            // 保存当前提供商的模型列表
            savedSettings.providerModels[currentProvider] = models;

            // 保存更新后的设置
            localStorage.setItem('api_settings', JSON.stringify(savedSettings));
            console.log(`已保存${currentProvider}的模型列表:`, models);
          } catch (error) {
            console.error('保存模型列表失败:', error);
          }
        } catch (error) {
          console.error('获取模型列表失败', error);
          setErrorMessage('获取模型列表失败，请检查API密钥和网络连接');
        } finally {
          setIsLoading(false);
        }
      };

      if (!isOpen) return null;

      // 定义动画样式
      const overlayStyle = {
        opacity: isOpen ? 1 : 0,
        backdropFilter: isOpen ? 'blur(8px)' : 'blur(0px)',
        transition: 'all 0.5s cubic-bezier(0.16, 1, 0.3, 1)'
      };

      const dialogStyle = {
        transform: isOpen ? 'scale(1) translateY(0)' : 'scale(0.9) translateY(-20px)',
        opacity: isOpen ? 1 : 0,
        transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)'
      };

      return (
        <div
          className="fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-30 backdrop-blur-sm"
          style={overlayStyle}
        >
          <div
            ref={dialogRef}
            className="bg-white rounded-2xl shadow-2xl w-[500px] max-w-full overflow-hidden"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderColor: 'var(--color-secondary)',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 0, 0, 0.05)',
              ...dialogStyle
            }}
          >
            <div className="flex justify-between items-center p-4 border-b" style={{ borderColor: 'var(--color-secondary)' }}>
              <h2
                className="text-xl font-medium transition-all duration-500 flex items-center"
                style={{
                  color: 'var(--color-primary)',
                  transform: isOpen ? 'translateY(0)' : 'translateY(-20px)',
                  opacity: isOpen ? 1 : 0,
                  transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s'
                }}
              >
                <span className="mr-2">⚡</span>
                API设置
              </h2>
              <button
                onClick={handleClose}
                className="text-gray-500 hover:text-gray-700 transition-all duration-300 transform hover:rotate-90"
                style={{
                  opacity: isOpen ? 1 : 0,
                  transition: 'all 0.3s ease-in-out 0.2s'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 选项卡导航 */}
            <div className="flex border-b" style={{ borderColor: 'var(--color-secondary)' }}>
              <button
                className={`px-4 py-2 text-sm font-medium transition-all duration-300 ${activeTab === 'general' ? 'border-b-2 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                style={{ borderColor: activeTab === 'general' ? 'var(--color-primary)' : 'transparent' }}
                onClick={() => setActiveTab('general')}
              >
                常规设置
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium transition-all duration-300 ${activeTab === 'dualai' ? 'border-b-2 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                style={{ borderColor: activeTab === 'dualai' ? 'var(--color-primary)' : 'transparent' }}
                onClick={() => setActiveTab('dualai')}
              >
                双AI协同
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium transition-all duration-300 ${activeTab === 'advanced' ? 'border-b-2 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                style={{ borderColor: activeTab === 'advanced' ? 'var(--color-primary)' : 'transparent' }}
                onClick={() => setActiveTab('advanced')}
              >
                高级参数
              </button>
            </div>

            <div
              className="p-6 max-h-[70vh] overflow-y-auto"
              style={{
                opacity: isOpen ? 1 : 0,
                transform: isOpen ? 'translateY(0)' : 'translateY(20px)',
                transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s'
              }}
            >
              {/* 常规设置选项卡 */}
              {activeTab === 'general' && (
                <div className="space-y-6">
                  {/* API提供商选择 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>API提供商</h3>
                    <div className="grid grid-cols-3 gap-3">
                      {providerOptions.map(provider => (
                        <div
                          key={provider.id}
                          className={`flex flex-col items-center justify-center p-3 rounded-lg cursor-pointer transition-all duration-300 ${currentProvider === provider.id ? 'bg-blue-50 border-blue-300' : 'bg-gray-50 border-gray-200 hover:bg-gray-100'}`}
                          style={{
                            border: '1px solid',
                            borderColor: currentProvider === provider.id ? 'var(--color-primary)' : 'var(--color-secondary)',
                            boxShadow: currentProvider === provider.id ? '0 2px 8px rgba(66, 153, 225, 0.15)' : 'none'
                          }}
                          onClick={() => handleProviderChange(provider.id)}
                        >
                          <div className={`text-blue-600 mb-2 transition-all duration-300 ${currentProvider === provider.id ? 'scale-110' : 'scale-100'}`}>
                            {provider.logo}
                          </div>
                          <div className="text-sm font-medium" style={{ color: currentProvider === provider.id ? 'var(--color-primary)' : 'var(--color-text-primary)' }}>
                            {provider.name}
                          </div>
                          {apiKeys[provider.id] && (
                            <div className="mt-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-600">
                              已配置
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* API密钥设置 */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-lg font-medium" style={{ color: 'var(--color-primary)' }}>API密钥</h3>
                      <span className="text-xs px-2 py-1 rounded-full bg-blue-50 text-blue-600">
                        {currentProvider === 'openai' ? 'OpenAI API密钥' :
                         currentProvider === 'google' ? 'Google API密钥' : '自定义API密钥'}
                      </span>
                    </div>

                    {/* 当前供应商的密钥输入框 */}
                    <div className="p-4 rounded-lg border border-blue-300 bg-blue-50 transition-all duration-300">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          {currentProvider === 'openai' && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                              <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                              <line x1="12" y1="22.08" x2="12" y2="12"></line>
                            </svg>
                          )}
                          {currentProvider === 'google' && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <circle cx="12" cy="12" r="4"></circle>
                              <line x1="21.17" y1="8" x2="12" y2="8"></line>
                              <line x1="3.95" y1="6.06" x2="8.54" y2="14"></line>
                              <line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>
                            </svg>
                          )}
                          {currentProvider === 'custom' && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                            </svg>
                          )}
                          <span className="font-medium">
                            {currentProvider === 'openai' ? 'OpenAI' :
                             currentProvider === 'google' ? 'Google AI' : '自定义'}
                          </span>
                        </div>
                        <div className={`px-2 py-1 text-xs rounded-full ${apiKeys[currentProvider] ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'}`}>
                          {apiKeys[currentProvider] ? '已配置' : '未配置'}
                        </div>
                      </div>

                      <div className="relative">
                        <input
                          type="password"
                          value={apiKeys[currentProvider]}
                          onChange={(e) => handleApiKeyChange(e.target.value, currentProvider)}
                          placeholder={`输入${providerOptions.find(p => p.id === currentProvider)?.name || ''}的API密钥`}
                          className="w-full p-3 pr-10 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200"
                          style={{
                            borderColor: 'var(--color-secondary)',
                            focusRing: 'var(--color-primary)'
                          }}
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                          </svg>
                        </div>
                      </div>

                      {/* 密钥状态指示 */}
                      <div className="mt-3 flex items-center">
                        {apiKeys[currentProvider] ? (
                          <div className="flex items-center text-green-600">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span className="text-xs">密钥已设置</span>
                          </div>
                        ) : (
                          <div className="flex items-center text-yellow-600">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <line x1="12" y1="8" x2="12" y2="12"></line>
                              <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <span className="text-xs">请输入API密钥</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <p className="text-xs mt-2 text-gray-500">
                      API密钥将安全地存储在您的浏览器中，不会发送到我们的服务器。每个提供商的密钥独立管理。
                    </p>
                  </div>

                  {/* 自定义端点URL（仅当选择"自定义"提供商时显示） */}
                  {currentProvider === 'custom' && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>API端点URL</h3>
                      <input
                        type="text"
                        value={apiEndpoints.custom}
                        onChange={(e) => handleApiEndpointChange(e.target.value, 'custom')}
                        placeholder="输入自定义API端点URL"
                        className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200"
                        style={{
                          borderColor: 'var(--color-secondary)',
                          focusRing: 'var(--color-primary)'
                        }}
                      />
                      <p className="text-xs mt-2 text-gray-500">
                        例如: https://api.example.com/v1
                      </p>
                    </div>
                  )}

                  {/* 模型选择 */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-lg font-medium" style={{ color: 'var(--color-primary)' }}>模型选择</h3>
                      <button
                        onClick={fetchAvailableModels}
                        className="text-xs px-3 py-1.5 rounded-lg transition-all duration-300 flex items-center"
                        style={{
                          backgroundColor: isLoading ? 'var(--color-secondary)' : 'var(--color-primary)',
                          color: 'white',
                          opacity: isLoading ? 0.7 : 1,
                          transform: 'scale(1)',
                          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)'
                        }}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <div className="w-3 h-3 mr-1 rounded-full border-2 border-t-2 border-white border-t-transparent animate-spin"></div>
                            <span>获取中...</span>
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>获取可用模型</span>
                          </>
                        )}
                      </button>
                    </div>

                    {errorMessage && (
                      <div className="mb-3 p-2 bg-red-50 text-red-600 rounded-lg text-sm">
                        {errorMessage}
                      </div>
                    )}

                    {/* 完全重新实现的自定义下拉框组件 */}
                    <div className="relative">
                      {/* 自定义下拉框触发器 - 使用button元素确保可访问性 */}
                      <button
                        type="button"
                        id="model-dropdown-trigger"
                        className="w-full p-3 pl-4 pr-10 border rounded-lg bg-white cursor-pointer flex items-center justify-between transition-all duration-200 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        style={{
                          borderColor: 'var(--color-secondary)',
                          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
                        }}
                        onClick={() => {
                          // 使用状态来控制下拉菜单的显示
                          const dropdownElement = document.getElementById('model-dropdown');
                          if (!dropdownElement) return;

                          const isHidden = dropdownElement.classList.contains('hidden');

                          // 关闭所有其他可能打开的下拉菜单
                          document.querySelectorAll('.custom-dropdown-menu:not(.hidden)').forEach(menu => {
                            if (menu.id !== 'model-dropdown') {
                              menu.classList.add('hidden');
                            }
                          });

                          if (isHidden) {
                            // 打开下拉菜单
                            dropdownElement.classList.remove('hidden');
                            // 使用RAF确保DOM更新后再添加动画
                            requestAnimationFrame(() => {
                              dropdownElement.style.opacity = '1';
                              dropdownElement.style.transform = 'translateY(0)';
                            });
                          } else {
                            // 关闭下拉菜单
                            dropdownElement.style.opacity = '0';
                            dropdownElement.style.transform = 'translateY(-10px)';
                            // 等待动画完成后隐藏元素
                            setTimeout(() => {
                              dropdownElement.classList.add('hidden');
                            }, 200);
                          }
                        }}
                        aria-haspopup="listbox"
                        aria-expanded="false"
                        aria-labelledby="model-dropdown-label"
                      >
                        <div className="flex items-center">
                          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                            <span className="text-blue-600 text-xs font-medium">
                              {currentModel.includes('gpt-4') ? '4' :
                               currentModel.includes('gpt-3') ? '3' :
                               currentModel.includes('gemini') ? 'G' :
                               currentModel.includes('claude') ? 'C' :
                               currentModel.includes('llama') ? 'L' :
                               currentModel.includes('mistral') ? 'M' : 'AI'}
                            </span>
                          </div>
                          <span className="truncate">{currentModel}</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                      </button>

                      {/* 下拉菜单 */}
                      <div
                        id="model-dropdown"
                        className="custom-dropdown-menu absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg hidden flex-col max-h-60 overflow-y-auto"
                        style={{
                          borderColor: 'var(--color-secondary)',
                          opacity: 0,
                          transform: 'translateY(-10px)',
                          transition: 'opacity 0.2s ease, transform 0.2s ease',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                        }}
                        role="listbox"
                      >
                        {/* 搜索框 - 当模型数量较多时有用 */}
                        {availableModels.length > 5 && (
                          <div className="sticky top-0 p-2 bg-white border-b" style={{ borderColor: 'var(--color-secondary)' }}>
                            <div className="relative">
                              <input
                                type="text"
                                placeholder="搜索模型..."
                                className="w-full p-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                style={{ borderColor: 'var(--color-secondary)' }}
                                onChange={(e) => {
                                  // 简单的搜索功能
                                  const searchTerm = e.target.value.toLowerCase();
                                  const items = document.querySelectorAll('.model-dropdown-item');
                                  items.forEach(item => {
                                    const text = item.textContent?.toLowerCase() || '';
                                    if (text.includes(searchTerm)) {
                                      (item as HTMLElement).style.display = 'flex';
                                    } else {
                                      (item as HTMLElement).style.display = 'none';
                                    }
                                  });
                                }}
                              />
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                              </svg>
                            </div>
                          </div>
                        )}

                        {/* 模型列表 */}
                        <div className="overflow-y-auto">
                          {availableModels.map((model, index) => (
                            <div
                              key={model}
                              id={`model-option-${index}`}
                              className={`model-dropdown-item p-3 cursor-pointer flex items-center hover:bg-blue-50 transition-colors duration-150 ${model === currentModel ? 'bg-blue-50' : ''}`}
                              onClick={() => {
                                handleModelChange(model);
                                // 关闭下拉菜单
                                const dropdown = document.getElementById('model-dropdown');
                                if (dropdown) {
                                  dropdown.style.opacity = '0';
                                  dropdown.style.transform = 'translateY(-10px)';
                                  setTimeout(() => {
                                    dropdown.classList.add('hidden');
                                  }, 200);
                                }
                              }}
                              role="option"
                              aria-selected={model === currentModel}
                              tabIndex={0}
                              onKeyDown={(e) => {
                                // 支持键盘导航
                                if (e.key === 'Enter' || e.key === ' ') {
                                  e.preventDefault();
                                  handleModelChange(model);
                                  // 关闭下拉菜单
                                  const dropdown = document.getElementById('model-dropdown');
                                  if (dropdown) {
                                    dropdown.style.opacity = '0';
                                    dropdown.style.transform = 'translateY(-10px)';
                                    setTimeout(() => {
                                      dropdown.classList.add('hidden');
                                    }, 200);
                                  }
                                }
                              }}
                            >
                              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                <span className="text-blue-600 text-xs font-medium">
                                  {model.includes('gpt-4') ? '4' :
                                   model.includes('gpt-3') ? '3' :
                                   model.includes('gemini') ? 'G' :
                                   model.includes('claude') ? 'C' :
                                   model.includes('llama') ? 'L' :
                                   model.includes('mistral') ? 'M' : 'AI'}
                                </span>
                              </div>
                              <div className="flex flex-col flex-grow min-w-0">
                                <span className="text-sm font-medium truncate">{model}</span>
                                <span className="text-xs text-gray-500 truncate">
                                  {model.includes('gpt-4') ? '高级模型，更强的推理能力' :
                                   model.includes('gpt-3') ? '基础模型，性价比高' :
                                   model.includes('gemini') ? 'Google AI模型' :
                                   model.includes('claude') ? 'Anthropic Claude模型' :
                                   model.includes('llama') ? 'Meta开源模型' :
                                   model.includes('mistral') ? 'Mistral AI模型' : '通用AI模型'}
                                </span>
                              </div>
                              {model === currentModel && (
                                <div className="ml-2 flex-shrink-0">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                  </svg>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 隐藏的原生select元素，用于保持表单提交功能 */}
                      <select
                        value={currentModel}
                        onChange={(e) => handleModelChange(e.target.value)}
                        className="sr-only"
                        aria-hidden="true"
                        tabIndex={-1}
                        style={{
                          position: 'absolute',
                          width: '1px',
                          height: '1px',
                          padding: 0,
                          margin: '-1px',
                          overflow: 'hidden',
                          clip: 'rect(0, 0, 0, 0)',
                          whiteSpace: 'nowrap',
                          borderWidth: 0
                        }}
                      >
                        {availableModels.map(model => (
                          <option key={model} value={model}>
                            {model}
                          </option>
                        ))}
                      </select>
                    </div>

                    <p className="text-xs mt-2 text-gray-500">
                      选择要使用的AI模型。不同模型有不同的能力和价格。
                    </p>
                  </div>
                </div>
              )}

              {/* 双AI协同选项卡 */}
              {activeTab === 'dualai' && (
                <div className="space-y-6">
                  {/* 双AI模式开关 */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium" style={{ color: 'var(--color-primary)' }}>启用双AI协同模式</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          使用两个专业化的AI模型：大纲AI负责结构规划，对话AI负责人物对话创作
                        </p>
                      </div>
                      <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full">
                        <input
                          type="checkbox"
                          id="dual-ai-toggle"
                          checked={dualAIEnabled}
                          onChange={(e) => handleDualAIModeToggle(e.target.checked)}
                          className="absolute w-6 h-6 transition duration-200 ease-in-out transform bg-white border-4 rounded-full appearance-none cursor-pointer checked:translate-x-6 checked:border-primary"
                          style={{
                            borderColor: dualAIEnabled ? 'var(--color-primary)' : 'var(--color-secondary)',
                            left: '0',
                            top: '0'
                          }}
                        />
                        <label
                          htmlFor="dual-ai-toggle"
                          className="block w-full h-full overflow-hidden rounded-full cursor-pointer"
                          style={{ backgroundColor: dualAIEnabled ? 'var(--color-primary)' : 'var(--color-secondary)' }}
                        ></label>
                      </div>
                    </div>
                  </div>

                  {/* 双AI配置区域 */}
                  {dualAIEnabled && (
                    <div className="space-y-6">
                      {/* 大纲AI配置 */}
                      <div className="p-4 border rounded-lg" style={{ borderColor: '#4285F4', backgroundColor: '#F8FAFF' }}>
                        <div className="flex items-center mb-4">
                          <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div>
                            <h4 className="text-lg font-medium text-blue-700">大纲AI模型</h4>
                            <p className="text-sm text-blue-600">专注于章节结构设计和情节规划</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">API端点</label>
                            <input
                              type="text"
                              value={dualAIConfig.models.outline.url}
                              onChange={(e) => handleDualAIConfigUpdate('outline', 'url', e.target.value)}
                              placeholder="https://api.openai.com/v1"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                            <input
                              type="password"
                              value={dualAIConfig.models.outline.apiKey}
                              onChange={(e) => handleDualAIConfigUpdate('outline', 'apiKey', e.target.value)}
                              placeholder="输入大纲AI的API密钥"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">模型名称</label>
                            <input
                              type="text"
                              value={dualAIConfig.models.outline.modelName}
                              onChange={(e) => handleDualAIConfigUpdate('outline', 'modelName', e.target.value)}
                              placeholder="gemini-2.5-pro-exp-03-25"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 对话AI配置 */}
                      <div className="p-4 border rounded-lg" style={{ borderColor: '#34A853', backgroundColor: '#F8FFF9' }}>
                        <div className="flex items-center mb-4">
                          <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                          </div>
                          <div>
                            <h4 className="text-lg font-medium text-green-700">对话AI模型</h4>
                            <p className="text-sm text-green-600">专注于人物对话创作和情感表达</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">API端点</label>
                            <input
                              type="text"
                              value={dualAIConfig.models.dialogue.url}
                              onChange={(e) => handleDualAIConfigUpdate('dialogue', 'url', e.target.value)}
                              placeholder="https://api.openai.com/v1"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                            <input
                              type="password"
                              value={dualAIConfig.models.dialogue.apiKey}
                              onChange={(e) => handleDualAIConfigUpdate('dialogue', 'apiKey', e.target.value)}
                              placeholder="输入对话AI的API密钥"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">模型名称</label>
                            <input
                              type="text"
                              value={dualAIConfig.models.dialogue.modelName}
                              onChange={(e) => handleDualAIConfigUpdate('dialogue', 'modelName', e.target.value)}
                              placeholder="gemini-2.5-pro-exp-03-25"
                              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2"
                              style={{ borderColor: 'var(--color-secondary)' }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 共享参数说明 */}
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="text-md font-medium text-gray-700 mb-2">共享参数</h4>
                        <p className="text-sm text-gray-600">
                          温度、最大Token数、Top P、Top K等参数将在两个AI模型间共享，可在"高级参数"选项卡中调整。
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 高级参数选项卡 */}
              {activeTab === 'advanced' && (
                <div className="space-y-6">
                  {/* 流式输出设置 */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium" style={{ color: 'var(--color-primary)' }}>流式输出</h3>
                      <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full">
                        <input
                          type="checkbox"
                          id="streaming-toggle"
                          checked={streamingEnabled}
                          onChange={(e) => handleStreamingChange(e.target.checked)}
                          className="absolute w-6 h-6 transition duration-200 ease-in-out transform bg-white border-4 rounded-full appearance-none cursor-pointer checked:translate-x-6 checked:border-primary"
                          style={{
                            borderColor: streamingEnabled ? 'var(--color-primary)' : 'var(--color-secondary)',
                            left: '0',
                            top: '0'
                          }}
                        />
                        <label
                          htmlFor="streaming-toggle"
                          className="block w-full h-full overflow-hidden rounded-full cursor-pointer"
                          style={{ backgroundColor: streamingEnabled ? 'var(--color-primary)' : 'var(--color-secondary)' }}
                        ></label>
                      </div>
                    </div>
                    <p className="text-sm mt-2 text-gray-600">
                      启用流式输出可以实时显示AI生成的内容，提供更好的用户体验。
                    </p>
                  </div>

                  {/* 温度设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>温度</h3>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        defaultValue="0.7"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          accentColor: 'var(--color-primary)'
                        }}
                      />
                      <span className="ml-3 text-sm" style={{ color: 'var(--color-text-primary)' }}>0.7</span>
                    </div>
                    <p className="text-xs mt-2 text-gray-500">
                      较低的值使输出更确定，较高的值使输出更随机和创造性。
                    </p>
                  </div>

                  {/* 最大令牌数 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>最大令牌数</h3>
                    <input
                      type="number"
                      defaultValue={this.maxTokens.toString()}
                      min="0"
                      max="100000"
                      className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200"
                      style={{
                        borderColor: 'var(--color-secondary)',
                        focusRing: 'var(--color-primary)'
                      }}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value >= 0) {
                          this.setMaxTokens(value);
                        }
                      }}
                    />
                    <p className="text-xs mt-2 text-gray-500">
                      限制AI生成的最大令牌数量。设置为0表示不限制，由API自行决定。较大的值可能会增加API调用成本。
                      <br />
                      <strong>注意：</strong> AI拆书功能需要较大的token数（建议80000以上）以处理长文本。
                    </p>
                  </div>

                  {/* Top P 设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>Top P</h3>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.05"
                        value={topP}
                        onChange={(e) => handleTopPChange(parseFloat(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          accentColor: 'var(--color-primary)'
                        }}
                      />
                      <span className="ml-3 text-sm" style={{ color: 'var(--color-text-primary)' }}>{topP.toFixed(2)}</span>
                    </div>
                    <p className="text-xs mt-2 text-gray-500">
                      控制模型考虑的词汇范围。较低的值会限制模型只考虑最可能的词汇。
                    </p>
                  </div>

                  {/* Top K 设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>Top K</h3>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="1"
                        max="100"
                        step="1"
                        value={topK}
                        onChange={(e) => handleTopKChange(parseInt(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          accentColor: 'var(--color-primary)'
                        }}
                      />
                      <span className="ml-3 text-sm" style={{ color: 'var(--color-text-primary)' }}>{topK}</span>
                    </div>
                    <p className="text-xs mt-2 text-gray-500">
                      控制候选词汇数量，范围1-100。较低的值使输出更集中，较高的值增加词汇多样性。
                    </p>
                  </div>

                  {/* Frequency Penalty 设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>频率惩罚</h3>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="-2"
                        max="2"
                        step="0.1"
                        defaultValue="0"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          accentColor: 'var(--color-primary)'
                        }}
                      />
                      <span className="ml-3 text-sm" style={{ color: 'var(--color-text-primary)' }}>0</span>
                    </div>
                    <p className="text-xs mt-2 text-gray-500">
                      减少模型重复使用相同词汇的倾向。较高的值会使输出更加多样化。
                    </p>
                  </div>

                  {/* Presence Penalty 设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>存在惩罚</h3>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="-2"
                        max="2"
                        step="0.1"
                        defaultValue="0"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          accentColor: 'var(--color-primary)'
                        }}
                      />
                      <span className="ml-3 text-sm" style={{ color: 'var(--color-text-primary)' }}>0</span>
                    </div>
                    <p className="text-xs mt-2 text-gray-500">
                      减少模型讨论已经提到过的主题的倾向。较高的值会鼓励模型讨论新主题。
                    </p>
                  </div>

                  {/* 停止序列 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>停止序列</h3>
                    <input
                      type="text"
                      placeholder="输入停止序列，用逗号分隔"
                      className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200"
                      style={{
                        borderColor: 'var(--color-secondary)',
                        focusRing: 'var(--color-primary)'
                      }}
                    />
                    <p className="text-xs mt-2 text-gray-500">
                      当模型生成这些序列时停止生成。例如: "###, END, STOP"
                    </p>
                  </div>

                  {/* 超时设置 */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>请求超时（秒）</h3>
                    <input
                      type="number"
                      defaultValue="60"
                      min="10"
                      max="300"
                      className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200"
                      style={{
                        borderColor: 'var(--color-secondary)',
                        focusRing: 'var(--color-primary)'
                      }}
                    />
                    <p className="text-xs mt-2 text-gray-500">
                      API请求的最大等待时间。对于较长的生成任务，建议设置更长的超时时间。
                    </p>
                  </div>
                </div>
              )}

              <div className="flex justify-end mt-6">
                <button
                  onClick={handleClose}
                  className="px-6 py-2.5 rounded-lg transition-all duration-300 transform hover:scale-105"
                  style={{
                    backgroundColor: 'var(--color-primary)',
                    color: 'white',
                    boxShadow: '0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  确认
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    };

    return <APISettingsDialog />;
  }
}
