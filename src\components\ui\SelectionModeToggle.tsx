"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface SelectionModeToggleProps {
  isSelectionMode: boolean;
  onToggle: () => void;
  disabled?: boolean;
  className?: string;
}

/**
 * 选择模式切换组件
 * 提供查看模式和选择模式之间的切换
 */
export const SelectionModeToggle: React.FC<SelectionModeToggleProps> = ({
  isSelectionMode,
  onToggle,
  disabled = false,
  className = ''
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {/* Toggle开关 */}
      <motion.button
        className={`
          relative inline-flex items-center h-8 w-16 rounded-full border-2 transition-all duration-300
          focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2
          ${disabled 
            ? 'bg-gray-200 border-gray-300 cursor-not-allowed' 
            : isSelectionMode
              ? 'bg-emerald-500 border-emerald-500'
              : 'bg-gray-200 border-gray-300 hover:border-emerald-300'
          }
        `}
        onClick={onToggle}
        disabled={disabled}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        whileHover={!disabled ? { scale: 1.02 } : {}}
        whileTap={!disabled ? { scale: 0.98 } : {}}
        aria-label={isSelectionMode ? '切换到查看模式' : '切换到选择模式'}
        role="switch"
        aria-checked={isSelectionMode}
      >
        {/* 滑动按钮 */}
        <motion.div
          className={`
            inline-block w-6 h-6 rounded-full shadow-lg transform transition-all duration-300
            ${disabled 
              ? 'bg-gray-400' 
              : isSelectionMode 
                ? 'bg-white' 
                : 'bg-white'
            }
          `}
          animate={{
            x: isSelectionMode ? 32 : 4,
            scale: isSelectionMode ? [1, 1.1, 1] : 1
          }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 25
          }}
        >
          {/* 模式图标 */}
          <div className="w-full h-full flex items-center justify-center">
            {isSelectionMode ? (
              // 选择模式图标 - 勾选列表
              <svg className="w-3 h-3 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            ) : (
              // 查看模式图标 - 眼睛
              <svg className="w-3 h-3 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </div>
        </motion.div>

        {/* 背景标签 */}
        <div className="absolute inset-0 flex items-center justify-between px-2 text-xs font-medium pointer-events-none">
          <span className={`transition-opacity duration-300 ${!isSelectionMode ? 'opacity-100 text-gray-600' : 'opacity-0'}`}>
            查看
          </span>
          <span className={`transition-opacity duration-300 ${isSelectionMode ? 'opacity-100 text-white' : 'opacity-0'}`}>
            选择
          </span>
        </div>
      </motion.button>

      {/* Tooltip提示 */}
      {showTooltip && !disabled && (
        <motion.div
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-50"
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 5 }}
          transition={{ duration: 0.2 }}
        >
          {isSelectionMode 
            ? '切换到查看模式' 
            : '选择需要修改的句子，提高处理效率'
          }
          {/* 箭头 */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900" />
        </motion.div>
      )}
    </div>
  );
};

/**
 * 选择模式状态指示器
 */
interface SelectionModeIndicatorProps {
  isSelectionMode: boolean;
  selectedCount: number;
  totalCount: number;
  className?: string;
}

export const SelectionModeIndicator: React.FC<SelectionModeIndicatorProps> = ({
  isSelectionMode,
  selectedCount,
  totalCount,
  className = ''
}) => {
  if (!isSelectionMode) return null;

  return (
    <motion.div
      className={`inline-flex items-center px-3 py-1.5 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <svg className="w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
      </svg>
      选择模式
      <span className="ml-2 px-2 py-0.5 bg-emerald-200 text-emerald-900 rounded text-xs">
        {selectedCount}/{totalCount}
      </span>
    </motion.div>
  );
};

/**
 * 快捷键提示组件
 */
interface KeyboardShortcutsProps {
  isVisible: boolean;
  className?: string;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  isVisible,
  className = ''
}) => {
  if (!isVisible) return null;

  const shortcuts = [
    { key: 'Space', description: '切换句子选择' },
    { key: 'Shift + A', description: '全选' },
    { key: 'Shift + D', description: '取消全选' },
    { key: 'Tab', description: '在句子间导航' }
  ];

  return (
    <motion.div
      className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.2 }}
    >
      <h4 className="text-sm font-medium text-gray-900 mb-3">快捷键</h4>
      <div className="space-y-2">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex items-center justify-between text-sm">
            <span className="text-gray-600">{shortcut.description}</span>
            <kbd className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-mono">
              {shortcut.key}
            </kbd>
          </div>
        ))}
      </div>
    </motion.div>
  );
};
