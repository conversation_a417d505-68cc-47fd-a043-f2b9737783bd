/* @功能调试面板样式 */
.mention-debug-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.debug-content {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  max-height: 90vh;
  width: 90%;
  overflow: hidden;
  animation: debugPanelAppear 0.3s ease;
}

@keyframes debugPanelAppear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 调试面板头部 */
.debug-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.debug-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  padding: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 调试面板主体 */
.debug-body {
  padding: 24px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 24px;
}

.debug-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.stat-item {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
}

/* 搜索测试 */
.search-test {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.search-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.search-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 搜索结果 */
.search-results {
  margin-top: 16px;
}

.search-results h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.results-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 4px;
}

.result-type {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.result-title {
  font-weight: 500;
  color: #333;
}

.result-desc {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.no-results {
  color: #666;
  font-style: italic;
  margin: 0;
}

/* 数据详情 */
.data-tabs {
  space-y: 8px;
}

.data-detail {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.data-detail summary {
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  font-weight: 500;
  border-radius: 6px 6px 0 0;
  transition: background-color 0.2s ease;
}

.data-detail summary:hover {
  background: #e9ecef;
}

.data-detail[open] summary {
  border-bottom: 1px solid #e0e0e0;
  border-radius: 6px 6px 0 0;
}

.data-content {
  padding: 16px;
  background: #f8f9fa;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 0;
  border-radius: 0 0 6px 6px;
}

.empty-data {
  padding: 16px;
  color: #666;
  font-style: italic;
  text-align: center;
  margin: 0;
}

/* 操作建议 */
.suggestions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
}

.suggestion {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.suggestion:last-child {
  margin-bottom: 0;
}

.suggestion-icon {
  font-size: 16px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .debug-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .debug-header {
    padding: 16px 20px;
  }
  
  .debug-body {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .search-input-group {
    flex-direction: column;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .debug-content {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .debug-header {
    border-bottom-color: #4a5568;
  }
  
  .debug-section h4 {
    color: #e2e8f0;
  }
  
  .stat-item {
    background: linear-gradient(135deg, #4a5568, #2d3748);
    border-color: #4a5568;
  }
  
  .search-test {
    background: #4a5568;
  }
  
  .search-input {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .data-detail {
    border-color: #4a5568;
  }
  
  .data-detail summary {
    background: #4a5568;
    color: #e2e8f0;
  }
  
  .data-content {
    background: #2d3748;
    color: #e2e8f0;
  }
}
