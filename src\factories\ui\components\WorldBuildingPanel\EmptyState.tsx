"use client";

import React from 'react';

/**
 * 世界观详情空状态组件
 */
export const EmptyState: React.FC = () => {
  return (
    <div className="h-full flex flex-col justify-center items-center p-8 text-center">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-200 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <h3 className="text-xl font-medium text-gray-400 mb-2">没有选中的世界观元素</h3>
      <p className="text-gray-500">从左侧列表选择一个世界观元素，或创建一个新的</p>
    </div>
  );
};
