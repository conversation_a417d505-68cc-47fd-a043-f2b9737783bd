/* AI助手按钮样式 */
.assistant-button {
  /* 按钮基础样式 */
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;

  /* 阴影效果 */
  box-shadow:
    0 4px 12px rgba(102, 126, 234, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Panel中的按钮样式 */
.assistant-panel {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
}

/* 悬停效果 */
.assistant-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 6px 20px rgba(102, 126, 234, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.assistant-button:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* 激活状态 */
.assistant-button.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow:
    0 6px 20px rgba(79, 172, 254, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.assistant-button.disabled {
  background: #e0e0e0;
  color: #9e9e9e;
  cursor: not-allowed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assistant-button.disabled:hover {
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图标容器 */
.assistant-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.assistant-button:hover .assistant-icon {
  transform: rotate(10deg);
}

.assistant-button.active .assistant-icon {
  transform: rotate(0deg);
}

/* 活跃指示器 */
.assistant-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4caf50;
  border: 2px solid white;
}

/* 脉冲动画 */
.pulse-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #4caf50;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 工具提示 */
.assistant-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;

  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;

  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  /* 箭头 */
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
  }
}

.assistant-button:hover .assistant-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assistant-button {
    right: 16px;
    width: 48px;
    height: 48px;
  }

  .assistant-tooltip {
    display: none; /* 移动端隐藏工具提示 */
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .assistant-button.disabled {
    background: #424242;
    color: #757575;
  }

  .assistant-tooltip {
    background: rgba(255, 255, 255, 0.9);
    color: #333;

    &::after {
      border-left-color: rgba(255, 255, 255, 0.9);
    }
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .assistant-button {
    border: 2px solid #000;
  }

  .assistant-button.active {
    border-color: #fff;
  }
}
