import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Markdown渲染组件
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  // 简化的Markdown渲染，专门处理工具调用结果
  const renderMarkdown = (text: string) => {
    return text
      // 处理粗体 **text**
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // 处理斜体 *text*
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理代码 `code`
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
      // 处理换行
      .replace(/\n/g, '<br/>');
  };

  return (
    <div
      className="markdown-content"
      dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
    />
  );
};

// 工具调用结果数据结构
export interface BookTitleResult {
  title: string;
  score: number;
  reason: string;
  extractedKeywords: string[];
  detectedFramework?: string;
}

export interface FrameworkResult {
  name: string;
  pattern: string;
  examples: string[];
  confidence: number;
}

export interface SynopsisResult {
  synopsis: any; // 现在是一个包含完整信息的对象
  length: number;
}

export interface WorldViewResult {
  name: string;
  type: 'fantasy' | 'sci-fi' | 'modern' | 'historical' | 'mixed';
  description: string;
  elements: {
    geography?: string[];
    races?: string[];
    magic_system?: string[];
    technology?: string[];
    politics?: string[];
    culture?: string[];
    history?: string[];
  };
  score: number;
  complexity: 'simple' | 'medium' | 'complex';
  tags: string[];
  summary: string;
}

export interface ToolCallResult {
  toolType: 'generate_book_titles' | 'analyze_framework' | 'generate_synopsis' | 'create_worldview' | 'manage_worldview';
  titles?: BookTitleResult[];
  framework?: FrameworkResult;
  synopsis?: string | {
    content?: string;
    wordCount?: number;
    score?: number;
    reason?: string;
    tagline?: string;
    hook?: string;
    core_conflict?: string;
    selling_points?: string[];
    target_audience?: string;
    sections?: Array<{
      name: string;
      content: string;
      wordCount: number;
      function?: string;
    }>;
  };
  worldview?: WorldViewResult;
  worldBuildings?: Array<{
    id: string;
    name: string;
    category?: string;
    description?: string;
    createdAt: Date;
  }>;
  metadata?: {
    keywords?: string[];
    framework?: string;
    requirements?: string;
    count?: number;
    description?: string;
    confidence?: number;
    exampleCount?: number;
    length?: number;
    worldType?: string;
    complexity?: string;
    elements?: string[];
    action?: string;
    bookId?: string;
  };
}

interface ToolCallResultBubbleProps {
  result: ToolCallResult;
  onAction?: (action: string, data: any) => void;
}

// 星级评分组件
const StarRating: React.FC<{ score: number }> = ({ score }) => {
  const fullStars = Math.floor(score);
  const hasHalfStar = score % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex items-center gap-1">
      {/* 满星 */}
      {Array.from({ length: fullStars }).map((_, index) => (
        <motion.svg
          key={`full-${index}`}
          className="w-4 h-4 text-yellow-400"
          fill="currentColor"
          viewBox="0 0 20 20"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: index * 0.1 }}
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </motion.svg>
      ))}
      
      {/* 半星 */}
      {hasHalfStar && (
        <motion.div
          className="relative w-4 h-4"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: fullStars * 0.1 }}
        >
          <svg className="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          <svg className="absolute inset-0 w-4 h-4 text-yellow-400 overflow-hidden" fill="currentColor" viewBox="0 0 20 20">
            <defs>
              <clipPath id="half-star">
                <rect x="0" y="0" width="10" height="20" />
              </clipPath>
            </defs>
            <path clipPath="url(#half-star)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        </motion.div>
      )}
      
      {/* 空星 */}
      {Array.from({ length: emptyStars }).map((_, index) => (
        <motion.svg
          key={`empty-${index}`}
          className="w-4 h-4 text-gray-300"
          fill="currentColor"
          viewBox="0 0 20 20"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: (fullStars + (hasHalfStar ? 1 : 0) + index) * 0.1 }}
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </motion.svg>
      ))}
      
      <span className="ml-1 text-sm text-gray-600">{score.toFixed(1)}</span>
    </div>
  );
};

// 书名卡片组件
const BookTitleCard: React.FC<{
  title: BookTitleResult;
  index: number;
  onAction?: (action: string, data: any) => void;
}> = ({ title, index, onAction }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(title.title);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
      onAction?.('copy', title);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const handleFavorite = () => {
    onAction?.('favorite', title);
  };

  const handleApply = () => {
    onAction?.('apply', title);
  };

  return (
    <motion.div
      className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200 relative"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -2 }}
    >
      {/* 书名标题 */}
      <div className="font-medium text-gray-800 mb-2 line-clamp-2">
        {title.title}
      </div>

      {/* 评分 */}
      <div className="mb-3">
        <StarRating score={title.score} />
      </div>

      {/* 创作理由 */}
      {title.reason && (
        <div className="text-sm text-gray-600 mb-3 line-clamp-2">
          💭 {title.reason}
        </div>
      )}

      {/* 关键词标签 */}
      {title.extractedKeywords && title.extractedKeywords.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {title.extractedKeywords.slice(0, 3).map((keyword, idx) => (
            <span
              key={idx}
              className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full"
            >
              {keyword}
            </span>
          ))}
          {title.extractedKeywords.length > 3 && (
            <span className="px-2 py-1 bg-gray-50 text-gray-500 text-xs rounded-full">
              +{title.extractedKeywords.length - 3}
            </span>
          )}
        </div>
      )}

      {/* 操作按钮 */}
      <motion.div
        className="flex gap-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0.7 }}
        transition={{ duration: 0.2 }}
      >
        <motion.button
          className="flex-1 px-3 py-1.5 bg-blue-50 text-blue-600 text-sm rounded-md hover:bg-blue-100 transition-colors duration-200 flex items-center justify-center gap-1"
          onClick={handleCopy}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {isCopied ? (
            <>
              <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              已复制
            </>
          ) : (
            <>
              <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              复制
            </>
          )}
        </motion.button>

        <motion.button
          className="px-3 py-1.5 bg-yellow-50 text-yellow-600 text-sm rounded-md hover:bg-yellow-100 transition-colors duration-200 flex items-center justify-center"
          onClick={handleFavorite}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </motion.button>

        <motion.button
          className="px-3 py-1.5 bg-green-50 text-green-600 text-sm rounded-md hover:bg-green-100 transition-colors duration-200 flex items-center justify-center"
          onClick={handleApply}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </motion.button>
      </motion.div>
    </motion.div>
  );
};

// 工具图标组件
const ToolIcon: React.FC<{ toolType: string }> = ({ toolType }) => {
  if (toolType === 'generate_book_titles') {
    return (
      <motion.svg
        className="w-5 h-5 text-blue-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </motion.svg>
    );
  }

  if (toolType === 'generate_synopsis') {
    return (
      <motion.svg
        className="w-5 h-5 text-green-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </motion.svg>
    );
  }

  if (toolType === 'create_worldview') {
    return (
      <motion.svg
        className="w-5 h-5 text-emerald-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </motion.svg>
    );
  }

  if (toolType === 'manage_worldview') {
    return (
      <motion.svg
        className="w-5 h-5 text-emerald-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </motion.svg>
    );
  }

  return (
    <motion.svg
      className="w-5 h-5 text-emerald-500"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </motion.svg>
  );
};

// 主组件
export const ToolCallResultBubble: React.FC<ToolCallResultBubbleProps> = ({ result, onAction }) => {
  const getToolTitle = () => {
    switch (result.toolType) {
      case 'generate_book_titles':
        return `AI为您生成了${result.titles?.length || 0}个书名`;
      case 'analyze_framework':
        return '框架分析结果';
      case 'generate_synopsis':
        return 'AI为您生成了作品简介';
      case 'create_worldview':
        return 'AI为您创建了世界观设定';
      case 'manage_worldview':
        return '世界观管理';
      default:
        return '工具调用结果';
    }
  };

  return (
    <motion.div
      className="max-w-[90%] bg-gradient-to-br from-blue-50 to-white border border-blue-200 rounded-2xl shadow-lg relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 气泡尖角 */}
      <div className="absolute top-5 -left-1.5 w-3 h-3 bg-blue-50 border-l border-b border-blue-200 transform rotate-45" />

      {/* 头部 */}
      <div className="flex items-center gap-3 p-4 border-b border-blue-100">
        <ToolIcon toolType={result.toolType} />
        <div className="flex-1">
          <h4 className="font-medium text-gray-800">{getToolTitle()}</h4>
          {result.metadata && (
            <div className="text-sm text-gray-600 mt-1">
              {result.metadata.keywords && (
                <span>关键词: {result.metadata.keywords.join('、')} </span>
              )}
              {result.metadata.framework && (
                <span>框架: {result.metadata.framework}</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4">
        {result.toolType === 'generate_book_titles' && result.titles && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {result.titles.map((title, index) => (
              <BookTitleCard
                key={index}
                title={title}
                index={index}
                onAction={onAction}
              />
            ))}
          </div>
        )}

        {result.toolType === 'analyze_framework' && result.framework && (
          <div className="space-y-3">
            <div>
              <h5 className="font-medium text-gray-800 mb-2">框架模式</h5>
              <div className="bg-gray-50 rounded-lg p-3 font-mono text-sm">
                {result.framework.pattern}
              </div>
            </div>
            {result.framework.examples && result.framework.examples.length > 0 && (
              <div>
                <h5 className="font-medium text-gray-800 mb-2">参考示例</h5>
                <div className="space-y-2">
                  {result.framework.examples.map((example, index) => (
                    <div key={index} className="bg-blue-50 rounded-lg p-2 text-sm">
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {result.toolType === 'generate_synopsis' && result.synopsis && (
          <div className="space-y-3">
            <div className="bg-gradient-to-br from-green-50 to-white border border-green-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                生成的作品简介
              </h5>

              {/* 显示完整的简介信息 */}
              {typeof result.synopsis === 'string' ? (
                // 兼容旧格式：直接是字符串
                <div className="text-gray-700 leading-relaxed">
                  <MarkdownRenderer content={result.synopsis} />
                </div>
              ) : (
                // 新格式：显示完整的结构化信息
                <div className="space-y-4">
                  {/* 标语 */}
                  {result.synopsis.tagline && (
                    <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r-lg">
                      <div className="text-sm font-medium text-blue-800 mb-1">🏷️ 标语</div>
                      <div className="text-blue-700">
                        <MarkdownRenderer content={result.synopsis.tagline} />
                      </div>
                    </div>
                  )}

                  {/* 开头钩子 */}
                  {result.synopsis.hook && (
                    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r-lg">
                      <div className="text-sm font-medium text-yellow-800 mb-1">🎣 开头钩子</div>
                      <div className="text-yellow-700">
                        <MarkdownRenderer content={result.synopsis.hook} />
                      </div>
                    </div>
                  )}

                  {/* 简介正文 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="text-sm font-medium text-gray-600 mb-2">📝 简介正文</div>
                    <div className="text-gray-700 leading-relaxed">
                      <MarkdownRenderer content={result.synopsis.content || ''} />
                    </div>
                  </div>

                  {/* 核心冲突 */}
                  {result.synopsis.core_conflict && (
                    <div className="bg-red-50 border-l-4 border-red-400 p-3 rounded-r-lg">
                      <div className="text-sm font-medium text-red-800 mb-1">⚔️ 核心冲突</div>
                      <div className="text-red-700">
                        <MarkdownRenderer content={result.synopsis.core_conflict} />
                      </div>
                    </div>
                  )}

                  {/* 主要卖点 */}
                  {result.synopsis.selling_points && result.synopsis.selling_points.length > 0 && (
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <div className="text-sm font-medium text-purple-800 mb-2">✨ 主要卖点</div>
                      <div className="space-y-1">
                        {result.synopsis.selling_points.map((point, index) => (
                          <div key={index} className="flex items-start gap-2 text-purple-700">
                            <span className="text-purple-500 font-medium">{index + 1}.</span>
                            <MarkdownRenderer content={point} />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 目标读者 */}
                  {result.synopsis.target_audience && (
                    <div className="bg-indigo-50 border-l-4 border-indigo-400 p-3 rounded-r-lg">
                      <div className="text-sm font-medium text-indigo-800 mb-1">🎯 目标读者</div>
                      <div className="text-indigo-700">
                        <MarkdownRenderer content={result.synopsis.target_audience} />
                      </div>
                    </div>
                  )}

                  {/* 段落分解 */}
                  {result.synopsis.sections && result.synopsis.sections.length > 0 && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-800 mb-3">📑 段落分解</div>
                      <div className="space-y-3">
                        {result.synopsis.sections.map((section, index) => (
                          <div key={index} className="bg-white border border-gray-100 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <h6 className="font-medium text-gray-800">{index + 1}. {section.name}</h6>
                              <span className="text-xs text-gray-500">{section.wordCount}字</span>
                            </div>
                            <div className="text-gray-700 text-sm mb-2">
                              <MarkdownRenderer content={section.content} />
                            </div>
                            {section.function && (
                              <div className="text-xs text-gray-500 italic">
                                功能：{section.function}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 创作理由 */}
                  {result.synopsis.reason && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="text-sm font-medium text-green-800 mb-2">💭 创作理由</div>
                      <div className="text-green-700 text-sm">
                        <MarkdownRenderer content={result.synopsis.reason} />
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 统计信息和操作按钮 */}
              <div className="mt-4 pt-3 border-t border-green-100">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                  <div className="flex items-center gap-4">
                    <span>
                      字数: {
                        typeof result.synopsis === 'string'
                          ? result.synopsis.length
                          : (result.synopsis?.wordCount || result.synopsis?.content?.length || 0)
                      } 字
                    </span>
                    {typeof result.synopsis !== 'string' && result.synopsis?.score && (
                      <span>AI评分: {result.synopsis.score.toFixed(1)}/10</span>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  <motion.button
                    className="px-3 py-1 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => {
                      // 只复制简介的content内容，不包含其他格式化信息
                      const contentToCopy = typeof result.synopsis === 'string'
                        ? result.synopsis
                        : (result.synopsis?.content || '');
                      navigator.clipboard.writeText(contentToCopy);
                      onAction?.('copy', contentToCopy);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    复制简介
                  </motion.button>
                  <motion.button
                    className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => {
                      const contentToApply = typeof result.synopsis === 'string'
                        ? result.synopsis
                        : (result.synopsis?.content || '');
                      onAction?.('apply', contentToApply);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    应用
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        )}

        {result.toolType === 'create_worldview' && result.worldview && (
          <div className="space-y-4">
            <div className="bg-gradient-to-br from-emerald-50 to-white border border-emerald-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <svg className="w-4 h-4 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {result.worldview.name}
              </h5>

              {/* 世界观基本信息 */}
              <div className="space-y-3">
                {/* 类型和复杂度 */}
                <div className="flex items-center gap-4 text-sm">
                  <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">
                    {result.worldview.type}
                  </span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                    {result.worldview.complexity}
                  </span>
                  <span className="text-yellow-500 flex items-center gap-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    {result.worldview.score}/10
                  </span>
                </div>

                {/* 世界观描述 */}
                <div className="bg-white border border-gray-200 rounded-lg p-3">
                  <div className="text-sm font-medium text-gray-600 mb-2">📝 世界观描述</div>
                  <div className="text-gray-700 leading-relaxed">
                    <MarkdownRenderer content={result.worldview.description} />
                  </div>
                </div>

                {/* 核心元素 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {result.worldview.elements.geography && result.worldview.elements.geography.length > 0 && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-green-800 mb-2 flex items-center gap-1">
                        🗺️ 地理环境
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.geography.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.races && result.worldview.elements.races.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-blue-800 mb-2 flex items-center gap-1">
                        👥 种族设定
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.races.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.magic_system && result.worldview.elements.magic_system.length > 0 && (
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                        ✨ 魔法体系
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.magic_system.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.technology && result.worldview.elements.technology.length > 0 && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-gray-800 mb-2 flex items-center gap-1">
                        ⚙️ 科技水平
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.technology.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.politics && result.worldview.elements.politics.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-red-800 mb-2 flex items-center gap-1">
                        🏛️ 政治制度
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.politics.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.culture && result.worldview.elements.culture.length > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-yellow-800 mb-2 flex items-center gap-1">
                        🎭 文化特色
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.culture.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.worldview.elements.history && result.worldview.elements.history.length > 0 && (
                    <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-3">
                      <div className="text-sm font-medium text-indigo-800 mb-2 flex items-center gap-1">
                        📚 历史背景
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.worldview.elements.history.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded text-xs">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* 特征标签 */}
                {result.worldview.tags && result.worldview.tags.length > 0 && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="text-sm font-medium text-gray-800 mb-2">🏷️ 特征标签</div>
                    <div className="flex flex-wrap gap-2">
                      {result.worldview.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 世界观总结 */}
                {result.worldview.summary && (
                  <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
                    <div className="text-sm font-medium text-emerald-800 mb-2">📋 世界观总结</div>
                    <div className="text-emerald-700 text-sm">
                      <MarkdownRenderer content={result.worldview.summary} />
                    </div>
                  </div>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="mt-4 pt-3 border-t border-emerald-100">
                <div className="flex gap-2">
                  <motion.button
                    className="px-3 py-1 bg-emerald-100 text-emerald-600 rounded-md hover:bg-emerald-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => {
                      const worldviewText = `# ${result.worldview?.name}\n\n${result.worldview?.description}\n\n## 核心元素\n\n${Object.entries(result.worldview?.elements || {}).map(([key, values]) => `**${key}**: ${Array.isArray(values) ? values.join('、') : values}`).join('\n')}\n\n## 总结\n${result.worldview?.summary}`;
                      navigator.clipboard.writeText(worldviewText);
                      onAction?.('copy', worldviewText);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    复制世界观
                  </motion.button>
                  <motion.button
                    className="px-3 py-1 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => {
                      onAction?.('save', result.worldview);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                    </svg>
                    保存到项目
                  </motion.button>
                  <motion.button
                    className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => {
                      onAction?.('apply', result.worldview);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    发送到对话
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        )}

        {result.toolType === 'manage_worldview' && result.worldBuildings && (
          <div className="space-y-4">
            <div className="bg-gradient-to-br from-emerald-50 to-white border border-emerald-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <svg className="w-4 h-4 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                项目世界观管理 ({result.worldBuildings.length}个)
              </h5>

              {/* 世界观列表 */}
              <div className="space-y-3">
                {result.worldBuildings.map((worldBuilding, index) => (
                  <motion.div
                    key={worldBuilding.id}
                    className="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-md transition-all duration-200"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h6 className="font-medium text-gray-800 mb-1">{worldBuilding.name}</h6>
                        {worldBuilding.category && (
                          <span className="inline-block px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs mb-2">
                            {worldBuilding.category}
                          </span>
                        )}
                        {worldBuilding.description && (
                          <p className="text-sm text-gray-600 line-clamp-2">{worldBuilding.description}</p>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          创建时间: {new Date(worldBuilding.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex gap-1 ml-3">
                        <motion.button
                          className="px-2 py-1 bg-emerald-100 text-emerald-600 rounded text-xs hover:bg-emerald-200 transition-colors duration-200"
                          onClick={() => onAction?.('select', worldBuilding)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          选择
                        </motion.button>
                        <motion.button
                          className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs hover:bg-blue-200 transition-colors duration-200"
                          onClick={() => onAction?.('send', worldBuilding)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          发送
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* 管理操作 */}
              <div className="mt-4 pt-3 border-t border-emerald-100">
                <div className="flex gap-2">
                  <motion.button
                    className="px-3 py-1 bg-emerald-100 text-emerald-600 rounded-md hover:bg-emerald-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => onAction?.('send_all', result.worldBuildings)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                    发送全部
                  </motion.button>
                  <motion.button
                    className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors duration-200 flex items-center gap-1"
                    onClick={() => onAction?.('open_panel', result.metadata)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    打开管理面板
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 底部统计 */}
      <div className="px-4 pb-4">
        <div className="text-xs text-gray-500 text-center">
          💡 您可以直接操作上方的结果，或继续与我对话讨论
        </div>
      </div>
    </motion.div>
  );
};

export default ToolCallResultBubble;
