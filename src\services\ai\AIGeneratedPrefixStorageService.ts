/**
 * AI生成的前置消息持久化存储服务
 * 管理AI生成的前置消息的保存、加载和分类
 * 支持世界书导入功能
 */

import { PrefixOption } from './PrefixMessageAIService';
import { PhaseType } from '../../types/ai-persona';
import { WorldBookPrefix, ImportResult } from '../../types/worldbook';
import { BaseDataService } from '../storage/DataService';
import { db } from '../../lib/db/dexie';

export interface SavedAIPrefix {
  id: string;
  content: string;
  category: PrefixOption['category'];
  description: string;
  useCase: string;
  reasoning: string;
  confidence: number;
  tags: string[];
  phase?: PhaseType;
  createdAt: Date;
  usageCount: number;
  lastUsed?: Date;
  isFavorite: boolean;
}

export class AIGeneratedPrefixStorageService extends BaseDataService {
  private static instance: AIGeneratedPrefixStorageService;
  private storageKey = 'ai_generated_prefixes';
  private savedPrefixes: SavedAIPrefix[] = [];

  private constructor() {
    super();
    // 异步加载数据，不阻塞构造函数
    this.loadSavedPrefixes().catch(error => {
      console.error('初始化加载前置消息失败:', error);
    });
  }

  static getInstance(): AIGeneratedPrefixStorageService {
    if (!AIGeneratedPrefixStorageService.instance) {
      AIGeneratedPrefixStorageService.instance = new AIGeneratedPrefixStorageService();
    }
    return AIGeneratedPrefixStorageService.instance;
  }

  /**
   * 保存AI生成的前置消息
   */
  savePrefixOptions(options: PrefixOption[], phase?: PhaseType): SavedAIPrefix[] {
    const newSavedPrefixes: SavedAIPrefix[] = options.map(option => ({
      id: `saved_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: option.content,
      category: option.category,
      description: option.description,
      useCase: option.useCase,
      reasoning: option.reasoning,
      confidence: option.confidence,
      tags: option.tags || [],
      phase,
      createdAt: new Date(),
      usageCount: 0,
      isFavorite: false
    }));

    // 检查重复，避免保存相同内容
    const uniqueNewPrefixes = newSavedPrefixes.filter(newPrefix => 
      !this.savedPrefixes.some(existing => existing.content === newPrefix.content)
    );

    if (uniqueNewPrefixes.length > 0) {
      this.savedPrefixes.push(...uniqueNewPrefixes);
      this.persistToStorage();
      console.log(`✅ 保存了 ${uniqueNewPrefixes.length} 个新的AI生成前置消息`);
    } else {
      console.log('⚠️ 所有前置消息都已存在，未保存新项');
    }

    return uniqueNewPrefixes;
  }

  /**
   * 获取所有保存的前置消息
   */
  getAllSavedPrefixes(): SavedAIPrefix[] {
    return [...this.savedPrefixes].sort((a, b) => {
      // 按收藏、使用次数、创建时间排序
      if (a.isFavorite !== b.isFavorite) {
        return a.isFavorite ? -1 : 1;
      }
      if (a.usageCount !== b.usageCount) {
        return b.usageCount - a.usageCount;
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  /**
   * 按阶段获取前置消息
   */
  getPrefixesByPhase(phase: PhaseType): SavedAIPrefix[] {
    return this.savedPrefixes.filter(prefix => 
      prefix.phase === phase || !prefix.phase // 包含通用的
    ).sort((a, b) => {
      if (a.isFavorite !== b.isFavorite) {
        return a.isFavorite ? -1 : 1;
      }
      return b.usageCount - a.usageCount;
    });
  }

  /**
   * 按类别获取前置消息
   */
  getPrefixesByCategory(category: PrefixOption['category']): SavedAIPrefix[] {
    return this.savedPrefixes.filter(prefix => prefix.category === category);
  }

  /**
   * 记录使用
   */
  async recordUsage(prefixId: string): Promise<void> {
    try {
      const prefix = await db.worldBookPrefixes.get(prefixId);
      if (prefix) {
        await db.worldBookPrefixes.update(prefixId, {
          usageCount: prefix.usageCount + 1,
          lastUsed: new Date(),
          updatedAt: new Date()
        });

        // 更新内存缓存
        const memoryPrefix = this.savedPrefixes.find(p => p.id === prefixId);
        if (memoryPrefix) {
          memoryPrefix.usageCount++;
          memoryPrefix.lastUsed = new Date();
        }

        // 清理相关缓存
        const keysToDelete = Array.from(this.cache.keys()).filter(key => key.includes(prefixId));
        keysToDelete.forEach(key => this.cache.delete(key));
      }
    } catch (error) {
      console.error('记录使用失败:', error);
    }
  }

  /**
   * 切换收藏状态
   */
  async toggleFavorite(prefixId: string): Promise<boolean> {
    try {
      const prefix = await db.worldBookPrefixes.get(prefixId);
      if (prefix) {
        const newFavoriteStatus = !prefix.isFavorite;
        await db.worldBookPrefixes.update(prefixId, {
          isFavorite: newFavoriteStatus,
          updatedAt: new Date()
        });

        // 更新内存缓存
        const memoryPrefix = this.savedPrefixes.find(p => p.id === prefixId);
        if (memoryPrefix) {
          memoryPrefix.isFavorite = newFavoriteStatus;
        }

        // 清理相关缓存
        const keysToDelete = Array.from(this.cache.keys()).filter(key => key.includes(prefixId));
        keysToDelete.forEach(key => this.cache.delete(key));

        return newFavoriteStatus;
      }
      return false;
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      return false;
    }
  }

  /**
   * 更新前置消息
   */
  async updatePrefix(prefixId: string, updates: Partial<WorldBookPrefix>): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date()
      };

      await db.worldBookPrefixes.update(prefixId, updateData);

      // 更新内存缓存
      const memoryPrefix = this.savedPrefixes.find(p => p.id === prefixId);
      if (memoryPrefix) {
        Object.assign(memoryPrefix, updateData);
      }

      // 清理相关缓存
      const keysToDelete = Array.from(this.cache.keys()).filter(key => key.includes(prefixId));
      keysToDelete.forEach(key => this.cache.delete(key));

      console.log(`✅ 更新前置消息成功: ${prefixId}`);
      return true;
    } catch (error) {
      console.error('更新前置消息失败:', error);
      return false;
    }
  }

  /**
   * 删除前置消息
   */
  async deletePrefix(prefixId: string): Promise<boolean> {
    try {
      // 先检查记录是否存在
      const existing = await db.worldBookPrefixes.get(prefixId);
      if (!existing) {
        return false;
      }

      // 从数据库删除
      await db.worldBookPrefixes.delete(prefixId);

      // 从内存缓存中删除
      const index = this.savedPrefixes.findIndex(p => p.id === prefixId);
      if (index !== -1) {
        this.savedPrefixes.splice(index, 1);
      }

      // 清理相关缓存
      const keysToDelete = Array.from(this.cache.keys()).filter(key => key.includes(prefixId));
      keysToDelete.forEach(key => this.cache.delete(key));

      console.log(`✅ 删除前置消息成功: ${prefixId}`);
      return true;
    } catch (error) {
      console.error('删除前置消息失败:', error);
      return false;
    }
  }

  /**
   * 批量删除
   */
  deletePrefixes(prefixIds: string[]): number {
    let deletedCount = 0;
    prefixIds.forEach(id => {
      const index = this.savedPrefixes.findIndex(p => p.id === id);
      if (index !== -1) {
        this.savedPrefixes.splice(index, 1);
        deletedCount++;
      }
    });
    
    if (deletedCount > 0) {
      this.persistToStorage();
    }
    
    return deletedCount;
  }

  /**
   * 搜索前置消息
   */
  searchPrefixes(query: string): SavedAIPrefix[] {
    const lowerQuery = query.toLowerCase();
    return this.savedPrefixes.filter(prefix => 
      prefix.content.toLowerCase().includes(lowerQuery) ||
      prefix.description.toLowerCase().includes(lowerQuery) ||
      prefix.useCase.toLowerCase().includes(lowerQuery) ||
      prefix.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const total = this.savedPrefixes.length;
    const favorites = this.savedPrefixes.filter(p => p.isFavorite).length;
    const byCategory = this.savedPrefixes.reduce((acc, prefix) => {
      acc[prefix.category] = (acc[prefix.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostUsed = [...this.savedPrefixes]
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 5);

    return {
      total,
      favorites,
      byCategory,
      mostUsed
    };
  }

  /**
   * 清理旧数据
   */
  cleanupOldPrefixes(daysOld: number = 30): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    const beforeCount = this.savedPrefixes.length;
    this.savedPrefixes = this.savedPrefixes.filter(prefix => 
      prefix.isFavorite || // 保留收藏的
      prefix.usageCount > 0 || // 保留使用过的
      new Date(prefix.createdAt) > cutoffDate // 保留最近的
    );
    
    const deletedCount = beforeCount - this.savedPrefixes.length;
    if (deletedCount > 0) {
      this.persistToStorage();
    }
    
    return deletedCount;
  }

  /**
   * 从数据库加载世界书前置消息
   */
  private async loadSavedPrefixes(): Promise<void> {
    try {
      // 从数据库加载世界书前置消息
      const worldBookPrefixes = await db.worldBookPrefixes.toArray();

      // 同时从localStorage加载非世界书的前置消息（向后兼容）
      const stored = localStorage.getItem(this.storageKey);
      let legacyPrefixes: SavedAIPrefix[] = [];

      if (stored) {
        const parsed = JSON.parse(stored);
        legacyPrefixes = parsed
          .filter((item: any) => !(item as WorldBookPrefix).worldBookSource) // 只保留非世界书条目
          .map((item: any) => ({
            ...item,
            createdAt: new Date(item.createdAt),
            lastUsed: item.lastUsed ? new Date(item.lastUsed) : undefined
          }));
      }

      // 合并数据库和localStorage的数据
      this.savedPrefixes = [...worldBookPrefixes, ...legacyPrefixes];

      console.log(`📚 从数据库加载了 ${worldBookPrefixes.length} 个世界书前置消息`);
      console.log(`📚 从localStorage加载了 ${legacyPrefixes.length} 个传统前置消息`);
      console.log(`📚 总计加载了 ${this.savedPrefixes.length} 个前置消息`);
    } catch (error) {
      console.error('加载前置消息失败:', error);
      this.savedPrefixes = [];
    }
  }

  /**
   * 持久化到数据库（已废弃，现在直接使用数据库操作）
   * @deprecated 现在直接使用数据库操作，无需单独的持久化方法
   */
  private persistToStorage(): void {
    // 保留方法以维持向后兼容性，但不执行任何操作
    console.log('📝 persistToStorage已废弃，现在使用数据库直接操作');
  }

  /**
   * 导出数据
   */
  exportData(): string {
    return JSON.stringify({
      version: '1.0',
      exportDate: new Date().toISOString(),
      prefixes: this.savedPrefixes
    }, null, 2);
  }

  /**
   * 导入数据
   */
  importData(jsonData: string): { success: boolean; imported: number; message: string } {
    try {
      const data = JSON.parse(jsonData);
      if (!data.prefixes || !Array.isArray(data.prefixes)) {
        return { success: false, imported: 0, message: '无效的数据格式' };
      }

      const importedPrefixes = data.prefixes.map((item: any) => ({
        ...item,
        id: `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(item.createdAt),
        lastUsed: item.lastUsed ? new Date(item.lastUsed) : undefined
      }));

      // 检查重复
      const uniqueImported = importedPrefixes.filter((imported: SavedAIPrefix) =>
        !this.savedPrefixes.some(existing => existing.content === imported.content)
      );

      this.savedPrefixes.push(...uniqueImported);
      this.persistToStorage();

      return {
        success: true,
        imported: uniqueImported.length,
        message: `成功导入 ${uniqueImported.length} 个前置消息`
      };
    } catch (error) {
      return {
        success: false,
        imported: 0,
        message: `导入失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // ==================== 世界书相关方法 ====================

  /**
   * 保存世界书前置消息
   * @param prefixes 世界书前置消息数组
   * @returns 导入结果
   */
  async saveWorldBookPrefixes(prefixes: WorldBookPrefix[]): Promise<ImportResult> {
    const errors: string[] = [];

    try {
      // 检查重复（基于worldBookSource和originalUid）
      const existingPrefixes = await db.worldBookPrefixes.toArray();

      const uniquePrefixes = prefixes.filter(prefix => {
        const isDuplicate = existingPrefixes.some(existing => {
          return existing.worldBookSource === prefix.worldBookSource &&
                 existing.originalUid === prefix.originalUid;
        });
        return !isDuplicate;
      });

      // 批量插入到数据库
      if (uniquePrefixes.length > 0) {
        await db.worldBookPrefixes.bulkAdd(uniquePrefixes);

        // 更新内存缓存
        this.savedPrefixes.push(...uniquePrefixes);

        // 标记为重要数据
        uniquePrefixes.forEach(prefix => {
          const cacheKey = this.getCacheKey('worldbook', prefix.worldBookSource || '', prefix.id);
          this.cache.set(cacheKey, prefix);
          this.markAsImportant(cacheKey);
        });
      }

      const fileName = prefixes.length > 0 ? prefixes[0].worldBookSource : '';
      console.log(`✅ 从世界书 "${fileName}" 保存了 ${uniquePrefixes.length} 个前置消息到数据库`);

      return {
        total: prefixes.length,
        imported: uniquePrefixes.length,
        skipped: prefixes.length - uniquePrefixes.length,
        errors,
        fileName,
        importedAt: new Date()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      errors.push(errorMessage);
      console.error('❌ 保存世界书前置消息到数据库失败:', error);

      return {
        total: prefixes.length,
        imported: 0,
        skipped: prefixes.length,
        errors,
        fileName: prefixes.length > 0 ? prefixes[0].worldBookSource : '',
        importedAt: new Date()
      };
    }
  }

  /**
   * 获取所有世界书来源列表
   * @returns 世界书来源数组
   */
  async getWorldBookSources(): Promise<string[]> {
    const cacheKey = this.getCacheKey('worldbook-sources');

    if (this.cache.has(cacheKey)) {
      this.markAsImportant(cacheKey);
      return this.cache.get(cacheKey);
    }

    try {
      const sources = await db.worldBookPrefixes
        .orderBy('worldBookSource')
        .uniqueKeys();

      const sourceList = sources
        .filter(Boolean)
        .map(source => String(source))
        .sort();

      // 更新缓存
      this.cache.set(cacheKey, sourceList);
      this.markAsImportant(cacheKey);
      this.cleanupCache();

      return sourceList;
    } catch (error) {
      console.error('获取世界书来源列表失败:', error);
      return [];
    }
  }

  /**
   * 按世界书来源过滤前置消息
   * @param source 世界书来源名称
   * @returns 该来源的前置消息数组
   */
  async getPrefixesByWorldBook(source: string): Promise<SavedAIPrefix[]> {
    const cacheKey = this.getCacheKey('worldbook-source', source);

    if (this.cache.has(cacheKey)) {
      this.markAsImportant(cacheKey);
      return this.cache.get(cacheKey);
    }

    try {
      const prefixes = await db.worldBookPrefixes
        .where('worldBookSource')
        .equals(source)
        .toArray();

      // 按原始顺序排序
      const sortedPrefixes = prefixes.sort((a, b) => {
        if (a.originalOrder !== undefined && b.originalOrder !== undefined) {
          return a.originalOrder - b.originalOrder;
        }
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

      // 更新缓存
      this.cache.set(cacheKey, sortedPrefixes);
      this.markAsImportant(cacheKey);
      this.cleanupCache();

      return sortedPrefixes;
    } catch (error) {
      console.error('从数据库获取世界书前置消息失败:', error);
      return [];
    }
  }

  /**
   * 获取世界书统计信息
   * @returns 世界书统计数据
   */
  async getWorldBookStatistics() {
    try {
      const worldBookPrefixes = await db.worldBookPrefixes.toArray();
      const totalWorldBookEntries = worldBookPrefixes.length;
      const sources = await this.getWorldBookSources();

      const bySource: Record<string, any> = {};

      // 使用Promise.all并行处理所有来源
      await Promise.all(sources.map(async (source) => {
        const sourcePrefixes = await this.getPrefixesByWorldBook(source) as WorldBookPrefix[];
        bySource[source] = {
          entryCount: sourcePrefixes.length,
          constantCount: sourcePrefixes.filter(p => p.isConstant).length,
          selectiveCount: sourcePrefixes.filter(p => p.isSelective).length,
          mostUsed: sourcePrefixes
            .sort((a, b) => b.usageCount - a.usageCount)
            .slice(0, 3)
        };
      }));

      const mostUsedWorldBook = worldBookPrefixes
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 5);

      return {
        totalWorldBookEntries,
        totalSources: sources.length,
        bySource,
        mostUsedWorldBook,
        constantEntries: worldBookPrefixes.filter(p => p.isConstant).length,
        selectiveEntries: worldBookPrefixes.filter(p => p.isSelective).length
      };
    } catch (error) {
      console.error('获取世界书统计信息失败:', error);
      return {
        totalWorldBookEntries: 0,
        totalSources: 0,
        bySource: {},
        mostUsedWorldBook: [],
        constantEntries: 0,
        selectiveEntries: 0
      };
    }
  }

  /**
   * 删除整个世界书来源的所有条目
   * @param source 世界书来源名称
   * @returns 删除的条目数量
   */
  async deleteWorldBookSource(source: string): Promise<number> {
    try {
      // 先获取要删除的条目数量
      const toDelete = await db.worldBookPrefixes
        .where('worldBookSource')
        .equals(source)
        .toArray();

      const deletedCount = toDelete.length;

      if (deletedCount > 0) {
        // 从数据库删除
        await db.worldBookPrefixes
          .where('worldBookSource')
          .equals(source)
          .delete();

        // 从内存缓存中删除
        this.savedPrefixes = this.savedPrefixes.filter(prefix => {
          const worldBookPrefix = prefix as WorldBookPrefix;
          return worldBookPrefix.worldBookSource !== source;
        });

        // 清理相关缓存
        const keysToDelete = Array.from(this.cache.keys()).filter(key =>
          key.includes(source) || key.includes('worldbook-sources')
        );
        keysToDelete.forEach(key => this.cache.delete(key));

        console.log(`✅ 从数据库删除了世界书 "${source}" 的 ${deletedCount} 个条目`);
      }

      return deletedCount;
    } catch (error) {
      console.error('删除世界书来源失败:', error);
      return 0;
    }
  }

  /**
   * 检查是否为世界书前置消息
   * @param prefix 前置消息
   * @returns 是否为世界书前置消息
   */
  isWorldBookPrefix(prefix: SavedAIPrefix): prefix is WorldBookPrefix {
    const worldBookPrefix = prefix as WorldBookPrefix;
    return worldBookPrefix.worldBookSource !== undefined;
  }

  /**
   * 搜索世界书前置消息
   * @param query 搜索关键词
   * @param source 可选的世界书来源过滤
   * @returns 搜索结果
   */
  searchWorldBookPrefixes(query: string, source?: string): SavedAIPrefix[] {
    const lowerQuery = query.toLowerCase();

    return this.savedPrefixes.filter(prefix => {
      const worldBookPrefix = prefix as WorldBookPrefix;

      // 必须是世界书条目
      if (!worldBookPrefix.worldBookSource) {
        return false;
      }

      // 如果指定了来源，必须匹配
      if (source && worldBookPrefix.worldBookSource !== source) {
        return false;
      }

      // 搜索内容
      return prefix.content.toLowerCase().includes(lowerQuery) ||
             prefix.description.toLowerCase().includes(lowerQuery) ||
             prefix.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
             worldBookPrefix.originalKeys.some(key => key.toLowerCase().includes(lowerQuery)) ||
             worldBookPrefix.originalKeysSecondary.some(key => key.toLowerCase().includes(lowerQuery));
    });
  }

  /**
   * 更新世界书前置消息的使用统计
   * @param prefixId 前置消息ID
   * @param source 世界书来源（用于日志）
   */
  recordWorldBookUsage(prefixId: string, source?: string): void {
    this.recordUsage(prefixId);
    if (source) {
      console.log(`📖 使用了世界书 "${source}" 的条目: ${prefixId}`);
    }
  }

  /**
   * 保存单个前置消息
   * @param prefix 前置消息
   * @returns 保存的前置消息
   */
  savePrefix(prefix: SavedAIPrefix): SavedAIPrefix {
    // 如果没有ID，生成一个新的ID
    if (!prefix.id) {
      prefix.id = `saved_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    // 设置创建时间
    const now = new Date();
    if (!prefix.createdAt) {
      prefix.createdAt = now;
    }

    // 检查是否已存在相同内容的前置消息
    const existingIndex = this.savedPrefixes.findIndex(p => p.content === prefix.content);
    if (existingIndex === -1) {
      this.savedPrefixes.push(prefix);
      this.persistToStorage();
      console.log(`✅ 保存新前置消息: ${prefix.description}`);
    } else {
      console.log(`⚠️ 前置消息已存在，未保存重复项: ${prefix.description}`);
    }

    return prefix;
  }


}
