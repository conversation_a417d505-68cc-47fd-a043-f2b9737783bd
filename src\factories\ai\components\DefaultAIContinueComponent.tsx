"use client";

import React, { useState } from 'react';
import { IAIContinueComponent } from '../interfaces';
import { AIResponse, AIRequestOptions } from '../interfaces/IAIComponent';
import { ConversationMessage } from '@/factories/ai/services/AIWritingService';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

/**
 * 默认AI续写组件实现
 */
export class DefaultAIContinueComponent extends UnifiedAIService implements IAIContinueComponent {
  private continueRequirements: string = '';
  private continueStyle: string = '';
  private futurePlot: string = '';
  private context: string = '';
  private bookId: string = '';
  private conversationHistory: ConversationMessage[] = [];
  private onContinuedCallback?: (text: string) => void;
  private currentRequest: AbortController | null = null;
  private isProcessing: boolean = false;

  constructor() {
    super(AIServiceType.CONTINUE);
  }

  /**
   * 设置续写要求
   * @param requirements 续写要求
   */
  setContinueRequirements(requirements: string): void {
    this.continueRequirements = requirements;
  }

  /**
   * 设置续写风格
   * @param style 续写风格
   */
  setContinueStyle(style: string): void {
    this.continueStyle = style;
  }

  /**
   * 设置后续剧情
   * @param plot 后续剧情
   */
  setFuturePlot(plot: string): void {
    this.futurePlot = plot;
  }

  /**
   * 设置上下文
   * @param context 上下文内容
   */
  setContext(context: string): void {
    this.context = context;
  }

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this.bookId = bookId;
  }

  /**
   * 设置对话历史
   * @param history 对话历史
   */
  setConversationHistory(history: ConversationMessage[]): void {
    this.conversationHistory = history;
  }

  /**
   * 设置续写完成回调
   * @param callback 回调函数
   */
  setOnContinuedCallback(callback: (text: string) => void): void {
    this.onContinuedCallback = callback;
  }

  /**
   * 续写内容
   * @returns 续写的内容
   */
  // 关联元素数据 - 确保这些属性被正确初始化
  private characters: Array<{id?: string, name: string, description?: string}> = [];
  private terminologies: Array<{id?: string, name: string, description?: string}> = [];
  private worldBuildings: Array<{id?: string, name: string, description?: string}> = [];
  private chapters: Array<{id?: string, name: string, description?: string, order?: number, content?: string}> = [];

  // 选中的关联元素ID
  private selectedCharacterIds: string[] = [];
  private selectedTerminologyIds: string[] = [];
  private selectedWorldBuildingIds: string[] = [];
  private selectedChapterIds: string[] = [];
  private currentChapterId: string = '';

  // 大纲关联数据
  private outlines: any[] = [];
  private selectedOutlineIds: string[] = [];
  private selectedOutlineNodeIds: string[] = [];
  private outlineContextMode: 'selected' | 'hierarchy' | 'full' = 'hierarchy';

  /**
   * 设置人物数据
   * @param characters 人物数据
   */
  setCharacters(characters: Array<{id?: string, name: string, description?: string}>): void {
    this.characters = characters;
  }

  /**
   * 设置术语数据
   * @param terminologies 术语数据
   */
  setTerminologies(terminologies: Array<{id?: string, name: string, description?: string}>): void {
    this.terminologies = terminologies;
  }

  /**
   * 设置世界观数据
   * @param worldBuildings 世界观数据
   */
  setWorldBuildings(worldBuildings: Array<{id?: string, name: string, description?: string}>): void {
    this.worldBuildings = worldBuildings;
  }

  /**
   * 设置章节数据
   * @param chapters 章节数据
   */
  setChapters(chapters: Array<{id?: string, name: string, description?: string, order?: number, content?: string}>): void {
    this.chapters = chapters;
  }

  /**
   * 设置选中的人物ID
   * @param ids 选中的人物ID
   */
  setSelectedCharacterIds(ids: string[]): void {
    this.selectedCharacterIds = ids;
  }

  /**
   * 设置选中的术语ID
   * @param ids 选中的术语ID
   */
  setSelectedTerminologyIds(ids: string[]): void {
    this.selectedTerminologyIds = ids;
  }

  /**
   * 设置选中的世界观ID
   * @param ids 选中的世界观ID
   */
  setSelectedWorldBuildingIds(ids: string[]): void {
    this.selectedWorldBuildingIds = ids;
  }

  /**
   * 设置选中的章节ID
   * @param ids 选中的章节ID
   */
  setSelectedChapterIds(ids: string[]): void {
    this.selectedChapterIds = ids;
  }

  /**
   * 设置当前章节ID
   * @param id 当前章节ID
   */
  setCurrentChapterId(id: string): void {
    this.currentChapterId = id;
    console.log('设置当前章节ID:', id);
    // 检查章节是否存在
    const chapter = this.chapters.find(chapter => chapter.id === id);
    console.log('当前章节信息:', chapter);
  }

  // 当前章节信息
  private currentChapterName: string = '';
  private currentChapterContent: string = '';
  private currentChapterOrder: number = 0;

  /**
   * 设置当前章节信息
   * @param chapter 当前章节信息
   */
  setCurrentChapter(chapter: {id: string, name: string, description?: string, content?: string, order?: number}): void {
    console.log('设置当前章节信息:', chapter);

    // 设置当前章节ID
    this.currentChapterId = chapter.id;

    // 设置当前章节名称
    this.currentChapterName = chapter.name || '';
    console.log('设置当前章节名称:', this.currentChapterName);

    // 设置当前章节内容（如果有）
    if (chapter.content) {
      this.currentChapterContent = chapter.content;
    }

    // 设置当前章节顺序（如果有）
    if (chapter.order !== undefined) {
      this.currentChapterOrder = chapter.order;
    }

    // 如果章节名称为空，尝试从chapters数组中获取
    if (!this.currentChapterName && this.chapters.length > 0) {
      const foundChapter = this.chapters.find(ch => ch.id === chapter.id);
      if (foundChapter && foundChapter.name) {
        this.currentChapterName = foundChapter.name;
        console.log('从chapters数组中获取到章节名称:', this.currentChapterName);
      }
    }
  }

  /**
   * 设置当前章节名称
   * @param name 章节名称
   */
  setCurrentChapterName(name: string): void {
    this.currentChapterName = name || '';
    console.log('setCurrentChapterName 被调用，设置当前章节名称:', name);

    // 如果章节名称为空，尝试从chapters数组中获取
    if (!this.currentChapterName && this.currentChapterId && this.chapters.length > 0) {
      const foundChapter = this.chapters.find(ch => ch.id === this.currentChapterId);
      if (foundChapter && foundChapter.name) {
        this.currentChapterName = foundChapter.name;
        console.log('从chapters数组中获取到章节名称:', this.currentChapterName);
      }
    }
  }

  /**
   * 设置当前章节内容
   * @param content 章节内容
   */
  setCurrentChapterContent(content: string): void {
    this.currentChapterContent = content;
    console.log('设置当前章节内容，长度:', content.length);
  }

  /**
   * 设置大纲数据
   * @param outlines 大纲数据
   */
  setOutlines(outlines: any[]): void {
    this.outlines = outlines;
  }

  /**
   * 设置选中的大纲ID
   * @param ids 选中的大纲ID
   */
  setSelectedOutlineIds(ids: string[]): void {
    this.selectedOutlineIds = ids;
  }

  /**
   * 设置选中的大纲节点ID
   * @param ids 选中的大纲节点ID
   */
  setSelectedOutlineNodeIds(ids: string[]): void {
    this.selectedOutlineNodeIds = ids;
  }

  /**
   * 设置大纲上下文模式
   * @param mode 上下文模式
   */
  setOutlineContextMode(mode: 'selected' | 'hierarchy' | 'full'): void {
    this.outlineContextMode = mode;
  }



  /**
   * 构建消息数组，确保关联元素作为单独的消息
   * @returns 消息数组
   */
  private async buildMessages(): Promise<Array<{ role: string; content: string }>> {
    // 动态导入AIServiceProvider来构建消息
    const { aiServiceProvider } = await import('@/services/ai/AIServiceProvider');
    return await aiServiceProvider.buildContinueMessages({
      continueRequirements: this.continueRequirements,
      continueStyle: this.continueStyle,
      futurePlot: this.futurePlot,
      context: this.context,
      currentChapterName: this.currentChapterName,
      currentChapterId: this.currentChapterId,
      chapters: this.chapters,
      characters: this.characters,
      terminologies: this.terminologies,
      worldBuildings: this.worldBuildings,
      selectedCharacterIds: this.selectedCharacterIds,
      selectedTerminologyIds: this.selectedTerminologyIds,
      selectedWorldBuildingIds: this.selectedWorldBuildingIds,
      selectedChapterIds: this.selectedChapterIds,
      // 新增：大纲关联参数
      outlines: this.outlines,
      selectedOutlineIds: this.selectedOutlineIds,
      selectedOutlineNodeIds: this.selectedOutlineNodeIds,
      outlineContextMode: this.outlineContextMode
    });
  }

  /**
   * 取消当前请求
   */
  public cancelContinue(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.isProcessing = false;
    }
    // 取消功能由UnifiedAIService基类处理
    console.log('DefaultAIContinueComponent: 请求取消');
  }

  /**
   * 续写内容
   * @returns 续写的内容
   */
  async continue(): Promise<string> {
    try {
      if (this.isProcessing) {
        throw new Error('已有续写请求正在处理中');
      }

      this.isProcessing = true;
    

      // 直接使用buildMessages方法构建消息数组
      const messages = await this.buildMessages();

      // 更新对话历史（可选）
      this.conversationHistory = messages.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content
      }));

      // 使用统一的AI调用方法
      const response = await this.callAI(messages);

      // 保存响应到对话历史
      this.conversationHistory.push({
        role: 'assistant',
        content: response.text
      });

      // 如果有回调函数，调用回调
      if (this.onContinuedCallback) {
        this.onContinuedCallback(response.text);
      }

      this.isProcessing = false;
      return response.text;
    } catch (error: any) {
      this.isProcessing = false;
      console.error('续写内容失败:', error);

      // 如果有回调函数，调用回调并传递错误信息
      if (this.onContinuedCallback) {
        this.onContinuedCallback('');
      }

      throw new Error(error.message || '续写内容失败');
    }
  }

  /**
   * 流式续写方法
   * @param onChunk 接收数据块的回调函数
   * @returns 完整的续写内容
   */
  async continueWithStreaming(onChunk: (chunk: string) => void): Promise<string> {
    try {
      if (this.isProcessing) {
        throw new Error('已有续写请求正在处理中');
      }

      this.isProcessing = true;

      // 直接使用buildMessages方法构建消息数组
      const messages = await this.buildMessages();

      // 更新对话历史（可选）
      this.conversationHistory = messages.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content
      }));

      // 创建AbortController用于取消请求
      this.currentRequest = new AbortController();

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(messages, onChunk);

      // 保存完整响应到对话历史
      this.conversationHistory.push({
        role: 'assistant',
        content: response.text
      });

      // 如果有回调函数，调用回调
      if (this.onContinuedCallback) {
        this.onContinuedCallback(response.text);
      }

      this.isProcessing = false;
      this.currentRequest = null;
      return response.text;
    } catch (error: any) {
      this.isProcessing = false;
      this.currentRequest = null;
      console.error('流式续写内容失败:', error);

      // 如果有回调函数，调用回调并传递错误信息
      if (this.onContinuedCallback) {
        this.onContinuedCallback('');
      }

      throw new Error(error.message || '流式续写内容失败');
    }
  }

  /**
   * 实现IAIComponent接口的sendRequest方法
   * @param prompt 提示词
   * @param options 请求选项
   * @returns AI响应
   */
  async sendRequest(_prompt: string, _options?: AIRequestOptions): Promise<AIResponse> {
    try {
      // 忽略参数，使用组件内部状态
      const content = await this.continue();
      return {
        text: content,
        success: true
      };
    } catch (error: any) {
      return {
        text: '',
        success: false,
        error: error.message || '续写内容失败'
      };
    }
  }

  /**
   * 渲染组件UI
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const AIContinueComponent = () => {
      const [continueRequirements, setContinueRequirements] = useState(this.continueRequirements);
      const [continueStyle, setContinueStyle] = useState(this.continueStyle);
      const [futurePlot, setFuturePlot] = useState(this.futurePlot);
      const [context, setContext] = useState(this.context);
      const [continuedContent, setContinuedContent] = useState('');
      const [isContinuing, setIsContinuing] = useState(false);
      const [useStreaming, setUseStreaming] = useState(true);

      // 处理流式输出的回调函数
      const handleStreamChunk = (chunk: string) => {
        setContinuedContent(prev => prev + chunk);
      };

      // 处理续写
      const handleContinue = async () => {
        this.setContinueRequirements(continueRequirements);
        this.setContinueStyle(continueStyle);
        this.setFuturePlot(futurePlot);
        this.setContext(context);
        this.setBookId('default'); // 使用默认书籍ID

        setIsContinuing(true);
        setContinuedContent(''); // 清空之前的内容

        try {
          if (useStreaming) {
            // 使用流式输出
            await this.continueWithStreaming(handleStreamChunk);
          } else {
            // 使用普通输出
            const content = await this.continue();
            setContinuedContent(content);
          }
        } catch (error: any) {
          console.error('续写内容失败', error);
          setContinuedContent(`续写失败: ${error.message || '未知错误'}`);
        } finally {
          setIsContinuing(false);
        }
      };

      // 取消续写
      const handleCancel = () => {
        this.cancelContinue();
        setIsContinuing(false);
      };

      // 插入到编辑器
      const handleInsert = () => {
        if (this.onContinuedCallback) {
          this.onContinuedCallback(continuedContent);
        } else {
          // 这里应该实现插入到编辑器的功能
          alert('内容已插入到编辑器（模拟）');
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">AI续写</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  上下文
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={6}
                  value={context}
                  onChange={(e) => setContext(e.target.value)}
                  placeholder="请输入上下文内容..."
                  readOnly={!!this.context}
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  续写要求
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={2}
                  value={continueRequirements}
                  onChange={(e) => setContinueRequirements(e.target.value)}
                  placeholder="请输入续写要求..."
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  续写风格
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={2}
                  value={continueStyle}
                  onChange={(e) => setContinueStyle(e.target.value)}
                  placeholder="请输入续写风格..."
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  后续剧情
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={2}
                  value={futurePlot}
                  onChange={(e) => setFuturePlot(e.target.value)}
                  placeholder="请输入后续剧情..."
                />
              </div>

              <div className="mb-4 flex items-center">
                <input
                  type="checkbox"
                  id="useStreaming"
                  checked={useStreaming}
                  onChange={(e) => setUseStreaming(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="useStreaming" className="text-sm text-gray-700">
                  使用流式输出（实时显示生成内容）
                </label>
              </div>

              <div className="flex space-x-2">
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
                  onClick={handleContinue}
                  disabled={isContinuing || !context}
                >
                  {isContinuing ? '续写中...' : '续写内容'}
                </button>

                {isContinuing && (
                  <button
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                    onClick={handleCancel}
                  >
                    取消
                  </button>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                续写结果
              </label>
              <div className="p-3 bg-gray-50 rounded border border-gray-200 h-64 overflow-auto whitespace-pre-wrap">
                {continuedContent || '续写结果将显示在这里...'}
              </div>

              {continuedContent && (
                <button
                  className="mt-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                  onClick={handleInsert}
                >
                  插入到编辑器
                </button>
              )}
            </div>
          </div>
        </div>
      );
    };

    return <AIContinueComponent />;
  }
}
