{"name": "ai-novel-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google/genai": "^0.12.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@types/d3": "^7.4.3", "@types/react-window": "^1.8.8", "d3": "^7.9.0", "dexie": "^3.2.4", "framer-motion": "^12.12.1", "isomorphic-dompurify": "^2.24.0", "next": "14.2.0", "next-themes": "^0.4.6", "openai": "^4.96.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-force-graph": "^1.47.6", "react-window": "^1.8.11", "reactflow": "^11.11.4", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.2.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}