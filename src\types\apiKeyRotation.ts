/**
 * API密钥轮播系统类型定义
 */

/**
 * API密钥状态
 */
export type APIKeyStatus = 'active' | 'waiting' | 'failed' | 'disabled';

/**
 * 轮播策略
 */
export type RotationStrategy = 'round-robin' | 'weighted' | 'random' | 'least-used';

/**
 * API密钥配置
 */
export interface APIKeyConfig {
  /** 密钥唯一标识 */
  id: string;
  /** API密钥 */
  key: string;
  /** 关联的URL */
  url: string;
  /** 密钥状态 */
  status: APIKeyStatus;
  /** 最后使用时间 */
  lastUsed: Date;
  /** 失败次数 */
  failureCount: number;
  /** 等待到期时间 */
  waitUntil?: Date;
  /** 自定义等待时间（毫秒） */
  customWaitTime?: number;
  /** 权重（用于加权轮播） */
  weight?: number;
  /** 使用次数统计 */
  usageCount: number;
  /** 成功率 */
  successRate: number;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 备注信息 */
  notes?: string;
}

/**
 * URL密钥池配置
 */
export interface URLKeyPool {
  /** URL地址 */
  url: string;
  /** 密钥列表 */
  keys: APIKeyConfig[];
  /** 当前轮播索引 */
  currentIndex: number;
  /** 轮播策略 */
  rotationStrategy: RotationStrategy;
  /** 是否启用轮播 */
  enabled: boolean;
  /** 最大失败次数 */
  maxFailures: number;
  /** 默认等待时间（毫秒） */
  defaultWaitTime: number;
  /** 健康检查间隔（毫秒） */
  healthCheckInterval: number;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 轮播系统配置
 */
export interface APIKeyRotationConfig {
  /** 配置版本 */
  version: string;
  /** URL密钥池映射 */
  urlPools: Record<string, URLKeyPool>;
  /** 全局设置 */
  globalSettings: {
    /** 是否启用轮播系统 */
    enabled: boolean;
    /** 默认轮播策略 */
    defaultStrategy: RotationStrategy;
    /** 默认等待时间（毫秒） */
    defaultWaitTime: number;
    /** 最大失败次数 */
    maxFailures: number;
    /** 健康检查间隔（毫秒） */
    healthCheckInterval: number;
    /** 是否启用自动恢复 */
    autoRecovery: boolean;
    /** 日志级别 */
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 请求结果
 */
export interface RequestResult {
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 响应数据 */
  data?: any;
  /** 使用的密钥ID */
  keyId: string;
  /** 请求时间 */
  timestamp: Date;
  /** 响应时间（毫秒） */
  responseTime: number;
}

/**
 * 密钥使用统计
 */
export interface KeyUsageStats {
  /** 密钥ID */
  keyId: string;
  /** 总请求次数 */
  totalRequests: number;
  /** 成功次数 */
  successCount: number;
  /** 失败次数 */
  failureCount: number;
  /** 成功率 */
  successRate: number;
  /** 平均响应时间 */
  avgResponseTime: number;
  /** 最后使用时间 */
  lastUsed: Date;
  /** 最后成功时间 */
  lastSuccess?: Date;
  /** 最后失败时间 */
  lastFailure?: Date;
}

/**
 * 轮播事件类型
 */
export type RotationEventType = 
  | 'key-selected'
  | 'key-failed'
  | 'key-recovered'
  | 'key-disabled'
  | 'pool-empty'
  | 'strategy-changed';

/**
 * 轮播事件
 */
export interface RotationEvent {
  /** 事件类型 */
  type: RotationEventType;
  /** 事件时间 */
  timestamp: Date;
  /** 相关URL */
  url: string;
  /** 相关密钥ID */
  keyId?: string;
  /** 事件数据 */
  data?: any;
  /** 事件消息 */
  message: string;
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  /** 密钥ID */
  keyId: string;
  /** 是否健康 */
  healthy: boolean;
  /** 检查时间 */
  timestamp: Date;
  /** 响应时间 */
  responseTime?: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 轮播管理器选项
 */
export interface RotationManagerOptions {
  /** 是否启用自动健康检查 */
  enableHealthCheck?: boolean;
  /** 健康检查间隔 */
  healthCheckInterval?: number;
  /** 是否启用事件日志 */
  enableEventLogging?: boolean;
  /** 最大事件日志数量 */
  maxEventLogs?: number;
  /** 是否启用统计 */
  enableStats?: boolean;
}
