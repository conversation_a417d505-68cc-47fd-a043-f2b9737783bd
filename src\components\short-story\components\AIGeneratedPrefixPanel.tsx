"use client";

import React, { useState, useEffect } from 'react';
import { PhaseType, AIPersonaConfig } from '../../../types/ai-persona';
import { ChatMessage } from './PhaseAIPanel';
import { PrefixMessageAIService, PrefixOption, AnalysisContext } from '../../../services/ai/PrefixMessageAIService';
import { AIGeneratedPrefixStorageService } from '../../../services/ai/AIGeneratedPrefixStorageService';

interface AIGeneratedPrefixPanelProps {
  phase: PhaseType;
  persona: AIPersonaConfig | null;
  content: string;
  history: ChatMessage[];
  userInput?: string;
  onOptionsGenerated: (options: PrefixOption[]) => void;
  onOptionsSelected: (selectedOptions: PrefixOption[]) => void;
}

const AIGeneratedPrefixPanel: React.FC<AIGeneratedPrefixPanelProps> = ({
  phase,
  persona,
  content,
  history,
  userInput,
  onOptionsGenerated,
  onOptionsSelected
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [analysisContext, setAnalysisContext] = useState<AnalysisContext | null>(null);
  const [generatedOptions, setGeneratedOptions] = useState<PrefixOption[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);
  const [aiService] = useState(() => new PrefixMessageAIService());
  const [storageService] = useState(() => AIGeneratedPrefixStorageService.getInstance());
  const [customUserInput, setCustomUserInput] = useState<string>('');

  // 智能生成前置消息
  const handleGenerate = async (forceRegenerate: boolean = false) => {
    console.log('🚀 开始AI智能生成前置消息', forceRegenerate ? '(强制重新生成)' : '');
    console.log('📊 输入参数:', {
      phase,
      persona: persona?.id || 'default',
      contentLength: content.length,
      historyLength: history.length,
      userInput: userInput || 'none',
      forceRegenerate
    });

    setIsGenerating(true);
    setError(null);

    try {
      // 第一步：分析上下文
      console.log('🔍 第一步：分析上下文');
      const context = await aiService.analyzeContext(phase, persona, content, history);
      console.log('✅ 上下文分析完成:', context);
      setAnalysisContext(context);

      // 模拟分析过程的延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 第二步：生成前置消息选项
      console.log('🤖 第二步：调用AI生成前置消息选项');
      const finalUserInput = customUserInput.trim() || userInput;
      const options = await aiService.generatePrefixOptions(context, finalUserInput, forceRegenerate);
      console.log('✅ AI生成完成，获得选项数量:', options.length);
      console.log('📋 生成的选项:', options);

      setGeneratedOptions(options);
      onOptionsGenerated(options);

    } catch (err) {
      console.error('❌ AI生成前置消息失败:', err);
      setError(`生成失败：${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // 处理选项选择
  const handleOptionToggle = (optionId: string) => {
    const newSelected = new Set(selectedOptions);
    if (newSelected.has(optionId)) {
      newSelected.delete(optionId);
    } else {
      newSelected.add(optionId);
    }
    setSelectedOptions(newSelected);
    // 注意：这里不立即调用onOptionsSelected，等用户点击"应用选择"按钮时再调用
  };

  // 重新生成
  const handleRegenerate = () => {
    setGeneratedOptions([]);
    setSelectedOptions(new Set());
    setAnalysisContext(null);
    handleGenerate(true); // 强制重新生成
  };

  // 一键应用推荐
  const handleApplyRecommended = () => {
    const recommended = generatedOptions
      .filter(option => option.confidence > 0.7)
      .slice(0, 3);

    const recommendedIds = new Set(recommended.map(option => option.id));
    setSelectedOptions(recommendedIds);
    onOptionsSelected(recommended);
  };

  // 应用选择的选项
  const handleApplySelected = () => {
    const selected = generatedOptions.filter(option => selectedOptions.has(option.id));
    if (selected.length > 0) {
      onOptionsSelected(selected);
      console.log(`✅ 应用了 ${selected.length} 个选择的前置消息`);
    } else {
      console.log('⚠️ 没有选择任何前置消息');
    }
  };

  // 保存到模板
  const handleSaveToTemplates = () => {
    if (generatedOptions.length > 0) {
      const savedPrefixes = storageService.savePrefixOptions(generatedOptions, phase);
      if (savedPrefixes.length > 0) {
        alert(`✅ 成功保存了 ${savedPrefixes.length} 个前置消息到模板库！`);
      } else {
        alert('⚠️ 所有前置消息都已存在于模板库中');
      }
    }
  };

  // 保存选择的选项到模板
  const handleSaveSelectedToTemplates = () => {
    const selected = generatedOptions.filter(option => selectedOptions.has(option.id));
    if (selected.length > 0) {
      const savedPrefixes = storageService.savePrefixOptions(selected, phase);
      if (savedPrefixes.length > 0) {
        alert(`✅ 成功保存了 ${savedPrefixes.length} 个选择的前置消息到模板库！`);
      } else {
        alert('⚠️ 所有选择的前置消息都已存在于模板库中');
      }
    } else {
      alert('⚠️ 请先选择要保存的前置消息');
    }
  };

  return (
    <div className="space-y-6">
      {/* 用户需求输入区域 */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          📝 描述您的具体需求（可选）
        </label>
        <textarea
          value={customUserInput}
          onChange={(e) => setCustomUserInput(e.target.value)}
          placeholder="例如：我想要分析情节节奏、需要人物对话建议、希望优化开头悬念等..."
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
          rows={3}
        />
        <div className="text-xs text-gray-500 mt-1">
          💡 描述您的具体需求，AI会生成更针对性的前置消息
        </div>
      </div>

      {/* 生成控制区域 */}
      <div className="text-center">
        <button
          onClick={() => handleGenerate(false)}
          disabled={isGenerating}
          className={`
            px-6 py-3 rounded-lg font-medium text-white transition-all duration-300
            ${isGenerating
              ? 'bg-gradient-to-r from-purple-400 to-blue-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 hover:scale-105'
            }
            shadow-lg hover:shadow-xl
          `}
        >
          {isGenerating ? (
            <div className="flex items-center space-x-2">
              <div className="ai-generating-icon">
                <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <span>AI正在分析...</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span>🤖 AI智能生成</span>
            </div>
          )}
        </button>

        {generatedOptions.length > 0 && !isGenerating && (
          <div className="mt-3 space-y-2">
            {/* 第一行：主要操作 */}
            <div className="flex justify-center space-x-3">
              <button
                onClick={handleRegenerate}
                className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                🔄 重新生成
              </button>
              <button
                onClick={handleApplyRecommended}
                disabled={generatedOptions.filter(option => option.confidence > 0.7).length === 0}
                className="px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                ⭐ 应用推荐 ({generatedOptions.filter(option => option.confidence > 0.7).length}个)
              </button>
              <button
                onClick={handleApplySelected}
                disabled={selectedOptions.size === 0}
                className="px-4 py-2 text-sm text-green-600 border border-green-300 rounded-lg hover:bg-green-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                ✅ 应用选择 ({selectedOptions.size}个)
              </button>
            </div>

            {/* 第二行：保存操作 */}
            <div className="flex justify-center space-x-3">
              <button
                onClick={handleSaveToTemplates}
                className="px-3 py-1 text-xs text-purple-600 border border-purple-300 rounded hover:bg-purple-50 transition-colors"
              >
                💾 保存全部到模板
              </button>
              <button
                onClick={handleSaveSelectedToTemplates}
                disabled={selectedOptions.size === 0}
                className="px-3 py-1 text-xs text-orange-600 border border-orange-300 rounded hover:bg-orange-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                📚 保存选择到模板 ({selectedOptions.size}个)
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* 上下文分析展示 */}
      {analysisContext && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-3">📊 AI分析结果</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="analysis-card bg-white p-3 rounded border">
              <div className="text-xs text-gray-600 mb-1">当前阶段</div>
              <div className="text-sm font-medium text-gray-800">{analysisContext.phaseFeatures.name}</div>
              <div className="text-xs text-gray-500 mt-1">
                进度: {Math.round(analysisContext.phaseFeatures.currentProgress * 100)}%
              </div>
            </div>

            <div className="analysis-card bg-white p-3 rounded border">
              <div className="text-xs text-gray-600 mb-1">内容特征</div>
              <div className="text-sm font-medium text-gray-800">{analysisContext.contentAnalysis.style}</div>
              <div className="text-xs text-gray-500 mt-1">
                {analysisContext.contentAnalysis.wordCount}字
              </div>
            </div>

            <div className="analysis-card bg-white p-3 rounded border">
              <div className="text-xs text-gray-600 mb-1">人设风格</div>
              <div className="text-sm font-medium text-gray-800">{analysisContext.personaTraits.communicationStyle}</div>
              <div className="text-xs text-gray-500 mt-1">
                {analysisContext.personaTraits.expertise.slice(0, 2).join('、')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 生成结果展示 */}
      {generatedOptions.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-800">
              🎯 AI生成的前置消息 ({generatedOptions.length}个选项)
            </h3>
            <div className="text-xs text-gray-500">
              已选择 {selectedOptions.size} 个
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            {generatedOptions.map((option, index) => (
              <div
                key={option.id}
                className={`
                  prefix-option-card p-4 border rounded-lg cursor-pointer transition-all duration-300
                  ${selectedOptions.has(option.id)
                    ? 'border-blue-500 bg-blue-50 shadow-md transform -translate-y-1'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm hover:-translate-y-0.5'
                  }
                `}
                onClick={() => handleOptionToggle(option.id)}
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: 'slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards'
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* 选项头部 */}
                    <div className="flex items-center space-x-2 mb-2">
                      <div className={`
                        w-4 h-4 border-2 rounded transition-all duration-300
                        ${selectedOptions.has(option.id)
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                        }
                      `}>
                        {selectedOptions.has(option.id) && (
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <span className="text-sm font-medium text-gray-800">{option.description}</span>
                      <div className="flex items-center space-x-1">
                        <div className={`
                          px-2 py-1 rounded-full text-xs
                          ${option.category === 'ace_framework' ? 'bg-purple-100 text-purple-700' :
                            option.category === 'format' ? 'bg-green-100 text-green-700' :
                            option.category === 'persona' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }
                        `}>
                          {option.category === 'ace_framework' ? 'ACE框架' :
                           option.category === 'format' ? '格式规范' :
                           option.category === 'persona' ? '人设强化' :
                           option.category === 'context' ? '上下文' : '自定义'}
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className={`
                            w-2 h-2 rounded-full
                            ${option.confidence > 0.8 ? 'bg-green-500' :
                              option.confidence > 0.6 ? 'bg-yellow-500' : 'bg-gray-400'
                            }
                          `}></div>
                          <span className="text-xs text-gray-500">
                            {Math.round(option.confidence * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 前置消息内容 */}
                    <div className="bg-gray-50 p-3 rounded text-xs text-gray-700 mb-2">
                      {option.content}
                    </div>

                    {/* 说明信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>💡 {option.reasoning}</span>
                      <span>📋 {option.useCase}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 样式定义 */}
      <style jsx>{`
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .prefix-option-card {
          opacity: 0;
        }

        .ai-generating-icon svg {
          animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        .analysis-card {
          animation: slideInLeft 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        .analysis-card:nth-child(1) { animation-delay: 0.1s; }
        .analysis-card:nth-child(2) { animation-delay: 0.2s; }
        .analysis-card:nth-child(3) { animation-delay: 0.3s; }

        @keyframes slideInLeft {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </div>
  );
};

export default AIGeneratedPrefixPanel;
