"use client";

import { INotificationComponent } from '../interfaces';

/**
 * 默认通知组件实现
 * 使用浏览器原生的alert和console方法
 */
export class DefaultNotificationComponent implements INotificationComponent {
  /**
   * 显示成功通知
   * @param message 通知消息
   */
  showSuccess(message: string): void {
    console.log(`✅ 成功: ${message}`);
    // 在实际应用中，这里应该使用更好的通知UI组件
    // 例如toast或者snackbar
    alert(`✅ 成功: ${message}`);
  }
  
  /**
   * 显示错误通知
   * @param message 通知消息
   */
  showError(message: string): void {
    console.error(`❌ 错误: ${message}`);
    // 在实际应用中，这里应该使用更好的通知UI组件
    alert(`❌ 错误: ${message}`);
  }
  
  /**
   * 显示信息通知
   * @param message 通知消息
   */
  showInfo(message: string): void {
    console.info(`ℹ️ 信息: ${message}`);
    // 在实际应用中，这里应该使用更好的通知UI组件
    alert(`ℹ️ 信息: ${message}`);
  }
  
  /**
   * 显示警告通知
   * @param message 通知消息
   */
  showWarning(message: string): void {
    console.warn(`⚠️ 警告: ${message}`);
    // 在实际应用中，这里应该使用更好的通知UI组件
    alert(`⚠️ 警告: ${message}`);
  }
}
