/**
 * 通用元素提取服务
 * 基于现有术语提取功能，抽象创建通用的元素提取服务
 * 支持多种提取类型（术语、ACE元素等）
 */

import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { ExtractedElement } from '@/types/ACEFrameworkTypes';

/**
 * 提取类型枚举
 */
export type ExtractionType = 'terminology' | 'ace-elements';

/**
 * 提取选项接口
 */
export interface ExtractionOptions {
  /** 提取类型 */
  extractionType: ExtractionType;
  /** 最低重要性（1-5） */
  minImportance?: number;
  /** 自定义提示词 */
  customPrompt?: string;
  /** 流式响应回调 */
  onStreamChunk?: (chunk: string) => void;
  /** 已有元素列表，用于去重 */
  existingElements?: ExtractedElement[];
}

/**
 * 提取结果接口
 */
export interface ExtractionResult {
  /** 提取的元素列表 */
  elements: ExtractedElement[];
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 原始AI响应 */
  rawResponse?: string;
}

/**
 * 通用元素提取服务类
 */
export class ElementExtractionService {
  private aiService: UnifiedAIService;

  constructor() {
    this.aiService = new UnifiedAIService(AIServiceType.WRITING);
  }

  /**
   * 提取元素的主要方法
   * @param text 要分析的文本
   * @param options 提取选项
   * @returns 提取结果
   */
  async extractElements(text: string, options: ExtractionOptions): Promise<ExtractionResult> {
    try {
      // 验证输入
      if (!text || text.trim().length === 0) {
        return {
          elements: [],
          success: false,
          error: '输入文本不能为空'
        };
      }

      // 根据提取类型选择不同的处理逻辑
      switch (options.extractionType) {
        case 'terminology':
          return await this.extractTerminology(text, options);
        case 'ace-elements':
          return await this.extractACEElements(text, options);
        default:
          return {
            elements: [],
            success: false,
            error: `不支持的提取类型: ${options.extractionType}`
          };
      }
    } catch (error) {
      console.error('元素提取失败:', error);
      return {
        elements: [],
        success: false,
        error: error instanceof Error ? error.message : '提取过程中发生未知错误'
      };
    }
  }

  /**
   * 提取术语元素
   * @param text 文本内容
   * @param options 提取选项
   * @returns 提取结果
   */
  private async extractTerminology(text: string, options: ExtractionOptions): Promise<ExtractionResult> {
    const messageBuilder = new MessageBuilder();

    // 系统消息 - 使用术语提取的系统提示词
    const systemPrompt = `你现在是“世界观设定猎手”，你的任务只有一个：从一堆文字里，把那些能构成世界观的“硬设定”给揪出来。

【动手指南】
1. 独有名词：人名、地名、门派名、组织名等。
2. 特殊物件：神器、丹药、法宝、特殊道具等。
3. 核心概念：功法、境界、特殊能力、世界规则等。
4. 为每个设定分配合适的类别。
5. 这玩意儿有多重要？（1-5星，5星最高）。
6. 用一句话简单说下这是个啥。

【输出格式】
老规矩，直接给我JSON，别带壳（Markdown）：
{
  "设定名称1": {
    "category": "类别",
    "description": "一句话描述",
    "importance": 数字(1-5)
  }
}`;

    messageBuilder.addSystemMessage(systemPrompt);

    // 用户消息
    const minImportance = options.minImportance || 3;
    const userPrompt = `下面这段文字，帮我把里面的“硬设定”挖出来，那些鸡毛蒜皮的就别管了，只给我 ${minImportance} 星及以上的干货：

${text}

直接给JSON，别啰嗦。`;

    messageBuilder.addUserMessage(userPrompt);

    // 添加自定义提示词
    if (options.customPrompt) {
      messageBuilder.addUserMessage(options.customPrompt);
    }

    return await this.sendExtractionRequest(messageBuilder, options, 'terminology');
  }

  /**
   * 提取ACE元素
   * @param text 文本内容
   * @param options 提取选项
   * @returns 提取结果
   */
  private async extractACEElements(text: string, options: ExtractionOptions): Promise<ExtractionResult> {
    // 检查文本长度，决定是否需要分段处理
    const shouldSegment = this.shouldSegmentText(text);

    if (shouldSegment) {
      return await this.extractACEElementsSegmented(text, options);
    } else {
      return await this.extractACEElementsSingle(text, options);
    }
  }

  /**
   * 判断是否需要分段处理文本
   * @param text 输入文本
   * @returns 是否需要分段
   */
  private shouldSegmentText(text: string): boolean {
    // 按句子数量判断，超过30句就分段处理
    const sentences = this.splitTextIntoSentences(text);
    return sentences.length > 30;
  }

  /**
   * 将文本分割成句子
   * @param text 输入文本
   * @returns 句子数组
   */
  private splitTextIntoSentences(text: string): string[] {
    if (!text) return [];

    // 按句号、感叹号、问号分割，保留标点符号
    const sentences = text.split(/(?<=[。！？.!?])/);

    return sentences.filter(sentence => sentence.trim().length > 0);
  }

  /**
   * 将文本按指定句子数分段
   * @param text 输入文本
   * @param sentencesPerSegment 每段句子数
   * @returns 分段后的文本数组
   */
  private splitTextIntoSegments(text: string, sentencesPerSegment: number): string[] {
    const sentences = this.splitTextIntoSentences(text);
    const segments: string[] = [];

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      const segment = segmentSentences.join('').trim();

      if (segment) {
        segments.push(segment);
      }
    }

    return segments.length > 0 ? segments : [text];
  }
// ... 其他函数保持不变 ...

  /**
   * 分段提取ACE元素（合并后单次请求版）
   * @param text 文本内容
   * @param options 提取选项
   * @returns 提取结果
   */
 // ... 其他函数保持不变 ...

  /**
   * 分段提取ACE元素（合并后单次请求版）
   * @param text 文本内容
   * @param options 提取选项
   * @returns 提取结果
   */
  private async extractACEElementsSegmented(text: string, options: ExtractionOptions): Promise<ExtractionResult> {
    try {
      // 将文本按10句一段分割
      const segments = this.splitTextIntoSegments(text, 10);
      const messageBuilder = new MessageBuilder();

      // 系统消息 - 终极融合版的“爆款挖掘机”
      const systemPrompt = `你现在是“金牌编辑兼爆款挖掘机”，你的任务是把一篇稿子拆解成最基本的“创作基因”，识别出所有能让它封神、能卖钱、能让读者尖叫的闪光点。

【核心工作原则：提炼“元素+用法”的可复制公式】
这是最高指令！你的所有输出都必须是“可复制的方法论”，而不是“对原文的总结”。你要把“鱼”拆解成“钓鱼的方法”，并且说明用的是什么“鱼饵”。

举例：
- **不要这样做（错误示范）**：“在第三段，作者描写了校园的场景。”（这是无效描述）
- **必须这样做（正确示范）**：“元素：[校园场景]。可复制用法：利用‘校园’这个封闭且有特殊规则（如考试、校规）的环境，将社会性冲突（如阶级、人际）微缩化，从而放大角色的个人挣扎，制造强代入感。可直接套用此模式设计‘职场’、‘军营’等场景冲突。”

【你的雷达要扫描以下11类宝藏】
1.  **金句钩子 (Golden Hook):** 能让读者记一辈子、或者能当签名的金句、神比喻、致命吐槽。
2.  **画面镜头 (Cinematic Scene):** 充满画面感的场景描写，读完脑子里像过了遍电影。
3.  **情节脉冲 (Plot Pulse):** 剧情的关键转折、高能反转、或者让人拍案叫绝的节奏控制点。
4.  **人物弧光 (Character Arc):** 一句话或一个动作，瞬间让角色立起来、活起来，或者展现了其成长变化。
5.  **情感扳机 (Emotion Trigger):** 能精准触发读者某种强烈情绪（爽、虐、甜、燃、痛）的段落或手法。
6.  **套路新编 (Trope Twist):** 把老掉牙的套路玩出了新花样，或者用得恰到好处。
7.  **流量密码 (Market Keyword):** 经过市场验证，本身就自带热度的核心元素或概念。
8.  **设定闪光 (World-building Spark):** 眼前一亮的设定，可以是一个新奇的世界规则、一个有趣的物品。
9.  **脑洞火花 (Creative Spark):** 独特的故事核心创意、新颖的“what if”概念，能作为整个故事的基石。
10. **情境舞台 (Contextual Stage):** 具有强冲突潜力的事件场景（如校园、医院、战场），本身就提供了情节设计的“脚手架”。
11. **身份符号 (Identity Symbol):** 具有故事延展性的名词（如消防员、医生、刺客），本身就捆绑了一套职业/身份带来的冲突和看点。

【输出格式】
直接给我JSON，别带壳（Markdown）。"description"字段必须严格遵守【核心工作原则】：
{
  "闪光点名称1": {
    "category": "金句钩子|画面镜头|情节脉冲|人物弧光|情感扳机|套路新编|流量密码|设定闪光|脑洞火花|情境舞台|身份符号",
    "description": "【关键】这里必须是‘元素+用法’的公式化提炼，解释清楚这个元素（是什么）+它背后的创作方法（怎么用），让用户无需看原文就能直接魔改应用。",
    "importance": 数字(1-5)
  }
}`;

      // 在系统提示词中明确最低重要度要求
      const minImportance = options.minImportance || 3;
      const enhancedSystemPrompt = systemPrompt.replace(
        '【核心工作原则：提炼"元素+用法"的可复制公式】',
        `【重要提醒：当前最低重要度要求是 ${minImportance} 星】
你只需要提取重要度达到 ${minImportance} 星及以上的元素，低于 ${minImportance} 星的元素请直接忽略，不要输出。

【核心工作原则：提炼"元素+用法"的可复制公式】`
      );

      messageBuilder.addSystemMessage(enhancedSystemPrompt);

      // 添加分段说明消息
      const introMessage = `我把稿子切成了 ${segments.length} 块，你给我通读一遍，然后把里面所有的“闪光点”都给我挖出来。鸡毛蒜皮的不要，只给我重要性 ${minImportance} 星及以上的干货。

记住你的核心原则：我不要知道原文写了什么，我只要能直接拿来用的、可复制的“写作配方”，这包括“方法论”也包括“高价值元素”本身。`;

      messageBuilder.addUserMessage(introMessage);

      // 逐段添加内容消息
      segments.forEach((segment, index) => {
        const segmentMessage = `【第 ${index + 1}/${segments.length} 段原文】：
${segment}`;
        messageBuilder.addUserMessage(segmentMessage);
      });

      // 添加最终分析请求，再次强化核心指令
      const finalMessage = `
      
【你的雷达要扫描以下11类宝藏】
1.  **金句钩子 (Golden Hook):** 能让读者记一辈子、或者能当签名的金句、神比喻、致命吐槽。
2.  **画面镜头 (Cinematic Scene):** 充满画面感的场景描写，读完脑子里像过了遍电影。
3.  **情节脉冲 (Plot Pulse):** 剧情的关键转折、高能反转、或者让人拍案叫绝的节奏控制点。
4.  **人物弧光 (Character Arc):** 一句话或一个动作，瞬间让角色立起来、活起来，或者展现了其成长变化。
5.  **情感扳机 (Emotion Trigger):** 能精准触发读者某种强烈情绪（爽、虐、甜、燃、痛）的段落或手法。
6.  **套路新编 (Trope Twist):** 把老掉牙的套路玩出了新花样，或者用得恰到好处。
7.  **流量密码 (Market Keyword):** 经过市场验证，本身就自带热度的核心元素或概念。
8.  **设定闪光 (World-building Spark):** 眼前一亮的设定，可以是一个新奇的世界规则、一个有趣的物品。
9.  **脑洞火花 (Creative Spark):** 独特的故事核心创意、新颖的“what if”概念，能作为整个故事的基石。
10. **情境舞台 (Contextual Stage):** 具有强冲突潜力的事件场景（如校园、医院、战场），本身就提供了情节设计的“脚手架”。
11. **身份符号 (Identity Symbol):** 具有故事延展性的名词（如消防员、医生、刺客），本身就捆绑了一套职业/身份带来的冲突和看点。

      
      以上就是全部 ${segments.length} 段内容。开工吧，金牌编辑！最后再强调一遍，你的任务是提炼“元素+用法”的公式，不是复述“故事梗概”。把你挖到的所有宝贝，都整理成一个可直接复刻的JSON交上来！`;
      messageBuilder.addUserMessage(finalMessage);

      // 添加自定义提示词
      if (options.customPrompt) {
        messageBuilder.addUserMessage(options.customPrompt);
      }

      // 发送单次请求包含所有分段
      return await this.sendExtractionRequest(messageBuilder, options, 'ace-elements');

    } catch (error) {
      console.error('分段提取ACE元素失败:', error);
      return {
        elements: [],
        success: false,
        error: error instanceof Error ? error.message : '分段提取失败'
      };
    }
  }

// ... 其他函数保持不变 ...

  /**
   * 单段提取ACE元素
   * @param text 文本内容
   * @param options 提取选项
   * @returns 提取结果
   */
  private async extractACEElementsSingle(text: string, options: ExtractionOptions): Promise<ExtractionResult> {
    const messageBuilder = new MessageBuilder();

    // 系统消息 - 专门用于ACE元素提取
    const systemPrompt = `你现在是“爆款挖掘机”，眼光毒辣，擅长从文章里挖出那些能让作品“值钱”、“好看”的闪光点。

【你要挖的是这五类宝藏】
1. 流量密码：能吸睛的词、梗、或者热门概念。
2. 写作手法：让文字更好看的骚操作，比如比喻、排比、反差。
3. 故事骨架：控制节奏和剧情走向的结构，比如“欲扬先抑”、“三段式”。
4. 独特文风：作者的个人印记和调调，别人模仿不来的那种。
5. 神来之笔：别人没想到的金点子、独特的创意。

【分类标准】
- 关键词：市场热词、流行元素、吸引眼球的词汇
- 技巧：写作手法、表达技巧、修辞方式
- 结构：情节安排、章节布局、叙述结构
- 风格：语言特色、文体风格、表现方式
- 创意：独特想法、新颖概念、创新元素
- 场景：世界观设定、事件场景，便于情节设计场景位置产生的特有连锁反应
- 名词： 各种职业，等等分类
【输出格式】
直接给我JSON，别带壳：
{
  "闪光点名称1": {
    "category": "关键词|技巧|结构|风格|创意",
    "description": "这玩意儿牛在哪，能怎么用",
    "importance": 数字(1-5)
  }
}`;

    // 在系统提示词中明确最低重要度要求
    const minImportance = options.minImportance || 3;
    const enhancedSystemPrompt = systemPrompt.replace(
      '【你要挖的是这五类宝藏】',
      `【重要提醒：当前最低重要度要求是 ${minImportance} 星】
你只需要提取重要度达到 ${minImportance} 星及以上的元素，低于 ${minImportance} 星的元素请直接忽略，不要输出。

【你要挖的是这五类宝藏】`
    );

    messageBuilder.addSystemMessage(enhancedSystemPrompt);

    // 用户消息
    const userPrompt = `从下面这段文字里，帮我把那些有潜力的“爆款基因”给提出来，只给我重要性 ${minImportance} 星及以上的：

${text}

特别留意下面这些东西：
1. 能学了就用的小技巧。
2. 能吸引读者的热门词或概念。
3. 作者独有的、让人眼前一亮的说法。
4. 不落俗套的剧情结构。
5. 作者独特的“内味儿”。

老规矩，JSON端上来，不要Markdown。`;

    messageBuilder.addUserMessage(userPrompt);

    // 添加自定义提示词
    if (options.customPrompt) {
      messageBuilder.addUserMessage(options.customPrompt);
    }

    return await this.sendExtractionRequest(messageBuilder, options, 'ace-elements');
  }

  /**
   * 发送提取请求
   * @param messageBuilder 消息构建器
   * @param options 提取选项
   * @param type 提取类型
   * @returns 提取结果
   */
  private async sendExtractionRequest(
    messageBuilder: MessageBuilder,
    options: ExtractionOptions,
    type: string
  ): Promise<ExtractionResult> {
    try {
      // 转换消息格式
      const messages = messageBuilder.build().map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content
      }));

      // 发送AI请求
      let result;
      if (options.onStreamChunk) {
        result = await this.aiService['callAIStreaming'](messages, options.onStreamChunk);
      } else {
        result = await this.aiService['callAI'](messages);
      }

      if (!result.success) {
        return {
          elements: [],
          success: false,
          error: result.error || '请求失败'
        };
      }

      // 解析JSON结果
      const elements = this.parseExtractionResult(result.text || '', type, options);

      return {
        elements,
        success: true,
        rawResponse: result.text
      };

    } catch (error) {
      console.error('发送提取请求失败:', error);
      return {
        elements: [],
        success: false,
        error: error instanceof Error ? error.message : '请求发送失败'
      };
    }
  }



  /**
   * 解析提取结果
   * @param responseText AI响应文本
   * @param type 提取类型
   * @param options 提取选项
   * @returns 解析后的元素列表
   */
  private parseExtractionResult(responseText: string, type: string, options: ExtractionOptions): ExtractedElement[] {
    try {
      // 提取JSON部分
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.warn('未找到有效的JSON响应');
        return [];
      }

      const jsonStr = jsonMatch[0];
      const data = JSON.parse(jsonStr);
      const elements: ExtractedElement[] = [];

      // 转换为ExtractedElement格式
      for (const [name, info] of Object.entries(data)) {
        const typedInfo = info as any;

        // 检查重要性是否满足最低要求
        const importance = typeof typedInfo.importance === 'string' ? parseInt(typedInfo.importance) : typedInfo.importance;
        const minImportance = options.minImportance || 3;

        if (importance && importance < minImportance) {
          console.log(`跳过低重要度元素: ${name} (重要度: ${importance}, 最低要求: ${minImportance})`);
          continue; // 跳过低于最低重要度的元素
        }

        const element: ExtractedElement = {
          id: this.generateElementId(name, type),
          category: typedInfo.category || '未分类',
          elements: [name], // 单个元素作为数组
          sourceText: typedInfo.description || '', // 将description存储在sourceText中
          extractedAt: new Date(),
          confidence: this.calculateConfidence(typedInfo.importance),
          tags: [type, typedInfo.category || '未分类']
        };

        elements.push(element);
      }

      // 去重处理
      return this.deduplicateElements(elements, options.existingElements || []);

    } catch (error) {
      console.error('解析提取结果失败:', error);
      return [];
    }
  }

  /**
   * 生成元素ID
   * @param name 元素名称
   * @param type 提取类型
   * @returns 唯一ID
   */
  private generateElementId(name: string, type: string): string {
    const timestamp = Date.now();
    const hash = this.simpleHash(name + type);
    return `${type}-${hash}-${timestamp}`;
  }

  /**
   * 简单哈希函数
   * @param str 输入字符串
   * @returns 哈希值
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 计算置信度
   * @param importance 重要性评分
   * @returns 置信度（0-1）
   */
  private calculateConfidence(importance: number | string): number {
    const imp = typeof importance === 'string' ? parseInt(importance) : importance;
    return Math.min(Math.max((imp || 3) / 5, 0), 1);
  }

  /**
   * 去重处理
   * @param newElements 新提取的元素
   * @param existingElements 已有元素
   * @returns 去重后的元素列表
   */
  private deduplicateElements(newElements: ExtractedElement[], existingElements: ExtractedElement[]): ExtractedElement[] {
    const existingNames = new Set(
      existingElements.flatMap(el => el.elements.map(e => e.toLowerCase()))
    );

    return newElements.filter(element => {
      const elementNames = element.elements.map(e => e.toLowerCase());
      return !elementNames.some(name => existingNames.has(name));
    });
  }


}

// 导出单例实例
export const elementExtractionService = new ElementExtractionService();
