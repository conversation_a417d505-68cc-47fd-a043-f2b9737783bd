/**
 * API密钥轮播配置管理器
 */

import { APIKeyRotationConfig, URLKeyPool, RotationStrategy } from '../../types/apiKeyRotation';

export class APIKeyRotationConfigManager {
  private static readonly STORAGE_KEY = 'api-key-rotation-config';
  private static readonly ENCRYPTION_KEY = 'api-rotation-key';
  private static instance: APIKeyRotationConfigManager;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): APIKeyRotationConfigManager {
    if (!APIKeyRotationConfigManager.instance) {
      APIKeyRotationConfigManager.instance = new APIKeyRotationConfigManager();
    }
    return APIKeyRotationConfigManager.instance;
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): APIKeyRotationConfig {
    return {
      version: '1.0.0',
      urlPools: {},
      globalSettings: {
        enabled: true,
        defaultStrategy: 'round-robin',
        defaultWaitTime: 60000, // 1分钟
        maxFailures: 3,
        healthCheckInterval: 300000, // 5分钟
        autoRecovery: true,
        logLevel: 'info'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * 加载配置
   */
  load(): APIKeyRotationConfig {
    try {
      const encrypted = localStorage.getItem(APIKeyRotationConfigManager.STORAGE_KEY);
      if (!encrypted) {
        console.log('📝 未找到轮播配置，创建默认配置');
        const defaultConfig = this.getDefaultConfig();
        this.save(defaultConfig);
        return defaultConfig;
      }

      const config = this.decrypt(encrypted);
      
      // 验证配置完整性
      if (this.validateConfig(config)) {
        console.log('✅ API密钥轮播配置加载成功');
        return config;
      } else {
        console.warn('⚠️ 配置验证失败，使用默认配置');
        const defaultConfig = this.getDefaultConfig();
        this.save(defaultConfig);
        return defaultConfig;
      }
    } catch (error) {
      console.error('❌ 加载轮播配置失败:', error);
      const defaultConfig = this.getDefaultConfig();
      this.save(defaultConfig);
      return defaultConfig;
    }
  }

  /**
   * 保存配置
   */
  save(config: APIKeyRotationConfig): void {
    try {
      // 更新时间戳
      config.updatedAt = new Date();
      
      // 加密并保存
      const encrypted = this.encrypt(config);
      localStorage.setItem(APIKeyRotationConfigManager.STORAGE_KEY, encrypted);
      
      console.log('✅ API密钥轮播配置已保存');
    } catch (error) {
      console.error('❌ 保存轮播配置失败:', error);
      throw new Error('配置保存失败');
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(config: any): config is APIKeyRotationConfig {
    return (
      config &&
      typeof config === 'object' &&
      typeof config.version === 'string' &&
      typeof config.urlPools === 'object' &&
      config.globalSettings &&
      typeof config.globalSettings.enabled === 'boolean'
    );
  }

  /**
   * 加密配置
   */
  private encrypt(config: APIKeyRotationConfig): string {
    try {
      const jsonString = JSON.stringify(config);
      // 简单的Base64编码，实际项目中应使用更安全的加密方法
      return btoa(unescape(encodeURIComponent(jsonString)));
    } catch (error) {
      console.error('加密失败:', error);
      throw new Error('配置加密失败');
    }
  }

  /**
   * 解密配置
   */
  private decrypt(encrypted: string): APIKeyRotationConfig {
    try {
      // 简单的Base64解码
      const jsonString = decodeURIComponent(escape(atob(encrypted)));
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('解密失败:', error);
      throw new Error('配置解密失败');
    }
  }

  /**
   * 添加URL密钥池
   */
  addURLPool(config: APIKeyRotationConfig, url: string, strategy: RotationStrategy = 'round-robin'): URLKeyPool {
    const pool: URLKeyPool = {
      url,
      keys: [],
      currentIndex: 0,
      rotationStrategy: strategy,
      enabled: true,
      maxFailures: config.globalSettings.maxFailures,
      defaultWaitTime: config.globalSettings.defaultWaitTime,
      healthCheckInterval: config.globalSettings.healthCheckInterval,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    config.urlPools[url] = pool;
    this.save(config);
    
    console.log(`✅ 已添加URL密钥池: ${url}`);
    return pool;
  }

  /**
   * 删除URL密钥池
   */
  removeURLPool(config: APIKeyRotationConfig, url: string): boolean {
    if (config.urlPools[url]) {
      delete config.urlPools[url];
      this.save(config);
      console.log(`✅ 已删除URL密钥池: ${url}`);
      return true;
    }
    return false;
  }

  /**
   * 更新全局设置
   */
  updateGlobalSettings(config: APIKeyRotationConfig, settings: Partial<APIKeyRotationConfig['globalSettings']>): void {
    config.globalSettings = {
      ...config.globalSettings,
      ...settings
    };
    this.save(config);
    console.log('✅ 全局设置已更新');
  }

  /**
   * 导出配置
   */
  export(config: APIKeyRotationConfig): string {
    // 移除敏感信息
    const exportConfig = {
      ...config,
      urlPools: Object.fromEntries(
        Object.entries(config.urlPools).map(([url, pool]) => [
          url,
          {
            ...pool,
            keys: pool.keys.map(key => ({
              ...key,
              key: '***' // 隐藏API密钥
            }))
          }
        ])
      )
    };

    return JSON.stringify(exportConfig, null, 2);
  }

  /**
   * 导入配置
   */
  import(configJson: string): APIKeyRotationConfig {
    try {
      const config = JSON.parse(configJson);
      
      if (!this.validateConfig(config)) {
        throw new Error('配置格式无效');
      }

      // 清除隐藏的API密钥
      Object.values(config.urlPools).forEach(pool => {
        pool.keys = pool.keys.filter(key => key.key !== '***');
      });

      return config;
    } catch (error) {
      throw new Error('配置导入失败: ' + (error as Error).message);
    }
  }

  /**
   * 重置配置
   */
  reset(): APIKeyRotationConfig {
    localStorage.removeItem(APIKeyRotationConfigManager.STORAGE_KEY);
    const defaultConfig = this.getDefaultConfig();
    this.save(defaultConfig);
    console.log('✅ 轮播配置已重置');
    return defaultConfig;
  }
}
