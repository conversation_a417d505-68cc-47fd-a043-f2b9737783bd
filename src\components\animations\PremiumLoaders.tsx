"use client";

import React from 'react';

interface LoaderProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

// 高级点状加载器
export const DotsLoader: React.FC<LoaderProps> = ({ 
  size = 'md', 
  color = 'var(--color-primary)',
  className = '' 
}) => {
  const sizeMap = {
    sm: { dot: 6, gap: 3 },
    md: { dot: 8, gap: 4 },
    lg: { dot: 12, gap: 6 }
  };

  const { dot, gap } = sizeMap[size];

  return (
    <div className={`premium-loading-dots ${className}`} style={{ gap: `${gap}px` }}>
      {[1, 2, 3].map(i => (
        <div
          key={i}
          className="premium-loading-dot"
          style={{
            width: `${dot}px`,
            height: `${dot}px`,
            background: color,
            animationDelay: `${(i - 1) * 0.16}s`
          }}
        />
      ))}
    </div>
  );
};

// 脉冲加载器
export const PulseLoader: React.FC<LoaderProps> = ({ 
  size = 'md', 
  color = 'var(--color-primary)',
  className = '' 
}) => {
  const sizeMap = {
    sm: 24,
    md: 40,
    lg: 60
  };

  const loaderSize = sizeMap[size];

  return (
    <div className={`premium-loading-pulse ${className}`}>
      <div
        style={{
          width: `${loaderSize}px`,
          height: `${loaderSize}px`,
          background: color,
          borderRadius: '50%',
          animation: 'pulse 1.5s ease-in-out infinite'
        }}
      />
    </div>
  );
};

// 旋转加载器
export const SpinLoader: React.FC<LoaderProps> = ({ 
  size = 'md', 
  color = 'var(--color-primary)',
  className = '' 
}) => {
  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32
  };

  const loaderSize = sizeMap[size];

  return (
    <div
      className={className}
      style={{
        width: `${loaderSize}px`,
        height: `${loaderSize}px`,
        border: `2px solid rgba(139, 69, 19, 0.2)`,
        borderTop: `2px solid ${color}`,
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};

// 骨架屏组件
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '20px',
  borderRadius = '4px',
  className = ''
}) => {
  return (
    <div
      className={`skeleton ${className}`}
      style={{
        width,
        height,
        borderRadius,
        display: 'block'
      }}
    />
  );
};

// 书籍卡片骨架屏
export const BookCardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`premium-card p-0 ${className}`}>
      {/* 封面骨架 */}
      <Skeleton height="160px" borderRadius="16px 16px 0 0" />
      
      <div className="p-6">
        {/* 标题骨架 */}
        <Skeleton height="24px" width="80%" className="mb-3" />
        
        {/* 描述骨架 */}
        <Skeleton height="16px" width="100%" className="mb-2" />
        <Skeleton height="16px" width="60%" className="mb-3" />
        
        {/* 日期骨架 */}
        <Skeleton height="14px" width="40%" />
      </div>
      
      {/* 操作按钮骨架 */}
      <div className="px-6 pb-6 pt-2 flex justify-between items-center border-t border-gray-100 border-opacity-50">
        <div className="flex space-x-1">
          <Skeleton width="32px" height="32px" borderRadius="8px" />
          <Skeleton width="32px" height="32px" borderRadius="8px" />
        </div>
        <Skeleton width="32px" height="32px" borderRadius="8px" />
      </div>
    </div>
  );
};

// 高级加载状态组件
interface LoadingStateProps {
  type?: 'dots' | 'pulse' | 'spin';
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  color?: string;
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'dots',
  size = 'md',
  text,
  color = 'var(--color-primary)',
  className = ''
}) => {
  const renderLoader = () => {
    switch (type) {
      case 'pulse':
        return <PulseLoader size={size} color={color} />;
      case 'spin':
        return <SpinLoader size={size} color={color} />;
      default:
        return <DotsLoader size={size} color={color} />;
    }
  };

  const textSizeMap = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      {renderLoader()}
      {text && (
        <p className={`${textSizeMap[size]} text-gray-600 animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  );
};

// 成功状态组件
export const SuccessState: React.FC<{ 
  text?: string; 
  className?: string;
  onComplete?: () => void;
}> = ({ 
  text = '操作成功', 
  className = '',
  onComplete 
}) => {
  React.useEffect(() => {
    if (onComplete) {
      const timer = setTimeout(onComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [onComplete]);

  return (
    <div className={`flex flex-col items-center gap-3 ${className}`}>
      <div className="success-checkmark" />
      <p className="text-base text-green-600 font-medium">
        {text}
      </p>
    </div>
  );
};

// 错误状态组件
export const ErrorState: React.FC<{ 
  text?: string; 
  className?: string;
  onRetry?: () => void;
}> = ({ 
  text = '操作失败', 
  className = '',
  onRetry 
}) => {
  return (
    <div className={`flex flex-col items-center gap-3 ${className}`}>
      <div 
        className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center error-shake"
        style={{ animation: 'shake 0.5s ease-in-out' }}
      >
        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
      <p className="text-base text-red-600 font-medium">
        {text}
      </p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          重试
        </button>
      )}
    </div>
  );
};

// 进度条组件
interface ProgressBarProps {
  progress: number; // 0-100
  color?: string;
  height?: number;
  animated?: boolean;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = 'var(--color-primary)',
  height = 8,
  animated = true,
  className = ''
}) => {
  return (
    <div 
      className={`w-full bg-gray-200 rounded-full overflow-hidden ${className}`}
      style={{ height: `${height}px` }}
    >
      <div
        className={`h-full rounded-full transition-all duration-500 ease-out ${animated ? 'animate-pulse' : ''}`}
        style={{
          width: `${Math.min(Math.max(progress, 0), 100)}%`,
          background: color,
          transition: 'width 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      />
    </div>
  );
};
