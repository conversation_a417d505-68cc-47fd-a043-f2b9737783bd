import { v4 as uuidv4 } from 'uuid';
import { db, Outline, OutlineVersion } from '../dexie';

export interface IOutlineRepository {
  getAllByBookId(bookId: string): Promise<Outline[]>;
  getById(id: string): Promise<Outline | undefined>;
  getByWorkId(workId: string): Promise<Outline | undefined>;
  create(outline: Omit<Outline, 'id' | 'lastModified' | 'version'>): Promise<string>;
  update(id: string, outline: Partial<Outline>): Promise<void>;
  delete(id: string): Promise<void>;
  getVersions(outlineId: string): Promise<OutlineVersion[]>;
}

export class OutlineRepository implements IOutlineRepository {
  async getAllByBookId(bookId: string): Promise<Outline[]> {
    console.log('🔍 OutlineRepository.getAllByBookId 调用参数:', { bookId });

    try {
      // 在大纲表中，workId 对应 bookId
      const outlines = await db.outlines
        .where('workId')
        .equals(bookId)
        .toArray();

      console.log('📋 找到的大纲数据:', {
        count: outlines.length,
        outlines: outlines.map(o => ({ id: o.id, title: o.title, workId: o.workId, nodesCount: o.nodes?.length || 0 }))
      });

      // 确保每个大纲都有 nodes 字段
      const processedOutlines = outlines.map(outline => ({
        ...outline,
        nodes: outline.nodes || []
      }));

      return processedOutlines;
    } catch (error) {
      console.error('❌ OutlineRepository.getAllByBookId 失败:', error);
      throw error;
    }
  }

  async getById(id: string): Promise<Outline | undefined> {
    try {
      const outline = await db.outlines.get(id);
      if (outline) {
        // 确保 nodes 字段存在
        outline.nodes = outline.nodes || [];
      }
      return outline;
    } catch (error) {
      console.error('获取大纲失败:', error);
      throw error;
    }
  }

  async getByWorkId(workId: string): Promise<Outline | undefined> {
    try {
      const outline = await db.outlines.where('workId').equals(workId).first();
      if (outline) {
        // 确保 nodes 字段存在
        outline.nodes = outline.nodes || [];
      }
      return outline;
    } catch (error) {
      console.error('根据workId获取大纲失败:', error);
      throw error;
    }
  }

  async create(outline: Omit<Outline, 'id' | 'lastModified' | 'version'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();

    const newOutline: Outline = {
      ...outline,
      id,
      lastModified: now,
      version: 1,
      nodes: outline.nodes || []
    };

    await db.outlines.add(newOutline);

    // 创建初始版本
    const version: OutlineVersion = {
      id: `outline-version-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      outlineId: id,
      version: 1,
      data: { ...newOutline },
      createdAt: now,
      createdBy: 'user'
    };

    await db.outlineVersions.add(version);

    return id;
  }

  async update(id: string, outline: Partial<Outline>): Promise<void> {
    const existingOutline = await db.outlines.get(id);
    if (!existingOutline) {
      throw new Error(`Outline with id ${id} not found`);
    }

    const now = new Date();
    const updatedOutline = {
      ...outline,
      lastModified: now,
      version: (existingOutline.version || 0) + 1
    };

    await db.outlines.update(id, updatedOutline);

    // 创建版本历史
    const version: OutlineVersion = {
      id: `outline-version-${Date.now()}`,
      outlineId: id,
      version: updatedOutline.version!,
      data: { ...existingOutline, ...updatedOutline },
      createdAt: now,
      createdBy: 'user'
    };

    await db.outlineVersions.add(version);
  }

  async delete(id: string): Promise<void> {
    await db.transaction('rw', db.outlines, db.outlineVersions, async () => {
      // 删除大纲
      await db.outlines.delete(id);

      // 删除相关版本历史
      await db.outlineVersions.where('outlineId').equals(id).delete();
    });
  }

  async getVersions(outlineId: string): Promise<OutlineVersion[]> {
    return await db.outlineVersions
      .where('outlineId')
      .equals(outlineId)
      .reverse()
      .sortBy('version');
  }
}

// 导出单例实例
export const outlineRepository = new OutlineRepository();
