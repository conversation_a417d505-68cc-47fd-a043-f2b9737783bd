"use client";

import React, { useState, useEffect } from 'react';
import { EdgeProps, getBezierPath, getSmoothStepPath, getMarkerEnd } from 'reactflow';
import { useStyle } from '../styles/StyleContext';

// 边样式接口
export interface EdgeStyle {
  type: 'straight' | 'bezier' | 'step' | 'smoothstep';
  strokeWidth: number;
  strokeColor: string;
  strokeOpacity: number;
  strokeDasharray?: string;
  animated?: boolean;
  animationSpeed?: number;
  arrowHeadType?: 'arrow' | 'arrowclosed' | 'circle' | 'diamond' | 'none';
}

// 根据关系类型获取边样式
export function getEdgeStyleForType(type: string): EdgeStyle {
  switch (type) {
    case 'parent-child':
      return {
        type: 'bezier',
        strokeWidth: 2,
        strokeColor: '#555',
        strokeOpacity: 0.8,
        arrowHeadType: 'none'
      };
    case 'association':
      return {
        type: 'straight',
        strokeWidth: 1.5,
        strokeColor: '#888',
        strokeOpacity: 0.7,
        strokeDasharray: '5,5',
        arrowHeadType: 'none'
      };
    case 'sequence':
      return {
        type: 'smoothstep',
        strokeWidth: 1.5,
        strokeColor: '#666',
        strokeOpacity: 0.8,
        arrowHeadType: 'arrow'
      };
    default:
      return {
        type: 'bezier',
        strokeWidth: 1,
        strokeColor: '#999',
        strokeOpacity: 0.6,
        arrowHeadType: 'none'
      };
  }
}

// 自定义边组件
const CustomEdge: React.FC<EdgeProps> = ({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  ...props
}) => {
  // 使用样式上下文
  const { getEdgeStyle } = useStyle();

  // 根据关系类型确定样式
  const edgeType = data?.relationshipType || 'parent-child';

  // 优先使用传递的样式，如果没有则从上下文获取
  let edgeStyle: EdgeStyle;
  if (data?.edgeStyle) {
    edgeStyle = data.edgeStyle as EdgeStyle;
    //console.LOg('使用传递的边样式:', edgeType, edgeStyle);
  } else {
    edgeStyle = getEdgeStyle(edgeType);
    //console.LOg('使用上下文边样式:', edgeType, edgeStyle);
  }

  // 优先使用传递的样式属性
  if (style) {
    if (style.stroke) edgeStyle.strokeColor = style.stroke as string;
    if (style.strokeWidth) edgeStyle.strokeWidth = style.strokeWidth as number;
    if (style.strokeDasharray) edgeStyle.strokeDasharray = style.strokeDasharray as string;
    if (style.opacity) edgeStyle.strokeOpacity = style.opacity as number;
  }

  // 确保颜色值有效
  if (!edgeStyle.strokeColor || edgeStyle.strokeColor === 'undefined') {
    console.warn('边样式颜色无效，使用默认颜色:', edgeStyle);
    edgeStyle.strokeColor = '#3b82f6'; // 默认蓝色
  }


  // 状态管理
  const [hover, setHover] = useState(false);
  const [pathLength, setPathLength] = useState(0);
  const [animationOffset, setAnimationOffset] = useState(0);

  // 计算路径
  let path = '';
  let markerEnd = '';

  // 根据边类型计算路径
  if (edgeStyle.type === 'bezier') {
    [path] = getBezierPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
      curvature: 0.2,
    });
  } else if (edgeStyle.type === 'smoothstep') {
    [path] = getSmoothStepPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
      borderRadius: 10,
    });
  } else if (edgeStyle.type === 'step') {
    [path] = getSmoothStepPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
      borderRadius: 0,
    });
  } else {
    // 直线
    path = `M ${sourceX},${sourceY} L ${targetX},${targetY}`;
  }

  // 如果需要箭头，添加箭头标记
  if (edgeStyle.arrowHeadType && edgeStyle.arrowHeadType !== 'none') {
    markerEnd = `url(#${edgeStyle.arrowHeadType})`;
    //console.LOg(`应用箭头样式: ${markerEnd}`);
  } else {
    //console.LOg('不应用箭头样式');
  }

  // 计算路径长度（用于动画）
  useEffect(() => {
    if (edgeStyle.animated && path) {
      const pathElement = document.getElementById(`path-${id}`);
      if (pathElement) {
        setPathLength(pathElement.getTotalLength());
      }
    }
  }, [path, id, edgeStyle.animated]);

  // 动画效果
  useEffect(() => {
    if (edgeStyle.animated && pathLength > 0) {
      const animationSpeed = edgeStyle.animationSpeed || 1;
      const animationDuration = 3000 / animationSpeed;
      const step = pathLength / 30; // 每步移动的距离

      const animate = () => {
        setAnimationOffset(prev => (prev + step) % pathLength);
      };

      //console.LOg(`启动边 ${id} 的动画，速度: ${animationSpeed}, 路径长度: ${pathLength}`);
      const intervalId = setInterval(animate, animationDuration / 30);
      return () => {
        //console.LOg(`清除边 ${id} 的动画`);
        clearInterval(intervalId);
      };
    }
  }, [id, pathLength, edgeStyle.animated, edgeStyle.animationSpeed]);

  // 计算悬停和选中时的样式
  const strokeWidth = hover
    ? edgeStyle.strokeWidth + 1
    : selected
      ? edgeStyle.strokeWidth + 0.5
      : edgeStyle.strokeWidth;

  const strokeOpacity = hover
    ? Math.min(edgeStyle.strokeOpacity + 0.2, 1)
    : selected
      ? Math.min(edgeStyle.strokeOpacity + 0.1, 1)
      : edgeStyle.strokeOpacity;

  // 动画样式
  const animatedStyle = edgeStyle.animated && pathLength > 0
    ? {
        strokeDasharray: `${pathLength / 3} ${pathLength / 3 * 2}`,
        strokeDashoffset: animationOffset,
      }
    : {};


  return (
    <g
      className={`react-flow__edge ${edgeType} ${selected ? 'selected' : ''} ${hover ? 'hover' : ''}`}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {/* 边路径 */}
      <path
        id={`path-${id}`}
        className="react-flow__edge-path"
        d={path}
        strokeWidth={strokeWidth}
        stroke={edgeStyle.strokeColor}
        strokeDasharray={edgeStyle.strokeDasharray}
        strokeOpacity={strokeOpacity}
        markerEnd={markerEnd}
        style={{
          ...style,
          ...animatedStyle,
          transition: 'stroke-width 0.2s, stroke-opacity 0.2s',
        }}
      />

      {/* 选中或悬停时的发光效果 */}
      {(selected || hover) && (
        <path
          className="react-flow__edge-path-glow"
          d={path}
          strokeWidth={strokeWidth + 2}
          stroke={edgeStyle.strokeColor}
          strokeOpacity={0.1}
          fill="none"
          style={{
            filter: 'blur(2px)',
          }}
        />
      )}
    </g>
  );
};

export default CustomEdge;
