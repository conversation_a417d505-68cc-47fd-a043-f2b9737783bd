import { db } from './dexie';

/**
 * 初始化数据库
 * 确保数据库连接正常，并在必要时添加示例数据
 */
export const initializeDatabase = async () => {
  try {
    console.log('初始化数据库...');

    // 检查存储配额
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      const usedMB = Math.round((estimate.usage || 0) / 1024 / 1024);
      const quotaMB = Math.round((estimate.quota || 0) / 1024 / 1024);
      console.log(`存储使用情况: ${usedMB}MB / ${quotaMB}MB`);

      // 如果使用率超过80%，发出警告
      if (estimate.usage && estimate.quota && (estimate.usage / estimate.quota) > 0.8) {
        console.warn('⚠️ 存储空间使用率超过80%，建议清理数据或备份重要内容');
      }
    }

    // 打开数据库连接
    await db.open();
    console.log('数据库连接已打开');

    // 检查数据库版本
    console.log('数据库版本:', db.verno);

    // 检查数据库表是否存在
    const tables = db.tables.map(table => table.name);
    console.log('数据库表:', tables);

    // 确保所有表都存在
    const requiredTables = ['books', 'chapters', 'outlines', 'outlineVersions', 'characters', 'worldBuilding', 'terminology', 'worldBookPrefixes'];
    const missingTables = requiredTables.filter(table => !tables.includes(table));

    if (missingTables.length > 0) {
      console.error('缺少必要的数据库表:', missingTables);
      console.log('尝试关闭并重新打开数据库...');
      await db.close();
      await db.open();
      console.log('数据库重新打开完成');
    }

    // 检查大纲表是否为空
    const outlineCount = await db.outlines.count();
    console.log('大纲数量:', outlineCount);

    console.log('数据库初始化完成');
    return true;
  } catch (error) {
    console.error('初始化数据库失败:', error);

    // ⚠️ 重要：不再自动重置数据库，避免数据丢失
    console.error('❌ 数据库初始化失败，请检查浏览器存储权限或清理浏览器缓存后重试');
    console.error('💡 如需重置数据库，请手动调用 resetDatabase() 函数');
    return false;
  }
};

/**
 * 手动重置数据库（危险操作，会清空所有数据）
 * 只有在用户明确确认的情况下才调用此函数
 */
export const resetDatabase = async (): Promise<boolean> => {
  try {
    console.warn('🚨 正在重置数据库，这将清空所有数据...');
    await db.delete();
    console.log('数据库已删除，重新创建...');
    await db.open();
    console.log('数据库重新创建完成');
    return true;
  } catch (resetError) {
    console.error('重置数据库失败:', resetError);
    return false;
  }
};

/**
 * 验证大纲数据
 * 确保大纲数据的完整性
 */
export const validateOutlineData = async (bookId: string) => {
  try {
    console.log('验证大纲数据...');

    // 获取大纲数据
    const outline = await db.outlines.where('workId').equals(bookId).first();

    if (!outline) {
      console.log('未找到大纲数据，创建空大纲');

      // 创建空大纲
      const newOutline = {
        id: `outline-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        workId: bookId,
        title: '大纲',
        nodes: [],
        lastModified: new Date(),
        version: 1
      };

      // 保存大纲
      await db.outlines.add(newOutline);
      console.log('创建空大纲成功');

      return newOutline;
    }

    // 确保nodes字段存在
    if (!outline.nodes) {
      console.log('大纲缺少nodes字段，添加空数组');
      outline.nodes = [];
      await db.outlines.put(outline);
    }

    console.log('大纲数据验证完成');
    return outline;
  } catch (error) {
    console.error('验证大纲数据失败:', error);
    throw error;
  }
};
