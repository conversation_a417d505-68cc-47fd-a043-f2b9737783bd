/**
 * AI请求处理模块
 * 负责处理AI请求发送、流式处理和思考画布生成
 */

import {
  ConversationMessage,
  AIRequestOptions,
  AIResponse,
  OutlineAIResponseExtended,
  ThinkingCanvasOptions,
  ThinkingCanvasData
} from '../types/SharedTypes';

// 导入AI服务提供者
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

export class AIRequestHandler extends UnifiedAIService {
  private static instance: AIRequestHandler;

  private constructor() {
    super(AIServiceType.TOOL_CALL);
  }

  public static getInstance(): AIRequestHandler {
    if (!AIRequestHandler.instance) {
      AIRequestHandler.instance = new AIRequestHandler();
    }
    return AIRequestHandler.instance;
  }

  /**
   * 发送AI请求（非流式）
   */
  async sendRequest(
    messages: ConversationMessage[],
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    try {
      console.log('发送AI请求:', { messageCount: messages.length, options });

      // 构建请求参数
      const requestOptions = {
        max_tokens: options.maxTokens || 4000,
        stream: false
      };

      // 发送请求 - 使用统一的AI调用方法
      const response = await this.callAI(messages, requestOptions);

      if (!response || !response.text) {
        throw new Error('AI服务返回空响应');
      }

      // 解析响应
      const parsedResponse = this.parseAIResponse(response.text);

      return {
        message: parsedResponse.message,
        creativeNotes: parsedResponse.creativeNotes,
        changes: parsedResponse.changes,
        metadata: parsedResponse.metadata,
        success: true
      };

    } catch (error) {
      console.error('AI请求失败:', error);
      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 发送流式AI请求
   */
  async sendStreamRequest(
    messages: ConversationMessage[],
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    try {
      console.log('发送流式AI请求:', { messageCount: messages.length, options });

      // 构建请求参数
      const requestOptions = {
        max_tokens: options.maxTokens || 4000,
        stream: true
      };

      let fullResponse = '';
      let lastProgressTime = Date.now();

      // 发送流式请求 - 使用统一的AI调用方法
      const response = await this.callAIStreaming(
        messages,
        (chunk: string) => {
          fullResponse += chunk;

          // 调用进度回调（限制频率）
          const now = Date.now();
          if (options.onProgress && (now - lastProgressTime > 100)) {
            options.onProgress(chunk);
            lastProgressTime = now;
          }
        },
        requestOptions
      );

      // 最后一次进度回调
      if (options.onProgress) {
        options.onProgress('');
      }

      // 检查响应是否成功
      if (!response.success) {
        throw new Error(response.error || '流式AI请求失败');
      }

      // 使用fullResponse作为最终响应内容
      if (!fullResponse) {
        throw new Error('流式AI服务返回空响应');
      }

      // 解析响应
      const parsedResponse = this.parseAIResponse(fullResponse);

      return {
        message: parsedResponse.message,
        creativeNotes: parsedResponse.creativeNotes,
        changes: parsedResponse.changes,
        metadata: parsedResponse.metadata,
        success: true
      };

    } catch (error) {
      console.error('流式AI请求失败:', error);
      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 生成思考画布
   */
  async generateThinkingCanvas(
    userMessage: string,
    options: ThinkingCanvasOptions
  ): Promise<OutlineAIResponseExtended> {
    try {
      console.log('生成思考画布:', { userMessage, options });

      // 构建思考画布专用消息
      const thinkingMessages = this.buildThinkingCanvasMessages(userMessage, options);

      // 发送AI请求
      const response = await this.sendRequest(thinkingMessages, {
        maxTokens: 2000
      });

      if (!response.success) {
        throw new Error(response.error || '思考画布生成失败');
      }

      // 创建思考画布数据
      const thinkingCanvas: ThinkingCanvasData = {
        id: this.generateThinkingCanvasId(),
        title: this.extractThinkingTitle(response.message),
        content: response.message,
        mode: options.mode,
        createdAt: new Date()
      };

      return {
        ...response,
        thinkingCanvas,
        metadata: {
          ...response.metadata,
          operationType: 'thinking_canvas',
          confidence: 0.8,
          hasThinkingCanvas: true,
          thinkingCanvasId: thinkingCanvas.id,
          workflowStep: 'completed',
          thinkingMode: options.mode
        }
      };

    } catch (error) {
      console.error('思考画布生成失败:', error);
      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '思考画布生成失败',
        metadata: {
          operationType: 'thinking_canvas',
          confidence: 0,
          hasThinkingCanvas: false,
          workflowStep: 'thinking',
          thinkingMode: options.mode
        }
      };
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(content: string): {
    message: string;
    creativeNotes?: string;
    changes?: any[];
    metadata?: any;
  } {
    // 尝试解析JSON格式的响应
    try {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        let jsonText = jsonMatch[1];
        let jsonContent;

        try {
          jsonContent = JSON.parse(jsonText);
        } catch (parseError) {
          // JSON解析失败，尝试修复常见语法错误
          console.log('🔧 JSON解析失败，尝试修复语法错误:', parseError);
          console.log('🔧 原始JSON文本:', jsonText.substring(0, 500) + '...');

          try {
            const fixedJson = this.fixJSONSyntaxErrors(jsonText);
            jsonContent = JSON.parse(fixedJson);
            console.log('✅ JSON语法修复成功');
          } catch (secondError) {
            console.error('🚨 JSON修复失败，使用AIResponseParser进行解析:', secondError);
            // 使用更强大的AIResponseParser进行解析
            const { AIResponseParser } = require('@/utils/ai/AIResponseParser');
            jsonContent = AIResponseParser.parseJSON(jsonText);
            console.log('✅ 使用AIResponseParser解析成功');
          }
        }

        console.log('🔍 解析到的JSON内容:', jsonContent);

        // 🔥 修复：正确处理changes数组
        let changes: any[] | undefined = undefined;

        if (Array.isArray(jsonContent.changes)) {
          // 如果changes是数组，直接使用
          changes = jsonContent.changes;
          console.log('✅ 检测到changes数组，长度:', changes.length);
        } else if (jsonContent.changes) {
          // 如果changes是单个对象，包装成数组
          changes = [jsonContent.changes];
          console.log('✅ 检测到单个changes对象，已包装成数组');
        } else if (jsonContent.title) {
          // 如果没有changes但有title，说明整个jsonContent就是一个节点
          changes = [jsonContent];
          console.log('✅ 检测到节点数据，已包装成changes数组');
        }

        console.log('🔍 最终changes数组:', changes);

        return {
          message: content,
          creativeNotes: jsonContent.creativeNotes,
          changes: changes,
          metadata: {
            hasStructuredData: true,
            nodeData: jsonContent,
            changesCount: changes ? changes.length : 0
          }
        };
      }
    } catch (error) {
      console.log('❌ JSON解析失败:', error);
      console.log('非JSON格式响应，使用文本解析');
    }

    // 文本格式解析
    return {
      message: content,
      metadata: {
        hasStructuredData: false
      }
    };
  }

  /**
   * 修复JSON语法错误
   * 🔥 增强版：集成AIResponseParser的完整修复逻辑
   */
  private fixJSONSyntaxErrors(jsonText: string): string {
    let fixed = jsonText;

    // 修复常见的AI生成错误
    // 1. 修复 "type":. "value" 这种错误（多了点号和引号）
    fixed = fixed.replace(/"([^"]+)"\s*:\s*\.\s*":\s*"([^"]+)"/g, '"$1": "$2"');

    // 2. 修复 "type":- "value" 这种错误（多了减号）
    fixed = fixed.replace(/"([^"]+)"\s*:\s*-\s*"([^"]+)"/g, '"$1": "$2"');

    // 3. 修复多余的冒号（如 "type": ": "value"）
    fixed = fixed.replace(/:\s*":\s*"/g, ': "');

    // 4. 修复数组格式错误
    fixed = fixed.replace(/\[\s*"([^"]*)",\s*\]/g, '["$1"]');
    fixed = fixed.replace(/\[\s*,/g, '[');
    fixed = fixed.replace(/,\s*\]/g, ']');

    // 5. 修复对象格式错误
    fixed = fixed.replace(/{\s*,/g, '{');
    fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

    // 6. 修复引号问题
    fixed = fixed.replace(/'/g, '"');

    // 7. 修复缺失的逗号
    fixed = fixed.replace(/}(\s*){/g, '},$1{');
    fixed = fixed.replace(/](\s*){/g, '],$1{');
    fixed = fixed.replace(/"(\s*)"([^:,}\]]+)":/g, '"$1"$2":');

    // 8. 修复数值格式错误
    fixed = fixed.replace(/:\s*"(\d+)"/g, ': $1');
    fixed = fixed.replace(/:\s*"(true|false)"/g, ': $1');

    // 9. 修复字段名缺少引号
    fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

    // 10. 🔥 新增：修复plotPoints字段特定错误
    // 修复错误的字段名
    fixed = fixed.replace(/"point"(\s*:\s*)/g, '"content"$1');
    fixed = fixed.replace(/"description"(\s*:\s*)/g, '"content"$1');
    fixed = fixed.replace(/"brief"(\s*:\s*)/g, '"content"$1');
    fixed = fixed.replace(/"guidance"(\s*:\s*)/g, '"avoidWriting"$1');
    fixed = fixed.replace(/"writingGuidance"(\s*:\s*)/g, '"avoidWriting"$1');

    // 11. 修复order字段类型错误（确保是数字）
    fixed = fixed.replace(/"order"\s*:\s*"(\d+)"/g, '"order": $1');

    console.log('🔧 增强版JSON语法修复:', jsonText.substring(0, 100), '→', fixed.substring(0, 100));
    return fixed;
  }

  /**
   * 构建思考画布消息
   */
  private buildThinkingCanvasMessages(
    userMessage: string,
    options: ThinkingCanvasOptions
  ): ConversationMessage[] {
    const messages: ConversationMessage[] = [];

    // 系统消息
    messages.push({
      role: 'system',
      content: `你是一个专业的创作思考助手。用户需要你帮助他们深入思考创作问题。

请根据用户的问题进行${options.mode === 'simple' ? '简洁' : options.mode === 'detailed' ? '详细' : '定制化'}的思考分析。

思考要求：
1. 理解用户的核心需求
2. 分析问题的关键要素
3. 提供具体可行的建议
4. 考虑可能的替代方案

请用清晰的结构组织你的思考过程。`
    });

    // 用户消息
    messages.push({
      role: 'user',
      content: userMessage
    });

    // 如果有特定要求，添加额外指导
    if (options.userRequirements) {
      messages.push({
        role: 'user',
        content: `特别要求：${options.userRequirements}`
      });
    }

    if (options.focusAreas && options.focusAreas.length > 0) {
      messages.push({
        role: 'user',
        content: `重点关注领域：${options.focusAreas.join('、')}`
      });
    }

    return messages;
  }

  /**
   * 生成思考画布ID
   */
  private generateThinkingCanvasId(): string {
    return `thinking_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 提取思考标题
   */
  private extractThinkingTitle(content: string): string {
    // 尝试从内容中提取标题
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && trimmed.length < 100) {
        return trimmed;
      }
    }
    return '思考分析';
  }

  /**
   * 验证AI服务可用性
   */
  validateAIService(): boolean {
    // 由于继承了UnifiedAIService，AI服务总是可用的
    return true;
  }

  /**
   * 获取AI服务状态
   */
  getAIServiceStatus(): {
    available: boolean;
    version?: string;
    capabilities?: string[];
  } {
    if (!this.validateAIService()) {
      return { available: false };
    }

    try {
      return {
        available: true,
        version: 'unknown',
        capabilities: ['text_generation', 'streaming']
      };
    } catch (error) {
      return { available: false };
    }
  }
}
