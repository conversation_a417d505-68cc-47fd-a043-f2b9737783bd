/* 关联管理面板样式 */
.association-panel {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.association-panel.collapsed {
  height: 48px;
}

.association-panel.expanded {
  height: auto;
  min-height: 48px;
}

/* 面板头部 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.panel-header:hover {
  background-color: #f1f5f9;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  font-size: 12px;
  color: #6b7280;
  background: white;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* 空状态提示 */
.empty-hint {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.hint-text {
  font-size: 12px;
  color: #9ca3af;
}

.hint-action {
  font-size: 11px;
  color: #3b82f6;
  font-weight: 500;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.expand-button:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.expand-icon {
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 面板内容 */
.panel-content {
  padding: 0 16px 16px 16px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 关联内容卡片区域 */
.association-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
  min-height: 60px;
}

.loading-state {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 关联内容卡片 */
.association-card {
  position: relative;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  animation: cardFlyIn 0.3s ease-out;
  animation-fill-mode: both;
}

@keyframes cardFlyIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  60% {
    transform: scale(1.05) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.association-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.card-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.card-title {
  font-size: 10px;
  color: #374151;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.remove-button {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  transform: scale(0.8);
}

.association-card:hover .remove-button {
  opacity: 1;
  transform: scale(1);
}

.remove-button:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 操作按钮区域 */
.panel-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: #3b82f6;
  color: white;
}

.action-button.primary:hover {
  background: #2563eb;
  filter: drop-shadow(0 0 8px #3b82f6);
}

.action-button.primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  filter: none;
}

.action-button.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.action-button.secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-button.danger {
  background: white;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.action-button.danger:hover {
  background: #fef2f2;
  border-color: #ef4444;
}

.action-button.large {
  padding: 12px 20px;
  font-size: 14px;
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态内容 */
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 20px;
  text-align: center;
  gap: 16px;
}

.empty-illustration {
  color: #d1d5db;
  opacity: 0.8;
}

.empty-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.empty-text p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

/* 空状态操作按钮 */
.empty-actions {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.empty-actions .action-button {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .association-cards {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
  }

  .association-card {
    height: 50px;
    padding: 6px;
  }

  .card-icon {
    font-size: 14px;
  }

  .card-title {
    font-size: 9px;
  }

  .panel-actions {
    flex-direction: column;
    gap: 6px;
  }

  .action-button {
    width: 100%;
    padding: 8px 12px;
  }

  .stats {
    flex-wrap: wrap;
    gap: 4px;
  }

  .stat-item {
    font-size: 11px;
    padding: 1px 4px;
  }
}

/* 卡片移除动画 */
.association-card.removing {
  animation: cardFlyOut 0.3s ease-in forwards;
}

@keyframes cardFlyOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateX(20px);
  }
}

/* 无障碍设计 */
.association-panel:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.action-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.remove-button:focus {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
  opacity: 1;
  transform: scale(1);
}
