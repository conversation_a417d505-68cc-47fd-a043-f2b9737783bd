import React from 'react';

interface DragGridProps {
  isDragging: boolean;
  position?: { x: number; y: number };
  viewport?: { x: number; y: number; zoom: number };
  gridSize?: number;
  gridColor?: string;
  opacity?: number;
}

/**
 * 坐标转换函数：将画布坐标转换为屏幕坐标
 * 修复：使用正确的ReactFlow坐标转换公式
 * 屏幕坐标 = 画布坐标 * 缩放 + viewport偏移
 */
const transformToScreenCoords = (
  canvasPos: { x: number; y: number },
  viewport: { x: number; y: number; zoom: number }
) => {
  return {
    x: canvasPos.x * viewport.zoom + viewport.x,
    y: canvasPos.y * viewport.zoom + viewport.y
  };
};

/**
 * 拖拽网格组件
 * 在用户拖拽节点时显示网格线，提供视觉对齐参考
 */
export const DragGrid: React.FC<DragGridProps> = ({
  isDragging,
  position,
  viewport,
  gridSize = 50,
  gridColor = '#e5e7eb',
  opacity = 0.5
}) => {
  if (!isDragging) return null;

  // 如果有位置和viewport信息，进行坐标转换
  const screenPosition = position && viewport
    ? transformToScreenCoords(position, viewport)
    : position;

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      <svg className="w-full h-full">
        <defs>
          <pattern 
            id="drag-grid" 
            width={gridSize} 
            height={gridSize} 
            patternUnits="userSpaceOnUse"
          >
            <path 
              d={`M ${gridSize} 0 L 0 0 0 ${gridSize}`} 
              fill="none" 
              stroke={gridColor} 
              strokeWidth="1" 
              opacity={opacity}
            />
          </pattern>
          
          {/* 高亮网格线 - 用于显示对齐位置 */}
          <pattern 
            id="highlight-grid" 
            width={gridSize} 
            height={gridSize} 
            patternUnits="userSpaceOnUse"
          >
            <path 
              d={`M ${gridSize} 0 L 0 0 0 ${gridSize}`} 
              fill="none" 
              stroke="#3b82f6" 
              strokeWidth="2" 
              opacity={0.8}
            />
          </pattern>
        </defs>
        
        {/* 基础网格 */}
        <rect width="100%" height="100%" fill="url(#drag-grid)" />
        
        {/* 如果有位置信息，显示高亮的对齐线 */}
        {screenPosition && viewport && (() => {
          // 修复：考虑缩放因子的网格计算
          const gridSizeScaled = gridSize * viewport.zoom;
          const alignedX = Math.round(screenPosition.x / gridSizeScaled) * gridSizeScaled;
          const alignedY = Math.round(screenPosition.y / gridSizeScaled) * gridSizeScaled;

          return (
            <g>
              {/* 垂直对齐线 */}
              <line
                x1={alignedX}
                y1="0"
                x2={alignedX}
                y2="100%"
                stroke="#3b82f6"
                strokeWidth="2"
                opacity="0.6"
                strokeDasharray="5,5"
              />

              {/* 水平对齐线 */}
              <line
                x1="0"
                y1={alignedY}
                x2="100%"
                y2={alignedY}
                stroke="#3b82f6"
                strokeWidth="2"
                opacity="0.6"
                strokeDasharray="5,5"
              />

              {/* 交叉点高亮 */}
              <circle
                cx={alignedX}
                cy={alignedY}
                r="4"
                fill="#3b82f6"
                opacity="0.8"
              />
            </g>
          );
        })()}
      </svg>
      
      {/* 网格信息提示 */}
      {screenPosition && viewport && (() => {
        // 修复：使用正确的网格对齐坐标计算
        const gridSizeScaled = gridSize * viewport.zoom;
        const alignedX = Math.round(screenPosition.x / gridSizeScaled) * gridSizeScaled;
        const alignedY = Math.round(screenPosition.y / gridSizeScaled) * gridSizeScaled;

        return (
          <div className="absolute top-4 left-4 bg-blue-100 border border-blue-300 rounded-lg px-3 py-2 text-sm text-blue-800 shadow-md">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <span>
                网格对齐: ({Math.round(alignedX)}, {Math.round(alignedY)})
              </span>
              <span className="text-xs opacity-75">
                (缩放: {(viewport.zoom * 100).toFixed(0)}%)
              </span>
            </div>
          </div>
        );
      })()}
    </div>
  );
};

/**
 * 磁性吸附工具函数
 * @param position 当前位置
 * @param allNodes 所有节点
 * @param snapDistance 吸附距离阈值
 * @returns 调整后的位置
 */
export function applyMagneticSnapping(
  position: { x: number; y: number },
  allNodes: Array<{ id: string; position: { x: number; y: number } }>,
  snapDistance: number = 20
): { x: number; y: number } {
  let adjustedPosition = { ...position };
  
  for (const node of allNodes) {
    // 水平对齐吸附
    if (Math.abs(position.x - node.position.x) < snapDistance) {
      adjustedPosition.x = node.position.x;
    }
    
    // 垂直对齐吸附
    if (Math.abs(position.y - node.position.y) < snapDistance) {
      adjustedPosition.y = node.position.y;
    }
  }
  
  return adjustedPosition;
}

/**
 * 网格吸附工具函数
 * @param position 当前位置
 * @param gridSize 网格大小
 * @returns 吸附到网格的位置
 */
export function snapToGrid(
  position: { x: number; y: number },
  gridSize: number = 50
): { x: number; y: number } {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize
  };
}

export default DragGrid;
