import React, { useState, useEffect } from 'react';
import { BannedWordCategory, BannedWordsStorageService } from '../../../services/storage/BannedWordsStorageService';
import CreateCategoryDialog from './CreateCategoryDialog';
import AIKeywordExtractorDialog from './AIKeywordExtractorDialog';

// 预设的禁用词汇模板
const BANNED_WORDS_TEMPLATES: BannedWordCategory[] = [
  {
    id: 'time_adverbs',
    name: '时间副词',
    description: '避免使用突兀的时间副词，改善叙述节奏',
    words: ['突然', '忽然', '瞬间', '刹那', '霎时', '顿时', '立刻', '马上', '一下子', '猛地'],
    color: 'bg-red-50 border-red-200 text-red-800'
  },
  {
    id: 'emotion_words',
    name: '情感词汇',
    description: '避免直白的情感表达，用行为和细节展现',
    words: ['激动', '兴奋', '狂欢', '愤怒', '悲伤', '开心', '高兴', '难过'],
    color: 'bg-orange-50 border-orange-200 text-orange-800'
  },
  {
    id: 'description_words',
    name: '描述词汇',
    description: '避免空洞的形容词，用具体细节描述',
    words: ['美丽', '漂亮', '好看', '丑陋', '难看', '很大', '很小', '非常'],
    color: 'bg-yellow-50 border-yellow-200 text-yellow-800'
  },
  {
    id: 'cliche_phrases',
    name: '陈词滥调',
    description: '避免使用过时的套话和陈词滥调',
    words: ['倾国倾城', '沉鱼落雁', '闭月羞花', '美丽动人', '不言而喻', '众所周知', '毫无疑问'],
    color: 'bg-green-50 border-green-200 text-green-800'
  },
  {
    id: 'filler_words',
    name: '填充词汇',
    description: '避免使用无意义的填充词汇',
    words: ['的话', '来说', '而言', '什么的', '之类的', '等等', '诸如此类'],
    color: 'bg-blue-50 border-blue-200 text-blue-800'
  }
];

interface BannedWordsPanelProps {
  onBannedWordsChange: (bannedWordsText: string) => void;
  initialBannedWords?: string;
  currentChatMessages?: Array<{id: string; type: string; content: string; timestamp: Date}>; // 当前聊天消息
}

const BannedWordsPanel: React.FC<BannedWordsPanelProps> = ({
  onBannedWordsChange,
  initialBannedWords = '',
  currentChatMessages = []
}) => {
  const [categories, setCategories] = useState<BannedWordCategory[]>(BANNED_WORDS_TEMPLATES);
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());
  const [customWords, setCustomWords] = useState<string>('');
  const [newWord, setNewWord] = useState<string>('');
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [showCreateDialog, setShowCreateDialog] = useState<boolean>(false);
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false);
  const [editingCategory, setEditingCategory] = useState<BannedWordCategory | null>(null);
  const [showAIExtractorDialog, setShowAIExtractorDialog] = useState<boolean>(false);
  const [categoryWords, setCategoryWords] = useState<Record<string, string[]>>({});
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // 存储服务实例
  const storageService = BannedWordsStorageService.getInstance();

  // 初始化：加载保存的配置
  useEffect(() => {
    const config = storageService.loadConfig();

    // 合并预设分类和自定义分类
    const allCategories = [...BANNED_WORDS_TEMPLATES, ...config.customCategories];
    setCategories(allCategories);

    // 恢复选中状态
    setSelectedCategories(new Set(config.selectedCategories));

    // 恢复自定义词汇
    setCustomWords(config.customWords);

    // 恢复分类的额外词汇
    setCategoryWords(config.categoryWords);

    // 标记初始化完成
    setIsInitialized(true);

    console.log('✅ 已加载禁用词汇配置', {
      selectedCategories: config.selectedCategories,
      customCategories: config.customCategories.length,
      customWords: config.customWords,
      categoryWords: Object.keys(config.categoryWords).length
    });
  }, []);

  // 解析初始禁用词汇
  useEffect(() => {
    if (initialBannedWords) {
      // 这里可以解析现有的禁用词汇表格式
      // 暂时简化处理
    }
  }, [initialBannedWords]);

  // 生成禁用词汇表文本
  const generateBannedWordsText = () => {
    const selectedWords: string[] = [];
    const categoryTexts: string[] = [];

    // 添加选中分类的词汇
    categories.forEach(category => {
      if (selectedCategories.has(category.id)) {
        // 合并预设词汇和用户添加的额外词汇
        const allWords = [
          ...category.words,
          ...(categoryWords[category.id] || [])
        ];

        if (allWords.length > 0) {
          categoryTexts.push(`- ${category.name}：${allWords.join('、')}`);
          selectedWords.push(...allWords);
        }
      }
    });

    // 添加自定义词汇
    const customWordsList = customWords
      .split(/[,，\n]/)
      .map(word => word.trim())
      .filter(word => word.length > 0);

    if (customWordsList.length > 0) {
      categoryTexts.push(`- 自定义词汇：${customWordsList.join('、')}`);
      selectedWords.push(...customWordsList);
    }

    if (selectedWords.length === 0) {
      return '';
    }

    // 生成标准格式
    const bannedWordsText = categoryTexts.length > 1
      ? `[禁用词汇表] += [\n${categoryTexts.join('\n')}\n]`
      : `[禁用词汇表] += [${selectedWords.join(', ')}]`;

    return bannedWordsText;
  };

  // 更新禁用词汇表
  useEffect(() => {
    const bannedWordsText = generateBannedWordsText();
    onBannedWordsChange(bannedWordsText);
  }, [selectedCategories, customWords, categories, categoryWords]);

  // 自动保存配置（只在初始化完成后保存）
  useEffect(() => {
    if (isInitialized) {
      storageService.saveSelectedCategories(Array.from(selectedCategories));
      console.log('💾 已保存选中分类', Array.from(selectedCategories));
    }
  }, [selectedCategories, isInitialized]);

  useEffect(() => {
    if (isInitialized) {
      storageService.saveCustomWords(customWords);
      console.log('💾 已保存自定义词汇');
    }
  }, [customWords, isInitialized]);

  useEffect(() => {
    if (isInitialized) {
      storageService.saveCategoryWords(categoryWords);
      console.log('💾 已保存分类词汇');
    }
  }, [categoryWords, isInitialized]);

  // 切换分类选择
  const toggleCategory = (categoryId: string) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId);
    } else {
      newSelected.add(categoryId);
    }
    setSelectedCategories(newSelected);
  };

  // 添加词汇到指定分类
  const addWordToCategory = (categoryId: string, word: string) => {
    if (!word.trim()) return;

    setCategoryWords(prev => ({
      ...prev,
      [categoryId]: [...(prev[categoryId] || []), word.trim()]
    }));
    setNewWord('');
  };

  // 从分类中删除词汇
  const removeWordFromCategory = (categoryId: string, wordIndex: number) => {
    setCategoryWords(prev => {
      const categoryWordsList = prev[categoryId] || [];
      return {
        ...prev,
        [categoryId]: categoryWordsList.filter((_, index) => index !== wordIndex)
      };
    });
  };

  // 创建自定义分类
  const handleCreateCategory = (categoryData: Omit<BannedWordCategory, 'id' | 'isCustom'>) => {
    const newCategory = storageService.addCustomCategory(categoryData);
    setCategories(prev => [...prev, newCategory]);
  };

  // 编辑自定义分类
  const handleEditCategory = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category?.isCustom) return;

    setEditingCategory(category);
    setShowEditDialog(true);
  };

  // 更新自定义分类
  const handleUpdateCategory = (categoryId: string, categoryData: Omit<BannedWordCategory, 'id' | 'isCustom'>) => {
    const updatedCategory = storageService.updateCustomCategory(categoryId, categoryData);
    if (updatedCategory) {
      setCategories(prev => prev.map(cat =>
        cat.id === categoryId ? updatedCategory : cat
      ));
    }
    setShowEditDialog(false);
    setEditingCategory(null);
  };

  // 删除自定义分类
  const handleDeleteCategory = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category?.isCustom) return;

    if (confirm(`确定要删除分类"${category.name}"吗？`)) {
      storageService.removeCustomCategory(categoryId);
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));

      // 从选中状态中移除
      const newSelected = new Set(selectedCategories);
      newSelected.delete(categoryId);
      setSelectedCategories(newSelected);
    }
  };

  // 处理AI提取的关键词
  const handleAIExtractedKeywords = (keywords: string[]) => {
    if (keywords.length === 0) return;

    // 创建一个"AI提取"分类，如果不存在的话
    let aiCategory = categories.find(cat => cat.name === 'AI提取');

    if (!aiCategory) {
      const newAICategory: Omit<BannedWordCategory, 'id' | 'isCustom'> = {
        name: 'AI提取',
        description: 'AI自动提取的问题词汇',
        words: keywords,
        color: 'bg-purple-50 border-purple-200 text-purple-800'
      };

      const createdCategory = storageService.addCustomCategory(newAICategory);
      setCategories(prev => [...prev, createdCategory]);

      // 自动选中新创建的分类
      setSelectedCategories(prev => new Set([...prev, createdCategory.id]));
    } else {
      // 如果分类已存在，添加到额外词汇中
      setCategoryWords(prev => ({
        ...prev,
        [aiCategory!.id]: [...(prev[aiCategory!.id] || []), ...keywords]
      }));

      // 确保分类被选中
      setSelectedCategories(prev => new Set([...prev, aiCategory!.id]));
    }
  };

  return (
    <div className="space-y-4">
      {/* 说明文字 */}
      <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
        <p className="text-sm text-purple-800">
          🚫 <strong>禁用词汇表：</strong>选择或添加您不希望AI使用的词汇和表达方式。
          AI会严格避免使用这些词汇，并寻找更好的替代表达。
        </p>
      </div>

      {/* 分类管理 */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">禁用词汇分类</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAIExtractorDialog(true)}
              className="px-3 py-1 text-xs bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors flex items-center space-x-1"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span>AI提取</span>
            </button>
            <button
              onClick={() => setShowCreateDialog(true)}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-1"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span>创建分类</span>
            </button>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-3">
          {categories.map((category) => (
            <div key={category.id} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* 分类头部 */}
              <div
                className={`p-3 cursor-pointer transition-colors ${
                  selectedCategories.has(category.id)
                    ? category.color
                    : 'bg-gray-50 hover:bg-gray-100'
                }`}
                onClick={() => toggleCategory(category.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        {selectedCategories.has(category.id) ? (
                          <svg className="w-4 h-4 text-current" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <div className="w-4 h-4 border border-gray-300 rounded"></div>
                        )}
                      </div>
                      <h4 className="text-sm font-medium flex items-center space-x-2">
                        <span>{category.name}</span>
                        {category.isCustom && (
                          <span className="px-1 py-0.5 text-xs bg-blue-100 text-blue-600 rounded">自定义</span>
                        )}
                      </h4>
                      <span className="text-xs text-gray-500">
                        ({(category.words.length + (categoryWords[category.id]?.length || 0))}个)
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{category.description}</p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {category.isCustom && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditCategory(category.id);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                          title="编辑自定义分类"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCategory(category.id);
                          }}
                          className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                          title="删除自定义分类"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setActiveCategory(activeCategory === category.id ? '' : category.id);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                      title="管理词汇"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* 词汇列表（展开时显示） */}
              {activeCategory === category.id && (
                <div className="p-3 border-t border-gray-200 bg-white">
                  {/* 预设词汇 */}
                  {category.words.length > 0 && (
                    <div className="mb-3">
                      <h5 className="text-xs font-medium text-gray-600 mb-2">预设词汇</h5>
                      <div className="flex flex-wrap gap-2">
                        {category.words.map((word, index) => (
                          <span
                            key={`preset-${index}`}
                            className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                          >
                            {word}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 用户添加的词汇 */}
                  {(categoryWords[category.id]?.length || 0) > 0 && (
                    <div className="mb-3">
                      <h5 className="text-xs font-medium text-gray-600 mb-2">添加的词汇</h5>
                      <div className="flex flex-wrap gap-2">
                        {(categoryWords[category.id] || []).map((word, index) => (
                          <span
                            key={`custom-${index}`}
                            className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                          >
                            {word}
                            <button
                              onClick={() => removeWordFromCategory(category.id, index)}
                              className="ml-1 text-gray-400 hover:text-red-500 transition-colors"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newWord}
                      onChange={(e) => setNewWord(e.target.value)}
                      placeholder="添加新词汇..."
                      className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          addWordToCategory(category.id, newWord);
                        }
                      }}
                    />
                    <button
                      onClick={() => addWordToCategory(category.id, newWord)}
                      disabled={!newWord.trim()}
                      className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    >
                      添加
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 自定义词汇 */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">自定义词汇</h3>
        <div className="space-y-2">
          <textarea
            value={customWords}
            onChange={(e) => setCustomWords(e.target.value)}
            placeholder="输入自定义的禁用词汇，用逗号或换行分隔..."
            className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none text-sm"
            rows={3}
          />
          <p className="text-xs text-gray-500">
            💡 提示：可以用逗号、中文逗号或换行来分隔多个词汇
          </p>
        </div>
      </div>

      {/* 预览 */}
      {(selectedCategories.size > 0 || customWords.trim()) && (
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">生成的禁用词汇表</h3>
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <pre className="text-xs text-gray-700 whitespace-pre-wrap">
              {generateBannedWordsText()}
            </pre>
          </div>
        </div>
      )}

      {/* 创建分类对话框 */}
      <CreateCategoryDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onCreateCategory={handleCreateCategory}
      />

      {/* 编辑分类对话框 */}
      <CreateCategoryDialog
        isOpen={showEditDialog}
        onClose={() => {
          setShowEditDialog(false);
          setEditingCategory(null);
        }}
        onCreateCategory={handleCreateCategory}
        onUpdateCategory={handleUpdateCategory}
        editingCategory={editingCategory}
      />

      {/* AI提取关键词对话框 */}
      <AIKeywordExtractorDialog
        isOpen={showAIExtractorDialog}
        onClose={() => setShowAIExtractorDialog(false)}
        onAddKeywords={handleAIExtractedKeywords}
        chatContent="" // 这里可以传入当前聊天内容
        allChatMessages={currentChatMessages.map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))}
      />
    </div>
  );
};

export default BannedWordsPanel;
