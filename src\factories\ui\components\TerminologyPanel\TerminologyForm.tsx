"use client";

import React, { useState, useEffect } from 'react';
import { Terminology, Character, WorldBuilding } from '@/lib/db/dexie';
import { CustomDropdown } from './CustomDropdown';
import { CharacterRelationSelector } from './CharacterRelationSelector';
import { WorldBuildingRelationSelector } from './WorldBuildingRelationSelector';

// 术语关联选择组件
interface TerminologyRelationSelectorProps {
  allTerminologies: Terminology[];
  selectedIds: string[];
  onChange: (ids: string[]) => void;
  currentTerminologyId?: string;
}

const TerminologyRelationSelector: React.FC<TerminologyRelationSelectorProps> = ({
  allTerminologies,
  selectedIds,
  onChange,
  currentTerminologyId
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // 过滤掉当前术语和已选择的术语
  const availableTerminologies = allTerminologies
    .filter(t => t.id !== currentTerminologyId)
    .filter(t => {
      if (searchQuery.trim() === '') return true;
      return t.name.toLowerCase().includes(searchQuery.toLowerCase());
    });

  const handleToggleTerminology = (id: string) => {
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter(sid => sid !== id));
    } else {
      onChange([...selectedIds, id]);
    }
  };

  return (
    <div className="mt-2">
      <div className="mb-2">
        <input
          type="text"
          placeholder="搜索术语..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            borderColor: 'rgba(139, 69, 19, 0.2)'
          }}
        />
      </div>

      <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md" style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}>
        {availableTerminologies.length === 0 ? (
          <div className="p-2 text-gray-500 text-center">没有找到匹配的术语</div>
        ) : (
          <div className="divide-y divide-gray-100">
            {availableTerminologies.map(term => (
              <div
                key={term.id}
                className="p-2 flex items-center hover:bg-gray-50 cursor-pointer"
                onClick={() => term.id && handleToggleTerminology(term.id)}
              >
                <input
                  type="checkbox"
                  checked={selectedIds.includes(term.id || '')}
                  onChange={() => {}} // 通过父元素的onClick处理
                  className="mr-2 h-4 w-4"
                />
                <div>
                  <span className="text-sm font-medium" style={{ color: 'var(--color-primary)' }}>{term.name}</span>
                  {term.category && (
                    <span className="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-gray-100 text-gray-600">
                      {term.category}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {selectedIds.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {selectedIds.map(id => {
            const term = allTerminologies.find(t => t.id === id);
            if (!term) return null;

            return (
              <div
                key={id}
                className="flex items-center bg-blue-50 px-2 py-0.5 rounded-full text-xs"
                style={{ backgroundColor: 'rgba(139, 69, 19, 0.1)', color: 'var(--color-primary)' }}
              >
                <span>{term.name}</span>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleTerminology(id);
                  }}
                  className="ml-1 text-gray-500 hover:text-red-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

interface TerminologyFormProps {
  terminology: Terminology;
  bookId: string;
  isNew: boolean;
  onSave: (terminology: Terminology) => void;
  onCancel: () => void;
  hideButtons?: boolean;
  terminologies?: Terminology[]; // 所有术语列表，用于关联选择
  characters?: Character[]; // 所有人物列表，用于关联选择
  worldBuildings?: WorldBuilding[]; // 所有世界观列表，用于关联选择
  onFieldUpdate?: (field: string, fieldLabel: string, currentValue: string) => void;
  onContentEnhance?: () => void;
}

/**
 * 术语表单组件
 */
export const TerminologyForm: React.FC<TerminologyFormProps> = ({
  terminology,
  bookId,
  isNew,
  onSave,
  onCancel,
  hideButtons = false,
  terminologies = [],
  characters = [],
  worldBuildings = [],
  onFieldUpdate,
  onContentEnhance
}) => {
  // 表单状态
  const [formData, setFormData] = useState<Terminology>({
    ...terminology,
    bookId,
    attributes: terminology.attributes || {}
  });

  // 别名数组状态
  const [aliasInput, setAliasInput] = useState('');
  const [aliases, setAliases] = useState<string[]>(terminology.alias || []);

  // 关联术语状态
  const [relatedTerminologyIds, setRelatedTerminologyIds] = useState<string[]>(
    terminology.relatedTerminologyIds || []
  );

  // 关联人物状态
  const [relatedCharacterIds, setRelatedCharacterIds] = useState<string[]>(
    terminology.relatedCharacterIds || []
  );

  // 关联世界观状态
  const [relatedWorldBuildingIds, setRelatedWorldBuildingIds] = useState<string[]>(
    terminology.relatedWorldBuildingIds || []
  );

  // 当术语变化时，更新表单数据
  useEffect(() => {
    setFormData({
      ...terminology,
      bookId,
      attributes: terminology.attributes || {}
    });
    setAliases(terminology.alias || []);
    setRelatedTerminologyIds(terminology.relatedTerminologyIds || []);
    setRelatedCharacterIds(terminology.relatedCharacterIds || []);
    setRelatedWorldBuildingIds(terminology.relatedWorldBuildingIds || []);
  }, [terminology, bookId]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('attributes.')) {
      const attributeName = name.split('.')[1];
      setFormData({
        ...formData,
        attributes: {
          ...formData.attributes,
          [attributeName]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // 添加别名
  const addAlias = () => {
    if (aliasInput.trim()) {
      const newAliases = [...aliases, aliasInput.trim()];
      setAliases(newAliases);
      setFormData({
        ...formData,
        alias: newAliases
      });
      setAliasInput('');
    }
  };

  // 删除别名
  const removeAlias = (index: number) => {
    const newAliases = aliases.filter((_, i) => i !== index);
    setAliases(newAliases);
    setFormData({
      ...formData,
      alias: newAliases
    });
  };

  // 处理关联术语变更
  const handleRelatedTerminologiesChange = (ids: string[]) => {
    setRelatedTerminologyIds(ids);
    setFormData({
      ...formData,
      relatedTerminologyIds: ids
    });
  };

  // 处理关联人物变更
  const handleRelatedCharactersChange = (ids: string[]) => {
    setRelatedCharacterIds(ids);
    setFormData({
      ...formData,
      relatedCharacterIds: ids
    });
  };

  // 处理关联世界观变更
  const handleRelatedWorldBuildingsChange = (ids: string[]) => {
    setRelatedWorldBuildingIds(ids);
    setFormData({
      ...formData,
      relatedWorldBuildingIds: ids
    });
  };

  // 处理保存
  const handleSave = async () => {
    // 验证必填字段
    if (!formData.name.trim()) {
      alert('请输入术语名称');
      return;
    }

    // 确保术语有必要的属性
    const terminologyToSave: Partial<Terminology> = {
      ...formData,
      name: formData.name.trim(),
      category: formData.category || '',
      description: formData.description || '',
      attributes: {
        ...formData.attributes,
        importance: formData.attributes?.importance || '3'
      },
      relatedTerminologyIds,
      relatedCharacterIds,
      relatedWorldBuildingIds,
      updatedAt: new Date()
    };

    // 添加调试日志
    console.log('保存术语数据:', terminologyToSave);

    try {
      // 处理双向关联 - 人物
      if (terminology.id) {
        // 获取当前关联的人物ID列表
        const currentCharacterIds = terminology.relatedCharacterIds || [];

        // 需要添加关联的人物ID列表
        const characterIdsToAdd = relatedCharacterIds.filter(id => !currentCharacterIds.includes(id));

        // 需要移除关联的人物ID列表
        const characterIdsToRemove = currentCharacterIds.filter(id => !relatedCharacterIds.includes(id));

        // 导入characterRepository
        const { characterRepository } = await import('@/lib/db/repositories');

        // 为每个新增的人物添加关联
        for (const characterId of characterIdsToAdd) {
          const character = await characterRepository.getById(characterId);
          if (character) {
            const relatedTerminologyIds = [...(character.relatedTerminologyIds || [])];
            if (!relatedTerminologyIds.includes(terminology.id)) {
              relatedTerminologyIds.push(terminology.id);
              await characterRepository.update(characterId, {
                relatedTerminologyIds
              });
              console.log(`已为人物 ${character.name} 添加术语关联`);
            }
          }
        }

        // 为每个移除的人物删除关联
        for (const characterId of characterIdsToRemove) {
          const character = await characterRepository.getById(characterId);
          if (character) {
            const relatedTerminologyIds = (character.relatedTerminologyIds || [])
              .filter(id => id !== terminology.id);
            await characterRepository.update(characterId, {
              relatedTerminologyIds
            });
            console.log(`已为人物 ${character.name} 移除术语关联`);
          }
        }

        // 处理双向关联 - 世界观
        // 获取当前关联的世界观ID列表
        const currentWorldBuildingIds = terminology.relatedWorldBuildingIds || [];

        // 需要添加关联的世界观ID列表
        const worldBuildingIdsToAdd = relatedWorldBuildingIds.filter(id => !currentWorldBuildingIds.includes(id));

        // 需要移除关联的世界观ID列表
        const worldBuildingIdsToRemove = currentWorldBuildingIds.filter(id => !relatedWorldBuildingIds.includes(id));

        // 导入worldBuildingRepository
        const { worldBuildingRepository } = await import('@/lib/db/repositories');

        // 为每个新增的世界观添加关联
        for (const worldBuildingId of worldBuildingIdsToAdd) {
          const worldBuilding = await worldBuildingRepository.getById(worldBuildingId);
          if (worldBuilding) {
            const relatedTerminologyIds = [...(worldBuilding.relatedTerminologyIds || [])];
            if (!relatedTerminologyIds.includes(terminology.id)) {
              relatedTerminologyIds.push(terminology.id);
              await worldBuildingRepository.update(worldBuildingId, {
                relatedTerminologyIds
              });
              console.log(`已为世界观 ${worldBuilding.name} 添加术语关联`);
            }
          }
        }

        // 为每个移除的世界观删除关联
        for (const worldBuildingId of worldBuildingIdsToRemove) {
          const worldBuilding = await worldBuildingRepository.getById(worldBuildingId);
          if (worldBuilding) {
            const relatedTerminologyIds = (worldBuilding.relatedTerminologyIds || [])
              .filter(id => id !== terminology.id);
            await worldBuildingRepository.update(worldBuildingId, {
              relatedTerminologyIds
            });
            console.log(`已为世界观 ${worldBuilding.name} 移除术语关联`);
          }
        }
      }
    } catch (error) {
      console.error('处理双向关联时出错:', error);
    }

    // 保存术语
    onSave(terminologyToSave as Terminology);
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
        <p className="text-sm text-blue-700">
          <span className="font-medium">提示：</span> 术语是构成小说世界观的关键元素，包括特殊物品、能力系统、组织势力、地理区域、核心概念等。详细记录这些术语不仅有助于保持世界观的一致性，还可以作为AI模型的上下文提示，提升创作质量。
        </p>
      </div>

      {/* 两列布局的表单 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 左列 - 基本信息 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
          <div className="space-y-4">
            {/* 名称 */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入术语名称"
                required
              />
            </div>

            {/* 类别 - 使用CustomDropdown组件 */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                类别
              </label>
              <CustomDropdown
                options={[
                  { value: '', label: '选择类别' },
                  { value: 'item', label: '物品/道具' },
                  { value: 'skill', label: '技能/能力' },
                  { value: 'organization', label: '组织/势力' },
                  { value: 'location', label: '地点/区域' },
                  { value: 'concept', label: '概念/规则' },
                  { value: 'event', label: '事件/历史' },
                  { value: 'system', label: '系统/机制' },
                  { value: 'creature', label: '生物/种族' },
                  { value: 'other', label: '其他' }
                ]}
                value={formData.category}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    category: value
                  });
                }}
                className="w-full"
                placeholder="选择类别"
                maxHeight={250}
                name="category"
              />
            </div>

            {/* 重要性 */}
            <div>
              <label htmlFor="attributes.importance" className="block text-sm font-medium text-gray-700 mb-1">
                重要性
              </label>
              <CustomDropdown
                options={[
                  { value: '', label: '选择重要性' },
                  { value: '1', label: '⭐ 次要术语 - 背景补充' },
                  { value: '2', label: '⭐⭐ 支持术语 - 丰富世界观' },
                  { value: '3', label: '⭐⭐⭐ 重要术语 - 影响情节' },
                  { value: '4', label: '⭐⭐⭐⭐ 核心术语 - 关键设定' },
                  { value: '5', label: '⭐⭐⭐⭐⭐ 关键术语 - 世界基石' }
                ]}
                value={formData.attributes?.importance || ''}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    attributes: {
                      ...formData.attributes,
                      importance: value
                    }
                  });
                }}
                className="w-full"
                placeholder="选择重要性"
                maxHeight={250}
                name="attributes.importance"
              />
            </div>

            {/* 别名 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                别名
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={aliasInput}
                  onChange={(e) => setAliasInput(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="输入别名"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addAlias();
                    }
                  }}
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
                <button
                  type="button"
                  onClick={addAlias}
                  className="px-4 py-2 text-white rounded-r-md transition-colors"
                  style={{ backgroundColor: 'var(--color-primary)' }}
                >
                  添加
                </button>
              </div>
              {aliases.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {aliases.map((alias, index) => (
                    <div
                      key={index}
                      className="flex items-center bg-gray-100 px-3 py-1 rounded-full"
                      style={{ backgroundColor: 'rgba(210, 180, 140, 0.2)' }}
                    >
                      <span className="text-sm" style={{ color: 'var(--color-primary)' }}>{alias}</span>
                      <button
                        type="button"
                        onClick={() => removeAlias(index)}
                        className="ml-2 text-gray-500 hover:text-red-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右列 - 详细信息 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">详细信息</h3>
          <div className="space-y-4">
            {/* 描述 */}
            <div>
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  描述
                </label>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => onFieldUpdate && onFieldUpdate('description', '描述', formData.description)}
                    className="px-2 py-1 text-xs rounded-md transition-colors flex items-center"
                    style={{
                      backgroundColor: 'var(--color-secondary)',
                      color: 'white',
                      boxShadow: '0 1px 3px rgba(210, 180, 140, 0.2)'
                    }}
                  >
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                    单字段更新
                  </button>
                  <button
                    type="button"
                    onClick={() => onContentEnhance && onContentEnhance()}
                    className="px-2 py-1 text-xs rounded-md transition-colors flex items-center"
                    style={{
                      backgroundColor: 'var(--color-primary)',
                      color: 'white',
                      boxShadow: '0 1px 3px rgba(139, 69, 19, 0.2)'
                    }}
                  >
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    内容补充
                  </button>
                </div>
              </div>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="详细描述这个术语的含义、特点和在故事中的作用..."
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderColor: 'rgba(139, 69, 19, 0.2)'
                }}
              />
            </div>

            {/* 根据类别显示不同的字段 */}
            {formData.category === 'item' && (
              <div>
                <label htmlFor="attributes.itemProperties" className="block text-sm font-medium text-gray-700 mb-1">
                  物品属性
                </label>
                <textarea
                  id="attributes.itemProperties"
                  name="attributes.itemProperties"
                  value={formData.attributes?.itemProperties || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述物品的外观、材质、功能、来源、稀有度等特性..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'skill' && (
              <div>
                <label htmlFor="attributes.skillEffects" className="block text-sm font-medium text-gray-700 mb-1">
                  技能效果
                </label>
                <textarea
                  id="attributes.skillEffects"
                  name="attributes.skillEffects"
                  value={formData.attributes?.skillEffects || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述技能的效果、使用条件、消耗、冷却、限制等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'organization' && (
              <div>
                <label htmlFor="attributes.organizationStructure" className="block text-sm font-medium text-gray-700 mb-1">
                  组织结构
                </label>
                <textarea
                  id="attributes.organizationStructure"
                  name="attributes.organizationStructure"
                  value={formData.attributes?.organizationStructure || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述组织的结构、成员、势力范围、目标、历史等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'location' && (
              <div>
                <label htmlFor="attributes.locationFeatures" className="block text-sm font-medium text-gray-700 mb-1">
                  地点特征
                </label>
                <textarea
                  id="attributes.locationFeatures"
                  name="attributes.locationFeatures"
                  value={formData.attributes?.locationFeatures || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述地点的地理特征、气候、文化、居民、特产等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'concept' && (
              <div>
                <label htmlFor="attributes.conceptDetails" className="block text-sm font-medium text-gray-700 mb-1">
                  概念详情
                </label>
                <textarea
                  id="attributes.conceptDetails"
                  name="attributes.conceptDetails"
                  value={formData.attributes?.conceptDetails || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述概念的定义、范围、影响、相关理论等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'system' && (
              <div>
                <label htmlFor="attributes.systemMechanics" className="block text-sm font-medium text-gray-700 mb-1">
                  系统机制
                </label>
                <textarea
                  id="attributes.systemMechanics"
                  name="attributes.systemMechanics"
                  value={formData.attributes?.systemMechanics || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述系统的运作机制、规则、等级、评价标准等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {formData.category === 'creature' && (
              <div>
                <label htmlFor="attributes.creatureTraits" className="block text-sm font-medium text-gray-700 mb-1">
                  生物特征
                </label>
                <textarea
                  id="attributes.creatureTraits"
                  name="attributes.creatureTraits"
                  value={formData.attributes?.creatureTraits || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="描述生物的外观、习性、能力、栖息地、社会结构等..."
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              </div>
            )}

            {/* 关联术语 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关联术语
              </label>
              <TerminologyRelationSelector
                allTerminologies={terminologies}
                selectedIds={relatedTerminologyIds}
                onChange={handleRelatedTerminologiesChange}
                currentTerminologyId={terminology.id}
              />
            </div>

            {/* 关联人物 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关联人物
              </label>
              <CharacterRelationSelector
                allCharacters={characters}
                selectedIds={relatedCharacterIds}
                onChange={handleRelatedCharactersChange}
                currentTerminologyId={terminology.id}
              />
            </div>

            {/* 关联世界观 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关联世界观
              </label>
              <WorldBuildingRelationSelector
                allWorldBuildings={worldBuildings}
                selectedIds={relatedWorldBuildingIds}
                onChange={handleRelatedWorldBuildingsChange}
                currentTerminologyId={terminology.id}
              />
            </div>

            {/* 备注 */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                备注
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="其他需要记录的信息..."
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderColor: 'rgba(139, 69, 19, 0.2)'
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 按钮 */}
      {!hideButtons && (
        <div className="flex justify-end space-x-2 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            id="terminology-form-save-button"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            保存
          </button>
        </div>
      )}
    </form>
  );
};
