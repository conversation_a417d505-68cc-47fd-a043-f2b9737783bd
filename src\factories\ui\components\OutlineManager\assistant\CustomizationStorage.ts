/**
 * 定制化选择存储管理
 */

export interface SavedCustomization {
  id: string;
  name: string;
  description: string;
  customizations: {
    [categoryId: string]: {
      optionId: string;
      optionLabel: string;
      optionDescription: string;
    }
  };
  createdAt: Date;
  usageCount: number;
  lastUsedAt: Date;
}

export class CustomizationStorage {
  private static readonly STORAGE_KEY = 'synopsis_ai_customizations';

  /**
   * 保存定制化选择
   */
  static save(name: string, description: string, customizations: { [categoryId: string]: { optionId: string; optionLabel: string; optionDescription: string; } }): SavedCustomization {
    const saved: SavedCustomization = {
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      customizations,
      createdAt: new Date(),
      usageCount: 0,
      lastUsedAt: new Date()
    };

    const existing = this.getAll();
    existing.push(saved);

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(existing));
    return saved;
  }

  /**
   * 获取所有保存的定制化选择
   */
  static getAll(): SavedCustomization[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const parsed = JSON.parse(stored);
      return parsed.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt),
        lastUsedAt: new Date(item.lastUsedAt)
      }));
    } catch (error) {
      console.error('获取保存的定制化选择失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取定制化选择
   */
  static getById(id: string): SavedCustomization | null {
    const all = this.getAll();
    return all.find(item => item.id === id) || null;
  }

  /**
   * 更新使用统计
   */
  static updateUsage(id: string): void {
    const all = this.getAll();
    const index = all.findIndex(item => item.id === id);

    if (index !== -1) {
      all[index].usageCount += 1;
      all[index].lastUsedAt = new Date();
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(all));
    }
  }

  /**
   * 删除定制化选择
   */
  static delete(id: string): boolean {
    const all = this.getAll();
    const filtered = all.filter(item => item.id !== id);

    if (filtered.length !== all.length) {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filtered));
      return true;
    }
    return false;
  }

  /**
   * 搜索定制化选择
   */
  static search(query: string): SavedCustomization[] {
    const all = this.getAll();
    const lowerQuery = query.toLowerCase();

    return all.filter(item =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 获取最近使用的定制化选择
   */
  static getRecent(limit: number = 5): SavedCustomization[] {
    const all = this.getAll();
    return all
      .sort((a, b) => {
        const aTime = a.lastUsedAt instanceof Date ? a.lastUsedAt.getTime() : new Date(a.lastUsedAt).getTime();
        const bTime = b.lastUsedAt instanceof Date ? b.lastUsedAt.getTime() : new Date(b.lastUsedAt).getTime();
        return bTime - aTime;
      })
      .slice(0, limit);
  }

  /**
   * 获取最常用的定制化选择
   */
  static getPopular(limit: number = 5): SavedCustomization[] {
    const all = this.getAll();
    return all
      .filter(item => item.usageCount > 0)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit);
  }
}
