"use client";

import React from 'react';

interface PaperTextureBackgroundProps {
  isActive: boolean;
  intensity?: 'low' | 'medium' | 'high';
}

/**
 * 纸张纹理背景组件
 * 创建subtle的纸张质感，带有轻微的呼吸效果
 */
export const PaperTextureBackground: React.FC<PaperTextureBackgroundProps> = ({
  isActive,
  intensity = 'medium'
}) => {
  // 根据强度调整透明度
  const getBaseOpacity = () => {
    switch (intensity) {
      case 'low': return 0.02;
      case 'medium': return 0.03;
      case 'high': return 0.05;
      default: return 0.03;
    }
  };

  // 根据激活状态调整动画速度
  const getAnimationDuration = () => {
    return isActive ? '4s' : '6s';
  };

  return (
    <div className="absolute inset-0 pointer-events-none rounded-xl overflow-hidden">
      <svg
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 200 200"
        style={{
          opacity: getBaseOpacity(),
          animation: `paperBreathe ${getAnimationDuration()} ease-in-out infinite`,
          transition: 'opacity 0.5s ease-in-out'
        }}
      >
        <defs>
          {/* 纸张纹理图案 */}
          <pattern
            id="paperTexture"
            x="0"
            y="0"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            {/* 基础纸张纹理 - 细小的点和线条 */}
            <rect width="20" height="20" fill="transparent" />
            
            {/* 随机分布的小点 */}
            <circle cx="3" cy="5" r="0.3" fill="#8B7355" opacity="0.6" />
            <circle cx="12" cy="8" r="0.2" fill="#8B7355" opacity="0.4" />
            <circle cx="7" cy="15" r="0.25" fill="#8B7355" opacity="0.5" />
            <circle cx="16" cy="3" r="0.2" fill="#8B7355" opacity="0.3" />
            <circle cx="2" cy="12" r="0.3" fill="#8B7355" opacity="0.7" />
            <circle cx="18" cy="17" r="0.2" fill="#8B7355" opacity="0.4" />
            <circle cx="9" cy="2" r="0.25" fill="#8B7355" opacity="0.5" />
            <circle cx="14" cy="19" r="0.2" fill="#8B7355" opacity="0.6" />
            
            {/* 细微的纤维线条 */}
            <line x1="1" y1="7" x2="4" y2="9" stroke="#8B7355" strokeWidth="0.1" opacity="0.3" />
            <line x1="8" y1="1" x2="11" y2="4" stroke="#8B7355" strokeWidth="0.1" opacity="0.4" />
            <line x1="15" y1="6" x2="19" y2="8" stroke="#8B7355" strokeWidth="0.1" opacity="0.3" />
            <line x1="5" y1="13" x2="8" y2="16" stroke="#8B7355" strokeWidth="0.1" opacity="0.5" />
            <line x1="11" y1="11" x2="14" y2="14" stroke="#8B7355" strokeWidth="0.1" opacity="0.3" />
            <line x1="2" y1="18" x2="6" y2="19" stroke="#8B7355" strokeWidth="0.1" opacity="0.4" />
            
            {/* 更细微的纹理点 */}
            <circle cx="5" cy="10" r="0.15" fill="#A0956B" opacity="0.3" />
            <circle cx="13" cy="6" r="0.1" fill="#A0956B" opacity="0.4" />
            <circle cx="8" cy="18" r="0.12" fill="#A0956B" opacity="0.3" />
            <circle cx="17" cy="12" r="0.1" fill="#A0956B" opacity="0.5" />
          </pattern>

          {/* 更大的纹理图案用于变化 */}
          <pattern
            id="paperTextureVariant"
            x="0"
            y="0"
            width="25"
            height="25"
            patternUnits="userSpaceOnUse"
          >
            <rect width="25" height="25" fill="transparent" />
            
            {/* 不同位置的纹理元素 */}
            <circle cx="4" cy="7" r="0.25" fill="#8B7355" opacity="0.5" />
            <circle cx="15" cy="12" r="0.3" fill="#8B7355" opacity="0.4" />
            <circle cx="9" cy="20" r="0.2" fill="#8B7355" opacity="0.6" />
            <circle cx="21" cy="5" r="0.25" fill="#8B7355" opacity="0.3" />
            <circle cx="6" cy="16" r="0.2" fill="#8B7355" opacity="0.5" />
            
            <line x1="2" y1="10" x2="6" y2="13" stroke="#8B7355" strokeWidth="0.1" opacity="0.4" />
            <line x1="12" y1="3" x2="16" y2="6" stroke="#8B7355" strokeWidth="0.1" opacity="0.3" />
            <line x1="18" y1="15" x2="23" y2="18" stroke="#8B7355" strokeWidth="0.1" opacity="0.5" />
          </pattern>
        </defs>

        {/* 主纹理层 */}
        <rect
          width="100%"
          height="100%"
          fill="url(#paperTexture)"
          style={{
            animation: `textureShift ${getAnimationDuration()} ease-in-out infinite`
          }}
        />

        {/* 变化纹理层 */}
        <rect
          width="100%"
          height="100%"
          fill="url(#paperTextureVariant)"
          style={{
            animation: `textureShiftAlt ${getAnimationDuration()} ease-in-out infinite`,
            animationDelay: `${parseFloat(getAnimationDuration()) / 2}s`
          }}
        />

        <style>
          {`
            @keyframes paperBreathe {
              0%, 100% {
                opacity: ${getBaseOpacity()};
                transform: scale(1);
              }
              50% {
                opacity: ${getBaseOpacity() * 1.5};
                transform: scale(1.002);
              }
            }
            
            @keyframes textureShift {
              0%, 100% {
                transform: translate(0, 0);
                opacity: 1;
              }
              25% {
                transform: translate(0.5px, 0.3px);
                opacity: 0.8;
              }
              50% {
                transform: translate(0.3px, 0.7px);
                opacity: 0.9;
              }
              75% {
                transform: translate(0.7px, 0.2px);
                opacity: 0.7;
              }
            }
            
            @keyframes textureShiftAlt {
              0%, 100% {
                transform: translate(0, 0);
                opacity: 0.6;
              }
              25% {
                transform: translate(-0.3px, 0.5px);
                opacity: 0.8;
              }
              50% {
                transform: translate(-0.6px, -0.2px);
                opacity: 0.4;
              }
              75% {
                transform: translate(-0.2px, -0.6px);
                opacity: 0.7;
              }
            }
            
            @media (prefers-reduced-motion: reduce) {
              svg {
                animation: none !important;
                opacity: ${getBaseOpacity()} !important;
                transform: scale(1) !important;
              }
              
              rect {
                animation: none !important;
                transform: translate(0, 0) !important;
                opacity: 1 !important;
              }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default PaperTextureBackground;
