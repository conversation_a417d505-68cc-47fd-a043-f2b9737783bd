"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BrainstormResult, AssociationData } from './types';
import './BookTitleGenerator.css';
import {
  createBookTitleGenerationService,
  TitleGenerationParams,
  TitleGenerationCallbacks,
  BookTitle as FactoryBookTitle,
  KeywordElement as FactoryKeywordElement,
  TitleFramework as FactoryTitleFramework,
  PRESET_KEYWORDS,
  PRESET_FRAMEWORKS as FACTORY_PRESET_FRAMEWORKS
} from '@/factories/ai/services/brainstorm';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import TitleAnalysisDialog from './TitleAnalysisDialog';
import { HeartIcon, BookIcon, TrashIcon, EditIcon, CopyIcon, SelectIcon } from '@/components/ui/AnimatedIcons';

// Toast通知系统
type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastMessage {
  id: string;
  type: ToastType;
  title: string;
  message: string;
  duration?: number;
}

// Toast管理Hook
const useToast = () => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const showToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = Date.now().toString();
    setToasts(prev => [...prev, { ...toast, id }]);

    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, toast.duration || 3000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  return { toasts, showToast, removeToast };
};

// Toast组件
const Toast: React.FC<{ toast: ToastMessage; onRemove: (id: string) => void }> = ({ toast, onRemove }) => {
  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-green-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      className={`${getToastStyles()} px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md`}
    >
      <span className="text-lg">{getIcon()}</span>
      <div className="flex-1">
        <div className="font-medium">{toast.title}</div>
        {toast.message && <div className="text-sm opacity-90">{toast.message}</div>}
      </div>
      <button
        onClick={() => onRemove(toast.id)}
        className="text-white hover:text-gray-200 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </motion.div>
  );
};

// Toast容器组件
const ToastContainer: React.FC<{ toasts: ToastMessage[]; onRemove: (id: string) => void }> = ({ toasts, onRemove }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast key={toast.id} toast={toast} onRemove={onRemove} />
        ))}
      </AnimatePresence>
    </div>
  );
};

// SVG图标组件
const BookTitleIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.path
      d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.8 }}
    />
    <motion.path
      d="M20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
      fill="currentColor"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    />
    <motion.path
      d="M8 12l2 2 4-4"
      stroke="currentColor"
      strokeWidth="2"
      strokeDasharray="8"
      strokeDashoffset="8"
      animate={{ strokeDashoffset: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
    />
  </motion.svg>
);

const KeywordIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.rect
      x="3" y="6" width="6" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.1 }}
    />
    <motion.rect
      x="3" y="11" width="8" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2 }}
    />
    <motion.rect
      x="3" y="16" width="5" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3 }}
    />
    <motion.circle
      cx="18" cy="8" r="2"
      fill="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.4, type: "spring", stiffness: 200 }}
    />
    <motion.circle
      cx="16" cy="13" r="1.5"
      fill="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
    />
  </motion.svg>
);

const FrameworkIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.path
      d="M4 4h4v4H4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4 }}
    />
    <motion.path
      d="M10 4h4v4h-4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.1 }}
    />
    <motion.path
      d="M16 4h4v4h-4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.2 }}
    />
    <motion.path
      d="M4 10h4v4H4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.3 }}
    />
    <motion.path
      d="M10 10h4v4h-4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.4 }}
    />
  </motion.svg>
);

const AnalysisIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.circle
      cx="11" cy="11" r="8"
      stroke="currentColor"
      strokeWidth="2"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.8 }}
    />
    <motion.path
      d="m21 21-4.35-4.35"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    />
    <motion.circle
      cx="8" cy="8" r="1"
      fill="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: [0, 1.2, 1] }}
      transition={{ delay: 0.8, duration: 0.5 }}
    />
    <motion.circle
      cx="11" cy="14" r="1"
      fill="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: [0, 1.2, 1] }}
      transition={{ delay: 1, duration: 0.5 }}
    />
    <motion.circle
      cx="14" cy="9" r="1"
      fill="currentColor"
      initial={{ scale: 0 }}
      animate={{ scale: [0, 1.2, 1] }}
      transition={{ delay: 1.2, duration: 0.5 }}
    />
  </motion.svg>
);

// 使用工厂化的类型定义
type KeywordElement = FactoryKeywordElement;
type TitleFramework = FactoryTitleFramework;

// 写作技巧展示组件
const WritingTechniquesDisplay: React.FC<{
  techniques?: Array<{
    id: string;
    name: string;
    category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
    description: string;
    examples: string[];
    effectiveness: number;
  }>;
}> = ({ techniques }) => {
  if (!techniques || techniques.length === 0) return null;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'layout': return 'bg-green-100 text-green-700 border-green-200';
      case 'emphasis': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'coolpoint': return 'bg-red-100 text-red-700 border-red-200';
      case 'creativity': return 'bg-purple-100 text-purple-700 border-purple-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="mb-3">
      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
        <span className="mr-2">🎯</span>
        写作技巧 ({techniques.length})
      </h4>
      <div className="flex flex-wrap gap-1">
        {techniques.slice(0, 6).map((technique, index) => (
          <motion.span
            key={technique.id}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className={`px-2 py-1 text-xs rounded border ${getCategoryColor(technique.category)}`}
            title={`${technique.description} (效果: ${technique.effectiveness}/10)`}
            whileHover={{ scale: 1.05 }}
          >
            {technique.name}
          </motion.span>
        ))}
        {techniques.length > 6 && (
          <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded border border-gray-200">
            +{techniques.length - 6}
          </span>
        )}
      </div>
    </div>
  );
};

// 风格特征展示组件
const StyleCharacteristicsDisplay: React.FC<{
  characteristics?: {
    layoutTechniques?: { paragraphStructure?: string[]; };
    omissionAndEmphasis?: { emphasizedElements?: string[]; };
    coolPointLayout?: { primaryCoolPoints?: string[]; };
    creativeConcept?: { coreCreativity?: string[]; };
  };
}> = ({ characteristics }) => {
  if (!characteristics) return null;

  const sections = [
    {
      key: 'layout',
      title: '📝 排版技巧',
      data: characteristics.layoutTechniques?.paragraphStructure,
      color: 'bg-green-100 text-green-700'
    },
    {
      key: 'emphasis',
      title: '🎯 强调要素',
      data: characteristics.omissionAndEmphasis?.emphasizedElements,
      color: 'bg-orange-100 text-orange-700'
    },
    {
      key: 'coolpoint',
      title: '💥 主要爽点',
      data: characteristics.coolPointLayout?.primaryCoolPoints,
      color: 'bg-red-100 text-red-700'
    },
    {
      key: 'creativity',
      title: '💡 核心创意',
      data: characteristics.creativeConcept?.coreCreativity,
      color: 'bg-purple-100 text-purple-700'
    }
  ].filter(section => section.data && section.data.length > 0);

  if (sections.length === 0) return null;

  return (
    <div className="mb-3">
      <h4 className="text-sm font-medium text-gray-700 mb-2">风格特征</h4>
      <div className="space-y-2">
        {sections.map((section, sectionIndex) => (
          <div key={section.key}>
            <div className="text-xs text-gray-600 mb-1">{section.title}</div>
            <div className="flex flex-wrap gap-1">
              {section.data!.slice(0, 3).map((item, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: sectionIndex * 0.1 + index * 0.05 }}
                  className={`px-2 py-1 text-xs rounded ${section.color}`}
                >
                  {item}
                </motion.span>
              ))}
              {section.data!.length > 3 && (
                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                  +{section.data!.length - 3}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// 复用模板展示组件
const ReusableTemplatesDisplay: React.FC<{
  templates?: Array<{
    id: string;
    name: string;
    pattern: string;
    description: string;
    effectiveness: number;
  }>;
}> = ({ templates }) => {
  if (!templates || templates.length === 0) return null;

  return (
    <div className="mb-3">
      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
        <span className="mr-2">🔄</span>
        复用模板 ({templates.length})
      </h4>
      <div className="space-y-1">
        {templates.slice(0, 2).map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="p-2 bg-blue-50 rounded border border-blue-200"
          >
            <div className="text-xs font-medium text-blue-700">{template.name}</div>
            <div className="text-xs text-blue-600 mt-1">{template.pattern}</div>
            <div className="text-xs text-gray-500 mt-1" title={template.description}>
              效果: {template.effectiveness}/10
            </div>
          </motion.div>
        ))}
        {templates.length > 2 && (
          <div className="text-xs text-gray-500 text-center py-1">
            还有 {templates.length - 2} 个模板...
          </div>
        )}
      </div>
    </div>
  );
};

// 书名生成参数接口（简化版）
interface BookTitleParams {
  keywords: string[];
  selectedFramework?: TitleFramework;
  customFramework?: string;
  associatedCharacters: string[];
  associatedWorldSettings: string[];
  associatedPlots: string[];
  specialRequirements: string; // 新增特别要求字段
}

// 扩展工厂化的BookTitle接口
interface BookTitle extends FactoryBookTitle {
  userRating?: number;
  parameters: BookTitleParams;
  generatedAt: Date;
}

interface BookTitleGeneratorProps {
  associationData?: AssociationData;
  selectedAssociations?: any;
  onGenerate: (request: any) => Promise<BrainstormResult[]>;
  onResultSelect: (result: BrainstormResult) => void;
}

// 使用工厂化的常量
const PRESET_FRAMEWORKS = FACTORY_PRESET_FRAMEWORKS;

/**
 * 书名生成器组件
 * 专业的左右分栏布局，参考全文标注面板设计
 */
const BookTitleGenerator: React.FC<BookTitleGeneratorProps> = ({
  associationData,
  selectedAssociations,
  onGenerate,
  onResultSelect
}) => {
  // 生成参数状态（简化版）
  const [params, setParams] = useState<BookTitleParams>({
    keywords: [],
    associatedCharacters: [],
    associatedWorldSettings: [],
    associatedPlots: [],
    specialRequirements: ''
  });

  // 关键词管理状态 - 初始化预置关键词
  const [keywordElements, setKeywordElements] = useState<KeywordElement[]>(() =>
    PRESET_KEYWORDS.map((preset, index) => ({
      ...preset,
      id: `preset_${index}`,
      frequency: 0,
      createdAt: new Date(),
    }))
  );
  const [keywordInput, setKeywordInput] = useState('');

  // 框架管理状态
  const [frameworks, setFrameworks] = useState<TitleFramework[]>(PRESET_FRAMEWORKS.map(f => ({
    ...f,
    usageCount: 0,
    createdAt: new Date()
  })));
  const [selectedFramework, setSelectedFramework] = useState<TitleFramework | null>(null);
  const [customFramework, setCustomFramework] = useState('');

  // 界面状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedTitles, setGeneratedTitles] = useState<BookTitle[]>([]);
  const [historyTitles, setHistoryTitles] = useState<BookTitle[]>([]);
  const [activeTab, setActiveTab] = useState<'results' | 'history' | 'analysis'>('results');

  // 弹窗状态
  const [showKeywordDialog, setShowKeywordDialog] = useState(false);
  const [showFrameworkDialog, setShowFrameworkDialog] = useState(false);
  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false);

  // Toast通知系统
  const { toasts, showToast, removeToast } = useToast();

  // 批量操作状态
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [selectedTitleIds, setSelectedTitleIds] = useState<Set<string>>(new Set());

  // 特别要求状态
  const [specialRequirements, setSpecialRequirements] = useState<string>('');

  // 特别要求localStorage支持
  useEffect(() => {
    const saved = localStorage.getItem('book-title-special-requirements');
    if (saved) {
      setSpecialRequirements(saved);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('book-title-special-requirements', specialRequirements);
  }, [specialRequirements]);

  // 初始化工厂化服务
  const titleGenerationService = useMemo(() => {
    // 创建API设置
    const settingsFactory = createSettingsFactory();
    const apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 创建AI发送器
    const aiSender = new DefaultAISenderComponent();

    // 创建书名生成服务
    return createBookTitleGenerationService(apiSettings, aiSender);
  }, []);

  // 从localStorage加载数据
  useEffect(() => {
    // 加载历史记录
    const savedHistory = localStorage.getItem('book-title-history');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setHistoryTitles(history);
      } catch (error) {
        console.error('加载书名历史失败:', error);
      }
    }

    // 加载关键词元素
    const savedKeywords = localStorage.getItem('book-title-keywords');
    if (savedKeywords) {
      try {
        const keywords = JSON.parse(savedKeywords);
        setKeywordElements(keywords);
      } catch (error) {
        console.error('加载关键词失败:', error);
      }
    }

    // 加载自定义框架
    const savedFrameworks = localStorage.getItem('book-title-frameworks');
    if (savedFrameworks) {
      try {
        const customFrameworks = JSON.parse(savedFrameworks);
        // 合并预置框架和自定义框架
        const allFrameworks = [
          ...PRESET_FRAMEWORKS.map(f => ({ ...f, usageCount: 0, createdAt: new Date() })),
          ...customFrameworks
        ];
        setFrameworks(allFrameworks);
      } catch (error) {
        console.error('加载自定义框架失败:', error);
      }
    }
  }, []);

  // 保存到历史记录
  const saveToHistory = useCallback((titles: BookTitle[]) => {
    const updatedHistory = [...titles, ...historyTitles].slice(0, 50); // 最多保存50条
    setHistoryTitles(updatedHistory);
    localStorage.setItem('book-title-history', JSON.stringify(updatedHistory));
  }, [historyTitles]);

  // 保存关键词到localStorage
  const saveKeywordsToStorage = useCallback((keywords: KeywordElement[]) => {
    localStorage.setItem('book-title-keywords', JSON.stringify(keywords));
  }, []);

  // 保存自定义框架到localStorage
  const saveFrameworksToStorage = useCallback((frameworks: TitleFramework[]) => {
    // 只保存自定义框架（非预置框架）
    const customFrameworks = frameworks.filter(f => !PRESET_FRAMEWORKS.find(preset => preset.id === f.id));

    // 添加调试日志
    console.log('🔍 保存框架到localStorage:');
    console.log('  - 总框架数:', frameworks.length);
    console.log('  - 自定义框架数:', customFrameworks.length);
    console.log('  - 自定义框架列表:', customFrameworks.map(f => ({ id: f.id, name: f.name, pattern: f.pattern })));

    localStorage.setItem('book-title-frameworks', JSON.stringify(customFrameworks));

    // 验证保存是否成功
    try {
      const saved = localStorage.getItem('book-title-frameworks');
      const parsedSaved = saved ? JSON.parse(saved) : [];
      console.log('✅ 保存验证成功，localStorage中的框架数:', parsedSaved.length);
    } catch (error) {
      console.error('❌ 保存验证失败:', error);
    }
  }, []);

  // 添加关键词元素
  const addKeywordElement = useCallback(() => {
    if (keywordInput.trim()) {
      const newKeyword: KeywordElement = {
        id: `keyword_${Date.now()}`,
        text: keywordInput.trim(),
        frequency: 1,
        hotness: 5, // 默认热度
        tags: [], // 默认无标签
        createdAt: new Date()
      };

      // 检查是否已存在，如果存在则增加频次
      const existingIndex = keywordElements.findIndex(k => k.text === newKeyword.text);
      let updatedKeywords: KeywordElement[];

      if (existingIndex >= 0) {
        updatedKeywords = [...keywordElements];
        updatedKeywords[existingIndex].frequency += 1;
      } else {
        updatedKeywords = [...keywordElements, newKeyword];
      }

      setKeywordElements(updatedKeywords);
      saveKeywordsToStorage(updatedKeywords);

      // 添加到当前生成参数
      if (!params.keywords.includes(newKeyword.text)) {
        setParams(prev => ({
          ...prev,
          keywords: [...prev.keywords, newKeyword.text]
        }));
      }

      setKeywordInput('');
    }
  }, [keywordInput, keywordElements, params.keywords, saveKeywordsToStorage]);

  // 移除关键词元素
  const removeKeywordElement = useCallback((keywordId: string) => {
    const keyword = keywordElements.find(k => k.id === keywordId);
    if (keyword) {
      const updatedKeywords = keywordElements.filter(k => k.id !== keywordId);
      setKeywordElements(updatedKeywords);
      saveKeywordsToStorage(updatedKeywords);

      setParams(prev => ({
        ...prev,
        keywords: prev.keywords.filter(k => k !== keyword.text)
      }));
    }
  }, [keywordElements, saveKeywordsToStorage]);

  // 切换关键词选择状态
  const toggleKeywordSelection = useCallback((keyword: string) => {
    setParams(prev => ({
      ...prev,
      keywords: prev.keywords.includes(keyword)
        ? prev.keywords.filter(k => k !== keyword)
        : [...prev.keywords, keyword]
    }));
  }, []);

  // 消息预处理 - 收集参考书名信息
  const preprocessGenerationContext = useCallback((framework: TitleFramework | null) => {
    if (!framework) return null;

    // 获取框架的参考书名（如果存在）
    const referenceBooks = (framework as any).referenceBooks || [];

    // 按时间排序，取最近的5个参考书名
    const recentBooks = referenceBooks
      .sort((a: any, b: any) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime())
      .slice(0, 5);

    // 统计类别分布，找出最常见的类别
    const categoryDistribution = (framework as any).categoryDistribution || {};
    const mostCommonCategory = Object.entries(categoryDistribution)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0];

    return {
      framework,
      referenceBooks: recentBooks,
      categoryHint: mostCommonCategory,
      hasReferences: recentBooks.length > 0
    };
  }, []);

  // 生成书名 - 使用工厂化服务
  const handleGenerate = useCallback(async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);

      // 预处理生成上下文
      const context = preprocessGenerationContext(selectedFramework);

      // 构建增强的用户需求描述
      let enhancedRequirements = '';

      // 添加特别要求（优先级最高）
      if (specialRequirements.trim()) {
        enhancedRequirements += `特别要求：${specialRequirements.trim()}\n\n`;
      }

      if (context?.hasReferences) {
        const { NovelCategory, NOVEL_CATEGORIES } = await import('./TitleAnalysisDialog');

        // 按来源分类参考书名
        const collectionBooks = context.referenceBooks.filter((book: any) => book.source === 'collection');
        const analysisBooks = context.referenceBooks.filter((book: any) => book.source === 'analysis');

        console.log('📚 参考书名统计:', {
          总数: context.referenceBooks.length,
          收藏生成: collectionBooks.length,
          分析提取: analysisBooks.length,
          详细列表: context.referenceBooks.map((book: any) => ({
            title: book.title,
            source: book.source,
            category: book.category
          }))
        });

        enhancedRequirements = `参考书名案例：
${context.referenceBooks.map((book: any, i: number) => {
  const categoryName = NOVEL_CATEGORIES[book.category as keyof typeof NOVEL_CATEGORIES]?.name || '其他';
  const sourceLabel = book.source === 'collection' ? '💖收藏' : '🔍分析';
  return `${i + 1}. ${book.title} (${categoryName}·${sourceLabel})`;
}).join('\n')}

请参考以上案例的风格和特征，生成符合框架模式的新书名。特别注意：
- 💖收藏来源：用户主动收藏的优质书名，代表用户偏好
- 🔍分析来源：通过AI分析提取的书名，具有专业参考价值
- 请综合考虑两种来源的书名特点，生成符合用户喜好和专业标准的新书名`;

        console.log('🤖 发送给AI的参考书名内容:', enhancedRequirements);
      } else {
        console.log('📚 当前框架没有参考书名');
      }

      // 构建工厂化生成参数
      const generationParams: TitleGenerationParams = {
        keywords: params.keywords,
        framework: selectedFramework || undefined,
        customFramework: customFramework || undefined,
        userRequirements: enhancedRequirements || undefined
      };

      // 构建回调函数
      const callbacks: TitleGenerationCallbacks = {
        onStart: () => {
          console.log('🚀 开始生成书名');
        },
        onProgress: (progress: number) => {
          console.log(`📊 生成进度: ${progress}%`);
        },
        onTitleGenerated: (title: FactoryBookTitle) => {
          console.log('📝 生成了新书名:', title.title);
        },
        onComplete: (titles: FactoryBookTitle[]) => {
          console.log('✅ 书名生成完成:', titles.length);
        },
        onError: (error: Error) => {
          console.error('❌ 书名生成失败:', error);
        }
      };

      // 使用工厂化服务生成书名
      const result = await titleGenerationService.generateTitles(generationParams, callbacks);

      if (result.success && result.titles.length > 0) {
        // 转换为组件内部的BookTitle格式
        const newTitles: BookTitle[] = result.titles.map(factoryTitle => ({
          ...factoryTitle,
          userRating: undefined,
          parameters: params,
          generatedAt: new Date(),
          isFavorite: factoryTitle.isFavorited // 映射字段名
        }));

        setGeneratedTitles(newTitles);
        saveToHistory(newTitles);

        // 更新框架使用次数
        if (selectedFramework) {
          setFrameworks(prev => prev.map(f =>
            f.id === selectedFramework.id
              ? { ...f, usageCount: f.usageCount + 1, lastUsedAt: new Date() }
              : f
          ));
        }

        setActiveTab('results');
      } else {
        throw new Error(result.error || '书名生成失败');
      }
    } catch (error) {
      console.error('书名生成失败:', error);
      // TODO: 显示错误提示
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, params, selectedFramework, customFramework, specialRequirements, titleGenerationService, saveToHistory]);

  // 从书名中提取关键词
  const extractKeywordsFromTitle = useCallback((title: string): string[] => {
    const keywords: string[] = [];
    keywordElements.forEach(element => {
      if (title.includes(element.text)) {
        keywords.push(element.text);
      }
    });
    return keywords;
  }, [keywordElements]);

  // 检测框架模式
  const detectFrameworkPattern = useCallback((title: string): string => {
    // 简单的模式检测逻辑
    if (title.includes('的')) return '{A}的{B}';
    if (title.includes('之')) return '{A}之{B}';
    if (title.includes('与')) return '{A}与{B}';
    return '未知模式';
  }, []);

  // 切换收藏状态 - 支持参考书名系统和历史记录同步
  const toggleFavorite = useCallback(async (titleId: string) => {
    // 先在生成结果中查找
    let targetTitle = generatedTitles.find(t => t.id === titleId);
    let isFromHistory = false;

    // 如果在生成结果中没找到，在历史记录中查找
    if (!targetTitle) {
      targetTitle = historyTitles.find(t => t.id === titleId);
      isFromHistory = true;
    }

    if (!targetTitle) return;

    const newFavoriteStatus = !targetTitle.isFavorited;

    // 更新生成结果中的收藏状态
    if (!isFromHistory) {
      setGeneratedTitles(prev =>
        prev.map(title =>
          title.id === titleId
            ? { ...title, isFavorited: newFavoriteStatus }
            : title
        )
      );
    }

    // 同步更新历史记录中的收藏状态
    setHistoryTitles(prev => {
      const updated = prev.map(title =>
        title.id === titleId
          ? { ...title, isFavorited: newFavoriteStatus }
          : title
      );
      // 立即保存到localStorage
      localStorage.setItem('book-title-history', JSON.stringify(updated));
      return updated;
    });

    // 如果是收藏操作且使用了框架，将书名添加到框架的参考书名中
    if (newFavoriteStatus && selectedFramework) {
      try {
        // 导入网文类别定义
        const { NovelCategory } = await import('./TitleAnalysisDialog');

        // 简单的类别检测逻辑（可以后续优化为AI检测）
        const detectCategory = (title: string): any => {
          if (title.includes('修仙') || title.includes('仙界') || title.includes('法宝')) return NovelCategory.XIANXIA;
          if (title.includes('都市') || title.includes('总裁') || title.includes('豪门')) return NovelCategory.URBAN;
          if (title.includes('穿越') || title.includes('古代') || title.includes('皇帝')) return NovelCategory.HISTORY;
          if (title.includes('科技') || title.includes('机甲') || title.includes('星际')) return NovelCategory.SCIFI;
          if (title.includes('游戏') || title.includes('网游') || title.includes('电竞')) return NovelCategory.GAME;
          return NovelCategory.OTHER;
        };

        const bookCategory = detectCategory(targetTitle.title);

        // 更新框架的参考书名
        setFrameworks(prev => prev.map(framework => {
          if (framework.id === selectedFramework.id) {
            const updatedFramework = { ...framework };

            // 初始化参考书名数组（如果不存在）
            if (!(updatedFramework as any).referenceBooks) {
              (updatedFramework as any).referenceBooks = [];
            }

            // 检查是否已存在该书名
            const existingTitles = new Set((updatedFramework as any).referenceBooks.map((book: any) => book.title));
            if (!existingTitles.has(targetTitle.title)) {
              // 添加新的参考书名
              (updatedFramework as any).referenceBooks.push({
                title: targetTitle.title,
                source: 'collection',
                category: bookCategory,
                addedAt: new Date()
              });

              // 更新类别分布统计
              if (!(updatedFramework as any).categoryDistribution) {
                (updatedFramework as any).categoryDistribution = {};
              }
              const current = (updatedFramework as any).categoryDistribution[bookCategory] || 0;
              (updatedFramework as any).categoryDistribution[bookCategory] = current + 1;

              // 更新描述
              const totalReferences = (updatedFramework as any).referenceBooks.length;
              updatedFramework.description = `${framework.description.split('，')[0]}，包含${framework.examples.length}个例子和${totalReferences}个参考书名`;
            }

            return updatedFramework;
          }
          return framework;
        }));

        // 显示收藏成功提示
        showToast({
          type: 'success',
          title: '收藏成功',
          message: `《${targetTitle.title}》已添加到框架参考书名`
        });

        console.log('✅ 收藏书名已添加到框架参考书名:', targetTitle.title);
      } catch (error) {
        console.error('❌ 添加参考书名失败:', error);
        showToast({
          type: 'error',
          title: '收藏失败',
          message: '添加到框架参考书名时出错'
        });
      }
    } else if (newFavoriteStatus) {
      // 普通收藏（没有选择框架）
      showToast({
        type: 'success',
        title: '收藏成功',
        message: `《${targetTitle.title}》已收藏`
      });
    } else {
      // 取消收藏 - 需要从所有框架的参考书名中移除
      try {
        setFrameworks(prev => prev.map(framework => {
          const updatedFramework = { ...framework };

          // 检查是否有参考书名数组
          if ((updatedFramework as any).referenceBooks) {
            const originalLength = (updatedFramework as any).referenceBooks.length;

            // 移除匹配的书名
            (updatedFramework as any).referenceBooks = (updatedFramework as any).referenceBooks.filter(
              (book: any) => book.title !== targetTitle.title
            );

            // 如果确实移除了书名，更新相关统计
            if ((updatedFramework as any).referenceBooks.length < originalLength) {
              // 重新计算类别分布
              const categoryDistribution: any = {};
              (updatedFramework as any).referenceBooks.forEach((book: any) => {
                categoryDistribution[book.category] = (categoryDistribution[book.category] || 0) + 1;
              });
              (updatedFramework as any).categoryDistribution = categoryDistribution;

              // 更新描述
              const totalReferences = (updatedFramework as any).referenceBooks.length;
              updatedFramework.description = `${framework.description.split('，')[0]}，包含${framework.examples.length}个例子和${totalReferences}个参考书名`;
            }
          }

          return updatedFramework;
        }));

        showToast({
          type: 'info',
          title: '已取消收藏',
          message: `《${targetTitle.title}》已从收藏和框架参考书名中移除`
        });

        console.log('✅ 已从所有框架参考书名中移除:', targetTitle.title);
      } catch (error) {
        console.error('❌ 移除参考书名失败:', error);
        showToast({
          type: 'warning',
          title: '取消收藏',
          message: `《${targetTitle.title}》收藏状态已更新，但移除参考书名时出错`
        });
      }
    }
  }, [generatedTitles, selectedFramework, showToast]);

  // 复制书名到剪贴板
  const copyToClipboard = useCallback(async (title: string) => {
    try {
      await navigator.clipboard.writeText(title);
      showToast({
        type: 'success',
        title: '复制成功',
        message: `《${title}》已复制到剪贴板`
      });
    } catch (error) {
      console.error('复制失败:', error);
      showToast({
        type: 'error',
        title: '复制失败',
        message: '无法访问剪贴板，请手动复制'
      });
    }
  }, [showToast]);

  // 录入书名到系统库
  const recordTitleToLibrary = useCallback(async (title: string) => {
    try {
      // 导入网文类别定义
      const { NovelCategory } = await import('./TitleAnalysisDialog');

      // 简单的类别检测逻辑
      const detectCategory = (title: string): any => {
        if (title.includes('修仙') || title.includes('仙界') || title.includes('法宝')) return NovelCategory.XIANXIA;
        if (title.includes('都市') || title.includes('总裁') || title.includes('豪门')) return NovelCategory.URBAN;
        if (title.includes('穿越') || title.includes('古代') || title.includes('皇帝')) return NovelCategory.HISTORY;
        if (title.includes('科技') || title.includes('机甲') || title.includes('星际')) return NovelCategory.SCIFI;
        if (title.includes('游戏') || title.includes('网游') || title.includes('电竞')) return NovelCategory.GAME;
        return NovelCategory.OTHER;
      };

      // 从书名中提取关键词
      const extractedKeywords: string[] = [];
      keywordElements.forEach(element => {
        if (title.includes(element.text)) {
          extractedKeywords.push(element.text);
        }
      });

      // 如果提取到关键词，更新关键词频次
      if (extractedKeywords.length > 0) {
        const updatedKeywords = keywordElements.map(element => {
          if (extractedKeywords.includes(element.text)) {
            return {
              ...element,
              frequency: element.frequency + 1,
              lastUsedAt: new Date()
            };
          }
          return element;
        });
        setKeywordElements(updatedKeywords);
        saveKeywordsToStorage(updatedKeywords);
      }

      // 检测框架模式并添加到框架库
      let detectedPattern = '';
      if (title.includes('的')) detectedPattern = '{A}的{B}';
      else if (title.includes('之')) detectedPattern = '{A}之{B}';
      else if (title.includes('：')) detectedPattern = '{A}：{B}';
      else if (title.includes('，')) detectedPattern = '{A}，{B}';

      if (detectedPattern) {
        const frameworkName = `${title.slice(0, 10)}...模式`;
        const existingFramework = frameworks.find(f => f.pattern === detectedPattern);

        if (existingFramework) {
          // 更新现有框架
          const updatedFrameworks = frameworks.map(f => {
            if (f.id === existingFramework.id) {
              const newExamples = f.examples.includes(title) ? f.examples : [...f.examples, title].slice(0, 5);
              return {
                ...f,
                examples: newExamples,
                usageCount: f.usageCount + 1,
                lastUsedAt: new Date()
              };
            }
            return f;
          });
          setFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
        } else {
          // 创建新框架
          const newFramework: TitleFramework = {
            id: `recorded_framework_${Date.now()}`,
            name: frameworkName,
            pattern: detectedPattern,
            description: `从书名《${title}》中提取的框架模式`,
            examples: [title],
            variables: detectedPattern.match(/\{[^}]+\}/g) || [],
            usageCount: 1,
            effectiveness: 5,
            createdAt: new Date(),
            lastUsedAt: new Date()
          };

          const updatedFrameworks = [...frameworks, newFramework];
          setFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
        }
      }

      showToast({
        type: 'success',
        title: '录入成功',
        message: `《${title}》已录入系统库，提取了${extractedKeywords.length}个关键词${detectedPattern ? '和1个框架模式' : ''}`
      });

    } catch (error) {
      console.error('录入失败:', error);
      showToast({
        type: 'error',
        title: '录入失败',
        message: '录入到系统库时出错'
      });
    }
  }, [keywordElements, frameworks, saveKeywordsToStorage, saveFrameworksToStorage, showToast]);

  // 批量操作功能
  const toggleBatchMode = useCallback(() => {
    setIsBatchMode(!isBatchMode);
    setSelectedTitleIds(new Set());
  }, [isBatchMode]);

  const toggleTitleSelection = useCallback((titleId: string) => {
    setSelectedTitleIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(titleId)) {
        newSet.delete(titleId);
      } else {
        newSet.add(titleId);
      }
      return newSet;
    });
  }, []);

  const selectAllTitles = useCallback(() => {
    const currentTitles = activeTab === 'results' ? generatedTitles : historyTitles;
    setSelectedTitleIds(new Set(currentTitles.map(t => t.id)));
  }, [activeTab, generatedTitles, historyTitles]);

  const clearSelection = useCallback(() => {
    setSelectedTitleIds(new Set());
  }, []);

  const batchFavorite = useCallback(async () => {
    const selectedTitles = (activeTab === 'results' ? generatedTitles : historyTitles)
      .filter(title => selectedTitleIds.has(title.id));

    for (const title of selectedTitles) {
      await toggleFavorite(title.id);
    }

    showToast({
      type: 'success',
      title: '批量收藏完成',
      message: `已收藏 ${selectedTitles.length} 个书名`
    });

    setSelectedTitleIds(new Set());
  }, [activeTab, generatedTitles, historyTitles, selectedTitleIds, toggleFavorite, showToast]);

  const batchRecord = useCallback(async () => {
    const selectedTitles = (activeTab === 'results' ? generatedTitles : historyTitles)
      .filter(title => selectedTitleIds.has(title.id));

    for (const title of selectedTitles) {
      await recordTitleToLibrary(title.title);
    }

    showToast({
      type: 'success',
      title: '批量录入完成',
      message: `已录入 ${selectedTitles.length} 个书名到系统库`
    });

    setSelectedTitleIds(new Set());
  }, [activeTab, generatedTitles, historyTitles, selectedTitleIds, recordTitleToLibrary, showToast]);

  const batchDelete = useCallback(() => {
    if (activeTab === 'history') {
      const selectedTitles = historyTitles.filter(title => selectedTitleIds.has(title.id));

      // 直接删除历史记录
      setHistoryTitles(prev => {
        const updated = prev.filter(title => !selectedTitleIds.has(title.id));
        localStorage.setItem('book-title-history', JSON.stringify(updated));
        return updated;
      });

      showToast({
        type: 'success',
        title: '批量删除完成',
        message: `已删除 ${selectedTitles.length} 条历史记录`
      });

      setSelectedTitleIds(new Set());
    }
  }, [activeTab, historyTitles, selectedTitleIds, showToast]);

  // 删除单个历史记录
  const deleteHistoryItem = useCallback((titleId: string) => {
    setHistoryTitles(prev => {
      const updated = prev.filter(title => title.id !== titleId);
      localStorage.setItem('book-title-history', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // 清空所有历史记录
  const clearAllHistory = useCallback(() => {
    setHistoryTitles([]);
    localStorage.removeItem('book-title-history');
  }, []);

  // 删除确认状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 处理分析结果
  const handleAnalysisComplete = useCallback((newKeywords: KeywordElement[], newFrameworks: TitleFramework[]) => {
    console.log('🔍 handleAnalysisComplete 被调用:', {
      newKeywords: newKeywords.length,
      newFrameworks: newFrameworks.length,
      newFrameworksData: newFrameworks.map(f => ({ id: f.id, name: f.name, pattern: f.pattern }))
    });

    // 更新关键词库
    if (newKeywords.length > 0) {
      const updatedKeywords = [...keywordElements, ...newKeywords];
      setKeywordElements(updatedKeywords);
      saveKeywordsToStorage(updatedKeywords);
      console.log('✅ 关键词已更新和保存:', newKeywords.length, '个');
    }

    // 更新框架库
    if (newFrameworks.length > 0) {
      const updatedFrameworks = [...frameworks, ...newFrameworks];
      console.log('🔍 准备更新框架库:', {
        原有框架数: frameworks.length,
        新增框架数: newFrameworks.length,
        更新后总数: updatedFrameworks.length
      });

      setFrameworks(updatedFrameworks);
      saveFrameworksToStorage(updatedFrameworks);
      console.log('✅ 框架已更新和保存');
    } else {
      console.log('⚠️ 没有新框架需要保存');
    }

    console.log('✅ 分析完成处理结束');
  }, [keywordElements, frameworks, saveKeywordsToStorage, saveFrameworksToStorage]);

  return (
    <div className="flex h-full bg-white title-generator-container">
      {/* 左侧参数设置区域 (40%宽度) */}
      <div className="w-2/5 border-r border-gray-200 bg-gray-50 title-generator-left flex flex-col h-full">
        {/* 上方内容区域 */}
        <div className="flex-1 min-h-0 p-6 overflow-y-auto custom-scrollbar">
          <div className="space-y-6">
          {/* 关键词元素管理按钮 */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">关键词元素</h3>
            <motion.button
              onClick={() => setShowKeywordDialog(true)}
              className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-all duration-300 bg-white hover:bg-blue-50 group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-500 group-hover:rotate-12 transition-transform duration-300">
                    <KeywordIcon className="w-6 h-6" />
                  </div>
                  <span className="font-medium text-gray-700">管理关键词</span>
                </div>
                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-sm font-medium">
                  {params.keywords.length}
                </span>
              </div>
            </motion.button>

            {/* 选中关键词的简要显示 */}
            {params.keywords.length > 0 && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-xs text-gray-500 mb-2">已选关键词：</div>
                <div className="flex flex-wrap gap-1">
                  {params.keywords.slice(0, 5).map((keyword) => (
                    <span
                      key={keyword}
                      className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs"
                    >
                      {keyword}
                    </span>
                  ))}
                  {params.keywords.length > 5 && (
                    <span className="px-2 py-1 bg-gray-200 text-gray-600 rounded text-xs">
                      +{params.keywords.length - 5}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 框架格式管理按钮 */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">框架格式</h3>
            <motion.button
              onClick={() => setShowFrameworkDialog(true)}
              className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 transition-all duration-300 bg-white hover:bg-purple-50 group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-purple-500 group-hover:animate-pulse">
                    <FrameworkIcon className="w-6 h-6" />
                  </div>
                  <span className="font-medium text-gray-700">选择框架</span>
                </div>
                <span className="text-sm text-gray-500 max-w-32 truncate">
                  {selectedFramework?.name || customFramework || '未选择'}
                </span>
              </div>
            </motion.button>

            {/* 选中框架的简要显示 */}
            {(selectedFramework || customFramework) && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-xs text-gray-500 mb-2">当前框架：</div>
                <div className="text-sm font-medium text-gray-700">
                  {selectedFramework?.name || '自定义框架'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {selectedFramework?.pattern || customFramework}
                </div>
                {selectedFramework?.description && (
                  <div className="text-xs text-gray-400 mt-1">
                    {selectedFramework.description}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 关键词框架拆解按钮 */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">书名分析</h3>
            <motion.button
              onClick={() => setShowAnalysisDialog(true)}
              className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-emerald-500 transition-all duration-300 bg-white hover:bg-emerald-50 group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-emerald-500 group-hover:scale-110 transition-transform duration-300">
                    <AnalysisIcon className="w-6 h-6" />
                  </div>
                  <span className="font-medium text-gray-700">关键词框架拆解</span>
                </div>
                <span className="text-xs text-gray-500">
                  分析现有书名
                </span>
              </div>
            </motion.button>
            <div className="mt-2 text-xs text-gray-500">
              输入优秀书名，AI自动拆解关键词和框架模式
            </div>
          </div>

          {/* 特别要求输入 */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">特别要求</h3>
            <div className="space-y-3">
              <textarea
                value={specialRequirements}
                onChange={(e) => setSpecialRequirements(e.target.value)}
                placeholder="描述你的特别要求，如：风格偏好、目标读者、特殊元素等..."
                className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none text-sm"
                maxLength={500}
              />
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>为AI提供更精准的生成指导</span>
                <span>{specialRequirements.length}/500</span>
              </div>
            </div>
          </div>

          </div>
        </div>

        {/* 底部生成按钮 */}
        <div className="flex-shrink-0 p-6 border-t border-gray-200 bg-white">
          <motion.button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="w-full py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium text-lg disabled:opacity-50 disabled:cursor-not-allowed generate-button relative overflow-hidden group"
            whileHover={{ scale: isGenerating ? 1 : 1.02 }}
            whileTap={{ scale: isGenerating ? 1 : 0.98 }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              initial={{ x: '-100%' }}
              animate={{ x: isGenerating ? '100%' : '-100%' }}
              transition={{
                duration: 1.5,
                repeat: isGenerating ? Infinity : 0,
                ease: "linear"
              }}
            />

            {isGenerating ? (
              <div className="flex items-center justify-center space-x-3 relative z-10">
                <motion.div
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>正在生成书名...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-3 relative z-10">
                <div className="text-white group-hover:scale-110 transition-transform duration-300">
                  <BookTitleIcon className="w-6 h-6" />
                </div>
                <span>生成书名</span>
              </div>
            )}
          </motion.button>
        </div>
      </div>

      {/* 右侧结果展示区域 (60%宽度) */}
      <div className="w-3/5 p-6 flex flex-col title-generator-right">
        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('results')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'results'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            生成结果 {generatedTitles.length > 0 && `(${generatedTitles.length})`}
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'history'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            历史记录 {historyTitles.length > 0 && `(${historyTitles.length})`}
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'analysis'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            数据分析
          </button>
        </div>

        {/* 内容区域 */}
        <div className={`flex-1 min-h-0 overflow-y-auto custom-scrollbar ${
          activeTab === 'results' ? 'results-scrollbar' :
          activeTab === 'history' ? 'history-scrollbar' :
          'analysis-scrollbar'
        }`}>
          <AnimatePresence mode="wait">
            {activeTab === 'results' ? (
              <motion.div
                key="results"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                {generatedTitles.length > 0 ? (
                  <>
                    {/* 生成结果操作栏 */}
                    <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-600">
                          共 {generatedTitles.length} 个书名
                        </div>
                        {isBatchMode && (
                          <div className="text-sm text-blue-600">
                            已选择 {selectedTitleIds.size} 项
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <motion.button
                          onClick={toggleBatchMode}
                          className={`px-3 py-1 rounded-lg transition-colors text-sm ${
                            isBatchMode
                              ? 'bg-blue-500 text-white hover:bg-blue-600'
                              : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {isBatchMode ? '退出批量' : '批量操作'}
                        </motion.button>
                        {isBatchMode && (
                          <>
                            <motion.button
                              onClick={selectAllTitles}
                              className="px-3 py-1 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              全选
                            </motion.button>
                            <motion.button
                              onClick={clearSelection}
                              className="px-3 py-1 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              清空选择
                            </motion.button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* 生成结果列表 */}
                    <div className="space-y-3 pb-4">
                      {generatedTitles.map((title, index) => (
                      <TitleCard
                        key={title.id}
                        title={title}
                        index={index}
                        onToggleFavorite={toggleFavorite}
                        onCopy={copyToClipboard}
                        onSelect={() => recordTitleToLibrary(title.title)}
                        isBatchMode={isBatchMode}
                        isSelected={selectedTitleIds.has(title.id)}
                        onBatchSelect={toggleTitleSelection}
                      />
                    ))}
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📚</div>
                      <p className="text-lg mb-2">等待生成书名</p>
                      <p className="text-sm">设置参数后点击生成按钮开始创作</p>
                    </div>
                  </div>
                )}
              </motion.div>
            ) : activeTab === 'history' ? (
              <motion.div
                key="history"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex flex-col h-full"
              >
                {historyTitles.length > 0 ? (
                  <>
                    {/* 历史记录操作栏 */}
                    <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-600">
                          共 {historyTitles.length} 条记录
                        </div>
                        {isBatchMode && (
                          <div className="text-sm text-blue-600">
                            已选择 {selectedTitleIds.size} 项
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <motion.button
                          onClick={toggleBatchMode}
                          className={`px-3 py-1 rounded-lg transition-colors text-sm ${
                            isBatchMode
                              ? 'bg-blue-500 text-white hover:bg-blue-600'
                              : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {isBatchMode ? '退出批量' : '批量操作'}
                        </motion.button>
                        {isBatchMode && (
                          <>
                            <motion.button
                              onClick={selectAllTitles}
                              className="px-3 py-1 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              全选
                            </motion.button>
                            <motion.button
                              onClick={clearSelection}
                              className="px-3 py-1 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              清空选择
                            </motion.button>
                          </>
                        )}
                        <motion.button
                          onClick={() => setShowClearConfirm(true)}
                          className="px-3 py-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors text-sm"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          清空全部
                        </motion.button>
                      </div>
                    </div>

                    {/* 历史记录列表 */}
                    <div className="flex-1 min-h-0 overflow-y-auto space-y-3 custom-scrollbar history-scrollbar">
                      {historyTitles.map((title, index) => (
                        <TitleCard
                          key={title.id}
                          title={title}
                          index={index}
                          onToggleFavorite={toggleFavorite}
                          onCopy={copyToClipboard}
                          onDelete={deleteHistoryItem}
                          showDelete={true}
                          onSelect={() => recordTitleToLibrary(title.title)}
                          isHistory={true}
                          isBatchMode={isBatchMode}
                          isSelected={selectedTitleIds.has(title.id)}
                          onBatchSelect={toggleTitleSelection}
                        />
                      ))}
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📋</div>
                      <p className="text-lg mb-2">暂无历史记录</p>
                      <p className="text-sm">生成的书名会自动保存在这里</p>
                    </div>
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="analysis"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                <div className="space-y-6 pb-4">
                  {/* 关键词统计 */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">关键词统计</h4>
                    {keywordElements.length > 0 ? (
                      <div className="space-y-3">
                        {keywordElements
                          .sort((a, b) => b.frequency - a.frequency)
                          .slice(0, 10)
                          .map((element) => (
                            <div key={element.id} className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className="px-2 py-1 rounded text-xs bg-emerald-100 text-emerald-700">
                                  ⭐ {element.hotness}
                                </span>
                                <span className="text-sm font-medium">{element.text}</span>
                                {element.tags && element.tags.length > 0 && (
                                  <div className="flex space-x-1">
                                    {element.tags.slice(0, 2).map((tag, idx) => (
                                      <span key={idx} className="px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-blue-500 h-2 rounded-full"
                                    style={{ width: `${Math.min(100, (element.frequency / Math.max(...keywordElements.map(k => k.frequency))) * 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-600">{element.frequency}</span>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">暂无关键词数据</p>
                    )}
                  </div>

                  {/* 框架使用统计 */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">框架使用统计</h4>
                    {frameworks.some(f => f.usageCount > 0) ? (
                      <div className="space-y-3">
                        {frameworks
                          .filter(f => f.usageCount > 0)
                          .sort((a, b) => b.usageCount - a.usageCount)
                          .map((framework) => (
                            <div key={framework.id} className="flex items-center justify-between">
                              <div>
                                <div className="text-sm font-medium">{framework.name}</div>
                                <div className="text-xs text-gray-500">{framework.pattern}</div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-purple-500 h-2 rounded-full"
                                    style={{ width: `${Math.min(100, (framework.usageCount / Math.max(...frameworks.map(f => f.usageCount))) * 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-600">{framework.usageCount}</span>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">暂无框架使用数据</p>
                    )}
                  </div>

                  {/* 生成趋势 */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">生成趋势</h4>
                    {historyTitles.length > 0 ? (
                      <div className="text-sm text-gray-600">
                        <p>总共生成了 {historyTitles.length} 个书名</p>
                        <p>平均AI评分: {(historyTitles.reduce((sum, t) => sum + t.aiScore, 0) / historyTitles.length).toFixed(1)}</p>
                        <p>收藏数量: {historyTitles.filter(t => t.isFavorited).length}</p>
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">暂无生成数据</p>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* 关键词管理弹窗 */}
      <KeywordManagementDialog
        isOpen={showKeywordDialog}
        onClose={() => setShowKeywordDialog(false)}
        keywordElements={keywordElements}
        selectedKeywords={params.keywords}
        onSelectionChange={(keywords) => setParams(prev => ({ ...prev, keywords }))}
        onAddKeyword={addKeywordElement}
        onRemoveKeyword={removeKeywordElement}

        keywordInput={keywordInput}
        onKeywordInputChange={setKeywordInput}
      />

      {/* 框架格式管理弹窗 */}
      <FrameworkManagementDialog
        isOpen={showFrameworkDialog}
        onClose={() => setShowFrameworkDialog(false)}
        frameworks={frameworks}
        selectedFramework={selectedFramework}
        onFrameworkSelect={setSelectedFramework}
        customFramework={customFramework}
        onCustomFrameworkChange={setCustomFramework}
        onSaveFramework={(newFramework) => {
          // 检查是否是更新现有框架
          const existingIndex = frameworks.findIndex(f => f.id === newFramework.id);
          let updatedFrameworks;

          if (existingIndex >= 0) {
            // 更新现有框架
            updatedFrameworks = frameworks.map(f =>
              f.id === newFramework.id ? newFramework : f
            );
          } else {
            // 添加新框架
            updatedFrameworks = [...frameworks, newFramework];
          }

          setFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
        }}
        onDeleteFramework={(frameworkId) => {
          const updatedFrameworks = frameworks.filter(f => f.id !== frameworkId);
          setFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
          // 如果删除的是当前选中的框架，清空选择
          if (selectedFramework?.id === frameworkId) {
            setSelectedFramework(null);
          }
        }}
      />

      {/* 关键词框架拆解弹窗 */}
      <TitleAnalysisDialog
        isOpen={showAnalysisDialog}
        onClose={() => setShowAnalysisDialog(false)}
        onAnalysisComplete={handleAnalysisComplete}
        existingKeywords={keywordElements}
        existingFrameworks={frameworks}
      />

      {/* 清空历史记录确认弹窗 */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4"
          >
            <div className="text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">确认清空历史记录</h3>
              <p className="text-gray-600 mb-6">
                此操作将删除所有历史记录，包括 {historyTitles.length} 条书名记录。此操作不可撤销。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    clearAllHistory();
                    setShowClearConfirm(false);
                  }}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  确认清空
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Toast通知容器 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* 批量操作底部栏 */}
      {isBatchMode && selectedTitleIds.size > 0 && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-xl shadow-2xl border border-gray-200 p-4 z-40"
        >
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              已选择 {selectedTitleIds.size} 个书名
            </div>

            <div className="flex space-x-2">
              <motion.button
                onClick={batchFavorite}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                ❤️ 批量收藏
              </motion.button>

              <motion.button
                onClick={batchRecord}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                ✅ 批量录入
              </motion.button>

              {activeTab === 'history' && (
                <motion.button
                  onClick={batchDelete}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  🗑️ 批量删除
                </motion.button>
              )}

              <motion.button
                onClick={clearSelection}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                取消选择
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

// 书名卡片组件
interface TitleCardProps {
  title: BookTitle;
  index: number;
  onToggleFavorite: (id: string) => void;
  onCopy: (title: string) => void;
  onSelect: () => void;
  onDelete?: (id: string) => void;
  showDelete?: boolean;
  isHistory?: boolean;
  // 批量操作相关
  isBatchMode?: boolean;
  isSelected?: boolean;
  onBatchSelect?: (id: string) => void;
}

const TitleCard: React.FC<TitleCardProps> = ({
  title,
  index,
  onToggleFavorite,
  onCopy,
  onSelect,
  onDelete,
  showDelete = false,
  isHistory = false,
  isBatchMode = false,
  isSelected = false,
  onBatchSelect
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1, duration: 0.3 }}
      className={`bg-white rounded-xl border p-4 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 ${
        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
      }`}
    >
      <div className="flex items-start justify-between">
        {/* 批量选择复选框 */}
        {isBatchMode && (
          <div className="mr-3 pt-1">
            <motion.button
              onClick={() => onBatchSelect?.(title.id)}
              className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                isSelected
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'border-gray-300 hover:border-blue-400'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isSelected && (
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </motion.button>
          </div>
        )}
        <div className="flex-1">
          <h4 className="text-lg font-semibold text-gray-800 mb-2">{title.title}</h4>
          
          {/* AI评分 */}
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-sm text-gray-500">AI评分:</span>
            <div className="flex items-center">
              {[1, 2, 3, 4, 5].map((star) => (
                <svg
                  key={star}
                  className={`w-4 h-4 ${
                    star <= title.aiScore ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
              <span className="text-sm text-gray-600 ml-1">({title.aiScore.toFixed(1)})</span>
            </div>
          </div>

          {/* 提取的关键词和框架信息 */}
          <div className="flex flex-wrap gap-2 mb-3">
            {title.extractedKeywords && title.extractedKeywords.length > 0 && (
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                关键词: {title.extractedKeywords.join('、')}
              </span>
            )}
            {title.detectedFramework && (
              <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                框架: {title.detectedFramework}
              </span>
            )}
            {title.frameworkMatch && (
              <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                模板: {title.frameworkMatch.name}
              </span>
            )}
          </div>

          {/* 关键词 */}
          {title.parameters.keywords.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {title.parameters.keywords.map((keyword) => (
                <span
                  key={keyword}
                  className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs"
                >
                  {keyword}
                </span>
              ))}
            </div>
          )}

          {/* 时间戳 */}
          {isHistory && (
            <p className="text-xs text-gray-400">
              生成于 {title.generatedAt.toLocaleString()}
            </p>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col space-y-2 ml-4">
          <motion.button
            onClick={() => onToggleFavorite(title.id)}
            className={`p-2 rounded-lg transition-colors ${
              title.isFavorited
                ? 'text-red-500 bg-red-50 hover:bg-red-100'
                : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <HeartIcon
              size={20}
              isActive={title.isFavorited}
              color="currentColor"
            />
          </motion.button>

          <motion.button
            onClick={() => onCopy(title.title)}
            className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <CopyIcon
              size={20}
              color="currentColor"
            />
          </motion.button>

          <motion.button
            onClick={onSelect}
            className="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <SelectIcon
              size={20}
              color="currentColor"
            />
          </motion.button>

          {/* 删除按钮 - 仅在历史记录中显示 */}
          {showDelete && onDelete && (
            <motion.button
              onClick={() => onDelete(title.id)}
              className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <TrashIcon
                size={20}
                color="currentColor"
              />
            </motion.button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// 书名管理弹窗组件
interface BookManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  framework: TitleFramework | null;
  onUpdateFramework: (framework: TitleFramework) => void;
}

const BookManagementDialog: React.FC<BookManagementDialogProps> = ({
  isOpen,
  onClose,
  framework,
  onUpdateFramework
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSource, setSelectedSource] = useState<'all' | 'analysis' | 'collection'>('all');
  const [selectedBookIds, setSelectedBookIds] = useState<Set<string>>(new Set());
  const [editingBook, setEditingBook] = useState<{ id: string; title: string } | null>(null);

  // 获取参考书名数据
  const referenceBooks = framework ? (framework as any).referenceBooks || [] : [];

  // 筛选书名
  const filteredBooks = referenceBooks.filter((book: any) => {
    const matchesSearch = book.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSource = selectedSource === 'all' || book.source === selectedSource;
    return matchesSearch && matchesSource;
  });

  // 删除书名
  const handleDeleteBook = useCallback((bookId: string) => {
    if (!framework) return;

    const updatedReferenceBooks = referenceBooks.filter((book: any) => book.title !== bookId);
    const updatedExamples = framework.examples.filter(example => example !== bookId);

    const updatedFramework = {
      ...framework,
      examples: updatedExamples,
      referenceBooks: updatedReferenceBooks
    } as any;

    onUpdateFramework(updatedFramework);
  }, [framework, referenceBooks, onUpdateFramework]);

  // 编辑书名
  const handleEditBook = useCallback((oldTitle: string, newTitle: string) => {
    if (!framework || !newTitle.trim() || newTitle === oldTitle) return;

    const updatedReferenceBooks = referenceBooks.map((book: any) =>
      book.title === oldTitle ? { ...book, title: newTitle } : book
    );
    const updatedExamples = framework.examples.map(example =>
      example === oldTitle ? newTitle : example
    );

    const updatedFramework = {
      ...framework,
      examples: updatedExamples,
      referenceBooks: updatedReferenceBooks
    } as any;

    onUpdateFramework(updatedFramework);
    setEditingBook(null);
  }, [framework, referenceBooks, onUpdateFramework]);

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    if (!framework || selectedBookIds.size === 0) return;

    const titlesToDelete = Array.from(selectedBookIds);
    const updatedReferenceBooks = referenceBooks.filter((book: any) => !titlesToDelete.includes(book.title));
    const updatedExamples = framework.examples.filter(example => !titlesToDelete.includes(example));

    const updatedFramework = {
      ...framework,
      examples: updatedExamples,
      referenceBooks: updatedReferenceBooks
    } as any;

    onUpdateFramework(updatedFramework);
    setSelectedBookIds(new Set());
  }, [framework, referenceBooks, selectedBookIds, onUpdateFramework]);

  if (!isOpen || !framework) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[90vh] flex flex-col"
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">管理框架书名</h2>
            <p className="text-sm text-gray-500 mt-1">
              框架：{framework.name} | 共 {referenceBooks.length} 本书名
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和筛选栏 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索书名..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={selectedSource}
              onChange={(e) => setSelectedSource(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部来源</option>
              <option value="analysis">分析提取</option>
              <option value="collection">收藏生成</option>
            </select>
            {selectedBookIds.size > 0 && (
              <button
                onClick={() => {
                  if (window.confirm(`确定要删除选中的 ${selectedBookIds.size} 本书名吗？`)) {
                    handleBatchDelete();
                  }
                }}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                删除选中 ({selectedBookIds.size})
              </button>
            )}
          </div>
        </div>

        {/* 书名列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredBooks.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">📚</div>
              <p>暂无书名数据</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredBooks.map((book: any, index: number) => (
                <motion.div
                  key={`${book.title}-${index}`}
                  className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  whileHover={{ scale: 1.01 }}
                >
                  <input
                    type="checkbox"
                    checked={selectedBookIds.has(book.title)}
                    onChange={(e) => {
                      const newSelected = new Set(selectedBookIds);
                      if (e.target.checked) {
                        newSelected.add(book.title);
                      } else {
                        newSelected.delete(book.title);
                      }
                      setSelectedBookIds(newSelected);
                    }}
                    className="mr-3"
                  />

                  <div className="flex-1">
                    {editingBook?.id === book.title ? (
                      <input
                        type="text"
                        value={editingBook?.title || ''}
                        onChange={(e) => setEditingBook(editingBook ? { ...editingBook, title: e.target.value } : null)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleEditBook(book.title, editingBook?.title || '');
                          } else if (e.key === 'Escape') {
                            setEditingBook(null);
                          }
                        }}
                        onBlur={() => handleEditBook(book.title, editingBook?.title || '')}
                        className="w-full px-2 py-1 border border-blue-500 rounded focus:outline-none"
                        autoFocus
                      />
                    ) : (
                      <div className="font-medium text-gray-800">{book.title}</div>
                    )}

                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded text-xs ${
                        book.source === 'analysis'
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-green-100 text-green-700'
                      }`}>
                        {book.source === 'analysis' ? '🔍 分析提取' : '💖 收藏生成'}
                      </span>
                      <span>{new Date(book.addedAt).toLocaleDateString()}</span>
                      {book.confidence && (
                        <span>置信度: {(book.confidence * 100).toFixed(0)}%</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setEditingBook({ id: book.title, title: book.title })}
                      className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                      title="编辑书名"
                    >
                      <EditIcon size={16} color="currentColor" />
                    </button>
                    <button
                      onClick={() => {
                        if (window.confirm(`确定要删除书名"${book.title}"吗？`)) {
                          handleDeleteBook(book.title);
                        }
                      }}
                      className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                      title="删除书名"
                    >
                      <TrashIcon size={16} color="currentColor" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* 弹窗底部 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            显示 {filteredBooks.length} / {referenceBooks.length} 本书名
            {selectedBookIds.size > 0 && ` | 已选择 ${selectedBookIds.size} 本`}
          </div>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            完成
          </button>
        </div>
      </motion.div>
    </div>
  );
};

// 框架格式管理弹窗组件
interface FrameworkManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  frameworks: TitleFramework[];
  selectedFramework: TitleFramework | null;
  onFrameworkSelect: (framework: TitleFramework | null) => void;
  customFramework: string;
  onCustomFrameworkChange: (value: string) => void;
  onSaveFramework: (framework: TitleFramework) => void;
  onDeleteFramework: (frameworkId: string) => void;
}

const FrameworkManagementDialog: React.FC<FrameworkManagementDialogProps> = ({
  isOpen,
  onClose,
  frameworks,
  selectedFramework,
  onFrameworkSelect,
  customFramework,
  onCustomFrameworkChange,
  onSaveFramework,
  onDeleteFramework
}) => {
  const [activeTab, setActiveTab] = useState<'preset' | 'custom'>('preset');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFrameworkForBookManagement, setSelectedFrameworkForBookManagement] = useState<TitleFramework | null>(null);
  const [showBookManagementDialog, setShowBookManagementDialog] = useState(false);
  const [expandedFrameworks, setExpandedFrameworks] = useState<Set<string>>(new Set());

  const filteredFrameworks = frameworks.filter(framework =>
    framework.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    framework.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 切换框架展开状态
  const toggleFrameworkExpansion = (frameworkId: string) => {
    setExpandedFrameworks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(frameworkId)) {
        newSet.delete(frameworkId);
      } else {
        newSet.add(frameworkId);
      }
      return newSet;
    });
  };

  // 处理框架更新
  const handleUpdateFramework = useCallback((updatedFramework: TitleFramework) => {
    // 直接调用onSaveFramework来触发更新，它会自动处理新增或更新
    onSaveFramework(updatedFramework);
  }, [onSaveFramework]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col"
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">框架格式管理</h2>
            <p className="text-sm text-gray-500 mt-1">选择预设框架或创建自定义框架</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('preset')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
              activeTab === 'preset'
                ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            预设框架
          </button>
          <button
            onClick={() => setActiveTab('custom')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
              activeTab === 'custom'
                ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            自定义框架
          </button>
        </div>

        {/* 弹窗内容 - 左右双栏设计 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧框架分类和搜索 */}
          <div className="w-1/3 border-r border-gray-200 p-4 flex flex-col">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">框架分类</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setActiveTab('preset')}
                  className={`w-full p-2 rounded-lg text-left transition-all ${
                    activeTab === 'preset'
                      ? 'bg-purple-50 text-purple-700 border border-purple-200'
                      : 'hover:bg-gray-50 text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <span>📋</span>
                    <span className="text-sm font-medium">预设框架</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('custom')}
                  className={`w-full p-2 rounded-lg text-left transition-all ${
                    activeTab === 'custom'
                      ? 'bg-purple-50 text-purple-700 border border-purple-200'
                      : 'hover:bg-gray-50 text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <span>✏️</span>
                    <span className="text-sm font-medium">自定义框架</span>
                  </div>
                </button>
              </div>
            </div>

            {activeTab === 'preset' && (
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">搜索框架</h3>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索框架名称或描述..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                />
              </div>
            )}

            {activeTab === 'custom' && (
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">框架变量</h3>
                <div className="space-y-2">
                  {[
                    { var: '{数字}', desc: '数字巨大' },
                    { var: '{动作}', desc: '成嫌疑犯' },
                    { var: '{角色}', desc: '警花老婆' },
                    { var: '{情绪}', desc: '崩溃' },
                    { var: '{身份设定}', desc: '刚成尸祖' },
                    { var: '{冲突事件}', desc: '你让我骂醒女娲' },
                    { var: '{地点/势力}', desc: '洪荒' },
                    { var: '{事件/身份}', desc: '帝王' }
                  ].map((item, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        // 插入变量到光标位置
                        const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
                        if (textarea) {
                          const start = textarea.selectionStart;
                          const end = textarea.selectionEnd;
                          const currentValue = customFramework;
                          const newValue = currentValue.substring(0, start) + item.var + currentValue.substring(end);
                          onCustomFrameworkChange(newValue);
                          // 设置新的光标位置
                          setTimeout(() => {
                            textarea.focus();
                            textarea.setSelectionRange(start + item.var.length, start + item.var.length);
                          }, 0);
                        }
                      }}
                      className="w-full text-left px-2 py-1 rounded text-xs hover:bg-emerald-50 hover:text-emerald-700 transition-colors"
                    >
                      <div className="font-mono text-purple-600">{item.var}</div>
                      <div className="text-gray-500">{item.desc}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 右侧框架列表或编辑器 */}
          <div className="w-2/3 p-4 flex flex-col">
            {activeTab === 'preset' ? (
              <div className="flex-1 overflow-y-auto">
                <div className="space-y-3">
                  {filteredFrameworks.map((framework) => {
                    const isExpanded = expandedFrameworks.has(framework.id);
                    const hasAnalysisData = framework.writingTechniques?.length > 0 ||
                                          framework.styleCharacteristics ||
                                          framework.reusableTemplates?.length > 0;

                    return (
                      <motion.div
                        key={framework.id}
                        layout
                        className={`rounded-lg border transition-all ${
                          selectedFramework?.id === framework.id
                            ? 'border-purple-500 bg-purple-50 shadow-md'
                            : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                        }`}
                        initial={false}
                        animate={{
                          height: isExpanded ? 'auto' : 'auto',
                          transition: { type: "spring", stiffness: 300, damping: 30 }
                        }}
                      >
                        {/* 基础信息区域 - 可点击选择框架 */}
                        <div
                          className="p-4 cursor-pointer"
                          onClick={() => onFrameworkSelect(framework)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-800 mb-1">{framework.name}</h3>
                              <p className="text-sm text-purple-600 mb-2">{framework.pattern}</p>
                              <p className="text-xs text-gray-500 mb-2">{framework.description}</p>

                              {/* 快速预览 - 显示技巧数量 */}
                              {hasAnalysisData && (
                                <div className="flex items-center space-x-2 mb-2">
                                  {framework.writingTechniques?.length > 0 && (
                                    <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                                      🎯 {framework.writingTechniques.length}个技巧
                                    </span>
                                  )}
                                  {framework.styleCharacteristics && (
                                    <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                                      📝 风格分析
                                    </span>
                                  )}
                                  {framework.reusableTemplates?.length > 0 && (
                                    <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">
                                      🔄 {framework.reusableTemplates.length}个模板
                                    </span>
                                  )}
                                </div>
                              )}

                              {/* 参考书名显示 */}
                              {(framework as any).referenceBooks && (framework as any).referenceBooks.length > 0 && (
                                <div className="mb-2">
                                  <div className="text-xs text-emerald-600 font-medium mb-1">
                                    📚 参考书名 ({(framework as any).referenceBooks.length})
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {(framework as any).referenceBooks.slice(0, 2).map((book: any, index: number) => (
                                      <span
                                        key={index}
                                        className="px-2 py-1 bg-emerald-50 text-emerald-700 rounded text-xs border border-emerald-200"
                                        title={`来源：${book.source === 'analysis' ? '分析提取' : '收藏生成'}`}
                                      >
                                        {book.title}
                                      </span>
                                    ))}
                                    {(framework as any).referenceBooks.length > 2 && (
                                      <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                        +{(framework as any).referenceBooks.length - 2}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              )}

                              {framework.examples && framework.examples.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {framework.examples.slice(0, 3).map((example, index) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs"
                                    >
                                      {example}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* 右侧操作按钮区域 */}
                            <div className="flex items-start space-x-2 ml-4">
                              {/* 展开/折叠按钮 - 只有包含分析数据的框架才显示 */}
                              {hasAnalysisData && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleFrameworkExpansion(framework.id);
                                  }}
                                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                                  title={isExpanded ? "收起详细分析" : "查看详细分析"}
                                >
                                  <motion.svg
                                    className="w-4 h-4 text-gray-500"
                                    animate={{ rotate: isExpanded ? 180 : 0 }}
                                    transition={{ duration: 0.3 }}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                  </motion.svg>
                                </button>
                              )}

                              {framework.usageCount > 0 && (
                                <div className="text-xs text-blue-500 px-2 py-1 bg-blue-50 rounded">
                                  使用 {framework.usageCount} 次
                                </div>
                              )}

                              {/* 管理书名按钮 - 只有有参考书名的框架才显示 */}
                              {(framework as any).referenceBooks && (framework as any).referenceBooks.length > 0 && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedFrameworkForBookManagement(framework);
                                    setShowBookManagementDialog(true);
                                  }}
                                  className="text-blue-500 hover:text-blue-700 text-sm p-1 rounded hover:bg-blue-50 transition-colors"
                                  title="管理书名"
                                >
                                  <BookIcon size={16} color="currentColor" />
                                </button>
                              )}

                              {/* 删除按钮 - 对用户创建的框架显示（排除系统预设框架） */}
                              {(!framework.id.startsWith('preset_') &&
                                !['hero_journey', 'location_event', 'character_destiny', 'item_power'].includes(framework.id)) && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm(`确定要删除框架"${framework.name}"吗？`)) {
                                      onDeleteFramework(framework.id);
                                    }
                                  }}
                                  className="text-red-500 hover:text-red-700 text-sm p-1 rounded hover:bg-red-50 transition-colors"
                                  title="删除框架"
                                >
                                  <TrashIcon size={16} color="currentColor" />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* 详细分析区域 - 可展开/折叠 */}
                        <AnimatePresence>
                          {isExpanded && hasAnalysisData && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ type: "spring", stiffness: 300, damping: 30 }}
                              className="border-t border-gray-200 p-4 bg-gray-50"
                            >
                              <div className="space-y-3">
                                {/* 写作技巧展示 */}
                                <WritingTechniquesDisplay techniques={framework.writingTechniques} />

                                {/* 风格特征展示 */}
                                <StyleCharacteristicsDisplay characteristics={framework.styleCharacteristics} />

                                {/* 复用模板展示 */}
                                <ReusableTemplatesDisplay templates={framework.reusableTemplates} />

                                {/* 分析置信度 */}
                                {framework.techniqueAnalysisConfidence && (
                                  <div className="text-xs text-gray-500 text-center pt-2 border-t border-gray-200">
                                    分析置信度: {Math.round(framework.techniqueAnalysisConfidence * 100)}%
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="flex-1 flex flex-col">
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">自定义框架模式</h3>
                  <textarea
                    value={customFramework}
                    onChange={(e) => onCustomFrameworkChange(e.target.value)}
                    placeholder="输入自定义框架模式，如：{主角}与{伙伴}的{冒险}&#10;&#10;使用 {} 包围变量名，如：{主角}、{地点}、{事件}等"
                    className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  />
                </div>

                {customFramework && (
                  <div className="space-y-3">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-700 mb-2">预览效果</h4>
                      <p className="text-sm text-blue-600">{customFramework}</p>
                    </div>

                    <button
                      onClick={() => {
                        // 保存自定义框架
                        const newFramework: TitleFramework = {
                          id: `custom_${Date.now()}`,
                          name: `自定义框架 ${new Date().toLocaleDateString()}`,
                          pattern: customFramework,
                          description: '用户自定义的框架模式',
                          examples: [],
                          variables: [], // 从pattern中提取变量
                          usageCount: 0,
                          effectiveness: 5, // 默认效果评分
                          createdAt: new Date(),
                          lastUsedAt: new Date()
                        };

                        onSaveFramework(newFramework);

                        // 选择新创建的框架
                        onFrameworkSelect(newFramework);

                        // 清空自定义框架输入
                        onCustomFrameworkChange('');
                      }}
                      className="w-full py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                    >
                      💾 保存为自定义框架
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 弹窗底部 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            {activeTab === 'preset'
              ? selectedFramework ? `已选择：${selectedFramework.name}` : '请选择一个框架'
              : customFramework ? '自定义框架已设置' : '请输入自定义框架'
            }
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              取消
            </button>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              确认选择
            </button>
          </div>
        </div>
      </motion.div>

      {/* 书名管理弹窗 */}
      <BookManagementDialog
        isOpen={showBookManagementDialog}
        onClose={() => setShowBookManagementDialog(false)}
        framework={selectedFrameworkForBookManagement}
        onUpdateFramework={handleUpdateFramework}
      />
    </div>
  );
};

// 关键词管理弹窗组件
interface KeywordManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  keywordElements: KeywordElement[];
  selectedKeywords: string[];
  onSelectionChange: (keywords: string[]) => void;
  onAddKeyword: () => void;
  onRemoveKeyword: (id: string) => void;
  keywordInput: string;
  onKeywordInputChange: (value: string) => void;
}

const KeywordManagementDialog: React.FC<KeywordManagementDialogProps> = ({
  isOpen,
  onClose,
  keywordElements,
  selectedKeywords,
  onSelectionChange,
  onAddKeyword,
  onRemoveKeyword,
  keywordInput,
  onKeywordInputChange
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredKeywords = keywordElements.filter(element =>
    element.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (element.tags && element.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
  );

  const handleKeywordToggle = (keyword: string) => {
    const newSelection = selectedKeywords.includes(keyword)
      ? selectedKeywords.filter(k => k !== keyword)
      : [...selectedKeywords, keyword];
    onSelectionChange(newSelection);
  };

  const handleAddKeyword = () => {
    onAddKeyword();
    onKeywordInputChange('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col"
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">关键词管理</h2>
            <p className="text-sm text-gray-500 mt-1">搜索、添加和管理书名关键词</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧分类和添加 */}
          <div className="w-1/3 border-r border-gray-200 p-4 flex flex-col">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">热门关键词</h3>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {PRESET_KEYWORDS.slice(0, 8).map((preset, index) => {
                  const isAdded = keywordElements.find(k => k.text === preset.text);
                  return (
                    <button
                      key={index}
                      onClick={() => {
                        if (!isAdded) {
                          // 这里需要通过输入框添加
                          onKeywordInputChange(preset.text);
                        }
                      }}
                      disabled={!!isAdded}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors border ${
                        isAdded
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : 'bg-gray-50 text-gray-700 hover:bg-emerald-50 hover:text-emerald-700 border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{preset.text}</span>
                        <span className="text-xs text-gray-500">⭐{preset.hotness}</span>
                      </div>
                      {preset.tags && preset.tags.length > 0 && (
                        <div className="flex space-x-1 mt-1">
                          {preset.tags.slice(0, 2).map((tag, idx) => (
                            <span key={idx} className="px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">添加关键词</h3>
              <div className="space-y-2">
                <input
                  type="text"
                  value={keywordInput}
                  onChange={(e) => onKeywordInputChange(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddKeyword()}
                  placeholder="输入吸睛关键词..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
                <button
                  onClick={handleAddKeyword}
                  disabled={!keywordInput.trim()}
                  className="w-full py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                >
                  添加关键词
                </button>
              </div>
            </div>
          </div>

          {/* 右侧关键词列表 */}
          <div className="w-2/3 p-4 flex flex-col">
            <div className="mb-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索关键词..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex-1 overflow-y-auto">
              <div className="grid grid-cols-2 gap-2">
                {filteredKeywords.map((element) => (
                  <motion.div
                    key={element.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      selectedKeywords.includes(element.text)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                    onClick={() => handleKeywordToggle(element.text)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 rounded text-xs bg-emerald-100 text-emerald-700">
                          ⭐ {element.hotness}
                        </span>
                        <span className="text-sm font-medium">{element.text}</span>
                        {element.tags && element.tags.length > 0 && (
                          <div className="flex space-x-1">
                            {element.tags.slice(0, 2).map((tag, idx) => (
                              <span key={idx} className="px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-gray-500">({element.frequency})</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onRemoveKeyword(element.id);
                          }}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 弹窗底部 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            已选择 {selectedKeywords.length} 个关键词
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              取消
            </button>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              确认选择
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default BookTitleGenerator;

// 导出管理组件供其他组件使用
export { KeywordManagementDialog, FrameworkManagementDialog };
