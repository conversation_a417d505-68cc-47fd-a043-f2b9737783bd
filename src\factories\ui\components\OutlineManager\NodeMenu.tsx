import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { aiNodeAppender } from './assistant/AINodeAppender';
import { OutlineNodeType } from '../../types/outline';
import AIAppendDialog from './AIAppendDialog';
import SynopsisAIDialog from './assistant/SynopsisAIDialog';
import { SynopsisFields } from './assistant/SynopsisAIService';

interface NodeMenuProps {
  isOpen: boolean;
  position: { x: number, y: number };
  onClose: () => void;
  onEdit: () => void;
  onInlineEdit?: () => void; // 添加就地编辑选项
  onDelete: () => void;
  onAddChild: (type: string) => void;
  onAIAppend?: (parentNode: OutlineNodeType, customPrompt?: string, generateCount?: number) => void; // 修改AI追加回调，支持自定义提示词和生成数量
  onSynopsisAI?: (synopsisData: SynopsisFields) => void; // 新增：Synopsis AI回调
  nodeType: 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue' | 'synopsis';
  nodeData?: OutlineNodeType; // 添加节点数据
  availableFrameworks?: any[]; // 新增：可用的ACE框架
}

const NodeMenu: React.FC<NodeMenuProps> = ({
  isOpen,
  position,
  onClose,
  onEdit,
  onInlineEdit,
  onDelete,
  onAddChild,
  onAIAppend,
  onSynopsisAI,
  nodeType,
  nodeData,
  availableFrameworks = []
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [showAddMenu, setShowAddMenu] = React.useState(false);
  const [isAIAppending, setIsAIAppending] = React.useState(false);
  const [showAIDialog, setShowAIDialog] = useState(false);
  const [showSynopsisAIDialog, setShowSynopsisAIDialog] = useState(false);

  // 处理点击外部关闭菜单 - 但当AI对话框显示时不监听
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 如果任何AI对话框正在显示，不处理点击外部事件
      if (showAIDialog || showSynopsisAIDialog) {
        return;
      }

      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        console.log('🔄 点击菜单外部，关闭菜单');
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, showAIDialog, showSynopsisAIDialog]); // 添加showSynopsisAIDialog依赖

  // 重置AI对话框状态当菜单关闭时
  useEffect(() => {
    if (!isOpen) {
      console.log('🔄 菜单关闭，重置AI对话框状态');
      setShowAIDialog(false);
      setShowSynopsisAIDialog(false);
      setShowAddMenu(false); // 同时重置添加子节点菜单状态
    }
  }, [isOpen]);

  // 计算菜单位置，确保在视口内
  const calculatePosition = () => {
    if (!menuRef.current) return position;

    const menuRect = menuRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let { x, y } = position;

    // 确保菜单不超出右边界
    if (x + menuRect.width > viewportWidth) {
      x = viewportWidth - menuRect.width - 10;
    }

    // 确保菜单不超出下边界
    if (y + menuRect.height > viewportHeight) {
      y = viewportHeight - menuRect.height - 10;
    }

    return { x, y };
  };

  if (!isOpen) return null;

  const adjustedPosition = calculatePosition();

  return createPortal(
    <div
      ref={menuRef}
      className="fixed z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 menu-container"
      style={{
        top: adjustedPosition.y,
        left: adjustedPosition.x,
        animation: 'fadeIn 0.2s ease-out',
        transformOrigin: 'top left',
        maxHeight: 'min(75vh, 600px)', // 添加高度限制，与其他编辑菜单保持一致
        overflowY: 'auto', // 允许滚动
        minWidth: '200px', // 确保最小宽度
        maxWidth: 'min(90vw, 400px)', // 限制最大宽度
        display: (showAIDialog || showSynopsisAIDialog) ? 'none' : 'block' // 当任何AI对话框显示时隐藏菜单
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="flex justify-between items-center px-3 py-2 border-b border-gray-100">
        <div className="text-sm font-medium text-gray-700">节点操作</div>
        <button
          className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div className="py-1">
        {/* 统一的编辑节点选项 - 优先使用就地编辑 */}
        {onInlineEdit ? (
          <button
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onInlineEdit();
              onClose();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            <span>编辑节点</span>
          </button>
        ) : (
          <button
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
              onClose();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>编辑节点</span>
          </button>
        )}

        {/* AI追加功能 */}
        {nodeData && aiNodeAppender.canAppendChild(nodeType) && onAIAppend && (
          <button
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 flex items-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={(e) => {
              e.stopPropagation();
              if (!isAIAppending) {
                console.log('🔥 AI追加按钮被点击，显示对话框');
                setShowAIDialog(true);
                // 不关闭菜单，让用户可以看到菜单状态
                // onClose(); // 移除立即关闭
              }
            }}
            disabled={isAIAppending}
          >
            <div className="flex items-center">
              {isAIAppending ? (
                <svg className="animate-spin h-4 w-4 mr-2 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              )}
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent font-medium">
                {isAIAppending ? '正在生成...' : aiNodeAppender.getAppendDescription(nodeType)}
              </span>
            </div>
            <div className="ml-auto">
              <span className="text-xs bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 px-2 py-1 rounded-full">
                AI
              </span>
            </div>
          </button>
        )}

        {/* Synopsis AI功能 - 仅对Synopsis节点显示 */}
        {nodeType === 'synopsis' && nodeData && onSynopsisAI && (
          <button
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 flex items-center transition-all duration-200"
            onClick={(e) => {
              e.stopPropagation();
              console.log('🧠 Synopsis AI按钮被点击，显示对话框');
              setShowSynopsisAIDialog(true);
            }}
          >
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent font-medium">
                🧠 AI核心梗概生成
              </span>
            </div>
            <div className="ml-auto">
              <span className="text-xs bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 px-2 py-1 rounded-full">
                AI
              </span>
            </div>
          </button>
        )}

        <div className="relative">
          <button
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center justify-between transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              setShowAddMenu(!showAddMenu);
            }}
          >
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span>添加子节点</span>
            </div>
            {showAddMenu ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </button>

          {showAddMenu && (
            <div className="absolute left-0 right-0 mt-1 bg-gray-50 rounded-md border border-gray-200 z-10 add-child-submenu">
              <div className="p-2 grid grid-cols-2 gap-2">
                <button
                  className="flex flex-col items-center justify-center p-2 rounded-lg bg-white hover:bg-purple-50 border border-gray-200 hover:border-purple-200 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild('volume');
                    onClose();
                  }}
                >
                  <div
                    className="w-6 h-6 rounded-full mb-1 flex items-center justify-center text-white text-xs"
                    style={{ background: 'var(--outline-volume)' }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium">总纲/卷</span>
                </button>

                <button
                  className="flex flex-col items-center justify-center p-2 rounded-lg bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild('chapter');
                    onClose();
                  }}
                >
                  <div
                    className="w-6 h-6 rounded-full mb-1 flex items-center justify-center text-white text-xs"
                    style={{ background: 'var(--outline-primary)' }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium">章节</span>
                </button>

                <button
                  className="flex flex-col items-center justify-center p-2 rounded-lg bg-white hover:bg-orange-50 border border-gray-200 hover:border-orange-200 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild('plot');
                    onClose();
                  }}
                >
                  <div
                    className="w-6 h-6 rounded-full mb-1 flex items-center justify-center text-white text-xs"
                    style={{ background: 'var(--outline-secondary)' }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium">剧情节点</span>
                </button>

                <button
                  className="flex flex-col items-center justify-center p-2 rounded-lg bg-white hover:bg-green-50 border border-gray-200 hover:border-green-200 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild('dialogue');
                    onClose();
                  }}
                >
                  <div
                    className="w-6 h-6 rounded-full mb-1 flex items-center justify-center text-white text-xs"
                    style={{ background: 'var(--outline-info)' }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium">对话设计</span>
                </button>

                <button
                  className="flex flex-col items-center justify-center p-2 rounded-lg bg-white hover:bg-indigo-50 border border-gray-200 hover:border-indigo-200 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild('synopsis');
                    onClose();
                  }}
                >
                  <div
                    className="w-6 h-6 rounded-full mb-1 flex items-center justify-center text-white text-xs"
                    style={{ background: 'var(--outline-info)' }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium">核心故事梗概</span>
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-100 my-1"></div>

        <button
          className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          <span>删除节点</span>
        </button>
      </div>

      {/* AI追加对话框 - 只有在showAIDialog为true时才渲染 */}
      {showAIDialog && nodeData && (
        <AIAppendDialog
          isOpen={showAIDialog}
          position={position}
          nodeType={nodeType}
          nodeData={nodeData}
          onClose={() => setShowAIDialog(false)}
          onConfirm={(customPrompt, generateCount) => {
            setShowAIDialog(false);
            setIsAIAppending(true);
            if (onAIAppend) {
              onAIAppend(nodeData, customPrompt, generateCount);
            }
            // 确认生成后关闭菜单
            onClose();
            // 重置状态的逻辑将在父组件中处理
            setTimeout(() => setIsAIAppending(false), 1000);
          }}
        />
      )}

      {/* Synopsis AI对话框 - 只有在showSynopsisAIDialog为true时才渲染 */}
      {showSynopsisAIDialog && nodeData && nodeType === 'synopsis' && (
        <SynopsisAIDialog
          isOpen={showSynopsisAIDialog}
          onClose={() => setShowSynopsisAIDialog(false)}
          onComplete={(synopsisData) => {
            setShowSynopsisAIDialog(false);
            if (onSynopsisAI) {
              onSynopsisAI(synopsisData);
            }
            // 完成后关闭菜单
            onClose();
          }}
          availableFrameworks={availableFrameworks}
          nodeData={nodeData}
        />
      )}
    </div>,
    document.body
  );
};

export default NodeMenu;
