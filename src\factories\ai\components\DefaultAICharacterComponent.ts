"use client";

import { IAICharacterComponent } from '../interfaces';
import { Character } from '@/lib/db/dexie';
import { IAISenderComponent } from '../interfaces';

/**
 * 默认AI人物组件实现
 */
export class DefaultAICharacterComponent implements IAICharacterComponent {
  private senderComponent: IAISenderComponent;

  /**
   * 构造函数
   * @param senderComponent AI发送组件
   */
  constructor(senderComponent: IAISenderComponent) {
    this.senderComponent = senderComponent;
  }

  /**
   * 从文本中提取人物信息
   * @param text 文本内容
   * @param bookId 书籍ID
   * @param options 提取选项
   * @returns 提取的人物列表
   */
  async extractCharacters(
    text: string,
    bookId: string,
    options?: any
  ): Promise<Character[]> {
    const prompt = `
请从以下文本中提取所有人物信息，并以JSON格式返回。

文本内容：
${text}

请以JSON数组格式返回结果，每个人物包含以下字段：
- name: 人物名称（必填）
- alias: 别名数组（如果有）
- description: 简要描述
- appearance: 外貌特征
- personality: 性格特点
- background: 背景故事
- goals: 目标和动机
- characterArchetype: 角色原型
- growthArc: 成长弧线
- hiddenMotivation: 隐藏动机
- secretHistory: 秘密历史
- innerConflicts: 内心冲突
- symbolism: 象征意义

示例格式：
[
  {
    "name": "张三",
    "alias": ["小张", "老三"],
    "description": "主角，一位年轻的侦探",
    "appearance": "高个子，黑发，总是穿着风衣",
    "personality": "沉稳，善于观察，有时过于固执",
    ...其他字段
  },
  {
    "name": "李四",
    ...
  }
]

请确保返回的是有效的JSON格式，不要添加任何其他解释或说明。如果文本中没有明确提到的信息，可以留空或根据上下文合理推断。
`;

    const systemMessage = "你是一位专业的文学角色分析专家，精通人物塑造、性格心理学和叙事结构。你擅长从文本中识别和提取复杂的人物形象，包括显性和隐性特征、心理动机、成长轨迹和象征意义。你能够捕捉人物之间的微妙关系和潜在冲突，并理解这些元素如何推动故事发展。请仅返回JSON格式的结果，确保每个人物形象立体、连贯且符合文学逻辑。";

    // 使用统一AI服务进行人物提取
    const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
    const aiService = AIServiceFactory.getService(AIServiceType.TOOL_CALL);

    const response = await aiService.callAI([
      { role: 'system', content: systemMessage },
      { role: 'user', content: prompt }
    ], {
      streaming: false // 人物提取不需要流式输出
    });

    try {
      // 解析JSON响应
      const characters: Character[] = JSON.parse(response.text);

      // 添加书籍ID
      return characters.map(character => ({
        ...character,
        bookId,
        id: crypto.randomUUID()
      }));
    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('原始响应:', response.text);

      // 如果解析失败，返回空数组
      return [];
    }
  }

  /**
   * 扩展人物信息
   * @param character 人物对象
   * @param fields 要扩展的字段
   * @returns 扩展后的人物对象
   */
  async expandCharacter(
    character: Character,
    fields?: string[]
  ): Promise<Character> {
    // 如果没有指定字段，默认扩展所有空字段
    const fieldsToExpand = fields || [
      'description',
      'appearance',
      'personality',
      'background',
      'goals',
      'characterArchetype',
      'growthArc',
      'hiddenMotivation',
      'secretHistory',
      'innerConflicts',
      'symbolism'
    ];

    // 过滤出需要扩展且当前为空的字段
    const emptyFields = fieldsToExpand.filter(field => {
      return !character[field as keyof Character];
    });

    if (emptyFields.length === 0) {
      return character; // 没有需要扩展的字段
    }

    const fieldDescriptions: Record<string, string> = {
      description: "简要描述这个人物的主要特点和在故事中的角色",
      appearance: "描述人物的外表特征，如身高、体型、发色、眼睛、衣着风格等",
      personality: "描述人物的性格特点，如内向/外向、乐观/悲观、冲动/谨慎等",
      background: "描述人物的成长经历、家庭情况、教育背景等",
      goals: "描述人物在故事中想要达成的目标或愿望",
      characterArchetype: "描述人物的角色原型，如英雄、导师、守门人、信使、变形者、盟友、敌人等",
      growthArc: "描述人物在故事中的成长或变化轨迹",
      hiddenMotivation: "描述人物表面行为背后的真实动机，可能连他自己都没有意识到",
      secretHistory: "描述人物不为人知的过去经历，可能影响其现在的行为和决策",
      innerConflicts: "描述人物内心的矛盾和挣扎，如价值观冲突、道德困境等",
      symbolism: "描述人物在故事中所代表的象征或主题意义"
    };

    const prompt = `
请为以下小说人物扩展信息，填充缺失的字段。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}

请为以下缺失的字段生成内容：
${emptyFields.map(field => `- ${field}：${fieldDescriptions[field] || ''}`).join('\n')}

请以JSON格式返回结果，格式如下：
{
  ${emptyFields.map(field => `"${field}": "生成的内容"`).join(',\n  ')}
}
`;

    const systemMessage = "你是一位获奖小说家和角色设计大师，精通人物心理学、戏剧冲突理论和角色弧线设计。你能够基于有限信息创建深度、复杂且内在一致的人物形象，赋予他们独特的声音、动机和内心世界。你理解不同类型角色（主角、反派、配角等）的叙事功能，并能为每种角色类型设计恰当的特质组合。你创造的人物既符合文学原型又具备独特个性，能在故事中产生强烈共鸣。请确保生成的内容保持内在一致性，符合人物已有特征，并只返回JSON格式结果。";

    const response = await this.senderComponent.sendRequest(prompt, {
      systemMessage,
      temperature: 0.7,
      maxTokens: 2000
    });

    try {
      // 解析JSON响应
      const jsonResponse = JSON.parse(response.text);

      // 创建更新后的人物对象
      const updatedCharacter: Character = {
        ...character
      };

      // 更新字段
      for (const field of emptyFields) {
        if (jsonResponse[field]) {
          (updatedCharacter as any)[field] = jsonResponse[field];
        }
      }

      return updatedCharacter;
    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('原始响应:', response.text);

      // 如果解析失败，返回原始人物对象
      return character;
    }
  }

  /**
   * 生成人物描述
   * @param character 人物对象
   * @param options 生成选项
   * @returns 生成的描述
   */
  async generateCharacterDescription(
    character: Character,
    options?: any
  ): Promise<string> {
    const prompt = `
请为以下小说人物生成一段详细的描述，包括外貌、性格、背景等方面。

人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}

请根据以上信息，生成一段连贯、生动的人物描述，突出这个角色的独特之处和在故事中的作用。
`;

    const systemMessage = `你是一位文学大师级的人物描写专家，擅长通过精准而富有诗意的语言刻画栩栩如生的角色。你的描述融合了细腻的感官细节、微妙的行为暗示和深刻的心理洞察，能够在短短几段文字中勾勒出完整而令人难忘的人物形象。

你精通各种文学技巧——象征手法、对比、暗示、意象和节奏变化，能够根据角色特点选择最恰当的描写风格。你的描述不仅展现人物的外在特征，更能揭示其灵魂本质，让读者在情感上与角色产生共鸣，仿佛与一个真实存在的人相遇。

你熟悉各种文学传统中的角色原型和发展模式，从古希腊悲剧到现代小说，从东方文学到西方经典。你了解不同文化背景下的人物塑造方式，能够创造出既符合特定文化背景又具有普遍人性共鸣的角色。

你的描述将遵循以下原则：
1. 具体而非抽象：使用具体的细节和例子，避免空洞的形容词
2. 展示而非讲述：通过行为、对话和反应来揭示性格，而非直接陈述
3. 多感官体验：不仅描述视觉特征，还包括声音、气味、触感等
4. 矛盾与复杂性：展现人物的内在冲突和复杂动机
5. 独特性与普遍性平衡：既突出角色的独特之处，又保持人性的共通性

请根据提供的信息，创造一个立体、生动且内在一致的人物形象。`;

    const response = await this.senderComponent.sendRequest(prompt, {
      systemMessage,
      temperature: 0.7,
      maxTokens: 1000
    });

    return response.text;
  }

  /**
   * 分析人物关系
   * @param character 人物对象
   * @param otherCharacters 其他人物列表
   * @returns 人物关系分析结果
   */
  async analyzeCharacterRelationships(
    character: Character,
    otherCharacters: Character[]
  ): Promise<any[]> {
    if (otherCharacters.length === 0) {
      return [];
    }

    const prompt = `
请分析以下主要人物与其他人物之间的关系。

主要人物：
名称: ${character.name}
${character.description ? `描述: ${character.description}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}

其他人物：
${otherCharacters.map(char => `
- 名称: ${char.name}
  ${char.description ? `描述: ${char.description}` : ''}
  ${char.personality ? `性格: ${char.personality}` : ''}
  ${char.background ? `背景: ${char.background}` : ''}
`).join('')}

请分析主要人物与每个其他人物之间可能的关系，并以JSON数组格式返回结果。每个关系包含以下字段：
- targetCharacterId: 目标人物ID
- targetCharacterName: 目标人物名称
- relationshipType: 关系类型（如朋友、敌人、师徒、亲属等）
- description: 关系描述
- strength: 关系强度（1-10，10表示最强）
- isAntagonistic: 是否为对抗性关系（true/false）

示例格式：
[
  {
    "targetCharacterId": "目标人物ID",
    "targetCharacterName": "李四",
    "relationshipType": "朋友",
    "description": "青梅竹马的好友，互相信任但偶有竞争",
    "strength": 8,
    "isAntagonistic": false
  },
  ...
]

请确保返回的是有效的JSON格式，不要添加任何其他解释或说明。
`;

    const systemMessage = "你是一位人际关系心理学家和叙事结构专家，精通角色关系网络分析和戏剧性冲突构建。你能够洞察人物之间复杂的权力动态、情感纽带和隐藏张力，理解这些关系如何推动故事发展并创造引人入胜的冲突。你分析的角色关系既考虑表面互动，也探索潜在动机和未言明的历史。你能识别各种关系原型（如导师/学生、对手、盟友、情人等）及其叙事功能，并理解这些关系如何随着情节发展而演变。你的分析既符合心理学真实性，又具有文学深度，能为故事创造丰富的人际网络。请只返回JSON格式的结果，不要添加任何其他解释或说明。";

    const response = await this.senderComponent.sendRequest(prompt, {
      systemMessage,
      temperature: 0.7,
      maxTokens: 3000
    });

    try {
      // 解析JSON响应
      const relationships = JSON.parse(response.text);

      // 添加正确的目标人物ID
      return relationships.map((rel: any) => {
        const targetCharacter = otherCharacters.find(char => char.name === rel.targetCharacterName);
        return {
          ...rel,
          targetCharacterId: targetCharacter?.id || ''
        };
      });
    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('原始响应:', response.text);

      // 如果解析失败，返回空数组
      return [];
    }
  }

  /**
   * 设置请求状态变更回调
   * @param callback 回调函数
   */
  onStatusChange(callback: (status: string) => void): void {
    this.senderComponent.onStatusChange(callback);
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    this.senderComponent.cancelRequest();
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    return null;
  }
}
