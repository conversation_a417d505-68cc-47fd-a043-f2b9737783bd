"use client";

import React, { useState } from 'react';

interface AIRewriteContinuePromptProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (prompt: string) => void;
  initialPrompt?: string;
}

/**
 * AI改写继续对话提示组件
 * 用于输入继续对话的提示
 */
const AIRewriteContinuePrompt: React.FC<AIRewriteContinuePromptProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialPrompt = ''
}) => {
  const [prompt, setPrompt] = useState(initialPrompt);

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim()) {
      onSubmit(prompt);
      setPrompt('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-50 transition-all duration-300 ease-in-out">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-medium text-gray-900">继续对话</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex items-end space-x-3">
          <div className="flex-1">
            <label htmlFor="continue-prompt" className="block text-sm font-medium text-gray-700 mb-1">
              请输入您的指示
            </label>
            <textarea
              id="continue-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              rows={3}
              placeholder="请输入您希望AI如何继续改写内容的指示..."
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm flex items-center h-10"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
            发送
          </button>
        </form>
      </div>
    </div>
  );
};

export default AIRewriteContinuePrompt;
