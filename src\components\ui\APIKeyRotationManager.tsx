"use client";

import React, { useState, useEffect } from 'react';
import { APIKeyConfig, URLKeyPool, RotationStrategy } from '@/types/apiKeyRotation';
import { APIKeyRotationManager } from '@/utils/ai/APIKeyRotationManager';

interface APIKeyRotationManagerUIProps {
  onClose?: () => void;
}

export const APIKeyRotationManagerUI: React.FC<APIKeyRotationManagerUIProps> = ({ onClose }) => {
  const [rotationManager] = useState(() => APIKeyRotationManager.getInstance());
  const [urlPools, setUrlPools] = useState<Record<string, URLKeyPool>>({});
  const [selectedUrl, setSelectedUrl] = useState<string>('');
  const [newUrl, setNewUrl] = useState<string>('');
  const [newApiKey, setNewApiKey] = useState<string>('');
  const [newKeyWeight, setNewKeyWeight] = useState<number>(1);
  const [newKeyWaitTime, setNewKeyWaitTime] = useState<number>(60000);
  const [newKeyNotes, setNewKeyNotes] = useState<string>('');
  const [isEnabled, setIsEnabled] = useState<boolean>(false);

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const config = rotationManager.getConfig();
    setUrlPools(config.urlPools);
    setIsEnabled(config.globalSettings.enabled);
    
    // 如果没有选中的URL，选择第一个
    if (!selectedUrl && Object.keys(config.urlPools).length > 0) {
      setSelectedUrl(Object.keys(config.urlPools)[0]);
    }
  };

  const handleToggleRotation = () => {
    const newEnabled = !isEnabled;
    setIsEnabled(newEnabled);
    
    const config = rotationManager.getConfig();
    config.globalSettings.enabled = newEnabled;
    rotationManager['configManager'].save(config);
  };

  const handleAddUrl = () => {
    if (!newUrl.trim()) return;
    
    rotationManager['configManager'].addURLPool(
      rotationManager.getConfig(),
      newUrl.trim()
    );
    
    setNewUrl('');
    loadData();
  };

  const handleAddApiKey = () => {
    if (!selectedUrl || !newApiKey.trim()) return;
    
    rotationManager.addAPIKey(selectedUrl, newApiKey.trim(), {
      weight: newKeyWeight,
      customWaitTime: newKeyWaitTime,
      notes: newKeyNotes.trim() || undefined
    });
    
    setNewApiKey('');
    setNewKeyWeight(1);
    setNewKeyWaitTime(60000);
    setNewKeyNotes('');
    loadData();
  };

  const handleRemoveApiKey = (keyId: string) => {
    if (!selectedUrl) return;
    
    rotationManager.removeAPIKey(selectedUrl, keyId);
    loadData();
  };

  const handleStrategyChange = (strategy: RotationStrategy) => {
    if (!selectedUrl) return;
    
    const config = rotationManager.getConfig();
    if (config.urlPools[selectedUrl]) {
      config.urlPools[selectedUrl].rotationStrategy = strategy;
      rotationManager['configManager'].save(config);
      loadData();
    }
  };

  const handleRecoverKey = (keyId: string) => {
    const config = rotationManager.getConfig();
    for (const pool of Object.values(config.urlPools)) {
      const key = pool.keys.find(k => k.id === keyId);
      if (key) {
        rotationManager['recoveryService'].manualRecoverKey(key);
        loadData();
        break;
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'waiting': return 'text-yellow-600';
      case 'failed': return 'text-red-600';
      case 'disabled': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '活跃';
      case 'waiting': return '等待中';
      case 'failed': return '失败';
      case 'disabled': return '已禁用';
      default: return '未知';
    }
  };

  const selectedPool = selectedUrl ? urlPools[selectedUrl] : null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">API密钥轮播管理</h2>
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={isEnabled}
                onChange={handleToggleRotation}
                className="rounded"
              />
              <span>启用轮播</span>
            </label>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* 左侧：URL列表 */}
          <div className="w-1/3 border-r p-4">
            <h3 className="font-medium mb-4">API端点</h3>
            
            {/* 添加新URL */}
            <div className="mb-4">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newUrl}
                  onChange={(e) => setNewUrl(e.target.value)}
                  placeholder="输入API端点URL"
                  className="flex-1 px-3 py-2 border rounded text-sm"
                />
                <button
                  onClick={handleAddUrl}
                  className="px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                >
                  添加
                </button>
              </div>
            </div>

            {/* URL列表 */}
            <div className="space-y-2">
              {Object.keys(urlPools).map((url) => (
                <div
                  key={url}
                  onClick={() => setSelectedUrl(url)}
                  className={`p-3 rounded cursor-pointer text-sm ${
                    selectedUrl === url
                      ? 'bg-blue-100 border-blue-300'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium truncate">{url}</div>
                  <div className="text-gray-500 text-xs">
                    {urlPools[url].keys.length} 个密钥
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 右侧：密钥管理 */}
          <div className="flex-1 p-4">
            {selectedPool ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium">密钥管理</h3>
                  <select
                    value={selectedPool.rotationStrategy}
                    onChange={(e) => handleStrategyChange(e.target.value as RotationStrategy)}
                    className="px-3 py-1 border rounded text-sm"
                  >
                    <option value="round-robin">轮询</option>
                    <option value="weighted">加权</option>
                    <option value="random">随机</option>
                    <option value="least-used">最少使用</option>
                  </select>
                </div>

                {/* 添加新密钥 */}
                <div className="mb-6 p-4 bg-gray-50 rounded">
                  <h4 className="font-medium mb-3">添加新密钥</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <input
                      type="text"
                      value={newApiKey}
                      onChange={(e) => setNewApiKey(e.target.value)}
                      placeholder="API密钥"
                      className="px-3 py-2 border rounded text-sm"
                    />
                    <input
                      type="number"
                      value={newKeyWeight}
                      onChange={(e) => setNewKeyWeight(Number(e.target.value))}
                      placeholder="权重"
                      min="1"
                      className="px-3 py-2 border rounded text-sm"
                    />
                    <input
                      type="number"
                      value={newKeyWaitTime}
                      onChange={(e) => setNewKeyWaitTime(Number(e.target.value))}
                      placeholder="等待时间(ms)"
                      min="1000"
                      className="px-3 py-2 border rounded text-sm"
                    />
                    <input
                      type="text"
                      value={newKeyNotes}
                      onChange={(e) => setNewKeyNotes(e.target.value)}
                      placeholder="备注"
                      className="px-3 py-2 border rounded text-sm"
                    />
                  </div>
                  <button
                    onClick={handleAddApiKey}
                    className="mt-3 px-4 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                  >
                    添加密钥
                  </button>
                </div>

                {/* 密钥列表 */}
                <div className="space-y-3">
                  {selectedPool.keys.map((key) => (
                    <div key={key.id} className="p-4 border rounded">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <span className={`font-medium ${getStatusColor(key.status)}`}>
                            {getStatusText(key.status)}
                          </span>
                          <span className="text-sm text-gray-500">
                            权重: {key.weight || 1}
                          </span>
                          <span className="text-sm text-gray-500">
                            使用: {key.usageCount}次
                          </span>
                          <span className="text-sm text-gray-500">
                            成功率: {key.successRate.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex gap-2">
                          {key.status === 'waiting' && (
                            <button
                              onClick={() => handleRecoverKey(key.id)}
                              className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
                            >
                              恢复
                            </button>
                          )}
                          <button
                            onClick={() => handleRemoveApiKey(key.id)}
                            className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                          >
                            删除
                          </button>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        密钥: {key.key.substring(0, 10)}...
                      </div>
                      {key.notes && (
                        <div className="text-sm text-gray-500 mt-1">
                          备注: {key.notes}
                        </div>
                      )}
                      {key.waitUntil && (
                        <div className="text-sm text-yellow-600 mt-1">
                          等待到: {new Date(key.waitUntil).toLocaleString()}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center text-gray-500 mt-8">
                请选择一个API端点来管理密钥
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
