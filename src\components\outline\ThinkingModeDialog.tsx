"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ThinkingMode } from './ThinkingModeSelector';

interface ThinkingModeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onModeSelect: (mode: ThinkingMode) => void;
  currentMode?: ThinkingMode;
}

/**
 * 思考模式选择弹窗组件
 * 在屏幕中央显示模式选择对话框
 */
const ThinkingModeDialog: React.FC<ThinkingModeDialogProps> = ({
  isOpen,
  onClose,
  onModeSelect,
  currentMode = 'standard'
}) => {
  const [selectedMode, setSelectedMode] = useState<ThinkingMode>(currentMode);

  const modes = [
    {
      id: 'standard' as ThinkingMode,
      title: '标准模式',
      subtitle: '快速直接生成',
      description: 'AI直接根据您的需求生成大纲节点，速度快，适合简单的创作需求。',
      icon: '⚡',
      color: 'from-green-500 to-emerald-500',
      features: [
        '快速响应，即时生成',
        '直接输出结果',
        '适合简单明确的需求',
        '节省时间和token消耗'
      ],
      pros: ['速度快', '操作简单', '成本低'],
      cons: ['缺少思考过程', '质量可能不够深入']
    },
    {
      id: 'thinking' as ThinkingMode,
      title: '思考模式',
      subtitle: '深度分析创作',
      description: 'AI先进行深度思考分析，然后基于思考内容生成大纲节点。您可以查看和编辑AI的思考过程，获得更高质量的创作建议。',
      icon: '💭',
      color: 'from-blue-500 to-purple-500',
      features: [
        '深度分析需求和背景',
        '可视化思考过程',
        '支持编辑思考内容',
        '生成高质量大纲节点'
      ],
      pros: ['质量更高', '过程透明', '可控制性强'],
      cons: ['耗时较长', 'token消耗更多']
    }
  ];

  const handleModeSelect = (mode: ThinkingMode) => {
    setSelectedMode(mode);
  };

  const handleConfirm = () => {
    onModeSelect(selectedMode);
    onClose();
  };

  const handleCancel = () => {
    setSelectedMode(currentMode);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleCancel}
          />
          
          {/* 弹窗内容 */}
          <motion.div
            className="relative bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          >
            {/* 头部 */}
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    选择AI工作模式
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    选择最适合您当前需求的AI工作方式
                  </p>
                </div>
                <button
                  onClick={handleCancel}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                </button>
              </div>
            </div>

            {/* 模式选择区域 */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {modes.map((mode) => {
                  const isSelected = selectedMode === mode.id;
                  
                  return (
                    <motion.div
                      key={mode.id}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                      onClick={() => handleModeSelect(mode.id)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {/* 选中指示器 */}
                      <div className="absolute top-4 right-4">
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          isSelected 
                            ? 'bg-blue-500 border-blue-500' 
                            : 'border-gray-300 dark:border-gray-600'
                        }`}>
                          {isSelected && (
                            <motion.svg
                              width="12"
                              height="12"
                              viewBox="0 0 24 24"
                              fill="white"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                            >
                              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </motion.svg>
                          )}
                        </div>
                      </div>

                      {/* 模式图标和标题 */}
                      <div className="flex items-center gap-3 mb-4">
                        <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${mode.color} flex items-center justify-center text-white text-xl`}>
                          {mode.icon}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {mode.title}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {mode.subtitle}
                          </p>
                        </div>
                      </div>

                      {/* 描述 */}
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        {mode.description}
                      </p>

                      {/* 特性列表 */}
                      <div className="space-y-2 mb-4">
                        {mode.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                            <div className="w-1.5 h-1.5 rounded-full bg-gray-400"></div>
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>

                      {/* 优缺点 */}
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <div className="text-green-600 dark:text-green-400 font-medium mb-1">优点</div>
                          {mode.pros.map((pro, index) => (
                            <div key={index} className="text-gray-500 dark:text-gray-400">
                              + {pro}
                            </div>
                          ))}
                        </div>
                        <div>
                          <div className="text-orange-600 dark:text-orange-400 font-medium mb-1">注意</div>
                          {mode.cons.map((con, index) => (
                            <div key={index} className="text-gray-500 dark:text-gray-400">
                              - {con}
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>

            {/* 底部操作区 */}
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  您可以随时在AI助手中切换模式
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    取消
                  </button>
                  <motion.button
                    onClick={handleConfirm}
                    className={`px-6 py-2 rounded-lg text-white font-medium transition-colors ${
                      selectedMode === 'thinking'
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600'
                        : 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    确认选择
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default ThinkingModeDialog;
