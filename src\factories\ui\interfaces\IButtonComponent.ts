import { IUIComponent } from './IUIComponent';

/**
 * 按钮类型
 */
export type ButtonType = 'primary' | 'secondary' | 'success' | 'danger' | 'info' | 'ghost';

/**
 * 按钮尺寸
 */
export type ButtonSize = 'small' | 'medium' | 'large';

/**
 * 按钮组件接口
 */
export interface IButtonComponent extends IUIComponent {
  /**
   * 设置按钮文本
   * @param text 按钮文本
   */
  setText(text: string): void;
  
  /**
   * 设置按钮类型
   * @param type 按钮类型
   */
  setType(type: ButtonType): void;
  
  /**
   * 设置按钮尺寸
   * @param size 按钮尺寸
   */
  setSize(size: ButtonSize): void;
  
  /**
   * 设置按钮是否禁用
   * @param disabled 是否禁用
   */
  setDisabled(disabled: boolean): void;
  
  /**
   * 设置按钮图标
   * @param icon 图标JSX元素
   */
  setIcon(icon: React.ReactNode): void;
  
  /**
   * 设置点击事件处理函数
   * @param handler 点击事件处理函数
   */
  onClick(handler: () => void): void;
}
