"use client";

import React, { useEffect } from 'react';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';

interface APISettingsDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
}

/**
 * API设置弹窗适配器组件
 */
const APISettingsDialog: React.FC<APISettingsDialogProps> = ({
  isOpen = false,
  onClose
}) => {
  // 创建设置工厂和组件
  const settingsFactory = createSettingsFactory();
  const apiSettingsDialog = settingsFactory.createAPISettingsDialogComponent();
  
  // 设置初始状态
  useEffect(() => {
    apiSettingsDialog.setIsOpen(isOpen);
  }, [isOpen]);
  
  // 设置回调
  useEffect(() => {
    // 监听关闭事件
    const originalSetIsOpen = apiSettingsDialog.setIsOpen;
    apiSettingsDialog.setIsOpen = (newIsOpen: boolean) => {
      originalSetIsOpen.call(apiSettingsDialog, newIsOpen);
      if (!newIsOpen && onClose) {
        onClose();
      }
    };
    
    return () => {
      // 恢复原始方法
      apiSettingsDialog.setIsOpen = originalSetIsOpen;
    };
  }, [onClose]);
  
  return <>{apiSettingsDialog.render()}</>;
};

export default APISettingsDialog;
