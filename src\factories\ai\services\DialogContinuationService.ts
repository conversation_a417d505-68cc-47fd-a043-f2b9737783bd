"use client";

import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { ConversationMessage } from './AIWritingService';
import {
  ContinueMode as UtilContinueMode,
  handleConversationContinuation,
  buildContinuationMessages
} from '@/utils/ai/ConversationContinuationUtil';

/**
 * 对话继续模式
 */
export type ContinueMode = UtilContinueMode;

/**
 * 对话继续参数
 */
export interface DialogContinuationParams {
  // 当前内容
  currentContent: string;

  // 用户的继续提示
  continuePrompt: string;

  // 继续模式
  continueMode: ContinueMode;

  // 书籍ID（用于本地存储）
  bookId: string;

  // 现有对话历史
  conversationHistory?: ConversationMessage[];

  // 服务类型（writing 或 rewrite）
  serviceType?: 'writing' | 'rewrite';

  // 注意：始终保留完整上下文，不再支持简化上下文模式

  // 关联消息（如章节、角色等信息）
  relatedMessages?: ConversationMessage[];
}

/**
 * 对话继续结果
 */
export interface DialogContinuationResult {
  // 用于显示的前缀（包含原内容和用户提示）
  prefix: string;

  // 用户提示
  userPrompt: string;

  // 更新后的对话历史
  updatedHistory: ConversationMessage[];
}

/**
 * 对话继续服务接口
 */
export interface DialogContinuationServiceInterface {
  /**
   * 处理继续对话
   * @param params 继续对话参数
   * @returns 继续对话结果
   */
  handleContinuation(params: DialogContinuationParams): DialogContinuationResult;

  /**
   * 构建消息
   * @param messageBuilder 消息构建器
   * @param conversationHistory 对话历史
   * @param continueMode 继续模式
   * @param continuePrompt 继续提示
   * @param serviceType 服务类型（writing 或 rewrite）
   */
  buildMessages(
    messageBuilder: MessageBuilder,
    conversationHistory: ConversationMessage[],
    continueMode: ContinueMode,
    continuePrompt?: string,
    serviceType?: 'writing' | 'rewrite'
  ): void;
}

/**
 * 对话继续服务实现
 */
export class DialogContinuationService implements DialogContinuationServiceInterface {
  /**
   * 处理继续对话
   * @param params 继续对话参数
   * @returns 继续对话结果
   */
  handleContinuation(params: DialogContinuationParams): DialogContinuationResult {
    // 使用可复用的工具函数处理对话继续
    return handleConversationContinuation(params);
  }

  /**
   * 构建消息
   * @param messageBuilder 消息构建器
   * @param conversationHistory 对话历史
   * @param continueMode 继续模式
   * @param continuePrompt 继续提示
   * @param serviceType 服务类型（writing 或 rewrite）
   */
  buildMessages(
    messageBuilder: MessageBuilder,
    conversationHistory: ConversationMessage[],
    continueMode: ContinueMode,
    continuePrompt?: string,
    serviceType: 'writing' | 'rewrite' = 'writing'
  ): void {
    // 使用可复用的工具函数构建消息
    buildContinuationMessages(
      messageBuilder,
      conversationHistory,
      continueMode,
      continuePrompt,
      serviceType
    );
  }
}

// 导出服务实例
export const dialogContinuationService = new DialogContinuationService();
