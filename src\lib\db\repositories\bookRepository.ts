import { v4 as uuidv4 } from 'uuid';
import { db, Book } from '../dexie';

export interface IBookRepository {
  getAll(): Promise<Book[]>;
  getById(id: string): Promise<Book | undefined>;
  create(book: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, book: Partial<Book>): Promise<void>;
  delete(id: string): Promise<void>;
  getWordCount(id: string): Promise<number>;
  updateWordCount(id: string): Promise<void>;
}

export class BookRepository implements IBookRepository {
  async getAll(): Promise<Book[]> {
    return await db.books.toArray();
  }

  async getById(id: string): Promise<Book | undefined> {
    return await db.books.get(id);
  }

  async create(book: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();
    
    await db.books.add({
      ...book,
      id,
      createdAt: now,
      updatedAt: now
    });
    
    return id;
  }

  async update(id: string, book: Partial<Book>): Promise<void> {
    await db.books.update(id, {
      ...book,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    // 删除书籍时，同时删除相关的章节、人物、术语和世界观
    await db.transaction('rw', 
      [db.books, db.chapters, db.characters, db.terminology, db.worldBuilding, db.aiSessions], 
      async () => {
        await db.chapters.where('bookId').equals(id).delete();
        await db.characters.where('bookId').equals(id).delete();
        await db.terminology.where('bookId').equals(id).delete();
        await db.worldBuilding.where('bookId').equals(id).delete();
        await db.aiSessions.where('bookId').equals(id).delete();
        await db.books.delete(id);
    });
  }

  async getWordCount(id: string): Promise<number> {
    const book = await db.books.get(id);
    return book?.wordCount || 0;
  }

  async updateWordCount(id: string): Promise<void> {
    // 计算所有章节的总字数
    const chapters = await db.chapters.where('bookId').equals(id).toArray();
    const totalWordCount = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0);
    
    // 更新书籍的总字数
    await db.books.update(id, {
      wordCount: totalWordCount,
      updatedAt: new Date()
    });
  }
}

// 创建并导出仓库实例
export const bookRepository = new BookRepository();
