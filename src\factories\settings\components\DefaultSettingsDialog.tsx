"use client";

import React, { useState, useEffect, useRef } from 'react';
import { ISettingsDialogComponent } from '../interfaces';

/**
 * 默认设置弹窗组件实现
 */
export class DefaultSettingsDialogComponent implements ISettingsDialogComponent {
  private isOpen: boolean = false;
  private currentFont: string = 'roboto';
  private fontChangeCallback: ((font: string) => void) | null = null;

  /**
   * 设置是否显示弹窗
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 设置当前字体
   * @param font 字体ID
   */
  setCurrentFont(font: string): void {
    this.currentFont = font;
    if (this.fontChangeCallback) {
      this.fontChangeCallback(font);
    }
  }

  /**
   * 获取当前字体
   * @returns 当前字体ID
   */
  getCurrentFont(): string {
    return this.currentFont;
  }

  /**
   * 设置字体变更回调
   * @param callback 回调函数
   */
  onFontChange(callback: (font: string) => void): void {
    this.fontChangeCallback = callback;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const SettingsDialog = () => {
      const [isOpen, setIsOpen] = useState(this.isOpen);
      const [currentFont, setCurrentFont] = useState(this.currentFont);
      const dialogRef = useRef<HTMLDivElement>(null);

      // 字体选项
      const fontOptions = [
        { id: 'roboto', name: 'Roboto字体', fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif' },
        { id: 'serif', name: '衬线字体', fontFamily: 'Georgia, "Times New Roman", serif' },
        { id: 'sans', name: '无衬线字体', fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif' },
        { id: 'siyuan', name: '思源字体', fontFamily: '"Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", sans-serif' },
        { id: 'mono', name: '等宽字体', fontFamily: '"Courier New", monospace' }
      ];

      // 监听isOpen变化
      useEffect(() => {
        setIsOpen(this.isOpen);
      }, [this.isOpen]);

      // 监听currentFont变化
      useEffect(() => {
        setCurrentFont(this.currentFont);
      }, [this.currentFont]);

      // 点击外部关闭弹窗
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
            handleClose();
          }
        };

        if (isOpen) {
          document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isOpen]);

      // 处理关闭弹窗
      const handleClose = () => {
        setIsOpen(false);
        this.setIsOpen(false);
      };

      // 处理字体变更
      const handleFontChange = (fontId: string) => {
        setCurrentFont(fontId);
        this.setCurrentFont(fontId);
      };

      if (!isOpen) return null;

      // 定义动画样式
      const overlayStyle = {
        opacity: isOpen ? 1 : 0,
        backdropFilter: isOpen ? 'blur(8px)' : 'blur(0px)',
        transition: 'all 0.5s cubic-bezier(0.16, 1, 0.3, 1)'
      };

      const dialogStyle = {
        transform: isOpen ? 'scale(1) translateY(0)' : 'scale(0.9) translateY(-20px)',
        opacity: isOpen ? 1 : 0,
        transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)'
      };

      return (
        <div
          className="fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-30 backdrop-blur-sm"
          style={overlayStyle}
        >
          <div
            ref={dialogRef}
            className="bg-white rounded-2xl shadow-2xl w-96 max-w-full overflow-hidden"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderColor: 'var(--color-secondary)',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 0, 0, 0.05)',
              ...dialogStyle
            }}
          >
            <div className="flex justify-between items-center p-4 border-b" style={{ borderColor: 'var(--color-secondary)' }}>
              <h2
                className="text-xl font-medium transition-all duration-500"
                style={{
                  color: 'var(--color-primary)',
                  transform: isOpen ? 'translateY(0)' : 'translateY(-20px)',
                  opacity: isOpen ? 1 : 0,
                  transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s'
                }}
              >
                设置
              </h2>
              <button
                onClick={handleClose}
                className="text-gray-500 hover:text-gray-700 transition-all duration-300 transform hover:rotate-90"
                style={{
                  opacity: isOpen ? 1 : 0,
                  transition: 'all 0.3s ease-in-out 0.2s'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div
              className="p-6"
              style={{
                opacity: isOpen ? 1 : 0,
                transform: isOpen ? 'translateY(0)' : 'translateY(20px)',
                transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s'
              }}
            >
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>字体设置</h3>
                <div className="space-y-3">
                  {fontOptions.map(font => (
                    <div key={font.id} className="flex items-center">
                      <input
                        type="radio"
                        id={`font-${font.id}`}
                        name="font"
                        checked={currentFont === font.id}
                        onChange={() => handleFontChange(font.id)}
                        className="mr-3 h-4 w-4"
                        style={{
                          accentColor: 'var(--color-primary)',
                          cursor: 'pointer'
                        }}
                      />
                      <label
                        htmlFor={`font-${font.id}`}
                        className="text-base cursor-pointer"
                        style={{
                          fontFamily: font.fontFamily,
                          color: 'var(--color-text-primary)'
                        }}
                      >
                        {font.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>主题设置</h3>
                <div className="flex items-center justify-between">
                  <span className="text-base" style={{ color: 'var(--color-text-primary)' }}>暗色模式</span>
                  <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full">
                    <input
                      type="checkbox"
                      id="toggle"
                      className="absolute w-6 h-6 transition duration-200 ease-in-out transform bg-white border-4 rounded-full appearance-none cursor-pointer checked:translate-x-6 checked:border-primary"
                      style={{
                        borderColor: 'var(--color-secondary)',
                        left: '0',
                        top: '0'
                      }}
                    />
                    <label
                      htmlFor="toggle"
                      className="block w-full h-full overflow-hidden rounded-full cursor-pointer"
                      style={{ backgroundColor: 'var(--color-secondary)' }}
                    ></label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 rounded transition-colors duration-200"
                  style={{
                    backgroundColor: 'var(--color-primary)',
                    color: 'white'
                  }}
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    };

    return <SettingsDialog />;
  }
}
