"use client";

import React, { useState, useEffect } from 'react';
import DOMPurify from 'isomorphic-dompurify';

interface HTMLPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  htmlContent: string;
  title?: string;
}

/**
 * HTML预览模态窗口组件
 * 用于在独立弹窗中安全地渲染HTML内容，特别是图表等可视化内容
 */
const HTMLPreviewModal: React.FC<HTMLPreviewModalProps> = ({
  isOpen,
  onClose,
  htmlContent,
  title = '预览HTML内容'
}) => {
  const [sanitizedHtml, setSanitizedHtml] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && htmlContent) {
      try {
        setIsLoading(true);
        setError(null);

        // 记录原始HTML内容
        console.log('原始HTML内容长度:', htmlContent.length);
        console.log('原始HTML内容预览:', htmlContent.substring(0, 200) + '...');

        // 直接使用原始HTML内容，不进行净化处理
        // 这样可以确保复杂的HTML结构（如D3图表）能够完整渲染
        setSanitizedHtml(htmlContent);

        console.log('HTML内容已设置，不进行DOMPurify净化');

        // 模拟加载过程
        setTimeout(() => {
          setIsLoading(false);
          console.log('HTML预览加载完成');
        }, 500);
      } catch (err) {
        console.error('HTML内容处理失败:', err);
        setError('HTML内容处理失败，请检查内容格式');
        setIsLoading(false);
      }
    }
  }, [isOpen, htmlContent]);

  // 如果模态窗口未打开，不渲染任何内容
  if (!isOpen) return null;

  // 切换全屏模式
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 复制HTML代码
  const copyHtmlCode = () => {
    navigator.clipboard.writeText(htmlContent)
      .then(() => {
        alert('HTML代码已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
        alert('复制失败');
      });
  };

  // 阻止事件冒泡
  const handleModalClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-fadeIn"
      onClick={handleModalClick} // 阻止点击事件穿透
    >
      <div
        className={`bg-[var(--color-primary-bg)] rounded-2xl shadow-xl flex flex-col overflow-hidden transition-all duration-300 ${
          isFullscreen ? 'w-full h-full rounded-none' : 'w-[90%] max-w-4xl h-[80%]'
        }`}
        onClick={handleModalClick} // 确保内部点击也不会穿透
      >
        {/* 标题栏 */}
        <div className="p-4 border-b border-[var(--color-secondary)] border-opacity-30 flex justify-between items-center">
          <h3 className="text-xl font-bold text-[var(--color-primary)]">{title}</h3>
          <div className="flex space-x-2">
            <button
              className="p-2 text-[var(--color-text-secondary)] hover:text-[var(--color-primary)] transition-colors"
              onClick={copyHtmlCode}
              title="复制HTML代码"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
            </button>
            <button
              className="p-2 text-[var(--color-text-secondary)] hover:text-[var(--color-primary)] transition-colors"
              onClick={toggleFullscreen}
              title={isFullscreen ? "退出全屏" : "全屏显示"}
            >
              {isFullscreen ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                </svg>
              )}
            </button>
            <button
              className="p-2 text-[var(--color-text-secondary)] hover:text-[var(--color-danger)] transition-colors"
              onClick={onClose}
              title="关闭"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-6 overflow-auto bg-white" onClick={handleModalClick}>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-full">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[var(--color-primary)] mb-6"></div>
              <span className="text-lg text-[var(--color-text-primary)]">加载中，请稍候...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full text-[var(--color-danger)]">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-lg">{error}</p>
            </div>
          ) : (
            <div
              className="html-preview-content w-full h-full"
              dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
              ref={(node) => {
                // 在内容加载后执行脚本
                if (node && !isLoading) {
                  console.log('HTML预览内容已加载，准备渲染');

                  // 总是使用iframe进行渲染，以确保完整的隔离环境
                  // 这样可以避免React的虚拟DOM与直接DOM操作的冲突
                  console.log('使用iframe进行隔离渲染');

                  // 创建一个iframe来渲染HTML内容
                  const iframe = document.createElement('iframe');
                  iframe.style.width = '100%';
                  iframe.style.height = '100%';
                  iframe.style.border = 'none';
                  iframe.sandbox = 'allow-scripts allow-same-origin allow-popups allow-modals';

                  // 清空当前节点并添加iframe
                  while (node.firstChild) {
                    node.removeChild(node.firstChild);
                  }
                  node.appendChild(iframe);

                  // 检查是否是完整的HTML文档
                  const isFullHtml = sanitizedHtml.trim().startsWith('<!DOCTYPE html>') ||
                                    sanitizedHtml.trim().startsWith('<html>');

                  // 检查是否包含D3.js代码或特殊标记
                  const containsD3 = sanitizedHtml.includes('d3.') ||
                                    sanitizedHtml.includes('d3.select') ||
                                    sanitizedHtml.includes('<!-- D3_CONTENT_MARKER -->');

                  // 准备要写入iframe的HTML内容
                  let finalHtml = sanitizedHtml;

                  // 如果是D3.js内容，使用特殊处理
                  if (containsD3) {
                    console.log('检测到D3.js内容，使用特殊处理');

                    // 移除可能的D3标记
                    let cleanContent = sanitizedHtml.replace('<!-- D3_CONTENT_MARKER -->', '').trim();

                    // 检查是否已经是完整的HTML文档
                    if (isFullHtml) {
                      finalHtml = cleanContent;
                    } else {
                      finalHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>D3.js可视化</title>
  <style>
    body { margin: 0; padding: 20px; font-family: system-ui, -apple-system, sans-serif; }
    svg { display: block; margin: 0 auto; }
    .chart-container { width: 100%; height: 100%; min-height: 300px; }
  </style>
  <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
  <div id="chart-container" class="chart-container"></div>
  <script>
    // 等待页面完全加载
    window.onload = function() {
      try {
        // 将D3代码包装在函数中执行，确保正确的作用域
        (function() {
          // 确保d3可用
          if (typeof d3 === 'undefined') {
            throw new Error('D3库未加载，请检查网络连接');
          }

          // 执行D3代码
          ${cleanContent}
        })();
      } catch (error) {
        console.error('D3.js执行错误:', error);
        document.getElementById('chart-container').innerHTML =
          '<div style="color: red; padding: 20px; border: 1px solid red; border-radius: 5px;">' +
          '<h3>D3.js代码执行出错</h3>' +
          '<p>' + error.message + '</p>' +
          '</div>';
      }
    };
  </script>
</body>
</html>`;
                    }
                  }
                  // 如果不是D3.js内容，但也不是完整的HTML文档，则进行常规包装
                  else if (!isFullHtml) {
                    finalHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>HTML内容预览</title>
  <style>
    body { margin: 0; padding: 20px; font-family: system-ui, -apple-system, sans-serif; }
    svg { display: block; margin: 0 auto; }
    #content-container { width: 100%; min-height: 100px; }
  </style>
</head>
<body>
  <div id="content-container">
    ${sanitizedHtml}
  </div>
</body>
</html>`;
                  }

                  // 写入HTML内容到iframe
                  const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                  iframeDoc.open();
                  iframeDoc.write(finalHtml);
                  iframeDoc.close();

                  console.log('HTML内容已加载到iframe中');
                }
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default HTMLPreviewModal;
