"use client";

import React from 'react';

interface FocusGlowEffectProps {
  intensity?: 'low' | 'medium' | 'high';
}

/**
 * 焦点光晕效果组件
 * 当编辑器获得焦点时显示柔和的光晕效果
 */
export const FocusGlowEffect: React.FC<FocusGlowEffectProps> = ({
  intensity = 'medium'
}) => {
  // 根据强度调整光晕参数
  const getGlowParams = () => {
    switch (intensity) {
      case 'low':
        return {
          blur: '8px',
          spread: '2px',
          opacity: 0.3,
          scale: 1.01
        };
      case 'medium':
        return {
          blur: '12px',
          spread: '3px',
          opacity: 0.4,
          scale: 1.015
        };
      case 'high':
        return {
          blur: '16px',
          spread: '4px',
          opacity: 0.5,
          scale: 1.02
        };
      default:
        return {
          blur: '12px',
          spread: '3px',
          opacity: 0.4,
          scale: 1.015
        };
    }
  };

  const glowParams = getGlowParams();

  return (
    <div className="absolute inset-0 pointer-events-none rounded-xl overflow-hidden">
      <svg
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{
          animation: 'focusGlowAppear 0.3s cubic-bezier(0.2, 0.8, 0.2, 1) forwards'
        }}
      >
        <defs>
          {/* 光晕滤镜 */}
          <filter id="focusGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          {/* 外部光晕渐变 */}
          <radialGradient id="outerGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#D4AF37" stopOpacity="0" />
            <stop offset="70%" stopColor="#D4AF37" stopOpacity={glowParams.opacity * 0.3} />
            <stop offset="85%" stopColor="#FFD700" stopOpacity={glowParams.opacity * 0.6} />
            <stop offset="95%" stopColor="#D4AF37" stopOpacity={glowParams.opacity * 0.4} />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 内部光晕渐变 */}
          <linearGradient id="innerGlow" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFD700" stopOpacity={glowParams.opacity * 0.2} />
            <stop offset="50%" stopColor="#D4AF37" stopOpacity={glowParams.opacity * 0.1} />
            <stop offset="100%" stopColor="#FFD700" stopOpacity={glowParams.opacity * 0.2} />
          </linearGradient>
        </defs>

        {/* 外部光晕 */}
        <rect
          x="-5"
          y="-5"
          width="110"
          height="110"
          fill="url(#outerGlow)"
          rx="8"
          style={{
            filter: `blur(${glowParams.blur})`,
            animation: 'glowPulse 2s ease-in-out infinite alternate'
          }}
        />

        {/* 边框光晕 */}
        <rect
          x="0"
          y="0"
          width="100"
          height="100"
          fill="none"
          stroke="url(#innerGlow)"
          strokeWidth="0.5"
          rx="6"
          style={{
            filter: 'url(#focusGlow)',
            animation: 'borderGlow 1.5s ease-in-out infinite alternate'
          }}
        />

        {/* 角落强调光点 */}
        <g className="corner-highlights">
          {/* 左上角 */}
          <circle
            cx="8"
            cy="8"
            r="2"
            fill="#FFD700"
            opacity={glowParams.opacity * 0.6}
            style={{
              filter: `blur(1px)`,
              animation: 'cornerSparkle 3s ease-in-out infinite',
              animationDelay: '0s'
            }}
          />
          
          {/* 右上角 */}
          <circle
            cx="92"
            cy="8"
            r="2"
            fill="#FFD700"
            opacity={glowParams.opacity * 0.6}
            style={{
              filter: `blur(1px)`,
              animation: 'cornerSparkle 3s ease-in-out infinite',
              animationDelay: '0.75s'
            }}
          />
          
          {/* 右下角 */}
          <circle
            cx="92"
            cy="92"
            r="2"
            fill="#FFD700"
            opacity={glowParams.opacity * 0.6}
            style={{
              filter: `blur(1px)`,
              animation: 'cornerSparkle 3s ease-in-out infinite',
              animationDelay: '1.5s'
            }}
          />
          
          {/* 左下角 */}
          <circle
            cx="8"
            cy="92"
            r="2"
            fill="#FFD700"
            opacity={glowParams.opacity * 0.6}
            style={{
              filter: `blur(1px)`,
              animation: 'cornerSparkle 3s ease-in-out infinite',
              animationDelay: '2.25s'
            }}
          />
        </g>

        <style>
          {`
            @keyframes focusGlowAppear {
              0% {
                opacity: 0;
                transform: scale(0.95);
              }
              100% {
                opacity: 1;
                transform: scale(${glowParams.scale});
              }
            }
            
            @keyframes glowPulse {
              0% {
                opacity: ${glowParams.opacity * 0.6};
                transform: scale(1);
              }
              100% {
                opacity: ${glowParams.opacity};
                transform: scale(1.02);
              }
            }
            
            @keyframes borderGlow {
              0% {
                opacity: 0.4;
                stroke-width: 0.3;
              }
              100% {
                opacity: 0.8;
                stroke-width: 0.7;
              }
            }
            
            @keyframes cornerSparkle {
              0%, 100% {
                opacity: ${glowParams.opacity * 0.3};
                transform: scale(0.8);
              }
              50% {
                opacity: ${glowParams.opacity * 0.8};
                transform: scale(1.2);
              }
            }
            
            @media (prefers-reduced-motion: reduce) {
              svg {
                animation: none !important;
                opacity: 1 !important;
                transform: scale(1) !important;
              }
              
              rect, circle {
                animation: none !important;
                opacity: ${glowParams.opacity * 0.5} !important;
                transform: scale(1) !important;
              }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default FocusGlowEffect;
