/**
 * 全局布局常量管理
 * 统一管理编辑器和其他组件的布局尺寸，确保一致性
 */

// 基础布局尺寸
export const LAYOUT_CONSTANTS = {
  // 头部区域高度
  HEADER_HEIGHT: 120,
  
  // 侧边栏宽度
  SIDEBAR_WIDTH: 256,
  
  // 基础内边距
  BASE_PADDING: 24,
  
  // 编辑器特定内边距
  EDITOR_PADDING: 16,
  
  // 底部状态栏高度
  FOOTER_HEIGHT: 60,
  
  // 最小编辑器高度
  MIN_EDITOR_HEIGHT: 400,
  
  // 最大编辑器高度比例（相对于视口）
  MAX_EDITOR_HEIGHT_RATIO: 0.8,
} as const;

// 编辑器布局计算
export const EDITOR_LAYOUT = {
  /**
   * 编辑器容器高度
   * 计算公式：100vh - 头部高度 - 基础内边距
   */
  get CONTAINER_HEIGHT() {
    return `calc(100vh - ${LAYOUT_CONSTANTS.HEADER_HEIGHT + LAYOUT_CONSTANTS.BASE_PADDING * 2}px)`;
  },
  
  /**
   * 文本区域高度
   * 计算公式：容器高度 - 编辑器内边距
   */
  get TEXTAREA_HEIGHT() {
    return `calc(100vh - ${LAYOUT_CONSTANTS.HEADER_HEIGHT + LAYOUT_CONSTANTS.BASE_PADDING * 2 + LAYOUT_CONSTANTS.EDITOR_PADDING * 2}px)`;
  },
  
  /**
   * 编辑器最小高度
   */
  get MIN_HEIGHT() {
    return `${LAYOUT_CONSTANTS.MIN_EDITOR_HEIGHT}px`;
  },
  
  /**
   * 编辑器最大高度
   */
  get MAX_HEIGHT() {
    return `${Math.round(window?.innerHeight * LAYOUT_CONSTANTS.MAX_EDITOR_HEIGHT_RATIO) || 600}px`;
  },
  
  /**
   * 获取响应式高度
   * @param screenHeight 屏幕高度
   * @returns 计算后的高度值
   */
  getResponsiveHeight(screenHeight: number = window?.innerHeight || 768) {
    const calculatedHeight = screenHeight - LAYOUT_CONSTANTS.HEADER_HEIGHT - LAYOUT_CONSTANTS.BASE_PADDING * 2;
    const minHeight = LAYOUT_CONSTANTS.MIN_EDITOR_HEIGHT;
    const maxHeight = Math.round(screenHeight * LAYOUT_CONSTANTS.MAX_EDITOR_HEIGHT_RATIO);
    
    return Math.max(minHeight, Math.min(calculatedHeight, maxHeight));
  }
} as const;

// CSS变量名称常量
export const CSS_VARIABLES = {
  HEADER_HEIGHT: '--editor-header-height',
  SIDEBAR_WIDTH: '--editor-sidebar-width',
  BASE_PADDING: '--editor-base-padding',
  EDITOR_PADDING: '--editor-padding',
  CONTAINER_HEIGHT: '--editor-container-height',
  TEXTAREA_HEIGHT: '--editor-textarea-height',
  MIN_HEIGHT: '--editor-min-height',
  MAX_HEIGHT: '--editor-max-height',
} as const;

// 生成CSS变量值的工具函数
export const generateCSSVariables = () => {
  return {
    [CSS_VARIABLES.HEADER_HEIGHT]: `${LAYOUT_CONSTANTS.HEADER_HEIGHT}px`,
    [CSS_VARIABLES.SIDEBAR_WIDTH]: `${LAYOUT_CONSTANTS.SIDEBAR_WIDTH}px`,
    [CSS_VARIABLES.BASE_PADDING]: `${LAYOUT_CONSTANTS.BASE_PADDING}px`,
    [CSS_VARIABLES.EDITOR_PADDING]: `${LAYOUT_CONSTANTS.EDITOR_PADDING}px`,
    [CSS_VARIABLES.CONTAINER_HEIGHT]: EDITOR_LAYOUT.CONTAINER_HEIGHT,
    [CSS_VARIABLES.TEXTAREA_HEIGHT]: EDITOR_LAYOUT.TEXTAREA_HEIGHT,
    [CSS_VARIABLES.MIN_HEIGHT]: EDITOR_LAYOUT.MIN_HEIGHT,
    [CSS_VARIABLES.MAX_HEIGHT]: EDITOR_LAYOUT.MAX_HEIGHT,
  };
};

// 响应式断点
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
  LARGE: 1536,
} as const;

// 响应式布局配置
export const RESPONSIVE_LAYOUT = {
  MOBILE: {
    HEADER_HEIGHT: 80,
    SIDEBAR_WIDTH: 0, // 移动端隐藏侧边栏
    BASE_PADDING: 16,
    EDITOR_PADDING: 12,
  },
  TABLET: {
    HEADER_HEIGHT: 100,
    SIDEBAR_WIDTH: 200,
    BASE_PADDING: 20,
    EDITOR_PADDING: 14,
  },
  DESKTOP: {
    ...LAYOUT_CONSTANTS,
  },
} as const;

/**
 * 根据屏幕宽度获取响应式布局配置
 * @param screenWidth 屏幕宽度
 * @returns 对应的布局配置
 */
export const getResponsiveLayout = (screenWidth: number = window?.innerWidth || 1024) => {
  if (screenWidth < BREAKPOINTS.MOBILE) {
    return RESPONSIVE_LAYOUT.MOBILE;
  } else if (screenWidth < BREAKPOINTS.TABLET) {
    return RESPONSIVE_LAYOUT.TABLET;
  } else {
    return RESPONSIVE_LAYOUT.DESKTOP;
  }
};

/**
 * 布局工具函数
 */
export const LayoutUtils = {
  /**
   * 检查是否为移动端
   */
  isMobile: () => (window?.innerWidth || 1024) < BREAKPOINTS.MOBILE,
  
  /**
   * 检查是否为平板端
   */
  isTablet: () => {
    const width = window?.innerWidth || 1024;
    return width >= BREAKPOINTS.MOBILE && width < BREAKPOINTS.DESKTOP;
  },
  
  /**
   * 检查是否为桌面端
   */
  isDesktop: () => (window?.innerWidth || 1024) >= BREAKPOINTS.DESKTOP,
  
  /**
   * 获取当前设备类型
   */
  getDeviceType: () => {
    const width = window?.innerWidth || 1024;
    if (width < BREAKPOINTS.MOBILE) return 'mobile';
    if (width < BREAKPOINTS.DESKTOP) return 'tablet';
    return 'desktop';
  },
  
  /**
   * 计算安全的编辑器高度
   * @param availableHeight 可用高度
   * @returns 安全的编辑器高度
   */
  getSafeEditorHeight: (availableHeight: number) => {
    const layout = getResponsiveLayout();
    const usedHeight = layout.HEADER_HEIGHT + layout.BASE_PADDING * 2 + layout.EDITOR_PADDING * 2;
    const calculatedHeight = availableHeight - usedHeight;
    
    return Math.max(LAYOUT_CONSTANTS.MIN_EDITOR_HEIGHT, calculatedHeight);
  }
};
