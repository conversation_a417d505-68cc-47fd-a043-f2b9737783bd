"use client";

import React, { useState, useEffect } from 'react';
import { Terminology } from '@/lib/db/dexie';

interface TerminologyBatchUpdateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (updates: BatchUpdateData) => Promise<void>;
  selectedTerminologyIds: string[];
  terminologies: Terminology[];
}

export interface BatchUpdateData {
  field: string;
  value: string;
  terminologyIds: string[];
}

/**
 * 术语批量更新对话框组件
 */
export const TerminologyBatchUpdateDialog: React.FC<TerminologyBatchUpdateDialogProps> = ({
  isOpen,
  onClose,
  onUpdate,
  selectedTerminologyIds,
  terminologies
}) => {
  // 更新字段
  const [field, setField] = useState<string>('category');
  // 更新值
  const [value, setValue] = useState<string>('');
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);

  // 当对话框打开时，重置状态
  useEffect(() => {
    if (isOpen) {
      setField('category');
      setValue('');
      setError(null);
    }
  }, [isOpen]);

  // 处理更新
  const handleUpdate = async () => {
    if (!field || !value) {
      setError('请选择要更新的字段和值');
      return;
    }

    if (selectedTerminologyIds.length === 0) {
      setError('请先选择要更新的术语');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await onUpdate({
        field,
        value,
        terminologyIds: selectedTerminologyIds
      });

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新术语时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取选中的术语名称列表
  const getSelectedTerminologyNames = () => {
    return terminologies
      .filter(term => selectedTerminologyIds.includes(term.id || ''))
      .map(term => term.name);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[500px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>批量更新术语</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-6">
            {/* 选中的术语 */}
            <div>
              <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>选中的术语</h3>
              <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[150px] overflow-y-auto"
                style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
              >
                {selectedTerminologyIds.length === 0 ? (
                  <div className="text-gray-500 text-center py-2">
                    没有选中的术语
                  </div>
                ) : (
                  <div className="space-y-1">
                    {getSelectedTerminologyNames().map((name, index) => (
                      <div key={index} className="text-gray-700">
                        {name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                已选择 {selectedTerminologyIds.length} 个术语
              </p>
            </div>

            {/* 更新字段 */}
            <div>
              <label htmlFor="field" className="block text-gray-700 mb-1">要更新的字段</label>
              <select
                id="field"
                value={field}
                onChange={(e) => setField(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderColor: 'rgba(139, 69, 19, 0.2)'
                }}
              >
                <option value="category">类别</option>
                <option value="attributes.importance">重要性</option>
              </select>
            </div>

            {/* 更新值 */}
            <div>
              <label htmlFor="value" className="block text-gray-700 mb-1">新值</label>
              {field === 'category' ? (
                <select
                  id="value"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                >
                  <option value="">选择类别</option>
                  <option value="item">物品/道具</option>
                  <option value="skill">技能/能力</option>
                  <option value="organization">组织/势力</option>
                  <option value="location">地点/区域</option>
                  <option value="concept">概念/规则</option>
                  <option value="event">事件/历史</option>
                  <option value="system">系统/机制</option>
                  <option value="creature">生物/种族</option>
                  <option value="other">其他</option>
                </select>
              ) : field === 'attributes.importance' ? (
                <select
                  id="value"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                >
                  <option value="">选择重要性</option>
                  <option value="1">⭐ 次要术语 - 背景补充</option>
                  <option value="2">⭐⭐ 支持术语 - 丰富世界观</option>
                  <option value="3">⭐⭐⭐ 重要术语 - 影响情节</option>
                  <option value="4">⭐⭐⭐⭐ 核心术语 - 关键设定</option>
                  <option value="5">⭐⭐⭐⭐⭐ 关键术语 - 世界基石</option>
                </select>
              ) : (
                <input
                  type="text"
                  id="value"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="输入新值"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
              )}
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </button>

          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
            }}
            onClick={handleUpdate}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
              </>
            ) : '更新'}
          </button>
        </div>
      </div>
    </div>
  );
};
