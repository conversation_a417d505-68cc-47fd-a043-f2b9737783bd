"use client";

import OpenAI from 'openai';
import { ConversationMessage } from '@/factories/ai/services/AIWritingService';
import {
  DualAIConfig,
  AIModelType,
  AIFunctionType,
  AIResponse,
  CallOptions,
  FUNCTION_MODEL_MAPPING,
  ModelConfig
} from '@/types/DualAIConfig';

/**
 * API客户端基类 - 使用OpenAI SDK
 */
class APIClient {
  private config: ModelConfig;
  private sharedConfig: any;
  private openai: OpenAI;

  constructor(modelConfig: ModelConfig, sharedConfig: any) {
    this.config = modelConfig;
    this.sharedConfig = sharedConfig;

    // 初始化OpenAI客户端
    this.openai = new OpenAI({
      apiKey: modelConfig.apiKey,
      baseURL: this.buildBaseUrl(modelConfig.url),
      dangerouslyAllowBrowser: true // 允许在浏览器中使用
    });
  }

  /**
   * 发送API请求
   */
  async call(
    messages: ConversationMessage[],
    options?: CallOptions
  ): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // 验证配置
      if (!this.config.apiKey || !this.config.url) {
        throw new Error('API配置不完整：缺少API密钥或端点URL');
      }

      // 构建消息数组
      const openaiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: this.config.systemPrompt
        },
        ...messages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        }))
      ];

      console.log('🚀 发送OpenAI API请求:', {
        baseURL: this.openai.baseURL,
        model: this.config.modelName,
        messagesCount: messages.length,
        hasApiKey: !!this.config.apiKey
      });

      // 检查是否需要流式响应
      const isStreaming = options?.streaming ?? this.sharedConfig.streaming ?? false;

      if (isStreaming) {
        return this.handleStreamingCall(openaiMessages, options, startTime, options?.onChunk);
      } else {
        return this.handleNormalCall(openaiMessages, options, startTime);
      }

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      console.error('❌ OpenAI API调用失败:', error);

      return {
        success: false,
        error: error.message || '未知错误',
        responseTime
      };
    }
  }

  /**
   * 处理普通调用
   */
  private async handleNormalCall(
    messages: OpenAI.Chat.ChatCompletionMessageParam[],
    options?: CallOptions,
    startTime?: number
  ): Promise<AIResponse> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: this.config.modelName,
        messages,
        temperature: options?.temperature ?? this.sharedConfig.temperature,
        max_tokens: options?.maxTokens ?? this.sharedConfig.maxTokens,
        top_p: options?.topP ?? this.sharedConfig.topP,
        frequency_penalty: options?.frequencyPenalty ?? this.sharedConfig.frequencyPenalty,
        presence_penalty: options?.presencePenalty ?? this.sharedConfig.presencePenalty,
      });

      const responseTime = startTime ? Date.now() - startTime : 0;

      return {
        success: true,
        text: completion.choices[0]?.message?.content || '',
        tokensUsed: completion.usage?.total_tokens || 0,
        responseTime,
        rawResponse: completion
      };
    } catch (error: any) {
      const responseTime = startTime ? Date.now() - startTime : 0;
      throw new Error(`OpenAI API调用失败: ${error.message}`);
    }
  }

  /**
   * 处理流式调用
   */
  private async handleStreamingCall(
    messages: OpenAI.Chat.ChatCompletionMessageParam[],
    options?: CallOptions,
    startTime?: number,
    onChunk?: (chunk: string) => void
  ): Promise<AIResponse> {
    try {
      const stream = await this.openai.chat.completions.create({
        model: this.config.modelName,
        messages,
        temperature: options?.temperature ?? this.sharedConfig.temperature,
        max_tokens: options?.maxTokens ?? this.sharedConfig.maxTokens,
        top_p: options?.topP ?? this.sharedConfig.topP,
        frequency_penalty: options?.frequencyPenalty ?? this.sharedConfig.frequencyPenalty,
        presence_penalty: options?.presencePenalty ?? this.sharedConfig.presencePenalty,
        stream: true,
      });

      let fullText = '';
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullText += content;
          onChunk?.(content); // 实时流式回调
        }
      }

      const responseTime = startTime ? Date.now() - startTime : 0;

      return {
        success: true,
        text: fullText,
        tokensUsed: 0, // 流式响应通常没有token统计
        responseTime,
        rawResponse: { text: fullText }
      };
    } catch (error: any) {
      const responseTime = startTime ? Date.now() - startTime : 0;
      throw new Error(`OpenAI 流式API调用失败: ${error.message}`);
    }
  }

  /**
   * 构建基础URL
   */
  private buildBaseUrl(url: string): string {
    if (!url) {
      throw new Error('API URL不能为空');
    }

    // 移除末尾的斜杠
    const cleanUrl = url.replace(/\/+$/, '');

    // 如果URL已经包含/v1，直接返回
    if (cleanUrl.endsWith('/v1')) {
      return cleanUrl;
    }

    // 如果URL不包含/v1，添加/v1
    return `${cleanUrl}/v1`;
  }
}

/**
 * 模型路由器
 */
class ModelRouter {
  /**
   * 根据功能类型选择模型
   */
  selectModel(functionType: AIFunctionType): AIModelType {
    return FUNCTION_MODEL_MAPPING[functionType] || 'outline';
  }

  /**
   * 根据内容特征智能选择模型
   */
  selectModelByContent(messages: ConversationMessage[]): AIModelType {
    const content = messages.map(m => m.content).join(' ').toLowerCase();
    
    // 对话相关关键词
    const dialogueKeywords = ['对话', '说话', '回答', '交谈', '聊天', '问答', '台词'];
    const hasDialogue = dialogueKeywords.some(keyword => content.includes(keyword));
    
    // 结构相关关键词
    const structureKeywords = ['大纲', '框架', '结构', '章节', '情节', '节奏'];
    const hasStructure = structureKeywords.some(keyword => content.includes(keyword));
    
    if (hasDialogue && !hasStructure) {
      return 'dialogue';
    }
    
    return 'outline'; // 默认使用大纲AI
  }
}

/**
 * 双AI协同服务
 */
export class DualAIService {
  private config: DualAIConfig;
  private outlineClient: APIClient;
  private dialogueClient: APIClient;
  private router: ModelRouter;

  constructor(config: DualAIConfig) {
    this.config = config;
    this.outlineClient = new APIClient(config.models.outline, config.shared);
    this.dialogueClient = new APIClient(config.models.dialogue, config.shared);
    this.router = new ModelRouter();
  }

  /**
   * 调用AI服务
   */
  async callAI(
    messages: ConversationMessage[],
    functionType: AIFunctionType,
    options?: CallOptions
  ): Promise<AIResponse> {
    try {
      // 选择合适的模型
      const modelType = this.router.selectModel(functionType);
      const client = modelType === 'outline' ? this.outlineClient : this.dialogueClient;
      
      console.log(`🤖 使用${modelType === 'outline' ? '大纲AI' : '对话AI'}处理${functionType}任务`);
      
      // 发送请求
      const response = await client.call(messages, options);
      
      // 添加模型类型信息
      response.modelType = modelType;
      
      // 记录使用统计
      this.trackUsage(modelType, response);
      
      return response;
      
    } catch (error: any) {
      console.error('❌ DualAIService调用失败:', error);
      return this.handleError(error, messages, functionType, options);
    }
  }

  /**
   * 智能调用（自动选择模型）
   */
  async smartCall(
    messages: ConversationMessage[],
    options?: CallOptions
  ): Promise<AIResponse> {
    const modelType = this.router.selectModelByContent(messages);
    const client = modelType === 'outline' ? this.outlineClient : this.dialogueClient;
    
    console.log(`🧠 智能选择${modelType === 'outline' ? '大纲AI' : '对话AI'}`);
    
    const response = await client.call(messages, options);
    response.modelType = modelType;
    
    this.trackUsage(modelType, response);
    
    return response;
  }

  /**
   * 测试连接
   */
  async testConnection(modelType?: AIModelType): Promise<{ outline: boolean; dialogue: boolean }> {
    const testMessage: ConversationMessage[] = [{
      role: 'user',
      content: '测试连接'
    }];

    const results = { outline: false, dialogue: false };

    if (!modelType || modelType === 'outline') {
      try {
        const response = await this.outlineClient.call(testMessage, { maxTokens: 10 });
        results.outline = response.success;
      } catch (error) {
        console.warn('大纲AI连接测试失败:', error);
      }
    }

    if (!modelType || modelType === 'dialogue') {
      try {
        const response = await this.dialogueClient.call(testMessage, { maxTokens: 10 });
        results.dialogue = response.success;
      } catch (error) {
        console.warn('对话AI连接测试失败:', error);
      }
    }

    return results;
  }

  /**
   * 记录使用统计
   */
  private trackUsage(modelType: AIModelType, response: AIResponse): void {
    // 这里可以实现使用统计逻辑
    console.log(`📊 ${modelType}AI使用统计:`, {
      tokensUsed: response.tokensUsed,
      responseTime: response.responseTime,
      success: response.success
    });
  }

  /**
   * 错误处理和降级
   */
  private async handleError(
    error: Error,
    messages: ConversationMessage[],
    functionType: AIFunctionType,
    options?: CallOptions
  ): Promise<AIResponse> {
    console.warn('🔄 尝试降级处理...');
    
    // 如果是对话AI失败，尝试使用大纲AI
    if (this.router.selectModel(functionType) === 'dialogue') {
      try {
        console.log('🔄 对话AI失败，降级到大纲AI');
        const response = await this.outlineClient.call(messages, options);
        response.modelType = 'outline';
        return response;
      } catch (fallbackError) {
        console.error('❌ 降级也失败了:', fallbackError);
      }
    }
    
    return {
      success: false,
      error: error.message || '所有AI模型都不可用',
      modelType: this.router.selectModel(functionType)
    };
  }

  /**
   * 获取配置信息
   */
  getConfig(): DualAIConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: DualAIConfig): void {
    this.config = newConfig;
    this.outlineClient = new APIClient(newConfig.models.outline, newConfig.shared);
    this.dialogueClient = new APIClient(newConfig.models.dialogue, newConfig.shared);
  }
}

/**
 * 创建双AI服务实例
 */
export function createDualAIService(config: DualAIConfig): DualAIService {
  return new DualAIService(config);
}
