/**
 * SVG图标样式
 * 文学创作主题图标的动画和交互效果
 */

/* 基础图标样式 */
.icon {
  transition: all 200ms ease-out;
  color: var(--color-primary, #8B4513);
  cursor: default;
}

.icon-clickable {
  cursor: pointer;
}

.icon-clickable:hover {
  opacity: 0.8;
}

.icon-clickable:focus {
  outline: 2px solid var(--color-primary, #8B4513);
  outline-offset: 2px;
  border-radius: 2px;
}

/* 动画图标基础样式 */
.icon-animated {
  transition: transform 200ms ease-out;
}

/* 羽毛笔图标动画 */
.feather-pen-icon.icon-animated:hover {
  animation: featherPenSway 600ms ease-in-out;
}

@keyframes featherPenSway {
  0%, 100% { 
    transform: rotate(0deg) scale(1.0); 
  }
  25% { 
    transform: rotate(-3deg) scale(1.05); 
  }
  75% { 
    transform: rotate(3deg) scale(1.05); 
  }
}

/* 人物图标动画 - 点头效果 */
.character-icon.icon-animated:hover {
  animation: characterNod 400ms ease-in-out;
}

@keyframes characterNod {
  0%, 100% { 
    transform: rotate(0deg); 
  }
  50% { 
    transform: rotate(2deg); 
  }
}

/* 书本图标动画 - 页面翻动 */
.book-icon.icon-animated:hover {
  animation: bookPageFlip 500ms ease-in-out;
}

@keyframes bookPageFlip {
  0% { 
    transform: scale(1.0); 
  }
  25% { 
    transform: scale(1.05) rotateY(5deg); 
  }
  75% { 
    transform: scale(1.05) rotateY(-5deg); 
  }
  100% { 
    transform: scale(1.0) rotateY(0deg); 
  }
}

/* 地图图标动画 - 展开效果 */
.map-icon.icon-animated:hover {
  animation: mapUnroll 400ms ease-out;
}

@keyframes mapUnroll {
  0% { 
    transform: scaleX(1.0); 
  }
  50% { 
    transform: scaleX(1.1); 
  }
  100% { 
    transform: scaleX(1.0); 
  }
}

/* 书签图标动画 - 飘动效果 */
.bookmark-icon.icon-animated:hover {
  animation: bookmarkWave 800ms ease-in-out;
}

@keyframes bookmarkWave {
  0%, 100% { 
    transform: rotate(0deg) scale(1.0); 
  }
  25% { 
    transform: rotate(-2deg) scale(1.02); 
  }
  75% { 
    transform: rotate(2deg) scale(1.02); 
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-animated:hover {
    animation: none;
    transform: scale(1.1);
  }
}

/* 无障碍支持 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .icon,
  .icon-animated,
  .icon-animated:hover {
    animation: none !important;
    transition: opacity 200ms ease-out;
  }
  
  .icon-animated:hover {
    opacity: 0.8;
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .icon {
    stroke-width: 2.5;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .icon {
    color: var(--color-primary-light, #D2B48C);
  }
}
