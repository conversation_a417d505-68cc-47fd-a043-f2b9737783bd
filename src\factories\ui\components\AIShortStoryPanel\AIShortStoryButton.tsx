"use client";

import React, { useState } from 'react';
import { CircleButton } from '@/adapters/ui';
import { ShortStoryWorkspaceDialog } from '@/components/short-story/ShortStoryWorkspaceDialog';

interface AIShortStoryButtonProps {
  bookId: string;
  onShortStoryGenerated?: (content: string) => void;
  position?: 'bottom-right' | 'custom' | 'wheel';
  angle?: number; // 角度，用于在圆形菜单中定位
  distance?: number; // 距离中心点的距离
  wheelIndex?: number; // 在轮盘中的索引位置
  wheelTotal?: number; // 轮盘中的总按钮数
  wheelStartAngle?: number; // 轮盘起始角度
  wheelEndAngle?: number; // 轮盘结束角度
  className?: string;
  disabled?: boolean;
}

/**
 * AI短篇创作按钮组件
 * 支持轮盘式布局，可以在右下角以轮盘形式排列
 */
export const AIShortStoryButton: React.FC<AIShortStoryButtonProps> = ({
  bookId,
  onShortStoryGenerated,
  position = 'bottom-right',
  angle = 45, // 默认在右下方，45度位置
  distance = 60,
  wheelIndex = 0,
  wheelTotal = 1,
  wheelStartAngle = 90, // 默认从90度开始（左上方）
  wheelEndAngle = 180, // 默认到180度结束（右上方）
  className = '',
  disabled = false
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (disabled) return;
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleShortStoryGenerated = (content: string) => {
    onShortStoryGenerated?.(content);
    setIsDialogOpen(false);
  };

  // 计算按钮位置样式
  const getPositionStyle = () => {
    if (position === 'custom' && angle !== undefined) {
      // 将角度转换为弧度
      const radians = (angle * Math.PI) / 180;
      // 计算x和y坐标
      const x = Math.cos(radians) * distance;
      const y = Math.sin(radians) * distance;

      return {
        transform: `translate(${x}px, ${y}px)`,
      };
    }

    if (position === 'wheel' && wheelTotal > 0) {
      // 计算在轮盘中的角度
      const angleRange = wheelEndAngle - wheelStartAngle;
      const buttonAngle = wheelStartAngle + (angleRange * wheelIndex / (wheelTotal - 1));

      // 将角度转换为弧度
      const radians = (buttonAngle * Math.PI) / 180;

      // 计算x和y坐标
      const x = Math.cos(radians) * distance;
      const y = Math.sin(radians) * distance;

      return {
        transform: `translate(${x}px, ${y}px)`,
      };
    }

    // 默认底部右侧位置
    return {};
  };

  return (
    <>
      <div
        className={`${position === 'bottom-right' ? 'fixed bottom-8 right-8 z-40' : 'relative'} ${className}`}
        style={position === 'custom' || position === 'wheel' ? getPositionStyle() : {}}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CircleButton
          icon={
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253M9 7h6m-6 4h6m-6 4h6" 
              />
            </svg>
          }
          text="AI短篇创作"
          color="var(--color-warning)"
          size="medium"
          disabled={disabled}
          onClick={handleClick}
        />

        {/* 悬停提示 */}
        {isHovered && (
          <div
            className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 z-50"
            style={{
              pointerEvents: 'none'
            }}
          >
            <span
              className="text-xs font-medium text-center transition-all duration-300 bg-gray-800 text-white px-2 py-1 rounded-md shadow-md whitespace-nowrap"
            >
              AI短篇创作
              {/* 添加小三角形指向按钮 */}
              <div
                className="absolute w-0 h-0 border-l-[5px] border-l-transparent border-r-[5px] border-r-transparent border-t-[5px] border-t-gray-800"
                style={{
                  bottom: '-5px',
                  left: '50%',
                  transform: 'translateX(-50%)'
                }}
              ></div>
            </span>
          </div>
        )}
      </div>

      {/* AI短篇创作工作区对话框 */}
      <ShortStoryWorkspaceDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        onShortStoryGenerated={handleShortStoryGenerated}
        bookId={bookId}
      />
    </>
  );
};

/**
 * 简化版AI短篇创作按钮
 * 用于在其他组件中快速集成
 */
export const SimpleAIShortStoryButton: React.FC<{
  bookId: string;
  onShortStoryGenerated?: (content: string) => void;
  buttonText?: string;
  buttonClassName?: string;
  disabled?: boolean;
}> = ({
  bookId,
  onShortStoryGenerated,
  buttonText = 'AI短篇创作',
  buttonClassName = '',
  disabled = false
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleClick = () => {
    if (disabled) return;
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleShortStoryGenerated = (content: string) => {
    onShortStoryGenerated?.(content);
    setIsDialogOpen(false);
  };

  return (
    <>
      <button
        className={`inline-flex items-center px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors shadow-sm text-sm font-medium ${buttonClassName}`}
        onClick={handleClick}
        disabled={disabled}
        title="使用AI创作短篇小说"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-4 w-4 mr-2" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253M9 7h6m-6 4h6m-6 4h6" 
          />
        </svg>
        {buttonText}
      </button>

      {/* AI短篇创作工作区对话框 */}
      <ShortStoryWorkspaceDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        onShortStoryGenerated={handleShortStoryGenerated}
        bookId={bookId}
      />
    </>
  );
};

export default AIShortStoryButton;
