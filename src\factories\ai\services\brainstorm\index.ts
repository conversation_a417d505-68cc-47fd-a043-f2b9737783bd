/**
 * 书名生成服务模块统一导出
 * 提供向后兼容的API接口
 */

// 主服务类和工厂函数
export { BookTitleGenerationService, createBookTitleGenerationService } from './BookTitleGenerationService';

// 类型定义
export type {
  KeywordElement,
  TitleFramework,
  BookTitle,
  TitleGenerationParams,
  TitleGenerationCallbacks,
  TitleGenerationResult,
  AITitleResponse,
  BookTitleGenerationServiceInterface
} from './types/BrainstormTypes';

// 常量
export { PRESET_KEYWORDS, PRESET_FRAMEWORKS } from './types/BrainstormTypes';

// 构建器
export { TitlePromptBuilder } from './builders/TitlePromptBuilder';

// 处理器
export { TitleResponseParser } from './processors/TitleResponseParser';

// 向后兼容的默认导出
import { createBookTitleGenerationService } from './BookTitleGenerationService';
export default createBookTitleGenerationService;
