import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface PortalProps {
  children: React.ReactNode;
  containerId?: string;
}

/**
 * Portal组件，用于将子组件渲染到DOM树的顶层
 * 这样可以避免父容器的CSS属性（如overflow）对子组件的影响
 */
export const Portal: React.FC<PortalProps> = ({
  children,
  containerId = 'portal-root'
}) => {
  const [container, setContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // 查找或创建容器元素
    let portalContainer = document.getElementById(containerId);

    if (!portalContainer) {
      portalContainer = document.createElement('div');
      portalContainer.id = containerId;
      portalContainer.style.position = 'fixed';
      portalContainer.style.zIndex = '9999';
      portalContainer.style.top = '0';
      portalContainer.style.left = '0';
      // 移除宽度和高度限制，允许内容正常显示
      // portalContainer.style.width = '0';
      // portalContainer.style.height = '0';
      document.body.appendChild(portalContainer);
    }

    setContainer(portalContainer);

    // 清理函数
    return () => {
      // 如果容器是由这个组件创建的，则在卸载时移除它
      if (portalContainer && portalContainer.childNodes.length === 0) {
        document.body.removeChild(portalContainer);
      }
    };
  }, [containerId]);

  // 只有在container存在时才渲染children
  return container ? createPortal(children, container) : null;
};

export default Portal;
