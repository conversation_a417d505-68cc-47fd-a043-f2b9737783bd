/**
 * 节点关联处理模块
 * 负责处理@节点功能和节点关联逻辑
 */

import { ConversationMessage, NodeSearchResult, OutlineNode } from '../types/SharedTypes';

export class NodeAssociationHandler {
  private static instance: NodeAssociationHandler;

  private constructor() {}

  public static getInstance(): NodeAssociationHandler {
    if (!NodeAssociationHandler.instance) {
      NodeAssociationHandler.instance = new NodeAssociationHandler();
    }
    return NodeAssociationHandler.instance;
  }

  /**
   * 构建@节点相关消息（增强版）
   */
  async buildMentionedNodesMessages(
    mentionedNodes: string[],
    outline: any,
    bookId?: string
  ): Promise<ConversationMessage[]> {
    const messages: ConversationMessage[] = [];

    if (!mentionedNodes || mentionedNodes.length === 0) {
      return messages;
    }

    // 分离章节ID和节点ID
    const chapterIds: string[] = [];
    const outlineNodeIds: string[] = [];

    for (const nodeId of mentionedNodes) {
      // 检查是否是章节ID（通过查找数据库）
      try {
        const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
        const { AIAssistantContextType } = await import('@/lib/db/dexie');

        const chapterResults = await aiAssistantDataService.searchMentionItems(
          bookId || '', '', [AIAssistantContextType.CHAPTER], 1000
        );

        const isChapter = chapterResults.some(item => item.id === nodeId);
        if (isChapter) {
          chapterIds.push(nodeId);
        } else {
          outlineNodeIds.push(nodeId);
        }
      } catch (error) {
        // 如果查找失败，默认当作大纲节点处理
        outlineNodeIds.push(nodeId);
      }
    }

    // 处理章节内容
    if (chapterIds.length > 0) {
      await this.buildChapterContentMessages(messages, chapterIds);
    }


      // 处理大纲节点 - 递归展开所有子节点信息
      if (outlineNodeIds.length > 0) {
        // [修改] 引导语从“重点考虑”变成“聚光灯”，更形象
        messages.push({
          role: 'user',
          content: `【聚光灯下的焦点】\n用户的目光特别落在了这 ${outlineNodeIds.length} 个地方，让我们凑近点看看：`
        });
  
        for (const nodeId of outlineNodeIds) {
          const node = this.findNodeById(nodeId, outline?.nodes || []);
          if (node) {
            // 构建当前节点的详细信息（复用之前修改过的人性化函数）
            this.buildDetailedNodeMessage(messages, node, outline);
  
            // 🔥 新增：递归展开所有子节点的详细信息（这个逻辑保留，但呈现方式可以优化）
            if (node.children && node.children.length > 0) {
              // [修改] 子节点的信息不再逐一展开，而是给一个整体速写，避免信息过载
              let childrenSummary = `\n--- “${node.title || '这个片段'}” 的后续发展 ---\n`;
              childrenSummary += `它下面连接着 ${node.children.length} 个故事分支，大致是关于：\n`;
              node.children.slice(0, 5).forEach((child: any, index: number) => {
                childrenSummary += `${index + 1}. ${child.title || '某个小故事'}${child.description ? ` (${child.description.substring(0, 30)}...)` : ''}\n`;
              });
              if (node.children.length > 5) {
                childrenSummary += `...等等，细节我们用到时再看。`;
              }
              messages.push({ role: 'user', content: childrenSummary });
            }
          }
        }
      }
  
      // 添加最终说明消息
      if (messages.length > 0) {
        // [修改] “重要说明”变成更合作的“创作提示”
        messages.push({
          role: 'user',
          content: '【创作提示】上面这些是咱们这次创作的“重点参考资料”，在动笔前多看两眼，确保咱们的思路能和这些内容对得上。'
        });
      }
  
      return messages;
    }
  
    /**
     * 构建章节内容消息 - 使用更自然的情感共鸣机制
     */
    private async buildChapterContentMessages(messages: ConversationMessage[], chapterIds: string[]): Promise<void> {
      try {
        const { db } = await import('@/lib/db/dexie');
        const chapters = await db.chapters.where('id').anyOf(chapterIds).toArray();
  
        if (chapters.length === 0) {
          messages.push({
            role: 'user',
            content: `【章节内容】\n我没找到相关的章节内容，看来我们可以自由发挥了。`
          });
          return;
        }
  
        chapters.sort((a, b) => (a.order || 0) - (b.order || 0));
        
        // [修改] 摒弃复杂的强制分段器，信任AI的理解能力
        // [修改] 引导语从“重要提示”变成“一起品读”，从命令到邀请
        messages.push({
          role: 'user',
          content: `【一起品读这几章】
  
  嘿，朋友。在动笔之前，我们先花点时间，一起沉浸到这 ${chapters.length} 个章节里，找找感觉。这很重要，能帮我们接下来的创作不跑偏。
  
  这几章分别是：
  ${chapters.map(ch => `第${ch.order}章：《${ch.title}》（${ch.wordCount}字）`).join('\n')}
  
  **品读时，试着去感受：**
  1. **人物的呼吸**：他们说话的口气、习惯，和字里行间的真实情绪。
  2. **故事的脉搏**：情节是怎么一步步推进的，节奏是快是慢？
  3. **世界的质感**：场景的氛围，环境的细节，它们共同营造出的感觉。
  
  我会把每一章的核心内容发给你，咱们一起过一遍。`
        });
  
        for (const chapter of chapters) {
          if (!chapter.content || !chapter.content.trim()) {
            messages.push({
              role: 'user',
              content: `【第${chapter.order}章】《${chapter.title}》\n(这一章目前还是空的，我们可以来填补它)`
            });
            continue;
          }
  
          // [修改] 不再分段发送，而是发送完整内容并附上摘要，相信AI能处理
          messages.push({
            role: 'user',
            content: `【品读章节：第${chapter.order}章 《${chapter.title}》】
  字数：${chapter.wordCount}字
  ${chapter.summary ? `本章速览：${chapter.summary}` : ''}
  
  --- 正文开始 ---
  ${chapter.content}
  --- 正文结束 ---`
          });
  
          // [修改] AI的确认不再是逐句报告，而是有感情的读后感
          messages.push({
            role: 'assistant',
            content: `好的，刚刚沉浸式读完了《${chapter.title}》。
  我对[可以根据章节内容即兴发挥，如：主角的无奈/反派的嚣张/紧张的氛围]印象很深。人物的性格和故事的走向，我心里有数了。`
          });
        }
        
        // [修改] 所有章节处理完毕，用更自信、更人性化的口吻总结
        messages.push({
          role: 'assistant',
          content: `好了，这 ${chapters.length} 个章节我都“吃”进去了，感觉已经和故事里的角色成了朋友。
  
  接下来我动笔时，脑子里会时刻装着这些画面：
  - **人物不会OOC**：我会让他们说符合自己身份和经历的话。
  - **情节不会断档**：新写的故事会像齿轮一样，和前面的剧情严丝合缝地咬合在一起。
  - **氛围不会跑偏**：故事的调性会保持一致，不会突然变味儿。
  
  放心吧，我们是在同一个世界里讲故事。`
        });
  
      } catch (error) {
        messages.push({
          role: 'user',
          content: `【章节内容加载失败】\n糟糕，想去翻翻前面的章节，但书页好像粘住了。没关系，我们凭着记忆和已有的信息来创作吧。`
        });
      }
    }
  /**
   * 获取节点详细信息
   */
  private async getNodeDetailInfo(nodeId: string, outline: any, bookId?: string): Promise<string | null> {
    // 首先在当前大纲中查找
    const node = this.findNodeById(nodeId, outline?.nodes || []);

    if (node) {
      return this.formatNodeInfo(node);
    }

    // 如果在当前大纲中找不到，尝试从其他来源获取
    if (bookId) {
      try {
        // 这里可以添加从数据库或其他存储中获取节点信息的逻辑
        // const nodeFromDB = await this.getNodeFromDatabase(nodeId, bookId);
        // if (nodeFromDB) {
        //   return this.formatNodeInfo(nodeFromDB);
        // }
      } catch (error) {
        console.error('从数据库获取节点信息失败:', error);
      }
    }

    return null;
  }

  /**
   * 构建单个节点的详细信息消息

  /**
   * 构建详细节点信息的消息
   */
  private buildDetailedNodeMessage(messages: ConversationMessage[], node: any, outline: any): void {
    // 获取节点层级和位置信息
    const enhancedNode = {
      ...node,
      level: this.getNodeLevel(outline.nodes, node.id),
      childrenCount: node.children ? node.children.length : 0,
      position: this.getNodePosition(outline.nodes, node.id)
    };

    // [修改] 标题从“节点信息”变成“场记板”，更有代入感
    let nodeContent = `【场记板/节点信息：${enhancedNode.title || enhancedNode.name || '一个未命名的场景'}】\n`;
    // [修改] 把技术术语人性化
    nodeContent += `场记板编号: ${enhancedNode.id}\n`;
    nodeContent += `场景类型: ${enhancedNode.type}\n`;
    nodeContent += `在故事地图上的位置: ${enhancedNode.title || enhancedNode.name || '未命名节点'}\n`;

    // [修改] 将零散的信息归类，更有条理和呼吸感
    nodeContent += `\n--- 场景核心要素 ---\n`;

    if (enhancedNode.creativeNotes) {
      nodeContent += `导演的场边笔记: ${enhancedNode.creativeNotes}\n`;
    }

    if (enhancedNode.relatedCharacters && enhancedNode.relatedCharacters.length > 0) {
      nodeContent += `出场演员：${enhancedNode.relatedCharacters.join('、')}\n`;
    }
    
    if (enhancedNode.emotionalTone) {
      nodeContent += `本场气氛：${enhancedNode.emotionalTone}\n`;
    }

    if (enhancedNode.conflictLevel) {
      nodeContent += `火药味指数：${enhancedNode.conflictLevel}/5\n`;
    }
    
    if (enhancedNode.chapterGoals) {
      nodeContent += `本场目标：${enhancedNode.chapterGoals}\n`;
    }

    if (enhancedNode.plotType) {
      nodeContent += `剧情风格：${enhancedNode.plotType}\n`;
    }
    
    // 对话相关信息
    if (enhancedNode.dialogueScene || enhancedNode.dialoguePurpose) {
      nodeContent += `\n--- 对白部分 ---\n`;
      if (enhancedNode.dialogueScene) {
        nodeContent += `布景和氛围：${enhancedNode.dialogueScene}\n`;
      }
      if (enhancedNode.dialoguePurpose) {
        nodeContent += `这场对话想干嘛：${enhancedNode.dialoguePurpose}\n`;
      }
    }

    // 悬念和关系动态
    if (enhancedNode.suspenseElements || enhancedNode.characterDynamics) {
      nodeContent += `\n--- 潜藏的暗流 ---\n`;
      if (enhancedNode.suspenseElements && enhancedNode.suspenseElements.length > 0) {
        nodeContent += `埋下的钩子：${enhancedNode.suspenseElements.join('、')}\n`;
      }
      if (enhancedNode.characterDynamics) {
        nodeContent += `人物关系变化：${enhancedNode.characterDynamics}\n`;
      }
    }
    
    // 🔥 Synopsis节点专有字段
    if (enhancedNode.type === 'synopsis') {
      nodeContent += `\n--- 故事的DNA --- (这是整个故事的内核)\n`;
      if (enhancedNode.synopsisBrainhole) nodeContent += `最初的灵感火花：${enhancedNode.synopsisBrainhole}\n`;
      if (enhancedNode.synopsisGenre) nodeContent += `故事是什么味儿的：${enhancedNode.synopsisGenre}\n`;
      if (enhancedNode.synopsisOpening) nodeContent += `第一钩怎么下/开头如何写的：${enhancedNode.synopsisOpening}\n`;
      if (enhancedNode.synopsisCoreOutline) nodeContent += `一句话梗概：${enhancedNode.synopsisCoreOutline}\n`;
      if (enhancedNode.synopsisEnding) nodeContent += `结尾最后一幕怎么收：${enhancedNode.synopsisEnding}\n`;
      if (enhancedNode.synopsisStoryDescription) nodeContent += `故事简介：${enhancedNode.synopsisStoryDescription}\n`;
      if (enhancedNode.synopsisAceReferences) nodeContent += `可以参考的大神作品：${enhancedNode.synopsisAceReferences}\n`;
    }

    // [修改] 剧情点部分，用“节拍”和“表演指导”的口吻
    if (enhancedNode.plotPoints && enhancedNode.plotPoints.length > 0) {
      nodeContent += `\n--- 关键节拍与表演指导 ---\n`;
      enhancedNode.plotPoints.slice(0, 3).forEach((p: any, index: number) => {
        const pointContent = typeof p === 'string' ? p : p.content || p.description || '一个未描述的动作';
        nodeContent += `\n节拍 ${index + 1}: ${pointContent}`;

        if (typeof p === 'object' && p !== null) {
          if (p.writingGuidance) nodeContent += `\n   表演要点：${p.writingGuidance}`;
          if (p.specificDescription) nodeContent += `\n   镜头可以给到：${p.specificDescription}`;
          if (p.avoidanceGuidance) nodeContent += `\n   表演雷区：${p.avoidanceGuidance}`;
          if (p.type) nodeContent += `\n   节拍类型：${p.type}`;
        }
      });
      if (enhancedNode.plotPoints.length > 3) {
        nodeContent += `\n\n...还有${enhancedNode.plotPoints.length - 3}个节拍，感觉对了就行。`;
      }
    }

    // [修改] 把“子节点”这个技术术语，换成更贴切的“分支”
    nodeContent += `\n\n--- 结构信息 ---\n`;
    if (enhancedNode.children && enhancedNode.children.length > 0) {
      nodeContent += `它下面有 ${enhancedNode.children.length} 个故事分支。`;
      const childTitles = enhancedNode.children.map((child: any) => child.title || child.name || '未命名分支').join('、');
      nodeContent += `\n这些分支是：${childTitles}`;
    } else {
      nodeContent += `这是故事的一个末端，暂时没有更多分支。`;
    }

    messages.push({
      role: 'user',
      content: nodeContent
    });
  }
 /**
   * 递归构建子节点的详细信息消息
   */
  private buildChildrenNodesMessages(
    messages: ConversationMessage[],
    children: any[],
    outline: any,
    depth: number = 1,
    maxDepth: number = 3
  ): void {
    // 防止过深递归
    if (depth > maxDepth) {
      return;
    }

    for (const child of children) {
      // 构建子节点的详细信息
      this.buildDetailedNodeMessage(messages, child, outline);

      // 递归处理子节点的子节点
      if (child.children && child.children.length > 0) {
        this.buildChildrenNodesMessages(messages, child.children, outline, depth + 1, maxDepth);
      }
    }
  }

  /**
   * 获取节点层级
   */
  private getNodeLevel(nodes: any[], nodeId: string, currentLevel: number = 1): number {
    if (!nodes || !Array.isArray(nodes)) return 1;

    for (const node of nodes) {
      if (node.id === nodeId) {
        return currentLevel;
      }
      if (node.children && Array.isArray(node.children)) {
        const level = this.getNodeLevel(node.children, nodeId, currentLevel + 1);
        if (level > 0) return level;
      }
    }
    return 1;
  }

  /**
   * 获取节点位置
   */
  private getNodePosition(nodes: any[], nodeId: string): any {
    if (!nodes || !Array.isArray(nodes)) return { index: 0, total: 0 };

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === nodeId) {
        return { index: i, total: nodes.length };
      }
      if (node.children && Array.isArray(node.children)) {
        const position = this.getNodePosition(node.children, nodeId);
        if (position.index >= 0) return position;
      }
    }
    return { index: 0, total: 0 };
  }

  /**
   * 格式化节点信息
   */

  private formatNodeInfo(node: any): string {
    // [修改] 从“节点信息”变成更生动的“快照”
    let info = `【节点信息\快照：${node.title || node.name || '一个没名字的片段'}】\n`;
    // [修改] 把技术术语变成更生活化的比喻
    info += `它的身份证号ID：${node.id}\n`;
    info += `这是个啥：${node.type || '还不知道'}\n`;

    if (node.content) {
      info += `里面写了啥：${node.content}\n`;
    }

    if (node.creativeNotes) {
      // [修改] “创作建议”改成“我的碎碎念”，更有个人色彩
      info += `我的碎碎念：${node.creativeNotes}\n`;
    }

    // 添加层级信息
    if (node.level !== undefined) {
      // [修改] “节点层级”改成“埋得有多深”
      info += `埋得有多深：${node.level} 层\n`;
    }

    // 添加位置信息
    const path = this.getNodePath(node.id, node);
    if (path) {
      // [修改] “节点路径”改成“它的家庭住址”
      info += `它的家庭住址 \节点路径：${path}\n`;
    }

    // 添加子节点信息
    if (node.children && node.children.length > 0) {
      // [修改] “子节点”改成“娃”，俏皮且有人性
      info += `它有几个娃\子节点：${node.children.length}个\n`;
      info += `这几个娃是\子节点列表：${node.children.map((child: any) => `《${child.title || child.name || '没取名'}》`).join('、')}\n`;
    }

    return info;
  }


  /**
   * 在节点树中查找指定ID的节点
   */
  findNodeById(nodeId: string, nodes: any[]): any | null {
    if (!nodes || nodes.length === 0) {
      return null;
    }

    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      
      // 递归查找子节点
      if (node.children && node.children.length > 0) {
        const found = this.findNodeById(nodeId, node.children);
        if (found) {
          return found;
        }
      }
    }

    return null;
  }

  /**
   * 获取节点路径
   */
  getNodePath(nodeId: string, rootNode?: any): string {
    if (!rootNode) {
      return '';
    }

    const findPath = (nodes: any[], targetId: string, currentPath: string[] = []): string[] | null => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.title || node.name || '未命名'];
        
        if (node.id === targetId) {
          return newPath;
        }
        
        if (node.children && node.children.length > 0) {
          const found = findPath(node.children, targetId, newPath);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };

    const path = findPath([rootNode], nodeId);
    return path ? path.join(' > ') : '';
  }

  /**
   * 查找相关节点
   */
  findRelatedNodes(nodeId: string, outline: any, maxResults: number = 5): NodeSearchResult[] {
    const results: NodeSearchResult[] = [];
    const targetNode = this.findNodeById(nodeId, outline?.nodes || []);
    
    if (!targetNode) {
      return results;
    }

    // 查找同级节点
    const siblings = this.findSiblingNodes(nodeId, outline?.nodes || []);
    siblings.forEach(sibling => {
      results.push({
        node: sibling,
        path: this.getNodePath(sibling.id, outline),
        level: sibling.level || 0
      });
    });

    // 查找父节点
    const parent = this.findParentNode(nodeId, outline?.nodes || []);
    if (parent) {
      results.push({
        node: parent,
        path: this.getNodePath(parent.id, outline),
        level: parent.level || 0
      });
    }

    // 查找子节点
    if (targetNode.children && targetNode.children.length > 0) {
      targetNode.children.forEach((child: any) => {
        results.push({
          node: child,
          path: this.getNodePath(child.id, outline),
          level: child.level || 0
        });
      });
    }

    return results.slice(0, maxResults);
  }

  /**
   * 查找同级节点
   */
  private findSiblingNodes(nodeId: string, nodes: any[]): any[] {
    const findSiblings = (nodeList: any[], targetId: string): any[] => {
      for (const node of nodeList) {
        if (node.children && node.children.length > 0) {
          // 检查是否在当前节点的子节点中
          const targetIndex = node.children.findIndex((child: any) => child.id === targetId);
          if (targetIndex !== -1) {
            // 返回除目标节点外的所有同级节点
            return node.children.filter((child: any) => child.id !== targetId);
          }
          
          // 递归查找
          const siblings = findSiblings(node.children, targetId);
          if (siblings.length > 0) {
            return siblings;
          }
        }
      }
      return [];
    };

    return findSiblings(nodes, nodeId);
  }

  /**
   * 查找父节点
   */
  private findParentNode(nodeId: string, nodes: any[]): any | null {
    const findParent = (nodeList: any[]): any | null => {
      for (const node of nodeList) {
        if (node.children && node.children.length > 0) {
          // 检查是否是直接父节点
          const hasChild = node.children.some((child: any) => child.id === nodeId);
          if (hasChild) {
            return node;
          }
          
          // 递归查找
          const parent = findParent(node.children);
          if (parent) {
            return parent;
          }
        }
      }
      return null;
    };

    return findParent(nodes);
  }

  /**
   * 验证节点关联的有效性
   */
  validateNodeAssociations(mentionedNodes: string[], outline: any): {
    validNodes: string[];
    invalidNodes: string[];
    warnings: string[];
  } {
    const validNodes: string[] = [];
    const invalidNodes: string[] = [];
    const warnings: string[] = [];

    mentionedNodes.forEach(nodeId => {
      const node = this.findNodeById(nodeId, outline?.nodes || []);
      if (node) {
        validNodes.push(nodeId);
      } else {
        invalidNodes.push(nodeId);
        warnings.push(`节点 ${nodeId} 在当前大纲中不存在`);
      }
    });

    return {
      validNodes,
      invalidNodes,
      warnings
    };
  }
}
