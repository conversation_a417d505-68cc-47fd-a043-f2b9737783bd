"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface BranchButtonProps {
  messageId: string;
  onCreateBranch: (messageId: string, branchName?: string) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * 分支对话按钮组件
 * 用于在消息气泡中显示创建分支的按钮
 */
const BranchButton: React.FC<BranchButtonProps> = ({
  messageId,
  onCreateBranch,
  disabled = false,
  className = ''
}) => {
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [branchName, setBranchName] = useState('');

  // 处理创建分支
  const handleCreateBranch = () => {
    if (disabled) return;
    setShowNameDialog(true);
  };

  // 确认创建分支
  const handleConfirmCreate = () => {
    const finalName = branchName.trim() || undefined;
    onCreateBranch(messageId, finalName);
    setShowNameDialog(false);
    setBranchName('');
  };

  // 取消创建
  const handleCancel = () => {
    setShowNameDialog(false);
    setBranchName('');
  };

  return (
    <>
      {/* 分支按钮 */}
      <motion.button
        onClick={handleCreateBranch}
        disabled={disabled}
        className={`
          p-2 rounded-lg transition-all duration-200 
          ${disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:bg-white hover:bg-opacity-20 hover:scale-110'
          }
          ${className}
        `}
        whileHover={disabled ? {} : { scale: 1.1 }}
        whileTap={disabled ? {} : { scale: 0.95 }}
        title="创建分支对话"
      >
        <svg 
          className="w-4 h-4" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" 
          />
        </svg>
      </motion.button>

      {/* 分支命名对话框 */}
      {showNameDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            className="bg-white rounded-lg p-6 w-96 max-w-[90vw]"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
          >
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              创建分支对话
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分支名称（可选）
              </label>
              <input
                type="text"
                value={branchName}
                onChange={(e) => setBranchName(e.target.value)}
                placeholder="输入分支名称，留空将自动生成"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleConfirmCreate();
                  } else if (e.key === 'Escape') {
                    handleCancel();
                  }
                }}
              />
            </div>

            <div className="text-sm text-gray-600 mb-6">
              将从此消息开始创建新的对话分支，保留之前的对话历史作为上下文。
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                取消
              </button>
              <motion.button
                onClick={handleConfirmCreate}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                创建分支
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
};

export default BranchButton;
