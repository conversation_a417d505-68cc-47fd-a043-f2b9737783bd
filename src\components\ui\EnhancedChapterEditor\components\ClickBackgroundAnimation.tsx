"use client";

import React, { useEffect, useState, useMemo } from 'react';

interface ClickBackgroundAnimationProps {
  x: number;
  y: number;
  onComplete?: () => void;
}

/**
 * 点击背景动画组件
 * 在用户点击编辑器时创建全屏背景彩虹波纹动画
 */
export const ClickBackgroundAnimation: React.FC<ClickBackgroundAnimationProps> = ({
  x,
  y,
  onComplete
}) => {
  const [isVisible, setIsVisible] = useState(true);

  // 生成动态彩虹色彩
  const colorScheme = useMemo(() => {
    const timestamp = Date.now();
    const baseHue = (timestamp % 360);
    
    return {
      primary: `hsl(${baseHue}, 70%, 60%)`,
      secondary: `hsl(${(baseHue + 60) % 360}, 75%, 65%)`,
      tertiary: `hsl(${(baseHue + 120) % 360}, 80%, 70%)`,
      quaternary: `hsl(${(baseHue + 180) % 360}, 85%, 75%)`,
      quinary: `hsl(${(baseHue + 240) % 360}, 90%, 80%)`,
      senary: `hsl(${(baseHue + 300) % 360}, 95%, 85%)`
    };
  }, [x, y]);

  // 生成随机星光粒子
  const starParticles = useMemo(() => {
    const count = 15 + Math.floor(Math.random() * 10); // 15-25个粒子
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      startX: x + (Math.random() - 0.5) * 100,
      startY: y + (Math.random() - 0.5) * 100,
      endX: x + (Math.random() - 0.5) * 400,
      endY: y + (Math.random() - 0.5) * 400,
      size: 1 + Math.random() * 3,
      delay: Math.random() * 0.5,
      duration: 1.5 + Math.random() * 1
    }));
  }, [x, y]);

  useEffect(() => {
    // 动画完成后隐藏组件
    const timer = setTimeout(() => {
      setIsVisible(false);
      onComplete?.();
    }, 2500); // 2.5秒动画持续时间

    return () => clearTimeout(timer);
  }, [onComplete]);

  if (!isVisible) return null;

  return (
    <div
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 5 // 在背景之上，但在内容之下
      }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%'
        }}
      >
        <defs>
          {/* 主背景波纹渐变 */}
          <radialGradient id={`bgRipple1-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.primary} stopOpacity="0.15" />
            <stop offset="20%" stopColor={colorScheme.secondary} stopOpacity="0.12" />
            <stop offset="40%" stopColor={colorScheme.tertiary} stopOpacity="0.09" />
            <stop offset="60%" stopColor={colorScheme.quaternary} stopOpacity="0.06" />
            <stop offset="80%" stopColor={colorScheme.quinary} stopOpacity="0.03" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 次背景波纹渐变 */}
          <radialGradient id={`bgRipple2-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.secondary} stopOpacity="0.12" />
            <stop offset="30%" stopColor={colorScheme.tertiary} stopOpacity="0.09" />
            <stop offset="60%" stopColor={colorScheme.quaternary} stopOpacity="0.06" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 第三层波纹渐变 */}
          <radialGradient id={`bgRipple3-${x}-${y}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colorScheme.tertiary} stopOpacity="0.1" />
            <stop offset="40%" stopColor={colorScheme.quaternary} stopOpacity="0.07" />
            <stop offset="100%" stopColor="transparent" />
          </radialGradient>

          {/* 光晕滤镜 */}
          <filter id={`bgGlow-${x}-${y}`}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* 主背景波纹 */}
        <circle
          cx={x / window.innerWidth * 100}
          cy={y / window.innerHeight * 100}
          r="2"
          fill={`url(#bgRipple1-${x}-${y})`}
          filter={`url(#bgGlow-${x}-${y})`}
          style={{
            animation: 'bgRipple1 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards'
          }}
        />

        {/* 次背景波纹 */}
        <circle
          cx={x / window.innerWidth * 100}
          cy={y / window.innerHeight * 100}
          r="1.5"
          fill={`url(#bgRipple2-${x}-${y})`}
          style={{
            animation: 'bgRipple2 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards',
            animationDelay: '0.2s'
          }}
        />

        {/* 第三层波纹 */}
        <circle
          cx={x / window.innerWidth * 100}
          cy={y / window.innerHeight * 100}
          r="1"
          fill={`url(#bgRipple3-${x}-${y})`}
          style={{
            animation: 'bgRipple3 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards',
            animationDelay: '0.4s'
          }}
        />

        {/* 星光粒子系统 */}
        <g className="bg-star-particles">
          {starParticles.map((particle) => (
            <circle
              key={particle.id}
              cx={particle.startX / window.innerWidth * 100}
              cy={particle.startY / window.innerHeight * 100}
              r={particle.size / 20}
              fill={colorScheme.senary}
              opacity="0.8"
              style={{
                animation: `bgStarParticle ${particle.duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`,
                animationDelay: `${particle.delay}s`
              }}
            />
          ))}
        </g>

        <style>
          {`
            @keyframes bgRipple1 {
              0% {
                r: 2;
                opacity: 0.15;
              }
              50% {
                r: 40;
                opacity: 0.08;
              }
              100% {
                r: 80;
                opacity: 0;
              }
            }
            
            @keyframes bgRipple2 {
              0% {
                r: 1.5;
                opacity: 0.12;
              }
              50% {
                r: 30;
                opacity: 0.06;
              }
              100% {
                r: 60;
                opacity: 0;
              }
            }
            
            @keyframes bgRipple3 {
              0% {
                r: 1;
                opacity: 0.1;
              }
              50% {
                r: 20;
                opacity: 0.05;
              }
              100% {
                r: 40;
                opacity: 0;
              }
            }
            
            @keyframes bgStarParticle {
              0% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
              }
              20% {
                opacity: 1;
                transform: scale(1.5) rotate(90deg);
              }
              80% {
                opacity: 0.6;
                transform: scale(1) rotate(270deg);
              }
              100% {
                opacity: 0;
                transform: scale(0.3) rotate(360deg);
              }
            }
            
            @media (prefers-reduced-motion: reduce) {
              circle {
                animation: none !important;
                opacity: 0.02 !important;
                r: 10 !important;
              }
              .bg-star-particles {
                display: none;
              }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default ClickBackgroundAnimation;
