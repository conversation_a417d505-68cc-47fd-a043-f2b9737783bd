import React from 'react';
import { motion } from 'framer-motion';

interface IconProps {
  className?: string;
  size?: number;
  color?: string;
  isActive?: boolean;
  onClick?: () => void;
}

// 动态心形图标 - 收藏功能
export const HeartIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor', 
  isActive = false,
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.path
        d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
        fill={isActive ? '#ef4444' : 'none'}
        stroke={color}
        strokeWidth="2"
        initial={false}
        animate={{
          fill: isActive ? '#ef4444' : 'none',
          scale: isActive ? [1, 1.2, 1] : 1
        }}
        transition={{ duration: 0.3 }}
      />
      {isActive && (
        <motion.g
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: [0, 1, 0], scale: [0, 1.5, 0] }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <circle cx="12" cy="12" r="3" fill="#ef4444" opacity="0.3" />
        </motion.g>
      )}
    </motion.svg>
  );
};

// 动态书本图标 - 管理功能
export const BookIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor',
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.05, rotateY: 15 }}
      whileTap={{ scale: 0.95 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.path
        d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6z"
        fill={color}
        initial={{ opacity: 0.8 }}
        whileHover={{ opacity: 1 }}
      />
      <motion.path
        d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 5h-8V5h8v2z"
        fill={color}
        initial={{ x: 0 }}
        whileHover={{ x: [0, 2, 0] }}
        transition={{ duration: 0.5 }}
      />
      <motion.path
        d="M10 9h8v2h-8V9zm0 3h8v2h-8v-2z"
        fill="white"
        initial={{ opacity: 0.7 }}
        whileHover={{ opacity: 1 }}
      />
    </motion.svg>
  );
};

// 动态垃圾桶图标 - 删除功能
export const TrashIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor',
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.path
        d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12z"
        fill={color}
        initial={{ y: 0 }}
        whileHover={{ y: [0, -1, 0] }}
        transition={{ duration: 0.4 }}
      />
      <motion.path
        d="M8 9h8v10H8V9z"
        fill="white"
        opacity="0.3"
      />
      <motion.path
        d="M15.5 4l-1-1h-5l-1 1H5v2h14V4z"
        fill={color}
        initial={{ rotateX: 0 }}
        whileHover={{ rotateX: [-5, 5, 0] }}
        transition={{ duration: 0.3 }}
      />
      <motion.path
        d="M10 11v6M14 11v6"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        initial={{ opacity: 0.7 }}
        whileHover={{ opacity: 1 }}
      />
    </motion.svg>
  );
};

// 动态编辑图标 - 编辑功能
export const EditIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor',
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.05, rotate: 5 }}
      whileTap={{ scale: 0.95 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.path
        d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z"
        fill={color}
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5 }}
      />
      <motion.path
        d="M20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
        fill={color}
        initial={{ rotate: 0 }}
        whileHover={{ rotate: [0, 10, 0] }}
        transition={{ duration: 0.3 }}
      />
      <motion.circle
        cx="18"
        cy="6"
        r="1"
        fill="white"
        initial={{ scale: 0 }}
        animate={{ scale: [0, 1.2, 1] }}
        transition={{ delay: 0.2, duration: 0.3 }}
      />
    </motion.svg>
  );
};

// 动态复制图标 - 复制功能
export const CopyIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor',
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.path
        d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1z"
        fill={color}
        initial={{ x: 0 }}
        whileHover={{ x: [0, -2, 0] }}
        transition={{ duration: 0.3 }}
      />
      <motion.path
        d="M19 5H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2z"
        fill={color}
        initial={{ x: 0 }}
        whileHover={{ x: [0, 2, 0] }}
        transition={{ duration: 0.3, delay: 0.1 }}
      />
      <motion.path
        d="M10 9h7v2h-7V9zm0 3h7v2h-7v-2zm0 3h7v2h-7v-2z"
        fill="white"
        opacity="0.7"
        initial={{ opacity: 0.7 }}
        whileHover={{ opacity: 1 }}
      />
    </motion.svg>
  );
};

// 动态选择图标 - 选择功能
export const SelectIcon: React.FC<IconProps> = ({ 
  className = '', 
  size = 20, 
  color = 'currentColor',
  onClick 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <motion.circle
        cx="12"
        cy="12"
        r="10"
        fill="none"
        stroke={color}
        strokeWidth="2"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5 }}
      />
      <motion.path
        d="M9 12l2 2 4-4"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      />
      <motion.circle
        cx="12"
        cy="12"
        r="3"
        fill={color}
        opacity="0.1"
        initial={{ scale: 0 }}
        animate={{ scale: [0, 1.2, 1] }}
        transition={{ duration: 0.4, delay: 0.3 }}
      />
    </motion.svg>
  );
};
