/* 浮动操作按钮组样式 */
.floating-action-group {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景遮罩 */
.floating-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 998;
}

/* 主按钮样式 */
.main-action-button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 1000;

  /* 阴影效果 */
  box-shadow:
    0 4px 12px rgba(102, 126, 234, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 主按钮悬停效果 */
.main-action-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 6px 20px rgba(102, 126, 234, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 主按钮展开状态 */
.main-action-button.expanded {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: scale(1.05);
}

/* 主按钮有活跃功能时 */
.main-action-button.has-active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% {
    box-shadow:
      0 4px 12px rgba(16, 185, 129, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow:
      0 6px 20px rgba(16, 185, 129, 0.6),
      0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

/* 主按钮禁用状态 */
.main-action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 主按钮图标 */
.main-button-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 活跃状态指示器 */
.active-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.active-indicator .pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  animation: pulse 2s infinite;
}

/* 主按钮提示 */
.main-button-tooltip {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1001;
}

.main-action-button:hover .main-button-tooltip {
  opacity: 1;
}

/* 子按钮容器 */
.sub-buttons-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  pointer-events: none;
}

/* 子按钮样式 */
.sub-action-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  color: white;
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: auto;

  /* 阴影效果 */
  box-shadow:
    0 3px 10px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 子按钮悬停效果 */
.sub-action-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    0 2px 5px rgba(0, 0, 0, 0.15);
}

/* AI助手按钮 */
.sub-action-button.assistant-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.sub-action-button.assistant-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.sub-action-button.assistant-button.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: subButtonGlow 2s ease-in-out infinite alternate;
}

/* 框架提取按钮 */
.sub-action-button.framework-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.sub-action-button.framework-button:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.sub-action-button.framework-button.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: subButtonGlow 2s ease-in-out infinite alternate;
}

/* 章节分析按钮 */
.sub-action-button.analysis-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.sub-action-button.analysis-button:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

.sub-action-button.analysis-button.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: subButtonGlow 2s ease-in-out infinite alternate;
}

@keyframes subButtonGlow {
  0% {
    box-shadow:
      0 3px 10px rgba(16, 185, 129, 0.4),
      0 1px 3px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow:
      0 4px 15px rgba(16, 185, 129, 0.6),
      0 2px 5px rgba(0, 0, 0, 0.15);
  }
}

/* 子按钮提示 */
.sub-button-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1002;
}

.sub-action-button:hover .sub-button-tooltip {
  opacity: 1;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-action-button {
    width: 52px;
    height: 52px;
  }
  
  .sub-action-button {
    width: 44px;
    height: 44px;
  }
  
  .main-button-icon {
    width: 20px;
    height: 20px;
  }
  
  .main-button-tooltip,
  .sub-button-tooltip {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  /* 移动端调整子按钮间距 */
  .sub-action-button.assistant-button {
    transform: translate(-50%, -50%) translateX(-60px);
  }
  
  .sub-action-button.framework-button {
    transform: translate(-50%, -50%) translateX(-120px);
  }
  
  .sub-action-button.analysis-button {
    transform: translate(-50%, -50%) translateX(-180px);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .main-action-button,
  .sub-action-button {
    border: 2px solid currentColor;
  }
  
  .main-button-tooltip,
  .sub-button-tooltip {
    background: black;
    border: 1px solid white;
  }
}
