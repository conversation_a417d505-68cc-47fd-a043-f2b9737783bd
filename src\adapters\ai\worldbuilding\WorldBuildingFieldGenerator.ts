"use client";

import { IAISenderComponent } from '@/factories/ai/interfaces/IAISenderComponent';
import { WorldBuilding } from '@/lib/db/dexie';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import createAdvancedMessageBuilder from '@/utils/ai/AdvancedMessageBuilder';
import { WorldBuildingPrompts } from '@/utils/ai/prompts/WorldBuildingPrompts';
import { getAssociatedChapterContent } from '@/utils/chapterAssociation';
import { IWorldBuildingFieldGenerator } from '../interfaces/WorldBuildingInterfaces';
import { WorldBuildingInfoFormatter } from './WorldBuildingInfoFormatter';

/**
 * 世界观字段生成器
 * 负责生成世界观元素的特定字段内容
 */
export class WorldBuildingFieldGenerator implements IWorldBuildingFieldGenerator {
  private currentRequest: AbortController | null = null;
  private infoFormatter: WorldBuildingInfoFormatter;

  /**
   * 创建世界观字段生成器
   * @param aiSender AI发送器
   */
  constructor(
    private aiSender: IAISenderComponent
  ) {
    this.infoFormatter = new WorldBuildingInfoFormatter();
  }

  /**
   * 使用关联章节内容生成世界观元素的特定字段
   * @param worldBuilding 世界观对象
   * @param fieldName 字段名称
   * @param bookId 书籍ID
   * @param relatedWorldBuildings 关联的世界观元素
   * @returns 生成的字段内容
   */
  async generateFieldWithAssociatedChapters(
    worldBuilding: WorldBuilding,
    fieldName: string,
    bookId: string,
    relatedWorldBuildings: string[] = [],
    customPrompt?: string
  ): Promise<string> {
    try {
      if (!worldBuilding.id) {
        throw new Error('世界观ID不存在，无法获取关联章节');
      }

      // 获取关联章节内容
      let chapterContent = await getAssociatedChapterContent(worldBuilding.id, 'worldbuilding', bookId);

      if (!chapterContent) {
        console.log('没有找到关联章节内容，使用默认内容');
        // 即使没有关联章节，也继续处理，使用一个默认内容
        chapterContent = `# 默认章节\n\n这是一个默认章节内容，用于在没有关联章节时进行处理。`;
      }

      console.log(`已加载章节内容，总长度: ${chapterContent.length}`);

      // 格式化世界观信息
      const worldBuildingInfo = this.infoFormatter.formatWorldBuildingInfo(worldBuilding);

      // 获取字段显示名称
      let fieldDisplayName = fieldName;
      if (fieldName.startsWith('attributes.')) {
        fieldDisplayName = fieldName.split('.')[1];
      }

      // 构建基础提示词
      const basePrompt = `请为世界观元素"${worldBuilding.name}"生成"${fieldDisplayName}"字段的内容。请参考以下信息：

世界观元素信息：
${worldBuildingInfo}

重要提示：
1. 如果是更新现有内容，只返回新的信息，不要重复已有内容
2. 新内容应该是对现有内容的补充，而不是替代
3. 确保返回的内容与现有内容有明显区别，避免返回相似或重复的信息

请生成详细、生动且符合小说风格的"${fieldDisplayName}"内容。${fieldDisplayName === '关联元素' ? '如果有关联的世界观元素，请以数组形式列出它们的名称。' : ''}`;

      // 创建高级消息构建器
      const advancedBuilder = createAdvancedMessageBuilder()
        // 添加系统角色提示词 - 使用通用系统提示词，因为字段生成是一种混合操作
        .addSystemPrompt(WorldBuildingPrompts.systemRolePrompt)
        // 添加助手角色确认任务
        .addAssistantConfirmation(`我将为世界观元素"${worldBuilding.name}"生成"${fieldDisplayName}"字段的内容。`)
        // 添加基础提示词
        .addBasePrompt(basePrompt)
        // 添加章节内容
        .addChapterContent(chapterContent);

      // 为每个关联世界观元素添加单独的消息
      if (relatedWorldBuildings.length > 0) {
        // 添加关联世界观元素
        await advancedBuilder.addRelatedWorldBuildings(worldBuilding, relatedWorldBuildings, worldBuilding.bookId);
      }

      // 添加输出格式指令
      advancedBuilder.addOutputFormat(`请直接生成"${fieldDisplayName}"的内容，不要有任何解释或前言后语。内容应该详细、生动且符合小说风格。

重要规则：
1. 只返回新信息，不要重复已有内容
2. 确保返回的内容与现有内容有明显区别
3. 新内容应该是对现有内容的补充，而不是替代
4. 不要使用"根据文本"、"文本提到"等解释性语言，直接提供信息本身`);

      // 添加自定义提示词
      advancedBuilder.addCustomPrompt(customPrompt);

      // 构建消息数组
      const messages = advancedBuilder.build();

      // 调用AI模型
      const result = await this.aiSender.sendRequest('', {
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        signal: this.currentRequest?.signal
      });

      return result.text;
    } catch (error) {
      console.error('使用关联章节生成字段内容失败:', error);
      throw error;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.currentRequest = null;
    }
  }
}
