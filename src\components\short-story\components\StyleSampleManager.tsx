/**
 * 风格样本管理组件
 * 用于管理用户的写作风格样本，支持添加、编辑、删除和激活/停用样本
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { StyleSample, StyleSampleService } from '../../../services/ai/StyleSampleService';
import StyleSampleCard from './StyleSampleCard';
import StyleSampleEditDialog from './StyleSampleEditDialog';

const styleSampleService = StyleSampleService.getInstance();

interface StyleSampleManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const StyleSampleManager: React.FC<StyleSampleManagerProps> = ({ isOpen, onClose }) => {
  const [samples, setSamples] = useState<StyleSample[]>([]);
  const [filteredSamples, setFilteredSamples] = useState<StyleSample[]>([]);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingSample, setEditingSample] = useState<StyleSample | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  useEffect(() => {
    if (isOpen) {
      loadSamples();
    }
  }, [isOpen]);

  // 筛选样本
  useEffect(() => {
    let filtered = samples;

    // 按搜索关键词筛选
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(sample =>
        sample.name.toLowerCase().includes(query) ||
        (sample.description && sample.description.toLowerCase().includes(query)) ||
        sample.content.toLowerCase().includes(query) ||
        (sample.tags && sample.tags.some(tag => tag.toLowerCase().includes(query)))
      );
    }

    // 按状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(sample =>
        statusFilter === 'active' ? sample.isActive : !sample.isActive
      );
    }

    setFilteredSamples(filtered);
  }, [samples, searchQuery, statusFilter]);

  const loadSamples = () => {
    setSamples(styleSampleService.getAllSamples());
  };

  // 处理创建新样本
  const handleCreateSample = () => {
    setEditingSample(null);
    setIsCreating(true);
    setShowEditDialog(true);
  };

  // 处理编辑样本
  const handleEditSample = (sample: StyleSample) => {
    setEditingSample(sample);
    setIsCreating(false);
    setShowEditDialog(true);
  };

  // 处理保存样本
  const handleSaveSample = (sampleData: Partial<StyleSample>) => {
    if (isCreating) {
      // 创建新样本
      const newSampleData = {
        name: sampleData.name || '',
        content: sampleData.content || '',
        description: sampleData.description,
        tags: sampleData.tags || [],
        isActive: sampleData.isActive ?? true
      };
      styleSampleService.saveSample(newSampleData);
    } else if (editingSample) {
      // 更新现有样本
      styleSampleService.updateSample(editingSample.id, sampleData);
    }

    setShowEditDialog(false);
    setEditingSample(null);
    setIsCreating(false);
    loadSamples();
  };

  // 处理删除样本
  const handleDeleteSample = (sample: StyleSample) => {
    styleSampleService.deleteSample(sample.id);
    loadSamples();
  };

  // 处理激活/停用样本
  const handleToggleActive = (sample: StyleSample) => {
    styleSampleService.toggleSampleActive(sample.id);
    loadSamples();
  };

  // 关闭编辑弹窗
  const handleCloseEditDialog = () => {
    setShowEditDialog(false);
    setEditingSample(null);
    setIsCreating(false);
  };

  const stats = styleSampleService.getSampleStats();

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10002] p-4">
      <motion.div
        className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        {/* 头部 */}
        <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">风格样本模仿</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              管理写作风格样本，AI会学习这些样本的风格特点来指导创作
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 统计信息 */}
        <div className="flex-shrink-0 p-6 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">总样本数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">激活样本</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalWords}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">总字数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.activeWords}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">激活字数</div>
            </div>
          </div>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto p-6 min-h-0">
          {/* 搜索和筛选栏 */}
          <div className="mb-6 space-y-4">
            {/* 搜索框 */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索样本名称、描述、内容或标签..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
                  />
                  <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* 状态筛选 */}
              <div className="flex space-x-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                  className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
                >
                  <option value="all">全部状态</option>
                  <option value="active">已激活</option>
                  <option value="inactive">未激活</option>
                </select>

                {/* 添加新样本按钮 */}
                <button
                  onClick={handleCreateSample}
                  className="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 whitespace-nowrap"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>添加样本</span>
                </button>
              </div>
            </div>

            {/* 搜索结果统计 */}
            {(searchQuery.trim() || statusFilter !== 'all') && (
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>
                  找到 {filteredSamples.length} 个样本
                  {searchQuery.trim() && ` 包含 "${searchQuery}"`}
                  {statusFilter !== 'all' && ` (${statusFilter === 'active' ? '已激活' : '未激活'})`}
                </span>
                {(searchQuery.trim() || statusFilter !== 'all') && (
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setStatusFilter('all');
                    }}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    清除筛选
                  </button>
                )}
              </div>
            )}
          </div>

          {/* 样本卡片网格 */}
          {samples.length === 0 ? (
            <div className="text-center py-12 text-gray-500 dark:text-gray-400">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-lg font-medium mb-2">还没有风格样本</p>
              <p className="text-sm">添加一些你喜欢的写作风格样本，让AI学习并模仿这些风格</p>
            </div>
          ) : filteredSamples.length === 0 ? (
            <div className="text-center py-12 text-gray-500 dark:text-gray-400">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <p className="text-lg font-medium mb-2">没有找到匹配的样本</p>
              <p className="text-sm">尝试调整搜索关键词或筛选条件</p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('all');
                }}
                className="mt-4 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                清除所有筛选条件
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence mode="popLayout">
                {filteredSamples.map((sample, index) => (
                  <StyleSampleCard
                    key={sample.id}
                    sample={sample}
                    onEdit={handleEditSample}
                    onDelete={handleDeleteSample}
                    onToggleActive={handleToggleActive}
                    index={index}
                  />
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* 编辑弹窗 */}
        <StyleSampleEditDialog
          isOpen={showEditDialog}
          sample={editingSample}
          onClose={handleCloseEditDialog}
          onSave={handleSaveSample}
          isCreating={isCreating}
        />
      </motion.div>
    </div>,
    document.body
  );
};
