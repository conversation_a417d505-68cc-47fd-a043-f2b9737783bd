/**
 * AI注释服务主类
 * 协调各个处理器完成AI注释任务，提供统一的API接口
 */

import {
  TextSegment,
  AnnotationCallbacks,
  AnnotationResult,
  AIAnnotationServiceInterface
} from './types/AnnotationTypes';
import { PromptBuilder } from './builders/PromptBuilder';
import { ResponseParser } from './processors/ResponseParser';
import { StreamProcessor } from './processors/StreamProcessor';

export class AIAnnotationService implements AIAnnotationServiceInterface {
  constructor(
    private apiSettings: any,
    private aiSender: any
  ) {
  }

  /**
   * 标注文本段落 - 主入口方法
   * @param segments 文本段落数组
   * @param callbacks 回调函数
   * @param userRequirements 用户要求
   */
  async annotateTextSegments(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks,
    userRequirements?: string
  ): Promise<AnnotationResult> {
    try {
      callbacks.onStart?.();

      console.log('🚀 开始AI注释处理:', {
        segmentCount: segments.length,
        userRequirements: userRequirements || '无'
      });

      // 常规处理逻辑
      return this.processSegmentsRegular(segments, callbacks, userRequirements);

    } catch (error: any) {
      console.error('❌ AI注释处理失败:', error);

      const errorResult: AnnotationResult = {
        segments: [],
        success: false,
        error: error instanceof Error ? error.message : 'AI注释处理失败',
        totalProcessed: 0,
        totalSuggestions: 0
      };

      callbacks.onError?.(error);
      return errorResult;
    }
  }

  /**
   * 常规段落处理
   * @param segments 文本段落数组
   * @param callbacks 回调函数
   * @param userRequirements 用户要求
   */
  private async processSegmentsRegular(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks,
    userRequirements?: string
  ): Promise<AnnotationResult> {
    const annotatedSegments: TextSegment[] = [];
    let totalSuggestions = 0;

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      console.log(`🔄 处理段落 ${i + 1}/${segments.length}:`, {
        segmentId: segment.id,
        sentenceCount: segment.sentences.length
      });

      // 更新进度
      callbacks.onProgress?.(
        (i / segments.length) * 100,
        i + 1,
        segments.length
      );

      try {
        // 处理单个段落
        const annotatedSegment = await this.processSingleSegment(
          segment,
          callbacks,
          userRequirements
        );

        annotatedSegments.push(annotatedSegment);

        // 统计建议数量
        const suggestionCount = annotatedSegment.sentences.filter(
          s => s.aiSuggestion && s.modificationType !== 'keep'
        ).length;
        totalSuggestions += suggestionCount;

        callbacks.onSegmentComplete?.(annotatedSegment);

        console.log(`✅ 段落 ${i + 1} 处理完成:`, {
          segmentId: annotatedSegment.id,
          suggestionCount
        });

        // 添加延迟避免API限制
        if (i < segments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error: any) {
        console.error(`❌ 段落 ${i + 1} 处理失败:`, error);

        // 创建失败的段落
        const failedSegment: TextSegment = {
          ...segment,
          sentences: segment.sentences.map(sentence => ({
            ...sentence,
            processingStatus: 'failed' as const,
            processingError: error?.message || String(error),
            lastProcessedAt: new Date(),
            aiSuggestion: '',
            modificationType: 'keep' as const
          }))
        };

        annotatedSegments.push(failedSegment);
        callbacks.onSegmentComplete?.(failedSegment);
      }
    }

    const result: AnnotationResult = {
      segments: annotatedSegments,
      success: true,
      totalProcessed: annotatedSegments.length,
      totalSuggestions
    };

    callbacks.onComplete?.(result);
    console.log('🎉 常规模式处理完成:', {
      totalSegments: annotatedSegments.length,
      totalSuggestions
    });

    return result;
  }

  /**
   * 处理单个段落
   * @param segment 文本段落
   * @param callbacks 回调函数
   * @param userRequirements 用户要求
   */
  private async processSingleSegment(
    segment: TextSegment,
    callbacks: AnnotationCallbacks,
    userRequirements?: string
  ): Promise<TextSegment> {
    try {
      console.log('🚀 开始处理段落:', {
        segmentId: segment.id,
        sentenceCount: segment.sentences.length
      });

      // 预先验证API配置
      const provider = this.apiSettings.getCurrentProvider() || 'openai';
      const apiKey = this.apiSettings.getAPIKey(provider);
      const model = this.apiSettings.getCurrentModel() || 'gpt-4';
      const apiEndpoint = this.apiSettings.getAPIEndpoint(provider);

      console.log('🔍 API配置检查:', {
        provider,
        model,
        hasApiKey: !!apiKey,
        apiKeyLength: apiKey ? apiKey.length : 0,
        apiEndpoint
      });

      if (!apiKey) {
        const detailedError = `API配置不完整：缺少${provider}的API密钥。请在设置中配置API密钥后重试。当前提供商：${provider}`;
        console.error('❌ API密钥缺失:', detailedError);
        throw new Error(detailedError);
      }

      console.log('✅ API配置验证通过');

      // 标记所有句子为处理中状态
      const processingSegment = {
        ...segment,
        sentences: segment.sentences.map(sentence => ({
          ...sentence,
          processingStatus: 'processing' as const,
          lastProcessedAt: new Date()
        }))
      };

      // 通知UI开始处理
      callbacks.onSegmentComplete?.(processingSegment);

      // 构建提示消息
      const messages = PromptBuilder.buildSegmentPrompt(segment, userRequirements);

      let streamResponse = '';
      const updatedSentences = [...processingSegment.sentences];

      // 发送AI请求
      const result = await this.sendAIRequest(
        messages,
        (chunk: string) => {
          streamResponse += chunk;
          StreamProcessor.processStreamingResponse(streamResponse, updatedSentences, callbacks);
        }
      );

      if (result.success && result.text) {
        console.log('✅ AI请求成功，开始最终解析');
        const updatedSegment = ResponseParser.parseSegmentResponse(segment, result.text);

        const finalSegment = {
          ...updatedSegment,
          sentences: updatedSegment.sentences.map(sentence => ({
            ...sentence,
            processingStatus: sentence.aiSuggestion ? 'completed' as const : 'failed' as const,
            lastProcessedAt: new Date()
          }))
        };

        console.log('✅ 段落处理完成:', {
          segmentId: segment.id,
          completedSentences: finalSegment.sentences.filter(s => s.processingStatus === 'completed').length,
          failedSentences: finalSegment.sentences.filter(s => s.processingStatus === 'failed').length
        });

        return finalSegment;
      }

      // 如果AI请求失败，但流式解析有结果，返回流式解析的结果
      console.log('⚠️ AI请求失败，使用流式解析结果');
      const updatedSegment = {
        ...segment,
        sentences: updatedSentences.map(sentence => ({
          ...sentence,
          processingStatus: sentence.aiSuggestion ? 'completed' as const : 'failed' as const,
          lastProcessedAt: new Date()
        }))
      };

      return updatedSegment;

    } catch (error: any) {
      console.error('❌ 生成句子建议失败:', {
        segmentId: segment.id,
        error: error?.message || String(error)
      });

      // 标记所有句子为失败状态
      const failedSegment = {
        ...segment,
        sentences: segment.sentences.map(sentence => ({
          ...sentence,
          processingStatus: 'failed' as const,
          processingError: error?.message || String(error),
          lastProcessedAt: new Date(),
          aiSuggestion: '',
          modificationType: 'keep' as const
        }))
      };

      return failedSegment;
    }
  }

  /**
   * 全文段落式确认处理
   * @param segments 文本段落数组
   * @param callbacks 回调函数
   * @param userRequirements 用户要求
   */
  async annotateFullText(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks,
    userRequirements?: string
  ): Promise<AnnotationResult> {
    try {
      callbacks.onStart?.();

      console.log('🚀 开始全文段落式确认处理:', {
        segmentCount: segments.length,
        totalSentences: segments.reduce((sum, seg) => sum + seg.sentences.length, 0)
      });

      const annotatedSegments: TextSegment[] = [];
      let totalSuggestions = 0;

      // 逐个段落处理，但每次都提供完整文档上下文
      for (let i = 0; i < segments.length; i++) {
        const currentSegment = segments[i];
        console.log(`🔄 段落式确认处理 ${i + 1}/${segments.length}:`, {
          segmentId: currentSegment.id,
          sentenceCount: currentSegment.sentences.length
        });

        // 更新进度
        callbacks.onProgress?.(
          (i / segments.length) * 100,
          i + 1,
          segments.length
        );

        try {
          // 标记当前段落为处理中状态
          const processingSegment = {
            ...currentSegment,
            sentences: currentSegment.sentences.map(sentence => ({
              ...sentence,
              processingStatus: 'processing' as const,
              lastProcessedAt: new Date()
            }))
          };

          // 通知UI开始处理当前段落
          callbacks.onSegmentComplete?.(processingSegment);

          // 构建段落式确认消息，包含完整文档上下文但专注当前段落
          const messages = PromptBuilder.buildFullTextPrompt(segments, userRequirements, i);

          let streamResponse = '';
          const updatedSentences = [...processingSegment.sentences];

          // 发送AI请求
          const result = await this.sendAIRequest(
            messages,
            (chunk: string) => {
              streamResponse += chunk;
              // 使用段落级流式处理
              StreamProcessor.processStreamingResponse(streamResponse, updatedSentences, callbacks);
            }
          );

          if (result.success && result.text) {
            console.log(`✅ 段落 ${i + 1} AI请求成功，开始解析`);
            // 解析当前段落的响应
            const updatedSegment = ResponseParser.parseSegmentResponse(currentSegment, result.text);

            const finalSegment = {
              ...updatedSegment,
              sentences: updatedSegment.sentences.map(sentence => ({
                ...sentence,
                processingStatus: sentence.aiSuggestion ? 'completed' as const : 'failed' as const,
                lastProcessedAt: new Date()
              }))
            };

            annotatedSegments.push(finalSegment);

            // 统计建议数量
            const suggestionCount = finalSegment.sentences.filter(
              s => s.aiSuggestion && s.modificationType !== 'keep'
            ).length;
            totalSuggestions += suggestionCount;

            callbacks.onSegmentComplete?.(finalSegment);

            console.log(`✅ 段落 ${i + 1} 处理完成:`, {
              segmentId: finalSegment.id,
              suggestionCount
            });

          } else {
            // 如果AI请求失败，但流式解析有结果，使用流式解析的结果
            console.log(`⚠️ 段落 ${i + 1} AI请求失败，使用流式解析结果`);
            const updatedSegment = {
              ...currentSegment,
              sentences: updatedSentences.map(sentence => ({
                ...sentence,
                processingStatus: sentence.aiSuggestion ? 'completed' as const : 'failed' as const,
                lastProcessedAt: new Date()
              }))
            };

            annotatedSegments.push(updatedSegment);
            callbacks.onSegmentComplete?.(updatedSegment);
          }

          // 添加延迟避免API限制
          if (i < segments.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error: any) {
          console.error(`❌ 段落 ${i + 1} 处理失败:`, error);

          // 创建失败的段落
          const failedSegment: TextSegment = {
            ...currentSegment,
            sentences: currentSegment.sentences.map(sentence => ({
              ...sentence,
              processingStatus: 'failed' as const,
              processingError: error?.message || String(error),
              lastProcessedAt: new Date(),
              aiSuggestion: '',
              modificationType: 'keep' as const
            }))
          };

          annotatedSegments.push(failedSegment);
          callbacks.onSegmentComplete?.(failedSegment);
        }
      }

      const result: AnnotationResult = {
        segments: annotatedSegments,
        success: true,
        totalProcessed: annotatedSegments.length,
        totalSuggestions
      };

      callbacks.onComplete?.(result);
      console.log('🎉 全文段落式确认处理完成:', {
        totalSegments: annotatedSegments.length,
        totalSuggestions
      });

      return result;

    } catch (error: any) {
      console.error('❌ 全文段落式确认处理失败:', error);

      const errorResult: AnnotationResult = {
        segments: [],
        success: false,
        error: error instanceof Error ? error.message : '全文段落式确认处理失败',
        totalProcessed: 0,
        totalSuggestions: 0
      };

      callbacks.onError?.(error);
      return errorResult;
    }
  }

  /**
   * 发送AI请求的通用方法
   * @param messages 消息数组
   * @param onChunk 流式回调函数
   * @returns AI响应结果
   */
  private async sendAIRequest(messages: any[], onChunk?: (chunk: string) => void): Promise<any> {
    try {
      // 获取API设置
      const provider = this.apiSettings.getCurrentProvider() || 'openai';
      const model = this.apiSettings.getCurrentModel() || 'gpt-4';
      const apiKey = this.apiSettings.getAPIKey(provider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(provider);
      const maxTokens = this.apiSettings.getMaxTokens?.() || 8000;

      console.log('🔍 sendAIRequest API配置详情:', {
        provider,
        model,
        hasApiKey: !!apiKey,
        apiKeyLength: apiKey ? apiKey.length : 0,
        apiEndpoint,
        maxTokens,
        messagesCount: messages.length
      });

      if (!apiKey) {
        const detailedError = `请先在设置中配置${provider === 'openai' ? 'OpenAI' : provider === 'google' ? 'Google' : '自定义'}的API密钥。当前提供商：${provider}，端点：${apiEndpoint}`;
        console.error('❌ API密钥缺失详情:', {
          provider,
          apiEndpoint,
          error: detailedError
        });
        throw new Error(detailedError);
      }

      console.log('📡 开始发送AI请求...');

      // 获取动态配置
      let aiConfig: any = {};
      try {
        const { configService } = await import('@/services/configService');
        aiConfig = await configService.getAIConfig();
        console.log('🤖 获取到的AI配置:', aiConfig);
      } catch (configError) {
        console.warn('🤖 无法获取AI配置，使用默认设置:', configError);
      }

      // 发送AI请求
      const result = await this.aiSender.sendStreamingRequest(
        '',
        onChunk || (() => {}),
        {
          messages: messages,
          provider: provider,
          model: model,
          apiKey: apiKey,
          apiEndpoint: apiEndpoint,
          // 使用动态配置，标注任务使用中等温度
          temperature: aiConfig.temperature || 0.5,
          topP: aiConfig.topP,
          topK: aiConfig.topK,
          // 使用动态配置的maxTokens，如果没有则不设置让API决定
          ...(aiConfig.maxTokens && { maxTokens: aiConfig.maxTokens }),
          streaming: !!onChunk
        }
      );

      console.log('✅ AI请求完成:', {
        success: result.success,
        hasText: !!result.text,
        textLength: result.text ? result.text.length : 0
      });

      return result;
    } catch (error: any) {
      console.error('❌ sendAIRequest失败:', {
        error: error.message,
        errorType: error.name || 'Unknown',
        stack: error.stack
      });
      throw error;
    }
  }
}

/**
 * 创建AI标注服务的工厂函数
 */
export function createAIAnnotationService(apiSettings: any, aiSender: any): AIAnnotationServiceInterface {
  return new AIAnnotationService(apiSettings, aiSender);
}
