/* 书名生成器专用样式 */

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 针对不同区域的滚动条颜色 */
.results-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

.history-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #10b981, #059669);
}

.analysis-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* 滑块样式 */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: #e2e8f0;
  outline: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.slider:hover {
  background: #cbd5e1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 书名卡片动画增强 */
.title-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(229, 231, 235, 1);
}

.title-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(59, 130, 246, 0.3);
}

/* 内容区域优化 */
.title-generator-container {
  height: 100%;
  min-height: 0;
}

.title-generator-left {
  height: 100%;
  min-height: 0;
}

.title-generator-right {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 左侧滚动条样式 */
.title-generator-left .custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.title-generator-left .custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.title-generator-left .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.title-generator-left .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* 按钮悬停光晕效果 */
.group:hover .text-blue-500 {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.group:hover .text-purple-500 {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.5));
}

.group:hover .text-emerald-500 {
  filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.5));
}

/* 关键词标签动画 */
.keyword-tag {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 生成按钮增强效果 */
.generate-button {
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.generate-button:hover {
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.generate-button:disabled {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: none;
}

/* 按钮组悬停增强效果 */
.group:hover .group-hover\:rotate-12 {
  transform: rotate(12deg);
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:animate-pulse {
  animation: pulse 1s infinite;
}

/* 星级评分动画 */
.star-rating {
  display: inline-flex;
  gap: 2px;
}

.star {
  transition: all 0.2s ease;
  cursor: pointer;
}

.star:hover {
  transform: scale(1.1);
}

/* 标签页切换动画 */
.tab-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画增强 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 操作按钮悬停效果 */
.action-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.action-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-button:hover::after {
  width: 100px;
  height: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title-generator-container {
    flex-direction: column;
  }
  
  .title-generator-left {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .title-generator-right {
    width: 100%;
  }
  
  .style-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .emotion-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .style-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .emotion-grid {
    grid-template-columns: 1fr;
  }
  
  .title-card {
    padding: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .slider {
    background: #374151;
  }
  
  .slider:hover {
    background: #4b5563;
  }
  
  .title-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .keyword-tag {
    background: #374151;
    color: #d1d5db;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .title-card,
  .star,
  .action-button,
  .keyword-tag {
    animation: none;
    transition: none;
  }
  
  .title-card:hover {
    transform: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .slider::-webkit-slider-thumb {
    border: 2px solid #000;
  }
  
  .title-card {
    border-width: 2px;
  }
  
  .action-button {
    border: 2px solid currentColor;
  }
}
