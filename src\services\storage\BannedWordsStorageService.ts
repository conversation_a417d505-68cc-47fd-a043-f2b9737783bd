// 禁用词汇分类定义
export interface BannedWordCategory {
  id: string;
  name: string;
  description: string;
  words: string[];
  color: string;
  isCustom?: boolean;
}

// 禁用词汇配置
export interface BannedWordsConfig {
  selectedCategories: string[];
  customCategories: BannedWordCategory[];
  customWords: string;
  categoryWords: Record<string, string[]>; // 分类ID -> 用户添加的额外词汇
}

export class BannedWordsStorageService {
  private static instance: BannedWordsStorageService;
  private readonly STORAGE_KEY = 'banned_words_config';

  private constructor() {}

  static getInstance(): BannedWordsStorageService {
    if (!BannedWordsStorageService.instance) {
      BannedWordsStorageService.instance = new BannedWordsStorageService();
    }
    return BannedWordsStorageService.instance;
  }

  /**
   * 保存禁用词汇配置
   */
  saveConfig(config: BannedWordsConfig): void {
    try {
      const configToSave = {
        ...config,
        selectedCategories: Array.from(config.selectedCategories)
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configToSave));
      console.log('✅ 禁用词汇配置已保存');
    } catch (error) {
      console.error('❌ 保存禁用词汇配置失败:', error);
    }
  }

  /**
   * 加载禁用词汇配置
   */
  loadConfig(): BannedWordsConfig {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        const config = JSON.parse(saved);
        console.log('✅ 已加载禁用词汇配置');
        return {
          selectedCategories: config.selectedCategories || [],
          customCategories: config.customCategories || [],
          customWords: config.customWords || '',
          categoryWords: config.categoryWords || {}
        };
      }
    } catch (error) {
      console.error('❌ 加载禁用词汇配置失败:', error);
    }

    // 返回默认配置
    return {
      selectedCategories: [],
      customCategories: [],
      customWords: '',
      categoryWords: {}
    };
  }

  /**
   * 保存选中的分类
   */
  saveSelectedCategories(selectedCategories: string[]): void {
    const config = this.loadConfig();
    config.selectedCategories = selectedCategories;
    this.saveConfig(config);
  }

  /**
   * 保存自定义分类
   */
  saveCustomCategories(customCategories: BannedWordCategory[]): void {
    const config = this.loadConfig();
    config.customCategories = customCategories;
    this.saveConfig(config);
  }

  /**
   * 保存自定义词汇
   */
  saveCustomWords(customWords: string): void {
    const config = this.loadConfig();
    config.customWords = customWords;
    this.saveConfig(config);
  }

  /**
   * 保存分类的额外词汇
   */
  saveCategoryWords(categoryWords: Record<string, string[]>): void {
    const config = this.loadConfig();
    config.categoryWords = categoryWords;
    this.saveConfig(config);
  }

  /**
   * 添加自定义分类
   */
  addCustomCategory(category: Omit<BannedWordCategory, 'id' | 'isCustom'>): BannedWordCategory {
    const config = this.loadConfig();
    const newCategory: BannedWordCategory = {
      ...category,
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      isCustom: true
    };

    config.customCategories.push(newCategory);
    this.saveConfig(config);

    console.log('✅ 已添加自定义分类:', newCategory.name);
    return newCategory;
  }

  /**
   * 更新自定义分类
   */
  updateCustomCategory(categoryId: string, updatedData: Partial<Omit<BannedWordCategory, 'id' | 'isCustom'>>): BannedWordCategory | null {
    const config = this.loadConfig();

    const categoryIndex = config.customCategories.findIndex(cat => cat.id === categoryId);
    if (categoryIndex === -1) {
      console.error('❌ 未找到要更新的自定义分类:', categoryId);
      return null;
    }

    // 更新分类信息
    config.customCategories[categoryIndex] = {
      ...config.customCategories[categoryIndex],
      ...updatedData
    };

    this.saveConfig(config);
    console.log('✅ 已更新自定义分类:', categoryId, updatedData);
    return config.customCategories[categoryIndex];
  }

  /**
   * 删除自定义分类
   */
  removeCustomCategory(categoryId: string): void {
    const config = this.loadConfig();

    // 从自定义分类中移除
    config.customCategories = config.customCategories.filter(cat => cat.id !== categoryId);

    // 从选中分类中移除
    config.selectedCategories = config.selectedCategories.filter(id => id !== categoryId);

    // 从分类词汇中移除
    delete config.categoryWords[categoryId];

    this.saveConfig(config);
    console.log('✅ 已删除自定义分类:', categoryId);
  }

  /**
   * 更新分类词汇
   */
  updateCategoryWords(categoryId: string, words: string[]): void {
    const config = this.loadConfig();
    config.categoryWords[categoryId] = words;
    this.saveConfig(config);
  }

  /**
   * 清空所有配置
   */
  clearConfig(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('✅ 已清空禁用词汇配置');
    } catch (error) {
      console.error('❌ 清空禁用词汇配置失败:', error);
    }
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    const config = this.loadConfig();
    return JSON.stringify(config, null, 2);
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson);

      // 验证配置格式
      if (typeof config === 'object' && config !== null) {
        this.saveConfig({
          selectedCategories: config.selectedCategories || [],
          customCategories: config.customCategories || [],
          customWords: config.customWords || '',
          categoryWords: config.categoryWords || {}
        });
        console.log('✅ 配置导入成功');
        return true;
      }
    } catch (error) {
      console.error('❌ 配置导入失败:', error);
    }
    return false;
  }
}
