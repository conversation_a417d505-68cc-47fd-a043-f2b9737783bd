"use client";

import React, { useState, useEffect } from 'react';
import { IButtonComponent, ButtonType, ButtonSize } from '../interfaces';

/**
 * 默认按钮组件实现
 */
export class DefaultButtonComponent implements IButtonComponent {
  private text: string = '';
  private type: ButtonType = 'primary';
  private size: ButtonSize = 'medium';
  private disabled: boolean = false;
  private icon: React.ReactNode = null;
  private clickHandler: (() => void) | null = null;

  /**
   * 设置按钮文本
   * @param text 按钮文本
   */
  setText(text: string): void {
    this.text = text;
  }

  /**
   * 设置按钮类型
   * @param type 按钮类型
   */
  setType(type: ButtonType): void {
    this.type = type;
  }

  /**
   * 设置按钮尺寸
   * @param size 按钮尺寸
   */
  setSize(size: ButtonSize): void {
    this.size = size;
  }

  /**
   * 设置按钮是否禁用
   * @param disabled 是否禁用
   */
  setDisabled(disabled: boolean): void {
    this.disabled = disabled;
  }

  /**
   * 设置按钮图标
   * @param icon 图标JSX元素
   */
  setIcon(icon: React.ReactNode): void {
    this.icon = icon;
  }

  /**
   * 设置点击事件处理函数
   * @param handler 点击事件处理函数
   */
  onClick(handler: () => void): void {
    this.clickHandler = handler;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const Button = () => {
      const [text, setText] = useState(this.text);
      const [type, setType] = useState(this.type);
      const [size, setSize] = useState(this.size);
      const [disabled, setDisabled] = useState(this.disabled);
      const [icon, setIcon] = useState<React.ReactNode>(this.icon);

      // 监听属性变化
      useEffect(() => {
        setText(this.text);
      }, [this.text]);

      useEffect(() => {
        setType(this.type);
      }, [this.type]);

      useEffect(() => {
        setSize(this.size);
      }, [this.size]);

      useEffect(() => {
        setDisabled(this.disabled);
      }, [this.disabled]);

      useEffect(() => {
        setIcon(this.icon);
      }, [this.icon]);

      // 处理点击事件
      const handleClick = () => {
        if (!disabled && this.clickHandler) {
          this.clickHandler();
        }
      };

      // 获取按钮样式
      const getButtonStyles = () => {
        // 基础样式
        let styles = {
          backgroundColor: '',
          color: '',
          borderColor: '',
          padding: '',
          fontSize: '',
          opacity: disabled ? 0.5 : 1,
          cursor: disabled ? 'not-allowed' : 'pointer',
        };

        // 类型样式
        switch (type) {
          case 'primary':
            styles.backgroundColor = 'var(--color-primary)';
            styles.color = 'white';
            styles.borderColor = 'var(--color-primary)';
            break;
          case 'secondary':
            styles.backgroundColor = 'var(--color-secondary)';
            styles.color = 'var(--color-text-primary)';
            styles.borderColor = 'var(--color-secondary)';
            break;
          case 'success':
            styles.backgroundColor = 'var(--color-success)';
            styles.color = 'white';
            styles.borderColor = 'var(--color-success)';
            break;
          case 'danger':
            styles.backgroundColor = 'var(--color-danger)';
            styles.color = 'white';
            styles.borderColor = 'var(--color-danger)';
            break;
          case 'info':
            styles.backgroundColor = 'var(--color-info)';
            styles.color = 'white';
            styles.borderColor = 'var(--color-info)';
            break;
          case 'ghost':
            styles.backgroundColor = 'transparent';
            styles.color = 'var(--color-primary)';
            styles.borderColor = 'var(--color-primary)';
            break;
        }

        // 尺寸样式
        switch (size) {
          case 'small':
            styles.padding = '0.25rem 0.5rem';
            styles.fontSize = '0.875rem';
            break;
          case 'medium':
            styles.padding = '0.5rem 1rem';
            styles.fontSize = '1rem';
            break;
          case 'large':
            styles.padding = '0.75rem 1.5rem';
            styles.fontSize = '1.125rem';
            break;
        }

        return styles;
      };

      const buttonStyles = getButtonStyles();

      return (
        <button
          className="btn rounded-lg transition-all duration-200 flex items-center justify-center"
          style={{
            ...buttonStyles,
            borderRadius: '0.5rem', // 更大的圆角
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // 添加阴影
          }}
          onClick={handleClick}
          disabled={disabled}
        >
          {icon && <span className="mr-2">{icon}</span>}
          {text}
        </button>
      );
    };

    return <Button />;
  }
}
