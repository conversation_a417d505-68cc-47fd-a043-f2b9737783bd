"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { BRAINSTORM_TYPES, BrainstormType } from './types';

interface TypeSelectorProps {
  selectedType: string | null;
  onTypeSelect: (typeId: string) => void;
  associationData?: any; // 关联数据，用于显示可用的关联信息
}

/**
 * 脑洞类型选择器组件
 * 显示所有可用的脑洞类型，并根据关联数据显示推荐
 */
const TypeSelector: React.FC<TypeSelectorProps> = ({
  selectedType,
  onTypeSelect,
  associationData
}) => {
  // 根据关联数据计算推荐度
  const getRecommendationScore = (type: BrainstormType): number => {
    if (!associationData) return 0;
    
    let score = 0;
    
    // 根据不同类型的关联数据计算推荐度
    switch (type.category) {
      case 'character':
        score = associationData.characters?.length || 0;
        break;
      case 'world':
        score = associationData.worldSettings?.length || 0;
        break;
      case 'content':
        score = (associationData.outline?.length || 0) + (associationData.glossary?.length || 0);
        break;
      case 'structure':
        score = associationData.outline?.length || 0;
        break;
    }
    
    return Math.min(score, 5); // 最大5分
  };

  const renderRecommendationBadge = (score: number) => {
    if (score === 0) return null;
    
    return (
      <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
        {score}
      </div>
    );
  };

  return (
    <div className="p-4 border-b border-gray-200 bg-gray-50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">选择脑洞类型</h3>
        <div className="text-sm text-gray-600">
          <span className="inline-flex items-center">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
            数字表示可关联的数据量
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {BRAINSTORM_TYPES.map((type) => {
          const recommendationScore = getRecommendationScore(type);
          const isSelected = selectedType === type.id;
          
          return (
            <motion.button
              key={type.id}
              onClick={() => onTypeSelect(type.id)}
              className={`relative p-4 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'ring-2 ring-purple-500 shadow-lg scale-105'
                  : 'hover:shadow-md hover:scale-102'
              }`}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className={`bg-gradient-to-r ${type.color} p-3 rounded-lg text-white relative`}>
                {renderRecommendationBadge(recommendationScore)}
                
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{type.icon}</span>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{type.name}</h4>
                    <p className="text-xs opacity-90 mt-1 line-clamp-2">{type.description}</p>
                  </div>
                </div>
                
                {/* 类别标签 */}
                <div className="mt-2">
                  <span className="inline-block px-2 py-1 bg-white/20 rounded-full text-xs">
                    {type.category === 'content' && '内容'}
                    {type.category === 'structure' && '结构'}
                    {type.category === 'character' && '角色'}
                    {type.category === 'world' && '世界'}
                  </span>
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
      
      {/* 关联数据概览 */}
      {associationData && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-800 mb-2">📊 可用关联数据</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div className="flex items-center space-x-1">
              <span>👤</span>
              <span>角色: {associationData.characters?.length || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>🌍</span>
              <span>世界观: {associationData.worldSettings?.length || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>📖</span>
              <span>词条: {associationData.glossary?.length || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>📋</span>
              <span>大纲: {associationData.outline?.length || 0}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TypeSelector;
