"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';


interface OutlineFramework {
  id: string;
  frameworkName: string;
  frameworkPattern: string;
  frameworkVariables: string[];
  patternType: string;
  extractedFrom: {
    chapterIds: string[];
    chapterTitles: string[];
    extractDate: Date;
  };
  usageCount: number;
  createdAt: string;
  lastUsedAt?: string;
}

interface FrameworkSelectorProps {
  selectedFramework?: OutlineFramework | null;
  selectedFrameworks?: OutlineFramework[];
  onFrameworkSelect: (framework: OutlineFramework | null) => void;
  onFrameworkMultiSelect?: (frameworks: OutlineFramework[]) => void;
  allowMultiple?: boolean;
  allowCustom?: boolean;
  onFrameworkDelete?: (framework: OutlineFramework) => void;
  allowDelete?: boolean;
  className?: string;
}

/**
 * 框架选择弹窗组件
 */
interface FrameworkSelectorModalProps {
  isOpen: boolean;
  frameworks: OutlineFramework[];
  selectedFramework?: OutlineFramework | null;
  selectedFrameworks?: OutlineFramework[]; // 多选支持
  onClose: () => void;
  onSelect: (framework: OutlineFramework | null) => void;
  onMultiSelect?: (frameworks: OutlineFramework[]) => void; // 多选回调
  isLoading: boolean;
  allowMultiple?: boolean; // 是否允许多选
  allowCustom?: boolean; // 是否允许自定义
  onDelete?: (framework: OutlineFramework) => void; // 删除回调
  allowDelete?: boolean; // 是否允许删除
}

const FrameworkSelectorModal: React.FC<FrameworkSelectorModalProps> = ({
  isOpen,
  frameworks,
  selectedFramework,
  selectedFrameworks = [],
  onClose,
  onSelect,
  onMultiSelect,
  isLoading,
  allowMultiple = false,
  allowCustom = false,
  onDelete,
  allowDelete = true
}) => {
  // 内部状态
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [internalSelectedFrameworks, setInternalSelectedFrameworks] = useState<OutlineFramework[]>(selectedFrameworks);
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [customFramework, setCustomFramework] = useState({
    frameworkName: '',
    frameworkPattern: '',
    patternType: '',
    frameworkVariables: [] as string[]
  });

  // 搜索和筛选逻辑
  const filteredFrameworks = frameworks.filter(framework => {
    const matchesSearch = framework.frameworkName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         framework.frameworkPattern.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (framework.patternType && framework.patternType.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesType = filterType === 'all' || framework.patternType === filterType;

    return matchesSearch && matchesType;
  });

  // 获取所有模式类型用于筛选
  const patternTypes = Array.from(new Set(frameworks.map(f => f.patternType).filter(Boolean)));

  // 多选处理逻辑
  const handleFrameworkToggle = useCallback((framework: OutlineFramework) => {
    if (allowMultiple) {
      const isSelected = internalSelectedFrameworks.some(f => f.id === framework.id);
      let newSelection: OutlineFramework[];

      if (isSelected) {
        newSelection = internalSelectedFrameworks.filter(f => f.id !== framework.id);
      } else {
        newSelection = [...internalSelectedFrameworks, framework];
      }

      setInternalSelectedFrameworks(newSelection);
      onMultiSelect?.(newSelection);
    } else {
      onSelect(framework);
    }
  }, [allowMultiple, internalSelectedFrameworks, onMultiSelect, onSelect]);

  // 删除框架处理逻辑
  const handleDeleteFramework = useCallback((framework: OutlineFramework, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡

    // 显示确认对话框
    if (window.confirm(`确定要删除框架"${framework.frameworkName}"吗？此操作不可撤销。`)) {
      // 调用外部删除回调
      onDelete?.(framework);
    }
  }, [onDelete]);

  // 自定义框架创建
  const handleCreateCustom = useCallback(() => {
    if (customFramework.frameworkName && customFramework.frameworkPattern) {
      const newFramework: OutlineFramework = {
        id: `custom_${Date.now()}`,
        frameworkName: customFramework.frameworkName,
        frameworkPattern: customFramework.frameworkPattern,
        frameworkVariables: customFramework.frameworkVariables,
        patternType: customFramework.patternType || '自定义',
        extractedFrom: {
          chapterIds: [],
          chapterTitles: [],
          extractDate: new Date()
        },
        usageCount: 0,
        createdAt: new Date().toISOString()
      };

      // 保存到localStorage
      const existingFrameworks = JSON.parse(localStorage.getItem('outline-frameworks') || '[]');
      const updatedFrameworks = [...existingFrameworks, newFramework];
      localStorage.setItem('outline-frameworks', JSON.stringify(updatedFrameworks));

      // 选择新创建的框架
      onSelect(newFramework);

      // 重置表单
      setCustomFramework({
        frameworkName: '',
        frameworkPattern: '',
        patternType: '',
        frameworkVariables: []
      });
      setShowCustomForm(false);
    }
  }, [customFramework, onSelect]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  // ESC键关闭弹窗
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden'; // 防止背景滚动
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* 弹窗内容 */}
      <div className="relative bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-6xl max-h-[85vh] overflow-hidden animate-in fade-in-0 zoom-in-95 duration-200">
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {allowMultiple ? '选择框架（多选）' : '选择框架'}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              共 {frameworks.length} 个可用框架 {allowMultiple && internalSelectedFrameworks.length > 0 && `，已选择 ${internalSelectedFrameworks.length} 个`}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {allowMultiple && internalSelectedFrameworks.length > 0 && (
              <button
                onClick={() => {
                  setInternalSelectedFrameworks([]);
                  onMultiSelect?.([]);
                }}
                className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                清除所有选择
              </button>
            )}
            {!allowMultiple && (
              <button
                onClick={() => onSelect(null)}
                className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                清除选择
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        {/* 左右栏布局 */}
        <div className="flex h-[70vh]">
          {/* 左栏：搜索和筛选 */}
          <div className="w-2/5 border-r border-gray-200 dark:border-gray-700 flex flex-col">
            {/* 搜索区域 */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索框架名称、模式或类型..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100"
                />
                <svg
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            {/* 筛选区域 */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">按类型筛选</h4>
              <div className="space-y-2">
                <button
                  onClick={() => setFilterType('all')}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    filterType === 'all'
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  全部类型 ({frameworks.length})
                </button>
                {patternTypes.map((type) => (
                  <button
                    key={type}
                    onClick={() => setFilterType(type)}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      filterType === type
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {type} ({frameworks.filter(f => f.patternType === type).length})
                  </button>
                ))}
              </div>
            </div>

            {/* 自定义框架按钮 */}
            {allowCustom && (
              <div className="p-4">
                <button
                  onClick={() => setShowCustomForm(true)}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-600 dark:text-green-400">
                    <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM17 13H13V17H11V13H7V11H11V7H13V11H17V13Z"/>
                  </svg>
                  <span className="text-green-700 dark:text-green-300 font-medium">创建自定义框架</span>
                </button>
              </div>
            )}
          </div>

          {/* 右栏：框架列表 */}
          <div className="w-3/5 flex flex-col">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-gray-600 dark:text-gray-400">加载中...</span>
                </div>
              </div>
            ) : filteredFrameworks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 px-6">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-gray-400">
                    <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z"/>
                  </svg>
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {searchQuery ? '未找到匹配的框架' : '暂无可用框架'}
                </h4>
                <p className="text-gray-500 dark:text-gray-400 text-center">
                  {searchQuery ? '请尝试其他搜索关键词' : '请先使用框架提取功能分析章节内容'}
                </p>
              </div>
            ) : (
              <div className="overflow-y-auto p-4 space-y-3">
                {filteredFrameworks.map((framework) => {
                  const isSelected = allowMultiple
                    ? internalSelectedFrameworks.some(f => f.id === framework.id)
                    : selectedFramework?.id === framework.id;

                  return (
                    <div
                      key={framework.id}
                      onClick={() => handleFrameworkToggle(framework)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md relative ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                    >
                      {/* 多选模式下的选择指示器 */}
                      {allowMultiple && (
                        <div className="absolute top-3 right-3">
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                            isSelected
                              ? 'bg-blue-500 border-blue-500'
                              : 'border-gray-300 dark:border-gray-600'
                          }`}>
                            {isSelected && (
                              <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                              </svg>
                            )}
                          </div>
                        </div>
                      )}

                      {/* 删除按钮 */}
                      {allowDelete && onDelete && (
                        <div className={`absolute top-3 ${allowMultiple ? 'right-10' : 'right-3'}`}>
                          <button
                            onClick={(e) => handleDeleteFramework(framework, e)}
                            className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-red-500 dark:hover:bg-red-500 text-gray-500 hover:text-white transition-all duration-200 flex items-center justify-center group"
                            title="删除框架"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                            </svg>
                          </button>
                        </div>
                      )}

                      <div className="flex items-start justify-between mb-3 pr-8">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 text-base">
                          {framework.frameworkName}
                        </h4>
                        <div className="flex flex-col items-end text-xs text-gray-500 dark:text-gray-400 space-y-1">
                          <span className="font-medium">使用 {framework.usageCount} 次</span>
                          <span>{formatDate(framework.createdAt)}</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-purple-600 dark:text-purple-400">框架模式：</span>
                          <code className="text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded">
                            {framework.frameworkPattern}
                          </code>
                        </div>

                        {framework.patternType && (
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-orange-600 dark:text-orange-400">模式类型：</span>
                            <span className="text-sm bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded">
                              {framework.patternType}
                            </span>
                          </div>
                        )}

                        {framework.frameworkVariables && framework.frameworkVariables.length > 0 && (
                          <div className="flex items-start space-x-2">
                            <span className="text-sm font-medium text-green-600 dark:text-green-400">变量列表：</span>
                            <div className="flex flex-wrap gap-1">
                              {framework.frameworkVariables.map((variable, index) => (
                                <span key={index} className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded">
                                  {variable}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* 自定义框架创建表单 */}
        {showCustomForm && (
          <div className="absolute inset-0 bg-white dark:bg-gray-900 z-10 flex flex-col">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">创建自定义框架</h3>
              <button
                onClick={() => setShowCustomForm(false)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <div className="max-w-2xl mx-auto space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    框架名称 *
                  </label>
                  <input
                    type="text"
                    value={customFramework.frameworkName}
                    onChange={(e) => setCustomFramework(prev => ({ ...prev, frameworkName: e.target.value }))}
                    placeholder="例如：主角重生逆袭框架"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    框架模式 *
                  </label>
                  <input
                    type="text"
                    value={customFramework.frameworkPattern}
                    onChange={(e) => setCustomFramework(prev => ({ ...prev, frameworkPattern: e.target.value }))}
                    placeholder="例如：{主角} {重生}后{逆袭} {反派}"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    使用 {"{变量名}"} 格式定义可替换的变量
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    模式类型
                  </label>
                  <input
                    type="text"
                    value={customFramework.patternType}
                    onChange={(e) => setCustomFramework(prev => ({ ...prev, patternType: e.target.value }))}
                    placeholder="例如：重生逆袭流"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    变量列表
                  </label>
                  <input
                    type="text"
                    value={customFramework.frameworkVariables.join(', ')}
                    onChange={(e) => setCustomFramework(prev => ({
                      ...prev,
                      frameworkVariables: e.target.value.split(',').map(v => v.trim()).filter(v => v)
                    }))}
                    placeholder="例如：主角, 重生, 逆袭, 反派"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    用逗号分隔多个变量
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowCustomForm(false)}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleCreateCustom}
                disabled={!customFramework.frameworkName || !customFramework.frameworkPattern}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                创建框架
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // 使用Portal渲染到body
  return typeof window !== 'undefined' ? createPortal(modalContent, document.body) : null;
};

/**
 * 框架选择器组件
 * 用于在大纲AI助手中选择可用的框架
 */
const FrameworkSelector: React.FC<FrameworkSelectorProps> = ({
  selectedFramework,
  selectedFrameworks = [],
  onFrameworkSelect,
  onFrameworkMultiSelect,
  allowMultiple = false,
  allowCustom = true,
  onFrameworkDelete,
  allowDelete = true,
  className = ''
}) => {
  const [frameworks, setFrameworks] = useState<OutlineFramework[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 加载框架数据
  const loadFrameworks = useCallback(() => {
    try {
      setIsLoading(true);

      // 使用ACEFrameworkManager获取所有类型的框架
      const { ACEFrameworkManager } = require('../../../../../services/ACEFrameworkManager');
      const allFrameworks = ACEFrameworkManager.getAllFrameworks();

      // 转换为OutlineFramework格式以保持兼容性
      const convertedFrameworks = allFrameworks.map((framework: any) => ({
        id: framework.id,
        frameworkName: framework.name,
        frameworkPattern: framework.pattern || framework.description,
        patternType: framework.category === 'synopsis-keywords' ? '简介关键词' :
                    framework.category === 'synopsis-framework' ? '简介框架' : '大纲框架',
        usageCount: framework.effectiveness || 0,
        createdAt: new Date().toISOString(),
        lastUsedAt: new Date().toISOString(),
        category: framework.category,
        // 保留原始数据
        ...framework
      }));

      // 按使用次数和创建时间排序
      const sortedFrameworks = convertedFrameworks.sort((a: any, b: any) => {
        if (a.usageCount !== b.usageCount) {
          return b.usageCount - a.usageCount; // 使用次数多的在前
        }
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(); // 新创建的在前
      });

      setFrameworks(sortedFrameworks);
      console.log('🔍 FrameworkSelector加载的框架数量:', sortedFrameworks.length);
      console.log('🔍 框架分类统计:', {
        'synopsis-keywords': sortedFrameworks.filter((f: any) => f.category === 'synopsis-keywords').length,
        'synopsis-framework': sortedFrameworks.filter((f: any) => f.category === 'synopsis-framework').length,
        'outline-framework': sortedFrameworks.filter((f: any) => f.category === 'outline-framework').length
      });

    } catch (error) {
      console.error('加载框架失败:', error);
      setFrameworks([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 组件挂载时加载框架
  useEffect(() => {
    loadFrameworks();
  }, [loadFrameworks]);

  // 选择框架
  const handleFrameworkSelect = useCallback((framework: OutlineFramework | null) => {
    if (framework) {
      // 更新使用次数
      const updatedFrameworks = frameworks.map(f =>
        f.id === framework.id
          ? { ...f, usageCount: f.usageCount + 1, lastUsedAt: new Date().toISOString() }
          : f
      );
      setFrameworks(updatedFrameworks);

      // 保存到localStorage
      localStorage.setItem('outline-frameworks', JSON.stringify(updatedFrameworks));
    }

    onFrameworkSelect(framework);
    setIsModalOpen(false);
  }, [frameworks, onFrameworkSelect]);

  // 删除框架
  const handleFrameworkDelete = useCallback((framework: OutlineFramework) => {
    // 从状态中移除
    const updatedFrameworks = frameworks.filter(f => f.id !== framework.id);
    setFrameworks(updatedFrameworks);

    // 更新localStorage
    localStorage.setItem('outline-frameworks', JSON.stringify(updatedFrameworks));

    // 如果删除的是当前选中的框架，清除选择
    if (selectedFramework?.id === framework.id) {
      onFrameworkSelect(null);
    }

    // 如果是多选模式，从选中列表中移除
    if (allowMultiple && onFrameworkMultiSelect && selectedFrameworks.some(f => f.id !== framework.id)) {
      const updatedSelection = selectedFrameworks.filter(f => f.id !== framework.id);
      onFrameworkMultiSelect(updatedSelection);
    }

    // 调用外部删除回调
    onFrameworkDelete?.(framework);
  }, [frameworks, selectedFramework, allowMultiple, selectedFrameworks, onFrameworkSelect, onFrameworkMultiSelect, onFrameworkDelete]);



  return (
    <div className={`framework-selector ${className}`}>
      {/* 选择按钮 */}
      <button
        onClick={() => setIsModalOpen(true)}
        className="framework-selector-button"
        disabled={isLoading}
      >
        <div className="framework-selector-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12"/>
          </svg>
        </div>
        <span className="framework-selector-text">
          {allowMultiple
            ? (selectedFrameworks.length > 0
                ? (selectedFrameworks.length === 1
                    ? selectedFrameworks[0].frameworkName
                    : `已选择 ${selectedFrameworks.length} 个框架`)
                : '选择框架（多选）')
            : (selectedFramework ? selectedFramework.frameworkName : '选择框架')
          }
        </span>
        <div className="framework-selector-arrow">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 10L12 15L17 10H7Z"/>
          </svg>
        </div>
      </button>

      {/* 框架选择弹窗 */}
      <FrameworkSelectorModal
        isOpen={isModalOpen}
        frameworks={frameworks}
        selectedFramework={selectedFramework}
        selectedFrameworks={selectedFrameworks}
        onClose={() => setIsModalOpen(false)}
        onSelect={handleFrameworkSelect}
        onMultiSelect={onFrameworkMultiSelect}
        isLoading={isLoading}
        allowMultiple={allowMultiple}
        allowCustom={allowCustom}
        onDelete={handleFrameworkDelete}
        allowDelete={allowDelete}
      />
    </div>
  );
};

export default FrameworkSelector;
