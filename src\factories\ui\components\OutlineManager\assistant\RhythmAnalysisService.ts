import { UnifiedAIService, AIServiceType } from '../../../../../services/ai/BaseAIService';

/**
 * 章节摘要接口
 */
export interface ChapterSummary {
  title: string;
  content: string;
  order: number;
  wordCount: number;
  rhythmType?: 'fast' | 'slow' | 'balanced';
  conflictLevel?: number;
  emotionalIntensity?: number;
  keyEvents?: string[];
}

/**
 * 智能节奏分析结果接口
 */
export interface SmartRhythmAnalysis {
  chapterCount: number;
  rhythmPattern: {
    overallPattern: string;
    currentTrend: string;
    distribution: {
      fast: number;
      slow: number;
      balanced: number;
    };
  };
  nextChapterSuggestion: string;
  specificGuidance: string;
  contentAnalysis: {
    strengths: string[];
    weaknesses: string[];
    suggestions: string[];
  };
  genreIdentification: {
    primaryGenre: string;
    confidence: number;
    characteristics: string[];
  };
  rhythmCurve: Array<{
    chapter: number;
    intensity: number;
    type: 'fast' | 'slow' | 'balanced';
  }>;
}

/**
 * 节奏分析服务类
 * 专门处理小说章节的节奏分析功能
 */
export class RhythmAnalysisService extends UnifiedAIService {
  private static instance: RhythmAnalysisService;

  private constructor() {
    super(AIServiceType.RHYTHM_ANALYSIS);
  }

  public static getInstance(): RhythmAnalysisService {
    if (!RhythmAnalysisService.instance) {
      RhythmAnalysisService.instance = new RhythmAnalysisService();
    }
    return RhythmAnalysisService.instance;
  }

  /**
   * 执行流式节奏分析（支持实时显示，使用分段发送）
   */
  public async performStreamingRhythmAnalysis(
    chapters: ChapterSummary[],
    selectedFramework?: any,
    selectedFrameworks?: any[],
    onChunk?: (chunk: string) => void
  ): Promise<SmartRhythmAnalysis> {
    if (chapters.length === 0) {
      throw new Error('NO_CHAPTERS');
    }

    try {
      console.log('🎯 开始流式节奏分析（包含框架信息）...');
      
      // 构建分段消息数组（包含完整章节内容和框架信息）
      const messages = this.buildSegmentedRhythmAnalysisMessages(
        chapters, 
        selectedFramework, 
        selectedFrameworks
      );
      
      let fullResponse = '';
      
      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        (chunk: string) => {
          fullResponse += chunk;
          if (onChunk) {
            onChunk(chunk);
          }
        }
      );

      if (!response.success) {
        throw new Error('AI_SERVICE_ERROR');
      }

      // 解析分析结果
      const analysisResult = this.parseRhythmAnalysisResponse(fullResponse, chapters);
      
      console.log('✅ 流式节奏分析完成:', analysisResult);
      return analysisResult;

    } catch (error) {
      console.error('流式节奏分析失败:', error);
      // 返回基础分析结果作为fallback
      return this.generateFallbackAnalysis(chapters);
    }
  }

  /**
   * 执行独立节奏分析（不依赖其他服务）
   */
  public async performIndependentRhythmAnalysis(
    chapters: ChapterSummary[],
    selectedFramework?: any,
    selectedFrameworks?: any[]
  ): Promise<SmartRhythmAnalysis> {
    if (chapters.length === 0) {
      throw new Error('NO_CHAPTERS');
    }

    try {
      // 加载专用的剧情导向节奏分析提示词
      const rhythmPrompt = this.getRhythmAnalysisPrompt();
      const chapterPrompt = this.getChapterIdentificationPrompt();
      
      // 构建章节分析请求（包含框架信息）
      const analysisRequest = this.buildAnalysisRequestWithFramework(
        chapters, 
        selectedFramework, 
        selectedFrameworks
      );
      
      const analysisMessages = [
        {
          role: 'system',
          content: rhythmPrompt
        },
        {
          role: 'user',
          content: chapterPrompt
        },
        {
          role: 'user',
          content: analysisRequest
        }
      ];

      console.log('🤖 开始剧情导向智能节奏分析...');
      const response = await this.callAI(analysisMessages);

      if (!response.success) {
        throw new Error('AI_SERVICE_ERROR');
      }

      // 解析分析结果
      const analysisResult = this.parseRhythmAnalysisResponse(response.text, chapters);
      
      console.log('✅ 独立节奏分析完成:', analysisResult);
      return analysisResult;

    } catch (error) {
      console.error('独立节奏分析失败:', error);
      // 返回基础分析结果作为fallback
      return this.generateFallbackAnalysis(chapters);
    }
  }

  /**
   * 递归查找所有章节节点，并从数据库获取真正的章节内容
   */
  public async findAllChaptersRecursively(nodes: any[], bookId?: string): Promise<ChapterSummary[]> {
    // 如果有bookId，优先从数据库获取真正的章节内容
    if (bookId) {
      return await this.getBookChaptersForAnalysis(bookId);
    }

    // 如果没有bookId，使用原有的节点遍历逻辑（兼容性保持）
    const chapters: ChapterSummary[] = [];

    const traverse = (nodeList: any[]) => {
      for (const node of nodeList) {
        if (node.type === 'chapter') {
          // 获取更完整的章节内容
          const chapterContent = node.description || node.content || '';
          const chapterSummary = node.summary || '';

          // 合并章节的所有可用内容
          let fullContent = '';
          if (chapterSummary) {
            fullContent += `章节摘要：${chapterSummary}\n\n`;
          }
          if (chapterContent) {
            fullContent += `章节内容：${chapterContent}`;
          }

          chapters.push({
            title: node.title || '未命名章节',
            content: fullContent || '暂无内容',
            order: chapters.length + 1,
            wordCount: fullContent.length,
            rhythmType: 'balanced',
            conflictLevel: 3,
            emotionalIntensity: 3,
            keyEvents: []
          });
        }

        // 递归查找子节点
        if (node.children && Array.isArray(node.children)) {
          traverse(node.children);
        }
      }
    };

    traverse(nodes);
    return chapters;
  }

  /**
   * 从数据库获取书籍的章节内容用于节奏分析
   */
  private async getBookChaptersForAnalysis(bookId: string): Promise<ChapterSummary[]> {
    try {
      const { db } = await import('../../../../../lib/db/dexie');
      const chapters = await db.chapters
        .where('bookId')
        .equals(bookId)
        .toArray();

      // 按order字段排序
      chapters.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

      console.log(`📚 节奏分析获取到 ${chapters.length} 个章节`);

      // 过滤有效章节并提取关键信息
      return chapters
        .filter((chapter: any) => chapter.content && chapter.content.trim().length > 50)
        .map((chapter: any) => ({
          order: chapter.order,
          title: chapter.title,
          wordCount: chapter.wordCount || chapter.content.length,
          content: chapter.content, // 使用真正的章节内容
          summary: chapter.summary || '',
          rhythmType: 'balanced' as const,
          conflictLevel: 3,
          emotionalIntensity: 3,
          keyEvents: []
        }));
    } catch (error) {
      console.error('获取章节内容失败:', error);
      return [];
    }
  }

  /**
   * 构建节奏分析专用的分段消息数组（包含完整章节内容和框架信息）
   */
  private buildSegmentedRhythmAnalysisMessages(
    chapters: ChapterSummary[],
    selectedFramework?: any,
    selectedFrameworks?: any[]
  ): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    // 添加系统提示词
    const systemPrompt = this.getRhythmAnalysisPrompt();
    messages.push({
      role: 'user',
      content: systemPrompt
    });

    // 添加框架信息（如果有）- 使用与AI助手相同的框架处理方式
    const frameworkMessages = this.buildFrameworkMessages(selectedFramework, selectedFrameworks);
    frameworkMessages.forEach(msg => {
      messages.push(msg);
    });

    // 添加分析任务说明
    messages.push({
      role: 'user',
      content: `请对以下${chapters.length}个章节进行十句一段的细化剧情导向节奏分析。我将分段发送每个章节的完整内容，请在收到所有章节后进行综合分析。

【章节概览】
${chapters.map(ch => `第${ch.order}章：${ch.title}（${ch.wordCount}字）`).join('\n')}

【分析模式】十句一段细化分析，通过更小的分段单位来细化分析章节内的节奏变化，利用上下文进行更精准的节奏判断。

请等待所有章节内容发送完毕后，按照剧情导向标准进行分析。`
    });

    // 分段发送每个章节的完整内容（十句一段）- 作为系统消息
    chapters.forEach((chapter, index) => {
      // 使用十句一段的分段方式
      const segments = this.segmentChapterContent(chapter.content || '', 'sentence', 10);

      segments.forEach((segment, segIndex) => {
        messages.push({
          role: 'user',
          content: `【第${chapter.order}章 - 段落${segIndex + 1}/${segments.length}】${chapter.title}

${segment}

---
这是第${chapter.order}章的第${segIndex + 1}/${segments.length}个段落（约10句），请分析其剧情内容和节奏特征。`
        });
      });

      // 章节完成系统提示
      if (index < chapters.length - 1) {
        messages.push({
          role: 'user',
          content: `第${chapter.order}章《${chapter.title}》的所有内容已提供完毕。接下来将提供第${chapters[index + 1].order}章内容。`
        });
      }
    });

    // 最终分析请求
    messages.push({
      role: 'user',
      content: `现在请基于以上${chapters.length}个章节的完整内容，进行十句一段的细化剧情导向节奏分析：

1. 逐段分析每个十句段落的剧情强度（1-5分）、冲突层次、发展方向
2. 分析章节内部的节奏变化曲线和发展趋势
3. 识别题材类型和节奏特点
4. 提供基于细化分段的具体节奏优化建议

请按照流式输出格式进行分析。`
    });

    return messages;
  }

  /**
   * 分段章节内容（支持按字符数或句子数分段）
   */
  private segmentChapterContent(content: string, mode: 'character' | 'sentence' = 'character', sentencesPerSegment: number = 10): string[] {
    if (!content) {
      return [content];
    }

    if (mode === 'sentence') {
      return this.segmentChapterContentBySentences(content, sentencesPerSegment);
    }

    // 原有的按字符分段逻辑
    if (content.length <= 2000) {
      return [content];
    }

    const segments: string[] = [];
    const paragraphs = content.split('\n\n');
    let currentSegment = '';

    for (const paragraph of paragraphs) {
      if (currentSegment.length + paragraph.length > 2000 && currentSegment) {
        segments.push(currentSegment.trim());
        currentSegment = paragraph;
      } else {
        currentSegment += (currentSegment ? '\n\n' : '') + paragraph;
      }
    }

    if (currentSegment) {
      segments.push(currentSegment.trim());
    }

    return segments.length > 0 ? segments : [content];
  }

  /**
   * 按句子数量分段章节内容（十句一段）
   */
  private segmentChapterContentBySentences(content: string, sentencesPerSegment: number = 10): string[] {
    if (!content) {
      return [content];
    }

    // 智能句子分割，处理中文标点和对话
    const sentences = this.splitIntoSentences(content);

    if (sentences.length <= sentencesPerSegment) {
      return [content];
    }

    const segments: string[] = [];
    let currentSegment: string[] = [];

    for (let i = 0; i < sentences.length; i++) {
      currentSegment.push(sentences[i]);

      // 当达到指定句子数量时，寻找最佳分割点
      if (currentSegment.length >= sentencesPerSegment) {
        // 检查是否在对话中间，如果是则延迟分割
        if (this.isInDialogue(sentences, i)) {
          continue;
        }

        // 创建分段
        segments.push(currentSegment.join(''));
        currentSegment = [];
      }
    }

    // 处理剩余的句子
    if (currentSegment.length > 0) {
      segments.push(currentSegment.join(''));
    }

    return segments.length > 0 ? segments : [content];
  }

  /**
   * 智能句子分割，处理中文标点和对话
   */
  private splitIntoSentences(content: string): string[] {
    // 预处理：保护对话内容
    const protectedContent = this.protectDialogueContent(content);

    // 中文句子结束标点符号
    const sentenceEnders = /([。！？；…]+)/g;

    // 分割句子
    const parts = protectedContent.split(sentenceEnders);
    const sentences: string[] = [];

    for (let i = 0; i < parts.length; i += 2) {
      const sentence = parts[i];
      const punctuation = parts[i + 1] || '';

      if (sentence && sentence.trim()) {
        sentences.push((sentence + punctuation).trim());
      }
    }

    // 恢复对话内容
    return sentences.map(sentence => this.restoreDialogueContent(sentence));
  }

  /**
   * 保护对话内容，避免在对话内部分割
   */
  private protectDialogueContent(content: string): string {
    // 使用占位符保护引号内的内容
    let protectedContent = content;
    const dialoguePattern = /["'"'](.*?)["'"']/g;
    const protectedDialogues: string[] = [];

    protectedContent = protectedContent.replace(dialoguePattern, (match) => {
      const index = protectedDialogues.length;
      protectedDialogues.push(match);
      return `__DIALOGUE_${index}__`;
    });

    // 存储保护的对话以便后续恢复
    (this as any)._protectedDialogues = protectedDialogues;

    return protectedContent;
  }

  /**
   * 恢复对话内容
   */
  private restoreDialogueContent(sentence: string): string {
    const protectedDialogues = (this as any)._protectedDialogues || [];

    return sentence.replace(/__DIALOGUE_(\d+)__/g, (match, index) => {
      return protectedDialogues[parseInt(index)] || match;
    });
  }

  /**
   * 检查当前位置是否在对话中间
   */
  private isInDialogue(sentences: string[], currentIndex: number): boolean {
    // 检查当前句子和下一句是否都包含引号
    const current = sentences[currentIndex];
    const next = sentences[currentIndex + 1];

    if (!next) return false;

    // 如果当前句子以引号开始但没有结束引号，说明对话还在继续
    const hasOpenQuote = /["'"']/.test(current) && !/["'"'].*["'"']/.test(current);
    const nextHasQuote = /["'"']/.test(next);

    return hasOpenQuote && nextHasQuote;
  }

  /**
   * 解析节奏分析响应
   */
  private parseRhythmAnalysisResponse(responseText: string, chapters: ChapterSummary[]): SmartRhythmAnalysis {
    try {
      // 基础分析结果
      const analysis: SmartRhythmAnalysis = {
        chapterCount: chapters.length,
        rhythmPattern: {
          overallPattern: '平衡发展',
          currentTrend: '稳步推进',
          distribution: {
            fast: Math.floor(chapters.length * 0.3),
            slow: Math.floor(chapters.length * 0.3),
            balanced: Math.floor(chapters.length * 0.4)
          }
        },
        nextChapterSuggestion: '建议下一章保持当前节奏，适当增加冲突强度',
        specificGuidance: '注重剧情推进与角色发展的平衡',
        contentAnalysis: {
          strengths: ['剧情连贯', '角色鲜明'],
          weaknesses: ['节奏可以更丰富'],
          suggestions: ['增加情感冲突', '丰富对话设计']
        },
        genreIdentification: {
          primaryGenre: '现代都市',
          confidence: 0.8,
          characteristics: ['现实背景', '情感主线']
        },
        rhythmCurve: chapters.map((chapter, index) => ({
          chapter: index + 1,
          intensity: 3,
          type: 'balanced' as const
        }))
      };

      return analysis;

    } catch (error) {
      console.error('解析节奏分析响应失败:', error);
      return this.generateFallbackAnalysis(chapters);
    }
  }

  /**
   * 生成降级分析结果
   */
  private generateFallbackAnalysis(chapters: ChapterSummary[]): SmartRhythmAnalysis {
    const cyclePosition = chapters.length % 4;

    return {
      chapterCount: chapters.length,
      rhythmPattern: {
        overallPattern: '标准三幕式结构',
        currentTrend: '稳步发展',
        distribution: {
          fast: Math.floor(chapters.length * 0.3),
          slow: Math.floor(chapters.length * 0.3),
          balanced: Math.floor(chapters.length * 0.4)
        }
      },
      nextChapterSuggestion: `建议下一章保持当前节奏发展`,
      specificGuidance: '保持剧情连贯性，注重角色发展和冲突设置',
      contentAnalysis: {
        strengths: ['结构完整', '逻辑清晰'],
        weaknesses: ['需要更多细节分析'],
        suggestions: ['增强情感描写', '丰富场景设置']
      },
      genreIdentification: {
        primaryGenre: '通用类型',
        confidence: 0.6,
        characteristics: ['标准结构', '平衡发展']
      },
      rhythmCurve: chapters.map((chapter, index) => ({
        chapter: index + 1,
        intensity: 2 + (index % 3),
        type: (['slow', 'balanced', 'fast'] as const)[index % 3]
      }))
    };
  }



  /**
   * 构建框架消息（参考AI助手的实现）
   */
  private buildFrameworkMessages(selectedFramework?: any, selectedFrameworks?: any[]): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    // 如果有多个框架，使用多框架处理
    if (selectedFrameworks && selectedFrameworks.length > 0) {
      if (selectedFrameworks.length === 1) {
        return this.buildSingleFrameworkMessages(selectedFrameworks[0]);
      } else {
        return this.buildMultiFrameworkMessages(selectedFrameworks);
      }
    }

    // 如果只有单个框架
    if (selectedFramework) {
      return this.buildSingleFrameworkMessages(selectedFramework);
    }

    return messages;
  }

  /**
   * 构建单个框架消息
   */
  private buildSingleFrameworkMessages(framework: any): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    let frameworkContent = `【参考创作模式】\n`;
    frameworkContent += `模式结构：${framework.frameworkPattern || framework.frameworkName}\n`;

    if (framework.patternType) {
      frameworkContent += `模式类型：${framework.patternType}\n`;
    }

    if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
      frameworkContent += `关键变量：${framework.frameworkVariables.join('、')}\n`;
    }

    // 添加框架分析内容
    if (framework.plotAnalysis) {
      frameworkContent += `\n【情节框架】\n`;
      if (framework.plotAnalysis.storyStructure) {
        frameworkContent += `故事结构：${framework.plotAnalysis.storyStructure}\n`;
      }
      if (framework.plotAnalysis.conflictDesign) {
        frameworkContent += `冲突设计：${framework.plotAnalysis.conflictDesign}\n`;
      }
      if (framework.plotAnalysis.rhythmControl) {
        frameworkContent += `节奏控制：${framework.plotAnalysis.rhythmControl}\n`;
      }
    }

    if (framework.styleAnalysis) {
      frameworkContent += `\n【风格特征】\n`;
      if (framework.styleAnalysis.writingStyle) {
        frameworkContent += `写作风格：${framework.styleAnalysis.writingStyle}\n`;
      }
      if (framework.styleAnalysis.pacingFramework) {
        frameworkContent += `节奏框架：${framework.styleAnalysis.pacingFramework}\n`;
      }
    }

    frameworkContent += `\n请在节奏分析中参考此创作模式的节奏特点和控制技巧。`;

    messages.push({
      role: 'user',
      content: frameworkContent
    });

    return messages;
  }

  /**
   * 构建多框架消息（分离展示，避免技巧杂糅）
   */
  private buildMultiFrameworkMessages(frameworks: any[]): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    let frameworkContent = `【参考创作模式组合】\n\n`;

    frameworks.forEach((framework, index) => {
      frameworkContent += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
      frameworkContent += `【框架 ${index + 1}：${framework.frameworkName}】\n`;
      frameworkContent += `模式结构：${framework.frameworkPattern}\n`;

      if (framework.plotAnalysis) {
        frameworkContent += `\n【情节技巧】\n`;
        if (framework.plotAnalysis.storyStructure) {
          frameworkContent += `故事结构：${framework.plotAnalysis.storyStructure}\n`;
        }
        if (framework.plotAnalysis.rhythmControl) {
          frameworkContent += `节奏控制：${framework.plotAnalysis.rhythmControl}\n`;
        }
      }

      if (framework.styleAnalysis) {
        frameworkContent += `\n【风格特征】\n`;
        if (framework.styleAnalysis.pacingFramework) {
          frameworkContent += `节奏框架：${framework.styleAnalysis.pacingFramework}\n`;
        }
      }

      frameworkContent += `\n`;
    });

    frameworkContent += `请学习以上各个框架的独特节奏技巧，在分析中保持每个框架的特色，避免技巧杂糅。`;

    messages.push({
      role: 'user',
      content: frameworkContent
    });

    return messages;
  }

  /**
   * 获取剧情导向节奏分析提示词（公开方法，供OutlineAIService调用）
   */
  public getRhythmAnalysisPrompt(): string {
    return `# 剧情导向节奏分析专用提示词

## 你的身份定位
你是专业的小说剧情分析师，精通网络文学的剧情节奏和读者心理。你的任务是基于剧情强度和发展方向分析章节节奏，而非表面的文本特征。

## 剧情导向分析原则

### 核心分析维度
**1. 剧情强度评估（1-5分制）**
- 1分：日常描写，无明显冲突
- 2分：轻微冲突，情节平缓发展
- 3分：中等冲突，有一定紧张感
- 4分：强烈冲突，高度紧张
- 5分：极致冲突，情绪爆发

**2. 冲突层次识别**
- 表层冲突：直接的对抗、争执、竞争
- 深层冲突：价值观碰撞、利益冲突、情感纠葛
- 潜在冲突：暗流涌动、危机潜伏、矛盾积累

**3. 情节发展方向**
- 上升期：矛盾激化、紧张感增强、冲突升级
- 爆发期：冲突全面爆发、情绪达到顶点、关键决战
- 缓解期：冲突解决、情绪平复、后果处理
- 转折期：剧情方向改变、新矛盾引入、格局重塑

### 题材特色考量
**玄幻修仙类**：
- 快节奏：境界突破、生死战斗、宗门冲突、法宝争夺
- 慢节奏：修炼感悟、师父传授、世界观建立、情感铺垫

**都市言情类**：
- 快节奏：情感爆发、职场竞争、危机处理、误会冲突
- 慢节奏：日常相处、内心独白、关系发展、生活描写

**悬疑推理类**：
- 快节奏：真相揭示、危险逼近、线索汇聚、紧急追逐
- 慢节奏：线索收集、推理分析、氛围营造、心理描写

**历史军事类**：
- 快节奏：战争场面、政治斗争、生死决断、策略对抗
- 慢节奏：历史背景、人物塑造、策略筹划、文化描写

## 剧情导向节奏判断标准

### 快节奏章节特征
**剧情强度**：≥4.0分（满分5分）
**冲突密度**：每1000字包含≥2个冲突点
**情节推进**：重要剧情发展≥3个
**情绪波动**：强烈情绪变化≥2次
**悬念设置**：新悬念或悬念解答≥1个

**典型表现**：
- 角色面临重大选择或危机
- 情节出现重要转折或突破
- 紧张感和压迫感强烈
- 读者情绪被强烈调动

### 慢节奏章节特征
**剧情强度**：≤2.0分
**铺垫比重**：≥40%内容用于铺垫
**内心描写**：≥30%篇幅描写内心活动
**环境建构**：≥25%内容用于环境/背景描写
**情感深度**：深入挖掘角色情感≥2处

**典型表现**：
- 重点描写角色内心世界
- 详细构建世界观或背景
- 为后续冲突做情感铺垫
- 展现角色成长或变化过程

### 平节奏章节特征
**剧情强度**：2.1-3.9分
**内容平衡**：冲突、铺垫、描写相对均衡
**过渡功能**：承上启下，连接前后剧情
**信息密度**：适中的新信息引入量
**节奏调节**：在快慢节奏间起缓冲作用

## 流式分析输出格式

请按以下格式逐章分析，然后提供整体建议：

### 逐章分析格式
\`\`\`
📖 第X章分析：《章节标题》
剧情强度：★★★☆☆ (3.2分)
冲突层次：深层冲突 - 价值观碰撞
发展方向：上升期 - 矛盾激化
节奏判断：平节奏 ✓
关键要素：[具体的剧情要素，如"主角面临道德选择"、"敌对势力首次正面交锋"]
\`\`\`

### 整体分析格式
\`\`\`
🎯 整体节奏分析：
节奏曲线：慢→平→快→慢→快→平→快
剧情发展：符合经典三幕式结构，张弛有度
题材识别：玄幻修仙类，重点关注修炼进展和宗门冲突
节奏分布：快节奏4章(40%)，慢节奏3章(30%)，平节奏3章(30%)

💡 下一章建议：
基于当前剧情发展，建议下一章使用【过渡节奏】
具体指导：当前刚经历高潮战斗，需要情绪缓冲和后续铺垫
剧情建议：处理战斗后果，角色内心变化，为下一轮冲突做准备
预期强度：2.5-3.0分，重点描写角色成长和关系变化
\`\`\`

## 重要提醒

1. **具体化原则**：避免抽象的建议，提供可量化的指标
2. **商业导向**：始终考虑读者喜好和商业价值
3. **剧情优先**：基于剧情内容而非文本表面特征进行分析
4. **题材适配**：根据小说类型调整节奏标准
5. **可操作性**：确保建议具有实际创作指导价值`;
  }

  /**
   * 构建包含框架信息的分析请求
   */
  private buildAnalysisRequestWithFramework(
    chapters: ChapterSummary[],
    selectedFramework?: any,
    selectedFrameworks?: any[]
  ): string {
    const chapterSummaries = chapters.map((chapter) => {
      // 使用完整的章节内容，而不是截断的预览
      const fullContent = chapter.content || '';
      return `
第${chapter.order}章：${chapter.title}
字数：${chapter.wordCount}
章节内容：${fullContent}
`;
    }).join('\n');

    let frameworkInfo = '';
    if (selectedFramework || (selectedFrameworks && selectedFrameworks.length > 0)) {
      frameworkInfo = '\n【参考框架信息】\n';

      if (selectedFramework) {
        frameworkInfo += `主要框架：${selectedFramework.frameworkName}\n`;
        frameworkInfo += `框架描述：${selectedFramework.description || '无描述'}\n`;
      }

      if (selectedFrameworks && selectedFrameworks.length > 0) {
        frameworkInfo += `框架组合：${selectedFrameworks.map(f => f.frameworkName).join('、')}\n`;
      }

      frameworkInfo += '\n请在节奏分析中考虑这些框架的特点和要求。\n';
    }

    return `
请对以下${chapters.length}个章节进行专业的剧情导向节奏分析：

${chapterSummaries}
${frameworkInfo}

**分析要求**：
1. 重点关注剧情强度和冲突密度，而非表面的对话占比
2. 根据情节发展方向判断节奏类型
3. 考虑题材特色（如玄幻重修炼进展，悬疑重线索揭示）
4. 如有框架信息，请结合框架特点进行分析
5. 提供下一章的具体节奏建议

请以流式方式返回分析结果，先分析各章节，最后提供综合建议。`;
  }

  /**
   * 获取章节识别提示词（公开方法，供OutlineAIService调用）
   */
  public getChapterIdentificationPrompt(): string {
    return `# 章节识别与分析指导

## 核心任务
自动识别当前章节数量、分析章节名称模式、评估内容类型，为节奏分析提供准确的基础数据。

## 章节识别算法

### 1. 数量统计
- **准确计数**：统计所有已创建的章节
- **序号识别**：识别章节编号规律（第X章、ChapterX等）
- **完整性检查**：检测是否有缺失或重复的章节

### 2. 命名模式分析
**标题风格识别**：
- 描述性标题：直接描述章节内容
- 悬念性标题：设置悬念吸引读者
- 情感性标题：突出情感色彩
- 功能性标题：标明章节作用

### 3. 内容类型分析
**开篇章节特征**：
- 世界观介绍占比高
- 主角登场和背景设定
- 较多的环境描写
- 相对较慢的节奏

**发展章节特征**：
- 剧情推进明显
- 冲突逐步升级
- 角色关系发展
- 节奏相对平衡

**高潮章节特征**：
- 冲突密度高
- 对话和动作频繁
- 情感强度大
- 快节奏为主

**过渡章节特征**：
- 承上启下功能
- 信息整理和铺垫
- 相对平缓的节奏
- 为下一阶段做准备

## 智能分析维度

### 1. 文本特征分析
**内容构成比例**：
- 对话占比统计
- 描写占比统计
- 叙述占比统计
- 动作占比统计

**剧情推进分析**：
- 主要情节线推进
- 支线剧情发展
- 角色关系变化
- 世界观扩展程度

### 2. 节奏特征提取
**剧情密度**：
- 单章重要事件数量
- 冲突解决速度
- 情节转折频率
- 信息揭示节奏

**情感密度**：
- 情感起伏频率
- 情感强度变化
- 情感类型多样性
- 角色情感发展

## 模式识别与预测

### 1. 循环模式识别
**章节功能循环**：
- 铺垫-冲突-爆发-缓解循环
- 日常-危机-解决-成长循环
- 伏笔布局情况统计

### 2. 循环模式识别
**剧情循环检测**：
- 冲突-发展-解决循环
- 紧张-缓解节奏循环
- 角色成长阶段循环
- 情感起伏波动循环

### 3. 读者体验评估
**阅读节奏**：
- 信息密度适中性
- 情感投入持续性
- 期待值管理效果
- 满足感提供程度

请基于以上指导原则，对提供的章节进行全面的识别和分析。`;
  }
}

// 导出单例实例
export const rhythmAnalysisService = RhythmAnalysisService.getInstance();
