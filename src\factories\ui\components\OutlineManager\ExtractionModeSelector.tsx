"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { ExtractionMode } from './OutlineFrameworkExtractService';

interface ExtractionModeSelectorProps {
  selectedMode: ExtractionMode;
  onModeChange: (mode: ExtractionMode) => void;
  disabled?: boolean;
}

interface ModeOption {
  mode: ExtractionMode;
  title: string;
  description: string;
  icon: string;
  features: string[];
  recommended?: string;
}

const modeOptions: ModeOption[] = [
  {
    mode: ExtractionMode.SUMMARY,
    title: '总结提取',
    description: '分析多个章节的共同模式和技巧',
    icon: '📊',
    features: [
      '提取跨章节的共性特征',
      '分析整体创作风格',
      '适合理解作品框架',
      '快速获得概览性分析'
    ],
    recommended: '推荐用于初次分析'
  },
  {
    mode: ExtractionMode.SINGLE,
    title: '单章节提取',
    description: '深度分析单个章节的详细技巧',
    icon: '🔍',
    features: [
      '深度分析单章节内容',
      '提取详细的写作技巧',
      '专注章节独特之处',
      '获得完整的技巧指导'
    ],
    recommended: '推荐用于精细学习'
  },
  {
    mode: ExtractionMode.RANGE,
    title: '范围提取',
    description: '灵活选择章节范围进行分析',
    icon: '📋',
    features: [
      '灵活的章节范围选择',
      '支持批量处理',
      '可选择分析模式',
      '适合补充提取'
    ],
    recommended: '推荐用于补充分析'
  }
];

const ExtractionModeSelector: React.FC<ExtractionModeSelectorProps> = ({
  selectedMode,
  onModeChange,
  disabled = false
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-6">
        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">选择提取模式</h3>
          <p className="text-sm text-gray-600">根据您的需求选择最适合的分析模式</p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {modeOptions.map((option) => {
          const isSelected = selectedMode === option.mode;
          
          return (
            <motion.div
              key={option.mode}
              whileHover={disabled ? {} : { scale: 1.02 }}
              whileTap={disabled ? {} : { scale: 0.98 }}
              className={`
                relative cursor-pointer rounded-xl border-2 p-6 transition-all duration-200
                ${isSelected 
                  ? 'border-orange-500 bg-orange-50 shadow-lg' 
                  : 'border-gray-200 bg-white hover:border-orange-300 hover:shadow-md'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => !disabled && onModeChange(option.mode)}
            >
              {/* 选中指示器 */}
              {isSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-4 right-4 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center"
                >
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </motion.div>
              )}

              {/* 推荐标签 */}
              {option.recommended && (
                <div className="absolute top-0 left-6 transform -translate-y-1/2">
                  <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                    {option.recommended}
                  </span>
                </div>
              )}

              <div className="flex items-start space-x-4">
                {/* 图标 */}
                <div className={`
                  text-3xl p-3 rounded-xl
                  ${isSelected ? 'bg-orange-100' : 'bg-gray-100'}
                `}>
                  {option.icon}
                </div>

                {/* 内容 */}
                <div className="flex-1">
                  <h4 className={`
                    text-lg font-semibold mb-2
                    ${isSelected ? 'text-orange-900' : 'text-gray-900'}
                  `}>
                    {option.title}
                  </h4>
                  
                  <p className={`
                    text-sm mb-4
                    ${isSelected ? 'text-orange-700' : 'text-gray-600'}
                  `}>
                    {option.description}
                  </p>

                  {/* 特性列表 */}
                  <div className="grid grid-cols-2 gap-2">
                    {option.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className={`
                          w-1.5 h-1.5 rounded-full
                          ${isSelected ? 'bg-orange-500' : 'bg-gray-400'}
                        `} />
                        <span className={`
                          text-xs
                          ${isSelected ? 'text-orange-700' : 'text-gray-600'}
                        `}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 选择提示 */}
              {isSelected && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4 pt-4 border-t border-orange-200"
                >
                  <div className="flex items-center space-x-2 text-orange-700">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium">已选择此模式</span>
                  </div>
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* 模式说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">提取模式说明</h4>
            <p className="text-xs text-blue-700">
              • <strong>总结提取</strong>：适合分析多个章节的共同特征，快速了解整体创作风格<br/>
              • <strong>单章节提取</strong>：适合深度学习特定章节的写作技巧，获得详细指导<br/>
              • <strong>范围提取</strong>：适合灵活选择章节范围，进行补充性分析
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtractionModeSelector;
