"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatSession } from '@/services/chat/SessionManager';
import { LightbulbIcon, ThoughtBubbleIcon, PlusIcon, TrashIcon, ClearIcon, ChevronIcon } from '@/components/icons/SessionIcons';

interface SessionSidebarProps {
  sessions: ChatSession[];
  currentSessionId: string | null;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onDeleteSession: (sessionId: string) => void;
  onRenameSession: (sessionId: string, newName: string) => void;
  onClearSession: (sessionId: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface SessionItemProps {
  session: ChatSession;
  isActive: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onRename: (newName: string) => void;
  onClear: () => void;
}

// 会话项组件
const SessionItem: React.FC<SessionItemProps> = ({
  session,
  isActive,
  onSelect,
  onDelete,
  onRename,
  onClear
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(session.name);
  const [showActions, setShowActions] = useState(false);

  const handleDoubleClick = () => {
    setIsEditing(true);
    setEditName(session.name);
  };

  const handleSaveEdit = () => {
    if (editName.trim() && editName !== session.name) {
      onRename(editName.trim());
    }
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(session.name);
    }
  };

  const formatTime = (date: Date) => {
    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效时间';

    const now = new Date();
    const diff = now.getTime() - validDate.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return validDate.toLocaleDateString();
  };

  return (
    <motion.div
      className={`relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
        isActive
          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
          : 'bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
      }`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      onClick={onSelect}
    >
      {/* 会话颜色指示器 */}
      <div
        className="absolute left-0 top-0 bottom-0 w-1 rounded-l-lg"
        style={{ backgroundColor: session.metadata?.color || '#3B82F6' }}
      />

      <div className="flex items-start space-x-3 ml-2">
        {/* 会话图标 */}
        <div className="flex-shrink-0">
          <LightbulbIcon
            size={18}
            color={isActive ? '#ffffff' : '#6b7280'}
            animated={isActive}
            className="transition-colors duration-200"
          />
        </div>

        <div className="flex-1 min-w-0">
          {/* 会话名称 */}
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyDown={handleKeyPress}
              className="w-full px-2 py-1 text-sm bg-white text-gray-900 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <h3
              className={`font-medium text-sm truncate ${
                isActive ? 'text-white' : 'text-gray-900'
              }`}
              onDoubleClick={handleDoubleClick}
            >
              {session.name}
            </h3>
          )}

          {/* 会话信息 */}
          <div className="flex items-center justify-between mt-1">
            <span
              className={`text-xs ${
                isActive ? 'text-blue-100' : 'text-gray-500'
              }`}
            >
              {session.metadata?.messageCount || 0} 条消息
            </span>
            <span
              className={`text-xs ${
                isActive ? 'text-blue-100' : 'text-gray-400'
              }`}
            >
              {formatTime(session.metadata?.lastActivity || session.updatedAt)}
            </span>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <AnimatePresence>
        {showActions && !isEditing && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute top-2 right-2 flex space-x-1"
          >
            {/* 清空按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClear();
              }}
              className={`p-1 rounded hover:bg-opacity-20 hover:bg-gray-500 transition-colors ${
                isActive ? 'text-white' : 'text-gray-400 hover:text-gray-600'
              }`}
              title="清空聊天"
            >
              <ClearIcon
                size={12}
                color="currentColor"
                animated={true}
              />
            </button>

            {/* 删除按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className={`p-1 rounded hover:bg-opacity-20 hover:bg-red-500 transition-colors ${
                isActive ? 'text-white hover:text-red-200' : 'text-gray-400 hover:text-red-500'
              }`}
              title="删除会话"
            >
              <TrashIcon
                size={12}
                color="currentColor"
                animated={true}
              />
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// 新建会话按钮组件
const NewSessionButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <motion.button
      onClick={onClick}
      className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors group"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-center justify-center space-x-2">
        <PlusIcon
          size={20}
          color="currentColor"
          animated={true}
          className="text-gray-400 group-hover:text-blue-500 transition-colors duration-200"
        />
        <span className="text-sm font-medium text-gray-600 group-hover:text-blue-600">
          新建会话
        </span>
      </div>
    </motion.button>
  );
};

// 主组件
const SessionSidebar: React.FC<SessionSidebarProps> = ({
  sessions,
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onDeleteSession,
  onRenameSession,
  onClearSession,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const handleDeleteSession = (sessionId: string) => {
    setShowDeleteConfirm(sessionId);
  };

  const confirmDelete = () => {
    if (showDeleteConfirm) {
      onDeleteSession(showDeleteConfirm);
      setShowDeleteConfirm(null);
    }
  };

  const handleClearSession = (sessionId: string) => {
    if (confirm('确定要清空这个会话的所有消息吗？')) {
      onClearSession(sessionId);
    }
  };

  // 使用统一的动画容器，不再单独处理折叠状态

  return (
    <motion.div
      className="bg-gray-50 border-r border-gray-200 flex flex-col h-full overflow-hidden"
      animate={{
        width: isCollapsed ? '64px' : 'clamp(320px, 25vw, 400px)'
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.4
      }}
    >
      {/* 头部 */}
      <motion.div
        className="border-b border-gray-200 bg-white"
        animate={{
          padding: isCollapsed ? '16px 8px' : '16px'
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <motion.h2
              className="text-lg font-semibold text-gray-900"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.2 }}
            >
              创意会话
            </motion.h2>
          )}
          {onToggleCollapse && (
            <button
              onClick={onToggleCollapse}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
              title={isCollapsed ? "展开会话列表" : "折叠会话列表"}
            >
              <ChevronIcon
                size={16}
                color="#6b7280"
                direction={isCollapsed ? "right" : "left"}
                animated={true}
              />
            </button>
          )}
        </div>
        {!isCollapsed && (
          <motion.p
            className="text-sm text-gray-500 mt-1"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2, delay: 0.1 }}
          >
            管理你的AI创意对话
          </motion.p>
        )}
      </motion.div>

      {/* 新建会话按钮 */}
      {!isCollapsed && (
        <motion.div
          className="p-4"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <NewSessionButton onClick={onNewSession} />
        </motion.div>
      )}

      {/* 折叠状态下的新建按钮 */}
      {isCollapsed && (
        <motion.div
          className="p-2 flex justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <button
            onClick={onNewSession}
            className="p-2 rounded-lg hover:bg-gray-200 transition-colors"
            title="新建会话"
          >
            <PlusIcon
              size={20}
              color="#6b7280"
              animated={true}
            />
          </button>
        </motion.div>
      )}

      {/* 会话列表 */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {!isCollapsed ? (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <AnimatePresence>
              {sessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, x: -100, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <SessionItem
                    session={session}
                    isActive={session.id === currentSessionId}
                    onSelect={() => onSessionSelect(session.id)}
                    onDelete={() => handleDeleteSession(session.id)}
                    onRename={(newName) => onRenameSession(session.id, newName)}
                    onClear={() => handleClearSession(session.id)}
                  />
                </motion.div>
              ))}
            </AnimatePresence>

            {sessions.length === 0 && (
              <div className="text-center py-8">
                <div className="flex justify-center mb-4">
                  <ThoughtBubbleIcon
                    size={48}
                    color="#9ca3af"
                    animated={true}
                  />
                </div>
                <p className="text-gray-500 text-sm">还没有创意会话</p>
                <p className="text-gray-400 text-xs mt-1">点击上方按钮开始创作</p>
              </div>
            )}
          </motion.div>
        ) : (
          // 折叠状态下的会话图标列表
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            {sessions.slice(0, 5).map((session) => (
              <motion.button
                key={session.id}
                onClick={() => onSessionSelect(session.id)}
                className={`w-full p-2 rounded-lg transition-colors ${
                  session.id === currentSessionId
                    ? 'bg-blue-500 text-white'
                    : 'hover:bg-gray-200 text-gray-600'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                title={session.name}
              >
                <LightbulbIcon
                  size={16}
                  color={session.id === currentSessionId ? '#ffffff' : '#6b7280'}
                  animated={session.id === currentSessionId}
                />
              </motion.button>
            ))}
          </motion.div>
        )}
      </div>

      {/* 删除确认弹窗 */}
      <AnimatePresence>
        {showDeleteConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowDeleteConfirm(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-sm mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">删除会话</h3>
              <p className="text-gray-600 mb-4">
                确定要删除这个会话吗？所有聊天记录将永久丢失。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  删除
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default SessionSidebar;
