"use client";

import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../../types/outline';

interface RelatedItemMap {
  [id: string]: string; // ID到名称的映射
}

interface InlineNodeDetailsPanelProps {
  node: OutlineNodeType | null;
  position: { x: number, y: number };
  onClose: () => void;
  containerRef: React.RefObject<HTMLElement>;
  onSelectNode?: (nodeId: string) => void;
  // 关联项名称映射
  relatedItemsMap?: {
    characters?: RelatedItemMap;
    worldBuilding?: RelatedItemMap;
    terminology?: RelatedItemMap;
  };
}

/**
 * 节点内联详情面板组件
 * 直接在节点位置附近显示详情，提供更直观的查看体验
 */
const InlineNodeDetailsPanel: React.FC<InlineNodeDetailsPanelProps> = ({
  node,
  position,
  onClose,
  containerRef,
  onSelectNode,
  relatedItemsMap = {
    characters: {},
    worldBuilding: {},
    terminology: {}
  }
}) => {
  // 状态管理
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState<'info' | 'children' | 'relations'>('info');

  // 面板引用
  const panelRef = useRef<HTMLDivElement>(null);

  // 计算面板位置
  const calculatePanelPosition = () => {
    if (!containerRef.current || !panelRef.current) return { top: 10, left: 10 };

    const containerRect = containerRef.current.getBoundingClientRect();
    const panelRect = panelRef.current.getBoundingClientRect();

    // 固定显示在左上角，避免位置计算问题
    let top = 10;
    let left = 10;

    // 确保面板不超出容器边界
    if (left + panelRect.width > containerRect.width) {
      left = containerRect.width - panelRect.width - 10;
    }

    if (top + panelRect.height > containerRect.height) {
      top = containerRect.height - panelRect.height - 10;
    }

    // 确保不小于0
    top = Math.max(10, top);
    left = Math.max(10, left);

    console.log('🎯 InlineNodeDetailsPanel位置计算:', {
      原始position: position,
      计算后位置: { top, left },
      容器尺寸: { width: containerRect.width, height: containerRect.height },
      面板尺寸: { width: panelRect.width, height: panelRect.height }
    });

    return { top, left };
  };

  // 组件挂载后设置状态
  useEffect(() => {
    setIsMounted(true);

    // 添加点击外部关闭事件
    const handleClickOutside = (e: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    // 添加ESC键关闭事件
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // 如果没有节点，不渲染任何内容
  if (!node) return null;

  // 计算位置
  const { top, left } = calculatePanelPosition();

  // 获取节点类型颜色
  const getNodeTypeColor = () => {
    return `var(--outline-${
      node.type === 'volume' ? 'volume' :
      node.type === 'chapter' ? 'primary' :
      node.type === 'plot' ? 'secondary' :
      node.type === 'dialogue' ? 'success' : 'info'
    })`;
  };

  // 渲染子节点列表
  const renderChildrenList = () => {
    if (!node.children || node.children.length === 0) {
      return (
        <div className="text-sm text-gray-500 italic p-3">
          此节点没有子节点
        </div>
      );
    }

    return (
      <ul className="max-h-[200px] overflow-y-auto">
        {node.children.map((child) => (
          <li
            key={child.id}
            className="border-b border-gray-100 last:border-b-0 cursor-pointer"
            onClick={() => onSelectNode && onSelectNode(child.id)}
          >
            <div className="px-3 py-2 hover:bg-gray-100 transition-colors">
              <div className="flex items-center">
                <div
                  className="w-2 h-2 rounded-full mr-2"
                  style={{
                    background: `var(--outline-${
                      child.type === 'volume' ? 'volume' :
                      child.type === 'chapter' ? 'primary' :
                      child.type === 'plot' ? 'secondary' :
                      child.type === 'dialogue' ? 'success' : 'info'
                    })`
                  }}
                ></div>
                <div className="text-sm font-medium">{child.title}</div>
              </div>
              {child.description && (
                <div className="text-xs text-gray-500 mt-1 ml-4 truncate">
                  {child.description.substring(0, 50)}{child.description.length > 50 ? '...' : ''}
                </div>
              )}
            </div>
          </li>
        ))}
      </ul>
    );
  };

  // 渲染关联信息
  const renderRelations = () => {
    return (
      <div className="p-3">
        {/* 关联角色 */}
        <div className="mb-3">
          <h4 className="text-xs font-medium text-gray-500 mb-1">关联角色</h4>
          {node.relatedCharacterIds && node.relatedCharacterIds.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {node.relatedCharacterIds.map(id => (
                <span
                  key={id}
                  className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs max-w-full break-words inline-block mb-1"
                  title={relatedItemsMap.characters?.[id] || id}
                >
                  {relatedItemsMap.characters?.[id] || id}
                </span>
              ))}
            </div>
          ) : (
            <div className="text-xs text-gray-500 italic">无关联角色</div>
          )}
        </div>

        {/* 关联世界观 */}
        <div className="mb-3">
          <h4 className="text-xs font-medium text-gray-500 mb-1">关联世界观</h4>
          {node.relatedWorldBuildingIds && node.relatedWorldBuildingIds.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {node.relatedWorldBuildingIds.map(id => (
                <span
                  key={id}
                  className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs max-w-full break-words inline-block mb-1"
                  title={relatedItemsMap.worldBuilding?.[id] || id}
                >
                  {relatedItemsMap.worldBuilding?.[id] || id}
                </span>
              ))}
            </div>
          ) : (
            <div className="text-xs text-gray-500 italic">无关联世界观</div>
          )}
        </div>

        {/* 关联术语 */}
        <div>
          <h4 className="text-xs font-medium text-gray-500 mb-1">关联术语</h4>
          {node.relatedTerminologyIds && node.relatedTerminologyIds.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {node.relatedTerminologyIds.map(id => (
                <span
                  key={id}
                  className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs max-w-full break-words inline-block mb-1"
                  title={relatedItemsMap.terminology?.[id] || id}
                >
                  {relatedItemsMap.terminology?.[id] || id}
                </span>
              ))}
            </div>
          ) : (
            <div className="text-xs text-gray-500 italic">无关联术语</div>
          )}
        </div>
      </div>
    );
  };

  // 使用Portal渲染到容器中
  return createPortal(
    <div
      ref={panelRef}
      className="absolute z-50 bg-white rounded-lg shadow-xl border border-gray-200 w-80 transition-all duration-300 transform"
      style={{
        top: `${top}px`,
        left: `${left}px`,
        opacity: isMounted ? 1 : 0,
        transform: isMounted ? 'translateY(0) scale(1)' : 'translateY(-10px) scale(0.98)',
        borderColor: getNodeTypeColor(),
        borderLeftWidth: '4px',
        boxShadow: `0 4px 20px rgba(0, 0, 0, 0.1), 0 0 0 1px ${getNodeTypeColor()}33`
      }}
    >
      {/* 面板标题 */}
      <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
        <div className="flex items-center">
          <div
            className="w-3 h-3 rounded-full mr-2"
            style={{ background: getNodeTypeColor() }}
          ></div>
          <h3 className="text-sm font-medium text-gray-800 truncate max-w-[180px]" title={node.title}>
            {node.title}
          </h3>
        </div>
        <button
          className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
          onClick={onClose}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 标签页导航 */}
      <div className="flex border-b border-gray-200">
        <button
          className={`flex-1 py-2 text-xs font-medium transition-all duration-200 ${
            activeTab === 'info'
              ? `text-gray-800 border-b-2 border-[${getNodeTypeColor()}]`
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('info')}
          style={{
            borderColor: activeTab === 'info' ? getNodeTypeColor() : 'transparent'
          }}
        >
          基本信息
        </button>
        <button
          className={`flex-1 py-2 text-xs font-medium transition-all duration-200 ${
            activeTab === 'children'
              ? `text-gray-800 border-b-2 border-[${getNodeTypeColor()}]`
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('children')}
          style={{
            borderColor: activeTab === 'children' ? getNodeTypeColor() : 'transparent'
          }}
        >
          子节点 ({node.children?.length || 0})
        </button>
        <button
          className={`flex-1 py-2 text-xs font-medium transition-all duration-200 ${
            activeTab === 'relations'
              ? `text-gray-800 border-b-2 border-[${getNodeTypeColor()}]`
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('relations')}
          style={{
            borderColor: activeTab === 'relations' ? getNodeTypeColor() : 'transparent'
          }}
        >
          关联
        </button>
      </div>

      {/* 内容区域 */}
      <div className="max-h-[300px] overflow-y-auto relative">
        {activeTab === 'info' && (
          <div className="p-4 animate-fadeIn">
            {/* 节点类型 */}
            <div className="mb-3">
              <h4 className="text-xs font-medium text-gray-500 mb-1">类型</h4>
              <div className="inline-block px-2 py-0.5 rounded-full text-xs font-medium text-white"
                style={{ background: getNodeTypeColor() }}
              >
                {node.type === 'volume' ? '总纲/卷' :
                 node.type === 'chapter' ? '章节' :
                 node.type === 'plot' ? '剧情节点' :
                 node.type === 'dialogue' ? '对话设计' :
                 node.type === 'scene' ? '场景' : '对话节点'}
              </div>
            </div>

            {/* 根据节点类型显示专门字段 */}
            {node.type === 'volume' && (
              <>
                {/* 卷主题 */}
                {node.volumeTheme && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">卷主题</h4>
                    <div className="text-sm text-gray-700 bg-purple-50 p-2 rounded-md border border-purple-200">
                      {node.volumeTheme}
                    </div>
                  </div>
                )}

                {/* 卷弧线 */}
                {node.volumeArc && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">卷弧线</h4>
                    <div className="text-sm text-gray-700 bg-purple-50 p-2 rounded-md border border-purple-200">
                      {node.volumeArc}
                    </div>
                  </div>
                )}

                {/* 预期章节数 */}
                {node.chapterCount && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">预期章节数</h4>
                    <div className="inline-block px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full">
                      {node.chapterCount} 章
                    </div>
                  </div>
                )}

                {/* 循环法模板 */}
                {node.cycleTemplate && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">循环法模板</h4>
                    <div className="text-sm text-gray-700 bg-purple-50 p-2 rounded-md border border-purple-200">
                      {node.cycleTemplate}
                    </div>
                  </div>
                )}
              </>
            )}

            {node.type === 'chapter' && (
              <>
                {/* 章节写作风格 */}
                {node.chapterStyle && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">写作风格</h4>
                    <div className="text-sm text-gray-700 bg-blue-50 p-2 rounded-md border border-blue-200">
                      {node.chapterStyle}
                    </div>
                  </div>
                )}

                {/* 章节写作手法 */}
                {node.chapterTechniques && node.chapterTechniques.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">写作手法</h4>
                    <div className="flex flex-wrap gap-1">
                      {node.chapterTechniques.map((technique, index) => (
                        <span key={index} className="inline-block px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {technique}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 章节目标 */}
                {node.chapterGoals && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">章节目标</h4>
                    <div className="text-sm text-gray-700 bg-blue-50 p-2 rounded-md border border-blue-200">
                      {node.chapterGoals}
                    </div>
                  </div>
                )}
              </>
            )}

            {node.type === 'plot' && (
              <>
                {/* 剧情类型 */}
                {node.plotType && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">剧情类型</h4>
                    <div className="inline-block px-2 py-0.5 bg-orange-100 text-orange-800 text-xs rounded-full">
                      {node.plotType === 'conflict' ? '冲突' :
                       node.plotType === 'twist' ? '转折' :
                       node.plotType === 'climax' ? '高潮' :
                       node.plotType === 'resolution' ? '解决' : node.plotType}
                    </div>
                  </div>
                )}

                {/* 剧情点 */}
                {node.plotPoints && node.plotPoints.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">剧情点</h4>
                    <div className="text-sm text-gray-700 bg-orange-50 p-2 rounded-md border border-orange-200 max-h-[120px] overflow-y-auto">
                      <div className="space-y-2">
                        {node.plotPoints.map((point, index) => (
                          <div key={point.id} className="border-b border-orange-200 pb-2 last:border-b-0">
                            <div className="flex items-start space-x-2">
                              <span className="flex-shrink-0 w-4 h-4 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                                {index + 1}
                              </span>
                              <div className="flex-1">
                                <div className="text-sm text-gray-800 mb-1">{point.content}</div>
                                {point.writingGuidance && (
                                  <div className="text-xs text-gray-600 bg-yellow-50 border border-yellow-200 rounded px-2 py-1">
                                    <span className="font-medium text-yellow-700">写作指导：</span>
                                    {point.writingGuidance}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 关联角色 */}
                {node.relatedCharacters && node.relatedCharacters.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">关联角色</h4>
                    <div className="flex flex-wrap gap-1">
                      {node.relatedCharacters.map((character, index) => (
                        <span key={index} className="inline-block px-2 py-0.5 bg-orange-100 text-orange-800 text-xs rounded-full">
                          {character}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {node.type === 'dialogue' && (
              <>
                {/* 对话场景 */}
                {node.dialogueScene && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">对话场景</h4>
                    <div className="text-sm text-gray-700 bg-green-50 p-2 rounded-md border border-green-200">
                      {node.dialogueScene}
                    </div>
                  </div>
                )}

                {/* 参与角色 */}
                {node.participants && node.participants.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">参与角色</h4>
                    <div className="flex flex-wrap gap-1">
                      {node.participants.map((participant, index) => {
                        // 安全处理participant，确保它是可渲染的字符串
                        const displayParticipant = typeof participant === 'string'
                          ? participant
                          : typeof participant === 'object' && participant !== null
                            ? (participant as any).name || (participant as any).title || JSON.stringify(participant)
                            : String(participant);

                        return (
                          <span key={index} className="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                            {displayParticipant}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* 对话目的 */}
                {node.dialoguePurpose && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">对话目的</h4>
                    <div className="text-sm text-gray-700 bg-green-50 p-2 rounded-md border border-green-200">
                      {node.dialoguePurpose}
                    </div>
                  </div>
                )}

                {/* 对话内容预览 */}
                {node.dialogueContent && node.dialogueContent.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-500 mb-1">对话内容</h4>
                    <div className="text-sm text-gray-700 bg-green-50 p-2 rounded-md border border-green-200 max-h-[120px] overflow-y-auto">
                      {node.dialogueContent.slice(0, 3).map((item: any, index: number) => {
                        // 🔥 安全处理对话项，防止React渲染错误
                        const safeItem = {
                          id: item?.id || `dialogue-${index}`,
                          speaker: typeof item?.speaker === 'string'
                            ? item.speaker
                            : typeof item?.speaker === 'object' && item.speaker !== null
                              ? (item.speaker as any).name || (item.speaker as any).title || JSON.stringify(item.speaker)
                              : typeof item?.name === 'string'
                                ? item.name  // 支持错误字段名映射
                                : String(item?.speaker || item?.name || '未知角色'),
                          content: typeof item?.content === 'string'
                            ? item.content
                            : typeof item?.content === 'object' && item.content !== null
                              ? JSON.stringify(item.content)
                              : String(item?.content || '')
                        };

                        return (
                          <div key={safeItem.id} className="mb-1">
                            <strong className="text-green-700">{safeItem.speaker}:</strong> {safeItem.content}
                          </div>
                        );
                      })}
                      {node.dialogueContent.length > 3 && (
                        <div className="text-xs text-gray-500 italic">...还有{node.dialogueContent.length - 3}条对话</div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* 节点描述 */}
            <div className="mb-3">
              <h4 className="text-xs font-medium text-gray-500 mb-1">描述</h4>
              <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded-md border border-gray-100 max-h-[120px] overflow-y-auto">
                {node.description ? (
                  // 使用whitespace-pre-wrap保留空行和换行
                  <div className="whitespace-pre-wrap">{node.description}</div>
                ) : (
                  <span className="text-gray-400 italic">无描述</span>
                )}
              </div>
            </div>

            {/* 创作建议 */}
            {node.creativeNotes && (
              <div className="mb-3">
                <h4 className="text-xs font-medium text-gray-500 mb-1 flex items-center gap-1">
                  <span>💡</span>
                  <span>创作建议</span>
                </h4>
                <div className="text-sm text-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 p-2 rounded-md border border-blue-200 max-h-[120px] overflow-y-auto">
                  <div className="whitespace-pre-wrap">{node.creativeNotes}</div>
                </div>
              </div>
            )}

            {/* 节点ID */}
            <div className="mb-3">
              <h4 className="text-xs font-medium text-gray-500 mb-1">节点ID</h4>
              <div className="text-xs text-gray-500 font-mono bg-gray-50 p-1.5 rounded overflow-auto">
                {node.id}
              </div>
            </div>

            {/* 节点位置信息 */}
            {node.position && (
              <div>
                <h4 className="text-xs font-medium text-gray-500 mb-1">位置信息</h4>
                <div className="text-xs text-gray-500 font-mono">
                  X: {Math.round(node.position.x)}, Y: {Math.round(node.position.y)}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'children' && (
          <div className="animate-fadeIn">
            {renderChildrenList()}
          </div>
        )}

        {activeTab === 'relations' && (
          <div className="animate-fadeIn">
            {renderRelations()}
          </div>
        )}
      </div>
    </div>,
    containerRef.current || document.body
  );
};

export default InlineNodeDetailsPanel;
