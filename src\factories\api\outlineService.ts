import { Outline, OutlineVersion, OutlineNodeType } from '../ui/types/outline';
import { db } from '@/lib/db/dexie';
import { Character } from '@/lib/db/dexie';
import { WorldBuilding } from '@/lib/db/dexie';
import { Terminology } from '@/lib/db/dexie';

/**
 * 大纲服务
 * 提供大纲相关的API操作
 */
export const outlineService = {
  /**
   * 获取指定作品的大纲
   * @param workId 作品ID
   * @returns 大纲数据
   */
  getOutline: async (workId: string): Promise<Outline | null> => {
    try {
      console.log('获取大纲:', workId);

      // 从IndexedDB获取大纲数据
      const outline = await db.outlines.where('workId').equals(workId).first();

      if (outline) {
        console.log('找到大纲:', outline.id);

        // 确保nodes字段存在
        if (!outline.nodes) {
          console.log('初始化空nodes数组');
          outline.nodes = [];
        }

        // 数据迁移：处理旧的节点类型
        outline.nodes = outlineService.migrateNodeTypes(outline.nodes);
      } else {
        console.log('未找到大纲');
      }

      return outline || null;
    } catch (error) {
      console.error('获取大纲失败:', error);
      throw error;
    }
  },

  /**
   * 清理节点数据中的函数，确保可以被序列化
   * @param nodes 节点数组
   * @returns 清理后的节点数组
   */
  cleanNodesForSerialization: (nodes: OutlineNodeType[]): OutlineNodeType[] => {
    return nodes.map(node => {
      // 创建节点副本
      const cleanedNode = { ...node };

      // 删除所有函数属性
      Object.keys(cleanedNode).forEach(key => {
        const value = (cleanedNode as any)[key];
        if (typeof value === 'function') {
          console.log(`🧹 清理节点 ${node.id} 中的函数属性: ${key}`);
          delete (cleanedNode as any)[key];
        }
      });

      // 递归清理子节点
      if (cleanedNode.children && cleanedNode.children.length > 0) {
        cleanedNode.children = outlineService.cleanNodesForSerialization(cleanedNode.children);
      }

      return cleanedNode;
    });
  },

  /**
   * 保存大纲
   * @param workId 作品ID
   * @param outline 大纲数据
   * @returns 保存后的大纲数据
   */
  saveOutline: async (workId: string, outline: Outline): Promise<Outline> => {
    try {
      console.log('开始保存大纲:', workId, outline);
      const now = new Date();

      // 创建一个新对象，避免直接修改传入的对象
      const outlineToSave = { ...outline };

      // 如果是新大纲，设置ID和创建时间
      if (!outlineToSave.id) {
        console.log('创建新大纲');
        outlineToSave.id = `outline-${Date.now()}`;
        outlineToSave.workId = workId;
        outlineToSave.version = 1;
        outlineToSave.lastModified = now;
        console.log('新大纲ID:', outlineToSave.id);
      } else {
        console.log('更新现有大纲:', outlineToSave.id);
        // 更新版本号和修改时间
        outlineToSave.version = outlineToSave.version ? outlineToSave.version + 1 : 1;
        outlineToSave.lastModified = now;
      }

      // 确保workId正确
      if (!outlineToSave.workId) {
        console.log('设置workId:', workId);
        outlineToSave.workId = workId;
      }

      // 确保nodes字段存在
      if (!outlineToSave.nodes) {
        console.log('初始化空nodes数组');
        outlineToSave.nodes = [];
      }

      // 🔥 修复：清理节点数据中的函数，确保可以被序列化
      console.log('🧹 清理节点数据中的函数...');
      outlineToSave.nodes = outlineService.cleanNodesForSerialization(outlineToSave.nodes);

      console.log('准备保存大纲数据:', outlineToSave);

      // 使用事务确保数据一致性
      await db.transaction('rw', db.outlines, db.outlineVersions, async () => {
        // 保存大纲数据
        console.log('保存大纲到数据库');
        await db.outlines.put(outlineToSave);
        console.log('大纲数据保存成功');

        // 创建版本历史
        const version: OutlineVersion = {
          id: `outline-version-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          outlineId: outlineToSave.id,
          version: outlineToSave.version,
          data: {
            ...outlineToSave,
            // 确保版本历史中的节点数据也是清理过的
            nodes: outlineService.cleanNodesForSerialization(outlineToSave.nodes)
          },
          createdAt: now,
          createdBy: 'user', // 可以根据实际情况设置创建者
        };

        console.log('保存版本历史:', version);
        await db.outlineVersions.put(version);
        console.log('版本历史保存成功');
      });

      console.log('大纲保存完成:', outlineToSave);
      return outlineToSave;
    } catch (error) {
      console.error('保存大纲失败:', error);
      throw error;
    }
  },

  /**
   * 获取大纲版本历史
   * @param workId 作品ID
   * @returns 版本历史列表
   */
  getOutlineVersions: async (workId: string): Promise<OutlineVersion[]> => {
    try {
      // 先获取大纲
      const outline = await outlineService.getOutline(workId);
      if (!outline) {
        return [];
      }

      // 获取版本历史
      const versions = await db.outlineVersions
        .where('outlineId')
        .equals(outline.id)
        .reverse()
        .sortBy('version');

      return versions;
    } catch (error) {
      console.error('获取大纲版本历史失败:', error);
      throw error;
    }
  },

  /**
   * 获取指定版本的大纲
   * @param workId 作品ID
   * @param versionId 版本ID
   * @returns 指定版本的大纲
   */
  getOutlineVersion: async (workId: string, versionId: string): Promise<OutlineVersion | null> => {
    try {
      // 获取指定版本
      const version = await db.outlineVersions.get(versionId);
      return version || null;
    } catch (error) {
      console.error('获取大纲版本失败:', error);
      throw error;
    }
  },

  /**
   * 恢复到指定版本的大纲
   * @param workId 作品ID
   * @param versionId 版本ID
   * @returns 恢复后的大纲
   */
  restoreOutlineVersion: async (workId: string, versionId: string): Promise<Outline> => {
    try {
      // 获取指定版本
      const version = await db.outlineVersions.get(versionId);
      if (!version) {
        throw new Error('版本不存在');
      }

      // 恢复大纲数据
      const restoredOutline: Outline = {
        ...version.data,
        version: version.data.version + 1,
        lastModified: new Date(),
        // 确保恢复的数据也是清理过的
        nodes: outlineService.cleanNodesForSerialization(version.data.nodes || [])
      };

      // 保存恢复后的大纲
      await db.outlines.put(restoredOutline);

      // 创建新的版本历史
      const newVersion: OutlineVersion = {
        id: `outline-version-${Date.now()}`,
        outlineId: restoredOutline.id,
        version: restoredOutline.version,
        data: {
          ...restoredOutline,
          // 确保新版本历史中的节点数据也是清理过的
          nodes: outlineService.cleanNodesForSerialization(restoredOutline.nodes || [])
        },
        createdAt: new Date(),
        createdBy: 'user',
        comment: `从版本 ${version.version} 恢复`
      };

      // 保存新的版本历史
      await db.outlineVersions.put(newVersion);

      return restoredOutline;
    } catch (error) {
      console.error('恢复大纲版本失败:', error);
      throw error;
    }
  },

  /**
   * 添加节点关联
   * @param workId 作品ID
   * @param nodeId 节点ID
   * @param relationType 关联类型
   * @param relationId 关联ID
   * @returns 更新后的大纲
   */
  addNodeRelation: async (
    workId: string,
    nodeId: string,
    relationType: 'character' | 'worldBuilding' | 'terminology',
    relationId: string
  ): Promise<Outline | null> => {
    try {
      // 获取大纲
      const outline = await outlineService.getOutline(workId);
      if (!outline) {
        throw new Error('大纲不存在');
      }

      // 更新节点
      const updateNodeInTree = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
        return nodes.map(node => {
          if (node.id === nodeId) {
            // 根据关联类型更新关联ID数组
            switch (relationType) {
              case 'character':
                const characterIds = node.relatedCharacterIds || [];
                if (!characterIds.includes(relationId)) {
                  return {
                    ...node,
                    relatedCharacterIds: [...characterIds, relationId]
                  };
                }
                break;
              case 'worldBuilding':
                const worldBuildingIds = node.relatedWorldBuildingIds || [];
                if (!worldBuildingIds.includes(relationId)) {
                  return {
                    ...node,
                    relatedWorldBuildingIds: [...worldBuildingIds, relationId]
                  };
                }
                break;
              case 'terminology':
                const terminologyIds = node.relatedTerminologyIds || [];
                if (!terminologyIds.includes(relationId)) {
                  return {
                    ...node,
                    relatedTerminologyIds: [...terminologyIds, relationId]
                  };
                }
                break;
            }
            return node;
          }

          if (node.children && node.children.length > 0) {
            return {
              ...node,
              children: updateNodeInTree(node.children)
            };
          }

          return node;
        });
      };

      const updatedNodes = updateNodeInTree(outline.nodes);
      const updatedOutline = {
        ...outline,
        nodes: updatedNodes
      };

      // 保存更新后的大纲
      return await outlineService.saveOutline(workId, updatedOutline);
    } catch (error) {
      console.error('添加节点关联失败:', error);
      throw error;
    }
  },

  /**
   * 移除节点关联
   * @param workId 作品ID
   * @param nodeId 节点ID
   * @param relationType 关联类型
   * @param relationId 关联ID
   * @returns 更新后的大纲
   */
  removeNodeRelation: async (
    workId: string,
    nodeId: string,
    relationType: 'character' | 'worldBuilding' | 'terminology',
    relationId: string
  ): Promise<Outline | null> => {
    try {
      // 获取大纲
      const outline = await outlineService.getOutline(workId);
      if (!outline) {
        throw new Error('大纲不存在');
      }

      // 更新节点
      const updateNodeInTree = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
        return nodes.map(node => {
          if (node.id === nodeId) {
            // 根据关联类型更新关联ID数组
            switch (relationType) {
              case 'character':
                const characterIds = node.relatedCharacterIds || [];
                return {
                  ...node,
                  relatedCharacterIds: characterIds.filter(id => id !== relationId)
                };
              case 'worldBuilding':
                const worldBuildingIds = node.relatedWorldBuildingIds || [];
                return {
                  ...node,
                  relatedWorldBuildingIds: worldBuildingIds.filter(id => id !== relationId)
                };
              case 'terminology':
                const terminologyIds = node.relatedTerminologyIds || [];
                return {
                  ...node,
                  relatedTerminologyIds: terminologyIds.filter(id => id !== relationId)
                };
            }
          }

          if (node.children && node.children.length > 0) {
            return {
              ...node,
              children: updateNodeInTree(node.children)
            };
          }

          return node;
        });
      };

      const updatedNodes = updateNodeInTree(outline.nodes);
      const updatedOutline = {
        ...outline,
        nodes: updatedNodes
      };

      // 保存更新后的大纲
      return await outlineService.saveOutline(workId, updatedOutline);
    } catch (error) {
      console.error('移除节点关联失败:', error);
      throw error;
    }
  },

  /**
   * 获取节点关联元素
   * @param workId 作品ID
   * @param nodeId 节点ID
   * @returns 关联元素
   */
  getNodeRelations: async (
    workId: string,
    nodeId: string
  ): Promise<{
    characters: Character[];
    worldBuildings: WorldBuilding[];
    terminologies: Terminology[];
  }> => {
    try {
      // 获取大纲
      const outline = await outlineService.getOutline(workId);
      if (!outline) {
        throw new Error('大纲不存在');
      }

      // 查找节点
      const findNode = (nodes: OutlineNodeType[]): OutlineNodeType | null => {
        for (const node of nodes) {
          if (node.id === nodeId) {
            return node;
          }

          if (node.children && node.children.length > 0) {
            const foundNode = findNode(node.children);
            if (foundNode) {
              return foundNode;
            }
          }
        }

        return null;
      };

      const node = findNode(outline.nodes);
      if (!node) {
        throw new Error('节点不存在');
      }

      // 获取关联元素
      const characterIds = node.relatedCharacterIds || [];
      const worldBuildingIds = node.relatedWorldBuildingIds || [];
      const terminologyIds = node.relatedTerminologyIds || [];

      const [characters, worldBuildings, terminologies] = await Promise.all([
        characterIds.length > 0 ? db.characters.where('id').anyOf(characterIds).toArray() : [],
        worldBuildingIds.length > 0 ? db.worldBuilding.where('id').anyOf(worldBuildingIds).toArray() : [],
        terminologyIds.length > 0 ? db.terminology.where('id').anyOf(terminologyIds).toArray() : []
      ]);

      return {
        characters,
        worldBuildings,
        terminologies
      };
    } catch (error) {
      console.error('获取节点关联元素失败:', error);
      throw error;
    }
  },

  /**
   * 迁移节点类型
   * 将旧的节点类型（character, plot等）转换为支持的类型
   * @param nodes 节点数组
   * @returns 迁移后的节点数组
   */
  migrateNodeTypes: (nodes: OutlineNodeType[]): OutlineNodeType[] => {
    return nodes.map(node => {
      // 创建节点副本
      const migratedNode = { ...node };

      // 处理旧的节点类型迁移
      if (node.type === 'scene') {
        console.warn(`迁移节点类型: ${node.type} -> plot (节点: ${node.title})`);
        migratedNode.type = 'plot';
      } else if (node.type === 'note') {
        console.warn(`迁移节点类型: ${node.type} -> dialogue (节点: ${node.title})`);
        migratedNode.type = 'dialogue';
      } else if (node.type === 'character' || node.type === 'plotPoint') {
        console.warn(`迁移节点类型: ${node.type} -> plot (节点: ${node.title})`);
        migratedNode.type = 'plot';
      } else if (!['volume', 'event', 'phaseGroup', 'chapter', 'plot', 'dialogue', 'synopsis'].includes(node.type)) {
        // 处理未知类型，默认迁移为plot
        console.warn(`迁移未知节点类型: ${node.type} -> plot (节点: ${node.title})`);
        migratedNode.type = 'plot';

        // 在描述中添加原始类型信息
        const originalTypeLabel = node.type === 'character' ? '角色' :
                                 node.type === 'plotPoint' ? '情节点' : node.type;

        if (migratedNode.description) {
          migratedNode.description = `[原${originalTypeLabel}节点] ${migratedNode.description}`;
        } else {
          migratedNode.description = `[原${originalTypeLabel}节点]`;
        }
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        migratedNode.children = outlineService.migrateNodeTypes(node.children);
      }

      return migratedNode;
    });
  }
};
