"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { MapIcon } from '@/components/icons';

interface WorldBuildingListProps {
  worldBuildings: WorldBuilding[];
  isLoading: boolean;
  searchQuery: string;
  selectedWorldBuilding: WorldBuilding | null;
  onSelectWorldBuilding: (worldBuilding: WorldBuilding) => void;
  onCreateWorldBuilding: () => void;
  onDeleteWorldBuilding: (worldBuildingId: string) => void;
  multiSelectMode?: boolean;
  selectedWorldBuildingIds?: string[];
  onMultiSelectChange?: (worldBuildingIds: string[]) => void;
}

// 扩展WorldBuilding接口，添加isCategory属性
interface WorldBuildingWithCategory extends WorldBuilding {
  isCategory?: boolean;
}

// 定义树节点接口
interface TreeNode {
  worldBuilding: WorldBuildingWithCategory;
  children: TreeNode[];
  isExpanded?: boolean;
}

/**
 * 世界观列表组件
 */
export const WorldBuildingList: React.FC<WorldBuildingListProps> = ({
  worldBuildings,
  isLoading,
  searchQuery,
  selectedWorldBuilding,
  onSelectWorldBuilding,
  onCreateWorldBuilding,
  onDeleteWorldBuilding,
  multiSelectMode = false,
  selectedWorldBuildingIds = [],
  onMultiSelectChange = () => {}
}) => {
  // 树状结构状态
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>({});

  // 创建按钮组件
  const renderCreateButton = () => (
    <button
      className="px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm font-medium transition-colors"
      onClick={onCreateWorldBuilding}
    >
      创建世界观
    </button>
  );

  // 构建树状结构
  useEffect(() => {
    // 按类别分组
    const groupedByCategory: Record<string, WorldBuilding[]> = {};

    worldBuildings.forEach(wb => {
      // 使用 getCategoryLabel 函数获取中文类别名称
      const categoryId = wb.category || 'other';
      let category = categoryId;

      // 从英文ID映射到中文名称
      const categoryMap: Record<string, string> = {
        'geography': '地理',
        'history': '历史',
        'culture': '文化',
        'race': '种族',
        'magic': '魔法系统',
        'organization': '组织',
        'item': '物品',
        'religion': '宗教',
        'politics': '政治',
        'natural_phenomena': '自然现象',
        'architecture': '建筑',
        'technology': '科技',
        'economy': '经济',
        'language': '语言',
        'art': '艺术',
        'other': '未分类'
      };

      if (categoryMap[categoryId]) {
        category = categoryMap[categoryId];
      }

      if (!groupedByCategory[category]) {
        groupedByCategory[category] = [];
      }
      groupedByCategory[category].push(wb);
    });

    // 转换为树状结构
    const tree: TreeNode[] = Object.entries(groupedByCategory).map(([category, items]) => {
      // 创建一个虚拟的根节点表示类别
      const rootWorldBuilding: WorldBuildingWithCategory = {
        id: `category-${category}`,
        name: category,
        bookId: items[0]?.bookId || '',
        category: '',
        description: `${category}类别的所有世界观元素`,
        createdAt: new Date(),
        updatedAt: new Date(),
        extractedFromChapterIds: [],
        relatedCharacterIds: [],
        relatedTerminologyIds: [],
        relatedWorldBuildingIds: [],
        isCategory: true // 标记为类别节点
      };

      return {
        worldBuilding: rootWorldBuilding,
        children: items.map(wb => ({ worldBuilding: wb, children: [] })),
        isExpanded: expandedNodes[category] !== false // 默认展开
      };
    });

    setTreeData(tree);
  }, [worldBuildings, expandedNodes]);

  // 切换节点展开/折叠状态
  const toggleNodeExpanded = (nodeId: string) => {
    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };

  // 处理多选变化
  const handleMultiSelectChange = (worldBuildingId: string) => {
    if (!worldBuildingId) return;

    const newSelectedIds = [...selectedWorldBuildingIds];
    const index = newSelectedIds.indexOf(worldBuildingId);

    if (index === -1) {
      // 添加到选中列表
      newSelectedIds.push(worldBuildingId);
    } else {
      // 从选中列表中移除
      newSelectedIds.splice(index, 1);
    }

    onMultiSelectChange(newSelectedIds);
  };

  // 渲染世界观树节点
  const renderTreeNode = (node: TreeNode, level: number = 0) => {
    const { worldBuilding, children, isExpanded = true } = node;
    const isSelected = selectedWorldBuilding?.id === worldBuilding.id;
    const isMultiSelected = worldBuilding.id && selectedWorldBuildingIds.includes(worldBuilding.id);
    const isCategory = worldBuilding.isCategory;
    const hasChildren = children && children.length > 0;

    // 处理点击事件
    const handleClick = () => {
      if (isCategory) {
        toggleNodeExpanded(worldBuilding.name);
      } else if (multiSelectMode) {
        if (worldBuilding.id) {
          handleMultiSelectChange(worldBuilding.id);
        }
      } else {
        onSelectWorldBuilding(worldBuilding);
      }
    };

    return (
      <div key={worldBuilding.id || `node-${Date.now()}`}>
        <div
          className={`panel-list-item p-3 cursor-pointer transition-all duration-200 ${
            isSelected
              ? 'bg-blue-50'
              : isMultiSelected
                ? 'bg-purple-50'
                : 'hover:bg-gray-50'
          } ${isCategory ? 'mb-1' : 'mb-0.5'}`}
          onClick={handleClick}
          style={{
            paddingLeft: `${level * 12 + 16}px`,
            borderRadius: isCategory ? '8px' : '6px',
            margin: isCategory ? '8px 8px 4px 8px' : '0px 8px 4px 16px',
            boxShadow: isSelected
              ? '0 2px 4px rgba(0, 0, 0, 0.1), inset 0 0 0 2px rgba(59, 130, 246, 0.5)'
              : isMultiSelected
                ? '0 2px 4px rgba(0, 0, 0, 0.1), inset 0 0 0 2px rgba(147, 51, 234, 0.5)'
                : isCategory
                  ? '0 2px 4px rgba(0, 0, 0, 0.05)'
                  : '0 1px 2px rgba(0, 0, 0, 0.05)',
            backgroundColor: isCategory
              ? isExpanded ? 'var(--color-primary-bg, #f0f7ff)' : 'var(--color-secondary-bg, #f5f5f5)'
              : isSelected
                ? 'var(--color-primary-bg, #f0f7ff)'
                : isMultiSelected
                  ? 'var(--color-purple-bg, #f5f3ff)'
                  : 'white'
          }}
        >
          <div className="flex justify-between items-start">
            <div className="flex items-start gap-3 flex-1">
              {!isCategory && (
                <MapIcon
                  size="sm"
                  animated={true}
                  className="mt-0.5 text-amber-700 flex-shrink-0"
                />
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-medium flex items-center">
                  {hasChildren && (
                    <span
                      className="mr-2 transform transition-transform duration-200 flex items-center justify-center"
                      style={{
                        transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                        color: isCategory ? 'var(--color-primary, #3b82f6)' : 'var(--color-secondary, #6b7280)',
                        width: '20px',
                        height: '20px'
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  )}

                  {isCategory ? (
                    <span
                      className="font-semibold"
                      style={{
                        color: 'var(--color-primary, #3b82f6)',
                        fontSize: '0.95rem'
                      }}
                    >
                      {worldBuilding.name}
                    </span>
                  ) : (
                    <span className="text-gray-800">
                      {worldBuilding.name}
                      {worldBuilding.attributes?.importance && (
                        <span className="ml-2 text-yellow-500 text-xs">
                          {'⭐'.repeat(parseInt(worldBuilding.attributes.importance))}
                        </span>
                      )}
                    </span>
                  )}
                </h3>

                {!isCategory && (
                  <div className="flex items-center mt-1">
                    {worldBuilding.extractedFromChapterIds && worldBuilding.extractedFromChapterIds.length > 0 && (
                      <span
                        className="inline-block px-2 py-0.5 text-xs rounded-full"
                        style={{
                          backgroundColor: 'var(--color-primary-bg, #f0f7ff)',
                          color: 'var(--color-primary, #3b82f6)'
                        }}
                      >
                        {worldBuilding.extractedFromChapterIds.length} 章节
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {!isCategory && worldBuilding.id && (
              <button
                className="text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteWorldBuilding(worldBuilding.id!);
                }}
                style={{
                  opacity: 0.7,
                  transform: 'scale(0.9)'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            )}
          </div>

          {!isCategory && worldBuilding.description && (
            <p className="text-sm text-gray-600 mt-2 ml-6 line-clamp-2">{worldBuilding.description}</p>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {children.map(childNode => renderTreeNode(childNode, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col" style={{ backgroundColor: 'var(--color-bg-light, #f9fafb)' }}>
      {/* 列表头部 */}
      <div className="p-4 flex justify-between items-center" style={{
        borderBottom: '1px solid var(--color-border, #e5e7eb)',
        backgroundColor: 'var(--color-white, white)'
      }}>
        <h2 className="font-semibold text-gray-800" style={{ fontSize: '1.05rem' }}>世界观元素</h2>
        <div>{renderCreateButton()}</div>
      </div>

      {/* 列表内容 */}
      <div className="flex-1 overflow-y-auto pt-2">
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--color-primary, #3b82f6)' }}></div>
          </div>
        ) : worldBuildings.length === 0 ? (
          <div className="flex flex-col justify-center items-center h-full p-4 text-center">
            {searchQuery ? (
              <>
                <p className="text-gray-500 mb-2">没有找到匹配的世界观元素</p>
                <p className="text-sm text-gray-400">尝试使用不同的搜索词</p>
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--color-secondary, #6b7280)' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-gray-500 mb-2">还没有创建世界观元素</p>
                <p className="text-sm text-gray-400 mb-4">点击"创建世界观"按钮开始构建你的世界</p>
                <div>{renderCreateButton()}</div>
              </>
            )}
          </div>
        ) : (
          <div className="pb-4">
            {treeData.map(node => renderTreeNode(node))}
          </div>
        )}
      </div>
    </div>
  );
};
