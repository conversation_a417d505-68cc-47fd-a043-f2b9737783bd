import { IAIComponent, AIResponse, AIRequestOptions } from './IAIComponent';

/**
 * AI发送组件接口
 * 用于处理AI请求的发送和响应
 */
export interface IAISenderComponent extends IAIComponent {
  /**
   * 发送AI请求
   * @param prompt 提示词
   * @param options 请求选项
   * @returns 响应结果
   */
  sendRequest(prompt: string, options?: AIRequestOptions): Promise<AIResponse>;

  /**
   * 发送流式AI请求
   * @param prompt 提示词
   * @param onChunk 接收数据块的回调函数
   * @param options 请求选项
   * @returns 完整响应结果
   */
  sendStreamingRequest(
    prompt: string,
    onChunk: (chunk: string) => void,
    options?: AIRequestOptions
  ): Promise<AIResponse>;

  /**
   * 取消当前请求
   */
  cancelRequest(): void;

  /**
   * 获取当前请求状态
   * @returns 请求状态
   */
  getRequestStatus(): AIRequestStatus;

  /**
   * 设置请求状态变更回调
   * @param callback 回调函数
   */
  onStatusChange(callback: (status: AIRequestStatus) => void): void;
}

// 使用 IAIComponent 中的 AIRequestOptions 接口

// 使用 IAIComponent 中的 AIResponse 接口

/**
 * AI消息
 */
export interface AIMessage {
  // 角色：system, user, assistant
  role: 'system' | 'user' | 'assistant';

  // 内容
  content: string;
}

/**
 * AI请求状态
 */
export type AIRequestStatus = 'idle' | 'loading' | 'streaming' | 'success' | 'error' | 'cancelled';
