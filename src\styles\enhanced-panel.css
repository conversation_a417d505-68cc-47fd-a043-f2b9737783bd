/**
 * 增强版面板样式
 * 文学创作主题的装饰元素和微交互动画
 */

/* 面板遮罩层 */
.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* 面板容器 */
.panel-container {
  position: relative;
  background: var(--color-white, #FFFFFF);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 90vw;
  max-height: 90vh;
}

/* 增强版面板动画 */
@keyframes panelEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.02) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1.0) translateY(0);
  }
}

/* 全局面板动画类 */
.panel-enhanced {
  animation: panelEnter 400ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 文学主题面板背景纹理 */
.panel-container.panel-literary {
  background-image:
    radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.02) 1px, transparent 1px),
    radial-gradient(circle at 80% 70%, rgba(139, 69, 19, 0.02) 1px, transparent 1px);
  background-size: 40px 40px, 60px 60px;
}

/* 四角装饰花纹 */
.panel-corner-decoration {
  position: absolute;
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23D2B48C" stroke-width="1" opacity="0.6"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/><path d="M8 12l2 2 4-4"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.6;
  pointer-events: none;
}

.panel-corner-decoration.top-left {
  top: 12px;
  left: 12px;
}

.panel-corner-decoration.top-right {
  top: 12px;
  right: 12px;
  transform: rotate(90deg);
}

.panel-corner-decoration.bottom-left {
  bottom: 12px;
  left: 12px;
  transform: rotate(-90deg);
}

.panel-corner-decoration.bottom-right {
  bottom: 12px;
  right: 12px;
  transform: rotate(180deg);
}

/* 面板头部 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-secondary, #D2B48C);
  background: linear-gradient(to bottom, var(--color-primary-bg, #F5F2E9), transparent);
}

.panel-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-title-icon {
  color: var(--color-primary, #8B4513);
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary, #8B4513);
  margin: 0;
}

.panel-close-button {
  background: none;
  border: none;
  color: var(--color-text-secondary, #666666);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 200ms ease-out;
}

.panel-close-button:hover {
  background: rgba(139, 69, 19, 0.1);
  color: var(--color-primary, #8B4513);
  transform: scale(1.1);
}

/* 装饰分割线 */
.panel-divider-decorative {
  position: relative;
  height: 1px;
  background: transparent;
  margin: 0 24px;
}

.panel-divider-decorative::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 16" fill="none"><path d="M10 8c20-4 40 4 60-2s40 6 60 2 40-6 60-2" stroke="%238B4513" stroke-width="1" opacity="0.4"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.4;
}

/* 面板内容区域 */
.panel-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  position: relative;
}

/* 内容区域水印效果 */
.panel-content.panel-literary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" fill="none" stroke="%23FFFFFF" stroke-width="1" opacity="0.03"><path d="M160 40L40 160M40 40l120 120M100 20v160M20 100h160"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.03;
  pointer-events: none;
  z-index: 0;
}

/* 面板底部 */
.panel-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid var(--color-secondary, #D2B48C);
  background: linear-gradient(to top, var(--color-primary-bg, #F5F2E9), transparent);
}

/* 列表项增强样式 */
.panel-list-item {
  transition: all 200ms ease-out;
  border-left: 3px solid transparent;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 4px 0;
}

.panel-list-item:hover {
  background: rgba(139, 69, 19, 0.05);
  transform: translateX(4px);
  border-left-color: var(--color-primary, #8B4513);
  box-shadow:
    inset 0 0 0 1px rgba(139, 69, 19, 0.1),
    0 2px 8px rgba(139, 69, 19, 0.1);
}

/* 搜索框增强样式 */
.panel-search-input {
  transition: all 250ms ease-out;
  border: 1px solid var(--color-secondary, #D2B48C);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  background: var(--color-white, #FFFFFF);
}

.panel-search-input:focus {
  outline: none;
  border-color: var(--color-primary, #8B4513);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.2);
  transform: scaleX(1.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-container {
    margin: 10px;
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 20px);
  }

  .panel-header,
  .panel-content,
  .panel-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .panel-corner-decoration {
    display: none;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .panel-container,
  .panel-list-item,
  .panel-search-input,
  .panel-close-button {
    animation: none !important;
    transition: opacity 200ms ease-out;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .panel-container {
    border: 2px solid var(--color-primary, #8B4513);
  }

  .panel-header,
  .panel-footer {
    border-color: var(--color-primary, #8B4513);
    border-width: 2px;
  }
}
