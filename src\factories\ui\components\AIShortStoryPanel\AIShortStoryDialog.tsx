"use client";

import React, { useState, useEffect } from 'react';
import { ShortStoryAIService } from '@/factories/ai/services/ShortStoryAIService';
import { ShortStoryLeftPanel } from './ShortStoryLeftPanel';
import { ShortStoryControls } from './ShortStoryControls';
import { StructurePreviewPanel } from './StructurePreviewPanel';
import { PhaseStrategyPanel } from './PhaseStrategyPanel';
import { ShortStoryTabbedView } from './ShortStoryTabbedView';
import { CoreMysteryPreviewPanel } from './CoreMysteryPreviewPanel';
import {
  ShortStoryParams,
  CoreMystery,
  SegmentStructure,
  ShortStoryResult,
  ShortStoryMode
} from '@/factories/ai/services/types/ShortStoryTypes';

interface AIShortStoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onShortStoryGenerated: (content: string) => void;
  bookId: string;
}

/**
 * AI短篇创作对话框
 * 提供完整的短篇创作界面和功能
 */
export const AIShortStoryDialog: React.FC<AIShortStoryDialogProps> = ({
  isOpen,
  onClose,
  onShortStoryGenerated,
  bookId
}) => {
  // 基础状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 创作参数
  const [userInput, setUserInput] = useState('');
  const [storyMode, setStoryMode] = useState<ShortStoryMode>('mystery');
  const [targetSegments, setTargetSegments] = useState(20); // 改为默认20段
  const [customPhilosophy, setCustomPhilosophy] = useState('');
  const [selectedACEFrameworkIds, setSelectedACEFrameworkIds] = useState<string[]>([]);
  
  // 关联元素
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>([]);
  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>([]);
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);
  const [selectedOutlineNodeIds, setSelectedOutlineNodeIds] = useState<string[]>([]);
  
  // 生成结果
  const [coreMystery, setCoreMystery] = useState<CoreMystery | null>(null);
  const [segments, setSegments] = useState<SegmentStructure[]>([]);
  const [fullText, setFullText] = useState('');
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(-1);
  const [streamingContent, setStreamingContent] = useState('');

  // 创作流程状态
  const [creationStep, setCreationStep] = useState<'input' | 'mystery-preview' | 'phase-strategy' | 'structure-preview' | 'manual-generation' | 'generating'>('input');
  const [structurePreview, setStructurePreview] = useState<SegmentStructure[]>([]);
  const [phaseStrategy, setPhaseStrategy] = useState<any>(null);

  // AI服务实例
  const [aiService] = useState(() => new ShortStoryAIService());

  // 重置状态
  const resetState = () => {
    setError(null);
    setCoreMystery(null);
    setSegments([]);
    setFullText('');
    setCurrentSegmentIndex(-1);
    setStreamingContent('');
  };

  // 第一步：生成结构预览
  const handleGenerateStructure = async () => {
    if (!userInput.trim()) {
      setError('请输入创作需求');
      return;
    }

    setIsLoading(true);
    setError(null);
    resetState();

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      // 第一步：生成核心悬念
      const mysteryResult = await aiService.generateCoreMystery(params);
      if (!mysteryResult.success || !mysteryResult.coreMystery) {
        setError(mysteryResult.error || '生成核心悬念失败');
        return;
      }

      const coreMystery = mysteryResult.coreMystery;
      setCoreMystery(coreMystery);

      // 🔥 修改：生成核心悬念后停止，让用户预览
      setCreationStep('mystery-preview');

    } catch (error: any) {
      console.error('生成结构预览失败:', error);
      setError(error.message || '生成结构预览失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 新增：确认核心悬念，继续生成阶段策划
  const handleConfirmMystery = async () => {
    if (!coreMystery) {
      setError('缺少核心悬念');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      // 第二步：生成阶段策划
      setCreationStep('phase-strategy');
      const strategyResult = await aiService.generatePhaseStrategy(coreMystery, params);
      if (!strategyResult.success || !strategyResult.phaseStrategy) {
        setError(strategyResult.error || '生成阶段策划失败');
        return;
      }

      setPhaseStrategy(strategyResult.phaseStrategy);

      // 第三步：基于策划生成分段结构
      const structureResult = await aiService.generateSegmentStructure(
        coreMystery,
        params.targetSegments || 20,
        params
      );

      if (!structureResult.success || !structureResult.segments) {
        setError(structureResult.error || '生成分段结构失败');
        return;
      }

      // 设置结构预览并切换到预览模式
      setStructurePreview(structureResult.segments);
      setCreationStep('structure-preview');

    } catch (error: any) {
      console.error('生成阶段策划失败:', error);
      setError(error.message || '生成阶段策划失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 新增：重新生成核心悬念
  const handleRegenerateMystery = async () => {
    setCreationStep('input');
    setCoreMystery(null);
    setPhaseStrategy(null);
    setStructurePreview([]);
    setSegments([]);
    setFullText('');
  };

  // 第二步：确认结构，进入手动生成模式
  const handleConfirmStructure = () => {
    if (!coreMystery || structurePreview.length === 0) {
      setError('缺少核心悬念或分段结构');
      return;
    }

    // 初始化segments状态为结构预览
    setSegments(structurePreview);
    // 切换到手动生成模式
    setCreationStep('manual-generation');
  };

  // 手动生成单个段落内容
  const handleGenerateSegment = async (segmentIndex: number) => {
    if (!coreMystery || segments.length === 0) {
      setError('缺少核心悬念或分段结构');
      return;
    }

    setIsLoading(true);
    setError(null);
    setCurrentSegmentIndex(segmentIndex);
    setStreamingContent('');

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      const contentResult = await aiService.generateSingleSegmentContent(
        segmentIndex,
        coreMystery,
        segments,
        params,
        (chunk: string) => {
          setStreamingContent(prev => prev + chunk);
        }
      );

      if (!contentResult.success || !contentResult.content) {
        setError(`生成第${segmentIndex + 1}段内容失败: ${contentResult.error}`);
        return;
      }

      // 更新对应段落的内容
      setSegments(prev => {
        const newSegments = [...prev];
        newSegments[segmentIndex] = {
          ...newSegments[segmentIndex],
          content: contentResult.content!,
          wordCount: contentResult.content!.length
        };
        return newSegments;
      });

      // 更新完整文本
      updateFullText();

    } catch (error: any) {
      console.error('生成段落内容失败:', error);
      setError(error.message || '生成段落内容失败');
    } finally {
      setIsLoading(false);
      setCurrentSegmentIndex(-1);
      setStreamingContent('');
    }
  };

  // 生成指定阶段的所有段落
  const handleGeneratePhase = async (phaseSegments: SegmentStructure[]) => {
    if (!coreMystery || segments.length === 0) {
      setError('缺少核心悬念或分段结构');
      return;
    }

    setIsLoading(true);
    setError(null);
    setStreamingContent('');

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      const result = await aiService.generatePhaseContent(
        phaseSegments,
        segments,
        coreMystery,
        params,
        (segmentIndex: number, content: string) => {
          // 段落完成回调
          setSegments(prev => {
            const newSegments = [...prev];
            newSegments[segmentIndex] = {
              ...newSegments[segmentIndex],
              content,
              wordCount: content.length
            };
            return newSegments;
          });
          updateFullText();
        },
        (chunk: string) => {
          // 流式输出回调
          setStreamingContent(prev => prev + chunk);
        }
      );

      if (!result.success) {
        setError(result.error || '生成阶段内容失败');
        return;
      }

      if (result.generatedSegments) {
        setSegments(result.generatedSegments);
        updateFullText();
      }

    } catch (error: any) {
      console.error('生成阶段内容失败:', error);
      setError(error.message || '生成阶段内容失败');
    } finally {
      setIsLoading(false);
      setCurrentSegmentIndex(-1);
      setStreamingContent('');
    }
  };

  // 更新完整文本
  const updateFullText = () => {
    const fullText = segments
      .filter(seg => seg.content)
      .map(segment => `${segment.segmentNumber}\n\n${segment.content}`)
      .join('\n\n');
    setFullText(fullText);
  };

  // 确认阶段策划，继续生成结构
  const handleConfirmPhaseStrategy = async () => {
    if (!coreMystery || !phaseStrategy) {
      setError('缺少核心悬念或阶段策划');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      // 基于策划生成分段结构
      const structureResult = await aiService.generateSegmentStructure(
        coreMystery,
        params.targetSegments || 20,
        params
      );

      if (!structureResult.success || !structureResult.segments) {
        setError(structureResult.error || '生成分段结构失败');
        return;
      }

      // 设置结构预览并切换到预览模式
      setStructurePreview(structureResult.segments);
      setCreationStep('structure-preview');

    } catch (error: any) {
      console.error('生成分段结构失败:', error);
      setError(error.message || '生成分段结构失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 重新生成阶段策划
  const handleRegeneratePhaseStrategy = async () => {
    if (!coreMystery) {
      setError('缺少核心悬念');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: ShortStoryParams = {
        userInput: userInput.trim(),
        bookId,
        selectedCharacterIds,
        selectedWorldBuildingIds,
        selectedTerminologyIds,
        selectedOutlineNodeIds,
        selectedACEFrameworkIds,
        targetSegments,
        storyTone: storyMode,
        customPhilosophy: storyMode === 'custom' ? customPhilosophy : undefined,
        availableFrameworks: [],
        selectedFrameworks: []
      };

      const strategyResult = await aiService.generatePhaseStrategy(coreMystery, params);
      if (!strategyResult.success || !strategyResult.phaseStrategy) {
        setError(strategyResult.error || '重新生成阶段策划失败');
        return;
      }

      setPhaseStrategy(strategyResult.phaseStrategy);

    } catch (error: any) {
      console.error('重新生成阶段策划失败:', error);
      setError(error.message || '重新生成阶段策划失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 重新生成结构
  const handleRegenerateStructure = () => {
    setCreationStep('input');
    setStructurePreview([]);
    setPhaseStrategy(null);
    setCoreMystery(null);
    setSegments([]);
    setFullText('');
  };

  // 处理应用结果
  const handleApply = () => {
    if (fullText) {
      onShortStoryGenerated(fullText);
    }
  };

  // 处理取消
  const handleCancel = () => {
    if (isLoading) {
      aiService.cancel();
      setIsLoading(false);
    }
    onClose();
  };

  // 清理副作用
  useEffect(() => {
    return () => {
      aiService.cancel();
    };
  }, [aiService]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-[95vw] h-[90vh] max-w-7xl flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-5 border-b bg-gradient-to-r from-amber-50 to-orange-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-5 w-5 text-white" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253M9 7h6m-6 4h6m-6 4h6" 
                />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">AI短篇创作</h2>
              <p className="text-sm text-gray-600">透露一半，逐渐补全的悬念短篇</p>
            </div>
          </div>
          
          <button
            onClick={handleCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isLoading}
          >
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 - 双栏布局 */}
        <div className="flex-1 overflow-hidden p-5 flex" style={{ minHeight: '700px' }}>
          {/* 左侧栏：创作参数和关联元素 */}
          <ShortStoryLeftPanel
            bookId={bookId}
            userInput={userInput}
            storyMode={storyMode}
            targetSegments={targetSegments}
            selectedCharacterIds={selectedCharacterIds}
            selectedWorldBuildingIds={selectedWorldBuildingIds}
            selectedTerminologyIds={selectedTerminologyIds}
            selectedOutlineNodeIds={selectedOutlineNodeIds}
            customPhilosophy={customPhilosophy}
            selectedACEFrameworkIds={selectedACEFrameworkIds}
            error={error}
            onUserInputChange={setUserInput}
            onStoryModeChange={setStoryMode}
            onTargetSegmentsChange={setTargetSegments}
            onCharacterIdsChange={setSelectedCharacterIds}
            onWorldBuildingIdsChange={setSelectedWorldBuildingIds}
            onTerminologyIdsChange={setSelectedTerminologyIds}
            onOutlineNodeIdsChange={setSelectedOutlineNodeIds}
            onCustomPhilosophyChange={setCustomPhilosophy}
            onACEFrameworkIdsChange={setSelectedACEFrameworkIds}
          />

          {/* 右侧栏：根据创作步骤显示不同内容 */}
          {creationStep === 'mystery-preview' ? (
            <CoreMysteryPreviewPanel
              coreMystery={coreMystery}
              isLoading={isLoading}
              onConfirm={handleConfirmMystery}
              onRegenerate={handleRegenerateMystery}
            />
          ) : creationStep === 'phase-strategy' ? (
            <PhaseStrategyPanel
              phaseStrategy={phaseStrategy}
              isLoading={isLoading}
              onConfirm={handleConfirmPhaseStrategy}
              onRegenerate={handleRegeneratePhaseStrategy}
            />
          ) : creationStep === 'structure-preview' ? (
            <StructurePreviewPanel
              coreMystery={coreMystery}
              structurePreview={structurePreview}
              isLoading={isLoading}
              onConfirm={handleConfirmStructure}
              onRegenerate={handleRegenerateStructure}
            />
          ) : (
            <ShortStoryTabbedView
              coreMystery={coreMystery}
              segments={segments}
              fullText={fullText}
              isLoading={isLoading}
              currentSegmentIndex={currentSegmentIndex}
              streamingContent={streamingContent}
              onGenerateSegment={handleGenerateSegment}
              onGeneratePhase={handleGeneratePhase}
            />
          )}
        </div>

        {/* 底部控制按钮 */}
        <ShortStoryControls
          isLoading={isLoading}
          hasResult={!!fullText}
          creationStep={creationStep}
          onGenerate={handleGenerateStructure}
          onApply={handleApply}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};
