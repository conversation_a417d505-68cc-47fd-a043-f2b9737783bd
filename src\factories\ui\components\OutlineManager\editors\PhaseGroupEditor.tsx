"use client";

import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { OutlineNodeType } from '../../../types/outline';

interface PhaseGroupEditorProps {
  node: OutlineNodeType;
  onChange?: (updatedNode: OutlineNodeType) => void;
  onSave?: (data: Partial<OutlineNodeType>) => void; // 保持向后兼容
  onCancel?: () => void;
}

// 暴露给父组件的方法
export interface PhaseGroupEditorRef {
  triggerSave: () => void;
}

/**
 * 阶段分组编辑器组件
 * 用于编辑循环阶段分组节点的信息
 */
export const PhaseGroupEditor = forwardRef<PhaseGroupEditorRef, PhaseGroupEditorProps>(({
  node,
  onChange,
  onSave,
  onCancel
}, ref) => {
  // 表单数据状态
  const [formData, setFormData] = useState({
    title: node.title || '',
    description: node.description || '',
    phaseName: node.phaseName || '',
    phaseDescription: node.phaseDescription || '',
    phasePosition: node.phasePosition || 0,
    phaseWritingGuidance: node.phaseWritingGuidance || '',
    phaseCreativeRequirements: node.phaseCreativeRequirements || [''],
    creativeNotes: node.creativeNotes || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 处理输入变化
  const handleInputChange = (field: string, value: any) => {
    const newFormData = {
      ...formData,
      [field]: value
    };

    setFormData(newFormData);

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // 在onChange模式下，实时更新节点数据
    if (onChange) {
      const updatedNode = { ...node, ...newFormData };
      onChange(updatedNode);
    }
  };

  // 处理创作要求变化
  const handleRequirementChange = (index: number, value: string) => {
    const newRequirements = [...formData.phaseCreativeRequirements];
    newRequirements[index] = value;
    handleInputChange('phaseCreativeRequirements', newRequirements);
  };

  // 添加创作要求
  const addRequirement = () => {
    const newRequirements = [...formData.phaseCreativeRequirements, ''];
    handleInputChange('phaseCreativeRequirements', newRequirements);
  };

  // 删除创作要求
  const removeRequirement = (index: number) => {
    if (formData.phaseCreativeRequirements.length > 1) {
      const newRequirements = formData.phaseCreativeRequirements.filter((_, i) => i !== index);
      handleInputChange('phaseCreativeRequirements', newRequirements);
    }
  };

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '阶段分组标题不能为空';
    }

    if (!formData.phaseName.trim()) {
      newErrors.phaseName = '阶段名称不能为空';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理保存
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    // 过滤空的创作要求
    const filteredRequirements = formData.phaseCreativeRequirements.filter(req => req.trim());

    const updatedData: Partial<OutlineNodeType> = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      phaseName: formData.phaseName.trim(),
      phaseDescription: formData.phaseDescription.trim(),
      phasePosition: formData.phasePosition,
      phaseWritingGuidance: formData.phaseWritingGuidance.trim(),
      phaseCreativeRequirements: filteredRequirements.length > 0 ? filteredRequirements : undefined,
      creativeNotes: formData.creativeNotes.trim()
    };

    // 支持新的onChange模式和旧的onSave模式
    if (onChange) {
      // 新模式：通过onChange更新节点数据
      const updatedNode = { ...node, ...updatedData };
      onChange(updatedNode);
    } else if (onSave) {
      // 旧模式：通过onSave保存数据
      onSave(updatedData);
    }
  };

  // 暴露给父组件的方法（只在有ref时才执行）
  useImperativeHandle(ref, () => ({
    triggerSave: handleSave
  }), [handleSave]);

  // 获取阶段颜色
  const getPhaseColor = () => {
    const phaseColors: Record<string, string> = {
      '开篇建立': 'border-blue-300 bg-blue-50',
      '冲突发展': 'border-red-300 bg-red-50',
      '高潮爽点': 'border-yellow-300 bg-yellow-50',
      '过渡转折': 'border-green-300 bg-green-50'
    };
    return phaseColors[formData.phaseName] || 'border-purple-300 bg-purple-50';
  };

  return (
    <div className="space-y-6">
      {/* 基础信息 */}
      <div className={`p-4 rounded-lg border-2 ${getPhaseColor()}`}>
        <h3 className="text-lg font-medium text-gray-800 mb-4">阶段分组信息</h3>
        
        <div className="space-y-4">
          {/* 标题 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              分组标题 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                errors.title ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="如：第一循环 - 开篇建立"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* 阶段名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              阶段名称 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.phaseName}
              onChange={(e) => handleInputChange('phaseName', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                errors.phaseName ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">请选择阶段</option>
              <option value="开篇建立">开篇建立</option>
              <option value="冲突发展">冲突发展</option>
              <option value="高潮爽点">高潮爽点</option>
              <option value="过渡转折">过渡转折</option>
            </select>
            {errors.phaseName && <p className="text-red-500 text-xs mt-1">{errors.phaseName}</p>}
          </div>

          {/* 阶段位置 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              阶段位置
            </label>
            <input
              type="number"
              min="0"
              value={formData.phasePosition}
              onChange={(e) => handleInputChange('phasePosition', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="在循环中的位置（从0开始）"
            />
          </div>
        </div>
      </div>

      {/* 阶段描述 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          阶段描述
        </label>
        <textarea
          value={formData.phaseDescription}
          onChange={(e) => handleInputChange('phaseDescription', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="描述这个阶段的作用和特点..."
        />
      </div>

      {/* 写作指导 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          写作指导
        </label>
        <textarea
          value={formData.phaseWritingGuidance}
          onChange={(e) => handleInputChange('phaseWritingGuidance', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="为这个阶段的章节提供写作指导..."
        />
      </div>

      {/* 创作要求 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          创作要求
        </label>
        <div className="space-y-2">
          {formData.phaseCreativeRequirements.map((requirement, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="text"
                value={requirement}
                onChange={(e) => handleRequirementChange(index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={`要求 ${index + 1}`}
              />
              {formData.phaseCreativeRequirements.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeRequirement(index)}
                  className="px-2 py-1 text-red-600 hover:text-red-800"
                  title="删除此要求"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            onClick={addRequirement}
            className="px-3 py-1 text-sm text-purple-600 hover:text-purple-800 border border-purple-300 rounded-md hover:bg-purple-50"
          >
            + 添加要求
          </button>
        </div>
      </div>

      {/* 创作笔记 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          创作笔记
        </label>
        <textarea
          value={formData.creativeNotes}
          onChange={(e) => handleInputChange('creativeNotes', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="记录关于这个阶段分组的创作想法..."
        />
      </div>

      {/* 在onChange模式下不显示操作按钮，使用InlineNodeEditor统一的保存按钮 */}
      {!onChange && onSave && (
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            保存
          </button>
        </div>
      )}
    </div>
  );
});

// 设置displayName用于调试
PhaseGroupEditor.displayName = 'PhaseGroupEditor';

export default PhaseGroupEditor;
