/**
 * AI创意助手聊天会话管理器
 * 负责会话的创建、切换、删除、持久化等操作
 */

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'suggestion' | 'result' | 'tool_call_result' | 'welcome';
  metadata?: any;
  toolCallResult?: any;
  // 新增字段：支持AI推理内容
  reasoning_content?: string; // AI推理过程内容
  // 新增字段：支持编辑和排序功能
  customOrder?: number; // 自定义排序序号
  isEdited?: boolean; // 是否已编辑标记
  originalContent?: string; // 原始内容备份
  editHistory?: EditHistoryEntry[]; // 编辑历史记录
  // 分支对话功能字段
  parentMessageId?: string; // 父消息ID（分支起点）
  branchId?: string; // 分支ID
  isBranchRoot?: boolean; // 是否为分支根消息
  childBranches?: string[]; // 子分支ID列表
}

// 编辑历史记录条目
export interface EditHistoryEntry {
  content: string; // 编辑后的内容
  timestamp: Date; // 编辑时间
  reason?: string; // 编辑原因（可选）
}

export interface ChatSession {
  id: string;
  name: string;
  bookId: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    messageCount: number;
    lastActivity: Date;
    tags?: string[];
    color?: string;
    icon?: string;
  };
  // 分支对话功能字段
  parentSessionId?: string; // 父会话ID（如果是分支会话）
  branchFromMessageId?: string; // 分支起始消息ID
  isBranch?: boolean; // 是否为分支会话
  branchName?: string; // 分支名称
}

// 分支信息接口
export interface BranchInfo {
  id: string;
  name: string;
  sessionId: string;
  parentMessageId: string;
  createdAt: Date;
  messageCount: number;
  isActive: boolean;
}

export class SessionManager {
  private static instance: SessionManager;
  private sessions: Map<string, ChatSession> = new Map();
  private currentSessionId: string | null = null;

  private constructor() {}

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * 创建新会话
   */
  public createSession(bookId: string, name?: string): ChatSession {
    const sessionId = this.generateSessionId();
    const sessionCount = this.getSessionsByBookId(bookId).length;

    const session: ChatSession = {
      id: sessionId,
      name: name || `创意讨论 #${sessionCount + 1}`,
      bookId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        messageCount: 0,
        lastActivity: new Date(),
        tags: [],
        color: this.getRandomColor(),
        icon: 'lightbulb' // 使用SVG图标标识符
      }
    };

    // 添加欢迎消息
    const welcomeMessage: ChatMessage = {
      id: 'welcome-' + sessionId,
      role: 'assistant',
      content: `🆕 **新会话开始** - 你好！我是AI创意助手 🎨

我可以帮你生成各种创意内容：
• 📚 **题材构思** - 新颖的故事题材和方向
• ⚡ **剧情脑洞** - 创新的情节发展和转折
• 👥 **人物设定** - 丰富的角色背景和特征
• 🌍 **世界观构建** - 完整的背景设定和规则

请告诉我你想要什么类型的创意帮助，或者直接描述你的需求！`,
      timestamp: new Date(),
      type: 'welcome' // 使用特殊类型标识欢迎消息
    };

    session.messages.push(welcomeMessage);
    session.metadata!.messageCount = 1;

    this.sessions.set(sessionId, session);
    this.currentSessionId = sessionId;
    this.saveSessionsToStorage(bookId);

    console.log('✅ 创建新会话:', session.name, sessionId);
    return session;
  }

  /**
   * 删除会话
   */
  public deleteSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    this.sessions.delete(sessionId);

    // 🔧 修复：删除当前会话后，不自动切换到其他会话
    // 让组件决定下一步操作（通常是创建新会话）
    if (this.currentSessionId === sessionId) {
      this.currentSessionId = null; // 设置为null，让组件决定下一步操作
    }

    this.saveSessionsToStorage(session.bookId);
    console.log('🗑️ 删除会话:', session.name, sessionId);
    return true;
  }

  /**
   * 切换到指定会话
   */
  public switchSession(sessionId: string): ChatSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    this.currentSessionId = sessionId;
    session.updatedAt = new Date();
    session.metadata!.lastActivity = new Date();

    this.saveSessionsToStorage(session.bookId);
    console.log('🔄 切换会话:', session.name, sessionId);
    return session;
  }

  /**
   * 更新会话信息
   */
  public updateSession(sessionId: string, updates: Partial<ChatSession>): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    Object.assign(session, updates);
    session.updatedAt = new Date();

    this.saveSessionsToStorage(session.bookId);
    console.log('📝 更新会话:', session.name, sessionId);
  }

  /**
   * 添加消息到会话
   */
  public addMessage(sessionId: string, message: ChatMessage): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.messages.push(message);
    session.metadata!.messageCount = session.messages.length;
    session.metadata!.lastActivity = new Date();
    session.updatedAt = new Date();

    this.saveSessionsToStorage(session.bookId);
  }

  /**
   * 更新会话中的消息
   */
  public updateMessage(sessionId: string, messageId: string, content: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const messageIndex = session.messages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      session.messages[messageIndex].content = content;
      session.updatedAt = new Date();
      this.saveSessionsToStorage(session.bookId);
    }
  }

  /**
   * 更新消息（支持编辑历史和更多字段）
   */
  public updateMessageWithHistory(sessionId: string, messageId: string, updates: Partial<ChatMessage>): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    const messageIndex = session.messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return false;

    const message = session.messages[messageIndex];

    // 如果更新内容，保存编辑历史
    if (updates.content && updates.content !== message.content) {
      // 首次编辑时保存原始内容
      if (!message.isEdited) {
        message.originalContent = message.content;
        message.editHistory = [];
      }

      // 添加编辑历史记录
      message.editHistory = message.editHistory || [];
      message.editHistory.push({
        content: message.content, // 保存当前内容作为历史
        timestamp: new Date(),
        reason: updates.metadata?.editReason || '用户编辑'
      });

      message.isEdited = true;
    }

    // 应用更新
    Object.assign(message, updates);
    session.updatedAt = new Date();
    this.saveSessionsToStorage(session.bookId);

    console.log('📝 更新消息:', messageId, '编辑历史条数:', message.editHistory?.length || 0);
    return true;
  }

  /**
   * 批量更新消息顺序
   */
  public updateMessagesOrder(sessionId: string, messages: ChatMessage[]): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    // 更新消息的customOrder字段
    messages.forEach((message, index) => {
      const existingMessage = session.messages.find(msg => msg.id === message.id);
      if (existingMessage) {
        existingMessage.customOrder = index;
      }
    });

    // 重新排序消息数组
    session.messages = messages;
    session.updatedAt = new Date();
    this.saveSessionsToStorage(session.bookId);

    console.log('🔄 更新消息顺序:', sessionId, '消息数量:', messages.length);
    return true;
  }

  /**
   * 获取排序后的消息列表
   */
  public getSortedMessages(sessionId: string, useCustomOrder: boolean = false): ChatMessage[] {
    const session = this.sessions.get(sessionId);
    if (!session) return [];

    if (useCustomOrder) {
      // 使用自定义排序
      return [...session.messages].sort((a, b) => {
        const orderA = a.customOrder ?? a.timestamp.getTime();
        const orderB = b.customOrder ?? b.timestamp.getTime();
        return orderA - orderB;
      });
    } else {
      // 使用时间排序（默认）
      return [...session.messages].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
  }

  /**
   * 清空会话消息
   */
  public clearSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // 保留欢迎消息，清空其他消息
    const welcomeMessage = session.messages.find(msg => msg.id.startsWith('welcome-'));
    session.messages = welcomeMessage ? [welcomeMessage] : [];
    session.metadata!.messageCount = session.messages.length;
    session.updatedAt = new Date();

    this.saveSessionsToStorage(session.bookId);
    console.log('🧹 清空会话:', session.name, sessionId);
  }

  /**
   * 获取指定书籍的所有会话
   */
  public getSessionsByBookId(bookId: string): ChatSession[] {
    return Array.from(this.sessions.values())
      .filter(session => session.bookId === bookId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  /**
   * 获取当前会话
   */
  public getCurrentSession(): ChatSession | null {
    return this.currentSessionId ? this.sessions.get(this.currentSessionId) || null : null;
  }

  /**
   * 获取当前会话ID
   */
  public getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * 加载指定书籍的会话数据
   */
  public loadSessions(bookId: string): ChatSession[] {
    try {
      const storageKey = `brainstorm-sessions-${bookId}`;
      const savedData = localStorage.getItem(storageKey);

      if (savedData) {
        const sessionsData = JSON.parse(savedData);

        // 恢复会话数据
        sessionsData.forEach((sessionData: any) => {
          // 转换日期字符串为Date对象
          sessionData.createdAt = new Date(sessionData.createdAt);
          sessionData.updatedAt = new Date(sessionData.updatedAt);
          sessionData.metadata.lastActivity = new Date(sessionData.metadata.lastActivity);

          // 转换消息时间戳
          sessionData.messages.forEach((msg: any) => {
            msg.timestamp = new Date(msg.timestamp);
          });

          this.sessions.set(sessionData.id, sessionData);
        });

        // 设置当前会话为最近更新的会话
        const sessions = this.getSessionsByBookId(bookId);
        if (sessions.length > 0) {
          this.currentSessionId = sessions[0].id;
        }

        console.log('📂 加载会话数据:', sessions.length, '个会话');
        return sessions;
      }
    } catch (error) {
      console.error('❌ 加载会话数据失败:', error);
    }

    // 如果没有保存的数据，创建默认会话
    const defaultSession = this.createSession(bookId);
    return [defaultSession];
  }

  /**
   * 保存会话数据到localStorage
   */
  private saveSessionsToStorage(bookId: string): void {
    try {
      const sessions = this.getSessionsByBookId(bookId);
      const storageKey = `brainstorm-sessions-${bookId}`;
      localStorage.setItem(storageKey, JSON.stringify(sessions));
    } catch (error) {
      console.error('❌ 保存会话数据失败:', error);
    }
  }

  /**
   * 生成唯一会话ID
   */
  private generateSessionId(): string {
    return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 生成分支ID
   */
  private generateBranchId(parentMessageId: string): string {
    return `branch-${parentMessageId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 从指定消息创建分支对话
   */
  public createBranchFromMessage(
    parentSessionId: string,
    parentMessageId: string,
    branchName?: string
  ): ChatSession | null {
    const parentSession = this.sessions.get(parentSessionId);
    if (!parentSession) {
      console.error('父会话不存在:', parentSessionId);
      return null;
    }

    // 找到分支起始消息的索引
    const messageIndex = parentSession.messages.findIndex(msg => msg.id === parentMessageId);
    if (messageIndex === -1) {
      console.error('分支起始消息不存在:', parentMessageId);
      return null;
    }

    // 创建分支会话
    const branchSessionId = this.generateSessionId();
    const branchId = this.generateBranchId(parentMessageId);

    // 复制分支点之前的所有消息作为上下文
    const contextMessages = parentSession.messages.slice(0, messageIndex + 1).map(msg => ({
      ...msg,
      branchId: branchId,
      // 标记分支起始消息
      isBranchRoot: msg.id === parentMessageId
    }));

    const branchSession: ChatSession = {
      id: branchSessionId,
      name: branchName || `分支对话 - ${new Date().toLocaleTimeString()}`,
      bookId: parentSession.bookId,
      messages: contextMessages,
      createdAt: new Date(),
      updatedAt: new Date(),
      parentSessionId: parentSessionId,
      branchFromMessageId: parentMessageId,
      isBranch: true,
      branchName: branchName,
      metadata: {
        messageCount: contextMessages.length,
        lastActivity: new Date(),
        tags: ['分支对话'],
        color: this.getRandomColor(),
        icon: 'git-branch'
      }
    };

    // 保存分支会话
    this.sessions.set(branchSessionId, branchSession);

    // 在父消息中记录子分支
    const parentMessage = parentSession.messages.find(msg => msg.id === parentMessageId);
    if (parentMessage) {
      if (!parentMessage.childBranches) {
        parentMessage.childBranches = [];
      }
      parentMessage.childBranches.push(branchId);
    }

    // 保存更新
    this.saveSessionsToStorage(parentSession.bookId);

    console.log('✅ 分支对话创建成功:', branchSessionId);
    return branchSession;
  }

  /**
   * 获取会话的所有分支
   */
  public getSessionBranches(sessionId: string): BranchInfo[] {
    const branches: BranchInfo[] = [];

    for (const [id, session] of this.sessions) {
      if (session.parentSessionId === sessionId && session.isBranch) {
        branches.push({
          id: session.id,
          name: session.branchName || session.name,
          sessionId: session.id,
          parentMessageId: session.branchFromMessageId || '',
          createdAt: session.createdAt,
          messageCount: session.messages.length,
          isActive: this.currentSessionId === session.id
        });
      }
    }

    return branches.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  }

  /**
   * 切换到分支会话
   */
  public switchToBranch(branchSessionId: string): boolean {
    const branchSession = this.sessions.get(branchSessionId);
    if (!branchSession || !branchSession.isBranch) {
      console.error('分支会话不存在:', branchSessionId);
      return false;
    }

    this.currentSessionId = branchSessionId;
    console.log('✅ 已切换到分支会话:', branchSessionId);
    return true;
  }

  /**
   * 删除分支会话
   */
  public deleteBranch(branchSessionId: string): boolean {
    const branchSession = this.sessions.get(branchSessionId);
    if (!branchSession || !branchSession.isBranch) {
      console.error('分支会话不存在或不是分支:', branchSessionId);
      return false;
    }

    // 从父消息中移除分支引用
    if (branchSession.parentSessionId && branchSession.branchFromMessageId) {
      const parentSession = this.sessions.get(branchSession.parentSessionId);
      if (parentSession) {
        const parentMessage = parentSession.messages.find(msg => msg.id === branchSession.branchFromMessageId);
        if (parentMessage && parentMessage.childBranches) {
          const branchId = this.generateBranchId(branchSession.branchFromMessageId!);
          parentMessage.childBranches = parentMessage.childBranches.filter(id => id !== branchId);
        }
      }
    }

    // 删除分支会话
    this.sessions.delete(branchSessionId);

    // 如果当前会话是被删除的分支，切换回父会话
    if (this.currentSessionId === branchSessionId && branchSession.parentSessionId) {
      this.currentSessionId = branchSession.parentSessionId;
    }

    // 保存更新
    this.saveSessionsToStorage(branchSession.bookId);

    console.log('✅ 分支会话已删除:', branchSessionId);
    return true;
  }

  /**
   * 获取随机颜色
   */
  private getRandomColor(): string {
    const colors = [
      '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B',
      '#EF4444', '#06B6D4', '#84CC16', '#F97316'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}

// 导出单例实例
export const sessionManager = SessionManager.getInstance();
