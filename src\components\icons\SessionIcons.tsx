"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface IconProps {
  size?: number;
  color?: string;
  animated?: boolean;
  className?: string;
}

// 创意灯泡图标 - 替换💡
export const LightbulbIcon: React.FC<IconProps> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '' 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      initial={animated ? { scale: 0, rotate: -180 } : undefined}
      animate={animated ? { scale: 1, rotate: 0 } : undefined}
      transition={animated ? { type: "spring", stiffness: 300, damping: 20 } : undefined}
    >
      {/* 灯泡主体 */}
      <motion.path
        d="M9 21c0 .5.4 1 1 1h4c.6 0 1-.4 1-1v-1H9v1z"
        fill={color}
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.6, delay: 0.2 } : undefined}
      />
      <motion.path
        d="M12 2a7 7 0 0 0-7 7c0 2.38 1.19 4.47 3 5.74V17a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-2.26c1.81-1.27 3-3.36 3-5.74a7 7 0 0 0-7-7z"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.8, delay: 0 } : undefined}
      />
      {/* 灯泡内部线条 */}
      <motion.path
        d="M12 9v4"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.4, delay: 0.4 } : undefined}
      />
      <motion.path
        d="M10.5 11.5l3-3"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.4, delay: 0.6 } : undefined}
      />
    </motion.svg>
  );
};

// 思考气泡图标 - 替换💭
export const ThoughtBubbleIcon: React.FC<IconProps> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '' 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      initial={animated ? { scale: 0 } : undefined}
      animate={animated ? { scale: 1 } : undefined}
      transition={animated ? { type: "spring", stiffness: 400, damping: 25 } : undefined}
    >
      {/* 主要思考泡泡 */}
      <motion.ellipse
        cx="12"
        cy="8"
        rx="8"
        ry="5"
        stroke={color}
        strokeWidth="2"
        fill="none"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.6, delay: 0 } : undefined}
      />
      {/* 中等泡泡 */}
      <motion.circle
        cx="8"
        cy="16"
        r="2"
        stroke={color}
        strokeWidth="2"
        fill="none"
        initial={animated ? { pathLength: 0, scale: 0 } : undefined}
        animate={animated ? { pathLength: 1, scale: 1 } : undefined}
        transition={animated ? { duration: 0.4, delay: 0.3 } : undefined}
      />
      {/* 小泡泡 */}
      <motion.circle
        cx="6"
        cy="20"
        r="1"
        fill={color}
        initial={animated ? { scale: 0 } : undefined}
        animate={animated ? { scale: 1 } : undefined}
        transition={animated ? { duration: 0.3, delay: 0.5 } : undefined}
      />
      {/* 思考内容点 */}
      <motion.circle
        cx="9"
        cy="8"
        r="1"
        fill={color}
        initial={animated ? { scale: 0 } : undefined}
        animate={animated ? { scale: 1 } : undefined}
        transition={animated ? { duration: 0.2, delay: 0.7 } : undefined}
      />
      <motion.circle
        cx="12"
        cy="8"
        r="1"
        fill={color}
        initial={animated ? { scale: 0 } : undefined}
        animate={animated ? { scale: 1 } : undefined}
        transition={animated ? { duration: 0.2, delay: 0.8 } : undefined}
      />
      <motion.circle
        cx="15"
        cy="8"
        r="1"
        fill={color}
        initial={animated ? { scale: 0 } : undefined}
        animate={animated ? { scale: 1 } : undefined}
        transition={animated ? { duration: 0.2, delay: 0.9 } : undefined}
      />
    </motion.svg>
  );
};

// 加号图标 - 新建会话
export const PlusIcon: React.FC<IconProps> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '' 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      whileHover={animated ? { rotate: 90 } : undefined}
      transition={animated ? { duration: 0.2 } : undefined}
    >
      {/* 垂直线 */}
      <motion.path
        d="M12 5v14"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.3, delay: 0 } : undefined}
      />
      {/* 水平线 */}
      <motion.path
        d="M5 12h14"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.3, delay: 0.2 } : undefined}
      />
      {/* 中心发光点 */}
      {animated && (
        <motion.circle
          cx="12"
          cy="12"
          r="2"
          fill={color}
          opacity="0.3"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 0] }}
          transition={{ duration: 0.6, delay: 0.4 }}
        />
      )}
    </motion.svg>
  );
};

// 垃圾桶图标 - 删除操作
export const TrashIcon: React.FC<IconProps> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '' 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      whileHover={animated ? { scale: 1.1 } : undefined}
    >
      {/* 垃圾桶主体 */}
      <motion.path
        d="M3 6h18l-2 12H5L3 6z"
        stroke={color}
        strokeWidth="2"
        fill="none"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.5, delay: 0 } : undefined}
      />
      {/* 垃圾桶盖子 */}
      <motion.path
        d="M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2"
        stroke={color}
        strokeWidth="2"
        fill="none"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.4, delay: 0.2 } : undefined}
      />
      {/* 垃圾桶内部线条 */}
      <motion.path
        d="M10 11v6"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.3, delay: 0.4 } : undefined}
      />
      <motion.path
        d="M14 11v6"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.3, delay: 0.5 } : undefined}
      />
    </motion.svg>
  );
};

// 清空图标 - 清空聊天
export const ClearIcon: React.FC<IconProps> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '' 
}) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      whileHover={animated ? { scale: 1.1 } : undefined}
    >
      {/* 扫帚柄 */}
      <motion.path
        d="M3 3l18 18"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.5, delay: 0 } : undefined}
      />
      {/* 扫帚头 */}
      <motion.path
        d="M19 7l-3 3-2-2 3-3a1 1 0 011.414 0L19 6.414A1 1 0 0119 7z"
        stroke={color}
        strokeWidth="2"
        fill="none"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.4, delay: 0.2 } : undefined}
      />
      {/* 清理效果点 */}
      {animated && (
        <>
          <motion.circle
            cx="8"
            cy="16"
            r="1"
            fill={color}
            opacity="0.6"
            initial={{ scale: 0 }}
            animate={{ scale: [0, 1, 0] }}
            transition={{ duration: 0.8, delay: 0.3, repeat: Infinity }}
          />
          <motion.circle
            cx="11"
            cy="13"
            r="1"
            fill={color}
            opacity="0.4"
            initial={{ scale: 0 }}
            animate={{ scale: [0, 1, 0] }}
            transition={{ duration: 0.8, delay: 0.5, repeat: Infinity }}
          />
          <motion.circle
            cx="14"
            cy="10"
            r="1"
            fill={color}
            opacity="0.2"
            initial={{ scale: 0 }}
            animate={{ scale: [0, 1, 0] }}
            transition={{ duration: 0.8, delay: 0.7, repeat: Infinity }}
          />
        </>
      )}
    </motion.svg>
  );
};

// 折叠/展开图标
export const ChevronIcon: React.FC<IconProps & { direction: 'left' | 'right' }> = ({ 
  size = 20, 
  color = 'currentColor', 
  animated = false, 
  className = '',
  direction = 'right'
}) => {
  const rotation = direction === 'left' ? 180 : 0;
  
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      style={{ transform: `rotate(${rotation}deg)` }}
      whileHover={animated ? { scale: 1.1 } : undefined}
    >
      <motion.path
        d="M9 18l6-6-6-6"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        initial={animated ? { pathLength: 0 } : undefined}
        animate={animated ? { pathLength: 1 } : undefined}
        transition={animated ? { duration: 0.4 } : undefined}
      />
    </motion.svg>
  );
};
