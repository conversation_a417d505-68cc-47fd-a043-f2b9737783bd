"use client";

import { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';

/**
 * 人物数据钩子
 * @param bookId 书籍ID
 * @param isOpen 是否打开
 */
export const useCharacterData = (bookId: string, isOpen: boolean) => {
  // 状态
  const [characters, setCharacters] = useState<Character[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'importance'>('name');
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // 加载人物数据
  useEffect(() => {
    if (isOpen) {
      // 设置加载状态
      setIsLoading(true);

      // 从API获取数据
      // 导入 characterRepository
      import('@/lib/db/repositories').then(({ characterRepository }) => {
        // 获取人物数据
        characterRepository.getAllByBookId(bookId)
          .then(data => {
            console.log('获取到人物数据:', data);
            setCharacters(data);
            setIsLoading(false);
          })
          .catch(error => {
            console.error('获取人物数据失败:', error);
            setCharacters([]);
            setIsLoading(false);
          });
      });
    }
  }, [isOpen, bookId]);

  // 过滤和排序人物
  const filteredCharacters = characters
    .filter(character => {
      if (!searchQuery) return true;
      return character.name.toLowerCase().includes(searchQuery.toLowerCase());
    })
    .sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      }
      // 假设importance是一个数字属性，如果不存在则使用0
      const aImportance = a.attributes?.importance ? parseInt(a.attributes.importance) : 0;
      const bImportance = b.attributes?.importance ? parseInt(b.attributes.importance) : 0;
      return bImportance - aImportance;
    });

  // 处理选择人物
  const handleSelectCharacter = (character: Character) => {
    setSelectedCharacter(character);
    setIsEditing(false);
  };

  // 处理创建人物
  const handleCreateCharacter = () => {
    const now = new Date();
    const newCharacter: Character = {
      bookId,
      name: '新人物',
      description: '',
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: [],
      relatedCharacterIds: [],
      relatedTerminologyIds: [],
      relatedWorldBuildingIds: []
    };

    setSelectedCharacter(newCharacter);
    setIsEditing(true);
  };

  // 处理编辑人物
  const handleEditCharacter = () => {
    if (selectedCharacter) {
      setIsEditing(true);
    }
  };

  // 处理保存人物
  const handleSaveCharacter = (character: Character) => {
    // 检查是否为新人物
    const isNew = !characters.some(c => c.id === character.id);

    if (isNew) {
      // 添加到列表
      setCharacters(prev => [...prev, character]);
    } else {
      // 更新列表
      setCharacters(prev => prev.map(c => c.id === character.id ? character : c));
    }

    // 更新选中人物
    setSelectedCharacter(character);

    // 退出编辑模式
    setIsEditing(false);

    return isNew;
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    // 如果是新人物，清除选中状态
    if (selectedCharacter && !characters.some(c => c.id === selectedCharacter.id)) {
      setSelectedCharacter(null);
    }

    // 退出编辑模式
    setIsEditing(false);
  };

  // 处理删除人物
  const handleDeleteCharacter = (characterId: string) => {
    console.log('处理删除人物:', characterId);

    // 从列表中移除
    setCharacters(prev => {
      const filtered = prev.filter(c => c.id !== characterId);
      console.log('删除后的人物列表:', filtered);
      return filtered;
    });

    // 清除选中状态
    if (selectedCharacter?.id === characterId) {
      console.log('清除选中状态');
      setSelectedCharacter(null);
      setIsEditing(false); // 确保退出编辑模式
    }

    // 添加动画效果
    const element = document.getElementById(`character-item-${characterId}`);
    if (element) {
      console.log('添加删除动画效果');
      element.style.animation = 'fadeOut 0.3s ease forwards';
      element.style.opacity = '0';
      element.style.transform = 'translateX(-20px)';
    }
  };

  return {
    characters,
    filteredCharacters,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedCharacter,
    isEditing,
    handleSelectCharacter,
    handleCreateCharacter,
    handleEditCharacter,
    handleSaveCharacter,
    handleCancelEdit,
    handleDeleteCharacter
  };
};
