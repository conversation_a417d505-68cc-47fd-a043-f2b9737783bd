"use client";

import React from 'react';

interface FloatingActionButtonProps {
  isVisible: boolean;
  position: { top: number; left: number };
  onClick: () => void;
  icon: React.ReactNode;
  title?: string;
}

/**
 * 浮动操作按钮组件
 * 用于在选中文本时显示操作按钮
 */
const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  isVisible,
  position,
  onClick,
  icon,
  title = 'AI改写选中内容'
}) => {
  if (!isVisible) return null;

  return (
    <div
      className="fixed z-50 transition-all duration-300"
      style={{
        pointerEvents: 'auto',
        transform: 'translateX(-50%)', // 只在水平方向上居中
        willChange: 'transform', // 优化性能
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)' // 增强视觉效果
      }}
    >
      <button
        className="flex items-center justify-center p-3 rounded-full shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 active:scale-95 transition-all duration-200"
        style={{

          backdropFilter: 'blur(4px)',
          border: '2px solid rgba(255, 255, 255, 0.5)'
        }}
        onClick={onClick}
        title={title}
      >
        {icon}
      </button>
    </div>
  );
};

export default FloatingActionButton;
