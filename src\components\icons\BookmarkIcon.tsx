"use client";

import React from 'react';
import IconBase, { IconBaseProps } from './IconBase';

/**
 * 书签图标 - 古典书签样式
 * 支持丝带飘动效果
 */
const BookmarkIcon: React.FC<Omit<IconBaseProps, 'children'>> = (props) => {
  return (
    <IconBase {...props} className={`bookmark-icon ${props.className || ''}`}>
      {/* 书签主体 */}
      <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" />
      {/* 丝带装饰 */}
      <path d="M12 2v4" strokeWidth="1" opacity="0.6" />
      <path d="M10 2h4" strokeWidth="1" opacity="0.6" />
      {/* 书签上的装饰花纹 */}
      <circle cx="12" cy="8" r="1.5" opacity="0.4" />
      <path d="M10.5 10.5l3 3" strokeWidth="1" opacity="0.3" />
      <path d="M13.5 10.5l-3 3" strokeWidth="1" opacity="0.3" />
    </IconBase>
  );
};

export default BookmarkIcon;
