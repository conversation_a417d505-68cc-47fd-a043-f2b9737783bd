/**
 * WritingGuidance字段统一权威管理类
 * 
 * 这是writingGuidance字段要求的唯一权威定义，解决了以下问题：
 * 1. 消除多处重复定义导致的权重分散
 * 2. 统一所有AI服务的writingGuidance标准
 * 3. 避免AI接收到重复指导信息产生混淆
 * 4. 简化维护成本，只需修改一处
 * 
 * 设计原则：
 * - 单一权威：所有writingGuidance要求统一在此定义
 * - 高权重：通过系统消息确保AI优先理解
 * - 具体化：提供明确的避免事项和推荐写法
 * - 可操作：让AI和用户都能清楚理解要求
 */

import { ConversationMessage } from '../types/SharedTypes';

export class WritingGuidanceAuthority {
  private static instance: WritingGuidanceAuthority;

  private constructor() {}

  public static getInstance(): WritingGuidanceAuthority {
    if (!WritingGuidanceAuthority.instance) {
      WritingGuidanceAuthority.instance = new WritingGuidanceAuthority();
    }
    return WritingGuidanceAuthority.instance;
  }
  /**
   * 构建统一的writingGuidance权威消息
   * 🔥 专门针对writingGuidance字段的避免AI味道要求
    /**
   * 构建统一的writingGuidance权威消息
   * 🔥 专门针对writingGuidance字段的避免AI味道要求
   */
    buildUnifiedGuidanceMessage(): ConversationMessage {
      return {
        role: 'user',
        content: `【写作心法：对抗AI写作套路，追求极致“个性化”细节】
  
  我们的最高目标是彻底根除写作中的“AI味道”。这意味着，我们必须识别并摧毁AI最爱使用的所有陈词滥调和写作捷径。核心方法是：**用独一无二、完全服务于当前角色和情境的“个性化”细节，来取代通用的、模板化的描写。**
  
  ---
  
  **🎯 \`shouldWriting\` 的核心任务：成为“细节大师”**
  
  *   **目标**：主动构思并提供一个**“非他不可”**的细节。这个动作或场景，换一个角色、换一个情境就不成立，从而产生强大的真实感和独特性。
  *   **要求**：直接给出场景片段，展示如何用一个独特的细节来揭示人物内心或推动情节。
  
  *   **当有参考作品时 (有ACE链接)：我们是“细节解码者”**
      *   **任务**：分析原文中真正“高光”的个性化细节。
      *   **范例**：“分析‘蒋雪回头对我眨了眨眼睛’：这个动作的精髓在于它的**不可替代性**。它不是一个通用的‘挑衅’表情，而是专属于蒋雪这个角色的、在婚礼这个特定场景下的、无声的胜利宣言。它将人物心机和戏剧冲突提升到了极致。”
  
  *   **当没有参考作品时：我们是“原创细节师”**
      *   **任务**：创造一个无法被轻易模仿的细节。
      *   **范例1 (塑造人物的冷静与残忍)**：“可以这样写：‘他杀完人，没有去擦手上的血，而是从口袋里拿出一块怀表，打开，看了一眼时间。对着我说，“比预定的早了三分钟，我们得找个地方处理掉这段多余的时间。”’——用对时间的极致控制来反衬对生命的极致漠视。”
  **🎯 \`avoidWriting\` 的核心任务：成为“AI套路粉碎机”**
  
  *   **目标**：精准识别并禁用AI在无计可施时，最爱用的那些陈词滥调和写作拐杖。
  *   **要求**：
      1.  使用**“严禁...”**的格式，给出简洁、明确的禁令。
      2.  **禁令本身就应该是我们识别出的AI套路。**
  
  *   **要严禁的AI套路范例**：
      *   “严禁使用‘手指有节奏地敲击桌面’来表现思考或不耐烦。”
      *   “严禁使用‘嘴角勾起一抹/一丝xx的微笑’来描写表情。”
      *   “严禁使用‘眼中闪过一丝不易察觉的xx’来暗示情绪。”
      *   “严禁使用‘浑身散发出冰冷/强大/xxx的气息’这种空洞描写。”
      *   “严禁直接描写心理活动，如‘他感到十分震惊/愤怒/困惑’。”
  
  ---
  
  **📝 附：我们整体的写作风格追求**
  *   **开局即战场**：快速进入核心冲突，用事件带出人物。
  *   **节奏为王**：避免情节拖沓，让故事始终推着读者走。
  *   **笔锋利落**：文风简练有力，用行动和结果来塑造氛围，而非辞藻堆砌。`
      };
    }
  
    /**
     * 构建助手确认消息
     */
    buildConfirmationMessage(): ConversationMessage {
      return {
        role: 'assistant',
        content: '指令已更新并完全同步。核心思想我已掌握：**对抗AI套路，追求“非他不可”的个性化细节。**\n我将作为“AI套路粉碎机”和“原创细节师”来工作。\n- **\avoidWriting\**：我会直接点名并禁用“手指敲桌”、“嘴角上扬”等AI常用套路。\n- **\shouldWriting\**：我会努力提供独特的、只能属于当前角色和情境的细节描写范例。\n保证输出符合这一最终、最高标准的专业内容。'
      };
    }
  /**
   * 验证writingGuidance内容是否符合权威要求
   */
  validateGuidanceContent(guidance: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // 检查长度
    if (guidance.length < 100) {
      issues.push('writingGuidance内容过短，应至少100字');
      suggestions.push('添加更具体的避免事项和应该做的事项');
    }

    // 检查是否包含避免事项
    if (!guidance.includes('🚫') && !guidance.includes('严禁') && !guidance.includes('不要') && !guidance.includes('避免')) {
      issues.push('缺少明确的避免指导');
      suggestions.push('添加"🚫严禁使用一丝xx"等具体避免事项');
    }

    // 检查是否包含具体指导
    if (!guidance.includes('直接写') && !guidance.includes('专注于')) {
      issues.push('缺少具体的写作指导');
      suggestions.push('添加"直接写xxx"等具体操作指导');
    }

    // 检查是否提及字数要求（不应该提及）
    if (guidance.includes('70字') || guidance.includes('字数') || guidance.includes('超过')) {
      issues.push('不应在writingGuidance中提及字数要求');
      suggestions.push('移除关于字数的说明，让内容自然达到70字以上');
    }

    // 检查是否提及技术术语（不应该提及）
    if (guidance.includes('ACE框架') || guidance.includes('框架')) {
      issues.push('不应在writingGuidance中提及技术术语');
      suggestions.push('用自己的话融合说明概念，不要过于言简意赅');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * 生成标准的writingGuidance内容模板
   */
  generateStandardGuidanceTemplate(plotPointContent: string, context?: {
    plotType?: 'conflict' | 'twist' | 'climax' | 'resolution';
    characters?: string[];
    specificAvoidance?: string[];
  }): string {
    const actionKeyword = this.extractActionKeywords(plotPointContent);
    const baseTemplate = `🚫严禁使用'一丝困惑'、'几分紧张'、'些许不安'等模糊表达、'如同野兽般'、'像刀锋一样'的套路化比喻，以及'显然表现出'、'明显体现'等官方文档式表达。直接写'${actionKeyword}'的生动具体行动和对话内容，不要过度描写环境细节。可以适当扩展相关剧情推进，但不要添加无关的场景描写或心理活动描述，避免拖慢节奏。专注于角色具体做了什么、说了什么，而不是感受到什么。推荐陌生化表达：动作先行，对话跟进，结果立现，让每个行动都有明确目的和可见结果。不出现解释性语句，除非是概念讲解。`;

    // 根据上下文添加特定指导
    if (context?.plotType === 'conflict') {
      return baseTemplate + '重点描写冲突双方的具体行动和言语交锋，避免过多的内心独白。展现实力对比和策略博弈，突出装逼打脸循环中的能力展示环节。';
    } else if (context?.plotType === 'twist') {
      return baseTemplate + '专注于转折点的关键信息揭示和角色反应，不要铺垫过多无关细节。体现反套路惊喜设计，让结果与预想完全相反。';
    } else if (context?.plotType === 'climax') {
      return baseTemplate + '突出高潮时刻的关键动作和决定性对话，保持紧凑的节奏感。融入五重核心驱动中的认知颠覆和资源增值元素。';
    } else if (context?.plotType === 'resolution') {
      return baseTemplate + '专注于问题解决的具体过程和结果展现，避免抽象的总结性描述。确保为下一轮冲突埋下伏笔，保持故事的无限延展性。';
    }

    // 添加特定避免事项（如果提供）
    if (context?.specificAvoidance && context.specificAvoidance.length > 0) {
      const avoidanceList = context.specificAvoidance.join('、');
      return baseTemplate + `特别避免使用'${avoidanceList}'等表达方式。`;
    }

    return baseTemplate;
  }

  /**
   * 从剧情点内容中提取关键行动词汇
   */
  private extractActionKeywords(content: string): string {
    // 简单的关键词提取逻辑，可以根据需要扩展
    const actionWords = ['发现', '决定', '行动', '对话', '冲突', '解决', '反应', '选择'];
    for (const word of actionWords) {
      if (content.includes(word)) {
        return word;
      }
    }
    return '角色行动';
  }

  /**
   * 获取权威要求的简化版本（用于其他地方的简单引用）
   */
  getSimplifiedRequirements(): string {
    return `按照writingGuidance统一要求：100字以上，禁用"一丝xx"等模糊表达，专注具体行动，使用"🚫严禁...，直接写..."格式。`;
  }
}
