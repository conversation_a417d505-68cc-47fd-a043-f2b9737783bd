# AI助手功能模块

## 概述

AI助手功能为大纲画布提供了智能对话和节点操作能力。用户可以通过自然语言与AI交互，AI会返回结构化的节点修改建议，用户可以预览并选择性应用这些变更。

## 核心特性

### 🎯 布局优化
- **画布收缩模式**: 点击助手按钮后，画布向左收缩而非被遮挡
- **平滑动画**: 使用CSS transform实现流畅的布局过渡
- **响应式设计**: 大屏侧边栏模式，小屏覆盖模式

### 💬 智能对话
- **自然语言交互**: 支持与AI进行自然语言对话
- **@节点功能**: 输入@符号可以提及和选择大纲中的任何节点
- **上下文感知**: AI能理解当前选中的节点和大纲结构

### 🔄 变更管理
- **JSON格式响应**: AI返回结构化的节点修改建议
- **变更预览**: 用户可以预览所有建议的变更
- **选择性应用**: 支持勾选部分变更进行应用

## 组件架构

```
AssistantButton          # 触发按钮
AssistantDrawer          # 抽屉容器
├── ChatInterface        # 对话界面
│   ├── NodeMentionSelector  # @节点选择器
│   └── 消息列表和输入框
└── ChangePreviewPanel   # 变更预览面板
    ├── 变更列表
    ├── 选择控制
    └── 应用/取消按钮
```

## 文件结构

```
assistant/
├── ChatInterface.tsx           # 聊天界面组件
├── ChatInterface.css          # 聊天界面样式
├── NodeMentionSelector.tsx    # 节点提及选择器
├── NodeMentionSelector.css    # 选择器样式
├── ChangePreviewPanel.tsx     # 变更预览面板
├── ChangePreviewPanel.css     # 预览面板样式
└── README.md                  # 本文档

../
├── AssistantButton.tsx        # 助手按钮
├── AssistantButton.css        # 按钮样式
├── AssistantDrawer.tsx        # 抽屉容器
├── AssistantDrawer.css        # 抽屉样式
└── OutlineCanvasLayout.css    # 布局样式
```

## 使用方法

### 基本使用

1. 点击画布右上角的AI助手按钮
2. 在对话框中输入自然语言指令
3. 使用@符号提及特定节点
4. 查看AI返回的变更建议
5. 选择需要应用的变更并确认

### @节点功能

```
@章节1 请扩展这个章节的内容
@场景2 添加更多的冲突元素
@笔记3 请优化这个笔记的描述
```

### 变更类型

- **create**: 创建新节点
- **update**: 更新现有节点
- **delete**: 删除节点

## 样式系统

### 主题支持
- 浅色主题（默认）
- 深色主题（自动检测）
- 高对比度模式

### 响应式断点
- 桌面端: >= 1200px（侧边栏模式）
- 平板端: 768px - 1199px（缩小侧边栏）
- 移动端: <= 767px（覆盖模式）

### 动画配置
- 布局过渡: 0.4s cubic-bezier(0.4, 0, 0.2, 1)
- 按钮悬停: 0.3s ease
- 减少动画模式支持

## 集成说明

### 在OutlineCanvas中集成

```tsx
// 1. 导入组件
import AssistantButton from './AssistantButton';
import AssistantDrawer from './AssistantDrawer';

// 2. 添加状态
const [isAssistantOpen, setIsAssistantOpen] = useState(false);

// 3. 添加处理函数
const handleAssistantToggle = () => setIsAssistantOpen(!isAssistantOpen);
const handleAssistantClose = () => setIsAssistantOpen(false);
const handleAssistantApplyChanges = (changes) => { /* 处理变更 */ };

// 4. 在JSX中使用
<div className={`outline-canvas-wrapper ${isAssistantOpen ? 'assistant-open' : ''}`}>
  <div className="outline-canvas-container">
    {/* ReactFlow组件 */}
    <Panel position="top-right">
      <AssistantButton onClick={handleAssistantToggle} isActive={isAssistantOpen} />
    </Panel>
  </div>
  <AssistantDrawer
    isOpen={isAssistantOpen}
    onClose={handleAssistantClose}
    outline={outline}
    onApplyChanges={handleAssistantApplyChanges}
  />
</div>
```

## 开发计划

### 已完成 ✅
- [x] 基础组件架构
- [x] 响应式布局系统
- [x] 对话界面和@节点功能
- [x] 变更预览和确认机制
- [x] 动画和样式优化

### 待完成 🔄
- [ ] 真实AI API集成
- [ ] 更丰富的节点操作类型
- [ ] 对话历史持久化
- [ ] 快捷操作模板
- [ ] 性能优化和错误处理

## 注意事项

1. **性能**: 使用React.memo优化组件渲染
2. **无障碍**: 支持键盘导航和屏幕阅读器
3. **兼容性**: 支持现代浏览器，IE11+
4. **安全**: 对用户输入进行适当的验证和清理
