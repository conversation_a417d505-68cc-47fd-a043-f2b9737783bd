import { IAIComponent } from './IAIComponent';

/**
 * AI选中改写组件接口
 * 用于根据用户选中的内容和要求进行智能改写
 */
export interface IAIRewriteComponent extends IAIComponent {
  /**
   * 设置选中的文本
   * @param text 选中的文本
   */
  setSelectedText(text: string): void;

  /**
   * 设置改写要求
   * @param requirements 改写要求
   */
  setRewriteRequirements(requirements: string): void;

  /**
   * 设置剧情
   * @param plot 剧情
   */
  setPlot(plot: string): void;

  /**
   * 设置上下文
   * @param before 选中文本前的内容
   * @param after 选中文本后的内容
   */
  setContext(before: string, after: string): void;

  /**
   * 改写文本
   * @returns 改写后的文本
   */
  rewrite(): Promise<string>;

  /**
   * 设置改写完成回调
   * @param callback 回调函数
   */
  setOnRewrittenCallback(callback: (text: string) => void): void;
}
