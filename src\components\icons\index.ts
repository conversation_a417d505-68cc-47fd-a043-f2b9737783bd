/**
 * SVG图标库导出文件
 * 文学创作主题图标集合
 */

export { default as IconBase } from './IconBase';
export type { IconBaseProps, IconSize } from './IconBase';

// 文学创作主题图标
export { default as FeatherPenIcon } from './FeatherPenIcon';
export { default as CharacterIcon } from './CharacterIcon';
export { default as BookIcon } from './BookIcon';
export { default as MapIcon } from './MapIcon';
export { default as BookmarkIcon } from './BookmarkIcon';

// 图标类型定义
export interface LiteraryIconProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
  animated?: boolean;
  onClick?: () => void;
}
