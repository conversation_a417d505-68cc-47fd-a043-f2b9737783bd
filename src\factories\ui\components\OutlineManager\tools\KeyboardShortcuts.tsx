"use client";

import React from 'react';

interface KeyboardShortcutsProps {
  showShortcuts: boolean;
}

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ showShortcuts }) => {
  if (!showShortcuts) return null;

  return (
    <div className="keyboard-shortcuts">
      <h3>键盘快捷键</h3>
      <ul>
        <li><kbd>Ctrl</kbd> + <kbd>X</kbd> - 切换剪刀模式</li>
        <li><kbd>Ctrl</kbd> + 右键拖动 - 使用剪刀删除连线</li>
        <li><kbd>Delete</kbd> - 删除选中的节点或边</li>
        <li><kbd>Ctrl</kbd> + <kbd>N</kbd> - 添加新根节点</li>
        <li><kbd>Enter</kbd> - 编辑选中的节点</li>
        <li><kbd>Esc</kbd> - 取消选择</li>
        <li><kbd>Alt</kbd> + <kbd>C</kbd> - 为选中节点添加子章节</li>
      </ul>
      <h3>鼠标操作</h3>
      <ul>
        <li>单击 - 选择节点</li>
        <li>双击 - 编辑节点</li>
        <li>右键 - 打开上下文菜单</li>
        <li>拖拽 - 移动节点</li>
        <li>滚轮 - 缩放画布</li>
      </ul>
    </div>
  );
};

export default KeyboardShortcuts;
