import { OutlineNodeType } from '../types/outline';

/**
 * 循环分配服务
 * 负责将卷纲的循环法模板分配给章节，建立卷纲与章节的关联关系
 */
export class CycleDistributionService {
  
  /**
   * 将卷纲的循环模板分配给章节
   * @param volumeNode 卷纲节点
   * @param chapterNodes 章节节点数组
   */
  async distributeCycleToChapters(
    volumeNode: OutlineNodeType, 
    chapterNodes: OutlineNodeType[]
  ): Promise<OutlineNodeType[]> {
    if (!volumeNode.cyclePhases || !volumeNode.cycleTemplate) {
      console.warn('卷纲缺少循环模板或阶段信息');
      return chapterNodes;
    }
    
    const phases = volumeNode.cyclePhases;
    const cycleLength = phases.length;
    
    console.log(`开始分配循环模板: ${volumeNode.cycleTemplate}, 阶段数: ${cycleLength}, 章节数: ${chapterNodes.length}`);
    
    const updatedChapters = chapterNodes.map((chapter, index) => {
      const phaseIndex = index % cycleLength;
      const cyclePhase = phases[phaseIndex];
      
      // 更新章节的循环信息
      const updatedChapter: OutlineNodeType = {
        ...chapter,
        cyclePhase: cyclePhase,
        phaseIndex: phaseIndex,
        volumeId: volumeNode.id,
        cycleTemplate: volumeNode.cycleTemplate,
        phaseGuidance: this.getPhaseGuidance(cyclePhase),
        phaseRequirements: this.getPhaseRequirements(cyclePhase)
      };
      
      console.log(`章节 ${index + 1}: ${chapter.title} -> 阶段: ${cyclePhase}`);
      
      return updatedChapter;
    });
    
    return updatedChapters;
  }
  
  /**
   * 获取循环阶段的写作指导
   * @param phase 循环阶段名称
   * @returns 写作指导文本
   */
  private getPhaseGuidance(phase: string): string {
    const guidanceMap: Record<string, string> = {
      '开篇建立': '建立场景、介绍角色、埋下伏笔，为后续冲突做铺垫。重点是世界观展示和角色登场。',
      '冲突发展': '引入矛盾、推进情节、加强张力，让读者产生期待。重点是矛盾激化和情节推进。',
      '高潮爽点': '解决冲突、释放张力、给予满足感，这是读者最期待的部分。重点是爽点释放和情感高潮。',
      '过渡转折': '承上启下、调整节奏、为下一循环做准备。重点是情节转折和节奏调整。',
      '起': '开始阶段，建立基础，引入要素。',
      '承': '承接发展，深化内容，推进情节。',
      '转': '转折变化，制造冲突，产生张力。',
      '合': '收束整合，解决问题，达成目标。',
      '续': '延续发展，为下一阶段做准备。'
    };
    
    return guidanceMap[phase] || `按照"${phase}"阶段的要求进行创作，注意该阶段的特点和目标。`;
  }
  
  /**
   * 获取循环阶段的具体要求
   * @param phase 循环阶段名称
   * @returns 要求列表
   */
  private getPhaseRequirements(phase: string): string[] {
    const requirementsMap: Record<string, string[]> = {
      '开篇建立': [
        '介绍主要角色和背景设定',
        '建立故事世界观和规则',
        '埋下关键伏笔和线索',
        '营造适当的氛围和基调'
      ],
      '冲突发展': [
        '引入主要矛盾或问题',
        '推进情节发展',
        '加强角色间的张力',
        '提升读者的期待感'
      ],
      '高潮爽点': [
        '解决核心冲突',
        '释放前期积累的张力',
        '提供读者期待的满足感',
        '展现角色成长或变化'
      ],
      '过渡转折': [
        '处理前期情节的后续',
        '调整故事节奏',
        '为下一循环埋下伏笔',
        '保持读者的阅读兴趣'
      ],
      '起': ['建立基础', '引入要素', '设定背景'],
      '承': ['承接发展', '深化内容', '推进情节'],
      '转': ['制造转折', '引入冲突', '产生张力'],
      '合': ['解决问题', '达成目标', '收束情节'],
      '续': ['延续发展', '过渡衔接', '准备下阶段']
    };
    
    return requirementsMap[phase] || [`完成"${phase}"阶段的相关任务`];
  }
  
  /**
   * 为单个新章节分配循环信息
   * @param volumeNode 父卷纲节点
   * @param chapterIndex 章节在卷中的索引
   * @returns 包含循环信息的章节数据
   */
  assignCycleToNewChapter(
    volumeNode: OutlineNodeType, 
    chapterIndex: number
  ): Partial<OutlineNodeType> {
    if (!volumeNode.cyclePhases || !volumeNode.cycleTemplate) {
      return {};
    }
    
    const phases = volumeNode.cyclePhases;
    const cycleLength = phases.length;
    const phaseIndex = chapterIndex % cycleLength;
    const cyclePhase = phases[phaseIndex];
    
    return {
      cyclePhase: cyclePhase,
      phaseIndex: phaseIndex,
      volumeId: volumeNode.id,
      cycleTemplate: volumeNode.cycleTemplate,
      phaseGuidance: this.getPhaseGuidance(cyclePhase),
      phaseRequirements: this.getPhaseRequirements(cyclePhase)
    };
  }
  
  /**
   * 检查章节是否已分配循环信息
   * @param chapter 章节节点
   * @returns 是否已分配
   */
  isChapterAssigned(chapter: OutlineNodeType): boolean {
    return !!(chapter.cyclePhase && chapter.volumeId);
  }
  
  /**
   * 获取循环阶段的显示颜色
   * @param phase 循环阶段名称
   * @returns CSS颜色类名
   */
  getPhaseColor(phase: string): string {
    const colorMap: Record<string, string> = {
      '开篇建立': 'bg-blue-100 text-blue-800 border-blue-200',
      '冲突发展': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      '高潮爽点': 'bg-red-100 text-red-800 border-red-200',
      '过渡转折': 'bg-green-100 text-green-800 border-green-200',
      '起': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      '承': 'bg-purple-100 text-purple-800 border-purple-200',
      '转': 'bg-orange-100 text-orange-800 border-orange-200',
      '合': 'bg-teal-100 text-teal-800 border-teal-200',
      '续': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    
    return colorMap[phase] || 'bg-gray-100 text-gray-800 border-gray-200';
  }
  
  /**
   * 获取循环阶段的图标
   * @param phase 循环阶段名称
   * @returns SVG图标路径
   */
  getPhaseIcon(phase: string): string {
    const iconMap: Record<string, string> = {
      '开篇建立': 'M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z',
      '冲突发展': 'M13 2L3 14h9l-.5 8L21 10h-9l.5-8z',
      '高潮爽点': 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
      '过渡转折': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'
    };
    
    return iconMap[phase] || 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z';
  }
}

// 导出单例实例
export const cycleDistributionService = new CycleDistributionService();
