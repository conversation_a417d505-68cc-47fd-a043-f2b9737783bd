"use client";

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Chapter } from '@/lib/db/dexie';
import { ExtractionMode } from './OutlineFrameworkExtractService';

interface ChapterSelectorProps {
  chapters: Chapter[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxSelection?: number;
  mode?: ExtractionMode;
  currentProcessingId?: string;
  processedIds?: string[];
  showRangeSelection?: boolean;
}

const ChapterSelector: React.FC<ChapterSelectorProps> = ({
  chapters,
  selectedIds,
  onSelectionChange,
  maxSelection = 10,
  mode = ExtractionMode.SUMMARY,
  currentProcessingId,
  processedIds = [],
  showRangeSelection = false
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);

  // 过滤章节
  const filteredChapters = chapters.filter(chapter =>
    chapter.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chapter.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chapter.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 切换选择状态（支持范围选择）
  const handleToggleSelection = useCallback((chapterId: string, event?: React.MouseEvent) => {
    const currentIndex = filteredChapters.findIndex(chapter => chapter.id === chapterId);

    // 范围选择逻辑（Shift + 点击）
    if (showRangeSelection && event?.shiftKey && lastSelectedIndex !== null && currentIndex !== -1) {
      const start = Math.min(lastSelectedIndex, currentIndex);
      const end = Math.max(lastSelectedIndex, currentIndex);
      const rangeIds = filteredChapters.slice(start, end + 1).map(chapter => chapter.id!).filter(id => id);

      // 合并范围选择的章节
      const newSelectedIds = Array.from(new Set([...selectedIds, ...rangeIds]));
      onSelectionChange(newSelectedIds.slice(0, maxSelection));
      setLastSelectedIndex(currentIndex);
      return;
    }

    // 普通选择逻辑
    const newSelectedIds = selectedIds.includes(chapterId)
      ? selectedIds.filter(id => id !== chapterId)
      : selectedIds.length < maxSelection
        ? [...selectedIds, chapterId]
        : selectedIds;

    onSelectionChange(newSelectedIds);
    setLastSelectedIndex(currentIndex);
  }, [selectedIds, onSelectionChange, maxSelection, filteredChapters, showRangeSelection, lastSelectedIndex]);

  // 全选/取消全选
  const handleSelectAll = useCallback(() => {
    if (selectedIds.length === filteredChapters.length) {
      onSelectionChange([]);
    } else {
      const allIds = filteredChapters.slice(0, maxSelection).map(chapter => chapter.id!).filter(id => id);
      onSelectionChange(allIds);
    }
  }, [selectedIds, filteredChapters, onSelectionChange, maxSelection]);

  // 获取章节状态
  const getChapterStatus = (chapterId: string) => {
    if (currentProcessingId === chapterId) return 'processing';
    if (processedIds.includes(chapterId)) return 'completed';
    if (selectedIds.includes(chapterId)) return 'selected';
    return 'unselected';
  };

  // 渲染章节项
  const renderChapterItem = (chapter: Chapter, level: number = 0) => {
    const isSelected = selectedIds.includes(chapter.id!);
    const canSelect = !isSelected && selectedIds.length < maxSelection;
    const status = getChapterStatus(chapter.id!);

    return (
      <motion.div
        key={chapter.id}
        className="chapter-item"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <div
          className={`
            flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200
            ${status === 'processing'
              ? 'bg-orange-100 border-2 border-orange-400 shadow-md'
              : status === 'completed'
                ? 'bg-green-100 border-2 border-green-300'
                : isSelected
                  ? 'bg-orange-50 border-2 border-orange-300'
                  : 'hover:bg-gray-50 border-2 border-transparent hover:border-gray-200'
            }
          `}
          style={{ marginLeft: `${level * 16}px` }}
          onClick={(e) => handleToggleSelection(chapter.id!, e)}
        >
          {/* 状态指示器 */}
          <div className="mr-3 flex items-center">
            {status === 'processing' && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-5 h-5 text-orange-500"
              >
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </motion.div>
            )}
            {status === 'completed' && (
              <div className="w-5 h-5 text-green-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            )}
            {(status === 'selected' || status === 'unselected') && (
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => {}}
                disabled={!canSelect && !isSelected}
                className="w-4 h-4 text-orange-600 rounded focus:ring-orange-500"
              />
            )}
          </div>

          {/* 章节信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              {/* 状态标签 */}
              <span className={`
                text-xs px-2 py-1 rounded font-medium
                ${status === 'processing'
                  ? 'bg-orange-100 text-orange-700'
                  : status === 'completed'
                    ? 'bg-green-100 text-green-700'
                    : 'bg-blue-100 text-blue-700'
                }
              `}>
                {status === 'processing' ? '分析中' : status === 'completed' ? '已完成' : '章节'}
              </span>

              {/* 标题 */}
              <h4 className={`
                font-medium truncate
                ${status === 'processing' ? 'text-orange-900' : status === 'completed' ? 'text-green-900' : 'text-gray-900'}
              `}>
                {chapter.title}
              </h4>

              {/* 字数信息 */}
              <span className="text-xs text-gray-500">
                {chapter.wordCount}字
              </span>

              {/* 顺序信息 */}
              <span className="text-xs text-gray-400">
                第{chapter.order}章
              </span>
            </div>

            {/* 摘要 */}
            {chapter.summary && (
              <p className={`
                text-sm mt-1 line-clamp-2
                ${status === 'processing' ? 'text-orange-700' : status === 'completed' ? 'text-green-700' : 'text-gray-600'}
              `}>
                {chapter.summary}
              </p>
            )}

            {/* 内容预览 */}
            {chapter.content && (
              <p className="text-xs text-gray-500 mt-1 line-clamp-1">
                {chapter.content.substring(0, 100)}...
              </p>
            )}

            {/* 处理状态提示 */}
            {status === 'processing' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-2 text-xs text-orange-600 bg-orange-50 rounded px-2 py-1"
              >
                正在分析此章节的创作技巧...
              </motion.div>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="chapter-selector">
      {/* 搜索框 */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索章节..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
          <svg 
            className="absolute left-3 top-2.5 w-5 h-5 text-gray-400"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSelectAll}
            className="text-sm text-orange-600 hover:text-orange-700 font-medium"
          >
            {selectedIds.length === filteredChapters.length ? '取消全选' : '全选'}
          </button>

          {showRangeSelection && (
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>按住Shift点击可选择范围</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          {/* 模式指示 */}
          <span className={`
            text-xs px-2 py-1 rounded font-medium
            ${mode === ExtractionMode.SINGLE
              ? 'bg-blue-100 text-blue-700'
              : mode === ExtractionMode.RANGE
                ? 'bg-purple-100 text-purple-700'
                : 'bg-gray-100 text-gray-700'
            }
          `}>
            {mode === ExtractionMode.SINGLE ? '单章节模式' : mode === ExtractionMode.RANGE ? '范围模式' : '总结模式'}
          </span>

          <span className="text-sm text-gray-500">
            已选择 {selectedIds.length}/{maxSelection}
          </span>
        </div>
      </div>

      {/* 章节列表 */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredChapters.length > 0 ? (
          filteredChapters.map(chapter => renderChapterItem(chapter))
        ) : (
          <div className="text-center py-8 text-gray-500">
            {searchQuery ? '未找到匹配的章节' : '暂无可选择的章节'}
          </div>
        )}
      </div>

      {/* 选择限制提示 */}
      {selectedIds.length >= maxSelection && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-700">
            已达到最大选择数量限制（{maxSelection}个）
          </p>
        </div>
      )}
    </div>
  );
};

export default ChapterSelector;
