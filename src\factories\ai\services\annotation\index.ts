/**
 * AI注释服务模块统一导出
 * 提供向后兼容的API接口
 */

// 主服务类和工厂函数
export { AIAnnotationService, createAIAnnotationService } from './AIAnnotationService';

// 类型定义
export type {
  TextSegment,
  Sentence,
  ModificationType,
  ProblemCategory,
  SeverityLevel,
  ImpactLevel,
  ProcessingStatus,
  Alternative,
  SentenceContext,
  AnnotationCallbacks,
  AnnotationResult,
  AISuggestionResponse,
  SuggestionItem,
  AIAnnotationServiceInterface,
  MessageBuilderInterface
} from './types/AnnotationTypes';

// 构建器
export { PromptBuilder } from './builders/PromptBuilder';

// 处理器
export { ResponseParser } from './processors/ResponseParser';
export { StreamProcessor } from './processors/StreamProcessor';

// 向后兼容的默认导出
import { createAIAnnotationService } from './AIAnnotationService';
export default createAIAnnotationService;
