/**
 * 书名生成相关类型定义
 */

/**
 * 关键词元素 - 具体的吸睛词汇
 */
export interface KeywordElement {
  id: string;
  text: string;           // 具体词汇，如"逆天"、"绝世"、"数字巨大"
  frequency: number;      // 使用频次
  hotness: number;        // 热度评分 (1-10)
  tags?: string[];        // 可选标签，如["玄幻", "热门"]
  createdAt: Date;
  lastUsedAt?: Date;
}

/**
 * 书名框架模板 - 吸睛的书名语言模式
 */
export interface TitleFramework {
  id: string;
  name: string;           // 模式名称，如"第X次模式"
  pattern: string;        // 语言模式，如"第{数字}次{动作}，{角色}{情绪}"
  description: string;    // 模式说明
  examples: string[];     // 成功案例
  variables: string[];    // 可用变量列表，如["数字", "动作", "角色", "情绪"]
  usageCount: number;
  effectiveness: number;  // 吸睛效果评分 (1-10)
  createdAt: Date;
  lastUsedAt?: Date;

  // 新增：简介分析相关字段（从简介分析中提取的框架会包含这些字段）
  writingTechniques?: Array<{
    id: string;
    name: string;
    category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
    description: string;
    examples: string[];
    techniqueType: string;
    effectiveness: number;
  }>;
  styleCharacteristics?: {
    layoutTechniques?: {
      paragraphStructure?: string[];
      lineBreakStrategy?: string[];
      rhythmControl?: string[];
      visualImpact?: number;
    };
    omissionAndEmphasis?: {
      omittedElements?: string[];
      emphasizedElements?: string[];
      contrastTechniques?: string[];
      suspensePoints?: string[];
    };
    coolPointLayout?: {
      primaryCoolPoints?: string[];
      coolPointTiming?: string[];
      coolPointIntensity?: number;
      anticipationBuilding?: string[];
    };
    creativeConcept?: {
      coreCreativity?: string[];
      conceptPresentation?: string[];
      uniquenessLevel?: number;
      marketAppeal?: string[];
    };
  };
  reusableTemplates?: Array<{
    id: string;
    name: string;
    pattern: string;
    description: string;
    techniques: string[];
    effectiveness: number;
  }>;
  techniqueAnalysisConfidence?: number;
}

/**
 * 生成的书名
 */
export interface BookTitle {
  id: string;
  title: string;
  aiScore: number;
  reason: string;
  extractedKeywords: string[];
  detectedFramework?: string;
  frameworkMatch?: TitleFramework;
  isFavorited: boolean;
  createdAt: Date;
}

/**
 * 书名生成参数
 */
export interface TitleGenerationParams {
  keywords: string[];
  framework?: TitleFramework;
  customFramework?: string;
  userRequirements?: string;
}

/**
 * 书名生成回调函数
 */
export interface TitleGenerationCallbacks {
  onStart?: () => void;
  onProgress?: (progress: number) => void;
  onTitleGenerated?: (title: BookTitle) => void;
  onComplete?: (titles: BookTitle[]) => void;
  onError?: (error: Error) => void;
}

/**
 * 书名生成结果
 */
export interface TitleGenerationResult {
  titles: BookTitle[];
  success: boolean;
  error?: string;
  totalGenerated: number;
  averageScore: number;
}

/**
 * AI书名生成响应
 */
export interface AITitleResponse {
  titles: Array<{
    title: string;
    score: number;
    reason: string;
    keywords?: string[];
    framework?: string;
  }>;
}

/**
 * 书名生成服务接口
 */
export interface BookTitleGenerationServiceInterface {
  /**
   * 生成书名
   */
  generateTitles(
    params: TitleGenerationParams,
    callbacks: TitleGenerationCallbacks
  ): Promise<TitleGenerationResult>;
}

/**
 * 预置热门关键词库 - 现代网文常用创意词汇
 */
export const PRESET_KEYWORDS: Omit<KeywordElement, 'id' | 'frequency' | 'createdAt' | 'lastUsedAt'>[] = [
  // 开局流关键词
  { text: '开局', hotness: 10, tags: ['开局流', '热门'] },
  { text: '拐跑', hotness: 9, tags: ['开局流', '震撼'] },
  { text: '退婚', hotness: 9, tags: ['开局流', '经典'] },
  { text: '觉醒', hotness: 8, tags: ['开局流', '转折'] },

  // 权威反应类
  { text: '急眼了', hotness: 10, tags: ['情绪', '网络用语'] },
  { text: '人麻了', hotness: 9, tags: ['情绪', '网络用语'] },
  { text: '傻眼', hotness: 8, tags: ['情绪', '惊讶'] },
  { text: '后悔了', hotness: 8, tags: ['情绪', '反转'] },

  // 时间跨度类
  { text: '挂机百年', hotness: 9, tags: ['时间', '积累'] },
  { text: '闭关万年', hotness: 8, tags: ['时间', '修炼'] },
  { text: '沉睡千年', hotness: 7, tags: ['时间', '觉醒'] },
  { text: '重活一世', hotness: 8, tags: ['时间', '重生'] },

  // 身份颠覆类
  { text: '刚成', hotness: 8, tags: ['身份', '转变'] },
  { text: '竟是', hotness: 7, tags: ['身份', '反转'] },
  { text: '原来是', hotness: 7, tags: ['身份', '揭秘'] },
  { text: '居然是', hotness: 7, tags: ['身份', '意外'] },

  // 实力展示类
  { text: '拥兵百万', hotness: 9, tags: ['实力', '具体'] },
  { text: '手握三军', hotness: 8, tags: ['实力', '权力'] },
  { text: '一剑', hotness: 8, tags: ['实力', '简洁'] },
  { text: '一拳', hotness: 7, tags: ['实力', '直接'] },

  // 态度反转类
  { text: '凭什么', hotness: 9, tags: ['态度', '反抗'] },
  { text: '让我道歉？', hotness: 9, tags: ['态度', '质疑'] },
  { text: '不可能', hotness: 7, tags: ['态度', '否定'] },
  { text: '我拒绝', hotness: 8, tags: ['态度', '坚决'] },

  // 现代网文背景
  { text: '洪荒', hotness: 9, tags: ['背景', '洪荒流'] },
  { text: '穿书', hotness: 9, tags: ['背景', '穿书流'] },
  { text: '女频', hotness: 8, tags: ['背景', '性转'] },
  { text: '蜀山', hotness: 7, tags: ['背景', '修仙'] }
];

/**
 * 预设框架模板 - 吸睛的书名语言模式
 */
export const PRESET_FRAMEWORKS: Omit<TitleFramework, 'usageCount' | 'createdAt' | 'lastUsedAt'>[] = [
  {
    id: 'nth-time-pattern',
    name: '第X次模式',
    pattern: '第{数字}次{动作}，{角色}{情绪}',
    description: '经典的重复冲突模式，强调次数和情绪爆发',
    examples: ['第99次成嫌疑犯，警花老婆崩溃', '第108次拒绝表白，校花急眼了'],
    variables: ['数字', '动作', '角色', '情绪'],
    effectiveness: 9
  },
  {
    id: 'opening-pattern',
    name: '开局模式',
    pattern: '开局{动作}，{角色}{情绪}',
    description: '开局即高潮的模式，立即制造冲突',
    examples: ['开局拐跑家族帝兵，老祖急眼了', '开局退婚，未婚妻后悔了'],
    variables: ['动作', '角色', '情绪'],
    effectiveness: 10
  },
  {
    id: 'honghuang-pattern',
    name: '洪荒模式',
    pattern: '洪荒：{身份设定}，{冲突事件}',
    description: '洪荒背景的身份冲突模式',
    examples: ['洪荒：刚成尸祖，你让我骂醒女娲', '洪荒：我是龙族叛徒，竟成了圣人'],
    variables: ['身份设定', '冲突事件'],
    effectiveness: 8
  },

];
