"use client";

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  NodeTypes,
  EdgeTypes,
  Node,
  Edge,
  NodeChange,
  EdgeChange,
  ConnectionLineType,
  Panel,
  Connection,
  addEdge,
  useNodesState,
  useEdgesState,
  SelectionMode,
  useReactFlow,
  ReactFlowProvider,
  Viewport,
  NodePositionChange,
} from 'reactflow';
import 'reactflow/dist/style.css';
import './OutlineMenu.css'; // 导入菜单样式
import './OutlineCanvas.css'; // 导入画布样式（性能优化）
import './OutlineCanvasLayout.css'; // 导入布局样式
import { Outline, OutlineNodeType } from '../../types/outline';
import OutlineCanvasNode from './OutlineCanvasNode';
import OutlineCanvasEdge from './OutlineCanvasEdge';
import CustomEdge from './edges/CustomEdge';
import OutlineCanvasErrorBoundary from './OutlineCanvasErrorBoundary';
import InlineNodeDetailsPanel from './InlineNodeDetailsPanel';
import PreviewNode from './PreviewNode';
import GeneratingNode from './GeneratingNode';

// 导入样式管理系统
import { StyleProvider, useStyle } from './styles/StyleContext';
import StyleControlPanel from './styles/StyleControlPanel';

// 导入拆分的工具组件
import CanvasMenu from './tools/CanvasMenu';
import ContextMenuManager from './tools/ContextMenuManager';
import KeyboardShortcuts from './tools/KeyboardShortcuts';
import DeleteConfirmDialog from './DeleteConfirmDialog';
import BatchOperationToolbar from './BatchOperationToolbar';
import BatchDeleteConfirmDialog from './BatchDeleteConfirmDialog';
import AssistantDrawer from './AssistantDrawer';
import OutlineFrameworkExtractDialog from './OutlineFrameworkExtractDialog';
import ChapterAnalysisDialog from './ChapterAnalysisDialog';
import { ExampleManagementDialog } from './assistant/components/ExampleManagementDialog';
import FloatingActionGroup from './FloatingActionGroup';

// Dummy showNotification if not globally available or for testing
const showNotification = (message: string, type: string, duration: number, position: string) => {
  console.log(`Notification (${type} @${position}, ${duration}ms): ${message}`);
};

// 节点类型中文映射函数
const getNodeTypeLabel = (type: string): string => {
  switch (type) {
    case 'volume':
      return '总纲/卷';
    case 'event':
      return '事件刚';
    case 'chapter':
      return '章节';
    case 'plot':
      return '剧情节点';
    case 'dialogue':
      return '对话节点';
    case 'synopsis':
      return '核心故事梗概';
    default:
      return type || '未知';
  }
};

import {
  generateUniqueNodeId,
  findNode,
  addChildNode,
  deleteNodeAndGetChildren,
  deepCopy
} from '../../utils/outlineUtils';
import { LayoutConfig } from './tools/LayoutControlPanel';
import { computeLayout, LayoutResult } from '../../utils/layoutAlgorithms';
import { animateLayoutChange, AnimationConfig, NodePositions } from '../../utils/layoutAnimationController';
import { LayoutHistoryManager } from '../../utils/layoutHistoryManager';
import { debounce } from '../../utils/debounce';
import { DragGrid, applyMagneticSnapping } from './DragGrid';

interface OutlineCanvasProps {
  outline: Outline | null;
  bookId?: string;
  onChange: (updatedOutline: Outline) => void;
  // 新增选择模式相关props
  selectionMode?: boolean;
  selectedNodeIds?: string[];
  onNodeSelectionChange?: (nodeId: string, selected: boolean) => void;
  // 新增ACE框架相关props
  selectedFramework?: any;
  selectedFrameworks?: any[];
  // 🔥 新增：AI助手中用户选择的章节ID
  selectedChapterIds?: string[];
}

const nodeTypes: NodeTypes = {
  outlineNode: OutlineCanvasNode,
  previewNode: PreviewNode,
  generatingNode: GeneratingNode
};
const edgeTypes: EdgeTypes = {
  outlineEdge: OutlineCanvasEdge,
  customEdge: CustomEdge
};

/**
 * 检测两条线段是否相交，带容错范围
 */
function segmentsIntersect(
  ax1: number, ay1: number, ax2: number, ay2: number,
  bx1: number, by1: number, bx2: number, by2: number,
  tolerance: number = 5
): boolean {
  // 添加容错范围，扩大边界框检查
  if (Math.max(ax1, ax2) + tolerance < Math.min(bx1, bx2) - tolerance ||
      Math.min(ax1, ax2) - tolerance > Math.max(bx1, bx2) + tolerance ||
      Math.max(ay1, ay2) + tolerance < Math.min(by1, by2) - tolerance ||
      Math.min(ay1, ay2) - tolerance > Math.max(by1, by2) + tolerance) {
    return false;
  }

  // 计算行列式
  const det = (ax2 - ax1) * (by2 - by1) - (ay2 - ay1) * (bx2 - bx1);

  // 如果行列式接近0，线段可能平行，但我们仍然检查是否足够接近
  if (Math.abs(det) < 0.001) {
    // 检查线段是否共线且重叠
    // 计算点到线段的距离
    const distanceToSegment = (px: number, py: number, x1: number, y1: number, x2: number, y2: number) => {
      const A = px - x1;
      const B = py - y1;
      const C = x2 - x1;
      const D = y2 - y1;

      const dot = A * C + B * D;
      const lenSq = C * C + D * D;
      let param = -1;

      if (lenSq !== 0) param = dot / lenSq;

      let xx, yy;

      if (param < 0) {
        xx = x1;
        yy = y1;
      } else if (param > 1) {
        xx = x2;
        yy = y2;
      } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
      }

      const dx = px - xx;
      const dy = py - yy;

      return Math.sqrt(dx * dx + dy * dy);
    };

    // 检查第二条线段的端点是否接近第一条线段
    if (distanceToSegment(bx1, by1, ax1, ay1, ax2, ay2) <= tolerance ||
        distanceToSegment(bx2, by2, ax1, ay1, ax2, ay2) <= tolerance) {
      return true;
    }

    // 检查第一条线段的端点是否接近第二条线段
    if (distanceToSegment(ax1, ay1, bx1, by1, bx2, by2) <= tolerance ||
        distanceToSegment(ax2, ay2, bx1, by1, bx2, by2) <= tolerance) {
      return true;
    }

    return false;
  }

  // 计算交点参数
  const lambda = ((by2 - by1) * (bx2 - ax1) + (bx1 - bx2) * (by2 - ay1)) / det;
  const gamma = ((ay1 - ay2) * (bx2 - ax1) + (ax2 - ax1) * (by2 - ay1)) / det;

  // 检查交点是否在线段上，添加容错范围
  return (0 - tolerance/100 <= lambda && lambda <= 1 + tolerance/100) &&
         (0 - tolerance/100 <= gamma && gamma <= 1 + tolerance/100);
}

const OutlineCanvas: React.FC<OutlineCanvasProps> = ({
  outline,
  bookId,
  onChange,
  selectionMode = false,
  selectedNodeIds = [],
  onNodeSelectionChange,
  selectedFramework,
  selectedFrameworks,
  selectedChapterIds = [] // 🔥 新增：接收用户选择的章节ID
}) => {
  const [nodes, setNodes, onNodesChangeReactFlow] = useNodesState([]);
  const [edges, setEdges, onEdgesChangeReactFlow] = useEdgesState([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [viewingNode, setViewingNode] = useState<OutlineNodeType | null>(null);
  const [layoutMode, setLayoutMode] = useState<'tree' | 'force' | 'radial' | 'grid' | 'horizontal' | 'vertical'>('tree');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [originalPositions, setOriginalPositions] = useState<NodePositions>({});

  // 删除确认弹窗状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<OutlineNodeType | null>(null);

  // 批量操作状态
  const [batchDeleteConfirmOpen, setBatchDeleteConfirmOpen] = useState(false);
  const [nodesToBatchDelete, setNodesToBatchDelete] = useState<OutlineNodeType[]>([]);

  // 在组件级别获取样式上下文
  const styleContext = useStyle();

  // 默认布局配置
  const [currentLayoutConfig, setCurrentLayoutConfig] = useState<LayoutConfig>({
    type: 'tree',
    nodeSpacing: 50,
    levelSpacing: 100,
    compactness: 0.8,
    direction: 'TB',
    alignSiblings: true,
    centerContent: true
  });

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const layoutHistoryManager = useRef(new LayoutHistoryManager()).current;
  const { getViewport } = useReactFlow();

  const [rfSelectedNodes, setRfSelectedNodes] = useState<Node[]>([]);
  const [rfSelectedEdges, setRfSelectedEdges] = useState<Edge[]>([]);

  // 剪刀模式状态 - 将在ScissorsTool组件中使用
  const [scissorsModeActive, setScissorsModeActive] = useState<boolean>(false);
  const [scissorsLineStart, setScissorsLineStart] = useState<{ x: number; y: number } | null>(null);
  const [scissorsLineEnd, setScissorsLineEnd] = useState<{ x: number; y: number } | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);

  // 上下文菜单状态 - 将在ContextMenuManager组件中使用
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    type: 'node' | 'pane' | 'selection';
    nodeId?: string;
  } | null>(null);

  // 快捷键提示状态 - 将在KeyboardShortcuts组件中使用
  const [showShortcuts, setShowShortcuts] = useState(false);

  // AI助手状态
  const [isAssistantOpen, setIsAssistantOpen] = useState(false);
  const [assistantButtonPosition, setAssistantButtonPosition] = useState<{ x: number; y: number } | null>(null);
  const [highlightedNodeIds, setHighlightedNodeIds] = useState<Set<string>>(new Set());

  // 框架提取功能状态
  const [isFrameworkExtractOpen, setIsFrameworkExtractOpen] = useState(false);
  const [frameworkExtractButtonPosition, setFrameworkExtractButtonPosition] = useState<{ x: number; y: number } | null>(null);

  // 章节分析功能状态
  const [isChapterAnalysisOpen, setIsChapterAnalysisOpen] = useState(false);
  const [chapterAnalysisButtonPosition, setChapterAnalysisButtonPosition] = useState<{ x: number; y: number } | null>(null);

  // 示例管理功能状态
  const [isExampleManagementOpen, setIsExampleManagementOpen] = useState(false);

  // 预览节点状态
  const [previewNodes, setPreviewNodes] = useState<OutlineNodeType[]>([]);
  const [selectedPreviewNodeIds, setSelectedPreviewNodeIds] = useState<Set<string>>(new Set());

  // 🔥 新增：AI助手中用户选择的章节ID状态 - 使用localStorage持久化
  const [assistantSelectedChapterIds, setAssistantSelectedChapterIds] = useState<string[]>(() => {
    if (!bookId) return [];
    try {
      const saved = localStorage.getItem(`assistant-selected-chapters-${bookId}`);
      const result = saved ? JSON.parse(saved) : [];
      console.log('🔍 OutlineCanvas初始化：从localStorage加载章节ID:', result);
      return result;
    } catch (error) {
      console.warn('⚠️ 加载保存的章节选择失败:', error);
      return [];
    }
  });

  // 🔥 新增：章节选择状态持久化
  useEffect(() => {
    if (bookId && assistantSelectedChapterIds.length >= 0) { // 允许空数组也保存
      try {
        localStorage.setItem(`assistant-selected-chapters-${bookId}`, JSON.stringify(assistantSelectedChapterIds));
        console.log('🔍 OutlineCanvas保存章节选择到localStorage:', assistantSelectedChapterIds);
      } catch (error) {
        console.warn('⚠️ 保存章节选择失败:', error);
      }
    }
  }, [assistantSelectedChapterIds, bookId]);

  // 🔥 新增：稳定的章节选择回调函数
  const handleSelectedChapterIdsChange = useCallback((chapterIds: string[]) => {
    console.log('🔍 OutlineCanvas接收章节选择变化:', chapterIds);
    setAssistantSelectedChapterIds(chapterIds);
  }, []);

  // 🔥 新增：节点折叠状态管理
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(() => {
    if (!bookId) return new Set<string>();
    try {
      const saved = localStorage.getItem(`collapsed-nodes-${bookId}`);
      const result = saved ? new Set<string>(JSON.parse(saved)) : new Set<string>();
      console.log('🔍 OutlineCanvas初始化：从localStorage加载折叠状态:', Array.from(result));
      return result;
    } catch (error) {
      console.warn('⚠️ 加载保存的折叠状态失败:', error);
      return new Set<string>();
    }
  });

  // 🔥 新增：自动折叠阈值
  const [autoCollapseThreshold] = useState(5);

  // 🔥 新增：折叠状态持久化
  useEffect(() => {
    if (bookId && collapsedNodes.size >= 0) { // 允许空Set也保存
      try {
        localStorage.setItem(`collapsed-nodes-${bookId}`, JSON.stringify(Array.from(collapsedNodes)));
        console.log('🔍 OutlineCanvas保存折叠状态到localStorage:', Array.from(collapsedNodes));
      } catch (error) {
        console.warn('⚠️ 保存折叠状态失败:', error);
      }
    }
  }, [collapsedNodes, bookId]);

  // 🔥 新增：用于跟踪上一次的折叠状态，检测折叠状态变化
  const prevCollapsedNodesRef = useRef<Set<string>>(new Set());

  // 🔥 新增：切换节点折叠状态
  const toggleNodeCollapse = useCallback((nodeId: string) => {
    setCollapsedNodes(prev => {
      const newSet = new Set(prev);
      const wasCollapsed = newSet.has(nodeId);

      if (wasCollapsed) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }

      return newSet;
    });
  }, []); // 🔥 修复：移除collapsedNodes依赖，使用函数式更新确保状态正确

  // 🔥 新增：批量折叠/展开操作
  const toggleAllNodesCollapse = useCallback((collapse: boolean) => {
    if (!outline) return;

    const allNodeIds = new Set<string>();
    const collectNodeIds = (nodes: OutlineNodeType[]) => {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          allNodeIds.add(node.id);
        }
        if (node.children) collectNodeIds(node.children);
      });
    };
    collectNodeIds(outline.nodes);

    setCollapsedNodes(collapse ? allNodeIds : new Set<string>());
  }, [outline]);

  // 🔥 新增：计算节点统计信息
  const calculateNodeStats = useCallback((node: OutlineNodeType): {
    childrenCount: number;
    collapsedChildrenTypes: { [key: string]: number }
  } => {
    if (!node.children || node.children.length === 0) {
      return { childrenCount: 0, collapsedChildrenTypes: {} };
    }

    const stats = { childrenCount: node.children.length, collapsedChildrenTypes: {} as { [key: string]: number } };

    node.children.forEach(child => {
      const type = child.type;
      stats.collapsedChildrenTypes[type] = (stats.collapsedChildrenTypes[type] || 0) + 1;
    });

    return stats;
  }, []);

  // 拖拽状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState<{ x: number; y: number } | null>(null);

  // 框架信息状态管理 - 从localStorage读取
  const [localSelectedFramework, setLocalSelectedFramework] = useState<any>(null);
  const [localSelectedFrameworks, setLocalSelectedFrameworks] = useState<any[]>([]);

  // 从localStorage加载框架信息
  useEffect(() => {
    if (bookId) {
      try {
        // 优先加载多选框架
        const savedFrameworks = localStorage.getItem(`outline-ai-frameworks-${bookId}`);
        if (savedFrameworks) {
          const frameworks = JSON.parse(savedFrameworks);
          setLocalSelectedFrameworks(frameworks);
          setLocalSelectedFramework(frameworks.length > 0 ? frameworks[0] : null);
          console.log('✅ OutlineCanvas加载多选框架:', frameworks.map((f: any) => f.frameworkName).join(', '));
        } else {
          // 回退到单选框架
          const savedFramework = localStorage.getItem(`outline-ai-framework-${bookId}`);
          if (savedFramework) {
            const framework = JSON.parse(savedFramework);
            setLocalSelectedFramework(framework);
            setLocalSelectedFrameworks([framework]);
            console.log('✅ OutlineCanvas加载单选框架:', framework.frameworkName);
          }
        }
      } catch (error) {
        console.error('OutlineCanvas加载框架信息失败:', error);
      }
    }
  }, [bookId]);

  // 位置合并函数：将保存的位置信息合并到当前大纲数据中
  const mergePositions = useCallback((currentOutline: Outline, savedOutline: Outline): Outline => {
    const mergedOutline = deepCopy(currentOutline);

    const mergeNodePositions = (currentNodes: OutlineNodeType[], savedNodes: OutlineNodeType[]): OutlineNodeType[] => {
      return currentNodes.map(currentNode => {
        const savedNode = savedNodes.find(saved => saved.id === currentNode.id);
        const mergedNode = { ...currentNode };

        // 如果找到保存的位置信息，则使用保存的位置
        if (savedNode && savedNode.position &&
            typeof savedNode.position.x === 'number' &&
            typeof savedNode.position.y === 'number') {
          mergedNode.position = { ...savedNode.position };
        } else {
        }

        // 递归处理子节点
        if (currentNode.children && savedNode?.children) {
          mergedNode.children = mergeNodePositions(currentNode.children, savedNode.children);
        }

        return mergedNode;
      });
    };

    mergedOutline.nodes = mergeNodePositions(mergedOutline.nodes, savedOutline.nodes);
    return mergedOutline;
  }, []);

  // 位置恢复标志，避免重复恢复
  const isPositionRestored = useRef(false);

  // 初始化时从localStorage恢复位置信息
  useEffect(() => {
    if (outline && bookId && !isPositionRestored.current) {
      try {
        const savedPositions = localStorage.getItem(`outline-positions-${bookId}`);
        if (savedPositions) {
          const savedOutline = JSON.parse(savedPositions);
       
          const mergedOutline = mergePositions(outline, savedOutline);

          // 检查是否有位置信息被恢复
          const hasPositionChanges = JSON.stringify(outline) !== JSON.stringify(mergedOutline);
          if (hasPositionChanges) {
      
            isPositionRestored.current = true;
            onChange(mergedOutline);
          } else {

          }
        } else {

        }
      } catch (error) {
        console.error('❌ 恢复位置信息失败:', error);
      }
    }
  }, [outline?.id, bookId, mergePositions, onChange]);

  // 当bookId变化时重置位置恢复标志
  useEffect(() => {
    isPositionRestored.current = false;
  }, [bookId]);

  // AI助手状态更新函数的引用，用于反向同步
  const assistantToggleChangeRef = useRef<((nodeId: string, selected: boolean) => void) | null>(null);

  // AI助手状态清理函数的引用
  const assistantClearStateRef = useRef<(() => void) | null>(null);

  // 处理从画布直接创建预览节点
  const handleCreateSelectedPreviewNodes = useCallback(() => {
    if (selectedPreviewNodeIds.size === 0) return;

    // 创建节点变更数组 - 使用正确的类型，包含creativeNotes
    const changes = previewNodes
      .filter(node => selectedPreviewNodeIds.has(node.id))
      .map(node => ({
        type: 'create' as const,
        nodeId: node.id,
        parentId: node.parentId || null,
        data: {
          title: node.title,
          description: node.description,
          creativeNotes: node.creativeNotes,
          type: node.type
        }
      }));

    if (changes.length > 0) {
      // 直接应用变更
      handleAssistantApplyChanges(changes);

      // 清理画布预览节点状态
      setPreviewNodes([]);
      setSelectedPreviewNodeIds(new Set());

      // 同步清理AI助手的状态
      if (assistantClearStateRef.current) {
        assistantClearStateRef.current();
      }

      showNotification(`✨ 已成功创建 ${changes.length} 个节点！`, 'success', 3000, 'top-center');
    }
  }, [selectedPreviewNodeIds, previewNodes]);

  // 处理预览节点选择切换
  const handlePreviewNodeToggle = useCallback((nodeId: string, selected: boolean) => {
    setSelectedPreviewNodeIds(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(nodeId);
      } else {
        newSet.delete(nodeId);
      }


      // 同步到AI助手面板
      if (assistantToggleChangeRef.current) {
        assistantToggleChangeRef.current(nodeId, selected);
      }

      return newSet;
    });
  }, []);

  // 处理从画布到AI助手的预览节点选择同步
  const handlePreviewNodeToggleFromCanvas = useCallback((nodeId: string, selected: boolean) => {
    // 这个函数将被传递给AssistantDrawer，用于从画布同步选择状态到AI助手面板
    // 这里可以添加具体的同步逻辑
  }, []);

  // 注册AI助手的状态切换函数
  const handleRegisterToggleFunction = useCallback((toggleFn: (nodeId: string, selected: boolean) => void) => {
    assistantToggleChangeRef.current = toggleFn;
  }, []);

  // 注册AI助手的状态清理函数
  const handleRegisterClearFunction = useCallback((clearFn: () => void) => {
    assistantClearStateRef.current = clearFn;
  }, []);

  // 相关项映射
  const [relatedItemsMap] = useState<{
    characters: Record<string, string>;
    worldBuilding: Record<string, string>;
    terminology: Record<string, string>;
  }>({ characters: {}, worldBuilding: {}, terminology: {} });

  // Stable transform function
  const transformOutlineToFlow = useCallback((currentOutline: Outline | null, currentSelectedNodeId: string | null, handlers: any) => {
    if (!currentOutline) return { nodes: [], edges: [] };
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];
    const nodeMap = new Map<string, OutlineNodeType>();

    // 创建所有节点的扁平数组，用于传递给VolumeEditor
    const allNodes: OutlineNodeType[] = [];
    const collectAllNodes = (nodesToCollect: OutlineNodeType[]) => {
      nodesToCollect.forEach(node => {
        allNodes.push(node);
        if (node.children) collectAllNodes(node.children);
      });
    };
    collectAllNodes(currentOutline.nodes);

    const buildNodeMap = (nodesToMap: OutlineNodeType[]) => {
      nodesToMap.forEach(node => {
        nodeMap.set(node.id, node);
        if (node.children) buildNodeMap(node.children);
      });
    };
    buildNodeMap(currentOutline.nodes);

    // 处理所有节点，创建节点和父子关系边
    const processNode = (node: OutlineNodeType, parentId: string | null = null, level = 0, siblingIndex = 0) => {
      const isSelected = node.id === currentSelectedNodeId;
      const isCollapsed = collapsedNodes.has(node.id);

      // 🔥 新增：计算节点统计信息
      const nodeStats = calculateNodeStats(node);

      // 优先使用保存的位置，如果没有则使用默认计算位置
      // 注意：必须检查position对象是否存在且包含有效的x,y坐标，而不是使用||操作符
      // 因为{x: 0, y: 0}是有效位置但会被||操作符误判为falsy
      let nodePosition;

      // 🔥 特别处理新创建的节点（AI生成的节点）
      if (node.isNew && node.position &&
          typeof node.position.x === 'number' &&
          typeof node.position.y === 'number') {
        nodePosition = { ...node.position };
      } else if (node.position &&
          typeof node.position.x === 'number' &&
          typeof node.position.y === 'number') {
        nodePosition = { ...node.position };
      } else {
        nodePosition = { x: siblingIndex * 300, y: level * 150 };
      }

      flowNodes.push({
        id: node.id, type: 'outlineNode',
        position: nodePosition,
        data: {
          ...node,
          selected: isSelected,
          ...handlers,
          level,
          siblingIndex,
          // 新增选择模式相关数据
          selectionMode,
          isNodeSelected: selectedNodeIds.includes(node.id),
          onNodeSelectionChange,
          // 传递所有节点给VolumeEditor使用
          allNodes,
          // 🔥 新增：折叠相关数据
          isCollapsed,
          childrenCount: nodeStats.childrenCount,
          collapsedChildrenTypes: nodeStats.collapsedChildrenTypes,
          onToggleCollapse: toggleNodeCollapse,
          // 🔥 新增：ACE框架数据传递
          availableFrameworks: selectedFrameworks || localSelectedFrameworks || []
        },
        style: { zIndex: isSelected ? 10 : 1 },
      });

      // 如果有父节点，创建父子关系边
      if (parentId) {
        const parentNode = nodeMap.get(parentId);
        // 获取边样式
        let edgeStyle;
        try {
          // 使用组件级别获取的样式上下文
          edgeStyle = styleContext.getEdgeStyle('parent-child');
        } catch (error) {
          console.error('获取边样式时出错:', error);
          // 默认样式
          edgeStyle = {
            type: 'bezier',
            strokeWidth: 2,
            strokeColor: '#3b82f6',
            strokeOpacity: 0.8,
            strokeDasharray: '',
            arrowHeadType: 'arrow',
            animated: false
          };
        }

        flowEdges.push({
          id: `e-parent-${parentId}-${node.id}`,
          source: parentId,
          target: node.id,
          type: 'customEdge',
          style: {
            stroke: edgeStyle.strokeColor,
            strokeWidth: edgeStyle.strokeWidth,
            strokeDasharray: edgeStyle.strokeDasharray,
            opacity: edgeStyle.strokeOpacity,
          },
          data: {
            nodeType: node.type,
            relationshipType: 'parent-child',
            sourceNodeType: parentNode?.type,
            targetNodeType: node.type,
            isNew: node.isNew,
            edgeStyle: edgeStyle
          }
        });
      }

      // 🔥 新增：递归处理子节点，但如果当前节点折叠则跳过子节点
      if (node.children && !isCollapsed) {
        node.children.forEach((child, childIdx) => {
          processNode(child, node.id, level + 1, childIdx);
        });
      }
    };

    // 处理所有节点
    currentOutline.nodes.forEach((node, idx) => {
      processNode(node, null, 0, idx);
    });

    // 单独处理兄弟关系边，基于nextSiblingId和prevSiblingId字段
    const processNodeSiblings = (node: OutlineNodeType) => {
      // 如果有下一个兄弟节点，创建兄弟关系边
      if (node.nextSiblingId) {
        const nextSibling = nodeMap.get(node.nextSiblingId);
        if (nextSibling) {
          // 获取边样式
          let edgeStyle;
          try {
            // 使用组件级别获取的样式上下文
            edgeStyle = styleContext.getEdgeStyle('association');
          } catch (error) {
            console.error('获取边样式时出错:', error);
            // 默认样式
            edgeStyle = {
              type: 'bezier',
              strokeWidth: 1.5,
              strokeColor: '#6b7280',
              strokeOpacity: 0.6,
              strokeDasharray: '5,5',
              arrowHeadType: 'arrow',
              animated: false
            };
          }

          flowEdges.push({
            id: `e-sibling-${node.id}-${node.nextSiblingId}`,
            source: node.id,
            target: node.nextSiblingId,
            type: 'customEdge',
            style: {
              stroke: edgeStyle.strokeColor,
              strokeWidth: edgeStyle.strokeWidth,
              strokeDasharray: edgeStyle.strokeDasharray,
              opacity: edgeStyle.strokeOpacity,
            },
            data: {
              nodeType: 'sibling',
              relationshipType: 'association',
              sourceNodeType: node.type,
              targetNodeType: nextSibling.type,
              isNew: node.isNew || nextSibling.isNew,
              edgeStyle: edgeStyle
            }
          });
        }
      }

      // 递归处理子节点的兄弟关系
      if (node.children) {
        node.children.forEach(child => {
          processNodeSiblings(child);
        });
      }
    };

    // 处理所有节点的兄弟关系
    currentOutline.nodes.forEach(node => {
      processNodeSiblings(node);
    });

    // 添加预览节点
    previewNodes.forEach((previewNode, index) => {
      const isSelected = selectedPreviewNodeIds.has(previewNode.id);

      // 计算预览节点位置 - 放在画布右侧
      const previewPosition = previewNode.position || {
        x: 800 + (index % 3) * 300,
        y: 100 + Math.floor(index / 3) * 200
      };

      flowNodes.push({
        id: `preview-${previewNode.id}`,
        type: 'previewNode',
        position: previewPosition,
        data: {
          ...previewNode,
          isSelected,
          onToggleSelect: handlePreviewNodeToggle
        },
        style: { zIndex: 5 },
        draggable: false, // 预览节点不可拖拽
      });
    });

    return { nodes: flowNodes, edges: flowEdges };
  }, [styleContext, previewNodes, selectedPreviewNodeIds, handlePreviewNodeToggle, selectionMode, selectedNodeIds, onNodeSelectionChange, collapsedNodes, calculateNodeStats, toggleNodeCollapse]);

  const handleNodeSelect = useCallback((nodeId: string | null) => {
    setSelectedNodeId(prev => (nodeId === prev ? prev : nodeId));
    if (nodeId && outline) {
      const found = findNode(outline.nodes, nodeId);
      setViewingNode(found || null);
    } else {
      setViewingNode(null);
    }
  }, [outline, setSelectedNodeId, setViewingNode]);

  const handleNodeEdit = useCallback((nodeId: string) => {

    // 设置选中的节点ID
    setSelectedNodeId(nodeId);

    // 查找节点数据
    if (outline) {
      const nodeData = findNode(outline.nodes, nodeId);
      if (nodeData) {

        setViewingNode(nodeData);

        // 显示成功提示，告知用户编辑面板已打开
        showNotification(`正在编辑节点: ${nodeData.title}`, 'info', 2000, 'top-center');
      } else {
        console.error('❌ 未找到节点数据，节点ID:', nodeId);
        showNotification('未找到节点数据', 'error', 3000, 'top-center');
      }
    }
  }, [setSelectedNodeId, outline, setViewingNode]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    // 找到要删除的节点数据
    const nodeToDeleteData = findNode(outline?.nodes || [], nodeId);
    if (!nodeToDeleteData) return;

    // 设置删除确认弹窗状态
    setNodeToDelete(nodeToDeleteData);
    setDeleteConfirmOpen(true);
  }, [outline]);

  // 处理确认删除
  const handleConfirmDelete = useCallback(async () => {
    if (!nodeToDelete) return;

    const nodeId = nodeToDelete.id;

    // 添加删除动画效果
    setNodes(nds => nds.map(n => n.id === nodeId ? { ...n, className: 'node-delete', data: { ...n.data, isDeleting: true } } : n));

    // 延迟执行删除操作，让动画播放
    setTimeout(() => {
      if (outline) {
        const updatedOutline = deepCopy(outline);
        const result = deleteNodeAndGetChildren(updatedOutline.nodes, nodeId);
        updatedOutline.nodes = result.updatedNodes;
        if (result.deletedNode) {
          showNotification(`已删除节点 "${result.deletedNode.title}" ${result.deletedChildren.length > 0 ? `及其 ${result.deletedChildren.length} 个子节点` : ''}`, 'success', 3000, 'top-center');
          onChange(updatedOutline);
          // Auto-save logic here if bookId exists
        }
      }

      // 关闭弹窗
      setDeleteConfirmOpen(false);
      setNodeToDelete(null);
    }, 300);
  }, [nodeToDelete, outline, onChange, bookId, setNodes]);

  // 处理取消删除
  const handleCancelDelete = useCallback(() => {
    setDeleteConfirmOpen(false);
    setNodeToDelete(null);
  }, []);

  // 批量操作处理函数
  const handleBatchDelete = useCallback(() => {
    if (rfSelectedNodes.length === 0) return;

    // 获取选中节点对应的大纲节点数据
    const selectedOutlineNodes = rfSelectedNodes
      .map(node => findNode(outline?.nodes || [], node.id))
      .filter(Boolean) as OutlineNodeType[];

    if (selectedOutlineNodes.length === 0) return;

    setNodesToBatchDelete(selectedOutlineNodes);
    setBatchDeleteConfirmOpen(true);
  }, [rfSelectedNodes, outline]);

  const handleConfirmBatchDelete = useCallback(async () => {
    if (nodesToBatchDelete.length === 0) return;

    // 批量删除节点
    const nodeIdsToDelete = nodesToBatchDelete.map(node => node.id);

    if (outline) {
      const updatedOutline = deepCopy(outline);

      // 逐个删除节点
      nodeIdsToDelete.forEach(nodeId => {
        const result = deleteNodeAndGetChildren(updatedOutline.nodes, nodeId);
        updatedOutline.nodes = result.updatedNodes;
      });

      onChange(updatedOutline);
      showNotification(`已删除 ${nodesToBatchDelete.length} 个节点`, 'success', 3000, 'top-center');
    }

    // 清除选择状态
    setRfSelectedNodes([]);
    setRfSelectedEdges([]);

    // 关闭弹窗
    setBatchDeleteConfirmOpen(false);
    setNodesToBatchDelete([]);
  }, [nodesToBatchDelete, outline, onChange, setRfSelectedNodes, setRfSelectedEdges]);

  const handleCancelBatchDelete = useCallback(() => {
    setBatchDeleteConfirmOpen(false);
    setNodesToBatchDelete([]);
  }, []);

  const handleClearSelection = useCallback(() => {
    setRfSelectedNodes([]);
    setRfSelectedEdges([]);
  }, [setRfSelectedNodes, setRfSelectedEdges]);

  const handleSelectSimilar = useCallback(() => {
    if (rfSelectedNodes.length !== 1) return;

    const selectedNode = rfSelectedNodes[0];
    const selectedNodeType = selectedNode.data?.type;

    if (!selectedNodeType) return;

    // 找到所有相同类型的节点
    const similarNodes = nodes.filter(node => node.data?.type === selectedNodeType);
    setRfSelectedNodes(similarNodes);
  }, [rfSelectedNodes, nodes, setRfSelectedNodes]);

  // 批量复制处理函数
  const handleBatchCopy = useCallback(() => {
    if (rfSelectedNodes.length === 0 || !outline) return;

    // 获取选中节点对应的大纲节点数据
    const selectedOutlineNodes = rfSelectedNodes
      .map(node => findNode(outline?.nodes || [], node.id))
      .filter(Boolean) as OutlineNodeType[];

    if (selectedOutlineNodes.length === 0) return;

    // 复制节点并生成新的ID和位置
    const copiedNodes: OutlineNodeType[] = [];
    const nodeIdMapping = new Map<string, string>(); // 旧ID -> 新ID的映射

    // 递归复制节点及其子节点
    const copyNodeRecursively = (node: OutlineNodeType): OutlineNodeType => {
      const newId = generateUniqueNodeId(node.type || 'node');
      nodeIdMapping.set(node.id, newId);

      const copiedNode: OutlineNodeType = {
        ...deepCopy(node),
        id: newId,
        title: `${node.title} 副本`,
        children: node.children ? node.children.map(copyNodeRecursively) : []
      };

      return copiedNode;
    };

    // 复制所有选中的节点
    selectedOutlineNodes.forEach(node => {
      const copiedNode = copyNodeRecursively(node);
      copiedNodes.push(copiedNode);
    });

    // 更新大纲数据
    const updatedOutline = {
      ...outline,
      nodes: [...outline.nodes, ...copiedNodes]
    };

    onChange(updatedOutline);

    // 显示成功通知
    showNotification(`已复制 ${copiedNodes.length} 个节点`, 'success', 3000, 'top-center');

    // 选中新复制的节点
    setTimeout(() => {
      const newNodeIds = copiedNodes.map(node => node.id);
      const newSelectedNodes = nodes.filter(node => newNodeIds.includes(node.id));
      setRfSelectedNodes(newSelectedNodes);
    }, 100);

  }, [rfSelectedNodes, outline, onChange, nodes, setRfSelectedNodes]);

  const handleAddChild = useCallback((parentId: string | null, type: string) => {
    const effectiveParentId = parentId || selectedNodeId;
    if (!effectiveParentId && !parentId) { // If no parent and no selected node, create root
        if (outline) {
            const newRootNode: OutlineNodeType = { id: generateUniqueNodeId(type), title: `新${getNodeTypeLabel(type)}`, type: type as any, children: [], isNew: true };
          const updatedOutline = deepCopy(outline);
            updatedOutline.nodes.push(newRootNode);
          onChange(updatedOutline);
            showNotification(`已添加根节点: ${newRootNode.title}`, 'success', 3000, 'top-center');
        }
        return;
      }
    if(!effectiveParentId) return; // Should not happen if logic above is correct

    const newNode: OutlineNodeType = { id: generateUniqueNodeId(type), title: `新${getNodeTypeLabel(type)}`, type: type as any, children: [], isNew: true };
    if (outline) {
      const updatedOutline = deepCopy(outline);
      const result = addChildNode(updatedOutline.nodes, effectiveParentId, newNode);
      if (result.found) {
      updatedOutline.nodes = result.updatedNodes;
      onChange(updatedOutline);
        showNotification(`已添加${effectiveParentId ? '子' : '根'}节点: ${newNode.title}`, 'success', 3000, 'top-center');
        // Auto-save logic
      }
    }
  }, [outline, onChange, bookId, selectedNodeId]);

  // 生成节点状态管理
  const [generatingNodes, setGeneratingNodes] = useState<Map<string, any>>(new Map());

  // 处理Synopsis AI功能
  const handleSynopsisAI = useCallback(async (synopsisData: any) => {
    console.log('🧠 handleSynopsisAI被调用，Synopsis数据:', synopsisData);

    if (!outline) {
      console.error('❌ outline不存在，无法更新Synopsis节点');
      return;
    }

    // 使用传递的nodeId或者selectedNodeId作为目标节点
    const targetNodeId = synopsisData.nodeId || selectedNodeId;
    if (!targetNodeId) {
      console.error('❌ 没有指定要更新的节点ID');
      showNotification('❌ 无法确定要更新的Synopsis节点', 'error', 3000, 'top-center');
      return;
    }

    try {
      // 创建大纲副本
      const updatedOutline = deepCopy(outline);

      // 递归查找并更新Synopsis节点
      const updateSynopsisNode = (nodes: OutlineNodeType[]): boolean => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          if (node.id === targetNodeId && node.type === 'synopsis') {
            // 更新Synopsis节点的字段，保持原始类型和其他属性
            const updatedNode = {
              ...node,
              synopsisBrainhole: synopsisData.synopsisBrainhole || node.synopsisBrainhole,
              synopsisGenre: synopsisData.synopsisGenre || node.synopsisGenre,
              synopsisOpening: synopsisData.synopsisOpening || node.synopsisOpening, // 🔥 新增：保存开头
              synopsisCoreOutline: synopsisData.synopsisCoreOutline || node.synopsisCoreOutline,
              synopsisEnding: synopsisData.synopsisEnding || node.synopsisEnding, // 🔥 新增：保存结尾
              synopsisStoryDescription: synopsisData.synopsisStoryDescription || node.synopsisStoryDescription,
              synopsisAceReferences: synopsisData.synopsisAceReferences || node.synopsisAceReferences,
              type: 'synopsis' as const, // 确保类型不变
            };

            // 更新节点标题（可选）
            if (synopsisData.synopsisCoreOutline) {
              updatedNode.title = synopsisData.synopsisCoreOutline.substring(0, 50) + '...';
            }

            // 替换原节点
            nodes[i] = updatedNode;

            console.log('✅ Synopsis节点数据已更新:', updatedNode);
            return true;
          }

          // 递归检查子节点
          if (node.children && Array.isArray(node.children)) {
            if (updateSynopsisNode(node.children)) {
              return true;
            }
          }
        }
        return false;
      };

      // 执行更新
      const updated = updateSynopsisNode(updatedOutline.nodes);

      if (updated) {
        // 更新大纲
        onChange(updatedOutline);

        // 保存到数据库（直接保存，不使用防抖）
        if (bookId) {
          try {
            (async () => {
              const { outlineService } = await import('@/factories/api/outlineService');
              await outlineService.saveOutline(bookId, updatedOutline);
              console.log('✅ Synopsis节点数据已保存到数据库');
            })();
          } catch (error) {
            console.error('❌ 保存Synopsis节点数据失败:', error);
          }
        }

        showNotification('🧠 Synopsis AI生成成功！节点数据已更新', 'success', 3000, 'top-center');
      } else {
        console.error('❌ 未找到要更新的Synopsis节点');
        showNotification('❌ 未找到要更新的Synopsis节点', 'error', 3000, 'top-center');
      }

    } catch (error) {
      console.error('❌ 更新Synopsis节点失败:', error);
      showNotification('❌ 更新Synopsis节点失败', 'error', 3000, 'top-center');
    }
  }, [outline, selectedNodeId, onChange, bookId, showNotification]);

  // 处理AI追加节点
  const handleAIAppend = useCallback(async (
    parentNode: OutlineNodeType,
    customPrompt?: string,
    generateCount: number = 1
  ) => {
    console.log('🚀 handleAIAppend被调用，父节点:', parentNode.title, '自定义提示词:', customPrompt, '生成数量:', generateCount);
    console.log('📖 AI助手选择的章节ID (assistantSelectedChapterIds):', assistantSelectedChapterIds);
    console.log('📖 当前localStorage中的章节ID:', localStorage.getItem(`assistant-selected-chapters-${bookId}`));
    if (!outline) return;

    // 确定要生成的子节点类型
    const childTypeMap: Record<string, string> = {
      'volume': 'chapter',
      'event': 'chapter',
      'chapter': 'plot',
      'plot': 'dialogue',
      'synopsis': 'plot'  // Synopsis节点可以追加剧情节点
    };
    const childType = childTypeMap[parentNode.type];
    if (!childType) {
      showNotification('❌ 此节点类型不支持AI追加', 'error', 3000, 'top-center');
      return;
    }

    // 生成临时预览节点ID
    const generatingNodeId = `generating-${parentNode.id}-${Date.now()}`;

    try {
      showNotification('🤖 AI正在智能生成节点...', 'info', 2000, 'top-center');

      // 创建生成中的预览节点
      const generatingNodeData = {
        id: generatingNodeId,
        parentNodeTitle: parentNode.title,
        generatingType: childType as 'chapter' | 'plot' | 'dialogue',
        customPrompt: customPrompt,
        progress: 0
      };

      // 添加到生成节点状态
      setGeneratingNodes(prev => new Map(prev.set(generatingNodeId, generatingNodeData)));

      // 在画布上显示生成预览节点
      setNodes(prevNodes => {
        const parentNodePosition = prevNodes.find(n => n.id === parentNode.id)?.position || { x: 0, y: 0 };
        const newGeneratingNode: Node = {
          id: generatingNodeId,
          type: 'generatingNode',
          position: {
            x: parentNodePosition.x + 50,
            y: parentNodePosition.y + 200
          },
          data: generatingNodeData,
          draggable: true, // 允许拖动
          selectable: true // 允许选择
        };

        return [...prevNodes, newGeneratingNode];
      });

      // 创建从父节点到生成节点的连接边
      setEdges(prevEdges => {
        const newEdge = {
          id: `edge-${parentNode.id}-${generatingNodeId}`,
          source: parentNode.id,
          target: generatingNodeId,
          type: 'customEdge',
          animated: true, // 添加动画效果表示正在生成
          style: {
            stroke: '#8b5cf6', // 紫色表示AI生成
            strokeWidth: 2,
            strokeDasharray: '5,5' // 虚线表示临时状态
          }
        };

        return [...prevEdges, newEdge];
      });

      // 动态导入AI追加服务
      const { aiNodeAppender } = await import('./assistant/AINodeAppender');

      // 执行AI追加操作
      const response = await aiNodeAppender.appendChildNode(
        parentNode,
        outline,
        (chunk: string) => {
          // 更新生成进度和实时文本
          setGeneratingNodes(prev => {
            const updated = new Map(prev);
            const nodeData = updated.get(generatingNodeId);
            if (nodeData) {
              nodeData.currentText = chunk;
              nodeData.progress = Math.min((nodeData.progress || 0) + 10, 90);
              updated.set(generatingNodeId, { ...nodeData });

              // 同时更新画布上的节点
              setNodes(prevNodes =>
                prevNodes.map(node =>
                  node.id === generatingNodeId
                    ? { ...node, data: { ...nodeData } }
                    : node
                )
              );
            }
            return updated;
          });
          console.log('AI生成进度:', chunk);
        },
        {
          bookId: bookId,
          customPrompt: customPrompt,
          selectedFramework: selectedFramework || localSelectedFramework,
          selectedFrameworks: selectedFrameworks || localSelectedFrameworks,
          generateCount: generateCount,
          selectedChapterIds: assistantSelectedChapterIds // 🔥 新增：传递用户选择的章节ID
        }
      );

      // 更新进度到100%
      setGeneratingNodes(prev => {
        const updated = new Map(prev);
        const nodeData = updated.get(generatingNodeId);
        if (nodeData) {
          nodeData.progress = 100;
          updated.set(generatingNodeId, { ...nodeData });
        }
        return updated;
      });

      if (response.success && response.changes && response.changes.length > 0) {
        // 应用AI生成的变更
        const updatedOutline = deepCopy(outline);

        // 🔥 关键修复：保存预览节点的位置信息，用于新节点的位置设置
        const parentNodePosition = nodes.find(n => n.id === parentNode.id)?.position || { x: 0, y: 0 };
        const basePreviewPosition = {
          x: parentNodePosition.x + 50,
          y: parentNodePosition.y + 200
        };

        // 为多个节点计算不同的位置，避免重叠
        const calculateNodePosition = (basePosition: {x: number, y: number}, index: number) => {
          return {
            x: basePosition.x + (index % 2) * 250, // 水平分布
            y: basePosition.y + Math.floor(index / 2) * 150 // 垂直分布
          };
        };

        response.changes.forEach((change, index) => {
          if (change.type === 'create' && change.data) {
            // 🔥 关键修复：为新节点计算位置，确保与预览节点位置一致
            const nodePosition = (response.changes && response.changes.length === 1)
              ? basePreviewPosition // 单个节点使用预览位置
              : calculateNodePosition(basePreviewPosition, index); // 多个节点智能分布

            const newNode: OutlineNodeType = {
              id: change.nodeId,
              title: change.data.title,
              type: change.data.type,
              description: change.data.description,
              creativeNotes: change.data.creativeNotes,
              children: [],
              isNew: true,
              // 复制其他专有字段
              ...change.data,
              // 🔥 关键修复：确保position字段不被覆盖，放在最后
              position: nodePosition,
            };



            // 添加到父节点下
            const result = addChildNode(updatedOutline.nodes, parentNode.id, newNode);
            if (result.found) {
              updatedOutline.nodes = result.updatedNodes;
            }
          }
        });

        onChange(updatedOutline);
        showNotification(`✨ AI成功生成了 ${response.changes.length} 个节点！`, 'success', 3000, 'top-center');

        // 移除生成预览节点和连接边
        setTimeout(() => {
          setNodes(prevNodes => prevNodes.filter(node => node.id !== generatingNodeId));
          setEdges(prevEdges => prevEdges.filter(edge =>
            edge.source !== generatingNodeId && edge.target !== generatingNodeId
          ));
          setGeneratingNodes(prev => {
            const updated = new Map(prev);
            updated.delete(generatingNodeId);
            return updated;
          });
        }, 1500); // 延迟1.5秒移除，让用户看到完成状态

        // 自动保存
        if (bookId) {
          // 延迟保存，避免依赖问题
          setTimeout(async () => {
            try {
              const { outlineService } = await import('@/factories/api/outlineService');
              await outlineService.saveOutline(bookId, updatedOutline);
            } catch (error) {
              console.error('自动保存失败:', error);
            }
          }, 1000);
        }
      } else {
        // 生成失败，移除预览节点和连接边
        setNodes(prevNodes => prevNodes.filter(node => node.id !== generatingNodeId));
        setEdges(prevEdges => prevEdges.filter(edge =>
          edge.source !== generatingNodeId && edge.target !== generatingNodeId
        ));
        setGeneratingNodes(prev => {
          const updated = new Map(prev);
          updated.delete(generatingNodeId);
          return updated;
        });
        showNotification(`❌ AI生成失败: ${response.error || '未知错误'}`, 'error', 3000, 'top-center');
      }
    } catch (error: any) {
      console.error('AI追加节点失败:', error);

      // 移除生成预览节点和连接边
      setNodes(prevNodes => prevNodes.filter(node => node.id !== generatingNodeId));
      setEdges(prevEdges => prevEdges.filter(edge =>
        edge.source !== generatingNodeId && edge.target !== generatingNodeId
      ));
      setGeneratingNodes(prev => {
        const updated = new Map(prev);
        updated.delete(generatingNodeId);
        return updated;
      });

      showNotification(`❌ AI追加失败: ${error.message || '网络错误'}`, 'error', 3000, 'top-center');
    }
  }, [outline, onChange, bookId, assistantSelectedChapterIds]);

  // 添加一个标志来跟踪是否正在更新节点内容（非位置）
  const [isUpdatingNodeContent, setIsUpdatingNodeContent] = useState(false);

  // 临时移除handleNodeUpdate，稍后在debouncedSave之后重新定义

  // 添加一个标志来跟踪是否正在应用布局
  const [isApplyingLayout, setIsApplyingLayout] = useState(false);

  // 布局锁定状态 - 用户手动调整后禁用自动布局
  const [layoutLocked, setLayoutLocked] = useState(false);

  // 手动定位的节点集合
  const [manuallyPositioned, setManuallyPositioned] = useState<Set<string>>(new Set());

  // 保存状态指示
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const [isSavingManually, setIsSavingManually] = useState(false);

  // 小地图显示控制
  const [showMiniMap, setShowMiniMap] = useState(() => {
    // 从localStorage读取用户偏好，默认显示
    const saved = localStorage.getItem('outline-canvas-minimap-visible');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // 切换小地图显示状态
  const toggleMiniMap = useCallback(() => {
    setShowMiniMap(prev => {
      const newValue = !prev;
      // 保存用户偏好到localStorage
      localStorage.setItem('outline-canvas-minimap-visible', JSON.stringify(newValue));
      return newValue;
    });
  }, []);

  // 创建防抖保存函数
  const debouncedSave = useMemo(
    () => debounce(async (outlineToSave: Outline, currentBookId: string) => {
      if (!currentBookId) return;

      try {
        setIsSaving(true);
   

        const { outlineService } = await import('@/factories/api/outlineService');
        await outlineService.saveOutline(currentBookId, outlineToSave);

        setLastSaveTime(new Date());

        // 显示简短的成功提示
        showNotification('已自动保存', 'success', 1500, 'top-center');
      } catch (error) {
        console.error('自动保存大纲数据失败:', error);
        showNotification('自动保存失败，请手动保存', 'error', 3000, 'top-center');
      } finally {
        setIsSaving(false);
      }
    }, 1000), // 增加防抖延迟到1秒，避免频繁保存
    [setIsSaving, setLastSaveTime]
  );

  // 节点更新处理函数 - 在debouncedSave之后定义
  const handleNodeUpdate = useCallback((updatedNodeData: OutlineNodeType) => {

    const cleanNode = { ...updatedNodeData };
    delete (cleanNode as any).onEdit; delete (cleanNode as any).onDelete; delete (cleanNode as any).onAddChild; delete (cleanNode as any).onSelect; delete (cleanNode as any).onUpdate; delete (cleanNode as any).selected;


    if (outline) {
      // 设置标志，防止重新渲染时重置节点位置
      setIsUpdatingNodeContent(true);

      const updatedOutline = deepCopy(outline);
      const updateRecursive = (nodesToUpdate: OutlineNodeType[]): OutlineNodeType[] => {
        return nodesToUpdate.map(n => {
          if (n.id === cleanNode.id) {
   
            // 保护现有的位置信息 - 如果原节点有位置信息且新节点没有，则保留原位置
            const preservedPosition = n.position && !cleanNode.position ? n.position : cleanNode.position;
            const updatedNode = { ...cleanNode, position: preservedPosition };
            return updatedNode;
          }
          if (n.children) return { ...n, children: updateRecursive(n.children) };
          return n;
      });
    };
      updatedOutline.nodes = updateRecursive(updatedOutline.nodes);
      onChange(updatedOutline);
      showNotification(`已更新节点: ${cleanNode.title}`, 'success', 3000, 'top-center');

      // 🔥 关键修复：添加数据库保存逻辑
      if (bookId) {
        debouncedSave(updatedOutline, bookId);
      }

      if (viewingNode && viewingNode.id === cleanNode.id) setViewingNode(cleanNode);

      // 延迟重置标志，确保更新完成
      setTimeout(() => {
        setIsUpdatingNodeContent(false);
      }, 100);
    }
  }, [outline, onChange, bookId, viewingNode, setViewingNode, setIsUpdatingNodeContent, debouncedSave]);

  // 添加一个ref来跟踪上一次的大纲数据，避免不必要的重新转换
  const prevOutlineRef = useRef<Outline | null>(null);

  // 添加状态更新防护机制
  const [isUpdatingNodes, setIsUpdatingNodes] = useState(false);



  // 添加useEffect来监听selectedNodeIds变化，强制更新画布节点状态
  useEffect(() => {
    // 防止重复更新
    if (isUpdatingNodes) {
      return;
    }

    // 如果在选择模式下，强制更新所有节点的选择状态
    if (selectionMode && nodes.length > 0) {

      // 检查是否真的需要更新，避免不必要的状态更新
      const needsUpdate = nodes.some(node =>
        node.data.isNodeSelected !== selectedNodeIds.includes(node.id)
      );

      if (needsUpdate) {
        setIsUpdatingNodes(true);
        setNodes(prevNodes =>
          prevNodes.map(node => ({
            ...node,
            data: {
              ...node.data,
              isNodeSelected: selectedNodeIds.includes(node.id)
            }
          }))
        );
        // 延迟重置更新标志
        setTimeout(() => setIsUpdatingNodes(false), 100);
      }
    }
  }, [selectedNodeIds, selectionMode, nodes.length]); // 移除setNodes依赖，避免循环

  // 添加大纲数据转换防护机制
  const [isTransformingOutline, setIsTransformingOutline] = useState(false);

  // 添加拖拽状态防护，防止拖拽期间重新转换大纲
  const [isDraggingNodes, setIsDraggingNodes] = useState(false);

  useEffect(() => {
    // 防止重复转换
    if (isTransformingOutline) {

      return;
    }

    // 如果正在应用布局或更新节点内容，不重置节点位置
    if (isApplyingLayout || isUpdatingNodeContent) {

      return;
    }

    // 🔥 关键修复：如果正在拖拽节点，不重新转换大纲，避免位置被重置
    if (isDraggingNodes) {
    
      return;
    }

    // 检查大纲数据是否真的发生了变化
    // 但是如果预览节点有变化，也需要重新转换
    const hasPreviewNodesChanged = previewNodes.length > 0 || selectedPreviewNodeIds.size > 0;

    // 🔥 修复：检查折叠状态是否发生变化
    const hasCollapsedNodesChanged =
      JSON.stringify(Array.from(collapsedNodes).sort()) !==
      JSON.stringify(Array.from(prevCollapsedNodesRef.current).sort());

    if (outline && prevOutlineRef.current && !hasPreviewNodesChanged && !hasCollapsedNodesChanged) {
      // 检查是否只是位置信息发生了变化
      const currentOutlineWithoutPositions = JSON.stringify(outline, (key, value) => {
        if (key === 'position') return undefined;
        return value;
      });
      const prevOutlineWithoutPositions = JSON.stringify(prevOutlineRef.current, (key, value) => {
        if (key === 'position') return undefined;
        return value;
      });

      // 如果除了位置信息外没有其他变化，检查是否有位置信息的变化
      if (currentOutlineWithoutPositions === prevOutlineWithoutPositions) {
        // 检查是否有位置信息的变化
        const currentPositions = JSON.stringify(outline, (key, value) => {
          if (key !== 'position') return undefined;
          return value;
        });
        const prevPositions = JSON.stringify(prevOutlineRef.current, (key, value) => {
          if (key !== 'position') return undefined;
          return value;
        });

        // 如果位置信息也没有变化，则跳过重新转换
        if (currentPositions === prevPositions) {
          return;
        }
        // 如果只有位置信息变化，继续执行转换以更新ReactFlow节点位置
      }
    }

    // 🔥 修复：更新上一次的折叠状态引用
    prevCollapsedNodesRef.current = new Set(collapsedNodes);

    setIsTransformingOutline(true);

    const handlers = { onEdit: handleNodeEdit, onDelete: handleNodeDelete, onAddChild: handleAddChild, onAIAppend: handleAIAppend, onSynopsisAI: handleSynopsisAI, onSelect: handleNodeSelect, onUpdate: handleNodeUpdate };
    const { nodes: flowNodes, edges: flowEdges } = transformOutlineToFlow(outline, selectedNodeId, handlers);

    setNodes(flowNodes);
    setEdges(flowEdges);

    // 更新上一次的大纲数据引用
    prevOutlineRef.current = outline ? JSON.parse(JSON.stringify(outline)) : null;

    // 延迟重置转换标志
    setTimeout(() => setIsTransformingOutline(false), 100);
  }, [outline, selectedNodeId, transformOutlineToFlow, handleNodeEdit, handleNodeDelete, handleAddChild, handleAIAppend, handleSynopsisAI, handleNodeSelect, handleNodeUpdate, isApplyingLayout, isUpdatingNodeContent, previewNodes.length, selectedPreviewNodeIds.size, collapsedNodes]); // 🔥 修复：添加collapsedNodes依赖，确保折叠状态变化时重新渲染

  const onNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChangeReactFlow(changes);



    // 检测拖拽状态变化
    const draggingChanges = changes.filter(
      (change): change is NodePositionChange =>
        change.type === 'position' && typeof change.dragging === 'boolean'
    );

    // 更新拖拽状态
    if (draggingChanges.length > 0) {
      const isDraggingNow = draggingChanges.some(change => change.dragging);
      setIsDragging(isDraggingNow);

      // 🔥 关键修复：同时更新节点拖拽状态，防止大纲转换
      setIsDraggingNodes(isDraggingNow);

      if (isDraggingNow) {
        // 拖拽开始，记录位置
        const draggingChange = draggingChanges.find(change => change.dragging && change.position);
        if (draggingChange && draggingChange.position) {
          setDragPosition(draggingChange.position);
        }
      } else {
        // 拖拽结束，清除位置
        setDragPosition(null);

        // 延迟重置拖拽状态，确保onChange调用完成后再允许大纲转换
        setTimeout(() => {
          setIsDraggingNodes(false);
        }, 100);
      }
    }

    // 🔥 修复拖拽结束检测逻辑
    // ReactFlow在拖拽结束时可能发送不同格式的事件，我们需要更宽松的检测条件
    const dragEndChanges = changes.filter(
      (change): change is NodePositionChange => {
        if (change.type !== 'position' || !change.position) return false;

        // 检测拖拽结束的多种情况：
        // 1. dragging明确为false
        // 2. dragging为undefined且当前处于拖拽状态（说明拖拽刚结束）
        // 3. 没有dragging字段但有position变化（某些ReactFlow版本的行为）
        const isDragEnd =
          change.dragging === false ||
          (change.dragging === undefined && isDragging) ||
          (!('dragging' in change) && isDragging);

        return isDragEnd;
      }
    );



  

    // 只有在拖拽结束时才更新大纲数据
    if (dragEndChanges.length > 0 && outline) {
 

      // 创建大纲数据的副本
      const updatedOutline = deepCopy(outline);

      // 批量更新所有节点位置
      dragEndChanges.forEach(positionChange => {
        const nodeId = positionChange.id;
        let newPosition = positionChange.position!; // 已经在filter中确保position存在

        // 应用磁性吸附
        const otherNodes = nodes.filter(n => n.id !== nodeId);
        newPosition = applyMagneticSnapping(newPosition, otherNodes);


        // 标记节点为手动定位
        setManuallyPositioned(prev => new Set(prev).add(nodeId));

        // 递归更新节点位置
        const updatePosRecursive = (nodesToUpdate: OutlineNodeType[]): OutlineNodeType[] => {
          return nodesToUpdate.map(n => {
            if (n.id === nodeId) return { ...n, position: newPosition };
            if (n.children && Array.isArray(n.children)) { // 确保children是数组
              return { ...n, children: updatePosRecursive(n.children) };
            }
            return n;
          });
        };

        updatedOutline.nodes = updatePosRecursive(updatedOutline.nodes);
      });

      // 如果有手动定位的节点，锁定布局
      if (dragEndChanges.length > 0) {
        setLayoutLocked(true);

      }

      // 立即保存到localStorage（同步）
      if (bookId) {
        try {
          localStorage.setItem(`outline-positions-${bookId}`, JSON.stringify(updatedOutline));

        } catch (error) {
          console.error('❌ 保存位置信息到localStorage失败:', error);
        }
      }

      // 🔥 关键修复：延迟调用onChange，避免立即触发transformOutlineToFlow重新执行
      // 这样可以确保ReactFlow的位置状态不会被立即重置
      setTimeout(() => {
        onChange(updatedOutline);

        // 触发防抖保存到数据库
        if (bookId) {
          debouncedSave(updatedOutline, bookId);
        }
      }, 50); // 短暂延迟，确保拖拽操作完全结束
    }
  }, [onNodesChangeReactFlow, outline, onChange, bookId, debouncedSave, setManuallyPositioned, setLayoutLocked, nodes, setIsDragging, setDragPosition]);

  // 🔥 关键修复：添加专门的拖拽结束事件监听器
  const onNodeDragStop = useCallback((event: React.MouseEvent, node: Node, nodes: Node[]) => {
    if (!outline || !bookId) return;

    // 创建大纲数据的副本
    const updatedOutline = deepCopy(outline);

    // 应用磁性吸附
    const otherNodes = nodes.filter(n => n.id !== node.id);
    const newPosition = applyMagneticSnapping(node.position, otherNodes);

    // 标记节点为手动定位
    setManuallyPositioned(prev => new Set(prev).add(node.id));

    // 递归更新节点位置
    const updatePosRecursive = (nodesToUpdate: OutlineNodeType[]): OutlineNodeType[] => {
      return nodesToUpdate.map(n => {
        if (n.id === node.id) return { ...n, position: newPosition };
        if (n.children && Array.isArray(n.children)) {
          return { ...n, children: updatePosRecursive(n.children) };
        }
        return n;
      });
    };

    updatedOutline.nodes = updatePosRecursive(updatedOutline.nodes);

    // 锁定布局
    setLayoutLocked(true);

    // 立即保存到localStorage（同步）
    try {
      localStorage.setItem(`outline-positions-${bookId}`, JSON.stringify(updatedOutline));
    } catch (error) {
      console.error('❌ 保存位置信息到localStorage失败:', error);
    }

    // 延迟调用onChange，避免立即触发transformOutlineToFlow重新执行
    setTimeout(() => {
      onChange(updatedOutline);

      // 触发防抖保存到数据库
      debouncedSave(updatedOutline, bookId);
    }, 50);

    // 重置拖拽状态
    setIsDragging(false);
    setDragPosition(null);
  }, [outline, bookId, setManuallyPositioned, setLayoutLocked, onChange, debouncedSave, setIsDragging, setDragPosition, applyMagneticSnapping]);

  const onConnect = useCallback((connection: Connection) => {
    // 获取当前样式
    try {
      // 获取边样式
      const edgeType = 'parent-child'; // 默认为父子关系
      const edgeStyle = styleContext.getEdgeStyle(edgeType);


      // 添加边到ReactFlow状态，直接设置样式
      setEdges((eds) => addEdge({
        ...connection,
        type: 'customEdge',
        style: {
          stroke: edgeStyle.strokeColor,
          strokeWidth: edgeStyle.strokeWidth,
          strokeDasharray: edgeStyle.strokeDasharray,
          opacity: edgeStyle.strokeOpacity,
        },
        data: {
          relationshipType: edgeType,
          edgeStyle: edgeStyle
        }
      }, eds));
    } catch (error) {
      console.error('应用样式时出错:', error);

      // 如果出错，使用默认样式
      setEdges((eds) => addEdge({
        ...connection,
        type: 'customEdge',
        data: { relationshipType: 'parent-child' }
      }, eds));
    }

    // 更新大纲数据结构
    if (connection.source && connection.target && outline) {


      // 创建大纲数据的副本
      const updatedOutline = deepCopy(outline);

      // 查找源节点和目标节点
      const sourceNode = findNode(updatedOutline.nodes, connection.source);
      const targetNode = findNode(updatedOutline.nodes, connection.target);

      if (sourceNode && targetNode) {

        // 根据连线类型更新关系
        const relationshipType = 'parent-child'; // 默认为父子关系

        if (relationshipType === 'parent-child') {
   

          // 如果目标节点已经有父节点，先从原父节点中移除
          const findAndRemoveChild = (nodes: OutlineNodeType[]): boolean => {
            for (let i = 0; i < nodes.length; i++) {
              const node = nodes[i];

              // 检查当前节点的子节点
              if (node.children && node.children.length > 0) {
                const childIndex = node.children.findIndex(child => child.id === targetNode.id);

                if (childIndex !== -1) {
                  // 找到了目标节点，从原父节点中移除
   
                  node.children.splice(childIndex, 1);
                  return true;
                }

                // 递归检查子节点
                if (findAndRemoveChild(node.children)) {
                  return true;
                }
              }
            }

            return false;
          };

          // 从根节点中移除目标节点
          const rootIndex = updatedOutline.nodes.findIndex(node => node.id === targetNode.id);
          if (rootIndex !== -1) {
 
            updatedOutline.nodes.splice(rootIndex, 1);
          } else {
            // 从其他节点的子节点中移除
            findAndRemoveChild(updatedOutline.nodes);
          }

          // 将目标节点添加为源节点的子节点
          if (!sourceNode.children) {
            sourceNode.children = [];
          }

          sourceNode.children.push(targetNode);

          // 更新目标节点的父节点ID
          targetNode.parentId = sourceNode.id;

          // 更新兄弟关系
          if (sourceNode.children.length > 1) {
            const prevSibling = sourceNode.children[sourceNode.children.length - 2];
            prevSibling.nextSiblingId = targetNode.id;
            targetNode.prevSiblingId = prevSibling.id;
            targetNode.nextSiblingId = undefined;
          }
        } else if (relationshipType === 'sibling') {


          // 设置兄弟关系
          sourceNode.nextSiblingId = targetNode.id;
          targetNode.prevSiblingId = sourceNode.id;
        }

        // 更新大纲数据
        onChange(updatedOutline);

        // 保存到数据库
        if (bookId) {
          (async () => {
            try {
              const { outlineService } = await import('@/factories/api/outlineService');

              await outlineService.saveOutline(bookId, updatedOutline);
 
              showNotification('连线已创建并保存', 'success', 3000, 'top-center');
            } catch (error) {
              console.error('连线创建后自动保存大纲数据失败:', error);
              showNotification('保存失败，请手动保存您的更改', 'error', 5000, 'top-center');
            }
          })();
        }
      } else {
        console.error(`未找到源节点或目标节点: ${connection.source} -> ${connection.target}`);
      }
    }
  }, [setEdges, outline, onChange, bookId, styleContext]);

  // 获取当前节点位置
  const getCurrentNodePositions = useCallback((): NodePositions => {
    return nodes.reduce((acc, node) => {
      acc[node.id] = { x: node.position.x, y: node.position.y };
      return acc;
    }, {} as NodePositions);
  }, [nodes]);

  // 更新节点位置
  const updateNodePositions = useCallback((positions: NodePositions) => {
    setNodes(nds =>
      nds.map(node => {
        if (positions[node.id]) {
          return {
            ...node,
            position: positions[node.id]
          };
        }
        return node;
      })
    );
  }, [setNodes]);





  // 预览布局
  const handlePreviewLayout = useCallback((config: LayoutConfig) => {
    if (!nodes.length) return;

    // 检查布局是否被锁定
    if (layoutLocked) {

      showNotification('布局已锁定，请先解锁布局或重置手动定位', 'warning', 3000, 'top-center');
      return;
    }

    // 设置标志，防止节点位置被重置
    setIsApplyingLayout(true);

    // 如果是第一次进入预览模式，保存原始位置
    if (!isPreviewMode) {
      setOriginalPositions(getCurrentNodePositions());
    }

    // 设置预览模式
    setIsPreviewMode(true);

    // 计算新布局
    const layoutResult = computeLayout(nodes, edges, config);

    // 创建动画配置
    const animationConfig: AnimationConfig = {
      duration: 600,
      easing: 'easeInOut',
      staggerDelay: 10,
      enableAnimation: true
    };

    // 获取当前位置
    const currentPositions = getCurrentNodePositions();

    // 应用布局动画
    animateLayoutChange(
      currentPositions,
      layoutResult,
      animationConfig,
      updateNodePositions,
      () => {
        // 动画完成后的处理


        // 预览模式下不重置标志，保持预览状态
        // 只有在应用或取消预览时才重置
      }
    );
  }, [nodes, edges, isPreviewMode, getCurrentNodePositions, updateNodePositions, setIsApplyingLayout]);

  // 取消预览布局
  const cancelPreviewLayout = useCallback(() => {
    if (!isPreviewMode) return;

    // 恢复原始位置
    updateNodePositions(originalPositions);

    // 退出预览模式
    setIsPreviewMode(false);

    // 重置标志，允许节点位置被重置
    setTimeout(() => {
      setIsApplyingLayout(false);
    }, 500); // 添加延迟，确保所有操作完成


  }, [isPreviewMode, originalPositions, updateNodePositions, setIsApplyingLayout]);

  // 应用布局
  const handleAutoLayout = useCallback(async (config?: LayoutConfig, forceApply: boolean = false) => {
    if (!nodes.length) return;

    // 检查布局是否被锁定（除非强制应用）
    if (layoutLocked && !forceApply) {

      showNotification('布局已锁定，请先解锁布局或重置手动定位', 'warning', 3000, 'top-center');
      return;
    }

    // 设置标志，防止节点位置被重置
    setIsApplyingLayout(true);

    // 使用提供的配置或当前配置
    const layoutConfig = config || currentLayoutConfig;

    // 如果提供了新配置，更新当前配置
    if (config) {
      setCurrentLayoutConfig(config);
    }


    // 计算新布局
    const layoutResult = computeLayout(nodes, edges, layoutConfig);

    // 创建动画配置
    const animationConfig: AnimationConfig = {
      duration: 800,
      easing: 'easeInOut',
      staggerDelay: 20,
      enableAnimation: true
    };

    // 获取当前位置
    const currentPositions = getCurrentNodePositions();

    // 如果是从预览模式应用，清除预览状态
    if (isPreviewMode) {
      setIsPreviewMode(false);
      setOriginalPositions({});
    }

    try {
      // 应用布局动画
      await new Promise<void>((resolve) => {
        animateLayoutChange(
          currentPositions,
          layoutResult,
          animationConfig,
          updateNodePositions,
          () => {
            resolve();
          }
        );
      });

      // 动画完成后的处理
      // 获取最终位置
      const finalPositions = getCurrentNodePositions();

      // 创建更新后的节点数组，确保位置信息正确
      const updatedNodes = nodes.map(node => {
        if (finalPositions[node.id]) {
          return {
            ...node,
            position: finalPositions[node.id]
          };
        }
        return node;
      });

      // 更新节点状态，确保位置信息持久化
      setNodes(updatedNodes);

      // 更新大纲数据
      if (outline) {
        const updatedOutline = deepCopy(outline);

        // 更新每个节点的位置
        Object.entries(finalPositions).forEach(([nodeId, position]) => {
          const updateNodePositionRecursive = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
            return nodes.map(n => {
              if (n.id === nodeId) {
                return { ...n, position };
              }
              if (n.children) {
                return { ...n, children: updateNodePositionRecursive(n.children) };
              }
              return n;
            });
          };

          updatedOutline.nodes = updateNodePositionRecursive(updatedOutline.nodes);
        });

        // 更新大纲数据
        onChange(updatedOutline);

        // 添加到历史记录
        layoutHistoryManager.addEntry({
          timestamp: Date.now(),
          layoutConfig: layoutConfig,
          nodePositions: finalPositions,
          description: `应用${layoutConfig.type}布局`
        });

        // 显示成功通知
        showNotification('自动布局已应用', 'success', 3000, 'top-center');

        // 应用布局后，清除手动定位标记并解锁布局
        if (forceApply) {
          setManuallyPositioned(new Set());
          setLayoutLocked(false);
          console.log('强制应用布局，已清除手动定位标记并解锁布局');
        }

        // 保存到数据库
        if (bookId) {
          try {
            // 导入 outlineService
            const { outlineService } = await import('@/factories/api/outlineService');


            // 等待保存完成
            await outlineService.saveOutline(bookId, updatedOutline);
 
          } catch (error) {
            console.error('布局应用后大纲数据保存失败:', error);
            showNotification('保存失败，请手动保存您的更改', 'error', 5000, 'top-center');
          }
        }
      }
    } catch (error) {
      console.error('应用布局时出错:', error);
      showNotification('应用布局失败', 'error', 3000, 'top-center');
    } finally {
      // 重置标志，允许节点位置被重置
      setTimeout(() => {
        setIsApplyingLayout(false);
      }, 500); // 添加延迟，确保所有操作完成
    }
  }, [
    nodes,
    edges,
    bookId,
    outline,
    onChange,
    currentLayoutConfig,
    isPreviewMode,
    getCurrentNodePositions,
    updateNodePositions,
    setNodes,
    layoutHistoryManager,
    setIsApplyingLayout
  ]);

  // 解锁布局函数
  const handleUnlockLayout = useCallback(() => {
    setLayoutLocked(false);

    showNotification('布局已解锁，可以使用自动布局功能', 'success', 2000, 'top-center');
  }, [setLayoutLocked]);

  // 重置手动定位函数
  const handleResetManualPositioning = useCallback(() => {
    setManuallyPositioned(new Set());
    setLayoutLocked(false);

    showNotification('已重置手动定位，布局已解锁', 'success', 2000, 'top-center');
  }, [setManuallyPositioned, setLayoutLocked]);

  // 布局状态指示器组件
  const LayoutStatusIndicator = () => {
    if (!layoutLocked) return null;

    return (
      <div className="absolute top-4 right-4 bg-yellow-100 border border-yellow-300 rounded-lg px-3 py-2 text-sm text-yellow-800 shadow-md z-50">
        <div className="flex items-center gap-2">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          <span>布局已锁定 ({manuallyPositioned.size} 个手动定位节点)</span>
          <button
            onClick={handleUnlockLayout}
            className="ml-2 text-yellow-600 hover:text-yellow-800 underline text-xs"
          >
            解锁
          </button>
        </div>
      </div>
    );
  };

  // 强制应用布局函数
  const handleForceApplyLayout = useCallback((config?: LayoutConfig) => {
    handleAutoLayout(config, true);
  }, [handleAutoLayout]);

  // AI助手相关处理函数
  const handleAssistantToggle = useCallback((buttonPosition?: { x: number; y: number }) => {
    if (buttonPosition) {
      setAssistantButtonPosition(buttonPosition);
    }
    setIsAssistantOpen(prev => !prev);
  }, []);

  const handleAssistantClose = useCallback(() => {
    setIsAssistantOpen(false);
  }, []);

  // 框架提取相关处理函数
  const handleFrameworkExtractToggle = useCallback((buttonPosition?: { x: number; y: number }) => {
    if (buttonPosition) {
      setFrameworkExtractButtonPosition(buttonPosition);
    }
    setIsFrameworkExtractOpen(prev => !prev);
  }, []);

  const handleFrameworkExtractClose = useCallback(() => {
    setIsFrameworkExtractOpen(false);
  }, []);

  // 章节分析相关处理函数
  const handleChapterAnalysisToggle = useCallback((buttonPosition?: { x: number; y: number }) => {
    if (buttonPosition) {
      setChapterAnalysisButtonPosition(buttonPosition);
    }
    setIsChapterAnalysisOpen(prev => !prev);
  }, []);

  const handleChapterAnalysisClose = useCallback(() => {
    setIsChapterAnalysisOpen(false);
  }, []);

  // 示例管理相关处理函数
  const handleExampleManagementToggle = useCallback(() => {
    setIsExampleManagementOpen(prev => !prev);
  }, []);

  const handleExampleManagementClose = useCallback(() => {
    setIsExampleManagementOpen(false);
  }, []);





  const handleAssistantApplyChanges = useCallback((changes: any[]) => {
 
    // 处理AI助手的变更建议
    if (!outline) {
      console.error('❌ 大纲数据不存在，无法应用变更');
      return;
    }


    const updatedOutline = deepCopy(outline);

    changes.forEach((change, index) => {
   

      switch (change.type) {
        case 'create':
  
          if (change.data) {
            const baseNode = {
              id: change.nodeId,
              title: change.data.title || '新节点',
              type: change.data.type || 'dialogue',
              description: change.data.description,
              creativeNotes: change.data.creativeNotes,
              children: [],
              isNew: true
            };

            // 根据节点类型添加专门字段
            let newNode;
            if (change.data.type === 'chapter') {
              newNode = {
                ...baseNode,
                chapterStyle: change.data.chapterStyle,
                chapterTechniques: change.data.chapterTechniques,
                chapterGoals: change.data.chapterGoals,
                rhythmPhase: change.data.rhythmPhase,
                rhythmGuidance: change.data.rhythmGuidance
              };
            } else if (change.data.type === 'plot') {
              newNode = {
                ...baseNode,
                plotPoints: change.data.plotPoints,
                plotType: change.data.plotType,
                relatedCharacters: change.data.relatedCharacters,
                conflictLevel: change.data.conflictLevel,
                suspenseElements: change.data.suspenseElements
              };
            } else if (change.data.type === 'dialogue') {
              newNode = {
                ...baseNode,
                dialogueScene: change.data.dialogueScene,
                participants: change.data.participants,
                dialoguePurpose: change.data.dialoguePurpose,
                dialogueContent: change.data.dialogueContent
              };
            } else {
              newNode = baseNode;
            }

        

            if (change.parentId) {
      
              // 尝试添加为子节点
              const result = addChildNode(updatedOutline.nodes, change.parentId, newNode);
              if (result.found) {
   
                updatedOutline.nodes = result.updatedNodes;
              } else {
                console.warn('⚠️ 父节点未找到，改为创建根节点');
                // 父节点不存在时，创建为根节点
                updatedOutline.nodes.push(newNode);
         
              }
            } else {
      
              // 添加为根节点
              updatedOutline.nodes.push(newNode);

            }
          } else {
            console.error('❌ 创建操作缺少必要数据:', change);
          }
          break;

        case 'update':
          if (change.data) {
            const updateRecursive = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
              return nodes.map(node => {
                if (node.id === change.nodeId) {
                  // 构建更新数据，包含专门字段
                  const updateData = { ...change.data };

                  // 根据节点类型确保专门字段被正确更新
                  if (change.data.type === 'chapter') {
                    updateData.chapterStyle = change.data.chapterStyle;
                    updateData.chapterTechniques = change.data.chapterTechniques;
                    updateData.chapterGoals = change.data.chapterGoals;
                    updateData.rhythmPhase = change.data.rhythmPhase;
                    updateData.rhythmGuidance = change.data.rhythmGuidance;
                  } else if (change.data.type === 'plot') {
                    updateData.plotPoints = change.data.plotPoints;
                    updateData.plotType = change.data.plotType;
                    updateData.relatedCharacters = change.data.relatedCharacters;
                    updateData.conflictLevel = change.data.conflictLevel;
                    updateData.suspenseElements = change.data.suspenseElements;
                  } else if (change.data.type === 'dialogue') {
                    updateData.dialogueScene = change.data.dialogueScene;
                    updateData.participants = change.data.participants;
                    updateData.dialoguePurpose = change.data.dialoguePurpose;
                    updateData.dialogueContent = change.data.dialogueContent;
                  }


                  return { ...node, ...updateData };
                }
                if (node.children) {
                  return { ...node, children: updateRecursive(node.children) };
                }
                return node;
              });
            };
            updatedOutline.nodes = updateRecursive(updatedOutline.nodes);
          }
          break;

        case 'delete':
          const result = deleteNodeAndGetChildren(updatedOutline.nodes, change.nodeId);
          updatedOutline.nodes = result.updatedNodes;
          break;
      }
    });


    onChange(updatedOutline);

    // 🔥 关键修复：添加数据库保存逻辑
    if (bookId) {
      debouncedSave(updatedOutline, bookId);
    }

    showNotification(`AI助手已应用 ${changes.length} 个变更`, 'success', 3000, 'top-center');

  }, [outline, onChange, addChildNode, deleteNodeAndGetChildren, deepCopy]);

  const handlePaneClick = useCallback(() => {
      setSelectedNodeId(null);
      setContextMenu(null);
  }, [setSelectedNodeId, setContextMenu]);
  const handleCloseDetailsPanel = useCallback(() => setViewingNode(null), [setViewingNode]);

  // KEYBOARD SHORTCUTS
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return;
      if (e.ctrlKey && e.key === 'x') {
        e.preventDefault();
        setScissorsModeActive(prev => {
          const newMode = !prev;
          if (newMode) {
            showNotification('剪刀模式已激活：按住 Ctrl 并右键拖动以删除连线', 'info', 5000, 'top-center');
            reactFlowWrapper.current?.classList.add('scissors-mode-cursor');
          } else {
            setScissorsLineStart(null); setScissorsLineEnd(null); setMousePosition(null);
            reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
          }
          return newMode;
        });
      }
      if (e.key === 'Delete') {
        e.preventDefault();
        if (rfSelectedNodes.length > 1) {
            // 批量删除
            handleBatchDelete();
        } else if (rfSelectedNodes.length === 1) {
            // 单个删除
            handleNodeDelete(rfSelectedNodes[0].id);
        } else if (selectedNodeId) {
            // 删除当前选中的节点
            handleNodeDelete(selectedNodeId);
        }
        if (rfSelectedEdges.length > 0) {
            // Simplified edge deletion, direct removal from state
            setEdges(eds => eds.filter(ed => !rfSelectedEdges.some(selEd => selEd.id === ed.id)));
            setRfSelectedEdges([]);
        }
      }

      // 全选功能
      if (e.ctrlKey && e.key === 'a') {
        e.preventDefault();
        setRfSelectedNodes([...nodes]);
      }

      // 复制功能
      if (e.ctrlKey && e.key === 'c' && rfSelectedNodes.length > 0) {
        e.preventDefault();
        handleBatchCopy();
      }
      if (selectedNodeId) {
        if (e.key === 'Enter') handleNodeEdit(selectedNodeId);
        if (e.altKey && e.key === 'c') handleAddChild(selectedNodeId, 'chapter');
        // ... other add child shortcuts
      }
      if (e.ctrlKey && e.key === 'n') { e.preventDefault(); handleAddChild(null, 'chapter'); }
      if (e.key === 'Escape') { setSelectedNodeId(null); setContextMenu(null); setRfSelectedNodes([]); setRfSelectedEdges([]); }
      // ... other shortcuts
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => {
        window.removeEventListener('keydown', handleKeyDown);
        reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
    };
  }, [selectedNodeId, rfSelectedNodes, rfSelectedEdges, handleNodeDelete, handleNodeEdit, handleAddChild, handleBatchDelete, handleBatchCopy, nodes, setScissorsModeActive, setRfSelectedNodes, setEdges, setRfSelectedEdges]);

  const handlePaneContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    if (scissorsModeActive) return;
    // Use clientX/clientY directly for positioning relative to viewport
    setContextMenu({ x: event.clientX, y: event.clientY, type: 'pane' });
  }, [scissorsModeActive, setContextMenu]); // Removed project as it might be causing offset

  // 处理画布鼠标按下事件
  const handlePaneMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.button === 2 && event.ctrlKey) { // 右键 + Ctrl
      event.preventDefault(); event.stopPropagation();
      setScissorsModeActive(true);
      const currentMouseX = event.clientX; const currentMouseY = event.clientY;
      setScissorsLineStart({ x: currentMouseX, y: currentMouseY });
      setScissorsLineEnd({ x: currentMouseX, y: currentMouseY });
      setMousePosition({ x: currentMouseX, y: currentMouseY });
      reactFlowWrapper.current?.classList.add('scissors-mode-cursor');
      setContextMenu(null);
    }
  }, [setScissorsModeActive, setScissorsLineStart, setScissorsLineEnd, setMousePosition, setContextMenu]);

  // 处理画布鼠标移动事件
  const handlePaneMouseMove = useCallback((event: React.MouseEvent) => {
    if (scissorsModeActive) {
      const currentMouseX = event.clientX; const currentMouseY = event.clientY;
      setScissorsLineEnd({ x: currentMouseX, y: currentMouseY });
      setMousePosition({ x: currentMouseX, y: currentMouseY });
    }
  }, [scissorsModeActive, setScissorsLineEnd, setMousePosition]);

  // 更新大纲中的父子关系
  const updateOutlineParentChildRelationship = useCallback((currentOutline: Outline, parentId: string, childId: string) => {
    // 查找并移除子节点
    const findAndRemoveChild = () => {
      let foundChild: OutlineNodeType | null = null;

      const processNode = (node: OutlineNodeType): OutlineNodeType => {
        if (node.id === parentId && node.children) {
          const childIndex = node.children.findIndex(c => c.id === childId);
          if (childIndex !== -1) {
            // 深拷贝子节点
            foundChild = deepCopy(node.children[childIndex]);
            const updatedChildren = [...node.children];

            // 更新兄弟关系
            if (childIndex > 0 && childIndex < updatedChildren.length - 1) {
              // 如果被删除的节点在中间，连接前后节点
              const prevSibling = updatedChildren[childIndex - 1];
              const nextSibling = updatedChildren[childIndex + 1];

              if (prevSibling.nextSiblingId === childId) {
                prevSibling.nextSiblingId = nextSibling.id;
              }
              if (nextSibling.prevSiblingId === childId) {
                nextSibling.prevSiblingId = prevSibling.id;
              }
            } else if (childIndex === 0 && updatedChildren.length > 1) {
              // 如果被删除的节点是第一个，更新下一个节点
              const nextSibling = updatedChildren[1];
              if (nextSibling.prevSiblingId === childId) {
                nextSibling.prevSiblingId = undefined;
              }
            } else if (childIndex === updatedChildren.length - 1 && childIndex > 0) {
              // 如果被删除的节点是最后一个，更新前一个节点
              const prevSibling = updatedChildren[childIndex - 1];
              if (prevSibling.nextSiblingId === childId) {
                prevSibling.nextSiblingId = undefined;
              }
            }

            // 移除子节点
            updatedChildren.splice(childIndex, 1);
            return { ...node, children: updatedChildren };
          }
        }

        if (node.children && node.children.length > 0) {
          return { ...node, children: node.children.map(processNode) };
        }

        return node;
      };

      currentOutline.nodes = currentOutline.nodes.map(processNode);

      // 清除孤立节点的关系
      if (foundChild) {
        (foundChild as any).parentId = undefined;
        (foundChild as any).nextSiblingId = undefined;
        (foundChild as any).prevSiblingId = undefined;
      }

      return foundChild;
    };

    // 查找并移除子节点
    const orphanedChild = findAndRemoveChild();

    // 如果找到了被移除的子节点，将其添加为根节点
    if (orphanedChild) {
      const childId = (orphanedChild as any).id;
      const childExistsAsRoot = currentOutline.nodes.some(node => node.id === childId);
      const childExistsElsewhere = findNode(currentOutline.nodes, childId) !== null;

      if (!childExistsAsRoot && !childExistsElsewhere) {
        currentOutline.nodes.push(orphanedChild as any);
      }
    }

 
  }, []);

  // 更新大纲中的兄弟关系
  const updateOutlineSiblingRelationship = useCallback((currentOutline: Outline, nodeId1: string, nodeId2: string) => {
    // 查找节点的前后兄弟节点
    const findSiblings = (nodes: OutlineNodeType[], targetId: string): { prev: OutlineNodeType | null, next: OutlineNodeType | null, parent: OutlineNodeType | null } => {
      let result = { prev: null as OutlineNodeType | null, next: null as OutlineNodeType | null, parent: null as OutlineNodeType | null };

      for (const node of nodes) {
        // 检查当前节点的子节点
        if (node.children && node.children.length > 0) {
          // 查找目标节点在子节点中的位置
          const childIndex = node.children.findIndex(child => child.id === targetId);

          if (childIndex !== -1) {
            // 找到了目标节点，记录其父节点
            result.parent = node;

            // 记录前一个兄弟节点
            if (childIndex > 0) {
              result.prev = node.children[childIndex - 1];
            }

            // 记录后一个兄弟节点
            if (childIndex < node.children.length - 1) {
              result.next = node.children[childIndex + 1];
            }

            return result;
          }

          // 递归查找子节点
          const childResult = findSiblings(node.children, targetId);
          if (childResult.parent) {
            return childResult;
          }
        }
      }

      // 检查根节点
      const rootIndex = nodes.findIndex(node => node.id === targetId);
      if (rootIndex !== -1) {
        if (rootIndex > 0) {
          result.prev = nodes[rootIndex - 1];
        }
        if (rootIndex < nodes.length - 1) {
          result.next = nodes[rootIndex + 1];
        }
      }

      return result;
    };

    // 断开节点1和节点2之间的连接
    const severLink = (nodesToSearch: OutlineNodeType[]) => {
      nodesToSearch.forEach(n => {
        if (n.id === nodeId1 && n.nextSiblingId === nodeId2) {
          n.nextSiblingId = undefined;

        }
        if (n.id === nodeId2 && n.prevSiblingId === nodeId1) {
          n.prevSiblingId = undefined;

        }
        if (n.children) severLink(n.children);
      });
    };

    // 断开直接连接
    severLink(currentOutline.nodes);

    // 查找节点1的兄弟节点
    const node1Siblings = findSiblings(currentOutline.nodes, nodeId1);
    // 查找节点2的兄弟节点
    const node2Siblings = findSiblings(currentOutline.nodes, nodeId2);

    // 如果节点1是节点2的前一个兄弟，且节点2有后一个兄弟
    if (node1Siblings.next && node1Siblings.next.id === nodeId2 && node2Siblings.next) {
      // 将节点1连接到节点2的后一个兄弟
      const node1 = findNode(currentOutline.nodes, nodeId1);
      const node2Next = node2Siblings.next;

      if (node1) {
        node1.nextSiblingId = node2Next.id;

      }

      if (node2Next) {
        node2Next.prevSiblingId = nodeId1;

      }
    }

    // 如果节点2是节点1的前一个兄弟，且节点1有后一个兄弟
    if (node2Siblings.next && node2Siblings.next.id === nodeId1 && node1Siblings.next) {
      // 将节点2连接到节点1的后一个兄弟
      const node2 = findNode(currentOutline.nodes, nodeId2);
      const node1Next = node1Siblings.next;

      if (node2) {
        node2.nextSiblingId = node1Next.id;

      }

      if (node1Next) {
        node1Next.prevSiblingId = nodeId2;

      }
    }

    console.log('更新兄弟关系后的大纲:', deepCopy(currentOutline));
  }, []);

  const handlePaneMouseUp = useCallback((_event: React.MouseEvent) => {
    if (scissorsModeActive && scissorsLineStart && scissorsLineEnd) {
      const rfBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!rfBounds) {
        setScissorsModeActive(false);
        reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
        return;
      }

      // 清除所有边的高亮效果
      document.querySelectorAll('.edge-highlight').forEach(el => {
        el.classList.remove('edge-highlight');
      });

      const viewport = getViewport();

      // 查找与剪刀线相交的边
      const edgesToDelete = edges.filter(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        const targetNode = nodes.find(n => n.id === edge.target);

        if (sourceNode && targetNode && sourceNode.position && targetNode.position) {
          const getNodeCenterInViewport = (node: Node<any, string | undefined>, vp: Viewport, bounds: DOMRect) => {
            const nodeWidth = node.width || 150; const nodeHeight = node.height || 40;
            const canvasX = node.position.x + nodeWidth / 2; const canvasY = node.position.y + nodeHeight / 2;
            return { x: bounds.left + (canvasX * vp.zoom + vp.x), y: bounds.top + (canvasY * vp.zoom + vp.y) };
          };

          const sourcePosVp = getNodeCenterInViewport(sourceNode, viewport, rfBounds);
          const targetPosVp = getNodeCenterInViewport(targetNode, viewport, rfBounds);

          // 检测剪刀线与边是否相交
          const isIntersecting = segmentsIntersect(
            scissorsLineStart.x, scissorsLineStart.y, scissorsLineEnd.x, scissorsLineEnd.y,
            sourcePosVp.x, sourcePosVp.y, targetPosVp.x, targetPosVp.y,
            10 // 增加容错范围
          );

          return isIntersecting;
        }
        return false;
      });

      // 如果找到相交的边，添加删除动画并更新数据
      if (edgesToDelete.length > 0) {
     

        // 添加删除动画效果
        edgesToDelete.forEach(edge => {
          const edgeElement = document.getElementById(edge.id);
          if (edgeElement) {
            edgeElement.classList.add('edge-delete');
            // 添加淡出动画
            edgeElement.style.opacity = '0';
            edgeElement.style.transition = 'opacity 0.3s ease';
          }
        });

        // 延迟删除边，等待动画完成
        setTimeout(() => {
          // 从状态中移除边
          setEdges(eds => eds.filter(ed => !edgesToDelete.some(delEd => delEd.id === ed.id)));

          // 如果有大纲数据，更新大纲关系
          if (outline) {
            const updatedOutline = deepCopy(outline);

            // 更新每条边的关系

            edgesToDelete.forEach(edge => {
              if (edge.source && edge.target) { // 确保源和目标已定义

                if (edge.data?.relationshipType === 'parent-child') {

                  updateOutlineParentChildRelationship(updatedOutline, edge.source, edge.target);
                } else if (edge.data?.relationshipType === 'sibling') {

                  updateOutlineSiblingRelationship(updatedOutline, edge.source, edge.target);
                } else {

                }
              }
            });

            // 更新大纲数据
            onChange(updatedOutline);

            // 确保edges状态与大纲数据一致
          
            const handlers = {
              onEdit: handleNodeEdit,
              onDelete: handleNodeDelete,
              onAddChild: handleAddChild,
              onSelect: handleNodeSelect,
              onUpdate: handleNodeUpdate
            };
            const { edges: newEdges } = transformOutlineToFlow(updatedOutline, selectedNodeId, handlers);
           
            setEdges(newEdges);

            // 显示成功通知
            showNotification(`已删除 ${edgesToDelete.length} 条连接`, 'success', 3000, 'top-center');

            // 添加数据库持久化逻辑
            if (bookId) {
              // 使用立即执行的异步函数
              (async () => {
                try {
                  // 导入 outlineService
                  const { outlineService } = await import('@/factories/api/outlineService');
       

                  // 等待保存完成
                  await outlineService.saveOutline(bookId, updatedOutline);
   
                } catch (error) {
                  console.error('大纲数据保存失败:', error);
                  showNotification('保存失败，请手动保存您的更改', 'error', 5000, 'top-center');
                }
              })();
            }
          }
        }, 300);
      } else {

      }
    }

    // 重置剪刀模式状态
    if (scissorsModeActive) {
      setScissorsModeActive(false);
      setScissorsLineStart(null);
      setScissorsLineEnd(null);
      setMousePosition(null);
      reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
    }
  }, [
    scissorsModeActive, scissorsLineStart, scissorsLineEnd, edges, nodes, getViewport, outline,
    onChange, bookId, setEdges, reactFlowWrapper, selectedNodeId, transformOutlineToFlow,
    handleNodeEdit, handleNodeDelete, handleAddChild, handleNodeSelect, handleNodeUpdate
  ]);

  // 强制更新节点和边的样式 - 优化版本，保护位置信息
  const updateStyles = useCallback(() => {
    try {

      // 首先保存当前所有节点的位置信息
      const currentPositions = new Map();
      nodes.forEach(node => {
        currentPositions.set(node.id, { ...node.position });
      });


      // 使用组件级别获取的样式上下文


      // 获取预设样式
      const currentPreset = styleContext.getCurrentPreset();


      // 更新边的样式
      const updatedEdges = edges.map(edge => {
        const edgeType = edge.data?.relationshipType || 'parent-child';
        const edgeStyle = styleContext.getEdgeStyle(edgeType);

        // 确保颜色值有效
        if (!edgeStyle.strokeColor || edgeStyle.strokeColor === 'undefined') {
          console.warn('边样式颜色无效，使用默认颜色:', edgeStyle);
          edgeStyle.strokeColor = '#3b82f6'; // 默认蓝色
        }

        // 确保箭头类型有效
        if (!edgeStyle.arrowHeadType) {
          edgeStyle.arrowHeadType = 'arrow'; // 默认箭头类型
        }

        // 确保动画设置有效
        if (edgeStyle.animated === undefined) {
          edgeStyle.animated = false; // 默认不启用动画
        }

        // 确保动画速度有效
        if (edgeStyle.animated && !edgeStyle.animationSpeed) {
          edgeStyle.animationSpeed = 1; // 默认动画速度
        }

        // 创建新的边对象
        return {
          ...edge,
          type: 'customEdge', // 确保使用自定义边组件
          // 直接应用样式
          style: {
            ...edge.style,
            stroke: edgeStyle.strokeColor,
            strokeWidth: edgeStyle.strokeWidth,
            strokeDasharray: edgeStyle.strokeDasharray,
            opacity: edgeStyle.strokeOpacity,
          },
          // 触发边的重新渲染并传递样式信息
          data: {
            ...edge.data,
            _styleUpdate: Date.now(),
            edgeStyle: edgeStyle, // 直接传递样式对象
            relationshipType: edgeType, // 确保关系类型正确
            animated: edgeStyle.animated, // 明确传递动画标志
            animationSpeed: edgeStyle.animationSpeed, // 明确传递动画速度
            arrowHeadType: edgeStyle.arrowHeadType, // 明确传递箭头类型
          }
        };
      });


      setEdges(updatedEdges);

      // 更新节点的样式 - 强化位置保护机制
      const updatedNodes = nodes.map(node => {
        const nodeType = node.data?.type || 'default';
        const nodeStyle = styleContext.getNodeStyle(nodeType);
        const isHighlighted = highlightedNodeIds.has(node.id);

        // 从保存的位置信息中恢复位置，确保不被覆盖
        const preservedPosition = currentPositions.get(node.id) || node.position;


        return {
          ...node,
          // 使用保存的位置信息，确保位置不被样式更新影响
          position: preservedPosition,
          // 直接应用样式，但避免影响位置相关的transform
          style: {
            ...node.style,
            background: nodeStyle.backgroundColor,
            borderColor: isHighlighted ? '#667eea' : nodeStyle.borderColor,
            borderWidth: isHighlighted ? '3px' : nodeStyle.borderWidth,
            borderRadius: nodeStyle.borderRadius,
            boxShadow: isHighlighted
              ? '0 0 20px rgba(102, 126, 234, 0.6), 0 0 40px rgba(102, 126, 234, 0.3)'
              : nodeStyle.boxShadow,
            // 注意：移除transform的scale，因为它可能与ReactFlow的位置transform冲突
            // transform: isHighlighted ? 'scale(1.05)' : 'scale(1)',
            transition: 'all 0.3s ease',
            zIndex: isHighlighted ? 1000 : 'auto',
          },
          // 触发节点的重新渲染并传递样式信息
          data: {
            ...node.data,
            _styleUpdate: Date.now(),
            nodeStyle: nodeStyle, // 直接传递样式对象
            isHighlighted: isHighlighted, // 传递高亮状态
            _positionProtected: true, // 标记位置已被保护
          }
        };
      });

      setNodes(updatedNodes);

      // 验证位置信息是否正确保护
      setTimeout(() => {

        const positionVerification = updatedNodes.map(node => ({
          id: node.id,
          expectedPosition: currentPositions.get(node.id),
          actualPosition: node.position,
          isCorrect: JSON.stringify(currentPositions.get(node.id)) === JSON.stringify(node.position)
        }));

        const incorrectPositions = positionVerification.filter(v => !v.isCorrect);
        if (incorrectPositions.length > 0) {
          console.warn('发现位置信息不一致的节点:', incorrectPositions);
          // 如果发现位置不一致，进行修复
          setNodes(currentNodes =>
            currentNodes.map(node => {
              const expectedPosition = currentPositions.get(node.id);
              if (expectedPosition && JSON.stringify(node.position) !== JSON.stringify(expectedPosition)) {
             
                return { ...node, position: expectedPosition };
              }
              return node;
            })
          );
        }
      }, 100);

      // 显示成功通知
      showNotification('样式已应用', 'success', 3000, 'top-center');
    } catch (error: any) {
      console.error('应用样式时出错:', error);
      showNotification('应用样式失败: ' + (error.message || '未知错误'), 'error', 5000, 'top-center');
    }
  }, [edges, nodes, setEdges, setNodes, styleContext, highlightedNodeIds]);

  // 防抖的样式更新函数
  const debouncedUpdateStyles = useCallback(
    debounce(() => {

      updateStyles();
    }, 50), // 50ms防抖延迟
    [updateStyles]
  );

  // 处理节点高亮 - 优化版本
  const handleNodeHighlight = useCallback((nodeIds: string[]) => {


    // 如果节点ID列表没有变化，直接返回
    const currentHighlightedIds = Array.from(highlightedNodeIds).sort().join(',');
    const newHighlightedIds = nodeIds.sort().join(',');
    if (currentHighlightedIds === newHighlightedIds) {
  
      return;
    }

    setHighlightedNodeIds(new Set(nodeIds));

    // 使用防抖的样式更新，减少频繁更新
    debouncedUpdateStyles();

    // 3秒后清除高亮
    setTimeout(() => {

      setHighlightedNodeIds(new Set());
      // 清除高亮后也要更新样式，使用防抖
      debouncedUpdateStyles();
    }, 3000);
  }, [highlightedNodeIds, debouncedUpdateStyles]);

  useEffect(() => {
    const styleId = 'outline-canvas-styles';
    if (document.getElementById(styleId)) return;
    const styleElement = document.createElement('style'); styleElement.id = styleId;
    styleElement.textContent = `
      .scissors-mode-cursor { cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path stroke="black" stroke-width="1.5" fill="white" d="M6.5,9.3l-2.4,4.5c-0.4,0.8,0.3,1.7,1.2,1.7h2.4c0.4,0,0.7-0.2,0.9-0.5l1.6-2.7L6.5,9.3z M17.5,9.3l2.4,4.5c0.4,0.8-0.3,1.7-1.2,1.7h-2.4c-0.4,0-0.7-0.2-0.9-0.5l-1.6-2.7L17.5,9.3z M9.4,9.3C8.6,8.7,7.8,7.8,7.5,7.5c-0.6-0.6-1.4-0.6-2,0c-0.6,0.6-0.6,1.4,0,2c0.3,0.3,1.2,1.2,1.8,2L9.4,9.3z M14.6,9.3c0.8-0.6,1.6-1.5,1.8-1.8c0.6-0.6,1.4-0.6,2,0c0.6,0.6,0.6,1.4,0,2c-0.3,0.3-1.2,1.2-1.8,2L14.6,9.3z M12,11.5l-2-3.5h4L12,11.5z\"/></svg>') 4 18, crosshair; }
      .edge-delete { animation: fade-out 0.3s ease-out forwards; }
      @keyframes fade-out { from { opacity: 1; } to { opacity: 0; } }

      /* 自定义滚动条样式 */
      .outline-canvas-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      .outline-canvas-container::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }
      .outline-canvas-container::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        transition: background 0.2s ease;
      }
      .outline-canvas-container::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
      }
      .outline-canvas-container::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0.05);
      }

      /* 确保按钮不被遮挡 */
      .react-flow__panel {
        z-index: 1000 !important;
      }
      .assistant-panel {
        z-index: 1001 !important;
      }
      .react-flow__controls {
        z-index: 1000 !important;
      }
      .react-flow__minimap {
        z-index: 1000 !important;
      }
    `;

    document.head.appendChild(styleElement);
    return () => { const el = document.getElementById(styleId); if (el) document.head.removeChild(el); };
  }, []);

  return (
    <div className={`outline-canvas-wrapper ${isAssistantOpen ? 'assistant-open' : ''}`}>
      <div
        className="outline-canvas-container"
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          willChange: 'transform',
          overflow: 'auto',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(0, 0, 0, 0.3) transparent'
        }}
        ref={reactFlowWrapper}
      >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChangeReactFlow}
        onConnect={onConnect}
        onNodeDragStop={onNodeDragStop}
        onPaneClick={handlePaneClick}
        onNodeClick={(_, node) => handleNodeSelect(node.id)}
        onEdgeClick={(_, edge) => {
            setSelectedNodeId(null);
            document.querySelectorAll('.selected-edge').forEach(el => el.classList.remove('.selected-edge'));
            document.getElementById(edge.id)?.classList.add('.selected-edge');
            setRfSelectedEdges([edge]); // Set this for Delete key to work on edges
        }}
        onNodeContextMenu={(event, node) => {
          event.preventDefault(); event.stopPropagation();
          // Use clientX/clientY directly for positioning
          setContextMenu({ x: event.clientX, y: event.clientY, type: 'node', nodeId: node.id });
          handleNodeSelect(node.id); // Select the node when its context menu is opened
        }}
        onContextMenu={handlePaneContextMenu} // This will still be called on pane, but our more specific onNodeContextMenu handles nodes
        onMouseDown={handlePaneMouseDown}
        onMouseMove={handlePaneMouseMove}
        onMouseUp={handlePaneMouseUp}
        onMove={(event, _viewport) => {
          // 改进缩放行为：确保缩放以鼠标位置为中心
          if (event && event.type === 'wheel') {
            // ReactFlow已经处理了以鼠标为中心的缩放
            // 这里可以添加额外的缩放逻辑或验证
          }
        }}
        onSelectionChange={({ nodes: selNodes, edges: selEdges }) => {
          setRfSelectedNodes(selNodes);
          setRfSelectedEdges(selEdges);
        }}
        selectionMode={SelectionMode.Partial}
        selectionOnDrag={true}
        multiSelectionKeyCode="Control"
        selectionKeyCode="Shift"
        fitView
        defaultEdgeOptions={{ type: 'customEdge', animated: true, data: { relationshipType: 'parent-child' } }}
        connectionLineType={ConnectionLineType.Bezier}
        minZoom={0.2} maxZoom={2.5}

        // 性能优化配置
        nodesDraggable={true}
        elementsSelectable={true}
        selectNodesOnDrag={false}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        snapToGrid={false}
        snapGrid={[15, 15]}
        nodeExtent={[[-5000, -5000], [10000, 10000]]}
        translateExtent={[[-10000, -10000], [20000, 20000]]}
        elevateNodesOnSelect={true}
        style={{ willChange: 'transform', overflow: 'auto' }}
      >
        {/* 定义全局marker */}
        <svg style={{ position: 'absolute', top: 0, left: 0, width: 0, height: 0 }}>
          <defs>
            {/* 开放箭头 */}
            <marker
              id="arrow"
              viewBox="0 0 10 10"
              refX="8"
              refY="5"
              markerWidth="8"
              markerHeight="8"
              orient="auto-start-reverse"
            >
              <path d="M 0 0 L 10 5 L 0 10" stroke="currentColor" fill="none" />
            </marker>

            {/* 闭合箭头 */}
            <marker
              id="arrowclosed"
              viewBox="0 0 10 10"
              refX="8"
              refY="5"
              markerWidth="8"
              markerHeight="8"
              orient="auto-start-reverse"
            >
              <path d="M 0 0 L 10 5 L 0 10 z" fill="currentColor" />
            </marker>

            {/* 圆形箭头 */}
            <marker
              id="circle"
              viewBox="0 0 10 10"
              refX="7"
              refY="5"
              markerWidth="7"
              markerHeight="7"
              orient="auto-start-reverse"
            >
              <circle cx="5" cy="5" r="4" fill="currentColor" />
            </marker>

            {/* 菱形箭头 */}
            <marker
              id="diamond"
              viewBox="0 0 10 10"
              refX="8"
              refY="5"
              markerWidth="8"
              markerHeight="8"
              orient="auto-start-reverse"
            >
              <path d="M 0 5 L 5 0 L 10 5 L 5 10 z" fill="currentColor" />
            </marker>

            {/* 无箭头 */}
            <marker
              id="none"
              viewBox="0 0 10 10"
              refX="0"
              refY="0"
              markerWidth="0"
              markerHeight="0"
            >
              <path d="" />
            </marker>
          </defs>
        </svg>
        <Background />
        <Controls />
        {showMiniMap && <MiniMap />}

        {/* 拖拽网格 */}
        <DragGrid
          isDragging={isDragging}
          position={dragPosition || undefined}
          viewport={getViewport()}
          gridSize={50}
          gridColor="#e5e7eb"
          opacity={0.5}
        />

        {/* 布局状态指示器 */}
        <LayoutStatusIndicator />
        <CanvasMenu
          layoutMode={layoutMode}
          setLayoutMode={setLayoutMode}
          handleAutoLayout={handleAutoLayout}
          handlePreviewLayout={handlePreviewLayout}
          cancelPreviewLayout={cancelPreviewLayout}
          isPreviewMode={isPreviewMode}
          currentLayoutConfig={currentLayoutConfig}
          scissorsModeActive={scissorsModeActive}
          toggleScissorsMode={() => {
            setScissorsModeActive(prev => {
              const newMode = !prev;
              if (newMode) {
                showNotification('剪刀模式已激活：按住 Ctrl 并右键拖动以删除连线', 'info', 5000, 'top-center');
                reactFlowWrapper.current?.classList.add('scissors-mode-cursor');
              } else {
                setScissorsLineStart(null);
                setScissorsLineEnd(null);
                setMousePosition(null);
                reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
              }
              return newMode;
            });
          }}
          showShortcuts={showShortcuts}
          setShowShortcuts={setShowShortcuts}
          selectedNodeId={selectedNodeId}
          handleAddChild={handleAddChild}
          updateStyles={updateStyles}
          // 新增布局控制相关属性
          layoutLocked={layoutLocked}
          isSaving={isSaving}
          lastSaveTime={lastSaveTime}
          manuallyPositionedCount={manuallyPositioned.size}
          onUnlockLayout={handleUnlockLayout}
          onResetManualPositioning={handleResetManualPositioning}
          onForceApplyLayout={handleForceApplyLayout}
          // 小地图控制相关属性
          showMiniMap={showMiniMap}
          onToggleMiniMap={toggleMiniMap}
        />

        {/* AI助手和框架提取按钮组 - 放在Panel中 */}
        <Panel position="top-right" className="assistant-panel">
          <div className="button-group">
            {/* 🔥 新增：折叠/展开控制按钮 */}
            <div className="flex items-center space-x-2 mb-2">
              <button
                onClick={() => toggleAllNodesCollapse(true)}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200 flex items-center space-x-1"
                title="折叠所有节点"
                disabled={!outline}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
                <span>折叠</span>
              </button>
              <button
                onClick={() => toggleAllNodesCollapse(false)}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200 flex items-center space-x-1"
                title="展开所有节点"
                disabled={!outline}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
                <span>展开</span>
              </button>
            </div>

            <FloatingActionGroup
              onAssistantClick={handleAssistantToggle}
              onFrameworkExtractClick={handleFrameworkExtractToggle}
              onChapterAnalysisClick={handleChapterAnalysisToggle}
              onExampleManagementClick={handleExampleManagementToggle}
              isAssistantActive={isAssistantOpen}
              isFrameworkExtractActive={isFrameworkExtractOpen}
              isChapterAnalysisActive={isChapterAnalysisOpen}
              disabled={!outline}
            />
          </div>
        </Panel>
      </ReactFlow>

      {viewingNode && (
        <InlineNodeDetailsPanel
          node={viewingNode}
          position={viewingNode.position || { x:0, y:0 }}
          onClose={handleCloseDetailsPanel}
          containerRef={reactFlowWrapper}
          onSelectNode={handleNodeSelect}
          relatedItemsMap={relatedItemsMap}
        />
      )}

      <ContextMenuManager
        contextMenu={contextMenu}
        setContextMenu={setContextMenu}
        outline={outline}
        rfSelectedNodes={rfSelectedNodes}
        setRfSelectedNodes={setRfSelectedNodes}
        handleNodeEdit={handleNodeEdit}
        handleNodeDelete={handleNodeDelete}
        handleAddChild={handleAddChild}
        handleNodeSelect={handleNodeSelect}
        scissorsModeActive={scissorsModeActive}
      />

      <KeyboardShortcuts showShortcuts={showShortcuts} />

      {/* 批量操作工具栏 */}
      <BatchOperationToolbar
        selectedNodes={rfSelectedNodes}
        onBatchDelete={handleBatchDelete}
        onBatchCopy={handleBatchCopy}
        onClearSelection={handleClearSelection}
        onSelectSimilar={handleSelectSimilar}
      />

      {/* 预览节点确认创建按钮 */}
      {selectedPreviewNodeIds.size > 0 && (
        <div
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 1000,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '14px',
            fontWeight: '600',
            animation: 'slideInUp 0.3s ease-out',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
          onClick={handleCreateSelectedPreviewNodes}
        >
          <span>✨</span>
          <span>创建选中节点 ({selectedPreviewNodeIds.size})</span>
        </div>
      )}

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        isOpen={deleteConfirmOpen}
        nodeData={nodeToDelete}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        showRecoveryHint={true}
      />

      {/* 批量删除确认弹窗 */}
      <BatchDeleteConfirmDialog
        isOpen={batchDeleteConfirmOpen}
        nodesToDelete={nodesToBatchDelete}
        onConfirm={handleConfirmBatchDelete}
        onCancel={handleCancelBatchDelete}
        showRecoveryHint={true}
      />

      {/* 剪刀线渲染 - 简化版 */}
      {scissorsModeActive && scissorsLineStart && scissorsLineEnd && mousePosition && reactFlowWrapper.current && (
        <svg style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 1000 }}>
          <line
            className="scissors-line"
            x1={scissorsLineStart.x - reactFlowWrapper.current.getBoundingClientRect().left}
            y1={scissorsLineStart.y - reactFlowWrapper.current.getBoundingClientRect().top}
            x2={scissorsLineEnd.x - reactFlowWrapper.current.getBoundingClientRect().left}
            y2={scissorsLineEnd.y - reactFlowWrapper.current.getBoundingClientRect().top}
            stroke="red"
            strokeWidth={3}
            strokeDasharray="5,5"
          />
          <text
            x={mousePosition.x - reactFlowWrapper.current.getBoundingClientRect().left}
            y={mousePosition.y - reactFlowWrapper.current.getBoundingClientRect().top}
            fontSize="24"
            fill="red"
            dominantBaseline="middle"
            textAnchor="middle"
            style={{ transform: 'translate(10px, -10px)' }}
          >
            ✂️
          </text>
        </svg>
      )}

      </div>

      {/* AI助手抽屉 - 移到画布容器外，与画布平级 */}
      <AssistantDrawer
        isOpen={isAssistantOpen}
        onClose={handleAssistantClose}
        outline={outline}
        bookId={bookId || ''}
        onApplyChanges={handleAssistantApplyChanges}
        selectedNodeId={selectedNodeId}
        buttonPosition={assistantButtonPosition || undefined}
        onNodeHighlight={handleNodeHighlight}
        onPreviewNodesChange={setPreviewNodes}
        onSelectedPreviewNodesChange={(selectedIds) => setSelectedPreviewNodeIds(new Set(selectedIds))}
        onPreviewNodeToggleFromCanvas={handlePreviewNodeToggleFromCanvas}
        onRegisterToggleFunction={handleRegisterToggleFunction}
        onRegisterClearFunction={handleRegisterClearFunction}
        // 🔥 新增：传递稳定的章节选择状态回调
        onSelectedChapterIdsChange={handleSelectedChapterIdsChange}
      />

      {/* 框架提取弹窗 */}
      <OutlineFrameworkExtractDialog
        isOpen={isFrameworkExtractOpen}
        onClose={handleFrameworkExtractClose}
        outline={outline}
        bookId={bookId || ''}
        buttonPosition={frameworkExtractButtonPosition || undefined}
      />

      {/* 章节分析弹窗 */}
      <ChapterAnalysisDialog
        isOpen={isChapterAnalysisOpen}
        onClose={handleChapterAnalysisClose}
        outline={outline}
        bookId={bookId || ''}
        buttonPosition={chapterAnalysisButtonPosition || undefined}
      />

      {/* 示例管理弹窗 */}
      <ExampleManagementDialog
        isOpen={isExampleManagementOpen}
        onClose={handleExampleManagementClose}
        onExampleActivated={(example) => {
          console.log('示例已激活:', example);
        }}
      />
    </div>
  );
};

// 修改导出方式，用ReactFlowProvider、StyleProvider和ErrorBoundary包装组件
export default function WrappedOutlineCanvas(props: OutlineCanvasProps) {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('🚨 OutlineCanvas发生严重错误:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      props: {
        hasOutline: !!props.outline,
        bookId: props.bookId,
        selectionMode: props.selectionMode,
        selectedNodeIds: props.selectedNodeIds?.length || 0
      }
    });
  };

  return (
    <OutlineCanvasErrorBoundary onError={handleError}>
      <ReactFlowProvider>
        <StyleProvider>
          <OutlineCanvas {...props} />
        </StyleProvider>
      </ReactFlowProvider>
    </OutlineCanvasErrorBoundary>
  );
}
