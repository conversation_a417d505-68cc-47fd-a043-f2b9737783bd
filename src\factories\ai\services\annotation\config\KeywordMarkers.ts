/**
 * 句子关键词标记配置
 * 用于AI辅助识别和标记不同类型的句子
 */

import { Sentence } from '../types/AnnotationTypes';

/**
 * 文本类型枚举
 */
export type TextType = 'dialogue' | 'narrative' | 'description' | 'thought';

/**
 * 阈值配置接口
 */
export interface ThresholdConfig {
  /** 密度阈值（人称代词/总词数） */
  density: number;
  /** 连续性阈值（连续句子中的重复度） */
  continuity: number;
  /** 分析窗口大小 */
  windowSize: number;
}

/**
 * 人称代词统计接口
 */
export interface PronounStats {
  他: number;
  她: number;
  我: number;
  你: number;
  totalWords: number;
  distribution: Array<{sentence: number, pronouns: string[]}>;
}

/**
 * 修辞手法分析结果接口
 */
export interface RhetoricalAnalysis {
  /** 是否检测到排比修辞 */
  hasParallelism: boolean;
  /** 结构相似度 (0-1) */
  structureSimilarity: number;
  /** 模式类型 */
  patternType: 'repetitive' | 'varied' | 'mixed';
  /** 语义变化程度 (0-1) */
  semanticVariation: number;
  /** 推荐操作 */
  recommendedAction: 'ignore' | 'caution' | 'suggest';
  /** 涉及的句子索引 */
  sentenceIndices: number[];
  /** 检测到的结构模式 */
  detectedPattern: string;
}

/**
 * 人称代词分析结果接口
 */
export interface PersonPronounAnalysis {
  /** 是否过度使用 */
  isOverused: boolean;
  /** 密度值 */
  density: number;
  /** 连续性值 */
  continuity: number;
  /** 文体类型 */
  textType: TextType;
  /** 修改建议 */
  suggestions: string[];
  /** 窗口统计信息 */
  windowStats: {
    totalPronouns: number;
    dominantPronoun: string;
    distribution: Array<{sentence: number, pronouns: string[]}>;
  };
  /** 修辞手法分析结果 */
  rhetoricalAnalysis?: RhetoricalAnalysis;
}

export interface KeywordMarker {
  /** 标记类型 */
  type: 'dialogue' | 'action' | 'emotion' | 'scene' | 'transition' | 'special';
  /** 标记名称 */
  name: string;
  /** 关键词列表 */
  keywords: string[];
  /** 标记优先级 (1-10, 数字越大优先级越高) */
  priority: number;
  /** 标记颜色 (用于UI显示) */
  color: string;
  /** 标记图标 */
  icon: string;
  /** 描述 */
  description: string;
}

/**
 * 基于文体的智能阈值配置
 */
export const THRESHOLDS: Record<TextType, ThresholdConfig> = {
  dialogue: { density: 0.25, continuity: 0.6, windowSize: 5 },    // 对话：放宽限制
  narrative: { density: 0.15, continuity: 0.4, windowSize: 7 },   // 叙述：标准限制
  description: { density: 0.10, continuity: 0.3, windowSize: 7 }, // 描述：严格限制
  thought: { density: 0.30, continuity: 0.7, windowSize: 5 }      // 心理：最宽松
};

/**
 * 关键词标记配置
 */
export const KEYWORD_MARKERS: KeywordMarker[] = [
  // 对话句
  {
    type: 'dialogue',
    name: '对话句',
    keywords: [
      '说', '道', '问', '答', '回答', '询问', '告诉', '解释', '声音', '语气',
      '话语', '言语', '开口', '出声', '低声', '高声', '轻声', '大声',
      '\u201c', '\u201d', '\u2018', '\u2019', '\u300c', '\u300d', '\u300e', '\u300f'
    ],
    priority: 8,
    color: '#10B981',
    icon: '\ud83d\udcac',
    description: '对话内容句子，可通过多种方式优化：①语气调整(正式↔口语化) ②节奏控制(长句拆分/短句合并) ③个性化表达(符合角色特点) ④情感层次(增加潜台词)'
  },

  // 动作句
  {
    type: 'action',
    name: '动作句',
    keywords: [
      '走', '跑', '站', '坐', '躺', '跳', '爬', '飞', '游', '滑',
      '拿', '放', '抓', '握', '推', '拉', '举', '扔', '接', '递',
      '看', '望', '瞧', '瞪', '盯', '瞥', '扫', '凝视', '注视',
      '听', '闻', '嗅', '摸', '碰', '触', '感受', '体验'
    ],
    priority: 7,
    color: '#F59E0B',
    icon: '\ud83c\udfc3',
    description: '动作行为句子，可通过多种方式优化：①动作细化("走"→"大步流星"/"蹑手蹑脚") ②感官描写(视觉/听觉/触觉) ③节奏变化(快慢结合) ④空间感营造(方位/距离)'
  },

  // 情感句
  {
    type: 'emotion',
    name: '情感句',
    keywords: [
      '高兴', '快乐', '兴奋', '激动', '喜悦', '愉快', '开心', '欣喜',
      '悲伤', '难过', '痛苦', '伤心', '哀伤', '忧郁', '沮丧', '失落',
      '愤怒', '生气', '恼怒', '暴怒', '愤慨', '恼火', '气愤',
      '恐惧', '害怕', '惊恐', '恐慌', '畏惧', '胆怯', '紧张',
      '惊讶', '震惊', '诧异', '意外', '吃惊', '惊奇', '惊愕'
    ],
    priority: 6,
    color: '#EF4444',
    icon: '\u2764\ufe0f',
    description: '情感心理句子，可通过多种方式优化：①情感层次(表层→深层情感) ②表达方式(直接→间接表达) ③身体反应(心理→生理表现) ④环境映衬(情景交融)'
  },

  // 场景句
  {
    type: 'scene',
    name: '场景句',
    keywords: [
      '房间', '客厅', '卧室', '厨房', '办公室', '教室', '医院', '商店',
      '街道', '公园', '广场', '山', '海', '河', '湖', '森林', '草地',
      '天空', '云', '太阳', '月亮', '星星', '雨', '雪', '风',
      '建筑', '房子', '楼', '桥', '路', '门', '窗', '墙'
    ],
    priority: 5,
    color: '#8B5CF6',
    icon: '\ud83c\udf1e\ufe0f',
    description: '环境场景句子，可通过多种方式优化：①细节丰富(五感描写) ②层次分明(远中近景) ③氛围营造(色彩/光影/声音) ④动静结合(静态描述+动态元素)'
  },

  // 转折句
  {
    type: 'transition',
    name: '转折句',
    keywords: [
      '但是', '然而', '不过', '可是', '只是', '却', '而', '反而',
      '相反', '与此同时', '同时', '接着', '然后', '于是', '因此',
      '所以', '因为', '由于', '既然', '虽然', '尽管', '即使'
    ],
    priority: 4,
    color: '#06B6D4',
    icon: '\ud83d\udd04',
    description: '转折变化句子，可通过多种方式优化：①转折词替换("但是"→"然而"/"不过") ②逻辑强化(因果关系明确) ③节奏调控(急转/缓转) ④情感递进(层层深入)'
  },

  // 特殊句
  {
    type: 'special',
    name: '特殊句',
    keywords: [
      '系统', '提示', '任务', '技能', '等级', '经验', '属性', '装备',
      '道具', '物品', '背包', '商店', '交易', '金币', '积分',
      '\u3010', '\u3011', '\u300e', '\u300f', '\u203b', '\u2605', '\u2606', '\u25c6', '\u25c7', '\u25a0', '\u25a1'
    ],
    priority: 3,
    color: '#84CC16',
    icon: '\u26a1',
    description: '特殊元素句子，可通过多种方式优化：①格式规范(统一标记样式) ②信息精简(核心内容突出) ③视觉区分(与正文分离) ④功能明确(作用清晰)'
  },

  // 模糊表达
  {
    type: 'transition',
    name: '模糊表达',
    keywords: [
      '一些', '几个', '几分', '一丝', '些许', '有些', '某些', '不少',
      '很多', '许多', '大量', '少量', '一点', '一下', '一会儿',
      '仿佛', '似乎', '好像', '大概', '可能', '也许', '或许', '大约',
      '差不多', '基本上', '几乎', '将近', '接近', '左右', '上下'
    ],
    priority: 9,
    color: '#F97316',
    icon: '\ud83c\udf2b\ufe0f',
    description: '模糊表达词汇，可通过多种方式优化：①数量具体化("一些"→"三个"/"五种") ②程度具体化("很"→"极其"/"相当") ③时间具体化("不久"→"三天后") ④感官具体化("有点"→"微微发烫")'
  },

  // 套路表达
  {
    type: 'transition',
    name: '套路表达',
    keywords: [
      '如刀锋般', '宛如', '犹如', '仿佛', '好似', '恰似', '正如',
      '心如刀绞', '五味杂陈', '百感交集', '千头万绪', '万念俱灰',
      '无形大手', '神秘力量', '莫名其妙', '不知不觉', '恍如隔世',
      '刹那间', '瞬间', '突然', '忽然', '猛然', '霎时', '顷刻',
      '内心深处', '灵魂深处', '心底最深处', '内心最柔软的地方'
    ],
    priority: 8,
    color: '#EC4899',
    icon: '\ud83d\udd04',
    description: '套路化表达，可通过多种方式优化：①词汇替换("突然"→"猛地"/"瞬间") ②句式重构(改变语序结构) ③情境化改写(结合具体场景) ④创新表达(避免常见套路)'
  },

  // 人称过度
  {
    type: 'transition',
    name: '人称过度',
    keywords: [
      '他他', '她她', '我我', '你你', // 连续重复
      '他走', '他看', '他说', '他想', '他感', '他听', '他拿', '他知道',
      '她走', '她看', '她说', '她想', '她感', '她听', '她拿', '她知道',
      '我走', '我看', '我说', '我想', '我感', '我听', '我拿', '我知道'
    ],
    priority: 9,
    color: '#DC2626',
    icon: '\u26a0\ufe0f',
    description: '前后句子的人称代词使用可能过度但要分情况去优化，如果句式太相同则考虑可能是别的以下内容方式将不适合，可通过多种方式优化：①用具体角色名/职业替换 ②调整代词位置(前置→后置) ③重组句子结构 ④适当省略代词，但具体请你查看上下句子的的代词出场率，判断是否替换，修改代词位置是可以的，但是修改代词位置的时候，需要保证代词的位置，不要出现主谓不一致的情况，比如"他走了一会，停下来休息了一会"，这个句子中，"他"是主语，"走了一会"是谓语，"停下来"是另一个谓语，但是"休息了一会"的主语就不是"他"了，所以这个句子是不成立的，你需要将"停下来休息了一会"修改为"他又停下来休息了一会"，才能保证主谓一致。\n注意：请你确保当句子出现“他、她”这两个人称代词的时候，判断前后数字的句子，是否过度的使用总和超过三个他，或者使用代词数量过度，\n如果是请你修改，因为这会从读者视角上，产生重复损失感，'
  }
];

/**
 * 句子标记分析器
 */
export class SentenceMarker {
  /**
   * 分析文本中的关键词标记
   * @param text 要分析的文本
   * @returns 匹配的标记数组
   */
  static analyzeText(text: string): Array<KeywordMarker & { matchedKeywords: string[] }> {
    const results: Array<KeywordMarker & { matchedKeywords: string[] }> = [];

    for (const marker of KEYWORD_MARKERS) {
      const matchedKeywords: string[] = [];

      for (const keyword of marker.keywords) {
        if (text.includes(keyword)) {
          matchedKeywords.push(keyword);
        }
      }

      if (matchedKeywords.length > 0) {
        results.push({
          ...marker,
          matchedKeywords
        });
      }
    }

    // 按优先级排序
    return results.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 生成句子的标记文本
   * @param text 句子文本
   * @param index 句子索引
   * @param sentences 可选的句子数组，用于上下文分析
   * @param currentIndex 可选的当前句子在数组中的索引
   * @returns 带标记的句子文本
   */
  static generateMarkedText(
    text: string,
    index: number,
    sentences?: Sentence[],
    currentIndex?: number
  ): string {
    const allMarkers = this.analyzeText(text);

    // 新增：上下文人称代词分析
    let contextAnalysis: PersonPronounAnalysis | null = null;
    if (sentences && currentIndex !== undefined) {
      contextAnalysis = this.analyzePersonPronounContext(sentences, currentIndex);
    }

    // 基础信息
    let markedText = `这是第 ${index + 1} 句话\n`;

    // 处理修辞手法分析结果
    if (contextAnalysis?.rhetoricalAnalysis?.hasParallelism) {
      const rhetorical = contextAnalysis.rhetoricalAnalysis;
      markedText += `\n🎭 修辞手法分析：\n`;
      markedText += `- 检测到排比结构（相似度：${(rhetorical.structureSimilarity * 100).toFixed(1)}%）\n`;
      markedText += `- 语义变化程度：${(rhetorical.semanticVariation * 100).toFixed(1)}%\n`;
      markedText += `- 模式类型：${rhetorical.patternType}\n`;

      if (rhetorical.recommendedAction === 'ignore') {
        markedText += `- 🟢 建议：保持当前结构，这是优秀的文学表达\n`;
      } else if (rhetorical.recommendedAction === 'caution') {
        markedText += `- 🟡 建议：结构良好，可适当增加语义变化\n`;
      } else {
        markedText += `- 🟠 建议：可以优化结构或增加变化\n`;
      }
      markedText += `\n`;
    }

    // 处理上下文人称代词分析结果（修复：并行显示，不再被修辞分析屏蔽）
    if (contextAnalysis?.isOverused) {
      // 如果同时存在修辞分析，调整显示方式
      if (contextAnalysis?.rhetoricalAnalysis?.hasParallelism) {
        markedText += `\n\ud83d\udcc8 代词使用分析（修辞语境下）：\n`;
        markedText += `- 密度：${(contextAnalysis.density * 100).toFixed(1)}%\n`;
        markedText += `- 连续性：${(contextAnalysis.continuity * 100).toFixed(1)}%\n`;
        markedText += `- 文体类型：${contextAnalysis.textType}\n`;
        markedText += `- 主要人称：${contextAnalysis.windowStats.dominantPronoun}\n`;
        markedText += `- \ud83d\udca1 在修辞手法中，适度的代词重复是可以接受的\n`;
        markedText += `\n`;
      } else {
        markedText += `\n\u26a0\ufe0f 上下文分析发现人称代词过度使用：\n`;
        markedText += `- 密度：${(contextAnalysis.density * 100).toFixed(1)}%\n`;
        markedText += `- 连续性：${(contextAnalysis.continuity * 100).toFixed(1)}%\n`;
        markedText += `- 文体类型：${contextAnalysis.textType}\n`;
        markedText += `- 主要人称：${contextAnalysis.windowStats.dominantPronoun}\n`;
        contextAnalysis.suggestions.forEach(suggestion => {
          markedText += `- ${suggestion}\n`;
        });
        markedText += `\n`;
      }
    }

    if (allMarkers.length > 0) {
      markedText += `以下是一些特征：\n`;

      allMarkers.forEach((marker, index) => {
        markedText += `${index + 1}. ${marker.name} ${marker.icon} - ${marker.description}\n`;

        // 特殊处理问题标记，添加创建提醒
        if (marker.name === '人称过度') {
          const keywords = marker.matchedKeywords.slice(0, 3).join('、'); // 最多显示3个关键词
          markedText += `- 发现人称代词过度使用\u3010${keywords}\u3011，建议：①具体角色名替换 ②前后置调整 ③句式重组 ④适当省略\n`;
          markedText += `  \u26a0\ufe0f 创建提醒：此句子已存在人称过度问题，如果你要创建新句子请避免使用\u3010${keywords}\u3011\n`;
        } else if (marker.name === '模糊表达') {
          const keywords = marker.matchedKeywords.slice(0, 3).join('、'); // 最多显示3个关键词
          markedText += `- 发现模糊表达\u3010${keywords}\u3011，需要具体化描述\n`;
          markedText += `  \u26a0\ufe0f 创建提醒：此句子已存在模糊表达问题，如果你要创建新句子请避免使用\u3010${keywords}\u3011\n`;
        } else if (marker.name === '套路表达') {
          const keywords = marker.matchedKeywords.slice(0, 3).join('、'); // 最多显示3个关键词
          markedText += `- 发现套路化表达\u3010${keywords}\u3011，尝试修改后半句的构成方式基于前后句\n`;
          markedText += `  \u26a0\ufe0f 创建提醒：此句子已存在套路表达问题，如果你要创建新句子请避免使用\u3010${keywords}\u3011\n`;
        }
      });
    } else {
      markedText += `以下是一些特征：\n`;
      markedText += `1. 普通句子 \ud83d\udcdd - 无特殊标记的常规句子\n`;
      markedText += `\n\u26a0\ufe0f 普通句子创建规范/与修改规范与禁忌：\n`;
      markedText += `\u3010严格禁止\u3011以下表达方式：\n`;
      markedText += `- 模糊表达禁用：禁止"一丝"、"几分"、"些许"、"有些"、"仿佛"、"似乎"、"好像"、"大概"、"可能"等模糊词汇\n`;
      markedText += `- 套路表达禁用：禁止"如刀锋般"、"宛如"、"犹如"、"心如刀绞"、"五味杂陈"、"无形大手"、"恍如隔世"等陈词滥调\n`;
      markedText += `- 人称过度禁用：禁止连续使用"他"、"她"、"我"等人称代词，避免"他他"、"她她"等重复\n`;
      markedText += `- 空洞表达禁用：禁止"内心深处"、"灵魂深处"、"心底最深处"等无实际内容的表达\n`;
      markedText += `\n\u3010如果你要创建，那么该句子的创建原则是\u3011普通句子应该：\n`;
      markedText += `①语言简洁明确 - 用具体词汇替代模糊表达\n`;
      markedText += `②表达具体生动 - 用实际动作、场景、对话推进情节\n`;
      markedText += `③避免过度情绪化 - 保持适度的情感表达，不要堆砌形容词\n`;
      markedText += `④保持自然流畅 - 符合人物性格和情境逻辑\n`;
      markedText += `\n\u3010推荐做法\u3011创建有价值的内容（优先级排序）：\n`;
      markedText += `- 优先使用对话来推进情节和展现人物关系\n`;
      markedText += `- 通过内心独白来展现人物的心理活动和情感冲突\n`;
      markedText += `- 用具体的动作描写来展现人物状态\n`;
      markedText += `- 通过环境细节来烘托氛围，而非直接的情感词汇\n`;
      markedText += `- 让每个句子都有实际的叙事功能，避免纯粹的修辞堆砌\n`;
      markedText += `\n\u3010台词创作指导\u3011：\n`;
      markedText += `- 对话要符合人物性格和身份，避免过于正式或书面化的表达\n`;
      markedText += `- 注重对话的节奏感和情感层次，通过语气词、停顿体现真实感\n`;
      markedText += `- 通过对话推进剧情发展，每句话都应有明确的目的和作用\n`;
      markedText += `- 善用潜台词，让对话具有多层含义和深度\n`;
      markedText += `\n\u3010内心对话规范\u3011：\n`;
      markedText += `- 内心独白要真实自然，符合人物当下的心理状态\n`;
      markedText += `- 展现人物的内在冲突和情感变化，避免过于直白的心理描述\n`;
      markedText += `- 通过内心活动增加故事深度，揭示人物的真实想法和动机\n`;
      markedText += `- 内心独白可以与外在行为形成对比，创造戏剧张力\n`;
    }

    markedText += `\n原文句子：${text}`;

    return markedText;
  }

  /**
   * 获取所有标记类型
   * @returns 标记类型数组
   */
  static getAllMarkers(): KeywordMarker[] {
    return [...KEYWORD_MARKERS];
  }

  /**
   * 根据类型获取标记
   * @param type 标记类型
   * @returns 对应类型的标记数组
   */
  static getMarkersByType(type: KeywordMarker['type']): KeywordMarker[] {
    return KEYWORD_MARKERS.filter(marker => marker.type === type);
  }

  /**
   * 分析句子的上下文人称代词使用情况（增强版）
   * @param sentences 句子数组
   * @param currentIndex 当前句子索引
   * @param windowSize 分析窗口大小（默认7句）
   * @returns 人称代词过度使用分析结果
   */
  static analyzePersonPronounContext(
    sentences: Sentence[],
    currentIndex: number,
    windowSize: number = 7
  ): PersonPronounAnalysis {
    // 实现滑动窗口分析
    const halfWindow = Math.floor(windowSize / 2);
    const windowStart = Math.max(0, currentIndex - halfWindow);
    const windowEnd = Math.min(sentences.length, currentIndex + halfWindow + 1);
    const windowSentences = sentences.slice(windowStart, windowEnd);

    // 统计人称代词分布
    const pronounStats = this.countPronounsInWindow(windowSentences);

    // 计算密度和连续性
    const density = this.calculatePronounDensity(windowSentences);
    const continuity = this.analyzePronounContinuity(windowSentences, currentIndex - windowStart);

    // 文体感知判断
    const textType = this.detectTextType(windowSentences);
    const threshold = this.getThresholdByTextType(textType);

    // 新增：修辞手法分析
    const rhetoricalAnalysis = this.analyzeRhetoricalPattern(windowSentences, currentIndex - windowStart);

    // 分析窗口统计信息
    const totalPronouns = pronounStats.他 + pronounStats.她 + pronounStats.我 + pronounStats.你;
    const dominantPronoun = this.findDominantPronoun(pronounStats);

    // 动态调整判断结果
    let isOverused = density > threshold.density || continuity > threshold.continuity;
    let suggestions = this.generateContextualSuggestions(pronounStats, textType);

    // 如果检测到修辞手法，调整判断和建议（调整阈值以适应实际文学创作）
    if (rhetoricalAnalysis.hasParallelism && rhetoricalAnalysis.semanticVariation > 0.5) {
      isOverused = false; // 修辞手法时不标记为过度使用
      suggestions = this.generateRhetoricalAwareSuggestions(pronounStats, textType, rhetoricalAnalysis);
    }

    return {
      isOverused,
      density,
      continuity,
      textType,
      suggestions,
      windowStats: {
        totalPronouns,
        dominantPronoun,
        distribution: pronounStats.distribution
      },
      rhetoricalAnalysis
    };
  }

  /**
   * 统计窗口内的人称代词
   * @param sentences 句子数组
   * @returns 人称代词统计结果
   */
  private static countPronounsInWindow(sentences: Sentence[]): PronounStats {
    const stats: PronounStats = {
      他: 0, 她: 0, 我: 0, 你: 0,
      totalWords: 0,
      distribution: []
    };

    sentences.forEach((sentence, index) => {
      const pronouns = this.extractPronouns(sentence.text);
      stats.distribution.push({sentence: index, pronouns});

      pronouns.forEach(pronoun => {
        if (pronoun === '他') stats.他++;
        else if (pronoun === '她') stats.她++;
        else if (pronoun === '我') stats.我++;
        else if (pronoun === '你') stats.你++;
      });

      stats.totalWords += this.countWords(sentence.text);
    });

    return stats;
  }

  /**
   * 提取句子中的人称代词
   * @param text 句子文本
   * @returns 人称代词数组
   */
  private static extractPronouns(text: string): string[] {
    const pronounRegex = /\b(他|她|我|你)\b/g;
    return text.match(pronounRegex) || [];
  }

  /**
   * 计算句子中的词数
   * @param text 句子文本
   * @returns 词数
   */
  private static countWords(text: string): number {
    // 中文分词简化版本
    return text.replace(/\s+/g, '').length;
  }

  /**
   * 计算人称代词密度
   * @param sentences 句子数组
   * @returns 密度值
   */
  private static calculatePronounDensity(sentences: Sentence[]): number {
    const stats = this.countPronounsInWindow(sentences);
    const totalPronouns = stats.他 + stats.她 + stats.我 + stats.你;
    return stats.totalWords > 0 ? totalPronouns / stats.totalWords : 0;
  }

  /**
   * 分析人称代词连续性
   * @param sentences 句子数组
   * @param currentIndex 当前句子在窗口中的索引
   * @returns 连续性分数
   */
  private static analyzePronounContinuity(sentences: Sentence[], currentIndex: number): number {
    // 分析当前句子前后的人称代词连续性
    const window = sentences.slice(Math.max(0, currentIndex - 2), currentIndex + 3);
    let continuityScore = 0;

    for (let i = 1; i < window.length; i++) {
      const prevPronouns = this.extractPronouns(window[i-1].text);
      const currPronouns = this.extractPronouns(window[i].text);

      // 检查是否有相同的人称代词在连续句子中出现
      const overlap = prevPronouns.filter(p => currPronouns.includes(p));
      continuityScore += overlap.length / Math.max(prevPronouns.length, currPronouns.length, 1);
    }

    return window.length > 1 ? continuityScore / (window.length - 1) : 0;
  }

  /**
   * 检测文本类型
   * @param sentences 句子数组
   * @returns 文本类型
   */
  private static detectTextType(sentences: Sentence[]): TextType {
    const combinedText = sentences.map(s => s.text).join('');

    // 对话检测
    if (/[\u201c\u300c\u300e].*[\u201d\u300d\u300f]/.test(combinedText)) {
      return 'dialogue';
    }

    // 心理描写检测
    if (/想|思考|觉得|认为|心里|内心/.test(combinedText)) {
      return 'thought';
    }

    // 动作描述检测
    if (/走|跑|看|说|做|来|去|站|坐|拿|放/.test(combinedText)) {
      return 'narrative';
    }

    // 默认为描述性文本
    return 'description';
  }

  /**
   * 根据文体类型获取阈值配置
   * @param textType 文体类型
   * @returns 阈值配置
   */
  private static getThresholdByTextType(textType: TextType): ThresholdConfig {
    return THRESHOLDS[textType] || THRESHOLDS.narrative;
  }

  /**
   * 找到最主要的人称代词
   * @param stats 人称代词统计
   * @returns 最主要的人称代词
   */
  private static findDominantPronoun(stats: PronounStats): string {
    const pronounCounts = [
      { pronoun: '他', count: stats.他 },
      { pronoun: '她', count: stats.她 },
      { pronoun: '我', count: stats.我 },
      { pronoun: '你', count: stats.你 }
    ];

    const dominant = pronounCounts.sort((a, b) => b.count - a.count)[0];
    return dominant.count > 0 ? dominant.pronoun : '无';
  }

  /**
   * 生成基于上下文的修改建议
   * @param stats 人称代词统计
   * @param textType 文体类型
   * @returns 修改建议数组
   */
  private static generateContextualSuggestions(stats: PronounStats, textType: TextType): string[] {
    const suggestions: string[] = [];

    // 分析最频繁的人称代词
    const pronounCounts = [
      { pronoun: '他', count: stats.他 },
      { pronoun: '她', count: stats.她 },
      { pronoun: '我', count: stats.我 },
      { pronoun: '你', count: stats.你 }
    ];

    const mostUsed = pronounCounts.sort((a, b) => b.count - a.count)[0];

    if (mostUsed && mostUsed.count > 3) {
      const pronoun = mostUsed.pronoun;

      switch (textType) {
        case 'dialogue':
          suggestions.push(`对话优化建议：①用"这个人"、"那家伙"等指代替换"${pronoun}" ②将"${pronoun}"后置到动作后 ③适当省略明确的代词`);
          suggestions.push(`例如："${pronoun}知道"→"知道的很清楚"或"心里明白"`);
          break;
        case 'narrative':
          suggestions.push(`叙述优化建议：①用具体角色名/职业替换"${pronoun}" ②调整句子结构避免开头就是"${pronoun}" ③用动作主体替代`);
          suggestions.push(`例如："${pronoun}走向"→"脚步移向"或"身影朝着"`);
          break;
        case 'description':
          suggestions.push(`描述优化建议：①用"这位"、"此人"等替换"${pronoun}" ②重组句子让主语多样化 ③用具体特征描述替代`);
          suggestions.push(`例如："${pronoun}的眼神"→"那双眼睛"或"目光中"`);
          break;
        case 'thought':
          suggestions.push(`心理描写优化：①适当的"${pronoun}"是正常的 ②可变换为"内心"、"心中" ③调整句式避免连续出现`);
          suggestions.push(`例如："${pronoun}想"→"内心思索"或"脑海中浮现"`);
          break;
      }
    }

    // 添加通用建议
    if (stats.totalWords > 0) {
      const density = (stats.他 + stats.她 + stats.我 + stats.你) / stats.totalWords;
      if (density > 0.2) {
        suggestions.push('通用优化策略：①前置改后置("他知道"→"知道的是他") ②省略明确代词("他走向门口"→"走向门口") ③具体化主语("他的手"→"那只手")');
        suggestions.push('结构重组示例：连续的"他"可以改为"这个人"、"男子"、"那家伙"等，或者调整句子让动作、物体作主语');
      }
    }

    return suggestions;
  }

  /**
   * 获取句子的主要标记
   * @param text 句子文本
   * @returns 主要标记对象，如果没有匹配则返回null
   */
  static getPrimaryMarker(text: string): (KeywordMarker & { matchedKeywords: string[] }) | null {
    const allMarkers = this.analyzeText(text);

    // 返回优先级最高的标记（已经按优先级排序）
    return allMarkers.length > 0 ? allMarkers[0] : null;
  }

  /**
   * 分析修辞手法模式
   * @param sentences 句子数组
   * @param currentIndex 当前句子在窗口中的索引
   * @returns 修辞手法分析结果
   */
  private static analyzeRhetoricalPattern(
    sentences: Sentence[],
    currentIndex: number
  ): RhetoricalAnalysis {
    // 提取句子结构模式
    const patterns = sentences.map(s => this.extractStructurePattern(s.text));

    // 计算结构相似度
    const similarity = this.calculateStructureSimilarity(patterns);

    // 分析语义变化
    const semanticVariation = this.analyzeSemanticVariation(sentences);

    // 判断是否为排比修辞（调整阈值以适应实际文学创作）
    const hasParallelism = similarity > 0.5 && sentences.length >= 3;

    // 确定模式类型
    let patternType: 'repetitive' | 'varied' | 'mixed' = 'mixed';
    if (similarity > 0.9) {
      patternType = 'repetitive';
    } else if (semanticVariation > 0.7) {
      patternType = 'varied';
    }

    // 确定推荐操作（调整阈值以适应实际文学创作）
    let recommendedAction: 'ignore' | 'caution' | 'suggest' = 'suggest';
    if (hasParallelism && semanticVariation > 0.8) {
      recommendedAction = 'ignore'; // 优秀的修辞，建议保持
    } else if (hasParallelism && semanticVariation > 0.5) {
      recommendedAction = 'caution'; // 可以优化
    }

    return {
      hasParallelism,
      structureSimilarity: similarity,
      patternType,
      semanticVariation,
      recommendedAction,
      sentenceIndices: sentences.map((_, i) => i),
      detectedPattern: patterns.length > 0 ? patterns[0] : ''
    };
  }

  /**
   * 提取句子的结构模式
   * @param text 句子文本
   * @returns 结构模式字符串
   */
  private static extractStructurePattern(text: string): string {
    // 提取句子的结构模式
    // 例如："我听到了...的回响" -> "我+动词+了+...+的+名词"
    return text
      .replace(/[，。！？；：]/g, '') // 移除标点
      .replace(/\d+/g, 'NUM') // 数字标准化
      .replace(/[a-zA-Z]+/g, 'ENG') // 英文标准化
      .replace(/["'""'']/g, 'QUOTE') // 引号标准化
      .substring(0, 20); // 取前20个字符作为模式
  }

  /**
   * 计算结构相似度
   * @param patterns 模式数组
   * @returns 相似度值 (0-1)
   */
  private static calculateStructureSimilarity(patterns: string[]): number {
    if (patterns.length < 2) return 0;

    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < patterns.length - 1; i++) {
      for (let j = i + 1; j < patterns.length; j++) {
        totalSimilarity += this.stringSimilarity(patterns[i], patterns[j]);
        comparisons++;
      }
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度值 (0-1)
   */
  private static stringSimilarity(str1: string, str2: string): number {
    // 使用编辑距离计算相似度
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = this.levenshteinDistance(str1, str2);
    return 1 - distance / maxLength;
  }

  /**
   * 计算编辑距离
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 分析语义变化程度
   * @param sentences 句子数组
   * @returns 语义变化程度 (0-1)
   */
  private static analyzeSemanticVariation(sentences: Sentence[]): number {
    if (sentences.length < 2) return 0;

    // 提取每句的关键词
    const keywordSets = sentences.map(s => this.extractKeywords(s.text));

    // 计算语义变化程度
    let totalVariation = 0;
    let comparisons = 0;

    for (let i = 0; i < keywordSets.length - 1; i++) {
      for (let j = i + 1; j < keywordSets.length; j++) {
        const variation = this.calculateKeywordVariation(keywordSets[i], keywordSets[j]);
        totalVariation += variation;
        comparisons++;
      }
    }

    return comparisons > 0 ? totalVariation / comparisons : 0;
  }

  /**
   * 提取句子中的关键词
   * @param text 句子文本
   * @returns 关键词数组
   */
  private static extractKeywords(text: string): string[] {
    // 简化的关键词提取，移除常见的停用词
    const stopWords = ['的', '了', '是', '在', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'];

    return text
      .replace(/[，。！？；：""'']/g, ' ') // 替换标点为空格
      .split(/\s+/) // 按空格分割
      .filter(word => word.length > 1 && !stopWords.includes(word)) // 过滤停用词和单字符
      .slice(0, 10); // 取前10个关键词
  }

  /**
   * 计算关键词变化程度
   * @param keywords1 关键词集合1
   * @param keywords2 关键词集合2
   * @returns 变化程度 (0-1)
   */
  private static calculateKeywordVariation(keywords1: string[], keywords2: string[]): number {
    if (keywords1.length === 0 && keywords2.length === 0) return 0;

    const set1 = new Set(keywords1);
    const set2 = new Set(keywords2);

    // 计算交集和并集
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    // 变化程度 = 1 - (交集大小 / 并集大小)
    return union.size > 0 ? 1 - (intersection.size / union.size) : 0;
  }

  /**
   * 生成修辞感知的建议
   * @param stats 人称代词统计
   * @param textType 文体类型
   * @param rhetoricalAnalysis 修辞分析结果
   * @returns 建议数组
   */
  private static generateRhetoricalAwareSuggestions(
    stats: PronounStats,
    textType: TextType,
    rhetoricalAnalysis: RhetoricalAnalysis
  ): string[] {
    const suggestions: string[] = [];

    if (rhetoricalAnalysis.hasParallelism) {
      suggestions.push('🎭 检测到排比修辞手法：');
      suggestions.push(`  - 结构相似度：${(rhetoricalAnalysis.structureSimilarity * 100).toFixed(1)}%`);
      suggestions.push(`  - 语义变化度：${(rhetoricalAnalysis.semanticVariation * 100).toFixed(1)}%`);

      if (rhetoricalAnalysis.recommendedAction === 'ignore') {
        suggestions.push('✅ 建议保持：这是有意的文学表达，具有良好的韵律感和语义变化');
        suggestions.push('🎨 可选优化：在保持结构的基础上，可以微调个别词汇增加变化');
      } else if (rhetoricalAnalysis.recommendedAction === 'caution') {
        suggestions.push('⚠️ 建议调整：虽然是排比结构，但语义变化较少，可以增加内容的多样性');
        suggestions.push('💡 优化方向：保持"' + rhetoricalAnalysis.detectedPattern + '"的结构，但丰富具体内容');
      } else {
        suggestions.push('🔄 建议优化：可以在保持部分重复结构的基础上，增加更多变化');
      }

      // 添加文体特定的建议
      switch (textType) {
        case 'dialogue':
          suggestions.push('💬 对话优化：排比在对话中可以增强语气，但注意符合角色性格');
          break;
        case 'narrative':
          suggestions.push('📖 叙述优化：排比可以增强叙述节奏，建议适度使用');
          break;
        case 'description':
          suggestions.push('🖼️ 描述优化：排比有助于营造氛围，可以保持这种表达方式');
          break;
        case 'thought':
          suggestions.push('💭 心理描写：排比能体现思维的重复性，符合心理活动特点');
          break;
      }
    } else {
      // 使用原有的建议生成逻辑
      return this.generateContextualSuggestions(stats, textType);
    }

    return suggestions;
  }
}