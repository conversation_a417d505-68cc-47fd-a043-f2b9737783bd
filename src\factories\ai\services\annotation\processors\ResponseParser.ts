/**
 * AI注释响应解析器
 * 负责解析AI返回的各种格式响应并转换为标准数据结构
 */

import { TextSegment, SuggestionItem, ModificationType } from '../types/AnnotationTypes';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

/**
 * Merge操作验证器
 * 确保merge类型的建议符合业务逻辑要求
 */
class MergeValidator {
  /**
   * 验证并修正merge操作
   * @param suggestions 原始建议数组
   * @returns 修正后的建议数组
   */
  static validateAndFixMergeOperations(suggestions: any[]): any[] {
    const fixedSuggestions = [...suggestions];

    for (let i = 0; i < fixedSuggestions.length - 1; i++) {
      const current = fixedSuggestions[i];
      const next = fixedSuggestions[i + 1];

      if (current.modificationType === 'merge') {
        console.log(`🔧 修正merge操作: 句子${i} -> 句子${i+1}`);

        // 强制将下一个句子标记为删除
        next.modificationType = 'delete';
        next.modifiedText = '';
        next.suggestion = '已与前句合并';
        next.reason = '此句已与前一句合并，无需单独处理';

        // 确保当前句子包含合并后的完整内容
        if (!current.modifiedText || current.modifiedText.trim() === '') {
          current.modifiedText = `${current.originalText} ${next.originalText}`;
          console.log(`🔧 自动生成合并内容: ${current.modifiedText}`);
        }

        // 添加合并标记，用于UI特殊处理
        current.isMergeOperation = true;
        next.isMergeTarget = true;
        next.mergeSourceIndex = i;
      }
    }

    return fixedSuggestions;
  }

  /**
   * 验证merge操作的完整性
   * @param suggestions 建议数组
   * @returns 验证结果
   */
  static validateMergeIntegrity(suggestions: any[]): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    for (let i = 0; i < suggestions.length - 1; i++) {
      const current = suggestions[i];
      const next = suggestions[i + 1];

      if (current.modificationType === 'merge') {
        if (next.modificationType !== 'delete') {
          issues.push(`句子${i}标记为merge，但句子${i+1}不是delete类型`);
        }

        if (next.modifiedText && next.modifiedText.trim() !== '') {
          issues.push(`句子${i+1}作为merge目标，但modifiedText不为空`);
        }

        if (!current.modifiedText || current.modifiedText.trim() === '') {
          issues.push(`句子${i}标记为merge，但缺少合并后的内容`);
        }
      }
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

export class ResponseParser {
  /**
   * 解析段落注释响应
   * @param segment 原始段落
   * @param response AI响应文本
   * @returns 更新后的段落
   */
  static parseSegmentResponse(
    segment: TextSegment,
    response: string
  ): TextSegment {
    try {
      console.log('🔍 开始解析段落响应');

      // 使用AIResponseParser解析JSON
      const defaultValue = { suggestions: [] };
      const parsedResponse = AIResponseParser.parseJSON(response, defaultValue);

      if (!parsedResponse || !parsedResponse.suggestions || parsedResponse.suggestions.length === 0) {
        console.log('⚠️ 段落响应解析失败，返回原始段落');
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => ({
            ...sentence,
            processingStatus: 'failed' as const,
            lastProcessedAt: new Date()
          }))
        };
      }

      let suggestions: any[] = parsedResponse.suggestions;
      console.log('✅ 段落响应解析成功，找到建议:', suggestions.length);

      // 新增：merge操作验证和修正
      const validationResult = MergeValidator.validateMergeIntegrity(suggestions);
      if (!validationResult.isValid) {
        console.warn('⚠️ 检测到merge操作问题:', validationResult.issues);
        suggestions = MergeValidator.validateAndFixMergeOperations(suggestions);
        console.log('✅ merge操作已自动修正');
      }

      // 创建更新后的句子数组
      const updatedSentences = segment.sentences.map((sentence, index) => {
        const suggestion = suggestions.find((s: any) => s.sentenceIndex === index);

        if (suggestion) {
          const normalizedSuggestion = this.normalizeSuggestion(suggestion);

          // 🔧 添加hasCreation字段验证
          if (suggestion.hasCreation && !normalizedSuggestion.hasCreation) {
            console.error('❌ hasCreation字段在标准化过程中丢失');
          }

          return {
            ...sentence,
            aiSuggestion: normalizedSuggestion.suggestion,
            modifiedText: normalizedSuggestion.modifiedText,
            modificationType: normalizedSuggestion.modificationType,
            category: normalizedSuggestion.category,
            severity: normalizedSuggestion.severity,
            impact: normalizedSuggestion.impact,
            confidence: normalizedSuggestion.confidence,
            alternatives: normalizedSuggestion.alternatives || [],
            tags: normalizedSuggestion.tags || [],
            reason: normalizedSuggestion.reason,
            // 新增连贯性相关字段
            transitionSuggestion: normalizedSuggestion.transitionSuggestion,
            coherenceScore: normalizedSuggestion.coherenceScore,
            flowIssues: normalizedSuggestion.flowIssues,
            contextRelevance: normalizedSuggestion.contextRelevance,
            // 🔧 确保hasCreation字段被正确传递
            hasCreation: normalizedSuggestion.hasCreation,
            createdSentences: normalizedSuggestion.createdSentences,
            insertPosition: normalizedSuggestion.insertPosition,
            contentType: normalizedSuggestion.contentType,
            insertMode: normalizedSuggestion.insertMode,
            contextHint: normalizedSuggestion.contextHint,
            processingStatus: 'completed' as const,
            lastProcessedAt: new Date()
          };
        }

        return {
          ...sentence,
          processingStatus: 'failed' as const,
          lastProcessedAt: new Date()
        };
      });

      return {
        ...segment,
        sentences: updatedSentences
      };
    } catch (error) {
      console.error('❌ 段落响应解析异常:', error);
      return {
        ...segment,
        sentences: segment.sentences.map(sentence => ({
          ...sentence,
          processingStatus: 'failed' as const,
          lastProcessedAt: new Date()
        }))
      };
    }
  }

  /**
   * 解析全文响应
   * @param segments 原始段落数组
   * @param response AI响应文本
   * @returns 更新后的段落数组
   */
  static parseFullTextResponse(
    segments: TextSegment[],
    response: string
  ): TextSegment[] {
    try {
      console.log('🔍 开始解析全文响应');

      // 使用AIResponseParser解析JSON
      const defaultValue = { suggestions: [] };
      const parsedResponse = AIResponseParser.parseJSON(response, defaultValue);

      if (!parsedResponse || !parsedResponse.suggestions || parsedResponse.suggestions.length === 0) {
        console.log('⚠️ 全文响应解析失败，返回原始段落');
        return segments.map(segment => ({
          ...segment,
          sentences: segment.sentences.map(sentence => ({
            ...sentence,
            processingStatus: 'failed' as const,
            lastProcessedAt: new Date()
          }))
        }));
      }

      let suggestions: any[] = parsedResponse.suggestions;
      console.log('✅ 全文响应解析成功，找到建议:', suggestions.length);

      // 新增：merge操作验证和修正
      const validationResult = MergeValidator.validateMergeIntegrity(suggestions);
      if (!validationResult.isValid) {
        console.warn('⚠️ 检测到merge操作问题:', validationResult.issues);
        suggestions = MergeValidator.validateAndFixMergeOperations(suggestions);
        console.log('✅ merge操作已自动修正');
      }

      // 创建更新后的段落数组
      const updatedSegments = segments.map(segment => ({
        ...segment,
        sentences: [...segment.sentences]
      }));

      // 应用建议到对应的句子
      suggestions.forEach((suggestion: any) => {
        const globalIndex = suggestion.sentenceIndex;
        const { segmentIndex, sentenceIndex } = this.findSentenceLocation(updatedSegments, globalIndex);

        if (segmentIndex >= 0 && sentenceIndex >= 0) {
          const normalizedSuggestion = this.normalizeSuggestion(suggestion);

          // 🔧 添加hasCreation字段验证
          if (suggestion.hasCreation && !normalizedSuggestion.hasCreation) {
            console.error('❌ hasCreation字段在标准化过程中丢失');
          }

          updatedSegments[segmentIndex].sentences[sentenceIndex] = {
            ...updatedSegments[segmentIndex].sentences[sentenceIndex],
            aiSuggestion: normalizedSuggestion.suggestion,
            modifiedText: normalizedSuggestion.modifiedText,
            modificationType: normalizedSuggestion.modificationType,
            category: normalizedSuggestion.category,
            severity: normalizedSuggestion.severity,
            impact: normalizedSuggestion.impact,
            confidence: normalizedSuggestion.confidence,
            alternatives: normalizedSuggestion.alternatives || [],
            tags: normalizedSuggestion.tags || [],
            reason: normalizedSuggestion.reason,
            // 🔧 确保hasCreation字段被正确传递
            hasCreation: normalizedSuggestion.hasCreation,
            createdSentences: normalizedSuggestion.createdSentences,
            insertPosition: normalizedSuggestion.insertPosition,
            contentType: normalizedSuggestion.contentType,
            insertMode: normalizedSuggestion.insertMode,
            contextHint: normalizedSuggestion.contextHint,
            processingStatus: 'completed' as const,
            lastProcessedAt: new Date()
          };
          console.log(`✅ 应用建议到句子 ${globalIndex + 1}:`, normalizedSuggestion.modificationType);

          // 🔧 添加hasCreation字段日志
          if (normalizedSuggestion.hasCreation) {
            console.log(`✅ 检测到hasCreation字段，创建句子数量:`, normalizedSuggestion.createdSentences?.length || 0);
          }
        }
      });

      // 标记未处理的句子为失败状态
      updatedSegments.forEach(segment => {
        segment.sentences.forEach(sentence => {
          if (!sentence.aiSuggestion) {
            (sentence as any).processingStatus = 'failed';
            (sentence as any).lastProcessedAt = new Date();
          }
        });
      });

      return updatedSegments;
    } catch (error) {
      console.error('❌ 全文响应解析异常:', error);
      return segments.map(segment => ({
        ...segment,
        sentences: segment.sentences.map(sentence => ({
          ...sentence,
          processingStatus: 'failed' as const,
          lastProcessedAt: new Date()
        }))
      }));
    }
  }

  /**
   * 标准化建议数据
   * @param suggestion 原始建议数据
   * @returns 标准化后的建议数据
   */
  static normalizeSuggestion(suggestion: any): any {

    return {
      suggestion: suggestion.suggestion || suggestion.modifiedText || '',
      modifiedText: suggestion.modifiedText || suggestion.suggestion || '',
      modificationType: suggestion.modificationType || 'keep',
      category: suggestion.category || 'expression',
      severity: suggestion.severity || 'medium',
      impact: suggestion.impact || 'moderate',
      confidence: suggestion.confidence || 0.8,
      alternatives: suggestion.alternatives || [],
      tags: suggestion.tags || [],
      reason: suggestion.reason || '',
      // 新增merge相关字段
      isMergeOperation: suggestion.isMergeOperation || false,
      isMergeTarget: suggestion.isMergeTarget || false,
      mergeSourceIndex: suggestion.mergeSourceIndex || null,
      // 🔧 修复：添加hasCreation相关字段
      hasCreation: suggestion.hasCreation || false,
      createdSentences: suggestion.createdSentences || [],
      insertPosition: suggestion.insertPosition || 'after',
      contentType: suggestion.contentType || 'dialogue',
      insertMode: suggestion.insertMode || 'batch',
      contextHint: suggestion.contextHint || ''
    };
  }

  /**
   * 根据全局句子索引找到对应的段落和句子位置
   * @param segments 段落数组
   * @param globalIndex 全局句子索引
   * @returns 段落索引和句子索引
   */
  static findSentenceLocation(segments: TextSegment[], globalIndex: number): { segmentIndex: number, sentenceIndex: number } {
    let currentIndex = 0;

    for (let segmentIndex = 0; segmentIndex < segments.length; segmentIndex++) {
      const segment = segments[segmentIndex];
      const segmentSentenceCount = segment.sentences.length;

      if (globalIndex >= currentIndex && globalIndex < currentIndex + segmentSentenceCount) {
        return {
          segmentIndex,
          sentenceIndex: globalIndex - currentIndex
        };
      }

      currentIndex += segmentSentenceCount;
    }

    return { segmentIndex: -1, sentenceIndex: -1 };
  }

  /**
   * 提取单个建议
   * @param text 响应文本
   * @returns 建议数组
   */
  static extractSingleSuggestions(text: string): any[] {
    const suggestions: any[] = [];

    try {
      // 添加超时保护，防止正则表达式回溯爆炸
      const startTime = Date.now();
      const TIMEOUT_MS = 2000; // 2秒超时

      // 使用更安全的正则表达式模式
      const suggestionPattern = /"sentenceIndex"\s*:\s*\d+[^}]*}/g;

      let match;
      while ((match = suggestionPattern.exec(text)) !== null) {
        // 检查超时
        if (Date.now() - startTime > TIMEOUT_MS) {
          console.warn('⚠️ 正则匹配超时，停止处理');
          break;
        }

        try {
          // 找到完整的JSON对象
          const matchStart = text.lastIndexOf('{', match.index);
          if (matchStart !== -1) {
            const jsonStr = text.substring(matchStart, match.index + match[0].length);
            const suggestion = JSON.parse(jsonStr);
            if (suggestion.sentenceIndex !== undefined) {
              suggestions.push(suggestion);
            }
          }
        } catch (e) {
          // 忽略解析失败的单个建议
        }
      }
    } catch (error) {
      console.error('❌ 提取单个建议失败:', error);
    }

    return suggestions;
  }

  /**
   * 提取字段数据 - 增强版字段匹配解析器
   * @param text 响应文本
   * @returns 字段数据数组
   */
  static extractAllFieldsFromText(text: string): any[] {
    console.log('🔧 开始增强字段匹配解析，文本长度:', text.length);

    try {
      // 🔧 直接使用全文字段匹配，跳过有问题的边界识别
      console.log('🔧 使用全文字段匹配解析，跳过边界识别');
      return this.extractGlobalFieldsFromText(text);

    } catch (error) {
      console.error('❌ 增强字段匹配解析失败:', error);
      return [];
    }
  }

  /**
   * 智能识别对象边界
   * @param text 响应文本
   * @returns 对象边界数组
   */
  private static findFieldObjectBoundaries(text: string): Array<{start: number, end: number, sentenceIndex: number}> {
    const boundaries: Array<{start: number, end: number, sentenceIndex: number}> = [];

    try {
      // 添加超时保护
      const startTime = Date.now();
      const TIMEOUT_MS = 1500; // 1.5秒超时

      // 🔧 修复：使用全局匹配找到所有sentenceIndex位置
      const sentenceIndexPattern = /"sentenceIndex"\s*:\s*(\d+)/g;
      const matches: Array<{index: number, sentenceIndex: number}> = [];
      let match;
      let iterationCount = 0;
      const MAX_ITERATIONS = 100; // 最大迭代次数

      // 首先收集所有匹配位置
      while ((match = sentenceIndexPattern.exec(text)) !== null) {
        // 检查超时和迭代次数
        if (Date.now() - startTime > TIMEOUT_MS || iterationCount++ > MAX_ITERATIONS) {
          console.warn('⚠️ 对象边界识别超时或超过最大迭代次数，停止处理');
          break;
        }

        matches.push({
          index: match.index,
          sentenceIndex: parseInt(match[1])
        });
      }

      // 🔧 修复：基于收集的匹配位置创建边界
      for (let i = 0; i < matches.length; i++) {
        const currentMatch = matches[i];
        const nextMatch = matches[i + 1];

        const startPos = Math.max(0, currentMatch.index - 50); // 向前扩展50个字符
        const endPos = nextMatch
          ? Math.max(nextMatch.index - 50, currentMatch.index + 100)
          : text.length;

        boundaries.push({
          start: startPos,
          end: endPos,
          sentenceIndex: currentMatch.sentenceIndex
        });
      }

      console.log('🔍 识别到对象边界:', boundaries.length, '个');
      console.log('🔍 边界详情:', boundaries.map(b => `句子${b.sentenceIndex}: ${b.start}-${b.end}`));
      return boundaries;

    } catch (error) {
      console.error('❌ 对象边界识别失败:', error);
      return [];
    }
  }

  /**
   * 从单个对象文本中提取字段
   * @param objectText 对象文本
   * @param sentenceIndex 句子索引
   * @returns 提取的对象
   */
  private static extractSingleObjectFieldsFromText(objectText: string, sentenceIndex: number): any | null {
    try {
      const result: any = {
        sentenceIndex,
        originalText: '',
        modificationType: 'keep',
        modifiedText: '',
        suggestion: '',
        reason: '',
        category: 'style',
        severity: 'medium',
        impact: 'moderate',
        confidence: 0.8,
        alternatives: [],
        tags: []
      };

      // 增强的字段匹配模式 - 支持多种格式
      const fieldPatterns = {
        originalText: /["']?originalText["']?\s*:\s*["']([^"']*?)["']/i,
        modificationType: /["']?modificationType["']?\s*:\s*["']([^"']*?)["']/i,
        modifiedText: /["']?modifiedText["']?\s*:\s*["']([^"']*?)["']/i,
        suggestion: /["']?suggestion["']?\s*:\s*["']([^"']*?)["']/i,
        reason: /["']?reason["']?\s*:\s*["']([^"']*?)["']/i,
        category: /["']?category["']?\s*:\s*["']([^"']*?)["']/i,
        severity: /["']?severity["']?\s*:\s*["']([^"']*?)["']/i,
        impact: /["']?impact["']?\s*:\s*["']([^"']*?)["']/i,
        confidence: /["']?confidence["']?\s*:\s*([\d.]+)/i,
        // create类型专用字段
        insertPosition: /["']?insertPosition["']?\s*:\s*["']([^"']*?)["']/i,
        contentType: /["']?contentType["']?\s*:\s*["']([^"']*?)["']/i,
        insertMode: /["']?insertMode["']?\s*:\s*["']([^"']*?)["']/i,
        contextHint: /["']?contextHint["']?\s*:\s*["']([^"']*?)["']/i
      };

      // 提取各个字段
      Object.entries(fieldPatterns).forEach(([fieldName, pattern]) => {
        const match = objectText.match(pattern);
        if (match) {
          if (fieldName === 'confidence') {
            result[fieldName] = parseFloat(match[1]) || 0.8;
          } else {
            result[fieldName] = match[1] || (fieldName === 'modificationType' ? 'keep' : '');
          }
        }
      });

      // 处理独立的句子创建功能
      const hasCreationMatch = objectText.match(/["']?hasCreation["']?\s*:\s*(true|false)/i);
      if (hasCreationMatch && hasCreationMatch[1] === 'true') {
        result.hasCreation = true;

        // 解析创建相关字段 - 改进的数组解析逻辑
        const createdSentencesMatch = objectText.match(/["']?createdSentences["']?\s*:\s*\[([^\]]*)\]/i);
        if (createdSentencesMatch) {
          try {
            // 解析数组内容 - 处理包含引号的句子
            const arrayContent = createdSentencesMatch[1];
            console.log('🔍 解析createdSentences数组内容:', arrayContent);

            // 使用更智能的分割方式，处理包含引号的句子
            const sentences = this.parseArrayContent(arrayContent);
            result.createdSentences = sentences;

            console.log('✅ 成功解析createdSentences:', sentences.length, '个句子');
          } catch (error) {
            console.warn('⚠️ createdSentences数组解析失败:', error);
            result.createdSentences = [];
          }
        } else {
          result.createdSentences = [];
        }

        // 解析其他创建相关字段
        const insertPositionMatch = objectText.match(/["']?insertPosition["']?\s*:\s*["']([^"']+)["']/i);
        if (insertPositionMatch) {
          result.insertPosition = insertPositionMatch[1];
        }

        const contentTypeMatch = objectText.match(/["']?contentType["']?\s*:\s*["']([^"']+)["']/i);
        if (contentTypeMatch) {
          result.contentType = contentTypeMatch[1];
        }

        const insertModeMatch = objectText.match(/["']?insertMode["']?\s*:\s*["']([^"']+)["']/i);
        if (insertModeMatch) {
          result.insertMode = insertModeMatch[1];
        }

        // contextHint可能包含复杂内容，使用更宽松的匹配
        const contextHintMatch = objectText.match(/["']?contextHint["']?\s*:\s*["']([^"']*(?:\\.[^"']*)*)["']/i);
        if (contextHintMatch) {
          result.contextHint = contextHintMatch[1].replace(/\\"/g, '"').replace(/\\'/g, "'");
        }

        console.log('✅ 解析创建相关字段:', {
          hasCreation: result.hasCreation,
          createdSentences: result.createdSentences?.length || 0,
          insertPosition: result.insertPosition,
          contentType: result.contentType,
          insertMode: result.insertMode,
          contextHint: result.contextHint ? result.contextHint.substring(0, 50) + '...' : ''
        });
      }

      // 验证提取的对象是否有效
      if (this.isValidExtractedFieldObject(result)) {
        return this.normalizeExtractedFieldSuggestion(result);
      }

      return null;
    } catch (error) {
      console.error('❌ 单个对象字段提取失败:', error);
      return null;
    }
  }

  /**
   * 全文字段匹配解析（改进版）
   * @param text 响应文本
   * @returns 字段数据数组
   */
  private static extractGlobalFieldsFromText(text: string): any[] {
    const results: any[] = [];

    try {
      console.log('🔧 开始全文字段匹配解析');

      // 🔧 尝试直接JSON解析完整对象
      const jsonObjectPattern = /\{[^{}]*?"sentenceIndex"\s*:\s*\d+[^{}]*?\}/g;
      const jsonMatches = Array.from(text.matchAll(jsonObjectPattern));

      console.log('🔍 找到JSON对象:', jsonMatches.length, '个');

      if (jsonMatches.length > 0) {
        jsonMatches.forEach((match, index) => {
          try {
            const jsonText = match[0];
            console.log(`🔍 尝试解析JSON对象 ${index + 1}:`, jsonText.substring(0, 100) + '...');

            const parsed = JSON.parse(jsonText);

            if (parsed.sentenceIndex !== undefined) {
              // 标准化解析的对象
              const normalized = this.normalizeExtractedFieldSuggestion(parsed);
              results.push(normalized);
              console.log(`✅ 成功解析JSON对象 ${index + 1}:`, normalized.sentenceIndex, normalized.modificationType, normalized.hasCreation ? '(有创建)' : '');
            }
          } catch (parseError) {
            console.warn(`⚠️ JSON解析失败 ${index + 1}:`, parseError);
          }
        });
      }

      // 🔧 如果JSON解析失败，降级为字段匹配
      if (results.length === 0) {
        console.log('🔧 JSON解析失败，降级为字段匹配');

        const indexMatches = this.safeRegexMatch(text, /"sentenceIndex"\s*:\s*(\d+)/g);
        const originalTextMatches = this.safeRegexMatch(text, /"originalText"\s*:\s*"([^"]+)"/g);
        const modificationTypeMatches = this.safeRegexMatch(text, /"modificationType"\s*:\s*"([^"]+)"/g);
        const modifiedTextMatches = this.safeRegexMatch(text, /"modifiedText"\s*:\s*"([^"]+)"/g);
        const suggestionMatches = this.safeRegexMatch(text, /"suggestion"\s*:\s*"([^"]+)"/g);
        const reasonMatches = this.safeRegexMatch(text, /"reason"\s*:\s*"([^"]+)"/g);

        // 创建相关字段匹配
        const hasCreationMatches = this.safeRegexMatch(text, /"hasCreation"\s*:\s*(true|false)/g);
        const insertPositionMatches = this.safeRegexMatch(text, /"insertPosition"\s*:\s*"([^"]+)"/g);
        const contentTypeMatches = this.safeRegexMatch(text, /"contentType"\s*:\s*"([^"]+)"/g);
        const insertModeMatches = this.safeRegexMatch(text, /"insertMode"\s*:\s*"([^"]+)"/g);
        const contextHintMatches = this.safeRegexMatch(text, /"contextHint"\s*:\s*"([^"]+)"/g);

        if (indexMatches) {
          indexMatches.forEach((match, i) => {
            const sentenceIndex = parseInt(match.match(/\d+/)?.[0] || '0');

            const result: any = {
              sentenceIndex,
              originalText: this.extractFieldValue(originalTextMatches?.[i]),
              modificationType: this.extractFieldValue(modificationTypeMatches?.[i]) || 'keep',
              modifiedText: this.extractFieldValue(modifiedTextMatches?.[i]),
              suggestion: this.extractFieldValue(suggestionMatches?.[i]),
              reason: this.extractFieldValue(reasonMatches?.[i]),
              category: 'style',
              severity: 'medium',
              impact: 'moderate',
              confidence: 0.8,
              alternatives: [],
              tags: []
            };

            // 添加创建相关字段
            const hasCreationValue = hasCreationMatches?.[i];
            if (hasCreationValue && hasCreationValue.includes('true')) {
              result.hasCreation = true;
              result.insertPosition = this.extractFieldValue(insertPositionMatches?.[i]) || 'after';
              result.contentType = this.extractFieldValue(contentTypeMatches?.[i]) || 'dialogue';
              result.insertMode = this.extractFieldValue(insertModeMatches?.[i]) || 'batch';
              result.contextHint = this.extractFieldValue(contextHintMatches?.[i]) || '';

              // 🔧 尝试解析createdSentences数组
              const createdSentencesMatch = text.match(/"createdSentences"\s*:\s*\[([^\]]*)\]/i);
              if (createdSentencesMatch) {
                try {
                  const arrayContent = createdSentencesMatch[1];
                  result.createdSentences = this.parseArrayContent(arrayContent);
                  console.log('✅ 成功解析createdSentences:', result.createdSentences.length, '个句子');
                } catch (error) {
                  console.warn('⚠️ createdSentences解析失败:', error);
                  result.createdSentences = [];
                }
              } else {
                result.createdSentences = [];
              }
            }

            results.push(result);
          });
        }
      }

      console.log('📊 全文字段匹配结果:', results.length, '个建议');

      // 🔧 输出hasCreation字段的统计
      const hasCreationCount = results.filter(r => r.hasCreation === true).length;
      if (hasCreationCount > 0) {
        console.log('✅ 检测到hasCreation字段:', hasCreationCount, '个');
      }

    } catch (error) {
      console.error('❌ 全文字段匹配失败:', error);
    }

    return results;
  }

  /**
   * 提取字段值
   * @param match 匹配结果
   * @returns 字段值
   */
  private static extractFieldValue(match?: string): string {
    if (!match) return '';
    const valueMatch = match.match(/"([^"]+)"/);
    return valueMatch ? valueMatch[1] : '';
  }

  /**
   * 安全的正则表达式匹配，带超时保护
   * @param text 要匹配的文本
   * @param pattern 正则表达式
   * @param timeout 超时时间（毫秒）
   * @returns 匹配结果或null
   */
  private static safeRegexMatch(text: string, pattern: RegExp, timeout: number = 1000): RegExpMatchArray | null {
    const start = Date.now();
    try {
      const result = text.match(pattern);
      if (Date.now() - start > timeout) {
        console.warn('⚠️ 正则匹配超时，返回null');
        return null;
      }
      return result;
    } catch (error) {
      console.error('❌ 正则匹配失败:', error);
      return null;
    }
  }

  /**
   * 验证提取的字段对象是否有效
   * @param obj 提取的对象
   * @returns 是否有效
   */
  private static isValidExtractedFieldObject(obj: any): boolean {
    try {
      // 基本验证：必须有sentenceIndex
      if (typeof obj.sentenceIndex !== 'number' || obj.sentenceIndex < 0) {
        return false;
      }

      // 验证modificationType是否有效
      const validTypes = ['modify', 'delete', 'merge', 'enhance', 'create', 'keep', 'add_dialogue', 'add_description', 'add_action', 'add_emotion'];
      if (!validTypes.includes(obj.modificationType)) {
        console.warn('⚠️ 无效的modificationType:', obj.modificationType);
        obj.modificationType = 'keep'; // 设置默认值
      }

      // 如果有建议内容或修改内容，认为是有效的
      if (obj.suggestion || obj.modifiedText || obj.reason) {
        return true;
      }

      // 如果有独立的创建操作，也认为是有效的
      if (obj.hasCreation === true) {
        return true;
      }

      // 如果是keep类型，也认为是有效的
      if (obj.modificationType === 'keep') {
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ 对象验证失败:', error);
      return false;
    }
  }

  /**
   * 标准化提取的字段建议
   * @param obj 原始对象
   * @returns 标准化后的对象
   */
  private static normalizeExtractedFieldSuggestion(obj: any): any {
    try {
      const normalized: any = {
        sentenceIndex: obj.sentenceIndex,
        originalText: obj.originalText || '',
        modificationType: obj.modificationType || 'keep',
        modifiedText: obj.modifiedText || '',
        suggestion: obj.suggestion || obj.modifiedText || '',
        reason: obj.reason || '',
        category: obj.category || 'style',
        severity: obj.severity || 'medium',
        impact: obj.impact || 'moderate',
        confidence: typeof obj.confidence === 'number' ? obj.confidence : 0.8,
        alternatives: Array.isArray(obj.alternatives) ? obj.alternatives : [],
        tags: Array.isArray(obj.tags) ? obj.tags : []
      };

      // 如果有独立的创建操作，添加专用字段
      if (obj.hasCreation === true) {
        normalized.hasCreation = true;
        normalized.createdSentences = Array.isArray(obj.createdSentences) ? obj.createdSentences : [];
        normalized.insertPosition = obj.insertPosition || 'after';
        normalized.contentType = obj.contentType || 'dialogue';
        normalized.insertMode = obj.insertMode || 'batch';
        normalized.contextHint = obj.contextHint || '';
      }

      return normalized;
    } catch (error) {
      console.error('❌ 对象标准化失败:', error);
      return obj;
    }
  }

  /**
   * 解析数组内容，处理包含引号的句子
   * @param arrayContent 数组内容字符串
   * @returns 解析后的句子数组
   */
  private static parseArrayContent(arrayContent: string): string[] {
    try {
      // 如果内容为空，返回空数组
      if (!arrayContent || arrayContent.trim() === '') {
        return [];
      }

      // 尝试直接JSON解析
      try {
        const parsed = JSON.parse(`[${arrayContent}]`);
        if (Array.isArray(parsed)) {
          return parsed.filter(item => typeof item === 'string' && item.trim().length > 0);
        }
      } catch (jsonError) {
        console.log('📝 JSON解析失败，使用智能分割方式');
      }

      // 智能分割方式：处理包含引号的句子
      const sentences: string[] = [];
      let currentSentence = '';
      let inQuotes = false;
      let quoteChar = '';
      let i = 0;

      while (i < arrayContent.length) {
        const char = arrayContent[i];

        // 处理引号
        if ((char === '"' || char === "'") && !inQuotes) {
          inQuotes = true;
          quoteChar = char;
          i++;
          continue;
        } else if (char === quoteChar && inQuotes) {
          // 检查是否是转义引号
          if (arrayContent[i - 1] === '\\') {
            currentSentence += char;
            i++;
            continue;
          }
          inQuotes = false;
          quoteChar = '';
          i++;
          continue;
        }

        // 处理逗号分隔
        if (char === ',' && !inQuotes) {
          const trimmed = currentSentence.trim();
          if (trimmed.length > 0) {
            sentences.push(trimmed);
          }
          currentSentence = '';
          i++;
          continue;
        }

        // 添加字符到当前句子
        if (inQuotes || (char !== ' ' || currentSentence.length > 0)) {
          currentSentence += char;
        }

        i++;
      }

      // 添加最后一个句子
      const trimmed = currentSentence.trim();
      if (trimmed.length > 0) {
        sentences.push(trimmed);
      }

      console.log('🔍 智能分割结果:', sentences);
      return sentences;

    } catch (error) {
      console.error('❌ 数组内容解析失败:', error);
      // 降级方案：简单分割
      return arrayContent
        .split(',')
        .map(item => item.trim().replace(/^["']|["']$/g, ''))
        .filter(item => item.length > 0);
    }
  }
}
