"use client";

import React, { useCallback } from 'react';
import { Framework } from '../types/framework';
import FrameworkSelector from './FrameworkSelector';
import './CompactControlPanel.css';

interface CompactControlPanelProps {
  // 素材库状态
  useMaterialLibrary: boolean;
  onMaterialLibraryToggle: () => void;

  // 框架选择
  selectedFramework: Framework | null;
  selectedFrameworks: Framework[];
  onFrameworkSelect: (framework: Framework) => void;
  onFrameworkMultiSelect: (frameworks: Framework[]) => void;

  // 关联管理
  associationCount: number;
  onOpenAssociationManager: () => void;
}

/**
 * 紧凑控制面板组件
 * 将框架选择、素材库状态、关联管理整合为紧凑的按钮组
 */
const CompactControlPanel: React.FC<CompactControlPanelProps> = ({
  useMaterialLibrary,
  onMaterialLibraryToggle,
  selectedFramework,
  selectedFrameworks,
  onFrameworkSelect,
  onFrameworkMultiSelect,
  associationCount,
  onOpenAssociationManager
}) => {
  // 处理素材库状态切换
  const handleMaterialLibraryToggle = useCallback(() => {
    onMaterialLibraryToggle();
  }, [onMaterialLibraryToggle]);

  // 处理关联管理按钮点击 - 直接打开弹窗
  const handleAssociationButtonClick = useCallback(() => {
    // 这里应该直接打开关联管理弹窗，而不是下拉展开
    // 我们需要修改这个逻辑来直接打开弹窗
    onOpenAssociationManager();
  }, [onOpenAssociationManager]);

  // 获取关联管理状态文本
  const getAssociationStatusText = useCallback(() => {
    if (associationCount > 0) {
      return `${associationCount} 项内容`;
    }
    return '添加关联';
  }, [associationCount]);



  return (
    <div className="compact-control-panel">
      {/* 素材库状态按钮 */}
      <button
        className={`control-button material-library-button ${useMaterialLibrary ? 'enabled' : 'disabled'}`}
        onClick={handleMaterialLibraryToggle}
        title={useMaterialLibrary ? '点击关闭素材库' : '点击开启素材库'}
        aria-label={useMaterialLibrary ? '关闭素材库' : '开启素材库'}
      >
        <div className="button-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <span className="button-text">
          {useMaterialLibrary ? '已开启' : '已关闭'}
        </span>
        <div className="status-indicator">
          <div className="status-dot"></div>
        </div>
      </button>

      {/* 框架选择器 - 直接使用FrameworkSelector组件 */}
      <div className="framework-selector-wrapper">
        <FrameworkSelector
          selectedFramework={selectedFramework}
          selectedFrameworks={selectedFrameworks}
          onFrameworkSelect={onFrameworkSelect}
          onFrameworkMultiSelect={onFrameworkMultiSelect}
          allowMultiple={true}
          allowCustom={true}
          allowDelete={true}
          className="compact-framework-selector"
        />
      </div>

      {/* 关联管理按钮 */}
      <button
        className={`control-button association-button ${associationCount > 0 ? 'has-content' : 'empty'}`}
        onClick={handleAssociationButtonClick}
        title={associationCount > 0 ? '管理关联内容' : '添加关联内容'}
        aria-label={associationCount > 0 ? '管理关联内容' : '添加关联内容'}
      >
        <div className="button-icon">
          {associationCount > 0 ? (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          ) : (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4v16m8-8H4"/>
            </svg>
          )}
        </div>
        <span className="button-text">
          {getAssociationStatusText()}
        </span>
        {associationCount > 0 && (
          <div className="content-badge">
            {associationCount}
          </div>
        )}
      </button>


    </div>
  );
};

export default CompactControlPanel;
