import { ISettingsComponent } from './ISettingsComponent';
import { APIKeyConfig, URLKeyPool, RotationStrategy } from '../../../types/apiKeyRotation';

/**
 * API设置弹窗组件接口
 */
export interface IAPISettingsDialogComponent extends ISettingsComponent {
  /**
   * 设置是否显示弹窗
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 获取当前API提供商
   * @returns 当前API提供商ID
   */
  getCurrentProvider(): string;
  
  /**
   * 设置当前API提供商
   * @param provider 提供商ID
   */
  setCurrentProvider(provider: string): void;
  
  /**
   * 设置API提供商变更回调
   * @param callback 回调函数
   */
  onProviderChange(callback: (provider: string) => void): void;
  
  /**
   * 获取当前API密钥
   * @param provider 提供商ID，如果不提供则返回当前提供商的密钥
   * @returns API密钥
   */
  getAPIKey(provider?: string): string;
  
  /**
   * 设置API密钥
   * @param key API密钥
   * @param provider 提供商ID，如果不提供则设置当前提供商的密钥
   */
  setAPIKey(key: string, provider?: string): void;
  
  /**
   * 获取当前模型
   * @returns 当前模型ID
   */
  getCurrentModel(): string;
  
  /**
   * 设置当前模型
   * @param model 模型ID
   */
  setCurrentModel(model: string): void;
  
  /**
   * 设置模型变更回调
   * @param callback 回调函数
   */
  onModelChange(callback: (model: string) => void): void;
  
  /**
   * 获取API端点URL
   * @param provider 提供商ID，如果不提供则返回当前提供商的URL
   * @returns API端点URL
   */
  getAPIEndpoint(provider?: string): string;
  
  /**
   * 设置API端点URL
   * @param url API端点URL
   * @param provider 提供商ID，如果不提供则设置当前提供商的URL
   */
  setAPIEndpoint(url: string, provider?: string): void;
  
  /**
   * 获取可用模型列表
   * @returns 模型列表
   */
  getAvailableModels(): string[];
  
  /**
   * 设置可用模型列表
   * @param models 模型列表
   */
  setAvailableModels(models: string[]): void;
  
  /**
   * 获取是否启用流式输出
   * @returns 是否启用流式输出
   */
  getStreamingEnabled(): boolean;
  
  /**
   * 设置是否启用流式输出
   * @param enabled 是否启用
   */
  setStreamingEnabled(enabled: boolean): void;

  // ===== API密钥轮播相关方法 =====

  /**
   * 是否启用API密钥轮播
   * @returns 是否启用轮播
   */
  isRotationEnabled(): boolean;

  /**
   * 设置是否启用API密钥轮播
   * @param enabled 是否启用
   */
  setRotationEnabled(enabled: boolean): void;

  /**
   * 添加API密钥到轮播池
   * @param url API端点URL
   * @param key API密钥
   * @param options 可选配置
   */
  addRotationKey(url: string, key: string, options?: {
    weight?: number;
    customWaitTime?: number;
    notes?: string;
  }): APIKeyConfig;

  /**
   * 删除轮播池中的API密钥
   * @param url API端点URL
   * @param keyId 密钥ID
   */
  removeRotationKey(url: string, keyId: string): boolean;

  /**
   * 获取URL的密钥池
   * @param url API端点URL
   */
  getURLKeyPool(url: string): URLKeyPool | null;

  /**
   * 获取所有URL密钥池
   */
  getAllURLKeyPools(): Record<string, URLKeyPool>;

  /**
   * 设置URL的轮播策略
   * @param url API端点URL
   * @param strategy 轮播策略
   */
  setRotationStrategy(url: string, strategy: RotationStrategy): void;

  /**
   * 获取下一个可用的API密钥
   * @param url API端点URL
   */
  getNextRotationKey(url: string): APIKeyConfig | null;

  /**
   * 报告API请求结果（用于故障恢复）
   * @param keyId 密钥ID
   * @param success 是否成功
   * @param error 错误信息（如果失败）
   * @param responseTime 响应时间（毫秒）
   */
  reportRequestResult(keyId: string, success: boolean, error?: string, responseTime?: number): void;

  /**
   * 手动恢复失败的密钥
   * @param keyId 密钥ID
   */
  recoverFailedKey(keyId: string): void;

  /**
   * 获取密钥使用统计
   */
  getKeyUsageStats(): Map<string, any>;
}
