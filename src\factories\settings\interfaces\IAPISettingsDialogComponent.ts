import { ISettingsComponent } from './ISettingsComponent';

/**
 * API设置弹窗组件接口
 */
export interface IAPISettingsDialogComponent extends ISettingsComponent {
  /**
   * 设置是否显示弹窗
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 获取当前API提供商
   * @returns 当前API提供商ID
   */
  getCurrentProvider(): string;
  
  /**
   * 设置当前API提供商
   * @param provider 提供商ID
   */
  setCurrentProvider(provider: string): void;
  
  /**
   * 设置API提供商变更回调
   * @param callback 回调函数
   */
  onProviderChange(callback: (provider: string) => void): void;
  
  /**
   * 获取当前API密钥
   * @param provider 提供商ID，如果不提供则返回当前提供商的密钥
   * @returns API密钥
   */
  getAPIKey(provider?: string): string;
  
  /**
   * 设置API密钥
   * @param key API密钥
   * @param provider 提供商ID，如果不提供则设置当前提供商的密钥
   */
  setAPIKey(key: string, provider?: string): void;
  
  /**
   * 获取当前模型
   * @returns 当前模型ID
   */
  getCurrentModel(): string;
  
  /**
   * 设置当前模型
   * @param model 模型ID
   */
  setCurrentModel(model: string): void;
  
  /**
   * 设置模型变更回调
   * @param callback 回调函数
   */
  onModelChange(callback: (model: string) => void): void;
  
  /**
   * 获取API端点URL
   * @param provider 提供商ID，如果不提供则返回当前提供商的URL
   * @returns API端点URL
   */
  getAPIEndpoint(provider?: string): string;
  
  /**
   * 设置API端点URL
   * @param url API端点URL
   * @param provider 提供商ID，如果不提供则设置当前提供商的URL
   */
  setAPIEndpoint(url: string, provider?: string): void;
  
  /**
   * 获取可用模型列表
   * @returns 模型列表
   */
  getAvailableModels(): string[];
  
  /**
   * 设置可用模型列表
   * @param models 模型列表
   */
  setAvailableModels(models: string[]): void;
  
  /**
   * 获取是否启用流式输出
   * @returns 是否启用流式输出
   */
  getStreamingEnabled(): boolean;
  
  /**
   * 设置是否启用流式输出
   * @param enabled 是否启用
   */
  setStreamingEnabled(enabled: boolean): void;
}
