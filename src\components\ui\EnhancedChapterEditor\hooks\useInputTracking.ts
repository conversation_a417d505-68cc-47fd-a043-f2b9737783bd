"use client";

import { useState, useCallback, useRef } from 'react';

interface RippleEffect {
  id: string;
  x: number;
  y: number;
  timestamp: number;
}

interface InputMetrics {
  totalCharacters: number;
  wordsPerMinute: number;
  lastInputTime: number;
  inputFrequency: number;
}

/**
 * 输入追踪Hook
 * 追踪用户输入行为，管理墨水扩散效果和输入统计
 */
export const useInputTracking = (textareaRef: React.RefObject<HTMLTextAreaElement>) => {
  const [ripples, setRipples] = useState<RippleEffect[]>([]);
  const [metrics, setMetrics] = useState<InputMetrics>({
    totalCharacters: 0,
    wordsPerMinute: 0,
    lastInputTime: 0,
    inputFrequency: 0
  });

  const inputHistoryRef = useRef<number[]>([]);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // 获取光标位置
  const getCursorPosition = useCallback(() => {
    if (!textareaRef.current) return { x: 0, y: 0 };

    const textarea = textareaRef.current;
    const rect = textarea.getBoundingClientRect();

    // 创建一个临时的div来计算光标位置
    const div = document.createElement('div');
    const style = window.getComputedStyle(textarea);

    // 复制textarea的样式到div
    div.style.position = 'absolute';
    div.style.visibility = 'hidden';
    div.style.whiteSpace = 'pre-wrap';
    div.style.wordWrap = 'break-word';
    div.style.font = style.font;
    div.style.padding = style.padding;
    div.style.border = style.border;
    div.style.width = style.width;
    div.style.height = style.height;
    div.style.lineHeight = style.lineHeight;

    document.body.appendChild(div);

    try {
      const selectionStart = textarea.selectionStart || 0;
      const textBeforeCursor = textarea.value.substring(0, selectionStart);

      div.textContent = textBeforeCursor;

      // 添加一个span来标记光标位置
      const span = document.createElement('span');
      span.textContent = '|';
      div.appendChild(span);

      const spanRect = span.getBoundingClientRect();
      const divRect = div.getBoundingClientRect();

      // 计算相对于textarea的位置
      const x = spanRect.left - rect.left;
      const y = spanRect.top - rect.top + textarea.scrollTop;

      return { x: Math.max(0, x), y: Math.max(0, y) };
    } catch (error) {
      console.warn('Failed to calculate cursor position:', error);
      return { x: rect.width / 2, y: rect.height / 2 };
    } finally {
      document.body.removeChild(div);
    }
  }, [textareaRef]);

  // 添加涟漪效果
  const addRipple = useCallback((x: number, y: number) => {
    const ripple: RippleEffect = {
      id: `ripple-${Date.now()}-${Math.random()}`,
      x,
      y,
      timestamp: Date.now()
    };

    setRipples(prev => [...prev, ripple]);

    // 1200ms后自动清理涟漪（匹配新动画持续时间）
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== ripple.id));
    }, 1200);
  }, []);

  // 更新输入统计
  const updateMetrics = useCallback((newLength: number) => {
    const now = Date.now();

    setMetrics(prev => {
      // 更新输入历史
      inputHistoryRef.current.push(now);

      // 只保留最近1分钟的输入记录
      const oneMinuteAgo = now - 60000;
      inputHistoryRef.current = inputHistoryRef.current.filter(time => time > oneMinuteAgo);

      // 计算WPM（假设平均每个单词5个字符）
      const recentInputs = inputHistoryRef.current.length;
      const wordsPerMinute = Math.round((recentInputs / 5) * 60 / 60); // 简化计算

      // 计算输入频率（每分钟按键次数）
      const inputFrequency = recentInputs;

      return {
        totalCharacters: newLength,
        wordsPerMinute,
        lastInputTime: now,
        inputFrequency
      };
    });
  }, []);

  // 处理输入事件
  const handleInput = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newLength = e.target.value.length;
    const lengthDiff = newLength - metrics.totalCharacters;

    // 只在有新字符输入时创建涟漪效果
    if (lengthDiff > 0) {
      // 防抖处理，避免过于频繁的动画
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        const { x, y } = getCursorPosition();
        addRipple(x, y);
      }, 50); // 50ms防抖
    }

    updateMetrics(newLength);
  }, [metrics.totalCharacters, getCursorPosition, addRipple, updateMetrics]);

  // 清理所有涟漪
  const clearRipples = useCallback(() => {
    setRipples([]);
  }, []);

  // 获取输入活跃度
  const getInputActivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastInput = now - metrics.lastInputTime;

    if (timeSinceLastInput < 1000) return 'active';
    if (timeSinceLastInput < 5000) return 'recent';
    if (timeSinceLastInput < 30000) return 'idle';
    return 'inactive';
  }, [metrics.lastInputTime]);

  // 检查是否为快速输入
  const isFastTyping = useCallback(() => {
    return metrics.inputFrequency > 60; // 每分钟超过60次按键
  }, [metrics.inputFrequency]);

  return {
    ripples,
    metrics,
    handleInput,
    clearRipples,
    getInputActivity,
    isFastTyping,
    getCursorPosition
  };
};
