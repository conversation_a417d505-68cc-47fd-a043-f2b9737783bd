import { IAIComponent } from './IAIComponent';

/**
 * 书籍类型
 */
export type Book = {
  id: string;
  title: string;
  chapters: Chapter[];
};

/**
 * 章节类型
 */
export type Chapter = {
  id: string;
  title: string;
  content: string;
};

/**
 * AI拆书组件接口
 * 用于分析和拆解现有作品，支持多本作品同步拆解和混合拆解
 */
export interface IAIBookAnalysisComponent extends IAIComponent {
  /**
   * 设置要分析的书籍
   * @param books 书籍列表
   */
  setBooks(books: Book[]): void;

  /**
   * 设置分析模式
   * @param mode 分析模式
   * - single: 单本拆解
   * - merged: 合并拆解
   * - mixed: 混合拆解
   * - sync: 同步拆解
   */
  setAnalysisMode(mode: 'single' | 'merged' | 'mixed' | 'sync'): void;

  /**
   * 设置提示词模板
   * @param template 提示词模板
   */
  setPromptTemplate(template: string): void;

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void;

  /**
   * 设置分析完成回调
   * @param callback 回调函数
   */
  setOnAnalysisComplete(callback: (result: string) => void): void;

  /**
   * 取消分析
   */
  cancelAnalysis(): void;

  /**
   * 分析书籍
   * @returns 分析结果
   */
  analyze(): Promise<string>;
}
