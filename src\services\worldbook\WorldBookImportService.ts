/**
 * 世界书导入服务
 * 负责处理JSON文件解析、数据验证、格式转换等核心逻辑
 */

import { 
  WorldBookEntry, 
  WorldBookData, 
  WorldBookPrefix, 
  ImportResult, 
  ImportOptions,
  ImportError,
  ImportErrorType,
  DEFAULT_CONVERSION_MAPPING,
  ConversionMapping
} from '../../types/worldbook';

export class WorldBookImportService {
  private static instance: WorldBookImportService;

  private constructor() {}

  static getInstance(): WorldBookImportService {
    if (!WorldBookImportService.instance) {
      WorldBookImportService.instance = new WorldBookImportService();
    }
    return WorldBookImportService.instance;
  }

  /**
   * 导入世界书文件
   * @param file JSON文件
   * @param options 导入选项
   * @returns 导入结果和转换后的前置消息
   */
  async importWorldBook(file: File, options: ImportOptions = {}): Promise<{
    result: ImportResult;
    prefixes: WorldBookPrefix[];
  }> {
    const errors: string[] = [];
    const fileName = this.extractFileName(file.name);

    try {
      // 1. 读取文件内容
      const content = await this.readFileContent(file);

      // 2. 解析JSON
      const worldBook = this.parseWorldBookJson(content);

      // 3. 验证格式
      this.validateWorldBookFormat(worldBook);

      // 4. 提取并过滤有效条目
      const entries = this.extractValidEntries(worldBook, options);

      // 5. 转换为前置消息格式
      const prefixes = this.convertEntriesToPrefixes(entries, fileName, options);

      const totalEntries = worldBook.entries ? Object.keys(worldBook.entries).length : 0;
      const result: ImportResult = {
        total: totalEntries,
        imported: prefixes.length,
        skipped: totalEntries - prefixes.length,
        errors,
        fileName,
        importedAt: new Date()
      };

      return { result, prefixes };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      errors.push(errorMessage);

      const result: ImportResult = {
        total: 0,
        imported: 0,
        skipped: 0,
        errors,
        fileName,
        importedAt: new Date()
      };

      return { result, prefixes: [] };
    }
  }

  /**
   * 导入世界书文件（简化版本，只返回结果）
   * @param file JSON文件
   * @param options 导入选项
   * @returns 导入结果
   */
  async importWorldBookSimple(file: File, options: ImportOptions = {}): Promise<ImportResult> {
    const { result } = await this.importWorldBook(file, options);
    return result;
  }

  /**
   * 预览世界书文件内容
   * @param file JSON文件
   * @returns 预览信息
   */
  async previewWorldBook(file: File): Promise<{
    fileName: string;
    totalEntries: number;
    validEntries: number;
    emptyEntries: number;
    disabledEntries: number;
    sampleEntries: WorldBookEntry[];
  }> {
    try {
      const content = await this.readFileContent(file);
      const worldBook = this.parseWorldBookJson(content);
      this.validateWorldBookFormat(worldBook);

      // 处理缺少entries字段的情况
      const entries = worldBook.entries ? Object.values(worldBook.entries) : [];

      const validEntries = entries.filter(entry =>
        entry && entry.content && entry.content.trim().length > 0
      );
      const emptyEntries = entries.filter(entry =>
        !entry || !entry.content || entry.content.trim().length === 0
      );
      const disabledEntries = entries.filter(entry => entry && entry.disable);

      // 获取前5个有效条目作为样本
      const sampleEntries = validEntries.slice(0, 5);

      return {
        fileName: this.extractFileName(file.name),
        totalEntries: entries.length,
        validEntries: validEntries.length,
        emptyEntries: emptyEntries.length,
        disabledEntries: disabledEntries.length,
        sampleEntries
      };

    } catch (error) {
      throw new Error(`预览失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns 文件内容字符串
   */
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        if (content) {
          resolve(content);
        } else {
          reject(new Error('文件内容为空'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsText(file, 'utf-8');
    });
  }

  /**
   * 解析世界书JSON
   * @param content JSON字符串
   * @returns 解析后的世界书数据
   */
  private parseWorldBookJson(content: string): WorldBookData {
    try {
      const parsed = JSON.parse(content);

      // 检查是否为预设文件格式（数组格式）
      if (Array.isArray(parsed)) {
        console.log('检测到预设文件格式，正在转换为世界书格式...');
        return this.convertPresetToWorldBook(parsed);
      }

      // 检查是否为鹿鹿预设格式（包含prompts数组）
      if (parsed && typeof parsed === 'object' && Array.isArray(parsed.prompts)) {
        console.log('检测到鹿鹿预设格式，正在转换为世界书格式...');
        return this.convertPromptsToWorldBook(parsed.prompts);
      }

      // 检查是否为标准世界书格式
      if (parsed && typeof parsed === 'object' && parsed.entries) {
        return parsed as WorldBookData;
      }

      // 如果是对象但没有entries字段，尝试创建entries
      if (parsed && typeof parsed === 'object') {
        console.log('检测到非标准格式，尝试转换...');
        return {
          entries: {},
          ...parsed
        } as WorldBookData;
      }

      throw new Error('无法识别的文件格式');
    } catch (error) {
      if (error instanceof Error && error.message !== 'JSON格式无效，请检查文件格式') {
        throw error;
      }
      throw new Error('JSON格式无效，请检查文件格式');
    }
  }

  /**
   * 验证世界书格式
   * @param worldBook 世界书数据
   */
  private validateWorldBookFormat(worldBook: WorldBookData): void {
    if (!worldBook || typeof worldBook !== 'object') {
      throw new Error('无效的世界书格式：根对象无效');
    }

    // 如果没有entries字段，尝试创建一个空的entries对象
    if (!worldBook.entries) {
      console.warn('世界书文件缺少entries字段，将创建空的entries对象');
      worldBook.entries = {};
    }

    if (typeof worldBook.entries !== 'object') {
      throw new Error('无效的世界书格式：entries字段类型错误');
    }

    // 验证entries中至少有一个条目
    const entryKeys = Object.keys(worldBook.entries);
    if (entryKeys.length === 0) {
      console.warn('世界书中没有找到任何条目，可能是空文件或格式不正确');
      // 不抛出错误，允许导入空的世界书
    }
  }

  /**
   * 提取有效条目
   * @param worldBook 世界书数据
   * @param options 导入选项
   * @returns 有效条目数组
   */
  private extractValidEntries(worldBook: WorldBookData, options: ImportOptions): WorldBookEntry[] {
    // 如果没有entries或entries为空，返回空数组
    if (!worldBook.entries || Object.keys(worldBook.entries).length === 0) {
      console.warn('世界书没有entries或entries为空');
      return [];
    }

    const entries = Object.values(worldBook.entries);

    return entries.filter(entry => {
      // 确保entry存在且为对象
      if (!entry || typeof entry !== 'object') {
        return false;
      }

      // 跳过空内容条目
      if (options.skipEmptyContent !== false) {
        if (!entry.content || entry.content.trim().length === 0) {
          return false;
        }
      }

      // 跳过禁用的条目
      if (options.skipDisabled && entry.disable) {
        return false;
      }

      // 检查最小内容长度
      if (options.minContentLength && entry.content && entry.content.length < options.minContentLength) {
        return false;
      }

      return true;
    });
  }

  /**
   * 转换条目为前置消息格式
   * @param entries 世界书条目数组
   * @param fileName 文件名
   * @param options 导入选项
   * @returns 前置消息数组
   */
  private convertEntriesToPrefixes(
    entries: WorldBookEntry[], 
    fileName: string, 
    options: ImportOptions
  ): WorldBookPrefix[] {
    const mapping = DEFAULT_CONVERSION_MAPPING;
    
    return entries.map(entry => this.convertEntry(entry, fileName, mapping, options));
  }

  /**
   * 转换单个条目
   * @param entry 世界书条目
   * @param fileName 文件名
   * @param mapping 转换映射
   * @param options 导入选项
   * @returns 前置消息
   */
  private convertEntry(
    entry: WorldBookEntry,
    fileName: string,
    mapping: ConversionMapping,
    options: ImportOptions
  ): WorldBookPrefix {
    // 生成唯一ID
    const id = `worldbook_${fileName}_${entry.uid || 'unknown'}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 安全地获取数组字段，如果不存在则使用空数组
    const keys = Array.isArray(entry.key) ? entry.key : [];
    const keysSecondary = Array.isArray(entry.keysecondary) ? entry.keysecondary : [];

    // 合并关键词
    const allTags = [
      ...keys,
      ...keysSecondary,
      fileName,
      entry.constant ? '常量' : '变量',
      entry.selective ? '选择性' : '非选择性'
    ].filter(tag => tag && typeof tag === 'string' && tag.trim().length > 0);

    // 计算置信度
    const confidence = mapping.confidenceCalculator(entry);

    return {
      // SavedAIPrefix字段
      id,
      content: entry.content || '',
      category: options.customCategory || mapping.defaultCategory,
      description: entry.comment || `世界书条目 ${entry.uid || 'unknown'}`,
      useCase: `来自世界书：${fileName}`,
      reasoning: `从${fileName}导入的世界书条目，原始UID: ${entry.uid || 'unknown'}`,
      confidence,
      tags: allTags,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0,
      isFavorite: false,

      // WorldBookPrefix特有字段
      worldBookSource: fileName,
      originalUid: entry.uid || 'unknown',
      isConstant: entry.constant || false,
      originalOrder: entry.order || 0,
      originalKeys: keys,
      originalKeysSecondary: keysSecondary,
      originalPosition: entry.position || 0,
      triggerProbability: entry.probability || 100,
      isSelective: entry.selective || false,
      // 关键词触发功能的默认设置
      enableKeywordTrigger: false, // 默认关闭，用户可以手动开启
      sendAsIndependentMessage: true // 默认作为独立消息发送
    };
  }

  /**
   * 转换预设文件格式为世界书格式
   * @param presetArray 预设文件数组
   * @returns 世界书数据
   */
  private convertPresetToWorldBook(presetArray: any[]): WorldBookData {
    const entries: { [key: string]: WorldBookEntry } = {};

    presetArray.forEach((preset, index) => {
      if (preset && typeof preset === 'object') {
        // 生成UID
        const uid = preset.identifier || `preset_${index}`;

        // 转换为世界书条目格式
        const entry: WorldBookEntry = {
          uid: uid,
          comment: preset.name || `预设条目 ${index + 1}`,
          content: preset.content || '',
          key: preset.keys || [],
          keysecondary: [],
          constant: false,
          selective: false,
          order: index,
          position: 0,
          disable: false,
          probability: 100
        };

        entries[uid] = entry;
      }
    });

    return {
      entries,
      name: '导入的预设文件',
      description: '从预设文件转换的世界书'
    };
  }

  /**
   * 转换鹿鹿预设prompts格式为世界书格式
   * @param promptsArray prompts数组
   * @returns 世界书数据
   */
  private convertPromptsToWorldBook(promptsArray: any[]): WorldBookData {
    const entries: { [key: string]: WorldBookEntry } = {};
    let validEntryCount = 0;

    promptsArray.forEach((prompt, index) => {
      if (prompt && typeof prompt === 'object') {
        // 过滤掉marker类型和空content的条目
        if (prompt.marker === true || !prompt.content || prompt.content.trim().length === 0) {
          console.log(`跳过条目 ${prompt.name || index}: marker=${prompt.marker}, content为空=${!prompt.content}`);
          return;
        }

        // 生成UID
        const uid = prompt.identifier || `prompt_${index}`;

        // 转换为世界书条目格式
        const entry: WorldBookEntry = {
          uid: uid,
          comment: prompt.name || `鹿鹿预设条目 ${index + 1}`,
          content: prompt.content,
          key: [], // prompts格式没有关键词，使用空数组
          keysecondary: [],
          constant: false,
          selective: false,
          order: index,
          position: 0,
          disable: prompt.enabled === false, // 如果有enabled字段且为false，则禁用
          probability: 100,
          // 关键词触发功能的默认设置
          enableKeywordTrigger: false, // 默认关闭，用户可以手动开启
          sendAsIndependentMessage: true // 默认作为独立消息发送
        };

        entries[uid] = entry;
        validEntryCount++;
        console.log(`转换条目: ${prompt.name} (${uid})`);
      }
    });

    console.log(`成功转换 ${validEntryCount} 个有效的鹿鹿预设条目`);

    return {
      entries,
      name: '导入的鹿鹿预设',
      description: '从鹿鹿预设文件转换的世界书'
    };
  }

  /**
   * 提取文件名（去除扩展名）
   * @param fullFileName 完整文件名
   * @returns 不含扩展名的文件名
   */
  private extractFileName(fullFileName: string): string {
    return fullFileName.replace(/\.json$/i, '');
  }

  /**
   * 验证单个条目格式
   * @param entry 条目对象
   * @param uid 条目UID
   * @returns 验证结果
   */
  private validateEntry(entry: any, uid: string): ImportError | null {
    if (!entry || typeof entry !== 'object') {
      return {
        type: ImportErrorType.INVALID_ENTRY_FORMAT,
        message: `条目 ${uid} 格式无效`,
        entryUid: parseInt(uid),
        details: entry
      };
    }
    
    // 检查必需字段
    const requiredFields = ['uid', 'comment', 'content'];
    for (const field of requiredFields) {
      if (!(field in entry)) {
        return {
          type: ImportErrorType.INVALID_ENTRY_FORMAT,
          message: `条目 ${uid} 缺少必需字段: ${field}`,
          entryUid: parseInt(uid),
          details: { missingField: field }
        };
      }
    }
    
    return null;
  }
}
