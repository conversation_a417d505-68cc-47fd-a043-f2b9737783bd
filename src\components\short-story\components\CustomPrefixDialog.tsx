import React, { useState, useEffect } from 'react';
import { SavedAIPrefix } from '../../../services/ai/AIGeneratedPrefixStorageService';
import {
  EditIcon,
  DeleteIcon,
  StarIcon,
  StarFilledIcon,
  PlusIcon,
  XIcon,
  getCategoryIcon
} from '../../common/icons/PrefixIcons';

interface CustomPrefixDialogProps {
  isOpen: boolean;
  onClose: () => void;
  customPrefixes: SavedAIPrefix[];
  selectedPrefixes: Set<string>;
  onPrefixToggle: (id: string) => void;
  onDeletePrefix: (id: string) => void;
  onToggleFavorite: (id: string) => void;
  onEditPrefix: (prefix: SavedAIPrefix) => void;
  onCreatePrefix: () => void;
  onApplySelected: () => void;
}

export const CustomPrefixDialog: React.FC<CustomPrefixDialogProps> = ({
  isOpen,
  onClose,
  customPrefixes,
  selectedPrefixes,
  onPrefixToggle,
  onDeletePrefix,
  onToggleFavorite,
  onEditPrefix,
  onCreatePrefix,
  onApplySelected
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // 重置搜索状态
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setShowFavoritesOnly(false);
    }
  }, [isOpen]);

  // 过滤自定义前置消息
  const getFilteredCustomPrefixes = (): SavedAIPrefix[] => {
    let filtered = customPrefixes;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(prefix =>
        prefix.content.toLowerCase().includes(query) ||
        prefix.description.toLowerCase().includes(query) ||
        prefix.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(prefix => prefix.isFavorite);
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const filteredPrefixes = getFilteredCustomPrefixes();

  const handlePrefixClick = (prefix: SavedAIPrefix, e: React.MouseEvent) => {
    e.stopPropagation();
    onPrefixToggle(prefix.id);
  };

  const handleDeleteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('确定要删除这个自定义前置消息吗？')) {
      onDeletePrefix(prefixId);
    }
  };

  const handleFavoriteClick = (prefixId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(prefixId);
  };

  const handleSelectAll = () => {
    filteredPrefixes.forEach(prefix => {
      if (!selectedPrefixes.has(prefix.id)) {
        onPrefixToggle(prefix.id);
      }
    });
  };

  const handleClearSelection = () => {
    filteredPrefixes.forEach(prefix => {
      if (selectedPrefixes.has(prefix.id)) {
        onPrefixToggle(prefix.id);
      }
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10002] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              {getCategoryIcon('custom', 24, 'text-green-600 mr-2')}
              自定义前置消息
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              管理您创建的自定义前置消息模板
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={onCreatePrefix}
              className="flex items-center px-3 py-2 text-sm text-green-600 border border-green-300 rounded-lg hover:bg-green-50"
              title="创建新的自定义前置消息"
            >
              <PlusIcon size={16} className="mr-1" />
              新建
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XIcon size={24} />
            </button>
          </div>
        </div>

        {/* 过滤器区域 */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-4 items-center">
            {/* 搜索框 */}
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="搜索自定义前置消息..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
              />
            </div>

            {/* 收藏过滤 */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showFavoritesOnly}
                onChange={(e) => setShowFavoritesOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="text-sm text-gray-700">仅显示收藏</span>
            </label>

            {/* 统计信息 */}
            <div className="text-sm text-gray-600">
              共 {customPrefixes.length} 个，显示 {filteredPrefixes.length} 个
            </div>
          </div>
        </div>

        {/* 操作栏 */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                disabled={filteredPrefixes.length === 0}
                className="px-3 py-1 text-sm text-green-600 border border-green-300 rounded hover:bg-green-50 disabled:text-gray-400 disabled:border-gray-300"
              >
                全选 ({filteredPrefixes.length})
              </button>
              <button
                onClick={handleClearSelection}
                disabled={selectedPrefixes.size === 0}
                className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:text-gray-400"
              >
                清空选择
              </button>
            </div>

            <div className="text-sm text-gray-600">
              已选择 {Array.from(selectedPrefixes).filter(id => 
                customPrefixes.some(p => p.id === id)
              ).length} 个模板
            </div>
          </div>
        </div>

        {/* 前置消息列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredPrefixes.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">📝</div>
              <p>没有找到匹配的自定义前置消息</p>
              <p className="text-sm mt-2">尝试调整搜索条件或创建新的自定义前置消息</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPrefixes.map((prefix) => (
                <div
                  key={prefix.id}
                  onClick={(e) => handlePrefixClick(prefix, e)}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedPrefixes.has(prefix.id)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-300 hover:bg-green-25'
                  }`}
                >
                  {/* 头部信息 */}
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2 flex-wrap">
                      <span className="text-xs px-2 py-1 bg-green-100 text-green-600 rounded">
                        自定义
                      </span>
                      {prefix.phase && (
                        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded">
                          {prefix.phase}
                        </span>
                      )}
                    </div>

                    <div className="flex space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditPrefix(prefix);
                        }}
                        className="text-gray-400 hover:text-blue-500 transition-colors"
                        title="编辑"
                      >
                        <EditIcon size={16} />
                      </button>
                      <button
                        onClick={(e) => handleFavoriteClick(prefix.id, e)}
                        className={`${prefix.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-500 transition-colors`}
                        title="收藏"
                      >
                        {prefix.isFavorite ? <StarFilledIcon size={16} /> : <StarIcon size={16} />}
                      </button>
                      <button
                        onClick={(e) => handleDeleteClick(prefix.id, e)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        title="删除"
                      >
                        <DeleteIcon size={16} />
                      </button>
                    </div>
                  </div>

                  {/* 内容 */}
                  <div className="mb-3">
                    <p className="text-sm text-gray-800 line-clamp-3">
                      {prefix.content}
                    </p>
                  </div>

                  {/* 描述 */}
                  <div className="mb-2">
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {prefix.description}
                    </p>
                  </div>

                  {/* 标签 */}
                  {prefix.tags.length > 0 && (
                    <div className="mb-2">
                      <div className="flex flex-wrap gap-1">
                        {prefix.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="text-xs px-1 py-0.5 bg-gray-100 text-gray-600 rounded">
                            {tag}
                          </span>
                        ))}
                        {prefix.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{prefix.tags.length - 3}</span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 底部信息 */}
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>使用 {prefix.usageCount} 次</span>
                    <span>{new Date(prefix.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部操作 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              显示 {filteredPrefixes.length} / {customPrefixes.length} 个自定义模板
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => {
                  onApplySelected();
                  onClose();
                }}
                disabled={Array.from(selectedPrefixes).filter(id => 
                  customPrefixes.some(p => p.id === id)
                ).length === 0}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                应用选择 ({Array.from(selectedPrefixes).filter(id => 
                  customPrefixes.some(p => p.id === id)
                ).length}个)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
