"use client";

import { useState, useCallback } from 'react';

interface FocusState {
  isFocused: boolean;
  isHovered: boolean;
  focusTime: number | null;
  hoverTime: number | null;
}

/**
 * 焦点状态管理Hook
 * 管理编辑器的焦点和悬停状态，用于控制动画效果
 */
export const useFocusState = () => {
  const [state, setState] = useState<FocusState>({
    isFocused: false,
    isHovered: false,
    focusTime: null,
    hoverTime: null
  });

  // 处理获得焦点
  const handleFocus = useCallback(() => {
    setState(prev => ({
      ...prev,
      isFocused: true,
      focusTime: Date.now()
    }));
  }, []);

  // 处理失去焦点
  const handleBlur = useCallback(() => {
    setState(prev => ({
      ...prev,
      isFocused: false,
      focusTime: null
    }));
  }, []);

  // 处理鼠标进入
  const handleMouseEnter = useCallback(() => {
    setState(prev => ({
      ...prev,
      isHovered: true,
      hoverTime: Date.now()
    }));
  }, []);

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    setState(prev => ({
      ...prev,
      isHovered: false,
      hoverTime: null
    }));
  }, []);

  // 获取焦点持续时间
  const getFocusDuration = useCallback(() => {
    if (!state.isFocused || !state.focusTime) return 0;
    return Date.now() - state.focusTime;
  }, [state.isFocused, state.focusTime]);

  // 获取悬停持续时间
  const getHoverDuration = useCallback(() => {
    if (!state.isHovered || !state.hoverTime) return 0;
    return Date.now() - state.hoverTime;
  }, [state.isHovered, state.hoverTime]);

  // 检查是否为长时间焦点（用于调整动画强度）
  const isLongFocus = useCallback(() => {
    return getFocusDuration() > 5000; // 5秒以上算长时间焦点
  }, [getFocusDuration]);

  // 检查是否为长时间悬停
  const isLongHover = useCallback(() => {
    return getHoverDuration() > 2000; // 2秒以上算长时间悬停
  }, [getHoverDuration]);

  // 获取当前交互状态
  const getInteractionState = useCallback(() => {
    if (state.isFocused && isLongFocus()) return 'deep-focus';
    if (state.isFocused) return 'focused';
    if (state.isHovered && isLongHover()) return 'long-hover';
    if (state.isHovered) return 'hovered';
    return 'idle';
  }, [state.isFocused, state.isHovered, isLongFocus, isLongHover]);

  return {
    ...state,
    handleFocus,
    handleBlur,
    handleMouseEnter,
    handleMouseLeave,
    getFocusDuration,
    getHoverDuration,
    isLongFocus,
    isLongHover,
    getInteractionState
  };
};
