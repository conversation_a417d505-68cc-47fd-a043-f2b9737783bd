"use client";

import { MessageBuilder } from './MessageBuilder';

/**
 * 扩展消息构建器
 * 用于构建带有额外标记的消息数组，支持标记系统生成的预设消息
 */
export class ExtendedMessageBuilder {
  private messageBuilder: MessageBuilder;

  /**
   * 创建扩展消息构建器
   */
  constructor() {
    this.messageBuilder = new MessageBuilder();
  }

  /**
   * 添加系统消息
   * @param content 消息内容
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @returns 当前构建器实例，支持链式调用
   */
  addSystemMessage(content: string, isSystemGenerated: boolean = true): ExtendedMessageBuilder {
    this.messageBuilder.addSystemMessage(content);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 添加助手消息
   * @param content 消息内容
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantMessage(content: string, isSystemGenerated: boolean = true): ExtendedMessageBuilder {
    this.messageBuilder.addAssistantMessage(content);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 添加用户消息
   * @param content 消息内容
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param importantRequest 重要请求（会追加到消息末尾）
   * @returns 当前构建器实例，支持链式调用
   */
  addUserMessage(content: string, isSystemGenerated: boolean = true, importantRequest?: string): ExtendedMessageBuilder {
    this.messageBuilder.addUserMessage(content, importantRequest);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 添加助手展示信息消息
   * 这是一个特殊的助手消息，用于展示已有信息
   * @param info 要展示的信息
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantInfoMessage(info: string, isSystemGenerated: boolean = true): ExtendedMessageBuilder {
    this.messageBuilder.addAssistantInfoMessage(info);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 添加助手确认任务消息
   * 这是一个特殊的助手消息，用于确认任务
   * @param taskName 任务名称
   * @param targetName 目标名称
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantConfirmMessage(taskName: string, targetName: string, isSystemGenerated: boolean = true): ExtendedMessageBuilder {
    this.messageBuilder.addAssistantConfirmMessage(taskName, targetName);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 添加用户直接生成指令消息
   * 这是一个特殊的用户消息，用于要求直接生成内容
   * @param contentType 内容类型
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @returns 当前构建器实例，支持链式调用
   */
  addUserDirectGenerateMessage(contentType: string, isSystemGenerated: boolean = true): ExtendedMessageBuilder {
    this.messageBuilder.addUserDirectGenerateMessage(contentType);
    // 在内部消息数组的最后一条消息上添加isSystemGenerated标记
    const messages = this.messageBuilder.build();
    const lastMessage = messages[messages.length - 1];
    lastMessage.isSystemGenerated = isSystemGenerated;
    return this;
  }

  /**
   * 获取构建的消息数组
   * @returns 消息数组
   */
  build(): Array<{ role: string; content: string; isSystemGenerated?: boolean }> {
    return this.messageBuilder.build();
  }

  /**
   * 清空消息数组
   * @returns 当前构建器实例，支持链式调用
   */
  clear(): ExtendedMessageBuilder {
    this.messageBuilder.clear();
    return this;
  }

  /**
   * 创建一个新的扩展消息构建器实例
   * @returns 新的扩展消息构建器实例
   */
  static create(): ExtendedMessageBuilder {
    return new ExtendedMessageBuilder();
  }
}

/**
 * 创建一个新的扩展消息构建器实例
 * @returns 新的扩展消息构建器实例
 */
export function createExtendedMessageBuilder(): ExtendedMessageBuilder {
  return ExtendedMessageBuilder.create();
}

export default createExtendedMessageBuilder;
