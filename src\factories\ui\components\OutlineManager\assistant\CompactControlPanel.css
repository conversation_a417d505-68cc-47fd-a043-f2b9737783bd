/* 紧凑控制面板样式 */
.compact-control-panel {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  padding: 16px;
  background-color: #FAFBFC;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  max-height: 56px;
  align-items: center;
}

/* 控制按钮基础样式 */
.control-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 40px;
  min-width: 120px;
}

.control-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-button:active {
  transform: scale(0.95);
  transition: transform 150ms ease-out;
}

.control-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 按钮内部布局 */
.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: transform 300ms ease;
}

.button-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 素材库按钮样式 */
.material-library-button.enabled {
  background-color: #10B981;
  border-color: #059669;
  color: white;
}

.material-library-button.enabled:hover {
  background-color: #059669;
  border-color: #047857;
}

.material-library-button.disabled {
  background-color: #EF4444;
  border-color: #DC2626;
  color: white;
}

.material-library-button.disabled:hover {
  background-color: #DC2626;
  border-color: #B91C1C;
}

/* 素材库状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.8;
}

.material-library-button.enabled .status-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 素材库按钮图标旋转动画 */
.material-library-button:active .button-icon {
  transform: rotate(360deg);
}

/* 框架选择器包装器样式 */
.framework-selector-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.framework-selector-wrapper .framework-selector {
  width: 100%;
}

.framework-selector-wrapper .framework-selector-button {
  width: 100%;
  min-height: 40px;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #D1D5DB;
  background-color: #F3F4F6;
  color: #374151;
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.framework-selector-wrapper .framework-selector-button:hover {
  background-color: #E5E7EB;
  border-color: #9CA3AF;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.framework-selector-wrapper .framework-selector-button:active {
  transform: scale(0.95);
  transition: transform 150ms ease-out;
}

/* 框架选择按钮样式 */
.framework-button {
  background-color: #F3F4F6;
  border-color: #D1D5DB;
  color: #374151;
}

.framework-button:hover {
  background-color: #E5E7EB;
  border-color: #9CA3AF;
}

.framework-button.has-selection {
  background-color: #EBF8FF;
  border-color: #3B82F6;
  color: #1E40AF;
}

.framework-button.has-selection:hover {
  background-color: #DBEAFE;
  border-color: #2563EB;
}

/* 选择徽章样式 */
.selection-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  background-color: #3B82F6;
  color: white;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
  animation: badgeAppear 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes badgeAppear {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 关联管理按钮样式 */
.association-button.empty {
  background-color: #F9FAFB;
  border-color: #D1D5DB;
  border-style: dashed;
  color: #6B7280;
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    border-color: #D1D5DB;
  }
  50% {
    border-color: #9CA3AF;
  }
}

.association-button.empty:hover {
  background-color: #F3F4F6;
  border-color: #9CA3AF;
  border-style: solid;
}

.association-button.has-content {
  background-color: #FEF3C7;
  border-color: #F59E0B;
  color: #92400E;
}

.association-button.has-content:hover {
  background-color: #FDE68A;
  border-color: #D97706;
}

/* 内容徽章样式 */
.content-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  background-color: #F59E0B;
  color: white;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
  animation: badgeAppear 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 框架选择器弹窗样式 */
.framework-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: overlayFadeIn 200ms ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.framework-selector-container {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: containerSlideIn 300ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-selector-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6B7280;
  transition: all 200ms ease;
  z-index: 10;
}

.close-selector-button:hover {
  background-color: #F3F4F6;
  color: #374151;
}

.close-selector-button:active {
  transform: scale(0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compact-control-panel {
    grid-template-columns: 1fr;
    gap: 8px;
    max-height: none;
    padding: 12px;
  }
  
  .control-button {
    min-height: 48px;
    padding: 12px 16px;
  }
  
  .framework-selector-container {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }
}

@media (max-width: 480px) {
  .compact-control-panel {
    padding: 8px;
    gap: 6px;
  }
  
  .control-button {
    min-width: auto;
    font-size: 13px;
  }
  
  .button-text {
    font-size: 13px;
  }
}

/* 章节分析按钮样式 */
.chapter-analysis-wrapper {
  margin: 8px 0;
  width: 100%;
}

.chapter-analysis-wrapper > div {
  width: 100%;
}

.example-management-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
}

.example-management-button:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
  transform: translateY(-1px);
}

.example-management-button:active {
  transform: scale(0.95);
}

/* 章节分析按钮内部样式 */
.chapter-analysis-wrapper button {
  width: 100%;
  min-height: 40px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
}
