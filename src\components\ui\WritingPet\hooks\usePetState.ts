"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { PetState } from '../index';

interface WritingState {
  activity: 'idle' | 'writing' | 'fast-writing' | 'paused';
  frequency: number;
  timeSinceLastInput: number;
}

interface PetStateHook {
  currentState: PetState;
  isAnimating: boolean;
  triggerCelebration: () => void;
  triggerSpecialAction: (action: string) => void;
  triggerWakeUp: () => void;
  triggerSleep: () => void;
}

/**
 * 宠物状态管理Hook
 * 根据写作状态自动切换宠物状态，并提供手动触发特殊动作的方法
 */
export const usePetState = (
  writingState: WritingState,
  animationLevel: 'low' | 'medium' | 'high'
): PetStateHook => {
  const [currentState, setCurrentState] = useState<PetState>('sleeping');
  const [isAnimating, setIsAnimating] = useState(false);
  const [lastStateChange, setLastStateChange] = useState(Date.now());
  const sleepTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 唤醒小猫
  const triggerWakeUp = useCallback(() => {
    if (currentState === 'sleeping') {
      // 清除睡眠定时器
      if (sleepTimerRef.current) {
        clearTimeout(sleepTimerRef.current);
        sleepTimerRef.current = null;
      }

      setCurrentState('waking');
      setIsAnimating(true);
      setLastStateChange(Date.now());

      // 2秒后完成唤醒
      setTimeout(() => {
        setCurrentState('idle');
        setIsAnimating(false);
      }, 2000);
    }
  }, [currentState]);

  // 让小猫入睡
  const triggerSleep = useCallback(() => {
    if (currentState !== 'sleeping' && currentState !== 'waking') {
      // 清除之前的睡眠定时器
      if (sleepTimerRef.current) {
        clearTimeout(sleepTimerRef.current);
      }

      // 10秒后开始入睡
      const timer = setTimeout(() => {
        setCurrentState('sleeping');
        setLastStateChange(Date.now());
        sleepTimerRef.current = null;
      }, 10000);

      sleepTimerRef.current = timer;
    }
  }, [currentState]);

  // 根据写作状态自动切换宠物状态
  useEffect(() => {
    // 如果正在睡觉或唤醒中，不进行自动状态切换
    if (currentState === 'sleeping' || currentState === 'waking') return;

    const now = Date.now();
    const timeSinceLastChange = now - lastStateChange;

    // 防止状态切换过于频繁
    if (timeSinceLastChange < 1000) return;

    let newState: PetState = 'idle';

    switch (writingState.activity) {
      case 'idle':
        // 长时间无输入，宠物变困倦
        if (writingState.timeSinceLastInput > 30000) { // 30秒
          newState = 'sleepy';
        } else {
          newState = 'idle';
        }
        break;

      case 'writing':
        newState = 'writing';
        break;

      case 'fast-writing':
        newState = 'excited';
        break;

      case 'paused':
        // 短暂停顿，保持写作状态
        if (writingState.timeSinceLastInput < 5000) { // 5秒内
          newState = 'writing';
        } else if (writingState.timeSinceLastInput < 15000) { // 15秒内
          newState = 'idle';
        } else {
          newState = 'sleepy';
        }
        break;
    }

    if (newState !== currentState) {
      setCurrentState(newState);
      setLastStateChange(now);

      // 状态切换时触发动画
      if (animationLevel !== 'low') {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 1000);
      }
    }
  }, [writingState, currentState, lastStateChange, animationLevel]);

  // 单独处理睡眠逻辑，避免循环依赖
  useEffect(() => {
    // 清除睡眠定时器当正在写作时
    if (writingState.activity === 'writing' || writingState.activity === 'fast-writing') {
      if (sleepTimerRef.current) {
        clearTimeout(sleepTimerRef.current);
        sleepTimerRef.current = null;
      }
      return;
    }

    // 当状态变为sleepy且没有睡眠定时器时，设置睡眠定时器
    if (currentState === 'sleepy' && !sleepTimerRef.current) {
      const timer = setTimeout(() => {
        setCurrentState('sleeping');
        setLastStateChange(Date.now());
        sleepTimerRef.current = null;
      }, 10000);

      sleepTimerRef.current = timer;
    }
  }, [currentState, writingState.activity]);

  // 触发庆祝动画
  const triggerCelebration = useCallback(() => {
    setCurrentState('celebrating');
    setIsAnimating(true);
    setLastStateChange(Date.now());

    // 庆祝动画持续3秒后回到之前的状态
    setTimeout(() => {
      setIsAnimating(false);
      // 根据当前写作状态决定回到什么状态
      if (writingState.activity === 'writing' || writingState.activity === 'fast-writing') {
        setCurrentState('writing');
      } else {
        setCurrentState('idle');
      }
    }, 3000);
  }, [writingState.activity]);

  // 触发特殊动作
  const triggerSpecialAction = useCallback((action: string) => {
    switch (action) {
      case 'click':
        // 点击时短暂变为兴奋状态
        const previousState = currentState;
        setCurrentState('excited');
        setIsAnimating(true);
        setLastStateChange(Date.now());

        setTimeout(() => {
          setCurrentState(previousState);
          setIsAnimating(false);
        }, 1500);
        break;

      case 'milestone':
        // 达成里程碑时庆祝
        triggerCelebration();
        break;

      case 'encouragement':
        // 鼓励动作
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 800);
        break;

      default:
        break;
    }
  }, [currentState, triggerCelebration]);

  return {
    currentState,
    isAnimating,
    triggerCelebration,
    triggerSpecialAction,
    triggerWakeUp,
    triggerSleep
  };
};
