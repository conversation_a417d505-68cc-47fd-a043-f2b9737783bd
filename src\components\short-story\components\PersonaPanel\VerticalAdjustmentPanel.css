/* AI历史消息垂直调整面板样式 */
.vertical-adjustment-panel {
  padding: 20px;
  background: #fefefe;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  max-height: 80vh;
  overflow-y: auto;
}

.panel-header {
  margin-bottom: 24px;
  text-align: center;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.panel-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 分析区域 */
.analysis-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.analyze-button {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.analyze-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.analyze-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.no-data-hint {
  margin-top: 12px;
  padding: 12px;
  background: #fef3cd;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  color: #92400e;
  font-size: 13px;
  text-align: center;
}

/* 分析结果 */
.analysis-results {
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.analysis-results h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.result-overview {
  margin-bottom: 16px;
}

.score-card {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.score-value {
  font-weight: 700;
  font-size: 16px;
}

.result-details {
  display: grid;
  gap: 16px;
}

.result-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.result-item strong {
  display: block;
  margin-bottom: 8px;
  color: #1f2937;
  font-size: 14px;
}

.result-item ul {
  margin: 0;
  padding-left: 16px;
  color: #4b5563;
  font-size: 13px;
}

.result-item li {
  margin-bottom: 4px;
}

.quality-metrics {
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #0ea5e9;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0f2fe;
  font-size: 13px;
}

.metric-item span:first-child {
  color: #475569;
}

.metric-item span:last-child {
  font-weight: 600;
  color: #0f172a;
}

/* AI生成进度 */
.ai-generating {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 12px;
  text-align: center;
}

.generating-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #bae6fd;
  border-top: 2px solid #0ea5e9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 效果统计 */
.effectiveness-stats {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #22c55e;
  border-radius: 12px;
}

.effectiveness-stats h4 {
  margin: 0 0 16px 0;
  color: #15803d;
  font-size: 16px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #15803d;
}

.stat-value.positive {
  color: #059669;
}

.stat-value.negative {
  color: #dc2626;
}

.stat-value.neutral {
  color: #6b7280;
}

.top-adjustments {
  border-top: 1px solid rgba(34, 197, 94, 0.2);
  padding-top: 16px;
}

.top-adjustments h5 {
  margin: 0 0 12px 0;
  color: #15803d;
  font-size: 14px;
  font-weight: 600;
}

.top-adjustment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(34, 197, 94, 0.1);
}

.adjustment-name {
  font-size: 13px;
  color: #374151;
  flex: 1;
}

.effectiveness-score {
  font-size: 13px;
  font-weight: 600;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

/* 生成的调整建议 */
.generated-adjustments {
  margin-bottom: 24px;
}

.generated-adjustments h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.adjustment-card {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.adjustment-card:hover {
  border-color: #8b5cf6;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
}

.adjustment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.adjustment-icon {
  font-size: 16px;
}

.adjustment-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.adjustment-category {
  padding: 2px 8px;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.adjustment-confidence {
  margin-left: auto;
  padding: 2px 8px;
  background: #dcfce7;
  color: #166534;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.adjustment-description {
  margin: 8px 0;
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

.adjustment-content {
  margin: 12px 0;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.adjustment-content code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1e293b;
  line-height: 1.4;
  word-break: break-all;
}

.adjustment-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.save-button, .create-button {
  padding: 6px 12px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.save-button:hover, .create-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* 已保存的调整模板 */
.saved-adjustments {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.add-custom-button {
  padding: 6px 12px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-custom-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 自定义表单 */
.custom-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fffbeb;
  border: 1px solid #fbbf24;
  border-radius: 8px;
}

.custom-form h5 {
  margin: 0 0 16px 0;
  color: #92400e;
  font-size: 14px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  color: #374151;
  font-size: 13px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.cancel-button {
  padding: 6px 12px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover {
  background: #4b5563;
}

/* 调整列表 */
.adjustments-list {
  display: grid;
  gap: 12px;
}

.saved-adjustment-item {
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.saved-adjustment-item:hover {
  border-color: #8b5cf6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

.adjustment-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.adjustment-status.active {
  padding: 2px 8px;
  background: #dcfce7;
  color: #166534;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.adjustment-status.inactive {
  padding: 2px 8px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.adjustment-meta {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  flex-wrap: wrap;
}

.activate-button {
  padding: 6px 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.activate-button.active {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.activate-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.activate-button.active:hover {
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.delete-button {
  padding: 6px 12px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.empty-state {
  padding: 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vertical-adjustment-panel {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .adjustment-header,
  .adjustment-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .adjustment-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
