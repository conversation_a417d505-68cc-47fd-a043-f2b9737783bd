/* AI助手抽屉样式 - 新的侧边栏模式 */
.assistant-drawer-overlay {
  /* 基础样式由OutlineCanvasLayout.css控制 */
  display: flex;
  justify-content: flex-end;
  align-items: stretch;
}

/* 抽屉主体 */
.assistant-drawer {
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;

  /* 添加缩放动画支持 */
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s ease;
}

/* 抽屉头部 */
.assistant-drawer-header {
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  display: flex;
  align-items: center;
  justify-content: space-between;
}

.assistant-drawer-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.assistant-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.assistant-drawer-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.assistant-drawer-title p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.assistant-drawer-close {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;

  display: flex;
  align-items: center;
  justify-content: center;

  transition: all 0.2s ease;
}

.assistant-drawer-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 抽屉内容 */
.assistant-drawer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 关联面板容器样式 */
.association-panel-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
  animation: panelSlideIn 300ms ease-out;
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.association-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: #F8FAFC;
  border-bottom: 1px solid #E2E8F0;
}

.association-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1E293B;
}

.close-association-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #64748B;
  transition: all 200ms ease;
}

.close-association-button:hover {
  background-color: #E2E8F0;
  color: #475569;
}

.close-association-button:active {
  transform: scale(0.95);
}

/* 聊天界面容器 */
.chat-interface-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assistant-drawer {
    width: 100vw;
    max-width: 100vw;
  }

  .assistant-drawer-header {
    padding: 16px;
  }

  .assistant-drawer-title h3 {
    font-size: 18px;
  }

  .assistant-avatar {
    width: 40px;
    height: 40px;
  }

  .node-preview-container .preview-confirm-panel {
    padding: 16px;
    border-radius: 0;
  }

  .confirm-panel-actions {
    gap: 8px;
  }

  .cancel-btn, .confirm-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 预览提示条样式 */
.preview-hint-bar {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideInUp 0.3s ease-out;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  margin-top: auto;
  flex-shrink: 0;
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-icon {
  font-size: 16px;
}

.hint-text {
  font-size: 14px;
  font-weight: 500;
}

.show-preview-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.show-preview-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .assistant-drawer {
    background: #1e1e1e;
    color: #ffffff;
  }

  .assistant-drawer-header {
    border-bottom-color: #333;
  }

  .node-preview-container {
    background: rgba(30, 30, 30, 0.98);
  }

  .node-preview-container .preview-confirm-panel {
    background: #2a2a2a;
    border-top-color: #333;
  }

  .confirm-panel-header h4 {
    color: #fff;
  }

  .confirm-panel-header p {
    color: #ccc;
  }

  .cancel-btn {
    background: #333;
    color: #ccc;
    border-color: #444;
  }

  .cancel-btn:hover {
    background: #444;
    color: #fff;
  }

  .clear-btn {
    background: #dc3545;
    color: white;
  }

  .clear-btn:hover {
    background: #c82333;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .assistant-drawer {
    border-left: 2px solid #000;
  }

  .assistant-drawer-header {
    border-bottom: 2px solid #000;
  }
}

/* 节点预览容器 - 集成到抽屉内部 */
.node-preview-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(8px);
  z-index: 10;
  display: flex;
  flex-direction: column;
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 确认操作面板 - 重新定位到底部 */
.node-preview-container .preview-confirm-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 20px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.4s ease-out 0.1s both;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 确认面板样式 */
.confirm-panel-header {
  margin-bottom: 15px;
}

.confirm-panel-header h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirm-panel-header p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.confirm-panel-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.action-group {
  display: flex;
  gap: 8px;
}

.cancel-btn, .clear-btn, .confirm-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.cancel-btn:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
}

.clear-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  flex-shrink: 0;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.confirm-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.confirm-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .assistant-drawer-overlay {
    animation: none;
  }

  .assistant-drawer {
    transition: none;
  }

  .assistant-drawer-close {
    transition: none;
  }

  .node-preview-container {
    animation: none;
  }

  .node-preview-container .preview-confirm-panel {
    animation: none;
  }
}

/* 框架选择区域样式 */
.framework-selection-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafbfc;
}

/* 素材库控制区域样式 */
.material-library-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  background: #f8fffe;
}

.assistant-framework-selector {
  margin-bottom: 0;
}

.assistant-framework-selector .framework-selector-button {
  background: white;
  border: 1px solid #e2e8f0;
  font-size: 13px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.assistant-framework-selector .framework-selector-button:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.assistant-framework-selector .framework-selector-text {
  font-weight: 500;
  color: #475569;
}

.assistant-framework-selector .framework-selector-dropdown {
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 280px;
}
