"use client";

import { createAIFactory } from '@/factories/ai/AIFactory';
import { createChapterSegmenter } from '@/utils/ai/ChapterSegmenter';
import { IWorldBuildingCreator, IWorldBuildingExtractor, IWorldBuildingFieldGenerator, IWorldBuildingUpdater } from '../interfaces/WorldBuildingInterfaces';
import { WorldBuildingCreator } from './WorldBuildingCreator';
import { WorldBuildingExtractor } from './WorldBuildingExtractor';
import { WorldBuildingFieldGenerator } from './WorldBuildingFieldGenerator';
import { WorldBuildingFieldStandardizer } from './WorldBuildingFieldStandardizer';
import { WorldBuildingInfoFormatter } from './WorldBuildingInfoFormatter';
import { WorldBuildingUpdater } from './WorldBuildingUpdater';
import { WorldBuildingUpdateSuggestionGenerator } from './WorldBuildingUpdateSuggestionGenerator';

/**
 * 世界观工厂
 * 负责创建和管理世界观相关的组件
 */
export class WorldBuildingFactory {
  private static instance: WorldBuildingFactory;
  private aiSender: any;
  private segmenter: any;

  private extractor: IWorldBuildingExtractor | null = null;
  private updater: IWorldBuildingUpdater | null = null;
  private creator: IWorldBuildingCreator | null = null;
  private fieldGenerator: IWorldBuildingFieldGenerator | null = null;
  private fieldStandardizer: WorldBuildingFieldStandardizer | null = null;
  private infoFormatter: WorldBuildingInfoFormatter | null = null;
  private suggestionGenerator: WorldBuildingUpdateSuggestionGenerator | null = null;

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    const aiFactory = createAIFactory();
    this.aiSender = aiFactory.createAISenderComponent();
    this.segmenter = createChapterSegmenter();
  }

  /**
   * 获取世界观工厂实例
   * @returns 世界观工厂实例
   */
  public static getInstance(): WorldBuildingFactory {
    if (!WorldBuildingFactory.instance) {
      WorldBuildingFactory.instance = new WorldBuildingFactory();
    }
    return WorldBuildingFactory.instance;
  }

  /**
   * 创建世界观提取器
   * @returns 世界观提取器
   */
  public createWorldBuildingExtractor(): IWorldBuildingExtractor {
    if (!this.extractor) {
      this.extractor = new WorldBuildingExtractor(this.aiSender, this.segmenter);
    }
    return this.extractor;
  }

  /**
   * 创建世界观更新器
   * @returns 世界观更新器
   */
  public createWorldBuildingUpdater(): IWorldBuildingUpdater {
    if (!this.updater) {
      this.updater = new WorldBuildingUpdater(this.aiSender, this.segmenter);
    }
    return this.updater;
  }

  /**
   * 创建世界观字段生成器
   * @returns 世界观字段生成器
   */
  public createWorldBuildingFieldGenerator(): IWorldBuildingFieldGenerator {
    if (!this.fieldGenerator) {
      this.fieldGenerator = new WorldBuildingFieldGenerator(this.aiSender);
    }
    return this.fieldGenerator;
  }

  /**
   * 创建世界观创建器
   * @returns 世界观创建器
   */
  public createWorldBuildingCreator(): IWorldBuildingCreator {
    if (!this.creator) {
      this.creator = new WorldBuildingCreator(this.aiSender, this.segmenter);
    }
    return this.creator;
  }

  /**
   * 创建世界观字段标准化器
   * @returns 世界观字段标准化器
   */
  public createWorldBuildingFieldStandardizer(): WorldBuildingFieldStandardizer {
    if (!this.fieldStandardizer) {
      this.fieldStandardizer = new WorldBuildingFieldStandardizer();
    }
    return this.fieldStandardizer;
  }

  /**
   * 创建世界观信息格式化器
   * @returns 世界观信息格式化器
   */
  public createWorldBuildingInfoFormatter(): WorldBuildingInfoFormatter {
    if (!this.infoFormatter) {
      this.infoFormatter = new WorldBuildingInfoFormatter();
    }
    return this.infoFormatter;
  }

  /**
   * 创建世界观更新建议生成器
   * @returns 世界观更新建议生成器
   */
  public createWorldBuildingUpdateSuggestionGenerator(): WorldBuildingUpdateSuggestionGenerator {
    if (!this.suggestionGenerator) {
      this.suggestionGenerator = new WorldBuildingUpdateSuggestionGenerator();
    }
    return this.suggestionGenerator;
  }
}
