import { IButtonComponent, ButtonType, ButtonSize } from './IButtonComponent';
import { ICircleButtonComponent, CircleButtonSize } from './ICircleButtonComponent';
import { IConfirmDialogComponent, ConfirmDialogType } from './IConfirmDialogComponent';
import { IPanelComponent, PanelSize } from './IPanelComponent';

/**
 * UI工厂接口
 */
export interface IUIFactory {
  /**
   * 创建按钮组件
   * @param text 按钮文本
   * @param type 按钮类型
   * @param size 按钮尺寸
   */
  createButtonComponent(
    text?: string,
    type?: ButtonType,
    size?: ButtonSize
  ): IButtonComponent;

  /**
   * 创建圆形按钮组件
   * @param icon 按钮图标
   * @param text 按钮文本
   * @param color 按钮颜色
   * @param size 按钮尺寸
   */
  createCircleButtonComponent(
    icon: React.ReactNode,
    text?: string,
    color?: string,
    size?: CircleButtonSize
  ): ICircleButtonComponent;

  /**
   * 创建确认对话框组件
   * @param title 对话框标题
   * @param message 对话框消息
   * @param isOpen 是否打开
   * @param confirmText 确认按钮文本
   * @param cancelText 取消按钮文本
   * @param confirmType 确认按钮类型
   */
  createConfirmDialogComponent(
    title: string,
    message: string,
    isOpen?: boolean,
    confirmText?: string,
    cancelText?: string,
    confirmType?: ConfirmDialogType
  ): IConfirmDialogComponent;

  /**
   * 创建面板组件
   * @param title 面板标题
   * @param isOpen 是否打开
   * @param size 面板尺寸
   * @param options 其他选项
   */
  createPanelComponent(
    title: string,
    isOpen?: boolean,
    size?: PanelSize,
    options?: {
      fixedHeight?: boolean;
      backgroundColor?: string;
      width?: string;
      height?: string;
    }
  ): IPanelComponent;
}
