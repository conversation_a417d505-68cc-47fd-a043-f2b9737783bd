import React from 'react';
import { AIWritingDialog } from './components/AIWritingPanel';

/**
 * AI写作面板工厂类
 * 用于创建AI写作相关的组件
 */
export class AIWritingPanelFactory {
  /**
   * 创建AI写作对话框组件
   * @param props 组件属性
   * @returns AI写作对话框组件
   */
  createAIWritingDialog(props: {
    isOpen: boolean;
    onClose: () => void;
    onInsertContent: (content: string) => void;
    bookId: string;
  }) {
    return React.createElement(AIWritingDialog, props);
  }
}

/**
 * 创建AI写作面板工厂实例
 * @returns AI写作面板工厂实例
 */
export function createAIWritingPanelFactory() {
  return new AIWritingPanelFactory();
}

export default createAIWritingPanelFactory;
