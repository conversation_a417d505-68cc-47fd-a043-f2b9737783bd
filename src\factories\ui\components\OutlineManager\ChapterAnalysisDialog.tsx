"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Outline } from '../../types/outline';
import { Chapter } from '@/lib/db/dexie';
import { chapterRepository } from '@/lib/db/repositories';
import ChapterSelector from './ChapterSelector';
import { ChapterAnalysisService, ChapterAnalysisResult, AnalysisConfig, SegmentConfig } from './assistant/services/ChapterAnalysisService';
import { ExampleInjectionManager } from './assistant/services/ExampleInjectionManager';
import './ChapterAnalysisDialog.css';

// 字段显示配置接口
interface FieldDisplayConfig {
  key: string;
  label: string;
  type: 'text' | 'array' | 'number';
  group: 'basic' | 'chapter' | 'plot' | 'creative';
  priority: number;
  description?: string;
}

// 字段分组配置
const FIELD_GROUPS = {
  basic: { title: '基础信息', icon: '📊', color: 'blue' },
  chapter: { title: '章节分析', icon: '📖', color: 'green' },
  plot: { title: '剧情分析', icon: '🎭', color: 'purple' },
  creative: { title: '创作指导', icon: '✨', color: 'orange' }
};

// 字段配置映射表
const FIELD_CONFIGS: FieldDisplayConfig[] = [
  // 基础信息组
  { key: 'overallStyle', label: '整体风格', type: 'text', group: 'basic', priority: 1 },

  { key: 'mainCharacters', label: '主要角色', type: 'array', group: 'basic', priority: 3 },
  { key: 'conflictLevel', label: '冲突强度', type: 'number', group: 'basic', priority: 4 },

  // 章节分析组
  { key: 'chapterTitle', label: '章节标题', type: 'text', group: 'chapter', priority: 1 },
  { key: 'chapterDescription', label: '章节描述', type: 'text', group: 'chapter', priority: 2 },
  { key: 'chapterStyle', label: '写作风格', type: 'text', group: 'chapter', priority: 3 },
  { key: 'chapterTechniques', label: '写作技巧', type: 'array', group: 'chapter', priority: 4 },
  { key: 'chapterGoals', label: '章节目标', type: 'array', group: 'chapter', priority: 5 },
  { key: 'rhythmPhase', label: '节奏阶段', type: 'text', group: 'chapter', priority: 6 },
  { key: 'rhythmGuidance', label: '节奏指导', type: 'text', group: 'chapter', priority: 7 },

  // 剧情分析组
  { key: 'plotTitle', label: '剧情标题', type: 'text', group: 'plot', priority: 1 },
  { key: 'plotDescription', label: '剧情描述', type: 'text', group: 'plot', priority: 2 },
  { key: 'plotType', label: '剧情类型', type: 'text', group: 'plot', priority: 3 },
  { key: 'suspenseElements', label: '悬念元素', type: 'array', group: 'plot', priority: 4 },
  { key: 'relatedCharacters', label: '相关角色', type: 'array', group: 'plot', priority: 5 },

  // 创作指导组
  { key: 'themes', label: '主要主题', type: 'array', group: 'creative', priority: 1 },
  { key: 'writingTechniques', label: '写作技巧', type: 'array', group: 'creative', priority: 2 },
  { key: 'creativeNotes', label: '创作笔记', type: 'text', group: 'creative', priority: 3 }
];

interface ChapterAnalysisDialogProps {
  isOpen: boolean;
  onClose: () => void;
  outline: Outline | null;
  bookId: string;
  buttonPosition?: { x: number; y: number };
}

type InputMode = 'text' | 'chapters';

// 字段项组件
const FieldItem: React.FC<{
  config: FieldDisplayConfig;
  value: any;
}> = ({ config, value }) => {
  if (!value || (Array.isArray(value) && value.length === 0)) {
    return null;
  }

  const renderValue = () => {
    switch (config.type) {
      case 'array':
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item: string, index: number) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
              >
                {item}
              </span>
            ))}
          </div>
        );
      case 'number':
        return (
          <div className="flex items-center gap-2">
            <span className="text-lg font-semibold">{value}</span>
            {config.key === 'conflictLevel' && (
              <span className="text-xs text-gray-500">
                ({value <= 2 ? '轻微' : value <= 3 ? '中等' : value <= 4 ? '激烈' : '极强'})
              </span>
            )}
          </div>
        );
      default:
        return <p className="text-sm text-gray-700">{value}</p>;
    }
  };

  return (
    <div className="border-b border-gray-100 pb-3 last:border-b-0">
      <div className="mb-1">
        <h5 className="font-medium text-gray-800 text-sm">{config.label}</h5>
      </div>
      {renderValue()}
    </div>
  );
};

// 字段分组组件
const FieldGroup: React.FC<{
  groupKey: keyof typeof FIELD_GROUPS;
  fields: FieldDisplayConfig[];
  data: ChapterAnalysisResult;
}> = ({ groupKey, fields, data }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const group = FIELD_GROUPS[groupKey];

  // 过滤出有值的字段
  const validFields = fields.filter(field => {
    const value = (data as any)[field.key];
    return value && (Array.isArray(value) ? value.length > 0 : true);
  });

  if (validFields.length === 0) {
    return null;
  }

  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800'
  };

  return (
    <div className={`border rounded-lg overflow-hidden ${colorClasses[group.color]}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between hover:bg-opacity-80 transition-colors"
      >
        <div className="flex items-center gap-2">
          <span className="text-lg">{group.icon}</span>
          <h4 className="font-medium">{group.title}</h4>
          <span className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
            {validFields.length} 项
          </span>
        </div>
        <svg
          className={`w-5 h-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="bg-white bg-opacity-50"
          >
            <div className="p-4 space-y-3">
              {validFields
                .sort((a, b) => a.priority - b.priority)
                .map(field => (
                  <FieldItem
                    key={field.key}
                    config={field}
                    value={(data as any)[field.key]}
                  />
                ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const ChapterAnalysisDialog: React.FC<ChapterAnalysisDialogProps> = ({
  isOpen,
  onClose,
  outline,
  bookId,
  buttonPosition
}) => {
  // 基础状态
  const [inputMode, setInputMode] = useState<InputMode>('text');

  // 输入相关状态
  const [customText, setCustomText] = useState('');
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);

  // 分析相关状态
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisLog, setAnalysisLog] = useState('');
  const [analysisResult, setAnalysisResult] = useState<ChapterAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 结果相关状态
  const [exampleName, setExampleName] = useState('');

  // 加载章节数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
    }
  }, [isOpen, bookId]);

  const loadChapters = async () => {
    try {
      const chapterData = await chapterRepository.getAllByBookId(bookId);
      setChapters(chapterData);
    } catch (error) {
      console.error('加载章节失败:', error);
      setError('加载章节数据失败');
    }
  };

  // 重置状态
  const resetState = useCallback(() => {
    setInputMode('text');
    setCustomText('');
    setSelectedChapterIds([]);
    setIsAnalyzing(false);
    setAnalysisLog('');
    setAnalysisResult(null);
    setError(null);
    setExampleName('');
  }, []);

  // 处理复制操作
  const handleCopy = useCallback((value: string) => {
    setCopySuccess('已复制到剪贴板');
    setTimeout(() => setCopySuccess(null), 2000);
  }, []);

  // 关闭对话框
  const handleClose = useCallback(() => {
    resetState();
    onClose();
  }, [resetState, onClose]);

  // 获取输入内容
  const getInputContent = useCallback(() => {
    if (inputMode === 'text') {
      return customText;
    } else {
      const selectedChapters = selectedChapterIds
        .map(id => chapters.find(chapter => chapter.id === id))
        .filter(chapter => chapter !== undefined) as Chapter[];
      
      return selectedChapters
        .map(chapter => `【${chapter.title}】\n${chapter.content || ''}`)
        .join('\n\n');
    }
  }, [inputMode, customText, selectedChapterIds, chapters]);

  // 字数统计 - 使用useMemo避免重复计算
  const wordCount = useMemo(() => {
    const content = getInputContent();
    return content.length;
  }, [getInputContent]);

  // 验证输入 - 使用useMemo避免重复计算
  const isInputValid = useMemo(() => {
    const content = getInputContent();
    return content.trim().length >= 100 && content.length <= 50000;
  }, [getInputContent]);

  // 开始分析
  const handleStartAnalysis = useCallback(async () => {
    const content = getInputContent();
    if (!content.trim()) {
      setError('请输入要分析的内容');
      return;
    }
    if (content.length < 100) {
      setError('内容太短，至少需要100个字符');
      return;
    }
    if (content.length > 50000) {
      setError('内容太长，最多支持50000个字符');
      return;
    }

    setIsAnalyzing(true);
    setError(null);
    setAnalysisLog('开始分析...\n');

    try {
      const analysisService = new ChapterAnalysisService();

      // 使用简单的配置：智能分段，每25个句子为一段，启用分批处理
      const simpleConfig: AnalysisConfig = {
        segmentConfig: {
          mode: 'smart',
          maxLength: 2500, // 约25个句子的长度
          minLength: 500,
          overlapRatio: 0.1,
          preserveContext: true
        },
        analysisDepth: 'standard',
        outputFormat: 'standard',
        includeACE: true,
        selectedFrameworks: [],
        // 启用分批处理，避免消息过长中断
        batchProcessing: {
          enabled: true,
          maxPlotPointsPerBatch: 40,
          enableContinuation: true
        }
      };

      const result = await analysisService.analyzeChapterContent(
        content,
        [], // 暂时不使用ACE框架
        (chunk: string) => {
          setAnalysisLog(prev => prev + chunk);
        },
        simpleConfig
      );

      if (result.success) {
        setAnalysisResult(result);
        setExampleName(`分析结果_${new Date().toLocaleString()}`);
        setAnalysisLog(prev => prev + '\n✅ 分析完成！');
      } else {
        throw new Error(result.error || '分析失败');
      }
    } catch (error) {
      console.error('分析失败:', error);
      setError(error instanceof Error ? error.message : '分析失败');
      setAnalysisLog(prev => prev + `\n❌ 分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsAnalyzing(false);
    }
  }, [getInputContent]);

  // 保存示例
  const handleSaveExample = useCallback(() => {
    if (!analysisResult || !exampleName.trim()) {
      setError('请输入示例名称');
      return;
    }

    try {
      // 保存完整的分析结果，包含所有新字段
      const savedExample = ExampleInjectionManager.saveExample(
        exampleName,
        analysisResult.plotPoints,
        analysisResult.overallStyle,
        analysisResult.mainCharacters,
        analysisResult.conflictLevel,
        // 传递新增的字段
        {
          chapterTitle: analysisResult.chapterTitle,
          chapterDescription: analysisResult.chapterDescription,
          chapterStyle: analysisResult.chapterStyle,
          chapterTechniques: analysisResult.chapterTechniques,
          chapterGoals: analysisResult.chapterGoals,
          rhythmPhase: analysisResult.rhythmPhase,
          rhythmGuidance: analysisResult.rhythmGuidance,
          plotTitle: analysisResult.plotTitle,
          plotDescription: analysisResult.plotDescription,
          plotType: analysisResult.plotType,
          suspenseElements: analysisResult.suspenseElements,
          relatedCharacters: analysisResult.relatedCharacters,
          creativeNotes: analysisResult.creativeNotes,
          themes: analysisResult.themes,
          writingTechniques: analysisResult.writingTechniques
        }
      );

      // 自动激活新保存的示例
      ExampleInjectionManager.activateExample(savedExample.id);

      setAnalysisLog(prev => prev + '\n✅ 示例已保存并激活！');
      
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (error) {
      console.error('保存示例失败:', error);
      setError('保存示例失败');
    }
  }, [analysisResult, exampleName, handleClose]);

  if (!isOpen) return null;

  const dialogContent = (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* 对话框内容 */}
          <motion.div
            initial={{ 
              opacity: 0, 
              scale: 0.9,
              x: buttonPosition ? buttonPosition.x - window.innerWidth / 2 : 0,
              y: buttonPosition ? buttonPosition.y - window.innerHeight / 2 : 0
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.9,
              x: buttonPosition ? buttonPosition.x - window.innerWidth / 2 : 0,
              y: buttonPosition ? buttonPosition.y - window.innerHeight / 2 : 0
            }}
            transition={{ 
              type: "spring", 
              damping: 25, 
              stiffness: 300,
              duration: 0.3
            }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800">章节JSON结构分析</h2>
                  <p className="text-sm text-gray-600">智能分段分析，生成剧情点JSON结构</p>
                </div>
              </div>

              <button
                onClick={handleClose}
                className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容区域 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              {!isAnalyzing && !analysisResult && (
                <div className="space-y-6">
                  {/* 输入方式选择 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">选择输入方式</h3>
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <button
                        className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all ${
                          inputMode === 'text'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                        onClick={() => setInputMode('text')}
                      >
                        自定义文本
                      </button>
                      <button
                        className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all ${
                          inputMode === 'chapters'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                        onClick={() => setInputMode('chapters')}
                      >
                        选择章节
                      </button>
                    </div>
                  </div>

                  {/* 自定义文本输入 */}
                  {inputMode === 'text' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        输入要分析的文本内容
                      </label>
                      <textarea
                        value={customText}
                        onChange={(e) => setCustomText(e.target.value)}
                        placeholder="请输入要分析的文本内容，系统将自动按25个句子分段进行分析..."
                        className="w-full h-64 p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors resize-none"
                      />
                      <div className="flex justify-between items-center mt-2 text-sm">
                        <span className="text-gray-500">
                          智能分段：约25个句子为一段，自动生成JSON结构
                        </span>
                        <span className={`font-medium ${
                          wordCount < 100 ? 'text-red-500' :
                          wordCount > 50000 ? 'text-red-500' :
                          wordCount > 40000 ? 'text-yellow-500' :
                          'text-green-500'
                        }`}>
                          {wordCount} / 50000 字符
                        </span>
                      </div>
                      {wordCount < 100 && customText.length > 0 && (
                        <p className="text-red-500 text-sm mt-1">
                          内容太短，至少需要100个字符
                        </p>
                      )}
                      {wordCount > 50000 && (
                        <p className="text-red-500 text-sm mt-1">
                          内容太长，最多支持50000个字符
                        </p>
                      )}
                    </div>
                  )}

                  {/* 章节选择 */}
                  {inputMode === 'chapters' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="text-lg font-medium text-gray-800">选择要分析的章节</h4>
                        <span className="text-sm text-gray-600">
                          已选择 {selectedChapterIds.length} 个章节
                        </span>
                      </div>
                      <ChapterSelector
                        chapters={chapters}
                        selectedIds={selectedChapterIds}
                        onSelectionChange={setSelectedChapterIds}
                        maxSelection={10}
                        showRangeSelection={true}
                      />
                      {selectedChapterIds.length > 0 && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h5 className="font-medium text-blue-800 mb-2">分析说明</h5>
                          <p className="text-sm text-blue-600">
                            将分析 {selectedChapterIds.length} 个章节（约 {wordCount} 字符），智能分段后提取JSON结构
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* 分析进行中 */}
              {isAnalyzing && (
                <div className="space-y-6">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-800">正在智能分段分析</h3>
                    <p className="text-sm text-gray-600 mt-2">AI正在按25个句子分段并提取JSON结构...</p>
                  </div>

                  {analysisLog && (
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto">
                      <pre className="whitespace-pre-wrap">{analysisLog}</pre>
                    </div>
                  )}
                </div>
              )}

              {/* 分析结果 */}
              {!isAnalyzing && analysisResult && (
                <div className="space-y-6">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-800">分析完成</h3>
                    <p className="text-sm text-gray-600 mt-2">
                      成功分析生成了 {analysisResult.plotPoints.length} 个剧情点的JSON结构
                    </p>
                  </div>

                  {/* 分析结果概览 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">{analysisResult.plotPoints.length}</div>
                      <div className="text-sm text-blue-800">剧情点</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">{analysisResult.mainCharacters.length}</div>
                      <div className="text-sm text-green-800">主要角色</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-yellow-600">{analysisResult.conflictLevel}</div>
                      <div className="text-sm text-yellow-800">冲突等级</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-600">{analysisResult.themes?.length || 0}</div>
                      <div className="text-sm text-purple-800">主题数量</div>
                    </div>
                  </div>

                  {/* 完整字段分组显示 */}
                  <div className="space-y-4">
                    {Object.keys(FIELD_GROUPS).map(groupKey => {
                      const groupFields = FIELD_CONFIGS.filter(field => field.group === groupKey);
                      return (
                        <FieldGroup
                          key={groupKey}
                          groupKey={groupKey as keyof typeof FIELD_GROUPS}
                          fields={groupFields}
                          data={analysisResult}
                        />
                      );
                    })}
                  </div>

                  {/* 剧情点列表 */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-3">分析生成的剧情点结构</h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {analysisResult.plotPoints.map((point, index) => (
                        <div key={index} className="bg-white p-3 rounded border">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-sm">剧情点 {point.order}</h5>
                            <span className={`px-2 py-1 text-xs rounded ${
                              point.type === 'setup' ? 'bg-blue-100 text-blue-800' :
                              point.type === 'conflict' ? 'bg-red-100 text-red-800' :
                              point.type === 'resolution' ? 'bg-green-100 text-green-800' :
                              'bg-purple-100 text-purple-800'
                            }`}>
                              {point.type === 'setup' ? '铺垫' :
                               point.type === 'conflict' ? '冲突' :
                               point.type === 'resolution' ? '解决' :
                               point.type === 'twist' ? '转折' :
                               point.type}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700 mb-2">{point.content}</p>
                          <div className="text-xs text-gray-500 space-y-1">
                            <p><strong>避免：</strong>{point.avoidWriting}</p>
                            <p><strong>推荐：</strong>{point.shouldWriting}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 示例名称输入 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">示例名称</label>
                    <input
                      type="text"
                      value={exampleName}
                      onChange={(e) => setExampleName(e.target.value)}
                      placeholder="为这个分析结果起个名字..."
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}
            </div>

            {/* 底部操作按钮 */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                {!isAnalyzing && !analysisResult && (
                  <span>输入内容后点击"开始分析"进行智能分段提取</span>
                )}
                {isAnalyzing && (
                  <span>正在智能分段分析中...</span>
                )}
                {!isAnalyzing && analysisResult && (
                  <span>分析完成，可以保存为示例</span>
                )}
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  {analysisResult ? '关闭' : '取消'}
                </button>

                {!isAnalyzing && !analysisResult && (
                  <button
                    onClick={handleStartAnalysis}
                    disabled={!isInputValid}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>开始分析</span>
                  </button>
                )}

                {!isAnalyzing && analysisResult && (
                  <button
                    onClick={handleSaveExample}
                    disabled={!exampleName.trim()}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>保存示例</span>
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );

  return createPortal(dialogContent, document.body);
};

export default ChapterAnalysisDialog;
