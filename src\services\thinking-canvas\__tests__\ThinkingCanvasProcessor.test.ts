import { ThinkingCanvasProcessor } from '../ThinkingCanvasProcessor';
import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { OutlineAIResponseExtended } from '@/factories/ui/components/OutlineManager/assistant/OutlineAIService';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('ThinkingCanvasProcessor', () => {
  let processor: ThinkingCanvasProcessor;

  beforeEach(() => {
    processor = ThinkingCanvasProcessor.getInstance();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
  });

  describe('extractThinkingCanvas', () => {
    it('should extract thinking canvas from valid response', () => {
      const mockResponse: OutlineAIResponseExtended = {
        message: 'Test message',
        success: true,
        thinkingCanvas: {
          id: 'test-id',
          title: 'Test Thinking Canvas',
          content: '# Test Content\n\nThis is a test.',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: ['test'],
          metadata: {
            wordCount: 20,
            editHistory: [],
            isStarred: false
          }
        }
      };

      const result = processor.extractThinkingCanvas(mockResponse);

      expect(result).toBeTruthy();
      expect(result?.id).toBe('test-id');
      expect(result?.title).toBe('Test Thinking Canvas');
      expect(result?.content).toBe('# Test Content\n\nThis is a test.');
    });

    it('should return null for response without thinking canvas', () => {
      const mockResponse: OutlineAIResponseExtended = {
        message: 'Test message',
        success: true
      };

      const result = processor.extractThinkingCanvas(mockResponse);

      expect(result).toBeNull();
    });

    it('should return null for invalid thinking canvas data', () => {
      const mockResponse: OutlineAIResponseExtended = {
        message: 'Test message',
        success: true,
        thinkingCanvas: {
          id: 'test-id',
          title: '', // Invalid: empty title
          content: '',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: [],
          metadata: {
            wordCount: 0,
            editHistory: [],
            isStarred: false
          }
        }
      };

      const result = processor.extractThinkingCanvas(mockResponse);

      expect(result).toBeNull();
    });
  });

  describe('validateThinkingCanvas', () => {
    it('should validate correct thinking canvas data', () => {
      const validCanvas: ThinkingCanvasData = {
        id: 'test-id',
        title: 'Test Title',
        content: 'Test content',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        tags: ['test'],
        metadata: {
          wordCount: 12,
          editHistory: [],
          isStarred: false
        }
      };

      const result = processor.validateThinkingCanvas(validCanvas);

      expect(result).toBe(true);
    });

    it('should reject thinking canvas with missing required fields', () => {
      const invalidCanvas = {
        id: 'test-id',
        // Missing title and content
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        tags: ['test'],
        metadata: {
          wordCount: 0,
          editHistory: [],
          isStarred: false
        }
      } as ThinkingCanvasData;

      const result = processor.validateThinkingCanvas(invalidCanvas);

      expect(result).toBe(false);
    });

    it('should reject thinking canvas with wrong data types', () => {
      const invalidCanvas = {
        id: 'test-id',
        title: 123, // Should be string
        content: 'Test content',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        tags: ['test'],
        metadata: {
          wordCount: 12,
          editHistory: [],
          isStarred: false
        }
      } as any;

      const result = processor.validateThinkingCanvas(invalidCanvas);

      expect(result).toBe(false);
    });
  });

  describe('saveThinkingCanvas', () => {
    it('should save valid thinking canvas to localStorage', async () => {
      const validCanvas: ThinkingCanvasData = {
        id: 'test-id',
        title: 'Test Title',
        content: 'Test content',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        tags: ['test'],
        metadata: {
          wordCount: 12,
          editHistory: [],
          isStarred: false
        }
      };

      localStorageMock.getItem.mockReturnValue('[]');

      await processor.saveThinkingCanvas(validCanvas);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'thinking_canvas_data',
        expect.stringContaining('test-id')
      );
    });

    it('should throw error for invalid thinking canvas', async () => {
      const invalidCanvas = {
        id: 'test-id',
        // Missing required fields
      } as ThinkingCanvasData;

      await expect(processor.saveThinkingCanvas(invalidCanvas)).rejects.toThrow();
    });
  });

  describe('getAllThinkingCanvases', () => {
    it('should return empty array when no data exists', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = processor.getAllThinkingCanvases();

      expect(result).toEqual([]);
    });

    it('should return parsed thinking canvases from localStorage', () => {
      const mockData = [
        {
          id: 'test-1',
          title: 'Test 1',
          content: 'Content 1',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: ['test'],
          metadata: {
            wordCount: 9,
            editHistory: [],
            isStarred: false
          }
        }
      ];

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));

      const result = processor.getAllThinkingCanvases();

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-1');
    });

    it('should filter out invalid data', () => {
      const mockData = [
        {
          id: 'test-1',
          title: 'Test 1',
          content: 'Content 1',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: ['test'],
          metadata: {
            wordCount: 9,
            editHistory: [],
            isStarred: false
          }
        },
        {
          id: 'test-2',
          // Missing required fields
        }
      ];

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));

      const result = processor.getAllThinkingCanvases();

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-1');
    });
  });

  describe('searchThinkingCanvases', () => {
    beforeEach(() => {
      const mockData = [
        {
          id: 'test-1',
          title: 'JavaScript Tutorial',
          content: 'Learn JavaScript basics',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: ['programming', 'tutorial'],
          metadata: {
            wordCount: 20,
            editHistory: [],
            isStarred: false
          }
        },
        {
          id: 'test-2',
          title: 'React Components',
          content: 'Building reusable components',
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
          tags: ['react', 'components'],
          metadata: {
            wordCount: 25,
            editHistory: [],
            isStarred: false
          }
        }
      ];

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));
    });

    it('should search by title', () => {
      const result = processor.searchThinkingCanvases('JavaScript');

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-1');
    });

    it('should search by content', () => {
      const result = processor.searchThinkingCanvases('components');

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-2');
    });

    it('should search by tags', () => {
      const result = processor.searchThinkingCanvases('programming');

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-1');
    });

    it('should return empty array for no matches', () => {
      const result = processor.searchThinkingCanvases('nonexistent');

      expect(result).toHaveLength(0);
    });
  });
});
