"use client";

import React, { useState, useEffect } from 'react';
import { dialogContinuationManager } from '@/utils/ai/DialogContinuationManager';
import { ConversationMessage as BaseConversationMessage } from '@/factories/ai/services/AIWritingService';

// Extend the ConversationMessage interface to include isSystemGenerated flag
interface ExtendedConversationMessage extends BaseConversationMessage {
  isSystemGenerated?: boolean; // Flag to mark system-generated preset messages
  isContextMessage?: boolean;  // Flag to mark context messages that shouldn't be displayed
}

interface AIRewritePreviewProps {
  isLoading: boolean;
  streamResponse: string;
  generatedContent: string;
  conversationHistory?: Array<ExtendedConversationMessage>;
  bookId?: string;
  onInsert: () => void;
  onInsertBubble?: (content: string) => void;
  onRestart?: () => void;
  onContinue?: () => void;
}

/**
 * AI改写预览组件
 * 用于显示AI生成的改写内容
 */
/**
 * 对话输入框弹窗组件
 */
interface DialogInputProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (prompt: string, mode: string) => void;
}

const DialogInput: React.FC<DialogInputProps> = ({
  isOpen,
  onClose,
  onSubmit
}) => {
  const [prompt, setPrompt] = useState('');
  const [mode, setMode] = useState('continue'); // 默认为继续创作模式

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim()) {
      onSubmit(prompt, mode);
      setPrompt('');
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-2xl w-[600px] max-w-[90vw]">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">继续对话</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-4">
            <div className="flex space-x-2 mb-3">
              <button
                onClick={() => setMode('continue')}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'continue'
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                继续创作
              </button>
              <button
                onClick={() => setMode('rewrite')}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'rewrite'
                    ? 'bg-purple-100 text-purple-700 border border-purple-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                重写内容
              </button>
              <button
                onClick={() => setMode('analyze')}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'analyze'
                    ? 'bg-green-100 text-green-700 border border-green-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                分析内容
              </button>
            </div>

            <div className="text-sm text-gray-500 mb-3">
              {mode === 'continue' && '保持当前风格和情节，继续创作内容'}
              {mode === 'rewrite' && '保持核心情节，以不同方式重写内容'}
              {mode === 'analyze' && '提供深入的文学分析和建议'}
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                rows={5}
                placeholder="请输入您的指示..."
              />
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={onClose}
                className="mr-3 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                </svg>
                发送
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const AIRewritePreview: React.FC<AIRewritePreviewProps> = ({
  isLoading,
  streamResponse,
  generatedContent,
  conversationHistory = [],
  bookId = '',
  onInsert: _onInsert, // Renamed to avoid unused variable warning
  onInsertBubble,
  onRestart: _onRestart, // Renamed to avoid unused variable warning
  onContinue
}) => {
  // 添加对话模式弹窗状态
  const [isDialogModalOpen, setIsDialogModalOpen] = useState(false);

  // 添加本地对话历史状态，用于在用户提交对话后立即更新气泡界面
  const [localConversationHistory, setLocalConversationHistory] = useState<ExtendedConversationMessage[]>(conversationHistory || []);

  // 添加一个状态来跟踪最后一次用户输入
  const [lastUserInput, setLastUserInput] = useState<string | null>(null);

  // 当props中的conversationHistory变化时，更新本地状态
  useEffect(() => {
    if (conversationHistory) {
      console.log('[AIRewritePreview] conversationHistory更新，长度:', conversationHistory.length);

      // 如果有最后一次用户输入，并且它不在conversationHistory中，则添加到本地历史
      if (lastUserInput) {
        const hasUserInput = conversationHistory.some(
          msg => msg.role === 'user' && msg.content === lastUserInput
        );

        if (!hasUserInput) {
          // 将用户输入添加到本地历史
          const updatedHistory = [
            ...conversationHistory,
            {
              role: 'user' as const,
              content: lastUserInput,
              isSystemGenerated: false // Explicitly mark as not system-generated
            }
          ];

          setLocalConversationHistory(updatedHistory);
          console.log('[AIRewritePreview] 添加用户输入到本地对话历史');
        } else {
          // 用户输入已存在，直接使用新的conversationHistory
          setLocalConversationHistory(conversationHistory);

          // 清除最后一次用户输入，因为它已经在历史中了
          setLastUserInput(null);
        }
      } else {
        // 没有最后一次用户输入，直接使用新的conversationHistory
        setLocalConversationHistory(conversationHistory);
      }
    }
  }, [conversationHistory, lastUserInput]);

  // 处理提交对话 - 简化版
  const handleSubmitDialog = async (prompt: string, mode: string) => {
    if (onContinue && prompt) {
      console.log('[AIRewritePreview] 处理提交对话，模式:', mode);

      // 获取当前内容
      const currentContent = generatedContent || streamResponse;

      // 保存用户提示到localStorage，供AIRewriteService使用
      localStorage.setItem(`ai-rewrite-user-prompt-${bookId}`, prompt);

      // 保存对话模式
      localStorage.setItem(`ai-rewrite-mode-${bookId}`, JSON.stringify(mode));

      // 使用 DialogContinuationManager 处理继续对话
      const result = await dialogContinuationManager.handleContinuation({
        currentContent,
        continuePrompt: prompt,
        continueMode: mode as 'continue' | 'rewrite' | 'analyze' | 'new',
        bookId,
        conversationHistory: (conversationHistory || []) as {
          role: 'system' | 'user' | 'assistant';
          content: string;
        }[],
        serviceType: 'rewrite' as const
      });

      // 将更新后的对话历史保存到localStorage
      const historyKey = `ai-rewrite-history-${bookId}`;
      localStorage.setItem(historyKey, JSON.stringify(result.updatedHistory));

      // 设置最后一次用户输入，这将在useEffect中被处理
      setLastUserInput(prompt);

      // 立即更新本地对话历史，添加用户消息
      // 这样用户可以立即看到自己的输入，而不需要等待下一次请求
      const updatedHistory = [
        ...localConversationHistory,
        {
          role: 'user' as const,
          content: prompt,
          isSystemGenerated: false // Explicitly mark as not system-generated
        }
      ];

      // 更新本地对话历史状态
      setLocalConversationHistory(updatedHistory);

      console.log('[AIRewritePreview] 已添加用户消息到本地对话历史，立即更新气泡界面');
    }
  };
  // 处理对话显示逻辑 - 使用isSystemGenerated标记过滤系统生成的预设消息
  const getDisplayMessages = () => {
    // 使用本地对话历史状态
    const history = localConversationHistory || conversationHistory;

    // 如果没有对话历史，但有流式响应或生成内容，创建一个简单的对话
    if (!history || history.length === 0) {
      if (streamResponse) {
        return [{ role: 'assistant', content: streamResponse }];
      }

      if (generatedContent) {
        return [{ role: 'assistant', content: generatedContent }];
      }

      return [];
    }

    // 创建一个新的消息数组，只包含用户和助手消息，并过滤掉系统生成的预设消息
    const displayMessages = history
      .filter(msg => {
        // 过滤掉系统角色的消息
        if (msg.role === 'system') return false;

        // 过滤掉标记为系统生成的预设消息
        if (msg.isSystemGenerated) return false;

        // 过滤掉标记为上下文消息的消息
        if (msg.isContextMessage) return false;

        // 保留其他用户和助手消息
        return msg.role === 'user' || msg.role === 'assistant';
      })
      .map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      }));

    // 如果有流式响应且正在加载，更新或添加最后一条助手消息
    if (streamResponse && isLoading) {
      const lastMessage = displayMessages[displayMessages.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        displayMessages[displayMessages.length - 1] = {
          ...lastMessage,
          content: streamResponse
        };
      } else {
        displayMessages.push({
          role: 'assistant',
          content: streamResponse
        });
      }
    }

    // 确保最后一次用户输入显示在对话中
    if (lastUserInput) {
      const hasLastUserInput = displayMessages.some(msg =>
        msg.role === 'user' && msg.content === lastUserInput
      );

      if (!hasLastUserInput) {
        displayMessages.push({
          role: 'user',
          content: lastUserInput
        });
      }
    }

    return displayMessages;
  };

  // 处理插入内容
  const handleInsertBubble = (content: string) => {
    if (onInsertBubble) {
      onInsertBubble(content);
    }
  };

  // 处理复制内容
  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        console.log('内容已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 我们直接使用simulateConversation函数处理流式响应
  // 不再使用displayMessages变量

  return (
    <>
      <div className="w-full h-full flex flex-col">
        {/* 标题区域 */}
        <div className="flex justify-between items-center mb-3 py-2">
          <h3 className="text-lg font-medium text-indigo-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            AI响应预览
          </h3>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {!streamResponse && !generatedContent && !isLoading ? (
            <EmptyState />
          ) : (
            <div className="flex-1 flex flex-col">
              <div
                className="p-5 border rounded-2xl flex-1 overflow-y-auto shadow-sm"
                style={{
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                  backgroundColor: 'rgba(238, 242, 255, 0.7)',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  minHeight: '600px',
                  maxHeight: 'calc(100vh - 300px)' // 设置最大高度，确保在视口内可滚动
                }}
              >
                {isLoading && !streamResponse && !generatedContent ? (
                  <LoadingSpinner />
                ) : (
                  <div
                    className="ai-rewrite-preview prose prose-indigo max-w-none h-full"
                    style={{
                      wordBreak: 'break-word',
                      fontSize: '1.05rem',
                      lineHeight: '1.8'
                    }}
                  >
                    {/* 使用气泡样式显示对话 */}
                    <div className="space-y-4">
                      {getDisplayMessages().map((message, index) => (
                        <div
                          key={index}
                          className={`bubble-container flex items-start mb-4 group ${message.role === 'user' ? 'justify-end' : ''}`}
                        >
                          {/* 用户气泡 */}
                          {message.role === 'user' ? (
                            <>
                              {/* 用户气泡内容 */}
                              <div
                                className="user-bubble flex-grow-0 max-w-[80%] bg-blue-500 text-white rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow"
                              >
                                <div className="whitespace-pre-line">
                                  {message.content}
                                </div>
                              </div>

                              {/* 用户头像 */}
                              <div className="flex-shrink-0 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            </>
                          ) : (
                            <>
                              {/* AI头像 */}
                              <div className="flex-shrink-0 bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-sm mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                              </div>

                              {/* AI气泡内容 */}
                              <div
                                className="ai-bubble flex-grow max-w-[80%] bg-indigo-50 border border-indigo-100 rounded-2xl p-4 shadow-sm relative hover:shadow-md transition-shadow"
                              >
                                <div
                                  className="whitespace-pre-line"
                                  dangerouslySetInnerHTML={{
                                    __html: index === getDisplayMessages().length - 1 && isLoading
                                      ? message.content + '<span class="typing-cursor">|</span>'
                                      : message.content
                                  }}
                                />
                              </div>

                              {/* 操作按钮 - 放在气泡旁边 */}
                              <div className="ml-2 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col space-y-2">
                                {/* 插入按钮 */}
                                {onInsertBubble && (
                                  <button
                                    className="bg-green-500 text-white rounded-full p-2 shadow-sm hover:bg-green-600 transition-colors"
                                    onClick={() => handleInsertBubble(message.content)}
                                    title="插入到编辑器"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                  </button>
                                )}

                                {/* 复制按钮 */}
                                <button
                                  className="bg-blue-500 text-white rounded-full p-2 shadow-sm hover:bg-blue-600 transition-colors"
                                  onClick={() => handleCopyContent(message.content)}
                                  title="复制内容"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                  </svg>
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      ))}

                      {/* 加载动画 - 只在加载中且有内容时显示在最后一个气泡下方 */}
                      {isLoading && (getDisplayMessages().length > 0) && (
                        <div className="loading-gradient h-2 bg-gradient-to-r from-indigo-100 via-indigo-200 to-indigo-100 rounded-full animate-pulse mt-2"></div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 对话输入框弹窗 */}
      <DialogInput
        isOpen={isDialogModalOpen}
        onClose={() => setIsDialogModalOpen(false)}
        onSubmit={async (prompt, mode) => {
          if (onContinue) {
            // 处理提交对话
            await handleSubmitDialog(prompt, mode);
            // 触发对应模式
            localStorage.setItem(`ai-rewrite-mode-${bookId}`, JSON.stringify(mode));
            // 调用继续对话函数
            onContinue();
            // 关闭弹窗
            setIsDialogModalOpen(false);

            // 添加调试日志
            console.log('[AIRewritePreview] 对话提交完成，等待AI回复');
            console.log('[AIRewritePreview] 当前lastUserInput:', lastUserInput);
          }
        }}
      />
    </>
  );
};

/**
 * 加载中动画组件
 */
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center h-full">
    <div className="relative">
      <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-500 rounded-full animate-spin"></div>
      <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-b-indigo-300 rounded-full animate-spin" style={{ animationDuration: '1.5s' }}></div>
    </div>
  </div>
);

/**
 * 空状态提示组件
 */
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center justify-center h-full border border-dashed border-indigo-200 rounded-2xl bg-indigo-50" style={{ minHeight: '600px' }}>
    <div className="bg-white p-6 rounded-xl shadow-md max-w-md text-center">
      <div className="bg-indigo-100 p-4 rounded-full inline-block mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      </div>
      <h3 className="text-xl font-bold text-gray-800 mb-2">准备好改写了吗？</h3>
      <p className="text-gray-600 mb-6">
        填写左侧的改写要素，选择要改写的文本，然后点击下方的"开始改写"按钮，AI将根据您的要求改写内容。
      </p>
      <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
        <p className="font-medium mb-2">提示：</p>
        <ul className="list-disc list-inside space-y-1 text-left">
          <li>选择相关章节可以提高内容的连贯性</li>
          <li>关联人物和术语可以保持设定一致性</li>
          <li>详细的改写要求可以获得更精准的结果</li>
        </ul>
      </div>
    </div>
  </div>
);

export default AIRewritePreview;
