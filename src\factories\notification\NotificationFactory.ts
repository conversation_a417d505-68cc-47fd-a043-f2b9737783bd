import { INotificationFactory, INotificationComponent } from './interfaces';
import { DefaultNotificationComponent } from './components';

/**
 * 默认通知工厂实现
 */
class DefaultNotificationFactory implements INotificationFactory {
  /**
   * 创建通知组件
   * @returns 通知组件
   */
  createNotificationComponent(): INotificationComponent {
    return new DefaultNotificationComponent();
  }
}

/**
 * 创建通知工厂
 * @param style 样式，默认为'default'
 * @returns 通知工厂实例
 */
export function createNotificationFactory(style: 'default' | 'toast' = 'default'): INotificationFactory {
  switch (style) {
    case 'default':
    default:
      return new DefaultNotificationFactory();
  }
}
