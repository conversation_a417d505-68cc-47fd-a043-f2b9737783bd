"use client";

import React, { useEffect, useRef } from 'react';

interface ContinuePromptModalProps {
  isOpen: boolean;
  continuePrompt: string;
  onContinuePromptChange: (value: string) => void;
  onClose: () => void;
  onSubmit: () => void;
  mode?: 'continue' | 'rewrite' | 'analyze' | 'new';
}

/**
 * 继续对话弹窗组件
 * 用于输入继续对话的提示词
 */
const ContinuePromptModal: React.FC<ContinuePromptModalProps> = ({
  isOpen,
  continuePrompt,
  onContinuePromptChange,
  onClose,
  onSubmit,
  mode = 'continue'
}) => {
  // 创建一个ref用于自动聚焦
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 当弹窗打开时，自动聚焦到输入框
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isOpen]);

  // 处理键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Enter 提交
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      if (continuePrompt.trim()) {
        onSubmit();
      }
    }
    // Escape 关闭
    else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  if (!isOpen) return null;

  // 根据不同的模式显示不同的标题和提示文本
  const getTitle = () => {
    switch (mode) {
      case 'continue':
        return '继续创作';
      case 'rewrite':
        return '重写内容';
      case 'analyze':
        return '分析内容';
      default:
        return '继续对话';
    }
  };

  const getPlaceholder = () => {
    switch (mode) {
      case 'continue':
        return '请输入继续创作的要求，AI将在已有内容的基础上继续创作...';
      case 'rewrite':
        return '请输入重写的要求，AI将重写已有内容，但保持核心情节不变...';
      case 'analyze':
        return '请输入分析的要求，AI将对已有内容进行分析和评价...';
      default:
        return '请输入继续对话的提示词...';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'continue':
        return '内容将直接接续已生成的内容';
      case 'rewrite':
        return '重写的内容将显示在已生成内容之后';
      case 'analyze':
        return '分析结果将显示在已生成内容之后';
      default:
        return '';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md p-6 animate-fadeIn">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-800">{getTitle()}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <p className="text-sm text-gray-500 mb-4">
          {getDescription()}
        </p>

        <textarea
          value={continuePrompt}
          onChange={(e) => onContinuePromptChange(e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 mb-4"
          rows={5}
          placeholder={getPlaceholder()}
          autoFocus
        />

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            取消
          </button>
          <button
            onClick={onSubmit}
            className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
            disabled={!continuePrompt.trim()}
          >
            提交
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContinuePromptModal;
