/**
 * 框架信息构建模块
 * 负责处理创作框架相关的消息构建
 */

import { ConversationMessage, FrameworkInfo } from '../types/SharedTypes';
import { FrameworkDataEnhancer } from './FrameworkDataEnhancer';

// OutlineFramework接口定义（完整版本，包含分析数据）
interface OutlineFramework {
  id: string;
  frameworkName: string;
  frameworkPattern: string;
  frameworkVariables: string[];
  patternType: string;
  extractedFrom: {
    chapterIds: string[];
    chapterTitles: string[];
    extractDate: Date;
  };
  usageCount?: number;
  createdAt?: string;
  lastUsedAt?: string;
  // 分析数据字段
  plotAnalysis?: {
    storyStructure?: string;
    conflictDesign?: string;
    rhythmControl?: string;
    plotPointsWithGuidance?: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }>;
  };
  dialogueAnalysis?: {
    // 保留核心旧字段（标记为可选，向后兼容）
    dialogueStructure?: string;
    plotAdvancement?: string;
    writingTechniques?: string;
    toneCharacteristics?: string[];
    stylePatterns?: string[];
    literaryAnalysis?: string;

    // 新增：完整对话提取
    completeDialogues?: Array<{
      content: string;      // 对话内容
      speaker?: string;     // 说话人
      context: string;      // 上下文
      position: number;     // 位置
    }>;

    // 新增：风格分析
    styleAnalysis?: {
      dialogueStyle: string;        // 对话风格特征
      characterVoice: string;       // 角色语言特色
      emotionalTone: string;        // 情感基调
      technicalFeatures: string;   // 写作技巧特征
    };
  };
  styleAnalysis?: {
    writingStyle?: string;
    expressionFeatures?: string;
    practicalMethods?: string;
    rhythmPatterns?: string[];
    pacingFramework?: string;
    outlineGuidance?: string;
  };
}

export class FrameworkInfoBuilder {
  private static instance: FrameworkInfoBuilder;
  private frameworkDataEnhancer: FrameworkDataEnhancer;

  private constructor() {
    this.frameworkDataEnhancer = FrameworkDataEnhancer.getInstance();
  }

  public static getInstance(): FrameworkInfoBuilder {
    if (!FrameworkInfoBuilder.instance) {
      FrameworkInfoBuilder.instance = new FrameworkInfoBuilder();
    }
    return FrameworkInfoBuilder.instance;
  }

  /**
   * 🔥 新增：将OutlineFramework转换为FrameworkInfo
   * 解决框架数据结构不匹配的问题
   */
  convertOutlineFrameworkToFrameworkInfo(outlineFramework: OutlineFramework): FrameworkInfo {
    console.log('� FrameworkInfoBuilder: 开始转换OutlineFramework:', {
      frameworkName: outlineFramework.frameworkName,
      hasPlotAnalysis: !!outlineFramework.plotAnalysis,
      hasDialogueAnalysis: !!outlineFramework.dialogueAnalysis,
      hasStyleAnalysis: !!outlineFramework.styleAnalysis,
      plotPointsCount: outlineFramework.plotAnalysis?.plotPointsWithGuidance?.length || 0
    });

    // 直接使用OutlineFramework中的数据，不再通过FrameworkDataEnhancer生成
    const frameworkInfo: FrameworkInfo = {
      frameworkPattern: outlineFramework.frameworkPattern,
      patternType: outlineFramework.patternType,
      frameworkVariables: outlineFramework.frameworkVariables,
      description: `从${outlineFramework.extractedFrom?.chapterTitles?.join('、') || '参考作品'}等章节提取的框架 - ${outlineFramework.frameworkName}`,
      examples: [],
      // 直接使用提取的分析数据，不再生成
      plotAnalysis: outlineFramework.plotAnalysis,
      dialogueAnalysis: outlineFramework.dialogueAnalysis,
      styleAnalysis: outlineFramework.styleAnalysis
    };

    console.log('✅ FrameworkInfoBuilder: 转换完成，最终plotPointsWithGuidance数量:',
      frameworkInfo.plotAnalysis?.plotPointsWithGuidance?.length || 0);

    return frameworkInfo;
  }

  /**
   * 🔥 新增：批量转换OutlineFramework数组
   */
  convertOutlineFrameworksToFrameworkInfos(outlineFrameworks: OutlineFramework[]): FrameworkInfo[] {
    return outlineFrameworks.map(framework => this.convertOutlineFrameworkToFrameworkInfo(framework));
  }

  /**
   * 构建框架参考消息
   */
  buildFrameworkMessages(selectedFramework?: FrameworkInfo): ConversationMessage[] {
    if (!selectedFramework) {
      return [];
    }

    const messages: ConversationMessage[] = [];

    // 构建框架参考消息 - 去除名称依赖，强调学习模式
    let frameworkContent = `【参考创作模式】\n`;
    frameworkContent += `模式结构：${selectedFramework.frameworkPattern}\n`;

    if (selectedFramework.patternType) {
      frameworkContent += `模式类型：${selectedFramework.patternType}\n`;
    }

    if (selectedFramework.frameworkVariables && selectedFramework.frameworkVariables.length > 0) {
      frameworkContent += `关键变量：${selectedFramework.frameworkVariables.join('、')}\n`;
    }

    if (selectedFramework.description) {
      frameworkContent += `模式说明：${selectedFramework.description}\n`;
    }

    if (selectedFramework.examples && selectedFramework.examples.length > 0) {
      frameworkContent += `参考示例：\n${selectedFramework.examples.map(example => `- ${example}`).join('\n')}\n`;
    }

    // 🎯 添加核心剧情点信息 - 框架提取的核心价值
    if (selectedFramework.plotAnalysis?.plotPoints &&
        Array.isArray(selectedFramework.plotAnalysis.plotPoints) &&
        selectedFramework.plotAnalysis.plotPoints.length > 0) {
      frameworkContent += `\n【📋 核心剧情点】\n`;
      selectedFramework.plotAnalysis.plotPoints.forEach((plotPoint: any, index: number) => {
        frameworkContent += `${index + 1}. ${plotPoint}\n`;
      });
      frameworkContent += `\n💡 **剧情点学习要点**：这些是从原作中提取的具体剧情发展节点，体现了作者的剧情推进技巧和节奏控制方法。在创建大纲时可以学习其剧情安排和发展逻辑。\n`;
    }

    // 🎨 添加行为表现特征 - 角色塑造的重要参考
    if (selectedFramework.plotAnalysis?.behaviorFrameworks &&
        Array.isArray(selectedFramework.plotAnalysis.behaviorFrameworks) &&
        selectedFramework.plotAnalysis.behaviorFrameworks.length > 0) {
      frameworkContent += `\n【🎭 行为表现特征】\n`;
      selectedFramework.plotAnalysis.behaviorFrameworks.forEach((behavior: any, index: number) => {
        frameworkContent += `${index + 1}. ${behavior}\n`;
      });
      frameworkContent += `\n💡 **行为特征学习要点**：这些是角色行为表现的具体特征，体现了作者的角色塑造技巧。在设计角色行为时可以参考这些表现方式。\n`;
    }

    // 🔥 新增：处理plotPointsWithGuidance信息
    if (selectedFramework.plotAnalysis?.plotPointsWithGuidance &&
        Array.isArray(selectedFramework.plotAnalysis.plotPointsWithGuidance) &&
        selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 0) {
      console.log('🔥 FrameworkInfoBuilder: 添加plotPointsWithGuidance内容，数量:',
        selectedFramework.plotAnalysis.plotPointsWithGuidance.length);
      frameworkContent += this.buildPlotPointsWithGuidanceContent(selectedFramework.plotAnalysis.plotPointsWithGuidance);
    } else {
      console.warn('🔥 FrameworkInfoBuilder: plotPointsWithGuidance为空或不存在:', {
        hasPlotAnalysis: !!selectedFramework.plotAnalysis,
        hasPlotPointsWithGuidance: !!selectedFramework.plotAnalysis?.plotPointsWithGuidance,
        isArray: Array.isArray(selectedFramework.plotAnalysis?.plotPointsWithGuidance),
        length: selectedFramework.plotAnalysis?.plotPointsWithGuidance?.length || 0
      });
    }

    // 🔥 新增：处理对话分析信息
    if (selectedFramework.dialogueAnalysis) {
      frameworkContent += this.buildDialogueAnalysisContent(selectedFramework.dialogueAnalysis);
    }

    // 🔥 新增：处理风格分析信息
    if (selectedFramework.styleAnalysis) {
      frameworkContent += this.buildStyleAnalysisContent(selectedFramework.styleAnalysis);
    }

    frameworkContent += `\n请参考这个创作模式的结构特点，但不要完全照搬，要根据具体情况灵活运用。`;

    messages.push({
      role: 'user',
      content: frameworkContent
    });

    // 添加框架理解确认消息
    messages.push({
      role: 'assistant',
      content: `我已理解这个创作模式的特点。我会参考其结构思路，但会根据您的具体需求进行适当调整，确保创作出符合您故事特色的大纲结构。`
    });

    return messages;
  }

  /**
   * 构建多框架参考消息
   */
  buildMultiFrameworkMessages(selectedFrameworks: FrameworkInfo[]): ConversationMessage[] {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    // 使用分离展示方法，避免技巧杂糅
    return this.buildSeparatedFrameworkMessages(selectedFrameworks);
  }

  /**
   * 生成框架标识消息
   */
  private generateFrameworkIdentifier(framework: any, index: number, total: number): string {
    const colorEmojis = ['🔵', '🟢', '🟡', '🔴', '🟠', '🟣', '🔶', '🔷', '🔸'];
    const colorEmoji = colorEmojis[index % colorEmojis.length];

    return `【${colorEmoji} 框架 ${index + 1}/${total} - ${framework.frameworkName || '未命名框架'}】
📖 来源：${framework.patternType || '写作特征分析'}
🎯 特征：${framework.frameworkPattern || '具体写作手法'}
💡 重点：专门学习此框架的独特技巧，避免与其他框架混合`;
  }

  /**
   * 生成框架独立确认消息
   */
  private generateFrameworkConfirmation(framework: any, index: number, total: number): string {
    const colorEmojis = ['🔵', '🟢', '🟡', '🔴', '🟠', '🟣', '🔶', '🔷', '🔸'];
    const colorEmoji = colorEmojis[index % colorEmojis.length];

    // 统计框架信息
    const plotPointsCount = framework.plotAnalysis?.plotPoints?.length || 0;
    const behaviorCount = framework.plotAnalysis?.behaviorFrameworks?.length || 0;
    const dialogueCount = framework.dialogueAnalysis ? 1 : 0;
    const styleCount = framework.styleAnalysis ? 1 : 0;

    return `我已完整理解第${index + 1}个框架"${framework.frameworkName || '未命名框架'}"的特点：
${colorEmoji} ✅ 核心剧情点：${plotPointsCount}个
${colorEmoji} ✅ 行为表现特征：${behaviorCount}个
${colorEmoji} ✅ 对话技巧特征：${dialogueCount}个
${colorEmoji} ✅ 风格特征要点：${styleCount}个

我将在创作时专门学习此框架的独特技巧，避免与其他框架混合使用。`;
  }

  /**
   * 构建分离的多框架消息（避免技巧杂糅）
   */
  buildSeparatedFrameworkMessages(selectedFrameworks: FrameworkInfo[]): ConversationMessage[] {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    const messages: ConversationMessage[] = [];

    // 添加框架分离说明
    messages.push({
      role: 'user',
      content: `【🎯 框架分离学习模式】
您选择了${selectedFrameworks.length}个创作框架作为参考。为避免技巧杂糅和准度混乱，我将为每个框架单独发送消息并分别确认。

📋 **分离原则**：
• 每个框架独立展示，明确标识归属
• 每个框架单独确认，避免混淆
• 专门学习各框架的独特技巧
• 避免不同框架技巧的混合使用

🎨 **学习目标**：深度理解每个框架的特点，在创作时选择最适合的框架技巧，确保效果最佳。`
    });

    // 为每个框架单独构建消息
    selectedFrameworks.forEach((framework, index) => {
      // 添加框架标识消息
      messages.push({
        role: 'user',
        content: this.generateFrameworkIdentifier(framework, index, selectedFrameworks.length)
      });

      let frameworkContent = ``;
      frameworkContent += `模式结构：${framework.frameworkPattern}\n`;

      if (framework.patternType) {
        frameworkContent += `模式类型：${framework.patternType}\n`;
      }

      if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
        frameworkContent += `关键变量：${framework.frameworkVariables.join('、')}\n`;
      }

      if (framework.description) {
        frameworkContent += `模式说明：${framework.description}\n`;
      }

      if (framework.examples && framework.examples.length > 0) {
        frameworkContent += `参考示例：\n${framework.examples.map(example => `- ${example}`).join('\n')}\n`;
      }

      // 🎯 添加核心剧情点信息 - 框架提取的核心价值
      if (framework.plotAnalysis?.plotPoints &&
          Array.isArray(framework.plotAnalysis.plotPoints) &&
          framework.plotAnalysis.plotPoints.length > 0) {
        frameworkContent += `\n【📋 核心剧情点】\n`;
        framework.plotAnalysis.plotPoints.forEach((plotPoint: any, pointIndex: number) => {
          frameworkContent += `${pointIndex + 1}. ${plotPoint}\n`;
        });
        frameworkContent += `\n💡 **剧情点学习要点**：这些是从原作中提取的具体剧情发展节点，体现了作者的剧情推进技巧和节奏控制方法。\n`;
      }

      // 🎨 添加行为表现特征 - 角色塑造的重要参考
      if (framework.plotAnalysis?.behaviorFrameworks &&
          Array.isArray(framework.plotAnalysis.behaviorFrameworks) &&
          framework.plotAnalysis.behaviorFrameworks.length > 0) {
        frameworkContent += `\n【🎭 行为表现特征】\n`;
        framework.plotAnalysis.behaviorFrameworks.forEach((behavior: any, behaviorIndex: number) => {
          frameworkContent += `${behaviorIndex + 1}. ${behavior}\n`;
        });
        frameworkContent += `\n💡 **行为特征学习要点**：这些是角色行为表现的具体特征，体现了作者的角色塑造技巧。\n`;
      }

      // 🔥 新增：处理plotPointsWithGuidance信息（ACE框架增强）
      if (framework.plotAnalysis?.plotPointsWithGuidance &&
          Array.isArray(framework.plotAnalysis.plotPointsWithGuidance) &&
          framework.plotAnalysis.plotPointsWithGuidance.length > 0) {
        console.log(`🔥 FrameworkInfoBuilder: 多框架模式 - 框架${index + 1}添加plotPointsWithGuidance，数量:`,
          framework.plotAnalysis.plotPointsWithGuidance.length);
        frameworkContent += this.buildPlotPointsWithGuidanceContent(framework.plotAnalysis.plotPointsWithGuidance);
      }

      // 🔥 新增：处理对话分析信息
      if (framework.dialogueAnalysis) {
        console.log(`🔥 FrameworkInfoBuilder: 多框架模式 - 框架${index + 1}添加对话分析信息`);
        frameworkContent += this.buildDialogueAnalysisContent(framework.dialogueAnalysis);
      }

      // 🔥 新增：处理风格分析信息
      if (framework.styleAnalysis) {
        console.log(`🔥 FrameworkInfoBuilder: 多框架模式 - 框架${index + 1}添加风格分析信息`);
        frameworkContent += this.buildStyleAnalysisContent(framework.styleAnalysis);
      }

      messages.push({
        role: 'user',
        content: frameworkContent
      });

      // 添加框架独立确认消息
      messages.push({
        role: 'assistant',
        content: this.generateFrameworkConfirmation(framework, index, selectedFrameworks.length)
      });
    });

    // 添加总体学习确认
    messages.push({
      role: 'assistant',
      content: `🎉 **框架分离学习完成**

我已通过分离模式完整学习了所有${selectedFrameworks.length}个框架的独特特点。每个框架都有明确的标识和独立的确认，确保了学习的准确性和深度。

📚 **学习成果**：
• 每个框架的技巧特征都被独立理解
• 避免了不同框架间的技巧杂糅
• 建立了清晰的框架应用边界
• 提升了创作时的选择精准度

� **创作策略**：
在后续创作中，我将根据具体需求选择最适合的单一框架技巧，而不是混合使用，确保创作效果的最佳化。

现在请告诉我您想要创建什么内容，我将精准选择最适合的框架特点为您服务。`
    });

    return messages;
  }

  /**
   * 构建素材库消息
   */
  buildMaterialLibraryMessage(): ConversationMessage | null {
    // 检查是否有素材库数据
    // 这里可以根据实际的素材库实现来构建消息
    // 暂时返回一个示例消息
    
    return {
      role: 'user',
      content: `【素材库信息】
当前素材库包含以下资源：
- 角色模板库：常见人物原型和性格设定
- 情节模板库：经典情节发展模式
- 世界观模板库：不同题材的世界观设定
- 对话模板库：各种场景的对话范例

您可以在创作过程中引用这些素材，我会根据需要为您提供相关的模板和建议。`
    };
  }

  /**
   * 构建框架信息摘要
   */
  buildFrameworkInfo(outline: any, options: any = {}): string {
    if (!options.selectedFramework && (!options.selectedFrameworks || options.selectedFrameworks.length === 0)) {
      return '当前未选择创作框架';
    }

    let info = '';

    if (options.selectedFrameworks && options.selectedFrameworks.length > 1) {
      info += `使用多框架参考模式（${options.selectedFrameworks.length}个框架）：\n`;
      options.selectedFrameworks.forEach((framework: FrameworkInfo, index: number) => {
        info += `${index + 1}. ${framework.frameworkPattern || '未命名框架'}\n`;
      });
    } else {
      const framework = options.selectedFramework || options.selectedFrameworks?.[0];
      if (framework) {
        info += `使用创作框架：${framework.frameworkPattern || '未命名框架'}\n`;
        if (framework.patternType) {
          info += `框架类型：${framework.patternType}\n`;
        }
        if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
          info += `关键要素：${framework.frameworkVariables.join('、')}\n`;
        }
      }
    }

    return info;
  }

  /**
   * 验证框架信息完整性
   */
  validateFrameworkInfo(framework: FrameworkInfo): boolean {
    if (!framework) {
      return false;
    }

    // 检查必要字段
    if (!framework.frameworkPattern) {
      return false;
    }

    return true;
  }

  /**
   * 获取框架建议
   */
  getFrameworkSuggestions(nodeType: string): string[] {
    const suggestions: { [key: string]: string[] } = {
      chapter: [
        '考虑使用三幕式结构安排章节内容',
        '确保每章都有明确的目标和冲突',
        '平衡动作场面和情感发展'
      ],
      scene: [
        '每个场景都应推进主线剧情',
        '注意场景间的过渡和连接',
        '合理安排角色出场和互动'
      ],
      section: [
        '保持段落内容的聚焦性',
        '注意节奏的快慢变化',
        '适当添加细节描写'
      ],
      dialogue: [
        '确保对话符合角色性格',
        '通过对话推进情节发展',
        '注意对话的自然性和真实感'
      ]
    };

    return suggestions[nodeType] || ['根据具体情况灵活运用框架特点'];
  }

  /**
   * 构建plotPointsWithGuidance内容
   * 这是ACE功能的核心实现
   */
  buildPlotPointsWithGuidanceContent(plotPointsWithGuidance: Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }>): string {
    if (!plotPointsWithGuidance || plotPointsWithGuidance.length === 0) {
      return '';
    }

    let content = `\n\n【🔥 框架剧情点写作指导 - ACE增强】\n`;
    content += `以下是从参考作品中提取的剧情点模式和写作指导，请学习其技巧并应用到当前创作中：\n\n`;

    // 不限制数量，显示所有剧情点（避免学习力度不够）
    plotPointsWithGuidance.forEach((guidancePoint, index) => {
      content += `**剧情点 ${index + 1}**：${guidancePoint.content}\n`;

      if (guidancePoint.specificDescription) {
        content += `📝 **具体描写特征**：${guidancePoint.specificDescription}\n`;
      }

      if (guidancePoint.avoidanceGuidance) {
        content += `⚠️ **avoidWriting 避免指导学习**：${guidancePoint.avoidanceGuidance}\n`;
      }

      content += `💡 **创作要点**：学习此剧情点的写作技巧，在创作类似情节时体现具体描写特征，严格遵循避免指导，确保avoidWriting字段达到70字以上的具体化要求。\n\n`;
    });

    content += `🎯 **重要提醒**：\n`;
    content += `- 充分利用specificDescription信息，在shouldWriting 生成精确的创作方向和指导\n`;
    content += `- 严格遵循avoidanceGuidance，避免"一丝xx"、"几分xx"等模糊表达\n`;
    content += `- 每个剧情点的avoidWriting必须70字以上，具体化、行为导向，确保shouldWriting 的高质量内容创作\n`;
    content += `- 专注角色具体行动，避免环境描写和比喻修辞\n`;
    content += `- 这些剧情点来自参考作品分析，学习其写作技巧和模式，应用到当前创作中\n`;

    return content;
  }

  /**
   * 提取specificDescription指导信息
   */
  extractSpecificDescriptionGuidance(plotPointsWithGuidance: Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }>): string[] {
    if (!plotPointsWithGuidance || plotPointsWithGuidance.length === 0) {
      return [];
    }

    return plotPointsWithGuidance
      .filter(point => point.specificDescription)
      .map(point => point.specificDescription);
  }

  /**
   * 提取avoidanceGuidance指导信息
   */
  extractAvoidanceGuidance(plotPointsWithGuidance: Array<{
    content: string;
    specificDescription: string;
    avoidanceGuidance: string;
  }>): string[] {
    if (!plotPointsWithGuidance || plotPointsWithGuidance.length === 0) {
      return [];
    }

    return plotPointsWithGuidance
      .filter(point => point.avoidanceGuidance)
      .map(point => point.avoidanceGuidance);
  }

  /**
   * 集成框架指导信息
   * 将plotPointsWithGuidance信息集成到创作指导中
   */
  integrateFrameworkGuidance(
    framework: FrameworkInfo,
    nodeType: string = 'plot'
  ): {
    specificDescriptions: string[];
    avoidanceGuidances: string[];
    integratedGuidance: string;
  } {
    const result = {
      specificDescriptions: [] as string[],
      avoidanceGuidances: [] as string[],
      integratedGuidance: ''
    };

    if (!framework.plotAnalysis?.plotPointsWithGuidance) {
      return result;
    }

    const plotPointsWithGuidance = framework.plotAnalysis.plotPointsWithGuidance;

    result.specificDescriptions = this.extractSpecificDescriptionGuidance(plotPointsWithGuidance);
    result.avoidanceGuidances = this.extractAvoidanceGuidance(plotPointsWithGuidance);

    // 生成集成指导
    let guidance = `基于框架分析的${nodeType}节点创作指导：\n`;

    if (result.specificDescriptions.length > 0) {
      guidance += `\n具体描写要点：\n`;
      result.specificDescriptions.forEach((desc, index) => {
        guidance += `${index + 1}. ${desc}\n`;
      });
    }

    if (result.avoidanceGuidances.length > 0) {
      guidance += `\n避免事项：\n`;
      result.avoidanceGuidances.forEach((avoid, index) => {
        guidance += `${index + 1}. ${avoid}\n`;
      });
    }

    result.integratedGuidance = guidance;
    return result;
  }

  /**
   * 构建对话分析内容
   */
  buildDialogueAnalysisContent(dialogueAnalysis: any): string {
    if (!dialogueAnalysis) return '';

    let content = `\n\n【💬 对话写作技巧参考】\n`;
    content += `以下是从参考作品中提取的对话写作技巧，请学习并应用：\n\n`;

    if (dialogueAnalysis.dialogueStructure) {
      content += `📋 **对话结构模式**：${dialogueAnalysis.dialogueStructure}\n`;
    }

    if (dialogueAnalysis.plotAdvancement) {
      content += `🎯 **剧情推进技巧**：${dialogueAnalysis.plotAdvancement}\n`;
    }

    if (dialogueAnalysis.writingTechniques) {
      content += `✍️ **写作技巧要点**：${dialogueAnalysis.writingTechniques}\n`;
    }

    if (dialogueAnalysis.toneCharacteristics && Array.isArray(dialogueAnalysis.toneCharacteristics)) {
      content += `🎭 **语气特征**：\n`;
      dialogueAnalysis.toneCharacteristics.forEach((tone: string, index: number) => {
        content += `  ${index + 1}. ${tone}\n`;
      });
    }

    if (dialogueAnalysis.stylePatterns && Array.isArray(dialogueAnalysis.stylePatterns)) {
      content += `📝 **行文模式**：\n`;
      dialogueAnalysis.stylePatterns.forEach((pattern: string, index: number) => {
        content += `  ${index + 1}. ${pattern}\n`;
      });
    }

    if (dialogueAnalysis.literaryAnalysis) {
      content += `📚 **文学化特征**：${dialogueAnalysis.literaryAnalysis}\n`;
    }

    content += `\n💡 **应用要点**：学习这些对话技巧的精髓，在创作对话节点时体现相应的结构特点和语言风格。\n`;

    return content;
  }

  /**
   * 构建风格分析内容
   */
  buildStyleAnalysisContent(styleAnalysis: any): string {
    if (!styleAnalysis) return '';

    let content = `\n\n【🎨 写作风格参考】\n`;
    content += `以下是从参考作品中提取的写作风格特征，请学习并融入：\n\n`;

    if (styleAnalysis.writingStyle) {
      content += `✨ **写作风格**：${styleAnalysis.writingStyle}\n`;
    }

    if (styleAnalysis.expressionFeatures) {
      content += `🎪 **表现特色**：${styleAnalysis.expressionFeatures}\n`;
    }

    if (styleAnalysis.practicalMethods) {
      content += `🛠️ **实用方法**：${styleAnalysis.practicalMethods}\n`;
    }

    if (styleAnalysis.rhythmPatterns && Array.isArray(styleAnalysis.rhythmPatterns)) {
      content += `🎵 **节奏模式**：\n`;
      styleAnalysis.rhythmPatterns.forEach((pattern: string, index: number) => {
        content += `  ${index + 1}. ${pattern}\n`;
      });
    }

    if (styleAnalysis.pacingFramework) {
      content += `⏱️ **节奏控制框架**：${styleAnalysis.pacingFramework}\n`;
    }

    if (styleAnalysis.outlineGuidance) {
      content += `📋 **大纲参照指导**：${styleAnalysis.outlineGuidance}\n`;
    }

    content += `\n💡 **应用要点**：将这些风格特征融入到节点创作中，保持与参考作品相似的写作质感和节奏控制。\n`;

    return content;
  }
}
