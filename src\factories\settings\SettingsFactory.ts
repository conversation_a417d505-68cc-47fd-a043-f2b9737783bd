import { ISettingsFactory } from './interfaces/ISettingsFactory';
import { ISettingsDialogComponent } from './interfaces/ISettingsDialogComponent';
import { IAPISettingsDialogComponent } from './interfaces/IAPISettingsDialogComponent';
import { DefaultSettingsDialogComponent } from './components/DefaultSettingsDialog';
import { DefaultAPISettingsDialogComponent } from './components/DefaultAPISettingsDialog';

/**
 * 默认设置工厂实现
 */
class DefaultSettingsFactory implements ISettingsFactory {
  /**
   * 创建设置弹窗组件
   */
  createSettingsDialogComponent(): ISettingsDialogComponent {
    return new DefaultSettingsDialogComponent();
  }

  /**
   * 创建API设置弹窗组件
   */
  createAPISettingsDialogComponent(): IAPISettingsDialogComponent {
    return new DefaultAPISettingsDialogComponent();
  }
}

/**
 * 创建设置工厂
 * @param style 样式，默认为'default'
 * @returns 设置工厂实例
 */
export function createSettingsFactory(style: 'default' | 'dark' = 'default'): ISettingsFactory {
  switch (style) {
    case 'default':
    default:
      return new DefaultSettingsFactory();
  }
}
