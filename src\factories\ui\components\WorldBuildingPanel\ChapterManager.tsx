"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  worldBuildingIds?: string[];
  terminologyIds?: string[];
}

interface ChapterManagerProps {
  isOpen: boolean;
  onClose: () => void;
  worldBuilding: WorldBuilding;
  onUpdate: (worldBuilding: WorldBuilding) => void;
  bookId: string;
}

/**
 * 章节管理对话框组件
 * 用于管理世界观元素与章节的关联
 */
export const ChapterManager: React.FC<ChapterManagerProps> = ({
  isOpen,
  onClose,
  worldBuilding,
  onUpdate,
  bookId
}) => {
  // 章节列表
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 选中的章节ID列表
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(worldBuilding.extractedFromChapterIds || []);
  // 搜索关键词
  const [searchQuery, setSearchQuery] = useState('');
  // 过滤后的章节列表
  const [filteredChapters, setFilteredChapters] = useState<GenericChapter[]>([]);
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 成功消息
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  // 保存的配置名称
  const [configName, setConfigName] = useState('');
  // 保存的配置列表
  const [savedConfigs, setSavedConfigs] = useState<{name: string, chapterIds: string[]}[]>([]);
  // 是否显示保存配置对话框
  const [showSaveConfigDialog, setShowSaveConfigDialog] = useState(false);

  // 加载章节数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
      loadSavedConfigs();
    }
  }, [isOpen, bookId]);

  // 过滤章节列表
  useEffect(() => {
    if (chapters.length > 0) {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const filtered = chapters.filter(chapter => 
          (chapter.title?.toLowerCase().includes(query)) || 
          (chapter.content?.toLowerCase().includes(query))
        );
        setFilteredChapters(filtered);
      } else {
        setFilteredChapters([...chapters].sort((a, b) => (a.order || 0) - (b.order || 0)));
      }
    }
  }, [chapters, searchQuery]);

  // 加载章节列表
  const loadChapters = async () => {
    setIsLoading(true);
    try {
      // 导入 chapterRepository
      const { chapterRepository } = await import('@/lib/db/repositories');

      // 获取章节列表
      const chaptersData = await chapterRepository.getAllByBookId(bookId);
      setChapters(chaptersData);
      setFilteredChapters([...chaptersData].sort((a, b) => (a.order || 0) - (b.order || 0)));
    } catch (error) {
      console.error('加载章节列表失败:', error);
      setError('加载章节列表失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 加载保存的配置
  const loadSavedConfigs = async () => {
    try {
      // 从localStorage获取保存的配置
      const savedConfigsStr = localStorage.getItem(`worldbuilding-chapter-configs-${bookId}`);
      if (savedConfigsStr) {
        const configs = JSON.parse(savedConfigsStr);
        setSavedConfigs(configs);
      }
    } catch (error) {
      console.error('加载保存的配置失败:', error);
    }
  };

  // 处理章节选择变化
  const handleChapterSelectionChange = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAllChapters = (checked: boolean) => {
    if (checked) {
      setSelectedChapterIds(filteredChapters.map(chapter => chapter.id!).filter(Boolean));
    } else {
      setSelectedChapterIds([]);
    }
  };

  // 保存章节关联
  const saveChapterAssociation = async () => {
    setIsSaving(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      // 更新世界观元素的关联章节
      const updatedWorldBuilding = { 
        ...worldBuilding,
        extractedFromChapterIds: selectedChapterIds
      };
      
      // 更新章节的关联世界观元素
      const { worldBuildingRepository } = await import('@/lib/db/repositories');
      
      // 获取当前关联的章节
      const currentChapterIds = worldBuilding.extractedFromChapterIds || [];
      
      // 需要添加关联的章节
      const chapterIdsToAdd = selectedChapterIds.filter(id => !currentChapterIds.includes(id));
      
      // 需要移除关联的章节
      const chapterIdsToRemove = currentChapterIds.filter(id => !selectedChapterIds.includes(id));
      
      // 为每个新增的章节添加关联
      for (const chapterId of chapterIdsToAdd) {
        await worldBuildingRepository.linkToChapter(worldBuilding.id!, chapterId);
      }
      
      // 为每个移除的章节删除关联
      for (const chapterId of chapterIdsToRemove) {
        await worldBuildingRepository.unlinkFromChapter(worldBuilding.id!, chapterId);
      }
      
      // 更新世界观元素
      onUpdate(updatedWorldBuilding);
      setSuccessMessage('章节关联保存成功');
      
      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('保存章节关联失败:', error);
      setError('保存章节关联失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsSaving(false);
    }
  };

  // 保存当前选择的章节配置
  const saveCurrentConfig = () => {
    if (!configName.trim()) {
      setError('请输入配置名称');
      return;
    }

    try {
      // 检查是否已存在同名配置
      const existingConfigIndex = savedConfigs.findIndex(config => config.name === configName);
      
      let newConfigs = [...savedConfigs];
      
      if (existingConfigIndex >= 0) {
        // 更新现有配置
        newConfigs[existingConfigIndex] = {
          name: configName,
          chapterIds: selectedChapterIds
        };
      } else {
        // 添加新配置
        newConfigs.push({
          name: configName,
          chapterIds: selectedChapterIds
        });
      }
      
      // 保存到localStorage
      localStorage.setItem(`worldbuilding-chapter-configs-${bookId}`, JSON.stringify(newConfigs));
      
      // 更新状态
      setSavedConfigs(newConfigs);
      setConfigName('');
      setShowSaveConfigDialog(false);
      setSuccessMessage('章节配置保存成功');
      
      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('保存章节配置失败:', error);
      setError('保存章节配置失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 加载保存的配置
  const loadConfig = (configIndex: number) => {
    const config = savedConfigs[configIndex];
    if (config) {
      setSelectedChapterIds(config.chapterIds);
      setSuccessMessage(`已加载配置: ${config.name}`);
      
      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    }
  };

  // 删除保存的配置
  const deleteConfig = (configIndex: number) => {
    if (window.confirm('确定要删除这个配置吗？')) {
      const newConfigs = savedConfigs.filter((_, index) => index !== configIndex);
      
      // 保存到localStorage
      localStorage.setItem(`worldbuilding-chapter-configs-${bookId}`, JSON.stringify(newConfigs));
      
      // 更新状态
      setSavedConfigs(newConfigs);
      setSuccessMessage('配置已删除');
      
      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">管理章节关联</h2>
          <p className="text-sm text-gray-600">为世界观元素 "{worldBuilding.name}" 管理关联的章节</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-y-auto">
          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* 成功消息 */}
          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
              {successMessage}
            </div>
          )}

          {/* 保存的配置 */}
          {savedConfigs.length > 0 && (
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">保存的章节配置</h3>
              <div className="flex flex-wrap gap-2 mb-4">
                {savedConfigs.map((config, index) => (
                  <div key={index} className="flex items-center bg-blue-50 rounded-lg p-2">
                    <span className="text-sm text-blue-700 mr-2">{config.name}</span>
                    <button
                      className="text-blue-500 hover:text-blue-700 mr-1"
                      onClick={() => loadConfig(index)}
                      title="加载配置"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      </svg>
                    </button>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={() => deleteConfig(index)}
                      title="删除配置"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 搜索框 */}
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索章节..."
                className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* 章节列表 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredChapters.length === 0 ? (
              <p className="text-center text-gray-500 py-4">没有找到章节</p>
            ) : (
              <div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-blue-600"
                      checked={selectedChapterIds.length === filteredChapters.length && filteredChapters.length > 0}
                      onChange={(e) => handleSelectAllChapters(e.target.checked)}
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700">全选</span>
                  </label>
                </div>
                <div className="space-y-1">
                  {filteredChapters.map((chapter) => (
                    <label key={chapter.id} className="flex items-center p-2 hover:bg-gray-100 rounded-lg">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedChapterIds.includes(chapter.id!)}
                        onChange={() => handleChapterSelectionChange(chapter.id!)}
                      />
                      <div className="ml-2">
                        <span className="text-sm font-medium text-gray-700">
                          {chapter.title || `第${(chapter.order !== undefined ? chapter.order : 0) + 1}章`}
                        </span>
                        {chapter.content && (
                          <p className="text-xs text-gray-500 line-clamp-1">
                            {chapter.content.replace(/<[^>]*>/g, '').substring(0, 50)}...
                          </p>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 选中的章节 */}
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">已选择 {selectedChapterIds.length} 个章节</h3>
            <div className="flex flex-wrap gap-2">
              {selectedChapterIds.map(id => {
                const chapter = chapters.find(c => c.id === id);
                return (
                  <div key={id} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm flex items-center">
                    <span>
                      {chapter ? (chapter.title || `第${(chapter.order !== undefined ? chapter.order : 0) + 1}章`) : id}
                    </span>
                    <button
                      className="ml-2 text-blue-500 hover:text-blue-700"
                      onClick={() => handleChapterSelectionChange(id)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 保存配置对话框 */}
        {showSaveConfigDialog && (
          <div className="p-4 border-t">
            <h3 className="text-sm font-medium text-gray-700 mb-2">保存当前章节选择为配置</h3>
            <div className="flex items-center">
              <input
                type="text"
                value={configName}
                onChange={(e) => setConfigName(e.target.value)}
                placeholder="输入配置名称..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                className="ml-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                onClick={saveCurrentConfig}
              >
                保存
              </button>
              <button
                className="ml-2 px-3 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={() => setShowSaveConfigDialog(false)}
              >
                取消
              </button>
            </div>
          </div>
        )}

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-between">
          <div>
            <button
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors mr-2"
              onClick={() => setShowSaveConfigDialog(true)}
              disabled={selectedChapterIds.length === 0}
            >
              保存为配置
            </button>
          </div>
          <div className="flex space-x-2">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
              onClick={onClose}
            >
              取消
            </button>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              onClick={saveChapterAssociation}
              disabled={isSaving}
            >
              {isSaving ? '保存中...' : '保存关联'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
