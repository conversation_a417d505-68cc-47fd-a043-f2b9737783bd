"use client";

import React from 'react';
import { CustomDropdown } from './CustomDropdown';

interface TerminologyPanelHeaderProps {
  searchQuery: string;
  sortBy: 'name' | 'category' | 'importance';
  onSearchChange: (query: string) => void;
  onSortChange: (sortBy: 'name' | 'category' | 'importance') => void;
  onCreateTerminology: () => void;
  onExtractTerminologies?: () => void; // AI提取术语
  onBatchUpdateTerminologies?: () => void; // 批量更新术语
  onAICreateTerminologies?: () => void; // AI创建术语
  multiSelectMode?: boolean; // 多选模式
  onToggleMultiSelectMode?: () => void; // 切换多选模式
}

/**
 * 术语面板头部组件
 */
export const TerminologyPanelHeader: React.FC<TerminologyPanelHeaderProps> = ({
  searchQuery,
  sortBy,
  onSearchChange,
  onSortChange,
  onCreateTerminology,
  onExtractTerminologies,
  onBatchUpdateTerminologies,
  onAICreateTerminologies,
  multiSelectMode = false,
  onToggleMultiSelectMode
}) => {
  // 搜索图标
  const SearchIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  );

  // 添加图标
  const AddIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
  );

  // AI图标
  const AIIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
  );

  // 批量更新图标
  const BatchUpdateIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  );

  // 多选图标
  const MultiSelectIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>
  );

  return (
    <div className="flex items-center justify-between p-4" style={{
      backgroundColor: 'var(--color-primary-bg)',
      borderBottom: '1px solid rgba(139, 69, 19, 0.1)',
      borderTopLeftRadius: '16px',
      borderTopRightRadius: '16px'
    }}>
      <div className="flex items-center">
        <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>术语管理</h2>

        {/* 创建术语按钮 */}
        <button
          className="ml-4 p-2 rounded-full flex items-center transition-all duration-300 transform hover:scale-110"
          style={{
            backgroundColor: 'var(--color-primary)',
            color: 'white',
            boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
          }}
          onClick={onCreateTerminology}
          title="创建新术语"
        >
          <AddIcon />
        </button>

        {/* AI提取术语按钮 */}
        {onExtractTerminologies && (
          <button
            className="ml-3 p-2 rounded-full flex items-center transition-all duration-300 transform hover:scale-110"
            style={{
              backgroundColor: 'var(--color-info)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(70, 130, 180, 0.2)'
            }}
            onClick={onExtractTerminologies}
            title="AI提取术语"
          >
            <AIIcon />
          </button>
        )}

        {/* AI创建术语按钮 */}
        {onAICreateTerminologies && (
          <button
            className="ml-3 p-2 rounded-full flex items-center transition-all duration-300 transform hover:scale-110"
            style={{
              backgroundColor: 'var(--color-success)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(85, 107, 47, 0.2)'
            }}
            onClick={onAICreateTerminologies}
            title="AI创建术语"
          >
            <AIIcon />
            <span className="ml-1 text-xs">+</span>
          </button>
        )}

        {/* 批量更新按钮 */}
        {onBatchUpdateTerminologies && (
          <button
            className="ml-3 p-2 rounded-full flex items-center transition-all duration-300 transform hover:scale-110"
            style={{
              backgroundColor: 'var(--color-secondary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(210, 180, 140, 0.2)'
            }}
            onClick={onBatchUpdateTerminologies}
            title="批量更新术语"
          >
            <BatchUpdateIcon />
          </button>
        )}

        {/* 多选模式切换按钮 */}
        {onToggleMultiSelectMode && (
          <button
            className="ml-3 p-2 rounded-full flex items-center transition-all duration-300 transform hover:scale-110"
            style={{
              backgroundColor: multiSelectMode ? 'var(--color-danger)' : 'var(--color-secondary)',
              color: 'white',
              boxShadow: multiSelectMode
                ? '0 2px 8px rgba(178, 34, 34, 0.2)'
                : '0 2px 8px rgba(210, 180, 140, 0.2)'
            }}
            onClick={onToggleMultiSelectMode}
            title={multiSelectMode ? '取消多选' : '多选模式'}
          >
            <MultiSelectIcon />
          </button>
        )}
      </div>

      <div className="flex items-center space-x-3">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索术语..."
            className="pl-8 pr-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              borderColor: 'rgba(139, 69, 19, 0.2)',
              width: '180px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
              transition: 'all 0.3s ease',
              color: 'var(--color-primary)'
            }}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
          <div className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
            <SearchIcon />
          </div>
        </div>
        <CustomDropdown
          options={[
            { value: 'name', label: '按名称排序' },
            { value: 'category', label: '按类别排序' },
            { value: 'importance', label: '按重要性排序' }
          ]}
          value={sortBy}
          onChange={(value) => onSortChange(value as 'name' | 'category' | 'importance')}
          className="w-[140px]"
          enableSearch={false}
          maxHeight={150}
        />
      </div>
    </div>
  );
};
