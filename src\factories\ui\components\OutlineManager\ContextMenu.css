.context-menu {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  padding: 6px 0;
  border: 1px solid #e0e0e0;
  user-select: none;
  animation: menu-fade-in 0.15s ease-out;
}

/* 子菜单样式 */
.context-menu.submenu {
  position: absolute;
  animation: submenu-fade-in 0.1s ease-out;
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
  font-size: 14px;
  color: #333;
  position: relative;
  border-left: 3px solid transparent;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
  border-left-color: #ccc;
  transform: translateX(2px);
}

.context-menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.context-menu-item-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  font-size: 16px;
}

.context-menu-item-label {
  flex: 1;
}

/* 快捷键提示 */
.context-menu-item-shortcut {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
  font-family: monospace;
}

/* 子菜单箭头 */
.context-menu-submenu-arrow {
  font-size: 10px;
  color: #999;
  margin-left: 8px;
}

/* 有子菜单的菜单项 */
.context-menu-item.has-submenu {
  position: relative;
}

.context-menu-item.has-submenu:hover {
  background-color: #f0f0f0;
}

.context-menu-item.submenu-open {
  background-color: #f0f0f0;
  border-left-color: #aaa;
}

/* 分隔线 */
.context-menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 6px 0;
}

/* 菜单项特殊样式 */
.context-menu-item.menu-item-primary {
  border-left-color: var(--outline-primary, #3b82f6);
}

.context-menu-item.menu-item-primary:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.context-menu-item.menu-item-secondary {
  border-left-color: var(--outline-secondary, #8b5cf6);
}

.context-menu-item.menu-item-secondary:hover {
  background-color: rgba(139, 92, 246, 0.1);
}

.context-menu-item.menu-item-info {
  border-left-color: var(--outline-info, #06b6d4);
}

.context-menu-item.menu-item-info:hover {
  background-color: rgba(6, 182, 212, 0.1);
}

.context-menu-item.menu-item-special {
  border-left-color: #f59e0b;
}

.context-menu-item.menu-item-special:hover {
  background-color: rgba(245, 158, 11, 0.1);
}

/* 菜单动画 */
@keyframes menu-fade-in {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 子菜单动画 */
@keyframes submenu-fade-in {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
