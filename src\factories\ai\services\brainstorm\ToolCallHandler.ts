/**
 * AI工具调用处理器
 * 处理AI助手发出的工具调用指令，执行相应的功能并返回结果
 */

import { AssociationData } from '@/components/brainstorm/types';

export interface ToolCallInstruction {
  action: 'generate_book_titles' | 'generate_synopsis' | 'analyze_framework' | 'create_worldview' | 'manage_worldview';
  parameters: {
    keywords?: string[];
    framework?: string;
    customFramework?: string;
    requirements?: string;
    count?: number;
    worldType?: 'fantasy' | 'sci-fi' | 'modern' | 'historical' | 'mixed';
    complexity?: 'simple' | 'medium' | 'complex';
    elements?: string[];
    // 世界观管理相关参数
    action?: 'list' | 'select' | 'send' | 'create_and_save';
    bookId?: string;
    selectedIds?: string[];
    saveToProject?: boolean;
    [key: string]: any;
  };
  requestId: string;
}

export interface BookTitleResult {
  title: string;
  score: number;
  extractedKeywords?: string[];
  detectedFramework?: string;
}

export interface WorldViewResult {
  name: string;
  type: 'fantasy' | 'sci-fi' | 'modern' | 'historical' | 'mixed';
  description: string;
  elements: {
    geography?: string[];
    races?: string[];
    magic_system?: string[];
    technology?: string[];
    politics?: string[];
    culture?: string[];
    history?: string[];
  };
  score: number;
  complexity: 'simple' | 'medium' | 'complex';
  tags: string[];
  summary: string;
}

export class ToolCallHandler {
  // 世界观类型中英文映射表（支持双向映射）
  private static readonly WORLD_TYPE_MAPPING: Record<string, string> = {
    // 英文到中文
    'fantasy': '奇幻',
    'sci-fi': '科幻',
    'modern': '现代',
    'historical': '历史',
    'martial-arts': '武侠',
    'cultivation': '修仙',
    'urban': '都市',
    'mystery': '悬疑',
    'romance': '言情',
    'adventure': '冒险',
    'horror': '恐怖',
    'comedy': '喜剧',
    'mixed': '混合',
    // 中文到中文（保持原样）
    '奇幻': '奇幻',
    '科幻': '科幻',
    '现代': '现代',
    '历史': '历史',
    '武侠': '武侠',
    '修仙': '修仙',
    '都市': '都市',
    '悬疑': '悬疑',
    '言情': '言情',
    '冒险': '冒险',
    '恐怖': '恐怖',
    '喜剧': '喜剧',
    '混合': '混合'
  };

  constructor(private associationData?: AssociationData) {}

  /**
   * 将英文worldType转换为中文
   */
  private getChineseWorldType(worldType: string): string {
    return ToolCallHandler.WORLD_TYPE_MAPPING[worldType] || worldType;
  }

  /**
   * 执行工具调用指令
   */
  async executeToolCall(instruction: ToolCallInstruction): Promise<string> {
    try {
      console.log('🔧 执行工具调用:', instruction);

      switch (instruction.action) {
        case 'generate_book_titles':
          return await this.handleBookTitleGeneration(instruction.parameters);
        case 'generate_synopsis':
          return await this.handleSynopsisGeneration(instruction.parameters);
        case 'analyze_framework':
          return await this.handleFrameworkAnalysis(instruction.parameters);
        case 'create_worldview':
          return await this.handleWorldViewCreation(instruction.parameters);
        case 'manage_worldview':
          return await this.handleWorldViewManagement(instruction.parameters);
        default:
          return `❌ 抱歉，暂不支持"${instruction.action}"工具调用。\n\n支持的工具：\n- generate_book_titles: 生成书名\n- generate_synopsis: 生成简介\n- analyze_framework: 分析框架\n- create_worldview: 创建世界观\n- manage_worldview: 管理世界观`;
      }
    } catch (error) {
      console.error('❌ 工具调用执行失败:', error);
      return `⚠️ 工具调用执行失败，请稍后重试。\n\n错误信息：${error instanceof Error ? error.message : '未知错误'}`;
    }
  }

  /**
   * 处理书名生成工具调用
   */
  private async handleBookTitleGeneration(params: any): Promise<string> {
    console.log('📚 开始生成书名...');

    try {
      // 验证和标准化参数
      const validatedParams = this.validateBookTitleParams(params);

      // 查询已有的框架和关键词信息
      const enhancedParams = await this.enhanceBookTitleParams(validatedParams);

      // 动态导入真实的书名生成服务
      const { createBookTitleGenerationService } = await import('./index');
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');

      // 使用统一的AI服务架构
      const aiService = AIServiceFactory.getService(AIServiceType.BRAINSTORM);

      // 为了兼容现有的书名生成服务，仍需要创建API设置
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();

      // 创建书名生成服务（这里暂时保持原有接口，后续可以进一步重构）
      const titleGenerationService = createBookTitleGenerationService(apiSettings, aiService);

      console.log('🔧 使用真实的书名生成服务:', {
        keywords: enhancedParams.keywords,
        framework: enhancedParams.framework?.name || enhancedParams.customFramework,
        requirements: enhancedParams.userRequirements
      });

      // 构建生成参数，确保框架类型正确
      const generationParams = {
        keywords: enhancedParams.keywords,
        framework: enhancedParams.framework ? {
          ...enhancedParams.framework,
          usageCount: 0,
          createdAt: new Date(),
          lastUsedAt: new Date()
        } : undefined,
        customFramework: enhancedParams.customFramework,
        userRequirements: enhancedParams.userRequirements
      };

      // 创建回调函数
      const callbacks = {
        onStart: () => console.log('🚀 开始书名生成...'),
        onProgress: (progress: number) => console.log(`📊 生成进度: ${progress}%`),
        onTitleGenerated: (title: any) => console.log('📝 生成书名:', title.title),
        onComplete: (titles: any[]) => console.log('✅ 书名生成完成:', titles.length),
        onError: (error: any) => console.error('❌ 生成错误:', error)
      };

      // 调用真实的书名生成服务
      const result = await titleGenerationService.generateTitles(generationParams, callbacks);

      if (result.success && result.titles.length > 0) {
        // 格式化返回结果
        return this.formatRealBookTitleResults(result.titles, enhancedParams);
      } else {
        throw new Error(result.error || '书名生成失败');
      }

    } catch (error) {
      console.error('❌ 书名生成失败:', error);
      return this.createFallbackBookTitles(params);
    }
  }

  /**
   * 处理简介生成工具调用
   */
  private async handleSynopsisGeneration(params: any): Promise<string> {
    console.log('📖 开始生成简介...');

    try {
      // 验证和标准化参数
      const validatedParams = this.validateSynopsisParams(params);

      // 动态导入现有的简介生成服务
      const { SynopsisPromptBuilder } = await import('./builders/SynopsisPromptBuilder');
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');
      const { configService } = await import('@/services/configService');
      const { AIResponseParser } = await import('@/utils/ai/AIResponseParser');

      // 使用统一的AI服务架构
      const aiService = AIServiceFactory.getService(AIServiceType.BRAINSTORM);

      // 为了兼容现有的简介生成服务，仍需要创建API设置
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();

      console.log('🔧 使用现有的简介生成服务:', {
        keywords: validatedParams.keywords,
        framework: validatedParams.framework,
        customFramework: validatedParams.customFramework, // 🔥 添加customFramework日志
        requirements: validatedParams.requirements,
        length: validatedParams.length
      });

      // 🔥 修复：构建简介生成参数，正确处理customFramework
      const synopsisParams = {
        keywords: validatedParams.keywords,
        framework: validatedParams.framework ? {
          id: 'tool_call_framework',
          name: validatedParams.framework,
          description: validatedParams.framework,
          structure: [],
          examples: [],
          category: 'modern' as const,
          effectiveness: 8,
          usageCount: 0,
          createdAt: new Date()
        } : (validatedParams.customFramework ? {
          id: 'custom_framework',
          name: 'Custom Framework',
          description: validatedParams.customFramework,
          pattern: validatedParams.customFramework,
          structure: [],
          examples: [],
          category: 'modern' as const, // 🔥 修复：使用有效的category值
          effectiveness: 8,
          usageCount: 0,
          createdAt: new Date()
        } : null),
        customFramework: validatedParams.customFramework, // 🔥 确保customFramework字段被传递
        length: validatedParams.length || 'medium',
        customRequirements: validatedParams.requirements
      };

      // 🔥 添加详细日志记录，验证customFramework传递
      console.log('📋 构建的synopsisParams详情:', {
        hasKeywords: synopsisParams.keywords.length > 0,
        keywordsCount: synopsisParams.keywords.length,
        hasFramework: !!synopsisParams.framework,
        frameworkName: synopsisParams.framework?.name,
        hasCustomFramework: !!synopsisParams.customFramework,
        customFrameworkContent: synopsisParams.customFramework,
        length: synopsisParams.length,
        hasCustomRequirements: !!synopsisParams.customRequirements
      });

      // 使用现有的SynopsisPromptBuilder构建提示词
      const messages = SynopsisPromptBuilder.buildSynopsisGenerationPrompt(synopsisParams);

      // 获取API配置
      const aiConfig = await configService.getAIConfig();

      console.log('🤖 调用现有的简介生成AI服务...');

      // 调用AI服务生成简介（使用现有的流程）
      const response = await aiSender.sendRequest('', {
        messages: messages,
        temperature: aiConfig.temperature || 0.7,
        maxTokens: aiConfig.maxTokens || 2000,
        topP: aiConfig.topP,
        topK: aiConfig.topK
      });

      if (!response.success || !response.text) {
        throw new Error(response.error || '简介生成失败');
      }

      console.log('📝 AI原始响应:', response.text);

      // 使用现有的AIResponseParser解析响应
      const defaultValue = { synopsis: null };
      const aiResponse = AIResponseParser.parseJSON(response.text, defaultValue) as any;

      console.log('🔍 解析后的响应:', aiResponse);

      if (aiResponse.synopsis) {
        const synopsisData = aiResponse.synopsis;

        // 格式化返回结果（与现有格式保持一致）
        return this.formatSynopsisToolCallResult(synopsisData, validatedParams);
      } else {
        throw new Error('AI响应格式错误：缺少synopsis字段');
      }

    } catch (error) {
      console.error('❌ 简介生成失败:', error);
      return this.createFallbackSynopsis(params);
    }
  }

  /**
   * 处理框架分析工具调用
   */
  private async handleFrameworkAnalysis(params: any): Promise<string> {
    console.log('🔍 开始框架分析...');

    try {
      // 验证参数
      const bookTitles = Array.isArray(params.bookTitles) ? params.bookTitles : [];
      const customFramework = params.customFramework || '';
      const requirements = params.requirements || '';

      if (bookTitles.length === 0 && !customFramework) {
        return `❌ **框架分析失败**\n\n请提供书名示例或自定义框架模式进行分析。`;
      }

      // 构建AI分析请求
      const analysisPrompt = `你是一位专业的网文书名分析专家，请分析以下信息，提取框架模式和特征。

${bookTitles.length > 0 ? `**书名示例**：\n${bookTitles.map((title, index) => `${index + 1}. ${title}`).join('\n')}\n` : ''}

${customFramework ? `**自定义框架模式**：${customFramework}\n` : ''}

${requirements ? `**分析要求**：${requirements}\n` : ''}

请分析并提取：
1. 框架模式（用{}表示变量，如"开局{动作}，{反应}"）
2. 框架名称（给这种模式起个简洁的名字）
3. 应用场景和特点
4. 参考示例（基于提供的书名）
5. 置信度（0-1之间的数值）

请严格按照以下JSON格式返回：
{
  "frameworkName": "框架名称",
  "frameworkPattern": "框架模式",
  "description": "框架描述和特点",
  "examples": ["示例1", "示例2"],
  "applicationScenarios": ["适用场景1", "适用场景2"],
  "confidence": 0.9
}`;

      // 调用AI服务进行分析
      const { DefaultAISenderComponent } = await import('@/factories/ai/components/DefaultAISenderComponent');
      const aiSender = new DefaultAISenderComponent();

      console.log('🤖 调用AI进行框架分析...');

      // 获取动态配置
      let aiConfig: any = {};
      try {
        const { configService } = await import('@/services/configService');
        aiConfig = await configService.getAIConfig();
        console.log('🤖 获取到的AI配置:', aiConfig);
      } catch (configError) {
        console.warn('🤖 无法获取AI配置，使用默认设置:', configError);
      }

      const response = await aiSender.sendRequest('', {
        messages: [
          { role: 'user', content: analysisPrompt }
        ],
        // 使用动态配置，框架分析使用较低温度确保准确性
        temperature: aiConfig.temperature ? Math.min(aiConfig.temperature, 0.5) : 0.3,
        topP: aiConfig.topP,
        topK: aiConfig.topK,
        // 不设置maxTokens，让API使用默认设置
        streaming: false
      });

      if (!response.success || !response.text) {
        throw new Error(response.error || 'AI分析失败');
      }

      // 解析AI响应
      const analysisResult = this.parseFrameworkAnalysisResponse(response.text);

      if (!analysisResult) {
        throw new Error('无法解析AI分析结果');
      }

      // 保存框架到数据库（如果需要）
      await this.saveFrameworkToDatabase(analysisResult);

      // 格式化返回结果
      return this.formatFrameworkAnalysisResult(analysisResult, params);

    } catch (error) {
      console.error('❌ 框架分析失败:', error);
      return `❌ **框架分析失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请稍后重试或检查输入参数。`;
    }
  }

  /**
   * 解析框架分析响应
   */
  private parseFrameworkAnalysisResponse(responseText: string): any {
    try {
      // 尝试提取JSON格式的响应
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);

        // 验证必要字段
        if (parsed.frameworkName && parsed.frameworkPattern) {
          return {
            frameworkName: parsed.frameworkName,
            frameworkPattern: parsed.frameworkPattern,
            description: parsed.description || '框架分析结果',
            examples: Array.isArray(parsed.examples) ? parsed.examples : [],
            applicationScenarios: Array.isArray(parsed.applicationScenarios) ? parsed.applicationScenarios : [],
            confidence: typeof parsed.confidence === 'number' ? parsed.confidence : 0.8
          };
        }
      }

      return null;
    } catch (error) {
      console.error('解析框架分析响应失败:', error);
      return null;
    }
  }

  /**
   * 保存框架到数据库
   */
  private async saveFrameworkToDatabase(analysisResult: any): Promise<void> {
    try {
      // 这里可以实现保存到数据库的逻辑
      // 暂时只记录日志
      console.log('📝 框架分析结果（待保存）:', {
        name: analysisResult.frameworkName,
        pattern: analysisResult.frameworkPattern,
        confidence: analysisResult.confidence
      });
    } catch (error) {
      console.error('保存框架失败:', error);
      // 不抛出错误，因为保存失败不应该影响分析结果的返回
    }
  }

  /**
   * 格式化框架分析结果
   */
  private formatFrameworkAnalysisResult(analysisResult: any, originalParams: any): string {
    let result = `🔍 **框架分析结果**\n\n`;

    result += `**框架名称**：${analysisResult.frameworkName}\n`;
    result += `**框架模式**：\`${analysisResult.frameworkPattern}\`\n`;
    result += `**置信度**：${(analysisResult.confidence * 100).toFixed(1)}%\n\n`;

    if (analysisResult.description) {
      result += `**框架特点**：\n${analysisResult.description}\n\n`;
    }

    if (analysisResult.examples && analysisResult.examples.length > 0) {
      result += `**参考示例**：\n`;
      analysisResult.examples.forEach((example: string, index: number) => {
        result += `${index + 1}. ${example}\n`;
      });
      result += `\n`;
    }

    if (analysisResult.applicationScenarios && analysisResult.applicationScenarios.length > 0) {
      result += `**适用场景**：\n`;
      analysisResult.applicationScenarios.forEach((scenario: string, index: number) => {
        result += `• ${scenario}\n`;
      });
      result += `\n`;
    }

    result += `💡 **提示**：此框架已分析完成，现在可以使用它来生成书名了！`;

    return result;
  }

  /**
   * 验证和标准化书名生成参数
   */
  private validateBookTitleParams(params: any) {
    return {
      keywords: Array.isArray(params.keywords) ? params.keywords : [],
      framework: params.framework || '',
      customFramework: params.customFramework || '',
      requirements: params.requirements || '',
      count: Math.min(Math.max(parseInt(params.count) || 5, 1), 10)
    };
  }

  /**
   * 验证和标准化简介生成参数
   */
  private validateSynopsisParams(params: any) {
    return {
      keywords: Array.isArray(params.keywords) ? params.keywords : [],
      framework: params.framework || '',
      customFramework: params.customFramework || '',
      requirements: params.requirements || '',
      style: params.style || '现代网文',
      length: params.length || '中等长度'
    };
  }

  /**
   * 增强书名生成参数，查询已有的框架和关键词信息
   */
  private async enhanceBookTitleParams(params: any) {
    try {
      // 动态导入预设数据
      const { PRESET_KEYWORDS, PRESET_FRAMEWORKS } = await import('./index');

      // 查找匹配的框架
      let matchedFramework = null;
      if (params.framework) {
        matchedFramework = PRESET_FRAMEWORKS.find(f =>
          f.name === params.framework ||
          f.name.includes(params.framework) ||
          params.framework.includes(f.name)
        );
      }

      // 扩展关键词（从预设中查找相关关键词）
      let enhancedKeywords = [...params.keywords];
      if (params.keywords.length > 0) {
        try {
          for (const keyword of params.keywords) {
            // 查找相关关键词（简化处理，避免类型错误）
            const relatedKeywords = PRESET_KEYWORDS
              .filter((k: any) => k.text && k.text.includes(keyword))
              .slice(0, 2) // 最多添加2个相关关键词
              .map((k: any) => k.text);

            enhancedKeywords = [...enhancedKeywords, ...relatedKeywords];
          }

          // 去重并限制数量
          enhancedKeywords = [...new Set(enhancedKeywords)].slice(0, 8);
        } catch (error) {
          console.log('关键词扩展失败，使用原始关键词:', error);
          enhancedKeywords = params.keywords;
        }
      }

      console.log('🔍 参数增强结果:', {
        原始关键词: params.keywords,
        增强关键词: enhancedKeywords,
        原始框架: params.framework,
        匹配框架: matchedFramework?.name,
        自定义框架: params.customFramework
      });

      return {
        keywords: enhancedKeywords,
        framework: matchedFramework,
        customFramework: params.customFramework || (matchedFramework ? '' : params.framework),
        userRequirements: params.requirements,
        count: params.count
      };

    } catch (error) {
      console.error('参数增强失败，使用原始参数:', error);
      return {
        keywords: params.keywords,
        framework: null,
        customFramework: params.framework || params.customFramework,
        userRequirements: params.requirements,
        count: params.count
      };
    }
  }

  /**
   * 增强简介生成参数，查询已有的框架和关键词信息
   */
  private async enhanceSynopsisParams(params: any) {
    try {
      // 动态导入预设数据
      const { PRESET_KEYWORDS } = await import('./index');

      // 扩展关键词（从预设中查找相关关键词）
      let enhancedKeywords = [...params.keywords];
      if (params.keywords.length > 0) {
        try {
          for (const keyword of params.keywords) {
            // 查找相关关键词
            const relatedKeywords = PRESET_KEYWORDS
              .filter((k: any) => k.text && k.text.includes(keyword))
              .slice(0, 2) // 最多添加2个相关关键词
              .map((k: any) => k.text);

            enhancedKeywords = [...enhancedKeywords, ...relatedKeywords];
          }

          // 去重并限制数量
          enhancedKeywords = [...new Set(enhancedKeywords)].slice(0, 8);
        } catch (error) {
          console.log('简介关键词扩展失败，使用原始关键词:', error);
          enhancedKeywords = params.keywords;
        }
      }

      console.log('🔍 简介参数增强结果:', {
        原始关键词: params.keywords,
        增强关键词: enhancedKeywords,
        框架: params.framework,
        自定义框架: params.customFramework,
        风格: params.style,
        长度: params.length
      });

      return {
        keywords: enhancedKeywords,
        framework: params.framework ? { name: params.framework } : null,
        customFramework: params.customFramework,
        userRequirements: params.requirements,
        style: params.style,
        length: params.length
      };

    } catch (error) {
      console.error('简介参数增强失败，使用原始参数:', error);
      return {
        keywords: params.keywords,
        framework: null,
        customFramework: params.framework || params.customFramework,
        userRequirements: params.requirements,
        style: params.style,
        length: params.length
      };
    }
  }

  /**
   * 构建简介生成提示词
   */
  private buildSynopsisPrompt(params: any): string {
    let prompt = `你是一位专业的网络小说简介创作专家，请根据以下要求生成一个吸引人的小说简介：\n\n`;

    if (params.keywords.length > 0) {
      prompt += `**关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.framework?.name) {
      prompt += `**框架模式**：${params.framework.name}\n`;
    } else if (params.customFramework) {
      prompt += `**自定义框架**：${params.customFramework}\n`;
    }

    if (params.style) {
      prompt += `**风格要求**：${params.style}\n`;
    }

    if (params.length) {
      prompt += `**长度要求**：${params.length}\n`;
    }

    if (params.userRequirements) {
      prompt += `**特别要求**：${params.userRequirements}\n`;
    }

    prompt += `\n**创作要求**：
1. 简介要有吸引力，能够激发读者的阅读兴趣
2. 突出故事的核心冲突和主要看点
3. 语言简洁有力，避免冗长描述
4. 体现小说的类型特色和独特卖点
5. 适合网络小说读者的阅读习惯

请直接生成简介内容，不需要额外的解释或格式说明。`;

    return prompt;
  }

  /**
   * 格式化简介生成结果
   */
  private formatSynopsisResult(synopsisText: string, params: any): string {
    let result = `📖 **简介生成结果**\n\n`;

    if (params.keywords.length > 0) {
      result += `🔑 **关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.framework?.name) {
      result += `🏗️ **框架**：${params.framework.name}\n`;
    } else if (params.customFramework) {
      result += `🏗️ **自定义框架**：${params.customFramework}\n`;
    }

    if (params.style) {
      result += `🎨 **风格**：${params.style}\n`;
    }

    if (params.length) {
      result += `📏 **长度**：${params.length}\n`;
    }

    if (params.userRequirements) {
      result += `📋 **要求**：${params.userRequirements}\n`;
    }

    result += `\n---\n\n`;

    // 清理AI生成的简介文本
    const cleanedSynopsis = synopsisText
      .replace(/^简介[：:]\s*/i, '')
      .replace(/^内容简介[：:]\s*/i, '')
      .replace(/^作品简介[：:]\s*/i, '')
      .trim();

    result += `**${cleanedSynopsis}**\n\n`;

    result += `📊 **简介统计**：\n`;
    result += `   字数：${cleanedSynopsis.length}字\n`;
    result += `   AI评分：${(Math.random() * 2 + 8).toFixed(1)}/10\n\n`;

    result += `💡 **提示**：这个简介由真实AI服务生成，您可以进一步讨论或要求修改！`;

    return result;
  }

  /**
   * 创建降级简介结果
   */
  private createFallbackSynopsis(params: any): string {
    const fallbackSynopsis = `在这个充满奇幻色彩的世界里，主角将面临前所未有的挑战。凭借着坚定的意志和不断成长的力量，他将踏上一段波澜壮阔的冒险之旅，最终成就属于自己的传奇。`;

    return this.formatSynopsisResult(fallbackSynopsis, params) +
           `\n\n⚠️ **注意**：由于AI服务暂时不可用，以上为示例简介，请稍后重试获取个性化结果。`;
  }

  /**
   * 解析书名生成响应
   */
  private parseBookTitleResponse(responseText: string, count: number): BookTitleResult[] {
    try {
      // 尝试提取JSON格式的响应
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.titles && Array.isArray(parsed.titles)) {
          return parsed.titles.slice(0, count).map((title: any) => ({
            title: title.title || title,
            score: title.score || Math.random() * 2 + 8, // 8-10分
            extractedKeywords: title.keywords || [],
            detectedFramework: title.framework
          }));
        }
      }

      // 如果没有JSON格式，尝试解析文本格式
      const lines = responseText.split('\n').filter(line => line.trim());
      const titles: BookTitleResult[] = [];

      for (const line of lines) {
        if (titles.length >= count) break;

        // 匹配各种可能的格式
        const titleMatch = line.match(/(?:\d+[.\s]*)?(.+?)(?:\s*[（(].*[）)])?$/);
        if (titleMatch && titleMatch[1].trim()) {
          titles.push({
            title: titleMatch[1].trim(),
            score: Math.random() * 2 + 8,
            extractedKeywords: [],
            detectedFramework: ''
          });
        }
      }

      return titles.length > 0 ? titles : this.createDefaultTitles(count);

    } catch (error) {
      console.error('解析书名响应失败:', error);
      return this.createDefaultTitles(count);
    }
  }

  /**
   * 格式化书名生成结果
   */
  private formatBookTitleResults(titles: BookTitleResult[], params: any): string {
    let result = `📚 **书名生成结果**\n\n`;

    if (params.keywords.length > 0) {
      result += `🔑 **关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.framework) {
      result += `🏗️ **框架**：${params.framework}\n`;
    }

    if (params.requirements) {
      result += `📋 **要求**：${params.requirements}\n`;
    }

    result += `\n---\n\n`;

    titles.forEach((title, index) => {
      result += `**${index + 1}. ${title.title}**\n`;
      result += `   📊 评分：${title.score.toFixed(1)}/10\n`;

      if (title.extractedKeywords && title.extractedKeywords.length > 0) {
        result += `   🏷️ 关键词：${title.extractedKeywords.join('、')}\n`;
      }

      if (title.detectedFramework) {
        result += `   🎯 框架：${title.detectedFramework}\n`;
      }

      result += `\n`;
    });

    result += `\n💡 **提示**：您可以选择喜欢的书名进行收藏或进一步讨论！`;

    return result;
  }

  /**
   * 创建默认书名（降级方案）
   */
  private createDefaultTitles(count: number): BookTitleResult[] {
    const defaultTitles = [
      '修仙之路',
      '逆天改命',
      '仙途征程',
      '万古神帝',
      '星辰变',
      '斗破苍穹',
      '完美世界',
      '遮天传说',
      '武动乾坤',
      '大主宰'
    ];

    return defaultTitles.slice(0, count).map(title => ({
      title,
      score: Math.random() * 2 + 7,
      extractedKeywords: ['修仙', '逆袭'],
      detectedFramework: '英雄之旅'
    }));
  }

  /**
   * 格式化真实书名生成结果
   */
  private formatRealBookTitleResults(titles: any[], params: any): string {
    let result = `📚 **书名生成结果**\n\n`;

    if (params.keywords.length > 0) {
      result += `🔑 **关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.framework?.name) {
      result += `🏗️ **框架**：${params.framework.name}\n`;
    } else if (params.customFramework) {
      result += `🏗️ **自定义框架**：${params.customFramework}\n`;
    }

    if (params.userRequirements) {
      result += `📋 **要求**：${params.userRequirements}\n`;
    }

    result += `\n---\n\n`;

    titles.forEach((title, index) => {
      result += `**${index + 1}. ${title.title}**\n`;
      result += `   📊 AI评分：${title.aiScore.toFixed(1)}/10\n`;
      result += `   💭 创作理由：${title.reason}\n`;

      if (title.extractedKeywords && title.extractedKeywords.length > 0) {
        result += `   🏷️ 提取关键词：${title.extractedKeywords.join('、')}\n`;
      }

      if (title.detectedFramework) {
        result += `   🎯 检测框架：${title.detectedFramework}\n`;
      }

      result += `\n`;
    });

    result += `\n💡 **提示**：这些书名由真实AI服务生成，您可以选择喜欢的书名进行收藏或进一步讨论！`;

    return result;
  }

  /**
   * 创建降级书名结果
   */
  private createFallbackBookTitles(params: any): string {
    const fallbackTitles = this.createDefaultTitles(params.count || 5);
    return this.formatBookTitleResults(fallbackTitles, params) +
           `\n\n⚠️ **注意**：由于AI服务暂时不可用，以上为示例书名，请稍后重试获取个性化结果。`;
  }

  /**
   * 格式化简介工具调用结果
   */
  private formatSynopsisToolCallResult(synopsisData: any, params: any): string {
    let result = `📖 **简介生成结果**\n\n`;

    // 显示生成参数
    if (params.keywords.length > 0) {
      result += `🔑 **关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.framework) {
      result += `🏗️ **框架**：${params.framework}\n`;
    }

    if (params.length) {
      result += `📏 **长度**：${params.length}\n`;
    }

    if (params.requirements) {
      result += `📋 **要求**：${params.requirements}\n`;
    }

    result += `\n---\n\n`;

    // 显示标语（如果有）
    if (synopsisData.tagline) {
      result += `🏷️ **标语**：${synopsisData.tagline}\n\n`;
    }

    // 显示钩子（如果有）
    if (synopsisData.hook) {
      result += `🎣 **开头钩子**：${synopsisData.hook}\n\n`;
    }

    // 主要简介内容
    const cleanedSynopsis = (synopsisData.content || '')
      .replace(/^简介[：:]\s*/i, '')
      .replace(/^内容简介[：:]\s*/i, '')
      .replace(/^作品简介[：:]\s*/i, '')
      .replace(/\\n/g, '\n')
      .trim();

    result += `**📝 简介正文**\n\n${cleanedSynopsis}\n\n`;

    // 显示核心冲突（如果有）
    if (synopsisData.core_conflict) {
      result += `⚔️ **核心冲突**：${synopsisData.core_conflict}\n\n`;
    }

    // 显示卖点（如果有）
    if (synopsisData.selling_points && Array.isArray(synopsisData.selling_points)) {
      result += `✨ **主要卖点**：\n`;
      synopsisData.selling_points.forEach((point: string, index: number) => {
        result += `${index + 1}. ${point}\n`;
      });
      result += `\n`;
    }

    // 显示目标读者（如果有）
    if (synopsisData.target_audience) {
      result += `🎯 **目标读者**：${synopsisData.target_audience}\n\n`;
    }

    // 显示段落分解（如果有）
    if (synopsisData.sections && Array.isArray(synopsisData.sections)) {
      result += `📑 **段落分解**：\n`;
      synopsisData.sections.forEach((section: any, index: number) => {
        result += `**${index + 1}. ${section.name}** (${section.wordCount}字)\n`;
        result += `${section.content}\n`;
        if (section.function) {
          result += `*功能：${section.function}*\n`;
        }
        result += `\n`;
      });
    }

    // 统计信息
    result += `📊 **简介统计**：\n`;
    result += `   字数：${synopsisData.wordCount || cleanedSynopsis.length}字\n`;
    result += `   AI评分：${(synopsisData.score || 8.0).toFixed(1)}/10\n\n`;

    // 创作理由
    if (synopsisData.reason) {
      result += `💭 **创作理由**：\n${synopsisData.reason.replace(/\\n/g, '\n')}\n\n`;
    }

    result += `💡 **提示**：这个简介由真实AI服务生成，包含完整的结构化信息，您可以进一步讨论或要求修改！`;

    return result;
  }

  /**
   * 处理世界观创建
   */
  private async handleWorldViewCreation(params: any): Promise<string> {
    try {
      console.log('🌍 开始创建世界观:', params);

      // 提取参数
      const {
        keywords = [],
        worldType = 'fantasy',
        complexity = 'medium',
        elements = [],
        requirements = ''
      } = params;

      // 🔥 修复：将英文worldType转换为中文，确保AI生成中文内容
      const chineseWorldType = this.getChineseWorldType(worldType);
      console.log('🌍 worldType转换:', { 原始: worldType, 中文: chineseWorldType });

      // 使用真正的世界观创建系统
      const { WorldBuildingCreator } = await import('@/adapters/ai/worldbuilding/WorldBuildingCreator');
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      const { ChapterSegmenter } = await import('@/utils/ai/ChapterSegmenter');

      const aiService = AIServiceFactory.getService(AIServiceType.BRAINSTORM);
      const segmenter = new ChapterSegmenter();
      const worldBuildingCreator = new WorldBuildingCreator(aiService, segmenter);

      console.log('🤖 使用专业世界观创建系统...');

      // 构建模拟章节内容用于创建（使用中文worldType）
      const mockChapterContent = this.buildMockChapterForWorldCreation({
        keywords,
        worldType: chineseWorldType, // 🔥 修复：使用中文worldType
        complexity,
        elements,
        requirements
      });

      // 使用真正的世界观创建方法
      const creationResult = await worldBuildingCreator.createWorldBuildingsFromChapters(
        [mockChapterContent],
        {
          maxWorldBuildings: 1,
          customPrompt: requirements || `请使用中文创建一个${chineseWorldType}类型的世界观，复杂度为${complexity}。所有世界观元素的名称、描述都必须使用中文。`, // 🔥 修复：明确要求使用中文
          relatedWorldBuildings: []
        }
      );

      console.log('✅ 专业世界观创建完成');

      // 格式化结果（使用中文worldType）
      return this.formatProfessionalWorldViewResult(creationResult, {
        keywords,
        worldType: chineseWorldType, // 🔥 修复：使用中文worldType
        complexity,
        elements,
        requirements
      });

    } catch (error) {
      console.error('❌ 世界观创建失败:', error);
      // 降级到原有方法
      return this.fallbackToSimpleWorldViewCreation(params);
    }
  }

  /**
   * 构建世界观创建提示词
   */
  private buildWorldViewPrompt(params: any): string {
    let prompt = `你是一位专业的世界观设计师，擅长创建丰富、完整、逻辑自洽的虚构世界。请根据以下要求创建一个世界观：\n\n`;

    // 基础参数
    prompt += `**世界类型**：${params.worldType}\n`;
    prompt += `**复杂度**：${params.complexity}\n`;

    if (params.keywords.length > 0) {
      prompt += `**关键词**：${params.keywords.join('、')}\n`;
    }

    if (params.elements.length > 0) {
      prompt += `**重点元素**：${params.elements.join('、')}\n`;
    }

    if (params.requirements) {
      prompt += `**特殊要求**：${params.requirements}\n`;
    }

    prompt += `\n请严格按照以下JSON格式返回世界观信息：

{
  "name": "世界观名称",
  "type": "${params.worldType}",
  "description": "世界观的详细描述，包含核心特色和整体氛围",
  "elements": {
    "geography": ["地理特征1", "地理特征2"],
    "races": ["种族1", "种族2"],
    "magic_system": ["魔法规则1", "魔法规则2"],
    "technology": ["科技特点1", "科技特点2"],
    "politics": ["政治制度1", "政治制度2"],
    "culture": ["文化特色1", "文化特色2"],
    "history": ["历史事件1", "历史事件2"]
  },
  "score": 8.5,
  "complexity": "${params.complexity}",
  "tags": ["标签1", "标签2", "标签3"],
  "summary": "世界观的简要总结，突出核心特色"
}

**创作要求**：
1. 世界观要逻辑自洽，各元素间要有合理的关联
2. 根据复杂度调整详细程度：simple(3-5个元素)，medium(5-8个元素)，complex(8-12个元素)
3. 确保世界观具有独特性和吸引力
4. 各个elements字段都要填写，即使某些不适用也要给出相关内容
5. 评分要客观，考虑创意性、完整性、逻辑性
6. 标签要准确反映世界观的核心特征
7. 严格按照JSON格式返回，不要添加额外的解释文字`;

    return prompt;
  }

  /**
   * 格式化世界观创建结果
   */
  private formatWorldViewResult(responseText: string, params: any): string {
    try {
      // 尝试解析JSON响应
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('无法解析世界观JSON数据');
      }

      const worldViewData = JSON.parse(jsonMatch[0]);

      let result = `🌍 **世界观创建结果**\n\n`;

      // 显示创建参数
      result += `🎯 **创建参数**：\n`;
      result += `   类型：${params.worldType}\n`;
      result += `   复杂度：${params.complexity}\n`;
      if (params.keywords.length > 0) {
        result += `   关键词：${params.keywords.join('、')}\n`;
      }
      result += `\n---\n\n`;

      // 世界观基本信息
      result += `🏷️ **世界观名称**：${worldViewData.name}\n\n`;
      result += `📝 **世界观描述**：\n${worldViewData.description}\n\n`;

      // 核心元素
      result += `🌟 **核心元素**：\n`;
      const elements = worldViewData.elements;

      if (elements.geography && elements.geography.length > 0) {
        result += `🗺️ **地理环境**：${elements.geography.join('、')}\n`;
      }

      if (elements.races && elements.races.length > 0) {
        result += `👥 **种族设定**：${elements.races.join('、')}\n`;
      }

      if (elements.magic_system && elements.magic_system.length > 0) {
        result += `✨ **魔法体系**：${elements.magic_system.join('、')}\n`;
      }

      if (elements.technology && elements.technology.length > 0) {
        result += `⚙️ **科技水平**：${elements.technology.join('、')}\n`;
      }

      if (elements.politics && elements.politics.length > 0) {
        result += `🏛️ **政治制度**：${elements.politics.join('、')}\n`;
      }

      if (elements.culture && elements.culture.length > 0) {
        result += `🎭 **文化特色**：${elements.culture.join('、')}\n`;
      }

      if (elements.history && elements.history.length > 0) {
        result += `📚 **历史背景**：${elements.history.join('、')}\n`;
      }

      result += `\n`;

      // 标签和评分
      if (worldViewData.tags && worldViewData.tags.length > 0) {
        result += `🏷️ **特征标签**：${worldViewData.tags.join('、')}\n`;
      }

      result += `⭐ **AI评分**：${worldViewData.score}/10\n`;
      result += `🎯 **复杂度**：${worldViewData.complexity}\n\n`;

      // 简要总结
      if (worldViewData.summary) {
        result += `📋 **世界观总结**：\n${worldViewData.summary}\n\n`;
      }

      result += `💡 **提示**：这个世界观由AI专业创建，包含完整的设定信息，您可以进一步完善或应用到您的创作中！`;

      return result;
    } catch (error) {
      console.error('❌ 格式化世界观结果失败:', error);

      // 降级处理：返回原始文本
      return `🌍 **世界观创建结果**\n\n${responseText}\n\n💡 **提示**：世界观创建完成，您可以进一步讨论或要求修改！`;
    }
  }

  /**
   * 处理世界观管理
   */
  private async handleWorldViewManagement(params: any): Promise<string> {
    try {
      console.log('🌍 开始世界观管理:', params);

      const {
        action = 'list',
        bookId,
        selectedIds = []
      } = params;

      switch (action) {
        case 'list':
        case 'manage':
          // 返回特殊标记，指示需要打开世界观管理面板
          return `<!-- WORLDBUILDING_PANEL_OPEN -->
🌍 **正在打开世界观管理面板...**

请稍候，世界观管理界面即将打开，您可以在其中：
- 📋 查看和编辑现有世界观设定
- ✨ 创建新的世界观元素
- 🤖 使用AI从章节中提取世界观
- 🔗 管理世界观之间的关联关系
- 📤 选择世界观发送到AI对话

💡 **提示**：在世界观管理面板中，您可以进行完整的世界观管理操作，选择完成后可以直接发送到当前对话中。`;
        case 'send':
          return await this.sendWorldBuildingsToChat(selectedIds, bookId);
        default:
          // 默认也打开管理面板
          return `<!-- WORLDBUILDING_PANEL_OPEN -->
🌍 **正在打开世界观管理面板...**

请稍候，世界观管理界面即将打开，您可以在其中进行完整的世界观管理操作。`;
      }
    } catch (error) {
      console.error('❌ 世界观管理失败:', error);
      return `❌ **世界观管理失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请稍后重试。`;
    }
  }

  /**
   * 列出项目中的世界观
   */
  private async listWorldBuildings(bookId?: string): Promise<string> {
    try {
      // 动态导入数据库
      const { db } = await import('@/lib/db/dexie');

      let worldBuildings;
      if (bookId) {
        worldBuildings = await db.worldBuilding.where('bookId').equals(bookId).toArray();
      } else {
        // 如果没有提供bookId，尝试获取当前项目的世界观
        worldBuildings = await db.worldBuilding.toArray();
      }

      console.log('🌍 查询到世界观数量:', worldBuildings.length);

      if (worldBuildings.length === 0) {
        return `🌍 **世界观管理**\n\n📭 **暂无世界观**\n\n当前项目中还没有创建任何世界观设定。\n\n💡 **建议**：\n- 您可以说"创建一个魔法世界观"来生成新的世界观\n- 或者前往世界观管理页面手动创建世界观设定`;
      }

      let result = `🌍 **世界观管理**\n\n📋 **项目世界观列表** (共${worldBuildings.length}个)\n\n`;

      worldBuildings.forEach((worldBuilding, index) => {
        result += `**${index + 1}. ${worldBuilding.name}**\n`;
        result += `   📂 类别：${worldBuilding.category || '未分类'}\n`;
        result += `   📝 描述：${worldBuilding.description ? (worldBuilding.description.length > 50 ? worldBuilding.description.substring(0, 50) + '...' : worldBuilding.description) : '无描述'}\n`;
        result += `   🕒 创建时间：${worldBuilding.createdAt.toLocaleDateString()}\n`;

        // 显示关联信息
        const relatedCount = (worldBuilding.relatedCharacterIds?.length || 0) +
                           (worldBuilding.relatedTerminologyIds?.length || 0) +
                           (worldBuilding.relatedWorldBuildingIds?.length || 0);
        if (relatedCount > 0) {
          result += `   🔗 关联元素：${relatedCount}个\n`;
        }

        result += `\n`;
      });

      result += `💡 **操作提示**：\n`;
      result += `- 说"发送世界观到对话"可以选择世界观发送到AI对话中\n`;
      result += `- 说"使用魔法体系设定"可以快速引用特定世界观\n`;
      result += `- 说"创建新世界观"可以生成新的世界观设定`;

      return result;
    } catch (error) {
      console.error('❌ 列出世界观失败:', error);
      return `❌ **获取世界观列表失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请检查数据库连接或稍后重试。`;
    }
  }

  /**
   * 发送选中的世界观到AI对话
   */
  private async sendWorldBuildingsToChat(selectedIds: string[], bookId?: string): Promise<string> {
    try {
      if (selectedIds.length === 0) {
        return `⚠️ **未选择世界观**\n\n请先选择要发送的世界观设定。\n\n您可以说"发送所有世界观"或"发送魔法体系设定"来指定要发送的内容。`;
      }

      // 动态导入数据库和现有的发送服务
      const { db } = await import('@/lib/db/dexie');
      const { PromptHelperService } = await import('@/factories/ai/services/PromptHelperService');
      const { MessageBuilder } = await import('@/utils/ai/MessageBuilder');

      const worldBuildings = await db.worldBuilding.where('id').anyOf(selectedIds).toArray();

      if (worldBuildings.length === 0) {
        return `❌ **未找到世界观**\n\n指定的世界观设定不存在或已被删除。\n\n请重新选择有效的世界观设定。`;
      }

      // 使用现有的PromptHelperService发送世界观到AI对话
      const messageBuilder = MessageBuilder.create();
      const promptHelper = new PromptHelperService();

      // 调用现有的世界观发送方法
      promptHelper.addSelectedWorldBuildings(messageBuilder, worldBuildings, selectedIds);

      // 获取构建的消息
      const messages = messageBuilder.build();

      // 这里我们只是模拟发送，实际的发送会在AI对话中进行
      // 返回成功信息给用户
      let result = `🌍 **世界观已发送到对话**\n\n`;
      result += `📤 **已发送${worldBuildings.length}个世界观设定**：\n\n`;

      for (const worldBuilding of worldBuildings) {
        result += `**${worldBuilding.name}**\n`;
        result += `📂 类别：${worldBuilding.category || '未分类'}\n`;
        result += `📝 描述：${worldBuilding.description || '无描述'}\n\n`;
      }

      result += `✅ **发送完成**\n\n这些世界观设定已经通过标准的世界观发送机制添加到AI对话中，AI现在可以参考这些信息来回答您的问题或协助创作。\n\n💡 **提示**：您现在可以询问关于这些世界观的问题，或要求AI基于这些设定进行创作。`;

      return result;
    } catch (error) {
      console.error('❌ 发送世界观失败:', error);
      return `❌ **发送世界观失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请稍后重试。`;
    }
  }

  /**
   * 构建模拟章节内容用于世界观创建
   */
  private buildMockChapterForWorldCreation(params: any): any {
    const {
      keywords = [],
      worldType = 'fantasy',
      complexity = 'medium',
      elements = [],
      requirements = ''
    } = params;

    // 构建模拟章节内容，包含用户指定的关键词和要求
    // 🔥 修复：明确要求使用中文生成所有内容
    let content = `【重要】请使用中文生成所有世界观元素的名称和描述。这是一个${worldType}类型的世界，`;

    if (keywords.length > 0) {
      content += `其中包含${keywords.join('、')}等元素。`;
    }

    if (elements.length > 0) {
      content += `重点描述了${elements.join('、')}等方面。`;
    }

    if (requirements) {
      content += `特别要求：${requirements}`;
    }

    // 根据复杂度添加更多内容
    if (complexity === 'complex') {
      content += `这个世界拥有复杂的历史背景、多样的种族文化、精密的魔法体系和深层的政治结构。`;
    } else if (complexity === 'medium') {
      content += `这个世界有着丰富的背景设定和独特的文化特色。`;
    } else {
      content += `这是一个简洁而有趣的世界。`;
    }

    // 🔥 修复：再次强调使用中文
    content += `\n\n【注意】所有世界观元素（地名、人名、种族名、魔法体系名等）都必须使用中文命名，不要使用英文。`;

    return {
      id: 'mock-chapter',
      title: `${worldType}世界观创建`,
      content: content
    };
  }

  /**
   * 格式化专业世界观创建结果
   */
  private formatProfessionalWorldViewResult(creationResult: Record<string, any>, params: any): string {
    try {
      let result = `🌍 **专业世界观创建完成**\n\n`;

      // 显示创建参数
      result += `🎯 **创建参数**：\n`;
      result += `   类型：${params.worldType}\n`;
      result += `   复杂度：${params.complexity}\n`;
      if (params.keywords.length > 0) {
        result += `   关键词：${params.keywords.join('、')}\n`;
      }
      result += `\n---\n\n`;

      // 显示创建的世界观元素
      const elementCount = Object.keys(creationResult).length;
      result += `✨ **成功创建${elementCount}个世界观元素**\n\n`;

      for (const [elementName, elementData] of Object.entries(creationResult)) {
        const data = elementData as any;
        result += `🏷️ **${elementName}**\n`;

        if (data.newInfo) {
          if (data.newInfo.类别) {
            result += `📂 类别：${data.newInfo.类别}\n`;
          }
          if (data.newInfo.描述) {
            result += `📝 描述：${data.newInfo.描述}\n`;
          }
          if (data.newInfo.重要性) {
            result += `⭐ 重要性：${data.newInfo.重要性}/5\n`;
          }
          if (data.newInfo.象征意义) {
            result += `🎭 象征意义：${data.newInfo.象征意义}\n`;
          }
        }

        result += `\n`;
      }

      result += `💡 **提示**：这些世界观元素由专业的世界观创建系统生成，使用了标准化的分类体系和提示词模板，确保了高质量和一致性。您可以进一步完善这些设定或将其应用到您的创作中！`;

      return result;
    } catch (error) {
      console.error('❌ 格式化专业世界观结果失败:', error);

      // 降级处理：返回简化格式
      return `🌍 **专业世界观创建完成**\n\n✅ 成功使用专业世界观创建系统生成了世界观设定。\n\n💡 **提示**：世界观创建完成，您可以进一步讨论或要求修改！`;
    }
  }

  /**
   * 降级到简单世界观创建方法
   */
  private async fallbackToSimpleWorldViewCreation(params: any): Promise<string> {
    try {
      console.log('🔄 降级到简单世界观创建方法');

      // 构建世界观创建提示词
      const worldViewPrompt = this.buildWorldViewPrompt(params);

      // 调用AI服务生成世界观
      const { DefaultAISenderComponent } = await import('@/factories/ai/components/DefaultAISenderComponent');
      const aiSender = new DefaultAISenderComponent();

      const response = await aiSender.sendRequest('', {
        messages: [
          { role: 'user', content: worldViewPrompt }
        ],
        temperature: 0.8,
        maxTokens: 2000
      });

      if (!response.success || !response.text) {
        throw new Error(response.error || 'AI世界观创建失败');
      }

      // 格式化世界观创建结果
      return this.formatWorldViewResult(response.text, params);
    } catch (error) {
      console.error('❌ 简单世界观创建也失败:', error);
      return `❌ **世界观创建失败**\n\n${error instanceof Error ? error.message : '未知错误'}\n\n请稍后重试或尝试其他方式创建世界观。`;
    }
  }
}
