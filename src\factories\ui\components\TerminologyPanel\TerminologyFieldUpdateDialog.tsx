"use client";

import React, { useState, useEffect } from 'react';
import { Terminology } from '@/lib/db/dexie';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { PromptCategory } from '@/lib/db/dexie';
import { ConfirmContentDialog } from './ConfirmContentDialog';

interface TerminologyFieldUpdateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (terminology: Partial<Terminology>) => Promise<void>;
  terminology: Terminology;
  field: string;
  fieldLabel: string;
  chapters?: any[]; // 章节列表
  terminologies?: Terminology[]; // 所有术语列表，用于关联选择
  bookId?: string; // 书籍ID
}

/**
 * 术语字段更新对话框组件
 */
export const TerminologyFieldUpdateDialog: React.FC<TerminologyFieldUpdateDialogProps> = ({
  isOpen,
  onClose,
  onUpdate,
  terminology,
  field,
  fieldLabel,
  chapters = [],
  terminologies = [],
  bookId
}) => {
  // 字段值
  const [fieldValue, setFieldValue] = useState<string>('');
  // 生成的值
  const [generatedValue, setGeneratedValue] = useState<string>('');
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 自定义提示词状态
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);
  // 确认弹窗状态
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  // 流式响应状态
  const [streamResponse, setStreamResponse] = useState('');
  // 选中的章节ID
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  // 章节范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');
  // 选中的关联术语ID
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);
  // AI相关状态
  const [aiSender] = useState(() => new DefaultAISenderComponent());
  const [apiSettings] = useState(() => {
    const settingsFactory = createSettingsFactory();
    return settingsFactory.createAPISettingsDialogComponent();
  });

  // 当对话框打开时，重置状态
  useEffect(() => {
    if (isOpen) {
      // 根据字段名获取当前值
      let currentValue = '';
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (parent === 'attributes' && terminology.attributes) {
          currentValue = terminology.attributes[child] || '';
        }
      } else {
        currentValue = (terminology as any)[field] || '';
      }

      setFieldValue(currentValue);
      setGeneratedValue('');
      setCustomPrompt('');
      setStreamResponse('');
      setSelectedChapterIds(terminology.extractedFromChapterIds || []);
      setSelectedTerminologyIds(terminology.relatedTerminologyIds || []);
      setIsConfirmDialogOpen(false);
      setError(null);
    }
  }, [isOpen, terminology, field]);

  // 处理提示词模板选择
  const handleSelectTemplate = (template: any) => {
    setCustomPrompt(template.content);
    setIsPromptManagerOpen(false);
  };

  // 处理章节选择
  const handleChapterSelection = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 处理术语选择
  const handleTerminologySelection = (terminologyId: string) => {
    setSelectedTerminologyIds(prev => {
      if (prev.includes(terminologyId)) {
        return prev.filter(id => id !== terminologyId);
      } else {
        return [...prev, terminologyId];
      }
    });
  };

  // 处理应用生成内容
  const handleApply = async () => {
    if (!generatedValue) {
      setError('没有可应用的生成内容');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 只有在更新名称字段时才验证名称不为空
      if (field === 'name' && !generatedValue.trim()) {
        setError('术语名称不能为空');
        return;
      }

      // 确保保留原有的术语名称和其他重要字段
      const updates: Partial<Terminology> = {
        id: terminology.id,
        name: terminology.name, // 明确保留术语名称
        extractedFromChapterIds: selectedChapterIds,
        relatedTerminologyIds: selectedTerminologyIds,
        // 保留其他重要字段
        category: terminology.category,
        bookId: terminology.bookId,
        description: terminology.description
      };

      // 根据字段名设置更新值
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (parent === 'attributes') {
          // 获取当前值
          const currentValue = terminology.attributes?.[child] || '';

          // 计算更新序号
          const updateCount = currentValue.match(/【更新补充\d+】/g)?.length || 0;
          const updateNumber = updateCount + 1;

          // 如果已有值，则追加内容而不是替换，并添加更新标记
          const updatedValue = currentValue
            ? `${currentValue}\n\n【更新补充${updateNumber}】\n${generatedValue}`
            : generatedValue;

          updates.attributes = {
            ...(terminology.attributes || {}),
            [child]: updatedValue
          };
        }
      } else {
        // 获取当前值
        const currentValue = (terminology as any)[field] || '';

        // 如果是名称字段，直接使用生成的值
        if (field === 'name') {
          const updatedValue = generatedValue;
          (updates as any)[field] = updatedValue;
        } else {
          // 计算更新序号
          const updateCount = currentValue.match(/【更新补充\d+】/g)?.length || 0;
          const updateNumber = updateCount + 1;

          // 如果已有值，则追加内容而不是替换，并添加更新标记
          const updatedValue = currentValue
            ? `${currentValue}\n\n【更新补充${updateNumber}】\n${generatedValue}`
            : generatedValue;

          (updates as any)[field] = updatedValue;
        }
      }

      // 添加调试日志，确保更新包含所有必要字段
      console.log('更新术语数据:', updates);

      await onUpdate(updates);

      // 更新字段值，显示已应用
      setFieldValue(generatedValue);

      // 添加成功消息
      setStreamResponse(prev => prev + `\n\n✅ 已成功应用并更新${fieldLabel}！\n`);

      // 隐藏应用按钮
      setShowApplyButton(false);

      // 不关闭对话框，让用户可以继续生成或查看结果
    } catch (err) {
      console.error(`更新${fieldLabel}失败:`, err);
      setError(err instanceof Error ? err.message : `更新${fieldLabel}时发生错误`);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理范围选择
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    if (!rangeStart || !rangeEnd || !chapters || chapters.length === 0) {
      alert('请输入有效的章节范围');
      return;
    }

    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      alert('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      alert('起始章节不能大于结束章节');
      return;
    }

    if (start > chapters.length || end > chapters.length) {
      alert(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      alert('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  // 处理AI生成
  const handleGenerate = async () => {
    setIsLoading(true);
    setError(null);
    setStreamResponse('');

    try {
      // 获取API设置
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      // 构建提示词
      const messageBuilder = new MessageBuilder();

      // 系统消息
      messageBuilder.addSystemMessage(`你是一个专业的小说创作助手，擅长帮助作者完善术语定义和描述。你的任务是根据提供的术语信息，追加或补充特定字段的内容。

重要规则：
1. 只生成新的、追加的内容，不要重复或返回已有的内容
2. 不要捏造与章节内容不符的信息
3. 保持与原有内容的风格一致性
4. 内容应该是对现有内容的扩展和丰富，而不是替换
5. 如果提供了章节内容，请严格基于章节内容进行创作，不要添加章节中没有的信息
6. 如果提供了关联术语，请确保新内容与这些术语保持一致性`);

      // 添加助手确认消息
      messageBuilder.addAssistantMessage(`我将帮助你完善术语的${fieldLabel}。`);

      // 用户消息 - 提供术语信息
      let userPrompt = `请帮我完善以下术语的${fieldLabel}：\n\n`;
      userPrompt += `术语名称：${terminology.name}\n`;
      if (terminology.category) userPrompt += `类别：${terminology.category}\n`;
      if (terminology.description) userPrompt += `描述：${terminology.description}\n`;
      userPrompt += `要更新的字段：${fieldLabel}\n`;

      // 添加当前字段值（如果有）
      if (fieldValue) {
        userPrompt += `\n当前${fieldLabel}：${fieldValue}\n`;
        userPrompt += `\n请基于以上信息，提供更加丰富、详细的${fieldLabel}。

重要说明：
1. 请只生成新的、追加的内容，不要重复或返回已有的${fieldLabel}
2. 生成的内容应该与现有内容无缝衔接，保持风格一致
3. 如果提供了章节内容，请严格基于章节内容进行创作，不要捏造与章节内容不符的信息
4. 内容应该是对现有内容的扩展和丰富，而不是替换`;
      } else {
        userPrompt += `\n请基于以上信息，创建一个丰富、详细的${fieldLabel}。

重要说明：
1. 内容应该符合术语的整体风格和设定
2. 如果提供了章节内容，请严格基于章节内容进行创作，不要捏造与章节内容不符的信息
3. 如果提供了关联术语，请确保内容与这些术语保持一致性`;
      }

      messageBuilder.addUserMessage(userPrompt);

      // 添加关联术语
      if (selectedTerminologyIds.length > 0) {
        // 先添加一条说明消息
        messageBuilder.addAssistantMessage(`以下是已有的关联术语，我会在创建内容时考虑这些术语的关联性：`);

        // 为每个关联术语添加单独的消息
        const relatedTerms = terminologies.filter(term => selectedTerminologyIds.includes(term.id || ''));
        for (const term of relatedTerms) {
          messageBuilder.addAssistantMessage(`术语: ${term.name}\n描述: ${term.description || '无描述'}`);
        }
      }

      // 添加"开始"消息变量，稍后会使用
      let startMessage = `现在，请开始为术语"${terminology.name}"生成${fieldLabel}，，不要解释性语言，只根据章节内容，和需求返回需求的内容。`;

      // 添加选中的章节内容
      if (selectedChapterIds.length > 0) {
        // 获取选中章节的内容
        const selectedChapters = chapters.filter(chapter => selectedChapterIds.includes(chapter.id));

        if (selectedChapters.length > 0) {
          // 如果有章节内容，更新开始消息
          startMessage = `现在，请基于以上章节内容，为术语"${terminology.name}"生成${fieldLabel}。不要解释性语言，只根据章节内容，和需求返回需求的内容。`;
          // 创建章节分段器
          const segmenter = {
            segmentChapter: (text: string) => {
              // 简单实现，按段落分割
              return text.split('\n\n').filter(segment => segment.trim().length > 0);
            }
          };

          // 添加一条说明消息，表示将要处理多个章节
          messageBuilder.addUserMessage(`以下是多个章节的内容，请逐一分析：`);

          // 为每个章节进行智能分段并添加单独的消息
          let globalSegmentIndex = 0;
          let totalSegmentsCount = 0;

          // 先计算总段落数
          for (const chapter of selectedChapters) {
            const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content}`;
            const segments = segmenter.segmentChapter(chapterText);
            totalSegmentsCount += segments.length;
          }

          // 为每个章节的每个段落添加单独的消息
          for (const chapter of selectedChapters) {
            const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content}`;
            const segments = segmenter.segmentChapter(chapterText);

            for (let i = 0; i < segments.length; i++) {
              globalSegmentIndex++;
              const segment = segments[i];
              const segmentWordCount = segment.split(/\s+/).length;

              // 按照要求的格式构建消息：第x章，第几段，多少字，内容
              const segmentPrompt = `第${chapter.title || `第${chapter.chapterNumber || '?'}章`}，第${i+1}/${segments.length}段，${segmentWordCount}字，内容：\n\n${segment}`;
              messageBuilder.addUserMessage(segmentPrompt);
              messageBuilder.addAssistantMessage(`我已阅读并分析了"${chapter.title || `第${chapter.chapterNumber || '?'}章`}"章节的第${i+1}段内容（总进度：${globalSegmentIndex}/${totalSegmentsCount}）。`);
            }
          }

          // 添加一条总结消息
          messageBuilder.addAssistantMessage(`我已完成所有章节的分析。我不需求解释性语言，只返回有关${terminology.name} 的内容，没有的话我就留空`);

          // 在章节处理后添加开始消息
          messageBuilder.addUserMessage(startMessage);
        }
      } else {
        // 如果没有选择章节，也添加开始消息
        messageBuilder.addUserMessage(startMessage);
      }

      // 如果有自定义提示词，添加到最后一条消息
      if (customPrompt) {
        messageBuilder.addUserMessage(`${customPrompt}`);
      }

      // 发送请求
      setStreamResponse(prev => prev + `正在生成${fieldLabel}...\n`);

      const result = await aiSender.sendRequest('', {
        messages: messageBuilder.build(),
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.7,
        max_tokens: 1000,
        stream: true,
        onStreamChunk: (chunk: string) => {
          setStreamResponse(prev => prev + chunk);
        }
      });

      // 保存生成的值，但不自动应用
      const generated = result.text.trim();
      setGeneratedValue(generated);
      // 清空自定义提示词
      setCustomPrompt('');

      // 显示确认弹窗，让用户选择是否应用生成结果
      setIsConfirmDialogOpen(true);

      // 添加成功消息
      setStreamResponse(prev => prev + `\n\n✅ 生成完成！请在弹窗中选择是否应用生成的内容。\n`);

    } catch (err) {
      console.error(`生成${fieldLabel}失败:`, err);
      setError(err instanceof Error ? err.message : `生成${fieldLabel}时发生错误`);
    } finally {
      setIsLoading(false);
    }
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[600px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>更新{fieldLabel}</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-6">
            {/* 术语信息 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h3 className="font-medium text-blue-800 mb-1">术语信息</h3>
              <p className="text-blue-700"><span className="font-medium">名称:</span> {terminology.name}</p>
              {terminology.category && (
                <p className="text-blue-700"><span className="font-medium">类别:</span> {terminology.category}</p>
              )}
              {terminology.description && (
                <p className="text-blue-700 line-clamp-2"><span className="font-medium">描述:</span> {terminology.description}</p>
              )}
            </div>

            {/* 关联章节 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-gray-700 font-medium">关联章节</label>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                      min="1"
                      max={chapters.length}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                      min="1"
                      max={chapters.length}
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRangeSelect('select')}
                    className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    选择
                  </button>
                  <button
                    type="button"
                    onClick={() => handleRangeSelect('deselect')}
                    className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    取消选择
                  </button>
                </div>
              </div>
              <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[150px] overflow-y-auto"
                style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
              >
                {chapters.length === 0 ? (
                  <div className="text-gray-500 text-center py-2">
                    没有可关联的章节
                  </div>
                ) : (
                  <div className="space-y-1">
                    {chapters.map(chapter => (
                      <div key={chapter.id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`chapter-${chapter.id}`}
                          checked={selectedChapterIds.includes(chapter.id)}
                          onChange={() => handleChapterSelection(chapter.id)}
                          className="mr-2 h-4 w-4"
                        />
                        <label htmlFor={`chapter-${chapter.id}`} className="text-gray-700 truncate">
                          {chapter.title || `第${chapter.chapterNumber || '?'}章`}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                选择与术语相关的章节，可以帮助AI更好地理解术语的上下文
              </p>
            </div>

            {/* 关联术语 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-gray-700 font-medium">关联术语</label>
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setSelectedTerminologyIds(terminologies.filter(t => t.id !== terminology.id).map(t => t.id || ''))}
                    className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    全选
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedTerminologyIds([])}
                    className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    清空
                  </button>
                </div>
              </div>
              <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[150px] overflow-y-auto"
                style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
              >
                {terminologies.length <= 1 ? (
                  <div className="text-gray-500 text-center py-2">
                    没有可关联的术语
                  </div>
                ) : (
                  <div className="space-y-1">
                    {terminologies
                      .filter(term => term.id !== terminology.id) // 排除当前术语
                      .map(term => (
                        <div key={term.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`term-${term.id}`}
                            checked={selectedTerminologyIds.includes(term.id || '')}
                            onChange={() => handleTerminologySelection(term.id || '')}
                            className="mr-2 h-4 w-4"
                          />
                          <label htmlFor={`term-${term.id}`} className="text-gray-700 truncate">
                            {term.name} {term.category ? `(${term.category})` : ''}
                          </label>
                        </div>
                      ))}
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                选择与此术语相关的其他术语，建立术语之间的关联
              </p>
            </div>

            {/* 自定义提示词 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-gray-700 font-medium">自定义提示词</label>
                <button
                  type="button"
                  onClick={() => setIsPromptManagerOpen(true)}
                  className="px-2 py-1 text-xs rounded-md transition-colors"
                  style={{
                    backgroundColor: 'var(--color-primary)',
                    color: 'white',
                    boxShadow: '0 1px 3px rgba(139, 69, 19, 0.2)'
                  }}
                >
                  选择模板
                </button>
              </div>
              <textarea
                id="custom-prompt-input"
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入自定义提示词，用于指导AI如何生成内容..."
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderColor: 'rgba(139, 69, 19, 0.2)'
                }}
              />
              <p className="text-sm text-gray-500 mt-1">
                在这里输入自定义提示词，用于指导AI如何生成内容
              </p>
            </div>



            {/* 流式响应 */}
            {streamResponse && (
              <div>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>AI响应</h3>
                <div
                  className="p-3 border rounded-lg whitespace-pre-wrap max-h-[200px] overflow-y-auto"
                  style={{
                    borderColor: 'rgba(139, 69, 19, 0.2)',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    fontFamily: 'monospace',
                    fontSize: '0.9rem'
                  }}
                >
                  {streamResponse}
                </div>
              </div>
            )}

            {/* 错误信息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
          </div>
        </div>

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </button>

          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
            }}
            onClick={handleGenerate}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                生成中...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                AI生成
              </>
            )}
          </button>
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.TERMINOLOGY}
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />

      {/* 确认内容对话框 */}
      <ConfirmContentDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
        onConfirm={handleApply}
        title={`确认${fieldLabel}`}
        content={generatedValue}
        fieldLabel={fieldLabel}
      />
    </div>
  );
};
