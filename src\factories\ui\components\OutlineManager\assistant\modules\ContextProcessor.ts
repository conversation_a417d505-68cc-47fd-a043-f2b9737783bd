/**
 * 上下文处理模块
 * 负责处理大纲上下文信息和上下文链路
 */

import { ConversationMessage, ContextChain, OutlineStats, OutlineNode } from '../types/SharedTypes';

export class ContextProcessor {
  private static instance: ContextProcessor;

  private constructor() {}

  public static getInstance(): ContextProcessor {
    if (!ContextProcessor.instance) {
      ContextProcessor.instance = new ContextProcessor();
    }
    return ContextProcessor.instance;
  }

  /**
   * 构建上下文链路信息消息
   */
  buildContextChainMessages(contextChains: ContextChain[]): ConversationMessage[] {
    const messages: ConversationMessage[] = [];

    if (!contextChains || contextChains.length === 0) {
      messages.push({
        role: 'system',
        content: `【上下文链路信息】
当前没有特定的上下文链路信息，节点将基于整体框架结构进行创建。
请根据已有的章节和剧情节点确定新节点的合适位置和关联关系。`
      });
    } else {
      // 添加链路总览
      messages.push({
        role: 'system',
        content: `【上下文链路总览】
检测到${contextChains.length}条上下文链路，这些链路将指导新节点的创建和关联：`
      });

      // 为每个链路添加详细信息
      contextChains.forEach((chain, index) => {
        messages.push({
          role: 'system',
          content: `【上下文链路${index + 1}】
层级关系：${chain.hierarchy || '未指定'}
序列关系：${chain.sequence || '未指定'}
关联节点：${chain.relatedNodes?.join('、') || '无'}
链路描述：${chain.description || '无特殊说明'}

请确保新创建的节点与此链路保持逻辑一致性。`
        });
      });

      // 添加链路使用指导
      messages.push({
        role: 'assistant',
        content: `我已完整理解所有上下文链路信息。我承诺在创建新节点时：
1. 绝不创建孤立的节点，每个新节点都将与现有结构紧密关联
2. 充分利用层级链路信息确定正确的父子关系和兄弟关系
3. 基于序列链路信息确保前后内容的逻辑连贯和自然过渡
4. 在回复中明确说明新节点如何与上下文连接
5. 确保整体故事的连续性和完整性`
      });
    }

    return messages;
  }

  /**
   * 构建大纲上下文信息
   */
  buildOutlineContext(outline: any): string {
    if (!outline || !outline.nodes) {
      return '\n\n**当前大纲**：空大纲';
    }

    const outlineStructure = this.buildOutlineStructure(outline.nodes, 0);
    const stats = this.getOutlineStats(outline.nodes);

    return `\n\n**当前大纲结构**：
${outlineStructure}

**大纲统计**：
- 总节点数：${stats.totalNodes}
- 章节数：${stats.chapters}
- 剧情节点数：${stats.scenes}
- 对话节点数：${stats.sections}
- 最大层级：${stats.maxLevel}`;
  }

  /**
   * 构建大纲结构树
   */
  buildOutlineStructure(nodes: any[], level: number = 0): string {
    if (!nodes || nodes.length === 0) {
      return '  '.repeat(level) + '（空）';
    }

    let structure = '';
    
    nodes.forEach((node, index) => {
      const indent = '  '.repeat(level);
      const prefix = level === 0 ? '📖' : level === 1 ? '📝' : level === 2 ? '🎬' : '💬';
      const title = node.title || node.name || '未命名节点';
      const nodeType = node.type ? `[${node.type}]` : '';
      
      structure += `${indent}${prefix} ${title} ${nodeType}\n`;
      
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        structure += this.buildOutlineStructure(node.children, level + 1);
      }
    });

    return structure;
  }

  /**
   * 获取大纲统计信息
   */
  getOutlineStats(nodes: any[]): OutlineStats {
    const stats: OutlineStats = {
      totalNodes: 0,
      chapters: 0,
      scenes: 0,
      sections: 0,
      maxLevel: 0
    };

    const countNodes = (nodeList: any[], currentLevel: number = 0) => {
      if (!nodeList) return;

      stats.maxLevel = Math.max(stats.maxLevel, currentLevel);

      nodeList.forEach(node => {
        stats.totalNodes++;

        // 根据节点类型统计
        switch (node.type) {
          case 'chapter':
            stats.chapters++;
            break;
          case 'scene':
            stats.scenes++;
            break;
          case 'section':
          case 'dialogue':
            stats.sections++;
            break;
        }

        // 递归统计子节点
        if (node.children && node.children.length > 0) {
          countNodes(node.children, currentLevel + 1);
        }
      });
    };

    countNodes(nodes);
    return stats;
  }

  /**
   * 分析大纲完整性
   */
  analyzeOutlineCompleteness(outline: any): {
    completeness: number;
    suggestions: string[];
    missingElements: string[];
  } {
    const suggestions: string[] = [];
    const missingElements: string[] = [];
    let completeness = 0;

    if (!outline || !outline.nodes || outline.nodes.length === 0) {
      return {
        completeness: 0,
        suggestions: ['建议先创建基础的章节结构'],
        missingElements: ['章节结构', '剧情节点', '角色设定']
      };
    }

    const stats = this.getOutlineStats(outline.nodes);
    
    // 基础结构检查
    if (stats.chapters > 0) {
      completeness += 30;
    } else {
      missingElements.push('章节结构');
      suggestions.push('建议添加章节来组织故事结构');
    }

    if (stats.scenes > 0) {
      completeness += 40;
    } else {
      missingElements.push('场景节点');
      suggestions.push('建议在章节下添加具体的场景节点');
    }

    if (stats.sections > 0) {
      completeness += 20;
    } else {
      missingElements.push('详细内容');
      suggestions.push('建议添加更详细的段落或对话节点');
    }

    // 结构平衡检查
    if (stats.chapters > 0 && stats.scenes / stats.chapters >= 2) {
      completeness += 10;
    } else {
      suggestions.push('建议每个章节包含2-5个场景节点');
    }

    return {
      completeness: Math.min(completeness, 100),
      suggestions,
      missingElements
    };
  }

  /**
   * 获取上下文摘要
   */
  getContextSummary(outline: any, contextChains: ContextChain[] = []): string {
    const stats = outline ? this.getOutlineStats(outline.nodes || []) : null;
    const chainCount = contextChains.length;

    let summary = '【上下文摘要】\n';
    
    if (stats) {
      summary += `大纲状态：${stats.totalNodes}个节点（${stats.chapters}章节，${stats.scenes}场景）\n`;
    } else {
      summary += '大纲状态：空大纲\n';
    }

    if (chainCount > 0) {
      summary += `链路信息：${chainCount}条上下文链路\n`;
    } else {
      summary += '链路信息：无特定链路\n';
    }

    return summary;
  }

  /**
   * 验证上下文一致性
   */
  validateContextConsistency(outline: any, contextChains: ContextChain[]): {
    isConsistent: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查大纲结构
    if (!outline || !outline.nodes || outline.nodes.length === 0) {
      issues.push('大纲结构为空');
      recommendations.push('建议先创建基础的大纲结构');
    }

    // 检查上下文链路
    if (contextChains && contextChains.length > 0) {
      contextChains.forEach((chain, index) => {
        if (!chain.hierarchy && !chain.sequence) {
          issues.push(`链路${index + 1}缺少层级或序列信息`);
          recommendations.push(`为链路${index + 1}补充完整的关系信息`);
        }
      });
    }

    return {
      isConsistent: issues.length === 0,
      issues,
      recommendations
    };
  }
}
