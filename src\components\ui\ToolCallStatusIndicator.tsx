"use client";

import React from 'react';
import { motion } from 'framer-motion';

export type ToolCallStatus = 'executing' | 'completed' | 'error';

interface ToolCallStatusIndicatorProps {
  status: ToolCallStatus;
  message?: string;
  className?: string;
}

// 执行中状态 - 齿轮旋转动画
const ExecutingIcon: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div className={`relative ${className}`}>
    <svg width="16" height="16" viewBox="0 0 16 16" className="absolute">
      {/* 主齿轮 */}
      <motion.g
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        style={{ transformOrigin: "8px 8px" }}
      >
        <path
          d="M8 2L9 4H11L10 6L12 7L10 9L11 11H9L8 13L7 11H5L6 9L4 7L6 6L5 4H7L8 2Z"
          fill="url(#executingGradient)"
          stroke="none"
        />
      </motion.g>
      
      {/* 副齿轮 */}
      <motion.g
        animate={{ rotate: -240 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        style={{ transformOrigin: "12px 4px" }}
      >
        <path
          d="M12 1L12.5 2H13.5L13 3L14 3.5L13 4.5L13.5 5H12.5L12 6L11.5 5H10.5L11 4.5L10 3.5L11 3L10.5 2H11.5L12 1Z"
          fill="url(#executingGradient2)"
          stroke="none"
        />
      </motion.g>

      {/* 渐变定义 */}
      <defs>
        <linearGradient id="executingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#8B5CF6" />
          <stop offset="100%" stopColor="#F97316" />
        </linearGradient>
        <linearGradient id="executingGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#F97316" />
          <stop offset="100%" stopColor="#8B5CF6" />
        </linearGradient>
      </defs>
    </svg>
  </div>
);

// 完成状态 - 勾选动画
const CompletedIcon: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div className={`relative ${className}`}>
    <svg width="16" height="16" viewBox="0 0 16 16">
      {/* 圆形背景动画 */}
      <motion.circle
        cx="8"
        cy="8"
        r="7"
        fill="transparent"
        stroke="none"
        initial={{ fill: "transparent" }}
        animate={{ fill: "#10B981" }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
      
      {/* 勾选路径动画 */}
      <motion.path
        d="M5 8L7 10L11 6"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.4, delay: 0.2, ease: "easeOut" }}
      />
      
      {/* 整体缩放动画 */}
      <motion.g
        initial={{ scale: 1 }}
        animate={{ scale: [1, 1.05, 1] }}
        transition={{ duration: 0.2, delay: 0.6, ease: "easeInOut" }}
        style={{ transformOrigin: "8px 8px" }}
      >
        <circle cx="8" cy="8" r="0" fill="transparent" />
      </motion.g>
    </svg>
  </div>
);

// 错误状态 - X标记动画
const ErrorIcon: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div className={`relative ${className}`}>
    <svg width="16" height="16" viewBox="0 0 16 16">
      {/* 圆形背景动画 */}
      <motion.circle
        cx="8"
        cy="8"
        r="7"
        fill="transparent"
        stroke="none"
        initial={{ fill: "transparent" }}
        animate={{ fill: "#EF4444" }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
      
      {/* X标记第一条线 */}
      <motion.path
        d="M5.5 5.5L10.5 10.5"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.2, delay: 0.2, ease: "easeOut" }}
      />
      
      {/* X标记第二条线 */}
      <motion.path
        d="M10.5 5.5L5.5 10.5"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.2, delay: 0.3, ease: "easeOut" }}
      />
      
      {/* 震动效果 */}
      <motion.g
        animate={{ x: [0, -1, 1, -1, 1, 0] }}
        transition={{ duration: 0.4, delay: 0.5, ease: "easeInOut" }}
        style={{ transformOrigin: "8px 8px" }}
      >
        <circle cx="8" cy="8" r="0" fill="transparent" />
      </motion.g>
    </svg>
  </div>
);

// 主组件
export const ToolCallStatusIndicator: React.FC<ToolCallStatusIndicatorProps> = ({
  status,
  message,
  className = ""
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'executing':
        return {
          icon: <ExecutingIcon />,
          text: message || '工具调用执行中...',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700'
        };
      case 'completed':
        return {
          icon: <CompletedIcon />,
          text: message || '工具调用执行完成',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-700'
        };
      case 'error':
        return {
          icon: <ErrorIcon />,
          text: message || '工具调用执行失败',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-700'
        };
      default:
        return {
          icon: <ExecutingIcon />,
          text: message || '工具调用中...',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-700'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <motion.div
      className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}
      initial={{ opacity: 0, y: 10, filter: 'blur(4px)' }}
      animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
      exit={{ opacity: 0, y: -10, filter: 'blur(4px)' }}
      transition={{ type: 'spring', damping: 25, stiffness: 200 }}
    >
      {/* 状态图标 */}
      <div className="flex-shrink-0">
        {config.icon}
      </div>
      
      {/* 状态文字 */}
      <span className={`text-sm font-medium ${config.textColor}`}>
        {config.text}
      </span>
    </motion.div>
  );
};

export default ToolCallStatusIndicator;
