/**
 * AI注释服务 - 向后兼容包装器
 * 重新导出新的模块化AI注释服务，保持向后兼容性
 */

// 重新导出新的模块化服务
export {
  AIAnnotationService,
  createAIAnnotationService,
  PromptBuilder,
  ResponseParser,
  StreamProcessor
} from './annotation';

export type {
  TextSegment,
  Sentence,
  ModificationType,
  ProblemCategory,
  SeverityLevel,
  ImpactLevel,
  ProcessingStatus,
  Alternative,
  SentenceContext,
  AnnotationCallbacks,
  AnnotationResult,
  AISuggestionResponse,
  SuggestionItem,
  AIAnnotationServiceInterface,
  MessageBuilderInterface
} from './annotation';

// 向后兼容的默认导出
import { createAIAnnotationService } from './annotation';
export default createAIAnnotationService;