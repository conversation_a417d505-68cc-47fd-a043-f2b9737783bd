@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入自定义样式 */
@import '../styles/icons.css';
@import '../styles/enhanced-panel.css';

:root {
  /* 基础色彩 */
  --color-primary-bg: #F5F2E9;      /* 米黄色背景 */
  --color-primary: #8B4513;         /* 深棕色主色 */
  --color-primary-light: #A05A2C;   /* 浅深棕色 */
  --color-primary-dark: #6B3100;    /* 暗深棕色 */
  --color-primary-rgb: 139, 69, 19; /* 深棕色主色RGB */

  --color-secondary: #D2B48C;       /* 淡棕色辅助色 */
  --color-secondary-light: #E0C9A6; /* 浅淡棕色 */
  --color-secondary-dark: #B8966E;  /* 暗淡棕色 */
  --color-secondary-rgb: 210, 180, 140; /* 淡棕色辅助色RGB */

  --color-success: #556B2F;         /* 橄榄绿成功色 */
  --color-success-light: #6B8545;   /* 浅橄榄绿 */
  --color-success-dark: #445626;    /* 暗橄榄绿 */
  --color-success-rgb: 85, 107, 47; /* 橄榄绿成功色RGB */

  --color-danger: #B22222;          /* 砖红色危险色 */
  --color-danger-light: #C73E3E;    /* 浅砖红色 */
  --color-danger-dark: #8B1A1A;     /* 暗砖红色 */
  --color-danger-rgb: 178, 34, 34;  /* 砖红色危险色RGB */

  --color-info: #4682B4;            /* 靛蓝色信息色 */
  --color-info-light: #5E96C5;      /* 浅靛蓝色 */
  --color-info-dark: #386890;       /* 暗靛蓝色 */
  --color-info-rgb: 70, 130, 180;   /* 靛蓝色信息色RGB */

  --color-warning: #DAA520;         /* 琥珀色警告色 */
  --color-warning-light: #E5B94C;   /* 浅琥珀色 */
  --color-warning-dark: #B7891A;    /* 暗琥珀色 */
  --color-warning-rgb: 218, 165, 32; /* 琥珀色警告色RGB */

  /* 中性色 */
  --color-text-primary: #333333;    /* 主要文本色 */
  --color-text-secondary: #666666;  /* 次要文本色 */
  --color-text-hint: #999999;       /* 提示文本色 */
  --color-white: #FFFFFF;           /* 白色 */

  /* 功能区域色 */
  --color-sidebar-bg: #F0EBE0;      /* 侧边栏背景色 */
  --color-editor-bg: #FFFFFF;       /* 编辑区背景色 */
  --color-ai-area-bg: #E6F0F5;      /* AI功能区背景色 */

  /* 节点类型背景色 - 仅用于大纲管理器 */
  --outline-primary: #5B6AF0;       /* 章节主色 - 蓝紫色 */
  --outline-primary-light: #7B8BFF; /* 浅蓝紫色 */
  --outline-primary-dark: #4A59D0;  /* 深蓝紫色 */
  --outline-primary-rgb: 91, 106, 240; /* 蓝紫色RGB */
  --outline-primary-bg: #F8F9FD;    /* 章节背景色 */

  --outline-secondary: #48CAE4;     /* 场景主色 - 青色 */
  --outline-secondary-light: #79DFF5; /* 浅青色 */
  --outline-secondary-dark: #3AA8C1; /* 深青色 */
  --outline-secondary-rgb: 72, 202, 228; /* 青色RGB */
  --outline-secondary-bg: #F0F7FA;  /* 场景背景色 */

  --outline-info: #9C6ADE;          /* 笔记主色 - 紫色 */
  --outline-info-light: #B794F6;    /* 浅紫色 */
  --outline-info-dark: #7E55B5;     /* 深紫色 */
  --outline-info-rgb: 156, 106, 222; /* 紫色RGB */
  --outline-info-bg: #F5F0FA;       /* 笔记背景色 */

  --outline-volume: #8B5CF6;        /* 总纲/卷主色 - 紫色 */
  --outline-volume-light: #A78BFA;  /* 浅紫色 */
  --outline-volume-dark: #7C3AED;   /* 深紫色 */
  --outline-volume-rgb: 139, 92, 246; /* 紫色RGB */
  --outline-volume-bg: #F5F3FF;     /* 总纲/卷背景色 */

  /* 暗色主题变量 */
  --color-dark-bg: #2C2418;         /* 深棕色背景 */
  --color-dark-card: #3E3424;       /* 中棕色卡片背景 */
  --color-dark-text: #F5F2E9;       /* 米白色文本 */

  /* 护眼主题变量 */
  --color-eyecare-bg: #E8F0E0;      /* 淡绿色背景 */
  --color-eyecare-text: #2E4A2E;    /* 深绿色文本 */

  /* 增强Markdown渲染的额外颜色 */
  --color-heading-1: #8B4513;       /* 一级标题颜色 */
  --color-heading-2: #A0522D;       /* 二级标题颜色 */
  --color-heading-3: #B8860B;       /* 三级标题颜色 */
  --color-heading-4: #CD853F;       /* 四级标题颜色 */
  --color-heading-5: #D2691E;       /* 五级标题颜色 */
  --color-heading-6: #E9967A;       /* 六级标题颜色 */

  --color-code-bg: rgba(139, 69, 19, 0.1); /* 代码背景色 */
  --color-code-text: #8B4513;       /* 代码文本色 */

  --color-blockquote-border: #8B4513; /* 引用边框色 */
  --color-blockquote-bg: rgba(139, 69, 19, 0.05); /* 引用背景色 */

  --color-link: #4682B4;            /* 链接颜色 */
  --color-link-hover: #2A4D69;      /* 链接悬停颜色 */

  --color-mark-bg: rgba(218, 165, 32, 0.2); /* 标记背景色 */

  /* 彩色括号颜色 */
  --color-bracket-round: #4682B4;   /* 圆括号颜色 */
  --color-bracket-square: #556B2F;  /* 方括号颜色 */
  --color-bracket-curly: #B22222;   /* 花括号颜色 */
  --color-bracket-angle: #DAA520;   /* 尖括号颜色 */

  /* 增强视觉效果变量 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);

  /* 光效变量 */
  --glow-primary: 0 0 8px rgba(139, 69, 19, 0.3);
  --glow-secondary: 0 0 8px rgba(210, 180, 140, 0.4);
  --glow-info: 0 0 8px rgba(70, 130, 180, 0.3);
  --glow-success: 0 0 8px rgba(85, 107, 47, 0.3);
  --glow-warning: 0 0 8px rgba(218, 165, 32, 0.3);
  --glow-danger: 0 0 8px rgba(178, 34, 34, 0.3);

  /* 渐变效果 */
  --gradient-primary: linear-gradient(135deg, rgba(139, 69, 19, 0.8), rgba(139, 69, 19, 0.6));
  --gradient-secondary: linear-gradient(135deg, rgba(210, 180, 140, 0.8), rgba(210, 180, 140, 0.6));
  --gradient-subtle: linear-gradient(to right, rgba(139, 69, 19, 0.05), transparent);

  /* 动画时间 */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* 动画曲线 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 基础样式 */
body {
  color: var(--color-text-primary);
  background: var(--color-primary-bg);
  font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* 基础元素样式 */
@layer base {
  h1 {
    @apply text-2xl font-bold mb-4 text-[#8B4513];
  }
  h2 {
    @apply text-xl font-semibold mb-3 text-[#8B4513];
  }
  h3 {
    @apply text-lg font-medium mb-2 text-[#8B4513];
  }
  a {
    @apply text-[#4682B4] hover:text-[#2A4D69] dark:text-[#6CA6CD] dark:hover:text-[#ADD8E6];
  }
  p {
    @apply mb-4 text-[#333333];
  }
}

/* 组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply px-4 py-2 rounded-lg transition-all duration-200;
  }

  .btn-primary {
    @apply bg-[#8B4513] text-white hover:bg-[#6B3100] active:scale-95;
  }

  .btn-secondary {
    @apply bg-[#D2B48C] text-[#333333] hover:bg-[#B8966E] active:scale-95;
  }

  .btn-success {
    @apply bg-[#556B2F] text-white hover:bg-[#445626] active:scale-95;
  }

  .btn-danger {
    @apply bg-[#B22222] text-white hover:bg-[#8B1A1A] active:scale-95;
  }

  .btn-info {
    @apply bg-[#4682B4] text-white hover:bg-[#386890] active:scale-95;
  }

  .btn-text {
    @apply bg-transparent text-[#8B4513] hover:bg-[rgba(139,69,19,0.1)] active:scale-95;
  }

  .btn-icon {
    @apply w-11 h-11 flex items-center justify-center rounded-full bg-transparent hover:bg-[rgba(139,69,19,0.1)] active:scale-95;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-xl shadow-md p-6 transition-all duration-200 border border-[#D2B48C];
  }

  .card:hover {
    @apply shadow-lg translate-y-[-3px];
  }

  /* 输入框样式 */
  .input {
    @apply px-4 py-2 border border-[#D2B48C] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#8B4513] focus:border-[#8B4513] bg-white;
  }

  .input-error {
    @apply border-[#B22222] focus:ring-[#B22222] focus:border-[#B22222];
  }

  /* 列表样式 */
  .list-item {
    @apply py-3 px-4 border-b border-[#D2B48C] last:border-b-0 hover:bg-[rgba(210,180,140,0.1)];
  }

  /* 对话框/弹窗样式 */
  .dialog {
    @apply bg-white rounded-2xl shadow-xl p-6 max-w-lg mx-auto;
  }

  .dialog-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50;
  }

  /* AI功能区样式 */
  .ai-area {
    @apply bg-[#E6F0F5] rounded-xl p-4 shadow-md;
  }

  /* 仪表盘样式 */
  .dashboard {
    @apply bg-white border border-[#D2B48C] rounded-xl shadow-md p-4;
  }

  /* 进度条样式 */
  .progress-bar {
    @apply bg-[#F0EBE0] rounded-full h-2 overflow-hidden;
  }

  .progress-bar-fill {
    @apply bg-[#8B4513] h-full transition-all duration-600 ease-out;
  }

  /* 写作统计面板SVG动画 */
  @keyframes draw-document {
    0% {
      stroke-dasharray: 0 100;
    }
    100% {
      stroke-dasharray: 100 0;
    }
  }

  @keyframes pulse-ring {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes target-pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.3);
      opacity: 0.8;
    }
  }

  @keyframes clock-tick {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(6deg);
    }
  }

  .animate-draw-document {
    stroke-dasharray: 100;
    animation: draw-document 2s ease-in-out infinite alternate;
  }

  .animate-pulse-ring {
    animation: pulse-ring 2s ease-in-out infinite;
  }

  .animate-target-pulse {
    animation: target-pulse 1.5s ease-in-out infinite;
  }

  .animate-clock-tick {
    transform-origin: 12px 12px;
    animation: clock-tick 60s linear infinite;
  }

  /* 高级SVG动画效果 */

  /* 文档图标动画 */
  @keyframes content-line-draw {
    0% {
      stroke-dasharray: 0 20;
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      stroke-dasharray: 20 0;
      opacity: 1;
    }
  }

  @keyframes document-glow {
    0%, 100% {
      opacity: 0;
    }
    50% {
      opacity: 0.8;
    }
  }

  .content-line {
    stroke-dasharray: 20;
    animation: content-line-draw 0.8s ease-in-out infinite;
  }

  .line-1 {
    animation-delay: 0s;
  }

  .line-2 {
    animation-delay: 0.3s;
  }

  .line-3 {
    animation-delay: 0.6s;
  }

  .document-glow {
    animation: document-glow 2s ease-in-out infinite;
  }

  /* 目标图标动画 */
  @keyframes flying-arrow {
    0% {
      transform: translateX(-10px);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(0);
      opacity: 0;
    }
  }

  @keyframes celebration-burst {
    0% {
      transform: scale(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: scale(2) rotate(360deg);
      opacity: 0;
    }
  }

  .flying-arrow {
    animation: flying-arrow 2s ease-in-out infinite;
  }

  .celebration-particles .particle {
    animation: celebration-burst 1.2s ease-out infinite;
  }

  .particle-1 { animation-delay: 0s; }
  .particle-2 { animation-delay: 0.1s; }
  .particle-3 { animation-delay: 0.2s; }
  .particle-4 { animation-delay: 0.3s; }
  .particle-5 { animation-delay: 0.4s; }
  .particle-6 { animation-delay: 0.5s; }
  .particle-7 { animation-delay: 0.6s; }
  .particle-8 { animation-delay: 0.7s; }

  /* 时钟动画 */
  @keyframes time-particle-flow {
    0% {
      transform: rotate(0deg) translate(0 -12px) scale(1);
      opacity: 0.6;
    }
    50% {
      opacity: 1;
      transform: rotate(180deg) translate(0 -12px) scale(1.2);
    }
    100% {
      transform: rotate(360deg) translate(0 -12px) scale(1);
      opacity: 0.6;
    }
  }

  .time-particle {
    animation: time-particle-flow 8s linear infinite;
  }

  .time-particle.particle-1 { animation-delay: 0s; }
  .time-particle.particle-2 { animation-delay: 1.3s; }
  .time-particle.particle-3 { animation-delay: 2.6s; }
  .time-particle.particle-4 { animation-delay: 4s; }
  .time-particle.particle-5 { animation-delay: 5.3s; }
  .time-particle.particle-6 { animation-delay: 6.6s; }

  /* 进度条液体效果 */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }

  .progress-bar-liquid {
    position: relative;
    overflow: hidden;
  }

  .progress-bar-liquid::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s ease-in-out infinite;
  }

  /* 数字滚动动画 */
  @keyframes number-roll-up {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes number-roll-down {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .number-increase {
    animation: number-roll-up 0.6s ease-out;
  }

  .number-decrease {
    animation: number-roll-down 0.6s ease-out;
  }

  /* 悬停光晕效果 */
  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 5px rgba(var(--color-primary-rgb), 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.6);
    }
  }

  .group:hover .glow-effect {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  /* 点击涟漪效果 */
  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }

  .ripple-effect {
    position: relative;
    overflow: hidden;
  }

  .ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ripple-effect:active::before {
    width: 300px;
    height: 300px;
    animation: ripple 0.6s ease-out;
  }

  /* 里程碑庆祝动画 */
  @keyframes milestone-celebration {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.1) rotate(5deg);
    }
    50% {
      transform: scale(1.2) rotate(-5deg);
    }
    75% {
      transform: scale(1.1) rotate(3deg);
    }
    100% {
      transform: scale(1) rotate(0deg);
    }
  }

  .milestone-celebration {
    animation: milestone-celebration 0.8s ease-in-out;
  }

  /* 专注模式提醒 */
  @keyframes focus-reminder {
    0%, 100% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(2deg);
    }
    75% {
      transform: rotate(-2deg);
    }
  }

  .focus-reminder {
    animation: focus-reminder 3s ease-in-out infinite;
  }

  /* 文字阴影效果 */
  .group-hover\:text-shadow-lg:hover {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 数字字体优化 */
  .tabular-nums {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
  }

  /* 字母间距优化 */
  .tracking-wide {
    letter-spacing: 0.025em;
  }

  /* 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark, var(--color-primary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 统计面板特殊效果 */
  .stats-card {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .stats-card:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

/* 动画样式 */
@layer utilities {
  /* 淡入动画 */
  .fade-in {
    @apply animate-[fadeIn_300ms_ease-out_forwards];
  }

  /* 淡入动画（带位移） */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  /* 卡片进入动画 */
  .card-enter {
    @apply animate-[fadeInUp_400ms_ease-out_forwards];
  }

  /* 缩放进入动画 */
  .animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
  }

  /* 按钮点击效果 */
  .btn-active {
    @apply scale-95;
  }

  /* 加载指示器 */
  .loading {
    @apply animate-[spin_1000ms_linear_infinite];
  }

  /* 文本平衡 */
  .text-balance {
    text-wrap: balance;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 彩色括号动画 */
@keyframes bracketPulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 光效动画 */
@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(var(--color-primary-rgb), 0.3); }
  50% { box-shadow: 0 0 15px rgba(var(--color-primary-rgb), 0.5); }
  100% { box-shadow: 0 0 5px rgba(var(--color-primary-rgb), 0.3); }
}

/* 大纲节点动画 */
@keyframes outlineNodeAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes outlineNodeExpand {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 1000px;
    opacity: 1;
  }
}

@keyframes outlineNodePulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(var(--outline-primary-rgb), 0);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(var(--outline-primary-rgb), 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(var(--outline-primary-rgb), 0);
  }
}

/* 大纲节点编辑器保存动画 */
@keyframes pulse-save {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--outline-primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--outline-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--outline-primary-rgb), 0);
  }
}

.pulse-save {
  animation: pulse-save 0.5s ease-out;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

/* 键盘快捷键提示面板 */
.keyboard-shortcuts {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  padding: 16px;
  z-index: 1000;
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-out;
  transform-origin: top left;
}

.keyboard-shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.keyboard-shortcuts-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.keyboard-shortcuts-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.keyboard-shortcuts-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.keyboard-shortcuts-group {
  margin-bottom: 16px;
}

.keyboard-shortcuts-group h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.keyboard-shortcut {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.keyboard-shortcut-key {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  font-family: monospace;
  color: #333;
  min-width: 60px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.keyboard-shortcut-description {
  margin-left: 10px;
  font-size: 13px;
  color: #555;
}

/* 节点创建动画 */
@keyframes nodeCreate {
  0% {
    opacity: 0;
    transform: scale(0.7);
  }
  70% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.node-create {
  animation: nodeCreate 0.4s ease-out forwards;
}

/* 节点删除动画 */
@keyframes nodeDelete {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.7);
  }
}

.node-delete {
  animation: nodeDelete 0.3s ease-in forwards;
}

/* 节点选中动画 */
@keyframes nodeSelect {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--outline-primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--outline-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--outline-primary-rgb), 0);
  }
}

.node-select {
  animation: nodeSelect 0.5s ease-out;
}

@keyframes outlineBadgePulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 删除动画 */
@keyframes nodeDelete {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  20% {
    opacity: 0.8;
    transform: translateX(-10px) scale(0.98);
  }
  100% {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
    max-height: 0;
    margin: 0;
    padding: 0;
  }
}

/* 确认对话框动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

.animate-fadeOut {
  animation: fadeOut 0.2s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

.animate-scaleOut {
  animation: scaleOut 0.3s ease-out forwards;
}

/* React Flow 自定义样式 */
.react-flow__node {
  transition: all 0.3s ease;
}

.react-flow__node:hover {
  z-index: 10;
  transform: scale(1.02);
}

.react-flow__controls {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  overflow: hidden;
}

.react-flow__controls-button {
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: white !important;
  color: #4b5563 !important;
  transition: all 0.2s ease;
}

.react-flow__controls-button:hover {
  background: #f9fafb !important;
  color: var(--outline-primary) !important;
}

.react-flow__minimap {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-flow__attribution {
  background: transparent !important;
  color: #9ca3af !important;
}

/* Markdown渲染增强样式 */
.enhanced-markdown {
  line-height: 1.6;
  font-size: 1rem;
}

/* 标题样式 */
.enhanced-markdown h1 {
  color: var(--color-heading-1);
  font-size: 1.8rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  border-bottom: 2px solid rgba(139, 69, 19, 0.2);
  padding-bottom: 0.3rem;
  position: relative;
  transition: all var(--transition-normal) var(--ease-out);
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

.enhanced-markdown h1::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--color-heading-1), transparent);
  border-radius: 2px;
  transform-origin: left;
  transform: scaleX(0.3);
  transition: transform var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h1:hover::after {
  transform: scaleX(1);
}

.enhanced-markdown h2 {
  color: var(--color-heading-2);
  font-size: 1.5rem;
  margin-top: 1.4rem;
  margin-bottom: 0.9rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(160, 82, 45, 0.2);
  padding-bottom: 0.2rem;
  position: relative;
  transition: all var(--transition-normal) var(--ease-out);
  text-shadow: 1px 1px 1px rgba(160, 82, 45, 0.1);
}

.enhanced-markdown h2::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--color-heading-2), transparent);
  border-radius: 1px;
  transform-origin: left;
  transform: scaleX(0.2);
  transition: transform var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h2:hover::after {
  transform: scaleX(0.8);
}

.enhanced-markdown h3 {
  color: var(--color-heading-3);
  font-size: 1.3rem;
  margin-top: 1.3rem;
  margin-bottom: 0.8rem;
  font-weight: 600;
  position: relative;
  padding-left: 0.5rem;
  transition: all var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.2rem;
  height: 80%;
  width: 3px;
  background: var(--color-heading-3);
  border-radius: 2px;
  opacity: 0.6;
  transition: all var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h3:hover::before {
  opacity: 1;
  transform: scaleY(1.1);
}

.enhanced-markdown h4 {
  color: var(--color-heading-4);
  font-size: 1.2rem;
  margin-top: 1.2rem;
  margin-bottom: 0.7rem;
  font-weight: 600;
  position: relative;
  transition: all var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h5 {
  color: var(--color-heading-5);
  font-size: 1.1rem;
  margin-top: 1.1rem;
  margin-bottom: 0.6rem;
  font-weight: 600;
  position: relative;
  transition: all var(--transition-normal) var(--ease-out);
}

.enhanced-markdown h6 {
  color: var(--color-heading-6);
  font-size: 1rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  position: relative;
  transition: all var(--transition-normal) var(--ease-out);
}

/* 列表样式 */
.enhanced-markdown ul {
  list-style-type: none;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.enhanced-markdown ul li {
  position: relative;
  margin-bottom: 0.6rem;
  padding-left: 0.5rem;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown ul li::before {
  content: "•";
  color: var(--color-secondary, #D2B48C);
  font-weight: bold;
  position: absolute;
  left: -1rem;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
  transform-origin: center;
}

.enhanced-markdown ul li:hover {
  transform: translateX(2px);
}

.enhanced-markdown ul li:hover::before {
  transform: scale(1.2);
  color: var(--color-primary, #8B4513);
}

/* 添加级联动画效果 */
.enhanced-markdown ul li:nth-child(1) {
  animation: fadeInRight 0.5s 0.1s both;
}
.enhanced-markdown ul li:nth-child(2) {
  animation: fadeInRight 0.5s 0.2s both;
}
.enhanced-markdown ul li:nth-child(3) {
  animation: fadeInRight 0.5s 0.3s both;
}
.enhanced-markdown ul li:nth-child(4) {
  animation: fadeInRight 0.5s 0.4s both;
}
.enhanced-markdown ul li:nth-child(5) {
  animation: fadeInRight 0.5s 0.5s both;
}

.enhanced-markdown ol {
  list-style-type: none;
  counter-reset: item;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.enhanced-markdown ol li {
  position: relative;
  margin-bottom: 0.6rem;
  padding-left: 0.5rem;
  counter-increment: item;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown ol li::before {
  content: counter(item) ".";
  color: var(--color-secondary, #D2B48C);
  font-weight: bold;
  position: absolute;
  left: -1.5rem;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown ol li:hover {
  transform: translateX(2px);
}

.enhanced-markdown ol li:hover::before {
  color: var(--color-primary, #8B4513);
}

/* 添加级联动画效果 */
.enhanced-markdown ol li:nth-child(1) {
  animation: fadeInRight 0.5s 0.1s both;
}
.enhanced-markdown ol li:nth-child(2) {
  animation: fadeInRight 0.5s 0.2s both;
}
.enhanced-markdown ol li:nth-child(3) {
  animation: fadeInRight 0.5s 0.3s both;
}
.enhanced-markdown ol li:nth-child(4) {
  animation: fadeInRight 0.5s 0.4s both;
}
.enhanced-markdown ol li:nth-child(5) {
  animation: fadeInRight 0.5s 0.5s both;
}

/* 引用样式 */
.enhanced-markdown blockquote {
  border-left: 4px solid var(--color-secondary, #D2B48C);
  padding-left: 1rem;
  margin: 1.5rem 0;
  color: var(--color-text-secondary, #666666);
  font-style: italic;
  background-color: rgba(210, 180, 140, 0.1);
  padding: 1rem 1.2rem;
  border-radius: 0 8px 8px 0;
  position: relative;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 0.5rem;
  font-size: 2.5rem;
  color: var(--color-secondary, #D2B48C);
  opacity: 0.3;
  font-family: Georgia, serif;
}

.enhanced-markdown blockquote:hover {
  transform: translateX(3px);
  box-shadow: var(--shadow-md);
  background-color: rgba(210, 180, 140, 0.15);
}

/* 代码样式 */
.enhanced-markdown code {
  font-family: 'Courier New', monospace;
  background-color: rgba(210, 180, 140, 0.2);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9rem;
  color: var(--color-primary, #8B4513);
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
  box-shadow: var(--shadow-sm);
}

.enhanced-markdown code:hover {
  background-color: rgba(210, 180, 140, 0.3);
  box-shadow: var(--shadow-md);
}

.enhanced-markdown pre {
  background-color: rgba(210, 180, 140, 0.1);
  padding: 1.2rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid rgba(210, 180, 140, 0.3);
  position: relative;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
  animation: scaleIn 0.5s var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown pre::before {
  content: attr(data-language);
  position: absolute;
  top: 0;
  right: 1rem;
  background: var(--color-secondary, #D2B48C);
  color: white;
  padding: 0.2rem 0.6rem;
  font-size: 0.7rem;
  border-radius: 0 0 4px 4px;
  opacity: 0.8;
  text-transform: uppercase;
  font-weight: bold;
}

.enhanced-markdown pre:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.enhanced-markdown pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
  box-shadow: none;
  display: block;
  line-height: 1.5;
}

/* 表格样式 */
.enhanced-markdown table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  animation: scaleIn 0.5s var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
  border: 1px solid rgba(210, 180, 140, 0.3);
}

.enhanced-markdown th {
  background-color: rgba(210, 180, 140, 0.2);
  padding: 0.8rem 1rem;
  text-align: left;
  color: var(--color-primary, #8B4513);
  font-weight: 600;
  border-bottom: 2px solid rgba(210, 180, 140, 0.5);
  position: relative;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown th:hover {
  background-color: rgba(210, 180, 140, 0.3);
}

.enhanced-markdown th::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--color-primary, #8B4513), transparent);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown th:hover::after {
  transform: scaleX(1);
}

.enhanced-markdown td {
  padding: 0.8rem 1rem;
  border-bottom: 1px solid rgba(210, 180, 140, 0.2);
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown tr:last-child td {
  border-bottom: none;
}

.enhanced-markdown tr {
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown tr:hover {
  background-color: rgba(210, 180, 140, 0.1);
}

.enhanced-markdown tr:nth-child(even) {
  background-color: rgba(210, 180, 140, 0.05);
}

.enhanced-markdown tr:nth-child(even):hover {
  background-color: rgba(210, 180, 140, 0.15);
}

/* 彩色括号样式 */
.enhanced-markdown .bracket-round-open {
  color: #4682B4; /* 靛蓝色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
}

.enhanced-markdown .bracket-round-close {
  color: #4682B4; /* 靛蓝色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
  animation-delay: 0.3s;
}

.enhanced-markdown .bracket-square-open {
  color: #556B2F; /* 橄榄绿 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
}

.enhanced-markdown .bracket-square-close {
  color: #556B2F; /* 橄榄绿 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
  animation-delay: 0.3s;
}

.enhanced-markdown .bracket-curly-open {
  color: #B22222; /* 砖红色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
}

.enhanced-markdown .bracket-curly-close {
  color: #B22222; /* 砖红色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
  animation-delay: 0.3s;
}

.enhanced-markdown .bracket-angle-open {
  color: #DAA520; /* 琥珀色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
}

.enhanced-markdown .bracket-angle-close {
  color: #DAA520; /* 琥珀色 */
  font-weight: 600;
  animation: bracketPulse 2s infinite;
  animation-delay: 0.3s;
}

/* 强调文本样式 */
.enhanced-markdown strong {
  color: var(--color-primary, #8B4513);
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding: 0 2px;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown strong::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(139, 69, 19, 0.2);
  border-radius: 2px;
  transform-origin: center;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown strong:hover {
  color: var(--color-primary, #8B4513);
  text-shadow: 0 0 1px rgba(139, 69, 19, 0.3);
}

.enhanced-markdown strong:hover::after {
  background: rgba(139, 69, 19, 0.4);
  height: 5px;
  transform: scaleX(1.05);
}

.enhanced-markdown em {
  color: var(--color-info, #4682B4);
  font-style: italic;
  position: relative;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown em::before,
.enhanced-markdown em::after {
  content: '*';
  color: rgba(70, 130, 180, 0.6);
  font-weight: bold;
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown em:hover {
  color: var(--color-info, #4682B4);
  text-shadow: 0 0 1px rgba(70, 130, 180, 0.5);
}

.enhanced-markdown em:hover::before,
.enhanced-markdown em:hover::after {
  color: rgba(70, 130, 180, 0.9);
}

.enhanced-markdown a {
  color: var(--color-info, #4682B4);
  text-decoration: none;
  border-bottom: 1px dotted var(--color-info, #4682B4);
  transition: all var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
  position: relative;
  padding: 0 2px;
}

.enhanced-markdown a::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--color-info, #4682B4);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform var(--transition-normal, 300ms) var(--ease-out, cubic-bezier(0, 0, 0.2, 1));
}

.enhanced-markdown a:hover {
  color: var(--color-link-hover, #2A4D69);
  border-bottom-color: transparent;
  background: rgba(70, 130, 180, 0.1);
}

.enhanced-markdown a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* 特殊格式样式 */
.enhanced-markdown .enhanced-special-format {
  background: linear-gradient(to right, rgba(var(--color-primary-rgb), 0.05), transparent);
  padding: 8px 12px;
  border-radius: 8px;
  margin: 12px 0;
  border-left: 3px solid var(--color-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.enhanced-markdown .enhanced-special-format:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 列表项样式增强 */
.enhanced-markdown .enhanced-list-item,
.enhanced-markdown .enhanced-ordered-list-item {
  position: relative;
  padding: 2px 0;
}

/* 代码样式增强 */
.enhanced-markdown .enhanced-inline-code {
  font-family: 'Courier New', monospace;
  position: relative;
  top: -1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 标记样式 */
.enhanced-markdown .enhanced-mark {
  position: relative;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 删除线样式 */
.enhanced-markdown .enhanced-del {
  position: relative;
}

.enhanced-markdown .enhanced-del::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--color-danger);
  opacity: 0.7;
}

/* 下划线样式 */
.enhanced-markdown .enhanced-underline {
  text-decoration: none;
  position: relative;
}

/* 段落样式增强 */
.enhanced-markdown .enhanced-paragraph {
  line-height: 1.7;
  position: relative;
  transition: all 0.2s ease;
}

.enhanced-markdown .enhanced-paragraph:hover {
  background: rgba(var(--color-primary-rgb), 0.02);
  border-radius: 4px;
}

/* 媒体查询 - 暗色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-primary-bg: #2C2418;
    --color-editor-bg: #3E3424;
    --color-sidebar-bg: #3E3424;
    --color-text-primary: #F5F2E9;
    --color-text-secondary: #D2B48C;
    --color-text-hint: #B8966E;
  }

  body {
    color: var(--color-text-primary);
    background: var(--color-primary-bg);
  }

  .card {
    background-color: #3E3424;
    border-color: #5A4A36;
  }

  .input {
    background-color: #3E3424;
    border-color: #5A4A36;
    color: #F5F2E9;
  }

  .dialog {
    background-color: #3E3424;
  }

  .dashboard {
    background-color: #3E3424;
    border-color: #5A4A36;
  }

  .progress-bar {
    background-color: #5A4A36;
  }

  /* 暗色模式下的Markdown样式调整 */
  .enhanced-markdown blockquote {
    background-color: rgba(210, 180, 140, 0.05);
    color: #D2B48C;
  }

  .enhanced-markdown code {
    background-color: rgba(210, 180, 140, 0.1);
    color: #D2B48C;
  }

  .enhanced-markdown pre {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: rgba(210, 180, 140, 0.2);
  }

  .enhanced-markdown th {
    background-color: rgba(210, 180, 140, 0.1);
    color: #D2B48C;
  }

  .enhanced-markdown tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.2);
  }
}
