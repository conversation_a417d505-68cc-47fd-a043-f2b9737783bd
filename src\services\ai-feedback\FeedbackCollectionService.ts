/**
 * AI反馈收集服务
 * 负责收集、存储和分析用户对AI回复的评分反馈
 */

import { MessageFeedback, FeedbackRating, FeedbackAnalysis, FeedbackStats } from '../../types/ai-feedback';
import { ChatMessage } from '../chat/ChatPersistenceService';

export class FeedbackCollectionService {
  private static instance: FeedbackCollectionService;
  private readonly STORAGE_KEY = 'ai-feedback-data';

  private constructor() {}

  static getInstance(): FeedbackCollectionService {
    if (!FeedbackCollectionService.instance) {
      FeedbackCollectionService.instance = new FeedbackCollectionService();
    }
    return FeedbackCollectionService.instance;
  }

  /**
   * 记录用户反馈
   */
  async recordFeedback(
    messageId: string,
    sessionId: string,
    phase: string,
    rating: FeedbackRating,
    userMessage: string,
    aiResponse: string,
    conversationHistory: ChatMessage[],
    systemPrompt: string,
    personaConfig?: string,
    comment?: string
  ): Promise<void> {
    try {
      const feedback: MessageFeedback = {
        id: `feedback-${Date.now()}`,
        messageId,
        sessionId,
        phase,
        rating,
        comment,
        timestamp: new Date(),
        userContext: {
          userMessage,
          aiResponse,
          conversationLength: conversationHistory.length,
          previousMessages: conversationHistory.slice(-5).map(msg => ({
            type: msg.type,
            content: msg.content,
            timestamp: msg.timestamp
          }))
        },
        systemContext: {
          systemPrompt,
          personaConfig,
          contentLength: aiResponse.length
        }
      };

      await this.saveFeedback(feedback);
      console.log(`反馈已记录: ${rating} for message ${messageId}`);
    } catch (error) {
      console.error('记录反馈失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定阶段的反馈统计
   */
  async getFeedbackStats(phase?: string): Promise<FeedbackStats> {
    try {
      const allFeedbacks = this.getAllFeedbacks();
      const filteredFeedbacks = phase 
        ? allFeedbacks.filter(f => f.phase === phase)
        : allFeedbacks;

      const totalMessages = filteredFeedbacks.length;
      const ratedMessages = filteredFeedbacks.length;

      // 按阶段统计
      const phaseStats: Record<string, any> = {};
      const phases = ['intro', 'buildup', 'climax', 'ending'];
      
      phases.forEach(p => {
        const phaseFeedbacks = allFeedbacks.filter(f => f.phase === p);
        const good = phaseFeedbacks.filter(f => f.rating === 'good').length;
        const average = phaseFeedbacks.filter(f => f.rating === 'average').length;
        const poor = phaseFeedbacks.filter(f => f.rating === 'poor').length;
        
        phaseStats[p] = {
          total: phaseFeedbacks.length,
          good,
          average,
          poor,
          averageScore: phaseFeedbacks.length > 0 
            ? (good * 3 + average * 2 + poor * 1) / phaseFeedbacks.length 
            : 0
        };
      });

      // 计算趋势
      const now = new Date();
      const last7Days = allFeedbacks.filter(f => 
        (now.getTime() - f.timestamp.getTime()) <= 7 * 24 * 60 * 60 * 1000
      ).length;
      const last30Days = allFeedbacks.filter(f => 
        (now.getTime() - f.timestamp.getTime()) <= 30 * 24 * 60 * 60 * 1000
      ).length;

      return {
        totalMessages,
        ratedMessages,
        ratingPercentage: totalMessages > 0 ? (ratedMessages / totalMessages) * 100 : 0,
        phaseStats,
        recentTrends: {
          last7Days,
          last30Days,
          improvement: last7Days > last30Days / 4 // 简单的改进判断
        }
      };
    } catch (error) {
      console.error('获取反馈统计失败:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * 分析反馈模式
   */
  async analyzeFeedbackPatterns(phase: string): Promise<FeedbackAnalysis> {
    try {
      const feedbacks = this.getAllFeedbacks().filter(f => f.phase === phase);
      
      if (feedbacks.length === 0) {
        return this.getEmptyAnalysis(phase);
      }

      const good = feedbacks.filter(f => f.rating === 'good');
      const average = feedbacks.filter(f => f.rating === 'average');
      const poor = feedbacks.filter(f => f.rating === 'poor');

      // 分析差评模式
      const poorPatterns = this.extractPatterns(poor);
      const goodPatterns = this.extractPatterns(good);

      // 生成建议
      const recommendations = this.generateRecommendations(poorPatterns, goodPatterns);

      return {
        phase,
        totalFeedbacks: feedbacks.length,
        ratingDistribution: {
          good: good.length,
          average: average.length,
          poor: poor.length
        },
        averageScore: (good.length * 3 + average.length * 2 + poor.length * 1) / feedbacks.length,
        commonPatterns: {
          poorPerformance: poorPatterns,
          goodPerformance: goodPatterns
        },
        recommendations
      };
    } catch (error) {
      console.error('分析反馈模式失败:', error);
      return this.getEmptyAnalysis(phase);
    }
  }

  /**
   * 获取消息的反馈评分
   */
  getMessageFeedback(messageId: string): MessageFeedback | null {
    try {
      const feedbacks = this.getAllFeedbacks();
      return feedbacks.find(f => f.messageId === messageId) || null;
    } catch (error) {
      console.error('获取消息反馈失败:', error);
      return null;
    }
  }

  /**
   * 删除反馈
   */
  async deleteFeedback(feedbackId: string): Promise<void> {
    try {
      const feedbacks = this.getAllFeedbacks();
      const filteredFeedbacks = feedbacks.filter(f => f.id !== feedbackId);
      this.saveAllFeedbacks(filteredFeedbacks);
    } catch (error) {
      console.error('删除反馈失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：保存反馈
   */
  private async saveFeedback(feedback: MessageFeedback): Promise<void> {
    try {
      const feedbacks = this.getAllFeedbacks();
      
      // 检查是否已存在该消息的反馈，如果存在则更新
      const existingIndex = feedbacks.findIndex(f => f.messageId === feedback.messageId);
      if (existingIndex >= 0) {
        feedbacks[existingIndex] = feedback;
      } else {
        feedbacks.push(feedback);
      }

      this.saveAllFeedbacks(feedbacks);
    } catch (error) {
      console.error('保存反馈失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：获取所有反馈
   */
  private getAllFeedbacks(): MessageFeedback[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const feedbacks = JSON.parse(stored);
      return feedbacks.map((feedback: any) => ({
        ...feedback,
        timestamp: new Date(feedback.timestamp),
        userContext: {
          ...feedback.userContext,
          previousMessages: feedback.userContext.previousMessages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }
      }));
    } catch (error) {
      console.error('获取反馈数据失败:', error);
      return [];
    }
  }

  /**
   * 私有方法：保存所有反馈
   */
  private saveAllFeedbacks(feedbacks: MessageFeedback[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(feedbacks));
    } catch (error) {
      console.error('保存反馈数据失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：提取模式
   */
  private extractPatterns(feedbacks: MessageFeedback[]): {
    userInputPatterns: string[];
    contextPatterns: string[];
    responsePatterns: string[];
  } {
    const userInputs = feedbacks.map(f => f.userContext.userMessage);
    const responses = feedbacks.map(f => f.userContext.aiResponse);
    
    // 简单的模式识别（实际应用中可以使用更复杂的NLP技术）
    const userInputPatterns = this.findCommonPatterns(userInputs);
    const responsePatterns = this.findCommonPatterns(responses);
    const contextPatterns = feedbacks.map(f => 
      `对话长度: ${f.userContext.conversationLength}, 回复长度: ${f.systemContext.contentLength}`
    );

    return {
      userInputPatterns,
      contextPatterns: [...new Set(contextPatterns)],
      responsePatterns
    };
  }

  /**
   * 私有方法：查找常见模式
   */
  private findCommonPatterns(texts: string[]): string[] {
    // 简单的关键词频率分析
    const keywords = ['如何', '什么', '为什么', '怎么', '建议', '修改', '优化', '分析'];
    const patterns: string[] = [];

    keywords.forEach(keyword => {
      const count = texts.filter(text => text.includes(keyword)).length;
      if (count > texts.length * 0.3) { // 如果超过30%的文本包含该关键词
        patterns.push(`包含"${keyword}"的问题 (${count}/${texts.length})`);
      }
    });

    return patterns;
  }

  /**
   * 私有方法：生成建议
   */
  private generateRecommendations(poorPatterns: any, goodPatterns: any): string[] {
    const recommendations: string[] = [];

    if (poorPatterns.userInputPatterns.length > 0) {
      recommendations.push(`注意处理包含以下模式的用户输入: ${poorPatterns.userInputPatterns.join(', ')}`);
    }

    if (goodPatterns.userInputPatterns.length > 0) {
      recommendations.push(`继续保持对以下类型问题的良好回复: ${goodPatterns.userInputPatterns.join(', ')}`);
    }

    if (poorPatterns.responsePatterns.length > 0) {
      recommendations.push(`避免使用以下回复模式: ${poorPatterns.responsePatterns.join(', ')}`);
    }

    return recommendations;
  }

  /**
   * 私有方法：获取空统计
   */
  private getEmptyStats(): FeedbackStats {
    return {
      totalMessages: 0,
      ratedMessages: 0,
      ratingPercentage: 0,
      phaseStats: {},
      recentTrends: {
        last7Days: 0,
        last30Days: 0,
        improvement: false
      }
    };
  }

  /**
   * 私有方法：获取空分析
   */
  private getEmptyAnalysis(phase: string): FeedbackAnalysis {
    return {
      phase,
      totalFeedbacks: 0,
      ratingDistribution: { good: 0, average: 0, poor: 0 },
      averageScore: 0,
      commonPatterns: {
        poorPerformance: { userInputPatterns: [], contextPatterns: [], responsePatterns: [] },
        goodPerformance: { userInputPatterns: [], contextPatterns: [], responsePatterns: [] }
      },
      recommendations: []
    };
  }
}
