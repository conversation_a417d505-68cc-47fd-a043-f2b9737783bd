"use client";

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import {
  ThinkingCanvas,
  ThinkingCanvasData,
  CanvasManagerState,
  CanvasManagerAction,
  EditRecord
} from '@/types/thinking-canvas';

// Context接口定义
interface ThinkingCanvasContextType {
  // 状态
  canvases: ThinkingCanvas[];
  currentCanvas: ThinkingCanvas | null;
  editMode: 'edit' | 'preview';
  sidebarCollapsed: boolean;
  isSaving: boolean;
  searchQuery: string;
  sortBy: 'date' | 'name' | 'updated';
  
  // 操作方法
  createCanvas: (title: string, template?: string) => void;
  deleteCanvas: (id: string) => void;
  updateCanvas: (id: string, updates: Partial<ThinkingCanvas>) => void;
  switchCanvas: (id: string) => void;
  setEditMode: (mode: 'edit' | 'preview') => void;
  toggleSidebar: () => void;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: 'date' | 'name' | 'updated') => void;
}

// 创建Context
const ThinkingCanvasContext = createContext<ThinkingCanvasContextType | undefined>(undefined);

// 初始状态
const initialState: CanvasManagerState = {
  canvases: [],
  currentCanvasId: null,
  editMode: 'edit',
  sidebarCollapsed: false,
  isSaving: false,
  searchQuery: '',
  sortBy: 'updated'
};

// Reducer函数
function canvasManagerReducer(state: CanvasManagerState, action: CanvasManagerAction): CanvasManagerState {
  switch (action.type) {
    case 'CREATE_CANVAS': {
      const newCanvas: ThinkingCanvas = {
        id: `canvas_${Date.now()}`,
        title: action.payload.title,
        content: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: [],
        template: action.payload.template,
        metadata: {
          wordCount: 0,
          editHistory: [{
            timestamp: new Date().toISOString(),
            action: 'create'
          }],
          isStarred: false
        }
      };
      
      return {
        ...state,
        canvases: [newCanvas, ...state.canvases],
        currentCanvasId: newCanvas.id
      };
    }
    
    case 'DELETE_CANVAS': {
      const newCanvases = state.canvases.filter(canvas => canvas.id !== action.payload.id);
      const newCurrentId = state.currentCanvasId === action.payload.id 
        ? (newCanvases.length > 0 ? newCanvases[0].id : null)
        : state.currentCanvasId;
      
      return {
        ...state,
        canvases: newCanvases,
        currentCanvasId: newCurrentId
      };
    }
    
    case 'UPDATE_CANVAS': {
      const updatedCanvases = state.canvases.map(canvas => {
        if (canvas.id === action.payload.id) {
          const updatedCanvas = { ...canvas, ...action.payload.updates };
          
          // 更新字数统计
          if (action.payload.updates.content !== undefined) {
            updatedCanvas.metadata = {
              ...updatedCanvas.metadata,
              wordCount: action.payload.updates.content.length,
              editHistory: [
                ...updatedCanvas.metadata.editHistory,
                {
                  timestamp: new Date().toISOString(),
                  action: 'update'
                }
              ]
            };
          }
          
          updatedCanvas.updatedAt = new Date().toISOString();
          return updatedCanvas;
        }
        return canvas;
      });
      
      return {
        ...state,
        canvases: updatedCanvases
      };
    }
    
    case 'SWITCH_CANVAS':
      return {
        ...state,
        currentCanvasId: action.payload.id
      };
    
    case 'SET_EDIT_MODE':
      return {
        ...state,
        editMode: action.payload.mode
      };
    
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed
      };
    
    case 'SET_SEARCH_QUERY':
      return {
        ...state,
        searchQuery: action.payload.query
      };
    
    case 'SET_SORT_BY':
      return {
        ...state,
        sortBy: action.payload.sortBy
      };
    
    case 'LOAD_CANVASES':
      return {
        ...state,
        canvases: action.payload.canvases
      };
    
    case 'SET_SAVING':
      return {
        ...state,
        isSaving: action.payload.isSaving
      };
    
    default:
      return state;
  }
}

// localStorage工具函数
const STORAGE_KEY = 'thinking-canvases';

const saveCanvasesToStorage = (canvases: ThinkingCanvas[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(canvases));
  } catch (error) {
    console.error('保存画布数据失败:', error);
  }
};

const loadCanvasesFromStorage = (): ThinkingCanvas[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('加载画布数据失败:', error);
    return [];
  }
};

// Provider组件
interface ThinkingCanvasProviderProps {
  children: React.ReactNode;
  initialData?: ThinkingCanvasData;
  onSave?: (data: ThinkingCanvasData) => void;
}

export const ThinkingCanvasProvider: React.FC<ThinkingCanvasProviderProps> = ({
  children,
  initialData,
  onSave
}) => {
  const [state, dispatch] = useReducer(canvasManagerReducer, initialState);
  
  // 计算当前画布
  const currentCanvas = state.currentCanvasId 
    ? state.canvases.find(canvas => canvas.id === state.currentCanvasId) || null
    : null;
  
  // 初始化时加载数据
  useEffect(() => {
    if (initialData) {
      // 如果有传入的初始数据，将其转换为ThinkingCanvas格式
      // ThinkingCanvasData -> ThinkingCanvas 的转换
      const content = `# ${initialData.title}

## 对话设计
**角色列表**: ${initialData.dialogueDesign.characters.join(', ')}
**对话推进逻辑**: ${initialData.dialogueDesign.dialogueFlow}
**情感基调**: ${initialData.dialogueDesign.emotionalTone}
**对话节奏**: ${initialData.dialogueDesign.pacing}
**对话技巧**: ${initialData.dialogueDesign.techniques.join(', ')}

## 剧情节奏
**紧张点设计**: ${initialData.plotRhythm.tensionPoints.join(', ')}
**高潮时刻**: ${initialData.plotRhythm.climaxMoments.join(', ')}
**节奏策略**: ${initialData.plotRhythm.pacingStrategy}
**读者爽感设计**: ${initialData.plotRhythm.readerEngagement}

## AI建议
${initialData.aiSuggestions.map(suggestion => `- ${suggestion}`).join('\n')}

## 用户备注
${initialData.userNotes}`;

      const canvasFromInitialData: ThinkingCanvas = {
        id: initialData.id,
        title: initialData.title,
        content: content,
        createdAt: initialData.createdAt,
        updatedAt: initialData.lastModified,
        tags: [],
        metadata: {
          wordCount: content.length,
          editHistory: [{
            timestamp: initialData.createdAt,
            action: 'create'
          }],
          isStarred: false
        }
      };

      dispatch({ type: 'LOAD_CANVASES', payload: { canvases: [canvasFromInitialData] } });
      dispatch({ type: 'SWITCH_CANVAS', payload: { id: canvasFromInitialData.id } });
    } else {
      // 否则从localStorage加载
      const savedCanvases = loadCanvasesFromStorage();
      if (savedCanvases.length > 0) {
        dispatch({ type: 'LOAD_CANVASES', payload: { canvases: savedCanvases } });
        // 设置第一个画布为当前画布
        dispatch({ type: 'SWITCH_CANVAS', payload: { id: savedCanvases[0].id } });
      }
    }
  }, [initialData]);
  
  // 保存数据到localStorage
  useEffect(() => {
    if (state.canvases.length > 0) {
      saveCanvasesToStorage(state.canvases);
    }
  }, [state.canvases]);
  
  // 操作方法
  const createCanvas = useCallback((title: string, template?: string) => {
    dispatch({ type: 'CREATE_CANVAS', payload: { title, template } });
  }, []);
  
  const deleteCanvas = useCallback((id: string) => {
    dispatch({ type: 'DELETE_CANVAS', payload: { id } });
  }, []);
  
  const updateCanvas = useCallback((id: string, updates: Partial<ThinkingCanvas>) => {
    dispatch({ type: 'UPDATE_CANVAS', payload: { id, updates } });
  }, []);
  
  const switchCanvas = useCallback((id: string) => {
    dispatch({ type: 'SWITCH_CANVAS', payload: { id } });
  }, []);
  
  const setEditMode = useCallback((mode: 'edit' | 'preview') => {
    dispatch({ type: 'SET_EDIT_MODE', payload: { mode } });
  }, []);
  
  const toggleSidebar = useCallback(() => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  }, []);
  
  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: { query } });
  }, []);
  
  const setSortBy = useCallback((sortBy: 'date' | 'name' | 'updated') => {
    dispatch({ type: 'SET_SORT_BY', payload: { sortBy } });
  }, []);
  
  const contextValue: ThinkingCanvasContextType = {
    // 状态
    canvases: state.canvases,
    currentCanvas,
    editMode: state.editMode,
    sidebarCollapsed: state.sidebarCollapsed,
    isSaving: state.isSaving,
    searchQuery: state.searchQuery,
    sortBy: state.sortBy,
    
    // 操作方法
    createCanvas,
    deleteCanvas,
    updateCanvas,
    switchCanvas,
    setEditMode,
    toggleSidebar,
    setSearchQuery,
    setSortBy
  };
  
  return (
    <ThinkingCanvasContext.Provider value={contextValue}>
      {children}
    </ThinkingCanvasContext.Provider>
  );
};

// Hook
export const useThinkingCanvas = () => {
  const context = useContext(ThinkingCanvasContext);
  if (context === undefined) {
    throw new Error('useThinkingCanvas must be used within a ThinkingCanvasProvider');
  }
  return context;
};
