"use client";

import React from 'react';

interface WorldBuildingPanelHeaderProps {
  searchQuery: string;
  sortBy: 'name' | 'category' | 'importance';
  onSearchChange: (query: string) => void;
  onSortChange: (sortBy: 'name' | 'category' | 'importance') => void;
  onExtractWorldBuildings?: () => void;
  onCreateWorldBuilding: () => void;
  onBatchUpdateWorldBuildings?: () => void;
  onAICreateWorldBuildings?: () => void;
  multiSelectMode?: boolean;
  onToggleMultiSelectMode?: () => void;
}

/**
 * 世界观面板头部组件
 */
export const WorldBuildingPanelHeader: React.FC<WorldBuildingPanelHeaderProps> = ({
  searchQuery,
  sortBy,
  onSearchChange,
  onSortChange,
  onExtractWorldBuildings,
  onCreateWorldBuilding,
  onBatchUpdateWorldBuildings,
  onAICreateWorldBuildings,
  multiSelectMode = false,
  onToggleMultiSelectMode
}) => {
  // 创建按钮组件
  const renderButton = (
    text: string,
    type: 'primary' | 'secondary' | 'danger',
    onClick?: () => void
  ) => {
    // 根据类型设置样式
    const getButtonStyle = () => {
      switch (type) {
        case 'primary':
          return 'bg-blue-500 hover:bg-blue-600 text-white';
        case 'secondary':
          return 'bg-gray-200 hover:bg-gray-300 text-gray-700';
        case 'danger':
          return 'bg-red-500 hover:bg-red-600 text-white';
        default:
          return 'bg-gray-200 hover:bg-gray-300 text-gray-700';
      }
    };

    return (
      <button
        className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${getButtonStyle()}`}
        onClick={onClick}
        disabled={!onClick}
      >
        {text}
      </button>
    );
  };

  return (
    <div className="flex justify-between items-center px-6 py-4 border-b" style={{ backgroundColor: 'var(--color-primary-bg)' }}>
      <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>世界观管理</h2>

      <div className="flex items-center space-x-4">
        {/* 搜索框 */}
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="搜索世界观元素..."
            className="w-64 px-3 py-2 pl-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
            style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* 排序下拉菜单 */}
        <select
          value={sortBy}
          onChange={(e) => onSortChange(e.target.value as 'name' | 'category' | 'importance')}
          className="px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
          style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}
        >
          <option value="name">按名称排序</option>
          <option value="category">按类别排序</option>
          <option value="importance">按重要性排序</option>
        </select>

        {/* 按钮组 */}
        <div className="flex space-x-3">
          {onToggleMultiSelectMode && (
            <div className="transform hover:scale-105 transition-transform duration-300">
              {renderButton(
                multiSelectMode ? '取消多选' : '多选',
                multiSelectMode ? 'danger' : 'secondary',
                onToggleMultiSelectMode
              )}
            </div>
          )}

          {onBatchUpdateWorldBuildings && multiSelectMode && (
            <div className="transform hover:scale-105 transition-transform duration-300">
              {renderButton('批量更新', 'secondary', onBatchUpdateWorldBuildings)}
            </div>
          )}

          {onExtractWorldBuildings && (
            <div className="transform hover:scale-105 transition-transform duration-300">
              {renderButton('AI提取', 'secondary', onExtractWorldBuildings)}
            </div>
          )}

          {onAICreateWorldBuildings && (
            <div className="transform hover:scale-105 transition-transform duration-300">
              {renderButton('AI创建', 'secondary', onAICreateWorldBuildings)}
            </div>
          )}

          <div className="transform hover:scale-105 transition-transform duration-300">
            {renderButton('创建', 'primary', onCreateWorldBuilding)}
          </div>
        </div>
      </div>
    </div>
  );
};
