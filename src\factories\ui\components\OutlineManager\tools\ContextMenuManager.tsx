"use client";

import React, { useCallback } from 'react';
import { Node, Edge } from 'reactflow';
import { Outline, OutlineNodeType } from '../../../types/outline';
import ContextMenu from '../ContextMenu';
import { findNode } from '../../../utils/outlineUtils';

interface ContextMenuManagerProps {
  contextMenu: {
    x: number;
    y: number;
    type: 'node' | 'pane' | 'selection';
    nodeId?: string;
  } | null;
  setContextMenu: React.Dispatch<React.SetStateAction<{
    x: number;
    y: number;
    type: 'node' | 'pane' | 'selection';
    nodeId?: string;
  } | null>>;
  outline: Outline | null;
  rfSelectedNodes: Node[];
  setRfSelectedNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  handleNodeEdit: (nodeId: string) => void;
  handleNodeDelete: (nodeId: string) => void;
  handleAddChild: (parentId: string | null, type: string) => void;
  handleNodeSelect: (nodeId: string | null) => void;
  scissorsModeActive: boolean;
}

const ContextMenuManager: React.FC<ContextMenuManagerProps> = ({
  contextMenu,
  setContextMenu,
  outline,
  rfSelectedNodes,
  setRfSelectedNodes,
  handleNodeEdit,
  handleNodeDelete,
  handleAddChild,
  handleNodeSelect,
  scissorsModeActive
}) => {
  // 获取上下文菜单项
  const getContextMenuItems = useCallback(() => {
    if (!contextMenu) return [];
    const { type, nodeId } = contextMenu;
    // 定义菜单项结构
    const items: Array<{ label: string; onClick: () => void; icon?: string; [key: string]: any }> = [];

    if (type === 'node' && nodeId) {
      const node = findNode(outline?.nodes || [], nodeId);
      if (node) {
        // 节点操作组 - 移除编辑选项，避免与就地编辑重复
        items.push({
          label: `删除节点: ${node.title}`,
          onClick: () => { handleNodeDelete(nodeId); setContextMenu(null); },
          icon: '🗑️'
        });

        // 添加子节点组 - 直接菜单项
        items.push({ type: 'divider' });
        items.push({
          label: '添加总纲/卷',
          onClick: () => { handleAddChild(nodeId, 'volume'); setContextMenu(null); },
          icon: '📚'
        });
        items.push({
          label: '添加事件刚',
          onClick: () => { handleAddChild(nodeId, 'event'); setContextMenu(null); },
          icon: '⚡'
        });
        items.push({
          label: '添加子章节',
          onClick: () => { handleAddChild(nodeId, 'chapter'); setContextMenu(null); },
          icon: '📄'
        });
        items.push({
          label: '添加剧情节点',
          onClick: () => { handleAddChild(nodeId, 'plot'); setContextMenu(null); },
          icon: '🎬'
        });
        items.push({
          label: '添加对话节点',
          onClick: () => { handleAddChild(nodeId, 'dialogue'); setContextMenu(null); },
          icon: '💬'
        });
      }
    } else if (type === 'pane') {
      // 画布操作组 - 直接菜单项
      items.push({
        label: '新建总纲/卷',
        onClick: () => { handleAddChild(null, 'volume'); setContextMenu(null); },
        icon: '📚'
      });
      items.push({
        label: '新建事件刚',
        onClick: () => { handleAddChild(null, 'event'); setContextMenu(null); },
        icon: '⚡'
      });
      items.push({
        label: '新建根章节',
        onClick: () => { handleAddChild(null, 'chapter'); setContextMenu(null); },
        icon: '📄'
      });
      items.push({
        label: '新建剧情节点',
        onClick: () => { handleAddChild(null, 'plot'); setContextMenu(null); },
        icon: '🎬'
      });
      items.push({
        label: '新建核心故事梗概',
        onClick: () => { handleAddChild(null, 'synopsis'); setContextMenu(null); },
        icon: '📋'
      });
      items.push({
        label: '新建对话节点',
        onClick: () => { handleAddChild(null, 'dialogue'); setContextMenu(null); },
        icon: '💬'
      });
    } else if (type === 'selection') {
      if (rfSelectedNodes.length > 0) {
        items.push({
          label: `删除选中的 ${rfSelectedNodes.length} 个节点`,
          onClick: () => {
            if (confirm(`确定要删除选中的 ${rfSelectedNodes.length} 个节点吗？`)) {
              rfSelectedNodes.forEach(node => handleNodeDelete(node.id));
              setRfSelectedNodes([]);
            }
            setContextMenu(null);
          },
          icon: '🗑️'
        });
      }
    }

    // 添加关闭菜单选项
    if (items.length > 0) {
      items.push({ type: 'divider' });
    }
    items.push({ label: '关闭菜单', onClick: () => setContextMenu(null), icon: '❌' });

    return items;
  }, [contextMenu, outline, handleNodeEdit, handleNodeDelete, handleAddChild, rfSelectedNodes, setRfSelectedNodes, setContextMenu]);

  // 处理画布右键菜单
  const handlePaneContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    if (scissorsModeActive) return;
    // 使用clientX/clientY直接定位
    setContextMenu({ x: event.clientX, y: event.clientY, type: 'pane' });
  }, [scissorsModeActive, setContextMenu]);

  // 处理节点右键菜单
  const handleNodeContextMenu = useCallback((event: React.MouseEvent, node: Node) => {
    event.preventDefault();
    event.stopPropagation();
    // 使用clientX/clientY直接定位
    setContextMenu({ x: event.clientX, y: event.clientY, type: 'node', nodeId: node.id });
    handleNodeSelect(node.id); // 选中节点
  }, [setContextMenu, handleNodeSelect]);

  // 渲染上下文菜单
  return (
    <>
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          items={getContextMenuItems()}
          onClose={() => setContextMenu(null)}
        />
      )}
    </>
  );
};

export default ContextMenuManager;
