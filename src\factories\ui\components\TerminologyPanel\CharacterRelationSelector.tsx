"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';

interface CharacterRelationSelectorProps {
  allCharacters: Character[];
  selectedIds: string[];
  onChange: (ids: string[]) => void;
  currentTerminologyId?: string;
}

/**
 * 人物关联选择器组件
 */
export const CharacterRelationSelector: React.FC<CharacterRelationSelectorProps> = ({
  allCharacters,
  selectedIds,
  onChange,
  currentTerminologyId
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // 过滤人物列表
  const availableCharacters = allCharacters
    .filter(character => {
      if (searchQuery.trim() === '') return true;
      return character.name.toLowerCase().includes(searchQuery.toLowerCase());
    });

  // 处理人物选择
  const handleToggleCharacter = (id: string) => {
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter(sid => sid !== id));
    } else {
      onChange([...selectedIds, id]);
    }
  };

  return (
    <div className="space-y-2">
      {/* 搜索框 */}
      <div className="relative">
        <input
          type="text"
          placeholder="搜索人物..."
          className="w-full pl-10 pr-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            borderColor: 'rgba(139, 69, 19, 0.2)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 已选择的人物 */}
      {selectedIds.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedIds.map(id => {
            const character = allCharacters.find(c => c.id === id);
            if (!character) return null;
            
            return (
              <div
                key={id}
                className="flex items-center bg-blue-50 px-3 py-1 rounded-full"
                style={{
                  backgroundColor: 'rgba(139, 69, 19, 0.1)',
                  color: 'var(--color-primary)'
                }}
              >
                <span className="text-sm">{character.name}</span>
                <button
                  type="button"
                  onClick={() => handleToggleCharacter(id)}
                  className="ml-2 text-gray-500 hover:text-red-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* 人物列表 */}
      <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md" style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}>
        {availableCharacters.length === 0 ? (
          <div className="p-2 text-gray-500 text-center">没有找到匹配的人物</div>
        ) : (
          <div className="divide-y divide-gray-100">
            {availableCharacters.map(character => (
              <div
                key={character.id}
                className="p-2 flex items-center hover:bg-gray-50 cursor-pointer"
                onClick={() => character.id && handleToggleCharacter(character.id)}
              >
                <input
                  type="checkbox"
                  checked={selectedIds.includes(character.id || '')}
                  onChange={() => {}} // 通过父元素的onClick处理
                  className="mr-2 h-4 w-4"
                />
                <div>
                  <span className="text-sm font-medium" style={{ color: 'var(--color-primary)' }}>{character.name}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
