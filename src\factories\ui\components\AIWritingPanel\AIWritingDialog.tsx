"use client";

import React, { useState, useEffect } from 'react';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { PromptCategory, Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { Outline } from '@/factories/ui/types/outline';
import { aiWritingService, ConversationMessage } from '@/factories/ai/services/AIWritingService';
import { dialogContinuationService } from '@/factories/ai/services/DialogContinuationService';
import SelectorDialog from './SelectorDialog';

// 导入子组件
import AIWritingLeftPanel from './AIWritingLeftPanel';
import AIWritingPreview from './AIWritingPreview';
import AIWritingControls from './AIWritingControls';
import ContinuePromptModal from './ContinuePromptModal';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  terminologyIds?: string[];
  worldBuildingIds?: string[];
  summary?: string;
  notes?: string;
}

interface AIWritingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertContent: (content: string) => void;
  bookId: string;
}

/**
 * AI写作对话框组件
 * 用于使用AI生成小说内容
 */
const AIWritingDialog: React.FC<AIWritingDialogProps> = ({
  isOpen,
  onClose,
  onInsertContent,
  bookId
}) => {
  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节 - 使用localStorage持久化
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-chapters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });



  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 提示词状态
  const [writingStyle, setWritingStyle] = useState<string>('');
  const [requirements, setRequirements] = useState<string>('');
  const [corePlot, setCorePlot] = useState<string>('');
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);
  const [promptType, setPromptType] = useState<'style' | 'requirements'>('style');

  // 监听输入变化，保存到localStorage
  useEffect(() => {
    if (writingStyle) {
      localStorage.setItem(`ai-writing-style-${bookId}`, writingStyle);
    }
  }, [writingStyle, bookId]);

  useEffect(() => {
    if (requirements) {
      localStorage.setItem(`ai-writing-requirements-${bookId}`, requirements);
    }
  }, [requirements, bookId]);

  useEffect(() => {
    if (corePlot) {
      localStorage.setItem(`ai-writing-plot-${bookId}`, corePlot);
    }
  }, [corePlot, bookId]);

  // 生成状态
  const [isLoading, setIsLoading] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // 流式响应状态
  const [streamResponse, setStreamResponse] = useState('');

  // 对话历史状态 - 使用localStorage持久化
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>(() => {
    // 从localStorage加载对话历史
    const savedHistory = localStorage.getItem(`ai-writing-history-${bookId}`);
    return savedHistory ? JSON.parse(savedHistory) : [];
  });

  // 继续对话状态 - 使用localStorage持久化
  const [continueMode, setContinueMode] = useState<'continue' | 'rewrite' | 'analyze' | 'new'>(() => {
    const savedMode = localStorage.getItem(`ai-writing-mode-${bookId}`);
    return savedMode ? JSON.parse(savedMode) : 'new';
  });

  const [continuePrompt, setContinuePrompt] = useState<string>('');
  // 使用单一状态控制继续对话弹窗
  const [showContinuePromptModal, setShowContinuePromptModal] = useState(false);

  // 初始化时从localStorage加载保存的输入内容
  useEffect(() => {
    if (isOpen) {
      // 加载保存的写作风格
      const savedStyle = localStorage.getItem(`ai-writing-style-${bookId}`);
      if (savedStyle) {
        setWritingStyle(savedStyle);
      }

      // 加载保存的写作要求
      const savedReqs = localStorage.getItem(`ai-writing-requirements-${bookId}`);
      if (savedReqs) {
        setRequirements(savedReqs);
      }

      // 加载保存的核心剧情
      const savedPlot = localStorage.getItem(`ai-writing-plot-${bookId}`);
      if (savedPlot) {
        setCorePlot(savedPlot);
      }

      // 加载保存的流式响应
      const savedResponse = localStorage.getItem(`ai-writing-response-${bookId}`);
      if (savedResponse) {
        setStreamResponse(savedResponse);
      }

      // 加载保存的生成内容
      const savedContent = localStorage.getItem(`ai-writing-content-${bookId}`);
      if (savedContent) {
        setGeneratedContent(savedContent);
      }
    }
  }, [isOpen, bookId]);

  // 关联元素数据
  const [characters, setCharacters] = useState<Character[]>([]);
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);
  const [outlines, setOutlines] = useState<Outline[]>([]);
  const [isLoadingRelatedElements, setIsLoadingRelatedElements] = useState(false);

  // 选中的关联元素 - 使用localStorage持久化
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-characters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-terminologies-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-worldbuildings-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedOutlineIds, setSelectedOutlineIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-outline-ids-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedOutlineNodeIds, setSelectedOutlineNodeIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-writing-selected-outlines-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  // 弹窗状态
  const [isChapterSelectorOpen, setIsChapterSelectorOpen] = useState(false);
  const [isCharacterSelectorOpen, setIsCharacterSelectorOpen] = useState(false);
  const [isTerminologySelectorOpen, setIsTerminologySelectorOpen] = useState(false);
  const [isWorldBuildingSelectorOpen, setIsWorldBuildingSelectorOpen] = useState(false);

  // API设置
  const [apiSettings] = useState(() => {
    const settingsFactory = createSettingsFactory();
    return settingsFactory.createAPISettingsDialogComponent();
  });

  // 当对话框打开时加载章节和关联元素
  useEffect(() => {
    if (isOpen) {
      loadChapters();
      loadRelatedElements();
    }
  }, [isOpen, bookId]);

  // 监听关联元素选择变化并保存到localStorage
  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-chapters-${bookId}`, JSON.stringify(selectedChapterIds));
    }
  }, [selectedChapterIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-characters-${bookId}`, JSON.stringify(selectedCharacterIds));
    }
  }, [selectedCharacterIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-terminologies-${bookId}`, JSON.stringify(selectedTerminologyIds));
    }
  }, [selectedTerminologyIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-worldbuildings-${bookId}`, JSON.stringify(selectedWorldBuildingIds));
    }
  }, [selectedWorldBuildingIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-outline-ids-${bookId}`, JSON.stringify(selectedOutlineIds));
    }
  }, [selectedOutlineIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-writing-selected-outlines-${bookId}`, JSON.stringify(selectedOutlineNodeIds));
    }
  }, [selectedOutlineNodeIds, bookId]);

  /**
   * 过滤掉预制的消息，只保留真正的对话部分
   * @param messages 对话消息数组
   * @returns 过滤后的对话消息数组
   */
  const filterPresetMessages = (messages: ConversationMessage[]): ConversationMessage[] => {
    if (!messages || messages.length === 0) {
      return [];
    }

    // 创建一个副本，避免修改原始数组
    let filteredMessages = [...messages];

    // 保留系统消息
    const systemMessages = filteredMessages.filter(msg => msg.role === 'system');

    // 预制消息的特征
    const presetMessages = [
      '我已阅读并分析了',
      '我已记住',
      '我将查看并分析',
      '作为您的文学创作顾问',
      '我已完成所有章节内容的分析',
      '我将参考以下章节内容',
      '我将考虑以下人物信息',
      '我将遵循以下世界观设定',
      '我将正确使用以下术语',
      '以下是相关人物信息',
      '以下是相关世界观信息',
      '以下是相关术语信息',
      '我已理解您的要求',
      '我将确保改写后的内容',
      '我将确保当前章节',
      '我将确保与需要改写的内容',
      '我将基于这些内容进行创作',
      '我将根据您提供的要求',
      '我会在创作中考虑这些人物的特点和背景',
      '我会在创作中遵循这些世界设定',
      '我会在创作中正确使用这些术语',
      '作为您的文学创作顾问',
      '我已准备好协助您',
      '我已准备好协助您进行小说创作'
    ];

    // 定义系统和用户预制消息的特征
    const systemPresetMessages = [
      '你是一位专业的文学改写专家',
      '擅长根据要求改写文本内容',
      '你的任务是根据用户提供的要求',
      '请注意以下几点'
    ];

    const userPresetMessages = [
      '我需要您改写一段文本内容',
      '请根据我提供的上下文和要求进行改写',
      '【当前模式】',
      '【改写要求】',
      '【剧情方向】'
    ];

    // 过滤掉所有预制消息（包括系统、用户和助手消息）
    const realMessages = filteredMessages.filter(msg => {
      // 根据消息角色使用不同的过滤列表
      if (msg.role === 'system') {
        return !systemPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'user') {
        return !userPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'assistant') {
        return !presetMessages.some(preset => msg.content.includes(preset));
      }

      // 其他角色的消息保留
      return true;
    });

    // 如果过滤后没有任何消息，但原始消息中有系统消息，则保留系统消息
    if (realMessages.length === 0 && systemMessages.length > 0) {
      return systemMessages;
    }

    return realMessages;
  };

  /**
   * 计算文本的实际字数（忽略空格和换行符）
   * @param text 要计算字数的文本
   * @returns 实际字数
   */
  const calculateActualWordCount = (text: string): number => {
    if (!text) return 0;
    return text.replace(/\s+/g, '').length;
  };

  /**
   * 加载章节列表
   */
  const loadChapters = async () => {
    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 chapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
    } catch (error) {
      console.error('加载章节数据失败:', error);
      setError('获取章节数据失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingChapters(false);
    }
  };

  /**
   * 加载关联元素（人物、术语、世界观）
   */
  const loadRelatedElements = async () => {
    setIsLoadingRelatedElements(true);
    setError(null);

    try {
      // 加载人物
      try {
        const { characterRepository } = await import('@/lib/db/repositories');
        const charactersData = await characterRepository.getAllByBookId(bookId);
        setCharacters(charactersData);
      } catch (error) {
        console.error('加载人物数据失败:', error);
      }

      // 加载术语
      try {
        const { terminologyRepository } = await import('@/lib/db/repositories');
        const terminologiesData = await terminologyRepository.getAllByBookId(bookId);
        setTerminologies(terminologiesData);
      } catch (error) {
        console.error('加载术语数据失败:', error);
      }

      // 加载世界观
      try {
        const { worldBuildingRepository } = await import('@/lib/db/repositories');
        const worldBuildingsData = await worldBuildingRepository.getAllByBookId(bookId);
        setWorldBuildings(worldBuildingsData);
      } catch (error) {
        console.error('加载世界观数据失败:', error);
      }

      // 加载大纲
      try {
        const { outlineRepository } = await import('@/lib/db/repositories');
        const outlinesData = await outlineRepository.getAllByBookId(bookId);
        setOutlines(outlinesData);
      } catch (error) {
        console.error('加载大纲数据失败:', error);
      }
    } catch (error) {
      console.error('加载关联元素失败:', error);
      setError('加载关联元素失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingRelatedElements(false);
    }
  };



  /**
   * 处理提示词模板选择
   */
  const handleSelectTemplate = (template: any) => {
    // 根据当前选择的提示词类型，更新对应的状态
    switch (promptType) {
      case 'style':
        setWritingStyle(template.content);
        break;
      case 'requirements':
      default:
        setRequirements(template.content);
        break;
    }
    setIsPromptManagerOpen(false);
  };

  /**
   * 处理范围选择
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    if (!rangeStart || !rangeEnd || !chapters || chapters.length === 0) {
      alert('请输入有效的章节范围');
      return;
    }

    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      alert('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      alert('起始章节不能大于结束章节');
      return;
    }

    if (start > chapters.length || end > chapters.length) {
      alert(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      alert('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  /**
   * 处理统一关联管理的变化
   */
  const handleAssociationsChange = (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    setSelectedChapterIds(associations.chapterIds);
    setSelectedCharacterIds(associations.characterIds);
    setSelectedTerminologyIds(associations.terminologyIds);
    setSelectedWorldBuildingIds(associations.worldBuildingIds);
  };

  /**
   * 生成内容
   */
  const handleGenerate = async () => {
    setIsLoading(true);
    setError(null);
    setStreamResponse('');
    setGeneratedContent('');

    try {
      // 获取API设置
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        setError(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
        setIsLoading(false);
        return;
      }

      // 使用AIWritingService生成内容
      const result = await aiWritingService.generateContent(
        {
          // API设置
          provider: currentProvider,
          model: currentModel,
          apiKey: apiKey,
          apiEndpoint: apiEndpoint,

          // 内容设置
          writingStyle: writingStyle,
          requirements: requirements,
          corePlot: corePlot,

          // 关联元素
          chapters: chapters,
          selectedChapterIds: selectedChapterIds,
          characters: characters,
          selectedCharacterIds: selectedCharacterIds,
          terminologies: terminologies,
          selectedTerminologyIds: selectedTerminologyIds,
          worldBuildings: worldBuildings,
          selectedWorldBuildingIds: selectedWorldBuildingIds,

          // 大纲关联元素
          outlines: outlines,
          selectedOutlineIds: selectedOutlineIds,
          selectedOutlineNodeIds: selectedOutlineNodeIds,
          outlineContextMode: 'hierarchy', // 默认使用层级模式

          // 对话历史和继续对话
          conversationHistory: conversationHistory,
          continueMode: continueMode,
          continuePrompt: continuePrompt,

          // 书籍ID
          bookId: bookId
        },
        {
          onStart: () => {
            console.log('开始生成内容...');
          },
          onStreamChunk: (chunk: string) => {
            // 使用防抖函数减少更新频率
            // 累积内容，但减少状态更新和存储频率
            setStreamResponse(prevResponse => {
              // 如果是第一个块且有前缀，则添加前缀
              if (prevResponse === '' && continueMode !== 'new') {
                const prefix = localStorage.getItem(`ai-writing-prefix-${bookId}`) || '';
                const newResponse = prefix + chunk;

                // 使用自定义事件而不是直接更新localStorage，减少频繁IO操作
                // 只在累积一定数量的字符后才更新localStorage
                if (newResponse.length % 100 === 0) {
                  localStorage.setItem(`ai-writing-response-${bookId}`, newResponse);
                }

                return newResponse;
              } else {
                const newResponse = prevResponse + chunk;

                // 使用自定义事件而不是直接更新localStorage，减少频繁IO操作
                // 只在累积一定数量的字符后才更新localStorage
                if (newResponse.length % 100 === 0) {
                  localStorage.setItem(`ai-writing-response-${bookId}`, newResponse);
                }

                return newResponse;
              }
            });
          },
          onComplete: (result) => {
            console.log('内容生成完成');

            // 获取生成的内容
            let generated = result.text.trim();

            // 如果是继续对话模式，并且有前缀，则拼接前缀和生成的内容
            if (continueMode !== 'new') {
              const prefix = localStorage.getItem(`ai-writing-prefix-${bookId}`) || '';
              if (prefix && !streamResponse.startsWith(prefix)) {
                generated = prefix + generated;
              } else {
                // 如果流式响应已经包含了前缀，则使用流式响应
                generated = streamResponse;
              }
            }

            setGeneratedContent(generated);

            // 持久化存储生成内容
            localStorage.setItem(`ai-writing-content-${bookId}`, generated);

            // 保存对话历史，但先过滤掉预制消息
            if (result.conversationHistory) {
              // 过滤掉预制消息
              const filteredHistory = filterPresetMessages(result.conversationHistory);
              setConversationHistory(filteredHistory);

              // 持久化存储过滤后的对话历史
              localStorage.setItem(`ai-writing-history-${bookId}`, JSON.stringify(filteredHistory));
            }

            // 重置继续对话状态
            setContinuePrompt('');
            setShowContinuePromptModal(false);

            // 清除前缀缓存
            localStorage.removeItem(`ai-writing-prefix-${bookId}`);
          },
          onError: (error) => {
            console.error('生成内容失败:', error);
            setError(error.message || '生成内容时发生错误');
          }
        }
      );

      if (!result.success) {
        setError(result.error || '生成内容失败');
      }

    } catch (error: any) {
      console.error('生成内容失败:', error);
      setError(error instanceof Error ? error.message : '生成内容时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 应用生成的内容
   */
  const handleApply = () => {
    if (generatedContent) {
      onInsertContent(generatedContent);
      onClose();
    }
  };

  /**
   * 提交继续对话
   */
  const handleSubmitContinue = () => {
    // 保存当前生成的内容，以便在新内容生成后拼接
    const currentContent = streamResponse || generatedContent;

    // 使用 DialogContinuationService 处理继续对话
    const result = dialogContinuationService.handleContinuation({
      currentContent,
      continuePrompt,
      continueMode,
      bookId,
      conversationHistory
    });

    // 设置流式响应为前缀
    setStreamResponse(result.prefix);

    // 更新对话历史
    setConversationHistory(result.updatedHistory);

    // 开始生成
    handleGenerate();
  };

  /**
   * 重新开始对话
   */
  const handleRestartConversation = () => {
    // 清空对话历史
    setConversationHistory([]);
    localStorage.removeItem(`ai-writing-history-${bookId}`);

    // 重置继续对话状态
    setContinueMode('new');
    localStorage.setItem(`ai-writing-mode-${bookId}`, JSON.stringify('new'));
    setContinuePrompt('');
    setShowContinuePromptModal(false);

    // 清空生成内容
    setGeneratedContent('');
    setStreamResponse('');

    // 清除localStorage中的流式响应和生成内容
    localStorage.removeItem(`ai-writing-response-${bookId}`);
    localStorage.removeItem(`ai-writing-content-${bookId}`);

    // 注意：不清除写作风格、要求和核心剧情，这些是用户输入的内容，应该保留
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-[95%] max-w-7xl max-h-[95vh] flex flex-col overflow-hidden">
        {/* 头部 */}
        <div className="p-4 border-b flex justify-between items-center bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center">
            <div className="bg-blue-600 text-white p-2 rounded-lg mr-3 shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-blue-700">AI写作助手</h2>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 - 双栏布局 */}
        <div className="flex-1 overflow-hidden p-5 flex" style={{ minHeight: '700px' }}>
          {/* 左侧栏：关联元素和提示词 */}
          <AIWritingLeftPanel
            bookId={bookId}
            selectedChapterIds={selectedChapterIds}
            selectedCharacterIds={selectedCharacterIds}
            selectedTerminologyIds={selectedTerminologyIds}
            selectedWorldBuildingIds={selectedWorldBuildingIds}
            selectedOutlineNodeIds={selectedOutlineNodeIds}
            writingStyle={writingStyle}
            requirements={requirements}
            corePlot={corePlot}
            error={error}

            onWritingStyleChange={setWritingStyle}
            onRequirementsChange={setRequirements}
            onCorePlotChange={setCorePlot}
            onOpenStyleTemplates={() => {
              setPromptType('style');
              setIsPromptManagerOpen(true);
            }}
            onOpenRequirementsTemplates={() => {
              setPromptType('requirements');
              setIsPromptManagerOpen(true);
            }}
            onAssociationsChange={handleAssociationsChange}
            onOutlineNodesChange={setSelectedOutlineNodeIds}
          />

          {/* 右侧栏：预览内容 */}
          <AIWritingPreview
            streamResponse={streamResponse}
            isLoading={isLoading}
            generatedContent={generatedContent}
            bookId={bookId}
            onApply={handleApply}
            onRestart={handleRestartConversation}
            onInsertBubble={(content) => {
              // 插入单个气泡内容到编辑器
              onInsertContent(content);
            }}
            onContinue={() => {
              setContinueMode('continue');
              localStorage.setItem(`ai-writing-mode-${bookId}`, JSON.stringify('continue'));
              setShowContinuePromptModal(true);
            }}
          />
        </div>

        {/* 继续对话弹窗 - 使用模态框 */}
        <ContinuePromptModal
          isOpen={showContinuePromptModal && !!generatedContent && !isLoading}
          continuePrompt={continuePrompt}
          onContinuePromptChange={setContinuePrompt}
          onClose={() => setShowContinuePromptModal(false)}
          onSubmit={handleSubmitContinue}
          mode={continueMode}
        />

        {/* 注意：我们不再使用底部面板，改为使用弹窗 */}

        {/* 底部按钮 */}
        <AIWritingControls
          selectedChapterIds={selectedChapterIds}
          selectedCharacterIds={selectedCharacterIds}
          selectedTerminologyIds={selectedTerminologyIds}
          selectedWorldBuildingIds={selectedWorldBuildingIds}
          selectedOutlineNodeIds={selectedOutlineNodeIds}
          isLoading={isLoading}
          generatedContent={generatedContent}
          bookId={bookId}
          onCancel={onClose}
          onGenerate={handleGenerate}
          onApply={handleApply}
          onContinue={() => {
            setContinueMode('continue');
            localStorage.setItem(`ai-writing-mode-${bookId}`, JSON.stringify('continue'));
            setShowContinuePromptModal(true);
          }}
          onRewrite={() => {
            setContinueMode('rewrite');
            localStorage.setItem(`ai-writing-mode-${bookId}`, JSON.stringify('rewrite'));
            setShowContinuePromptModal(true);
          }}
          onAnalyze={() => {
            setContinueMode('analyze');
            localStorage.setItem(`ai-writing-mode-${bookId}`, JSON.stringify('analyze'));
            setShowContinuePromptModal(true);
          }}
        />

        {/* 提示词模板管理器 */}
        <PromptTemplateManager
          isOpen={isPromptManagerOpen}
          onClose={() => setIsPromptManagerOpen(false)}
          category={promptType === 'style' ? PromptCategory.WRITING_STYLE : PromptCategory.WRITING_REQUIREMENTS}
          onSelectTemplate={handleSelectTemplate}
          initialPrompt={
            promptType === 'style' ? writingStyle : requirements
          }
        />

        {/* 章节选择器对话框 */}
        <SelectorDialog
          isOpen={isChapterSelectorOpen}
          onClose={() => setIsChapterSelectorOpen(false)}
          title="选择参考章节"
          items={chapters.map(chapter => {
            // 使用辅助函数计算实际字数
            const actualWordCount = calculateActualWordCount(chapter.content);

            return {
              id: chapter.id!,
              name: chapter.title || `第${chapter.chapterNumber || '?'}章`,
              description: `${actualWordCount}字 · ${chapter.updatedAt ? new Date(chapter.updatedAt).toLocaleDateString('zh-CN') : '未知日期'}`
            };
          })}
          selectedIds={selectedChapterIds}
          onSelect={setSelectedChapterIds}
          isLoading={isLoadingChapters}
          loadingText="加载章节中..."
          emptyText="没有找到章节"
          extraContent={
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
              <h4 className="text-sm font-medium text-blue-800 mb-2">章节范围选择</h4>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={rangeStart}
                  onChange={(e) => setRangeStart(e.target.value)}
                  placeholder="起始章节"
                  className="w-24 px-2 py-1 border border-blue-300 rounded-md text-sm"
                  min="1"
                />
                <span className="text-blue-500">至</span>
                <input
                  type="number"
                  value={rangeEnd}
                  onChange={(e) => setRangeEnd(e.target.value)}
                  placeholder="结束章节"
                  className="w-24 px-2 py-1 border border-blue-300 rounded-md text-sm"
                  min="1"
                />
                <button
                  onClick={() => handleRangeSelect('select')}
                  className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 transition-colors"
                >
                  选择范围
                </button>
                <button
                  onClick={() => handleRangeSelect('deselect')}
                  className="px-3 py-1 bg-red-500 text-white rounded-md text-sm hover:bg-red-600 transition-colors"
                >
                  取消范围
                </button>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                提示: 输入章节编号范围（如1至5）快速选择多个章节
              </p>
            </div>
          }
        />

        {/* 人物选择器对话框 */}
        <SelectorDialog
          isOpen={isCharacterSelectorOpen}
          onClose={() => setIsCharacterSelectorOpen(false)}
          title="选择关联人物"
          items={characters.map(character => ({
            id: character.id!,
            name: character.name,
            description: character.description
          }))}
          selectedIds={selectedCharacterIds}
          onSelect={setSelectedCharacterIds}
          isLoading={isLoadingRelatedElements}
          loadingText="加载人物中..."
          emptyText="没有找到人物"
        />

        {/* 术语选择器对话框 */}
        <SelectorDialog
          isOpen={isTerminologySelectorOpen}
          onClose={() => setIsTerminologySelectorOpen(false)}
          title="选择关联术语"
          items={terminologies.map(terminology => ({
            id: terminology.id!,
            name: terminology.name,
            description: terminology.description
          }))}
          selectedIds={selectedTerminologyIds}
          onSelect={setSelectedTerminologyIds}
          isLoading={isLoadingRelatedElements}
          loadingText="加载术语中..."
          emptyText="没有找到术语"
        />

        {/* 世界观选择器对话框 */}
        <SelectorDialog
          isOpen={isWorldBuildingSelectorOpen}
          onClose={() => setIsWorldBuildingSelectorOpen(false)}
          title="选择关联世界观"
          items={worldBuildings.map(worldBuilding => ({
            id: worldBuilding.id!,
            name: worldBuilding.name,
            description: worldBuilding.description
          }))}
          selectedIds={selectedWorldBuildingIds}
          onSelect={setSelectedWorldBuildingIds}
          isLoading={isLoadingRelatedElements}
          loadingText="加载世界观中..."
          emptyText="没有找到世界观元素"
        />
      </div>
    </div>
  );
};

export default AIWritingDialog;
