"use client";

import React from 'react';
import { Terminology } from '@/lib/db/dexie';
import { BookmarkIcon } from '@/components/icons';

interface TerminologyListProps {
  terminologies: Terminology[];
  isLoading: boolean;
  searchQuery: string;
  selectedTerminology: Terminology | null;
  onSelectTerminology: (terminology: Terminology) => void;
  onCreateTerminology: () => void;
  onDeleteTerminology: (terminology: Terminology) => void;
}

/**
 * 术语列表组件
 */
export const TerminologyList: React.FC<TerminologyListProps> = ({
  terminologies,
  isLoading,
  searchQuery,
  selectedTerminology,
  onSelectTerminology,
  onCreateTerminology,
  onDeleteTerminology
}) => {
  // 创建按钮组件
  const renderCreateButton = () => (
    <button
      className="px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm font-medium transition-colors"
      onClick={onCreateTerminology}
    >
      创建术语
    </button>
  );

  // 获取术语类别的中文名称
  const getCategoryLabel = (category: string): string => {
    const categoryMap: Record<string, string> = {
      'item': '物品/道具',
      'skill': '技能/能力',
      'organization': '组织/势力',
      'location': '地点/区域',
      'concept': '概念/规则',
      'event': '事件/历史',
      'system': '系统/机制',
      'creature': '生物/种族',
      'other': '其他'
    };

    return categoryMap[category] || category;
  };

  // 获取重要性星级显示
  const getImportanceStars = (importance: string | undefined): string => {
    if (!importance) return '';

    const level = parseInt(importance);
    if (isNaN(level)) return '';

    return '⭐'.repeat(level);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 列表头部 */}
      <div className="p-4 border-b border-gray-200 flex justify-between items-center" style={{
        backgroundColor: 'rgba(245, 242, 233, 0.7)',
        borderBottom: '1px solid rgba(139, 69, 19, 0.1)'
      }}>
        <h3 className="text-lg font-medium" style={{ color: '#8B4513' }}>术语列表</h3>
        {renderCreateButton()}
      </div>

      {/* 列表内容 */}
      <div className="flex-1 overflow-auto" style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: '#8B4513' }}></div>
          </div>
        ) : terminologies.length === 0 ? (
          <div className="flex flex-col justify-center items-center h-full text-gray-500" style={{ padding: '40px 0' }}>
            {searchQuery ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <p>没有找到匹配的术语</p>
                <p className="text-sm mt-2 text-gray-400">尝试使用不同的搜索词</p>
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p>还没有创建任何术语</p>
                <p className="text-sm mt-2 text-gray-400">点击"创建术语"按钮开始添加</p>
              </>
            )}
          </div>
        ) : (
          <ul className="divide-y divide-gray-100">
            {terminologies.map((terminology) => (
              <li
                key={terminology.id || 'new'}
                className="panel-list-item cursor-pointer transition-all duration-200"
                style={{
                  padding: '12px 16px',
                  margin: '4px 8px',
                  borderRadius: '12px',
                  backgroundColor: selectedTerminology && selectedTerminology.id === terminology.id
                    ? 'rgba(139, 69, 19, 0.08)'
                    : 'rgba(255, 255, 255, 0.7)',
                  boxShadow: selectedTerminology && selectedTerminology.id === terminology.id
                    ? '0 2px 8px rgba(139, 69, 19, 0.1)'
                    : '0 1px 3px rgba(0, 0, 0, 0.02)',
                  transform: selectedTerminology && selectedTerminology.id === terminology.id
                    ? 'translateX(5px) scale(1.01)'
                    : 'translateX(0) scale(1)',
                  borderLeft: selectedTerminology && selectedTerminology.id === terminology.id
                    ? '3px solid #8B4513'
                    : '3px solid transparent'
                }}
                onClick={() => onSelectTerminology(terminology)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3 flex-1">
                    <BookmarkIcon
                      size="sm"
                      animated={true}
                      className="mt-0.5 text-amber-700 flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium" style={{ color: '#8B4513' }}>{terminology.name || '未命名术语'}</h4>
                      <div className="flex items-center mt-1">
                        {terminology.category && (
                          <span className="inline-block text-xs px-2 py-1 rounded-full mr-2" style={{
                            backgroundColor: 'rgba(139, 69, 19, 0.1)',
                            color: '#8B4513'
                          }}>
                            {getCategoryLabel(terminology.category)}
                          </span>
                        )}
                        {terminology.attributes?.importance && (
                          <span className="text-xs text-yellow-600">
                            {getImportanceStars(terminology.attributes.importance)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    className="text-gray-400 hover:text-red-500 transition-all duration-300 p-2 rounded-full hover:bg-red-50"
                    style={{
                      opacity: 0.7,
                      transform: 'scale(0.9)',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteTerminology(terminology);
                    }}
                    title="删除术语"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                {terminology.description && (
                  <p className="text-sm mt-2 line-clamp-2" style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    {terminology.description}
                  </p>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};
