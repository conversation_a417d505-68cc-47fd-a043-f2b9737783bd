import { IUIFactory } from './interfaces/IUIFactory';
import { IButtonComponent, ButtonType, ButtonSize } from './interfaces/IButtonComponent';
import { ICircleButtonComponent, CircleButtonSize } from './interfaces/ICircleButtonComponent';
import { IConfirmDialogComponent, ConfirmDialogType } from './interfaces/IConfirmDialogComponent';
import { IPanelComponent, PanelSize } from './interfaces/IPanelComponent';
import { DefaultButtonComponent } from './components/DefaultButton';
import { DefaultCircleButtonComponent } from './components/DefaultCircleButton';
import { DefaultConfirmDialogComponent } from './components/DefaultConfirmDialog';
import { DefaultPanelComponent } from './components/DefaultPanel';

/**
 * 默认UI工厂实现
 */
class DefaultUIFactory implements IUIFactory {
  /**
   * 创建按钮组件
   * @param text 按钮文本
   * @param type 按钮类型
   * @param size 按钮尺寸
   */
  createButtonComponent(
    text: string = '',
    type: ButtonType = 'primary',
    size: ButtonSize = 'medium'
  ): IButtonComponent {
    const button = new DefaultButtonComponent();
    button.setText(text);
    button.setType(type);
    button.setSize(size);
    return button;
  }

  /**
   * 创建圆形按钮组件
   * @param icon 按钮图标
   * @param text 按钮文本
   * @param color 按钮颜色
   * @param size 按钮尺寸
   */
  createCircleButtonComponent(
    icon: React.ReactNode,
    text: string = '',
    color: string = 'var(--color-info)',
    size: CircleButtonSize = 'medium'
  ): ICircleButtonComponent {
    const button = new DefaultCircleButtonComponent();
    button.setIcon(icon);
    button.setText(text);
    button.setColor(color);
    button.setSize(size);
    return button;
  }

  /**
   * 创建确认对话框组件
   * @param title 对话框标题
   * @param message 对话框消息
   * @param isOpen 是否打开
   * @param confirmText 确认按钮文本
   * @param cancelText 取消按钮文本
   * @param confirmType 确认按钮类型
   */
  createConfirmDialogComponent(
    title: string,
    message: string,
    isOpen: boolean = false,
    confirmText: string = '确认',
    cancelText: string = '取消',
    confirmType: ConfirmDialogType = 'primary'
  ): IConfirmDialogComponent {
    const dialog = new DefaultConfirmDialogComponent();
    dialog.setTitle(title);
    dialog.setMessage(message);
    dialog.setIsOpen(isOpen);
    dialog.setConfirmText(confirmText);
    dialog.setCancelText(cancelText);
    dialog.setConfirmType(confirmType);
    return dialog;
  }

  /**
   * 创建面板组件
   * @param title 面板标题
   * @param isOpen 是否打开
   * @param size 面板尺寸
   * @param options 其他选项
   */
  createPanelComponent(
    title: string,
    isOpen: boolean = false,
    size: PanelSize = 'medium',
    options: {
      fixedHeight?: boolean;
      backgroundColor?: string;
      width?: string;
      height?: string;
    } = {}
  ): IPanelComponent {
    const panel = new DefaultPanelComponent();
    panel.setTitle(title);
    panel.setIsOpen(isOpen);
    panel.setSize(size);

    // 设置其他选项
    if (options.fixedHeight !== undefined) {
      panel.setFixedHeight(options.fixedHeight);
    }

    if (options.backgroundColor !== undefined) {
      panel.setBackgroundColor(options.backgroundColor);
    }

    if (options.width !== undefined) {
      panel.setWidth(options.width);
    }

    if (options.height !== undefined) {
      panel.setHeight(options.height);
    }

    return panel;
  }
}

/**
 * 创建UI工厂
 * @param style 样式，默认为'default'
 * @returns UI工厂实例
 */
export function createUIFactory(style: 'default' | 'dark' = 'default'): IUIFactory {
  switch (style) {
    case 'default':
    default:
      return new DefaultUIFactory();
  }
}
