"use client";

import React, { useEffect, useState } from 'react';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';

interface SettingsDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
  currentFont?: string;
  onFontChange?: (font: string) => void;
}

/**
 * 设置弹窗适配器组件
 */
const SettingsDialog: React.FC<SettingsDialogProps> = ({
  isOpen = false,
  onClose,
  currentFont = 'roboto',
  onFontChange
}) => {
  // 创建设置工厂和组件
  const settingsFactory = createSettingsFactory();
  const settingsDialog = settingsFactory.createSettingsDialogComponent();
  
  // 设置初始状态
  useEffect(() => {
    settingsDialog.setIsOpen(isOpen);
  }, [isOpen]);
  
  useEffect(() => {
    settingsDialog.setCurrentFont(currentFont);
  }, [currentFont]);
  
  // 设置回调
  useEffect(() => {
    if (onFontChange) {
      settingsDialog.onFontChange(onFontChange);
    }
    
    // 监听关闭事件
    const originalSetIsOpen = settingsDialog.setIsOpen;
    settingsDialog.setIsOpen = (newIsOpen: boolean) => {
      originalSetIsOpen.call(settingsDialog, newIsOpen);
      if (!newIsOpen && onClose) {
        onClose();
      }
    };
    
    return () => {
      // 恢复原始方法
      settingsDialog.setIsOpen = originalSetIsOpen;
    };
  }, [onClose, onFontChange]);
  
  return <>{settingsDialog.render()}</>;
};

export default SettingsDialog;
