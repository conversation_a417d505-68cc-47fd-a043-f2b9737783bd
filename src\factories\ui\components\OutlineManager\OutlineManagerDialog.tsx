"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/adapters/ui';
import { createUIFactory } from '../../UIFactory';
import { OutlineTree } from './OutlineTree';
import { useOutlineManager } from '../../hooks/useOutlineManager';
import { Outline, OutlineNodeType } from '../../types/outline';
import { createAnimationFactory } from '@/factories/animation';
import ViewSwitcher, { OutlineViewType } from './ViewSwitcher';
import OutlineCanvas from './OutlineCanvas';

// 节点类型中文映射函数
const getNodeTypeLabel = (type: string): string => {
  switch (type) {
    case 'volume':
      return '总纲/卷';
    case 'event':
      return '事件刚';
    case 'chapter':
      return '章节';
    case 'plot':
      return '剧情节点';
    case 'dialogue':
      return '对话设计';
    default:
      return type || '未知';
  }
};

interface OutlineManagerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bookId?: string;
  bookTitle?: string;
}

/**
 * 大纲管理对话框组件
 * 优化弹窗体验，添加动画和模糊背景
 */
export const OutlineManagerDialog: React.FC<OutlineManagerDialogProps> = ({
  isOpen,
  onClose,
  bookId,
  bookTitle = '未命名作品'
}) => {
  const { outline, saveOutline } = useOutlineManager();
  const [localOutline, setLocalOutline] = useState<Outline | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [currentView, setCurrentView] = useState<OutlineViewType>('tree');
  const dialogRef = useRef<HTMLDivElement>(null);

  // 创建UI工厂
  const uiFactory = createUIFactory();

  // 创建动画工厂
  const animationFactory = createAnimationFactory();
  const fadeAnimation = animationFactory.createFadeAnimation('none', 400, 0, isOpen && !isClosing);
  const scaleAnimation = animationFactory.createScaleAnimation(0.95, 1.0, 400, 0, isOpen && !isClosing);

  // 处理关闭动画
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };

  // 当outline变化时更新localOutline
  useEffect(() => {
    console.log('大纲数据变化:', outline);

    if (outline) {
      try {
        // 确保大纲有ID和workId
        const updatedOutline = { ...outline };

        if (!updatedOutline.id) {
          console.log('大纲缺少ID，添加ID');
          updatedOutline.id = `outline-${Date.now()}`;
        }

        if (!updatedOutline.workId && bookId) {
          console.log('大纲缺少workId，添加workId:', bookId);
          updatedOutline.workId = bookId;
        }

        // 确保nodes字段存在
        if (!updatedOutline.nodes) {
          console.log('初始化空nodes数组');
          updatedOutline.nodes = [];
        }

        // 确保nodes是数组类型
        if (!Array.isArray(updatedOutline.nodes)) {
          console.warn('nodes不是数组类型，重置为空数组');
          updatedOutline.nodes = [];
        }

        // 确保lastModified是Date类型
        if (!(updatedOutline.lastModified instanceof Date)) {
          console.log('lastModified不是Date类型，转换为Date');
          updatedOutline.lastModified = new Date(updatedOutline.lastModified || Date.now());
        }

        // 确保version是数字类型
        if (typeof updatedOutline.version !== 'number') {
          console.log('version不是数字类型，设置为1');
          updatedOutline.version = 1;
        }

        setLocalOutline(updatedOutline);
      } catch (error) {
        console.error('处理大纲数据时出错:', error);

        // 创建一个新的大纲
        const newOutline = {
          id: `outline-${Date.now()}`,
          workId: bookId,
          title: '大纲',
          nodes: [],
          lastModified: new Date(),
          version: 1
        };

        setLocalOutline(newOutline);
      }
    } else if (isOpen && bookId) {
      // 如果没有大纲数据但对话框已打开，创建一个新的大纲
      console.log('没有大纲数据但对话框已打开，创建新大纲');

      const newOutline = {
        id: `outline-${Date.now()}`,
        workId: bookId,
        title: '大纲',
        nodes: [],
        lastModified: new Date(),
        version: 1
      };

      console.log('创建新大纲:', newOutline);
      setLocalOutline(newOutline);
    } else {
      setLocalOutline(null);
    }
  }, [outline, isOpen, bookId]);

  // 组件挂载时记录日志
  useEffect(() => {
    console.log('大纲管理对话框挂载, isOpen:', isOpen, 'bookId:', bookId);

    return () => {
      console.log('大纲管理对话框卸载');
    };
  }, [isOpen, bookId]);

  // 处理保存
  const handleSave = async () => {
    if (!localOutline) {
      console.error('没有大纲数据可保存');
      return;
    }

    console.log('开始保存大纲:', localOutline);

    try {
      setIsSaving(true);

      // 确保大纲有ID和workId
      const outlineToSave = { ...localOutline };

      if (!outlineToSave.id) {
        console.log('大纲缺少ID，添加ID');
        outlineToSave.id = `outline-${Date.now()}`;
      }

      if (!outlineToSave.workId && bookId) {
        console.log('大纲缺少workId，添加workId:', bookId);
        outlineToSave.workId = bookId;
      }

      // 确保lastModified和version字段存在
      outlineToSave.lastModified = new Date();
      outlineToSave.version = outlineToSave.version ? outlineToSave.version + 1 : 1;

      // 保存大纲
      console.log('调用saveOutline保存大纲', outlineToSave);

      // 尝试最多3次保存
      let savedOutline = null;
      let saveError = null;

      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`保存尝试 #${attempt}`);
          savedOutline = await saveOutline(outlineToSave);
          console.log('大纲保存成功:', savedOutline);
          break;
        } catch (error) {
          console.error(`保存尝试 #${attempt} 失败:`, error);
          saveError = error;

          if (attempt < 3) {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      if (savedOutline) {
        // 保存成功，更新本地状态
        setLocalOutline(savedOutline);
        onClose();
      } else {
        // 所有尝试都失败
        console.error('所有保存尝试都失败:', saveError);
        alert('保存大纲失败，请稍后重试。');
      }
    } catch (error) {
      console.error('保存大纲过程中发生错误:', error);
      alert('保存大纲时发生错误，请稍后重试。');
    } finally {
      setIsSaving(false);
    }
  };

  // 处理添加根节点
  const handleAddRootNode = () => {
    if (!localOutline) return;

    const newNode: OutlineNodeType = {
      id: `node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      title: `新${getNodeTypeLabel('chapter')}`,
      type: 'chapter',
      children: []
    };

    // 获取当前选中的节点（如果有）
    const selectedNode = document.querySelector('.outline-tree .bg-white.border-[var\\(--outline-primary\\)], .outline-tree .bg-white.border-[var\\(--outline-secondary\\)], .outline-tree .bg-white.border-[var\\(--outline-info\\)]');

    if (selectedNode && currentView === 'tree') {
      // 如果在树视图中有选中的节点，尝试获取节点ID
      const nodeId = selectedNode.closest('li')?.getAttribute('data-node-id');

      if (nodeId) {
        // 递归查找选中的节点并添加子节点
        const addChildToSelectedNode = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
          return nodes.map(node => {
            if (node.id === nodeId) {
              return {
                ...node,
                children: [...(node.children || []), newNode],
                expanded: true // 确保父节点展开
              };
            }

            if (node.children && node.children.length > 0) {
              return {
                ...node,
                children: addChildToSelectedNode(node.children)
              };
            }

            return node;
          });
        };

        setLocalOutline({
          ...localOutline,
          nodes: addChildToSelectedNode(localOutline.nodes)
        });
        return;
      }
    }

    // 如果没有选中节点或者不在树视图中，则添加为根节点
    setLocalOutline({
      ...localOutline,
      nodes: [...localOutline.nodes, newNode]
    });
  };

  // 处理大纲变更
  const handleOutlineChange = async (updatedOutline: Outline) => {
    console.log('🔥 OutlineManagerDialog处理大纲变更:', updatedOutline);

    // 立即更新本地状态
    setLocalOutline(updatedOutline);

    // 🔥 关键修复：自动保存到数据库
    if (bookId && updatedOutline) {
      try {
        console.log('🔥 自动保存大纲变更到数据库');

        // 确保大纲有必要的字段
        const outlineToSave = { ...updatedOutline };

        if (!outlineToSave.id) {
          outlineToSave.id = `outline-${Date.now()}`;
        }

        if (!outlineToSave.workId) {
          outlineToSave.workId = bookId;
        }

        // 更新修改时间和版本
        outlineToSave.lastModified = new Date();
        outlineToSave.version = outlineToSave.version ? outlineToSave.version + 1 : 1;

        // 保存到数据库
        await saveOutline(outlineToSave);
        console.log('✅ 大纲变更已自动保存到数据库');

      } catch (error) {
        console.error('❌ 自动保存大纲变更失败:', error);
        // 不显示错误提示，避免干扰用户体验
        // 用户可以通过手动保存按钮重试
      }
    }
  };

  // 处理全屏切换
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理视图切换
  const handleViewChange = (view: OutlineViewType) => {
    setCurrentView(view);
  };

  // 处理点击模态框背景
  const handleModalClick = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止点击内容区域关闭模态框
    e.stopPropagation();
  };

  // 获取动画样式
  const fadeStyle = fadeAnimation.getStyle();
  const scaleStyle = scaleAnimation.getStyle();

  // 如果对话框未打开且未处于关闭动画中，则不渲染
  if (!isOpen && !isClosing) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        backgroundColor: `rgba(0, 0, 0, ${isClosing ? 0 : 0.5})`,
        backdropFilter: `blur(${isClosing ? 0 : 5}px)`,
        transition: 'background-color 0.3s ease, backdrop-filter 0.3s ease',
        ...fadeStyle
      }}
      onClick={handleClose}
    >
      <div
        ref={dialogRef}
        className={`bg-[var(--color-primary-bg)] rounded-2xl shadow-xl flex flex-col overflow-hidden transition-all duration-300 ${
          isFullscreen ? 'w-full h-full rounded-none' : 'w-[90%] max-w-4xl h-[85%]'
        }`}
        style={{
          transform: isClosing ? 'scale(0.95)' : 'scale(1)',
          opacity: isClosing ? 0 : 1,
          ...scaleStyle
        }}
        onClick={handleModalClick}
      >
        {/* 标题栏 */}
        <div className="px-6 py-4 border-b border-[var(--color-secondary)] border-opacity-30 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-[var(--color-primary)]">{`${bookTitle} - 大纲管理`}</h2>
          <div className="flex items-center space-x-2">
            {/* 全屏切换按钮 */}
            <button
              className="p-2 rounded-full hover:bg-gray-100 text-gray-600 transition-colors duration-200"
              onClick={toggleFullscreen}
              title={isFullscreen ? "退出全屏" : "全屏显示"}
            >
              {isFullscreen ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9L4 4m0 0l5 5m-5-5v5m16-5l-5 5m5 0l-5-5m5 5h-5" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                </svg>
              )}
            </button>
            {/* 关闭按钮 */}
            <button
              className="p-2 rounded-full hover:bg-gray-100 text-gray-600 transition-colors duration-200"
              onClick={handleClose}
              title="关闭"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex flex-col flex-1 p-6 overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <p>加载中...</p>
            </div>
          ) : (
            <>
              <div className="flex justify-between mb-4">
                <p className="text-gray-600">
                  {localOutline?.nodes.length || 0} 个节点
                </p>
                <div className="flex items-center space-x-3">
                  {/* 视图切换器 */}
                  <ViewSwitcher currentView={currentView} onViewChange={handleViewChange} />

                  <div className="flex">
                    <button
                      className="mr-2 p-2 rounded-full hover:bg-gray-100"
                      title="查看历史版本"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                    <button
                      className="p-2 rounded-full hover:bg-gray-100"
                      title="添加章节"
                      onClick={handleAddRootNode}
                      data-testid="add-node-button"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-auto bg-white rounded-lg shadow-inner">
                {currentView === 'tree' && (
                  <OutlineTree
                    outline={localOutline}
                    bookId={bookId}
                    onChange={handleOutlineChange}
                  />
                )}

                {currentView === 'canvas' && (
                  <div className="h-full">
                    <OutlineCanvas
                      outline={localOutline}
                      bookId={bookId}
                      onChange={handleOutlineChange}
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* 底部操作区 */}
        <div className="px-6 py-4 border-t border-[var(--color-secondary)] border-opacity-30 bg-[var(--color-primary-bg)] bg-opacity-90 backdrop-blur-sm">
          <div className="flex justify-end space-x-3">
            <Button
              text="取消"
              type="secondary"
              onClick={handleClose}
              className="hover:bg-gray-100 transition-colors duration-200"
            />
            <Button
              text={isSaving ? '保存中...' : '保存'}
              type="primary"
              disabled={isSaving}
              onClick={handleSave}
              className="shadow-md hover:shadow-lg transition-shadow duration-200"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
};
