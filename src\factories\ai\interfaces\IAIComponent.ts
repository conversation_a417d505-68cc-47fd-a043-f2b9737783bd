import React from 'react';

/**
 * AI请求选项接口
 */
export interface AIRequestOptions {
  // 系统消息
  systemMessage?: string;

  // 温度参数 (0.0-1.0)
  temperature?: number;

  // 最大生成令牌数
  maxTokens?: number;

  // Top P参数 - 控制核心采样的概率质量 (0.0-1.0)
  topP?: number;

  // Top K参数 - 控制候选词汇数量，范围通常为1-100
  topK?: number;

  // 频率惩罚参数
  frequencyPenalty?: number;

  // 存在惩罚参数
  presencePenalty?: number;

  // 是否启用流式输出
  streaming?: boolean;

  // 其他选项
  [key: string]: any;
}

/**
 * AI响应接口
 */
export interface AIResponse {
  // 响应文本
  text: string;

  // 是否成功
  success: boolean;

  // 错误信息，如果有
  error?: string;

  // 原始响应数据
  rawResponse?: any;

  // 使用的模型
  model?: string;

  // 使用的提供商
  provider?: string;

  // 使用的令牌数
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * 基础AI组件接口
 * 所有AI功能组件都必须实现此接口
 */
export interface IAIComponent {
  /**
   * 渲染组件UI
   */
  render(): React.ReactNode;

  /**
   * 发送AI请求
   * @param prompt 提示词
   * @param options 请求选项
   * @returns AI响应
   */
  sendRequest(prompt: string, options?: AIRequestOptions): Promise<AIResponse>;
}
