/**
 * 聊天持久化服务
 * 负责聊天记录的保存、加载和管理
 */

import { PhaseType } from '../../types/ai-persona';
import { ChatMessage, EditHistoryEntry } from './SessionManager';

// 为了向后兼容，保留原有的简化接口
export interface SimpleChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export interface ChatSession {
  id: string;
  title: string;
  phase: PhaseType;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export class ChatPersistenceService {
  private static instance: ChatPersistenceService;
  private readonly STORAGE_KEY = 'chat-sessions';
  private readonly ACTIVE_SESSION_KEY = 'active-chat-session';

  private constructor() {}

  static getInstance(): ChatPersistenceService {
    if (!ChatPersistenceService.instance) {
      ChatPersistenceService.instance = new ChatPersistenceService();
    }
    return ChatPersistenceService.instance;
  }

  /**
   * 获取指定阶段的所有聊天会话
   */
  async getChatSessions(phase: PhaseType): Promise<ChatSession[]> {
    try {
      const allSessions = this.getAllSessions();
      return allSessions.filter(session => session.phase === phase);
    } catch (error) {
      console.error('获取聊天会话失败:', error);
      return [];
    }
  }

  /**
   * 获取当前活跃的聊天会话
   */
  async getActiveSession(phase: PhaseType): Promise<ChatSession | null> {
    try {
      const activeSessionId = localStorage.getItem(`${this.ACTIVE_SESSION_KEY}-${phase}`);
      if (!activeSessionId) return null;

      const sessions = await this.getChatSessions(phase);
      return sessions.find(session => session.id === activeSessionId) || null;
    } catch (error) {
      console.error('获取活跃会话失败:', error);
      return null;
    }
  }

  /**
   * 创建新的聊天会话
   */
  async createNewSession(phase: PhaseType, title?: string): Promise<ChatSession> {
    try {
      const newSession: ChatSession = {
        id: `chat-${phase}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        title: title || this.generateSessionTitle(phase),
        phase,
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };

      // 将其他会话设为非活跃
      await this.deactivateOtherSessions(phase);

      // 保存新会话
      await this.saveSession(newSession);

      // 设为活跃会话
      await this.setActiveSession(newSession);

      return newSession;
    } catch (error) {
      console.error('创建聊天会话失败:', error);
      throw error;
    }
  }

  /**
   * 保存聊天消息到会话
   */
  async saveMessage(sessionId: string, message: ChatMessage): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const sessionIndex = sessions.findIndex(s => s.id === sessionId);

      if (sessionIndex === -1) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      // 检查是否已存在相同ID的消息
      const existingMessageIndex = sessions[sessionIndex].messages.findIndex(m => m.id === message.id);

      if (existingMessageIndex >= 0) {
        // 更新现有消息
        sessions[sessionIndex].messages[existingMessageIndex] = message;
      } else {
        // 添加新消息
        sessions[sessionIndex].messages.push(message);
      }

      sessions[sessionIndex].updatedAt = new Date();

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('保存消息失败:', error);
      throw error;
    }
  }

  /**
   * 更新会话中的消息
   */
  async updateMessage(sessionId: string, messageId: string, newContent: string): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      const message = session.messages.find(m => m.id === messageId);
      if (!message) {
        throw new Error(`消息 ${messageId} 不存在`);
      }

      message.content = newContent;
      session.updatedAt = new Date();

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('更新消息失败:', error);
      throw error;
    }
  }

  /**
   * 更新消息（支持编辑历史和更多字段）
   */
  async updateMessageWithHistory(sessionId: string, messageId: string, updates: Partial<ChatMessage>): Promise<boolean> {
    try {
      const sessions = this.getAllSessions();
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      const message = session.messages.find(m => m.id === messageId);
      if (!message) {
        throw new Error(`消息 ${messageId} 不存在`);
      }

      // 如果更新内容，保存编辑历史
      if (updates.content && updates.content !== message.content) {
        // 首次编辑时保存原始内容
        if (!message.isEdited) {
          message.originalContent = message.content;
          message.editHistory = [];
        }

        // 添加编辑历史记录
        message.editHistory = message.editHistory || [];
        message.editHistory.push({
          content: message.content, // 保存当前内容作为历史
          timestamp: new Date(),
          reason: updates.metadata?.editReason || '用户编辑'
        });

        message.isEdited = true;
      }

      // 应用更新
      Object.assign(message, updates);
      session.updatedAt = new Date();

      this.saveAllSessions(sessions);

      console.log('📝 更新消息:', messageId, '编辑历史条数:', message.editHistory?.length || 0);
      return true;
    } catch (error) {
      console.error('更新消息失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新消息顺序
   */
  async updateMessagesOrder(sessionId: string, messages: ChatMessage[]): Promise<boolean> {
    try {
      const sessions = this.getAllSessions();
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      // 更新消息的customOrder字段
      messages.forEach((message, index) => {
        const existingMessage = session.messages.find(msg => msg.id === message.id);
        if (existingMessage) {
          existingMessage.customOrder = index;
        }
      });

      // 重新排序消息数组
      session.messages = messages;
      session.updatedAt = new Date();

      this.saveAllSessions(sessions);

      console.log('🔄 更新消息顺序:', sessionId, '消息数量:', messages.length);
      return true;
    } catch (error) {
      console.error('更新消息顺序失败:', error);
      throw error;
    }
  }

  /**
   * 获取排序后的消息列表
   */
  async getSortedMessages(sessionId: string, useCustomOrder: boolean = false): Promise<ChatMessage[]> {
    try {
      const sessions = this.getAllSessions();
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        return [];
      }

      if (useCustomOrder) {
        // 使用自定义排序
        return [...session.messages].sort((a, b) => {
          const orderA = a.customOrder ?? a.timestamp.getTime();
          const orderB = b.customOrder ?? b.timestamp.getTime();
          return orderA - orderB;
        });
      } else {
        // 使用时间排序（默认）
        return [...session.messages].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      }
    } catch (error) {
      console.error('获取排序消息失败:', error);
      return [];
    }
  }

  /**
   * 切换到指定会话
   */
  async switchToSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const sessions = this.getAllSessions();
      const targetSession = sessions.find(s => s.id === sessionId);

      if (!targetSession) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      // 将其他同阶段会话设为非活跃
      await this.deactivateOtherSessions(targetSession.phase);

      // 设为活跃会话
      await this.setActiveSession(targetSession);

      return targetSession;
    } catch (error) {
      console.error('切换会话失败:', error);
      return null;
    }
  }

  /**
   * 删除聊天会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const filteredSessions = sessions.filter(s => s.id !== sessionId);

      this.saveAllSessions(filteredSessions);

      // 如果删除的是活跃会话，清除活跃会话标记
      const deletedSession = sessions.find(s => s.id === sessionId);
      if (deletedSession) {
        localStorage.removeItem(`${this.ACTIVE_SESSION_KEY}-${deletedSession.phase}`);
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      throw error;
    }
  }

  /**
   * 重命名聊天会话
   */
  async renameSession(sessionId: string, newTitle: string): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        throw new Error(`会话 ${sessionId} 不存在`);
      }

      session.title = newTitle;
      session.updatedAt = new Date();

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('重命名会话失败:', error);
      throw error;
    }
  }

  /**
   * 清空指定阶段的所有聊天记录
   */
  async clearPhaseChats(phase: PhaseType): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const filteredSessions = sessions.filter(s => s.phase !== phase);

      this.saveAllSessions(filteredSessions);
      localStorage.removeItem(`${this.ACTIVE_SESSION_KEY}-${phase}`);
    } catch (error) {
      console.error('清空聊天记录失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：获取所有会话
   */
  private getAllSessions(): ChatSession[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const sessions = JSON.parse(stored);
      return sessions.map((session: any) => ({
        ...session,
        createdAt: new Date(session.createdAt),
        updatedAt: new Date(session.updatedAt),
        messages: session.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
          // 处理编辑历史的日期反序列化
          editHistory: msg.editHistory?.map((entry: any) => ({
            ...entry,
            timestamp: new Date(entry.timestamp)
          })) || undefined
        }))
      }));
    } catch (error) {
      console.error('获取所有会话失败:', error);
      return [];
    }
  }

  /**
   * 私有方法：保存所有会话
   */
  private saveAllSessions(sessions: ChatSession[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('保存会话失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：保存单个会话
   */
  private async saveSession(session: ChatSession): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      const existingIndex = sessions.findIndex(s => s.id === session.id);

      if (existingIndex >= 0) {
        sessions[existingIndex] = session;
      } else {
        sessions.push(session);
      }

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('保存会话失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：设置活跃会话
   */
  private async setActiveSession(session: ChatSession): Promise<void> {
    try {
      localStorage.setItem(`${this.ACTIVE_SESSION_KEY}-${session.phase}`, session.id);

      // 更新会话状态
      const sessions = this.getAllSessions();
      sessions.forEach(s => {
        if (s.phase === session.phase) {
          s.isActive = s.id === session.id;
        }
      });

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('设置活跃会话失败:', error);
      throw error;
    }
  }

  /**
   * 私有方法：将其他会话设为非活跃
   */
  private async deactivateOtherSessions(phase: PhaseType): Promise<void> {
    try {
      const sessions = this.getAllSessions();
      sessions.forEach(session => {
        if (session.phase === phase) {
          session.isActive = false;
        }
      });

      this.saveAllSessions(sessions);
    } catch (error) {
      console.error('设置会话非活跃失败:', error);
    }
  }

  /**
   * 私有方法：生成会话标题
   */
  private generateSessionTitle(phase: PhaseType): string {
    const phaseNames = {
      intro: '导语',
      buildup: '铺垫',
      climax: '高潮',
      ending: '结尾'
    };

    const now = new Date();
    const timeStr = now.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return `${phaseNames[phase]}创作 ${timeStr}`;
  }
}
