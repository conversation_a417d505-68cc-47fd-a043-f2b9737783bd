import { IAIComponent } from './IAIComponent';
import { ConversationMessage } from '../services/AIWritingService';

/**
 * AI续写组件接口
 * 用于根据上下文智能续写内容
 */
export interface IAIContinueComponent extends IAIComponent {
  /**
   * 设置续写要求
   * @param requirements 续写要求
   */
  setContinueRequirements(requirements: string): void;

  /**
   * 设置续写风格
   * @param style 续写风格
   */
  setContinueStyle(style: string): void;

  /**
   * 设置后续剧情
   * @param plot 后续剧情
   */
  setFuturePlot(plot: string): void;

  /**
   * 设置上下文
   * @param context 上下文内容
   */
  setContext(context: string): void;

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId?(bookId: string): void;

  /**
   * 设置对话历史
   * @param history 对话历史
   */
  setConversationHistory?(history: ConversationMessage[]): void;

  /**
   * 设置续写完成回调
   * @param callback 回调函数
   */
  setOnContinuedCallback?(callback: (text: string) => void): void;

  /**
   * 设置人物数据
   * @param characters 人物数据
   */
  setCharacters?(characters: Array<{id?: string, name: string, description?: string}>): void;

  /**
   * 设置术语数据
   * @param terminologies 术语数据
   */
  setTerminologies?(terminologies: Array<{id?: string, name: string, description?: string}>): void;

  /**
   * 设置世界观数据
   * @param worldBuildings 世界观数据
   */
  setWorldBuildings?(worldBuildings: Array<{id?: string, name: string, description?: string}>): void;

  /**
   * 设置章节数据
   * @param chapters 章节数据
   */
  setChapters?(chapters: Array<{id?: string, name: string, description?: string, order?: number}>): void;

  /**
   * 设置选中的人物ID
   * @param ids 选中的人物ID
   */
  setSelectedCharacterIds?(ids: string[]): void;

  /**
   * 设置选中的术语ID
   * @param ids 选中的术语ID
   */
  setSelectedTerminologyIds?(ids: string[]): void;

  /**
   * 设置选中的世界观ID
   * @param ids 选中的世界观ID
   */
  setSelectedWorldBuildingIds?(ids: string[]): void;

  /**
   * 设置选中的章节ID
   * @param ids 选中的章节ID
   */
  setSelectedChapterIds?(ids: string[]): void;

  /**
   * 设置当前章节ID
   * @param id 当前章节ID
   */
  setCurrentChapterId?(id: string): void;

  /**
   * 续写内容
   * @returns 续写的内容
   */
  continue(): Promise<string>;

  /**
   * 流式续写方法
   * @param onChunk 接收数据块的回调函数
   * @returns 完整的续写内容
   */
  continueWithStreaming?(onChunk: (chunk: string) => void): Promise<string>;

  /**
   * 取消当前请求
   */
  cancelContinue?(): void;
}
