"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface MermaidRendererProps {
  code: string;
  onError?: (error: string) => void;
  className?: string;
}

/**
 * Mermaid图表渲染组件
 * 支持在AI对话中渲染Mermaid图表
 */
const MermaidRenderer: React.FC<MermaidRendererProps> = ({
  code,
  onError,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [diagramId, setDiagramId] = useState<string>('');

  useEffect(() => {
    const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setDiagramId(id);
    renderDiagram(code, id);
  }, [code]);

  const renderDiagram = async (diagramCode: string, id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 动态导入render-mermaid工具
      const { renderMermaid } = await import('../../../utils/mermaid-renderer');
      
      // 渲染图表
      await renderMermaid({
        diagram_definition: diagramCode,
        title: '创作流程图'
      });

      setIsLoading(false);
    } catch (err: any) {
      console.error('Mermaid渲染失败:', err);
      const errorMessage = err.message || '图表渲染失败';
      setError(errorMessage);
      setIsLoading(false);
      onError?.(errorMessage);
    }
  };

  const handleRetry = () => {
    renderDiagram(code, diagramId);
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code).then(() => {
      // 可以添加复制成功的提示
    });
  };

  if (isLoading) {
    return (
      <motion.div
        className={`mermaid-container loading ${className}`}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="mermaid-card">
          <div className="mermaid-header">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-gray-700">正在渲染图表...</span>
            </div>
          </div>
          
          <div className="mermaid-content">
            <div className="flex items-center justify-center h-32">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className={`mermaid-container error ${className}`}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="mermaid-card">
          <div className="mermaid-header">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full" />
              <span className="text-sm font-medium text-red-700">图表渲染失败</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleRetry}
                className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
              >
                重试
              </button>
              <button
                onClick={handleCopyCode}
                className="text-xs text-gray-600 hover:text-gray-800 transition-colors"
              >
                复制代码
              </button>
            </div>
          </div>
          
          <div className="mermaid-content">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700 mb-2">错误信息：{error}</p>
              <details className="text-xs text-gray-600">
                <summary className="cursor-pointer hover:text-gray-800">查看原始代码</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                  <code>{code}</code>
                </pre>
              </details>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className={`mermaid-container success ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="mermaid-card">
        <div className="mermaid-header">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full" />
            <span className="text-sm font-medium text-gray-700">创作流程图</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCopyCode}
              className="text-xs text-gray-600 hover:text-gray-800 transition-colors"
              title="复制Mermaid代码"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="mermaid-content">
          <div id={diagramId} className="mermaid-diagram" />
        </div>
      </div>
    </motion.div>
  );
};

export default MermaidRenderer;

// CSS样式（可以移到单独的CSS文件中）
const styles = `
.mermaid-container {
  margin: 12px 0;
  max-width: 100%;
}

.mermaid-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mermaid-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.mermaid-content {
  padding: 16px;
}

.mermaid-diagram {
  text-align: center;
  overflow-x: auto;
}

.mermaid-diagram svg {
  max-width: 100%;
  height: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mermaid-content {
    padding: 12px;
  }
  
  .mermaid-diagram {
    font-size: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .mermaid-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .mermaid-header {
    background: #111827;
    border-bottom-color: #374151;
  }
}
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}
