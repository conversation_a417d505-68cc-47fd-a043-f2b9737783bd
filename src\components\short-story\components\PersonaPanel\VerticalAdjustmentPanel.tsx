import React, { useState, useEffect } from 'react';
import {
  VerticalAdjustmentService,
  VerticalAdjustment,
  AIResponseAnalysis,
  ConversationMessage
} from '../../../../services/ai/VerticalAdjustmentService';
import { PersonaStorageService } from '../../../../services/ai-persona/PersonaStorageService';
import { AIPersonaConfig } from '../../../../types/ai-persona';
import './VerticalAdjustmentPanel.css';
export type PhaseType = 'intro' | 'setup' | 'compression' | 'climax' | 'resolution' | 'ending' | 'buildup' | 'custom';

interface VerticalAdjustmentPanelProps {
  className?: string;
  phase?: PhaseType; // 当前阶段
}

export const VerticalAdjustmentPanel: React.FC<VerticalAdjustmentPanelProps> = ({
  className = '',
  phase = 'intro'
}) => {
  const [adjustmentService] = useState(() => new VerticalAdjustmentService(phase));
  const [savedAdjustments, setSavedAdjustments] = useState<VerticalAdjustment[]>([]);
  const [analysisResult, setAnalysisResult] = useState<AIResponseAnalysis | null>(null);
  const [generatedAdjustments, setGeneratedAdjustments] = useState<VerticalAdjustment[]>([]);
  const [effectivenessStats, setEffectivenessStats] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGeneratingAdjustments, setIsGeneratingAdjustments] = useState(false);
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [currentPersona, setCurrentPersona] = useState<AIPersonaConfig | null>(null);
  const [customAdjustment, setCustomAdjustment] = useState({
    name: '',
    category: 'content' as VerticalAdjustment['category'],
    adjustmentContent: '',
    description: ''
  });

  // 获取对话历史的函数
  const getConversationHistory = (): ConversationMessage[] => {
    try {
      console.log('🔍 开始获取对话历史，当前阶段:', phase);

      // 首先尝试从ChatPersistenceService获取当前阶段的对话历史
      const allSessions = localStorage.getItem('chat-sessions');
      console.log('🔍 chat-sessions数据:', allSessions ? '存在' : '不存在');

      if (allSessions) {
        const sessions = JSON.parse(allSessions);
        console.log('🔍 找到会话数量:', sessions.length);

        const currentPhaseSession = sessions.find((session: any) =>
          session.phase === phase && session.isActive
        );

        console.log('🔍 当前阶段活跃会话:', currentPhaseSession ? '找到' : '未找到');

        if (currentPhaseSession && currentPhaseSession.messages && currentPhaseSession.messages.length > 0) {
          console.log('🔍 找到消息数量:', currentPhaseSession.messages.length);

          // 获取反馈服务来获取用户评论
          const getFeedbackForMessage = (messageId: string) => {
            try {
              // 动态导入FeedbackCollectionService
              const { FeedbackCollectionService } = require('../../../../services/ai-feedback/FeedbackCollectionService');
              const feedbackService = FeedbackCollectionService.getInstance();
              const feedback = feedbackService.getMessageFeedback(messageId);

              if (feedback) {
                // 将FeedbackRating转换为数字评分
                let numericRating = 3; // 默认一般
                switch (feedback.rating) {
                  case 'good': numericRating = 5; break;
                  case 'average': numericRating = 3; break;
                  case 'poor': numericRating = 1; break;
                }

                return {
                  rating: numericRating,
                  feedback: feedback.comment || ''
                };
              }
            } catch (error) {
              console.warn('获取消息反馈失败:', error);
            }
            return { rating: undefined, feedback: undefined };
          };

          return currentPhaseSession.messages.map((msg: any) => {
            const feedbackData = getFeedbackForMessage(msg.id);
            return {
              id: msg.id || `msg-${Date.now()}-${Math.random()}`,
              type: msg.type || 'user',
              content: msg.content || '',
              timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
              rating: feedbackData.rating,
              feedback: feedbackData.feedback
            };
          });
        }
      }

      // 降级：尝试从其他localStorage键获取对话历史
      const fallbackKeys = [
        `ai-writing-history-current`,
        `ai-rewrite-history-current`,
        `ai-continue-history-current`,
        `ai-phase-history-${phase}`
      ];

      for (const key of fallbackKeys) {
        const saved = localStorage.getItem(key);
        if (saved) {
          const parsed = JSON.parse(saved);
          if (Array.isArray(parsed) && parsed.length > 0) {
            // 获取反馈服务来获取用户评论
            const getFeedbackForMessage = (messageId: string) => {
              try {
                const { FeedbackCollectionService } = require('../../../../services/ai-feedback/FeedbackCollectionService');
                const feedbackService = FeedbackCollectionService.getInstance();
                const feedback = feedbackService.getMessageFeedback(messageId);

                if (feedback) {
                  let numericRating = 3;
                  switch (feedback.rating) {
                    case 'good': numericRating = 5; break;
                    case 'average': numericRating = 3; break;
                    case 'poor': numericRating = 1; break;
                  }

                  return {
                    rating: numericRating,
                    feedback: feedback.comment || ''
                  };
                }
              } catch (error) {
                console.warn('获取消息反馈失败:', error);
              }
              return { rating: undefined, feedback: undefined };
            };

            return parsed.map((msg: any) => {
              const feedbackData = getFeedbackForMessage(msg.id);
              return {
                id: msg.id || `msg-${Date.now()}-${Math.random()}`,
                type: msg.type || (msg.role === 'user' ? 'user' : 'ai'),
                content: msg.content || '',
                timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
                rating: feedbackData.rating,
                feedback: feedbackData.feedback
              };
            });
          }
        }
      }

      console.log('🔍 未找到对话历史，检查的键:', ['chat-sessions', ...fallbackKeys]);
      return [];
    } catch (error) {
      console.error('获取对话历史失败:', error);
      return [];
    }
  };

  useEffect(() => {
    // 当阶段变化时，更新服务的当前阶段并重新加载数据
    adjustmentService.setCurrentPhase(phase);
    setSavedAdjustments(adjustmentService.getSavedAdjustments());
    setEffectivenessStats(adjustmentService.getAdjustmentEffectivenessStats());

    // 加载当前阶段的人设配置
    const loadPersonaConfig = async () => {
      try {
        const personaConfig = await PersonaStorageService.getInstance().getPersonaConfig(phase);
        setCurrentPersona(personaConfig);
        console.log('🎭 加载人设配置成功:', personaConfig.id);
      } catch (error) {
        console.error('🎭 加载人设配置失败:', error);
        setCurrentPersona(null);
      }
    };

    loadPersonaConfig();
  }, [adjustmentService, phase]);

  const handleAnalyzeHistory = async () => {
    setIsAnalyzing(true);
    setIsGeneratingAdjustments(false);
    setGeneratedAdjustments([]);

    try {
      // 获取对话历史
      const conversationHistory = getConversationHistory();
      console.log('🔍 获取到的对话历史:', conversationHistory.length, '条');

      if (conversationHistory.length === 0) {
        console.warn('⚠️ 没有对话历史可供分析');
        return;
      }

      const messages: ConversationMessage[] = conversationHistory;

      console.log('📊 开始分析AI回复...');
      const analysis = adjustmentService.analyzeAIResponses(messages);
      console.log('📊 分析结果:', analysis);
      setAnalysisResult(analysis);

      // 使用AI生成调整建议，传入人设配置
      console.log('🤖 开始生成AI调整建议，包含人设配置:', !!currentPersona);
      setIsGeneratingAdjustments(true);
      try {
        const adjustments = await adjustmentService.generateAdjustments(analysis, messages, currentPersona);
        console.log('🤖 生成的调整建议:', adjustments);
        setGeneratedAdjustments(adjustments);

        // 更新保存的调整列表
        setSavedAdjustments(adjustmentService.getSavedAdjustments());
      } catch (adjustmentError) {
        console.error('🤖 AI调整建议生成失败:', adjustmentError);
        setGeneratedAdjustments([]);
      } finally {
        setIsGeneratingAdjustments(false);
      }
    } catch (error) {
      console.error('❌ 分析失败:', error);
      setAnalysisResult(null);
      setGeneratedAdjustments([]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSaveAdjustment = (adjustment: VerticalAdjustment) => {
    adjustmentService.saveAdjustment(adjustment);
    setSavedAdjustments(adjustmentService.getSavedAdjustments());
  };

  const handleActivateAdjustment = (id: string) => {
    adjustmentService.activateAdjustment(id);
    setSavedAdjustments(adjustmentService.getSavedAdjustments());
  };

  const handleDeleteAdjustment = (id: string) => {
    if (confirm('确定要删除这个调整模板吗？')) {
      adjustmentService.deleteAdjustment(id);
      setSavedAdjustments(adjustmentService.getSavedAdjustments());
    }
  };

  const handleCreateCustomAdjustment = () => {
    if (!customAdjustment.name || !customAdjustment.adjustmentContent) {
      alert('请填写完整的调整信息');
      return;
    }

    const adjustment = adjustmentService.createCustomAdjustment(
      customAdjustment.name,
      customAdjustment.category,
      customAdjustment.adjustmentContent,
      customAdjustment.description
    );

    handleSaveAdjustment(adjustment);
    setShowCustomForm(false);
    setCustomAdjustment({
      name: '',
      category: 'content',
      adjustmentContent: '',
      description: ''
    });
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      style: '🎨',
      structure: '🏗️',
      content: '📝',
      engagement: '💬',
      personalization: '👤'
    };
    return icons[category as keyof typeof icons] || '⚙️';
  };

  const getCategoryName = (category: string) => {
    const names = {
      style: '风格调整',
      structure: '结构优化',
      content: '内容增强',
      engagement: '互动提升',
      personalization: '个性化'
    };
    return names[category as keyof typeof names] || category;
  };

  const getPhaseDisplayName = (phase: PhaseType) => {
    const phaseNames = {
      'intro': '导语',
      'setup': '铺垫期',
      'compression': '爆发情绪',
      'climax': '反转',
      'resolution': '让读者解气',
      'ending': '大结局',
      'buildup': '铺垫期'
    };
    return phaseNames[phase] || '当前';
  };

  return (
    <div className={`vertical-adjustment-panel ${className}`}>
      <div className="panel-header">
        <h3>🎯 个性化历史建议 - {getPhaseDisplayName(phase)}</h3>
        <p>分析{getPhaseDisplayName(phase)}阶段AI历史回复模式，生成具体化的自我改进建议，支持多个建议同时激活使用</p>
      </div>

      {/* 分析区域 */}
      <div className="analysis-section">
        <button
          onClick={handleAnalyzeHistory}
          disabled={isAnalyzing || getConversationHistory().length === 0}
          className="analyze-button"
        >
          {isAnalyzing ? '🔄 分析中...' : '🧠 生成个性化历史建议'}
        </button>

        {getConversationHistory().length === 0 && (
          <p className="no-data-hint">暂无对话历史，请先与AI进行一些对话</p>
        )}

        {analysisResult && (
          <div className="analysis-results">
            <h4>📈 分析结果</h4>

            <div className="result-overview">
              <div className="score-card">
                <span className="score-label">综合评分</span>
                <span className="score-value">
                  {Math.round(analysisResult.overallScore * 100)}%
                </span>
              </div>
            </div>

            <div className="result-details">
              <div className="result-item">
                <strong>📋 回复模式:</strong>
                <ul>
                  <li>平均长度: {analysisResult.responsePatterns.averageLength}字</li>
                  {analysisResult.responsePatterns.commonStructures.map((structure, index) => (
                    <li key={index}>{structure}</li>
                  ))}
                  {analysisResult.responsePatterns.guidanceTypes.map((type, index) => (
                    <li key={index}>指导类型: {type}</li>
                  ))}
                </ul>
              </div>

              <div className="result-item">
                <strong>⚠️ 需要改进:</strong>
                <ul>
                  {analysisResult.improvementAreas.weakPoints.map((point, index) => (
                    <li key={index}>{point}</li>
                  ))}
                  {analysisResult.improvementAreas.repetitivePatterns.map((pattern, index) => (
                    <li key={index}>{pattern}</li>
                  ))}
                </ul>
              </div>

              <div className="quality-metrics">
                <strong>📊 质量指标:</strong>
                <div className="metrics-grid">
                  <div className="metric-item">
                    <span>有用性</span>
                    <span>{Math.round(analysisResult.qualityMetrics.helpfulness * 100)}%</span>
                  </div>
                  <div className="metric-item">
                    <span>具体性</span>
                    <span>{Math.round(analysisResult.qualityMetrics.specificity * 100)}%</span>
                  </div>
                  <div className="metric-item">
                    <span>可操作性</span>
                    <span>{Math.round(analysisResult.qualityMetrics.actionability * 100)}%</span>
                  </div>
                  <div className="metric-item">
                    <span>互动性</span>
                    <span>{Math.round(analysisResult.qualityMetrics.engagement * 100)}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* AI生成进度 */}
      {isGeneratingAdjustments && (
        <div className="ai-generating">
          <div className="generating-indicator">
            <div className="spinner"></div>
            <span>🤖 AI正在分析并生成个性化调整建议...</span>
          </div>
        </div>
      )}

      {/* 生成的指向性调整建议 */}
      {generatedAdjustments.length > 0 && !isGeneratingAdjustments && (
        <div className="generated-adjustments">
          <h4>💡 个性化历史建议</h4>
          <p className="suggestions-hint">以下是基于您的AI历史回复分析生成的具体化自我改进建议，可多选激活：</p>
          <div className="adjustments-grid">
            {generatedAdjustments.map(adjustment => (
              <div key={adjustment.id} className="adjustment-card compact">
                <div className="adjustment-header">
                  <span className="adjustment-icon">
                    {getCategoryIcon(adjustment.category)}
                  </span>
                  <div className="adjustment-info">
                    <span className="adjustment-name">{adjustment.name}</span>
                    <span className="adjustment-category">
                      {getCategoryName(adjustment.category)}
                    </span>
                  </div>
                  <span className="adjustment-confidence">
                    {Math.round(adjustment.confidence * 100)}%
                  </span>
                </div>
                <div className="adjustment-content">
                  <p className="adjustment-direction">{adjustment.adjustmentContent}</p>
                  <p className="adjustment-description">{adjustment.description}</p>
                </div>
                <div className="adjustment-actions">
                  <button
                    onClick={() => handleSaveAdjustment(adjustment)}
                    className="save-button compact"
                  >
                    💾 保存并激活
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 效果统计 */}
      {effectivenessStats && effectivenessStats.totalRatings > 0 && (
        <div className="effectiveness-stats">
          <h4>📊 调整效果统计</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">平均评分</span>
              <span className="stat-value">{(effectivenessStats.averageRating).toFixed(1)}/5.0</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">总评分数</span>
              <span className="stat-value">{effectivenessStats.totalRatings}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">改进趋势</span>
              <span className={`stat-value ${effectivenessStats.improvementTrend > 0 ? 'positive' : effectivenessStats.improvementTrend < 0 ? 'negative' : 'neutral'}`}>
                {effectivenessStats.improvementTrend > 0 ? '↗️' : effectivenessStats.improvementTrend < 0 ? '↘️' : '➡️'}
                {Math.abs(effectivenessStats.improvementTrend * 100).toFixed(1)}%
              </span>
            </div>
          </div>

          {effectivenessStats.topPerformingAdjustments.length > 0 && (
            <div className="top-adjustments">
              <h5>🏆 表现最佳的调整</h5>
              {effectivenessStats.topPerformingAdjustments.slice(0, 3).map((adj: any) => (
                <div key={adj.id} className="top-adjustment-item">
                  <span className="adjustment-name">{adj.name}</span>
                  <span className="effectiveness-score">
                    {Math.round((adj.effectivenessScore || 0) * 100)}%
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 已保存的调整模板 */}
      <div className="saved-adjustments">
        <div className="section-header">
          <h4>📚 已保存的调整模板</h4>
          <button
            onClick={() => setShowCustomForm(!showCustomForm)}
            className="add-custom-button"
          >
            ➕ 自定义调整
          </button>
        </div>

        {/* 自定义调整表单 */}
        {showCustomForm && (
          <div className="custom-form">
            <h5>创建自定义调整</h5>
            <div className="form-group">
              <label>调整名称:</label>
              <input
                type="text"
                value={customAdjustment.name}
                onChange={(e) => setCustomAdjustment(prev => ({ ...prev, name: e.target.value }))}
                placeholder="例如: 情感表达增强"
              />
            </div>
            <div className="form-group">
              <label>调整类别:</label>
              <select
                value={customAdjustment.category}
                onChange={(e) => setCustomAdjustment(prev => ({
                  ...prev,
                  category: e.target.value as VerticalAdjustment['category']
                }))}
              >
                <option value="content">内容增强</option>
                <option value="style">风格调整</option>
                <option value="structure">结构优化</option>
                <option value="engagement">互动提升</option>
                <option value="personalization">个性化</option>
              </select>
            </div>
            <div className="form-group">
              <label>调整内容:</label>
              <textarea
                value={customAdjustment.adjustmentContent}
                onChange={(e) => setCustomAdjustment(prev => ({
                  ...prev,
                  adjustmentContent: e.target.value
                }))}
                placeholder="具体的自我改进指导，如：我应该用【】标记重要概念，下次要将核心术语用【】包围"
                rows={2}
              />
            </div>
            <div className="form-group">
              <label>描述说明:</label>
              <input
                type="text"
                value={customAdjustment.description}
                onChange={(e) => setCustomAdjustment(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                placeholder="简要描述这个调整的作用"
              />
            </div>
            <div className="form-actions">
              <button onClick={handleCreateCustomAdjustment} className="create-button">
                ✅ 创建调整
              </button>
              <button onClick={() => setShowCustomForm(false)} className="cancel-button">
                ❌ 取消
              </button>
            </div>
          </div>
        )}

        {savedAdjustments.length === 0 ? (
          <p className="empty-state">暂无保存的调整模板，请先分析历史回复或创建自定义调整</p>
        ) : (
          <div className="adjustments-list">
            {savedAdjustments.map(adjustment => (
              <div key={adjustment.id} className="saved-adjustment-item">
                <div className="adjustment-info">
                  <div className="adjustment-title">
                    <span className="adjustment-icon">
                      {getCategoryIcon(adjustment.category)}
                    </span>
                    <span className="adjustment-name">{adjustment.name}</span>
                    <span className={`adjustment-status ${adjustment.isActive ? 'active' : 'inactive'}`}>
                      {adjustment.isActive ? '✅ 已激活' : '⭕ 未激活'}
                    </span>
                  </div>
                  <p className="adjustment-description">{adjustment.description}</p>
                  <div className="adjustment-meta">
                    <span>类别: {getCategoryName(adjustment.category)}</span>
                    <span>使用次数: {adjustment.usageCount}</span>
                    {adjustment.effectivenessScore && (
                      <span>效果评分: {Math.round(adjustment.effectivenessScore * 100)}%</span>
                    )}
                  </div>
                </div>
                <div className="adjustment-actions">
                  <button
                    onClick={() => handleActivateAdjustment(adjustment.id)}
                    className={`activate-button ${adjustment.isActive ? 'active' : ''}`}
                  >
                    {adjustment.isActive ? '🔄 取消激活' : '🎯 激活使用'}
                  </button>
                  <button
                    onClick={() => handleDeleteAdjustment(adjustment.id)}
                    className="delete-button"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
