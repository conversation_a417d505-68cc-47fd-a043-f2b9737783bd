import Dexie, { Table } from 'dexie';
import { WorldBookPrefix } from '../../types/worldbook';

// 定义数据模型接口
export interface Book {
  id?: string;
  title: string;
  description: string;
  coverImage?: string;
  author: string;
  tags: string[];
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastOpenedAt: Date;
  settings: BookSettings;
}

export interface BookSettings {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  theme: 'light' | 'dark' | 'sepia';
  autoSave: boolean;
  autoSaveInterval: number; // in milliseconds
}

export interface Chapter {
  id?: string;
  bookId: string;
  title: string;
  content: string;
  order: number;
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  characterIds: string[]; // 关联的人物ID
  terminologyIds: string[]; // 关联的术语ID
  worldBuildingIds: string[]; // 关联的世界观ID
  summary?: string; // 章节摘要
  notes?: string; // 章节笔记
}

export interface Character {
  id?: string;
  bookId: string;
  name: string;
  alias?: string[];
  description: string;
  appearance?: string;
  personality?: string;
  background?: string;
  goals?: string;
  relationships?: CharacterRelationship[];
  attributes?: Record<string, string>; // 自定义属性
  createdAt: Date;
  updatedAt: Date;
  extractedFromChapterIds: string[]; // 从哪些章节提取的
  relatedCharacterIds: string[]; // 关联的其他人物
  relatedTerminologyIds: string[]; // 关联的术语
  relatedWorldBuildingIds: string[]; // 关联的世界观
  notes?: string; // 笔记

  // 隐藏信息素字段
  hiddenMotivation?: string; // 隐藏动机
  secretHistory?: string; // 秘密历史
  innerConflicts?: string; // 内心冲突
  characterArchetype?: string; // 角色原型
  growthArc?: string; // 成长弧线
  symbolism?: string; // 象征意义
}

export interface CharacterRelationship {
  targetCharacterId: string;
  relationshipType: string; // 如"朋友"、"敌人"、"家人"等
  description: string;
}

export interface Terminology {
  id?: string;
  bookId: string;
  name: string;
  alias?: string[];
  category: string; // 如"物品"、"技能"、"组织"等
  description: string;
  attributes?: Record<string, string>; // 自定义属性
  createdAt: Date;
  updatedAt: Date;
  extractedFromChapterIds: string[]; // 从哪些章节提取的
  relatedCharacterIds: string[]; // 关联的人物
  relatedTerminologyIds: string[]; // 关联的其他术语
  relatedWorldBuildingIds: string[]; // 关联的世界观
  notes?: string; // 笔记
}

export interface WorldBuilding {
  id?: string;
  bookId: string;
  name: string;
  category: string; // 如"地理"、"历史"、"文化"等
  description: string;
  attributes?: Record<string, string>; // 自定义属性
  createdAt: Date;
  updatedAt: Date;
  extractedFromChapterIds: string[]; // 从哪些章节提取的
  relatedCharacterIds: string[]; // 关联的人物
  relatedTerminologyIds: string[]; // 关联的术语
  relatedWorldBuildingIds: string[]; // 关联的其他世界观元素
  notes?: string; // 笔记
}

export interface Outline {
  id?: string;
  workId: string;
  title: string;
  nodes: OutlineNode[];
  lastModified: Date;
  version: number;
}

export interface OutlineNode {
  id: string;
  title: string;
  description?: string;
  creativeNotes?: string; // AI生成的创作建议，包含台词设计、心理描写、节奏控制等指导
  type: 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue';
  children?: OutlineNode[];
  expanded?: boolean;
  relatedCharacterIds?: string[];
  relatedWorldBuildingIds?: string[];
  relatedTerminologyIds?: string[];
  metadata?: Record<string, any>;

  // 节点在画布中的位置
  position?: {
    x: number;
    y: number;
  };

  // 节点关系字段
  prevSiblingId?: string;
  nextSiblingId?: string;
  parentId?: string;

  // ========== 总纲/卷特定字段 ==========
  volumeTheme?: string;      // 卷主题
  volumeArc?: string;        // 卷弧线
  chapterCount?: number;     // 预期章节数
  cycleTemplate?: string;    // 循环法模板
  targetWordCount?: number;  // 目标字数
  rhythmStrategy?: string;   // 节奏策略
  cyclePhases?: string[];    // 循环阶段规划

  // ========== 事件刚特定字段 ==========
  eventStart?: string;       // 事件起始描述
  eventEnd?: string;         // 事件结束描述
  eventTrigger?: string;     // 事件触发条件
  eventConsequence?: string; // 事件结果影响
  eventScope?: string;       // 事件影响范围

  // ========== 章节特定字段 ==========
  chapterStyle?: string;     // 写作风格
  chapterTechniques?: string[]; // 写作手法
  chapterGoals?: string;     // 章节目标
  rhythmPhase?: string;      // 节奏阶段
  rhythmGuidance?: string;   // 节奏指导

  // ========== 剧情节点特定字段 ==========
  plotPoints?: any[];        // 剧情点列表
  plotType?: string;         // 剧情类型
  relatedCharacters?: string[]; // 关联角色

  // ========== 对话设计特定字段 ==========
  dialogueScene?: string;    // 对话场景
  participants?: string[];   // 参与角色
  dialoguePurpose?: string;  // 对话目的
  dialogueContent?: any[];   // 对话内容
}

export interface OutlineVersion {
  id?: string;
  outlineId: string;
  version: number;
  data: Outline;
  createdAt: Date;
  createdBy: string;
  comment?: string;
}

export enum AIFeatureType {
  WRITING = 'writing',
  BOOK_ANALYSIS = 'book_analysis',
  REWRITE = 'rewrite',
  CONTINUE = 'continue',
  CHARACTER_EXTRACTION = 'character_extraction',
  TERMINOLOGY_EXTRACTION = 'terminology_extraction',
  WORLD_BUILDING_EXTRACTION = 'world_building_extraction'
}

export interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface AISession {
  id?: string;
  feature: AIFeatureType;
  bookId: string;
  chapterId?: string;
  inputs: Record<string, any>; // 输入参数
  messages: Message[]; // 对话历史
  createdAt: Date;
  updatedAt: Date;
}

export enum PromptCategory {
  WRITING_REQUIREMENTS = 'writing_requirements',
  WRITING_STYLE = 'writing_style',
  CONTINUE_REQUIREMENTS = 'continue_requirements',
  CONTINUE_STYLE = 'continue_style',
  REWRITE_REQUIREMENTS = 'rewrite_requirements',
  REWRITE_PLOT = 'rewrite_plot', // 添加改写剧情类别
  BOOK_ANALYSIS = 'book_analysis',
  WORLD_BUILDING = 'world_building',
  TERMINOLOGY = 'terminology' // 添加术语类别
}

export interface PromptTemplate {
  id?: string;
  category: PromptCategory;
  name: string;
  content: string;
  description?: string;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface FieldDefinition {
  id: string;
  name: string;
  displayName: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  validationRules?: ValidationRule[];
  order: number;
  group?: string;
}

export interface ValidationRule {
  type: string;
  params?: any;
  message: string;
}

export interface GroupConfig {
  id: string;
  name: string;
  order: number;
}

export interface LayoutConfig {
  type: string;
  columns?: number;
  groups?: GroupConfig[];
}

export interface FieldTemplate {
  id?: string;
  name: string;
  description: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
  fields: FieldDefinition[];
  layout?: LayoutConfig;
}

// AI助手关联上下文类型
export enum AIAssistantContextType {
  CHAPTER = 'chapter',
  CHARACTER = 'character',
  TERMINOLOGY = 'terminology',
  WORLD_BUILDING = 'worldBuilding',
  OUTLINE_NODE = 'outlineNode'
}

// AI助手关联配置
export interface AIAssistantContext {
  id?: string;
  bookId: string;
  contextType: AIAssistantContextType;
  contextId: string;
  contextTitle: string; // 缓存标题，避免频繁查询
  isActive: boolean;
  priority: number; // 优先级，用于排序
  usageCount: number; // 使用次数，用于智能推荐
  lastUsedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>; // 额外元数据
}

// AI助手关联模板（快速选择组合）
export interface AIAssistantTemplate {
  id?: string;
  bookId: string;
  name: string;
  description?: string;
  contextIds: string[]; // 关联的上下文ID列表
  isDefault: boolean; // 是否为默认模板
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 用户设置表 - 替代localStorage中的全局设置
export interface UserSettings {
  id?: string;
  userId?: string; // 为未来多用户准备，当前可为空
  category: string; // 'api', 'pet', 'annotation', 'dual-ai', 'recommendation'
  key: string;
  value: any; // JSON格式存储复杂数据
  createdAt: Date;
  updatedAt: Date;
}

// 用户状态表 - 替代localStorage中的书籍相关状态
export interface UserState {
  id?: string;
  bookId?: string; // 书籍相关状态需要bookId，全局状态可为空
  category: string; // 'assistant', 'framework', 'layout', 'canvas', 'collapsed-nodes'
  key: string;
  value: any; // JSON格式存储复杂数据
  createdAt: Date;
  updatedAt: Date;
}

// 历史记录表 - 替代localStorage中的各类历史记录
export interface HistoryRecord {
  id?: string;
  bookId?: string; // 书籍相关历史需要bookId，全局历史可为空
  type: string; // 'title', 'synopsis', 'dialogue', 'writing', 'rewrite'
  content: any; // JSON格式存储历史内容
  metadata?: Record<string, any>; // 额外元数据
  createdAt: Date;
}

// 定义数据库类
export class NovelDatabase extends Dexie {
  books!: Table<Book, string>;
  chapters!: Table<Chapter, string>;
  characters!: Table<Character, string>;
  terminology!: Table<Terminology, string>;
  worldBuilding!: Table<WorldBuilding, string>;
  outlines!: Table<Outline, string>;
  outlineVersions!: Table<OutlineVersion, string>;
  aiSessions!: Table<AISession, string>;
  promptTemplates!: Table<PromptTemplate, string>;
  aiAssistantContexts!: Table<AIAssistantContext, string>;
  aiAssistantTemplates!: Table<AIAssistantTemplate, string>;
  userSettings!: Table<UserSettings, string>;
  userStates!: Table<UserState, string>;
  historyRecords!: Table<HistoryRecord, string>;
  worldBookPrefixes!: Table<WorldBookPrefix, string>;

  constructor() {
    super('NovelDatabase');

    // 保持旧版本兼容
    this.version(26).stores({
      books: 'id, title, createdAt, updatedAt',
      chapters: 'id, bookId, title, content, order, wordCount, *characterIds, *terminologyIds, *worldBuildingIds',
      characters: 'id, bookId, name, *relatedCharacterIds, *relatedTerminologyIds, *relatedWorldBuildingIds',
      terminology: 'id, bookId, name, *relatedTerminologyIds, *relatedCharacterIds, *relatedWorldBuildingIds',
      worldBuilding: 'id, bookId, name, *relatedWorldBuildingIds, *relatedCharacterIds, *relatedTerminologyIds',
      outlines: 'id, workId, title, version, lastModified',
      outlineVersions: 'id, outlineId, version, createdAt',
      aiSessions: 'id, feature, bookId, chapterId, createdAt, updatedAt',
      promptTemplates: 'id, category, name, createdAt, *tags',
      aiAssistantContexts: 'id, bookId, contextType, contextId, isActive, priority, usageCount, lastUsedAt, createdAt',
      aiAssistantTemplates: 'id, bookId, name, isDefault, usageCount, createdAt'
    });

    // 新版本添加localStorage迁移表
    this.version(27).stores({
      books: 'id, title, createdAt, updatedAt',
      chapters: 'id, bookId, title, content, order, wordCount, *characterIds, *terminologyIds, *worldBuildingIds',
      characters: 'id, bookId, name, *relatedCharacterIds, *relatedTerminologyIds, *relatedWorldBuildingIds',
      terminology: 'id, bookId, name, *relatedTerminologyIds, *relatedCharacterIds, *relatedWorldBuildingIds',
      worldBuilding: 'id, bookId, name, *relatedWorldBuildingIds, *relatedCharacterIds, *relatedTerminologyIds',
      outlines: 'id, workId, title, version, lastModified',
      outlineVersions: 'id, outlineId, version, createdAt',
      aiSessions: 'id, feature, bookId, chapterId, createdAt, updatedAt',
      promptTemplates: 'id, category, name, createdAt, *tags',
      aiAssistantContexts: 'id, bookId, contextType, contextId, isActive, priority, usageCount, lastUsedAt, createdAt',
      aiAssistantTemplates: 'id, bookId, name, isDefault, usageCount, createdAt',
      userSettings: 'id, userId, category, key, createdAt, updatedAt, [category+key]',
      userStates: 'id, bookId, category, key, createdAt, updatedAt, [bookId+category+key]',
      historyRecords: 'id, bookId, type, createdAt'
    });

    // 版本28：添加世界书前置消息表
    this.version(28).stores({
      books: 'id, title, createdAt, updatedAt',
      chapters: 'id, bookId, title, content, order, wordCount, *characterIds, *terminologyIds, *worldBuildingIds',
      characters: 'id, bookId, name, *relatedCharacterIds, *relatedTerminologyIds, *relatedWorldBuildingIds',
      terminology: 'id, bookId, name, *relatedTerminologyIds, *relatedCharacterIds, *relatedWorldBuildingIds',
      worldBuilding: 'id, bookId, name, *relatedWorldBuildingIds, *relatedCharacterIds, *relatedTerminologyIds',
      outlines: 'id, workId, title, version, lastModified',
      outlineVersions: 'id, outlineId, version, createdAt',
      aiSessions: 'id, feature, bookId, chapterId, createdAt, updatedAt',
      promptTemplates: 'id, category, name, createdAt, *tags',
      aiAssistantContexts: 'id, bookId, contextType, contextId, isActive, priority, usageCount, lastUsedAt, createdAt',
      aiAssistantTemplates: 'id, bookId, name, isDefault, usageCount, createdAt',
      userSettings: 'id, userId, category, key, createdAt, updatedAt, [category+key]',
      userStates: 'id, bookId, category, key, createdAt, updatedAt, [bookId+category+key]',
      historyRecords: 'id, bookId, type, createdAt',
      worldBookPrefixes: 'id, worldBookSource, category, *originalKeys, *tags, createdAt, updatedAt'
    });
  }
}

// 创建并导出数据库实例
export const db = new NovelDatabase();
