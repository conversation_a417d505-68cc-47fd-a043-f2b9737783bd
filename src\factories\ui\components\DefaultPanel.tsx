"use client";

import React from 'react';
import { IPanelComponent, PanelSize } from '../interfaces/IPanelComponent';
import { createAnimationFactory } from '@/factories/animation';

/**
 * 默认面板组件实现
 */
export class DefaultPanelComponent implements IPanelComponent {
  private title: string = '';
  private isOpen: boolean = false;
  private size: PanelSize = 'medium';
  private content: React.ReactNode = null;
  private header: React.ReactNode = null;
  private footer: React.ReactNode = null;
  private closeHandler: (() => void) | null = null;
  private className: string = '';
  private fixedHeight: boolean = false;
  private backgroundColor: string = 'rgba(255, 255, 255, 0.95)';
  private width: string = '80%';
  private height: string = '70%';

  /**
   * 设置面板标题
   * @param title 面板标题
   */
  setTitle(title: string): void {
    this.title = title;
  }

  /**
   * 设置面板是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
  }

  /**
   * 设置面板尺寸
   * @param size 面板尺寸
   */
  setSize(size: PanelSize): void {
    this.size = size;
  }

  /**
   * 设置面板内容
   * @param content 面板内容
   */
  setContent(content: React.ReactNode): void {
    this.content = content;
  }

  /**
   * 设置面板头部
   * @param header 面板头部
   */
  setHeader(header: React.ReactNode): void {
    this.header = header;
  }

  /**
   * 设置面板底部
   * @param footer 面板底部
   */
  setFooter(footer: React.ReactNode): void {
    this.footer = footer;
  }

  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void {
    this.closeHandler = handler;
  }

  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void {
    this.className = className;
  }

  /**
   * 设置是否固定高度
   * @param fixedHeight 是否固定高度
   */
  setFixedHeight(fixedHeight: boolean): void {
    this.fixedHeight = fixedHeight;
  }

  /**
   * 设置背景颜色
   * @param backgroundColor 背景颜色
   */
  setBackgroundColor(backgroundColor: string): void {
    this.backgroundColor = backgroundColor;
  }

  /**
   * 设置宽度
   * @param width 宽度
   */
  setWidth(width: string): void {
    this.width = width;
  }

  /**
   * 设置高度
   * @param height 高度
   */
  setHeight(height: string): void {
    this.height = height;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    if (!this.isOpen) return null;

    // 创建动画工厂
    const animationFactory = createAnimationFactory();
    const fadeAnimation = animationFactory.createFadeAnimation('none', 400, 0, true);
    const scaleAnimation = animationFactory.createScaleAnimation(0.9, 1.0, 400, 0, true);

    // 获取动画样式
    const fadeStyle = fadeAnimation.getStyle();
    const scaleStyle = scaleAnimation.getStyle();

    // 获取面板尺寸样式
    const sizeStyles = {
      small: 'max-w-md',
      medium: 'max-w-2xl',
      large: 'max-w-4xl',
    };

    return (
      <div
        className="fixed inset-0 flex items-center justify-center z-50 transition-opacity duration-300"
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          backdropFilter: 'blur(5px)',
          opacity: this.isOpen ? 1 : 0,
          ...fadeStyle
        }}
        onClick={this.closeHandler || (() => {})}
      >
        <div
          className={`rounded-lg shadow-xl overflow-hidden flex flex-col ${this.className}`}
          style={{
            backgroundColor: this.backgroundColor,
            width: this.width,
            height: this.height,
            maxHeight: this.fixedHeight ? this.height : '80vh',
            maxWidth: sizeStyles[this.size],
            ...scaleStyle
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 面板头部 */}
          <div className="px-6 py-4 border-b flex justify-between items-center">
            {this.header || (
              <>
                <h2 className="text-xl font-semibold text-gray-800">{this.title}</h2>
                <button
                  className="text-gray-500 hover:text-gray-700 focus:outline-none"
                  onClick={this.closeHandler || (() => {})}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </>
            )}
          </div>

          {/* 面板内容 */}
          <div className="flex-1 overflow-auto p-6">
            {this.content}
          </div>

          {/* 面板底部 */}
          {this.footer && (
            <div className="px-6 py-4 border-t bg-gray-50">
              {this.footer}
            </div>
          )}
        </div>
      </div>
    );
  }
}
