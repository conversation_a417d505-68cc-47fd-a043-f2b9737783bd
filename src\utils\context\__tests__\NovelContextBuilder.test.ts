/**
 * NovelContextBuilder 测试文件
 */

import { NovelContextBuilder, Chapter } from '../NovelContextBuilder';

describe('NovelContextBuilder', () => {
  const mockChapters: Chapter[] = [
    {
      id: '1',
      title: '第一章：开始',
      order: 0,
      content: '这是第一章的内容。\n\n第一段内容。\n\n第二段内容。'
    },
    {
      id: '2',
      title: '第二章：发展',
      order: 1,
      content: '这是第二章的内容。\n\n第一段内容。\n\n第二段内容。\n\n第三段内容。'
    },
    {
      id: '3',
      title: '第三章：高潮',
      order: 2,
      content: '这是第三章的内容。\n\n第一段内容。'
    }
  ];

  const currentChapter = mockChapters[1]; // 第二章

  test('应该正确计算段落位置', () => {
    const builder = new NovelContextBuilder(mockChapters, currentChapter, 10);
    const context = builder.buildFullContext();

    expect(context.currentChapter.id).toBe('2');
    expect(context.currentPosition.totalParagraphs).toBe(3);
    expect(context.beforeChapters).toHaveLength(1);
    expect(context.afterChapters).toHaveLength(1);
  });

  test('应该生成正确的上下文消息', () => {
    const builder = new NovelContextBuilder(mockChapters, currentChapter, 10);
    const messages = builder.buildContextMessages();

    // 应该包含位置概述
    expect(messages[0].content).toContain('【当前写作位置】第2章《第二章：发展》');
    
    // 应该包含前序章节
    expect(messages.some(msg => msg.content.includes('【第1章完整内容：第一章：开始】'))).toBe(true);
    
    // 应该包含后续章节
    expect(messages.some(msg => msg.content.includes('【第3章完整内容：第三章：高潮】'))).toBe(true);
  });

  test('应该正确处理空章节', () => {
    const emptyChapter: Chapter = {
      id: '1',
      title: '空章节',
      order: 0,
      content: ''
    };

    const builder = new NovelContextBuilder([emptyChapter], emptyChapter, 0);
    const context = builder.buildFullContext();

    expect(context.currentPosition.totalParagraphs).toBe(1);
    expect(context.currentPosition.currentParagraph).toBe(1);
  });

  test('应该正确分割段落', () => {
    const content = '第一段内容。\n\n第二段内容。\n\n第三段内容。';
    const chapter: Chapter = {
      id: '1',
      title: '测试章节',
      order: 0,
      content
    };

    const builder = new NovelContextBuilder([chapter], chapter, 15); // 光标在第二段开始
    const context = builder.buildFullContext();

    expect(context.currentPosition.totalParagraphs).toBe(3);
    expect(context.currentPosition.beforeParagraphs).toBe(1);
    expect(context.currentPosition.afterParagraphs).toBe(1);
  });

  test('应该生成兼容的上下文字符串', () => {
    const builder = new NovelContextBuilder(mockChapters, currentChapter, 10);
    const contextString = builder.buildContextString();

    expect(contextString).toContain('【当前写作位置】');
    expect(contextString).toContain('【第1章完整内容：第一章：开始】');
    expect(contextString).toContain('【第3章完整内容：第三章：高潮】');
  });
});
