"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Edge, Node, Viewport, useReactFlow } from 'reactflow';
import { Outline } from '../../../types/outline';
import { deepCopy } from '../../../utils/outlineUtils';

interface ScissorsToolProps {
  nodes: Node[];
  edges: Edge[];
  outline: Outline | null;
  bookId?: string;
  reactFlowWrapper: React.RefObject<HTMLDivElement>;
  onEdgesChange: (edges: Edge[]) => void;
  onChange: (updatedOutline: Outline) => void;
  updateOutlineParentChildRelationship: (currentOutline: Outline, parentId: string, childId: string) => void;
  updateOutlineSiblingRelationship: (currentOutline: Outline, nodeId1: string, nodeId2: string) => void;
}

/**
 * 检测两条线段是否相交，带容错范围
 */
function segmentsIntersect(
  ax1: number, ay1: number, ax2: number, ay2: number,
  bx1: number, by1: number, bx2: number, by2: number,
  tolerance: number = 5
): boolean {
  // 添加容错范围，扩大边界框检查
  if (Math.max(ax1, ax2) + tolerance < Math.min(bx1, bx2) - tolerance ||
      Math.min(ax1, ax2) - tolerance > Math.max(bx1, bx2) + tolerance ||
      Math.max(ay1, ay2) + tolerance < Math.min(by1, by2) - tolerance ||
      Math.min(ay1, ay2) - tolerance > Math.max(by1, by2) + tolerance) {
    return false;
  }
  
  // 计算行列式
  const det = (ax2 - ax1) * (by2 - by1) - (ay2 - ay1) * (bx2 - bx1);
  
  // 如果行列式接近0，线段可能平行，但我们仍然检查是否足够接近
  if (Math.abs(det) < 0.001) {
    // 检查线段是否共线且重叠
    // 计算点到线段的距离
    const distanceToSegment = (px: number, py: number, x1: number, y1: number, x2: number, y2: number) => {
      const A = px - x1;
      const B = py - y1;
      const C = x2 - x1;
      const D = y2 - y1;
      
      const dot = A * C + B * D;
      const lenSq = C * C + D * D;
      let param = -1;
      
      if (lenSq !== 0) param = dot / lenSq;
      
      let xx, yy;
      
      if (param < 0) {
        xx = x1;
        yy = y1;
      } else if (param > 1) {
        xx = x2;
        yy = y2;
      } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
      }
      
      const dx = px - xx;
      const dy = py - yy;
      
      return Math.sqrt(dx * dx + dy * dy);
    };
    
    // 检查第二条线段的端点是否接近第一条线段
    if (distanceToSegment(bx1, by1, ax1, ay1, ax2, ay2) <= tolerance ||
        distanceToSegment(bx2, by2, ax1, ay1, ax2, ay2) <= tolerance) {
      return true;
    }
    
    // 检查第一条线段的端点是否接近第二条线段
    if (distanceToSegment(ax1, ay1, bx1, by1, bx2, by2) <= tolerance ||
        distanceToSegment(ax2, ay2, bx1, by1, bx2, by2) <= tolerance) {
      return true;
    }
    
    return false;
  }
  
  // 计算交点参数
  const lambda = ((by2 - by1) * (bx2 - ax1) + (bx1 - bx2) * (by2 - ay1)) / det;
  const gamma = ((ay1 - ay2) * (bx2 - ax1) + (ax2 - ax1) * (by2 - ay1)) / det;
  
  // 检查交点是否在线段上，添加容错范围
  return (0 - tolerance/100 <= lambda && lambda <= 1 + tolerance/100) && 
         (0 - tolerance/100 <= gamma && gamma <= 1 + tolerance/100);
}

// Dummy showNotification if not globally available or for testing
const showNotification = (message: string, type: string, duration: number, position: string) => {
  console.log(`Notification (${type} @${position}, ${duration}ms): ${message}`);
};

const ScissorsTool: React.FC<ScissorsToolProps> = ({
  nodes,
  edges,
  outline,
  bookId,
  reactFlowWrapper,
  onEdgesChange,
  onChange,
  updateOutlineParentChildRelationship,
  updateOutlineSiblingRelationship
}) => {
  const [scissorsModeActive, setScissorsModeActive] = useState<boolean>(false);
  const [scissorsLineStart, setScissorsLineStart] = useState<{ x: number; y: number } | null>(null);
  const [scissorsLineEnd, setScissorsLineEnd] = useState<{ x: number; y: number } | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const { getViewport } = useReactFlow();

  // 处理鼠标按下事件
  const handlePaneMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.button === 2 && event.ctrlKey) { // 右键 + Ctrl
      event.preventDefault();
      event.stopPropagation();
      setScissorsModeActive(true);
      const currentMouseX = event.clientX;
      const currentMouseY = event.clientY;
      setScissorsLineStart({ x: currentMouseX, y: currentMouseY });
      setScissorsLineEnd({ x: currentMouseX, y: currentMouseY });
      setMousePosition({ x: currentMouseX, y: currentMouseY });
      reactFlowWrapper.current?.classList.add('scissors-mode-cursor');
    }
  }, [reactFlowWrapper]);

  // 处理鼠标移动事件
  const handlePaneMouseMove = useCallback((event: React.MouseEvent) => {
    if (scissorsModeActive) {
      const currentMouseX = event.clientX;
      const currentMouseY = event.clientY;
      setScissorsLineEnd({ x: currentMouseX, y: currentMouseY });
      setMousePosition({ x: currentMouseX, y: currentMouseY });
      
      // 如果剪刀线起点和终点都存在，检测与边的相交并高亮显示
      if (scissorsLineStart && reactFlowWrapper.current) {
        const rfBounds = reactFlowWrapper.current.getBoundingClientRect();
        const viewport = getViewport();
        
        // 清除所有边的高亮效果
        document.querySelectorAll('.edge-highlight').forEach(el => {
          el.classList.remove('edge-highlight');
        });
        
        // 检测相交的边并高亮显示
        edges.forEach(edge => {
          const sourceNode = nodes.find(n => n.id === edge.source);
          const targetNode = nodes.find(n => n.id === edge.target);
          
          if (sourceNode && targetNode && sourceNode.position && targetNode.position) {
            const getNodeCenterInViewport = (node: Node<any, string | undefined>, vp: Viewport, bounds: DOMRect) => {
              const nodeWidth = node.width || 150;
              const nodeHeight = node.height || 40;
              const canvasX = node.position.x + nodeWidth / 2;
              const canvasY = node.position.y + nodeHeight / 2;
              return { x: bounds.left + (canvasX * vp.zoom + vp.x), y: bounds.top + (canvasY * vp.zoom + vp.y) };
            };
            
            const sourcePosVp = getNodeCenterInViewport(sourceNode, viewport, rfBounds);
            const targetPosVp = getNodeCenterInViewport(targetNode, viewport, rfBounds);
            
            // 检测剪刀线与边是否相交
            const isIntersecting = segmentsIntersect(
              scissorsLineStart.x, scissorsLineStart.y, currentMouseX, currentMouseY,
              sourcePosVp.x, sourcePosVp.y, targetPosVp.x, targetPosVp.y,
              10 // 增加容错范围
            );
            
            // 如果相交，高亮显示边
            if (isIntersecting) {
              const edgePath = document.getElementById(edge.id);
              if (edgePath) {
                edgePath.classList.add('edge-highlight');
              }
            }
          }
        });
      }
    }
  }, [scissorsModeActive, scissorsLineStart, edges, nodes, getViewport, reactFlowWrapper]);

  // 处理鼠标松开事件
  const handlePaneMouseUp = useCallback((event: React.MouseEvent) => {
    if (scissorsModeActive && scissorsLineStart && scissorsLineEnd) {
      const rfBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!rfBounds) {
        setScissorsModeActive(false);
        reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
        return;
      }
      
      // 清除所有边的高亮效果
      document.querySelectorAll('.edge-highlight').forEach(el => {
        el.classList.remove('edge-highlight');
      });
      
      const viewport = getViewport();
      
      // 查找与剪刀线相交的边
      const edgesToDelete = edges.filter(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        const targetNode = nodes.find(n => n.id === edge.target);
        
        if (sourceNode && targetNode && sourceNode.position && targetNode.position) {
          const getNodeCenterInViewport = (node: Node<any, string | undefined>, vp: Viewport, bounds: DOMRect) => {
            const nodeWidth = node.width || 150;
            const nodeHeight = node.height || 40;
            const canvasX = node.position.x + nodeWidth / 2;
            const canvasY = node.position.y + nodeHeight / 2;
            return { x: bounds.left + (canvasX * vp.zoom + vp.x), y: bounds.top + (canvasY * vp.zoom + vp.y) };
          };
          
          const sourcePosVp = getNodeCenterInViewport(sourceNode, viewport, rfBounds);
          const targetPosVp = getNodeCenterInViewport(targetNode, viewport, rfBounds);
          
          // 使用改进的相交检测函数，增加容错范围
          return segmentsIntersect(
            scissorsLineStart.x, scissorsLineStart.y, scissorsLineEnd.x, scissorsLineEnd.y,
            sourcePosVp.x, sourcePosVp.y, targetPosVp.x, targetPosVp.y,
            10 // 增加容错范围
          );
        }
        return false;
      });
      
      // 如果找到相交的边，添加删除动画并更新数据
      if (edgesToDelete.length > 0) {
        console.log(`找到 ${edgesToDelete.length} 条相交的边，准备删除`);
        
        // 添加删除动画效果
        edgesToDelete.forEach(edge => {
          const edgeElement = document.getElementById(edge.id);
          if (edgeElement) {
            edgeElement.classList.add('edge-delete');
            // 添加淡出动画
            edgeElement.style.opacity = '0';
            edgeElement.style.transition = 'opacity 0.3s ease';
          }
        });
        
        // 延迟删除边，等待动画完成
        setTimeout(() => {
          // 从状态中移除边
          const updatedEdges = edges.filter(ed => !edgesToDelete.some(delEd => delEd.id === ed.id));
          onEdgesChange(updatedEdges);
          
          // 如果有大纲数据，更新大纲关系
          if (outline) {
            const updatedOutline = deepCopy(outline);
            
            // 更新每条边的关系
            edgesToDelete.forEach(edge => {
              if (edge.source && edge.target) { // 确保源和目标已定义
                if (edge.data?.relationshipType === 'parent-child') {
                  updateOutlineParentChildRelationship(updatedOutline, edge.source, edge.target);
                } else if (edge.data?.relationshipType === 'sibling') {
                  updateOutlineSiblingRelationship(updatedOutline, edge.source, edge.target);
                }
              }
            });
            
            // 更新大纲数据
            onChange(updatedOutline);
            
            // 显示成功通知
            showNotification(`已删除 ${edgesToDelete.length} 条连接`, 'success', 3000, 'top-center');
            
            // 添加数据库持久化逻辑
            if (bookId) {
              try {
                // 导入 outlineService
                import('@/factories/api/outlineService').then(({ outlineService }) => {
                  console.log('剪刀模式删除边后自动保存大纲数据...');
                  outlineService.saveOutline(bookId, updatedOutline)
                    .then(() => console.log('大纲数据自动保存成功'))
                    .catch(error => {
                      console.error('大纲数据自动保存失败:', error);
                      showNotification('保存失败，请手动保存您的更改', 'error', 5000, 'top-center');
                    });
                });
              } catch (error) {
                console.error('导入 outlineService 失败:', error);
                showNotification('保存失败，请手动保存您的更改', 'error', 5000, 'top-center');
              }
            }
          }
        }, 300);
      } else {
        console.log('没有找到相交的边');
      }
    }
    
    // 无论是否删除了边，都重置剪刀模式状态
    if (scissorsModeActive) {
      setScissorsModeActive(false);
      setScissorsLineStart(null);
      setScissorsLineEnd(null);
      setMousePosition(null);
      reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
    }
  }, [
    scissorsModeActive, scissorsLineStart, scissorsLineEnd, edges, nodes, getViewport, outline, 
    onChange, bookId, onEdgesChange, reactFlowWrapper, updateOutlineParentChildRelationship, 
    updateOutlineSiblingRelationship
  ]);

  // 切换剪刀模式
  const toggleScissorsMode = useCallback(() => {
    setScissorsModeActive(prev => {
      const newMode = !prev;
      if (newMode) {
        showNotification('剪刀模式已激活：按住 Ctrl 并右键拖动以删除连线', 'info', 5000, 'top-center');
        reactFlowWrapper.current?.classList.add('scissors-mode-cursor');
      } else {
        setScissorsLineStart(null);
        setScissorsLineEnd(null);
        setMousePosition(null);
        reactFlowWrapper.current?.classList.remove('scissors-mode-cursor');
      }
      return newMode;
    });
  }, [reactFlowWrapper]);

  // 添加样式
  useEffect(() => {
    const styleId = 'scissors-mode-styles';
    if (document.getElementById(styleId)) return;
    const styleElement = document.createElement('style');
    styleElement.id = styleId;
    styleElement.textContent = `
      .scissors-mode-cursor { cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%23ff0000" d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3h-3z"/></svg>') 0 24, auto; }
      .edge-highlight { stroke: #ff0000 !important; stroke-width: 3px !important; stroke-dasharray: 5, 5 !important; animation: edge-pulse 1s infinite; }
      .edge-delete { animation: fade-out 0.3s ease-out forwards; }
      @keyframes edge-pulse { from { stroke-opacity: 0.5; } 50% { stroke-opacity: 1; } to { stroke-opacity: 0.5; } }
      @keyframes fade-out { from { opacity: 1; } to { opacity: 0; } }
    `;
    document.head.appendChild(styleElement);
    return () => {
      const el = document.getElementById(styleId);
      if (el) document.head.removeChild(el);
    };
  }, []);

  // 渲染剪刀线
  const renderScissorsLine = () => {
    if (scissorsModeActive && scissorsLineStart && scissorsLineEnd && mousePosition && reactFlowWrapper.current) {
      const bounds = reactFlowWrapper.current.getBoundingClientRect();
      return (
        <svg style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 1000 }}>
          {/* 剪刀线 */}
          <line
            className="scissors-line"
            x1={scissorsLineStart.x - bounds.left}
            y1={scissorsLineStart.y - bounds.top}
            x2={scissorsLineEnd.x - bounds.left}
            y2={scissorsLineEnd.y - bounds.top}
            stroke="red"
            strokeWidth={3}
            strokeDasharray="5,5"
          />
          
          {/* 起点标记 */}
          <circle
            cx={scissorsLineStart.x - bounds.left}
            cy={scissorsLineStart.y - bounds.top}
            r={5}
            fill="red"
          />
          
          {/* 终点标记（剪刀图标） */}
          <text
            x={mousePosition.x - bounds.left}
            y={mousePosition.y - bounds.top}
            fontSize="24"
            fill="red"
            dominantBaseline="middle"
            textAnchor="middle"
            style={{
              transform: 'translate(10px, -10px)',
              filter: 'drop-shadow(0 0 2px white)'
            }}
          >
            ✂️
          </text>
          
          {/* 提示文本 */}
          <text
            x={mousePosition.x - bounds.left + 20}
            y={mousePosition.y - bounds.top - 20}
            fontSize="12"
            fill="red"
            style={{
              fontWeight: 'bold',
              filter: 'drop-shadow(0 0 2px white)'
            }}
          >
            剪刀模式：拖动以删除连线
          </text>
        </svg>
      );
    }
    return null;
  };

  return {
    scissorsModeActive,
    handlePaneMouseDown,
    handlePaneMouseMove,
    handlePaneMouseUp,
    toggleScissorsMode,
    renderScissorsLine
  };
};

export default ScissorsTool;
