"use client";

import React, { useState, useEffect } from 'react';

// 高级动画组件 - 500美金级别的动画效果
// 注意：这个组件设计为在安装framer-motion后使用，目前使用CSS动画作为过渡

interface AnimatedCardProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
  onClick?: () => void;
}

// 高级卡片动画组件
export const AnimatedCard: React.FC<AnimatedCardProps> = ({ 
  children, 
  delay = 0, 
  className = '',
  onClick 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div
      className={`premium-animated-card ${isVisible ? 'visible' : ''} ${isHovered ? 'hovered' : ''} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
      style={{
        transform: isVisible 
          ? (isHovered ? 'translateY(-12px) scale(1.02) rotateX(5deg)' : 'translateY(0) scale(1)')
          : 'translateY(30px) scale(0.95)',
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)',
        transformStyle: 'preserve-3d',
        perspective: '1000px'
      }}
    >
      {children}
    </div>
  );
};

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

// 高级按钮动画组件
export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  className = ''
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const newRipple = { id: Date.now(), x, y };
    setRipples(prev => [...prev, newRipple]);
    
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);

    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);

    if (onClick) onClick();
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 100%)',
          color: 'white',
          boxShadow: '0 4px 12px rgba(139, 69, 19, 0.3)'
        };
      case 'secondary':
        return {
          background: 'linear-gradient(135deg, #D2B48C 0%, #DEB887 100%)',
          color: '#333',
          boxShadow: '0 4px 12px rgba(210, 180, 140, 0.3)'
        };
      case 'danger':
        return {
          background: 'linear-gradient(135deg, #B22222 0%, #8B1A1A 100%)',
          color: 'white',
          boxShadow: '0 4px 12px rgba(178, 34, 34, 0.3)'
        };
      default:
        return {};
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return { padding: '8px 16px', fontSize: '14px' };
      case 'md':
        return { padding: '12px 24px', fontSize: '16px' };
      case 'lg':
        return { padding: '16px 32px', fontSize: '18px' };
      default:
        return {};
    }
  };

  return (
    <button
      className={`premium-animated-button ${className}`}
      onClick={handleClick}
      disabled={disabled || loading}
      style={{
        ...getVariantStyles(),
        ...getSizeStyles(),
        position: 'relative',
        border: 'none',
        borderRadius: '12px',
        fontWeight: '600',
        cursor: disabled || loading ? 'not-allowed' : 'pointer',
        overflow: 'hidden',
        transform: isPressed ? 'scale(0.95)' : 'scale(1)',
        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: disabled ? 0.6 : 1,
        ...(disabled || loading ? {} : {
          ':hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(139, 69, 19, 0.4)'
          }
        })
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = variant === 'primary' 
            ? '0 8px 25px rgba(139, 69, 19, 0.4)'
            : variant === 'secondary'
            ? '0 8px 25px rgba(210, 180, 140, 0.4)'
            : '0 8px 25px rgba(178, 34, 34, 0.4)';
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = variant === 'primary' 
            ? '0 4px 12px rgba(139, 69, 19, 0.3)'
            : variant === 'secondary'
            ? '0 4px 12px rgba(210, 180, 140, 0.3)'
            : '0 4px 12px rgba(178, 34, 34, 0.3)';
        }
      }}
    >
      {/* 波纹效果 */}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="ripple"
          style={{
            position: 'absolute',
            left: ripple.x,
            top: ripple.y,
            width: '0',
            height: '0',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.6)',
            transform: 'translate(-50%, -50%)',
            animation: 'ripple 0.6s linear',
            pointerEvents: 'none'
          }}
        />
      ))}
      
      {/* 内容 */}
      <span style={{ position: 'relative', zIndex: 1, display: 'flex', alignItems: 'center', gap: '8px' }}>
        {loading && (
          <div
            style={{
              width: '16px',
              height: '16px',
              border: '2px solid currentColor',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
          />
        )}
        {children}
      </span>
    </button>
  );
};

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  className?: string;
}

// 数字动画计数器
export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 1000,
  className = ''
}) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.floor(easeOutQuart * value);
      
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return (
    <span 
      className={`animated-counter ${className}`}
      style={{
        fontVariantNumeric: 'tabular-nums',
        transition: 'color 0.3s ease'
      }}
    >
      {displayValue.toLocaleString()}
    </span>
  );
};

// 页面转场动画组件
interface PageTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
}

export const PageTransition: React.FC<PageTransitionProps> = ({ children, isVisible }) => {
  return (
    <div
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
        transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
        willChange: 'transform, opacity'
      }}
    >
      {children}
    </div>
  );
};
