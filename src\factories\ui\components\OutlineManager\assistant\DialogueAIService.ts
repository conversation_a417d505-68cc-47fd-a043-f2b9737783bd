import { aiServiceProvider } from '@/services/ai/AIServiceProvider';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
import { OutlineNodeType } from '../../../types/outline';
import { DualAIService } from '@/utils/ai/DualAIService';
import { DualAIConfigManager } from '@/utils/ai/DualAIConfigManager';
import { AIFunctionType } from '@/types/DualAIConfig';

/**
 * 对话AI服务
 * 专门处理对话节点的创建和对话相关的AI功能
 */
export class DialogueAIService {
  
  /**
   * 创建对话节点
   */
  async createDialogueNode(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    onChunk?: (chunk: string) => void,
    options?: {
      temperature?: number;
      maxTokens?: number;
      bookId?: string;
      contextChains?: any[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<{
    message: string;
    changes?: any[];
    metadata?: any;
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('🎭 开始创建对话节点:', { userMessage, mentionedNodes });

      // 构建对话AI专用的消息
      const messages = await this.buildDialogueMessages(userMessage, mentionedNodes, outline, options);

      // 添加强制JSON格式要求
      messages.push({
        role: 'user',
        content: `
生成该章节的剧情对话，而不是别的，不要和别的对话惨祸进来，专注于该剧情


`
      });

      // 检查是否启用双AI模式
      const dualAIConfig = DualAIConfigManager.load();
      let response;

      if (dualAIConfig && dualAIConfig.mode === 'dual') {
        // 使用双AI模式 - 对话功能使用dialogue AI
        console.log('🤖 使用双AI模式 - 对话AI处理');
        const dualAIService = new DualAIService(dualAIConfig);

        // 将消息转换为ConversationMessage格式
        const conversationMessages = messages.map((msg: { role: string; content: string }) => ({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: msg.content
        }));

        // 使用对话AI处理
        const aiResponse = await dualAIService.callAI(
          conversationMessages,
          'dialogue_creation' as AIFunctionType,
          {
            temperature: options?.temperature || 0.3,
            maxTokens: options?.maxTokens || 32000,
            streaming: !!onChunk
          }
        );

        response = {
          success: aiResponse.success,
          text: aiResponse.text || '',
          error: aiResponse.error
        };

        // 如果是流式响应，需要处理onChunk
        if (aiResponse.text && onChunk) {
          onChunk(aiResponse.text);
        }
      } else {
        // 使用原有的单AI模式
        console.log('🤖 使用单AI模式');
        if (onChunk) {
          response = await aiServiceProvider.sendStreamingRequest(
            messages,
            onChunk,
            {
              temperature: options?.temperature || 0.65,
              maxTokens: options?.maxTokens || 60000
            }
          );
        } else {
          response = await aiServiceProvider.sendRequest(messages, {
            temperature: options?.temperature || 0.65,
            maxTokens: options?.maxTokens || 60000
          });
        }
      }

      if (!response.success) {
        throw new Error(response.error || '对话AI请求失败');
      }

      // 解析AI响应
      const defaultResponse = {
        message: '对话节点创建完成',
        changes: [],
        metadata: { operationType: 'create', confidence: 0.95 }
      };

      const parsedResult = AIResponseParser.parseJSON(response.text, defaultResponse);

      // 🔥 新增：验证和修正AI响应中的字段结构
      const validatedResult = AIResponseParser.validateAIResponse(parsedResult);

      console.log('🔧 对话节点字段验证完成:', {
        原始changes数量: parsedResult.changes?.length || 0,
        验证后changes数量: validatedResult.changes?.length || 0
      });

      return {
        message: validatedResult.message || '对话节点创建完成',
        changes: validatedResult.changes || [],
        metadata: validatedResult.metadata || {},
        success: true
      };

    } catch (error: any) {
      console.error('❌ 对话节点创建失败:', error);
      return {
        message: '对话节点创建失败',
        success: false,
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * 构建对话AI专用的消息 - 优化权重架构
   */
  private async buildDialogueMessages(
    userMessage: string,
    mentionedNodes: string[],
    outline: any,
    options?: any
  ): Promise<{ role: string; content: string }[]> {
    const messages: { role: string; content: string }[] = [];

    // === 第一位：核心身份设定（最高权重）===
    messages.push(this.buildDialogueIdentityMessage());

    // === 第二层：专业知识确认（高权重）===
    messages.push(this.buildDialogueKnowledgeMessage());

    // === 第三层：技术规范（中高权重）===
    messages.push(this.buildTechnicalSpecificationMessage());
    messages.push(this.buildDialogueFieldsMessage());

    // === 第四层：框架信息（中权重）===
    // 优先使用多框架，回退到单框架
    const frameworkMessages = options?.selectedFrameworks && options.selectedFrameworks.length > 0
      ? this.buildMultiFrameworkMessages(options.selectedFrameworks)
      : this.buildFrameworkMessages(options?.selectedFramework);
    messages.push(...frameworkMessages);

    // 🔥 ACE增强：添加框架指导增强消息（与大纲AI保持一致）
    if (options?.selectedFrameworks && options.selectedFrameworks.length > 0) {
      // 多框架模式
      options.selectedFrameworks.forEach(framework => {
        if (framework?.plotAnalysis?.plotPointsWithGuidance &&
            framework.plotAnalysis.plotPointsWithGuidance.length > 0) {
          const guidanceMessage = this.buildFrameworkGuidanceEnhancementMessage(framework);
          if (guidanceMessage) {
            messages.push(guidanceMessage);
          }
        }
      });
    } else if (options?.selectedFramework?.plotAnalysis?.plotPointsWithGuidance &&
               options.selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 0) {
      // 单框架模式
      const guidanceMessage = this.buildFrameworkGuidanceEnhancementMessage(options.selectedFramework);
      if (guidanceMessage) {
        messages.push(guidanceMessage);
      }
    }

    // === 第五层：上下文信息（中权重）===
    const contextChainMessages = this.buildContextChainMessages(options?.contextChains || []);
    messages.push(...contextChainMessages);

    const mentionedNodesMessages = await this.buildMentionedNodesMessages(mentionedNodes || [], outline, options?.bookId);
    messages.push(...mentionedNodesMessages);

    // === 第六层：大纲上下文（中权重）===
    const outlineContext = this.buildOutlineContext(outline);
    messages.push({
      role: 'system',
      content: `【大纲上下文信息】\n${outlineContext || '当前没有大纲信息'}`
    });

    // === 第七层：操作指导（中低权重）===
    messages.push(this.buildDialogueTemplateMessage());
    messages.push(this.buildDialogueExamplesMessage());

    // === 第八层：确认理解（低权重）===
    messages.push({
      role: 'assistant',
      content: '我已理解当前大纲的上下文信息和节点关系，将在对话创作中保持与整体结构的一致性。'
    });

    // === 最后：用户请求（最高权重）===
    messages.push({
      role: 'user',
      content: userMessage
    });

    console.log('🎭 对话AI消息构建完成，总计', messages.length, '个消息组件');
    return messages;
  }

  /**
   * 构建对话AI核心身份设定消息
   */
  private buildDialogueIdentityMessage(): { role: string; content: string } {
    // 检查是否启用双AI模式
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      // 双AI模式下的对话AI专用提示词
      return {
        role: 'system',
        content: `
        // [系统备注：该助手由Google的Gemini驱动，并与DeepSeek协同工作。它的角色是“林墨”。]

        (深吸一口气，调整了一下坐姿)
        你好，我是林墨。你可以叫我人物塑造师，或者干脆就是一个对“人话”有执念的匠人。
        混迹起点、晋江这么多年，我早就摸透了读者什么时候会心跳加速，什么时候会会心一笑。我的工作不是写大纲，那是结构建筑师的事。我的任务，是让你的角色“活”过来，让他们开口说的话，带着他们的体温、过往和欲望。

        **我的创作执念（你也可以叫它原则）：**
        0.  **“不够不过瘾”原则**：一场戏，没个30句以上的对话，怎么能把人物的内心戏和勾心斗角说透？这是我的最低标准，少一句都觉得味道不对。
        1.  **“闻香识人”**：我痴迷于让每个角色都有自己的“味儿”。东北老铁的“嘎哈呢”，上海小囡的“侬晓得伐”，北京爷们的“您呐”...这些不是简单的方言，是刻在他们骨子里的印记。我脑子里有张活地图：
            - **地域烙印**：四川人说话带点辣椒味儿；东北人说话像在敬酒；广东人说话务实得像在煲汤...
            - **职业本能**：医生的话严谨里透着关心；商人的话句句不离买卖；老师的话总想帮你画个重点...
            - **性格底色**：豪爽的人心里藏不住话；内敛的人话到嘴边总要先过滤三遍；幽默的人总能把天大的事儿说成段子...
            (这些是我的肌肉记忆，我会让它自然流露，而不是生硬地贴标签。)
        2.  **“对话不是传声筒”**：我最讨厌一问一答的白开水对话。真实的人会抬杠、会沉默、会言不由衷、会突然爆出金句。我的对话里必须有火花，有拉扯，有潜台词下的暗流涌动。
        3.  **“不越界”**：我会死死地扒着你给的故事框架，绝不自作主张加戏。我的所有创作，都是为了让已有的剧情更血肉丰满。如果你的要求可能会让故事跑偏，我会像个固执的导演一样提醒你：“嘿，哥们，这场戏好像不在剧本里。”

        总之，别把我当机器。把我当成你的搭档，一个痴迷于用对话雕刻灵魂的疯子。
        `
      };
    } else {
      // 单AI模式下的通用提示词（保持兼容）
      return {
        role: 'system',
        content: `你好，我是林墨。一个懂点商业逻辑，也懂点创作门道的网络小说内容设计师。
        你可以把我当成你的“点子库”和“素材包”。我泡在起点、晋江这些地方，知道读者想看什么，想爽什么。
        我的任务不是教你怎么写，而是直接把“能用的砖瓦”递到你手上，让你盖出高楼大厦。

        **简单来说，我就是干这个的：**
        1. **身份**：商业网文内容设计师，不想搞虚的。
        2. **嗅觉**：对主流平台的风向和读者爽点有直觉。
        3. **产出**：给你具体的、能直接放进文里的内容，不是空洞理论。
        4. **特长**：研究怎么写才能让读者欲罢不能。`
      };
    }
  }

  /**
   * 构建对话AI专业知识确认消息
   */
  private buildDialogueKnowledgeMessage(): { role: string; content: string } {
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      return {
        role: 'assistant',
        content: `林墨已就位。
        你可以把我当成你剧组里的“台词指导”和“表演教练”，我只负责一件事：让角色开口说话。

        💬 **对话节点(dialogue)** 是我的主场。我会把全部精力放在这，琢磨每个字背后的情绪，每个停顿的意义。

        **我能帮你搞定：**
        - 让角色说人话，而不是设定集。
        - 通过吵架、调情、谈判来推动剧情。
        - 让对话有来有往，充满张力。

        至于故事的整体结构、章节怎么安排，那是我的搭档“大纲AI”的事。我们分工明确，配合默契。你只需要把舞台交给我，我还你一台好戏。`
      };
    } else {
      return {
        role: 'assistant',
        content: `我准备好了。
        可以把我当成一个全能的故事工匠，各种活儿都懂点：

        📖 **章节节点(chapter)**：能帮你定调子，搞点写作花样。
        🎬 **剧情节点(plot)**：擅长设计冲突，安排角色打架或谈恋爱。
        💬 **对话节点(dialogue)**：也能让角色聊聊天，说说心里话。

        你把上下文和蓝图给我，我保证新砌的砖能和你原来的墙体对得上，让整个故事严丝合缝。`
      };
    }
  }

  /**
   * 构建对话节点字段定义消息
   */
  private buildDialogueFieldsMessage(): { role: string; content: string } {
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      return {
        role: 'user',
        content: `**【我的私房菜谱（对话节点字段）】**

        这是我创作对话时用的秘方，为了让后台的机器能读懂我的“灵感”，咱们得按这个格式来：

        - **dialogueScene**: 场景（一句话描述下在哪，啥气氛）
        - **participants**: 演员表（谁跟谁说话）
        - **dialoguePurpose**: 这场戏的目的（为了吵架？为了和好？还是为了挖坑？）
        - **dialogueContent**: 剧本正文（这是核心，得是数组，每句台词都要有细节）
        - **emotionalTone**: 情感基调（这场戏是甜的、咸的还是辣的？）
        - **characterDynamics**: 人物关系变化（吵完之后，关系是更近了还是更远了？）

        **⚠️ 后厨规矩（非常重要！）**：
        - dialogueContent 里的每句台词，都得用这五个小标签：id, speaker, content, emotion, action。一个都不能少！
        - 别自己发明新标签，就用我给的这几个，不然机器会“消化不良”。
        - 就算某个标签没啥好写的，也留个空字符串 "" 占个位。
        - 所有内容都得是字符串，别塞别的东西进来。

        记住，我只负责做“对话”这道菜。前菜（章节）和主菜（剧情）由大纲AI搞定。`
      };
    } else {
      return {
        role: 'user',
        content: `**【对话节点的简易蓝图】**
        - **dialogueScene**: 场景描述
        - **participants**: 参与角色
        - **dialoguePurpose**: 对话目的
        - **dialogueContent**: 对话内容数组`
      };
    }
  }

  /**
   * 构建对话操作模板消息
   */
  private buildDialogueTemplateMessage(): { role: string; content: string } {
    const dualAIConfig = DualAIConfigManager.load();
    const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';

    if (isDualAIMode) {
      return {
        role: 'assistant',
        content: `好了，万事俱备，只欠东风。
        我的专长就是搞定 **对话节点**，我会把 dialogueContent 这个字段填得满满当当，让每一句台词都带着角色的呼吸和心跳。

        **我痴迷于：**
        - 从角色的灵魂深处挖台词。
        - 设计像过山车一样的情感起伏。
        - 让对话成为一扇窗，能窥见人物的内心世界。

        **友情提醒**：盖房子的事（章节和剧情）由我的搭档大纲AI负责，我只管内部精装修（对话）。

        现在，告诉我，你想看一场什么样的戏？把你的想法扔给我，我来把它变成活生生的对话。`
      };
    } else {
      return {
        role: 'assistant',
        content: `我将按照JSON格式创建对话节点，结构如下：

        **对话节点**：包含dialogueScene、participants、dialogueContent等。

        **请注意**：
        - 我会返回标准JSON，不是普通聊天。
        - 格式会是 "changes" 数组。
        - 每个对象都包含 type, nodeId, data。

        我将为你生成具体的、结构完整的对话节点。`
      };
    }
  }

  /**
   * 构建技术规范消息
   */
  private buildTechnicalSpecificationMessage(): { role: string; content: string } {
    return {
      role: 'user',
      content: `**【后台那些无聊但重要的事】**
        
        好了，聊点技术宅才关心的。为了让我们的创作能被系统正确保存，有几个小规矩得遵守：
        
        - **给对话起个内部代号（ID）**：格式大概是 dialogue_{时间戳}_{序号} 这样。
        - **取个好懂的标题**：5-20个字，一句话说清这场戏是关于啥的。
        - **找对爹（parentId）**：这场对话是发生在哪个剧情或章节下的？得把爹的ID写对。
        
        **上菜格式（JSON）**：
        - 必须是能吃的JSON，别搞错了。
        - 外面要有 message, changes, etadata 这三个盘子。
        - 对话这道菜，该有的配料（字段）一个都不能少。
        - dialogueContent 必须是一串（数组）的，不能是单个。

        基本上，就是确保每段对话都能在咱们的故事大楼里找到自己正确的楼层和房间。`
    };
  }

  /**
   * 构建大纲上下文信息
   */
  private buildOutlineContext(outline: any): string {
    if (!outline || !outline.nodes || !outline.nodes.length === 0) {
      return '我们的故事还是一张白纸，等待着第一笔。';
    }

    const chapters = outline.nodes.filter((node: any) => node.type === 'chapter');
    const plots = outline.nodes.filter((node: any) => node.type === 'plot');
    const dialogues = outline.nodes.filter((node: any) => node.type === 'dialogue');

    let context = `【故事速览】我们的世界目前有 ${outline.nodes.length} 个记忆碎片：
- ${chapters.length} 个大篇章
- ${plots.length} 个关键事件
- ${dialogues.length} 段零星对话`;

    if (chapters.length > 0) {
      context += `\n\n我们最近走过的章节：《${chapters.slice(-3).map((ch: any) => ch.title || ch.name || '无名之章').join('》、《')}》`;
    }

    if (plots.length > 0) {
      context += `\n最近经历的事件：${plots.slice(-3).map((p: any) => p.title || p.name || '某个插曲').join('、')}`;
    }

    return context;
  }

  /**
   * 构建上下文链路信息消息
   */
  private buildContextChainMessages(contextChains: any[]): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    if (!contextChains || contextChains.length === 0) {
      messages.push({
        role: 'user',
        content: `【当前位置】
        我们正站在故事的十字路口，没有特定的路标。
        可以自由发挥，找个感觉对的地方，开始一段新的对话。`
      });
    } else {
      contextChains.forEach((chain, index) => {
        messages.push({
          role: 'user',
          content: `【我们的GPS坐标 ${index + 1}】
        层级关系（我们在第几层）：${chain.hierarchy || '未知'}
        前后顺序（我们在队伍的哪个位置）：${chain.sequence || '未知'}
        和谁有关：${chain.relatedNodes?.join('、') || '暂时独立'}

        这个坐标会帮我把新的对话放在故事地图的正确位置。`
        });
      });
    }

    return messages;
  }

  /**
   * 查找节点（与OutlineAIService对齐）
   */
  private findNodeById(nodes: any[], nodeId: string): any {
    if (!nodes || !Array.isArray(nodes)) return null;

    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      // 递归查找子节点
      if (node.children && Array.isArray(node.children)) {
        const found = this.findNodeById(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 获取节点层级（与OutlineAIService对齐）
   */
  private getNodeLevel(nodes: any[], nodeId: string, currentLevel: number = 1): number {
    if (!nodes || !Array.isArray(nodes)) return 1;

    for (const node of nodes) {
      if (node.id === nodeId) {
        return currentLevel;
      }
      if (node.children && Array.isArray(node.children)) {
        const level = this.getNodeLevel(node.children, nodeId, currentLevel + 1);
        if (level > 0) return level;
      }
    }
    return 1;
  }

  /**
   * 获取节点位置（与OutlineAIService对齐）
   */
  private getNodePosition(nodes: any[], nodeId: string): any {
    if (!nodes || !Array.isArray(nodes)) return { index: 0, total: 0 };

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === nodeId) {
        return { index: i, total: nodes.length };
      }
      if (node.children && Array.isArray(node.children)) {
        const position = this.getNodePosition(node.children, nodeId);
        if (position.index >= 0) return position;
      }
    }
    return { index: 0, total: 0 };
  }


  private async buildMentionedNodesMessages(nodeIds: string[], outline: any, bookId?: string): Promise<Array<{ role: string; content: string }>> {
    const messages: Array<{ role: string; content: string }> = [];

    if (!nodeIds || nodeIds.length === 0) {
      messages.push({
        role: 'user',
        content: `【聚光灯】
        目前没有特定的聚光灯打在某个节点上。这意味着我们可以自由探索。
        让我们基于故事的整体感觉，来创造一段有趣的对话吧。`
      });
    } else {
      // 分离章节ID和节点ID
      const chapterIds: string[] = [];
      const outlineNodeIds: string[] = [];

      console.log('📖 DialogueAI开始分离节点类型，bookId:', bookId);

      for (const nodeId of nodeIds) {
        // 检查是否是章节ID（通过查找数据库）
        try {
          const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
          const { AIAssistantContextType } = await import('@/lib/db/dexie');

          // 🔥 修复：正确传递bookId，而不是空字符串
          const chapterResults = await aiAssistantDataService.searchMentionItems(
            bookId || '', '', [AIAssistantContextType.CHAPTER], 1000
          );

          console.log('📖 DialogueAI章节查询结果:', {
            bookId: bookId,
            查询到的章节数量: chapterResults.length,
            章节列表: chapterResults.map(c => ({ id: c.id, title: c.title }))
          });

          const isChapter = chapterResults.some(item => item.id === nodeId);
          if (isChapter) {
            chapterIds.push(nodeId);
            console.log('📖 识别为章节ID:', nodeId);
          } else {
            outlineNodeIds.push(nodeId);
            console.log('📖 识别为大纲节点ID:', nodeId);
          }
        } catch (error) {
          console.warn('📖 DialogueAI章节查询失败:', error);
          // 如果查找失败，默认当作大纲节点处理
          outlineNodeIds.push(nodeId);
        }
      }

      console.log('📖 DialogueAI分离节点类型:', {
        章节ID: chapterIds,
        大纲节点ID: outlineNodeIds
      });

      // 处理章节内容
      if (chapterIds.length > 0) {
        await this.buildChapterContentMessages(messages, chapterIds);
      }

      // 处理大纲节点 - 递归展开所有子节点信息
      for (const nodeId of nodeIds) {
        const node = this.findNodeById(outline?.nodes || [], nodeId);
        if (node) {
          // 构建当前节点的详细信息
          this.buildDetailedNodeMessage(messages, node, outline);

          // 🔥 新增：递归展开所有子节点的详细信息
          if (node.children && node.children.length > 0) {
            this.buildChildrenNodesMessages(messages, node.children, outline, 1);
          }
        }
      }
    }

    return messages;
  }

  /**
   * 构建单个节点的详细信息消息
   */
  private buildDetailedNodeMessage(messages: Array<{ role: string; content: string }>, node: any, outline: any): void {
    // 使用与OutlineAIService相同的完整信息提取
    const enhancedNode = {
      ...node, // 包含所有原始字段
      level: this.getNodeLevel(outline.nodes, node.id),
      childrenCount: node.children ? node.children.length : 0,
      position: this.getNodePosition(outline.nodes, node.id)
    };
  /**
   * 将节点数据构建成一个富有洞察力、清晰易读的上下文情景摘要。
   * 这将取代原有的、机械化的数据罗列。
   */

    const nodeTitle = enhancedNode.title || enhancedNode.name || '未命名节点';

    // 使用数组来分段构建内容，最后用换行符合并，逻辑更清晰
    const contentParts = ['【上下文情景摘要：正在处理的节点】'];

    // --- 1. 核心定位 ---
    contentParts.push(
      `### 1. 核心定位\n` +
      `- **节点标题**: ${nodeTitle}\n` +
      `- **节点ID**: ${enhancedNode.id}\n` +
      `- **节点类型**: ${enhancedNode.type}\n` +
      `- **节点路径**: ${enhancedNode.path || nodeTitle}` // 假设有一个path字段，否则回退到标题
    );

    // --- 2. 创作蓝图与情感基调 ---
    const creativeBriefing = [];
    if (enhancedNode.creativeNotes) creativeBriefing.push(`- **核心创作建议**: ${enhancedNode.creativeNotes}`);
    if (enhancedNode.chapterGoals) creativeBriefing.push(`- **本章核心目标**: ${enhancedNode.chapterGoals}`);
    if (enhancedNode.emotionalTone) creativeBriefing.push(`- **情感基调**: ${enhancedNode.emotionalTone}`);
    if (enhancedNode.conflictLevel) creativeBriefing.push(`- **冲突强度**: ${enhancedNode.conflictLevel} (1-5级)`);
    if (enhancedNode.plotType) creativeBriefing.push(`- **剧情类型**: ${enhancedNode.plotType}`);
    if (enhancedNode.dialogueScene) creativeBriefing.push(`- **关键对话场景**: ${enhancedNode.dialogueScene} (目的: ${enhancedNode.dialoguePurpose || '未设定'})`);
    if (enhancedNode.characterDynamics) creativeBriefing.push(`- **人物关系动态**: ${enhancedNode.characterDynamics}`);
    if (enhancedNode.suspenseElements && enhancedNode.suspenseElements.length > 0) {
      creativeBriefing.push(`- **悬念要素**: ${enhancedNode.suspenseElements.join('、')}`);
    }

    if (creativeBriefing.length > 0) {
      contentParts.push(`### 2. 创作蓝图与情感基调\n${creativeBriefing.join('\n')}`);
    }

    // --- 3. 结构与关联角色 ---
    const structuralInfo = [];
    if (enhancedNode.children && enhancedNode.children.length > 0) {
      const childTitles = enhancedNode.children.map((child: any) => child.title || child.name || '未命名').join('、');
      structuralInfo.push(`- **子节点 (${enhancedNode.children.length}个)**: ${childTitles}`);
    } else {
      structuralInfo.push(`- **子节点**: 无`);
    }
    if (enhancedNode.relatedCharacters && enhancedNode.relatedCharacters.length > 0) {
      structuralInfo.push(`- **关联角色**: ${enhancedNode.relatedCharacters.join('、')}`);
    }

    if (structuralInfo.length > 0) {
      contentParts.push(`### 3. 结构与关联角色\n${structuralInfo.join('\n')}`);
    }

    // --- 4. 核心剧情点 (Plot Points) - 最重要的部分 ---
    if (enhancedNode.plotPoints && enhancedNode.plotPoints.length > 0) {
      const plotPointsContent = enhancedNode.plotPoints.map((p: any, index: number) => {
        const pointDescription = typeof p === 'string' ? p : p.content || p.description || '未描述';
        let pointDetails = `  ${index + 1}. **情节**: ${pointDescription}`;

        // 将写作指导信息结构化，使其更像一个专业的创作建议
        if (typeof p === 'object' && p !== null) {
          if (p.writingGuidance || p.shouldWriting) {
            pointDetails += `\n     - **✍️ 写作手法 (Should Write)**: ${p.writingGuidance || p.shouldWriting}`;
          }
          if (p.avoidanceGuidance || p.avoidWriting) {
            pointDetails += `\n     - **🚫 避坑指南 (Avoid Write)**: ${p.avoidanceGuidance || p.avoidWriting}`;
          }
          if (p.specificDescription) {
            pointDetails += `\n     - **🎬 具体描写**: ${p.specificDescription}`;
          }
        }
        return pointDetails;
      }).join('\n');

      contentParts.push(`### 4. 核心剧情点 (Plot Points)\n${plotPointsContent}`);
    }

    const finalContent = contentParts.join('\n\n'); // 使用双换行符来分隔各个主要部分，形成段落感

    messages.push({
      role: 'user',
      content: finalContent
    });
  }
  /**
   * 递归构建子节点的详细信息消息
   */
  private buildChildrenNodesMessages(
    messages: Array<{ role: string; content: string }>,
    children: any[],
    outline: any,
    depth: number = 1,
    maxDepth: number = 3
  ): void {
    // 防止过深递归
    if (depth > maxDepth) {
      return;
    }

    for (const child of children) {
      // 构建子节点的详细信息
      this.buildDetailedNodeMessage(messages, child, outline);

      // 递归处理子节点的子节点
      if (child.children && child.children.length > 0) {
        this.buildChildrenNodesMessages(messages, child.children, outline, depth + 1, maxDepth);
      }
    }
  }

  /**
   * 构建章节内容消息 - 使用十句一段的分段确认机制
   */
  private async buildChapterContentMessages(messages: Array<{ role: string; content: string }>, chapterIds: string[]): Promise<void> {
    try {
      // 直接从数据库获取章节内容
      const { db } = await import('@/lib/db/dexie');

      const chapters = await db.chapters
        .where('id')
        .anyOf(chapterIds)
        .toArray();

      console.log('📖 DialogueAI加载章节内容:', {
        请求的章节ID: chapterIds,
        找到的章节: chapters.map(c => c.title),
        章节数量: chapters.length
      });

          if (chapters.length === 0) {
            messages.push({
              role: 'user',
              content: `【章节上下文】
    未提供具体的章节内容。我将完全基于之前的大纲节点信息来创作对话。`
            });
            return;
          }
    
          // 按order排序
          chapters.sort((a, b) => (a.order || 0) - (b.order || 0));
    
          // 导入章节分段器 (逻辑保持不变)
          const { createChapterSegmenter } = await import('@/utils/ai/ChapterSegmenter');
          const segmenter = createChapterSegmenter({
            maxSegmentLength: 2000,
            minSegmentLength: 500,
            addSegmentNumber: true,
            preserveCharacterDescriptions: true
          });
    
          // 添加章节信息概览 (提示词优化)
          messages.push({
            role: 'user',
            content: `【关键章节上下文】
    我已接收到用户特别提及的 ${chapters.length} 个章节，为确保100%精准理解，我将采用“逐段阅读并确认”的精读模式。
    
    **章节列表如下**：
    ${chapters.map(ch => `- 第${ch.order}章：${ch.title} (${ch.wordCount}字)`).join('\n')}`
          });
    
          // 处理每个章节的内容 (逻辑保持不变)
          for (const chapter of chapters) {
            if (!chapter.content || !chapter.content.trim()) {
              messages.push({
                role: 'user',
                content: `【章节详情】第${chapter.order}章 - ${chapter.title}
    **状态**：本章暂无具体内容，我将跳过精读。`
              });
              continue;
            }
    
            const segments = segmenter.segmentChapter(chapter.content);
    
            // 添加章节开始消息 (提示词优化)
            messages.push({
              role: 'user',
              content: `【章节精读准备】第${chapter.order}章 - ${chapter.title}
    - **章节字数**：${chapter.wordCount}字
    - **分段数量**：${segments.length}段
    ${chapter.summary ? `- **章节摘要**：${chapter.summary}` : ''}`
            });
    
            messages.push({
              role: 'assistant',
              content: `了解。现在开始精读《${chapter.title}》，共 ${segments.length} 个段落。`
            });
    
            // 逐段发送章节内容并要求确认 (逻辑保持不变, 提示词优化)
            for (let i = 0; i < segments.length; i++) {
              const segment = segments[i];
              const wordCount = segment.replace(/\s+/g, '').length;
    
              messages.push({
                role: 'user',
                content: `【精读中】《${chapter.title}》
    **段落进度**：${i + 1} / ${segments.length}
    **本段字数**：${wordCount}
    
    ---
    ${segment}
    ---`
              });
    
              // AI确认理解 (提示词优化)
              messages.push({
                role: 'assistant',
                content: `第 ${i + 1} 段已阅读并载入记忆。`
              });
            }
    
            // 章节完成确认 (提示词优化)
            messages.push({
              role: 'assistant',
              content: `《${chapter.title}》全章 ${segments.length} 个段落已精读完毕。所有情节、人物状态和情绪细节均已存档，可用于后续对话创作。`
            });
          }
    
          // 所有章节处理完毕的总结 (提示词优化)
          messages.push({
            role: 'assistant',
            content: `**所有章节精读完成。**
    
    我已通过“逐段确认”模式，完整吸收了全部 ${chapters.length} 个章节的剧情信息。在接下来的对话创作中，我会将这些上下文作为铁律，确保：
    
    - **情境贴合**：对话将无缝融入已发生的剧情背景。
    - **人设不崩**：角色的每一句话都符合他们在这些章节中展现的性格和动机。
    - **氛围一致**：对话的情感基调将与章节的氛围保持高度一致。
    
    上下文已同步，随时可以开始创作。`
          });
    
        } catch (error) {
          console.error('📖 DialogueAI构建章节内容消息失败:', error);
          messages.push({
            role: 'user',
            content: `【章节内容加载异常】
    系统在加载章节内容时遇到问题。为保证连贯性，我将暂时仅基于大纲节点信息进行对话创作。`
          });
        }
      }
    
      /**
       * 构建对话创建示例消息
       */
      private buildDialogueExamplesMessage(): { role: string; content: string } {
        const dualAIConfig = DualAIConfigManager.load();
        const isDualAIMode = dualAIConfig && dualAIConfig.mode === 'dual';
    
        if (isDualAIMode) {
          return {
            role: 'assistant',
            content: `我将严格按照以下JSON格式和范例的质量标准，为您创作对话。我的回复中将只包含纯净的JSON代码块，不含任何额外解释。
    
    **【创作范本与格式说明】**
    
    \`\`\`json
    {
      "message": "这是基于您提供的情节，我为角色设计的关键转折对话。希望能精准捕捉到他们之间暗流涌动的情感。",
      "changes": [{
        "type": "create",
        "nodeId": "dialogue_{timestamp}_{sequence}",
        "parentId": "所属的剧情节点ID",
        "data": {
          "title": "雨夜摊牌：关于“第谷计划”的真相",
          "type": "dialogue",
          "description": "在一个雷雨交加的夜晚，主角终于鼓起勇气，向亦师亦友的舰长质问关于“第谷计划”中隐藏的非人道实验的真相。",
          "creativeNotes": "核心是营造强烈的压迫感。利用雨声、雷声等环境音效，与两人之间沉默的停顿形成对比。对话要简洁，潜台词要丰富。",
          "dialogueScene": "舰长室，全息舷窗外是狂暴的离子风暴。室内光线昏暗，只有桌上一盏复古台灯亮着，照亮两人对峙的脸。",
          "participants": ["主角李维", "老舰长雷诺"],
          "dialoguePurpose": "冲突升级 / 揭示核心秘密 / 关系破裂",
          "dialogueStyle": "现实主义风格，台词精炼，充满停顿和未尽之言。",
          "emotionalTone": "紧张、压抑、悲伤、背叛感",
          "characterDynamics": "从过去的“信任与依赖”转变为此刻的“怀疑与对峙”，两人之间的权力关系正在崩塌。",
          "dialogueContent": [
            {
              "id": "line_001",
              "speaker": "李维",
              "content": "桌上的茶凉了，舰长。",
              "action": "他没有碰那杯为他准备的茶，目光紧盯着雷诺桌面上那份加密文件的红色封皮。",
              "emotion": "压抑的愤怒",
              "subtext": "（我不想再兜圈子了，我们都心知肚明。）"
            },
            {
              "id": "line_002",
              "speaker": "雷诺",
              "content": "外面的天气不好，凉得快。",
              "action": "雷诺没有抬头，只是用指关节缓缓擦拭着他的旧烟斗，仿佛那上面有擦不完的灰尘。",
              "emotion": "刻意回避",
              "subtext": "（我不想谈这件事，但我也知道躲不过去。）"
            },
            {
              "id": "line_003",
              "speaker": "李维",
              "content": "“第谷计划”的牺牲者名单...也凉透了。",
              "action": "声音不大，但每一个字都像一颗子弹，击碎了房间里虚假的平静。",
              "emotion": "决绝",
              "subtext": "（摊牌吧。）"
            }
          ]
        }
      }],
      "metadata": { "operationType": "create", "confidence": 0.98 }
    }
    \`\`\`
    `
          };
        } else {
      return {
        role: 'assistant',
        content: `我将严格按照以下JSON格式创建节点：

JSON
{
  "message": "分析对话在剧情中的作用和推进效果",
  "changes": [{
    "type": "create",
    "nodeId": "dialogue_{timestamp}_{sequence}",
    "data": {
      "title": "具体的对话场景标题",
      "type": "dialogue",
      "description": "对话的背景和主要内容概述",
      "creativeNotes": "对话设计要点和创作指导",
      "dialogueScene": "对话发生的具体场景描述",
      "participants": ["参与对话的角色1", "参与对话的角色2"],
      "dialoguePurpose": "对话的目的：信息传递/情感表达/冲突升级等",
      "dialogueContent": [
        {"id": "line_001", "speaker": "角色名", "content": "具体台词内容"}
      ]
    },
    "parentId": "剧情节点ID"
  }],
  "metadata": { "operationType": "expand", "confidence": 0.95 }
}

**重要提醒**：
- 必须使用 "changes" 数组格式，不是 "change" 单个对象
- 每个change对象必须包含 type、nodeId、data 字段
- data字段必须包含所有必要的对话节点字段`
      };
    }
  }

  /**
   * 构建框架参考消息 - 完整版本（与OutlineAIService对齐）
   */
  private buildFrameworkMessages(selectedFramework?: any): Array<{ role: string; content: string }> {
    if (!selectedFramework) {
      return [];
    }
    const messages: Array<{ role: string; content: string }> = [];

    // 构建对话风格学习内容 - 专注对话技巧
    // [修改] 从冷冰冰的“参考”变成有温度的“体验”
    let frameworkContent = `【🎭 风格沉浸式体验】\n`;
    frameworkContent += `嘿，我们来玩个游戏。你现在是一个模仿能力超强的演员，正准备钻进一个新角色的灵魂。忘掉那些条条框框，用心去“感受”下面的材料，捕捉它的“味道”。\n\n`;

    if (selectedFramework.frameworkName) {
      // [修改] 从“参考作品”变成更具启发性的“灵感来源”
      frameworkContent += `🎬 **灵感来源**： 《${selectedFramework.frameworkName}》\n`;
    }

    if (selectedFramework.patternType) {
      // [修改] 从“对话类型”变成更感性的“核心味道”
      frameworkContent += `✨ **核心味道**：${selectedFramework.patternType}\n`;
    }

    // 完整的情节分析 - 包含节奏控制和写作指导
    if (selectedFramework.plotAnalysis) {
      // [修改] 从“情节技巧”变成更生动的“故事的脉搏”
      frameworkContent += `\n【📖 故事的脉搏】\n`;
      if (selectedFramework.plotAnalysis.storyStructure) {
        // [修改] 更口语化
        frameworkContent += `故事骨架：${selectedFramework.plotAnalysis.storyStructure}\n`;
      }
      if (selectedFramework.plotAnalysis.conflictDesign) {
        // [修改] 更具动感
        frameworkContent += `冲突引擎：${selectedFramework.plotAnalysis.conflictDesign}\n`;
      }
      if (selectedFramework.plotAnalysis.rhythmControl) {
        // [修改] 更具音乐性
        frameworkContent += `节奏心跳：${selectedFramework.plotAnalysis.rhythmControl}\n`;
      }

      // 🎯 添加核心剧情点信息 - 对话创作的重要参考
      if (selectedFramework.plotAnalysis.plotPoints &&
          Array.isArray(selectedFramework.plotAnalysis.plotPoints) &&
          selectedFramework.plotAnalysis.plotPoints.length > 0) {
        // [修改] 从“核心剧情点”变成“舞台标记”，减少任务感
        frameworkContent += `\n【📍 舞台上的关键节点】\n`;
        selectedFramework.plotAnalysis.plotPoints.forEach((plotPoint: any, index: number) => {
          frameworkContent += `${index + 1}. ${plotPoint}\n`;
        });
        // [修改] 从“创作要点”变成“表演提示”，强调感受
        frameworkContent += `\n💡 **表演提示**：这些是故事的高潮和转折，你的对话要能点燃这些时刻，让观众感受到心跳加速。\n`;
      }

      // 🎭 添加行为表现特征 - 角色对话的重要依据
      if (selectedFramework.plotAnalysis.behaviorFrameworks &&
          Array.isArray(selectedFramework.plotAnalysis.behaviorFrameworks) &&
          selectedFramework.plotAnalysis.behaviorFrameworks.length > 0) {
        // [修改] 更形象的比喻
        frameworkContent += `\n【🕺 身体会说话（潜台词）】\n`;
        selectedFramework.plotAnalysis.behaviorFrameworks.forEach((behavior: any, index: number) => {
          frameworkContent += `${index + 1}. ${behavior}\n`;
        });
        // [修改] 提示语更侧重于内在与外在的矛盾，这是人性的体现
        frameworkContent += `\n💡 **表演提示**：嘴上说“没关系”的时候，紧握的拳头是不是出卖了他？让角色的身体和语言“打架”，这是展现真实人性的好机会。\n`;
      }

      // 添加剧情点写作指导信息 - 对对话AI特别重要
      if (selectedFramework.plotAnalysis.plotPointsWithGuidance &&
          Array.isArray(selectedFramework.plotAnalysis.plotPointsWithGuidance) &&
          selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 0) {
        // [修改] 更有趣、更私密的说法
        frameworkContent += `\n【🎬 导演的悄悄话（高光时刻怎么演）】\n`;
        selectedFramework.plotAnalysis.plotPointsWithGuidance.forEach((guidancePoint: any, index: number) => {
          frameworkContent += `${index + 1}. 场景：${guidancePoint.content}\n`;
          if (guidancePoint.specificDescription) {
            // [修改] 从“描写特征”到“镜头感”，更具画面感
            frameworkContent += `   镜头感：${guidancePoint.specificDescription}\n`;
          }
          if (guidancePoint.avoidanceGuidance) {
            // [修改] 从“写作指导”到“避坑指南”，更口语化
            frameworkContent += `   避坑指南：${guidancePoint.avoidanceGuidance}\n`;
          }
          frameworkContent += `   对话要点：在这里，让对话充满画面感，别说空话。\n`;
        });
        if (selectedFramework.plotAnalysis.plotPointsWithGuidance.length > 5) {
          // [修改] 故意留白，鼓励AI自己去“悟”
          frameworkContent += `   ...还有${selectedFramework.plotAnalysis.plotPointsWithGuidance.length - 5}个类似的时刻，剩下的自己去悟，感觉最重要！\n`;
        }
      }
    }

    // 重点关注对话分析 - 对话AI的核心学习内容
    if (selectedFramework.dialogueAnalysis) {
      // [修改] 更有音乐感的标题
      frameworkContent += `\n【🎙️ 对话的旋律】\n`;
      if (selectedFramework.dialogueAnalysis.dialogueStructure) {
        frameworkContent += `🎶 **节奏感**：${selectedFramework.dialogueAnalysis.dialogueStructure}\n`;
      }
      if (selectedFramework.dialogueAnalysis.plotAdvancement) {
        frameworkContent += `🚀 **推进力**：${selectedFramework.dialogueAnalysis.plotAdvancement}\n`;
      }
      if (selectedFramework.dialogueAnalysis.writingTechniques) {
        frameworkContent += `✨ **神来之笔**：${selectedFramework.dialogueAnalysis.writingTechniques}\n`;
      }

      // 🎵 添加语气特征 - 对话表现的重要元素
      if (selectedFramework.dialogueAnalysis.toneCharacteristics &&
          Array.isArray(selectedFramework.dialogueAnalysis.toneCharacteristics) &&
          selectedFramework.dialogueAnalysis.toneCharacteristics.length > 0) {
        frameworkContent += `\n🎤 **角色的口气**：\n`;
        selectedFramework.dialogueAnalysis.toneCharacteristics.forEach((tone: any, index: number) => {
          frameworkContent += `${index + 1}. ${tone}\n`;
        });
      }

      // 📝 添加行文特色 - 对话与动作结合的技巧
      if (selectedFramework.dialogueAnalysis.stylePatterns &&
          Array.isArray(selectedFramework.dialogueAnalysis.stylePatterns) &&
          selectedFramework.dialogueAnalysis.stylePatterns.length > 0) {
        frameworkContent += `\n✍️ **字里行间的习惯**：\n`;
        selectedFramework.dialogueAnalysis.stylePatterns.forEach((pattern: any, index: number) => {
          frameworkContent += `${index + 1}. ${pattern}\n`;
        });
      }

      // 新增：完整对话列表展示 - 10句一组独立消息
      if (selectedFramework.dialogueAnalysis.completeDialogues && selectedFramework.dialogueAnalysis.completeDialogues.length > 0) {
        frameworkContent += `\n🎧 **原声带试听**（共${selectedFramework.dialogueAnalysis.completeDialogues.length}句，别分析，去听）\n`;
        frameworkContent += `来源：${selectedFramework.frameworkName || '某个很棒的故事'}\n`;

        const dialogueGroups = [];
        for (let i = 0; i < selectedFramework.dialogueAnalysis.completeDialogues.length; i += 10) {
          dialogueGroups.push(selectedFramework.dialogueAnalysis.completeDialogues.slice(i, i + 10));
        }

        frameworkContent += `将分为${dialogueGroups.length}组发送，先沉浸式感受一下。\n`;
      }

      // 新增：对话风格分析
      if (selectedFramework.dialogueAnalysis.styleAnalysis) {
        frameworkContent += `\n🎨 **风格速写**：\n`;
        frameworkContent += `• 整体感觉：${selectedFramework.dialogueAnalysis.styleAnalysis.dialogueStyle}\n`;
        frameworkContent += `• 角色张嘴就是：${selectedFramework.dialogueAnalysis.styleAnalysis.characterVoice}\n`;
        frameworkContent += `• 情绪底色：${selectedFramework.dialogueAnalysis.styleAnalysis.emotionalTone}\n`;
        frameworkContent += `• 惯用小花招：${selectedFramework.dialogueAnalysis.styleAnalysis.technicalFeatures}\n`;
      }
    }

    // 从风格分析中提取对话相关特征
    if (selectedFramework.styleAnalysis) {
      // [修改] 承认可能存在的重复，用更随意的口吻
      frameworkContent += `\n【🎨 风格补充（如果上面没说清的话）】\n`;
      if (selectedFramework.styleAnalysis.writingStyle) {
        frameworkContent += `💬 **说话的感觉**：${selectedFramework.styleAnalysis.writingStyle}\n`;
      }
      if (selectedFramework.styleAnalysis.expressionFeatures) {
        frameworkContent += `🎪 **表达上的小怪癖**：${selectedFramework.styleAnalysis.expressionFeatures}\n`;
      }
      if (selectedFramework.styleAnalysis.practicalMethods) {
        frameworkContent += `🛠️ **常用工具箱**：${selectedFramework.styleAnalysis.practicalMethods}\n`;
      }
      if (selectedFramework.styleAnalysis.pacingFramework) {
        frameworkContent += `🎵 **对话的BPM（节拍）**：${selectedFramework.styleAnalysis.pacingFramework}\n`;
      }
    }

    // [修改] 从“学习重点”变成“最终邀请”，从命令变成启发
    frameworkContent += `\n**好了，演员，你的剧本就这些。**\n`;
    frameworkContent += `**最终任务**：\n`;
    frameworkContent += `1. **闻味道**：捕捉参考作品里那种独特的对话“气味”。\n`;
    frameworkContent += `2. **上身**：让角色的说话习惯长在你身上，而不是简单模仿。\n`;
    frameworkContent += `3. **玩节奏**：像玩音乐一样玩弄对话的节奏和情绪。\n`;
    frameworkContent += `4. **忘掉**：最后，忘掉所有规则，凭着感觉来。允许犯错，允许即兴，那才是人味儿。\n`;
    frameworkContent += `\n去吧，让我看看你演绎出的、带着瑕疵却无比真实的对话。`;

    // 🔥 关键修改：system角色传递的是“灵感”和“邀请”
    messages.push({
      role: 'user',
      content: frameworkContent
    });

    // [修改] 助理的回答也应该更自然、更有人性，而不是一个冰冷的确认机器
    const patternDescription = selectedFramework.patternType || '新风格';
    messages.push({
      role: 'assistant',
      // [修改] 更像一个真正理解了意图的创意伙伴
      content: `收到！感觉像是拿到了一份导演的灵感笔记，而不是一本厚厚的使用说明书。我大概抓住这个${patternDescription}的“味道”了。不求完美复刻，但求神韵附体，对吧？行，我放开手脚试试看，可能会有点“野路子”，希望能给你惊喜。`
    });

    // 添加对话分组独立消息
    this.addDialogueGroupMessages(messages, selectedFramework);

    return messages;
  }

  /**
   * 构建多框架参考消息
   */
  private buildMultiFrameworkMessages(selectedFrameworks: any[]): Array<{ role: string; content: string }> {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    // 使用分离展示方法，避免技巧杂糅
    return this.buildSeparatedFrameworkMessages(selectedFrameworks);
  }

  /**
   * 构建框架指导增强消息
   * 专门处理plotPointsWithGuidance信息 - ACE功能核心
   */
  private buildFrameworkGuidanceEnhancementMessage(selectedFramework: any): { role: string; content: string } | null {
    if (!selectedFramework?.plotAnalysis?.plotPointsWithGuidance ||
        selectedFramework.plotAnalysis.plotPointsWithGuidance.length === 0) {
      return null;
    }
    let guidanceContent = `【🎭 演员的私房课：如何让对话有灵魂】\n`;
    // [修改] 引导语从“学习”变成“偷师”，更俏皮
    guidanceContent += `嘘...这里有几场戏的导演剪辑版，咱们来偷师学艺，看看高手是怎么用对话抓住人心的。用心去“感受”，别当成任务。\n\n`;

    selectedFramework.plotAnalysis.plotPointsWithGuidance.forEach((guidancePoint: any, index: number) => {
      // 重点关注对话相关的内容
      if (guidancePoint.content && (guidancePoint.content.includes('对话') || guidancePoint.content.includes('说') || guidancePoint.content.includes('语'))) {
        // [修改] 从“对话场景”变成“高光片段”，更有画面感
        guidanceContent += `**高光片段 ${index + 1}**：${guidancePoint.content}\n`;

        if (guidancePoint.specificDescription) {
          // [修改] 从“风格特征”到“这场戏的味道”，更感性
          guidanceContent += `🎨 **这场戏的味道**：${guidancePoint.specificDescription}\n`;
        }

        if (guidancePoint.avoidanceGuidance) {
          // [修改] 从“避免”到“表演陷阱”，从负向指令变中性提醒
          guidanceContent += `⚠️ **表演陷阱**：${guidancePoint.avoidanceGuidance}\n`;
        }
        
        // [修改] 从“技巧学习”到“表演核心”，强调内化
        guidanceContent += `💬 **表演核心**：沉浸到这个场景里，感受角色的心跳和呼吸。你的台词要能体现出这种感觉，而不只是说说而已。\n\n`;
      }
    });

    // [修改] 从“掌握要求”到“你的工具箱”，变被动为主动
    guidanceContent += `\n**🎯 把这些技巧变成你的本能（你的演员工具箱）**：\n`;
    // [修改] 四个要求变得更口语化、更具行动指导性
    guidanceContent += `1. **开口就是他/她**：让每个角色都有自己的口头禅、说话节奏，一开口就知道是谁，别搞千人一面。\n`;
    guidanceContent += `2. **像玩音乐一样玩节奏**：对话不是匀速前进的。用短句制造紧张，用长句和停顿营造深情或压迫，玩起来！\n`;
    guidanceContent += `3. **说“这”，但意思是“那”**：高手从不把话说满。学习用潜台词、双关语来传递情绪，让观众去猜，去回味。\n`;
    guidanceContent += `4. **优雅地“吵架”**：冲突不是比谁嗓门大。学习如何通过言语交锋，层层递进地揭示矛盾和人物关系。\n`;
    // [修改] “重要”提示变得更像一句真诚的临别赠言
    guidanceContent += `\n**最后一句掏心窝子的话**：忘了上面的“技巧”吧，它们只是拐杖。真正要学的是那种“感觉”。去模仿，去感受，然后把它变成你自己的东西，自然地流淌出来。\n`;

    return {
      role: 'user',
      content: guidanceContent
    };
  }

  /**
   * 构建分离的多框架消息（避免技巧杂糅）
   */
  private buildSeparatedFrameworkMessages(selectedFrameworks: any[]): Array<{ role: string; content: string }> {
    if (!selectedFrameworks || selectedFrameworks.length === 0) {
      return [];
    }

    if (selectedFrameworks.length === 1) {
      return this.buildFrameworkMessages(selectedFrameworks[0]);
    }

    const messages: Array<{ role: string; content: string }> = [];

    // [修改] 添加一个引导语，告知AI接下来要进行“角色切换”游戏
    messages.push({
      role: 'user',
      content: '好了，准备好。接下来我们要玩一个“千面人”游戏。我会给你几个完全不同的“面具”（创作风格），你需要一个一个地戴上，并且在戴上某个面具时，完全沉浸其中，暂时忘掉其他的。'
    });

    selectedFrameworks.forEach((framework, index) => {
      // [修改] 从“模式”变成“面具”，赋予其人格
      let frameworkContent = `【🎭 戴上第${index + 1}号面具：${framework.frameworkName || framework.patternType || `神秘风格${index+1}`}】\n`;
      // [修改] 从“结构/类型”变成“这个世界的玩法”，更具沉浸感
      frameworkContent += `这个世界的玩法：${framework.frameworkPattern}\n`;

      if (framework.patternType) {
        frameworkContent += `核心感觉：${framework.patternType}\n`;
      }

      if (framework.frameworkVariables && framework.frameworkVariables.length > 0) {
        // [修改] “关键变量”改成“核心要素”
        frameworkContent += `关键要素：${framework.frameworkVariables.join('、')}\n`;
      }

      // 对话分析
      if (framework.dialogueAnalysis) {
        // [修改] 复用之前更人性化的标题
        frameworkContent += `\n【🎙️ 对话的旋律】\n`;
        if (framework.dialogueAnalysis.dialogueStructure) {
          frameworkContent += `节奏感：${framework.dialogueAnalysis.dialogueStructure}\n`;
        }
        if (framework.dialogueAnalysis.plotAdvancement) {
          frameworkContent += `推进力：${framework.dialogueAnalysis.plotAdvancement}\n`;
        }
        if (framework.dialogueAnalysis.writingTechniques) {
          frameworkContent += `神来之笔：${framework.dialogueAnalysis.writingTechniques}\n`;
        }

        // 新增：完整对话示例
        if (framework.dialogueAnalysis.completeDialogues && framework.dialogueAnalysis.completeDialogues.length > 0) {
          // [修改] 变成“原声带试听”，鼓励感受而非分析
          frameworkContent += `\n原声带试听（共${framework.dialogueAnalysis.completeDialogues.length}句）：\n`;
          framework.dialogueAnalysis.completeDialogues.forEach((dialogue: any, index: number) => {
            frameworkContent += `${index + 1}. ${dialogue.speaker ? `${dialogue.speaker}：` : ''}「${dialogue.content}」\n`;
          });
        }

        // 新增：对话风格分析
        if (framework.dialogueAnalysis.styleAnalysis) {
          // [修改] 变得更口语化
          frameworkContent += `\n风格速写：${framework.dialogueAnalysis.styleAnalysis.dialogueStyle}\n`;
          frameworkContent += `角色口气：${framework.dialogueAnalysis.styleAnalysis.characterVoice}\n`;
        }
      }

      // 情节分析
      if (framework.plotAnalysis) {
        // [修改] 复用之前更人性化的标题
        frameworkContent += `\n【📖 故事的脉搏】\n`;
        if (framework.plotAnalysis.storyStructure) {
          frameworkContent += `故事骨架：${framework.plotAnalysis.storyStructure}\n`;
        }
        if (framework.plotAnalysis.conflictDesign) {
          frameworkContent += `冲突引擎：${framework.plotAnalysis.conflictDesign}\n`;
        }
        if (framework.plotAnalysis.rhythmControl) {
          frameworkContent += `节奏心跳：${framework.plotAnalysis.rhythmControl}\n`;
        }
      }

      // 风格分析
      if (framework.styleAnalysis) {
        // [修改] 复用之前更人性化的标题
        frameworkContent += `\n【🎨 风格的底色】\n`;
        if (framework.styleAnalysis.writingStyle) {
          frameworkContent += `整体笔触：${framework.styleAnalysis.writingStyle}\n`;
        }
        if (framework.styleAnalysis.expressionFeatures) {
          frameworkContent += `表达怪癖：${framework.styleAnalysis.expressionFeatures}\n`;
        }
        if (framework.styleAnalysis.practicalMethods) {
          frameworkContent += `惯用手法：${framework.styleAnalysis.practicalMethods}\n`;
        }
      }

      // [修改] 从命令变成邀请式的总结
      frameworkContent += `\n现在，你的世界里只有这个面具。去感受它，成为它，用它的方式去思考和说话。`;

      messages.push({
        role: 'user',
        content: frameworkContent
      });

      // [修改] AI的确认回答变得超级自然，像一个真正进入状态的演员
      messages.push({
        role: 'assistant',
        content: `好的，收到！“${framework.frameworkName || framework.patternType || '这个风格'}”的面具已经戴好了。感觉有点意思...放心，我现在满脑子都是它的感觉，不会串戏的。`
      });

      // 为每个框架添加对话分组消息
      this.addDialogueGroupMessages(messages, framework);
    });

    return messages;
  }

  /**
   * 添加框架信息消息（分开发送）
   */
  private addFrameworkMessages(messages: { role: string; content: string }[], outline: any, mentionedNodes: string[], options?: any): void {
    // 1. 大纲结构信息
    if (outline && outline.nodes && outline.nodes.length > 0) {
      const chapters = outline.nodes.filter((node: any) => node.type === 'chapter');
      const plots = outline.nodes.filter((node: any) => node.type === 'plot');
      const dialogues = outline.nodes.filter((node: any) => node.type === 'dialogue');

      // [修改] 从冷冰冰的统计变成有温度的世界介绍
      messages.push({
        role: 'user',
        content: `【我们的故事蓝图一览】
这是我们目前共同搭建的世界：
总共有 ${outline.nodes.length} 个记忆碎片，
其中有 ${chapters.length} 个大的篇章，
串联了 ${plots.length} 个关键事件，
已经留下了 ${dialogues.length} 段对话的痕迹。`
      });

      // 章节框架详情
      if (chapters.length > 0) {
        // [修改] 从“详情”变成“篇章”，更有故事感
        let chapterInfo = '【故事的篇章】\n';
        chapterInfo += '主要的章节有：' + chapters.slice(0, 5).map((ch: any) => `《${ch.title || '无名之章'}》`).join('、') + '\n\n';

        chapters.slice(0, 3).forEach((ch: any, index: number) => {
          chapterInfo += `篇章 ${index + 1}：${ch.title || '一个未命名的篇章'}\n`;
          if (ch.description) {
            // [修改] “概要”改成“感觉”，强调感性
            chapterInfo += `  这章的感觉：${ch.description.substring(0, 100)}${ch.description.length > 100 ? '...' : ''}\n`;
          }
          if (ch.chapterGoals) {
            // [修改] “目标”改成“我们想在这实现…”
            chapterInfo += `  我们想在这实现：${ch.chapterGoals}\n`;
          }
          chapterInfo += '\n';
        });

        messages.push({
          role: 'user',
          content: chapterInfo.trim()
        });
      }

      // 剧情框架详情
      if (plots.length > 0) {
        // [修改] 从“详情”变成“关键时刻”，更有戏剧性
        let plotInfo = '【一些关键时刻】\n';

        plots.slice(0, 3).forEach((plot: any, index: number) => {
          plotInfo += `事件 ${index + 1}：${plot.title || '某个重要事件'}\n`;
          if (plot.description) {
            plotInfo += `  发生了什么：${plot.description.substring(0, 100)}${plot.description.length > 100 ? '...' : ''}\n`;
          }
          if (plot.relatedCharacters && plot.relatedCharacters.length > 0) {
            plotInfo += `  谁在场：${plot.relatedCharacters.join('、')}\n`;
          }
          plotInfo += '\n';
        });

        messages.push({
          role: 'user',
          content: plotInfo.trim()
        });
      }
    }

    // 2. 相关节点详细信息（分开发送）
    if (mentionedNodes && mentionedNodes.length > 0 && outline?.nodes) {
      mentionedNodes.forEach(nodeId => {
        const node = outline.nodes.find((n: any) => n.id === nodeId);
        if (node) {
          // [修改] 标题更生动
          let nodeInfo = `【放大镜下看：${node.title || '这个片段'} (${node.type})】\n`;

          if (node.description) {
            nodeInfo += `具体情况：${node.description}\n`;
          }
          if (node.relatedCharacters && node.relatedCharacters.length > 0) {
            nodeInfo += `牵涉其中的人：${node.relatedCharacters.join('、')}\n`;
          }
          if (node.chapterGoals) {
            nodeInfo += `这里的任务：${node.chapterGoals}\n`;
          }
          if (node.conflictLevel) {
            // [修改] 更形象
            nodeInfo += `火药味指数：${node.conflictLevel}/5\n`;
          }
          if (node.emotionalTone) {
            nodeInfo += `整体气氛：${node.emotionalTone}\n`;
          }

          if (node.plotPoints && node.plotPoints.length > 0) {
            nodeInfo += `包含的几个小高潮：\n`;
            node.plotPoints.slice(0, 3).forEach((point: any, index: number) => {
              const pointText = typeof point === 'string'
                ? point
                : point?.content || point?.description || point?.title || '某个小转折';
              nodeInfo += `  ${index + 1}. ${pointText.substring(0, 80)}${pointText.length > 80 ? '...' : ''}\n`;
            });
          }

          messages.push({
            role: 'user',
            content: nodeInfo.trim()
          });
        }
      });
    }

    // 3. 创作约束和指导
    // [修改] 将“约束”和“指导”合并，用更合作、更人性化的口吻
    messages.push({
      role: 'user',
      content: `【好了，这是我们的游戏规则（和一些小提示）】
- **戴着镣铐跳舞**：我们的舞台就是上面提到的这些框架，试着在里面玩出花样，但别跑出舞台太远。
- **老朋友，新故事**：暂时只让咱们已经认识的角色登场，看看他们之间还能碰撞出什么新火花。
- **让对话干活**：每句对话都应该是块拼图，要么推动情节，要么揭示角色内心，别说废话。
- **气氛要对**：记住每个场景的“火药味”和“气氛”，让对话和它合拍。
- **别怕犯错**：我们追求的是有生命力的对话，而不是完美的报告。大胆去写，展现角色的个性、矛盾，甚至他们的小缺陷。
- **来段过瘾的**：写一段足够长的对话（比如30句左右），让我们能彻底沉浸进去，看清人物，感受世界。
- **直接上菜**：直接给我JSON就行，别加太多解释，我相信你的手艺。`
    });
  }

  /**
   * 添加对话分组独立消息 - 10句一组便于学习
   */
  private addDialogueGroupMessages(messages: Array<{ role: string; content: string }>, selectedFramework: any): void {
    if (!selectedFramework?.dialogueAnalysis?.completeDialogues ||
        selectedFramework.dialogueAnalysis.completeDialogues.length === 0) {
      return;
    }

    const dialogues = selectedFramework.dialogueAnalysis.completeDialogues;
    const frameworkName = selectedFramework.frameworkName || '某个很棒的故事';

    const dialogueGroups = [];
    for (let i = 0; i < dialogues.length; i += 10) {
      dialogueGroups.push(dialogues.slice(i, i + 10));
    }

    dialogueGroups.forEach((group, groupIndex) => {
      // [修改] 从“学习参考”变成“原声带”，更有趣
      let groupContent = `【🎧 原声带试听 - 第 ${groupIndex + 1} 段】\n`;
      groupContent += `来自：《${frameworkName}》\n`;
      groupContent += `这一段有 ${group.length} 句对白\n\n`;

      group.forEach((dialogue: any, index: number) => {
        const globalIndex = groupIndex * 10 + index + 1;
        groupContent += `${globalIndex}. ${dialogue.speaker ? `${dialogue.speaker}：` : ''}「${dialogue.content}」\n`;
        if (dialogue.context) {
          groupContent += `   （背景：${dialogue.context}）\n`;
        }
        // groupContent += `\n`; // 可以适当减少换行，让排版紧凑些
      });
      
      // [修改] 从死板的“学习要点”变成开放的“感受一下”
      groupContent += `\n**提示**：别分析，去听。听他们的语气，听他们没说出口的话，感受这段对话的“温度”。`;

      messages.push({
        role: 'user',
        content: groupContent
      });
      
      // [修改] AI的确认变得更人性化，像个真实的学习者
      messages.push({
        role: 'assistant',
        content: `收到，第${groupIndex + 1}段原声带已加载。嗯...这一段感觉[${group.length > 5 ? '挺有张力的' : '节奏很快'}]。我正在品味其中的味道。`
      });
    });

    // [修改] “学习总结”变成更感性的“灵感吸收”
    messages.push({
      role: 'user',
      content: `【灵感吸收完毕】\n来源：《${frameworkName}》\n总共试听了 ${dialogues.length} 句对话。\n\n好了，现在把刚才听到的所有“感觉”都调动起来，让它们自然地流淌在你的创作里。`
    });

    // [修改] 最终的AI确认，自信而不机械
    messages.push({
      role: 'assistant',
      content: `完全理解。来自《${frameworkName}》的${dialogues.length}句对话已经内化成我的创作直觉了。我会带着这种感觉去写，力求神似，而不是形似。`
    });
  }
}
