/**
 * 上下文链路选择器组件
 * 允许用户选择要包含的上下文链路类型
 */

import React, { useState, useEffect, useCallback } from 'react';
import { ContextChain, ContextChainService, ContextOptions } from './ContextChainService';

interface ContextChainSelectorProps {
  nodeId: string;
  outline: any;
  onChainsSelected: (chains: ContextChain[]) => void;
  onClose: () => void;
}

interface ChainOptionCardProps {
  chain: ContextChain;
  isSelected: boolean;
  onToggle: () => void;
}

const ChainOptionCard: React.FC<ChainOptionCardProps> = ({ chain, isSelected, onToggle }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const getChainTheme = () => {
    switch (chain.type) {
      case 'hierarchy':
        return {
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          selectedBg: 'bg-blue-100'
        };
      case 'sequence':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          selectedBg: 'bg-green-100'
        };
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          selectedBg: 'bg-gray-100'
        };
    }
  };

  const theme = getChainTheme();

  return (
    <div 
      className={`chain-option-card border-2 rounded-lg p-4 transition-all duration-200 cursor-pointer
        ${isSelected ? `${theme.selectedBg} ${theme.borderColor}` : `bg-white border-gray-200 hover:${theme.bgColor}`}
        ${isHovered ? 'shadow-md transform scale-[1.02]' : 'shadow-sm'}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onToggle}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{chain.icon}</span>
          <div>
            <h4 className={`font-semibold ${theme.color}`}>{chain.title}</h4>
            <p className="text-sm text-gray-600">{chain.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-700">
              {chain.nodes.length} 个节点
            </div>
            <div className="text-xs text-gray-500">
              优先级 {chain.priority}
            </div>
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onToggle}
              className={`w-5 h-5 rounded border-2 ${theme.borderColor} ${theme.color}`}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      </div>
      
      {isSelected && (
        <div className="mt-4 border-t pt-4">
          <button 
            className={`text-sm ${theme.color} hover:underline flex items-center space-x-1`}
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
          >
            <span>{isExpanded ? '收起' : '预览'} 节点列表</span>
            <span className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
              ▼
            </span>
          </button>
          
          {isExpanded && (
            <div className="mt-3 space-y-2 max-h-40 overflow-y-auto">
              {chain.nodes.map((node, index) => (
                <div key={node.id} className="flex items-center space-x-2 text-sm">
                  <span className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </span>
                  <span className="text-xs px-2 py-1 bg-gray-100 rounded text-gray-600">
                    {node.type}
                  </span>
                  <span className="flex-1 truncate">{node.title}</span>
                  <span className="text-xs text-gray-500">L{node.level}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export const ContextChainSelector: React.FC<ContextChainSelectorProps> = ({
  nodeId,
  outline,
  onChainsSelected,
  onClose
}) => {
  const [availableChains, setAvailableChains] = useState<ContextChain[]>([]);
  const [selectedChains, setSelectedChains] = useState<Set<string>>(new Set(['hierarchy']));
  const [isLoading, setIsLoading] = useState(false);
  const [contextService] = useState(() => new ContextChainService(outline));

  // 加载可用的链路
  const loadAvailableChains = useCallback(async () => {
    setIsLoading(true);
    try {
      console.log('🔍 加载节点的上下文链路:', nodeId);
      
      const options: ContextOptions = {
        includeHierarchy: true,
        includeSequence: true,
        maxNodesPerChain: 10,
        priorityThreshold: 0.5
      };

      const chains = await contextService.getNodeContextChains(nodeId, options);
      setAvailableChains(chains);
      
      // 自动选择高优先级链路
      const autoSelected = new Set(
        chains.filter(chain => chain.priority <= 2).map(chain => chain.type)
      );
      setSelectedChains(autoSelected);
      
      console.log('✅ 链路加载完成:', chains.length);
    } catch (error) {
      console.error('❌ 加载上下文链路失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [nodeId, contextService]);

  useEffect(() => {
    loadAvailableChains();
  }, [loadAvailableChains]);

  // 处理链路选择切换
  const handleChainToggle = useCallback((chainType: string) => {
    const newSelected = new Set(selectedChains);
    if (newSelected.has(chainType)) {
      newSelected.delete(chainType);
    } else {
      newSelected.add(chainType);
    }
    setSelectedChains(newSelected);
    
    // 通知父组件
    const selectedChainObjects = availableChains.filter(chain => 
      newSelected.has(chain.type)
    );
    onChainsSelected(selectedChainObjects);
  }, [selectedChains, availableChains, onChainsSelected]);

  // 确认选择
  const handleConfirm = useCallback(() => {
    const selectedChainObjects = availableChains.filter(chain => 
      selectedChains.has(chain.type)
    );
    onChainsSelected(selectedChainObjects);
    onClose();
  }, [selectedChains, availableChains, onChainsSelected, onClose]);

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'h' || e.key === 'H') {
        handleChainToggle('hierarchy');
      } else if (e.key === 's' || e.key === 'S') {
        handleChainToggle('sequence');
      } else if (e.key === 'Enter') {
        handleConfirm();
      } else if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleChainToggle, handleConfirm, onClose]);

  const totalSelectedNodes = availableChains
    .filter(chain => selectedChains.has(chain.type))
    .reduce((sum, chain) => sum + chain.nodes.length, 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>📎</span>
                <span>选择上下文链路</span>
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                选择要包含的相关节点，帮助AI更好地理解上下文
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-96">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">分析节点关联中...</span>
            </div>
          ) : (
            <div className="space-y-4">
              {availableChains.map(chain => (
                <ChainOptionCard
                  key={chain.type}
                  chain={chain}
                  isSelected={selectedChains.has(chain.type)}
                  onToggle={() => handleChainToggle(chain.type)}
                />
              ))}
              
              {availableChains.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <span className="text-4xl mb-2 block">🔍</span>
                  <p>未找到可用的上下文链路</p>
                  <p className="text-sm">该节点可能是独立节点</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="p-6 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              已选择 <span className="font-semibold text-blue-600">{selectedChains.size}</span> 个链路，
              共 <span className="font-semibold text-blue-600">{totalSelectedNodes}</span> 个节点
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleConfirm}
                disabled={selectedChains.size === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
                  disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200
                  hover:shadow-md active:scale-95"
              >
                确认选择
              </button>
            </div>
          </div>
          
          <div className="mt-3 text-xs text-gray-500">
            快捷键：H-层级链路 | S-序列链路 | Enter-确认 | Esc-取消
          </div>
        </div>
      </div>
    </div>
  );
};
