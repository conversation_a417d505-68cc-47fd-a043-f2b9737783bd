"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface FullTextAnnotationButtonProps {
  bookId: string;
  chapterContent?: string;
  onAnnotationStart?: () => void;
  onAnnotationComplete?: (result: any) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
}

/**
 * 全文标注按钮组件
 * 用于启动AI全文标注功能
 */
const FullTextAnnotationButton: React.FC<FullTextAnnotationButtonProps> = ({
  bookId,
  chapterContent,
  onAnnotationStart,
  onAnnotationComplete,
  variant = 'default',
  className = '',
  disabled = false
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleClick = async () => {
    if (disabled || isProcessing || !chapterContent) {
      return;
    }

    try {
      setIsProcessing(true);
      onAnnotationStart?.();

      // TODO: 这里将调用全文标注服务
      console.log('开始全文标注:', { bookId, contentLength: chapterContent.length });
      
      // 模拟处理过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // TODO: 实际的标注逻辑
      const mockResult = {
        success: true,
        segmentsCount: Math.ceil(chapterContent.length / 400),
        suggestionsCount: Math.floor(chapterContent.length / 100)
      };

      onAnnotationComplete?.(mockResult);

    } catch (error) {
      console.error('全文标注失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 根据variant渲染不同样式的按钮
  const renderButton = () => {
    const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";
    
    switch (variant) {
      case 'compact':
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing || !chapterContent}
            className={`${baseClasses} px-3 py-1 text-xs bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.02 }}
            whileTap={{ scale: disabled ? 1 : 0.98 }}
          >
            {isProcessing ? (
              <>
                <motion.div
                  className="w-3 h-3 mr-1 border border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                处理中
              </>
            ) : (
              <>
                <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                全文标注
              </>
            )}
          </motion.button>
        );

      case 'icon-only':
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing || !chapterContent}
            className={`${baseClasses} p-2 bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.05 }}
            whileTap={{ scale: disabled ? 1 : 0.95 }}
            title="全文标注"
          >
            {isProcessing ? (
              <motion.div
                className="w-4 h-4 border border-white border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            ) : (
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            )}
          </motion.button>
        );

      default:
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing || !chapterContent}
            className={`${baseClasses} px-4 py-2 text-sm bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-md ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.02 }}
            whileTap={{ scale: disabled ? 1 : 0.98 }}
          >
            {isProcessing ? (
              <>
                <motion.div
                  className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                正在标注...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                全文标注
              </>
            )}
          </motion.button>
        );
    }
  };

  // 如果没有章节内容，显示禁用状态
  if (!chapterContent) {
    return (
      <div className={`inline-flex items-center text-xs text-gray-400 ${className}`}>
        <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        {variant === 'icon-only' ? '' : '需要章节内容'}
      </div>
    );
  }

  return renderButton();
};

export default FullTextAnnotationButton;
