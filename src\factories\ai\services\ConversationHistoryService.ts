"use client";

import { ConversationMessage, RewriteContentParams } from './AIRewriteService';
import { PromptBuilderServiceInterface, createPromptBuilderService } from './PromptBuilderService';

/**
 * 对话历史服务接口
 */
export interface ConversationHistoryServiceInterface {
  /**
   * 过滤预设消息
   * @param messages 消息列表
   * @returns 过滤后的消息列表
   */
  filterPresetMessages(messages: ConversationMessage[]): ConversationMessage[];

  /**
   * 构建更新后的对话历史
   * @param params 改写参数
   * @param rewrittenContent 改写后的内容
   * @returns 更新后的对话历史
   */
  buildUpdatedHistory(
    params: RewriteContentParams,
    rewrittenContent: string
  ): ConversationMessage[];
}

/**
 * 对话历史服务实现
 */
export class ConversationHistoryService implements ConversationHistoryServiceInterface {
  private promptBuilderService: PromptBuilderServiceInterface;
  
  constructor(promptBuilderService?: PromptBuilderServiceInterface) {
    this.promptBuilderService = promptBuilderService || createPromptBuilderService();
  }

  /**
   * 过滤预设消息
   * @param messages 消息列表
   * @returns 过滤后的消息列表
   */
  public filterPresetMessages(messages: ConversationMessage[]): ConversationMessage[] {
    if (!messages || messages.length === 0) {
      return [];
    }

    // 预设的助手消息特征
    const presetMessages = [
      '我将查看并分析',
      '我已阅读并分析了',
      '我已记住',
      '我将参考以下',
      '确保创作内容符合',
      '确保与需要改写的内容',
      '确保改写后的内容',
      '我已记住上文的最后几句话',
      '我已记住下文的开头几句话'
    ];

    // 定义系统和用户预制消息的特征
    const systemPresetMessages = [
      '你是一位专业的文学改写专家',
      '擅长根据要求改写文本内容',
      '你的任务是根据用户提供的要求',
      '请注意以下几点'
    ];

    const userPresetMessages = [
      '我需要您改写一段文本内容',
      '请根据我提供的上下文和要求进行改写',
      '【当前模式】',
      '【改写要求】',
      '【剧情方向】'
    ];

    // 过滤掉所有预制消息（包括系统、用户和助手消息）
    const filteredMessages = messages.filter(msg => {
      // 根据消息角色使用不同的过滤列表
      if (msg.role === 'system') {
        return !systemPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'user') {
        return !userPresetMessages.some(preset => msg.content.includes(preset));
      } else if (msg.role === 'assistant') {
        return !presetMessages.some(preset => msg.content.includes(preset));
      }

      // 其他角色的消息保留
      return true;
    });

    return filteredMessages;
  }

  /**
   * 构建更新后的对话历史
   * @param params 改写参数
   * @param rewrittenContent 改写后的内容
   * @returns 更新后的对话历史
   */
  public buildUpdatedHistory(
    params: RewriteContentParams,
    rewrittenContent: string
  ): ConversationMessage[] {
    // 构建新的对话历史
    let updatedHistory: ConversationMessage[] = [];

    // 如果有现有历史且不是新对话，保留历史但过滤掉预制消息
    if (params.conversationHistory && params.conversationHistory.length > 0) {
      // 过滤掉预制的消息，只保留真正的对话部分
      const filteredHistory = this.filterPresetMessages(params.conversationHistory);

      // 检查是否有系统消息，如果没有，添加一个
      const hasSystemMessage = filteredHistory.some(msg => msg.role === 'system');
      if (!hasSystemMessage) {
        updatedHistory.push({
          role: 'system',
          content: this.promptBuilderService.getSystemPrompt(params)
        });
      }

      // 添加过滤后的历史消息
      updatedHistory = updatedHistory.concat(filteredHistory);

      // 添加用户的初始请求
      let initialUserPrompt = `我需要您改写一段文本内容，请根据我提供的上下文和要求进行改写。`;

      // 确定当前模式
      let currentMode = "书写模式";
      if (params.rewriteRequirements && params.rewriteRequirements.includes("分析")) {
        currentMode = "分析模式";
        initialUserPrompt = `我需要您分析一段文本内容，请根据我提供的上下文和要求进行分析。`;
      } else if (params.rewriteRequirements && params.rewriteRequirements.includes("续写")) {
        currentMode = "续写模式";
        initialUserPrompt = `我需要您续写一段文本内容，请根据我提供的上下文和要求进行续写。`;
      }

      // 添加模式信息
      initialUserPrompt += `\n\n【当前模式】\n${currentMode}`;

      // 添加改写要求
      if (params.rewriteRequirements) {
        initialUserPrompt += `\n\n【改写要求】\n${params.rewriteRequirements}`;
      }

      // 添加剧情方向
      if (params.plot) {
        initialUserPrompt += `\n\n【剧情方向】\n${params.plot}`;
      }

      updatedHistory.push({
        role: 'user',
        content: initialUserPrompt
      });
    }

    // 添加AI的回复到对话历史的末尾
    updatedHistory.push({
      role: 'assistant',
      content: rewrittenContent
    });

    return updatedHistory;
  }
}

// 创建对话历史服务的工厂函数
export function createConversationHistoryService(
  promptBuilderService?: PromptBuilderServiceInterface
): ConversationHistoryServiceInterface {
  return new ConversationHistoryService(promptBuilderService);
}
