"use client";

import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { OutlineAIResponseExtended } from '@/factories/ui/components/OutlineManager/assistant/OutlineAIService';

/**
 * 思考画布处理服务
 * 负责思考画布的数据处理、存储和验证
 */
export class ThinkingCanvasProcessor {
  private static instance: ThinkingCanvasProcessor;
  private readonly STORAGE_KEY = 'thinking_canvas_data';
  private readonly MAX_STORAGE_SIZE = 50; // 最多存储50个思考画布

  private constructor() {}

  public static getInstance(): ThinkingCanvasProcessor {
    if (!ThinkingCanvasProcessor.instance) {
      ThinkingCanvasProcessor.instance = new ThinkingCanvasProcessor();
    }
    return ThinkingCanvasProcessor.instance;
  }

  /**
   * 从AI响应中提取思考画布数据
   */
  public extractThinkingCanvas(response: OutlineAIResponseExtended): ThinkingCanvasData | null {
    try {
      if (!response.thinkingCanvas) {
        console.log('响应中没有思考画布数据');
        return null;
      }

      const canvas = response.thinkingCanvas;
      
      // 验证必要字段
      if (!canvas.content || !canvas.title) {
        console.warn('思考画布缺少必要字段:', canvas);
        return null;
      }

      // 确保数据完整性
      const processedCanvas: ThinkingCanvasData = {
        id: canvas.id || `thinking_${Date.now()}`,
        title: canvas.title,
        content: canvas.content,
        createdAt: canvas.createdAt || new Date().toISOString(),
        updatedAt: canvas.updatedAt || new Date().toISOString(),
        tags: Array.isArray(canvas.tags) ? canvas.tags : ['AI生成'],
        metadata: {
          wordCount: canvas.content.length,
          editHistory: [],
          isStarred: false,
          ...canvas.metadata
        }
      };

      console.log('✅ 思考画布提取成功:', processedCanvas.id);
      return processedCanvas;

    } catch (error: any) {
      console.error('❌ 思考画布提取失败:', error);
      return null;
    }
  }

  /**
   * 验证思考画布数据完整性
   */
  public validateThinkingCanvas(data: ThinkingCanvasData): boolean {
    try {
      // 检查必要字段
      if (!data.id || !data.title || !data.content) {
        console.warn('思考画布缺少必要字段');
        return false;
      }

      // 检查数据类型
      if (typeof data.title !== 'string' || typeof data.content !== 'string') {
        console.warn('思考画布字段类型错误');
        return false;
      }

      // 检查标签格式
      if (data.tags && !Array.isArray(data.tags)) {
        console.warn('思考画布标签格式错误');
        return false;
      }

      // 检查元数据
      if (data.metadata && typeof data.metadata !== 'object') {
        console.warn('思考画布元数据格式错误');
        return false;
      }

      return true;
    } catch (error: any) {
      console.error('思考画布验证失败:', error);
      return false;
    }
  }

  /**
   * 保存思考画布到本地存储
   */
  public async saveThinkingCanvas(data: ThinkingCanvasData): Promise<void> {
    try {
      if (!this.validateThinkingCanvas(data)) {
        throw new Error('思考画布数据验证失败');
      }

      // 获取现有数据
      const existingData = this.getAllThinkingCanvases();
      
      // 检查是否已存在
      const existingIndex = existingData.findIndex(item => item.id === data.id);
      
      if (existingIndex >= 0) {
        // 更新现有数据
        existingData[existingIndex] = {
          ...data,
          updatedAt: new Date().toISOString()
        };
        console.log('🔄 更新思考画布:', data.id);
      } else {
        // 添加新数据
        existingData.unshift(data); // 新数据放在前面
        console.log('➕ 新增思考画布:', data.id);
      }

      // 限制存储数量
      if (existingData.length > this.MAX_STORAGE_SIZE) {
        existingData.splice(this.MAX_STORAGE_SIZE);
        console.log('🗑️ 清理旧的思考画布数据');
      }

      // 保存到localStorage
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(existingData));
      console.log('💾 思考画布保存成功');

    } catch (error: any) {
      console.error('❌ 思考画布保存失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有思考画布
   */
  public getAllThinkingCanvases(): ThinkingCanvasData[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (!data) {
        return [];
      }

      const parsed = JSON.parse(data);
      if (!Array.isArray(parsed)) {
        console.warn('思考画布存储数据格式错误，重置为空数组');
        return [];
      }

      return parsed.filter(item => this.validateThinkingCanvas(item));
    } catch (error: any) {
      console.error('获取思考画布数据失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取思考画布
   */
  public getThinkingCanvasById(id: string): ThinkingCanvasData | null {
    const allCanvases = this.getAllThinkingCanvases();
    return allCanvases.find(canvas => canvas.id === id) || null;
  }

  /**
   * 删除思考画布
   */
  public deleteThinkingCanvas(id: string): boolean {
    try {
      const existingData = this.getAllThinkingCanvases();
      const filteredData = existingData.filter(item => item.id !== id);
      
      if (filteredData.length === existingData.length) {
        console.warn('未找到要删除的思考画布:', id);
        return false;
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredData));
      console.log('🗑️ 思考画布删除成功:', id);
      return true;
    } catch (error: any) {
      console.error('思考画布删除失败:', error);
      return false;
    }
  }

  /**
   * 搜索思考画布
   */
  public searchThinkingCanvases(query: string): ThinkingCanvasData[] {
    const allCanvases = this.getAllThinkingCanvases();
    const lowerQuery = query.toLowerCase();

    return allCanvases.filter(canvas => 
      canvas.title.toLowerCase().includes(lowerQuery) ||
      canvas.content.toLowerCase().includes(lowerQuery) ||
      canvas.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 清理过期的思考画布（超过30天）
   */
  public cleanupExpiredCanvases(): number {
    try {
      const allCanvases = this.getAllThinkingCanvases();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const validCanvases = allCanvases.filter(canvas => {
        const createdAt = new Date(canvas.createdAt);
        return createdAt > thirtyDaysAgo;
      });

      const removedCount = allCanvases.length - validCanvases.length;
      
      if (removedCount > 0) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(validCanvases));
        console.log(`🧹 清理了 ${removedCount} 个过期的思考画布`);
      }

      return removedCount;
    } catch (error: any) {
      console.error('清理过期思考画布失败:', error);
      return 0;
    }
  }

  /**
   * 导出思考画布为Markdown
   */
  public exportToMarkdown(canvas: ThinkingCanvasData): string {
    const markdown = `# ${canvas.title}

**创建时间**: ${new Date(canvas.createdAt).toLocaleString()}
**更新时间**: ${new Date(canvas.updatedAt).toLocaleString()}
**标签**: ${canvas.tags.join(', ')}

---

${canvas.content}

---

*由AI大纲助手生成*
`;

    return markdown;
  }

  /**
   * 获取存储统计信息
   */
  public getStorageStats(): {
    totalCount: number;
    totalSize: string;
    oldestDate: string;
    newestDate: string;
  } {
    const allCanvases = this.getAllThinkingCanvases();
    
    if (allCanvases.length === 0) {
      return {
        totalCount: 0,
        totalSize: '0 KB',
        oldestDate: '',
        newestDate: ''
      };
    }

    const totalSize = JSON.stringify(allCanvases).length;
    const dates = allCanvases.map(canvas => new Date(canvas.createdAt));
    const oldestDate = new Date(Math.min(...dates.map(d => d.getTime())));
    const newestDate = new Date(Math.max(...dates.map(d => d.getTime())));

    return {
      totalCount: allCanvases.length,
      totalSize: `${(totalSize / 1024).toFixed(1)} KB`,
      oldestDate: oldestDate.toLocaleDateString(),
      newestDate: newestDate.toLocaleDateString()
    };
  }
}

// 导出单例实例
export const thinkingCanvasProcessor = ThinkingCanvasProcessor.getInstance();
