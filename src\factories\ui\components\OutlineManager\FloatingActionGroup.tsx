"use client";

import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './FloatingActionGroup.css';

interface FloatingActionGroupProps {
  disabled?: boolean;
  onAssistantClick: (buttonPosition?: { x: number; y: number }) => void;
  onFrameworkExtractClick: (buttonPosition?: { x: number; y: number }) => void;
  onChapterAnalysisClick: (buttonPosition?: { x: number; y: number }) => void;
  onExampleManagementClick: () => void;
  isAssistantActive: boolean;
  isFrameworkExtractActive: boolean;
  isChapterAnalysisActive: boolean;
}

/**
 * 浮动操作按钮组
 * 主按钮点击后横向展开显示三个功能按钮
 */
const FloatingActionGroup: React.FC<FloatingActionGroupProps> = ({
  disabled = false,
  onAssistantClick,
  onFrameworkExtractClick,
  onChapterAnalysisClick,
  onExampleManagementClick,
  isAssistantActive,
  isFrameworkExtractActive,
  isChapterAnalysisActive
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const mainButtonRef = useRef<HTMLButtonElement>(null);

  // 获取按钮位置
  const getButtonPosition = useCallback(() => {
    if (mainButtonRef.current) {
      const rect = mainButtonRef.current.getBoundingClientRect();
      return {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
    }
    return undefined;
  }, []);

  // 切换展开状态
  const handleMainButtonClick = useCallback(() => {
    if (disabled) return;
    setIsExpanded(prev => !prev);
  }, [disabled]);

  // 处理子按钮点击
  const handleSubButtonClick = useCallback((
    action: 'assistant' | 'framework' | 'analysis' | 'examples'
  ) => {
    const position = getButtonPosition();

    switch (action) {
      case 'assistant':
        onAssistantClick(position);
        break;
      case 'framework':
        onFrameworkExtractClick(position);
        break;
      case 'analysis':
        onChapterAnalysisClick(position);
        break;
      case 'examples':
        onExampleManagementClick();
        break;
    }

    // 点击子按钮后收起菜单
    setIsExpanded(false);
  }, [onAssistantClick, onFrameworkExtractClick, onChapterAnalysisClick, onExampleManagementClick, getButtonPosition]);

  // 检查是否有任何功能处于活跃状态
  const hasActiveFunction = isAssistantActive || isFrameworkExtractActive || isChapterAnalysisActive;

  return (
    <div className="floating-action-group">
      {/* 主按钮 */}
      <button
        ref={mainButtonRef}
        className={`main-action-button ${isExpanded ? 'expanded' : ''} ${hasActiveFunction ? 'has-active' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={handleMainButtonClick}
        disabled={disabled}
        title="AI助手工具"
        aria-label="打开AI助手工具菜单"
      >
        <div className="main-button-icon">
          <motion.svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            animate={{ rotate: isExpanded ? 45 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <path
              d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
              fill="currentColor"
            />
            <path
              d="M19 15L20.09 18.26L24 19L20.09 19.74L19 23L17.91 19.74L14 19L17.91 18.26L19 15Z"
              fill="currentColor"
            />
            <path
              d="M5 15L6.09 18.26L10 19L6.09 19.74L5 23L3.91 19.74L0 19L3.91 18.26L5 15Z"
              fill="currentColor"
            />
          </motion.svg>
        </div>

        {/* 活跃状态指示器 */}
        {hasActiveFunction && (
          <div className="active-indicator">
            <div className="pulse-dot"></div>
          </div>
        )}

        {/* 悬停提示 */}
        <div className="main-button-tooltip">
          AI助手工具
        </div>
      </button>

      {/* 子按钮组 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="sub-buttons-container"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {/* AI助手按钮 */}
            <motion.button
              className={`sub-action-button assistant-button ${isAssistantActive ? 'active' : ''}`}
              onClick={() => handleSubButtonClick('assistant')}
              title="AI助手 - 智能对话和内容生成"
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: -80, opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.2 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z" fill="currentColor"/>
                <path d="M18 14C16.34 14 15 15.34 15 17C15 18.66 16.34 20 18 20C19.66 20 21 18.66 21 17C21 15.34 19.66 14 18 14Z" fill="currentColor"/>
              </svg>
              <div className="sub-button-tooltip">AI助手</div>
            </motion.button>

            {/* 框架提取按钮 */}
            <motion.button
              className={`sub-action-button framework-button ${isFrameworkExtractActive ? 'active' : ''}`}
              onClick={() => handleSubButtonClick('framework')}
              title="框架提取 - 分析大纲结构，提取创作技巧"
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: -160, opacity: 1 }}
              transition={{ delay: 0.15, duration: 0.2 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 3V21H21V19H5V3H3Z" fill="currentColor"/>
                <path d="M7 17H9V10H7V17ZM11 17H13V7H11V17ZM15 17H17V13H15V17Z" fill="currentColor"/>
                <path d="M19 8L17 6L15 8L13 6L11 8L9 6L7 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                <circle cx="19" cy="5" r="2" fill="currentColor"/>
                <circle cx="15" cy="7" r="1.5" fill="currentColor"/>
                <circle cx="11" cy="5" r="1.5" fill="currentColor"/>
                <circle cx="7" cy="7" r="1.5" fill="currentColor"/>
              </svg>
              <div className="sub-button-tooltip">框架提取</div>
            </motion.button>

            {/* 章节分析按钮 */}
            <motion.button
              className={`sub-action-button analysis-button ${isChapterAnalysisActive ? 'active' : ''}`}
              onClick={() => handleSubButtonClick('analysis')}
              title="章节分析 - 分析章节内容，生成JSON示例"
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: -240, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.2 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                <path d="M15 13l2 2-2 2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                <circle cx="19" cy="15" r="2" fill="currentColor"/>
                <circle cx="17" cy="13" r="1" fill="currentColor"/>
                <circle cx="17" cy="17" r="1" fill="currentColor"/>
              </svg>
              <div className="sub-button-tooltip">章节分析</div>
            </motion.button>

            {/* 示例管理按钮 */}
            <motion.button
              className="sub-action-button examples-button"
              onClick={() => handleSubButtonClick('examples')}
              title="示例管理 - 管理和选择AI生成的JSON示例"
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: -320, opacity: 1 }}
              transition={{ delay: 0.25, duration: 0.2 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 7V5C3 3.89 3.89 3 5 3H19C20.11 3 21 3.89 21 5V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                <path d="M3 7H21V19C21 20.11 20.11 21 19 21H5C3.89 21 3 20.11 3 19V7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                <path d="M8 11H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                <path d="M8 15H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                <circle cx="17" cy="8" r="2" fill="currentColor"/>
                <circle cx="7" cy="8" r="1.5" fill="currentColor"/>
              </svg>
              <div className="sub-button-tooltip">示例管理</div>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 背景遮罩 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="floating-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsExpanded(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingActionGroup;
