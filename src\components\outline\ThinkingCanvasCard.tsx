"use client";

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ThinkingCanvasData } from '@/types/thinking-canvas';
import { thinkingCanvasProcessor } from '@/services/thinking-canvas/ThinkingCanvasProcessor';

interface ThinkingCanvasCardProps {
  canvas: ThinkingCanvasData;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  onEdit?: (canvas: ThinkingCanvasData) => void;
  onSave?: (canvas: ThinkingCanvasData) => void;
  onDelete?: (canvasId: string) => void;
  className?: string;
}

/**
 * 思考画布预览卡片组件
 * 显示思考画布的折叠/展开视图，支持编辑和管理操作
 */
const ThinkingCanvasCard: React.FC<ThinkingCanvasCardProps> = ({
  canvas,
  isExpanded = false,
  onToggleExpand,
  onEdit,
  onSave,
  onDelete,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(canvas.content);
  const [editedTitle, setEditedTitle] = useState(canvas.title);
  const [isSaving, setIsSaving] = useState(false);

  // 获取预览内容（前3行）
  const getPreviewContent = useCallback(() => {
    const lines = canvas.content.split('\n').filter(line => line.trim());
    return lines.slice(0, 3).join('\n');
  }, [canvas.content]);

  // 处理编辑模式切换
  const handleEditToggle = useCallback(() => {
    if (isEditing) {
      // 取消编辑，恢复原内容
      setEditedContent(canvas.content);
      setEditedTitle(canvas.title);
    }
    setIsEditing(!isEditing);
  }, [isEditing, canvas.content, canvas.title]);

  // 处理保存
  const handleSave = useCallback(async () => {
    if (isSaving) return;
    
    setIsSaving(true);
    try {
      const updatedCanvas: ThinkingCanvasData = {
        ...canvas,
        title: editedTitle.trim() || canvas.title,
        content: editedContent.trim() || canvas.content,
        updatedAt: new Date().toISOString(),
        metadata: {
          ...canvas.metadata,
          wordCount: editedContent.length,
          editHistory: [
            ...(canvas.metadata.editHistory || []),
            {
              timestamp: new Date().toISOString(),
              changes: 'Manual edit'
            }
          ]
        }
      };

      // 保存到本地存储
      await thinkingCanvasProcessor.saveThinkingCanvas(updatedCanvas);
      
      // 通知父组件
      onSave?.(updatedCanvas);
      
      setIsEditing(false);
    } catch (error) {
      console.error('保存思考画布失败:', error);
    } finally {
      setIsSaving(false);
    }
  }, [canvas, editedTitle, editedContent, isSaving, onSave]);

  // 处理删除
  const handleDelete = useCallback(() => {
    if (window.confirm('确定要删除这个思考画布吗？')) {
      onDelete?.(canvas.id);
    }
  }, [canvas.id, onDelete]);

  // 处理导出
  const handleExport = useCallback(() => {
    const markdown = thinkingCanvasProcessor.exportToMarkdown(canvas);
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${canvas.title}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [canvas]);

  return (
    <motion.div
      className={`thinking-canvas-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      layout
    >
      {/* 卡片头部 */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
        <div className="flex items-center gap-3 flex-1">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm">💭</span>
          </div>
          
          {isEditing ? (
            <input
              type="text"
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              className="flex-1 px-2 py-1 text-lg font-semibold bg-white border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="思考画布标题"
            />
          ) : (
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {canvas.title}
            </h3>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* 展开/折叠按钮 */}
          {onToggleExpand && (
            <button
              onClick={onToggleExpand}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              title={isExpanded ? "折叠" : "展开"}
            >
              <motion.svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <path d="M7 10l5 5 5-5z"/>
              </motion.svg>
            </button>
          )}
          
          {/* 操作按钮 */}
          <div className="flex items-center gap-1">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50"
                  title="保存"
                >
                  {isSaving ? (
                    <motion.div
                      className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  )}
                </button>
                <button
                  onClick={handleEditToggle}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="取消"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={handleEditToggle}
                  className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                  title="编辑"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                  </svg>
                </button>
                <button
                  onClick={handleExport}
                  className="p-2 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors"
                  title="导出"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                  </svg>
                </button>
                {onDelete && (
                  <button
                    onClick={handleDelete}
                    className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                    title="删除"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                    </svg>
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* 卡片内容 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={isExpanded ? 'expanded' : 'collapsed'}
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="overflow-hidden"
        >
          <div className="p-4">
            {isEditing ? (
              <textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="w-full h-64 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="在这里编辑思考内容..."
              />
            ) : (
              <div className="prose prose-sm max-w-none">
                <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                  {isExpanded ? canvas.content : getPreviewContent()}
                  {!isExpanded && canvas.content.split('\n').length > 3 && (
                    <span className="text-gray-400">...</span>
                  )}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
      
      {/* 卡片底部信息 */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <span>字数: {canvas.metadata.wordCount || canvas.content.length}</span>
            <span>创建: {new Date(canvas.createdAt).toLocaleDateString()}</span>
            {canvas.updatedAt !== canvas.createdAt && (
              <span>更新: {new Date(canvas.updatedAt).toLocaleDateString()}</span>
            )}
          </div>
          
          {canvas.tags && canvas.tags.length > 0 && (
            <div className="flex items-center gap-1">
              {canvas.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
              {canvas.tags.length > 3 && (
                <span className="text-gray-400">+{canvas.tags.length - 3}</span>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ThinkingCanvasCard;
