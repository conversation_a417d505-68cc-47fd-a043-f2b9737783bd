"use client";

/**
 * 删除确认选项接口
 */
export interface DeleteConfirmationOptions {
  title: string;
  message: string;
  itemName?: string;
  dangerLevel?: 'low' | 'medium' | 'high';
  confirmText?: string;
  cancelText?: string;
  showRecoveryHint?: boolean;
  customIcon?: string;
}

/**
 * 删除确认结果接口
 */
export interface DeleteConfirmationResult {
  confirmed: boolean;
  timestamp: Date;
  options: DeleteConfirmationOptions;
}

/**
 * 删除确认服务
 * 提供统一的删除确认机制，确保所有删除操作都经过用户确认
 */
export class DeleteConfirmationService {
  private static instance: DeleteConfirmationService;
  private confirmationCallbacks: Map<string, (result: boolean) => void> = new Map();

  private constructor() {}

  /**
   * 获取服务实例（单例模式）
   */
  public static getInstance(): DeleteConfirmationService {
    if (!DeleteConfirmationService.instance) {
      DeleteConfirmationService.instance = new DeleteConfirmationService();
    }
    return DeleteConfirmationService.instance;
  }

  /**
   * 显示删除确认对话框
   * @param options 确认选项
   * @returns Promise<boolean> 用户是否确认删除
   */
  public async confirmDelete(options: DeleteConfirmationOptions): Promise<boolean> {
    return new Promise((resolve) => {
      const confirmationId = this.generateConfirmationId();
      
      // 存储回调函数
      this.confirmationCallbacks.set(confirmationId, resolve);
      
      // 触发确认对话框显示事件
      this.showConfirmationDialog(confirmationId, options);
      
      // 设置超时（30秒后自动取消）
      setTimeout(() => {
        if (this.confirmationCallbacks.has(confirmationId)) {
          this.handleConfirmationResult(confirmationId, false);
        }
      }, 30000);
    });
  }

  /**
   * 处理确认结果
   * @param confirmationId 确认ID
   * @param confirmed 是否确认
   */
  public handleConfirmationResult(confirmationId: string, confirmed: boolean): void {
    const callback = this.confirmationCallbacks.get(confirmationId);
    if (callback) {
      callback(confirmed);
      this.confirmationCallbacks.delete(confirmationId);
    }
  }

  /**
   * 生成确认ID
   */
  private generateConfirmationId(): string {
    return `delete_confirm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 显示确认对话框（通过事件系统）
   */
  private showConfirmationDialog(confirmationId: string, options: DeleteConfirmationOptions): void {
    // 创建自定义事件
    const event = new CustomEvent('show-delete-confirmation', {
      detail: {
        confirmationId,
        options: {
          ...options,
          confirmText: options.confirmText || '删除',
          cancelText: options.cancelText || '取消',
          dangerLevel: options.dangerLevel || 'medium'
        }
      }
    });
    
    // 分发事件
    window.dispatchEvent(event);
  }

  /**
   * 快速确认删除（预设选项）
   */
  public async quickConfirmDelete(itemName: string, itemType: string = '项目'): Promise<boolean> {
    return this.confirmDelete({
      title: `删除${itemType}`,
      message: `确定要删除${itemType}"${itemName}"吗？此操作不可撤销。`,
      itemName,
      dangerLevel: 'medium',
      showRecoveryHint: false
    });
  }

  /**
   * 危险删除确认（高风险操作）
   */
  public async dangerousConfirmDelete(itemName: string, itemType: string, additionalWarning?: string): Promise<boolean> {
    const message = additionalWarning 
      ? `确定要删除${itemType}"${itemName}"吗？${additionalWarning} 此操作不可撤销！`
      : `确定要删除${itemType}"${itemName}"吗？此操作不可撤销！`;

    return this.confirmDelete({
      title: `⚠️ 危险操作：删除${itemType}`,
      message,
      itemName,
      dangerLevel: 'high',
      confirmText: '确认删除',
      showRecoveryHint: false,
      customIcon: '⚠️'
    });
  }

  /**
   * 批量删除确认
   */
  public async batchConfirmDelete(count: number, itemType: string): Promise<boolean> {
    return this.confirmDelete({
      title: `批量删除${itemType}`,
      message: `确定要删除选中的 ${count} 个${itemType}吗？此操作不可撤销。`,
      dangerLevel: count > 10 ? 'high' : 'medium',
      confirmText: `删除 ${count} 个${itemType}`,
      showRecoveryHint: false
    });
  }

  /**
   * 清理所有待处理的确认
   */
  public clearPendingConfirmations(): void {
    this.confirmationCallbacks.forEach(callback => callback(false));
    this.confirmationCallbacks.clear();
  }
}

/**
 * 导出服务实例
 */
export const deleteConfirmationService = DeleteConfirmationService.getInstance();

/**
 * React Hook：使用删除确认服务
 */
export function useDeleteConfirmation() {
  const service = DeleteConfirmationService.getInstance();

  return {
    confirmDelete: service.confirmDelete.bind(service),
    quickConfirmDelete: service.quickConfirmDelete.bind(service),
    dangerousConfirmDelete: service.dangerousConfirmDelete.bind(service),
    batchConfirmDelete: service.batchConfirmDelete.bind(service)
  };
}

/**
 * 便捷函数：快速删除确认
 */
export async function confirmDelete(options: DeleteConfirmationOptions): Promise<boolean> {
  return deleteConfirmationService.confirmDelete(options);
}

/**
 * 便捷函数：快速确认删除
 */
export async function quickConfirmDelete(itemName: string, itemType: string = '项目'): Promise<boolean> {
  return deleteConfirmationService.quickConfirmDelete(itemName, itemType);
}

/**
 * 便捷函数：危险删除确认
 */
export async function dangerousConfirmDelete(itemName: string, itemType: string, additionalWarning?: string): Promise<boolean> {
  return deleteConfirmationService.dangerousConfirmDelete(itemName, itemType, additionalWarning);
}
