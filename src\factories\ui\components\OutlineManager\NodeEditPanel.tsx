"use client";

import React, { useState, useEffect } from 'react';
import { OutlineNodeType } from '../../types/outline';
import { Button } from '@/adapters/ui';

interface NodeEditPanelProps {
  node: OutlineNodeType | null;
  onClose: () => void;
  onSave: (updatedNode: OutlineNodeType) => void;
}

/**
 * 节点编辑面板组件
 * 用于在侧边栏编辑节点信息
 */
const NodeEditPanel: React.FC<NodeEditPanelProps> = ({ node, onClose, onSave }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [creativeNotes, setCreativeNotes] = useState('');
  const [type, setType] = useState<'volume' | 'chapter' | 'plot' | 'dialogue'>('chapter');
  const [isVisible, setIsVisible] = useState(false);

  // 当节点变化时更新表单
  useEffect(() => {
    if (node) {
      setTitle(node.title || '');
      setDescription(node.description || '');
      setCreativeNotes(node.creativeNotes || '');
      setType(node.type || 'chapter');
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [node]);

  // 处理保存
  const handleSave = () => {
    if (!node) return;

    const updatedNode: OutlineNodeType = {
      ...node,
      title: title.trim() || node.title,
      description: description.trim(),
      creativeNotes: creativeNotes.trim(),
      type,
      // 显式保留位置信息，确保不会丢失
      position: node.position
    };

    onSave(updatedNode);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    onClose();
  };

  // 如果没有选中节点，不渲染面板
  if (!isVisible || !node) return null;

  return (
    <div
      className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-50 flex flex-col transition-transform duration-300 ease-in-out"
      style={{
        transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
        borderLeft: '1px solid rgba(var(--outline-secondary-rgb), 0.3)'
      }}
    >
      {/* 面板标题 */}
      <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center bg-[var(--color-primary-bg)]">
        <h3 className="text-lg font-medium text-[var(--color-primary)]">编辑节点</h3>
        <button
          className="p-1 rounded-full hover:bg-gray-100 text-gray-500"
          onClick={handleCancel}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 表单内容 */}
      <div className="flex-1 overflow-auto p-4">
        {/* 节点类型选择 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">节点类型</label>
          <div className="grid grid-cols-2 gap-2 mb-2">
            <button
              className={`py-2 px-3 rounded-md border ${
                type === 'volume'
                  ? 'bg-purple-500 text-white border-purple-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('volume')}
            >
              总纲/卷
            </button>
            <button
              className={`py-2 px-3 rounded-md border ${
                type === 'chapter'
                  ? 'bg-[var(--outline-primary)] text-white border-[var(--outline-primary)]'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('chapter')}
            >
              章节
            </button>
          </div>
          <div className="flex space-x-2">
            <button
              className={`flex-1 py-2 px-3 rounded-md border ${
                type === 'plot'
                  ? 'bg-[var(--outline-secondary)] text-white border-[var(--outline-secondary)]'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('plot')}
            >
              剧情节点
            </button>
            <button
              className={`flex-1 py-2 px-3 rounded-md border ${
                type === 'dialogue'
                  ? 'bg-[var(--outline-info)] text-white border-[var(--outline-info)]'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setType('dialogue')}
            >
              对话设计
            </button>
          </div>
        </div>

        {/* 标题输入 */}
        <div className="mb-4">
          <label htmlFor="node-title" className="block text-sm font-medium text-gray-700 mb-1">标题</label>
          <input
            id="node-title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--outline-primary)] focus:border-[var(--outline-primary)]"
            placeholder="输入节点标题"
          />
        </div>

        {/* 描述输入 */}
        <div className="mb-4">
          <label htmlFor="node-description" className="block text-sm font-medium text-gray-700 mb-1">描述</label>
          <textarea
            id="node-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--outline-primary)] focus:border-[var(--outline-primary)] min-h-[120px]"
            placeholder="输入节点描述"
          />
        </div>

        {/* 创作建议输入 */}
        <div className="mb-4">
          <label htmlFor="node-creative-notes" className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-2">
            <span>💡</span>
            <span>创作建议</span>
          </label>
          <textarea
            id="node-creative-notes"
            value={creativeNotes}
            onChange={(e) => setCreativeNotes(e.target.value)}
            className="w-full px-3 py-2 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 min-h-[100px] bg-gradient-to-r from-blue-50 to-indigo-50"
            placeholder="输入创作建议，如台词设计、心理描写、节奏控制等..."
          />
        </div>

        {/* 节点信息 */}
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <p className="text-xs text-gray-500 mb-1">节点ID: {node.id}</p>
          <p className="text-xs text-gray-500">子节点数量: {node.children?.length || 0}</p>
        </div>
      </div>

      {/* 底部按钮 */}
      <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 flex justify-end space-x-2">
        <Button
          text="取消"
          onClick={handleCancel}
          className="hover:bg-gray-100"
        />
        <Button
          text="保存"
          onClick={handleSave}
          className="shadow-sm bg-blue-500 text-white hover:bg-blue-600"
        />
      </div>
    </div>
  );
};

export default NodeEditPanel;
