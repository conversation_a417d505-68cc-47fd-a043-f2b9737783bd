"use client";

import { MessageBuilder } from './MessageBuilder';
import { WorldBuilding } from '@/lib/db/dexie';
import { WorldBuildingInfoFormatter } from '@/adapters/ai/worldbuilding/WorldBuildingInfoFormatter';

/**
 * 高级消息构建器
 * 用于构建更复杂的消息结构，支持多消息处理
 */
export class AdvancedMessageBuilder {
  private messageBuilder: MessageBuilder;
  private worldBuildingInfoFormatter: WorldBuildingInfoFormatter;

  /**
   * 创建高级消息构建器
   */
  constructor() {
    this.messageBuilder = new MessageBuilder();
    this.worldBuildingInfoFormatter = new WorldBuildingInfoFormatter();
  }

  /**
   * 获取内部的消息构建器
   * @returns 消息构建器
   */
  getMessageBuilder(): MessageBuilder {
    return this.messageBuilder;
  }

  /**
   * 添加系统角色提示词
   * @param systemPrompt 系统角色提示词
   * @returns 当前构建器实例，支持链式调用
   */
  addSystemPrompt(systemPrompt: string): AdvancedMessageBuilder {
    this.messageBuilder.addSystemMessage(systemPrompt);
    return this;
  }

  /**
   * 添加助手角色确认任务
   * @param assistantPrompt 助手角色确认任务提示词
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantConfirmation(assistantPrompt: string): AdvancedMessageBuilder {
    this.messageBuilder.addAssistantMessage(assistantPrompt);
    return this;
  }

  /**
   * 添加基础提示词
   * @param basePrompt 基础提示词
   * @returns 当前构建器实例，支持链式调用
   */
  addBasePrompt(basePrompt: string): AdvancedMessageBuilder {
    this.messageBuilder.addUserMessage(basePrompt);
    return this;
  }

  /**
   * 添加章节内容
   * @param chapterContent 章节内容
   * @param segmentIndex 段落索引
   * @param totalSegments 总段落数
   * @returns 当前构建器实例，支持链式调用
   */
  addChapterContent(chapterContent: string, segmentIndex: number = 0, totalSegments: number = 1): AdvancedMessageBuilder {
    const chapterPrompt = `以下是章节内容（段落 ${segmentIndex + 1}/${totalSegments}）：\n\n${chapterContent}`;
    this.messageBuilder.addUserMessage(chapterPrompt);
    this.messageBuilder.addAssistantMessage(`我已阅读并分析了提供的章节内容。`);
    return this;
  }

  /**
   * 添加多个章节内容，每个章节作为单独的消息
   * @param chapters 章节内容数组，每个元素包含标题和内容
   * @returns 当前构建器实例，支持链式调用
   */
  addMultipleChapters(chapters: Array<{ title: string; content: string }>): AdvancedMessageBuilder {
    if (chapters.length === 0) {
      return this;
    }

    this.messageBuilder.addUserMessage(`以下是多个章节的内容，请逐一分析：`);

    for (const chapter of chapters) {
      const chapterPrompt = `# ${chapter.title}\n\n${chapter.content}`;
      this.messageBuilder.addUserMessage(chapterPrompt);
      this.messageBuilder.addAssistantMessage(`我已阅读并分析了"${chapter.title}"章节的内容。`);
    }

    this.messageBuilder.addAssistantMessage(`我已完成所有章节的分析。`);
    return this;
  }

  /**
   * 添加关联世界观元素
   * @param worldBuilding 当前世界观对象
   * @param relatedElements 关联世界观元素数组
   * @param bookId 书籍ID
   * @returns 当前构建器实例，支持链式调用
   */
  async addRelatedWorldBuildings(worldBuilding: WorldBuilding, relatedElements: string[], bookId?: string): Promise<AdvancedMessageBuilder> {
    if (relatedElements.length === 0) {
      return this;
    }

    // 处理关联元素，只显示名称
    const processedElements = relatedElements.map(element => {
      // 检查是否是 "id:name" 格式
      const parts = element.split(':');
      if (parts.length === 2) {
        return parts[1]; // 只返回名称部分
      }
      return element; // 如果不是 "id:name" 格式，则返回原始值
    });

    // 添加引导消息
    this.messageBuilder.addUserMessage(`请注意以下已有的世界观元素，它们与"${worldBuilding.name}"有关联：`);

    // 获取关联世界观元素的完整信息
    const relatedWorldBuildingsInfo = await this.worldBuildingInfoFormatter.getRelatedWorldBuildingsInfo(relatedElements, bookId);

    // 为每个关联世界观元素添加单独的消息
    for (const { info } of relatedWorldBuildingsInfo) {
      this.messageBuilder.addAssistantMessage(info);
    }

    // 添加总结消息
    this.messageBuilder.addUserMessage(`请在处理时，考虑上述世界观元素与"${worldBuilding.name}"的关联。`);
    this.messageBuilder.addAssistantMessage(`我将在处理"${worldBuilding.name}"时，考虑它与上述世界观元素的关联。`);

    return this;
  }

  /**
   * 添加输出格式指令
   * @param formatPrompt 输出格式提示词
   * @returns 当前构建器实例，支持链式调用
   */
  addOutputFormat(formatPrompt: string): AdvancedMessageBuilder {
    this.messageBuilder.addUserMessage(formatPrompt);
    return this;
  }

  /**
   * 添加自定义提示词作为最后一个用户消息
   * @param customPrompt 自定义提示词
   * @returns 当前构建器实例，支持链式调用
   */
  addCustomPrompt(customPrompt?: string): AdvancedMessageBuilder {
    if (customPrompt) {
      this.messageBuilder.addUserMessage(`用户重要要求：${customPrompt}`);
    }
    return this;
  }

  /**
   * 构建消息数组
   * @returns 消息数组
   */
  build(): Array<{ role: string; content: string }> {
    return this.messageBuilder.build();
  }

  /**
   * 清空消息数组
   * @returns 当前构建器实例，支持链式调用
   */
  clear(): AdvancedMessageBuilder {
    this.messageBuilder.clear();
    return this;
  }

  /**
   * 创建一个新的高级消息构建器实例
   * @returns 新的高级消息构建器实例
   */
  static create(): AdvancedMessageBuilder {
    return new AdvancedMessageBuilder();
  }
}

/**
 * 创建一个新的高级消息构建器实例
 * @returns 新的高级消息构建器实例
 */
export function createAdvancedMessageBuilder(): AdvancedMessageBuilder {
  return AdvancedMessageBuilder.create();
}

export default createAdvancedMessageBuilder;
