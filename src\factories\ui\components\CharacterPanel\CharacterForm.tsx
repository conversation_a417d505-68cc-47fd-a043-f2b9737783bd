"use client";

import React, { useState, useEffect } from 'react';
import { Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { createUIFactory } from '../../UIFactory.ts';
import { v4 as uuidv4 } from 'uuid';
import { CharacterFieldAIButton } from './CharacterFieldAIButton';
import { TerminologyRelationSelector } from './TerminologyRelationSelector';
import { WorldBuildingRelationSelector } from './WorldBuildingRelationSelector';

interface CharacterFormProps {
  character: Character;
  bookId: string;
  isNew: boolean;
  onSave: (character: Character) => void;
  onCancel: () => void;
  className?: string;
  hideButtons?: boolean;
  terminologies?: Terminology[]; // 所有术语列表，用于关联选择
  worldBuildings?: WorldBuilding[]; // 所有世界观列表，用于关联选择
}

/**
 * 人物表单组件
 */
export const CharacterForm: React.FC<CharacterFormProps> = ({
  character,
  bookId,
  isNew,
  onSave,
  onCancel,
  className,
  hideButtons = false,
  terminologies = [],
  worldBuildings = []
}) => {
  // 创建UI工厂实例
  const uiFactory = createUIFactory();

  // 表单状态
  const [formData, setFormData] = useState<Character>({
    ...character,
    bookId: bookId
  });

  // 选项卡状态
  const [activeTab, setActiveTab] = useState<'basic' | 'details' | 'hidden' | 'relations'>('basic');

  // 关联术语状态
  const [relatedTerminologyIds, setRelatedTerminologyIds] = useState<string[]>(
    character.relatedTerminologyIds || []
  );

  // 关联世界观状态
  const [relatedWorldBuildingIds, setRelatedWorldBuildingIds] = useState<string[]>(
    character.relatedWorldBuildingIds || []
  );

  // 当character属性变化时更新表单数据
  useEffect(() => {
    setFormData({
      ...character,
      bookId: bookId
    });
    setRelatedTerminologyIds(character.relatedTerminologyIds || []);
    setRelatedWorldBuildingIds(character.relatedWorldBuildingIds || []);
  }, [character, bookId]);

  // 初始化时调整所有文本区域的高度
  useEffect(() => {
    // 在下一个渲染周期调整所有文本区域的高度
    setTimeout(() => {
      document.querySelectorAll('textarea').forEach((textarea) => {
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight}px`;
      });
    }, 100);
  }, [isNew, activeTab]); // 当isNew或activeTab变化时重新调整

  // 处理表单字段变化
  const handleChange = (field: keyof Character, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理关联术语变更
  const handleRelatedTerminologiesChange = (ids: string[]) => {
    setRelatedTerminologyIds(ids);
    setFormData(prev => ({
      ...prev,
      relatedTerminologyIds: ids
    }));
  };

  // 处理关联世界观变更
  const handleRelatedWorldBuildingsChange = (ids: string[]) => {
    setRelatedWorldBuildingIds(ids);
    setFormData(prev => ({
      ...prev,
      relatedWorldBuildingIds: ids
    }));
  };

  // 处理保存
  const handleSave = async () => {
    // 移除名称必填的验证
    // 如果没有名称，使用"新人物"作为默认名称
    if (!formData.name || !formData.name.trim()) {
      formData.name = '新人物';
    }

    // 准备保存的数据
    const now = new Date();
    const characterToSave: Character = {
      ...formData,
      id: isNew ? uuidv4() : formData.id, // 使用isNew标志来决定是否生成新ID
      bookId: bookId,
      createdAt: formData.createdAt || now,
      updatedAt: now,
      extractedFromChapterIds: formData.extractedFromChapterIds || [],
      relatedCharacterIds: formData.relatedCharacterIds || [],
      relatedTerminologyIds: relatedTerminologyIds,
      relatedWorldBuildingIds: relatedWorldBuildingIds
    };

    console.log('保存人物:', characterToSave, '是否为新人物:', isNew);

    try {
      // 处理双向关联 - 术语
      if (character.id) {
        // 获取当前关联的术语ID列表
        const currentTerminologyIds = character.relatedTerminologyIds || [];

        // 需要添加关联的术语ID列表
        const terminologyIdsToAdd = relatedTerminologyIds.filter(id => !currentTerminologyIds.includes(id));

        // 需要移除关联的术语ID列表
        const terminologyIdsToRemove = currentTerminologyIds.filter(id => !relatedTerminologyIds.includes(id));

        // 导入terminologyRepository
        const { terminologyRepository } = await import('@/lib/db/repositories');

        // 为每个新增的术语添加关联
        for (const terminologyId of terminologyIdsToAdd) {
          const terminology = await terminologyRepository.getById(terminologyId);
          if (terminology) {
            const relatedCharacterIds = [...(terminology.relatedCharacterIds || [])];
            if (!relatedCharacterIds.includes(character.id)) {
              relatedCharacterIds.push(character.id);
              await terminologyRepository.update(terminologyId, {
                relatedCharacterIds
              });
              console.log(`已为术语 ${terminology.name} 添加人物关联`);
            }
          }
        }

        // 为每个移除的术语删除关联
        for (const terminologyId of terminologyIdsToRemove) {
          const terminology = await terminologyRepository.getById(terminologyId);
          if (terminology) {
            const relatedCharacterIds = (terminology.relatedCharacterIds || [])
              .filter(id => id !== character.id);
            await terminologyRepository.update(terminologyId, {
              relatedCharacterIds
            });
            console.log(`已为术语 ${terminology.name} 移除人物关联`);
          }
        }

        // 处理双向关联 - 世界观
        // 获取当前关联的世界观ID列表
        const currentWorldBuildingIds = character.relatedWorldBuildingIds || [];

        // 需要添加关联的世界观ID列表
        const worldBuildingIdsToAdd = relatedWorldBuildingIds.filter(id => !currentWorldBuildingIds.includes(id));

        // 需要移除关联的世界观ID列表
        const worldBuildingIdsToRemove = currentWorldBuildingIds.filter(id => !relatedWorldBuildingIds.includes(id));

        // 导入worldBuildingRepository
        const { worldBuildingRepository } = await import('@/lib/db/repositories');

        // 为每个新增的世界观添加关联
        for (const worldBuildingId of worldBuildingIdsToAdd) {
          const worldBuilding = await worldBuildingRepository.getById(worldBuildingId);
          if (worldBuilding) {
            const relatedCharacterIds = [...(worldBuilding.relatedCharacterIds || [])];
            if (!relatedCharacterIds.includes(character.id)) {
              relatedCharacterIds.push(character.id);
              await worldBuildingRepository.update(worldBuildingId, {
                relatedCharacterIds
              });
              console.log(`已为世界观 ${worldBuilding.name} 添加人物关联`);
            }
          }
        }

        // 为每个移除的世界观删除关联
        for (const worldBuildingId of worldBuildingIdsToRemove) {
          const worldBuilding = await worldBuildingRepository.getById(worldBuildingId);
          if (worldBuilding) {
            const relatedCharacterIds = (worldBuilding.relatedCharacterIds || [])
              .filter(id => id !== character.id);
            await worldBuildingRepository.update(worldBuildingId, {
              relatedCharacterIds
            });
            console.log(`已为世界观 ${worldBuilding.name} 移除人物关联`);
          }
        }
      }
    } catch (error) {
      console.error('处理双向关联时出错:', error);
    }

    // 调用保存回调
    onSave(characterToSave);
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();
  };



  // 自动调整文本区域高度
  const autoResizeTextarea = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = e.target;
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';
    // 设置新高度
    textarea.style.height = `${textarea.scrollHeight}px`;
  };

  // 渲染表单字段
  const renderFormField = (
    label: string,
    field: keyof Character,
    placeholder: string,
    rows: number = 3,
    required: boolean = false,
    tooltip?: string
  ) => {
    return (
      <div className="mb-4">
        <div className="flex items-center mb-1">
          <label className="block text-sm font-medium text-gray-700">
            {label} {required && <span className="text-red-500">*</span>}
          </label>

          {/* AI按钮 - 生成和更新 */}
          {field !== 'name' && field !== 'alias' && (
            <div className="flex">
              {/* 生成按钮 */}
              <CharacterFieldAIButton
                character={formData}
                fieldName={field as string}
                fieldDisplayName={label}
                bookId={bookId}
                mode="generate"
                onSave={(updatedCharacter) => {
                  setFormData(updatedCharacter);
                  // 在下一个渲染周期调整所有文本区域的高度
                  setTimeout(() => {
                    document.querySelectorAll('textarea').forEach((textarea) => {
                      textarea.style.height = 'auto';
                      textarea.style.height = `${textarea.scrollHeight}px`;
                    });
                  }, 0);
                }}
              />

              {/* 更新按钮 */}
              <CharacterFieldAIButton
                character={formData}
                fieldName={field as string}
                fieldDisplayName={label}
                bookId={bookId}
                mode="update"
                onSave={(updatedCharacter) => {
                  setFormData(updatedCharacter);
                  // 在下一个渲染周期调整所有文本区域的高度
                  setTimeout(() => {
                    document.querySelectorAll('textarea').forEach((textarea) => {
                      textarea.style.height = 'auto';
                      textarea.style.height = `${textarea.scrollHeight}px`;
                    });
                  }, 0);
                }}
              />
            </div>
          )}

          {tooltip && (
            <div className="group relative tooltip-container" style={{ marginLeft: '8px' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div
                className="tooltip-content"
              >
                {tooltip}
                <div className="tooltip-arrow"></div>
              </div>
              <style jsx>{`
                .tooltip-container {
                  position: relative;
                  display: inline-block;
                }
                .tooltip-content {
                  position: absolute;
                  z-index: 9999;
                  width: 280px;
                  padding: 8px;
                  background-color: rgba(0, 0, 0, 0.8);
                  color: white;
                  font-size: 12px;
                  border-radius: 4px;
                  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                  bottom: 100%;
                  left: 50%;
                  transform: translateX(-50%);
                  margin-bottom: 10px;
                  opacity: 0;
                  visibility: hidden;
                  transition: all 0.2s ease;
                  pointer-events: none;
                }
                .tooltip-arrow {
                  position: absolute;
                  top: 100%;
                  left: 50%;
                  transform: translateX(-50%);
                  border-width: 4px;
                  border-style: solid;
                  border-color: transparent;
                  border-top-color: rgba(0, 0, 0, 0.8);
                }
                .tooltip-container:hover .tooltip-content {
                  opacity: 1;
                  visibility: visible;
                }
              `}</style>
            </div>
          )}
        </div>
        <textarea
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none overflow-hidden"
          rows={rows}
          value={formData[field] as string || ''}
          onChange={(e) => {
            handleChange(field, e.target.value);
            autoResizeTextarea(e);
          }}
          placeholder={placeholder}
          required={required}
          style={{ minHeight: `${rows * 1.5}em` }}
          ref={(el) => {
            // 初始化时调整高度
            if (el) {
              setTimeout(() => {
                el.style.height = 'auto';
                el.style.height = `${el.scrollHeight}px`;
              }, 0);
            }
          }}
        />
      </div>
    );
  };

  return (
    <form id="character-form" onSubmit={handleSubmit} className={`h-full overflow-auto ${className || ''}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">
          {isNew ? '创建新人物' : `编辑人物: ${character.name}`}
        </h2>
      </div>

      {/* 选项卡导航 */}
      <div className="flex border-b mb-6">
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'basic' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('basic')}
        >
          基本信息
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'details' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('details')}
        >
          详细特征
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'hidden' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('hidden')}
        >
          隐藏信息素
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'relations' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('relations')}
        >
          关系
        </button>
      </div>

      <div className="space-y-4">
        {/* 基本信息选项卡 */}
        {activeTab === 'basic' && (
          <div>
            {/* 名称 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                名称
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="输入人物名称（可选，留空将创建'新人物'）"
              />
            </div>

            {/* 别名 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                别名
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {(formData.alias || []).map((alias, index) => (
                  <div
                    key={index}
                    className="bg-gray-100 px-2 py-1 rounded-md flex items-center text-sm"
                  >
                    <span>{alias}</span>
                    <button
                      type="button"
                      className="ml-1 text-gray-500 hover:text-gray-700"
                      onClick={() => {
                        const newAlias = [...(formData.alias || [])];
                        newAlias.splice(index, 1);
                        handleChange('alias', newAlias);
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex">
                <input
                  type="text"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="添加别名（如：小名、称号等）"
                  id="aliasInput"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const input = document.getElementById('aliasInput') as HTMLInputElement;
                      if (input.value.trim()) {
                        const newAlias = [...(formData.alias || []), input.value.trim()];
                        handleChange('alias', newAlias);
                        input.value = '';
                      }
                    }
                  }}
                />
                <button
                  type="button"
                  className="px-3 py-2 bg-gray-200 rounded-r-md hover:bg-gray-300"
                  onClick={() => {
                    const input = document.getElementById('aliasInput') as HTMLInputElement;
                    if (input.value.trim()) {
                      const newAlias = [...(formData.alias || []), input.value.trim()];
                      handleChange('alias', newAlias);
                      input.value = '';
                    }
                  }}
                >
                  添加
                </button>
              </div>
            </div>

            {/* 描述 */}
            {renderFormField(
              '描述',
              'description',
              '简要描述这个人物的主要特点和在故事中的角色',
              3,
              true,
              '这是对人物的简短概述，会显示在人物列表中'
            )}

            {/* 角色原型 */}
            {renderFormField(
              '角色原型',
              'characterArchetype',
              '例如：英雄、导师、守门人、信使、变形者、盟友、敌人等',
              1,
              false,
              '角色原型有助于确定人物在故事中的功能和象征意义'
            )}
          </div>
        )}

        {/* 详细特征选项卡 */}
        {activeTab === 'details' && (
          <div>
            {/* 外貌 */}
            {renderFormField(
              '外貌',
              'appearance',
              '描述人物的外表特征，如身高、体型、发色、眼睛、衣着风格等',
              3,
              false,
              '具体描述人物的外观，帮助读者在脑海中形成清晰的形象'
            )}

            {/* 性格 */}
            {renderFormField(
              '性格',
              'personality',
              '描述人物的性格特点，如内向/外向、乐观/悲观、冲动/谨慎等',
              3,
              false,
              '性格特点决定了人物在面对情境时的反应方式'
            )}

            {/* 背景 */}
            {renderFormField(
              '背景',
              'background',
              '描述人物的成长经历、家庭情况、教育背景等',
              3,
              false,
              '背景故事解释了人物为什么会成为现在的样子'
            )}

            {/* 目标 */}
            {renderFormField(
              '目标',
              'goals',
              '描述人物在故事中想要达成的目标或愿望',
              3,
              false,
              '明确的目标能够驱动人物行动并推动情节发展'
            )}

            {/* 成长弧线 */}
            {renderFormField(
              '成长弧线',
              'growthArc',
              '描述人物在故事中的成长或变化轨迹',
              3,
              false,
              '人物的成长弧线展示了他们如何从故事开始到结束发生变化'
            )}
          </div>
        )}

        {/* 隐藏信息素选项卡 */}
        {activeTab === 'hidden' && (
          <div>
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    隐藏信息素是AI不会自动构思的深层次人物特征，这些信息有助于创造更加立体、复杂的人物形象。
                  </p>
                </div>
              </div>
            </div>

            {/* 隐藏动机 */}
            {renderFormField(
              '隐藏动机',
              'hiddenMotivation',
              '人物表面行为背后的真实动机，可能连他自己都没有意识到',
              3,
              false,
              '隐藏动机能够增加人物的复杂性和故事的张力'
            )}

            {/* 秘密历史 */}
            {renderFormField(
              '秘密历史',
              'secretHistory',
              '人物不为人知的过去经历，可能影响其现在的行为和决策',
              3,
              false,
              '秘密历史可以在故事关键时刻揭露，制造转折点'
            )}

            {/* 内心冲突 */}
            {renderFormField(
              '内心冲突',
              'innerConflicts',
              '人物内心的矛盾和挣扎，如价值观冲突、道德困境等',
              3,
              false,
              '内心冲突使人物更加真实，也是推动人物成长的关键'
            )}

            {/* 象征意义 */}
            {renderFormField(
              '象征意义',
              'symbolism',
              '人物在故事中所代表的象征或主题意义',
              3,
              false,
              '象征意义将人物与故事的更深层主题联系起来'
            )}
          </div>
        )}

        {/* 关系选项卡 */}
        {activeTab === 'relations' && (
          <div>
            {/* 关联术语 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关联术语
              </label>
              <TerminologyRelationSelector
                allTerminologies={terminologies}
                selectedIds={relatedTerminologyIds}
                onChange={handleRelatedTerminologiesChange}
                currentCharacterId={character.id}
              />
            </div>

            {/* 关联世界观 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关联世界观
              </label>
              <WorldBuildingRelationSelector
                allWorldBuildings={worldBuildings}
                selectedIds={relatedWorldBuildingIds}
                onChange={handleRelatedWorldBuildingsChange}
                currentCharacterId={character.id}
              />
            </div>

            {/* 备注 */}
            {renderFormField(
              '备注',
              'notes',
              '添加关于人物的任何其他信息或备注',
              3,
              false,
              '可以记录任何不适合放在其他字段的信息'
            )}
          </div>
        )}

        {/* 按钮区域 */}
        {!hideButtons && (
          <div className="flex justify-end space-x-3 mt-6">
            {(() => {
              const cancelButton = uiFactory.createButtonComponent('取消', 'secondary', 'medium');
              cancelButton.onClick(onCancel);
              return cancelButton.render();
            })()}
            {(() => {
              const saveButton = uiFactory.createButtonComponent('保存', 'success', 'medium');
              saveButton.onClick(handleSave);
              return saveButton.render();
            })()}
          </div>
        )}
      </div>
    </form>
  );
};
