"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { AssociationData } from './types';

interface AssociationPanelProps {
  associationData: AssociationData;
  selectedType: string | null;
  onDataSelect: (dataType: string, items: any[]) => void;
}

/**
 * 关联数据面板组件
 * 显示和管理与脑洞生成相关的数据（角色、世界观、词条、大纲）
 */
const AssociationPanel: React.FC<AssociationPanelProps> = ({
  associationData,
  selectedType,
  onDataSelect
}) => {
  const [activeTab, setActiveTab] = useState<string>('characters');
  const [selectedItems, setSelectedItems] = useState<{[key: string]: any[]}>({
    characters: [],
    worldSettings: [],
    glossary: [],
    outline: []
  });

  const tabs = [
    { id: 'characters', name: '角色', icon: '👤', data: associationData.characters },
    { id: 'worldSettings', name: '世界观', icon: '🌍', data: associationData.worldSettings },
    { id: 'glossary', name: '词条', icon: '📖', data: associationData.glossary },
    { id: 'outline', name: '大纲', icon: '📋', data: associationData.outline },
  ];

  const handleItemToggle = (dataType: string, item: any) => {
    const currentSelected = selectedItems[dataType] || [];
    const isSelected = currentSelected.some(selected => selected.id === item.id);
    
    let newSelected;
    if (isSelected) {
      newSelected = currentSelected.filter(selected => selected.id !== item.id);
    } else {
      newSelected = [...currentSelected, item];
    }
    
    setSelectedItems(prev => ({
      ...prev,
      [dataType]: newSelected
    }));
    
    onDataSelect(dataType, newSelected);
  };

  const renderDataItem = (item: any, dataType: string) => {
    const isSelected = selectedItems[dataType]?.some(selected => selected.id === item.id);
    
    return (
      <motion.div
        key={item.id}
        className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
          isSelected 
            ? 'border-purple-500 bg-purple-50' 
            : 'border-gray-200 bg-white hover:border-gray-300'
        }`}
        onClick={() => handleItemToggle(dataType, item)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium text-sm text-gray-900">
              {item.name || item.title || item.term}
            </h4>
            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
              {item.description || item.content || item.definition}
            </p>
            {item.traits && (
              <div className="flex flex-wrap gap-1 mt-2">
                {item.traits.slice(0, 3).map((trait: any, index: number) => {
                  // 安全处理trait，确保它可以被渲染
                  let displayTrait: string;

                  if (typeof trait === 'string') {
                    displayTrait = trait;
                  } else if (typeof trait === 'object' && trait !== null) {
                    // 如果是对象，尝试提取name、title或feature字段，否则转为JSON字符串
                    displayTrait = trait.name || trait.title || trait.feature || JSON.stringify(trait);
                  } else {
                    displayTrait = String(trait);
                  }

                  return (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                      {displayTrait}
                    </span>
                  );
                })}
              </div>
            )}
            {item.category && (
              <span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {item.category}
              </span>
            )}
          </div>
          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
            isSelected ? 'border-purple-500 bg-purple-500' : 'border-gray-300'
          }`}>
            {isSelected && (
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  const getSelectedCount = () => {
    return Object.values(selectedItems).reduce((total, items) => total + items.length, 0);
  };

  return (
    <div className="bg-white border-t border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">关联数据</h3>
          <div className="text-sm text-gray-600">
            已选择 {getSelectedCount()} 项数据
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          选择相关数据来增强AI脑洞生成的上下文理解
        </p>
      </div>

      {/* 标签页 */}
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
              <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                {tab.data?.length || 0}
              </span>
            </div>
          </button>
        ))}
      </div>

      {/* 内容区域 */}
      <div className="p-4 max-h-64 overflow-y-auto">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`${activeTab === tab.id ? 'block' : 'hidden'}`}
          >
            {tab.data && tab.data.length > 0 ? (
              <div className="space-y-3">
                {tab.data.map((item) => renderDataItem(item, tab.id))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">{tab.icon}</div>
                <p>暂无{tab.name}数据</p>
                <p className="text-sm mt-1">添加{tab.name}数据后可用于脑洞生成</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 操作按钮 */}
      {getSelectedCount() > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              将使用选中的数据作为生成上下文
            </div>
            <button
              onClick={() => setSelectedItems({
                characters: [],
                worldSettings: [],
                glossary: [],
                outline: []
              })}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              清空选择
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssociationPanel;
