"use client";

import React from 'react';

interface AIWritingControlsProps {
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  selectedOutlineNodeIds?: string[];
  isLoading: boolean;
  generatedContent: string;
  bookId: string;
  onCancel: () => void;
  onGenerate: () => void;
  onApply: () => void;
  onContinue: () => void;
  onRewrite: () => void;
  onAnalyze: () => void;
}

/**
 * AI写作控制按钮组件
 * 包含底部控制按钮
 */
const AIWritingControls: React.FC<AIWritingControlsProps> = ({
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  selectedOutlineNodeIds = [],
  isLoading,
  generatedContent,
  bookId,
  onCancel,
  onGenerate,
  onApply,
  onContinue,
  onRewrite,
  onAnalyze
}) => {
  return (
    <div className="p-5 border-t flex justify-between items-center bg-gradient-to-r from-gray-50 to-indigo-50">
      <div>
        <button
          className="px-5 py-2.5 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
          onClick={onCancel}
          disabled={isLoading}
        >
          取消
        </button>
      </div>

      <div className="flex items-center space-x-4">
        {/* 选中元素统计 */}
        <div className="flex flex-col space-y-2">
          <div className="flex space-x-3 bg-white px-4 py-2 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{selectedChapterIds.length} 章节</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{selectedCharacterIds.length} 人物</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{selectedTerminologyIds.length} 术语</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-amber-500 mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{selectedWorldBuildingIds.length} 世界观</span>
            </div>
          </div>

          {/* 大纲节点使用提示 */}
          {selectedOutlineNodeIds.length > 0 && (
            <div className="bg-amber-50 border border-amber-200 px-3 py-2 rounded-lg">
              <div className="flex items-center text-xs text-amber-700">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                📋 将参考 {selectedOutlineNodeIds.length} 个大纲节点进行创作
              </div>
            </div>
          )}
        </div>

        {/* 生成内容按钮或继续对话按钮组 */}
        {generatedContent ? (
          <div className="flex space-x-3">
            {/* 继续对话按钮组 */}
            <button
              onClick={onContinue}
              className="px-4 py-2.5 bg-indigo-500 text-white rounded-xl hover:bg-indigo-600 transition-colors shadow-sm text-sm flex items-center"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              继续创作
            </button>
            <button
              onClick={onRewrite}
              className="px-4 py-2.5 bg-amber-500 text-white rounded-xl hover:bg-amber-600 transition-colors shadow-sm text-sm flex items-center"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重写内容
            </button>
            <button
              onClick={onAnalyze}
              className="px-4 py-2.5 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors shadow-sm text-sm flex items-center"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              分析内容
            </button>
            <button
              className="px-4 py-2.5 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-colors flex items-center shadow-md font-medium"
              onClick={onApply}
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              应用内容
            </button>
          </div>
        ) : (
          <button
            className="px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-xl hover:from-indigo-600 hover:to-blue-700 transition-colors flex items-center shadow-md font-medium"
            onClick={onGenerate}
            disabled={isLoading}
          >
            {isLoading ? (
              <svg className="animate-spin mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                开始AI创作
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default AIWritingControls;
