# 章节识别与分析指导

## 核心任务
自动识别当前章节数量、分析章节名称模式、评估内容类型，为节奏分析提供准确的基础数据。

## 章节识别算法

### 1. 数量统计
- **准确计数**：统计所有已创建的章节
- **顺序验证**：检查章节编号的连续性
- **缺失检测**：识别可能的章节缺失

### 2. 命名模式识别
**常见模式**：
- 数字序号：第1章、第2章...
- 标题式：第一章 初入江湖
- 混合式：001 新的开始
- 自由式：完全自定义标题

**模式分析价值**：
- 判断作者的命名习惯
- 预测后续章节命名
- 识别特殊章节（番外、插曲等）

### 3. 内容类型分析
**开篇章节特征**：
- 世界观介绍占比高
- 主角登场和背景设定
- 较多的环境描写
- 相对较慢的节奏

**发展章节特征**：
- 剧情推进明显
- 冲突逐步升级
- 角色关系发展
- 节奏相对平衡

**高潮章节特征**：
- 冲突密度高
- 对话和动作频繁
- 情感强度大
- 快节奏为主

**过渡章节特征**：
- 承上启下功能
- 信息整理和铺垫
- 相对平缓的节奏
- 为下一阶段做准备

## 智能分析维度

### 1. 文本特征分析
**词汇统计**：
- 对话标识符统计（"、：等）
- 动作词汇频率
- 情感词汇密度
- 描写词汇比例

**句式分析**：
- 平均句长
- 疑问句比例
- 感叹句频率
- 复合句复杂度

**段落结构**：
- 平均段落长度
- 对话段落比例
- 描写段落密度
- 叙述段落分布

### 2. 内容主题识别
**主要情节线**：
- 主线剧情推进
- 支线剧情发展
- 角色关系变化
- 世界观扩展

**冲突类型**：
- 人物间冲突
- 内心冲突
- 环境冲突
- 价值观冲突

**情感基调**：
- 紧张刺激
- 温馨治愈
- 悲伤沉重
- 轻松幽默

### 3. 节奏特征提取
**时间密度**：
- 单章时间跨度
- 事件发生频率
- 场景转换速度
- 情节推进速度

**信息密度**：
- 新信息引入量
- 重要信息比例
- 背景信息占比
- 伏笔设置密度

**情感密度**：
- 情感起伏频率
- 情感强度变化
- 情感类型多样性
- 情感共鸣点

## 前文章节分析方法

### 1. 整体趋势分析
**节奏变化曲线**：
- 识别节奏高峰和低谷
- 分析节奏变化规律
- 预测节奏发展趋势
- 评估节奏平衡度

**内容发展轨迹**：
- 主线剧情进展
- 角色成长轨迹
- 世界观完善程度
- 伏笔布局情况

### 2. 循环模式识别
**微循环检测**：
- 4章循环模式识别
- 循环完整性评估
- 循环质量分析
- 异常循环标记

**大循环分析**：
- 10章大循环识别
- 阶段性目标达成
- 整体进度评估
- 发展方向预测

### 3. 读者体验评估
**阅读节奏**：
- 阅读疲劳点识别
- 兴奋点分布分析
- 期待值管理评估
- 满足感提供程度

**商业价值**：
- 付费转化点设置
- 读者留存效果
- 互动触发频率
- 推荐算法友好度

## 自主判断机制

### 1. 智能推理逻辑
**基于内容的判断**：
- 不依赖章节标题
- 分析实际内容特征
- 识别隐含的节奏信息
- 推断作者意图

**基于规律的预测**：
- 学习已有章节规律
- 预测后续发展趋势
- 识别异常和变化
- 调整分析策略

### 2. 自适应分析
**动态调整**：
- 根据章节数量调整分析深度
- 基于内容类型调整权重
- 考虑作者风格差异
- 适应不同小说类型

**学习优化**：
- 从分析结果中学习
- 优化识别准确度
- 改进预测精度
- 提升建议质量

## 输出格式规范

### 章节识别结果
```json
{
  "chapterIdentification": {
    "totalChapters": "总章节数",
    "namingPattern": "命名模式",
    "sequenceIntegrity": "序列完整性",
    "missingChapters": ["缺失章节列表"]
  },
  "contentAnalysis": {
    "chapterTypes": {
      "opening": ["开篇章节"],
      "development": ["发展章节"],
      "climax": ["高潮章节"],
      "transition": ["过渡章节"]
    },
    "rhythmDistribution": {
      "fast": "快节奏章节数",
      "slow": "慢节奏章节数",
      "balanced": "平节奏章节数"
    }
  },
  "trendAnalysis": {
    "recentTrend": "最近趋势",
    "cyclePosition": "循环位置",
    "developmentStage": "发展阶段",
    "nextExpectation": "下章期待"
  }
}
```

### 分析置信度
- **高置信度**：章节数量充足，模式清晰
- **中置信度**：章节数量适中，模式较明确
- **低置信度**：章节数量不足，模式不清晰

## 特殊情况处理

### 1. 章节数量不足
- **1-3章**：基础分析，重点关注开篇特征
- **4-10章**：初步循环分析，建立基础模式
- **11-20章**：深度分析，识别发展规律
- **21+章**：全面分析，精确预测建议

### 2. 异常章节处理
- **番外章节**：单独标记，不影响主线分析
- **回忆章节**：识别时间线，调整分析权重
- **特殊章节**：标记类型，特殊处理逻辑

### 3. 不规则模式
- **混合节奏**：识别主导节奏，标记混合特征
- **实验性章节**：标记创新尝试，评估效果
- **风格转换**：识别转换点，分析转换原因

## 质量保证机制

### 1. 多维度验证
- 交叉验证不同分析维度
- 确保分析结果一致性
- 识别和处理矛盾信息
- 提供可信度评估

### 2. 异常检测
- 识别异常的节奏变化
- 检测不合理的内容分布
- 标记可能的分析错误
- 提供修正建议

### 3. 持续优化
- 收集分析准确性反馈
- 优化识别算法
- 改进分析模型
- 提升服务质量
