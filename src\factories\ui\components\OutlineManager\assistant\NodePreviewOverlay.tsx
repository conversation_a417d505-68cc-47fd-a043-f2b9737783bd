"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { OutlineNodeType, PlotPoint, DialogueItem, PlotType } from '../../../types/outline';
import './NodePreviewOverlay.css';

interface NodeChange {
  type: 'create' | 'update' | 'delete';
  nodeId: string;
  data?: Partial<OutlineNodeType>;
  parentId?: string;
}

// 重名节点信息接口
interface DuplicateNodeInfo {
  changeId: string;
  generatedTitle: string;
  existingNode: OutlineNodeType;
  suggestedNewTitle: string;
  conflictType: 'exact_match' | 'similar_title';
}

// 冲突解决方案接口
interface ConflictResolution {
  nodeId: string;
  action: 'update' | 'create_with_suffix' | 'skip';
  newTitle?: string;
}

interface NodePreviewOverlayProps {
  changes: NodeChange[];
  outline: any;
  onToggleChange: (changeId: string, selected: boolean) => void;
  selectedChanges: string[];
  onConflictResolved?: (resolvedChanges: NodeChange[]) => void; // 新增接口
}

/**
 * 节点预览覆盖层组件
 * 在大纲树中显示虚化的预览节点
 */
const NodePreviewOverlay: React.FC<NodePreviewOverlayProps> = ({
  changes,
  outline,
  onToggleChange,
  selectedChanges,
  onConflictResolved
}) => {
  const [previewNodes, setPreviewNodes] = useState<Map<string, NodeChange>>(new Map());
  const [duplicateConflicts, setDuplicateConflicts] = useState<DuplicateNodeInfo[]>([]);
  const [showConflictResolver, setShowConflictResolver] = useState(false);

  // 获取所有现有节点的标题
  const getAllNodeTitles = useCallback((nodes: OutlineNodeType[]): string[] => {
    const titles: string[] = [];

    const collectTitles = (nodeList: OutlineNodeType[]) => {
      nodeList.forEach(node => {
        if (node.title) {
          titles.push(node.title.trim());
        }
        if (node.children && node.children.length > 0) {
          collectTitles(node.children);
        }
      });
    };

    collectTitles(nodes);
    return titles;
  }, []);

  // 根据标题查找现有节点
  const findNodeByTitle = useCallback((nodes: OutlineNodeType[], title: string): OutlineNodeType | null => {
    const trimmedTitle = title.trim();

    const searchNodes = (nodeList: OutlineNodeType[]): OutlineNodeType | null => {
      for (const node of nodeList) {
        if (node.title && node.title.trim() === trimmedTitle) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = searchNodes(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    return searchNodes(nodes);
  }, []);

  // 生成唯一标题
  const generateUniqueTitle = useCallback((baseTitle: string, existingTitles: string[]): string => {
    if (!existingTitles.includes(baseTitle)) {
      return baseTitle;
    }

    // 智能后缀策略
    const strategies = [
      (title: string, counter: number) => `${title} (${counter})`,
      (title: string, counter: number) => `${title} - 副本${counter}`,
      (title: string, counter: number) => `${title}_${counter}`,
      (title: string, counter: number) => `新${title}${counter}`
    ];

    for (const strategy of strategies) {
      for (let i = 1; i <= 99; i++) {
        const newTitle = strategy(baseTitle, i);
        if (!existingTitles.includes(newTitle)) {
          return newTitle;
        }
      }
    }

    // 最后的备选方案
    return `${baseTitle}_${Date.now()}`;
  }, []);

  // 重名检测逻辑
  const detectDuplicateNodes = useCallback((changes: NodeChange[]): DuplicateNodeInfo[] => {
    console.log('🔍 开始重名检测:', {
      changesLength: changes.length,
      outlineExists: !!outline,
      outlineNodesExists: !!outline?.nodes,
      outlineNodesLength: outline?.nodes?.length || 0
    });

    if (!outline?.nodes) {
      console.log('⚠️ 重名检测跳过：outline.nodes不存在');
      return [];
    }

    const duplicates: DuplicateNodeInfo[] = [];
    const existingTitles = getAllNodeTitles(outline.nodes);

    console.log('📋 现有节点标题列表:', existingTitles);

    changes.forEach(change => {
      // 检查创建和更新操作
      if ((change.type === 'create' || change.type === 'update') && change.data?.title) {
        const title = change.data.title.trim();
        console.log(`🔍 检查${change.type === 'create' ? '创建' : '更新'}节点 "${title}" 是否重名...`);

        const existingNode = findNodeByTitle(outline.nodes, title);

        // 对于更新操作，需要排除自身
        if (existingNode && (change.type === 'create' || existingNode.id !== change.nodeId)) {
          console.log(`🚨 发现重名节点: "${title}"`, existingNode);
          duplicates.push({
            changeId: change.nodeId,
            generatedTitle: title,
            existingNode: existingNode,
            suggestedNewTitle: generateUniqueTitle(title, existingTitles),
            conflictType: 'exact_match'
          });
        } else if (existingNode && change.type === 'update' && existingNode.id === change.nodeId) {
          console.log(`✅ 更新节点 "${title}" - 更新自身，无冲突`);
        } else {
          console.log(`✅ 节点 "${title}" 无重名冲突`);
        }
      } else {
        console.log('⏭️ 跳过非创建/更新节点或无标题节点:', change);
      }
    });

    console.log('🔍 重名检测完成:', {
      duplicatesCount: duplicates.length,
      duplicates: duplicates
    });

    return duplicates;
  }, [outline, getAllNodeTitles, findNodeByTitle, generateUniqueTitle]);

  useEffect(() => {
    // 检测重名冲突
    const duplicates = detectDuplicateNodes(changes);
    setDuplicateConflicts(duplicates);

    // 如果有冲突，显示解决界面
    if (duplicates.length > 0) {
      setShowConflictResolver(true);
      console.log('🔍 检测到重名节点:', duplicates);
    } else {
      setShowConflictResolver(false);
    }

    // 将变更转换为预览节点映射
    const nodeMap = new Map<string, NodeChange>();
    changes.forEach(change => {
      if (change.type === 'create') {
        nodeMap.set(change.nodeId, change);
      }
    });
    setPreviewNodes(nodeMap);
  }, [changes, detectDuplicateNodes]);

  // 获取节点图标
  const getNodeIcon = (type?: string) => {
    switch (type) {
      case 'volume':
        return '📚';
      case 'event':
        return '⚡';
      case 'chapter':
        return '📖';
      case 'plot':
        return '🎭';
      case 'dialogue':
        return '💬';
      case 'scene':
        return '🎬';
      case 'section':
        return '📝';
      default:
        return '📄';
    }
  };

  // 剧情类型标签转换
  const getPlotTypeLabel = (plotType: PlotType): string => {
    const labels = {
      'conflict': '冲突',
      'twist': '转折',
      'climax': '高潮',
      'resolution': '解决'
    };
    return labels[plotType] || plotType;
  };

  // 安全的字段访问
  const safeGetField = (nodeData: any, fieldPath: string, defaultValue: any = null) => {
    try {
      return fieldPath.split('.').reduce((obj, key) => obj?.[key], nodeData) ?? defaultValue;
    } catch {
      return defaultValue;
    }
  };

  // 章节预览组件（蓝色主题）
  const ChapterPreview: React.FC<{nodeData: Partial<OutlineNodeType>}> = ({ nodeData }) => (
    <div className="chapter-preview">
      <div className="preview-title">{nodeData.title}</div>
      {nodeData.chapterStyle && (
        <div className="chapter-field">
          <span className="field-label">写作风格：</span>
          <span className="field-value">{nodeData.chapterStyle}</span>
        </div>
      )}
      {nodeData.chapterTechniques && nodeData.chapterTechniques.length > 0 && (
        <div className="chapter-field">
          <span className="field-label">写作手法：</span>
          <div className="techniques-tags">
            {nodeData.chapterTechniques.map((technique, index) => (
              <span key={index} className="technique-tag">{technique}</span>
            ))}
          </div>
        </div>
      )}
      {nodeData.chapterGoals && (
        <div className="chapter-field">
          <span className="field-label">章节目标：</span>
          <span className="field-value">{nodeData.chapterGoals}</span>
        </div>
      )}
      {/* 新增：节奏阶段 */}
      {nodeData.rhythmPhase && (
        <div className="chapter-field">
          <span className="field-label">节奏阶段：</span>
          <span className="field-value rhythm-phase">{nodeData.rhythmPhase}</span>
        </div>
      )}
      {/* 新增：节奏指导 */}
      {nodeData.rhythmGuidance && (
        <div className="chapter-field">
          <span className="field-label">节奏指导：</span>
          <div className="field-value guidance-text">{nodeData.rhythmGuidance}</div>
        </div>
      )}
      {/* 新增：创作指导 */}
      {nodeData.creativeNotes && (
        <div className="chapter-field">
          <span className="field-label">创作指导：</span>
          <div className="field-value creative-notes">{nodeData.creativeNotes}</div>
        </div>
      )}
      {nodeData.description && (
        <div className="description">{nodeData.description}</div>
      )}
    </div>
  );

  // 剧情预览组件（橙色主题）
  const PlotPreview: React.FC<{nodeData: Partial<OutlineNodeType>}> = ({ nodeData }) => (
    <div className="plot-preview">
      <div className="preview-title">{nodeData.title}</div>
      {nodeData.plotType && (
        <div className="plot-field">
          <span className="field-label">剧情类型：</span>
          <span className="field-value">{getPlotTypeLabel(nodeData.plotType)}</span>
        </div>
      )}
      {nodeData.plotPoints && nodeData.plotPoints.length > 0 && (
        <div className="plot-field">
          <span className="field-label">剧情点：</span>
          <div className="plot-points-list">
            {nodeData.plotPoints.slice(0, 3).map((point, index) => (
              <div key={point.id} className="plot-point">
                {index + 1}. {point.content}
              </div>
            ))}
            {nodeData.plotPoints.length > 3 && (
              <div className="more-points">...还有{nodeData.plotPoints.length - 3}个剧情点</div>
            )}
          </div>
        </div>
      )}
      {nodeData.relatedCharacters && nodeData.relatedCharacters.length > 0 && (
        <div className="plot-field">
          <span className="field-label">关联角色：</span>
          <span className="field-value">{nodeData.relatedCharacters.join(', ')}</span>
        </div>
      )}
      {nodeData.description && (
        <div className="description">{nodeData.description}</div>
      )}
    </div>
  );

  // 对话预览组件（绿色主题）
  const DialoguePreview: React.FC<{nodeData: Partial<OutlineNodeType>}> = ({ nodeData }) => (
    <div className="dialogue-preview">
      <div className="preview-title">{nodeData.title}</div>
      {nodeData.dialogueScene && (
        <div className="dialogue-field">
          <span className="field-label">场景：</span>
          <span className="field-value">{nodeData.dialogueScene}</span>
        </div>
      )}
      {nodeData.participants && nodeData.participants.length > 0 && (
        <div className="dialogue-field">
          <span className="field-label">参与角色：</span>
          <div className="participants-tags">
            {nodeData.participants.map((participant, index) => {
              // 安全处理participant，确保它是可渲染的字符串
              const displayParticipant = typeof participant === 'string'
                ? participant
                : typeof participant === 'object' && participant !== null
                  ? (participant as any).name || (participant as any).title || JSON.stringify(participant)
                  : String(participant);

              return (
                <span key={index} className="participant-tag">{displayParticipant}</span>
              );
            })}
          </div>
        </div>
      )}
      {nodeData.dialoguePurpose && (
        <div className="dialogue-field">
          <span className="field-label">对话目的：</span>
          <span className="field-value">{nodeData.dialoguePurpose}</span>
        </div>
      )}
      {nodeData.dialogueContent && nodeData.dialogueContent.length > 0 && (
        <div className="dialogue-field">
          <span className="field-label">对话内容：</span>
          <div className="dialogue-content-preview">
            {nodeData.dialogueContent.slice(0, 2).map((item, index) => (
              <div key={item.id} className="dialogue-item">
                <strong>{item.speaker}:</strong> {item.content}
              </div>
            ))}
            {nodeData.dialogueContent.length > 2 && (
              <div className="more-content">...还有{nodeData.dialogueContent.length - 2}条对话</div>
            )}
          </div>
        </div>
      )}
      {nodeData.description && (
        <div className="description">{nodeData.description}</div>
      )}
    </div>
  );

  // 处理冲突解决
  const handleConflictResolution = useCallback((resolution: ConflictResolution) => {
    console.log('🔧 处理冲突解决:', resolution);

    // 1. 根据用户选择修改changes数据
    const updatedChanges = changes.map(change => {
      if (change.nodeId === resolution.nodeId) {
        switch (resolution.action) {
          case 'update':
            // 保持原有的update操作，但确保数据完整
            return {
              ...change,
              data: {
                ...change.data,
                title: change.data?.title || '更新节点'
              }
            };
          case 'create_with_suffix':
            // 修改标题并转换为create操作
            return {
              ...change,
              type: 'create' as const,
              nodeId: `new-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // 确保唯一ID
              data: {
                ...change.data,
                title: resolution.newTitle || `${change.data?.title} (1)`
              }
            };
          case 'skip':
            // 返回null，稍后过滤掉
            return null;
          default:
            return change;
        }
      }
      return change;
    }).filter(Boolean) as NodeChange[]; // 过滤掉null值

    console.log('🔧 冲突解决后的changes:', updatedChanges);

    // 2. 通知父组件更新数据
    if (onConflictResolved) {
      onConflictResolved(updatedChanges);
    }

    // 3. 关闭冲突解决界面
    setShowConflictResolver(false);
    setDuplicateConflicts([]);

    // 4. 显示成功反馈
    console.log('✅ 冲突解决完成');
  }, [changes, onConflictResolved]);

  // 测试重名检测功能
  const handleTestDuplicateDetection = useCallback(() => {
    console.log('🧪 开始测试重名检测功能...');

    // 创建一个测试用的重名节点
    const testChanges: NodeChange[] = [
      {
        type: 'create',
        nodeId: 'test-duplicate-' + Date.now(),
        data: {
          title: '测试重名节点',
          description: '这是一个用于测试重名检测的节点',
          type: 'chapter'
        }
      }
    ];

    // 手动触发重名检测
    const duplicates = detectDuplicateNodes(testChanges);

    if (duplicates.length > 0) {
      console.log('✅ 重名检测正常工作，发现冲突:', duplicates);
      setDuplicateConflicts(duplicates);
      setShowConflictResolver(true);
    } else {
      console.log('ℹ️ 没有发现重名冲突，这可能是正常的');
      // 显示一个临时的测试冲突
      const testDuplicate: DuplicateNodeInfo = {
        changeId: testChanges[0].nodeId,
        generatedTitle: '测试重名节点',
        existingNode: {
          id: 'existing-test',
          title: '测试重名节点',
          type: 'plot',
          children: []
        } as OutlineNodeType,
        suggestedNewTitle: '测试重名节点 (1)',
        conflictType: 'exact_match'
      };

      setDuplicateConflicts([testDuplicate]);
      setShowConflictResolver(true);
      console.log('🧪 显示测试冲突界面');
    }
  }, [detectDuplicateNodes]);

  // 渲染重名警告卡片
  const renderDuplicateWarningCard = (duplicate: DuplicateNodeInfo) => {
    return (
      <div key={duplicate.changeId} className="duplicate-warning-card">
        <div className="warning-header">
          <span className="warning-icon">⚠️</span>
          <h4>发现重名节点</h4>
        </div>

        <div className="conflict-details">
          <div className="existing-node">
            <span className="label">现有节点：</span>
            <span className="title">{duplicate.existingNode.title}</span>
            <span className="type">({duplicate.existingNode.type})</span>
          </div>

          <div className="generated-node">
            <span className="label">AI生成：</span>
            <span className="title">{duplicate.generatedTitle}</span>
          </div>
        </div>

        <div className="resolution-options">
          <button
            className="option-btn update"
            onClick={() => handleConflictResolution({
              nodeId: duplicate.changeId,
              action: 'update'
            })}
            title="用AI生成的内容更新现有节点"
          >
            <span className="btn-icon">🔄</span>
            更新现有节点
          </button>

          <button
            className="option-btn create-new"
            onClick={() => handleConflictResolution({
              nodeId: duplicate.changeId,
              action: 'create_with_suffix',
              newTitle: duplicate.suggestedNewTitle
            })}
            title={`创建新节点：${duplicate.suggestedNewTitle}`}
          >
            <span className="btn-icon">➕</span>
            创建为"{duplicate.suggestedNewTitle}"
          </button>

          <button
            className="option-btn skip"
            onClick={() => handleConflictResolution({
              nodeId: duplicate.changeId,
              action: 'skip'
            })}
            title="跳过此节点，不进行任何操作"
          >
            <span className="btn-icon">⏭️</span>
            跳过此节点
          </button>
        </div>
      </div>
    );
  };

  // 渲染冲突解决界面
  const renderConflictResolver = () => {
    if (!showConflictResolver || duplicateConflicts.length === 0) {
      return null;
    }

    return (
      <div className="conflict-resolver">
        <div className="resolver-header">
          <h3>🔍 发现 {duplicateConflicts.length} 个重名节点</h3>
          <p>请选择如何处理这些重名冲突：</p>
        </div>

        <div className="conflicts-list">
          {duplicateConflicts.map(duplicate => renderDuplicateWarningCard(duplicate))}
        </div>

        <div className="resolver-actions">
          <button
            className="batch-btn create-all"
            onClick={() => {
              duplicateConflicts.forEach(dup => {
                handleConflictResolution({
                  nodeId: dup.changeId,
                  action: 'create_with_suffix',
                  newTitle: dup.suggestedNewTitle
                });
              });
            }}
          >
            全部创建新节点
          </button>

          <button
            className="batch-btn update-all"
            onClick={() => {
              duplicateConflicts.forEach(dup => {
                handleConflictResolution({
                  nodeId: dup.changeId,
                  action: 'update'
                });
              });
            }}
          >
            全部更新现有
          </button>

          <button
            className="batch-btn skip-all"
            onClick={() => {
              duplicateConflicts.forEach(dup => {
                handleConflictResolution({
                  nodeId: dup.changeId,
                  action: 'skip'
                });
              });
            }}
          >
            全部跳过
          </button>
        </div>
      </div>
    );
  };

  // 渲染预览节点
  const renderPreviewNode = (change: NodeChange) => {
    const isSelected = selectedChanges.includes(change.nodeId);
    const nodeData = change.data || {};
    const type = nodeData.type || 'scene';

    // 根据节点类型渲染不同的预览内容
    const renderNodeContent = () => {
      switch (type) {
        case 'chapter':
          return <ChapterPreview nodeData={nodeData} />;
        case 'plot':
          return <PlotPreview nodeData={nodeData} />;
        case 'dialogue':
          return <DialoguePreview nodeData={nodeData} />;
        default:
          // 兼容旧的节点类型
          return (
            <div className="generic-preview">
              <div className="preview-title">{nodeData.title || '新节点'}</div>
              {nodeData.description && (
                <div className="description">{nodeData.description}</div>
              )}
              {nodeData.creativeNotes && (
                <div className="creative-notes">
                  <div className="creative-notes-header">
                    <span className="creative-notes-icon">💡</span>
                    <span className="creative-notes-label">创作建议</span>
                  </div>
                  <div className="creative-notes-content">{nodeData.creativeNotes}</div>
                </div>
              )}
            </div>
          );
      }
    };

    return (
      <div
        key={change.nodeId}
        className={`preview-node ${isSelected ? 'selected' : ''} ${type}-preview-container`}
        onClick={() => onToggleChange(change.nodeId, !isSelected)}
        title={nodeData.description || nodeData.title}
      >
        <div className="preview-node-content">
          <div className="preview-node-icon">
            <span className="plus-icon">+</span>
            <span className="node-type-icon">{getNodeIcon(type)}</span>
          </div>
          <div className="preview-node-text">
            {renderNodeContent()}
          </div>
          <div className="preview-node-checkbox">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onToggleChange(change.nodeId, e.target.checked)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      </div>
    );
  };

  // 根据父节点ID查找插入位置
  const findInsertPosition = (parentId?: string) => {
    if (!parentId || !outline?.nodes) {
      return { parent: null, index: outline?.nodes?.length || 0 };
    }

    // 递归查找父节点
    const findParent = (nodes: OutlineNodeType[]): OutlineNodeType | null => {
      for (const node of nodes) {
        if (node.id === parentId) {
          return node;
        }
        if (node.children) {
          const found = findParent(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const parent = findParent(outline.nodes);
    return {
      parent,
      index: parent?.children?.length || 0
    };
  };

  // 渲染预览节点列表
  const renderPreviewNodes = () => {
    const createChanges = changes.filter(change => change.type === 'create');

    if (createChanges.length === 0) {
      return null;
    }

    return (
      <div className="preview-nodes-container">
        <div className="preview-nodes-header">
          <h4>📋 待创建的节点</h4>
          <div className="preview-nodes-count">
            {createChanges.length} 个新节点
          </div>
        </div>

        <div className="preview-nodes-list">
          {createChanges.map(change => renderPreviewNode(change))}
        </div>

        <div className="preview-nodes-actions">
          <button
            className="preview-action-btn select-all"
            onClick={() => {
              const allSelected = createChanges.every(change =>
                selectedChanges.includes(change.nodeId)
              );
              createChanges.forEach(change => {
                onToggleChange(change.nodeId, !allSelected);
              });
            }}
          >
            {createChanges.every(change => selectedChanges.includes(change.nodeId))
              ? '取消全选'
              : '全选'
            }
          </button>

          {/* 测试按钮 - 仅在开发环境显示 */}
          {process.env.NODE_ENV === 'development' && (
            <button
              className="preview-action-btn test-duplicate"
              onClick={handleTestDuplicateDetection}
              style={{
                background: '#FF8C00',
                color: 'white',
                marginLeft: '8px'
              }}
            >
              🧪 测试重名检测
            </button>
          )}
        </div>
      </div>
    );
  };

  // 添加调试日志
  console.log('🔍 NodePreviewOverlay Debug:', {
    changesLength: changes.length,
    changes: changes,
    selectedChanges: selectedChanges,
    selectedChangesLength: selectedChanges.length,
    changesDetail: changes.map(c => ({ nodeId: c.nodeId, type: c.type, hasData: !!c.data }))
  });

  if (changes.length === 0) {
    console.log('⚠️ NodePreviewOverlay: changes数组为空');
    return (
      <div className="node-preview-overlay">
        <div className="preview-nodes-container">
          <div className="preview-nodes-header">
            <h4>⚠️ 节点清单为空</h4>
          </div>
          <div style={{ padding: '20px', color: '#666', textAlign: 'center' }}>
            <p>📋 当前没有待创建的节点</p>
            <p style={{ fontSize: '14px', marginTop: '10px' }}>
              这可能是因为：
            </p>
            <ul style={{ textAlign: 'left', marginTop: '10px', paddingLeft: '20px' }}>
              <li>AI没有生成新的节点建议</li>
              <li>当前请求不需要创建新节点</li>
              <li>数据传递过程中出现问题</li>
            </ul>
            <p style={{ fontSize: '12px', marginTop: '15px', color: '#999' }}>
              调试信息: selectedChanges.length = {selectedChanges.length}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="node-preview-overlay">
      {/* 优先显示冲突解决界面 */}
      {showConflictResolver ? renderConflictResolver() : renderPreviewNodes()}
    </div>
  );
};

export default NodePreviewOverlay;
