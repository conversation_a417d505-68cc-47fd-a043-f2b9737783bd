/* 框架提取按钮样式 */
.framework-extract-button {
  /* 按钮基础样式 */
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  cursor: pointer;

  /* 阴影效果 */
  box-shadow:
    0 4px 12px rgba(255, 107, 53, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Panel中的按钮样式 */
.framework-extract-panel {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
}

/* 悬停效果 */
.framework-extract-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 6px 20px rgba(255, 107, 53, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 激活状态 */
.framework-extract-button.active {
  background: linear-gradient(135deg, #E55A2B 0%, #D4841F 100%);
  box-shadow:
    0 6px 20px rgba(255, 107, 53, 0.6),
    0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 禁用状态 */
.framework-extract-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 图标容器 */
.framework-extract-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.framework-extract-button:hover .framework-extract-icon {
  transform: rotate(5deg);
}

.framework-extract-button.active .framework-extract-icon {
  transform: rotate(10deg);
}

/* 活跃状态指示器 */
.framework-extract-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10B981;
  display: flex;
  align-items: center;
  justify-content: center;
}

.framework-extract-indicator .pulse-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  animation: pulse 2s infinite;
}

/* 悬停提示 */
.framework-extract-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.framework-extract-button:hover .framework-extract-tooltip {
  opacity: 1;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 按钮组合样式 - 当与AI助手按钮一起使用时 */
.button-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.button-group .framework-extract-button,
.button-group .assistant-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .framework-extract-button {
    width: 44px;
    height: 44px;
  }
  
  .framework-extract-icon {
    width: 20px;
    height: 20px;
  }
  
  .framework-extract-tooltip {
    font-size: 11px;
    padding: 3px 6px;
  }
}
