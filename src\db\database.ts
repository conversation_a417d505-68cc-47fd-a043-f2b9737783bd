import Dexie, { Table } from 'dexie';

/**
 * 书籍模型
 */
export interface Book {
  id?: string;
  title: string;
  description: string;
  coverImage?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 章节模型
 */
export interface Chapter {
  id?: string;
  bookId: string;
  title: string;
  content: string;
  order: number;
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 写作统计模型
 */
export interface WritingStats {
  id?: string;
  bookId: string;
  date: string;
  wordCount: number;
  timeSpent: number; // 以分钟为单位
}

/**
 * 应用数据库
 */
class AppDatabase extends Dexie {
  books!: Table<Book, string>;
  chapters!: Table<Chapter, string>;
  writingStats!: Table<WritingStats, string>;

  constructor() {
    super('AINovelPlatform');

    // 初始版本
    this.version(1).stores({
      books: 'id, title, createdAt, updatedAt',
      chapters: 'id, bookId, title, order, wordCount, createdAt, updatedAt',
      writingStats: 'id, bookId, date'
    });

    // 版本2：添加复合索引 [bookId+date]
    this.version(2).stores({
      books: 'id, title, createdAt, updatedAt',
      chapters: 'id, bookId, title, order, wordCount, createdAt, updatedAt',
      writingStats: 'id, bookId, date, [bookId+date]'
    });
  }
}

/**
 * 数据库实例
 */
export const db = new AppDatabase();
