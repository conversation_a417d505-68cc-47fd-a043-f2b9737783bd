"use client";

/**
 * 章节分段选项
 */
export interface SegmentOptions {
  /**
   * 最大段落长度（字符数）
   * 默认值：2000
   */
  maxSegmentLength?: number;

  /**
   * 最小段落长度（字符数）
   * 默认值：500
   */
  minSegmentLength?: number;

  /**
   * 是否添加段落编号
   * 默认值：true
   */
  addSegmentNumber?: boolean;

  /**
   * 是否尝试保持人物描述的完整性
   * 默认值：true
   */
  preserveCharacterDescriptions?: boolean;
}

/**
 * 默认分段选项
 */
const DEFAULT_OPTIONS: SegmentOptions = {
  maxSegmentLength: 2000,
  minSegmentLength: 500,
  addSegmentNumber: true,
  preserveCharacterDescriptions: true
};

/**
 * 章节分段处理工具
 * 将长篇章节内容分割成适当大小的段落，以便AI模型能够更好地理解和处理
 */
export class ChapterSegmenter {
  private options: SegmentOptions;

  /**
   * 创建章节分段处理工具
   * @param options 分段选项
   */
  constructor(options?: SegmentOptions) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  /**
   * 分段处理章节内容
   * @param chapterContent 章节内容
   * @returns 分段后的章节内容数组
   */
  segmentChapter(chapterContent: string): string[] {
    try {
      // 提取章节标题（如果有）
      let chapterTitle = '';
      const titleMatch = chapterContent.match(/^#\s+(.+?)(?:\n|$)/);
      if (titleMatch) {
        chapterTitle = titleMatch[1].trim();
        // 移除标题行，避免重复
        chapterContent = chapterContent.replace(/^#\s+(.+?)(?:\n|$)/, '').trim();
      }

      // 1. 按句子分割整个章节内容（改为10句一段的分割方式）
      // 修复：移除 \s* 以保留换行符和空白字符
      const sentences = chapterContent.split(/(?<=[。！？.!?])/).filter(s => s.trim());

      // 2. 每10句组成一段
      const segments: string[] = [];
      const sentencesPerSegment = 10;

      for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
        const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
        // 修复：直接连接句子，保持原有换行结构
        let segmentText = segmentSentences.join('').trim();

        // 规范化换行：将多个连续换行符替换为双换行符
        segmentText = segmentText.replace(/\n{3,}/g, '\n\n');

        if (segmentText) {
          segments.push(segmentText);
        }
      }

      // 如果没有分割出任何段落，回退到原始内容
      if (segments.length === 0 && chapterContent.trim()) {
        segments.push(chapterContent);
      }

      // 3. 为每个段落添加上下文信息
      if (this.options.addSegmentNumber) {
        return segments.map((segment, index) => {
          let header = '';

          // 添加章节标题（如果有）
          if (chapterTitle) {
            header += `# 当前剧情，第${chapterTitle}\n\n`;
          }

          // 添加段落信息
          header += `## 段落信息
          ## 当前剧情第${index + 1}个段落信息\\n`;
          header += `- 当前剧情段落段落编号: ${index + 1}/${segments.length}\n`;
          header += `- 段落字数: ${segment.length}\n\n`;

          // 添加段落内容
          header += `## 段落内容\n\n 请你查看后注意衔接和逻辑关系，避免产生冲突`;

          return header + segment;
        });
      }

      return segments;
    } catch (error) {
      console.error('章节分段处理失败:', error);
      // 回退到简单的固定长度分段
      return this.fallbackSegmentation(chapterContent);
    }
  }

  /**
   * 拆分过长的段落（改为10句一段的分割方式）
   * @param paragraph 段落内容
   * @returns 拆分后的段落数组
   */
  private splitLongParagraph(paragraph: string): string[] {
    const result: string[] = [];

    // 按句子分割
    // 修复：移除 \s* 以保留换行符和空白字符
    const sentences = paragraph.split(/(?<=[。！？.!?])/).filter(s => s.trim());

    // 每10句组成一段
    const sentencesPerSegment = 10;

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      // 修复：直接连接句子，保持原有换行结构
      let segmentText = segmentSentences.join('').trim();

      // 规范化换行：将多个连续换行符替换为双换行符
      segmentText = segmentText.replace(/\n{3,}/g, '\n\n');

      if (segmentText) {
        result.push(segmentText);
      }
    }

    // 如果没有分割出任何段落，回退到原始段落
    if (result.length === 0 && paragraph.trim()) {
      result.push(paragraph);
    }

    return result;
  }

  /**
   * 回退到简单的固定长度分段
   * @param chapterContent 章节内容
   * @returns 分段后的章节内容数组
   */
  private fallbackSegmentation(chapterContent: string): string[] {
    const maxLength = this.options.maxSegmentLength !== undefined
      ? this.options.maxSegmentLength
      : DEFAULT_OPTIONS.maxSegmentLength!;

    const segments: string[] = [];

    // 简单地按固定长度分段
    for (let i = 0; i < chapterContent.length; i += maxLength) {
      const segment = chapterContent.substring(i, i + maxLength);
      segments.push(segment);
    }

    // 为每个段落添加上下文信息
    if (this.options.addSegmentNumber) {
      return segments.map((segment, index) => {
        let header = '';

        header += `## 段落信息
          ## 当前剧情第${index + 1}个段落信息\\n`;
          header += `- 当前剧情段落段落编号: ${index + 1}/${segments.length}\n`;
          header += `- 段落字数: ${segment.length}\n\n`;

          // 添加段落内容
          header += `## 段落内容\n\n 请你查看后注意衔接和逻辑关系，避免产生冲突`;


        return header + segment;
      });
    }

    return segments;
  }

  /**
   * 设置分段选项
   * @param options 分段选项
   */
  setOptions(options: SegmentOptions): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 获取当前分段选项
   * @returns 当前分段选项
   */
  getOptions(): SegmentOptions {
    return { ...this.options };
  }
}

/**
 * 创建章节分段处理工具
 * @param options 分段选项
 * @returns 章节分段处理工具实例
 */
export function createChapterSegmenter(options?: SegmentOptions): ChapterSegmenter {
  return new ChapterSegmenter(options);
}

export default createChapterSegmenter;
