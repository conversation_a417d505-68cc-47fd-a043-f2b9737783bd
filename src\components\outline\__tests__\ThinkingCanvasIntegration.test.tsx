import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import WorkflowIndicator from '../WorkflowIndicator';
import ThinkingModeSelector from '../ThinkingModeSelector';
import ThinkingCanvasCard from '../ThinkingCanvasCard';
import { ThinkingCanvasData } from '@/types/thinking-canvas';

// Mock Framer Motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    svg: ({ children, ...props }: any) => <svg {...props}>{children}</svg>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('思考画布UI组件集成测试', () => {
  describe('WorkflowIndicator', () => {
    it('应该正确显示工作流程步骤', () => {
      render(
        <WorkflowIndicator
          currentStep="thinking"
          isProcessing={false}
        />
      );

      expect(screen.getByText('思考')).toBeInTheDocument();
      expect(screen.getByText('生成')).toBeInTheDocument();
      expect(screen.getByText('完成')).toBeInTheDocument();
    });

    it('应该正确显示处理中状态', () => {
      render(
        <WorkflowIndicator
          currentStep="thinking"
          isProcessing={true}
        />
      );

      expect(screen.getByText(/正在.*深度分析需求/)).toBeInTheDocument();
    });

    it('应该支持步骤点击', () => {
      const onStepClick = jest.fn();
      render(
        <WorkflowIndicator
          currentStep="generating"
          isProcessing={false}
          onStepClick={onStepClick}
        />
      );

      // 点击已完成的思考步骤
      const thinkingStep = screen.getByText('思考').closest('div');
      if (thinkingStep) {
        fireEvent.click(thinkingStep);
        expect(onStepClick).toHaveBeenCalledWith('thinking');
      }
    });
  });

  describe('ThinkingModeSelector', () => {
    it('应该正确显示模式选项', () => {
      render(
        <ThinkingModeSelector
          mode="standard"
          onModeChange={() => {}}
        />
      );

      expect(screen.getByText('标准模式')).toBeInTheDocument();
      expect(screen.getByText('思考模式')).toBeInTheDocument();
      expect(screen.getByText('直接生成大纲节点')).toBeInTheDocument();
      expect(screen.getByText('先思考再写大纲')).toBeInTheDocument();
    });

    it('应该支持模式切换', () => {
      const onModeChange = jest.fn();
      render(
        <ThinkingModeSelector
          mode="standard"
          onModeChange={onModeChange}
        />
      );

      const thinkingModeButton = screen.getByText('思考模式').closest('button');
      if (thinkingModeButton) {
        fireEvent.click(thinkingModeButton);
        expect(onModeChange).toHaveBeenCalledWith('thinking');
      }
    });

    it('应该在思考模式下显示使用提示', () => {
      render(
        <ThinkingModeSelector
          mode="thinking"
          onModeChange={() => {}}
        />
      );

      expect(screen.getByText('思考模式使用提示：')).toBeInTheDocument();
      expect(screen.getByText(/AI会先生成思考画布/)).toBeInTheDocument();
    });

    it('应该支持禁用状态', () => {
      render(
        <ThinkingModeSelector
          mode="standard"
          onModeChange={() => {}}
          disabled={true}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeDisabled();
      });
    });
  });

  describe('ThinkingCanvasCard', () => {
    const mockCanvas: ThinkingCanvasData = {
      id: 'test-canvas-1',
      title: '测试思考画布',
      content: '# 创作思考\n\n## 需求分析\n这是一个测试思考画布的内容。\n\n## 实现方案\n详细的实现步骤...',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      tags: ['测试', 'AI生成'],
      metadata: {
        wordCount: 50,
        editHistory: [],
        isStarred: false
      }
    };

    it('应该正确显示思考画布信息', () => {
      render(
        <ThinkingCanvasCard
          canvas={mockCanvas}
          isExpanded={false}
        />
      );

      expect(screen.getByText('测试思考画布')).toBeInTheDocument();
      expect(screen.getByText('字数: 50')).toBeInTheDocument();
      expect(screen.getByText('测试')).toBeInTheDocument();
      expect(screen.getByText('AI生成')).toBeInTheDocument();
    });

    it('应该支持展开/折叠功能', () => {
      const onToggleExpand = jest.fn();
      render(
        <ThinkingCanvasCard
          canvas={mockCanvas}
          isExpanded={false}
          onToggleExpand={onToggleExpand}
        />
      );

      const expandButton = screen.getByTitle('展开');
      fireEvent.click(expandButton);
      expect(onToggleExpand).toHaveBeenCalled();
    });

    it('应该支持编辑功能', async () => {
      const onSave = jest.fn();
      render(
        <ThinkingCanvasCard
          canvas={mockCanvas}
          isExpanded={true}
          onSave={onSave}
        />
      );

      // 点击编辑按钮
      const editButton = screen.getByTitle('编辑');
      fireEvent.click(editButton);

      // 应该显示编辑界面
      expect(screen.getByDisplayValue('测试思考画布')).toBeInTheDocument();
      expect(screen.getByDisplayValue(mockCanvas.content)).toBeInTheDocument();

      // 修改标题
      const titleInput = screen.getByDisplayValue('测试思考画布');
      fireEvent.change(titleInput, { target: { value: '修改后的标题' } });

      // 点击保存按钮
      const saveButton = screen.getByTitle('保存');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(onSave).toHaveBeenCalledWith(
          expect.objectContaining({
            title: '修改后的标题'
          })
        );
      });
    });

    it('应该支持删除功能', () => {
      const onDelete = jest.fn();
      // Mock window.confirm
      window.confirm = jest.fn(() => true);

      render(
        <ThinkingCanvasCard
          canvas={mockCanvas}
          isExpanded={true}
          onDelete={onDelete}
        />
      );

      const deleteButton = screen.getByTitle('删除');
      fireEvent.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith('确定要删除这个思考画布吗？');
      expect(onDelete).toHaveBeenCalledWith('test-canvas-1');
    });

    it('应该支持导出功能', () => {
      // Mock URL.createObjectURL and document.createElement
      global.URL.createObjectURL = jest.fn(() => 'mock-url');
      global.URL.revokeObjectURL = jest.fn();
      
      const mockLink = {
        href: '',
        download: '',
        click: jest.fn(),
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      jest.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink as any);
      jest.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink as any);

      render(
        <ThinkingCanvasCard
          canvas={mockCanvas}
          isExpanded={true}
        />
      );

      const exportButton = screen.getByTitle('导出');
      fireEvent.click(exportButton);

      expect(document.createElement).toHaveBeenCalledWith('a');
      expect(mockLink.download).toBe('测试思考画布.md');
      expect(mockLink.click).toHaveBeenCalled();
    });
  });

  describe('组件集成测试', () => {
    it('应该正确处理思考画布工作流程', async () => {
      const mockWorkflowState = {
        currentStep: 'thinking' as const,
        thinkingCanvas: null,
        generatedChanges: null,
        error: null,
        isProcessing: true
      };

      // 这里可以添加更复杂的集成测试
      // 测试模式切换、工作流程状态变化、思考画布生成等
      expect(mockWorkflowState.currentStep).toBe('thinking');
      expect(mockWorkflowState.isProcessing).toBe(true);
    });
  });
});
