"use client";

import React, { useState, useCallback } from 'react';
import { OutlineNodeType, DialogueItem } from '../../../types/outline';

interface DialogueEditorProps {
  node: OutlineNodeType;
  onChange: (updatedNode: OutlineNodeType) => void;
}

/**
 * 对话编辑器组件
 * 专门用于编辑对话类型的节点，提供对话特有的字段编辑功能
 */
const DialogueEditor: React.FC<DialogueEditorProps> = ({ node, onChange }) => {
  // 本地状态管理
  const [title, setTitle] = useState(node.title || '');
  const [dialogueScene, setDialogueScene] = useState(node.dialogueScene || '');
  const [participants, setParticipants] = useState<string[]>(node.participants || []);
  const [dialoguePurpose, setDialoguePurpose] = useState(node.dialoguePurpose || '');
  const [dialogueContent, setDialogueContent] = useState<DialogueItem[]>(node.dialogueContent || []);
  const [description, setDescription] = useState(node.description || '');

  // 更新节点数据
  const updateNode = useCallback((updates: Partial<OutlineNodeType>) => {
    const updatedNode = { ...node, ...updates };
    onChange(updatedNode);
  }, [node, onChange]);

  // 处理标题变化
  const handleTitleChange = useCallback((value: string) => {
    setTitle(value);
    updateNode({ title: value });
  }, [updateNode]);

  // 处理场景变化
  const handleSceneChange = useCallback((value: string) => {
    setDialogueScene(value);
    updateNode({ dialogueScene: value });
  }, [updateNode]);

  // 处理目的变化
  const handlePurposeChange = useCallback((value: string) => {
    setDialoguePurpose(value);
    updateNode({ dialoguePurpose: value });
  }, [updateNode]);

  // 处理描述变化
  const handleDescriptionChange = useCallback((value: string) => {
    setDescription(value);
    updateNode({ description: value });
  }, [updateNode]);

  // 添加参与角色
  const addParticipant = useCallback((participant: string) => {
    if (participant.trim() && !participants.includes(participant.trim())) {
      const newParticipants = [...participants, participant.trim()];
      setParticipants(newParticipants);
      updateNode({ participants: newParticipants });
    }
  }, [participants, updateNode]);

  // 移除参与角色
  const removeParticipant = useCallback((index: number) => {
    const newParticipants = participants.filter((_, i) => i !== index);
    setParticipants(newParticipants);
    updateNode({ participants: newParticipants });
  }, [participants, updateNode]);

  // 添加对话项
  const addDialogueItem = useCallback(() => {
    const newDialogueItem: DialogueItem = {
      id: `dialogue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      speaker: participants[0] || '',
      content: '',
      emotion: '',
      action: ''
    };
    const newDialogueContent = [...dialogueContent, newDialogueItem];
    setDialogueContent(newDialogueContent);
    updateNode({ dialogueContent: newDialogueContent });
  }, [dialogueContent, participants, updateNode]);

  // 更新对话项
  const updateDialogueItem = useCallback((index: number, updates: Partial<DialogueItem>) => {
    const newDialogueContent = dialogueContent.map((item, i) => 
      i === index ? { ...item, ...updates } : item
    );
    setDialogueContent(newDialogueContent);
    updateNode({ dialogueContent: newDialogueContent });
  }, [dialogueContent, updateNode]);

  // 删除对话项
  const removeDialogueItem = useCallback((index: number) => {
    const newDialogueContent = dialogueContent.filter((_, i) => i !== index);
    setDialogueContent(newDialogueContent);
    updateNode({ dialogueContent: newDialogueContent });
  }, [dialogueContent, updateNode]);

  // 移动对话项
  const moveDialogueItem = useCallback((fromIndex: number, toIndex: number) => {
    const newDialogueContent = [...dialogueContent];
    const [movedItem] = newDialogueContent.splice(fromIndex, 1);
    newDialogueContent.splice(toIndex, 0, movedItem);
    setDialogueContent(newDialogueContent);
    updateNode({ dialogueContent: newDialogueContent });
  }, [dialogueContent, updateNode]);

  return (
    <div className="space-y-4 dialogue-editor">
      {/* 对话标题 */}
      <div>
        <label className="flex items-center text-sm font-medium text-green-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          对话场景
        </label>
        <input
          type="text"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          className="w-full px-3 py-2 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 bg-gradient-to-r from-green-50 to-emerald-50"
          placeholder="输入对话场景标题"
        />
      </div>

      {/* 对话场景 */}
      <div>
        <label className="flex items-center text-sm font-medium text-green-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          场景描述
        </label>
        <input
          type="text"
          value={dialogueScene}
          onChange={(e) => handleSceneChange(e.target.value)}
          className="w-full px-3 py-2 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 bg-gradient-to-r from-green-50 to-emerald-50"
          placeholder="描述对话发生的场景，如：客厅、办公室、咖啡厅等"
        />
      </div>

      {/* 参与角色 */}
      <div>
        <label className="flex items-center text-sm font-medium text-green-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          参与角色
        </label>
        <div className="space-y-2">
          {/* 角色标签显示 */}
          {participants.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {participants.map((participant, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200"
                >
                  {participant}
                  <button
                    onClick={() => removeParticipant(index)}
                    className="ml-1 text-green-600 hover:text-green-800"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          )}
          {/* 添加角色输入 */}
          <input
            type="text"
            className="w-full px-3 py-2 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 text-sm bg-gradient-to-r from-green-50 to-emerald-50"
            placeholder="输入角色名称后按回车添加"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const input = e.target as HTMLInputElement;
                addParticipant(input.value);
                input.value = '';
              }
            }}
          />
        </div>
      </div>

      {/* 对话目的 */}
      <div>
        <label className="flex items-center text-sm font-medium text-green-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
          </svg>
          对话目的
        </label>
        <textarea
          value={dialoguePurpose}
          onChange={(e) => handlePurposeChange(e.target.value)}
          className="w-full px-3 py-2 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 min-h-[60px] text-sm bg-gradient-to-r from-green-50 to-emerald-50"
          placeholder="这段对话要达成什么目的，如：信息传递、情感表达、冲突升级等"
        />
      </div>

      {/* 对话内容 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="flex items-center text-sm font-medium text-green-700">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            对话内容
          </label>
          <button
            onClick={addDialogueItem}
            className="px-3 py-1 text-xs bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            disabled={participants.length === 0}
          >
            + 添加对话
          </button>
        </div>
        
        <div className="space-y-3 max-h-60 overflow-y-auto">
          {dialogueContent.map((item, index) => (
            <div key={item.id} className="p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-start justify-between mb-2">
                <select
                  value={item.speaker}
                  onChange={(e) => updateDialogueItem(index, { speaker: e.target.value })}
                  className="text-sm font-medium border border-green-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-green-400 bg-white"
                >
                  <option value="">选择说话者</option>
                  {participants.map((participant) => (
                    <option key={participant} value={participant}>{participant}</option>
                  ))}
                </select>
                
                <div className="flex space-x-1">
                  {index > 0 && (
                    <button
                      onClick={() => moveDialogueItem(index, index - 1)}
                      className="text-green-600 hover:text-green-800"
                      title="上移"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                      </svg>
                    </button>
                  )}
                  {index < dialogueContent.length - 1 && (
                    <button
                      onClick={() => moveDialogueItem(index, index + 1)}
                      className="text-green-600 hover:text-green-800"
                      title="下移"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  )}
                  <button
                    onClick={() => removeDialogueItem(index)}
                    className="text-red-600 hover:text-red-800"
                    title="删除"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
              
              <textarea
                value={item.content}
                onChange={(e) => updateDialogueItem(index, { content: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-green-200 rounded focus:outline-none focus:ring-1 focus:ring-green-400 mb-2"
                placeholder="输入对话内容"
                rows={2}
              />
              
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="text"
                  value={item.emotion || ''}
                  onChange={(e) => updateDialogueItem(index, { emotion: e.target.value })}
                  className="px-2 py-1 text-xs border border-green-200 rounded focus:outline-none focus:ring-1 focus:ring-green-400"
                  placeholder="情感状态"
                />
                <input
                  type="text"
                  value={item.action || ''}
                  onChange={(e) => updateDialogueItem(index, { action: e.target.value })}
                  className="px-2 py-1 text-xs border border-green-200 rounded focus:outline-none focus:ring-1 focus:ring-green-400"
                  placeholder="动作描述"
                />
              </div>
            </div>
          ))}
          
          {dialogueContent.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
              <p className="text-sm">暂无对话内容</p>
              {participants.length === 0 && (
                <p className="text-xs text-gray-400 mt-1">请先添加参与角色</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 对话描述 */}
      <div>
        <label className="flex items-center text-sm font-medium text-green-700 mb-2">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
          </svg>
          对话描述
        </label>
        <textarea
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          className="w-full px-3 py-2 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 min-h-[80px] text-sm bg-gradient-to-r from-green-50 to-emerald-50"
          placeholder="输入对话的详细描述"
        />
      </div>
    </div>
  );
};

export default DialogueEditor;
