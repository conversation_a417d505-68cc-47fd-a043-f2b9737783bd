/* 硬件加速优化 */
.react-flow__node {
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.react-flow__edge {
  will-change: transform;
}

.react-flow-wrapper {
  will-change: transform;
}

/* 拖拽优化 */
.react-flow__node-default.dragging {
  transition: none !important;
}

/* 节点拖拽时的样式 */
.react-flow__node.dragging {
  cursor: grabbing !important;
  z-index: 1000 !important;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* 提高节点选择器的优先级 */
.react-flow__node.selected {
  z-index: 1001 !important;
}

/* 优化边的渲染性能 */
.react-flow__edge-path {
  will-change: d;
}

/* 优化连接线的渲染性能 */
.react-flow__connection-path {
  will-change: d;
}

/* 优化背景的渲染性能 */
.react-flow__background {
  will-change: transform;
}

/* 优化控制面板的渲染性能 */
.react-flow__controls {
  will-change: transform;
}

/* 优化小地图的渲染性能 */
.react-flow__minimap {
  will-change: transform;
}
