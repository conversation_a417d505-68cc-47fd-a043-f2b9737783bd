/**
 * 统一的AI服务基类
 * 提供标准化的AI API调用方法，避免硬编码和重复逻辑
 */

import { AIResponse, AIRequestOptions } from '@/factories/ai/interfaces/IAIComponent';
import { AIConfig } from '@/services/configService';

/**
 * AI服务类型枚举
 * 用于区分双AI系统和统一管理的AI功能
 */
export enum AIServiceType {
  // 双AI系统（独立管理）
  DUAL_AI_OUTLINE = 'dual_ai_outline',
  DUAL_AI_DIALOGUE = 'dual_ai_dialogue',

  // 统一管理的AI功能
  VERTICAL_ADJUSTMENT = 'vertical_adjustment',
  PREFIX_MESSAGE = 'prefix_message',
  ANNOTATION = 'annotation',
  BRAINSTORM = 'brainstorm',
  TOOL_CALL = 'tool_call',
  BOOK_ANALYSIS = 'book_analysis',
  SHORT_STORY = 'short_story',
  WRITING = 'writing',
  CONTINUE = 'continue',
  REWRITE = 'rewrite',
  PERSONA_ANALYSIS = 'persona_analysis',
  PERSONA_OPTIMIZATION = 'persona_optimization',
  SYNOPSIS = 'synopsis',
  RHYTHM_ANALYSIS = 'rhythm_analysis'
}

/**
 * AI配置验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * AI服务基类
 * 所有AI相关服务都应该继承此类，确保统一的配置管理和API调用方式
 */
export abstract class BaseAIService {
  protected serviceName: string;
  protected serviceType: AIServiceType;

  constructor(serviceName: string, serviceType?: AIServiceType) {
    this.serviceName = serviceName;
    this.serviceType = serviceType || AIServiceType.VERTICAL_ADJUSTMENT;
  }

  /**
   * 获取AI配置
   * 统一的配置获取方法，支持降级处理
   */
  protected async getAIConfig(): Promise<AIConfig> {
    try {
      const { configService } = await import('@/services/configService');
      const config = await configService.getAIConfig();

      // 验证配置
      const validation = this.validateConfig(config);
      if (!validation.isValid) {
        console.warn(`${this.serviceName}: AI配置验证失败:`, validation.errors);
      }

      if (validation.warnings && validation.warnings.length > 0) {
        console.warn(`${this.serviceName}: AI配置警告:`, validation.warnings);
      }

      return config;
    } catch (error) {
      console.error(`${this.serviceName}: 获取AI配置失败:`, error);

      // 返回默认配置
      return {
        temperature: 0.7,
        maxTokens: 4000,
        model: 'gemini-2.5-pro-exp-03-25'
      };
    }
  }

  /**
   * 验证AI配置
   */
  protected validateConfig(config: AIConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证temperature
    if (config.temperature !== undefined) {
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      } else if (config.temperature > 1.5) {
        warnings.push('High temperature (>1.5) may produce unpredictable results');
      }
    }

    // 验证maxTokens
    if (config.maxTokens !== undefined) {
      if (config.maxTokens < 1) {
        errors.push('MaxTokens must be positive');
      } else if (config.maxTokens > 100000) {
        warnings.push('Very high maxTokens may cause performance issues');
      }
    }

    // 验证topK
    if (config.topK !== undefined) {
      if (typeof config.topK !== 'number') {
        errors.push('TopK must be a number');
      } else if (config.topK < 1 || config.topK > 100) {
        errors.push('TopK must be between 1 and 100');
      } else if (config.topK > 80) {
        warnings.push('High topK (>80) may produce very diverse but potentially inconsistent results');
      }
    }

    // 验证model
    if (config.model && typeof config.model !== 'string') {
      errors.push('Model must be a string');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * 标准化的AI调用方法
   * 使用DefaultAISenderComponent和动态配置
   */
  protected async callAI(
    messages: Array<{role: string, content: string}>,
    options?: Partial<AIRequestOptions>
  ): Promise<AIResponse> {
    try {
      console.log(`🤖 ${this.serviceName}: 开始AI调用`);

      // 动态导入DefaultAISenderComponent
      const { DefaultAISenderComponent } = await import('@/factories/ai/components/DefaultAISenderComponent');
      const aiSender = new DefaultAISenderComponent();

      // 获取配置
      const config = await this.getAIConfig();
      console.log(`🤖 ${this.serviceName}: 使用配置:`, {
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        model: config.model
      });

      // 构建请求选项
      const requestOptions: any = {
        messages,
        // 只在配置存在时才设置参数，让DefaultAISenderComponent处理默认值
        ...(config.temperature !== undefined && { temperature: config.temperature }),
        ...(config.maxTokens !== undefined && { maxTokens: config.maxTokens }),
        ...(config.topP !== undefined && { topP: config.topP }),
        ...(config.topK !== undefined && { topK: config.topK }),
        // 合并用户提供的选项
        ...options
      };

      // 发送请求
      const response = await aiSender.sendRequest('', requestOptions);

      console.log(`🤖 ${this.serviceName}: AI调用完成:`, {
        success: response.success,
        textLength: response.text?.length || 0
      });

      return response;
    } catch (error: any) {
      console.error(`❌ ${this.serviceName}: AI调用失败:`, error);
      throw error;
    }
  }

  /**
   * 流式AI调用方法
   */
  protected async callAIStreaming(
    messages: Array<{role: string, content: string}>,
    onChunk: (chunk: string) => void,
    options?: Partial<AIRequestOptions>
  ): Promise<AIResponse> {
    try {
      console.log(`🤖 ${this.serviceName}: 开始流式AI调用`);

      // 动态导入DefaultAISenderComponent
      const { DefaultAISenderComponent } = await import('@/factories/ai/components/DefaultAISenderComponent');
      const aiSender = new DefaultAISenderComponent();

      // 获取配置
      const config = await this.getAIConfig();

      // 构建请求选项
      const requestOptions: any = {
        messages,
        ...(config.temperature !== undefined && { temperature: config.temperature }),
        ...(config.maxTokens !== undefined && { maxTokens: config.maxTokens }),
        ...(config.topP !== undefined && { topP: config.topP }),
        ...(config.topK !== undefined && { topK: config.topK }),
        streaming: true,
        ...options
      };

      // 发送流式请求
      const response = await aiSender.sendStreamingRequest('', onChunk, requestOptions);

      console.log(`🤖 ${this.serviceName}: 流式AI调用完成:`, {
        success: response.success,
        textLength: response.text?.length || 0
      });

      return response;
    } catch (error: any) {
      console.error(`❌ ${this.serviceName}: 流式AI调用失败:`, error);
      throw error;
    }
  }

  /**
   * 获取服务特定的配置调整
   * 子类可以重写此方法来提供服务特定的配置调整
   */
  protected getServiceSpecificConfig(): Partial<AIRequestOptions> {
    return {};
  }

  /**
   * 构建标准化的错误响应
   */
  protected createErrorResponse(error: any): AIResponse {
    return {
      text: '',
      success: false,
      error: error.message || '未知错误'
    };
  }
}

/**
 * AI配置验证器
 * 提供独立的配置验证功能
 */
export class AIConfigValidator {
  /**
   * 验证AI配置
   */
  static validate(config: AIConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (config.temperature !== undefined) {
      if (typeof config.temperature !== 'number') {
        errors.push('Temperature must be a number');
      } else if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      } else if (config.temperature > 1.5) {
        warnings.push('High temperature (>1.5) may produce unpredictable results');
      }
    }

    if (config.maxTokens !== undefined) {
      if (typeof config.maxTokens !== 'number') {
        errors.push('MaxTokens must be a number');
      } else if (config.maxTokens < 1) {
        errors.push('MaxTokens must be positive');
      } else if (config.maxTokens > 100000) {
        warnings.push('Very high maxTokens may cause performance issues');
      }
    }

    if (config.model !== undefined && typeof config.model !== 'string') {
      errors.push('Model must be a string');
    }

    if (config.apiKey !== undefined && typeof config.apiKey !== 'string') {
      errors.push('API key must be a string');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * 检查配置是否完整
   */
  static isComplete(config: AIConfig): boolean {
    return !!(config.model && config.apiKey);
  }
}

/**
 * 统一AI服务实现
 * 继承BaseAIService，为统一管理的AI功能提供标准实现
 */
export class UnifiedAIService extends BaseAIService {
  constructor(serviceType: AIServiceType) {
    super(serviceType, serviceType);
  }

  /**
   * 为不同服务类型提供特定的配置调整
   */
  protected getServiceSpecificConfig(): Partial<AIRequestOptions> {
    switch (this.serviceType) {
      case AIServiceType.ANNOTATION:
        return { temperature: 0.5 }; // 标注需要中等温度确保准确性

      case AIServiceType.TOOL_CALL:
        return { temperature: 0.3 }; // 工具调用需要低温度确保精确性

      case AIServiceType.BRAINSTORM:
        return { temperature: 0.8 }; // 头脑风暴需要高创意性

      case AIServiceType.VERTICAL_ADJUSTMENT:
        return { temperature: 0.7 }; // 垂直调整需要平衡创意和准确性

      case AIServiceType.PREFIX_MESSAGE:
        return { temperature: 0.6 }; // 前置消息需要适度创意

      case AIServiceType.BOOK_ANALYSIS:
        return { temperature: 0.4 }; // 书籍分析需要较高准确性

      case AIServiceType.SHORT_STORY:
        return { temperature: 0.55 }; // 短篇创作需要高创意性

      case AIServiceType.WRITING:
        return { temperature: 0.7 }; // AI写作需要平衡创意和准确性

      case AIServiceType.CONTINUE:
        return { temperature: 0.7 }; // 续写需要保持风格一致性

      case AIServiceType.REWRITE:
        return { temperature: 0.6 }; // 改写需要较高准确性

      case AIServiceType.PERSONA_ANALYSIS:
        return { temperature: 0.7 }; // 人设分析需要适中的创造性

      case AIServiceType.PERSONA_OPTIMIZATION:
        return { temperature: 0.6 }; // 人设优化需要较高准确性和适度创意

      case AIServiceType.SYNOPSIS:
        return { temperature: 0.8 }; // 核心梗概生成需要高创意性

      case AIServiceType.RHYTHM_ANALYSIS:
        return { temperature: 0.6 }; // 节奏分析需要较高准确性

      default:
        return {}; // 使用默认配置
    }
  }

  /**
   * 获取有效配置（合并全局配置和服务特定配置）
   */
  protected async getEffectiveConfig(): Promise<AIConfig & Partial<AIRequestOptions>> {
    const globalConfig = await this.getAIConfig();
    const serviceSpecificConfig = this.getServiceSpecificConfig();

    return {
      ...globalConfig,
      ...serviceSpecificConfig // 服务特定配置覆盖全局配置
    };
  }

  /**
   * 重写callAI方法，使用有效配置
   */
  protected async callAI(
    messages: Array<{role: string, content: string}>,
    options?: Partial<AIRequestOptions>
  ): Promise<AIResponse> {
    const effectiveConfig = await this.getEffectiveConfig();

    // 合并有效配置和用户选项
    const finalOptions = {
      ...effectiveConfig,
      ...options // 用户选项优先级最高
    };

    return super.callAI(messages, finalOptions);
  }

  /**
   * 重写callAIStreaming方法，使用有效配置
   */
  protected async callAIStreaming(
    messages: Array<{role: string, content: string}>,
    onChunk: (chunk: string) => void,
    options?: Partial<AIRequestOptions>
  ): Promise<AIResponse> {
    const effectiveConfig = await this.getEffectiveConfig();

    const finalOptions = {
      ...effectiveConfig,
      ...options
    };

    return super.callAIStreaming(messages, onChunk, finalOptions);
  }
}

/**
 * AI架构守护者
 * 确保双AI系统和统一AI系统的边界清晰
 */
export class AIArchitectureGuard {
  /**
   * 判断是否为双AI服务
   */
  static isDualAIService(serviceType: string | AIServiceType): boolean {
    return [
      AIServiceType.DUAL_AI_OUTLINE,
      AIServiceType.DUAL_AI_DIALOGUE
    ].includes(serviceType as AIServiceType);
  }

  /**
   * 验证服务使用的配置源是否正确
   */
  static validateServiceUsage(serviceType: AIServiceType, configSource: 'dual' | 'unified') {
    const isDualAI = this.isDualAIService(serviceType);

    if (isDualAI && configSource === 'unified') {
      throw new Error(`双AI服务 ${serviceType} 不应使用统一配置`);
    }

    if (!isDualAI && configSource === 'dual') {
      throw new Error(`统一AI服务 ${serviceType} 不应使用双AI配置`);
    }
  }

  /**
   * 获取服务应该使用的配置源
   */
  static getConfigSource(serviceType: AIServiceType): 'dual' | 'unified' {
    return this.isDualAIService(serviceType) ? 'dual' : 'unified';
  }
}

/**
 * AI服务工厂
 * 根据服务类型创建合适的AI服务实例
 */
export class AIServiceFactory {
  /**
   * 创建AI服务实例
   */
  static createService(serviceType: AIServiceType): UnifiedAIService {
    // 验证不是双AI服务
    if (AIArchitectureGuard.isDualAIService(serviceType)) {
      throw new Error(`双AI服务 ${serviceType} 应使用 DualAIService，不应通过此工厂创建`);
    }

    return new UnifiedAIService(serviceType);
  }

  /**
   * 获取服务实例（带缓存）
   */
  private static serviceCache = new Map<AIServiceType, UnifiedAIService>();

  static getService(serviceType: AIServiceType): UnifiedAIService {
    if (!this.serviceCache.has(serviceType)) {
      this.serviceCache.set(serviceType, this.createService(serviceType));
    }

    return this.serviceCache.get(serviceType)!;
  }

  /**
   * 清除服务缓存
   */
  static clearCache() {
    this.serviceCache.clear();
  }
}
