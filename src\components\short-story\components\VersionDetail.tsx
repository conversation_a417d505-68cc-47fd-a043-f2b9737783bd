"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { PersonaVersion, AIPersonaConfig, PhaseType } from '../../../types/ai-persona';

interface VersionDetailProps {
  version: PersonaVersion | null;
  persona: AIPersonaConfig | null;
  onCategoryManage: () => void;
}

const VersionDetail: React.FC<VersionDetailProps> = ({
  version,
  persona,
  onCategoryManage
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));

  // 切换展开状态
  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  // 获取阶段显示名称
  const getPhaseDisplayName = (phase?: PhaseType): string => {
    if (!phase) return '未设置阶段';

    const phaseNames: Record<PhaseType, string> = {
      'intro': '导语阶段',
      'setup': '铺垫期',
      'compression': '爆发情绪阶段',
      'climax': '反转阶段',
      'resolution': '让读者解气阶段',
      'ending': '大结局阶段',
      'buildup': '铺垫期',
      'custom': '自定义阶段'
    };
    return phaseNames[phase] || phase;
  };

  // 获取当前阶段的分类显示
  const getCurrentPhaseCategories = () => {
    if (!config.categories || config.categories.length === 0) {
      return <span className="text-gray-500 dark:text-gray-400 text-sm">暂无分类</span>;
    }

    // 筛选出当前阶段的分类
    const currentPhaseCategories = config.categories.filter(category =>
      category.phase === config.phase
    );

    if (currentPhaseCategories.length === 0) {
      return <span className="text-gray-500 dark:text-gray-400 text-sm">当前阶段暂无分类</span>;
    }

    return (
      <div className="flex flex-wrap gap-2">
        {currentPhaseCategories.map((category, index) => (
          <span
            key={index}
            className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs rounded-full"
          >
            {category.name}
          </span>
        ))}
      </div>
    );
  };

  // 格式化日期
  const formatFullDate = (date?: Date): string => {
    if (!date) return '未知时间';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    return validDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 渲染空状态
  if (!version) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 dark:text-gray-400">请选择一个版本查看详情</p>
        </div>
      </div>
    );
  }

  const config = version.config;

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              版本 {version.version} 详情
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {formatFullDate(version.createdAt)}
            </p>
          </div>

          {/* 分类管理按钮 */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onCategoryManage}
            className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm"
          >
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              分类管理
            </div>
          </motion.button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* 基本信息 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection('basic')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <h4 className="font-medium text-gray-900 dark:text-gray-100">基本信息</h4>
            <motion.svg
              animate={{ rotate: expandedSections.has('basic') ? 180 : 0 }}
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>

          {expandedSections.has('basic') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4 space-y-3"
            >
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">当前阶段分类</label>
                  <div className="mt-2">
                    {getCurrentPhaseCategories()}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">阶段</label>
                    <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                      {getPhaseDisplayName(config.phase)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">版本描述</label>
                    <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                      {version.description || '无描述'}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">人设ID</label>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 font-mono">
                  {config.id}
                </p>
              </div>
            </motion.div>
          )}
        </div>

        {/* 系统提示词 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection('prompt')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <h4 className="font-medium text-gray-900 dark:text-gray-100">系统提示词</h4>
            <motion.svg
              animate={{ rotate: expandedSections.has('prompt') ? 180 : 0 }}
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>

          {expandedSections.has('prompt') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4"
            >
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 max-h-64 overflow-y-auto">
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-mono">
                  {config.systemPrompt}
                </pre>
              </div>
            </motion.div>
          )}
        </div>

        {/* 个性化设置 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection('customizations')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <h4 className="font-medium text-gray-900 dark:text-gray-100">个性化设置</h4>
            <motion.svg
              animate={{ rotate: expandedSections.has('customizations') ? 180 : 0 }}
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>

          {expandedSections.has('customizations') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4 space-y-3"
            >
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">性格特征</label>
                <div className="mt-1 flex flex-wrap gap-2">
                  {config.customizations.personality.map((trait, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {trait}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">专业领域</label>
                <div className="mt-1 flex flex-wrap gap-2">
                  {config.customizations.expertise.map((skill, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">沟通风格</label>
                <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                  {config.customizations.communicationStyle}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">回应模式</label>
                <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                  {config.customizations.responsePattern}
                </p>
              </div>
            </motion.div>
          )}
        </div>

        {/* 元数据 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection('metadata')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <h4 className="font-medium text-gray-900 dark:text-gray-100">元数据</h4>
            <motion.svg
              animate={{ rotate: expandedSections.has('metadata') ? 180 : 0 }}
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>

          {expandedSections.has('metadata') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4 space-y-3"
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">创建时间</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {formatFullDate(config.metadata.createdAt)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">更新时间</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {formatFullDate(config.metadata.updatedAt)}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">版本号</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {config.metadata.version}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">使用次数</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {config.metadata.usageCount || 0}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">默认人设</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {config.metadata.isDefault ? '是' : '否'}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VersionDetail;
