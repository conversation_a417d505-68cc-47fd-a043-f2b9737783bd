"use client";

import React, { useEffect, useState } from 'react';
import { createAnimationFactory } from '@/factories/animation/AnimationFactory';

interface ScaleAnimationProps {
  children: React.ReactNode;
  startScale?: number;
  endScale?: number;
  duration?: number;
  delay?: number;
  visible?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 缩放动画适配器组件
 */
const ScaleAnimation: React.FC<ScaleAnimationProps> = ({
  children,
  startScale = 0.95,
  endScale = 1,
  duration = 300,
  delay = 0,
  visible = true,
  className = '',
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  
  // 创建动画工厂和组件
  const animationFactory = createAnimationFactory();
  const scaleAnimation = animationFactory.createScaleAnimation(startScale, endScale, duration, delay, isVisible);
  
  // 设置动画属性
  useEffect(() => {
    scaleAnimation.setStartScale(startScale);
  }, [startScale]);
  
  useEffect(() => {
    scaleAnimation.setEndScale(endScale);
  }, [endScale]);
  
  useEffect(() => {
    scaleAnimation.setDuration(duration);
  }, [duration]);
  
  useEffect(() => {
    scaleAnimation.setDelay(delay);
  }, [delay]);
  
  // 控制可见性
  useEffect(() => {
    if (visible) {
      // 延迟一帧，确保DOM已经渲染
      requestAnimationFrame(() => {
        setIsVisible(true);
      });
    } else {
      setIsVisible(false);
    }
  }, [visible]);
  
  // 如果不可见且没有动画，则不渲染
  if (!visible && !isVisible) {
    return null;
  }
  
  // 合并样式和类名
  const animationClassName = scaleAnimation.getClassName();
  const animationStyle = scaleAnimation.getStyle();
  
  const mergedClassName = `${animationClassName} ${className}`.trim();
  const mergedStyle = { ...animationStyle, ...style };
  
  return (
    <div className={mergedClassName} style={mergedStyle}>
      {children}
    </div>
  );
};

export default ScaleAnimation;
