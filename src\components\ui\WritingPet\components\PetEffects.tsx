"use client";

import React, { useState, useEffect } from 'react';
import { PetState } from '../index';

interface PetEffectsProps {
  state: PetState;
  isAnimating: boolean;
  animationLevel: 'low' | 'medium' | 'high';
  triggerKey: number; // 用于触发新特效的key
}

interface Effect {
  id: string;
  type: 'star' | 'heart' | 'sparkle' | 'note';
  x: number;
  y: number;
  delay: number;
}

/**
 * 宠物特效组件
 * 管理各种特效动画，如星星、爱心、音符等
 */
export const PetEffects: React.FC<PetEffectsProps> = ({
  state,
  isAnimating,
  animationLevel,
  triggerKey
}) => {
  const [effects, setEffects] = useState<Effect[]>([]);

  // 当状态变化或触发key变化时，生成新特效
  useEffect(() => {
    if (animationLevel === 'low') return;

    let newEffects: Effect[] = [];

    // 根据状态生成不同的特效
    switch (state) {
      case 'excited':
        newEffects = generateStarEffects();
        break;
      case 'celebrating':
        newEffects = [...generateStarEffects(), ...generateHeartEffects()];
        break;
      case 'writing':
        if (animationLevel === 'high') {
          newEffects = generateNoteEffects();
        }
        break;
    }

    // 点击触发的特效
    if (triggerKey > 0) {
      newEffects = [...newEffects, ...generateClickEffects()];
    }

    if (newEffects.length > 0) {
      setEffects(newEffects);
      
      // 3秒后清理特效
      const timer = setTimeout(() => {
        setEffects([]);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [state, triggerKey, animationLevel]);

  // 生成星星特效
  const generateStarEffects = (): Effect[] => {
    return Array.from({ length: 3 }, (_, i) => ({
      id: `star-${Date.now()}-${i}`,
      type: 'star',
      x: 20 + (Math.random() - 0.5) * 30,
      y: 15 + (Math.random() - 0.5) * 20,
      delay: i * 200
    }));
  };

  // 生成爱心特效
  const generateHeartEffects = (): Effect[] => {
    return Array.from({ length: 2 }, (_, i) => ({
      id: `heart-${Date.now()}-${i}`,
      type: 'heart',
      x: 20 + (Math.random() - 0.5) * 20,
      y: 10 + i * 5,
      delay: i * 300
    }));
  };

  // 生成音符特效
  const generateNoteEffects = (): Effect[] => {
    return Array.from({ length: 2 }, (_, i) => ({
      id: `note-${Date.now()}-${i}`,
      type: 'note',
      x: 25 + i * 10,
      y: 12 + i * 3,
      delay: i * 400
    }));
  };

  // 生成点击特效
  const generateClickEffects = (): Effect[] => {
    return Array.from({ length: 5 }, (_, i) => ({
      id: `click-${Date.now()}-${i}`,
      type: Math.random() > 0.5 ? 'star' : 'sparkle',
      x: 20 + (Math.random() - 0.5) * 40,
      y: 20 + (Math.random() - 0.5) * 30,
      delay: i * 100
    })) as Effect[];
  };

  // 渲染单个特效
  const renderEffect = (effect: Effect) => {
    const commonProps = {
      key: effect.id,
      style: {
        position: 'absolute' as const,
        left: `${effect.x}%`,
        top: `${effect.y}%`,
        transform: 'translate(-50%, -50%)',
        animationDelay: `${effect.delay}ms`,
        pointerEvents: 'none' as const
      }
    };

    switch (effect.type) {
      case 'star':
        return (
          <div {...commonProps} className="pet-effect pet-effect-star">
            <svg width="8" height="8" viewBox="0 0 8 8">
              <path
                d="M4 0 L5 3 L8 3 L6 5 L7 8 L4 6 L1 8 L2 5 L0 3 L3 3 Z"
                fill="#FFD700"
              />
            </svg>
          </div>
        );

      case 'heart':
        return (
          <div {...commonProps} className="pet-effect pet-effect-heart">
            <svg width="10" height="10" viewBox="0 0 10 10">
              <path
                d="M5 9 C5 9 1 6 1 3.5 C1 2 2 1 3.5 1 C4.5 1 5 2 5 2 C5 2 5.5 1 6.5 1 C8 1 9 2 9 3.5 C9 6 5 9 5 9 Z"
                fill="#FF69B4"
              />
            </svg>
          </div>
        );

      case 'sparkle':
        return (
          <div {...commonProps} className="pet-effect pet-effect-sparkle">
            <svg width="6" height="6" viewBox="0 0 6 6">
              <circle cx="3" cy="3" r="1" fill="#87CEEB" />
              <line x1="3" y1="0" x2="3" y2="6" stroke="#87CEEB" strokeWidth="0.5" />
              <line x1="0" y1="3" x2="6" y2="3" stroke="#87CEEB" strokeWidth="0.5" />
            </svg>
          </div>
        );

      case 'note':
        return (
          <div {...commonProps} className="pet-effect pet-effect-note">
            <svg width="8" height="10" viewBox="0 0 8 10">
              <circle cx="2" cy="8" r="1.5" fill="#9370DB" />
              <rect x="3.5" y="2" width="0.5" height="6" fill="#9370DB" />
              <path d="M4 2 Q6 1 8 2 L8 6 Q6 5 4 6 Z" fill="#9370DB" />
            </svg>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="pet-effects-container">
      {effects.map(renderEffect)}
    </div>
  );
};

export default PetEffects;
