"use client";

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../../types/outline';

// 节点类型中文映射函数
const getNodeTypeLabel = (type: string): string => {
  switch (type) {
    case 'chapter':
      return '章节';
    case 'plot':
      return '剧情节点';
    case 'dialogue':
      return '对话设计';
    case 'scene':
      return '场景';
    case 'note':
      return '笔记';
    default:
      return type || '未知';
  }
};

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  nodeData: OutlineNodeType | null;
  onConfirm: () => Promise<void> | void;
  onCancel: () => void;
  customMessage?: string;
  showRecoveryHint?: boolean;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  nodeData,
  onConfirm,
  onCancel,
  customMessage,
  showRecoveryHint = true
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const cancelButtonRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 计算子节点数量和类型
  const getChildrenInfo = () => {
    if (!nodeData?.children || nodeData.children.length === 0) {
      return null;
    }

    const childrenByType = nodeData.children.reduce((acc, child) => {
      const type = child.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: nodeData.children.length,
      byType: childrenByType
    };
  };

  // 生成智能化删除提示
  const getDeleteMessage = () => {
    if (customMessage) return customMessage;
    if (!nodeData) return '确定要删除这个节点吗？';

    const childrenInfo = getChildrenInfo();
    const nodeTypeLabel = getNodeTypeLabel(nodeData.type);

    if (!childrenInfo) {
      return `删除此${nodeTypeLabel}不会影响其他内容。`;
    }

    const typeDescriptions = Object.entries(childrenInfo.byType)
      .map(([type, count]) => `${count}个${getNodeTypeLabel(type)}`)
      .join('和');

    return `删除此${nodeTypeLabel}将同时删除其下的${typeDescriptions}。`;
  };

  // 处理确认删除
  const handleConfirm = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setError(null);
      await onConfirm();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除失败，请重试');
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onCancel();
      } else if (e.key === 'Enter' && !isLoading) {
        handleConfirm();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, isLoading, onCancel]);

  // 自动聚焦到取消按钮
  useEffect(() => {
    if (isOpen && cancelButtonRef.current) {
      cancelButtonRef.current.focus();
    }
  }, [isOpen]);

  // 重置状态
  useEffect(() => {
    if (!isOpen) {
      setIsLoading(false);
      setError(null);
    }
  }, [isOpen]);

  if (!isOpen || !nodeData) return null;

  const childrenInfo = getChildrenInfo();

  return createPortal(
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)',
        animation: 'fadeIn 0.3s ease-out'
      }}
      onClick={onCancel}
    >
      <div
        ref={dialogRef}
        className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
        style={{
          animation: 'scaleIn 0.3s ease-out',
          transformOrigin: 'center center'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 标题区域 */}
        <div className="px-6 pt-6 pb-4 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-50 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            确认删除
          </h3>
        </div>

        {/* 节点信息卡片 */}
        <div className="px-6 pb-4">
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-3">
              <div
                className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                style={{
                  background: `var(--outline-${
                    nodeData.type === 'chapter' ? 'primary' :
                    nodeData.type === 'plot' ? 'secondary' :
                    nodeData.type === 'dialogue' ? 'success' :
                    nodeData.type === 'scene' ? 'secondary' : 'info'
                  })`
                }}
              >
                {getNodeTypeLabel(nodeData.type).charAt(0)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {nodeData.title}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {getNodeTypeLabel(nodeData.type)}
                </p>
                {nodeData.description && (
                  <p className="text-xs text-gray-600 mt-2 line-clamp-2">
                    {nodeData.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* 删除影响说明 */}
          <div className="text-sm text-gray-700 mb-4">
            <p className="mb-2">{getDeleteMessage()}</p>
            
            {childrenInfo && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-xs font-medium mb-1">
                  ⚠️ 影响范围
                </p>
                <p className="text-yellow-700 text-xs">
                  将同时删除 {childrenInfo.total} 个子节点
                </p>
              </div>
            )}
          </div>

          {/* 恢复提示 */}
          {showRecoveryHint && (
            <div className="text-xs text-gray-500 mb-4 text-center">
              💡 删除的内容可在30天内恢复
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="px-6 pb-6 flex space-x-3">
          <button
            ref={cancelButtonRef}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-gray-300"
            onClick={onCancel}
            disabled={isLoading}
          >
            取消
          </button>
          <button
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
            ) : (
              '确认删除'
            )}
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes scaleIn {
          from { 
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
          }
          to { 
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
        
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>,
    document.body
  );
};

export default DeleteConfirmDialog;
