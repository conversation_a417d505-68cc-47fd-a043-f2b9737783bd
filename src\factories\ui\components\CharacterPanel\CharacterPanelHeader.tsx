"use client";

import React from 'react';
import { SearchIcon } from './icons';
import { CustomDropdown } from './CustomDropdown';

interface CharacterPanelHeaderProps {
  searchQuery: string;
  sortBy: 'name' | 'importance';
  onSearchChange: (query: string) => void;
  onSortChange: (sortBy: 'name' | 'importance') => void;
  onExtractCharacters?: () => void; // 可选的人物提取回调
  onCreateCharacter?: () => void; // 可选的人物创建回调
}

/**
 * 人物面板头部组件
 */
export const CharacterPanelHeader: React.FC<CharacterPanelHeaderProps> = ({
  searchQuery,
  sortBy,
  onSearchChange,
  onSortChange,
  onExtractCharacters,
  onCreateCharacter
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <h2 className="text-xl font-semibold" style={{
          color: 'var(--color-primary)',
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
          transition: 'color 0.3s ease'
        }}>人物管理</h2>

        {/* 人物提取按钮 */}
        {onExtractCharacters && (
          <button
            className="ml-4 p-1.5 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors flex items-center"
            onClick={onExtractCharacters}
            title="从章节提取人物信息"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span className="ml-1 text-sm">提取人物</span>
          </button>
        )}

        {/* AI创建人物按钮 */}
        {onCreateCharacter && (
          <button
            className="ml-4 p-1.5 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors flex items-center"
            onClick={onCreateCharacter}
            title="使用AI创建人物"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="ml-1 text-sm">AI创建</span>
          </button>
        )}
      </div>
      <div className="flex items-center space-x-3">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索人物..."
            className="pl-8 pr-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            style={{
              backgroundColor: 'rgba(240, 245, 250, 0.7)',
              borderColor: 'var(--color-secondary)',
              width: '180px',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
              transition: 'all 0.3s ease'
            }}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
          <SearchIcon
            className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"
            style={{
              transition: 'transform 0.3s ease',
              transform: searchQuery ? 'translate(-50%, -50%) scale(1.1)' : 'translate(-50%, -50%) scale(1)'
            }}
          />
        </div>
        <CustomDropdown
          options={[
            { value: 'name', label: '按名称排序' },
            { value: 'importance', label: '按重要性排序' }
          ]}
          value={sortBy}
          onChange={(value) => onSortChange(value as 'name' | 'importance')}
          className="w-[140px]"
        />
      </div>
    </div>
  );
};
