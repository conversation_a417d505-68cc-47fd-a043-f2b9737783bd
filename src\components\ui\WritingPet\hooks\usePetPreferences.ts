"use client";

import { useState, useEffect } from 'react';
import { PetType } from '../index';

interface PetPreferences {
  petType: PetType;
  animationLevel: 'low' | 'medium' | 'high';
  isEnabled: boolean;
  soundEnabled: boolean;
}

interface PetPreferencesHook extends PetPreferences {
  setPetType: (type: PetType) => void;
  setAnimationLevel: (level: 'low' | 'medium' | 'high') => void;
  setIsEnabled: (enabled: boolean) => void;
  setSoundEnabled: (enabled: boolean) => void;
  resetToDefaults: () => void;
}

const DEFAULT_PREFERENCES: PetPreferences = {
  petType: 'cat',
  animationLevel: 'medium',
  isEnabled: true,
  soundEnabled: false
};

const STORAGE_KEY = 'writing-pet-preferences';

/**
 * 宠物偏好设置Hook
 * 管理用户的宠物偏好设置，包括类型、动画级别等
 */
export const usePetPreferences = (): PetPreferencesHook => {
  const [preferences, setPreferences] = useState<PetPreferences>(DEFAULT_PREFERENCES);

  // 从本地存储加载偏好设置
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        
        // 验证数据有效性
        const validPreferences: PetPreferences = {
          petType: ['cat', 'bird', 'dragon', 'rabbit'].includes(parsed.petType) 
            ? parsed.petType 
            : DEFAULT_PREFERENCES.petType,
          animationLevel: ['low', 'medium', 'high'].includes(parsed.animationLevel)
            ? parsed.animationLevel
            : DEFAULT_PREFERENCES.animationLevel,
          isEnabled: typeof parsed.isEnabled === 'boolean' 
            ? parsed.isEnabled 
            : DEFAULT_PREFERENCES.isEnabled,
          soundEnabled: typeof parsed.soundEnabled === 'boolean'
            ? parsed.soundEnabled
            : DEFAULT_PREFERENCES.soundEnabled
        };

        setPreferences(validPreferences);
      }
    } catch (error) {
      console.warn('Failed to load pet preferences:', error);
      setPreferences(DEFAULT_PREFERENCES);
    }
  }, []);

  // 检查系统减少动画设置
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      if (e.matches && preferences.animationLevel === 'high') {
        // 如果系统设置了减少动画，自动降低动画级别
        setAnimationLevel('low');
      }
    };

    // 初始检查
    if (mediaQuery.matches && preferences.animationLevel === 'high') {
      setAnimationLevel('low');
    }

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [preferences.animationLevel]);

  // 保存偏好设置到本地存储
  const savePreferences = (newPreferences: PetPreferences) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newPreferences));
    } catch (error) {
      console.warn('Failed to save pet preferences:', error);
    }
  };

  // 设置宠物类型
  const setPetType = (type: PetType) => {
    const newPreferences = { ...preferences, petType: type };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
  };

  // 设置动画级别
  const setAnimationLevel = (level: 'low' | 'medium' | 'high') => {
    const newPreferences = { ...preferences, animationLevel: level };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
  };

  // 设置是否启用宠物
  const setIsEnabled = (enabled: boolean) => {
    const newPreferences = { ...preferences, isEnabled: enabled };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
  };

  // 设置是否启用声音
  const setSoundEnabled = (enabled: boolean) => {
    const newPreferences = { ...preferences, soundEnabled: enabled };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
  };

  // 重置为默认设置
  const resetToDefaults = () => {
    setPreferences(DEFAULT_PREFERENCES);
    savePreferences(DEFAULT_PREFERENCES);
  };

  return {
    ...preferences,
    setPetType,
    setAnimationLevel,
    setIsEnabled,
    setSoundEnabled,
    resetToDefaults
  };
};
