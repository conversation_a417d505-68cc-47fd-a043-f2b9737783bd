/* 框架选择器样式 */
.framework-selector {
  width: 100%;
  margin-bottom: 12px;
}

/* 选择按钮 */
.framework-selector-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #475569;
}

.framework-selector-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.framework-selector-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.framework-selector-icon {
  margin-right: 8px;
  color: #64748b;
}

.framework-selector-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.framework-selector-arrow {
  margin-left: 8px;
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .framework-selector-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}
