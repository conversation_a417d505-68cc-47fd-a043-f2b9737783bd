"use client";

/**
 * 短篇创作相关类型定义
 */

/**
 * 短篇创作参数
 */
export interface ShortStoryParams {
  // 基础参数
  userInput: string;                    // 用户输入的创作需求
  bookId: string;                       // 书籍ID
  
  // 关联元素
  selectedCharacterIds: string[];       // 选中的人物ID
  selectedWorldBuildingIds: string[];   // 选中的世界观ID
  selectedTerminologyIds: string[];     // 选中的术语ID
  selectedOutlineNodeIds: string[];     // 选中的大纲节点ID
  selectedACEFrameworkIds: string[];   // 选中的ACE框架ID
  
  // 创作设置
  targetSegments?: number;              // 目标段落数量，默认20（最低20段）
  suspenseLevel?: 'low' | 'medium' | 'high'; // 悬念强度
  storyTone?: 'mystery' | 'thriller' | 'horror' | 'drama' | 'comedy' | 'philosophical' | 'custom'; // 故事基调
  customPhilosophy?: string;            // 自定义哲学要求（当storyTone为custom时使用）
  
  // ACE框架
  availableFrameworks?: any[];          // 可用的ACE框架
  selectedFrameworks?: any[];           // 选中的ACE框架
}

/**
 * 核心悬念结构
 */
export interface CoreMystery {
  id: string;
  title: string;                        // 短篇标题
  coreQuestion: string;                 // 核心悬念问题
  revealedHalf: string;                 // 开篇透露的一半信息
  hiddenHalf: string;                   // 需要逐步揭露的另一半
  finalTruth: string;                   // 最终真相
  emotionalImpact: string;              // 情感冲击点
  
  // 关联元素信息
  mainCharacter?: string;               // 主角信息
  settingInfo?: string;                 // 背景设定
  keyTerms?: string[];                  // 关键术语
  plotConnection?: string;              // 与大纲的连接
}

/**
 * 段落结构
 */
export interface SegmentStructure {
  segmentNumber: number;                // 段落编号 1-20
  purpose: string;                      // 段落目的（改为string以支持更灵活的描述）
  informationLevel: number;             // 信息透露程度 1-10
  tensionLevel: number;                 // 紧张感程度 1-10
  content?: string;                     // 段落内容（改为可选）
  cliffhanger?: string;                 // 段落结尾的小钩子
  wordCount?: number;                   // 字数统计
  paymentHookFlag?: boolean;            // 付费卡点标记（约4000字处的钩子）

  // 悬念控制
  mysteryElements?: string[];           // 本段涉及的悬念元素（改为可选）
  revealedInfo?: string[];              // 本段透露的信息（改为可选）
  hiddenInfo?: string[];                // 本段隐藏的信息（改为可选）
}

/**
 * 段落目的类型
 */
export type SegmentPurpose = 
  | 'setup'           // 建立悬念 (1-3段)
  | 'development'     // 深入探索 (4-6段)
  | 'climax'          // 真相逼近 (7-9段)
  | 'resolution';     // 真相大白 (10段)

/**
 * 短篇生成结果
 */
export interface ShortStoryResult {
  success: boolean;
  coreMystery?: CoreMystery;
  segments?: SegmentStructure[];
  fullText?: string;                    // 完整的短篇文本
  error?: string;
  
  // 统计信息
  totalWordCount?: number;              // 总字数
  averageSegmentLength?: number;        // 平均段落长度
  suspenseCurve?: number[];             // 悬念强度曲线
}

/**
 * 付费卡点设计
 */
export interface PaymentHook {
  position: string;                     // 卡点位置（如"约4000字处（第8-10段）"）
  hookStrategy: string;                 // 最强钩子制作策略
  cliffhangerDesign: string;            // 悬崖式卡点设计
  readerPsychology: string;             // 读者心理操控要点
}

/**
 * 阶段策划信息
 */
export interface PhaseStrategy {
  phaseType: string;                    // 阶段类型（铺垫期、挤压期、高潮期、结局期）
  phaseGoal: string;                    // 阶段目标效果
  revealStrategy: string;               // "说一半"的具体策略
  hideStrategy: string;                 // "藏一半"的具体策略
  plotDensityTactics: string[];         // 剧情密度提升战术
  emotionalTarget: string;              // 目标情绪效果
  keyTechniques: string[];              // 关键技巧列表
  paymentHook?: PaymentHook;            // 付费卡点设计（仅挤压期有）
}

/**
 * 完整的四阶段策划
 */
export interface FullPhaseStrategy {
  setupPhase: PhaseStrategy;            // 铺垫期策划
  compressionPhase: PhaseStrategy;      // 挤压期策划
  climaxPhase: PhaseStrategy;           // 高潮期策划
  resolutionPhase: PhaseStrategy;       // 结局期策划
}

/**
 * 短篇生成回调函数
 */
export interface ShortStoryCallbacks {
  onStart?: () => void;
  onMysteryGenerated?: (mystery: CoreMystery) => void;
  onPhaseStrategyGenerated?: (strategy: FullPhaseStrategy) => void;
  onStructureGenerated?: (segments: SegmentStructure[]) => void;
  onSegmentGenerated?: (segment: SegmentStructure, index: number) => void;
  onStreamChunk?: (chunk: string, segmentIndex: number) => void;
  onComplete?: (result: ShortStoryResult) => void;
  onError?: (error: string) => void;
}

/**
 * 悬念引擎配置
 */
export interface SuspenseEngineConfig {
  // 信息分层配置
  revealRatio: number;                  // 开篇透露比例 0.3-0.7
  progressionCurve: 'linear' | 'exponential' | 'sigmoid'; // 信息释放曲线
  
  // 冲击力配置
  openingImpact: 'subtle' | 'moderate' | 'shocking'; // 开头冲击力
  cliffhangerIntensity: 'light' | 'medium' | 'strong'; // 悬念强度
  
  // 真幻感配置
  realityDistortion: boolean;           // 是否启用现实扭曲
  informationAsymmetry: boolean;        // 是否使用信息不对等
}

/**
 * 数字分段模板
 */
export interface SegmentTemplate {
  segmentRange: [number, number];       // 段落范围，如 [1, 3]
  purpose: SegmentPurpose;
  description: string;                  // 模板描述
  techniques: string[];                 // 推荐技巧
  wordCountRange: [number, number];     // 字数范围
  tensionRange: [number, number];       // 紧张感范围
  informationRange: [number, number];   // 信息透露范围
}

/**
 * 短篇创作模式
 */
export type ShortStoryMode =
  | 'mystery'         // 悬疑模式
  | 'thriller'        // 惊悚模式
  | 'horror'          // 恐怖模式
  | 'drama'           // 剧情模式
  | 'comedy'          // 喜剧模式
  | 'philosophical'   // 哲学思辨模式
  | 'custom';         // 自定义模式

/**
 * 关联元素信息
 */
export interface AssociatedElements {
  characters: any[];                    // 关联的人物
  worldBuildings: any[];                // 关联的世界观
  terminologies: any[];                 // 关联的术语
  outlineNodes: any[];                  // 关联的大纲节点
  chapters: any[];                      // 关联的章节
}
