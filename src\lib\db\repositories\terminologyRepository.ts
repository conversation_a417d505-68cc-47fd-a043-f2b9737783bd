import { v4 as uuidv4 } from 'uuid';
import { db, Terminology } from '../dexie';

export interface ITerminologyRepository {
  getAllByBookId(bookId: string): Promise<Terminology[]>;
  getById(id: string): Promise<Terminology | undefined>;
  create(terminology: Omit<Terminology, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, terminology: Partial<Terminology>): Promise<void>;
  delete(id: string): Promise<void>;
  getByChapterId(chapterId: string): Promise<Terminology[]>;
  linkToChapter(terminologyId: string, chapterId: string): Promise<void>;
  unlinkFromChapter(terminologyId: string, chapterId: string): Promise<void>;
}

export class TerminologyRepository implements ITerminologyRepository {
  async getAllByBookId(bookId: string): Promise<Terminology[]> {
    return await db.terminology
      .where('bookId')
      .equals(bookId)
      .toArray();
  }

  async getById(id: string): Promise<Terminology | undefined> {
    return await db.terminology.get(id);
  }

  async create(terminology: Omit<Terminology, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();
    
    await db.terminology.add({
      ...terminology,
      id,
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: terminology.extractedFromChapterIds || [],
      relatedCharacterIds: terminology.relatedCharacterIds || [],
      relatedTerminologyIds: terminology.relatedTerminologyIds || [],
      relatedWorldBuildingIds: terminology.relatedWorldBuildingIds || []
    });
    
    return id;
  }

  async update(id: string, terminology: Partial<Terminology>): Promise<void> {
    await db.terminology.update(id, {
      ...terminology,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    const terminology = await db.terminology.get(id);
    if (!terminology) {
      throw new Error(`Terminology with id ${id} not found`);
    }
    
    await db.transaction('rw', [db.terminology, db.chapters, db.characters, db.worldBuilding], async () => {
      // 从所有相关章节中移除该术语的引用
      for (const chapterId of terminology.extractedFromChapterIds) {
        const chapter = await db.chapters.get(chapterId);
        if (chapter) {
          const updatedTerminologyIds = chapter.terminologyIds.filter(tid => tid !== id);
          await db.chapters.update(chapterId, { terminologyIds: updatedTerminologyIds });
        }
      }
      
      // 从所有相关人物中移除该术语的引用
      const relatedCharacters = await db.characters
        .where('relatedTerminologyIds')
        .anyOf([id])
        .toArray();
      
      for (const relatedCharacter of relatedCharacters) {
        const updatedRelatedTerminologyIds = relatedCharacter.relatedTerminologyIds.filter(tid => tid !== id);
        await db.characters.update(relatedCharacter.id!, { relatedTerminologyIds: updatedRelatedTerminologyIds });
      }
      
      // 从所有相关术语中移除该术语的引用
      const relatedTerminologies = await db.terminology
        .where('relatedTerminologyIds')
        .anyOf([id])
        .toArray();
      
      for (const relatedTerminology of relatedTerminologies) {
        const updatedRelatedTerminologyIds = relatedTerminology.relatedTerminologyIds.filter(tid => tid !== id);
        await db.terminology.update(relatedTerminology.id!, { relatedTerminologyIds: updatedRelatedTerminologyIds });
      }
      
      // 从所有相关世界观中移除该术语的引用
      const relatedWorldBuildings = await db.worldBuilding
        .where('relatedTerminologyIds')
        .anyOf([id])
        .toArray();
      
      for (const relatedWorldBuilding of relatedWorldBuildings) {
        const updatedRelatedTerminologyIds = relatedWorldBuilding.relatedTerminologyIds.filter(tid => tid !== id);
        await db.worldBuilding.update(relatedWorldBuilding.id!, { relatedTerminologyIds: updatedRelatedTerminologyIds });
      }
      
      // 删除术语
      await db.terminology.delete(id);
    });
  }

  async getByChapterId(chapterId: string): Promise<Terminology[]> {
    const chapter = await db.chapters.get(chapterId);
    if (!chapter || !chapter.terminologyIds || chapter.terminologyIds.length === 0) {
      return [];
    }
    
    return await db.terminology
      .where('id')
      .anyOf(chapter.terminologyIds)
      .toArray();
  }

  async linkToChapter(terminologyId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.terminology, db.chapters], async () => {
      // 更新术语的提取章节列表
      const terminology = await db.terminology.get(terminologyId);
      if (terminology) {
        const extractedFromChapterIds = [...new Set([...terminology.extractedFromChapterIds, chapterId])];
        await db.terminology.update(terminologyId, { extractedFromChapterIds });
      }
      
      // 更新章节的术语列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const terminologyIds = [...new Set([...chapter.terminologyIds, terminologyId])];
        await db.chapters.update(chapterId, { terminologyIds });
      }
    });
  }

  async unlinkFromChapter(terminologyId: string, chapterId: string): Promise<void> {
    await db.transaction('rw', [db.terminology, db.chapters], async () => {
      // 更新术语的提取章节列表
      const terminology = await db.terminology.get(terminologyId);
      if (terminology) {
        const extractedFromChapterIds = terminology.extractedFromChapterIds.filter(id => id !== chapterId);
        await db.terminology.update(terminologyId, { extractedFromChapterIds });
      }
      
      // 更新章节的术语列表
      const chapter = await db.chapters.get(chapterId);
      if (chapter) {
        const terminologyIds = chapter.terminologyIds.filter(id => id !== terminologyId);
        await db.chapters.update(chapterId, { terminologyIds });
      }
    });
  }
}

// 创建并导出仓库实例
export const terminologyRepository = new TerminologyRepository();
