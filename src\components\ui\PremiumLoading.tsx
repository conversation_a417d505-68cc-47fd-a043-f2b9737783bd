"use client";

import React from 'react';

interface PremiumLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  variant?: 'dots' | 'spinner' | 'pulse';
}

export const PremiumLoading: React.FC<PremiumLoadingProps> = ({
  size = 'md',
  text,
  variant = 'dots'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  if (variant === 'dots') {
    return (
      <div className="flex flex-col items-center gap-3">
        <div className="premium-loading">
          <div className="premium-loading-dot"></div>
          <div className="premium-loading-dot"></div>
          <div className="premium-loading-dot"></div>
        </div>
        {text && (
          <p className={`text-gray-600 ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === 'spinner') {
    return (
      <div className="flex flex-col items-center gap-3">
        <div className={`
          ${sizeClasses[size]}
          border-3
          border-blue-200
          border-t-blue-500
          rounded-full
          animate-spin
        `} />
        {text && (
          <p className={`text-gray-600 ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className="flex flex-col items-center gap-3">
        <div className={`
          ${sizeClasses[size]}
          bg-gradient-to-r
          from-blue-400
          to-purple-500
          rounded-full
          animate-pulse
        `} />
        {text && (
          <p className={`text-gray-600 ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  return null;
};

export default PremiumLoading;
