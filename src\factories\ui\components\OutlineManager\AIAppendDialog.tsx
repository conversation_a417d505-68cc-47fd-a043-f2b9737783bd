import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../types/outline';

interface AIAppendDialogProps {
  isOpen: boolean;
  position: { x: number; y: number };
  nodeType: 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue' | 'synopsis';
  nodeData: OutlineNodeType;
  onClose: () => void;
  onConfirm: (customPrompt: string, generateCount?: number) => void;
}

const AIAppendDialog: React.FC<AIAppendDialogProps> = ({
  isOpen,
  position,
  nodeType,
  nodeData,
  onClose,
  onConfirm
}) => {
  const [customPrompt, setCustomPrompt] = useState('');
  const [generateCount, setGenerateCount] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const dialogRef = useRef<HTMLDivElement>(null);

  // 获取要生成的子节点类型
  const getChildNodeType = (parentType: string): string => {
    const typeMap: Record<string, string> = {
      'volume': '章节',
      'event': '章节',
      'chapter': '剧情点',
      'plot': '对话'
    };
    return typeMap[parentType] || '节点';
  };

  // 获取占位符提示
  const getPlaceholder = (parentType: string): string => {
    const placeholders: Record<string, string> = {
      'volume': '例如：生成一个关于商战的章节，重点描述主角的第一次重要决策...',
      'chapter': '例如：创建一个冲突剧情点，展现主角与对手的正面交锋...',
      'plot': '例如：设计一段紧张的对话，揭示重要信息并推进剧情...'
    };
    return placeholders[parentType] || '请输入您的生成要求...';
  };

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        handleConfirm();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, customPrompt]);

  // 自动聚焦输入框
  useEffect(() => {
    if (isOpen && inputRef.current) {
      console.log('🎯 AI对话框已打开，聚焦输入框');
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // 处理确认
  const handleConfirm = () => {
    if (customPrompt.trim()) {
      onConfirm(customPrompt.trim(), generateCount);
    } else {
      onConfirm('', generateCount); // 使用默认提示词
    }
    setCustomPrompt('');
    setGenerateCount(1);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setCustomPrompt('');
    setGenerateCount(1);
    onClose();
  };

  // 计算对话框位置 - 与NodeMenu保持一致，显示在节点右边
  const getDialogStyle = () => {
    const dialogWidth = 360;
    const dialogHeight = 320; // 增加高度以容纳生成数量选择

    // position.x 和 position.y 是从NodeMenu传递过来的，已经是节点右边的位置
    // 与NodeMenu的位置计算保持一致：rect.right + 5, rect.top
    let x = position.x; // 已经是节点右边 + 5px
    let y = position.y; // 已经是节点顶部

    // 确保对话框不超出屏幕边界
    if (x + dialogWidth > window.innerWidth) {
      x = window.innerWidth - dialogWidth - 20;
    }
    if (x < 20) {
      x = 20;
    }

    // 如果下方空间不够，向上调整
    if (y + dialogHeight > window.innerHeight) {
      y = window.innerHeight - dialogHeight - 20;
    }
    if (y < 20) {
      y = 20;
    }

    console.log('🎯 AIAppendDialog位置计算:', {
      原始位置: position,
      计算后位置: { x, y },
      对话框尺寸: { dialogWidth, dialogHeight },
      屏幕尺寸: { width: window.innerWidth, height: window.innerHeight }
    });

    return {
      left: `${x}px`,
      top: `${y}px`,
      width: `${dialogWidth}px`
    };
  };

  if (!isOpen) return null;

  const childNodeType = getChildNodeType(nodeType);

  return createPortal(
    <div
      ref={dialogRef}
      className="fixed bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden animate-in zoom-in-95 duration-300 z-[10000]"
      style={getDialogStyle()}
      onClick={(e) => e.stopPropagation()} // 阻止事件冒泡
      onMouseDown={(e) => e.stopPropagation()} // 阻止mousedown事件冒泡
    >
        {/* 头部 */}
        <div className="bg-gradient-to-r from-purple-500 to-blue-500 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div>
                <h3 className="text-white font-semibold text-lg">AI智能生成{childNodeType}</h3>
                <p className="text-white text-opacity-80 text-sm">为"{nodeData.title}"添加{childNodeType}</p>
              </div>
            </div>

            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center hover:bg-opacity-30 transition-all duration-200"
              title="关闭对话框"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              自定义生成要求 <span className="text-gray-400">(可选)</span>
            </label>
            <textarea
              ref={inputRef}
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder={getPlaceholder(nodeType)}
              className="w-full h-20 px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm"
              maxLength={500}
            />
            <div className="flex justify-between items-center mt-2">
              <p className="text-xs text-gray-500">
                留空将使用智能默认提示词
              </p>
              <span className="text-xs text-gray-400">
                {customPrompt.length}/500
              </span>
            </div>
          </div>

          {/* 生成数量选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              生成数量
            </label>
            <div className="flex items-center space-x-3">
              <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setGenerateCount(Math.max(1, generateCount - 1))}
                  className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={generateCount <= 1}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                <input
                  type="number"
                  value={generateCount}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 1;
                    setGenerateCount(Math.min(Math.max(1, value), 10));
                  }}
                  className="w-16 px-3 py-2 text-center border-0 focus:ring-0 focus:outline-none"
                  min="1"
                  max="10"
                />
                <button
                  onClick={() => setGenerateCount(Math.min(10, generateCount + 1))}
                  className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={generateCount >= 10}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
              <div className="flex-1">
                <p className="text-xs text-gray-500">
                  一次生成 <span className="font-medium text-purple-600">{generateCount}</span> 个{childNodeType}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  建议1-3个，最多10个
                </p>
              </div>
            </div>
          </div>

          {/* 快捷提示 */}
          <div className="mb-4">
            <p className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3">
              💡 <strong>提示：</strong>您可以描述想要的{childNodeType}风格、内容重点或特殊要求，
              并选择生成数量（1-10个）。AI会结合上下文智能生成符合您需求的{childNodeType}。
            </p>
          </div>

          {/* 按钮组 */}
          <div className="flex space-x-3">
            <button
              onClick={handleConfirm}
              className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2.5 rounded-lg font-medium hover:from-purple-600 hover:to-blue-600 transition-all duration-200 flex items-center justify-center space-x-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>生成 {generateCount} 个{childNodeType}</span>
            </button>
            <button
              onClick={handleCancel}
              className="px-4 py-2.5 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200"
            >
              取消
            </button>
          </div>

          {/* 快捷键提示 */}
          <div className="mt-3 text-xs text-gray-400 text-center">
            <kbd className="px-1.5 py-0.5 bg-gray-100 rounded text-xs">Ctrl+Enter</kbd> 快速生成 ·
            <kbd className="px-1.5 py-0.5 bg-gray-100 rounded text-xs ml-1">Esc</kbd> 取消 ·
            点击右上角 <span className="text-gray-600">✕</span> 关闭
          </div>
        </div>
    </div>,
    document.body
  );
};

export default AIAppendDialog;
