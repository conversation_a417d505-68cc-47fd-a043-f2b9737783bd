/**
 * 测试简介框架推荐修复效果
 * 这是一个简单的测试脚本，用于验证AI创意助手中的简介推荐功能
 */

// 模拟测试数据
const PRESET_KEYWORDS = [
  { text: '修仙', hotness: 95 },
  { text: '都市', hotness: 90 },
  { text: '重生', hotness: 88 },
  { text: '系统', hotness: 85 },
  { text: '穿越', hotness: 82 },
  { text: '异能', hotness: 80 },
  { text: '玄幻', hotness: 78 },
  { text: '科幻', hotness: 75 },
  { text: '武侠', hotness: 72 },
  { text: '历史', hotness: 70 },
  { text: '军事', hotness: 68 },
  { text: '商战', hotness: 65 }
];

const PRESET_SYNOPSIS_FRAMEWORKS = [
  {
    id: 'classic-three-act',
    name: '经典三段式',
    description: '适用于大多数类型的小说，结构清晰，易于理解',
    effectiveness: 9
  },
  {
    id: 'character-driven',
    name: '人物驱动式',
    description: '以主角为核心，突出人物魅力和成长历程',
    effectiveness: 8
  },
  {
    id: 'mystery-intro',
    name: '悬疑引入式',
    description: '适合悬疑、推理、都市异能类作品',
    effectiveness: 8
  },
  {
    id: 'worldview-showcase',
    name: '世界观展示式',
    description: '适合设定复杂的奇幻、科幻类作品',
    effectiveness: 7
  }
];

// 模拟推荐信息生成方法
function generateSynopsisRecommendationsForUser() {
  try {
    // 选择热门关键词（前10个）
    const hotKeywords = PRESET_KEYWORDS
      .sort((a, b) => (b.hotness || 0) - (a.hotness || 0))
      .slice(0, 10)
      .map(k => k.text);

    // 选择高效的简介框架（前5个）
    const topSynopsisFrameworks = PRESET_SYNOPSIS_FRAMEWORKS
      .sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0))
      .slice(0, 5);

    return `📖 **简介创作资源推荐**

🔥 **热门关键词**：${hotKeywords.join('、')}

🏗️ **高效简介框架**：
${topSynopsisFrameworks.map(f => `• **${f.name}**：${f.description}`).join('\n')}

💡 **使用提示**：您可以直接告诉我使用这些关键词和框架来生成简介，我会自动调用专业的简介生成工具为您创作！

---

`;
  } catch (error) {
    console.error('生成简介用户推荐信息失败:', error);
    return '';
  }
}

// 模拟关键词检测方法
function detectSynopsisKeywords(userInput) {
  const synopsisKeywords = ['简介', '内容简介', '故事简介', '作品简介', '书籍简介', '小说简介'];
  return synopsisKeywords.some(keyword => 
    userInput.toLowerCase().includes(keyword.toLowerCase())
  );
}

// 测试用例
function runTests() {
  console.log('🧪 开始测试简介框架推荐功能...\n');

  // 测试用例1：简介生成请求
  console.log('📝 测试用例1：简介生成请求');
  const testInput1 = '帮我生成一个简介';
  const isDetected1 = detectSynopsisKeywords(testInput1);
  console.log(`输入：${testInput1}`);
  console.log(`检测结果：${isDetected1 ? '✅ 检测到简介关键词' : '❌ 未检测到简介关键词'}`);
  
  if (isDetected1) {
    const recommendations1 = generateSynopsisRecommendationsForUser();
    console.log('推荐信息：');
    console.log(recommendations1);
  }

  // 测试用例2：简介写作咨询
  console.log('\n📝 测试用例2：简介写作咨询');
  const testInput2 = '简介怎么写';
  const isDetected2 = detectSynopsisKeywords(testInput2);
  console.log(`输入：${testInput2}`);
  console.log(`检测结果：${isDetected2 ? '✅ 检测到简介关键词' : '❌ 未检测到简介关键词'}`);
  
  if (isDetected2) {
    const recommendations2 = generateSynopsisRecommendationsForUser();
    console.log('推荐信息：');
    console.log(recommendations2);
  }

  // 测试用例3：非简介请求（对比测试）
  console.log('\n📝 测试用例3：非简介请求（对比测试）');
  const testInput3 = '帮我生成书名';
  const isDetected3 = detectSynopsisKeywords(testInput3);
  console.log(`输入：${testInput3}`);
  console.log(`检测结果：${isDetected3 ? '✅ 检测到简介关键词' : '❌ 未检测到简介关键词'}`);
  
  if (!isDetected3) {
    console.log('✅ 正确：书名请求不会触发简介推荐');
  }

  console.log('\n🎉 测试完成！');
}

// 运行测试
runTests();
