"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { WorkflowStep } from '@/services/thinking-canvas/ThinkingCanvasWorkflow';

interface WorkflowIndicatorProps {
  currentStep: WorkflowStep;
  isProcessing: boolean;
  onStepClick?: (step: WorkflowStep) => void;
  className?: string;
}

/**
 * 工作流程指示器组件
 * 显示思考画布的三阶段工作流程：thinking → generating → completed
 */
const WorkflowIndicator: React.FC<WorkflowIndicatorProps> = ({
  currentStep,
  isProcessing,
  onStepClick,
  className = ''
}) => {
  const steps = [
    {
      id: 'thinking' as WorkflowStep,
      label: '思考',
      icon: '💭',
      description: '深度分析需求'
    },
    {
      id: 'generating' as WorkflowStep,
      label: '生成',
      icon: '⚡',
      description: '生成大纲节点'
    },
    {
      id: 'completed' as WorkflowStep,
      label: '完成',
      icon: '✅',
      description: '应用到大纲'
    }
  ];

  const getStepStatus = (stepId: WorkflowStep) => {
    const stepIndex = steps.findIndex(s => s.id === stepId);
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    
    if (stepIndex < currentIndex) {
      return 'completed';
    } else if (stepIndex === currentIndex) {
      return isProcessing ? 'processing' : 'current';
    } else {
      return 'pending';
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#34A853'; // 绿色
      case 'current':
        return '#1A73E8'; // 蓝色
      case 'processing':
        return '#1A73E8'; // 蓝色
      case 'pending':
        return '#E8EAED'; // 灰色
      default:
        return '#E8EAED';
    }
  };

  const isStepClickable = (stepId: WorkflowStep) => {
    const status = getStepStatus(stepId);
    return status === 'completed' && onStepClick && !isProcessing;
  };

  return (
    <div className={`workflow-indicator ${className}`}>
      <div className="flex items-center justify-between max-w-md mx-auto">
        {steps.map((step, index) => {
          const status = getStepStatus(step.id);
          const isClickable = isStepClickable(step.id);
          
          return (
            <React.Fragment key={step.id}>
              {/* 步骤节点 */}
              <motion.div
                className={`relative flex flex-col items-center ${isClickable ? 'cursor-pointer' : ''}`}
                onClick={() => isClickable && onStepClick?.(step.id)}
                whileHover={isClickable ? { scale: 1.1 } : {}}
                whileTap={isClickable ? { scale: 0.95 } : {}}
              >
                {/* 节点圆圈 */}
                <motion.div
                  className="relative w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm shadow-lg"
                  style={{ backgroundColor: getStepColor(status) }}
                  animate={status === 'processing' ? {
                    scale: [1, 1.1, 1],
                    boxShadow: [
                      '0 4px 12px rgba(26, 115, 232, 0.3)',
                      '0 6px 20px rgba(26, 115, 232, 0.5)',
                      '0 4px 12px rgba(26, 115, 232, 0.3)'
                    ]
                  } : {}}
                  transition={{
                    duration: 1.5,
                    repeat: status === 'processing' ? Infinity : 0,
                    ease: "easeInOut"
                  }}
                >
                  {/* 节点图标 */}
                  <span className="text-lg leading-none">
                    {status === 'processing' ? (
                      <motion.div
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                    ) : (
                      step.icon
                    )}
                  </span>
                  
                  {/* 脉冲动画（仅当前步骤） */}
                  {status === 'current' && !isProcessing && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-blue-400"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.8, 0, 0.8]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                  )}
                </motion.div>
                
                {/* 步骤标签 */}
                <div className="mt-2 text-center">
                  <div className={`text-sm font-medium ${
                    status === 'completed' || status === 'current' || status === 'processing'
                      ? 'text-gray-900'
                      : 'text-gray-400'
                  }`}>
                    {step.label}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {step.description}
                  </div>
                </div>
              </motion.div>
              
              {/* 连接线 */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4">
                  <motion.div
                    className="h-0.5 bg-gradient-to-r"
                    style={{
                      backgroundImage: `linear-gradient(to right, ${getStepColor(status)}, ${getStepColor(getStepStatus(steps[index + 1].id))})`
                    }}
                    initial={{ scaleX: 0 }}
                    animate={{ 
                      scaleX: status === 'completed' ? 1 : 0.3
                    }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                    style={{ transformOrigin: 'left' }}
                  />
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
      
      {/* 进度描述 */}
      <div className="mt-4 text-center">
        <div className="text-sm text-gray-600">
          {isProcessing ? (
            <span className="flex items-center justify-center gap-2">
              <motion.div
                className="w-3 h-3 bg-blue-500 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
              正在{steps.find(s => s.id === currentStep)?.description}...
            </span>
          ) : (
            `当前阶段：${steps.find(s => s.id === currentStep)?.description}`
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkflowIndicator;
