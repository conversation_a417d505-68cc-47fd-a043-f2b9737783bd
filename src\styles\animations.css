/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* z-index层级管理 */
:root {
  --z-index-node: 10;
  --z-index-node-selected: 20;
  --z-index-handle: 30;
  --z-index-menu: 9999;
  --z-index-submenu: 10000;
}

/* 菜单容器样式 */
.menu-container {
  z-index: var(--z-index-menu);
  animation: fadeIn 0.2s ease-out;
}

/* 子菜单容器样式 */
.add-child-menu {
  z-index: var(--z-index-submenu);
  animation: slideDown 0.2s ease-out;
}

/* 节点选中状态样式 */
.node-selected {
  z-index: var(--z-index-node-selected);
  animation: pulse 2s infinite;
}

/* 连接点样式 */
.react-flow__handle {
  z-index: var(--z-index-handle);
}
