/* 全局高度标准变量 */
:root {
  --modal-max-height: 75vh;
  --modal-max-height-mobile: 85vh;
  --canvas-max-height: 75vh;
  --canvas-max-height-mobile: 80vh;
}

@media (max-width: 768px) {
  :root {
    --modal-max-height: var(--modal-max-height-mobile);
    --canvas-max-height: var(--canvas-max-height-mobile);
  }
}

/* 节点预览覆盖层样式 - 重构为内部组件 */
.node-preview-overlay {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
}

/* 预览节点容器 */
.preview-nodes-container {
  padding: 20px;
  height: 100%;
  max-height: var(--modal-max-height);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-nodes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.preview-nodes-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-nodes-count {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 预览节点列表 */
.preview-nodes-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  padding-right: 8px;
  /* 滚动阴影提示 */
  background:
    linear-gradient(white 30%, rgba(255,255,255,0)),
    linear-gradient(rgba(255,255,255,0), white 70%) 0 100%,
    radial-gradient(50% 0, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0)),
    radial-gradient(50% 100%, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0)) 0 100%;
  background-repeat: no-repeat;
  background-color: transparent;
  background-size: 100% 40px, 100% 40px, 100% 14px, 100% 14px;
  background-attachment: local, local, scroll, scroll;
}

/* 预览节点列表滚动条样式 */
.preview-nodes-list::-webkit-scrollbar {
  width: 6px;
}

.preview-nodes-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.preview-nodes-list::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #667eea, #764ba2);
  border-radius: 3px;
}

.preview-nodes-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #5a6fd8, #6a4190);
}

/* 单个预览节点 - 增强视觉效果 */
.preview-node {
  margin-bottom: 12px;
  border: 2px dashed #667eea;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.05);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
  animation: fadeInUp 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

.preview-node::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
}

.preview-node:hover {
  opacity: 1;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
  border-style: solid;
  border-color: #5a6fd8;
}

.preview-node:hover::before {
  left: 100%;
}

.preview-node.selected {
  opacity: 1;
  border-style: solid;
  background: rgba(102, 126, 234, 0.12);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.preview-node.selected::after {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: checkmarkPop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.preview-node-content {
  padding: 12px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 预览节点图标 */
.preview-node-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.plus-icon {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: breathe 2s ease-in-out infinite;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
  border: 2px solid white;
}

.node-type-icon {
  font-size: 18px;
  opacity: 0.8;
}

/* 预览节点文本 */
.preview-node-text {
  flex: 1;
  min-width: 0;
}

.preview-node-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.3;
}

.preview-node-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 8px;
}

/* 创作建议样式 */
.preview-node-creative-notes {
  margin-top: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  padding: 8px;
  font-size: 11px;
}

.creative-notes-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-weight: 600;
  color: #667eea;
}

.creative-notes-icon {
  font-size: 12px;
}

.creative-notes-label {
  font-size: 10px;
  letter-spacing: 0.5px;
}

.creative-notes-content {
  font-size: 10px;
  line-height: 1.4;
  color: #555;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.6);
  padding: 6px;
  border-radius: 4px;
  border-left: 2px solid #667eea;
}

/* 预览节点复选框 */
.preview-node-checkbox {
  flex-shrink: 0;
  margin-top: 2px;
}

.preview-node-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #667eea;
}

/* 预览节点操作按钮 */
.preview-nodes-actions {
  border-top: 1px solid #e0e0e0;
  padding-top: 15px;
  display: flex;
  gap: 10px;
}

.preview-action-btn {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #667eea;
  border-radius: 6px;
  background: white;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-action-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.preview-action-btn:active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 0.8;
    transform: translateY(0) scale(1);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
  }
  50% {
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
  }
}

@keyframes checkmarkPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动指示器样式 */
.scroll-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(102, 126, 234, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.preview-nodes-list:hover .scroll-indicator,
.conflicts-list:hover .scroll-indicator {
  opacity: 1;
}

/* 智能高度调整 */
.dynamic-height {
  min-height: 200px;
  max-height: var(--modal-max-height);
  height: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-nodes-container {
    padding: 15px;
  }

  .preview-node-content {
    padding: 10px;
    gap: 10px;
  }

  .preview-node-icon {
    width: 28px;
    height: 28px;
  }

  .node-type-icon {
    font-size: 16px;
  }

  .plus-icon {
    width: 18px;
    height: 18px;
    font-size: 10px;
    top: -4px;
    right: -4px;
  }

  .preview-node-title {
    font-size: 13px;
  }

  .preview-node-description {
    font-size: 11px;
  }
}



/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .node-preview-overlay {
    background: rgba(30, 30, 30, 0.95);
    border-left-color: #333;
  }

  .preview-nodes-header {
    border-bottom-color: #333;
  }

  .preview-nodes-header h4 {
    color: #fff;
  }

  .preview-node {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
  }

  .preview-node.selected {
    background: rgba(102, 126, 234, 0.2);
  }

  .preview-node-title {
    color: #fff;
  }

  .preview-node-description {
    color: #ccc;
  }

  .preview-nodes-actions {
    border-top-color: #333;
  }

  .preview-action-btn {
    background: #333;
    color: #667eea;
    border-color: #667eea;
  }

  .preview-action-btn:hover {
    background: #667eea;
    color: white;
  }
}

/* 冲突解决界面样式 */
.conflict-resolver {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #FF8C00;
  margin: 16px 0;
  max-height: var(--modal-max-height);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.resolver-header {
  margin-bottom: 20px;
  text-align: center;
  flex-shrink: 0;
}

.resolver-header h3 {
  margin: 0 0 8px 0;
  color: #FF8C00;
  font-size: 18px;
  font-weight: 600;
}

.resolver-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  flex: 1;
  overflow-y: auto;
  max-height: calc(var(--modal-max-height) - 200px);
  padding-right: 8px;
  /* 滚动阴影提示 */
  background:
    linear-gradient(white 30%, rgba(255,255,255,0)),
    linear-gradient(rgba(255,255,255,0), white 70%) 0 100%,
    radial-gradient(50% 0, rgba(255, 140, 0, 0.2), rgba(255, 140, 0, 0)),
    radial-gradient(50% 100%, rgba(255, 140, 0, 0.2), rgba(255, 140, 0, 0)) 0 100%;
  background-repeat: no-repeat;
  background-color: transparent;
  background-size: 100% 40px, 100% 40px, 100% 14px, 100% 14px;
  background-attachment: local, local, scroll, scroll;
}

/* 冲突列表滚动条样式 */
.conflicts-list::-webkit-scrollbar {
  width: 6px;
}

.conflicts-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.conflicts-list::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #FF8C00, #FF6B35);
  border-radius: 3px;
}

.conflicts-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #e67e00, #e55a2b);
}

/* 重名警告卡片样式 */
.duplicate-warning-card {
  background: #FFF8F0;
  border: 1px solid #FFD700;
  border-radius: 8px;
  padding: 16px;
  position: relative;
  animation: slideInFromLeft 0.3s ease-out;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.warning-icon {
  font-size: 20px;
}

.warning-header h4 {
  margin: 0;
  color: #FF8C00;
  font-size: 16px;
  font-weight: 600;
}

.conflict-details {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #E5E7EB;
}

.existing-node,
.generated-node {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.existing-node:last-child,
.generated-node:last-child {
  margin-bottom: 0;
}

.conflict-details .label {
  font-weight: 600;
  color: #374151;
  min-width: 80px;
}

.conflict-details .title {
  color: #1F2937;
  font-weight: 500;
  background: #F3F4F6;
  padding: 2px 8px;
  border-radius: 4px;
}

.conflict-details .type {
  color: #6B7280;
  font-size: 12px;
}

/* 解决选项按钮 */
.resolution-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.option-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 120px;
  justify-content: center;
}

.option-btn .btn-icon {
  font-size: 14px;
}

.option-btn.update {
  background: #3B82F6;
  color: white;
}

.option-btn.update:hover {
  background: #2563EB;
  transform: translateY(-1px);
}

.option-btn.create-new {
  background: #10B981;
  color: white;
}

.option-btn.create-new:hover {
  background: #059669;
  transform: translateY(-1px);
}

.option-btn.skip {
  background: #6B7280;
  color: white;
}

.option-btn.skip:hover {
  background: #4B5563;
  transform: translateY(-1px);
}

/* 批量操作按钮 */
.resolver-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #E5E7EB;
  flex-shrink: 0;
  background: white;
  margin-top: auto;
}

.batch-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.batch-btn.create-all {
  background: #10B981;
  color: white;
}

.batch-btn.create-all:hover {
  background: #059669;
  transform: translateY(-1px);
}

.batch-btn.update-all {
  background: #3B82F6;
  color: white;
}

.batch-btn.update-all:hover {
  background: #2563EB;
  transform: translateY(-1px);
}

.batch-btn.skip-all {
  background: #6B7280;
  color: white;
}

.batch-btn.skip-all:hover {
  background: #4B5563;
  transform: translateY(-1px);
}

/* 冲突解决动画 */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 - 冲突解决界面 */
@media (max-width: 768px) {
  .conflict-resolver {
    padding: 16px;
    margin: 12px 0;
  }

  .resolution-options {
    flex-direction: column;
  }

  .option-btn {
    min-width: auto;
  }

  .resolver-actions {
    flex-direction: column;
  }

  .batch-btn {
    min-width: auto;
  }
}

/* 差异化预览组件样式 */

/* 通用预览样式 */
.preview-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.3;
}

.field-label {
  font-weight: 600;
  font-size: 12px;
  margin-right: 4px;
}

.field-value {
  font-size: 12px;
  color: #555;
}

.description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  border-left: 2px solid #ddd;
}

/* 章节预览样式（蓝色主题） */
.chapter-preview {
  border-left: 4px solid #2563eb;
  background: linear-gradient(to right, #eff6ff, #dbeafe);
  padding: 12px;
  border-radius: 8px;
}

.chapter-preview .field-label {
  color: #1d4ed8;
}

.chapter-field {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
  gap: 4px;
  flex-wrap: wrap;
}

.techniques-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.technique-tag {
  background: #dbeafe;
  color: #1e40af;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid #93c5fd;
  display: inline-block;
}

.chapter-preview .description {
  border-left-color: #2563eb;
}

/* 剧情预览样式（橙色主题） */
.plot-preview {
  border-left: 4px solid #ea580c;
  background: linear-gradient(to right, #fff7ed, #fed7aa);
  padding: 12px;
  border-radius: 8px;
}

.plot-preview .field-label {
  color: #c2410c;
}

.plot-field {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
  gap: 4px;
  flex-wrap: wrap;
}

.plot-points-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.plot-point {
  background: #fed7aa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  border-left: 2px solid #ea580c;
  line-height: 1.3;
}

.more-points {
  font-size: 10px;
  color: #c2410c;
  font-style: italic;
  margin-top: 2px;
}

.plot-preview .description {
  border-left-color: #ea580c;
}

/* 对话预览样式（绿色主题） */
.dialogue-preview {
  border-left: 4px solid #059669;
  background: linear-gradient(to right, #ecfdf5, #a7f3d0);
  padding: 12px;
  border-radius: 8px;
}

.dialogue-preview .field-label {
  color: #047857;
}

.dialogue-field {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
  gap: 4px;
  flex-wrap: wrap;
}

.participants-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.participant-tag {
  background: #a7f3d0;
  color: #065f46;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid #6ee7b7;
  display: inline-block;
}

.dialogue-content-preview {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.dialogue-item {
  background: #f0fdf4;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  border-left: 2px solid #059669;
  line-height: 1.3;
}

.dialogue-item strong {
  color: #047857;
}

.more-content {
  font-size: 10px;
  color: #047857;
  font-style: italic;
  margin-top: 2px;
}

.dialogue-preview .description {
  border-left-color: #059669;
}

/* 通用预览样式 */
.generic-preview {
  padding: 12px;
  border-radius: 8px;
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-left: 4px solid #6b7280;
}

.generic-preview .preview-title {
  color: #374151;
}

.generic-preview .description {
  border-left-color: #6b7280;
}

.generic-preview .creative-notes {
  margin-top: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  padding: 8px;
  font-size: 11px;
}

.generic-preview .creative-notes-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-weight: 600;
  color: #667eea;
}

.generic-preview .creative-notes-icon {
  font-size: 12px;
}

.generic-preview .creative-notes-label {
  font-size: 10px;
  letter-spacing: 0.5px;
}

.generic-preview .creative-notes-content {
  font-size: 10px;
  line-height: 1.4;
  color: #555;
  background: rgba(255, 255, 255, 0.6);
  padding: 6px;
  border-radius: 4px;
  border-left: 2px solid #667eea;
}

/* 预览容器类型样式 */
.chapter-preview-container {
  border-left: 3px solid #2563eb;
}

.plot-preview-container {
  border-left: 3px solid #ea580c;
}

.dialogue-preview-container {
  border-left: 3px solid #059669;
}

/* 响应式设计 - 差异化预览 */
@media (max-width: 768px) {
  .chapter-preview,
  .plot-preview,
  .dialogue-preview,
  .generic-preview {
    padding: 10px;
  }

  .preview-title {
    font-size: 13px;
  }

  .field-label,
  .field-value {
    font-size: 11px;
  }

  .technique-tag,
  .participant-tag {
    font-size: 10px;
    padding: 1px 6px;
  }

  .plot-point,
  .dialogue-item {
    font-size: 10px;
    padding: 3px 6px;
  }

  .more-points,
  .more-content {
    font-size: 9px;
  }
}
