"use client";

import React, { useEffect, useState } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  worldBuilding: WorldBuilding | null;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * 删除确认对话框组件
 */
export const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  worldBuilding,
  onConfirm,
  onCancel
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // 处理对话框显示和隐藏的动画效果
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setTimeout(() => setIsAnimating(true), 50);
    } else {
      setIsAnimating(false);
      setTimeout(() => setIsVisible(false), 300);
    }
  }, [isOpen]);

  // 如果不可见，不渲染任何内容
  if (!isVisible || !worldBuilding) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        opacity: isAnimating ? 1 : 0,
        transition: 'opacity 0.3s ease',
        zIndex: 1000 // 确保对话框在最顶层
      }}
    >
      <div
        className="bg-white rounded-lg shadow-xl overflow-hidden"
        style={{
          width: '400px',
          transform: isAnimating ? 'scale(1)' : 'scale(0.9)',
          transition: 'transform 0.3s ease',
          border: '1px solid var(--color-border)',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* 头部 */}
        <div className="bg-red-50 px-6 py-4 border-b border-red-100 flex items-center">
          <div className="bg-red-100 rounded-full p-2 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-red-700">确认删除</h3>
        </div>

        {/* 内容 */}
        <div className="px-6 py-4">
          <p className="text-gray-700 mb-2">您确定要删除以下世界观元素吗？</p>
          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-4">
            <p className="font-bold text-lg text-gray-800">{worldBuilding.name}</p>
            {worldBuilding.category && (
              <p className="text-gray-600 text-sm mt-1">类别: {worldBuilding.category}</p>
            )}
            {worldBuilding.description && (
              <p className="text-gray-600 text-sm mt-1 line-clamp-2">{worldBuilding.description}</p>
            )}
          </div>
          <p className="text-red-600 text-sm">此操作不可撤销，删除后数据将无法恢复。</p>
        </div>

        {/* 底部按钮 */}
        <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3 border-t border-gray-100">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-300"
            onClick={onCancel}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-300"
            onClick={() => {
              // 添加删除动画效果
              setIsAnimating(false);
              setTimeout(() => {
                onConfirm();
              }, 300);
            }}
          >
            确认删除
          </button>
        </div>
      </div>
    </div>
  );
};
