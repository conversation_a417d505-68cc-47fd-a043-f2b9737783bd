"use client";

import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { Chapter, Character, Terminology, WorldBuilding } from '@/lib/db/dexie';
import { Outline } from '@/factories/ui/types/outline';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { Message } from '@/utils/ai/types';
import { PromptHelperServiceInterface, createPromptHelperService } from './PromptHelperService';
import { dialogContinuationService } from './DialogContinuationService';
import { aiWritingPrompts, SmartInstructionContext } from '../prompts/AIWritingPrompts';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

/**
 * AI写作服务接口
 */
export interface AIWritingServiceInterface {
  /**
   * 生成内容
   * @param params 生成参数
   * @param callbacks 回调函数
   */
  generateContent(
    params: GenerateContentParams,
    callbacks: GenerateContentCallbacks
  ): Promise<GenerateContentResult>;
}

/**
 * 生成内容参数
 */
export interface GenerateContentParams {
  // API设置
  provider: string;
  model: string;
  apiKey: string;
  apiEndpoint?: string;

  // 内容设置
  writingStyle?: string;
  requirements?: string;
  corePlot?: string;
  continuePrompt?: string;
  continueMode?: 'new' | 'continue' | 'rewrite' | 'analyze';

  // 关联元素
  chapters: any[]; // 使用any类型以兼容不同的Chapter接口
  selectedChapterIds: string[];
  characters: Character[];
  selectedCharacterIds: string[];
  terminologies: Terminology[];
  selectedTerminologyIds: string[];
  worldBuildings: WorldBuilding[];
  selectedWorldBuildingIds: string[];

  // 新增：大纲关联元素
  outlines?: Outline[];
  selectedOutlineIds?: string[];
  selectedOutlineNodeIds?: string[];
  outlineContextMode?: 'selected' | 'hierarchy' | 'full';

  // 书籍ID
  bookId: string;

  // 对话历史
  conversationHistory?: ConversationMessage[];
}

/**
 * 对话消息
 */
export interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * 生成内容回调函数
 */
export interface GenerateContentCallbacks {
  onStart?: () => void;
  onStreamChunk?: (chunk: string) => void;
  onComplete?: (result: GenerateContentResult) => void;
  onError?: (error: Error) => void;
}

/**
 * 生成内容结果
 */
export interface GenerateContentResult {
  text: string;
  success: boolean;
  error?: string;
  conversationHistory?: ConversationMessage[]; // 返回更新后的对话历史
}

/**
 * AI写作服务实现
 */
export class AIWritingService extends UnifiedAIService implements AIWritingServiceInterface {
  private aiSender: DefaultAISenderComponent;
  private apiSettings: any;
  private promptHelper: PromptHelperServiceInterface;

  constructor() {
    super(AIServiceType.WRITING); // 添加新的写作服务类型
    this.aiSender = new DefaultAISenderComponent();
    this.promptHelper = createPromptHelperService();

    // 获取API设置
    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 确保在客户端环境中初始化时加载设置
    if (typeof window !== 'undefined') {
      // 延迟一点时间再获取API设置，确保localStorage已加载
      setTimeout(() => {
        // 重新获取API设置，确保从localStorage加载了最新的设置
        this.apiSettings = settingsFactory.createAPISettingsDialogComponent();
        console.log('AIWritingService: 已重新加载API设置');
      }, 200);
    }
  }

  /**
   * 构建优化后的系统提示词（向后兼容方法）
   * @deprecated 推荐使用拆分系统消息模式，通过aiWritingPrompts.buildSystemMessages()
   * @param params 生成参数
   * @returns 系统提示词
   */
  private buildSystemPrompt(params: GenerateContentParams): string {
    return aiWritingPrompts.buildSystemPrompt(params.requirements);
  }

  /**
   * 检查是否有选中的关联元素
   * @param params 生成参数
   * @returns 是否有关联元素
   */
  private hasSelectedAssociations(params: GenerateContentParams): boolean {
    return (
      params.selectedChapterIds.length > 0 ||
      params.selectedCharacterIds.length > 0 ||
      params.selectedTerminologyIds.length > 0 ||
      params.selectedWorldBuildingIds.length > 0 ||
      (params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0)
    );
  }

  /**
   * 生成智能创作指令
   * @param params 生成参数
   * @returns 智能指令
   */
  private generateSmartInstruction(params: GenerateContentParams): string {
    const context: SmartInstructionContext = {
      hasContext: !!(params.conversationHistory && params.conversationHistory.length > 0),
      hasAssociations: this.hasSelectedAssociations(params),
      isNewCreation: params.continueMode === 'new' || !params.continueMode,
      corePlot: params.corePlot,
      writingStyle: params.writingStyle,
      continueMode: params.continueMode
    };

    return aiWritingPrompts.generateSmartInstruction(context);
  }

  /**
   * 按优先级顺序添加关联元素
   * @param messageBuilder 消息构建器
   * @param params 生成参数
   */
  private addAssociationElementsInOrder(messageBuilder: MessageBuilder, params: GenerateContentParams): void {
    // 优先级1：大纲节点（结构性信息）
    if (params.outlines && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0) {
      console.log('✅ 添加大纲节点信息（优先级1）');
      this.promptHelper.addSelectedOutlineNodes(
        messageBuilder,
        params.outlines,
        params.selectedOutlineNodeIds,
        params.outlineContextMode
      );
    }

    // 优先级2：章节内容（上下文信息）
    if (params.selectedChapterIds.length > 0) {
      console.log('✅ 添加章节内容（优先级2）');
      this.promptHelper.addSelectedChapters(
        messageBuilder,
        params.chapters,
        params.selectedChapterIds
      );
    }

    // 优先级3：人物信息（角色设定）
    if (params.selectedCharacterIds.length > 0) {
      console.log('✅ 添加人物信息（优先级3）');
      this.promptHelper.addSelectedCharacters(
        messageBuilder,
        params.characters,
        params.selectedCharacterIds
      );
    }

    // 优先级4：世界观信息（背景设定）
    if (params.selectedWorldBuildingIds.length > 0) {
      console.log('✅ 添加世界观信息（优先级4）');
      this.promptHelper.addSelectedWorldBuildings(
        messageBuilder,
        params.worldBuildings,
        params.selectedWorldBuildingIds
      );
    }

    // 优先级5：术语信息（概念解释）
    if (params.selectedTerminologyIds.length > 0) {
      console.log('✅ 添加术语信息（优先级5）');
      this.promptHelper.addSelectedTerminologies(
        messageBuilder,
        params.terminologies,
        params.selectedTerminologyIds
      );
    }
  }

  /**
   * 构建优化后的消息
   * @param params 生成参数
   * @returns 消息数组
   */
  private buildOptimizedMessages(params: GenerateContentParams): Message[] {
    const messageBuilder = new MessageBuilder();

    // 1. 拆分的系统消息（最高权重）- 使用新的拆分模式
    console.log('🔧 使用拆分系统消息模式，提升AI理解精度');
    const systemMessages = aiWritingPrompts.buildSystemMessages(params.requirements);
    systemMessages.forEach(msg => {
      messageBuilder.addSystemMessage(msg.content);
    });

    // 2. 关联元素消息（中等权重，按优先级顺序）
    this.addAssociationElementsInOrder(messageBuilder, params);

    // 3. 写作风格消息（独立消息，高权重）
    if (params.writingStyle) {
      const styleMessage = aiWritingPrompts.generateWritingStyleMessage(params.writingStyle);
      messageBuilder.addUserMessage(styleMessage);
    }

    // 4. 核心剧情消息（独立消息，高权重）
    if (params.corePlot) {
      const plotMessage = aiWritingPrompts.generateCorePlotMessage(params.corePlot);
      messageBuilder.addUserMessage(plotMessage);
    }

    // 5. 创作指令消息（最高权重）
    const instruction = this.generateSmartInstruction(params);
    messageBuilder.addUserMessage(instruction);

    return messageBuilder.build();
  }

  /**
   * 记录性能指标
   * @param params 生成参数
   * @param messages 消息数组
   */
  private logPerformanceMetrics(params: GenerateContentParams, messages: Message[]): void {
    const estimatedTokens = this.estimateTokenCount(messages);

    // 统计不同类型的消息数量
    const systemMessageCount = messages.filter(m => m.role === 'system').length;
    const userMessageCount = messages.filter(m => m.role === 'user').length;
    const assistantMessageCount = messages.filter(m => m.role === 'assistant').length;

    console.log('🔍 AI写作服务性能指标:', {
      messageCount: messages.length,
      systemMessages: systemMessageCount,
      userMessages: userMessageCount,
      assistantMessages: assistantMessageCount,
      estimatedTokens: estimatedTokens,
      hasAssociations: this.hasSelectedAssociations(params),
      continueMode: params.continueMode,
      usingSplitMode: systemMessageCount > 1, // 拆分模式会有多个系统消息
      timestamp: new Date().toISOString()
    });

    // 性能警告
    if (estimatedTokens > 8000) {
      console.warn('⚠️ 消息Token数量较大，可能影响性能:', estimatedTokens);
    }

    if (messages.length > 25) { // 调整阈值，因为拆分模式会增加消息数量
      console.warn('⚠️ 消息数量较多，可能影响响应速度:', messages.length);
    }

    // 拆分模式的特殊日志
    if (systemMessageCount > 1) {
      console.log('✅ 使用拆分系统消息模式，系统消息数量:', systemMessageCount);
    }
  }

  /**
   * 估算Token数量
   * @param messages 消息数组
   * @returns 估算的Token数量
   */
  private estimateTokenCount(messages: Message[]): number {
    const totalLength = messages.reduce((sum, message) => sum + message.content.length, 0);
    // 粗略估算：1个Token约等于4个字符（中文）或0.75个单词（英文）
    return Math.ceil(totalLength / 3);
  }

  /**
   * 生成内容
   * @param params 生成参数
   * @param callbacks 回调函数
   */
  async generateContent(
    params: GenerateContentParams,
    callbacks: GenerateContentCallbacks
  ): Promise<GenerateContentResult> {
    try {
      callbacks.onStart?.();

      console.log('🚀 AI写作服务启动，使用优化后的提示词系统');

      // 记录性能指标
      const startTime = Date.now();

      // 生成创作提示
      const hints = aiWritingPrompts.generateCreationHints({
        hasCharacters: params.selectedCharacterIds.length > 0,
        hasOutlines: !!(params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0),
        hasWorldBuilding: params.selectedWorldBuildingIds.length > 0,
        hasTerminologies: params.selectedTerminologyIds.length > 0,
        hasChapters: params.selectedChapterIds.length > 0,
        writingStyle: params.writingStyle
      });

      console.log('💡 创作提示:', hints);

      let messages: Message[];

      // 根据是否有对话历史选择不同的消息构建策略
      if (params.conversationHistory && params.conversationHistory.length > 0 && params.continueMode !== 'new') {
        console.log('📚 使用现有对话历史继续对话，历史长度:', params.conversationHistory.length);

        // 使用 DialogContinuationService 构建消息
        const messageBuilder = new MessageBuilder();
        dialogContinuationService.buildMessages(
          messageBuilder,
          params.conversationHistory,
          params.continueMode as 'new' | 'continue' | 'rewrite' | 'analyze',
          params.continuePrompt
        );
        messages = messageBuilder.build();
      } else {
        console.log('🆕 创建新对话，使用优化后的消息构建');

        // 使用优化后的消息构建方法
        messages = this.buildOptimizedMessages(params);
      }

      // 记录消息构建性能
      const buildTime = Date.now() - startTime;
      console.log('⏱️ 消息构建耗时:', buildTime, 'ms');

      // 记录性能指标
      this.logPerformanceMetrics(params, messages);

      // 确保流式输出已启用
      const streamingEnabled = this.apiSettings.getStreamingEnabled();
      console.log('📡 流式输出状态:', streamingEnabled ? '已启用' : '已禁用');

      // 获取API设置中的maxTokens值，与其他AI服务保持一致
      const maxTokens = this.apiSettings.getMaxTokens?.() || 65000;
      console.log('🔧 使用maxTokens设置:', maxTokens);

      // 使用统一的AI流式调用方法
      const result = await this.callAIStreaming(
        messages,
        (chunk: string) => {
          // 直接处理每个流式响应块
          if (callbacks.onStreamChunk) {
            callbacks.onStreamChunk(chunk);
          }
        },
        {
          streaming: true
        }
      );

      // 处理结果
      const generatedContent = result.text.trim();

      // 记录生成性能
      const totalTime = Date.now() - startTime;
      console.log('✅ 内容生成完成，总耗时:', totalTime, 'ms');

      // 构建新的对话历史
      let updatedHistory: ConversationMessage[] = [];

      // 如果有现有对话历史，并且是继续对话模式，则使用现有对话历史
      if (params.conversationHistory && params.conversationHistory.length > 0 && params.continueMode !== 'new') {
        console.log('📚 使用现有对话历史构建更新后的对话历史');
        // 复制现有对话历史
        updatedHistory = [...params.conversationHistory];
      } else {
        console.log('🆕 创建新的对话历史');
        // 将当前使用的消息转换为对话历史格式
        updatedHistory = messages.map(msg => ({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: msg.content
        }));
      }

      // 添加AI的回复到对话历史的末尾
      updatedHistory.push({
        role: 'assistant',
        content: generatedContent
      });

      const contentResult: GenerateContentResult = {
        text: generatedContent,
        success: true,
        conversationHistory: updatedHistory
      };

      callbacks.onComplete?.(contentResult);
      return contentResult;

    } catch (error: any) {
      console.error('生成内容失败:', error);

      const errorResult: GenerateContentResult = {
        text: '',
        success: false,
        error: error instanceof Error ? error.message : '生成内容时发生错误'
      };

      callbacks.onError?.(error);
      return errorResult;
    }
  }
}

// 导出服务实例
export const aiWritingService = new AIWritingService();
