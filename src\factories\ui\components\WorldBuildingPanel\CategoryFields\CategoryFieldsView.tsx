"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { createFieldView } from '../CategoryFieldsUtils';

interface CategoryFieldsViewProps {
  worldBuilding: WorldBuilding;
}

/**
 * 类别特定字段查看组件
 */
export const CategoryFieldsView: React.FC<CategoryFieldsViewProps> = ({ worldBuilding }) => {
  const category = worldBuilding.category || '';

  // 根据不同类别显示特定字段
  switch (category) {
    case 'geography':
      return (
        <div className="space-y-3">
          {createFieldView('description', '地理描述', worldBuilding)}
          {createFieldView('significance', '在故事中的意义', worldBuilding)}
          {createFieldView('inhabitants', '居民/生物', worldBuilding)}
        </div>
      );

    case 'history':
      return (
        <div className="space-y-3">
          {createFieldView('event', '事件描述', worldBuilding)}
          {createFieldView('timeframe', '时间框架', worldBuilding)}
          {createFieldView('keyFigures', '关键人物', worldBuilding)}
          {createFieldView('impact', '对当前世界的影响', worldBuilding)}
        </div>
      );

    case 'culture':
      return (
        <div className="space-y-3">
          {createFieldView('overview', '文化概述', worldBuilding)}
          {createFieldView('customs', '重要习俗', worldBuilding)}
          {createFieldView('beliefs', '信仰与禁忌', worldBuilding)}
          {createFieldView('arts', '艺术与表达', worldBuilding)}
        </div>
      );

    case 'race':
      return (
        <div className="space-y-3">
          {createFieldView('characteristics', '种族特征', worldBuilding)}
          {createFieldView('society', '社会结构', worldBuilding)}
          {createFieldView('relations', '与其他种族的关系', worldBuilding)}
          {createFieldView('specialAbilities', '特殊能力', worldBuilding)}
        </div>
      );

    case 'magic':
      return (
        <div className="space-y-3">
          {createFieldView('principles', '魔法原理', worldBuilding)}
          {createFieldView('limitations', '限制与代价', worldBuilding)}
          {createFieldView('practitioners', '习得方式', worldBuilding)}
          {createFieldView('commonSpells', '常见法术', worldBuilding)}
        </div>
      );

    case 'organization':
      return (
        <div className="space-y-3">
          {createFieldView('purpose', '组织目的', worldBuilding)}
          {createFieldView('structure', '组织结构', worldBuilding)}
          {createFieldView('keyMembers', '关键成员', worldBuilding)}
          {createFieldView('resources', '资源与影响力', worldBuilding)}
        </div>
      );

    case 'item':
      return (
        <div className="space-y-3">
          {createFieldView('description', '物品描述', worldBuilding)}
          {createFieldView('origin', '来源/历史', worldBuilding)}
          {createFieldView('powers', '能力/功能', worldBuilding)}
          {createFieldView('significance', '在故事中的意义', worldBuilding)}
        </div>
      );

    case 'religion':
      return (
        <div className="space-y-3">
          {createFieldView('beliefs', '核心信仰', worldBuilding)}
          {createFieldView('practices', '仪式与实践', worldBuilding)}
          {createFieldView('organization', '宗教组织', worldBuilding)}
          {createFieldView('influence', '社会影响', worldBuilding)}
        </div>
      );

    case 'politics':
      return (
        <div className="space-y-3">
          {createFieldView('system', '政治体系', worldBuilding)}
          {createFieldView('keyFigures', '关键人物', worldBuilding)}
          {createFieldView('factions', '派系与利益集团', worldBuilding)}
          {createFieldView('conflicts', '当前冲突', worldBuilding)}
        </div>
      );

    // 其他类别使用通用字段
    default:
      return (
        <div className="space-y-3">
          {createFieldView('mainFeatures', '主要特征', worldBuilding)}
          {createFieldView('relevance', '与故事的关联', worldBuilding)}
          {createFieldView('uniqueAspects', '独特之处', worldBuilding)}

          {/* 显示所有其他属性 */}
          {Object.entries(worldBuilding.attributes || {}).map(([key, value]) => {
            // 排除已知的通用字段
            if (['mainFeatures', 'relevance', 'uniqueAspects'].includes(key)) {
              return null;
            }
            return (
              <div key={key}>
                <h4 className="text-sm font-medium text-gray-500">{key}</h4>
                <p className="mt-1 text-gray-700 whitespace-pre-wrap">{value}</p>
              </div>
            );
          })}
        </div>
      );
  }
};
