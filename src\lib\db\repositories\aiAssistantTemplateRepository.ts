import { db, AIAssistantTemplate } from '../dexie';

/**
 * AI助手模板数据访问层
 * 提供AI助手快速选择模板的数据操作接口
 */
export class AIAssistantTemplateRepository {
  
  /**
   * 获取指定书籍的所有模板
   * @param bookId 书籍ID
   * @returns 模板列表
   */
  async getTemplates(bookId: string): Promise<AIAssistantTemplate[]> {
    return await db.aiAssistantTemplates
      .where('bookId')
      .equals(bookId)
      .orderBy('usageCount')
      .reverse()
      .toArray();
  }

  /**
   * 获取默认模板
   * @param bookId 书籍ID
   * @returns 默认模板列表
   */
  async getDefaultTemplates(bookId: string): Promise<AIAssistantTemplate[]> {
    return await db.aiAssistantTemplates
      .where({ bookId, isDefault: true })
      .orderBy('usageCount')
      .reverse()
      .toArray();
  }

  /**
   * 创建新模板
   * @param template 模板数据
   * @returns 创建的模板
   */
  async createTemplate(template: Omit<AIAssistantTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<AIAssistantTemplate> {
    const now = new Date();
    const newTemplate: AIAssistantTemplate = {
      ...template,
      id: crypto.randomUUID(),
      createdAt: now,
      updatedAt: now
    };
    
    await db.aiAssistantTemplates.add(newTemplate);
    return newTemplate;
  }

  /**
   * 更新模板
   * @param templateId 模板ID
   * @param updates 更新数据
   * @returns 更新后的模板
   */
  async updateTemplate(
    templateId: string, 
    updates: Partial<Omit<AIAssistantTemplate, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<AIAssistantTemplate | null> {
    const template = await db.aiAssistantTemplates.get(templateId);
    if (!template) return null;

    const updatedTemplate: AIAssistantTemplate = {
      ...template,
      ...updates,
      updatedAt: new Date()
    };

    await db.aiAssistantTemplates.put(updatedTemplate);
    return updatedTemplate;
  }

  /**
   * 删除模板
   * @param templateId 模板ID
   */
  async deleteTemplate(templateId: string): Promise<void> {
    await db.aiAssistantTemplates.delete(templateId);
  }

  /**
   * 增加模板使用次数
   * @param templateId 模板ID
   */
  async incrementUsage(templateId: string): Promise<void> {
    const template = await db.aiAssistantTemplates.get(templateId);
    if (template) {
      await db.aiAssistantTemplates.update(templateId, {
        usageCount: template.usageCount + 1,
        updatedAt: new Date()
      });
    }
  }

  /**
   * 设置默认模板
   * @param templateId 模板ID
   * @param bookId 书籍ID
   */
  async setAsDefault(templateId: string, bookId: string): Promise<void> {
    await db.transaction('rw', db.aiAssistantTemplates, async () => {
      // 先取消其他默认模板
      await db.aiAssistantTemplates
        .where({ bookId, isDefault: true })
        .modify({ isDefault: false, updatedAt: new Date() });
      
      // 设置新的默认模板
      await db.aiAssistantTemplates.update(templateId, {
        isDefault: true,
        updatedAt: new Date()
      });
    });
  }

  /**
   * 取消默认模板
   * @param templateId 模板ID
   */
  async unsetDefault(templateId: string): Promise<void> {
    await db.aiAssistantTemplates.update(templateId, {
      isDefault: false,
      updatedAt: new Date()
    });
  }

  /**
   * 清空指定书籍的所有模板
   * @param bookId 书籍ID
   */
  async clearBookTemplates(bookId: string): Promise<void> {
    await db.aiAssistantTemplates.where('bookId').equals(bookId).delete();
  }

  /**
   * 获取最常用的模板
   * @param bookId 书籍ID
   * @param limit 返回数量限制
   * @returns 最常用的模板列表
   */
  async getMostUsedTemplates(bookId: string, limit: number = 5): Promise<AIAssistantTemplate[]> {
    return await db.aiAssistantTemplates
      .where('bookId')
      .equals(bookId)
      .orderBy('usageCount')
      .reverse()
      .limit(limit)
      .toArray();
  }

  /**
   * 搜索模板
   * @param bookId 书籍ID
   * @param query 搜索关键词
   * @returns 匹配的模板列表
   */
  async searchTemplates(bookId: string, query: string): Promise<AIAssistantTemplate[]> {
    const results = await db.aiAssistantTemplates
      .where('bookId')
      .equals(bookId)
      .toArray();
    
    // 客户端过滤
    const lowerQuery = query.toLowerCase();
    return results.filter(template => 
      template.name.toLowerCase().includes(lowerQuery) ||
      (template.description && template.description.toLowerCase().includes(lowerQuery))
    ).sort((a, b) => {
      // 优先显示名称开头匹配的
      const aStartsWith = a.name.toLowerCase().startsWith(lowerQuery);
      const bStartsWith = b.name.toLowerCase().startsWith(lowerQuery);
      
      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;
      
      // 然后按使用频率排序
      return b.usageCount - a.usageCount;
    });
  }

  /**
   * 复制模板
   * @param templateId 源模板ID
   * @param newName 新模板名称
   * @returns 复制的模板
   */
  async duplicateTemplate(templateId: string, newName: string): Promise<AIAssistantTemplate | null> {
    const sourceTemplate = await db.aiAssistantTemplates.get(templateId);
    if (!sourceTemplate) return null;

    const now = new Date();
    const duplicatedTemplate: AIAssistantTemplate = {
      ...sourceTemplate,
      id: crypto.randomUUID(),
      name: newName,
      isDefault: false, // 复制的模板不设为默认
      usageCount: 0, // 重置使用次数
      createdAt: now,
      updatedAt: now
    };

    await db.aiAssistantTemplates.add(duplicatedTemplate);
    return duplicatedTemplate;
  }

  /**
   * 获取模板详情（包含关联的上下文信息）
   * @param templateId 模板ID
   * @returns 模板详情
   */
  async getTemplateWithContexts(templateId: string): Promise<{
    template: AIAssistantTemplate;
    contexts: any[];
  } | null> {
    const template = await db.aiAssistantTemplates.get(templateId);
    if (!template) return null;

    // 获取关联的上下文
    const contexts = await db.aiAssistantContexts
      .where('id')
      .anyOf(template.contextIds)
      .toArray();

    return {
      template,
      contexts
    };
  }
}

// 导出单例实例
export const aiAssistantTemplateRepository = new AIAssistantTemplateRepository();
