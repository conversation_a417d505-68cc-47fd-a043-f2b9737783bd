import { MessageBuilder } from './MessageBuilder';

/**
 * 大纲节点类型定义
 */
export interface OutlineNode {
  id: string;
  title: string;
  type: string;
  description?: string;
  creativeNotes?: string; // AI生成的创作建议，包含台词设计、心理描写、节奏控制等指导
  content?: string;
  level?: number;
  order?: number;
  position?: string;
  parentId?: string;
  children?: OutlineNode[];
}

/**
 * 消息接口定义
 */
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  isSystemGenerated?: boolean;
  isContextMessage?: boolean;
}

/**
 * 大纲节点消息构建器
 * 专门用于构建单节点单条目的消息，确保AI能够精确理解每个大纲节点
 */
export class OutlineNodeMessageBuilder {
  private maxContentLength: number;
  private includeConfirmation: boolean;

  constructor(options: {
    maxContentLength?: number;
    includeConfirmation?: boolean;
  } = {}) {
    this.maxContentLength = options.maxContentLength || 200;
    this.includeConfirmation = options.includeConfirmation !== false;
  }

  /**
   * 为单个大纲节点构建消息组
   * @param node 大纲节点
   * @returns 消息数组（包含用户消息和AI确认消息）
   */
  buildNodeMessages(node: OutlineNode): Message[] {
    const messages: Message[] = [];

    // 构建基础节点信息消息（系统消息 - 了解自己的情况）
    const systemMessage: Message = {
      role: 'system',
      content: this.formatNodeContent(node),
      isSystemGenerated: true,
      isContextMessage: true
    };
    messages.push(systemMessage);

    // 如果有创作建议，添加独立的创作建议消息（系统消息 - 了解自己的情况）
    if (node.creativeNotes && node.creativeNotes.trim()) {
      const creativeNotesMessage: Message = {
        role: 'system',
        content: `【创作建议 - ${node.title}】\n${node.creativeNotes}`,
        isSystemGenerated: true,
        isContextMessage: true
      };
      messages.push(creativeNotesMessage);
    }

    // 构建AI确认消息（助手确认 - 了解自己的方式）
    if (this.includeConfirmation) {
      const confirmContent = node.creativeNotes
        ? `我已理解${this.getNodeTypeLabel(node.type)}"${node.title}"的信息和创作建议，将在创作中重点参考这些指导要点。`
        : `我已理解${this.getNodeTypeLabel(node.type)}"${node.title}"的信息，将在创作中重点考虑。`;

      const confirmMessage: Message = {
        role: 'assistant',
        content: confirmContent,
        isSystemGenerated: true
      };
      messages.push(confirmMessage);
    }

    return messages;
  }

  /**
   * 格式化单个节点内容
   * @param node 大纲节点
   * @returns 格式化后的内容字符串
   */
  private formatNodeContent(node: OutlineNode): string {
    let content = `【大纲节点】\n**${node.title}**`;

    // 添加节点类型
    if (node.type) {
      const typeLabel = this.getNodeTypeLabel(node.type);
      content += ` (${typeLabel})`;
    }

    // 添加描述
    if (node.description) {
      content += `\n描述：${node.description}`;
    }

    // 添加层级和位置信息
    const metaInfo = this.buildMetaInfo(node);
    if (metaInfo) {
      content += `\n${metaInfo}`;
    }

    // 添加内容摘要（如果有且不为空）
    if (node.content && node.content.trim()) {
      const truncatedContent = this.truncateContent(node.content);
      content += `\n内容：${truncatedContent}`;
    }

    return content;
  }

  /**
   * 构建AI确认消息
   * @param node 大纲节点
   * @returns 确认消息内容
   */
  private buildConfirmationMessage(node: OutlineNode): string {
    const nodeType = this.getNodeTypeLabel(node.type);
    return `我已理解${nodeType}"${node.title}"的信息，将在续写中重点考虑。`;
  }

  /**
   * 构建节点元信息
   * @param node 大纲节点
   * @returns 元信息字符串
   */
  private buildMetaInfo(node: OutlineNode): string {
    const metaParts: string[] = [];

    // 添加层级信息
    if (node.level !== undefined) {
      metaParts.push(`层级：第${node.level}级`);
    }

    // 添加位置信息
    if (node.position) {
      metaParts.push(`位置：${node.position}`);
    } else if (node.order !== undefined) {
      metaParts.push(`顺序：第${node.order + 1}个`);
    }

    return metaParts.length > 0 ? metaParts.join(' | ') : '';
  }

  /**
   * 截断内容到指定长度
   * @param content 原始内容
   * @returns 截断后的内容
   */
  private truncateContent(content: string): string {
    if (content.length <= this.maxContentLength) {
      return content;
    }

    // 智能截断：尽量在句号、感叹号、问号处截断
    const truncated = content.substring(0, this.maxContentLength);
    const lastPunctuation = Math.max(
      truncated.lastIndexOf('。'),
      truncated.lastIndexOf('！'),
      truncated.lastIndexOf('？'),
      truncated.lastIndexOf('.')
    );

    if (lastPunctuation > this.maxContentLength * 0.7) {
      return truncated.substring(0, lastPunctuation + 1) + '...';
    }

    return truncated + '...';
  }

  /**
   * 获取节点类型标签
   * @param type 节点类型
   * @returns 中文标签
   */
  private getNodeTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'volume': '总纲/卷',
      'event': '事件刚',
      'chapter': '章节',
      'plot': '剧情节点',
      'dialogue': '对话节点',
      'synopsis': '核心故事梗概',
      'section': '段落',
      'character': '人物',
      'location': '地点',
      'timeline': '时间线'
    };
    return labels[type] || '节点';
  }

  /**
   * 批量构建多个节点的消息
   * @param nodes 节点数组
   * @param includeOverview 是否包含总览消息
   * @returns 完整的消息数组
   */
  buildBatchMessages(nodes: OutlineNode[], includeOverview: boolean = true): Message[] {
    const messages: Message[] = [];

    // 添加总览消息
    if (includeOverview && nodes.length > 0) {
      messages.push({
        role: 'user',
        content: `【大纲节点信息】\n用户特别选择了以下${nodes.length}个大纲节点：`,
        isSystemGenerated: true,
        isContextMessage: true
      });
    }

    // 为每个节点添加消息
    for (const node of nodes) {
      const nodeMessages = this.buildNodeMessages(node);
      messages.push(...nodeMessages);
    }

    // 添加总结确认消息
    if (this.includeConfirmation && nodes.length > 1) {
      messages.push({
        role: 'assistant',
        content: `我已完整理解所有${nodes.length}个大纲节点的信息，将在续写中充分考虑这些内容。`,
        isSystemGenerated: true
      });
    }

    return messages;
  }

  /**
   * 将消息添加到MessageBuilder
   * @param messageBuilder MessageBuilder实例
   * @param messages 要添加的消息数组
   */
  addMessagesToBuilder(messageBuilder: MessageBuilder, messages: Message[]): void {
    for (const message of messages) {
      switch (message.role) {
        case 'user':
          messageBuilder.addUserMessage(
            message.content,
            undefined,
            message.isSystemGenerated,
            message.isContextMessage
          );
          break;
        case 'assistant':
          messageBuilder.addAssistantMessage(
            message.content,
            message.isSystemGenerated
          );
          break;
        case 'system':
          messageBuilder.addSystemMessage(
            message.content,
            message.isSystemGenerated,
            message.isContextMessage
          );
          break;
      }
    }
  }

  /**
   * 验证节点数据完整性
   * @param node 节点数据
   * @returns 验证结果
   */
  validateNode(node: OutlineNode): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!node.id) {
      issues.push('节点缺少ID');
    }

    if (!node.title || node.title.trim() === '') {
      issues.push('节点缺少标题');
    }

    if (!node.type) {
      issues.push('节点缺少类型');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

/**
 * 创建大纲节点消息构建器的工厂函数
 * @param options 配置选项
 * @returns OutlineNodeMessageBuilder实例
 */
export function createOutlineNodeMessageBuilder(options?: {
  maxContentLength?: number;
  includeConfirmation?: boolean;
}): OutlineNodeMessageBuilder {
  return new OutlineNodeMessageBuilder(options);
}
