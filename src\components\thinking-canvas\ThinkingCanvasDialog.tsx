"use client";

import React from 'react';
import ThinkingCanvasManager from './ThinkingCanvasManager';
import { ThinkingCanvasData } from '@/types/thinking-canvas';

interface ThinkingCanvasDialogProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: ThinkingCanvasData;
  onSave?: (data: ThinkingCanvasData) => void;
}

/**
 * 思考画布主弹窗组件
 * 现在使用新的ThinkingCanvasManager实现，支持传入特定的思考画布数据
 */
const ThinkingCanvasDialog: React.FC<ThinkingCanvasDialogProps> = ({
  isOpen,
  onClose,
  initialData,
  onSave
}) => {
  return (
    <ThinkingCanvasManager
      isOpen={isOpen}
      onClose={onClose}
      initialData={initialData}
      onSave={onSave}
    />
  );
};

export default ThinkingCanvasDialog;
