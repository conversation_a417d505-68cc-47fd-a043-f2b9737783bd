"use client";

import React from 'react';
import { UnifiedAssociationButton } from '@/components/ui/UnifiedAssociationButton';
import { OutlineManagementButton } from '@/components/ui/OutlineManagementButton';
import { ACEFrameworkButton } from '@/components/ui/ACEFrameworkButton';
import { ShortStoryMode } from '@/factories/ai/services/types/ShortStoryTypes';

interface ShortStoryLeftPanelProps {
  bookId: string;
  userInput: string;
  storyMode: ShortStoryMode;
  targetSegments: number;
  selectedCharacterIds: string[];
  selectedWorldBuildingIds: string[];
  selectedTerminologyIds: string[];
  selectedOutlineNodeIds: string[];
  customPhilosophy: string;
  selectedACEFrameworkIds: string[];
  error: string | null;

  onUserInputChange: (value: string) => void;
  onStoryModeChange: (mode: ShortStoryMode) => void;
  onTargetSegmentsChange: (count: number) => void;
  onCharacterIdsChange: (ids: string[]) => void;
  onWorldBuildingIdsChange: (ids: string[]) => void;
  onTerminologyIdsChange: (ids: string[]) => void;
  onOutlineNodeIdsChange: (ids: string[]) => void;
  onCustomPhilosophyChange: (value: string) => void;
  onACEFrameworkIdsChange: (ids: string[]) => void;
}

/**
 * 短篇创作左侧面板
 * 包含创作参数设置和关联元素选择
 */
export const ShortStoryLeftPanel: React.FC<ShortStoryLeftPanelProps> = ({
  bookId,
  userInput,
  storyMode,
  targetSegments,
  selectedCharacterIds,
  selectedWorldBuildingIds,
  selectedTerminologyIds,
  selectedOutlineNodeIds,
  customPhilosophy,
  selectedACEFrameworkIds,
  error,
  onUserInputChange,
  onStoryModeChange,
  onTargetSegmentsChange,
  onCharacterIdsChange,
  onWorldBuildingIdsChange,
  onTerminologyIdsChange,
  onOutlineNodeIdsChange,
  onCustomPhilosophyChange,
  onACEFrameworkIdsChange
}) => {

  // 折叠状态管理
  const [expandedSections, setExpandedSections] = React.useState({
    input: true,
    mode: false,
    segments: false,
    associations: false,
    aceFrameworks: false,
    tips: false
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };



  // 故事模式选项 - 重新设计哲学
  const storyModeOptions = [
    {
      value: 'mystery',
      label: '悬疑推理',
      description: '逻辑严密的线索布局，真相层层剥离',
      philosophy: '信息差制造 + 逻辑链条构建',
      icon: '🔍'
    },
    {
      value: 'thriller',
      label: '心理惊悚',
      description: '心理压迫感递增，情绪张力爆发',
      philosophy: '心理暗示 + 情绪操控技巧',
      icon: '⚡'
    },
    {
      value: 'horror',
      label: '恐怖氛围',
      description: '未知恐惧渗透，现实扭曲感营造',
      philosophy: '恐惧心理学 + 现实感破坏',
      icon: '👻'
    },
    {
      value: 'drama',
      label: '情感剧情',
      description: '人性深度挖掘，情感冲突爆发',
      philosophy: '情感共鸣 + 人性矛盾展现',
      icon: '💭'
    },
    {
      value: 'comedy',
      label: '反转喜剧',
      description: '荒诞与现实交织，黑色幽默渗透',
      philosophy: '认知反差 + 荒诞现实主义',
      icon: '😄'
    },
    {
      value: 'philosophical',
      label: '哲学思辨',
      description: '深层思考引导，存在意义探讨',
      philosophy: '思维实验 + 哲学悖论构建',
      icon: '🤔'
    },
    {
      value: 'custom',
      label: '自定义哲学',
      description: '根据具体需求定制创作理念',
      philosophy: customPhilosophy || '请在下方输入您的创作要求',
      icon: '🎨'
    }
  ];

  // 获取当前选中模式的信息
  const currentMode = storyModeOptions.find(option => option.value === storyMode) || storyModeOptions[0];

  return (
    <div className="w-1/3 pr-5 border-r border-gray-200 overflow-y-auto">
      <div className="space-y-3">

        {/* 创作需求 - 始终展开 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm">
          <div className="p-4">
            <h3 className="text-base font-semibold text-blue-800 mb-3 flex items-center">
              <span className="mr-2">✍️</span>
              创作需求
            </h3>
            <textarea
              value={userInput}
              onChange={(e) => onUserInputChange(e.target.value)}
              placeholder="描述你想要创作的短篇内容，例如：&#10;• 一个急诊科医生发现连续五个'意外'伤者的异常&#10;• 参加游戏的人只有我能看到死亡预告&#10;• 心理咨询师发现来访者的矛盾细节"
              className="w-full h-28 px-3 py-2 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"
            />
            <div className="mt-2 text-xs text-blue-600">
              💡 描述异常现象或矛盾情况，AI会设计悬念结构
            </div>
          </div>
        </div>

        {/* 创作哲学模式 - 优化选择 */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex items-center mb-3">
              <span className="mr-2">{currentMode.icon}</span>
              <h3 className="text-base font-semibold text-gray-800">创作哲学</h3>
            </div>

            {/* 自定义下拉选择 */}
            <div className="relative">
              <button
                onClick={() => toggleSection('mode')}
                className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-sm bg-white cursor-pointer transition-all hover:border-purple-200 shadow-sm text-left flex items-center justify-between"
              >
                <div className="flex items-center">
                  <span className="mr-2">{currentMode.icon}</span>
                  <span className="font-medium">{currentMode.label}</span>
                  <span className="ml-2 text-gray-500 text-xs">- {currentMode.description}</span>
                </div>
                <svg
                  className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.mode ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* 下拉选项 */}
              {expandedSections.mode && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border-2 border-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                  {storyModeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => {
                        onStoryModeChange(option.value as ShortStoryMode);
                        setExpandedSections(prev => ({ ...prev, mode: false }));
                      }}
                      className={`w-full px-4 py-3 text-left hover:bg-purple-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                        storyMode === option.value ? 'bg-purple-100 text-purple-800' : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">{option.icon}</span>
                        <div className="flex-1">
                          <div className="font-medium text-sm">{option.label}</div>
                          <div className="text-xs text-gray-600 mt-1">{option.description}</div>
                          <div className="text-xs text-purple-600 mt-1 font-medium">
                            💡 {option.philosophy}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 当前选择的哲学理念显示 */}
            <div className="mt-3 p-3 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-100">
              <div className="text-sm text-gray-800 mb-1">
                <strong>{currentMode.label}</strong>
              </div>
              <div className="text-xs text-gray-600 mb-2">
                {currentMode.description}
              </div>
              <div className="text-xs text-purple-700 font-medium bg-white/60 px-2 py-1 rounded">
                💡 {currentMode.philosophy}
              </div>
            </div>
          </div>
        </div>

        {/* 自定义哲学输入框 - 当选择自定义时显示 */}
        {storyMode === 'custom' && (
          <div className="bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 rounded-xl border-2 border-orange-200 shadow-lg animate-in slide-in-from-top-2 duration-500 ease-out">
            <div className="p-5">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-amber-400 rounded-lg flex items-center justify-center mr-3">
                  <span className="text-white text-sm">🎨</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-orange-800">自定义创作哲学</h3>
                  <p className="text-xs text-orange-600">定制您独特的创作理念和风格要求</p>
                </div>
              </div>

              <div className="relative">
                <textarea
                  value={customPhilosophy}
                  onChange={(e) => onCustomPhilosophyChange(e.target.value)}
                  placeholder="请详细描述您的创作哲学和具体要求，例如：&#10;&#10;🎭 叙述风格：&#10;• 采用意识流写作手法，注重内心独白&#10;• 使用第二人称叙述，增强代入感&#10;&#10;🎨 艺术手法：&#10;• 运用象征主义，每个物件都有深层含义&#10;• 借鉴魔幻现实主义，现实与幻想交织&#10;&#10;💭 哲学思考：&#10;• 探讨存在主义主题，关注个体选择&#10;• 融入后现代解构思维，质疑传统叙事"
                  className="w-full h-32 px-4 py-3 border-2 border-orange-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-300 resize-none text-sm bg-white/90 backdrop-blur-sm transition-all placeholder:text-gray-400"
                />

                {/* 字数统计 */}
                <div className="absolute bottom-2 right-3 text-xs text-gray-400">
                  {customPhilosophy.length}/500
                </div>
              </div>

              <div className="mt-3 p-3 bg-white/60 rounded-lg border border-orange-100">
                <div className="flex items-start space-x-2">
                  <span className="text-orange-500 text-sm">💡</span>
                  <div className="text-xs text-orange-700">
                    <p className="font-medium mb-1">创作提示：</p>
                    <p>详细的哲学要求能让AI更好地理解您的创作意图，生成更符合期望的短篇内容。建议从叙述风格、艺术手法、哲学思考等维度进行描述。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}



        {/* 四节奏分段设置 - 折叠式 */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <button
            onClick={() => toggleSection('segments')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center">
              <span className="mr-2">📊</span>
              <div>
                <h3 className="text-base font-semibold text-gray-800">四节奏分段</h3>
                <p className="text-sm text-gray-600">{targetSegments}段 - 铺垫({Math.ceil(targetSegments * 0.25)}) 挤压({Math.ceil(targetSegments * 0.35)}) 高潮({Math.ceil(targetSegments * 0.25)}) 结局({Math.floor(targetSegments * 0.15)})</p>
              </div>
            </div>
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.segments ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.segments && (
            <div className="px-4 pb-4 border-t border-gray-100">
              <div className="mt-3 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    总段落数量（最低20段）
                  </label>
                  <select
                    value={targetSegments}
                    onChange={(e) => onTargetSegmentsChange(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                  >
                    <option value={20}>20段 - 标准四节奏</option>
                    <option value={24}>24段 - 扩展四节奏</option>
                    <option value={28}>28段 - 深度四节奏</option>
                    <option value={32}>32段 - 完整四节奏</option>
                    <option value={40}>40段 - 史诗四节奏</option>
                  </select>
                </div>

                {/* 四节奏可视化 */}
                <div className="bg-gradient-to-r from-blue-50 via-orange-50 via-red-50 to-purple-50 p-3 rounded-lg">
                  <div className="grid grid-cols-4 gap-2 text-xs">
                    <div className="text-center">
                      <div className="w-full h-2 bg-blue-400 rounded mb-1"></div>
                      <div className="text-blue-600 font-medium">铺垫期</div>
                      <div className="text-gray-600">{Math.ceil(targetSegments * 0.25)}段</div>
                    </div>
                    <div className="text-center">
                      <div className="w-full h-2 bg-orange-400 rounded mb-1"></div>
                      <div className="text-orange-600 font-medium">挤压期</div>
                      <div className="text-gray-600">{Math.ceil(targetSegments * 0.35)}段</div>
                    </div>
                    <div className="text-center">
                      <div className="w-full h-2 bg-red-400 rounded mb-1"></div>
                      <div className="text-red-600 font-medium">高潮期</div>
                      <div className="text-gray-600">{Math.ceil(targetSegments * 0.25)}段</div>
                    </div>
                    <div className="text-center">
                      <div className="w-full h-2 bg-purple-400 rounded mb-1"></div>
                      <div className="text-purple-600 font-medium">结局期</div>
                      <div className="text-gray-600">{Math.floor(targetSegments * 0.15)}段</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 关联元素 - 折叠式 */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <button
            onClick={() => toggleSection('associations')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center">
              <span className="mr-2">🔗</span>
              <div>
                <h3 className="text-base font-semibold text-gray-800">关联元素</h3>
                <p className="text-sm text-gray-600">
                  {[
                    selectedCharacterIds.length > 0 && `人物(${selectedCharacterIds.length})`,
                    selectedWorldBuildingIds.length > 0 && `世界观(${selectedWorldBuildingIds.length})`,
                    selectedTerminologyIds.length > 0 && `术语(${selectedTerminologyIds.length})`,
                    selectedOutlineNodeIds.length > 0 && `大纲(${selectedOutlineNodeIds.length})`
                  ].filter(Boolean).join(' ') || '未选择关联元素'}
                </p>
              </div>
            </div>
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.associations ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.associations && (
            <div className="px-4 pb-4 border-t border-gray-100">
              <div className="mt-3 space-y-3">
                {/* 关联元素选择 - 使用统一关联按钮 */}
                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <UnifiedAssociationButton
                      bookId={bookId}
                      selectedCharacterIds={selectedCharacterIds}
                      selectedWorldBuildingIds={selectedWorldBuildingIds}
                      selectedTerminologyIds={selectedTerminologyIds}
                      onAssociationsChange={(associations) => {
                        onCharacterIdsChange(associations.characterIds);
                        onWorldBuildingIdsChange(associations.worldBuildingIds);
                        onTerminologyIdsChange(associations.terminologyIds);
                      }}
                      variant="compact"
                      className="flex-1"
                    />

                    <OutlineManagementButton
                      bookId={bookId}
                      selectedOutlineNodeIds={selectedOutlineNodeIds}
                      onOutlineNodesChange={onOutlineNodeIdsChange}
                      variant="compact"
                      className="flex-1"
                    />
                  </div>

                  {/* 显示当前选择的统计 */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-blue-50 p-2 rounded text-center">
                      <div className="text-blue-600 font-medium">👤 人物</div>
                      <div className="text-blue-800">{selectedCharacterIds.length}个</div>
                    </div>
                    <div className="bg-green-50 p-2 rounded text-center">
                      <div className="text-green-600 font-medium">🌍 世界观</div>
                      <div className="text-green-800">{selectedWorldBuildingIds.length}个</div>
                    </div>
                    <div className="bg-purple-50 p-2 rounded text-center">
                      <div className="text-purple-600 font-medium">📚 术语</div>
                      <div className="text-purple-800">{selectedTerminologyIds.length}个</div>
                    </div>
                    <div className="bg-orange-50 p-2 rounded text-center">
                      <div className="text-orange-600 font-medium">📋 大纲</div>
                      <div className="text-orange-800">{selectedOutlineNodeIds.length}个</div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 text-center mt-2 p-2 bg-gray-50 rounded">
                  💡 点击"管理关联"按钮选择关联元素，AI会在创作时参考这些信息
                </div>
              </div>
            </div>
          )}
        </div>

        {/* ACE框架选择 - 按钮模式 */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="mr-2">🎯</span>
                <div>
                  <h3 className="text-base font-semibold text-gray-800">ACE创作框架</h3>
                  <p className="text-sm text-gray-600">
                    {selectedACEFrameworkIds.length > 0
                      ? `已选择 ${selectedACEFrameworkIds.length} 个专业框架`
                      : '选择专业创作框架'}
                  </p>
                </div>
              </div>

              <ACEFrameworkButton
                selectedFrameworkIds={selectedACEFrameworkIds}
                onFrameworkIdsChange={onACEFrameworkIdsChange}
                variant="compact"
                className="ml-3"
              />
            </div>

            {selectedACEFrameworkIds.length > 0 && (
              <div className="mt-3 text-xs text-purple-600 bg-purple-50 p-2 rounded">
                💡 已选择的ACE框架将为AI提供专业的剧情设计、节奏控制、悬念营造等创作技巧
              </div>
            )}
          </div>
        </div>

        {/* 创作提示 - 折叠式 */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <button
            onClick={() => toggleSection('tips')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center">
              <span className="mr-2">💡</span>
              <div>
                <h3 className="text-base font-semibold text-gray-800">创作技巧</h3>
                <p className="text-sm text-gray-600">短篇创作的核心理念和实用技巧</p>
              </div>
            </div>
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.tips ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {expandedSections.tips && (
            <div className="px-4 pb-4 border-t border-gray-100">
              <div className="mt-3 space-y-2 text-xs">
                <div className="flex items-start space-x-2 p-2 bg-amber-50 rounded">
                  <span className="text-amber-500">💡</span>
                  <span className="text-amber-700">短篇核心是"透露一半，逐渐补全"的悬念设计</span>
                </div>
                <div className="flex items-start space-x-2 p-2 bg-blue-50 rounded">
                  <span className="text-blue-500">🎯</span>
                  <span className="text-blue-700">开头要有强烈异常感，但不解释原因</span>
                </div>
                <div className="flex items-start space-x-2 p-2 bg-green-50 rounded">
                  <span className="text-green-500">🔍</span>
                  <span className="text-green-700">利用专业人士视角发现普通人看不到的异常</span>
                </div>
                <div className="flex items-start space-x-2 p-2 bg-purple-50 rounded">
                  <span className="text-purple-500">⚡</span>
                  <span className="text-purple-700">制造信息差，让读者"分不清真假"</span>
                </div>
                <div className="flex items-start space-x-2 p-2 bg-pink-50 rounded">
                  <span className="text-pink-500">🎭</span>
                  <span className="text-pink-700">数字分段作为节奏器，每段都要有推进</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 错误提示 - 紧凑式 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-3 shadow-sm">
            <div className="flex items-start space-x-2">
              <svg className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">生成失败</h4>
                <p className="text-xs text-red-600 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
