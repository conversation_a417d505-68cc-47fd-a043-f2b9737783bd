"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import PhaseAIAvatar, { PhaseType, AvatarState } from './PhaseAIAvatar';
import MermaidRenderer from './MermaidRenderer';
import { parseMessageContent, formatTextContent } from '../utils/messageParser';
import { FeedbackRating } from '../../../types/ai-feedback';
import SpecialContentRenderer from './SpecialContentRenderer';
import { FeedbackCommentDialog } from './FeedbackCommentDialog';
import { ChatMessage } from '../../../services/chat/SessionManager';

interface EnhancedMessageBubbleProps {
  message: ChatMessage;
  phase: PhaseType;
  isLatest?: boolean;
  isStreaming?: boolean;
  onAvatarClick?: () => void;
  onFeedback?: (messageId: string, rating: FeedbackRating, comment?: string) => void;
  currentFeedback?: {
    rating: FeedbackRating;
    comment?: string;
  } | null;
  // 编辑相关回调
  onMessageUpdate?: (updatedMessage: ChatMessage) => void;
  sessionId?: string; // 用于消息更新
  enableEditing?: boolean; // 是否启用编辑功能
  // 重新生成相关
  onRegenerate?: (messageId: string) => void; // 重新生成回调
  isRegenerating?: boolean; // 是否正在重新生成
  enableRegenerate?: boolean; // 是否启用重新生成功能
}

/**
 * 增强的消息气泡组件
 * 支持SVG头像、MAR渲染和丰富的视觉效果
 */
const EnhancedMessageBubble: React.FC<EnhancedMessageBubbleProps> = ({
  message,
  phase,
  isLatest = false,
  isStreaming = false,
  onAvatarClick,
  onFeedback,
  currentFeedback,
  onMessageUpdate,
  sessionId,
  enableEditing = true,
  onRegenerate,
  isRegenerating = false,
  enableRegenerate = true
}) => {
  const [showFeedbackButtons, setShowFeedbackButtons] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [pendingRating, setPendingRating] = useState<FeedbackRating | null>(null);

  // 编辑相关状态
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [isSaving, setIsSaving] = useState(false);
  // 阶段颜色配置
  const phaseColors = {
    intro: {
      primary: '#3B82F6',
      light: '#3B82F620',
      lighter: '#3B82F610',
      gradient: 'linear-gradient(135deg, #3B82F620, #3B82F610)'
    },
    setup: {
      primary: '#10B981',
      light: '#10B98120',
      lighter: '#10B98110',
      gradient: 'linear-gradient(135deg, #10B98120, #10B98110)'
    },
    compression: {
      primary: '#F59E0B',
      light: '#F59E0B20',
      lighter: '#F59E0B10',
      gradient: 'linear-gradient(135deg, #F59E0B20, #F59E0B10)'
    },
    climax: {
      primary: '#EF4444',
      light: '#EF444420',
      lighter: '#EF444410',
      gradient: 'linear-gradient(135deg, #EF444420, #EF444410)'
    },
    resolution: {
      primary: '#8B5CF6',
      light: '#8B5CF620',
      lighter: '#8B5CF610',
      gradient: 'linear-gradient(135deg, #8B5CF620, #8B5CF610)'
    },
    ending: {
      primary: '#EC4899',
      light: '#EC489920',
      lighter: '#EC489910',
      gradient: 'linear-gradient(135deg, #EC489920, #EC489910)'
    },
    // 兼容旧的命名
    buildup: {
      primary: '#10B981',
      light: '#10B98120',
      lighter: '#10B98110',
      gradient: 'linear-gradient(135deg, #10B98120, #10B98110)'
    }
  };

  const colors = phaseColors[phase] || phaseColors.intro; // 默认使用导语阶段的颜色

  // 处理反馈评分 - 打开评论对话框
  const handleFeedback = (rating: FeedbackRating) => {
    setPendingRating(rating);
    setShowCommentDialog(true);
    setShowFeedbackButtons(false);
  };

  // 确认反馈（包含评论）
  const handleConfirmFeedback = (rating: FeedbackRating, comment: string) => {
    if (onFeedback) {
      onFeedback(message.id, rating, comment);
    }
    setShowCommentDialog(false);
    setPendingRating(null);
  };

  // 取消反馈
  const handleCancelFeedback = () => {
    setShowCommentDialog(false);
    setPendingRating(null);
    setShowFeedbackButtons(true); // 重新显示评分按钮
  };

  // 开始编辑
  const handleStartEdit = () => {
    setEditContent(message.content);
    setIsEditing(true);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditContent(message.content);
    setIsEditing(false);
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    if (!sessionId || !onMessageUpdate || editContent.trim() === '') return;

    setIsSaving(true);
    try {
      // 导入ChatPersistenceService
      const { ChatPersistenceService } = await import('../../../services/chat/ChatPersistenceService');
      const chatPersistenceService = ChatPersistenceService.getInstance();

      // 创建更新后的消息对象
      const updatedMessage: ChatMessage = {
        ...message,
        content: editContent.trim(),
        isEdited: true,
        originalContent: message.originalContent || message.content,
        editHistory: [
          ...(message.editHistory || []),
          {
            content: message.content,
            timestamp: new Date(),
            reason: '用户编辑'
          }
        ]
      };

      // 更新消息
      await chatPersistenceService.updateMessageWithHistory(sessionId, message.id, updatedMessage);

      // 通知父组件
      onMessageUpdate(updatedMessage);

      setIsEditing(false);
      console.log('✅ 消息编辑成功:', message.id);
    } catch (error) {
      console.error('❌ 消息编辑失败:', error);
      alert('编辑失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 重新生成处理
  const handleRegenerate = () => {
    if (onRegenerate && !isRegenerating) {
      onRegenerate(message.id);
    }
  };

  // 检查是否可以编辑（用户消息和AI消息都可以编辑）
  const canEdit = enableEditing && !isStreaming && !isRegenerating;

  // 检查是否可以重新生成（只有AI消息可以重新生成）
  const canRegenerate = enableRegenerate && (message.role === 'assistant' || message.type === 'ai') && !isStreaming && !isEditing;

  // 反馈按钮配置
  const feedbackButtons = [
    {
      rating: 'good' as FeedbackRating,
      icon: '👍',
      label: '好',
      color: 'text-green-600 hover:bg-green-100',
      activeColor: 'bg-green-100 text-green-700'
    },
    {
      rating: 'average' as FeedbackRating,
      icon: '👌',
      label: '一般',
      color: 'text-yellow-600 hover:bg-yellow-100',
      activeColor: 'bg-yellow-100 text-yellow-700'
    },
    {
      rating: 'poor' as FeedbackRating,
      icon: '👎',
      label: '差',
      color: 'text-red-600 hover:bg-red-100',
      activeColor: 'bg-red-100 text-red-700'
    }
  ];

  // 解析消息内容，包括独立的 reasoning_content
  const parsedContent = parseMessageContent(message.content, message.reasoning_content);

  // 调试日志
  console.log('🔍 EnhancedMessageBubble 调试信息:', {
    messageId: message.id,
    hasReasoningContent: !!message.reasoning_content,
    reasoningContentLength: message.reasoning_content?.length || 0,
    parsedContentLength: parsedContent.length,
    parsedContentTypes: parsedContent.map(p => p.type)
  });

  // 获取头像状态
  const getAvatarState = (): AvatarState => {
    if (isStreaming) return 'responding';
    if (isLatest && message.type === 'ai') return 'excited';
    return 'default';
  };

  // 用户消息气泡 - 5美金艺术设计
  if (message.type === 'user' || message.role === 'user') {
    return (
      <motion.div
        className="flex justify-end mb-6"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
      >
        <div className="flex items-end space-x-4 max-w-[85%]">
          {isEditing ? (
            // 编辑模式 - 高端设计
            <motion.div
              className="bg-white rounded-2xl p-6 shadow-2xl border border-gray-100 min-w-[350px] backdrop-blur-sm"
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
              style={{
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(59, 130, 246, 0.1)'
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-gray-800">编辑消息</span>
                </div>
                <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  {editContent.length} 字符
                </div>
              </div>

              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full min-h-[100px] p-4 text-gray-800 bg-gray-50 rounded-xl border-0 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white resize-none text-sm leading-relaxed transition-all duration-200"
                style={{
                  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.06)'
                }}
                placeholder="编辑消息内容..."
                autoFocus
                disabled={isSaving}
              />

              <div className="flex items-center justify-between mt-4">
                <div className="text-xs text-gray-500 flex items-center space-x-2">
                  <span>{message.isEdited ? '再次编辑' : '首次编辑'}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="px-4 py-2 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 disabled:opacity-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    disabled={isSaving || editContent.trim() === ''}
                    className="px-6 py-2 text-xs bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    {isSaving && (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                    )}
                    {isSaving ? '保存中...' : '保存'}
                  </button>
                </div>
              </div>
            </motion.div>
          ) : (
            // 显示模式 - 高端设计
            <div
              className="relative group"
              style={{ maxWidth: '100%' }}
            >
              <div
                className="p-4 rounded-2xl text-white relative overflow-hidden transition-all duration-300 hover:shadow-xl"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
              >
                {/* 背景装饰 */}
                <div
                  className="absolute inset-0 opacity-10"
                  style={{
                    background: 'radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 50%)'
                  }}
                ></div>

                <div className="relative z-10">
                  <div className="whitespace-pre-wrap leading-relaxed text-sm">
                    {message.content}
                  </div>

                  {/* 底部工具栏 */}
                  <div className="flex items-center justify-between mt-3 pt-2 border-t border-white border-opacity-20">
                    <div className="text-xs opacity-80 flex items-center space-x-3">
                      <span>
                        {message.timestamp instanceof Date ? message.timestamp.toLocaleTimeString() : new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                      {message.isEdited && (
                        <span className="flex items-center space-x-1 text-white text-opacity-90" title="此消息已被编辑">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          <span>已编辑</span>
                        </span>
                      )}
                    </div>

                    {canEdit && (
                      <button
                        onClick={handleStartEdit}
                        className="opacity-70 hover:opacity-100 transition-all duration-200 text-white p-2 rounded-lg hover:bg-white hover:bg-opacity-20 transform hover:scale-110"
                        title="编辑消息"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 用户头像 - 高端设计 */}
          <div
            className="flex-shrink-0 w-12 h-12 rounded-full text-white flex items-center justify-center shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>
      </motion.div>
    );
  }

  // AI消息气泡
  return (
    <motion.div
      className="flex justify-start mb-4"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="flex items-start space-x-3 max-w-[85%]">
        {/* AI头像 */}
        <PhaseAIAvatar
          phase={phase}
          state={getAvatarState()}
          isAnimating={isStreaming}
          onClick={onAvatarClick}
          showSettingsIcon={!!onAvatarClick}
        />

        <div className="flex-1">
          {isEditing ? (
            // AI消息编辑模式 - 高端设计
            <motion.div
              className="bg-white rounded-2xl p-6 shadow-2xl border border-gray-100 min-w-[450px] backdrop-blur-sm"
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
              style={{
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(249, 115, 22, 0.1)'
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-gray-800">编辑AI回复</span>
                  <span className="text-xs bg-amber-100 text-amber-800 px-3 py-1 rounded-full font-medium">
                    ⚠️ 谨慎编辑
                  </span>
                </div>
                <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  {editContent.length} 字符
                </div>
              </div>

              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full min-h-[140px] p-4 text-gray-800 bg-gray-50 rounded-xl border-0 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:bg-white resize-none text-sm leading-relaxed transition-all duration-200"
                style={{
                  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.06)'
                }}
                placeholder="编辑AI回复内容..."
                autoFocus
                disabled={isSaving}
              />

              <div className="flex items-center justify-between mt-4">
                <div className="text-xs text-gray-500 flex items-center space-x-2">
                  <span>{message.isEdited ? '再次编辑' : '首次编辑'}</span>
                  <span className="text-amber-600">• 编辑AI回复可能影响对话连贯性</span>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="px-4 py-2 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 disabled:opacity-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    disabled={isSaving || editContent.trim() === ''}
                    className="px-6 py-2 text-xs bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    {isSaving && (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                    )}
                    {isSaving ? '保存中...' : '保存'}
                  </button>
                </div>
              </div>
            </motion.div>
          ) : (
            // AI消息显示模式 - 高端设计
            <div className="relative">
              <div
                className="rounded-2xl shadow-lg border border-gray-100 relative overflow-hidden transition-all duration-300 hover:shadow-xl"
                style={{
                  background: colors.gradient,
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
              >
                {/* 背景装饰 */}
                <div
                  className="absolute inset-0 opacity-5"
                  style={{
                    background: 'radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)'
                  }}
                ></div>

                <div className="relative z-10 p-4">
                  {/* 渲染解析后的内容 */}
                  {parsedContent.map((part, index) => {
                    if (part.type === 'mermaid') {
                      return (
                        <MermaidRenderer
                          key={part.id || `mermaid-${index}`}
                          code={part.content}
                          className="my-3"
                          onError={(error) => {
                            console.error('Mermaid渲染错误:', error);
                          }}
                        />
                      );
                    } else if (part.type === 'thinking' || part.type === 'reasoning') {
                      return (
                        <SpecialContentRenderer
                          key={part.id || `${part.type}-${index}`}
                          part={part}
                          onToggleCollapse={(partId, isCollapsed) => {
                            console.log(`${part.type} 内容折叠状态变更:`, partId, isCollapsed);
                          }}
                        />
                      );
                    } else {
                      return (
                        <div
                          key={part.id || `text-${index}`}
                          className="whitespace-pre-wrap leading-relaxed text-gray-800 text-sm"
                          dangerouslySetInnerHTML={{
                            __html: formatTextContent(part.content)
                          }}
                        />
                      );
                    }
                  })}

                  {/* 调试信息：显示 reasoning_content 是否存在 */}
                  {message.reasoning_content && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="text-xs text-yellow-800 font-medium mb-2">
                        🔍 调试：检测到 reasoning_content
                      </div>
                      <div className="text-xs text-yellow-700">
                        长度: {message.reasoning_content.length} 字符
                      </div>
                      <div className="text-xs text-yellow-600 mt-1 max-h-20 overflow-y-auto">
                        预览: {message.reasoning_content.substring(0, 100)}...
                      </div>
                    </div>
                  )}

                  {/* 流式输入指示器 */}
                  {(isStreaming || isRegenerating) && (
                    <motion.div
                      className="flex items-center space-x-2 mt-3 p-3 bg-white bg-opacity-50 rounded-lg backdrop-blur-sm"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      <div className="flex space-x-1">
                        <motion.div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: colors.primary }}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            delay: 0
                          }}
                        />
                        <motion.div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: colors.primary }}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            delay: 0.2
                          }}
                        />
                        <motion.div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: colors.primary }}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            delay: 0.4
                          }}
                        />
                      </div>
                      <span className="text-xs text-gray-600 font-medium">
                        {isRegenerating ? 'AI正在重新生成...' : 'AI正在思考...'}
                      </span>
                    </motion.div>
                  )}
                </div>

                {/* 底部操作栏 - 高端设计 */}
                {!isStreaming && !isRegenerating && (
                  <div className="border-t border-gray-200 bg-white bg-opacity-50 backdrop-blur-sm p-3">
                    <div className="flex items-center justify-between">
                      {/* 左侧：时间戳和状态 */}
                      <div className="text-xs text-gray-500 flex items-center space-x-3">
                        <span>
                          {message.timestamp instanceof Date ? message.timestamp.toLocaleTimeString() : new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                        {message.isEdited && (
                          <span className="flex items-center space-x-1 text-gray-400" title="此消息已被编辑">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            <span>已编辑</span>
                          </span>
                        )}
                      </div>

                      {/* 右侧：操作按钮 */}
                      <div className="flex items-center space-x-2">
                        {canEdit && (
                          <button
                            onClick={handleStartEdit}
                            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white hover:bg-opacity-70 rounded-lg transition-all duration-200 transform hover:scale-105"
                            title="编辑AI回复"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                        )}

                        {canRegenerate && (
                          <button
                            onClick={handleRegenerate}
                            disabled={isRegenerating}
                            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white hover:bg-opacity-70 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="重新生成回复"
                          >
                            <svg
                              className={`w-4 h-4 ${isRegenerating ? 'animate-spin' : ''}`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 反馈按钮区域 - 移到底部操作栏外面 */}
          {onFeedback && !isStreaming && !isRegenerating && !isEditing && (
            <div className="flex items-center justify-between mt-2 px-1">
              <div className="flex items-center space-x-2">
                {/* 阶段信息 */}
                <div className="flex items-center space-x-1">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: colors.primary }}
                  />
                  <span className="text-xs text-gray-500 capitalize">
                    {phase === 'intro' && '导语'}
                    {phase === 'setup' && '铺垫期'}
                    {phase === 'compression' && '爆发情绪'}
                    {phase === 'climax' && '反转'}
                    {phase === 'resolution' && '让读者解气'}
                    {phase === 'ending' && '大结局'}
                    {/* 兼容旧的命名 */}
                    {phase === 'buildup' && '铺垫期'}
                  </span>
                </div>
              </div>

              <div className="flex items-center space-x-1">
                {!showFeedbackButtons && !currentFeedback ? (
                  <button
                    onClick={() => setShowFeedbackButtons(true)}
                    className="text-xs text-gray-400 hover:text-gray-600 transition-colors px-1 py-0.5 rounded hover:bg-gray-100"
                    title="评价回复质量"
                  >
                    评价
                  </button>
                ) : showFeedbackButtons ? (
                  <div className="flex items-center space-x-1">
                    {feedbackButtons.map((button) => (
                      <button
                        key={button.rating}
                        onClick={() => handleFeedback(button.rating)}
                        className={`text-xs px-2 py-1 rounded transition-colors ${button.color}`}
                        title={button.label}
                      >
                        <span className="mr-1">{button.icon}</span>
                        {button.label}
                      </button>
                    ))}
                    <button
                      onClick={() => setShowFeedbackButtons(false)}
                      className="text-xs text-gray-400 hover:text-gray-600 px-1"
                      title="取消"
                    >
                      ✕
                    </button>
                  </div>
                ) : currentFeedback ? (
                  <div className="flex items-center space-x-1">
                    {feedbackButtons.map((button) => (
                      button.rating === currentFeedback.rating ? (
                        <div key={button.rating} className="flex items-center space-x-1">
                          <span
                            className={`text-xs px-2 py-1 rounded ${button.activeColor}`}
                            title={currentFeedback.comment ? `已评价: ${button.label} - ${currentFeedback.comment}` : `已评价: ${button.label}`}
                          >
                            <span className="mr-1">{button.icon}</span>
                            {button.label}
                            {currentFeedback.comment && (
                              <span className="ml-1 text-xs opacity-75">💬</span>
                            )}
                          </span>
                        </div>
                      ) : null
                    ))}
                    <button
                      onClick={() => setShowFeedbackButtons(true)}
                      className="text-xs text-gray-400 hover:text-gray-600 px-1"
                      title="修改评价"
                    >
                      ✏️
                    </button>
                  </div>
                ) : null}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 反馈评论对话框 */}
      <FeedbackCommentDialog
        isOpen={showCommentDialog}
        rating={pendingRating || 'good'}
        initialComment={currentFeedback?.comment || ''}
        onConfirm={handleConfirmFeedback}
        onCancel={handleCancelFeedback}
        aiResponse={message.content}
      />
    </motion.div>
  );
};

export default EnhancedMessageBubble;
