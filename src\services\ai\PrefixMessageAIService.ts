import { AIPersona, PhaseType } from '../../types/ai-persona';
import { ChatMessage } from '../../components/short-story/components/PhaseAIPanel';
import { UnifiedAIService, AIServiceType } from './BaseAIService';

// 前置消息选项接口
export interface PrefixOption {
  id: string;
  content: string;
  category: 'persona' | 'format' | 'context' | 'ace_framework' | 'custom';
  description: string;
  useCase: string;
  confidence: number; // AI推荐置信度 0-1
  reasoning: string; // 推荐理由
  tags: string[]; // 标签，便于分类和搜索
}

// 上下文分析结果
export interface AnalysisContext {
  phaseFeatures: {
    name: string;
    requirements: string[];
    wordCountRange: string;
    currentProgress: number; // 0-1
  };
  personaTraits: {
    communicationStyle: string;
    expertise: string[];
    personality: string[];
    responsePattern: string;
  };
  contentAnalysis: {
    wordCount: number;
    style: string;
    tone: string;
    issues: string[];
    strengths: string[];
  };
  historyPatterns: {
    commonQuestions: string[];
    preferredFormats: string[];
    satisfactionTrend: number; // 0-1
    recentUserMessages: ChatMessage[]; // 最近的小懒消息
    recentAIResponses: ChatMessage[]; // 最近的AI回复
    messageRatings: { messageId: string; rating: number; feedback?: string }[]; // 消息评分
  };
}

// 用户偏好模型
export interface UserPreferenceProfile {
  preferredCategories: Record<string, number>; // 类别偏好权重
  effectivePatterns: string[]; // 有效的前置消息模式
  feedbackHistory: {
    prefixId: string;
    rating: number;
    timestamp: Date;
  }[];
  personalizedWeights: Record<string, number>; // 个性化推荐权重
}

export class PrefixMessageAIService extends UnifiedAIService {
  private userProfile: UserPreferenceProfile;
  private cacheKey = 'prefix_message_cache';
  private profileKey = 'user_preference_profile';

  constructor() {
    super(AIServiceType.PREFIX_MESSAGE);
    this.loadUserProfile();
  }

  /**
   * 分析当前上下文
   */
  async analyzeContext(
    phase: PhaseType,
    persona: AIPersona | null,
    content: string,
    history: ChatMessage[]
  ): Promise<AnalysisContext> {
    // 分析阶段特征
    const phaseFeatures = this.extractPhaseFeatures(phase, content);

    // 分析人设特点
    const personaTraits = this.extractPersonaTraits(persona);

    // 分析内容特征
    const contentAnalysis = this.analyzeContent(content);

    // 分析历史模式（异步）
    const historyPatterns = await this.analyzeHistory(history);

    return {
      phaseFeatures,
      personaTraits,
      contentAnalysis,
      historyPatterns
    };
  }

  /**
   * 生成前置消息选项
   */
  async generatePrefixOptions(
    context: AnalysisContext,
    userInput?: string,
    forceRegenerate: boolean = false
  ): Promise<PrefixOption[]> {
    // 检查缓存（除非强制重新生成）
    if (!forceRegenerate) {
      const cacheKey = this.generateCacheKey(context, userInput);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('🔄 使用缓存的前置消息选项');
        return cached;
      }
    }

    try {
      console.log('🆕 生成新的前置消息选项');
      // 调用AI服务生成
      const response = await this.callAIService(context, userInput);

      // 解析生成结果
      const options = this.parseGeneratedOptions(response, context);

      // 应用个性化权重
      const personalizedOptions = this.applyPersonalization(options);

      // 缓存结果（添加时间戳避免重复）
      const cacheKey = this.generateCacheKey(context, userInput, Date.now());
      this.saveToCache(cacheKey, personalizedOptions);

      return personalizedOptions;
    } catch (error) {
      console.error('生成前置消息失败:', error);
      // 返回备用选项
      return this.getFallbackOptions(context);
    }
  }

  /**
   * 学习用户反馈
   */
  async learnFromFeedback(
    selections: string[],
    ratings: { prefixId: string; rating: number }[]
  ): Promise<void> {
    // 更新用户偏好
    this.updateUserPreferences(selections, ratings);

    // 优化生成策略
    this.optimizeGenerationStrategy();

    // 保存用户档案
    this.saveUserProfile();
  }

  /**
   * 获取智能推荐
   */
  async getSmartRecommendations(
    userInput: string,
    context: AnalysisContext
  ): Promise<PrefixOption[]> {
    // 分析用户意图
    const intent = this.analyzeUserIntent(userInput);

    // 基于意图推荐
    const recommendations = await this.generateIntentBasedOptions(intent, context);

    return recommendations;
  }

  // 私有方法实现

  private extractPhaseFeatures(phase: PhaseType, content: string) {
    const phaseConfig = {
      intro: {
        name: '导语',
        requirements: ['建立悬念', '抓住注意力', '简洁有力'],
        wordCountRange: '50-150字',
        targetRange: [50, 150]
      },
      setup: {
        name: '铺垫期',
        requirements: ['情绪拉升', '人物困境', '伏笔埋设'],
        wordCountRange: '150-2000字',
        targetRange: [150, 2000]
      },
      compression: {
        name: '爆发情绪',
        requirements: ['情绪爆发', '小高潮', '反击开始'],
        wordCountRange: '2000-5000字',
        targetRange: [2000, 5000]
      },
      climax: {
        name: '反转',
        requirements: ['多重反转', '见招拆招', '爽点密集'],
        wordCountRange: '5000-8000字',
        targetRange: [5000, 8000]
      },
      resolution: {
        name: '让读者解气',
        requirements: ['反派转变', '假意和好', '情绪缓解'],
        wordCountRange: '8000-10000字',
        targetRange: [8000, 10000]
      },
      ending: {
        name: '大结局',
        requirements: ['最终反转', '大仇小仇一起报', '完美收尾'],
        wordCountRange: '10000字+',
        targetRange: [10000, Infinity]
      },
      buildup: {
        name: '铺垫期',
        requirements: ['情绪拉升', '人物困境', '伏笔埋设'],
        wordCountRange: '150-2000字',
        targetRange: [150, 2000]
      },
      custom: {
        name: '自定义',
        requirements: ['灵活应用', '需求分析', '个性化指导'],
        wordCountRange: '灵活字数',
        targetRange: [0, Infinity]
      }
    };

    const config = phaseConfig[phase] || phaseConfig.intro;
    const wordCount = content.length;
    const [min, max] = config.targetRange;
    const currentProgress = Math.min(1, Math.max(0, (wordCount - min) / (max - min)));

    return {
      name: config.name,
      requirements: config.requirements,
      wordCountRange: config.wordCountRange,
      currentProgress
    };
  }

  private extractPersonaTraits(persona: AIPersona | null) {
    if (!persona) {
      return {
        communicationStyle: '标准专业',
        expertise: ['通用创作指导'],
        personality: ['专业', '客观'],
        responsePattern: '分析+建议'
      };
    }

    return {
      communicationStyle: persona.customizations?.communicationStyle || '专业',
      expertise: persona.customizations?.expertise || [],
      personality: persona.customizations?.personality || [],
      responsePattern: persona.customizations?.responsePattern || '分析+建议'
    };
  }

  private analyzeContent(content: string) {
    const wordCount = content.length;

    // 简单的内容分析
    const hasDialogue = content.includes('"') || content.includes('"');
    const hasAction = /跑|走|看|说|想/.test(content);
    const hasEmotion = /愤怒|高兴|悲伤|激动|紧张/.test(content);

    let style = '叙述性';
    if (hasDialogue && hasAction) style = '对话驱动';
    else if (hasAction) style = '动作导向';
    else if (hasEmotion) style = '情感丰富';

    let tone = '中性';
    if (/！|？/.test(content)) tone = '激烈';
    else if (/，|。/.test(content)) tone = '平和';

    const issues = [];
    const strengths = [];

    if (wordCount < 50) issues.push('内容过短');
    if (wordCount > 2000) issues.push('内容过长');
    if (!hasDialogue) issues.push('缺少对话');

    if (hasEmotion) strengths.push('情感表达丰富');
    if (hasAction) strengths.push('动作描写生动');

    return {
      wordCount,
      style,
      tone,
      issues,
      strengths
    };
  }

  private async analyzeHistory(history: ChatMessage[]) {
    const recentMessages = history.slice(-10);

    const commonQuestions: string[] = [];
    const preferredFormats: string[] = [];
    let satisfactionTrend = 0.7; // 默认值

    // 分离小懒消息和AI回复
    const recentUserMessages = recentMessages.filter(msg => msg.type === 'user');
    const recentAIResponses = recentMessages.filter(msg => msg.type === 'ai');

    // 分析常见问题类型
    const questionPatterns = recentUserMessages
      .map(msg => this.categorizeQuestion(msg.content));

    // 统计问题类型
    const questionCounts = questionPatterns.reduce((acc, pattern) => {
      acc[pattern] = (acc[pattern] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(questionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .forEach(([pattern]) => commonQuestions.push(pattern));

    // 获取真实的消息评分数据
    const messageRatings = await this.getRealMessageRatings(recentAIResponses);

    // 计算满意度趋势
    if (messageRatings.length > 0) {
      const avgRating = messageRatings.reduce((sum, rating) => sum + rating.rating, 0) / messageRatings.length;
      satisfactionTrend = avgRating / 5; // 评分是1-5分
    }

    return {
      commonQuestions,
      preferredFormats,
      satisfactionTrend,
      recentUserMessages: recentUserMessages.slice(-5), // 最近5条小懒消息
      recentAIResponses: recentAIResponses.slice(-5), // 最近5条AI回复
      messageRatings
    };
  }

  private async getRealMessageRatings(messages: ChatMessage[]): Promise<{ messageId: string; rating: number; feedback?: string }[]> {
    try {
      // 导入真实的反馈服务
      const { FeedbackCollectionService } = await import('../../services/ai-feedback/FeedbackCollectionService');
      const feedbackService = FeedbackCollectionService.getInstance();

      const ratings: { messageId: string; rating: number; feedback?: string }[] = [];

      for (const msg of messages) {
        const feedback = feedbackService.getMessageFeedback(msg.id);
        if (feedback) {
          // 将FeedbackRating转换为数字评分
          let numericRating = 3; // 默认一般
          switch (feedback.rating) {
            case 'good': numericRating = 5; break;
            case 'average': numericRating = 3; break;
            case 'poor': numericRating = 1; break;
          }

          ratings.push({
            messageId: msg.id,
            rating: numericRating,
            feedback: feedback.comment
          });
        }
      }

      return ratings;
    } catch (error) {
      console.error('获取真实评分数据失败:', error);
      // 返回空数组，使用默认满意度
      return [];
    }
  }

  private categorizeQuestion(content: string): string {
    if (/如何|怎么|怎样/.test(content)) return '方法询问';
    if (/为什么|原因/.test(content)) return '原因分析';
    if (/修改|改进|优化/.test(content)) return '修改建议';
    if (/评价|分析|看法/.test(content)) return '评价分析';
    if (/下一步|接下来/.test(content)) return '后续指导';
    return '一般询问';
  }

  // 基于用户消息和AI回复的分层消息构建方法

  /**
   * 构建用户（小懒）的消息层 - 基于聊天历史
   */
  private buildUserMessageLayer(context: AnalysisContext, userInput?: string): string {
    const recentMessages = context.historyPatterns.recentUserMessages || [];
    const userMessageHistory = recentMessages.slice(-3).map((msg, index) =>
      `${index + 1}. ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`
    ).join('\n');

    return `[小懒的最近消息记录] = [${userMessageHistory || '暂无历史消息'}]

[小懒当前的问题] = [${userInput || '需要创作指导'}]

[创作背景信息] = [当前阶段：${context.phaseFeatures.name}，已写字数：${context.contentAnalysis.wordCount}字，主要困难：${context.contentAnalysis.issues.join('、') || '无明显问题'}]`;
  }

  /**
   * 构建AI助手的回复层 - 包含历史回复和评价分析
   */
  private buildAIResponseLayer(context: AnalysisContext): string {
    const recentResponses = context.historyPatterns.recentAIResponses.slice(-3);
    const ratings = context.historyPatterns.messageRatings;
    const avgRating = ratings.length > 0 ?
      (ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length).toFixed(1) : '暂无';

    const responseHistory = recentResponses.map((msg, index) =>
      `${index + 1}. ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`
    ).join('\n');

    const positiveFeeback = ratings.filter(r => r.rating >= 4 && r.feedback).map(r => r.feedback).slice(0, 2);
    const improvementAreas = ratings.filter(r => r.rating <= 3).length > 0 ?
      '需要提升回复的针对性和实用性' : '整体表现良好';

    return `[AI助手的最近回复记录] = [${responseHistory || '暂无历史回复'}]

[用户评价分析] = [平均评分：${avgRating}/5.0，用户满意度：${Math.round(context.historyPatterns.satisfactionTrend * 100)}%，正面反馈：${positiveFeeback.join('；') || '暂无具体反馈'}，改进方向：${improvementAreas}]

[当前助手配置] = [沟通风格：${context.personaTraits.communicationStyle}，专业领域：${context.personaTraits.expertise.join('、')}，回复模式：${context.personaTraits.responsePattern}]

[基于评价的自我优化方向] = [根据用户反馈调整回复风格，提升针对${context.phaseFeatures.name}阶段的专业性，优化回复的结构和实用性]`;
  }

  /**
   * 构建上下文分析消息
   */
  private buildContextAnalysisMessage(context: AnalysisContext): string {
    const ratings = context.historyPatterns.messageRatings;
    const avgRating = ratings.length > 0 ?
      (ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length).toFixed(1) : '暂无';

    return `[当前创作状态分析] = [当前阶段：${context.phaseFeatures.name}，核心要求：${context.phaseFeatures.requirements.join('、')}，完成进度：${Math.round(context.phaseFeatures.currentProgress * 100)}%]

[内容分析] = [字数：${context.contentAnalysis.wordCount}字，风格：${context.contentAnalysis.style}，需要改进：${context.contentAnalysis.issues.join('、') || '无明显问题'}，现有优势：${context.contentAnalysis.strengths.join('、') || '待发掘'}]

[AI助手配置] = [沟通风格：${context.personaTraits.communicationStyle}，专业领域：${context.personaTraits.expertise.join('、')}，回复模式：${context.personaTraits.responsePattern}]

[小懒的评价总结] = [平均评分：${avgRating}/5.0 (共${context.historyPatterns.messageRatings.length}条评价)，满意度趋势：${Math.round(context.historyPatterns.satisfactionTrend * 100)}%，常见问题类型：${context.historyPatterns.commonQuestions.join('、') || '暂无'}]

[注意事项] = [具体的评价信息已经标注在上面对应的AI回复消息中]`;
  }

  /**
   * 构建优化任务消息 - 基于聊天历史和评价反馈
   */
  private buildOptimizationTaskMessage(): string {
    return `[任务目标] = [基于上述小懒的消息历史和AI助手的回复记录及小懒的评价，请生成3-5个前置消息选项来优化AI助手的回复效果]

[分析要点] = [1. 小懒的消息模式：从历史消息中识别常见问题类型和沟通习惯；2. AI回复质量：从小懒的评价中了解哪些回复受欢迎，哪些需要改进；3. 上下文连贯性：确保AI能够理解对话的连续性和发展脉络；4. 个性化适配：根据小懒的评价反馈调整AI的回复风格和专业度]

[优化目标] = [1. 提升AI对聊天上下文的理解能力；2. 根据小懒的历史评价优化回复质量；3. 增强AI回复的针对性和实用性；4. 保持对话的连贯性和个性化]

[前置消息类型] = [context: 增强对聊天历史和上下文的理解；persona: 基于用户评价优化AI人设表现；format: 根据用户偏好规范回复格式；ace_framework: 结合评价反馈强化专业指导]

[输出要求] = [请严格按照JSON数组格式输出，不要包含其他文字：
[
  {
    "content": "前置消息内容（50-200字）",
    "category": "context|persona|format|ace_framework",
    "description": "功能描述",
    "useCase": "适用场景",
    "reasoning": "基于聊天历史和评价分析的推荐理由"
  }
]]

[最终说明] = [这些前置消息将帮助AI更好地理解对话上下文，并根据小懒的历史评价提供更优质的回复]`;
  }

  private async callAIService(context: AnalysisContext, userInput?: string): Promise<string> {
    try {
      console.log('🤖 PrefixMessageAIService: 开始调用AI服务生成前置消息');

      // 构建分层消息结构 - 参考ShortStoryPhaseAIService的处理方式
      const messages = [
        // 系统角色定义
        {
          role: 'system',
          content: '你是一位专业的AI前置消息生成专家。分析用户与AI助手的对话模式，生成能够优化AI回复效果的前置消息选项。'
        }
      ];

      // 添加聊天历史（最近5条消息）
      const recentHistory = context.historyPatterns.recentUserMessages
        .concat(context.historyPatterns.recentAIResponses)
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        .slice(-5);

      recentHistory.forEach((msg, index) => {
        const wordCount = msg.content.length;

        let content = '';

        // 如果是用户消息，使用 []=[] 格式
        if (msg.type === 'user') {
          content = `[小懒的消息 #${index + 1}] = [${msg.content}]`;
        } else {
          // AI回复消息，添加字数和评价信息
          content = `[AI助手回复 #${index + 1}] = [${msg.content}] (${wordCount}字)`;

          // 查找对应的评价
          const rating = context.historyPatterns.messageRatings.find(r => r.messageId === msg.id);
          if (rating) {
            const ratingText = rating.rating >= 4 ? '好评' : rating.rating === 3 ? '一般' : '差评';
            content += `\n\n[小懒评价] = [${ratingText}(${rating.rating}/5)`;
            if (rating.feedback) {
              content += ` - "${rating.feedback}"`;
            }
            content += `]`;
          }
        }

        messages.push({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: content
        });
      });

      // 添加当前上下文分析
      messages.push({
        role: 'user',
        content: this.buildContextAnalysisMessage(context)
      });

      // 添加用户当前需求（如果有）
      if (userInput) {
        messages.push({
          role: 'user',
          content: `[小懒当前的具体需求] = [${userInput}]`
        });
      }

      // 最终任务指令
      messages.push({
        role: 'user',
        content: this.buildOptimizationTaskMessage()
      });

      console.log('📝 构建了', messages.length, '层消息结构');

      // 使用统一的AI调用方法
      const response = await this.callAI(messages, {
        streaming: false // 确保不使用流式输出
      });

      console.log('🤖 PrefixMessageAIService: AI服务响应:', {
        success: response.success,
        textLength: response.text?.length || 0,
        error: response.error
      });

      if (response.success && response.text) {
        console.log('✅ PrefixMessageAIService: AI服务调用成功，返回内容长度:', response.text.length);
        return response.text;
      } else {
        console.error('❌ PrefixMessageAIService: AI服务调用失败:', response.error);
        throw new Error(response.error || 'AI服务调用失败');
      }
    } catch (error) {
      console.error('❌ 调用AI服务异常:', error);
      // 如果AI服务失败，返回备用的模拟数据
      console.log('🔄 使用备用方案生成前置消息');
      return this.getFallbackAIResponse();
    }
  }

  private getFallbackAIResponse(): string {
    return JSON.stringify([
      {
        content: `[当前阶段专业要求] = [注重情节节奏的把控，强化人物情感表达，确保读者代入感]`,
        category: 'ace_framework',
        description: '基于ACE框架的专业创作指导',
        useCase: '需要专业创作建议时',
        reasoning: '当前阶段需要专业的创作指导来提升质量'
      },
      {
        content: `[回复格式要求] = [请按以下结构回复：1. 【现状分析】；2. 【具体建议】；3. 【示例展示】；4. 【下一步】]`,
        category: 'format',
        description: '结构化回复格式',
        useCase: '需要条理清晰的建议时',
        reasoning: '结构化回复能让建议更加清晰易懂'
      },
      {
        content: `[人设强化] = [请以资深创作导师的身份回复，提供具体可操作的建议，分析优缺点并给出改进方向]`,
        category: 'persona',
        description: '强化AI专业性和权威性',
        useCase: '需要专业权威指导时',
        reasoning: '专业人设能提升指导的可信度和实用性'
      }
    ]);
  }

  private getRandomPhase(): string {
    const phases = ['导语', '铺垫期', '爆发情绪', '反转', '解气', '结局'];
    return phases[Math.floor(Math.random() * phases.length)];
  }

  private parseGeneratedOptions(response: string, context: AnalysisContext): PrefixOption[] {
    try {
      // 尝试提取JSON内容
      let jsonContent = response.trim();

      // 如果响应包含其他文字，尝试提取JSON部分
      const jsonMatch = jsonContent.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        jsonContent = jsonMatch[0];
      }

      // 清理可能的markdown代码块标记
      jsonContent = jsonContent.replace(/```json\s*/, '').replace(/```\s*$/, '');

      const rawOptions = JSON.parse(jsonContent);

      // 确保返回的是数组
      const optionsArray = Array.isArray(rawOptions) ? rawOptions : [rawOptions];

      return optionsArray.map((option: any, index: number) => ({
        id: `generated_${Date.now()}_${index}`,
        content: option.content || '前置消息内容生成失败',
        category: this.validateCategory(option.category),
        description: option.description || 'AI生成的前置消息',
        useCase: option.useCase || '通用场景',
        confidence: this.calculateConfidence(option, context),
        reasoning: option.reasoning || '基于当前上下文的智能推荐',
        tags: this.generateTags(option)
      })).filter(option => option.content.length > 10); // 过滤掉内容过短的选项

    } catch (error) {
      console.error('解析AI响应失败:', error, '原始响应:', response);
      return this.getFallbackOptions(context);
    }
  }

  private validateCategory(category: string): PrefixOption['category'] {
    const validCategories: PrefixOption['category'][] = ['persona', 'format', 'context', 'ace_framework', 'custom'];
    return validCategories.includes(category as any) ? category as PrefixOption['category'] : 'custom';
  }

  private calculateConfidence(option: any, context: AnalysisContext): number {
    // 基于多个因素计算置信度
    let confidence = 0.7; // 基础置信度

    // 根据阶段匹配度调整
    if (option.category === 'ace_framework') confidence += 0.1;

    // 根据内容质量调整
    if (option.content.length > 50 && option.content.length < 200) confidence += 0.1;

    // 根据用户偏好调整
    const categoryPreference = this.userProfile.preferredCategories[option.category] || 0.5;
    confidence = confidence * (0.7 + categoryPreference * 0.3);

    return Math.min(1, Math.max(0.3, confidence));
  }

  private generateTags(option: any): string[] {
    const tags = [option.category];

    if (option.content.includes('专业')) tags.push('专业');
    if (option.content.includes('格式')) tags.push('格式');
    if (option.content.includes('分析')) tags.push('分析');
    if (option.content.includes('建议')) tags.push('建议');

    return tags;
  }

  private applyPersonalization(options: PrefixOption[]): PrefixOption[] {
    // 根据用户偏好调整选项顺序和置信度
    return options
      .map(option => ({
        ...option,
        confidence: this.adjustConfidenceByPreference(option)
      }))
      .sort((a, b) => b.confidence - a.confidence);
  }

  private adjustConfidenceByPreference(option: PrefixOption): number {
    const categoryWeight = this.userProfile.preferredCategories[option.category] || 0.5;
    return option.confidence * (0.7 + categoryWeight * 0.3);
  }

  private getFallbackOptions(context: AnalysisContext): PrefixOption[] {
    // 返回备用的前置消息选项
    return [
      {
        id: 'fallback_1',
        content: `[${context.phaseFeatures.name}阶段指导] = [请重点关注${context.phaseFeatures.requirements.join('、')}，确保创作质量]`,
        category: 'ace_framework',
        description: '基础阶段指导',
        useCase: '通用创作指导',
        confidence: 0.6,
        reasoning: '基于当前阶段的基础指导',
        tags: ['基础', '阶段指导']
      },
      {
        id: 'fallback_2',
        content: '[回复要求] = [请提供具体可操作的建议，包含现状分析和改进方向]',
        category: 'format',
        description: '基础格式要求',
        useCase: '需要结构化建议时',
        confidence: 0.5,
        reasoning: '确保回复的结构化和实用性',
        tags: ['格式', '结构化']
      }
    ];
  }

  // 缓存和存储相关方法
  private generateCacheKey(context: AnalysisContext, userInput?: string, timestamp?: number): string {
    const contextData = {
      phase: context.phaseFeatures.name,
      wordCount: Math.floor(context.contentAnalysis.wordCount / 100) * 100,
      style: context.contentAnalysis.style,
      userInput: userInput?.substring(0, 50),
      timestamp: timestamp // 添加时间戳确保唯一性
    };

    // 使用简单的哈希算法替代btoa，避免中文字符问题
    const contextString = JSON.stringify(contextData);
    let hash = 0;
    for (let i = 0; i < contextString.length; i++) {
      const char = contextString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36).substring(0, 16);
  }

  private getFromCache(key: string): PrefixOption[] | null {
    try {
      const cached = localStorage.getItem(`${this.cacheKey}_${key}`);
      if (cached) {
        const data = JSON.parse(cached);
        if (Date.now() - data.timestamp < 30 * 60 * 1000) { // 30分钟缓存
          return data.options;
        }
      }
    } catch (error) {
      console.error('读取缓存失败:', error);
    }
    return null;
  }

  private saveToCache(key: string, options: PrefixOption[]): void {
    try {
      const data = {
        options,
        timestamp: Date.now()
      };
      localStorage.setItem(`${this.cacheKey}_${key}`, JSON.stringify(data));
    } catch (error) {
      console.error('保存缓存失败:', error);
    }
  }

  private loadUserProfile(): void {
    try {
      const saved = localStorage.getItem(this.profileKey);
      if (saved) {
        this.userProfile = JSON.parse(saved);
      } else {
        this.userProfile = this.createDefaultProfile();
      }
    } catch (error) {
      console.error('加载用户档案失败:', error);
      this.userProfile = this.createDefaultProfile();
    }
  }

  private createDefaultProfile(): UserPreferenceProfile {
    return {
      preferredCategories: {
        'ace_framework': 0.7,
        'format': 0.6,
        'persona': 0.5,
        'context': 0.6,
        'custom': 0.4
      },
      effectivePatterns: [],
      feedbackHistory: [],
      personalizedWeights: {}
    };
  }

  private saveUserProfile(): void {
    try {
      localStorage.setItem(this.profileKey, JSON.stringify(this.userProfile));
    } catch (error) {
      console.error('保存用户档案失败:', error);
    }
  }

  private updateUserPreferences(selections: string[], ratings: { prefixId: string; rating: number }[]): void {
    // 更新偏好权重
    ratings.forEach(({ prefixId, rating }) => {
      this.userProfile.feedbackHistory.push({
        prefixId,
        rating,
        timestamp: new Date()
      });
    });

    // 保持历史记录在合理范围内
    if (this.userProfile.feedbackHistory.length > 100) {
      this.userProfile.feedbackHistory = this.userProfile.feedbackHistory.slice(-100);
    }
  }

  private optimizeGenerationStrategy(): void {
    // 基于反馈历史优化生成策略
    const recentFeedback = this.userProfile.feedbackHistory.slice(-20);

    // 计算各类别的平均评分
    const categoryRatings: Record<string, number[]> = {};

    recentFeedback.forEach(feedback => {
      // 这里需要根据prefixId找到对应的category
      // 简化处理，假设从ID中可以推断类别
      const category = this.inferCategoryFromId(feedback.prefixId);
      if (!categoryRatings[category]) {
        categoryRatings[category] = [];
      }
      categoryRatings[category].push(feedback.rating);
    });

    // 更新偏好权重
    Object.entries(categoryRatings).forEach(([category, ratings]) => {
      const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      this.userProfile.preferredCategories[category] = Math.max(0.1, Math.min(1, avgRating / 5));
    });
  }

  private inferCategoryFromId(prefixId: string): string {
    if (prefixId.includes('ace')) return 'ace_framework';
    if (prefixId.includes('format')) return 'format';
    if (prefixId.includes('persona')) return 'persona';
    if (prefixId.includes('context')) return 'context';
    return 'custom';
  }

  private analyzeUserIntent(userInput: string): string {
    if (/如何|怎么|怎样/.test(userInput)) return '方法询问';
    if (/修改|改进|优化/.test(userInput)) return '修改建议';
    if (/分析|评价/.test(userInput)) return '分析评价';
    if (/节奏|情节/.test(userInput)) return '节奏分析';
    if (/人物|角色/.test(userInput)) return '人物塑造';
    if (/对话/.test(userInput)) return '对话优化';
    return '通用指导';
  }

  private async generateIntentBasedOptions(intent: string, context: AnalysisContext): Promise<PrefixOption[]> {
    // 基于意图生成特定的前置消息选项
    const intentTemplates: Record<string, Partial<PrefixOption>> = {
      '节奏分析': {
        content: '[节奏分析要求] = [请重点分析当前内容的节奏感，包括情节推进速度、情绪起伏、读者期待值的管理]',
        category: 'context',
        description: '专注于节奏分析的前置消息',
        useCase: '需要分析创作节奏时'
      },
      '人物塑造': {
        content: '[人物塑造指导] = [请从人物性格、动机、成长弧线等角度分析，提供人物塑造的具体建议]',
        category: 'context',
        description: '专注于人物塑造的指导',
        useCase: '需要人物塑造建议时'
      },
      '对话优化': {
        content: '[对话优化要求] = [请重点关注对话的自然性、个性化、推进情节的作用，提供对话改进建议]',
        category: 'context',
        description: '专注于对话优化的指导',
        useCase: '需要对话改进建议时'
      }
    };

    const template = intentTemplates[intent];
    if (template) {
      return [{
        id: `intent_${Date.now()}`,
        content: template.content!,
        category: template.category as any,
        description: template.description!,
        useCase: template.useCase!,
        confidence: 0.8,
        reasoning: `基于用户意图"${intent}"的专门指导`,
        tags: [intent, '意图识别']
      }];
    }

    return [];
  }
}
