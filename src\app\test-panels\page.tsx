"use client";

import React, { useState } from 'react';
import { CharacterPanelAdapter } from '@/adapters/character/CharacterPanelAdapter';
import { TerminologyPanelAdapter } from '@/adapters/terminology/TerminologyPanelAdapter';
import { WorldBuildingPanelAdapter } from '@/adapters/worldbuilding/WorldBuildingPanelAdapter';
import { FeatherPenIcon, CharacterIcon, BookmarkIcon, MapIcon } from '@/components/icons';

/**
 * 面板测试页面
 * 用于测试增强版面板的SVG图标和动画效果
 */
export default function TestPanelsPage() {
  const [activePanel, setActivePanel] = useState<string | null>(null);
  const testBookId = 'test-book-id';

  const openPanel = (panelType: string) => {
    setActivePanel(panelType);
  };

  const closePanel = () => {
    setActivePanel(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 p-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <FeatherPenIcon size="lg" animated={true} className="text-amber-700" />
            <h1 className="text-3xl font-bold text-amber-900">
              AI小说平台 - 增强面板演示
            </h1>
          </div>
          <p className="text-amber-700 text-lg">
            体验文学创作主题的SVG图标和微交互动画
          </p>
        </div>

        {/* 图标展示区域 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-xl font-semibold text-amber-900 mb-6 flex items-center gap-2">
            <FeatherPenIcon size="md" animated={true} />
            文学主题图标库
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center p-4 rounded-lg hover:bg-amber-50 transition-colors">
              <FeatherPenIcon size="lg" animated={true} className="mx-auto mb-2 text-amber-700" />
              <p className="text-sm text-gray-600">羽毛笔</p>
              <p className="text-xs text-gray-400">摆动动画</p>
            </div>
            
            <div className="text-center p-4 rounded-lg hover:bg-amber-50 transition-colors">
              <CharacterIcon size="lg" animated={true} className="mx-auto mb-2 text-amber-700" />
              <p className="text-sm text-gray-600">人物</p>
              <p className="text-xs text-gray-400">点头动画</p>
            </div>
            
            <div className="text-center p-4 rounded-lg hover:bg-amber-50 transition-colors">
              <BookmarkIcon size="lg" animated={true} className="mx-auto mb-2 text-amber-700" />
              <p className="text-sm text-gray-600">书签</p>
              <p className="text-xs text-gray-400">飘动动画</p>
            </div>
            
            <div className="text-center p-4 rounded-lg hover:bg-amber-50 transition-colors">
              <MapIcon size="lg" animated={true} className="mx-auto mb-2 text-amber-700" />
              <p className="text-sm text-gray-600">地图</p>
              <p className="text-xs text-gray-400">展开动画</p>
            </div>
          </div>
        </div>

        {/* 面板测试按钮 */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-xl font-semibold text-amber-900 mb-6">
            增强面板测试
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button
              onClick={() => openPanel('character')}
              className="group p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border-2 border-blue-200 hover:border-blue-300 transition-all duration-200 hover:shadow-lg hover:scale-105"
            >
              <div className="flex items-center gap-3 mb-3">
                <CharacterIcon 
                  size="md" 
                  animated={true} 
                  className="text-blue-600 group-hover:scale-110 transition-transform" 
                />
                <h3 className="text-lg font-semibold text-blue-900">人物管理</h3>
              </div>
              <p className="text-blue-700 text-sm">
                体验人物面板的增强效果，包括羽毛笔图标、装饰元素和微交互动画
              </p>
            </button>

            <button
              onClick={() => openPanel('terminology')}
              className="group p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border-2 border-green-200 hover:border-green-300 transition-all duration-200 hover:shadow-lg hover:scale-105"
            >
              <div className="flex items-center gap-3 mb-3">
                <BookmarkIcon 
                  size="md" 
                  animated={true} 
                  className="text-green-600 group-hover:scale-110 transition-transform" 
                />
                <h3 className="text-lg font-semibold text-green-900">术语管理</h3>
              </div>
              <p className="text-green-700 text-sm">
                体验术语面板的书签图标、文学主题装饰和列表项悬停效果
              </p>
            </button>

            <button
              onClick={() => openPanel('worldbuilding')}
              className="group p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border-2 border-purple-200 hover:border-purple-300 transition-all duration-200 hover:shadow-lg hover:scale-105"
            >
              <div className="flex items-center gap-3 mb-3">
                <MapIcon 
                  size="md" 
                  animated={true} 
                  className="text-purple-600 group-hover:scale-110 transition-transform" 
                />
                <h3 className="text-lg font-semibold text-purple-900">世界观管理</h3>
              </div>
              <p className="text-purple-700 text-sm">
                体验世界观面板的地图图标、四角装饰花纹和面板展开动画
              </p>
            </button>
          </div>

          <div className="mt-8 p-4 bg-amber-50 rounded-lg border border-amber-200">
            <h4 className="font-semibold text-amber-900 mb-2">增强效果说明：</h4>
            <ul className="text-sm text-amber-800 space-y-1">
              <li>• 面板展开时的弹性动画效果（400ms缓动）</li>
              <li>• 标题区域的羽毛笔图标悬停摆动</li>
              <li>• 列表项悬停时的背景变化和位移动画</li>
              <li>• 四角古典花纹装饰元素</li>
              <li>• 细微的纸张纹理背景</li>
              <li>• 支持无障碍的动画降级</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 面板组件 */}
      {activePanel === 'character' && (
        <CharacterPanelAdapter
          bookId={testBookId}
          isOpen={true}
          onClose={closePanel}
        />
      )}

      {activePanel === 'terminology' && (
        <TerminologyPanelAdapter
          bookId={testBookId}
          isOpen={true}
          onClose={closePanel}
        />
      )}

      {activePanel === 'worldbuilding' && (
        <WorldBuildingPanelAdapter
          bookId={testBookId}
          isOpen={true}
          onClose={closePanel}
        />
      )}
    </div>
  );
}
