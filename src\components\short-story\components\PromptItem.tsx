"use client";

import React, { useState, useEffect, forwardRef } from 'react';
import { motion } from 'framer-motion';
import { CategoryPrompt } from '../../../types/ai-persona';

interface PromptItemProps {
  prompt: CategoryPrompt;
  onUpdate: (content: string) => void;
  onDelete: () => void;
  index: number;
}

const PromptItem = forwardRef<HTMLDivElement, PromptItemProps>(({
  prompt,
  onUpdate,
  onDelete,
  index
}, ref) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');

  // 同步prompt.content到editedContent状态
  useEffect(() => {
    setEditedContent(prompt.content);
  }, [prompt.content]);

  // 处理编辑
  const handleEdit = () => {
    setIsEditing(true);
    setEditedContent(prompt.content);
  };

  const handleSave = () => {
    if (editedContent.trim() && editedContent.trim() !== prompt.content) {
      onUpdate(editedContent.trim());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedContent(prompt.content);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  // 处理删除
  const handleDelete = () => {
    if (window.confirm('确定要删除这个提示词吗？')) {
      onDelete();
    }
  };

  // 格式化创建时间
  const formatDate = (date: Date): string => {
    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    const now = new Date();
    const diffMs = now.getTime() - validDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;

    return validDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      ref={ref}
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{
        duration: 0.2,
        delay: index * 0.03
      }}
      className="group p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-gray-300 dark:hover:border-gray-500 transition-colors"
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-2">
              <textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                rows={3}
                autoFocus
              />
              <div className="flex items-center justify-end space-x-2">
                <button
                  onClick={handleCancel}
                  className="px-2 py-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSave}
                  disabled={!editedContent.trim()}
                  className="px-2 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  保存
                </button>
              </div>
            </div>
          ) : (
            <div>
              <button
                onClick={handleEdit}
                className="text-left w-full text-sm text-gray-900 dark:text-gray-100 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
              >
                <div className="whitespace-pre-wrap break-words">
                  {prompt.content}
                </div>
              </button>
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                {formatDate(prompt.createdAt)}
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        {!isEditing && (
          <div className="flex items-center space-x-1 ml-3 opacity-0 group-hover:opacity-100 transition-opacity">
            {/* 编辑按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleEdit}
              className="p-1 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors"
              title="编辑提示词"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </motion.button>

            {/* 删除按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleDelete}
              className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
              title="删除提示词"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
});

PromptItem.displayName = 'PromptItem';

export default PromptItem;
