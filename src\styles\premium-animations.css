/* 500美金级别的专业动画系统 */

/* 全局动画变量 */
:root {
  /* 高级缓动函数 */
  --ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 800ms;

  /* 3D透视 */
  --perspective: 1000px;
}

/* 高级卡片动画 */
.premium-animated-card {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

.premium-animated-card.visible {
  animation: cardEnter 0.6s var(--ease-back) forwards;
}

@keyframes cardEnter {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95) rotateX(-10deg);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02) rotateX(2deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

.premium-animated-card.hovered {
  animation: cardHover 0.3s var(--ease-smooth) forwards;
}

@keyframes cardHover {
  0% {
    transform: translateY(0) scale(1) rotateX(0deg);
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.12);
  }
  100% {
    transform: translateY(-12px) scale(1.02) rotateX(5deg);
    box-shadow: 0 20px 40px rgba(139, 69, 19, 0.2);
  }
}

/* 高级按钮动画 */
.premium-animated-button {
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
}

.premium-animated-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.premium-animated-button:hover::before {
  left: 100%;
}

/* 波纹动画 */
@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* 旋转加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 高级加载动画 */
.premium-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.premium-loading-dots {
  display: flex;
  gap: 4px;
}

.premium-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-primary);
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.premium-loading-dot:nth-child(1) { animation-delay: -0.32s; }
.premium-loading-dot:nth-child(2) { animation-delay: -0.16s; }
.premium-loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 脉冲加载动画 */
.premium-loading-pulse {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 骨架屏加载动画 */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(210, 180, 140, 0.1) 25%,
    rgba(210, 180, 140, 0.2) 50%,
    rgba(210, 180, 140, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: skeleton 1.5s ease-in-out infinite;
}

@keyframes skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 成功动画 */
.success-checkmark {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-success);
  position: relative;
  animation: successPop 0.6s var(--ease-back);
}

.success-checkmark::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 9px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  animation: checkmarkDraw 0.3s ease-in-out 0.2s both;
}

@keyframes successPop {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes checkmarkDraw {
  0% {
    height: 0;
  }
  100% {
    height: 12px;
  }
}

/* 错误动画 */
.error-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* 页面转场动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s var(--ease-smooth);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s var(--ease-sharp);
}

/* 列表项级联动画 */
.list-item-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.list-item-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.4s var(--ease-smooth);
}

.list-item-enter:nth-child(1) { transition-delay: 0ms; }
.list-item-enter:nth-child(2) { transition-delay: 100ms; }
.list-item-enter:nth-child(3) { transition-delay: 200ms; }
.list-item-enter:nth-child(4) { transition-delay: 300ms; }
.list-item-enter:nth-child(5) { transition-delay: 400ms; }

/* 模态框动画 */
.modal-backdrop {
  background: rgba(0, 0, 0, 0);
  transition: background 0.3s ease;
}

.modal-backdrop.show {
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  transform: scale(0.7) translateY(50px);
  opacity: 0;
  transition: all 0.3s var(--ease-back);
}

.modal-content.show {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 通知动画 */
.notification-enter {
  transform: translateX(100%);
  opacity: 0;
}

.notification-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.4s var(--ease-back);
}

.notification-exit {
  transform: translateX(0);
  opacity: 1;
}

.notification-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s var(--ease-sharp);
}

/* 高级悬停效果 */
.hover-lift {
  transition: all 0.3s var(--ease-smooth);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.15);
}

.hover-glow {
  transition: all 0.3s var(--ease-smooth);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
  transform: scale(1.02);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
