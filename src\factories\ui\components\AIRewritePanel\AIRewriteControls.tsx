"use client";

import React from 'react';

interface AIRewriteControlsProps {
  isLoading: boolean;
  onGenerate: () => void;
  onCancel?: () => void;
  disabled?: boolean;
}

/**
 * AI改写控制组件
 * 用于控制生成过程
 */
const AIRewriteControls: React.FC<AIRewriteControlsProps> = ({
  isLoading,
  onGenerate,
  onCancel,
  disabled = false
}) => {
  return (
    <div className="flex items-center justify-end space-x-3 mt-4">
      {isLoading && onCancel && (
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-sm flex items-center"
          type="button"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          取消生成
        </button>
      )}

      <button
        onClick={onGenerate}
        disabled={isLoading || disabled}
        className={`px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm flex items-center ${(isLoading || disabled) ? 'opacity-50 cursor-not-allowed' : ''}`}
        type="button"
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            生成中...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            开始改写
          </>
        )}
      </button>
    </div>
  );
};

export default AIRewriteControls;
