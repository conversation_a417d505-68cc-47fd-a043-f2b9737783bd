"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { OutlineNodeType } from '../../../types/outline';
import './ChangePreviewPanel.css';

interface NodeChange {
  type: 'create' | 'update' | 'delete';
  nodeId: string;
  data?: Partial<OutlineNodeType>;
  parentId?: string;
}

interface ChangePreviewPanelProps {
  changes: NodeChange[];
  outline: any;
  onApply: (selectedChanges: NodeChange[]) => void;
  onCancel: () => void;
  onBack: () => void;
}

/**
 * 变更预览面板组件
 * 显示AI建议的变更，允许用户选择性确认
 */
const ChangePreviewPanel: React.FC<ChangePreviewPanelProps> = ({
  changes,
  outline,
  onApply,
  onCancel,
  onBack
}) => {
  const [selectedChanges, setSelectedChanges] = useState<Set<string>>(
    new Set(changes.map((_, index) => index.toString()))
  );

  // 获取节点标题
  const getNodeTitle = useCallback((nodeId: string) => {
    const findNode = (nodes: any[]): string | null => {
      for (const node of nodes) {
        if (node.id === nodeId) return node.title;
        if (node.children) {
          const found = findNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };
    
    return findNode(outline?.nodes || []) || '未知节点';
  }, [outline]);

  // 获取变更类型的中文标签
  const getChangeTypeLabel = useCallback((type: string) => {
    switch (type) {
      case 'create': return '新增';
      case 'update': return '修改';
      case 'delete': return '删除';
      default: return type;
    }
  }, []);

  // 获取变更类型的图标
  const getChangeTypeIcon = useCallback((type: string) => {
    switch (type) {
      case 'create':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        );
      case 'update':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
        );
      case 'delete':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        );
      default:
        return null;
    }
  }, []);

  // 获取变更类型的颜色
  const getChangeTypeColor = useCallback((type: string) => {
    switch (type) {
      case 'create': return '#4caf50';
      case 'update': return '#ff9800';
      case 'delete': return '#f44336';
      default: return '#666';
    }
  }, []);

  // 处理变更选择
  const handleChangeToggle = useCallback((index: number) => {
    setSelectedChanges(prev => {
      const newSet = new Set(prev);
      const indexStr = index.toString();
      
      if (newSet.has(indexStr)) {
        newSet.delete(indexStr);
      } else {
        newSet.add(indexStr);
      }
      
      return newSet;
    });
  }, []);

  // 处理全选/取消全选
  const handleSelectAll = useCallback(() => {
    if (selectedChanges.size === changes.length) {
      setSelectedChanges(new Set());
    } else {
      setSelectedChanges(new Set(changes.map((_, index) => index.toString())));
    }
  }, [selectedChanges.size, changes.length]);

  // 处理应用变更
  const handleApply = useCallback(() => {
    const changesToApply = changes.filter((_, index) => 
      selectedChanges.has(index.toString())
    );
    onApply(changesToApply);
  }, [changes, selectedChanges, onApply]);

  // 计算统计信息
  const stats = useMemo(() => {
    const total = changes.length;
    const selected = selectedChanges.size;
    const byType = changes.reduce((acc, change) => {
      acc[change.type] = (acc[change.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return { total, selected, byType };
  }, [changes, selectedChanges]);

  return (
    <div className="change-preview-panel">
      {/* 头部 */}
      <div className="preview-header">
        <button 
          className="back-button"
          onClick={onBack}
          aria-label="返回对话"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
          </svg>
        </button>
        
        <div className="preview-title">
          <h3>变更预览</h3>
          <p>AI为您准备了 {stats.total} 个建议变更</p>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="preview-stats">
        <div className="stats-item">
          <span className="stats-label">总计</span>
          <span className="stats-value">{stats.total}</span>
        </div>
        <div className="stats-item">
          <span className="stats-label">已选择</span>
          <span className="stats-value">{stats.selected}</span>
        </div>
        {Object.entries(stats.byType).map(([type, count]) => (
          <div key={type} className="stats-item">
            <span className="stats-label">{getChangeTypeLabel(type)}</span>
            <span className="stats-value" style={{ color: getChangeTypeColor(type) }}>
              {count}
            </span>
          </div>
        ))}
      </div>

      {/* 变更列表 */}
      <div className="preview-content">
        <div className="changes-header">
          <label className="select-all-checkbox">
            <input
              type="checkbox"
              checked={selectedChanges.size === changes.length && changes.length > 0}
              onChange={handleSelectAll}
            />
            <span>全选变更</span>
          </label>
        </div>

        <div className="changes-list">
          {changes.map((change, index) => (
            <div 
              key={index}
              className={`change-item ${selectedChanges.has(index.toString()) ? 'selected' : ''}`}
            >
              <label className="change-checkbox">
                <input
                  type="checkbox"
                  checked={selectedChanges.has(index.toString())}
                  onChange={() => handleChangeToggle(index)}
                />
              </label>

              <div className="change-content">
                <div className="change-header">
                  <div 
                    className="change-type-badge"
                    style={{ backgroundColor: getChangeTypeColor(change.type) }}
                  >
                    {getChangeTypeIcon(change.type)}
                    <span>{getChangeTypeLabel(change.type)}</span>
                  </div>
                  
                  <div className="change-target">
                    {change.type === 'create' ? (
                      <span>新节点</span>
                    ) : (
                      <span>{getNodeTitle(change.nodeId)}</span>
                    )}
                  </div>
                </div>

                <div className="change-details">
                  {change.type === 'create' && change.data && (
                    <div className="change-detail">
                      <strong>标题:</strong> {change.data.title}
                      {change.data.description && (
                        <>
                          <br />
                          <strong>描述:</strong> {change.data.description}
                        </>
                      )}
                      {change.parentId && (
                        <>
                          <br />
                          <strong>父节点:</strong> {getNodeTitle(change.parentId)}
                        </>
                      )}
                    </div>
                  )}
                  
                  {change.type === 'update' && change.data && (
                    <div className="change-detail">
                      {Object.entries(change.data).map(([key, value]) => (
                        <div key={key}>
                          <strong>{key}:</strong> {String(value)}
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {change.type === 'delete' && (
                    <div className="change-detail">
                      <span className="delete-warning">
                        ⚠️ 此操作将删除节点及其所有子节点
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="preview-actions">
        <button 
          className="cancel-button"
          onClick={onCancel}
        >
          取消
        </button>
        
        <button 
          className="apply-button"
          onClick={handleApply}
          disabled={selectedChanges.size === 0}
        >
          应用变更 ({selectedChanges.size})
        </button>
      </div>
    </div>
  );
};

export default ChangePreviewPanel;
