/**
 * 消息内容解析工具
 * 用于解析AI响应中的不同内容类型（文本、Mermaid图表等）
 */

export interface MessagePart {
  type: 'text' | 'mermaid' | 'thinking' | 'reasoning';
  content: string;
  id?: string;
  isCollapsed?: boolean; // 用于控制折叠状态
  title?: string; // 用于显示标题
}

/**
 * 解析消息内容，提取文本、Mermaid图表、Thinking标签和reasoning_content
 */
export function parseMessageContent(content: string, reasoningContent?: string): MessagePart[] {
  const parts: MessagePart[] = [];

  console.log('🔍 parseMessageContent 调试 - 输入内容长度:', content?.length || 0);
  console.log('🔍 parseMessageContent 调试 - reasoning_content 长度:', reasoningContent?.length || 0);
  console.log('🔍 parseMessageContent 调试 - reasoning_content 预览:', reasoningContent?.substring(0, 100) || 'null');

  // 增强的 reasoning_content 提取和验证
  let extractedReasoningContent = reasoningContent;

  // 如果没有传入 reasoning_content，尝试从 content 中提取
  if (!extractedReasoningContent && content) {
    extractedReasoningContent = extractReasoningContentFromText(content);
    if (extractedReasoningContent) {
      console.log('🔍 parseMessageContent 调试 - 从内容中提取到 reasoning_content:', extractedReasoningContent.length, '字符');
      // 从主内容中移除已提取的 reasoning_content
      content = content.replace(/reasoning_content\s*:\s*[\s\S]*?(?=\n\n|\n[A-Za-z_]+\s*:|$)/i, '').trim();
    }
  }

  // 验证和清理 reasoning_content
  if (extractedReasoningContent) {
    extractedReasoningContent = validateAndCleanReasoningContent(extractedReasoningContent);
  }

  // 定义需要解析的正则表达式（不包括reasoning_content，因为它是独立参数）
  const patterns = [
    {
      name: 'thinking',
      regex: /<Thinking>([\s\S]*?)<\/Thinking>/gi,
      type: 'thinking' as const,
      title: '思考过程'
    },
    {
      name: 'mermaid',
      regex: /```mermaid\n([\s\S]*?)\n```/g,
      type: 'mermaid' as const,
      title: 'Mermaid图表'
    }
  ];

  // 收集所有匹配项及其位置
  const matches: Array<{
    type: 'thinking' | 'mermaid';
    content: string;
    start: number;
    end: number;
    title: string;
  }> = [];

  patterns.forEach(pattern => {
    let match;
    const regex = new RegExp(pattern.regex.source, pattern.regex.flags);

    while ((match = regex.exec(content)) !== null) {
      matches.push({
        type: pattern.type,
        content: match[1].trim(),
        start: match.index,
        end: match.index + match[0].length,
        title: pattern.title
      });
    }
  });

  // 按位置排序
  matches.sort((a, b) => a.start - b.start);

  let lastIndex = 0;

  // 处理所有匹配项
  matches.forEach((match, index) => {
    // 添加匹配项之前的文本内容
    if (match.start > lastIndex) {
      const textContent = content.slice(lastIndex, match.start).trim();
      if (textContent) {
        parts.push({
          type: 'text',
          content: textContent,
          id: `text-${parts.length}`
        });
      }
    }

    // 添加匹配的特殊内容
    if (match.content) {
      parts.push({
        type: match.type,
        content: match.content,
        id: `${match.type}-${parts.length}`,
        isCollapsed: match.type === 'thinking', // thinking默认折叠
        title: match.title
      });
    }

    lastIndex = match.end;
  });

  // 添加剩余的文本内容
  if (lastIndex < content.length) {
    const remainingContent = content.slice(lastIndex).trim();
    if (remainingContent) {
      parts.push({
        type: 'text',
        content: remainingContent,
        id: `text-${parts.length}`
      });
    }
  }

  // 如果没有找到任何特殊内容，返回整个内容作为文本
  if (parts.length === 0 && content && content.trim()) {
    parts.push({
      type: 'text',
      content: content.trim(),
      id: 'text-0'
    });
  }

  // 处理独立的 reasoning_content 参数（增强容错机制）
  if (extractedReasoningContent && extractedReasoningContent.trim()) {
    parts.push({
      type: 'reasoning',
      content: extractedReasoningContent.trim(),
      id: `reasoning-${parts.length}`,
      isCollapsed: false, // reasoning_content 默认展开
      title: '推理内容'
    });
    console.log('🔍 parseMessageContent 调试 - 成功添加 reasoning_content 部分');
  } else {
    console.log('🔍 parseMessageContent 调试 - 未添加 reasoning_content 部分');
  }

  console.log('🔍 parseMessageContent 调试 - 解析结果:', parts.map(p => ({ type: p.type, contentLength: p.content?.length || 0 })));

  return parts;
}

/**
 * 检查内容是否包含Mermaid图表
 */
export function containsMermaid(content: string): boolean {
  const mermaidRegex = /```mermaid\n[\s\S]*?\n```/;
  return mermaidRegex.test(content);
}

/**
 * 检查内容是否包含Thinking标签
 */
export function containsThinking(content: string): boolean {
  const thinkingRegex = /<Thinking>[\s\S]*?<\/Thinking>/i;
  return thinkingRegex.test(content);
}

/**
 * 检查内容是否包含reasoning_content
 */
export function containsReasoning(content: string): boolean {
  const reasoningRegex = /reasoning_content:\s*[\s\S]*?(?=\n\n|\n[A-Za-z_]+:|$)/i;
  return reasoningRegex.test(content);
}

/**
 * 提取所有Thinking内容
 */
export function extractThinkingBlocks(content: string): string[] {
  const thinkingRegex = /<Thinking>([\s\S]*?)<\/Thinking>/gi;
  const blocks: string[] = [];
  let match;

  while ((match = thinkingRegex.exec(content)) !== null) {
    const thinkingContent = match[1].trim();
    if (thinkingContent) {
      blocks.push(thinkingContent);
    }
  }

  return blocks;
}

/**
 * 提取所有reasoning_content内容
 */
export function extractReasoningBlocks(content: string): string[] {
  const reasoningRegex = /reasoning_content:\s*([\s\S]*?)(?=\n\n|\n[A-Za-z_]+:|$)/gi;
  const blocks: string[] = [];
  let match;

  while ((match = reasoningRegex.exec(content)) !== null) {
    const reasoningContent = match[1].trim();
    if (reasoningContent) {
      blocks.push(reasoningContent);
    }
  }

  return blocks;
}

/**
 * 提取所有Mermaid代码块
 */
export function extractMermaidBlocks(content: string): string[] {
  const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g;
  const blocks: string[] = [];
  let match;

  while ((match = mermaidRegex.exec(content)) !== null) {
    const mermaidCode = match[1].trim();
    if (mermaidCode) {
      blocks.push(mermaidCode);
    }
  }

  return blocks;
}

/**
 * 移除内容中的Mermaid代码块，只保留文本
 */
export function stripMermaidBlocks(content: string): string {
  const mermaidRegex = /```mermaid\n[\s\S]*?\n```/g;
  return content.replace(mermaidRegex, '').trim();
}

/**
 * 格式化文本内容，处理markdown格式
 */
export function formatTextContent(content: string): string {
  // 处理粗体文本
  content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // 处理斜体文本
  content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 处理代码片段
  content = content.replace(/`(.*?)`/g, '<code>$1</code>');

  // 处理换行
  content = content.replace(/\n/g, '<br>');

  return content;
}

/**
 * 验证Mermaid代码的基本语法
 */
export function validateMermaidCode(code: string): { isValid: boolean; error?: string } {
  try {
    // 基本的语法检查
    const trimmedCode = code.trim();

    if (!trimmedCode) {
      return { isValid: false, error: '代码不能为空' };
    }

    // 检查是否包含基本的Mermaid语法
    const validKeywords = [
      'graph', 'flowchart', 'sequenceDiagram', 'classDiagram',
      'stateDiagram', 'gantt', 'pie', 'journey', 'gitgraph'
    ];

    const hasValidKeyword = validKeywords.some(keyword =>
      trimmedCode.toLowerCase().includes(keyword.toLowerCase())
    );

    if (!hasValidKeyword) {
      return {
        isValid: false,
        error: '未识别到有效的Mermaid图表类型'
      };
    }

    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 从文本中提取reasoning_content（增强版本）
 */
export function extractReasoningContentFromText(content: string): string | null {
  if (!content) return null;

  try {
    // 方法1: 标准格式 "reasoning_content: 内容"
    let reasoningMatch = content.match(/reasoning_content\s*:\s*([\s\S]*?)(?=\n\n|\n[A-Za-z_]+\s*:|$)/i);
    if (reasoningMatch) {
      console.log('🔍 extractReasoningContentFromText - 使用标准格式提取');
      return reasoningMatch[1].trim();
    }

    // 方法2: JSON格式中的reasoning_content字段
    const jsonMatch = content.match(/\{[\s\S]*"reasoning_content"\s*:\s*"([^"]*)"[\s\S]*\}/i);
    if (jsonMatch) {
      console.log('🔍 extractReasoningContentFromText - 使用JSON格式提取');
      return jsonMatch[1].trim();
    }

    // 方法3: 消息对象格式
    const messageMatch = content.match(/reasoning_content\s*:\s*"([^"]*?)"/i);
    if (messageMatch) {
      console.log('🔍 extractReasoningContentFromText - 使用消息对象格式提取');
      return messageMatch[1].trim();
    }

    // 方法4: 多行块格式
    const blockMatch = content.match(/reasoning_content\s*:\s*```([\s\S]*?)```/i);
    if (blockMatch) {
      console.log('🔍 extractReasoningContentFromText - 使用多行块格式提取');
      return blockMatch[1].trim();
    }

    console.log('🔍 extractReasoningContentFromText - 未找到reasoning_content');
    return null;
  } catch (error) {
    console.error('🔍 extractReasoningContentFromText - 提取过程中出错:', error);
    return null;
  }
}

/**
 * 验证和清理reasoning_content
 */
export function validateAndCleanReasoningContent(content: string): string {
  if (!content) return '';

  try {
    // 基本清理
    let cleaned = content.trim();

    // 移除可能的引号包围
    if ((cleaned.startsWith('"') && cleaned.endsWith('"')) ||
        (cleaned.startsWith("'") && cleaned.endsWith("'"))) {
      cleaned = cleaned.slice(1, -1);
    }

    // 移除可能的转义字符
    cleaned = cleaned.replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\"/g, '"');

    // 确保内容不为空
    if (!cleaned.trim()) {
      console.log('🔍 validateAndCleanReasoningContent - 清理后内容为空');
      return '';
    }

    // 限制长度（防止过长内容）
    if (cleaned.length > 10000) {
      console.log('🔍 validateAndCleanReasoningContent - 内容过长，截断到10000字符');
      cleaned = cleaned.substring(0, 10000) + '...';
    }

    console.log('🔍 validateAndCleanReasoningContent - 清理完成，长度:', cleaned.length);
    return cleaned;
  } catch (error) {
    console.error('🔍 validateAndCleanReasoningContent - 验证过程中出错:', error);
    return content; // 出错时返回原内容
  }
}

/**
 * 生成示例Mermaid代码（用于测试）
 */
export function generateSampleMermaid(type: 'flowchart' | 'sequence' | 'gantt' = 'flowchart'): string {
  switch (type) {
    case 'flowchart':
      return `flowchart TD
    A[开始创作] --> B{选择阶段}
    B -->|导语| C[建立悬念]
    B -->|铺垫| D[情节推进]
    B -->|高潮| E[冲突爆发]
    B -->|结尾| F[完美收尾]
    C --> G[完成创作]
    D --> G
    E --> G
    F --> G`;

    case 'sequence':
      return `sequenceDiagram
    participant 作者 as 作者
    participant AI as AI助手
    participant 读者 as 读者

    作者->>AI: 请帮我分析这段文字
    AI->>AI: 分析文本结构
    AI->>作者: 提供修改建议
    作者->>作者: 修改文本
    作者->>读者: 发布作品
    读者->>作者: 反馈意见`;

    case 'gantt':
      return `gantt
    title 短篇创作时间规划
    dateFormat  YYYY-MM-DD
    section 创作阶段
    导语创作    :done, intro, 2024-01-01, 2024-01-02
    铺垫构建    :active, buildup, 2024-01-02, 2024-01-04
    高潮设计    :climax, 2024-01-04, 2024-01-05
    结尾收尾    :ending, 2024-01-05, 2024-01-06
    section 修改完善
    初稿修改    :revision1, 2024-01-06, 2024-01-07
    最终润色    :polish, 2024-01-07, 2024-01-08`;

    default:
      return generateSampleMermaid('flowchart');
  }
}
