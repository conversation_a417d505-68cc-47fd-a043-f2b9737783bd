"use client";

import { useState, useEffect } from 'react';
import { WorldBuilding } from '@/lib/db/dexie';

/**
 * 世界观详情状态管理 Hook
 */
export const useWorldBuildingDetail = (
  worldBuilding: WorldBuilding | null,
  isEditing: boolean,
  onSave: (worldBuilding: WorldBuilding) => void
) => {
  // 编辑状态下的世界观数据
  const [editingWorldBuilding, setEditingWorldBuilding] = useState<WorldBuilding | null>(null);

  // 当选中的世界观变化时，更新编辑状态下的世界观数据
  useEffect(() => {
    if (worldBuilding) {
      setEditingWorldBuilding({ ...worldBuilding });
    } else {
      setEditingWorldBuilding(null);
    }
  }, [worldBuilding, isEditing]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (!editingWorldBuilding) return;

    const { name, value } = e.target;
    setEditingWorldBuilding(prev => {
      if (!prev) return prev;

      if (name.startsWith('attributes.')) {
        const attributeName = name.split('.')[1];
        return {
          ...prev,
          attributes: {
            ...prev.attributes,
            [attributeName]: value
          }
        };
      }

      return {
        ...prev,
        [name]: value
      };
    });
  };

  // 处理保存
  const handleSave = () => {
    if (editingWorldBuilding) {
      onSave(editingWorldBuilding);
    }
  };

  return {
    editingWorldBuilding,
    handleInputChange,
    handleSave
  };
};
