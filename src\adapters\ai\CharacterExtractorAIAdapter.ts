"use client";

import { Character } from '@/lib/db/dexie';
import { createAIFactory } from '@/factories/ai/AIFactory';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import createChapterSegmenter, { SegmentOptions } from '@/utils/ai/ChapterSegmenter';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

/**
 * 人物提取结果
 */
export interface CharacterExtractionResult {
  /**
   * 新发现的信息
   */
  newInfo: {
    [key: string]: string;
  };

  /**
   * 更新建议的原因
   */
  updateReasons?: {
    [key: string]: string;
  };
}

/**
 * 更新建议
 */
export interface UpdateSuggestion {
  /**
   * 字段名
   */
  field: string;

  /**
   * 当前值
   */
  currentValue: string | null;

  /**
   * 建议值
   */
  suggestedValue: string;

  /**
   * 更新类型：'add'（添加）或'update'（更新）
   */
  type: 'add' | 'update';

  /**
   * 更新原因
   */
  reason: string;
}

/**
 * 人物提取AI适配器
 * 用于从章节内容中提取和更新人物信息
 */
export class CharacterExtractorAIAdapter {
  private aiSender: any;
  private apiSettings: any;
  private segmenter: any;
  private currentRequest: AbortController | null = null;

  /**
   * 创建人物提取AI适配器
   */
  constructor() {
    const aiFactory = createAIFactory();
    this.aiSender = aiFactory.createAISenderComponent();

    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();

    this.segmenter = createChapterSegmenter();
  }

  /**
   * 从章节内容中提取人物信息
   * @param chapterContent 章节内容
   * @param options 分段选项
   * @returns 提取到的人物信息
   */
  async extractCharactersFromChapter(
    chapterContent: string,
    options?: SegmentOptions
  ): Promise<{ [name: string]: CharacterExtractionResult }> {
    try {
      // 设置分段选项
      if (options) {
        this.segmenter.setOptions(options);
      }

      // 分段处理章节内容
      const segments = this.segmenter.segmentChapter(chapterContent);

      // 创建请求控制器
      this.currentRequest = new AbortController();

      // 从每个段落中提取人物信息
      const segmentResults = await Promise.all(
        segments.map((segment: string, index: number) =>
          this.extractCharactersFromSegment(segment, index, segments.length)
        )
      );

      // 合并所有段落的提取结果
      const mergedResults: { [name: string]: CharacterExtractionResult } = {};

      for (const result of segmentResults) {
        for (const [name, info] of Object.entries(result)) {
          if (!mergedResults[name]) {
            mergedResults[name] = {
              newInfo: {},
              updateReasons: {}
            };
          }

          // 合并新信息
          const typedInfo = info as CharacterExtractionResult;
          for (const [field, value] of Object.entries(typedInfo.newInfo)) {
            mergedResults[name].newInfo[field] = value as string;
          }

          // 合并更新原因
          if (typedInfo.updateReasons) {
            for (const [field, reason] of Object.entries(typedInfo.updateReasons)) {
              mergedResults[name].updateReasons = mergedResults[name].updateReasons || {};
              mergedResults[name].updateReasons[field] = reason as string;
            }
          }
        }
      }

      return mergedResults;
    } catch (error) {
      console.error('从章节提取人物信息失败:', error);
      throw error;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 从章节段落中提取人物信息
   * @param segment 章节段落
   * @param segmentIndex 段落索引
   * @param totalSegments 总段落数
   * @returns 提取到的人物信息
   */
  private async extractCharactersFromSegment(
    segment: string,
    segmentIndex: number = 0,
    totalSegments: number = 1
  ): Promise<{ [name: string]: CharacterExtractionResult }> {
    try {
      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      // 系统提示词
      const systemPrompt = `
你是一位专业的小说人物分析专家，擅长从文本中提取和分析人物信息。你需要遵循以下指导原则：

1. 专业性：使用文学分析的专业视角，识别文本中的显性和隐性人物特征
2. 全面性：关注人物的多个维度，包括外貌、性格、背景、目标、关系等
3. 客观性：基于文本提供客观分析，避免主观臆断和过度解读
4. 精确性：准确识别文本中提到的所有人物，包括主要角色和次要角色
5. 结构化：按照指定的字段结构提取信息，确保输出格式规范

你的任务是从提供的章节文本中提取所有人物的关键信息，并按照指定格式输出。请特别关注以下字段：
- description（描述）：人物的简要概述
- appearance（外貌）：身高、体型、面部特征、服饰等外表特征
- personality（性格）：性格特点、行为模式、情绪表现等
- background（背景）：出身、经历、职业等背景信息
- goals（目标）：人物在故事中想要达成的目标或愿望
- characterArchetype（角色原型）：如英雄、导师、守门人、变形者等
- growthArc（成长弧线）：人物在故事中的成长或变化轨迹
- hiddenMotivation（隐藏动机）：人物表面行为背后的真实动机
- secretHistory（秘密历史）：人物不为人知的过去经历
- innerConflicts（内心冲突）：人物内心的矛盾和挣扎
- symbolism（象征意义）：人物在故事中所代表的象征或主题意义
- relationships（关系）：与其他人物的关系
`;

      // 用户提示词
      const userPrompt = `
我正在处理一部小说的章节内容，需要提取其中的人物信息。

这是章节的第 ${segmentIndex + 1} 段内容，共 ${totalSegments} 段。

章节内容：

${segment}
`;

      // 助手回应
      const assistantResponse = `
我将仔细分析这段章节内容，提取所有出现的人物信息。我会关注人物的外貌、性格、背景、关系、动机、行为模式等方面的描述。
`;

      // 输出格式提示词
      const outputFormatPrompt = `
请以JSON格式输出提取结果，格式如下：

{
  "人物名称1": {
    "newInfo": {
      "description": "人物的简要概述",
      "appearance": "外貌描述（身高、体型、面部特征、服饰等）",
      "personality": "性格描述（性格特点、行为模式、情绪表现等）",
      "background": "背景描述（出身、经历、职业等）",
      "goals": "人物在故事中想要达成的目标或愿望",
      "characterArchetype": "角色原型（如英雄、导师、守门人、变形者等）",
      "growthArc": "人物在故事中的成长或变化轨迹",
      "hiddenMotivation": "人物表面行为背后的真实动机",
      "secretHistory": "人物不为人知的过去经历",
      "innerConflicts": "人物内心的矛盾和挣扎",
      "symbolism": "人物在故事中所代表的象征或主题意义",
      "relationships": "与其他人物的关系描述"
    }
  },
  "人物名称2": {
    "newInfo": {
      // 同上，根据文本中实际提到的信息填写
    }
  }
}

重要说明：
1. 只提取文本中明确描述或合理推断的信息，避免过度解读
2. 如果某个字段没有相关信息，请省略该字段
3. 如果段落中没有明确的人物信息，请返回空对象 {}
4. 确保提取的是本段内容中的信息，而不是之前段落的信息
5. 字段名称必须严格按照上述格式，不要创建新的字段名
6. 对于每个字段，提供详细、具体的描述，而不是简单的一两个词
7. 如果文本中提到了人物关系，请在relationships字段中详细描述
`;

      // 构建多角色消息数组
      const messages = createMessageBuilder()
        .addSystemMessage(systemPrompt)
        .addUserMessage(userPrompt)
        .addAssistantMessage(assistantResponse)
        .addUserMessage(outputFormatPrompt)
        .build();

      // 获取AI配置（包括top_p和top_k）
      let aiConfig: any = {};
      try {
        const { configService } = await import('@/services/configService');
        aiConfig = await configService.getAIConfig();
      } catch (error) {
        console.warn('无法获取AI配置，使用默认值', error);
      }

      // 调用AI模型
      const result = await this.aiSender.sendRequest('', {
        messages,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.3, // 使用较低的温度，确保输出的一致性
        topP: aiConfig.topP,
        topK: aiConfig.topK,
        max_tokens: 2000,
        signal: this.currentRequest?.signal
      });

      // 使用增强的AIResponseParser解析JSON结果
      return AIResponseParser.parseJSON<{ [name: string]: CharacterExtractionResult }>(result.text, {});
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('请求被取消');
        return {};
      }
      console.error('从段落提取人物信息失败:', error);
      return {};
    }
  }

  /**
   * 从章节内容中更新特定人物的信息
   * @param character 人物对象
   * @param chapterContent 章节内容
   * @param options 分段选项
   * @returns 更新建议
   */
  async updateCharacterFromChapter(
    character: Character,
    chapterContent: string,
    options?: SegmentOptions
  ): Promise<UpdateSuggestion[]> {
    try {
      // 设置分段选项
      if (options) {
        this.segmenter.setOptions(options);
      }

      // 分段处理章节内容
      const segments = this.segmenter.segmentChapter(chapterContent);

      // 创建请求控制器
      this.currentRequest = new AbortController();

      // 从每个段落中提取人物信息
      const segmentResults = await Promise.all(
        segments.map((segment: string, index: number) =>
          this.extractCharacterInfoFromSegment(segment, character, index, segments.length)
        )
      );

      // 合并所有段落的提取结果
      const mergedResult: CharacterExtractionResult = {
        newInfo: {},
        updateReasons: {}
      };

      for (const result of segmentResults) {
        // 合并新信息
        for (const [field, value] of Object.entries(result.newInfo)) {
          mergedResult.newInfo[field] = value as string;
        }

        // 合并更新原因
        if (result.updateReasons) {
          for (const [field, reason] of Object.entries(result.updateReasons)) {
            mergedResult.updateReasons = mergedResult.updateReasons || {};
            mergedResult.updateReasons[field] = reason as string;
          }
        }
      }

      // 生成更新建议
      return this.generateUpdateSuggestions(character, mergedResult);
    } catch (error) {
      console.error('从章节更新人物信息失败:', error);
      throw error;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 从章节段落中提取特定人物的信息
   * @param segment 章节段落
   * @param character 人物对象
   * @param segmentIndex 段落索引
   * @param totalSegments 总段落数
   * @returns 提取到的人物信息
   */
  private async extractCharacterInfoFromSegment(
    segment: string,
    character: Character,
    segmentIndex: number = 0,
    totalSegments: number = 1
  ): Promise<CharacterExtractionResult> {
    try {
      // 获取API设置
      const currentProvider = this.apiSettings.getCurrentProvider();
      const currentModel = this.apiSettings.getCurrentModel();
      const apiKey = this.apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

      // 格式化人物信息
      const characterInfo = this.formatCharacterInfo(character);

      // 系统提示词
      const systemPrompt = `
你是一位专业的小说人物分析专家，擅长从文本中提取和分析人物信息。你需要遵循以下指导原则：

1. 专业性：使用文学分析的专业视角，识别文本中的显性和隐性人物特征
2. 全面性：关注人物的多个维度，包括外貌、性格、背景、目标、关系等
3. 客观性：基于文本提供客观分析，避免主观臆断和过度解读
4. 精确性：只关注指定的人物，提取与该人物相关的所有信息
5. 结构化：按照指定的字段结构提取信息，确保输出格式规范

你的任务是从提供的章节文本中提取指定人物的关键信息，并按照指定格式输出。请特别关注以下字段：
- description（描述）：人物的简要概述
- appearance（外貌）：身高、体型、面部特征、服饰等外表特征
- personality（性格）：性格特点、行为模式、情绪表现等
- background（背景）：出身、经历、职业等背景信息
- goals（目标）：人物在故事中想要达成的目标或愿望
- characterArchetype（角色原型）：如英雄、导师、守门人、变形者等
- growthArc（成长弧线）：人物在故事中的成长或变化轨迹
- hiddenMotivation（隐藏动机）：人物表面行为背后的真实动机
- secretHistory（秘密历史）：人物不为人知的过去经历
- innerConflicts（内心冲突）：人物内心的矛盾和挣扎
- symbolism（象征意义）：人物在故事中所代表的象征或主题意义
- relationships（关系）：与其他人物的关系
`;

      // 用户提示词
      const userPrompt = `
我正在处理一部小说的章节内容，需要提取关于"【${character.name}】"的信息。

这是章节的【第 ${segmentIndex + 1} 】段内容，共【 ${totalSegments} 】段。

章节段落内容=[

${segment}
    ]
`;

      // 输出格式提示词
      const outputFormatPrompt = `
请以JSON格式输出提取结果，包括新发现的信息和更新建议。格式如下：

{
  "newInfo": {
    "description": "人物的简要概述",
    "appearance": "外貌描述（身高、体型、面部特征、服饰等）",
    "personality": "性格描述（性格特点、行为模式、情绪表现等）",
    "background": "背景描述（出身、经历、职业等）",
    "goals": "人物在故事中想要达成的目标或愿望",
    "characterArchetype": "角色原型（如英雄、导师、守门人、变形者等）",
    "growthArc": "人物在故事中的成长或变化轨迹",
    "hiddenMotivation": "人物表面行为背后的真实动机",
    "secretHistory": "人物不为人知的过去经历",
    "innerConflicts": "人物内心的矛盾和挣扎",
    "symbolism": "人物在故事中所代表的象征或主题意义",
    "relationships": "与其他人物的关系描述"
  },
  "updateReasons": {
    "description": "更新描述的原因和依据",
    "appearance": "更新外貌的原因和依据",
    "personality": "更新性格的原因和依据"
    // 对应字段的更新原因，说明文本中的具体依据
  }
}

重要说明：
1. 只提取文本中明确描述或合理推断的信息，避免过度解读
2. 如果某个字段没有相关信息，请省略该字段
3. 确保提取的是本段内容中的信息，而不是之前段落的信息
4. 字段名称必须严格按照上述格式，不要创建新的字段名
5. 对于每个字段，提供详细、具体的描述，而不是简单的一两个词
6. 对于每个更新的字段，请在updateReasons中提供具体的文本依据，引用原文中的关键句子
7. 直接返回JSON对象，不要使用Markdown代码块或其他格式标记
8. 不要在JSON前后添加任何额外的文本或解释
`;

      // 构建多角色消息数组
      const messages = createMessageBuilder()
        .addSystemMessage(systemPrompt)
        .addAssistantInfoMessage(characterInfo)
        .addUserMessage(userPrompt)
        .addAssistantConfirmMessage('人物信息提取', character.name)
        .addUserMessage(outputFormatPrompt)
        .build();

      // 调用AI模型
      const result = await this.aiSender.sendRequest('', {
        messages,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.3, // 使用较低的温度，确保输出的一致性
        max_tokens: 2000,
        signal: this.currentRequest?.signal
      });

      // 使用增强的AIResponseParser解析JSON结果
      return AIResponseParser.parseJSON<CharacterExtractionResult>(result.text, { newInfo: {} });
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('请求被取消');
        return { newInfo: {} };
      }
      console.error('从段落提取人物信息失败:', error);
      return { newInfo: {} };
    }
  }

  /**
   * 格式化人物信息
   * @param character 人物对象
   * @returns 格式化后的人物信息字符串
   */
  private formatCharacterInfo(character: Character): string {
    let info = `人物名称: ${character.name}\n`;

    if (character.alias && character.alias.length > 0) {
      info += `别名: ${character.alias.join(', ')}\n`;
    }

    if (character.description) {
      info += `描述: ${character.description}\n`;
    }

    if (character.appearance) {
      info += `外貌: ${character.appearance}\n`;
    }

    if (character.personality) {
      info += `性格: ${character.personality}\n`;
    }

    if (character.background) {
      info += `背景: ${character.background}\n`;
    }

    if (character.goals) {
      info += `目标: ${character.goals}\n`;
    }

    if (character.characterArchetype) {
      info += `角色原型: ${character.characterArchetype}\n`;
    }

    if (character.growthArc) {
      info += `成长弧线: ${character.growthArc}\n`;
    }

    if (character.hiddenMotivation) {
      info += `隐藏动机: ${character.hiddenMotivation}\n`;
    }

    if (character.secretHistory) {
      info += `秘密历史: ${character.secretHistory}\n`;
    }

    if (character.innerConflicts) {
      info += `内心冲突: ${character.innerConflicts}\n`;
    }

    if (character.symbolism) {
      info += `象征意义: ${character.symbolism}\n`;
    }

    return info;
  }

  /**
   * 生成更新建议
   * @param character 人物对象
   * @param extractedInfo 提取到的人物信息
   * @returns 更新建议
   */
  private generateUpdateSuggestions(
    character: Character,
    extractedInfo: CharacterExtractionResult
  ): UpdateSuggestion[] {
    const suggestions: UpdateSuggestion[] = [];

    // 遍历提取的新信息
    for (const [field, value] of Object.entries(extractedInfo.newInfo)) {
      // 检查字段是否存在于character对象中
      if (field in character) {
        // 检查字段是否有值
        if (character[field as keyof Character]) {
          // 字段已存在且有值，生成更新建议
          suggestions.push({
            field,
            currentValue: character[field as keyof Character] as string,
            suggestedValue: value,
            type: 'update',
            reason: extractedInfo.updateReasons?.[field] || '从章节中发现新信息'
          });
        } else {
          // 字段存在但没有值，生成添加建议
          suggestions.push({
            field,
            currentValue: null,
            suggestedValue: value,
            type: 'add',
            reason: extractedInfo.updateReasons?.[field] || '从章节中发现新信息'
          });
        }
      }
    }

    return suggestions;
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.currentRequest = null;
    }
  }
}

/**
 * 创建人物提取AI适配器
 * @returns 人物提取AI适配器实例
 */
export function createCharacterExtractorAIAdapter(): CharacterExtractorAIAdapter {
  return new CharacterExtractorAIAdapter();
}

export default createCharacterExtractorAIAdapter;
