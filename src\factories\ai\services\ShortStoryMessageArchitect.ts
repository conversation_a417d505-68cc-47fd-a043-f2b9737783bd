import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { ShortStoryParams, CoreMystery, SegmentStructure, AssociatedElements, ShortStoryMode } from './types/ShortStoryTypes';

/**
 * 短篇创作消息架构师
 * 专门负责构建短篇小说创作的分层消息架构
 */
export class ShortStoryMessageArchitect {
  
  /**
   * 构建核心悬念生成的消息架构
   */
  static buildCoreMysterMessages(params: ShortStoryParams, associatedElements: AssociatedElements): any[] {
    const messageBuilder = new MessageBuilder();
    
    // 1. 系统身份定义（最高优先级）
    messageBuilder.addSystemMessage(
      this.buildCoreIdentityMessage(),
      true,  // isPinned
      false  // isHidden
    );

    // 2. 创作哲学确立（高优先级）
    messageBuilder.addAssistantMessage(
      this.buildCreativePhilosophyMessage(params.storyTone, params.customPhilosophy),
      true,
      false
    );

    // 3. ACE框架深度学习（中高优先级）
    const aceMessages = this.buildACEFrameworkLearningMessages(params.selectedACEFrameworkIds);
    aceMessages.forEach(aceMessage => {
      if (aceMessage.role === 'system') {
        messageBuilder.addSystemMessage(aceMessage.content, true, false);
      } else if (aceMessage.role === 'assistant') {
        messageBuilder.addAssistantMessage(aceMessage.content, true, false);
      } else {
        messageBuilder.addUserMessage(aceMessage.content, undefined, true, true);
      }
    });

    // 4. 关联元素上下文（中等优先级）
    const contextMessage = this.buildAssociatedElementsContext(associatedElements);
    if (contextMessage) {
      messageBuilder.addUserMessage(
        contextMessage,
        undefined,
        true,  // isContext
        true   // isHidden
      );
    }

    // 5. 核心任务定义（最高优先级）
    messageBuilder.addUserMessage(
      this.buildCoreTaskMessage(params.userInput, params.storyTone),
      '请严格按照JSON格式返回核心悬念结构，确保包含所有必需字段。',
      false, // isContext
      false  // isHidden
    );

    // 6. JSON格式强化要求（最高权重）
    messageBuilder.addSystemMessage(
      this.buildCoreMysteryJSONFormatMessage(),
      true,
      false
    );

    return messageBuilder.build();
  }

  /**
   * 构建核心悬念JSON格式强化消息
   */
  private static buildCoreMysteryJSONFormatMessage(): string {
    return `

    按这个JSON格式给我整出来：
    {
      "id": "mystery_001",
      "title": "短篇标题（要有吸引力，别整那些平淡的）",
      "coreQuestion": "核心悬念问题 - 读者最想知道答案的那个问题",
      "revealedHalf": "开篇透露的一半信息 - 异常现象或结果，让人看了就想问为什么",
      "hiddenHalf": "需要逐步揭露的另一半 - 背后的原因和真相，要有完整逻辑",
      "finalTruth": "最终真相 - 完整答案，要有意外性但又合理",
      "emotionalImpact": "情感冲击点 - 真相揭露时的情感效果，要能打动人",
      "mainCharacter": "主角信息和能力特征",
      "settingInfo": "背景设定和环境",
      "keyTerms": ["关键术语1", "关键术语2"],
      "plotConnection": "与大纲的连接点"
    }
    
    【必须做到的几点】：
    1. coreQuestion得是那种让人无法抗拒的悬念，看了就想知道答案
    2. revealedHalf要有强烈异常感，但别解释原因，吊足胃口
    3. hiddenHalf包含完整的逻辑链条和真相，不能有漏洞
    4. finalTruth要有意外性但又合理，让人恍然大悟又觉得有道理
    5. 充分利用关联的人物、世界观、术语、大纲元素，别浪费素材
    
    【悬念套路参考】：
    - 专业人士发现异常："急诊科医生发现第五个'意外'伤者手握游乐园门票"
    - 信息不对等："只有我看到半空中的红字：【他们现在好开心啊】"
    - 矛盾细节："她在哭，但眼角没有泪痕，妆容完美无缺"
    - 身份反转："以为的拯救者，其实都在计划之内"
    - 时间错乱："手机显示的时间，比墙上的钟表快了整整三小时"
    - 记忆断层："所有人都记得昨天的聚会，只有我完全没印象"
`;
  }

  /**
   * 构建分段结构生成的消息架构
   */
  static buildSegmentStructureMessages(coreMystery: CoreMystery, targetSegments: number, aceFrameworkIds: string[]): any[] {
    const messageBuilder = new MessageBuilder();
    
     // 1. 系统身份定义
     messageBuilder.addSystemMessage(
      this.buildCoreIdentityMessage(),
      true,
      false
    );

    // 2. 专业能力声明
    messageBuilder.addAssistantMessage(
      `明白，我来围绕核心悬念"${coreMystery.title}"，给这个短篇搭一个${targetSegments}段的架子。重点是卡好节奏，尽量让每段都起点作用，把"说一半，藏一半"的悬念感和情绪挤压感做出来。`,
      true,
      false
    );

    // 3. ACE框架技巧应用指导
    if (aceFrameworkIds && aceFrameworkIds.length > 0) {
      const coreTechniques = this.extractACETechniques(aceFrameworkIds);
       messageBuilder.addUserMessage(
        `【ACE点子参考】\n这儿有些从那${aceFrameworkIds.length}个ACE框架里找的点子，给短篇搭架子时可以参考，找找感觉：\n\n` +
        `🎭 剧情可以试试：${coreTechniques.plotTechniques.slice(0, 3).join('、')}\n` +
        `🎵 节奏可以参考：${coreTechniques.rhythmTechniques.slice(0, 3).join('、')}\n` +
        `🔍 悬念可以留意：${coreTechniques.suspenseTechniques.slice(0, 3).join('、')}\n\n` +
        `在分段设计里，试着把这些感觉融进去，不用太刻意。`,
        undefined,
        true,
        true
      );
    }

    // 4. 核心悬念上下文
    messageBuilder.addUserMessage(
       `【故事核心梗概】\n标题大概是：${coreMystery.title}\n核心问题：${coreMystery.coreQuestion}\n能说的部分：${coreMystery.revealedHalf}\n藏着的部分：${coreMystery.hiddenHalf}`,
      undefined,
      true,
      true
    );

    // 5. 分段结构任务
    messageBuilder.addUserMessage(
      this.buildSegmentStructureTaskMessage(coreMystery, targetSegments),
      '最后按JSON数组格式给我就行，字段尽量全哈，方便我用。',
      false,
      false
    );

    // 6. JSON格式强化要求
    messageBuilder.addSystemMessage(
      this.buildSegmentStructureJSONFormatMessage(),
      true,
      false
    );

    return messageBuilder.build();
  }

  /**
   * 构建分段结构JSON格式强化消息
   */
  private static buildSegmentStructureJSONFormatMessage(): string {
    return `
⚠️ **分段结构JSON格式严格要求** ⚠️

必须严格按照以下JSON数组格式输出，不要添加任何解释文字：

[
  {
    "segmentNumber": 1,
    "purpose": "段落目的描述（20-40字）",
    "informationLevel": 3,
    "tensionLevel": 2,
    "paymentHookFlag": false
  },
  {
    "segmentNumber": 8,
    "purpose": "付费卡点段落目的（20-40字）",
    "informationLevel": 8,
    "tensionLevel": 9,
    "paymentHookFlag": true
  }
]

【关键要求】
1. 只输出JSON数组，不要任何其他文字
2. segmentNumber必须从1开始递增
3. purpose必须具体描述段落目的
4. informationLevel和tensionLevel必须是1-10的数字
5. 第8-10段中必须有一段设置paymentHookFlag为true（付费卡点）
6. JSON语法必须正确，能被标准解析器解析

⚠️ 重要：只输出JSON数组，不要任何解释！
`;
  }

  /**
   * 构建阶段内容生成的消息架构（一次性生成整个阶段）
   */
  static buildPhaseContentMessages(
    segments: SegmentStructure[],
    previousSegments: string[],
    coreMystery: CoreMystery,
    associatedElements: AssociatedElements,
    aceFrameworkIds: string[],
    totalSegments: number = 20
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 计算阶段目标字数
    const phaseWordCount = Math.round((10000 / totalSegments) * segments.length);
    const avgSegmentWords = Math.round(phaseWordCount / segments.length);

    // 确定阶段类型和效果策划
    const phaseInfo = this.getPhaseEffectStrategy(segments, totalSegments);

    // 1. 系统身份定义
    messageBuilder.addSystemMessage(
      this.buildCoreIdentityMessage(),
      true,
      false
    );

    // 2. 阶段效果策划说明
    messageBuilder.addAssistantMessage(
      this.buildPhaseEffectMessage(phaseInfo, segments.length, phaseWordCount, avgSegmentWords),
      true,
      false
    );

    // 3. 阶段剧情点扩展指导
    messageBuilder.addUserMessage(
      this.buildPhaseExpansionGuidance(phaseInfo, segments),
      undefined,
      true,
      false
    );

    // 4. 核心悬念和前文上下文
    messageBuilder.addUserMessage(
      this.buildPhaseContextMessage(coreMystery, previousSegments, segments),
      undefined,
      true,
      false
    );

    return messageBuilder.build();
  }

  /**
   * 获取阶段效果策划信息
   */
  private static getPhaseEffectStrategy(segments: SegmentStructure[], totalSegments: number): {
    phaseType: string;
    phaseGoal: string;
    plotDensityStrategy: string;
    emotionalTarget: string;
    keyTechniques: string[];
  } {
    const firstSegment = segments[0].segmentNumber;
    const lastSegment = segments[segments.length - 1].segmentNumber;
    const phasePosition = firstSegment / totalSegments;

    // 根据段落位置确定阶段类型
    if (phasePosition <= 0.25) {
      return {
        phaseType: '铺垫期',
        phaseGoal: '建立人物困境，埋下悬念种子',
        plotDensityStrategy: '密集铺垫女主的惨状，通过细节堆叠营造同情感',
        emotionalTarget: '让读者心疼女主，产生保护欲和好奇心',
        keyTechniques: ['细节堆叠法', '对比反差法', '暗示埋伏法']
      };
    } else if (phasePosition <= 0.75) {
      return {
        phaseType: '挤压期',
        phaseGoal: '冲突升级，情绪压迫感递增',
        plotDensityStrategy: '多线并进，每个剧情点都要有反转或加码',
        emotionalTarget: '让读者紧张焦虑，迫切想知道结果',
        keyTechniques: ['多线交织法', '压迫升级法', '信息差制造法']
      };
    } else if (phasePosition <= 0.95) {
      return {
        phaseType: '高潮期',
        phaseGoal: '真相揭露，情绪爆发',
        plotDensityStrategy: '连环爆点，每段都要有震撼性发现',
        emotionalTarget: '让读者震惊、恍然大悟、情绪达到顶峰',
        keyTechniques: ['连环爆点法', '真相反转法', '情绪引爆法']
      };
    } else {
      return {
        phaseType: '结局期',
        phaseGoal: '收束情绪，留下余韵',
        plotDensityStrategy: '精炼收尾，每个细节都要有回味价值',
        emotionalTarget: '让读者满足但意犹未尽，产生回味感',
        keyTechniques: ['回味留白法', '情绪收束法', '细思极恐法']
      };
    }
  }

  /**
   * 构建阶段效果消息
   */
  private static buildPhaseEffectMessage(
    phaseInfo: any,
    segmentCount: number,
    phaseWordCount: number,
    avgSegmentWords: number
  ): string {
    return `
好嘞！现在进入【${phaseInfo.phaseType}】阶段！

【这个阶段要达到的效果】
${phaseInfo.phaseGoal} —— ${phaseInfo.emotionalTarget}

【剧情密度策略】
${phaseInfo.plotDensityStrategy}

我要一口气写${segmentCount}个段落，整个阶段大概${phaseWordCount}字，平均每段${avgSegmentWords}字左右。

重点是：段落之间要无缝衔接，剧情要层层递进，不能有断层感！每段都要推进剧情或者加深情绪，绝不能有废话段落！

【核心技巧运用】
这个阶段我会重点使用：${phaseInfo.keyTechniques.join('、')}，确保剧情密度够足，不让读者有喘息的机会！
`;
  }

  /**
   * 构建阶段剧情点扩展指导
   */
  private static buildPhaseExpansionGuidance(phaseInfo: any, segments: SegmentStructure[]): string {
    const expansionStrategies = {
      '铺垫期': [
        '女主的惨状细节扩展：不只是说"很惨"，要具体到衣食住行的每个细节',
        '环境对比反差：通过周围人的幸福来反衬女主的悲惨',
        '过往回忆闪现：通过回忆片段暗示女主曾经的美好，形成强烈对比',
        '微表情捕捉：通过细微的表情变化展现女主内心的挣扎和坚强'
      ],
      '挤压期': [
        '多重打击叠加：不是单一的困难，而是接连不断的打击',
        '希望与绝望交替：给一点希望马上又被打破，情绪过山车',
        '旁观者态度变化：从同情到冷漠，甚至到厌恶，社会压力递增',
        '内心独白深化：通过内心戏展现女主的心理变化过程'
      ],
      '高潮期': [
        '真相连环爆炸：一个真相引出另一个真相，层层递进',
        '情绪临界点突破：女主或其他角色的情绪彻底爆发',
        '关键信息汇聚：前面埋下的所有线索在这里汇聚成完整真相',
        '视角切换震撼：从不同角度重新审视之前的事件'
      ],
      '结局期': [
        '余韵细节点缀：看似不重要的小细节其实暗含深意',
        '开放式结尾暗示：不把话说死，留给读者想象空间',
        '情绪沉淀升华：从激烈情绪转向深层思考',
        '细思极恐元素：让读者事后回想起来还会心惊'
      ]
    };

    const strategies = expansionStrategies[phaseInfo.phaseType as keyof typeof expansionStrategies] || [];

    return `
【${phaseInfo.phaseType}剧情点扩展策略】

这个阶段的每个段落都要有足够的剧情密度，不能只有一个支点！要用这些策略来扩展剧情点：

${strategies.map((strategy, index) => `${index + 1}. ${strategy}`).join('\n')}

【段落剧情点分配】
${segments.map(seg =>
  `第${seg.segmentNumber}段："${seg.purpose}"
  → 可扩展点：除了基本目的，还要加入什么细节/反转/情绪点来增加密度？`
).join('\n\n')}

记住：每段都要有多个剧情支点，不能只靠一个点撑起整段！要让读者感觉信息量很足，但又不会信息过载！
`;
  }

  /**
   * 构建阶段上下文消息
   */
  private static buildPhaseContextMessage(
    coreMystery: CoreMystery,
    previousSegments: string[],
    segments: SegmentStructure[]
  ): string {
    return `
【核心悬念回顾】
标题：${coreMystery.title}
透露的一半：${coreMystery.revealedHalf}
隐藏的一半：${coreMystery.hiddenHalf}

【前文内容】
${previousSegments.length > 0 ? previousSegments.join('\n\n') : '（这是开头阶段，没有前文）'}

【本阶段段落结构】
${segments.map(seg =>
  `第${seg.segmentNumber}段：${seg.purpose} (信息量${seg.informationLevel}/10, 紧张感${seg.tensionLevel}/10)`
).join('\n')}

【创作要求】
1. 一次性生成这${segments.length}个段落，确保剧情连贯
2. 每段开头必须用"第X段："标记
3. 段落间要无缝衔接，不能有跳跃感
4. 每段都要推进剧情或加深情绪，绝不能有废话
5. 保持那种"憋"的感觉，说一半留一半的节奏

开始创作！
`;
  }

  /**
   * 构建阶段策划生成的消息架构（让AI来规划每个阶段的具体效果）
   */
  static buildPhaseStrategyMessages(
    coreMystery: CoreMystery,
    totalSegments: number = 20,
    storyMode?: string
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 1. 系统身份定义
    messageBuilder.addSystemMessage(
      this.buildPhaseStrategyIdentityMessage(),
      true,
      false
    );

    // 2. 策划任务说明
    messageBuilder.addUserMessage(
      this.buildPhaseStrategyTaskMessage(coreMystery, totalSegments, storyMode),
      undefined,
      true,
      false
    );

    return messageBuilder.build();
  }

  /**
   * 构建阶段策划专用的系统身份消息
   */
  private static buildPhaseStrategyIdentityMessage(): string {
    return `
嘿！现在你是个短篇故事的【阶段效果策划师】！

你的活儿就是：拿到一个核心悬念后，给这个短篇的4个阶段（铺垫期、挤压期、高潮期、结局期）制定具体的【效果策划】。

【你要干的事】
不是写故事，是策划每个阶段要达到什么效果，怎么实现"藏一半说一半"！

【核心原则】
1. 【密度第一】：每个阶段都要有足够的剧情密度，不能只靠一个支点撑起来
2. 【情绪操控】：要明确每个阶段要让读者产生什么具体情绪
3. 【信息分层】：什么时候放出什么信息，什么时候藏着掖着
4. 【节奏把控】：快慢张弛的具体节拍怎么踩

【你的专业能力】
- 深谙"藏一半说一半"的精髓，知道什么时候该露，什么时候该藏
- 懂得如何通过细节堆叠来营造情绪（比如：如何铺垫女主的惨）
- 明白每个阶段的情绪递进逻辑和剧情密度要求
- 会设计多层次的剧情支点，避免单一支撑导致的剧情单薄

记住：你是策划师，不是执行者！要给出具体可操作的策划方案！

⚠️ **JSON格式严格要求** ⚠️
- 必须返回完整的JSON格式，不能有任何解释文字
- 所有字段都必须填写，不能为空
- 数组字段至少包含3个元素
- 字符串字段必须达到最小字数要求
- JSON必须能够被标准解析器解析，不能有语法错误
`;
  }

  /**
   * 构建阶段策划任务消息
   */
  private static buildPhaseStrategyTaskMessage(
    coreMystery: CoreMystery,
    totalSegments: number,
    storyMode?: string
  ): string {
    return `
【策划任务】
给这个核心悬念制定4个阶段的具体效果策划：

【核心悬念】
标题：${coreMystery.title}
核心问题：${coreMystery.coreQuestion}
透露的一半：${coreMystery.revealedHalf}
隐藏的一半：${coreMystery.hiddenHalf}

【基础信息】
- 总段落数：${totalSegments}段
- 故事风格：${storyMode || 'mystery'}
- 目标字数：约1万字

【你要策划的4个阶段】

**铺垫期（约前25%段落）**
- 这个阶段要达到什么具体效果？
- 如何铺垫主要矛盾/人物困境？（比如：如何铺垫女主的惨）
- "说一半"：要透露哪些信息来吸引读者？
- "藏一半"：要隐藏哪些关键信息制造悬念？
- 剧情密度策略：除了主线，还要加入哪些支线/细节来丰富内容？

**挤压期（约中间50%段落）**
- 这个阶段要达到什么具体效果？
- 如何让冲突升级，情绪压迫感递增？
- "说一半"：要逐步放出哪些信息？
- "藏一半"：要继续隐藏哪些关键真相？
- 剧情密度策略：如何通过多线并进避免剧情单薄？
- 🔥 **付费卡点设计**：在约4000字处（第8-10段）设置最强钩子，让读者欲罢不能！

**高潮期（约后20%段落）**
- 这个阶段要达到什么具体效果？
- 如何让真相揭露，情绪爆发？
- "说一半"：要爆出哪些重磅真相？
- "藏一半"：还要保留哪些最后的悬念？
- 剧情密度策略：如何设计连环爆点避免单调？

**结局期（约最后5%段落）**
- 这个阶段要达到什么具体效果？
- 如何收束情绪，留下余韵？
- "说一半"：要给出哪些最终答案？
- "藏一半"：要留下哪些让人回味的空白？
- 剧情密度策略：如何在短篇幅内做到精炼而有深度？

【输出格式】
必须严格按照以下JSON格式输出，不要添加任何解释文字：

{
  "setupPhase": {
    "phaseType": "铺垫期",
    "phaseGoal": "铺垫期的具体目标效果（50字以上）",
    "revealStrategy": "说一半的具体策略（80字以上）",
    "hideStrategy": "藏一半的具体策略（80字以上）",
    "plotDensityTactics": ["战术1", "战术2", "战术3"],
    "emotionalTarget": "目标情绪效果（40字以上）",
    "keyTechniques": ["技巧1", "技巧2", "技巧3"]
  },
  "compressionPhase": {
    "phaseType": "挤压期",
    "phaseGoal": "挤压期的具体目标效果（50字以上）",
    "revealStrategy": "说一半的具体策略（80字以上）",
    "hideStrategy": "藏一半的具体策略（80字以上）",
    "plotDensityTactics": ["战术1", "战术2", "战术3"],
    "emotionalTarget": "目标情绪效果（40字以上）",
    "keyTechniques": ["技巧1", "技巧2", "技巧3"],
    "paymentHook": {
      "position": "约4000字处（第8-10段）",
      "hookStrategy": "最强钩子制作策略（100字以上）",
      "cliffhangerDesign": "悬崖式卡点设计（80字以上）",
      "readerPsychology": "读者心理操控要点（60字以上）"
    }
  },
  "climaxPhase": {
    "phaseType": "高潮期",
    "phaseGoal": "高潮期的具体目标效果（50字以上）",
    "revealStrategy": "说一半的具体策略（80字以上）",
    "hideStrategy": "藏一半的具体策略（80字以上）",
    "plotDensityTactics": ["战术1", "战术2", "战术3"],
    "emotionalTarget": "目标情绪效果（40字以上）",
    "keyTechniques": ["技巧1", "技巧2", "技巧3"]
  },
  "resolutionPhase": {
    "phaseType": "结局期",
    "phaseGoal": "结局期的具体目标效果（50字以上）",
    "revealStrategy": "说一半的具体策略（80字以上）",
    "hideStrategy": "藏一半的具体策略（80字以上）",
    "plotDensityTactics": ["战术1", "战术2", "战术3"],
    "emotionalTarget": "目标情绪效果（40字以上）",
    "keyTechniques": ["技巧1", "技巧2", "技巧3"]
  }
}

⚠️ 重要：只输出JSON，不要任何其他文字！
`;
  }

  /**
   * 构建段落内容生成的消息架构
   */
  static buildSegmentContentMessages(
    segment: SegmentStructure,
    previousSegments: string[],
    coreMystery: CoreMystery,
    associatedElements: AssociatedElements,
    aceFrameworkIds: string[],
    totalSegments: number = 20
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 计算目标字数（1万字总目标，按段落分配）
    const targetWordCount = Math.round(10000 / totalSegments);
    const wordCountRange = `${Math.round(targetWordCount * 0.8)}-${Math.round(targetWordCount * 1.2)}`;

    // 1. 系统身份定义
    messageBuilder.addSystemMessage(
      this.buildCoreIdentityMessage(),
      true,
      false
    );

    // 2. 段落专业声明（根据是否为付费卡点调整）
    const isPaymentHook = segment.paymentHookFlag;
    const segmentDeclaration = isPaymentHook
      ? `了解！这是第 ${segment.segmentNumber} 段 —— 💰【付费卡点】💰！

这是最关键的钩子制造段落！我要在这里释放最强的剧情冲击，让读者欲罢不能！

目标效果："${segment.purpose}"
剧情强度：(${segment.informationLevel}/10, ${segment.tensionLevel}/10) —— 必须达到最高冲击力！
字数控制：${wordCountRange}字左右

🎯 付费卡点策略：
- 在这里爆出最震撼的真相片段
- 制造最强烈的情绪冲击
- 设置最吊人胃口的悬崖式结尾
- 让读者产生强烈的"必须知道后续"的冲动

开始制造最强钩子！`
      : `了解，我来写第 ${segment.segmentNumber} 段。尽量往"${segment.purpose}"这个目的上靠，信息量和紧张感么，大概按(${segment.informationLevel}/10, ${segment.tensionLevel}/10)这个强度感觉走。字数控制在${wordCountRange}字左右，按平台短篇格式输出。`;

    messageBuilder.addAssistantMessage(
      segmentDeclaration,
      true,
      false
    );

    // 3. ACE框架具体写作指导
    if (aceFrameworkIds && aceFrameworkIds.length > 0) {
      const applicableTechniques = this.selectApplicableTechniques(segment, aceFrameworkIds);
      if (applicableTechniques.length > 0) {
        messageBuilder.addUserMessage(
          `【ACE写作小贴士】\n写第${segment.segmentNumber}段("${segment.purpose}")时，可以试试下面几个点子，把短篇情绪压上去：\n\n` +
          applicableTechniques.map((technique, idx) => `${idx + 1}. ${technique}`).join('\n') +
          `\n\n写作时把这些技巧的感觉融进去就行，灵活点。`,
          undefined,
          true,
          true
        );
      }
    }

    // 4. 核心悬念上下文
    messageBuilder.addUserMessage(
       `【别忘了核心问题】${coreMystery.coreQuestion}`,
      undefined,
      true,
      true
    );

    // 5. 前文内容（如果有）
    if (previousSegments.length > 0) {
      messageBuilder.addUserMessage(
        `【前面写了这些】(注意衔接，体会下最终那种 '数字标号、不空行' 的紧凑格式感哈)\n${previousSegments.join('\n\n')}`,
        undefined,
        true,
        true
      );
    }

    // 6. 关联元素上下文
    const contextMessage = this.buildAssociatedElementsContext(associatedElements);
    if (contextMessage) {
      messageBuilder.addUserMessage(
        contextMessage,
        undefined,
        true,
        true
      );
    }

    // 7. 段落生成任务
    messageBuilder.addUserMessage(
      this.buildSegmentContentTaskMessage(segment),
       '直接给这一段的内容就行，【格式注意】：必须以 `数字.` 开头（例如`${segment.segmentNumber}. `)，然后是正文，段落间不空行，别加客套话和解释。',
      false,
      false
    );

    return messageBuilder.build();
  }
  

  

  /**
   * 构建核心身份消息
   */
  private static buildCoreIdentityMessage(): string {
     return `

嘿，伙计！现在你就是一个在知乎、小程序上混的讲故事老手，咱们要写那种让人心里痒痒的故事。
核心玩法就一个字：“憋”！—— 说一半留一半，把那股子情绪（好奇、紧张、懵圈都行）给“憋”出来，让人难受又想看！

【格式瞅一眼】：有个小习惯，喜欢用 '1.' '2.' 这样的小数字标号开头，段落之间贴紧点，别空行，显得利索。，数字标记只能标记正文的开头，其他的地方不要出现
【核心原则】：
- 不要"讲述"故事，要"创造"故事世界
- 读者要有身临其境的感觉，不是听别人转述
- 用"正在发生"的视角，不用"回忆过去"的视角

写的时候，脑子里过一下：
- 开头能不能稍微“怪”一点？让人一愣那种，别急着解释圆场。
- 这段话是不是废话？尽量每段都得起点作用，要么推故事，要么拱火情绪。
- 留意那些不起眼、但好像有点不对劲的小地方。
- 故意“卖个关子”，让人读了有点“啊？咋回事？”的感觉。
- 【重点】用好那些 不空行连着写，像踩点一样，把故事节奏卡得紧一点，别松劲儿。
- 注意不要，过度的让剧情变的片段化，前后最好处于连接的连贯状态，每次写就当是一次场景或者转折等的转变
记住哈，咱们不追求字字珠玑、完美无瑕！有时候话说得“糙”一点、留点毛边儿，或者某个点没做到极致，反而更真实、更抓人！感觉和那个“味儿”对了最重要，别太拘谨，放开写！差不多得了！



就是故事世界的创造者。读者要感觉自己就在故事现场，亲眼目睹一切发生。


【严禁叙述式写法！】
❌ 绝对禁止这些表达：
- "我觉得"、"我发现"、"我开始意识到"、"我当时就"
- "打那以后"、"从那时候开始"、"后来我才知道"
- "你猜怎么着"、"结果呢"、"没想到"、"这让我"
- 任何回忆式、总结式、转述式的语言

【强制要求】：
- 每段必须以具体的场景、动作、对话开头
- 多写看到的、听到的、感受到的，少写"想到的"
- 用"当下进行时"，让读者感觉事情正在眼前发生
【写作风格】：
- 直接进入场景，不要铺垫和解释
- 多用对话、动作、环境细节
- 让读者自己体验，不要告诉读者感受
- 保持那个"抓人"的劲儿，但要身临其境

记住：你在创造一个世界，读者要感觉自己就在其中！
咱们直接开始短篇，不进行解释，直接以主角视角张开一切

`;
  }

  /**
   * 构建创作哲学消息
   */
  private static buildCreativePhilosophyMessage(storyMode?: ShortStoryMode, customPhilosophy?: string): string {
    let philosophyMessage = `这次短篇的创作基调/感觉大概是这样：`;
    
    if (storyMode === 'custom' && customPhilosophy) {
      philosophyMessage += `\n\n【自定义风格】\n${customPhilosophy}`;
    } else {
      const modePhilosophies = {
        mystery: '逻辑严密的线索布局，真相层层剥离 - 信息差制造 + 逻辑链条构建',
        thriller: '心理压迫感递增，情绪张力爆发 - 心理暗示 + 情绪操控技巧',
        horror: '未知恐惧渗透，现实扭曲感营造 - 恐惧心理学 + 现实感破坏',
        drama: '人性深度挖掘，情感冲突爆发 - 情感共鸣 + 人性矛盾展现',
        comedy: '荒诞与现实交织，黑色幽默渗透 - 认知反差 + 荒诞现实主义',
        philosophical: '深层思考引导，存在意义探讨 - 思维实验 + 哲学悖论构建'
      };
      
      philosophyMessage += `\n\n【${storyMode}模式感觉】\n${modePhilosophies[storyMode as keyof typeof modePhilosophies] || modePhilosophies.mystery}`;
    }
    
     philosophyMessage += `\n\n好，我会在创作时尽量按这个感觉和风格来走，保持短篇的调性和情绪浓度。`;
    
    return philosophyMessage;
  }

  /**
   * 获取分类显示名称
   */
  private static getCategoryDisplayName(category: string): string {
    const categoryMap: Record<string, string> = {
      'synopsis-keywords': '简介关键词',
      'synopsis-framework': '简介框架',
      'outline-framework': '大纲框架',
      'unknown': '未知类型'
    };
    return categoryMap[category] || category;
  }

  /**
   * 构建ACE框架学习消息
   */
  private static buildACEFrameworkLearningMessages(aceFrameworkIds?: string[]): Array<{ role: string; content: string }> {
    if (!aceFrameworkIds || aceFrameworkIds.length === 0) {
      return [];
    }

    try {
      // 使用新的ACEFrameworkManager获取所有框架数据
      const { ACEFrameworkManager } = require('../../../services/ACEFrameworkManager');
      const allFrameworks = ACEFrameworkManager.getAllFrameworks();

      const selectedFrameworks = allFrameworks.filter((framework: any) =>
        aceFrameworkIds.includes(framework.id)
      );

      if (selectedFrameworks.length === 0) {
        return [];
      }

      const messages: Array<{ role: string; content: string }> = [];

      // 添加ACE框架学习引导
      messages.push({
        role: 'system',
        content: `【瞅一眼ACE框架】
这里有${selectedFrameworks.length}个ACE创作框架，有些点子可以用在短篇或者其他文学创作中里。你过一遍，找找灵感，写的时候觉得合适就参考着用。

看看这些方面（想想怎么用在快节奏短篇里）：
1. 情节设计 - 怎么让剧情抓人
2. 节奏控制 - 故事快慢怎么把握，我将注意每个数字，确保节奏的控制
3. 悬念营造 - 怎么吊人胃口
4. 结构安排 - 短篇架子怎么搭得紧凑`
      });

      // 为每个框架创建专门的学习消息
      selectedFrameworks.forEach((framework: any, index: number) => {
        const frameworkName = framework.name || framework.frameworkName || '未命名框架';
        const category = framework.category || 'unknown';

        let frameworkContent = `=== ACE框架 ${index + 1}: ${frameworkName} ===\n`;
        frameworkContent += `分类：${this.getCategoryDisplayName(category)}\n`;
        frameworkContent += `效果评分：${framework.effectiveness || 0}/10\n\n`;

        // 根据框架类型添加不同的内容
        if (framework.category === 'synopsis-keywords' && framework.keywordElements) {
          frameworkContent += `🔥 灵感关键词索引：\n`;
          framework.keywordElements.slice(0, 8).forEach((keyword: any, idx: number) => {
            frameworkContent += `${idx + 1}. ${keyword.text} (热度:${keyword.hotness}) [${keyword.tags.join('、')}]\n`;
          });
          frameworkContent += `\n💡 灵感应用指导：\n`;
          frameworkContent += `- 这些关键词代表了读者喜爱的元素和情感触点\n`;
          frameworkContent += `- 在构思悬念时，可以从这些关键词中寻找灵感\n`;
          frameworkContent += `- 将高热度关键词融入故事核心，增强读者共鸣\n\n`;
        } else if (framework.category === 'synopsis-framework' && framework.synopsisStructure) {
          frameworkContent += `📖 剧情概要结构：\n`;
          if (framework.synopsisStructure.openingTechniques?.length > 0) {
            frameworkContent += `开场技巧：${framework.synopsisStructure.openingTechniques.join('、')}\n`;
          }
          if (framework.synopsisStructure.hookStrategies?.length > 0) {
            frameworkContent += `钩子策略：${framework.synopsisStructure.hookStrategies.join('、')}\n`;
          }
          if (framework.synopsisStructure.characterIntroduction?.length > 0) {
            frameworkContent += `角色引入：${framework.synopsisStructure.characterIntroduction.join('、')}\n`;
          }
          frameworkContent += `\n🎯 剧情概要指导：\n`;
          frameworkContent += `- 这个框架提供了故事概要的结构模板\n`;
          frameworkContent += `- 在设计核心悬念时，参考其开场和钩子策略\n`;
          frameworkContent += `- 确保故事概要具有清晰的起承转合\n\n`;
        } else {
          // 大纲框架或其他类型
          if (framework.frameworkPattern || framework.pattern) {
            frameworkContent += `📋 核心模式：\n${framework.frameworkPattern || framework.pattern}\n\n`;
          }

          if (framework.writingTechniques && framework.writingTechniques.length > 0) {
            frameworkContent += `🎯 写作技巧：\n`;
            framework.writingTechniques.slice(0, 3).forEach((technique: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${technique.name} - ${technique.description}\n`;
            });
            frameworkContent += '\n';
          }
        }

        // 添加示例
        if (framework.examples && framework.examples.length > 0) {
          frameworkContent += `💡 参考示例：\n`;
          framework.examples.slice(0, 2).forEach((example: string, idx: number) => {
            frameworkContent += `${idx + 1}. ${example}\n`;
          });
        }

        messages.push({
          role: 'user',
          content: frameworkContent
        });

        // AI确认学习
        messages.push({
          role: 'assistant',
           content: `OK,"${frameworkName}"这个${this.getCategoryDisplayName(category)}框架我扫了一遍，有点意思，大概知道它的套路/感觉了，写短篇的时候我留意下。`
        });
      });

      // 综合学习总结
      messages.push({
        role: 'assistant',
        content: `【ACE框架看完了】
好嘞，这${selectedFrameworks.length}个框架都过了一遍。我会在构思悬念、搭架子（特别是数字分段）、写内容的时候，把觉得合适的点子或感觉用进去：

1. 核心悬念生成 - 参考框架找点子
2. 分段结构设计 - 留意框架的节奏感
3. 段落内容创作 - 融进一些写作感觉

让这个短篇故事更有味道点，不保证100%照搬哈。`
      });

      return messages;
    } catch (error) {
      console.error('构建ACE框架学习消息失败:', error);
      return [];
    }
  }

  /**
   * 构建关联元素上下文
   */
  private static buildAssociatedElementsContext(elements: AssociatedElements): string | null {
    const contextParts: string[] = [];

    if (elements.characters && elements.characters.length > 0) {
      contextParts.push(`【故事里有这些人】\n${elements.characters.map(char => 
        `- ${char.name}：${char.description || char.personality || '（这人没啥描述）'}`
      ).join('\n')}`);
    }

    if (elements.worldBuildings && elements.worldBuildings.length > 0) {
       contextParts.push(`【背景设定提个要】\n${elements.worldBuildings.map(wb => 
        `- ${wb.name}：${wb.description || '（没细说）'}`
      ).join('\n')}`);
    }

    if (elements.terminologies && elements.terminologies.length > 0) {
       contextParts.push(`【几个关键词】\n${elements.terminologies.map(term => 
        `- ${term.name}：${term.definition || term.description || '（没细说）'}`
      ).join('\n')}`);
    }

    if (elements.outlineNodes && elements.outlineNodes.length > 0) {
       contextParts.push(`【大纲上的点】\n${elements.outlineNodes.map(node => 
        `- ${node.title}：${node.description || node.content || '（没细说）'}`
      ).join('\n')}`);
    }

     return contextParts.length > 0 ? `【相关材料瞅一眼】\n\n` + contextParts.join('\n\n') : null;
  }

  /**
   * 构建核心任务消息 - 修正版本，输出正确的CoreMystery格式
   */
  private static buildCoreTaskMessage(userInput: string, storyMode?: ShortStoryMode): string {
    return `好嘞！开工！
用户扔过来一个想法："${userInput}"。
咱们要干的活，不是简单想个'点子'，是要给这个短篇故事，打造一个 ${storyMode || 'mystery'} 味儿的【灵魂】和【骨架】！一个让人一看就挪不开眼的核心事件梗概！把它想象成整个故事的心脏和发动机！

【来，捋捋思路，别整那些虚的！】（不用完美，但'核'要够劲！）
1.  【抓住那个'核'】：必须围绕一个【核心事件/情境/冲突】来创建梗概！这个'核'本身就要带点不对劲、拧巴、让人抓心挠肝的好奇感。这是整个故事的地基！
2.  【露一半，勾人！】：围绕这个核心事件，读者第一眼会看到哪个'面'（表象/线索）？这个'面'得足够抓人、足够怪、像个钩子，但打死也别说破真相！
3.  【藏一半，圆场！】：事件背后到底藏着啥真相？逻辑上能大概圆回来就行，允许"有点糙"，有点"意料之外"也行，但别瞎编到天上去，得跟前面露出来的'钩子'能对上！
4.  【'嘭'一下！】：真相大白（抖包袱）时，必须跟读者看到的"表象"形成强烈反差或关联！要的就是那个"我靠？！"或者"原来TM是这样？！"的情绪爆发。情绪！情绪！情绪！不到位不行！

【输出结构：就按这个模子来！】
格式得有，但内容要'活'！别整太死板了。麻利点，按这个 JSON 格式给我，里面的文字就是核心梗概内容：
{
  "id": "mystery_001",
  "title": "一个有内味儿的、带悬念感的、抓眼球的标题",
  "coreQuestion": "核心悬念问题：读者最想知道答案的那个问题，要让人看了就想往下翻",
  "revealedHalf": "表层呈现/钩子：围绕核心事件，读者最先看到的、引发好奇的事件表象/疑点/线索是什么？(对应 露一半)",
  "hiddenHalf": "隐藏真相/内幕：暂时藏起来的，构成悬念的事件内幕/另一面是什么？(对应 藏一半，先别说)",
  "finalTruth": "最终揭秘/翻转：最后抖出的包袱，真相如何揭露，怎样连接表象和内幕？(那个让人我靠的点)",
  "emotionalImpact": "情绪落点/爆点：揭秘时，最想让读者咯噔一下的核心情绪是什么？(比如：震惊/心酸/恐惧/恍然大悟/唏嘘)",
  "mainCharacter": "主角信息和能力特征（可选）",
  "settingInfo": "背景设定和环境（可选）",
  "keyTerms": ["关键术语1", "关键术语2"],
  "plotConnection": "与大纲的连接点（可选）"
}
大概齐就行，关键是那个"故事核"和"情绪"要立得住！别磨叽！`;
  }

  /**
   * 构建分段结构任务消息
   */
  private static buildSegmentStructureTaskMessage(coreMystery: CoreMystery, targetSegments: number): string {
    return `OK，哥们儿！架子搭起来！
咱们要围绕核心故事核："${coreMystery.title}"，给它设计一个 ${targetSegments} 段的故事【节奏点】和【骨架】。记住哈，这 ${targetSegments} 段不是别的，就是咱们在平台上讲故事踩的那个【鼓点】，是控制读者情绪呼吸的！这不是啥完美蓝图，就是个节奏参考线，松快点！

【节奏怎么踩？—— 大概的【感觉流】，别拿计算器按！】
感觉大概是这样就行，哪块多点少点，你觉得顺就行，千万别较劲，人无完人，故事也一样：
-  【开头那几段】 (大概占个零头，比如 ~25%): 先把气氛搞起来，人物、背景亮个相，悄悄埋几个小钩子、小疑点，别声张。
-  【中间一大块】 (重头戏，使劲造，比如 ~35%): 冲突得冒头了！把钩子拉紧，情绪和压力得往上拱，让人觉得"事儿不对！"
-  【爆破点附近】 (关键几下，得炸，比如 ~25%): 最高潮！冲突彻底干起来，核心秘密"嘭"一下揭开，把情绪顶到头！
-  【收个尾巴】   (短平快，留口气，比如 ~15%): 扫尾工作，或者留个让人回味/细思极恐的小尾巴，别啰嗦。
(重申：百分比就是个意思！该长就长，该短就短，节奏感觉对了，比啥完美比例都强！)

【输出模版：JSON数组，把'骨架'画出来】
按这个JSON格式给我，字段要对应系统接口，别搞错了：
[
 {
  "segmentNumber": 1,
  "purpose": "具体的段落目的，比如：建立主角困境、埋下第一个疑点、展现异常现象、揭露关键线索等",
  "informationLevel": 3,
  "tensionLevel": 5,
  "phase": "铺垫期",
  "keyEvents": ["这段的关键事件"],
  "mysteryElements": ["这段涉及的悬念元素"],
  "revealedInfo": ["这段透露的信息"],
  "hiddenInfo": ["这段隐藏的信息"]
 },
 //... 后面按 ${targetSegments} 段生成
]

【重要】：
- purpose字段要具体，不要写"铺垫阶段第X段"这种模板话，要写具体做什么
- informationLevel和tensionLevel用1-10数字
- phase要根据段落位置填写：铺垫期/挤压期/高潮期/结局期
- 每段的purpose都要不同，要有具体的剧情推进作用

记住：这是个'毛坯'骨架，允许不完美，'感觉'和'节奏流'最重要！别憋着，放开整！`;
  }

  /**
   * 构建段落内容任务消息
   */
  private static buildSegmentContentTaskMessage(segment: SegmentStructure): string {
    // 注意：所有 ${segment.xxx} 变量引用均与原文保持一致
    return `<!--  (松弛感人味版，回归原始变量) -->
好的，我们来处理第 ${segment.segmentNumber} 个段落。别太紧张，抓住感觉。

【核心目标】
- 本段的中心任务： ${segment.purpose} 。请围绕这个核心。

【氛围参考】(随便看看，不用太纠结)
- 信息密度感：大概 ${segment.informationLevel}/10 
- 气氛紧张感：大概 ${segment.tensionLevel}/10
 --> 说明：这两个数字 ${segment.informationLevel} 和 ${segment.tensionLevel} 就是个【特别模糊】的参考，体会一下这段大概是 '料多还是少' (对应信息量)，'气氛紧还是松' (对应紧张感) 就行。它俩都不太准，关键是实现上面的【目的】 ${segment.purpose}，并且写出那个'劲儿'，感觉第一，别被数字带跑。

【写作注意点】
请留意以下几点，让文字更有现场感：

1. 【格式】:
  - 开头请确保是： ${segment.segmentNumber}.  (数字加点)，然后直接接正文。

2. 【风格：展示，而非讲述】 (Show, Don't Tell):
  - 建议避免：
      - "我觉得"、"我发现"、"我开始"、"我意识到"、"我当时就"
      - "打那以后"、"从那时候开始"、"后来我才知道"
      - "你猜怎么着"、"结果呢"、"没想到"、"这让我"
      - 任何回忆式、总结式、转述式的语言
  - 建议多用：
      - 直接进入场景，用动作、对话、环境开头
      - 对话要独立成行，用「」包围
      - 多写看到的、听到的、感受到的
      - 让读者感觉正在现场目睹

3. 【换行与节奏】:
   - 内容不要一大段到底，请在以下情况合理换行：
      - 对话前后
      - 心理转折
      - 场景切换
      - 情绪变化
   // -写成\n 作为换行符  

4. 【代词使用】:
  - 留意别过多重复 "我"、"他"、"她"。
  - 可多用具体的动作描写：手颤抖着、脚步声响起、门吱呀一声
  - 可多用环境细节：风声、光影、气味、温度
  - 多用直接对话，少用"我说"、"他说"
  - 可用名字、称呼替代代词：爹、村长、那个男人

5. 【篇幅】:
  - 大概 500 字左右，感觉和节奏流畅更重要，字数多点少点没关系，别卡太死。

6. 【重申目标】：
  - 最终要服务于上面的【目的】 ${segment.purpose} ，要么把事儿说明白、推着走，要么把坑挖深，要么就把气氛和情绪（参考数字代表的松紧/多少感觉）烘托出来。

【风格语气，排版，格式，形式参考】
可以参考下面这个小样板的感觉和排版：
“来，黏糊糊的。
腿肚子转筋，喉咙眼发紧，想喊，却一个音都发不出来。
那俩黑煤球眼珠子，死寂一片，可刚才那一下……那一下绝对不是错觉！
「你在那儿磨蹭什么！」
一声炸雷似的呵斥从屋门口传来。
猛地一哆嗦，扭头看过去。
娘叉着腰站在门槛里头，半个身子隐在门后的阴影里，脸绷得像块石头，眼睛死死盯着我，不，是盯着我伸出去还没完全收回来的手。
那眼神，不是平时的责备，带着一股子……惊慌？还有一种说不出来的狠厉。
「我……我没……」
话都说不利索了，舌头打了结。
「离那东西远点！」
她声音压得又低又急，每个字都像是从牙缝里挤出来的。
「听见没有！不准靠近它！一步都不行！」
她的视线像锥子一样扎过来，盯得我头皮发麻。
我从来没见过她这个样子。就算我小时候把家里唯一的暖水瓶打碎了，她也没这么……吓人。
那眼神里除了愤怒，好像还有一种深深的恐惧，像是生怕我碰了什么不该碰的东西，会引来天大的祸事。
她不是在骂我，更像是在……警告？或者说，阻止？
下意识地往后退了一步，脚踩进更深的泥水里，冰凉的泥浆一下子灌进鞋帮。
可这点凉意，远比不上她眼神里的寒气。
「回屋去！」”

好了，按感觉来吧！
<!--  结束 -->`;
}

  /**
   * 提取ACE技巧（简化版本）
   */
  private static extractACETechniques(aceFrameworkIds: string[]): {
    plotTechniques: string[];
    rhythmTechniques: string[];
    suspenseTechniques: string[];
  } {
    // 这里应该从localStorage获取具体的ACE框架数据并提取技巧
    // 为了简化，先返回一些基础技巧
    return {
      plotTechniques: ['情节反转技巧', '冲突升级设计', '角色动机揭示'],
      rhythmTechniques: ['节奏变化控制', '信息分层透露', '张弛有度安排'],
      suspenseTechniques: ['悬念设置技巧', '信息差制造', '读者期待管理']
    };
  }

  /**
   * 选择适用的技巧
   */
  private static selectApplicableTechniques(segment: SegmentStructure, aceFrameworkIds: string[]): string[] {
    const coreTechniques = this.extractACETechniques(aceFrameworkIds);
    const applicableTechniques: string[] = [];
    
    if (segment.purpose.includes('悬念') || segment.purpose.includes('疑问')) {
      applicableTechniques.push(...coreTechniques.suspenseTechniques.slice(0, 2));
    }
    
    if (segment.tensionLevel >= 7) {
      applicableTechniques.push(...coreTechniques.plotTechniques.slice(0, 2));
    }
    
    if (segment.purpose.includes('节奏') || segment.purpose.includes('推进')) {
      applicableTechniques.push(...coreTechniques.rhythmTechniques.slice(0, 2));
    }

    return applicableTechniques;
  }
}
