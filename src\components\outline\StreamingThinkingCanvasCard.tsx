"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface StreamingThinkingCanvasCardProps {
  content: string;
  isStreaming: boolean;
  title?: string;
  onEdit?: () => void;
  className?: string;
}

/**
 * 流式思考画布卡片组件
 * 专门用于显示流式生成的思考内容
 */
const StreamingThinkingCanvasCard: React.FC<StreamingThinkingCanvasCardProps> = ({
  content,
  isStreaming,
  title = "AI正在思考中...",
  onEdit,
  className = ''
}) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const contentRef = useRef<HTMLDivElement>(null);

  // 流式显示内容
  useEffect(() => {
    if (isStreaming) {
      setDisplayedContent(content);
      
      // 自动滚动到底部
      if (contentRef.current) {
        contentRef.current.scrollTop = contentRef.current.scrollHeight;
      }
    } else {
      setDisplayedContent(content);
    }
  }, [content, isStreaming]);

  // 获取字数统计
  const wordCount = displayedContent.length;

  return (
    <motion.div
      className={`streaming-thinking-canvas-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      layout
    >
      {/* 卡片头部 */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
        <div className="flex items-center gap-3 flex-1">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            {isStreaming ? (
              <motion.div
                className="w-4 h-4 bg-white rounded-full"
                animate={{ 
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.7, 1]
                }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            ) : (
              <span className="text-white text-sm">💭</span>
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {title}
            </h3>
            {isStreaming && (
              <div className="flex items-center gap-2 mt-1">
                <div className="flex gap-1">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      className="w-1.5 h-1.5 bg-blue-500 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">正在生成思考内容...</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* 编辑按钮 - 仅在非流式状态下显示 */}
          {!isStreaming && onEdit && (
            <button
              onClick={onEdit}
              className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
              title="在思考画布中编辑"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </button>
          )}
          
          {/* 流式状态指示器 */}
          {isStreaming && (
            <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
              <motion.div
                className="w-2 h-2 bg-blue-500 rounded-full"
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
              <span>流式输出中</span>
            </div>
          )}
        </div>
      </div>
      
      {/* 思考内容显示区域 */}
      <div className="relative">
        <div 
          ref={contentRef}
          className="p-4 max-h-96 overflow-y-auto"
        >
          {displayedContent ? (
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {displayedContent}
                {isStreaming && (
                  <motion.span
                    className="inline-block w-2 h-5 bg-blue-500 ml-1"
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-8 text-gray-500">
              <div className="text-center">
                <motion.div
                  className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <p>等待AI开始思考...</p>
              </div>
            </div>
          )}
        </div>
        
        {/* 渐变遮罩 - 仅在内容溢出时显示 */}
        {displayedContent && (
          <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent pointer-events-none" />
        )}
      </div>
      
      {/* 卡片底部信息 */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <span>字数: {wordCount}</span>
            {isStreaming && (
              <span className="flex items-center gap-1">
                <motion.div
                  className="w-1.5 h-1.5 bg-green-500 rounded-full"
                  animate={{ scale: [1, 1.3, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
                实时生成中
              </span>
            )}
            {!isStreaming && displayedContent && (
              <span className="text-green-600">✓ 思考完成</span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
              AI思考
            </span>
            <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
              流式输出
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default StreamingThinkingCanvasCard;
