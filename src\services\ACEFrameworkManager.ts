/**
 * 扩展的ACE框架管理器
 * 支持简介关键词、简介框架、大纲框架的统一管理
 */

import {
  ExtendedACEFramework,
  ACEFrameworkCategory,
  PRESET_SYNOPSIS_KEYWORDS,
  PRESET_SYNOPSIS_FRAMEWORKS,
  filterFrameworksByCategory,
  validateFramework,
  ExtractedElement
} from '../types/ACEFrameworkTypes';

// 导入现有的预置数据
import { PRESET_KEYWORDS } from '../factories/ai/services/brainstorm/types/BrainstormTypes';

export class ACEFrameworkManager {
  /**
   * 获取所有ACE框架（按分类整合）
   */
  static getAllFrameworks(): ExtendedACEFramework[] {
    const allFrameworks: ExtendedACEFramework[] = [];

    // 1. 获取简介关键词框架
    const keywordFrameworks = this.getSynopsisKeywordFrameworks();
    allFrameworks.push(...keywordFrameworks);

    // 2. 获取简介框架
    const synopsisFrameworks = this.getSynopsisFrameworks();
    allFrameworks.push(...synopsisFrameworks);

    // 3. 获取大纲框架
    const outlineFrameworks = this.getOutlineFrameworks();
    allFrameworks.push(...outlineFrameworks);

    // 4. 获取拆解元素框架
    const extractedFrameworks = this.getExtractedElementsFrameworks();
    allFrameworks.push(...extractedFrameworks);

    return allFrameworks;
  }

  /**
   * 按分类获取框架
   */
  static getFrameworksByCategory(category: ACEFrameworkCategory): ExtendedACEFramework[] {
    const allFrameworks = this.getAllFrameworks();
    return filterFrameworksByCategory(allFrameworks, category);
  }

  /**
   * 获取简介关键词框架
   */
  static getSynopsisKeywordFrameworks(): ExtendedACEFramework[] {
    const frameworks: ExtendedACEFramework[] = [];

    // 添加预置的简介关键词框架
    frameworks.push(...PRESET_SYNOPSIS_KEYWORDS);

    // 从PRESET_KEYWORDS转换关键词数据
    const keywordGroups = this.groupKeywordsByCategory();
    Object.entries(keywordGroups).forEach(([groupName, keywords]) => {
      frameworks.push({
        id: `keywords-${groupName.toLowerCase().replace(/\s+/g, '-')}`,
        name: `${groupName}市场热门关键词`,
        category: 'synopsis-keywords',
        description: `${groupName}类型的读者偏爱元素，提升作品市场竞争力`,
        effectiveness: Math.min(10, 5 + keywords.length),
        examples: keywords.slice(0, 3).map(k => k.text),
        keywordElements: keywords
      });
    });
    // 1. 从localStorage获取经过市场验证的热门设定
    try {
      const savedKeywords = localStorage.getItem('book-title-keywords');
      if (savedKeywords) {
        const userKeywords = JSON.parse(savedKeywords);
        if (Array.isArray(userKeywords) && userKeywords.length > 0) {
          frameworks.push({
            id: 'keywords-user-saved-title',
            name: '市场验证的热门设定 (世界观/角色)',
            category: 'synopsis-keywords',
            description: '这不仅是设定，这是经过市场反复验证、读者用真金白银和点击率投票选出的“热门世界观公式”。它们之所以成为市场硬通货，是因为每一个概念都自带一套完整的、能快速激发读者强烈代入感和追读欲望的成熟叙事逻辑。比如‘系统流’，它就是当前市场上最热门的成长模板之一，能保证故事有持续的爽点和明确的升级路径。',
            effectiveness: 9, // 市场验证过的核心设定，效能极高
            examples: userKeywords.slice(0, 3).map((k: any) => k.text || k),
            keywordElements: userKeywords.map((k: any) => ({
              text: k.text || k,
              hotness: k.hotness || 5,
              tags: k.tags || ['市场硬通货', '爆款地基', '热门公式'],
              frequency: k.frequency || 0
            }))
          });
        }
      }
    } catch (error) {
      console.warn('加载热门设定失败，问题不大：', error);
    }

    // 2. 从localStorage获取经过市场验证的热门情节
    try {
      const synopsisKeywords = localStorage.getItem('synopsis-keywords');
      if (synopsisKeywords) {
        const userSynopsisKeywords = JSON.parse(synopsisKeywords);
        if (Array.isArray(userSynopsisKeywords) && userSynopsisKeywords.length > 0) {
          frameworks.push({
            id: 'keywords-user-saved-synopsis',
            name: '市场验证的热门情节 (冲突/爽点)',
            category: 'synopsis-keywords',
            description: '这些是经过市场验证，能够确保情节‘有戏’、读者‘爱看’的“爆款情节发动机”。它们之所以热门，是因为每一个都精准地命中了一个或多个读者百看不厌的戏剧冲突模型。这是在海量作品中被证明行之有效的、最高效的情感调动工具。比如‘退婚流’，它不是简单的事件，而是市场上公认的、能瞬间引爆读者情绪、保证故事前期核心爽度的王牌热门套路。',
            effectiveness: 9, // 直接制造冲突和爽点，效能极高
            examples: userSynopsisKeywords.slice(0, 3).map((k: any) => k.text || k),
            keywordElements: userSynopsisKeywords.map((k: any) => ({
              text: k.text || k,
              hotness: k.hotness || 5,
              tags: k.tags || ['爆款发动机', '爽点直供', '王牌套路'],
              frequency: k.frequency || 0
            }))
          });
        }
      }
    } catch (error) {
      console.warn('加载热门情节失败，先不管了：', error);
    }
    console.log('🔍 获取简介关键词框架:', frameworks.length, '个');
    return frameworks;
  }

  /**
   * 获取简介框架
   */
  static getSynopsisFrameworks(): ExtendedACEFramework[] {
    const frameworks: ExtendedACEFramework[] = [];

    // 添加预置的简介框架
    frameworks.push(...PRESET_SYNOPSIS_FRAMEWORKS);

    // 从localStorage获取简介框架
    try {
      const savedFrameworks = localStorage.getItem('synopsis-frameworks');
      if (savedFrameworks) {
        const userFrameworks = JSON.parse(savedFrameworks);
        userFrameworks.forEach((framework: any) => {
          console.log('🔍 处理简介框架:', {
            id: framework.id,
            name: framework.name,
            hasStructureAnalysis: !!framework.structureAnalysis,
            hasWritingTechniques: !!framework.writingTechniques,
            hasReusableTemplates: !!framework.reusableTemplates,
            structureAnalysis: framework.structureAnalysis
          });

          const convertedFramework: ExtendedACEFramework = {
            id: framework.id,
            name: framework.name,
            category: 'synopsis-framework',
            pattern: framework.pattern,
            description: framework.description || '',
            effectiveness: framework.effectiveness || 5,
            examples: framework.examples || [],
            synopsisStructure: {
              // 从writingTechniques提取不同类型的技巧，避免与structureAnalysis重复
              openingTechniques: framework.writingTechniques?.filter((t: any) =>
                t.category === 'layout').map((t: any) => t.name) || [],
              conflictPresentation: framework.writingTechniques?.filter((t: any) =>
                t.category === 'emphasis').map((t: any) => t.name) || [],
              characterIntroduction: framework.writingTechniques?.filter((t: any) =>
                t.category === 'coolpoint').map((t: any) => t.name) || [],
              worldBuilding: framework.writingTechniques?.filter((t: any) =>
                t.category === 'creativity').map((t: any) => t.name) || [],
              hookStrategies: framework.styleCharacteristics?.omissionAndEmphasis?.suspensePoints || []
            },
            // 保留原始数据以便在AI消息中使用
            structureAnalysis: framework.structureAnalysis,
            reusableTemplates: framework.reusableTemplates,
            writingTechniques: framework.writingTechniques,
            styleCharacteristics: framework.styleCharacteristics
          };
          frameworks.push(convertedFramework);
          console.log('✅ 转换简介框架:', convertedFramework.name);
        });
      }
    } catch (error) {
      console.warn('获取简介框架失败:', error);
    }

    return frameworks;
  }

  /**
   * 获取大纲框架（保持现有逻辑）
   */
  static getOutlineFrameworks(): ExtendedACEFramework[] {
    const frameworks: ExtendedACEFramework[] = [];

    // 1. 从 'outline-frameworks' 获取（主要的大纲框架存储位置）
    try {
      const outlineFrameworks = localStorage.getItem('outline-frameworks');
      console.log('🔍 读取outline-frameworks:', outlineFrameworks ? '有数据' : '无数据');

      if (outlineFrameworks) {
        const userFrameworks = JSON.parse(outlineFrameworks);
        console.log('🔍 解析outline-frameworks数据:', userFrameworks.length, '个框架');
        console.log('🔍 框架列表:', userFrameworks.map((f: any) => ({
          id: f.id,
          name: f.frameworkName || f.name,
          pattern: f.frameworkPattern || f.pattern
        })));

        userFrameworks.forEach((framework: any) => {
          const convertedFramework: ExtendedACEFramework = {
            id: framework.id || `framework_${Date.now()}`,
            name: framework.frameworkName || framework.name || '未命名框架',
            category: 'outline-framework',
            pattern: framework.frameworkPattern || framework.pattern || '',
            description: framework.plotAnalysis ?
              `故事结构：${framework.plotAnalysis.storyStructure || ''}
冲突设计：${framework.plotAnalysis.conflictDesign || ''}
节奏控制：${framework.plotAnalysis.rhythmControl || ''}`.trim() :
              (framework.description || framework.frameworkPattern || ''),
            effectiveness: framework.usageCount || framework.effectiveness || 0,
            examples: framework.examples || [],
            writingTechniques: framework.writingTechniques || [],
            styleCharacteristics: framework.styleCharacteristics || {},
            plotAnalysis: framework.plotAnalysis || {},
            // 保留原始数据结构以便兼容
            ...framework
          };
          frameworks.push(convertedFramework);
          console.log('✅ 转换框架:', convertedFramework.name);
        });
      }
    } catch (error) {
      console.warn('获取outline-frameworks失败:', error);
    }

    // 2. 从 'book-title-frameworks' 获取（书名框架，也可能包含大纲相关）
    try {
      const titleFrameworks = localStorage.getItem('book-title-frameworks');
      if (titleFrameworks) {
        const userFrameworks = JSON.parse(titleFrameworks);
        userFrameworks.forEach((framework: any) => {
          // 避免重复添加（检查ID是否已存在）
          if (!frameworks.find(f => f.id === framework.id)) {
            if (validateFramework(framework)) {
              frameworks.push({
                ...framework,
                category: 'outline-framework'
              });
            } else {
              // 转换旧格式的框架数据
              const convertedFramework: ExtendedACEFramework = {
                id: framework.id || `framework_${Date.now()}`,
                name: framework.name || framework.frameworkPattern || '未命名框架',
                category: 'outline-framework',
                pattern: framework.pattern || framework.frameworkPattern,
                description: framework.description || '',
                effectiveness: framework.effectiveness || 0,
                examples: framework.examples || [],
                writingTechniques: framework.writingTechniques || [],
                styleCharacteristics: framework.styleCharacteristics || {},
                plotAnalysis: framework.plotAnalysis || {}
              };
              frameworks.push(convertedFramework);
            }
          }
        });
      }
    } catch (error) {
      console.warn('获取book-title-frameworks失败:', error);
    }

    console.log('🔍 ACEFrameworkManager获取大纲框架:', frameworks.length, '个');
    console.log('框架列表:', frameworks.map(f => ({ id: f.id, name: f.name })));

    return frameworks.sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0));
  }

  /**
   * 按类别分组关键词
   */
  private static groupKeywordsByCategory(): Record<string, Array<{text: string, hotness: number, tags: string[]}>> {
    const groups: Record<string, Array<{text: string, hotness: number, tags: string[]}>> = {};

    PRESET_KEYWORDS.forEach(keyword => {
      const primaryTag = keyword.tags?.[0] || '其他';
      if (!groups[primaryTag]) {
        groups[primaryTag] = [];
      }
      groups[primaryTag].push({
        text: keyword.text,
        hotness: keyword.hotness,
        tags: keyword.tags || []
      });
    });

    return groups;
  }

  /**
   * 获取分类统计信息
   */
  static getCategoryStats(): Record<ACEFrameworkCategory, number> {
    const stats = {
      'synopsis-keywords': this.getSynopsisKeywordFrameworks().length,
      'synopsis-framework': this.getSynopsisFrameworks().length,
      'outline-framework': this.getOutlineFrameworks().length,
      'extracted-elements': this.getExtractedElementsFrameworks().length
    };

    console.log('🔍 ACEFrameworkManager分类统计:', stats);
    return stats;
  }

  /**
   * 搜索框架
   */
  static searchFrameworks(query: string, category?: ACEFrameworkCategory): ExtendedACEFramework[] {
    let frameworks = category ? this.getFrameworksByCategory(category) : this.getAllFrameworks();

    if (!query.trim()) {
      return frameworks;
    }

    const searchTerm = query.toLowerCase();
    return frameworks.filter(framework =>
      framework.name.toLowerCase().includes(searchTerm) ||
      framework.description.toLowerCase().includes(searchTerm) ||
      framework.examples.some(example => example.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * 获取分类显示名称
   */
  static getCategoryDisplayName(category: string): string {
    const categoryMap: Record<string, string> = {
      'synopsis-keywords': '市场关键词',
      'synopsis-framework': '核心剧情框架',
      'outline-framework': '大纲框架',
      'extracted-elements': '拆解元素',
      'unknown': '未知类型'
    };
    return categoryMap[category] || category;
  }

  /**
   * 构建ACE框架学习消息
   */
  static buildACELearningMessages(frameworkIds: string[], storyMode: string): Array<{ role: string; content: string }> {
    if (!frameworkIds || frameworkIds.length === 0) {
      return [];
    }

    try {
      // 获取所有框架数据
      const allFrameworks = ACEFrameworkManager.getAllFrameworks();

      console.log('🔍 ACEFrameworkManager.buildACELearningMessages 获取到的框架数量:', allFrameworks.length);
      console.log('🔍 框架分类统计:', {
        'synopsis-keywords': allFrameworks.filter((f: any) => f.category === 'synopsis-keywords').length,
        'synopsis-framework': allFrameworks.filter((f: any) => f.category === 'synopsis-framework').length,
        'outline-framework': allFrameworks.filter((f: any) => f.category === 'outline-framework').length
      });

      console.log('🔍 传入的frameworkIds:', frameworkIds);
      console.log('🔍 所有框架的ID列表:', allFrameworks.map((f: any) => ({ id: f.id, name: f.name, category: f.category })));

      const selectedFrameworks = allFrameworks.filter((framework: any) => {
        const isSelected = frameworkIds.includes(framework.id);
        console.log(`🔍 框架 ${framework.id} (${framework.name}) 是否被选中:`, isSelected);
        return isSelected;
      });

      console.log('🔍 选中的框架数量:', selectedFrameworks.length);
      console.log('🔍 选中的框架详情:', selectedFrameworks.map((f: any) => ({ id: f.id, name: f.name, category: f.category })));

      if (selectedFrameworks.length === 0) {
        return [];
      }

      const messages: Array<{ role: string; content: string }> = [];

      // 添加ACE框架学习引导
      messages.push({
        role: 'system',
        content: `
        至高指导员 005：
        【ACE框架深度学习模式】
  你将学习${selectedFrameworks.length}个专业的ACE创作框架，这些框架包含了成功短篇小说的核心技巧。请深度理解每个框架的独特特征，并在创作时灵活运用。

  学习重点：
  1. 情节设计技巧 - 如何设计引人入胜的剧情点
  2. 节奏控制方法 - 如何掌握叙述的快慢节奏
  3. 悬念营造技巧 - 如何制造和维持读者的好奇心
  4. 人物塑造方法 - 如何在短篇中快速建立鲜明角色

  当前创作模式：${storyMode}，请特别关注与此模式相关的技巧。`
      });
      
      // 为每个框架创建专门的学习消息
      selectedFrameworks.forEach((framework: any, index: number) => {
        console.log(`🔍 处理框架 ${index + 1}:`, {
          id: framework.id,
          name: framework.name,
          category: framework.category,
          hasKeywordElements: !!framework.keywordElements,
          hasSynopsisStructure: !!framework.synopsisStructure,
          hasPlotAnalysis: !!framework.plotAnalysis
        });

        let frameworkContent = `=== ACE框架 ${index + 1}: ${framework.name} ===\n`;
        frameworkContent += `分类：${ACEFrameworkManager.getCategoryDisplayName(framework.category)}\n`;
        frameworkContent += `效果评级：${framework.effectiveness}/10\n\n`;

        // 根据框架类型生成不同的学习内容
        if (framework.category === 'synopsis-keywords') {
          console.log('✅ 匹配到简介关键词框架');
        } else if (framework.category === 'synopsis-framework') {
          console.log('✅ 匹配到简介框架');
        } else {
          console.log('✅ 匹配到大纲框架或其他类型');
        }

        if (framework.category === 'synopsis-keywords') {
          // 关键词框架 - 偏向市场价值和读者吸引力
          frameworkContent += `🔥 市场热门关键词索引：\n`;
          if (framework.keywordElements) {
            framework.keywordElements.forEach((keyword: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${keyword.text} (热度:${keyword.hotness}) [${keyword.tags.join('、')}]\n`;
            });
          }
          frameworkContent += `\n💡 市场价值应用指导：\n`;
          frameworkContent += `- 这些关键词代表了市场热门元素和读者偏好趋势\n`;
          frameworkContent += `- 在构思核心剧情时，融入高热度关键词提升商业价值\n`;
          frameworkContent += `- 将市场热门元素巧妙植入故事，增强作品竞争力\n`;
          frameworkContent += `- 根据关键词热度和标签，选择最具市场潜力的元素\n\n`;

        } else if (framework.category === 'synopsis-framework') {
          // 核心剧情框架 - 偏向核心剧情构建
          frameworkContent += `📖 核心剧情结构框架：\n`;

          // 显示structureAnalysis数据（如果存在）
          if (framework.structureAnalysis) {
            frameworkContent += `开场结构：${framework.structureAnalysis.opening}\n`;
            frameworkContent += `发展结构：${framework.structureAnalysis.development}\n`;
            frameworkContent += `结尾结构：${framework.structureAnalysis.ending}\n`;
          }

          // 显示synopsisStructure数据（写作技巧分类）- 避免重复
          if (framework.synopsisStructure) {
            if (framework.synopsisStructure.openingTechniques && framework.synopsisStructure.openingTechniques.length > 0) {
              frameworkContent += `开场技巧：${framework.synopsisStructure.openingTechniques.join('、')}\n`;
            }
            if (framework.synopsisStructure.hookStrategies && framework.synopsisStructure.hookStrategies.length > 0) {
              frameworkContent += `钩子策略：${framework.synopsisStructure.hookStrategies.join('、')}\n`;
            }
            if (framework.synopsisStructure.characterIntroduction && framework.synopsisStructure.characterIntroduction.length > 0) {
              frameworkContent += `角色引入：${framework.synopsisStructure.characterIntroduction.join('、')}\n`;
            }
            if (framework.synopsisStructure.conflictPresentation && framework.synopsisStructure.conflictPresentation.length > 0) {
              frameworkContent += `冲突呈现：${framework.synopsisStructure.conflictPresentation.join('、')}\n`;
            }
            if (framework.synopsisStructure.worldBuilding && framework.synopsisStructure.worldBuilding.length > 0) {
              frameworkContent += `世界构建：${framework.synopsisStructure.worldBuilding.join('、')}\n`;
            }
          }

          // 添加可复用模板信息
          if (framework.reusableTemplates && framework.reusableTemplates.length > 0) {
            frameworkContent += `\n📋 可复用模板：\n`;
            framework.reusableTemplates.slice(0, 3).forEach((template: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${template.name}：${template.pattern}\n`;
              frameworkContent += `   描述：${template.description}\n`;
            });
          }

          // 添加写作技巧信息
          if (framework.writingTechniques && framework.writingTechniques.length > 0) {
            frameworkContent += `\n✍️ 写作技巧：\n`;
            framework.writingTechniques.slice(0, 5).forEach((technique: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${technique.name}（${technique.category}）：${technique.description}\n`;
            });
          }

          frameworkContent += `\n🎯 核心剧情构建指导：\n`;
          frameworkContent += `- 这个框架提供了核心剧情的专业构建模板\n`;
          frameworkContent += `- 在设计故事主线时，运用其核心结构和技巧策略\n`;
          frameworkContent += `- 注重剧情冲突的层次递进和情感张力营造\n`;
          frameworkContent += `- 确保核心剧情具有强烈的戏剧性和市场吸引力\n\n`;

        } else {
          // 大纲框架 - 显示完整的框架信息
          if (framework.pattern) {
            frameworkContent += `📋 核心模式：\n${framework.pattern}\n\n`;
          }

          if (framework.description) {
            frameworkContent += `📝 框架描述：\n${framework.description}\n\n`;
          }

          // 剧情分析信息
          if (framework.plotAnalysis) {
            const plotAnalysis = framework.plotAnalysis;
            if (plotAnalysis.storyStructure) {
              frameworkContent += `🏗️ 故事结构：\n${plotAnalysis.storyStructure}\n\n`;
            }
            if (plotAnalysis.conflictDesign) {
              frameworkContent += `⚔️ 冲突设计：\n${plotAnalysis.conflictDesign}\n\n`;
            }
            if (plotAnalysis.rhythmControl) {
              frameworkContent += `🎵 节奏控制：\n${plotAnalysis.rhythmControl}\n\n`;
            }

            // 核心剧情点
            if (plotAnalysis.plotPoints && plotAnalysis.plotPoints.length > 0) {
              frameworkContent += `🎯 核心剧情点：\n`;
              plotAnalysis.plotPoints.forEach((point: any, idx: number) => {
                frameworkContent += `${idx + 1}. ${point}\n`;
              });
              frameworkContent += `\n`;
            }

            // 详细剧情指导
            if (plotAnalysis.plotPointsWithGuidance && plotAnalysis.plotPointsWithGuidance.length > 0) {
              frameworkContent += `🎭 剧情设计技巧：\n`;
              plotAnalysis.plotPointsWithGuidance.forEach((point: any, idx: number) => {
                frameworkContent += `${idx + 1}. ${point.content}\n`;
                frameworkContent += `   具体描写：${point.specificDescription}\n`;
                frameworkContent += `   避免事项：${point.avoidanceGuidance}\n\n`;
              });
            }
          }

          // 写作技巧信息
          if (framework.writingTechniques && framework.writingTechniques.length > 0) {
            frameworkContent += `✍️ 写作技巧：\n`;
            framework.writingTechniques.forEach((technique: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${technique.name}（${technique.category}）：${technique.description}\n`;
              if (technique.examples && technique.examples.length > 0) {
                frameworkContent += `   示例：${technique.examples.join('、')}\n`;
              }
            });
            frameworkContent += `\n`;
          }

          // 风格特征信息
          if (framework.styleCharacteristics) {
            frameworkContent += `🎨 风格特征：\n`;
            const style = framework.styleCharacteristics;

            if (style.languageStyle && style.languageStyle.length > 0) {
              frameworkContent += `语言风格：${style.languageStyle.join('、')}\n`;
            }
            if (style.narrativeStyle && style.narrativeStyle.length > 0) {
              frameworkContent += `叙述风格：${style.narrativeStyle.join('、')}\n`;
            }
            if (style.layoutTechniques) {
              if (style.layoutTechniques.rhythmControl && style.layoutTechniques.rhythmControl.length > 0) {
                frameworkContent += `节奏技巧：${style.layoutTechniques.rhythmControl.join('、')}\n`;
              }
              if (style.layoutTechniques.structureDesign && style.layoutTechniques.structureDesign.length > 0) {
                frameworkContent += `结构设计：${style.layoutTechniques.structureDesign.join('、')}\n`;
              }
            }
            if (style.omissionAndEmphasis) {
              if (style.omissionAndEmphasis.suspensePoints && style.omissionAndEmphasis.suspensePoints.length > 0) {
                frameworkContent += `悬念点：${style.omissionAndEmphasis.suspensePoints.join('、')}\n`;
              }
              if (style.omissionAndEmphasis.emphasisTechniques && style.omissionAndEmphasis.emphasisTechniques.length > 0) {
                frameworkContent += `强调技巧：${style.omissionAndEmphasis.emphasisTechniques.join('、')}\n`;
              }
            }
            frameworkContent += `\n`;
          }
        }

        messages.push({
          role: 'user',
          content: frameworkContent
        });

        // AI确认学习 - 根据框架类型定制回复
        let confirmationContent = `好的，005指导员我已学习，我会在接下来引导"小懒"进行技巧等应用\n`;
        confirmationContent += `我已深度学习"${framework.name}"${ACEFrameworkManager.getCategoryDisplayName(framework.category)}框架。`;

        if (framework.category === 'synopsis-keywords') {
          confirmationContent += `我理解了：
1. 这些关键词代表的市场热门趋势和商业价值点
2. 如何从高热度关键词中提取市场化创作元素
3. 将热门关键词融入核心剧情的专业方法
4. 根据市场热度选择最具竞争力元素的策略

我将在构思核心剧情时，运用这些市场热门关键词，确保故事具有强大的商业吸引力。`;
        } else if (framework.category === 'synopsis-framework') {
          confirmationContent += `我理解了：
1. 这个框架的核心剧情构建专业模板
2. 核心冲突和戏剧张力的营造技巧
3. 剧情主线的层次递进和情感爆发点设计
4. 如何构建具有市场竞争力的核心剧情结构

我将在设计核心剧情时，运用这个专业框架，确保故事具有强烈的戏剧性和商业价值。`;
        } else {
          confirmationContent += `我理解了：
1. 该框架的独特剧情设计方法
2. 节奏控制和悬念营造的具体技巧
3. 如何在短篇创作中应用这些技巧
4. 与${storyMode}模式的结合要点

我将在创作时灵活运用这些专业技巧，确保生成的内容具有该框架的精髓。`;
        }

        messages.push({
          role: 'assistant',
          content: confirmationContent
        });
      });

      // 综合学习总结
      const coreTechniques = ACEFrameworkManager.extractCoreTechniques(selectedFrameworks);
      messages.push({
        role: 'assistant',
        content: `
       好的， 005领导员我已学习，我会在接下来引导 "小懒"进行技巧等应用
        【ACE框架学习完成】
  我已完成${selectedFrameworks.length}个ACE框架的深度学习，掌握了以下核心技巧：

  🎭 剧情技巧 (${coreTechniques.plotTechniques.length}个)：
  ${coreTechniques.plotTechniques.slice(0, 3).join('、')}${coreTechniques.plotTechniques.length > 3 ? '等' : ''}

  🎵 节奏技巧 (${coreTechniques.rhythmTechniques.length}个)：
  ${coreTechniques.rhythmTechniques.slice(0, 3).join('、')}${coreTechniques.rhythmTechniques.length > 3 ? '等' : ''}

  🔍 悬念技巧 (${coreTechniques.suspenseTechniques.length}个)：
  ${coreTechniques.suspenseTechniques.slice(0, 3).join('、')}${coreTechniques.suspenseTechniques.length > 3 ? '等' : ''}

  我将在短篇创作的三个阶段中系统性地应用这些技巧，确保作品质量。`
      });

      return messages;
    } catch (error) {
      console.error('构建ACE框架学习消息失败:', error);
      return [];
    }
  }

  /**
   * 提取框架的核心技巧
   */
  static extractCoreTechniques(frameworks: any[]): {
    plotTechniques: string[];
    rhythmTechniques: string[];
    suspenseTechniques: string[];
  } {
    const plotTechniques: string[] = [];
    const rhythmTechniques: string[] = [];
    const suspenseTechniques: string[] = [];

    frameworks.forEach(framework => {
      // 根据框架类型提取不同的技巧
      if (framework.category === 'synopsis-keywords' && framework.keywordElements) {
        framework.keywordElements.forEach((keyword: any) => {
          if (keyword.tags.includes('开局流')) {
            plotTechniques.push(`${keyword.text}技巧`);
          }
          if (keyword.tags.includes('情绪')) {
            suspenseTechniques.push(`${keyword.text}营造`);
          }
          if (keyword.tags.includes('时间')) {
            plotTechniques.push(`${keyword.text}设定`);
          }
        });
      } else if (framework.category === 'synopsis-framework' && framework.synopsisStructure) {
        if (framework.synopsisStructure.hookStrategies) {
          suspenseTechniques.push(...framework.synopsisStructure.hookStrategies);
        }
        if (framework.synopsisStructure.openingTechniques) {
          plotTechniques.push(...framework.synopsisStructure.openingTechniques);
        }
        if (framework.synopsisStructure.characterIntroduction) {
          plotTechniques.push(...framework.synopsisStructure.characterIntroduction);
        }
      } else {
        // 大纲框架或旧格式框架
        if (framework.plotAnalysis?.plotPointsWithGuidance) {
          framework.plotAnalysis.plotPointsWithGuidance.forEach((point: any) => {
            plotTechniques.push(point.content);
          });
        }

        if (framework.styleCharacteristics) {
          const style = framework.styleCharacteristics;

          if (style.layoutTechniques?.rhythmControl) {
            rhythmTechniques.push(...style.layoutTechniques.rhythmControl);
          }

          if (style.omissionAndEmphasis?.suspensePoints) {
            suspenseTechniques.push(...style.omissionAndEmphasis.suspensePoints);
          }
        }
      }
    });

    return {
      plotTechniques: [...new Set(plotTechniques)], // 去重
      rhythmTechniques: [...new Set(rhythmTechniques)],
      suspenseTechniques: [...new Set(suspenseTechniques)]
    };
  }

  // ==================== 拆解元素管理功能 ====================

  /**
   * 获取拆解元素框架
   */
  static getExtractedElementsFrameworks(): ExtendedACEFramework[] {
    const frameworks: ExtendedACEFramework[] = [];

    try {
      const savedElements = localStorage.getItem('ace-extracted-elements');
      if (savedElements) {
        const extractedElements: ExtractedElement[] = JSON.parse(savedElements);

        // 按类别分组拆解元素
        const groupedElements = this.groupExtractedElementsByCategory(extractedElements);

        // 为每个类别创建一个框架
        Object.entries(groupedElements).forEach(([category, elements]) => {
          frameworks.push({
            id: `extracted-${category.toLowerCase().replace(/\s+/g, '-')}`,
            name: `拆解元素 - ${category}`,
            category: 'extracted-elements',
            description: `AI智能拆解的${category}类元素，包含${elements.length}个可复用的创作元素`,
            effectiveness: Math.min(10, 3 + elements.length),
            examples: elements.slice(0, 3).map(e => e.elements[0]),
            extractedElements: elements.map(e => ({
              text: e.elements[0],
              category: e.category,
              confidence: e.confidence || 0.8,
              tags: e.tags || [],
              sourceText: e.sourceText || ''
            })),
            extractedAt: new Date(),
            extractionMethod: 'ai-extracted'
          });
        });
      }
    } catch (error) {
      console.warn('获取拆解元素失败:', error);
    }

    console.log('🔍 获取拆解元素框架:', frameworks.length, '个');
    return frameworks;
  }

  /**
   * 保存拆解元素
   */
  static saveExtractedElements(elements: ExtractedElement[]): boolean {
    try {
      // 获取现有元素
      const existingElements = this.getExtractedElements();

      // 去重处理
      const deduplicatedElements = this.deduplicateExtractedElements(elements, existingElements);

      if (deduplicatedElements.length === 0) {
        console.log('🔍 没有新的拆解元素需要保存');
        return true;
      }

      // 合并元素
      const allElements = [...existingElements, ...deduplicatedElements];

      // 保存到localStorage
      localStorage.setItem('ace-extracted-elements', JSON.stringify(allElements));

      console.log('✅ 成功保存拆解元素:', deduplicatedElements.length, '个新元素');
      return true;
    } catch (error) {
      console.error('❌ 保存拆解元素失败:', error);
      return false;
    }
  }

  /**
   * 获取拆解元素
   */
  static getExtractedElements(): ExtractedElement[] {
    try {
      const savedElements = localStorage.getItem('ace-extracted-elements');
      if (savedElements) {
        return JSON.parse(savedElements);
      }
    } catch (error) {
      console.warn('获取拆解元素失败:', error);
    }
    return [];
  }

  /**
   * 按类别分组拆解元素
   */
  private static groupExtractedElementsByCategory(elements: ExtractedElement[]): Record<string, ExtractedElement[]> {
    const groups: Record<string, ExtractedElement[]> = {};

    elements.forEach(element => {
      const category = element.category || '未分类';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(element);
    });

    return groups;
  }

  /**
   * 拆解元素去重
   */
  private static deduplicateExtractedElements(newElements: ExtractedElement[], existingElements: ExtractedElement[]): ExtractedElement[] {
    const existingTexts = new Set(
      existingElements.flatMap(el => el.elements.map(e => e.toLowerCase()))
    );

    return newElements.filter(element => {
      const elementTexts = element.elements.map(e => e.toLowerCase());
      return !elementTexts.some(text => existingTexts.has(text));
    });
  }

  /**
   * 检测同义词
   */
  static detectSynonyms(element1: ExtractedElement, element2: ExtractedElement): boolean {
    const texts1 = element1.elements.map(e => e.toLowerCase());
    const texts2 = element2.elements.map(e => e.toLowerCase());

    // 检查是否有相同或包含关系的文本
    return texts1.some(text1 =>
      texts2.some(text2 =>
        text1 === text2 ||
        text1.includes(text2) ||
        text2.includes(text1) ||
        this.calculateSimilarity(text1, text2) > 0.8
      )
    );
  }

  /**
   * 计算文本相似度（简单实现）
   */
  private static calculateSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1;

    const longer = text1.length > text2.length ? text1 : text2;
    const shorter = text1.length > text2.length ? text2 : text1;

    if (longer.length === 0) return 1;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 删除拆解元素
   */
  static deleteExtractedElement(elementId: string): boolean {
    try {
      const elements = this.getExtractedElements();
      const filteredElements = elements.filter(el => el.id !== elementId);

      localStorage.setItem('ace-extracted-elements', JSON.stringify(filteredElements));

      console.log('✅ 成功删除拆解元素:', elementId);
      return true;
    } catch (error) {
      console.error('❌ 删除拆解元素失败:', error);
      return false;
    }
  }

  /**
   * 清空所有拆解元素
   */
  static clearAllExtractedElements(): boolean {
    try {
      localStorage.removeItem('ace-extracted-elements');
      console.log('✅ 成功清空所有拆解元素');
      return true;
    } catch (error) {
      console.error('❌ 清空拆解元素失败:', error);
      return false;
    }
  }
}
