"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { WorldBuildingFieldAIButton } from './WorldBuildingFieldAIButton';

/**
 * 创建通用的文本区域字段（编辑模式）
 */
export const createTextareaField = (
  id: string,
  label: string,
  placeholder: string,
  editingWorldBuilding: WorldBuilding,
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
) => (
  <div>
    <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
      {label}
      <div className="flex ml-auto">
        {/* AI生成按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="generate"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(event);

            // 在下一个渲染周期调整所有文本区域的高度
            setTimeout(() => {
              document.querySelectorAll('textarea').forEach((textarea) => {
                textarea.style.height = 'auto';
                textarea.style.height = `${textarea.scrollHeight}px`;
              });
            }, 0);
          }}
        />

        {/* AI更新按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="update"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(event);

            // 在下一个渲染周期调整所有文本区域的高度
            setTimeout(() => {
              document.querySelectorAll('textarea').forEach((textarea) => {
                textarea.style.height = 'auto';
                textarea.style.height = `${textarea.scrollHeight}px`;
              });
            }, 0);
          }}
        />
      </div>
    </label>
    <textarea
      id={id}
      name={`attributes.${id}`}
      value={editingWorldBuilding.attributes?.[id] || ''}
      onChange={(e) => {
        handleInputChange(e);
        // 自动调整高度
        e.target.style.height = 'auto';
        e.target.style.height = `${e.target.scrollHeight}px`;
      }}
      rows={5}
      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 min-h-[120px] resize-none overflow-hidden"
      placeholder={placeholder}
      ref={(el) => {
        // 初始化时调整高度
        if (el) {
          setTimeout(() => {
            el.style.height = 'auto';
            el.style.height = `${el.scrollHeight}px`;
          }, 0);
        }
      }}
    />
  </div>
);

/**
 * 创建通用的输入字段（编辑模式）
 */
export const createInputField = (
  id: string,
  label: string,
  placeholder: string,
  editingWorldBuilding: WorldBuilding,
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
) => (
  <div>
    <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
      {label}
      <div className="flex ml-auto">
        {/* AI生成按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="generate"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLInputElement>;
            handleInputChange(event);
          }}
        />

        {/* AI更新按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="update"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLInputElement>;
            handleInputChange(event);
          }}
        />
      </div>
    </label>
    <input
      type="text"
      id={id}
      name={`attributes.${id}`}
      value={editingWorldBuilding.attributes?.[id] || ''}
      onChange={handleInputChange}
      className="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 min-h-[42px]"
      placeholder={placeholder}
      style={{ lineHeight: '1.5' }}
    />
  </div>
);

/**
 * 创建通用的字段显示组件（查看模式）
 */
export const createFieldView = (id: string, label: string, worldBuilding: WorldBuilding) => {
  const value = worldBuilding.attributes?.[id];
  if (!value) return null;

  return (
    <div>
      <h4 className="text-sm font-medium text-gray-500">{label}</h4>
      <p className="mt-1 text-gray-700 whitespace-pre-wrap">{value}</p>
    </div>
  );
};

/**
 * 获取类别选项 - 树形结构
 */
export const getCategoryOptions = () => {
  // 定义主类和子类的树形结构
  const categoryTree = [
    {
      id: 'physical',
      label: '物理世界',
      children: [
        { id: 'geography', label: '地理 - 地点、地形、区域' },
        { id: 'natural_phenomena', label: '自然现象 - 气候、灾害、季节' },
        { id: 'architecture', label: '建筑 - 建筑物、结构、地标' }
      ]
    },
    {
      id: 'social',
      label: '社会结构',
      children: [
        { id: 'politics', label: '政治 - 制度、派系、权力结构' },
        { id: 'economy', label: '经济 - 货币、贸易、资源' },
        { id: 'organization', label: '组织 - 团体、机构、势力' }
      ]
    },
    {
      id: 'cultural',
      label: '文化体系',
      children: [
        { id: 'culture', label: '文化 - 习俗、传统、艺术' },
        { id: 'religion', label: '宗教 - 信仰、神灵、仪式' },
        { id: 'language', label: '语言 - 方言、文字、交流' },
        { id: 'art', label: '艺术 - 音乐、绘画、文学' }
      ]
    },
    {
      id: 'beings',
      label: '生命体系',
      children: [
        { id: 'race', label: '种族 - 人种、生物、特征' }
      ]
    },
    {
      id: 'knowledge',
      label: '知识体系',
      children: [
        { id: 'magic', label: '魔法系统 - 法则、能力、限制' },
        { id: 'technology', label: '科技 - 发明、技术、进步' }
      ]
    },
    {
      id: 'temporal',
      label: '时间体系',
      children: [
        { id: 'history', label: '历史 - 事件、时间线、纪元' }
      ]
    },
    {
      id: 'artifacts',
      label: '物品体系',
      children: [
        { id: 'item', label: '物品 - 神器、工具、装备' }
      ]
    },
    {
      id: 'other',
      label: '其他',
      children: [
        { id: 'other', label: '其他 - 未分类元素' }
      ]
    }
  ];

  // 将树形结构转换为扁平的选项列表，用于下拉菜单
  const flatOptions = [{ value: '', label: '选择类别' }];

  categoryTree.forEach(mainCategory => {
    // 添加主类作为分组标题
    flatOptions.push({ value: `group_${mainCategory.id}`, label: mainCategory.label, isGroupTitle: true });

    // 添加子类，并在值中包含主类信息
    mainCategory.children.forEach(subCategory => {
      flatOptions.push({
        value: subCategory.id,
        label: subCategory.label,
        mainCategory: mainCategory.id,
        mainCategoryLabel: mainCategory.label
      });
    });
  });

  return flatOptions;
};

/**
 * 获取类别的主类信息
 */
export const getCategoryMainInfo = (categoryId: string) => {
  const options = getCategoryOptions();
  const category = options.find(option => option.value === categoryId);

  if (category && category.mainCategory) {
    return {
      mainCategoryId: category.mainCategory,
      mainCategoryLabel: category.mainCategoryLabel
    };
  }

  return null;
};

/**
 * 获取类别ID对应的中文名称
 */
export const getCategoryLabel = (categoryId: string) => {
  const options = getCategoryOptions();
  const category = options.find(option => option.value === categoryId);

  if (category) {
    // 从标签中提取中文名称（例如："地理 - 地点、地形、区域" -> "地理"）
    const label = category.label;
    const chineseName = label.split(' - ')[0];
    return chineseName;
  }

  return categoryId; // 如果找不到对应的标签，则返回原始ID
};

/**
 * 获取重要性选项
 */
export const getImportanceOptions = () => [
  { value: '', label: '选择重要性' },
  { value: '1', label: '⭐ 次要元素 - 背景补充' },
  { value: '2', label: '⭐⭐ 支持元素 - 丰富世界观' },
  { value: '3', label: '⭐⭐⭐ 重要元素 - 影响情节' },
  { value: '4', label: '⭐⭐⭐⭐ 核心元素 - 关键设定' },
  { value: '5', label: '⭐⭐⭐⭐⭐ 关键元素 - 世界基石' }
];

/**
 * 获取时间跨度选项
 */
export const getTimeSpanOptions = () => [
  { value: '', label: '选择时间跨度' },
  { value: '瞬时', label: '瞬时 - 特定时刻' },
  { value: '短期', label: '短期 - 数天或数周' },
  { value: '中期', label: '中期 - 数月至数年' },
  { value: '长期', label: '长期 - 数十年' },
  { value: '历史性', label: '历史性 - 数百年' },
  { value: '史诗级', label: '史诗级 - 数千年或更长' }
];

/**
 * 获取影响范围选项
 */
export const getScopeOptions = () => [
  { value: '', label: '选择影响范围' },
  { value: '个人', label: '个人 - 影响单个角色' },
  { value: '群体', label: '群体 - 影响特定群体' },
  { value: '区域', label: '区域 - 影响特定区域' },
  { value: '国家', label: '国家 - 影响整个国家' },
  { value: '世界', label: '世界 - 影响整个世界' },
  { value: '宇宙', label: '宇宙 - 影响宇宙规则' }
];

/**
 * 创建短文本区域字段（编辑模式）- 用于需要多行但不需要太高的字段
 */
export const createShortTextareaField = (
  id: string,
  label: string,
  placeholder: string,
  editingWorldBuilding: WorldBuilding,
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
) => (
  <div>
    <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
      {label}
      <div className="flex ml-auto">
        {/* AI生成按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="generate"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(event);

            // 在下一个渲染周期调整所有文本区域的高度
            setTimeout(() => {
              document.querySelectorAll('textarea').forEach((textarea) => {
                textarea.style.height = 'auto';
                textarea.style.height = `${textarea.scrollHeight}px`;
              });
            }, 0);
          }}
        />

        {/* AI更新按钮 */}
        <WorldBuildingFieldAIButton
          worldBuilding={editingWorldBuilding}
          fieldName={`attributes.${id}`}
          fieldDisplayName={label}
          bookId={editingWorldBuilding.bookId}
          mode="update"
          onSave={(updatedWorldBuilding) => {
            // 更新表单数据
            const event = {
              target: {
                name: `attributes.${id}`,
                value: updatedWorldBuilding.attributes?.[id] || ''
              }
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(event);

            // 在下一个渲染周期调整所有文本区域的高度
            setTimeout(() => {
              document.querySelectorAll('textarea').forEach((textarea) => {
                textarea.style.height = 'auto';
                textarea.style.height = `${textarea.scrollHeight}px`;
              });
            }, 0);
          }}
        />
      </div>
    </label>
    <textarea
      id={id}
      name={`attributes.${id}`}
      value={editingWorldBuilding.attributes?.[id] || ''}
      onChange={(e) => {
        handleInputChange(e);
        // 自动调整高度
        e.target.style.height = 'auto';
        e.target.style.height = `${e.target.scrollHeight}px`;
      }}
      rows={2}
      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 min-h-[60px] resize-none overflow-hidden"
      placeholder={placeholder}
      ref={(el) => {
        // 初始化时调整高度
        if (el) {
          setTimeout(() => {
            el.style.height = 'auto';
            el.style.height = `${el.scrollHeight}px`;
          }, 0);
        }
      }}
    />
  </div>
);
