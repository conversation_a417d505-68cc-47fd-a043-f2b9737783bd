"use client";

import React, { useState, useEffect } from 'react';
import { userSettingsService } from '../../../services/storage/DataService';

interface ChatHistorySettings {
  messageLimit: number; // 5-50，默认20
  showMessageCount: boolean; // 是否显示消息计数
}

interface ChatHistorySettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSettingsChange?: (settings: ChatHistorySettings) => void;
}

/**
 * 历史消息设置对话框组件
 * 允许用户自定义历史消息显示条数和相关设置
 */
export const ChatHistorySettingsDialog: React.FC<ChatHistorySettingsDialogProps> = ({
  isOpen,
  onClose,
  onSettingsChange
}) => {
  const [settings, setSettings] = useState<ChatHistorySettings>({
    messageLimit: 20,
    showMessageCount: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 加载设置
  useEffect(() => {
    if (isOpen) {
      loadSettings();
    }
  }, [isOpen]);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const savedSettings = await userSettingsService.get<ChatHistorySettings>(
        'chat',
        'history',
        { messageLimit: 20, showMessageCount: true }
      );

      console.log('🔍 加载历史消息设置调试信息:');
      console.log('- 加载的设置:', savedSettings);
      console.log('- messageLimit值:', savedSettings.messageLimit);
      console.log('- messageLimit类型:', typeof savedSettings.messageLimit);

      setSettings(savedSettings);
    } catch (error) {
      console.error('加载历史消息设置失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      console.log('🔍 保存历史消息设置调试信息:');
      console.log('- 要保存的设置:', settings);
      console.log('- messageLimit值:', settings.messageLimit);
      console.log('- messageLimit类型:', typeof settings.messageLimit);

      await userSettingsService.set('chat', 'history', settings);

      // 验证保存是否成功
      const savedSettings = await userSettingsService.get('chat', 'history', { messageLimit: 20 });
      console.log('- 保存后读取的设置:', savedSettings);
      console.log('- 保存后的messageLimit:', savedSettings.messageLimit);

      onSettingsChange?.(settings);
      onClose();
    } catch (error) {
      console.error('保存历史消息设置失败:', error);
      alert('保存设置失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    if (confirm('确定要重置为默认设置吗？')) {
      const defaultSettings: ChatHistorySettings = {
        messageLimit: 20,
        showMessageCount: true
      };
      setSettings(defaultSettings);
      try {
        await userSettingsService.set('chat', 'history', defaultSettings);
        onSettingsChange?.(defaultSettings);
      } catch (error) {
        console.error('重置设置失败:', error);
        alert('重置设置失败，请重试');
      }
    }
  };

  const handleMessageLimitChange = (value: number) => {
    setSettings(prev => ({ ...prev, messageLimit: value }));
  };

  const handleShowMessageCountChange = (checked: boolean) => {
    setSettings(prev => ({ ...prev, showMessageCount: checked }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-30 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-[480px] max-w-full overflow-hidden">
        {/* 标题栏 */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-xl font-medium text-gray-800 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            历史消息设置
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">加载设置中...</span>
            </div>
          ) : (
            <>
              {/* 历史消息条数设置 */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  历史消息条数: {settings.messageLimit} 条
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="5"
                    max="100"
                    value={settings.messageLimit}
                    onChange={(e) => handleMessageLimitChange(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>5条</span>
                    <span>25条</span>
                    <span>100条</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  设置AI对话中显示的历史消息数量。更多历史消息可以提供更好的上下文，但会增加处理时间。
                </p>
              </div>

              {/* 显示消息计数设置 */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="showMessageCount"
                    checked={settings.showMessageCount}
                    onChange={(e) => handleShowMessageCountChange(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="showMessageCount" className="text-sm font-medium text-gray-700">
                    显示消息计数
                  </label>
                </div>
                <p className="text-xs text-gray-500 ml-7">
                  在消息气泡中显示消息编号，便于跟踪对话进度。
                </p>
              </div>

              {/* 预览信息 */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-blue-800 mb-2">当前设置预览</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <div>• 历史消息条数: {settings.messageLimit} 条</div>
                  <div>• 消息计数显示: {settings.showMessageCount ? '开启' : '关闭'}</div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-between items-center p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleReset}
            disabled={isLoading || isSaving}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
          >
            重置默认
          </button>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isSaving}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading || isSaving}
              className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSaving && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              {isSaving ? '保存中...' : '保存设置'}
            </button>
          </div>
        </div>
      </div>

      {/* 样式 */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  );
};

export default ChatHistorySettingsDialog;
