import { useState, useEffect, useCallback } from 'react';
import { Terminology } from '@/lib/db/dexie';

/**
 * 术语数据钩子
 * 用于管理术语数据的状态和操作
 */
export const useTerminologyData = (bookId: string, isOpen: boolean) => {
  // 术语列表
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  // 过滤后的术语列表
  const [filteredTerminologies, setFilteredTerminologies] = useState<Terminology[]>([]);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 搜索关键词
  const [searchQuery, setSearchQuery] = useState('');
  // 排序方式
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'importance'>('name');
  // 选中的术语
  const [selectedTerminology, setSelectedTerminology] = useState<Terminology | null>(null);
  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);

  // 加载术语数据
  const loadTerminologies = useCallback(async () => {
    if (!bookId) return;

    setIsLoading(true);
    try {
      // 导入术语仓库
      const { terminologyRepository } = await import('@/lib/db/repositories');

      // 获取术语列表
      const data = await terminologyRepository.getAllByBookId(bookId);

      // 更新状态
      setTerminologies(data);

      // 如果有选中的术语，更新选中的术语
      // 但不要在依赖项中包含selectedTerminology，避免循环依赖
      if (selectedTerminology) {
        const updated = data.find(t => t.id === selectedTerminology.id);
        if (updated && JSON.stringify(updated) !== JSON.stringify(selectedTerminology)) {
          setSelectedTerminology(updated);
        }
      }
    } catch (error) {
      console.error('加载术语数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [bookId]); // 移除selectedTerminology依赖，避免循环

  // 当面板打开或书籍ID变化时，加载术语数据
  useEffect(() => {
    if (isOpen) {
      loadTerminologies();
    }
  }, [isOpen, loadTerminologies]);

  // 根据搜索关键词和排序方式过滤和排序术语列表
  useEffect(() => {
    // 过滤
    let filtered = terminologies;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = terminologies.filter(terminology =>
        terminology.name.toLowerCase().includes(query) ||
        terminology.description.toLowerCase().includes(query) ||
        (terminology.category && terminology.category.toLowerCase().includes(query))
      );
    }

    // 排序
    filtered = [...filtered].sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else if (sortBy === 'category') {
        return (a.category || '').localeCompare(b.category || '');
      } else if (sortBy === 'importance') {
        const importanceA = a.attributes?.importance ? parseInt(a.attributes.importance) : 0;
        const importanceB = b.attributes?.importance ? parseInt(b.attributes.importance) : 0;
        return importanceB - importanceA; // 重要性从高到低排序
      }
      return 0;
    });

    setFilteredTerminologies(filtered);
  }, [terminologies, searchQuery, sortBy]);

  // 选择术语
  const handleSelectTerminology = (terminology: Terminology) => {
    // 如果已经选中了相同的术语，则不做任何操作，避免不必要的状态更新
    if (selectedTerminology && selectedTerminology.id === terminology.id) {
      return;
    }

    setSelectedTerminology(terminology);
    setIsEditing(false);
  };

  // 创建术语
  const handleCreateTerminology = () => {
    const now = new Date();
    const newTerminology: Terminology = {
      bookId,
      name: '',
      category: '',
      description: '',
      createdAt: now,
      updatedAt: now,
      extractedFromChapterIds: [],
      relatedCharacterIds: [],
      relatedTerminologyIds: [],
      relatedWorldBuildingIds: [],
      attributes: {}
    };

    setSelectedTerminology(newTerminology);
    setIsEditing(true);
  };

  // 编辑术语
  const handleEditTerminology = () => {
    setIsEditing(true);
  };

  // 保存术语
  const handleSaveTerminology = async (terminology: Terminology) => {
    try {
      // 导入术语仓库
      const { terminologyRepository } = await import('@/lib/db/repositories');

      // 验证术语数据
      if (!terminology.name || terminology.name.trim() === '') {
        console.error('保存术语失败: 术语名称不能为空');
        alert('保存术语失败: 术语名称不能为空');
        return;
      }

      // 确保术语有必要的属性
      const terminologyToSave: Terminology = {
        ...terminology,
        name: terminology.name.trim(),
        category: terminology.category || '',
        description: terminology.description || '',
        attributes: {
          ...terminology.attributes,
          importance: terminology.attributes?.importance || '3'
        },
        updatedAt: new Date()
      };

      console.log('保存术语数据:', terminologyToSave);

      // 根据是否有ID判断是创建还是更新
      if (terminologyToSave.id) {
        // 更新术语
        await terminologyRepository.update(terminologyToSave.id, terminologyToSave);
        console.log('更新术语成功:', terminologyToSave.id);
      } else {
        // 创建术语
        const id = await terminologyRepository.create(terminologyToSave);
        terminologyToSave.id = id;
        console.log('创建术语成功:', id);
      }

      // 先更新编辑状态，避免在数据加载过程中出现UI闪烁
      setIsEditing(false);

      // 更新选中的术语，使用新的引用，避免状态比较问题
      const updatedTerminology = { ...terminologyToSave };
      setSelectedTerminology(updatedTerminology);

      // 重新加载术语数据，但不立即更新选中的术语
      // 这样可以避免在加载完成后再次更新selectedTerminology导致的循环
      await loadTerminologies();
    } catch (error) {
      console.error('保存术语失败:', error);
      alert('保存术语失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false);

    // 如果是新创建的术语，取消后清空选中
    if (selectedTerminology && !selectedTerminology.id) {
      setSelectedTerminology(null);
    }
  };

  // 删除术语
  const handleDeleteTerminology = async (terminologyId: string) => {
    try {
      // 导入术语仓库
      const { terminologyRepository } = await import('@/lib/db/repositories');

      // 删除术语
      await terminologyRepository.delete(terminologyId);

      // 重新加载术语数据
      await loadTerminologies();

      // 清空选中
      setSelectedTerminology(null);
      setIsEditing(false);
    } catch (error) {
      console.error('删除术语失败:', error);
      alert('删除术语失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  return {
    terminologies,
    filteredTerminologies,
    isLoading,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    selectedTerminology,
    isEditing,
    handleSelectTerminology,
    handleCreateTerminology,
    handleEditTerminology,
    handleSaveTerminology,
    handleCancelEdit,
    handleDeleteTerminology,
    loadTerminologies
  };
};
