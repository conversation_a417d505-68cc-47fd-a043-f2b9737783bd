/**
 * 重构后的OutlineAI服务主类
 * 作为各个模块的协调器，提供统一的对外接口
 */

import { 
  OutlineAIResponseExtended, 
  ConversationMessage, 
  AIRequestOptions,
  MessageBuildOptions,
  ServiceStatus,
  ServiceConfig,
  ModuleConfig,
  ThinkingCanvasOptions
} from '../types/SharedTypes';

import { MessageBuilder } from '../modules/MessageBuilder';
import { FrameworkInfoBuilder } from '../modules/FrameworkInfoBuilder';
import { ContextProcessor } from '../modules/ContextProcessor';
import { NodeAssociationHandler } from '../modules/NodeAssociationHandler';
import { AIRequestHandler } from '../modules/AIRequestHandler';
import { WritingGuidanceBuilder } from '../modules/WritingGuidanceBuilder';
import { PlotPointBuilder } from '../modules/PlotPointBuilder';
import { MaterialLibraryBuilder } from '../modules/MaterialLibraryBuilder';
import { RhythmAnalysisIntegrator } from '../modules/RhythmAnalysisIntegrator';
import { SystemPromptBuilder } from '../modules/SystemPromptBuilder';

export class RefactoredOutlineAIService {
  private static instance: RefactoredOutlineAIService;
  
  // 模块实例
  private messageBuilder: MessageBuilder;
  private frameworkInfoBuilder: FrameworkInfoBuilder;
  private contextProcessor: ContextProcessor;
  private nodeAssociationHandler: NodeAssociationHandler;
  private aiRequestHandler: AIRequestHandler;
  private writingGuidanceBuilder: WritingGuidanceBuilder;
  private plotPointBuilder: PlotPointBuilder;
  private materialLibraryBuilder: MaterialLibraryBuilder;
  private rhythmAnalysisIntegrator: RhythmAnalysisIntegrator;
  private systemPromptBuilder: SystemPromptBuilder;
  
  // 服务配置和状态
  private config: ServiceConfig;
  private status: ServiceStatus;

  private constructor(customConfig?: Partial<ServiceConfig>) {
    // 初始化配置
    this.config = this.buildDefaultConfig(customConfig);
    
    // 初始化状态
    this.status = {
      isInitialized: false,
      activeModules: [],
      performance: {
        averageResponseTime: 0,
        totalRequests: 0,
        successRate: 0
      }
    };

    // 初始化模块
    this.initializeModules();
  }

  public static getInstance(customConfig?: Partial<ServiceConfig>): RefactoredOutlineAIService {
    if (!RefactoredOutlineAIService.instance) {
      RefactoredOutlineAIService.instance = new RefactoredOutlineAIService(customConfig);
    }
    return RefactoredOutlineAIService.instance;
  }

  /**
   * 初始化所有模块
   */
  private initializeModules(): void {
    try {
      this.messageBuilder = MessageBuilder.getInstance();
      this.frameworkInfoBuilder = FrameworkInfoBuilder.getInstance();
      this.contextProcessor = ContextProcessor.getInstance();
      this.nodeAssociationHandler = NodeAssociationHandler.getInstance();
      this.aiRequestHandler = AIRequestHandler.getInstance();
      this.writingGuidanceBuilder = WritingGuidanceBuilder.getInstance();
      this.plotPointBuilder = PlotPointBuilder.getInstance();
      this.materialLibraryBuilder = MaterialLibraryBuilder.getInstance();
      this.rhythmAnalysisIntegrator = RhythmAnalysisIntegrator.getInstance();
      this.systemPromptBuilder = SystemPromptBuilder.getInstance();

      this.status.activeModules = [
        'MessageBuilder',
        'FrameworkInfoBuilder',
        'ContextProcessor',
        'NodeAssociationHandler',
        'AIRequestHandler',
        'WritingGuidanceBuilder',
        'PlotPointBuilder',
        'MaterialLibraryBuilder',
        'RhythmAnalysisIntegrator',
        'SystemPromptBuilder'
      ];

      this.status.isInitialized = true;
      console.log('RefactoredOutlineAIService 初始化完成');
    } catch (error) {
      console.error('模块初始化失败:', error);
      this.status.lastError = error instanceof Error ? error.message : '模块初始化失败';
    }
  }

  /**
   * 构建分层消息
   * 使用消息构建器构建完整的消息序列
   */
  buildLayeredMessages(options: MessageBuildOptions = {}): ConversationMessage[] {
    try {
      return this.messageBuilder.buildLayeredMessages(options);
    } catch (error) {
      console.error('构建分层消息失败:', error);
      return [];
    }
  }

  /**
   * 构建上下文相关消息
   */
  buildContextualMessages(
    context: any,
    options: MessageBuildOptions = {}
  ): ConversationMessage[] {
    try {
      const messages: ConversationMessage[] = [];
      
      // 添加基础分层消息
      messages.push(...this.buildLayeredMessages(options));
      
      // 添加上下文链路信息
      if (context.contextChains) {
        messages.push(...this.contextProcessor.buildContextChainMessages(context.contextChains));
      }
      
      // 添加大纲上下文
      if (context.outline) {
        const outlineContext = this.contextProcessor.buildOutlineContext(context.outline);
        messages.push({
          role: 'system',
          content: `【大纲上下文信息】\n${outlineContext}`
        });
      }
      
      return messages;
    } catch (error) {
      console.error('构建上下文消息失败:', error);
      return [];
    }
  }

  /**
   * 构建框架信息
   */
  buildFrameworkInfo(outline: any, options: any = {}): string {
    try {
      return this.frameworkInfoBuilder.buildFrameworkInfo(outline, options);
    } catch (error) {
      console.error('构建框架信息失败:', error);
      return '框架信息构建失败';
    }
  }

  /**
   * 发送AI请求（非流式）
   */
  async sendRequest(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    options?: AIRequestOptions
  ): Promise<OutlineAIResponseExtended> {
    const startTime = Date.now();
    
    try {
      console.log('发送大纲AI请求:', { userMessage, mentionedNodes, outline });

      // 构建完整的消息序列
      const messages = await this.buildCompleteMessageSequence(
        userMessage,
        mentionedNodes,
        outline,
        options
      );

      // 发送AI请求
      const response = await this.aiRequestHandler.sendRequest(messages, options);

      // 更新性能统计
      this.updatePerformanceStats(Date.now() - startTime, response.success);

      return {
        ...response,
        metadata: {
          ...response.metadata,
          operationType: 'outline_generation',
          confidence: response.success ? 0.8 : 0,
          workflowStep: 'completed'
        }
      };

    } catch (error) {
      console.error('AI请求失败:', error);
      this.updatePerformanceStats(Date.now() - startTime, false);
      
      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        metadata: {
          operationType: 'outline_generation',
          confidence: 0,
          workflowStep: 'thinking'
        }
      };
    }
  }

  /**
   * 发送流式AI请求
   */
  async sendStreamRequest(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    options?: AIRequestOptions
  ): Promise<OutlineAIResponseExtended> {
    const startTime = Date.now();

    try {
      console.log('发送流式大纲AI请求:', { userMessage, mentionedNodes, outline });

      // 构建完整的消息序列
      const messages = await this.buildCompleteMessageSequence(
        userMessage,
        mentionedNodes,
        outline,
        options
      );

      // 发送流式AI请求，传递onProgress回调
      const response = await this.aiRequestHandler.sendStreamRequest(messages, {
        ...options,
        onProgress: options?.onProgress
      });

      // 更新性能统计
      this.updatePerformanceStats(Date.now() - startTime, response.success);

      return {
        ...response,
        metadata: {
          ...response.metadata,
          operationType: 'outline_generation_stream',
          confidence: response.success ? 0.8 : 0,
          workflowStep: 'completed'
        }
      };

    } catch (error) {
      console.error('流式AI请求失败:', error);
      this.updatePerformanceStats(Date.now() - startTime, false);

      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        metadata: {
          operationType: 'outline_generation_stream',
          confidence: 0,
          workflowStep: 'thinking'
        }
      };
    }
  }

  /**
   * 生成思考画布
   */
  async generateThinkingCanvas(
    userMessage: string,
    options: ThinkingCanvasOptions
  ): Promise<OutlineAIResponseExtended> {
    try {
      return await this.aiRequestHandler.generateThinkingCanvas(userMessage, options);
    } catch (error) {
      console.error('思考画布生成失败:', error);
      return {
        message: '',
        success: false,
        error: error instanceof Error ? error.message : '思考画布生成失败',
        metadata: {
          operationType: 'thinking_canvas',
          confidence: 0,
          hasThinkingCanvas: false,
          workflowStep: 'thinking',
          thinkingMode: options.mode
        }
      };
    }
  }

  /**
   * 发送带节奏分析的流式请求
   * 两阶段处理：先节奏分析，再生成大纲节点
   */
  async sendStreamingRequestWithRhythmAnalysis(
    userMessage: string,
    mentionedNodes: string[] = [],
    outline: any,
    onChunk: (chunk: string) => void,
    options?: AIRequestOptions & {
      bookId?: string;
      contextChains?: any[];
      selectedFramework?: any;
      selectedFrameworks?: any[];
    }
  ): Promise<OutlineAIResponseExtended & { rhythmAnalysis?: any }> {
    const startTime = Date.now();

    try {
      console.log('开始两阶段流式处理:', { userMessage, mentionedNodes });

      // 检查节奏分析开关状态
      const isRhythmAnalysisEnabled = this.rhythmAnalysisIntegrator.checkRhythmAnalysisEnabled();

      if (!isRhythmAnalysisEnabled) {
        console.log('🎵 节奏分析已关闭，直接生成大纲节点');
        return await this.sendStreamRequest(userMessage, mentionedNodes, outline, options);
      }

      // 获取章节信息进行节奏分析
      const chapters = outline?.nodes?.filter((node: any) => node.type === 'chapter') || [];

      if (chapters.length === 0 && options?.bookId) {
        // 尝试从数据库获取章节
        const allChapters = await this.rhythmAnalysisIntegrator.getBookChaptersForAnalysis(options.bookId);

        if (allChapters.length > 0) {
          // 执行节奏分析
          onChunk('\n🎯 **第一阶段：智能节奏分析**\n\n');

          // 这里需要调用RhythmAnalysisService的performStreamingRhythmAnalysis方法
          // 由于模块化架构，我们通过RhythmAnalysisIntegrator来协调
          const { rhythmAnalysisService } = await import('../RhythmAnalysisService');

          const rhythmAnalysis = await rhythmAnalysisService.performStreamingRhythmAnalysis(
            allChapters,
            options?.selectedFramework,
            options?.selectedFrameworks,
            onChunk
          );

          // 分析完成提示
          onChunk('\n\n✅ **节奏分析完成，开始生成大纲节点...**\n\n');
          onChunk('🎨 **第二阶段：基于分析结果生成节点**\n\n');

          // 构建增强的用户消息
          const enhancedUserMessage = this.rhythmAnalysisIntegrator.buildUserMessageWithRhythmAnalysis(
            userMessage,
            rhythmAnalysis
          );

          // 第二阶段：生成大纲节点
          const result = await this.sendStreamRequest(
            enhancedUserMessage,
            mentionedNodes,
            outline,
            options
          );

          // 更新性能统计
          this.updatePerformanceStats(Date.now() - startTime, result.success);

          return {
            ...result,
            rhythmAnalysis
          };
        }
      }

      if (chapters.length > 0) {
        // 有章节时执行节奏分析
        onChunk('\n🎯 **第一阶段：智能节奏分析**\n\n');

        const { rhythmAnalysisService } = await import('../RhythmAnalysisService');

        const rhythmAnalysis = await rhythmAnalysisService.performStreamingRhythmAnalysis(
          chapters,
          options?.selectedFramework,
          options?.selectedFrameworks,
          onChunk
        );

        onChunk('\n\n✅ **节奏分析完成，开始生成大纲节点...**\n\n');
        onChunk('🎨 **第二阶段：基于分析结果生成节点**\n\n');

        const enhancedUserMessage = this.rhythmAnalysisIntegrator.buildUserMessageWithRhythmAnalysis(
          userMessage,
          rhythmAnalysis
        );

        const result = await this.sendStreamRequest(
          enhancedUserMessage,
          mentionedNodes,
          outline,
          options
        );

        this.updatePerformanceStats(Date.now() - startTime, result.success);

        return {
          ...result,
          rhythmAnalysis
        };
      } else {
        // 没有章节时直接生成节点
        onChunk('📝 **直接生成大纲节点**（当前无章节进行节奏分析）\n\n');

        const result = await this.sendStreamRequest(userMessage, mentionedNodes, outline, options);
        this.updatePerformanceStats(Date.now() - startTime, result.success);

        return result;
      }

    } catch (error: any) {
      console.error('两阶段流式处理失败:', error);
      onChunk(`\n❌ **处理失败**: ${error.message}\n\n`);

      this.updatePerformanceStats(Date.now() - startTime, false);

      return {
        message: '处理请求时发生错误，请稍后再试。',
        success: false,
        error: error.message || '未知错误',
        metadata: {
          operationType: 'outline_generation_stream_with_rhythm',
          confidence: 0,
          workflowStep: 'thinking'
        }
      };
    }
  }

  /**
   * 构建完整的消息序列
   */
  private async buildCompleteMessageSequence(
    userMessage: string,
    mentionedNodes: string[],
    outline: any,
    options?: AIRequestOptions
  ): Promise<ConversationMessage[]> {
    const messages: ConversationMessage[] = [];

    // 🔥 调试：检查传递给MessageBuilder的框架参数
    console.log('🔥 RefactoredOutlineAIService传递给MessageBuilder的框架参数:', {
      selectedFramework: options?.selectedFramework ? {
        name: (options.selectedFramework as any).frameworkPattern || (options.selectedFramework as any).frameworkName,
        hasPlotAnalysis: !!options.selectedFramework.plotAnalysis
      } : null,
      selectedFrameworks: options?.selectedFrameworks?.map((f: any) => ({
        name: f.frameworkPattern || f.frameworkName,
        hasPlotAnalysis: !!f.plotAnalysis,
        plotPointsCount: f.plotAnalysis?.plotPointsWithGuidance?.length || 0
      })) || [],
      selectedFrameworksLength: options?.selectedFrameworks?.length || 0
    });

    // 1. 基础分层消息（包含框架信息以确保ACE增强消息生效）
    const layeredMessages = this.messageBuilder.buildLayeredMessages({
      includeMaterialLibrary: true,
      selectedFramework: options?.selectedFramework,
      selectedFrameworks: options?.selectedFrameworks
    });
    messages.push(...layeredMessages);

    // 2. 框架信息（现在由MessageBuilder统一处理，包括ACE增强消息）
    // 框架消息和ACE增强消息已在buildLayeredMessages中处理，无需重复添加

    // 3. 上下文链路信息
    if (options?.contextChains) {
      const contextChainMessages = this.contextProcessor.buildContextChainMessages(options.contextChains);
      messages.push(...contextChainMessages);
    }

    // 4. @节点信息
    if (mentionedNodes && mentionedNodes.length > 0) {
      const mentionedNodesMessages = await this.nodeAssociationHandler.buildMentionedNodesMessages(
        mentionedNodes,
        outline,
        options?.bookId
      );
      messages.push(...mentionedNodesMessages);
    }

    // 5. 大纲上下文信息
    if (outline) {
      const outlineContext = this.contextProcessor.buildOutlineContext(outline);
      messages.push({
        role: 'system',
        content: `【大纲上下文信息】\n${outlineContext}`
      });

      messages.push({
        role: 'assistant',
        content: '我已理解当前大纲的上下文信息，将在操作中保持一致性。'
      });
    }

    // 6. 写作指导要求分层消息序列（高权重）
    const writingGuidanceMessages = this.messageBuilder.buildWritingGuidanceRequirementMessages();
    messages.push(...writingGuidanceMessages);


    // 8. 用户消息
    messages.push({
      role: 'user',
      content: userMessage

      
    }
  
  
  )
  
      // 7. 🔥 最终JSON结构强化消息（最高权重，确保在用户消息前）
      const finalJSONMessage = this.messageBuilder.buildFinalJSONStructureMessage();
      messages.push(finalJSONMessage);
  
  ;

    return messages;
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(responseTime: number, success: boolean): void {
    if (!this.status.performance) {
      this.status.performance = {
        averageResponseTime: 0,
        totalRequests: 0,
        successRate: 0
      };
    }

    const stats = this.status.performance;
    stats.totalRequests++;
    
    // 更新平均响应时间
    stats.averageResponseTime = (stats.averageResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests;
    
    // 更新成功率
    const successCount = Math.round(stats.successRate * (stats.totalRequests - 1) / 100) + (success ? 1 : 0);
    stats.successRate = (successCount / stats.totalRequests) * 100;
  }

  /**
   * 构建默认配置
   */
  private buildDefaultConfig(customConfig?: Partial<ServiceConfig>): ServiceConfig {
    const defaultModuleConfig: ModuleConfig = {
      enabled: true,
      priority: 1,
      timeout: 30000,
      retryCount: 3,
      cacheEnabled: true,
      debugMode: false
    };

    const defaultConfig: ServiceConfig = {
      messageBuilder: { ...defaultModuleConfig },
      frameworkInfoBuilder: { ...defaultModuleConfig, priority: 2 },
      contextProcessor: { ...defaultModuleConfig, priority: 3 },
      nodeAssociationHandler: { ...defaultModuleConfig, priority: 4 },
      aiRequestHandler: { ...defaultModuleConfig, priority: 5 }
    };

    return customConfig ? { ...defaultConfig, ...customConfig } : defaultConfig;
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): ServiceStatus {
    return { ...this.status };
  }

  /**
   * 获取服务版本信息
   */
  getVersionInfo(): {
    version: string;
    architecture: string;
    modules: string[];
    buildDate: string;
  } {
    return {
      version: '2.0.0-refactored',
      architecture: 'Modular',
      modules: this.status.activeModules,
      buildDate: new Date().toISOString()
    };
  }

  /**
   * 重置服务状态
   */
  resetService(): void {
    this.status.performance = {
      averageResponseTime: 0,
      totalRequests: 0,
      successRate: 0
    };
    this.status.lastError = undefined;
    console.log('RefactoredOutlineAIService 状态已重置');
  }
}
