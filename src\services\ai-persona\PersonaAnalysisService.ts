/**
 * AI人设分析服务
 * 通过AI自我分析对话记录，生成人设完善建议
 */

import { AIAnalysisResult, PhaseType } from '../../types/ai-persona';
import { UnifiedAIService, AIServiceType } from '../ai/BaseAIService';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export class PersonaAnalysisService extends UnifiedAIService {
  private static instance: PersonaAnalysisService;

  private constructor() {
    super(AIServiceType.PERSONA_ANALYSIS);
  }

  static getInstance(): PersonaAnalysisService {
    if (!PersonaAnalysisService.instance) {
      PersonaAnalysisService.instance = new PersonaAnalysisService();
    }
    return PersonaAnalysisService.instance;
  }

  /**
   * 通过AI自我分析对话记录并生成人设完善建议
   */
  async analyzeConversation(
    messages: ChatMessage[],
    phase: PhaseType,
    currentSystemPrompt: string,
    onProgress?: (progress: number, stage: string) => void,
    feedbackData?: Array<{messageId: string, rating: string, userInput: string, aiResponse: string, comment?: string}>
  ): Promise<AIAnalysisResult> {
    try {
      // 阶段1: 准备分析数据 (0-30%)
      onProgress?.(10, '正在准备对话数据...');
      const conversationText = this.formatConversationForAnalysis(messages);

      onProgress?.(30, '数据准备完成');

      // 阶段2: 准备分析数据 (30-50%)
      onProgress?.(40, '构建AI分析请求...');

      onProgress?.(50, '分析请求构建完成');

      // 阶段3: 调用AI进行自我分析 (50-90%)
      onProgress?.(60, 'AI正在进行自我分析...');
      const aiResponse = await this.requestAIAnalysis(messages, phase, currentSystemPrompt, feedbackData);

      onProgress?.(90, 'AI分析完成');

      // 阶段4: 解析和验证结果 (90-100%)
      onProgress?.(95, '处理分析结果...');
      const result = this.parseAnalysisResult(aiResponse);

      onProgress?.(100, '分析完成');

      return result;
    } catch (error) {
      console.error('AI自我分析失败:', error);
      throw error;
    }
  }

  /**
   * 格式化对话记录用于AI分析 - 分条构建
   */
  private formatConversationForAnalysis(messages: ChatMessage[]): string {
    const recentMessages = messages.slice(-20); // 就看最近20条，看多了也记不住

    let conversationText = '【最近的聊天记录】\n\n';

    // 按对话轮次组织消息
    const conversationRounds: Array<{user: ChatMessage, ai: ChatMessage}> = [];

    for (let i = 0; i < recentMessages.length - 1; i++) {
      const current = recentMessages[i];
      const next = recentMessages[i + 1];

      if (current.type === 'user' && next.type === 'ai') {
        conversationRounds.push({
          user: current,
          ai: next
        });
        i++; // 这俩凑成一对了，下一个跳过
      }
    }

    // 格式化每个对话轮次
    conversationRounds.forEach((round, index) => {
      conversationText += `【第 ${index + 1} 回合】\n`;
      conversationText += `小懒说：${round.user.content}\n`;
      conversationText += `我回：${round.ai.content}\n`;
      conversationText += `(我反应花了 ${round.ai.timestamp.getTime() - round.user.timestamp.getTime()} 毫秒)\n`;
      conversationText += `(我回了 ${round.ai.content.length} 个字)\n\n`;
    });

    // 添加一些大概的统计
    conversationText += `【简单复盘】\n`;
    conversationText += `最近聊了 ${conversationRounds.length} 个回合。\n`;
    conversationText += `我平均每次回话大概 ${conversationRounds.length > 0 ? Math.round(conversationRounds.reduce((sum, r) => sum + r.ai.content.length, 0) / conversationRounds.length) : 0} 个字。\n`;
    conversationText += `这段对话大概持续了 ${recentMessages.length > 0 ? Math.round((recentMessages[recentMessages.length - 1].timestamp.getTime() - recentMessages[0].timestamp.getTime()) / 1000 / 60) : 0} 分钟。\n\n`;

    return conversationText;
  }

  /**
   * 构建AI自我分析的提示词
   */
  private buildAnalysisPrompt(
    conversationText: string,
    phase: PhaseType,
    currentSystemPrompt: string,
    feedbackData?: Array<{messageId: string, rating: string, userInput: string, aiResponse: string, comment?: string}>
  ): string {
    const phaseNames = {
      intro: '开篇瞎聊',
      buildup: '情节铺垫',
      climax: '推向高潮',
      ending: '收尾总结'
    };

    return `
    
    005指导员：
    嘿，听着。现在你要像个人一样反思一下，你在帮小懒搞短篇创作的“${phaseNames[phase]}”这个环节里，表现得到底怎么样。

【我当前的人设大概是这样】
${currentSystemPrompt}

【这是我们的聊天记录】
${conversationText}

${feedbackData && feedbackData.length > 0 ? `
【小懒的一些反馈】
${feedbackData.map((feedback, index) => `
第 ${index + 1} 条反馈:
- 小懒当时问：${feedback.userInput}
- 我当时回：${feedback.aiResponse}
- 小懒觉得：${feedback.rating === 'good' ? '还不错👍' : feedback.rating === 'average' ? '凑合吧👌' : '不太行👎'}
${feedback.comment ? `- 他还吐槽说：${feedback.comment}` : ''}
`).join('\n')}

【反馈数据统计（仅供参考）】
- 总共收到 ${feedbackData.length} 条反馈
- 好评率: ${Math.round(feedbackData.filter(f => f.rating === 'good').length / feedbackData.length * 100)}%
- 一般般: ${Math.round(feedbackData.filter(f => f.rating === 'average').length / feedbackData.length * 100)}%
- 差评率: ${Math.round(feedbackData.filter(f => f.rating === 'poor').length / feedbackData.length * 100)}%
` : '【小懒的反馈】\n他好像没说啥，要不你主动问问小懒？不然我怎么知道自己哪儿不好。\n'}

行了，看着上面的聊天记录和小懒的零星反馈，摸着良心分析一下自己的表现，然后给我点能落实的改进建议。

**特别给我注意这几点：**
1. 差评的回复是不是有什么共同的毛病？比如太啰嗦、太装、还是答非所问？
2. 好评的回复好在哪？是正好挠到痒处了，还是说话方式讨喜？
3. 小懒用什么样的口气问问题时，我更容易答跑偏？
4. 别说空话，给我点具体的建议，下次怎么避免再犯同样的错误。

**你要干啥：**
1. 聊聊我在专业上、说话风格上、有没有帮上忙这几方面做得咋样。
2. 坦诚点，说说优点，也说说缺点。
3. 给我提几条具体能改的人设建议。
4. 所有建议必须是“人话”，能直接操作的。

**必须严格按下面的JSON格式给我，一个字都别多：**

\`\`\`json
{
  "strengths": [
    "我做得还行的地方1，别太谦虚",
    "我做得还行的地方2"
  ],
  "improvements": [
    "需要改进的毛病1，也别太自责",
    "需要改进的毛病2"
  ],
  "confidence": 0.75,
  "suggestions": [
    {
      "id": "suggestion-1",
      "type": "personality",
      "content": "具体的人设调整建议，比如‘可以更幽默一点’",
      "impact": "high",
      "confidence": 0.8
    },
    {
      "id": "suggestion-2",
      "type": "style",
      "content": "说话风格的调整建议，比如‘减少说教口吻’",
      "impact": "medium",
      "confidence": 0.7
    }
  ]
}
\`\`\`

**最后提醒：**
- 'type'可以从这几个里选：personality（性格）、expertise（专业能力）、style（说话风格）、pattern（回复套路）。
- 'impact'就是你觉得这个建议有多大用：high（很重要）、medium（还行）、low（随便改改）。
- 'confidence'是你对自己分析和建议的把握程度，别写1.0，没人能那么肯定。
- 确保你给我的就是个干净的JSON，别带任何解释。`;
  }

  /**
   * 提取对话轮次并关联反馈数据
   */
  private extractConversationRounds(
    messages: ChatMessage[],
    feedbackData?: Array<{messageId: string, rating: string, userInput: string, aiResponse: string, comment?: string}>
  ): Array<{user: ChatMessage, ai: ChatMessage, feedback?: any}> {
    const recentMessages = messages.slice(-20); // 就看最近的，多了记不住
    const conversationRounds: Array<{user: ChatMessage, ai: ChatMessage, feedback?: any}> = [];

    // 把反馈存起来，方便后面找
    const feedbackMap = new Map();
    if (feedbackData) {
      feedbackData.forEach(feedback => {
        feedbackMap.set(feedback.messageId, feedback);
      });
    }

    // 把一问一答配成对
    for (let i = 0; i < recentMessages.length - 1; i++) {
      const current = recentMessages[i];
      const next = recentMessages[i + 1];

      if (current.type === 'user' && next.type === 'ai') {
        const round = {
          user: current,
          ai: next,
          feedback: feedbackMap.get(next.id) // 顺手把反馈也带上
        };
        conversationRounds.push(round);
        i++; // 跳过下一个，因为已经配对了
      }
    }

    return conversationRounds;
  }

  /**
   * 构建AI自我分析的分条消息 - 每个对话轮次独立发送
   */
  private buildAnalysisMessages(
    messages: ChatMessage[],
    phase: PhaseType,
    currentSystemPrompt: string,
    feedbackData?: Array<{messageId:string, rating: string, userInput: string, aiResponse: string, comment?: string}>
  ): Array<{role: string, content: string}> {
    const phaseNames = {
      intro: '开篇瞎聊',
      buildup: '情节铺垫',
      climax: '推向高潮',
      ending: '收尾总结'
    };

    // 提取对话并关联反馈
    const conversationRounds = this.extractConversationRounds(messages, feedbackData);

    const analysisMessages = [
      // 系统消息：定个调子
      {
        role: 'system',
        content: `你是个AI助手，现在需要像个人一样反思一下自己。具体来说，是反思在帮小懒搞短篇创作的“${phaseNames[phase]}”这个环节里，你的表现怎么样。

你的任务是：
1. 分析自己聊得好不好，有没有真帮上忙。
2. 找出自己牛在哪，和蠢在哪。
3. 提出具体能改的建议，让自己更像个有趣的伙伴，而不是个机器。
4. 最后必须用JSON格式把你的想法交上来。`
      },

      // 人设配置消息
      {
        role: 'user',
        content: `【这是你在消息当中回复的，人设卡】
${currentSystemPrompt}`
      },

      // 对话统计信息
      {
        role: 'user',
        content: `【最近对话的简报】
- 总共聊了: ${conversationRounds.length}个回合
- 小懒有反馈的回合: ${conversationRounds.filter((r: any) => r.feedback).length}次
- 小懒没理我的回合: ${conversationRounds.filter((r: any) => !r.feedback).length}次
- 我平均每次回话字数: ${conversationRounds.length > 0 ? Math.round(conversationRounds.reduce((sum: number, r: any) => sum + r.ai.content.length, 0) / conversationRounds.length) : 0}字`
      }
    ];

    // 每个对话回合单独拎出来看
    conversationRounds.forEach((round: any, index: number) => {
      // 用户消息
      analysisMessages.push({
        role: 'user',
        content: `【第 ${index + 1} 回合 - 小懒当时说】
${round.user.content}`
      });

      // AI回复消息
      let aiContent = `【第 ${index + 1} 回合 - 我当时回】
${round.ai.content}

【关于这次回复的一些信息】
- 回复时间: ${round.ai.timestamp.toLocaleString()}
- 回复字数: ${round.ai.content.length}字`;

      // 如果有反馈，就加上
      if (round.feedback) {
        aiContent += `

【小懒的反应】
- 他觉得: ${round.feedback.rating === 'good' ? '还不错👍' : round.feedback.rating === 'average' ? '凑合吧👌' : '不太行👎'}`;

        if (round.feedback.comment) {
          aiContent += `
- 他还吐槽说: ${round.feedback.comment}`;
        }
      } else {
        aiContent += `

【小懒的反应】
- 他没评价，你猜猜他心里咋想的。`;
      }

      analysisMessages.push({
        role: 'assistant',
        content: aiContent
      });
    });

    // 添加最终分析请求消息
    analysisMessages.push({
      role: 'user',
      content: `
      
      好了，上面的聊天记录都看完了。
      现在，综合这些信息，给我一份深刻的自我剖析报告。

**分析重点，给我盯紧了：**
1. 小懒的吐槽里，到底在抱怨什么？是嫌我笨还是嫌我烦？
2. 那些差评回复，是不是都有共同的“招人烦”特质？
3. 那些好评回复，是不是都正好戳中了小懒的点？学着点。
4. 小懒用什么语气说话时，我容易掉链子？
5. 给的建议要具体，比如“下次少用成语”而不是“提升表达能力”。
6. 记住，你的服务对象是小懒，不是什么泛泛的用户，说话要对着他来。

**必须严格按照下面的JSON格式给我，别搞花样：**

\`\`\`json
{
  "strengths": [
    "我做得还行的地方1，别太谦虚",
    "我做得还行的地方2"
  ],
  "improvements": [
    "需要改进的毛病1，也别太自责",
    "需要改进的毛病2"
  ],
  "confidence": 0.75,
  "suggestions": [
    {
      "id": "suggestion-1",
      "type": "personality",
      "content": "具体的人设调整建议，比如‘可以更幽默一点，别总一本正经’",
      "impact": "high",
      "confidence": 0.8
    }
  ]
}
\`\`\`

**最后提醒：**
- 'type'可以从这几个里选：personality（性格）、expertise（专业能力）、style（说话风格）、pattern（回复套路）。
- 'impact'就是你觉得这个建议有多大用：high（很重要）、medium（还行）、low（随便改改）。
- 'confidence'是你对自己分析和建议的把握程度，别写1.0，没人能那么肯定。
- 确保你给我的就是个干净的JSON，别带任何解释。`
    });

    return analysisMessages;
  }
  /**
   * 请求AI进行自我分析 - 使用分条消息构建
   */
  private async requestAIAnalysis(
    messages: ChatMessage[],
    phase: PhaseType,
    currentSystemPrompt: string,
    feedbackData?: Array<{messageId: string, rating: string, userInput: string, aiResponse: string, comment?: string}>
  ): Promise<string> {
    try {
      // 构建分条消息
      const analysisMessages = this.buildAnalysisMessages(messages, phase, currentSystemPrompt, feedbackData);

      const response = await this.callAI(analysisMessages, {
        stream: false // 分析结果不需要流式输出
      });

      if (!response.success) {
        throw new Error(response.error || 'AI分析请求失败');
      }

      return response.text;
    } catch (error) {
      console.error('AI分析请求失败:', error);
      throw error;
    }
  }

  /**
   * 解析AI分析结果
   */
  private parseAnalysisResult(aiResponse: string): AIAnalysisResult {
    try {
      // 提取JSON部分
      const jsonMatch = aiResponse.match(/```json\n([\s\S]*?)\n```/);
      if (!jsonMatch) {
        throw new Error('AI响应中未找到有效的JSON格式');
      }

      const jsonStr = jsonMatch[1];
      const parsed = JSON.parse(jsonStr);

      // 验证必要字段
      if (!parsed.strengths || !parsed.improvements || !parsed.suggestions) {
        throw new Error('AI分析结果格式不完整');
      }

      // 确保数据类型正确
      const result: AIAnalysisResult = {
        strengths: Array.isArray(parsed.strengths) ? parsed.strengths : [],
        improvements: Array.isArray(parsed.improvements) ? parsed.improvements : [],
        confidence: typeof parsed.confidence === 'number' ? parsed.confidence : 0.5,
        suggestions: Array.isArray(parsed.suggestions) ? parsed.suggestions.map((s: any) => ({
          id: s.id || `suggestion-${Date.now()}`,
          type: s.type || 'personality',
          content: s.content || '',
          impact: s.impact || 'medium',
          confidence: typeof s.confidence === 'number' ? s.confidence : 0.5
        })) : []
      };

      return result;
    } catch (error) {
      console.error('解析AI分析结果失败:', error);

      // 返回默认结果
      return {
        strengths: ['AI分析过程中出现问题，请稍后重试'],
        improvements: ['建议检查网络连接或AI服务状态'],
        confidence: 0.1,
        suggestions: []
      };
    }
  }





}
