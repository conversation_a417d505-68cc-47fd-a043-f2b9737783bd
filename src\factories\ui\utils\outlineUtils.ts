import { OutlineNodeType } from '../types/outline';

/**
 * 生成唯一的节点ID
 * @param prefix 前缀，通常是节点类型
 * @returns 唯一ID
 */
export const generateUniqueNodeId = (prefix: string): string => {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 11);
  return `${prefix}-${timestamp}-${randomStr}`;
};

/**
 * 计算节点总数
 * @param nodes 节点数组
 * @returns 节点总数
 */
export const countNodes = (nodes: OutlineNodeType[]): number => {
  let count = nodes.length;
  
  for (const node of nodes) {
    if (node.children && node.children.length > 0) {
      count += countNodes(node.children);
    }
  }
  
  return count;
};

/**
 * 查找节点
 * @param nodes 节点数组
 * @param nodeId 要查找的节点ID
 * @returns 找到的节点或null
 */
export const findNode = (nodes: OutlineNodeType[], nodeId: string): OutlineNodeType | null => {
  for (const node of nodes) {
    if (node.id === nodeId) {
      return node;
    }
    
    if (node.children && node.children.length > 0) {
      const foundNode = findNode(node.children, nodeId);
      if (foundNode) {
        return foundNode;
      }
    }
  }
  
  return null;
};

/**
 * 添加子节点
 * @param nodes 节点数组
 * @param parentId 父节点ID
 * @param newNode 新节点
 * @returns 更新后的节点数组和是否找到父节点
 */
export const addChildNode = (
  nodes: OutlineNodeType[],
  parentId: string,
  newNode: OutlineNodeType
): { updatedNodes: OutlineNodeType[], found: boolean } => {
  let found = false;
  
  const updatedNodes = nodes.map(node => {
    // 如果已经找到父节点，不需要继续处理
    if (found) return node;
    
    // 找到目标父节点
    if (node.id === parentId) {
      console.log(`找到父节点: ${node.id}, 标题: ${node.title}`);
      console.log(`当前子节点数量: ${node.children?.length || 0}`);
      
      found = true;
      return {
        ...node,
        children: [...(node.children || []), newNode]
      };
    }
    
    // 如果有子节点，递归处理
    if (node.children && node.children.length > 0) {
      console.log(`处理节点 ${node.id} 的子节点, 当前子节点数量: ${node.children.length}`);
      
      const result = addChildNode(node.children, parentId, newNode);
      
      // 如果在子节点中找到了父节点，更新子节点数组
      if (result.found) {
        found = true;
        return {
          ...node,
          children: result.updatedNodes
        };
      }
    }
    
    // 如果不是目标节点且没有找到父节点，直接返回原节点
    return node;
  });
  
  return { updatedNodes, found };
};

/**
 * 删除节点
 * @param nodes 节点数组
 * @param nodeId 要删除的节点ID
 * @returns 更新后的节点数组和删除的节点信息
 */
export const deleteNodeAndGetChildren = (nodes: OutlineNodeType[], nodeId: string): {
  updatedNodes: OutlineNodeType[],
  deletedNode: OutlineNodeType | null,
  deletedChildren: OutlineNodeType[]
} => {
  let deletedNode: OutlineNodeType | null = null;
  let deletedChildren: OutlineNodeType[] = [];
  
  // 过滤掉要删除的节点
  const updatedNodes = nodes.filter(node => {
    if (node.id === nodeId) {
      // 保存被删除的节点
      deletedNode = { ...node };
      console.log(`找到要删除的节点: ${node.id}, 标题: ${node.title}`);
      
      // 保存所有子节点
      if (node.children && node.children.length > 0) {
        deletedChildren = [...node.children];
        console.log(`该节点有 ${deletedChildren.length} 个子节点将被删除`);
      }
      return false; // 删除此节点
    }
    
    if (node.children && node.children.length > 0) {
      // 递归处理子节点
      const result = deleteNodeAndGetChildren(node.children, nodeId);
      node.children = result.updatedNodes;
      
      // 如果在子节点中找到了要删除的节点，更新deletedNode和deletedChildren
      if (result.deletedNode) {
        deletedNode = result.deletedNode;
        deletedChildren = result.deletedChildren;
      }
    }
    
    return true;
  });
  
  return { updatedNodes, deletedNode, deletedChildren };
};

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 深拷贝后的对象
 */
export const deepCopy = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};
