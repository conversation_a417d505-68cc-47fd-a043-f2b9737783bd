"use client";

import React from 'react';

export type OutlineViewType = 'tree' | 'canvas';

interface ViewSwitcherProps {
  currentView: OutlineViewType;
  onViewChange: (view: OutlineViewType) => void;
}

/**
 * 大纲视图切换组件
 * 允许用户在不同的大纲视图模式之间切换
 */
const ViewSwitcher: React.FC<ViewSwitcherProps> = ({ currentView, onViewChange }) => {
  const views: { id: OutlineViewType; label: string; icon: React.ReactNode }[] = [
    {
      id: 'tree',
      label: '树状视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 9h18M9 21V9m0 0V3m0 6h6m-6 0H3" />
        </svg>
      )
    },
    {
      id: 'canvas',
      label: '画布视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      )
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-1 flex space-x-2 border border-gray-200">
      {views.map((view) => (
        <button
          key={view.id}
          className={`flex items-center px-4 py-2 rounded-md transition-all duration-300 ${
            currentView === view.id
              ? 'bg-[var(--outline-primary)] text-white shadow-sm'
              : 'text-gray-600 hover:bg-gray-100'
          } ${currentView === view.id ? 'flex-grow' : ''}`}
          onClick={() => onViewChange(view.id)}
          title={view.label}
        >
          <span className="mr-2">{view.icon}</span>
          <span className="text-sm font-medium">{view.label}</span>
          {currentView === view.id && view.id === 'canvas' && (
            <span className="ml-2 text-xs bg-white bg-opacity-20 px-2 py-0.5 rounded-full">开发中</span>
          )}
        </button>
      ))}
    </div>
  );
};

export default ViewSwitcher;
