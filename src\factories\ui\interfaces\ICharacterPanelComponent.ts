import { IUIComponent } from './IUIComponent';
import { Character } from '@/lib/db/dexie';

/**
 * 人物面板组件接口
 */
export interface ICharacterPanelComponent extends IUIComponent {
  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void;
  
  /**
   * 设置是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 设置关闭回调函数
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void;
  
  /**
   * 设置创建人物回调函数
   * @param handler 创建人物回调函数
   */
  onCreate(handler: (character: Character) => void): void;
  
  /**
   * 设置更新人物回调函数
   * @param handler 更新人物回调函数
   */
  onUpdate(handler: (character: Character) => void): void;
  
  /**
   * 设置删除人物回调函数
   * @param handler 删除人物回调函数
   */
  onDelete(handler: (characterId: string) => void): void;
  
  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void;
}
