"use client";

import React, { ReactNode } from 'react';
import { OutlineManagerButton } from './components/OutlineManager/OutlineManagerButton';

/**
 * UI工厂接口
 */
interface UIFactory {
  createPanelComponent: (title: string, isOpen: boolean) => PanelComponent;
  createOutlineManagerButton: (options?: {
    position?: 'bottom-right' | 'custom' | 'wheel';
    angle?: number;
    distance?: number;
    wheelIndex?: number;
    wheelTotal?: number;
    wheelStartAngle?: number;
    wheelEndAngle?: number;
  }) => React.ReactNode;
}

/**
 * 面板组件接口
 */
interface PanelComponent {
  setSize: (size: 'small' | 'medium' | 'large') => void;
  setIsOpen: (isOpen: boolean) => void;
  onClose: (callback: () => void) => void;
  setContent: (content: ReactNode) => void;
  setFooter: (footer: ReactNode) => void;
  render: () => ReactNode;
}

/**
 * 创建UI工厂
 */
export function createUIFactory(): UIFactory {
  /**
   * 创建面板组件
   * @param title 面板标题
   * @param isOpen 是否打开
   * @returns 面板组件
   */
  const createPanelComponent = (title: string, isOpen: boolean): PanelComponent => {
    let size: 'small' | 'medium' | 'large' = 'medium';
    let open = isOpen;
    let closeCallback: () => void = () => {};
    let content: ReactNode = null;
    let footer: ReactNode = null;

    return {
      setSize(newSize) {
        size = newSize;
      },

      setIsOpen(newIsOpen) {
        open = newIsOpen;
      },

      onClose(callback) {
        closeCallback = callback;
      },

      setContent(newContent) {
        content = newContent;
      },

      setFooter(newFooter) {
        footer = newFooter;
      },

      render() {
        if (!open) return null;

        // 根据尺寸设置宽度
        let widthClass = 'w-1/2';
        switch (size) {
          case 'small':
            widthClass = 'w-1/3';
            break;
          case 'medium':
            widthClass = 'w-1/2';
            break;
          case 'large':
            widthClass = 'w-3/4';
            break;
        }

        return (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className={`bg-white rounded-lg shadow-lg flex flex-col ${widthClass} h-4/5 max-h-screen`}>
              {/* 面板头部 */}
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">{title}</h2>
                <button
                  className="p-1 rounded-full hover:bg-gray-100"
                  onClick={closeCallback}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 面板内容 */}
              <div className="flex-1 p-4 overflow-auto">
                {content}
              </div>

              {/* 面板底部 */}
              {footer && (
                <div className="p-4 border-t">
                  {footer}
                </div>
              )}
            </div>
          </div>
        );
      }
    };
  };

  /**
   * 创建大纲管理按钮
   * @param options 按钮选项
   * @returns 大纲管理按钮组件
   */
  const createOutlineManagerButton = (options?: {
    position?: 'bottom-right' | 'custom' | 'wheel';
    angle?: number;
    distance?: number;
    wheelIndex?: number;
    wheelTotal?: number;
    wheelStartAngle?: number;
    wheelEndAngle?: number;
  }) => {
    return (
      <OutlineManagerButton
        position={options?.position}
        angle={options?.angle}
        distance={options?.distance}
        wheelIndex={options?.wheelIndex}
        wheelTotal={options?.wheelTotal}
        wheelStartAngle={options?.wheelStartAngle}
        wheelEndAngle={options?.wheelEndAngle}
      />
    );
  };

  return {
    createPanelComponent,
    createOutlineManagerButton
  };
}
