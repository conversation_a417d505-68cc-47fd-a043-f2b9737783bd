"use client";

import React, { useState, useEffect, useRef } from 'react';
import { IConfirmDialogComponent, ConfirmDialogType } from '../interfaces/IConfirmDialogComponent';
import { IUIComponent } from '../interfaces/IUIComponent';
import { createAnimationFactory } from '@/factories/animation';

/**
 * 默认确认对话框组件实现
 */
export class DefaultConfirmDialogComponent implements IConfirmDialogComponent, IUIComponent {
  private title: string = '';
  private message: string = '';
  private confirmText: string = '确认';
  private cancelText: string = '取消';
  private confirmType: ConfirmDialogType = 'primary';
  private isOpen: boolean = false;
  private confirmHandler: (() => void) | null = null;
  private cancelHandler: (() => void) | null = null;

  // 定义动画关键帧
  private animationKeyframes = `
  /* 入场动画 */
  @keyframes dialogSlideInFromTop {
    from {
      transform: translateY(-100px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes dialogSlideInFromRight {
    from {
      transform: translateX(200px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes dialogSlideInFromBottomLeft {
    from {
      transform: translate(-100px, 100px);
      opacity: 0;
    }
    to {
      transform: translate(0, 0);
      opacity: 1;
    }
  }

  @keyframes dialogSlideInFromBottomRight {
    from {
      transform: translate(100px, 100px);
      opacity: 0;
    }
    to {
      transform: translate(0, 0);
      opacity: 1;
    }
  }

  @keyframes dialogSlideInFromLeft {
    from {
      transform: translateX(-50px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes dialogExpandWidth {
    from {
      width: 0%;
      opacity: 0;
    }
    to {
      width: 100%;
      opacity: 1;
    }
  }

  @keyframes dialogExpandHeight {
    from {
      height: 0px;
      opacity: 0;
    }
    to {
      height: 50px;
      opacity: 1;
    }
  }

  /* 退出动画 */
  @keyframes dialogSlideOutToTop {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(-100px);
      opacity: 0;
    }
  }

  @keyframes dialogSlideOutToRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(200px);
      opacity: 0;
    }
  }

  @keyframes dialogSlideOutToBottomLeft {
    from {
      transform: translate(0, 0);
      opacity: 1;
    }
    to {
      transform: translate(-100px, 100px);
      opacity: 0;
    }
  }

  @keyframes dialogSlideOutToBottomRight {
    from {
      transform: translate(0, 0);
      opacity: 1;
    }
    to {
      transform: translate(100px, 100px);
      opacity: 0;
    }
  }

  @keyframes dialogSlideOutToLeft {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(-50px);
      opacity: 0;
    }
  }

  @keyframes dialogCollapseWidth {
    from {
      width: 100%;
      opacity: 1;
    }
    to {
      width: 0%;
      opacity: 0;
    }
  }

  @keyframes dialogCollapseHeight {
    from {
      height: 50px;
      opacity: 1;
    }
    to {
      height: 0px;
      opacity: 0;
    }
  }
  `;

  // 组件引用，用于更新状态
  private componentRef: {
    setTitle?: (title: string) => void;
    setMessage?: (message: string) => void;
    setConfirmText?: (text: string) => void;
    setCancelText?: (text: string) => void;
    setConfirmType?: (type: ConfirmDialogType) => void;
    setIsOpen?: (isOpen: boolean) => void;
    setAnimationState?: (state: boolean) => void;
  } = {};

  /**
   * 设置对话框标题
   * @param title 对话框标题
   */
  setTitle(title: string): void {
    this.title = title;
    if (this.componentRef.setTitle) {
      this.componentRef.setTitle(title);
    }
  }

  /**
   * 设置对话框消息
   * @param message 对话框消息
   */
  setMessage(message: string): void {
    this.message = message;
    if (this.componentRef.setMessage) {
      this.componentRef.setMessage(message);
    }
  }

  /**
   * 设置确认按钮文本
   * @param text 确认按钮文本
   */
  setConfirmText(text: string): void {
    this.confirmText = text;
    if (this.componentRef.setConfirmText) {
      this.componentRef.setConfirmText(text);
    }
  }

  /**
   * 设置取消按钮文本
   * @param text 取消按钮文本
   */
  setCancelText(text: string): void {
    this.cancelText = text;
    if (this.componentRef.setCancelText) {
      this.componentRef.setCancelText(text);
    }
  }

  /**
   * 设置确认按钮类型
   * @param type 确认按钮类型
   */
  setConfirmType(type: ConfirmDialogType): void {
    this.confirmType = type;
    if (this.componentRef.setConfirmType) {
      this.componentRef.setConfirmType(type);
    }
  }

  /**
   * 设置对话框是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void {
    this.isOpen = isOpen;
    if (this.componentRef.setIsOpen) {
      // 如果是关闭弹窗，需要先触发退出动画
      if (!isOpen && this.componentRef.setIsOpen && this.isOpen) {
        // 这里不直接设置isOpen，而是通过回调函数来处理
        // 这样可以确保在关闭弹窗时先触发退出动画
        const setAnimationState = (state: boolean) => {
          // 这个函数会在组件内部被调用，用于设置动画状态
          // 我们需要在外部保存这个引用
          if (this.componentRef.setAnimationState) {
            this.componentRef.setAnimationState(state);
          }
        };

        // 添加setAnimationState到componentRef
        this.componentRef.setAnimationState = setAnimationState;

        // 先关闭动画状态，触发退出动画
        if (this.componentRef.setAnimationState) {
          this.componentRef.setAnimationState(false);
        }

        // 延迟设置isOpen，等待退出动画完成
        setTimeout(() => {
          if (this.componentRef.setIsOpen) {
            this.componentRef.setIsOpen(isOpen);
          }
        }, 500);
      } else {
        // 如果是打开弹窗，直接设置isOpen
        this.componentRef.setIsOpen(isOpen);
      }
    }
  }

  /**
   * 设置确认回调函数
   * @param handler 确认回调函数
   */
  onConfirm(handler: () => void): void {
    this.confirmHandler = handler;
  }

  /**
   * 设置取消回调函数
   * @param handler 取消回调函数
   */
  onCancel(handler: () => void): void {
    this.cancelHandler = handler;
  }

  /**
   * 渲染组件
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const ConfirmDialog = () => {
      const dialogRef = useRef<HTMLDivElement>(null);

      // 创建动画工厂
      const animationFactory = createAnimationFactory();
      const fadeAnimation = animationFactory.createFadeAnimation('none', 400, 0, this.isOpen);

      // 使用useState来跟踪弹窗的实际可见性和动画状态
      const [isVisible, setIsVisible] = useState(this.isOpen);
      const [animationState, setAnimationState] = useState(this.isOpen);
      const [title, setTitle] = useState(this.title);
      const [message, setMessage] = useState(this.message);
      const [confirmText, setConfirmText] = useState(this.confirmText);
      const [cancelText, setCancelText] = useState(this.cancelText);
      const [confirmType, setConfirmType] = useState(this.confirmType);
      const [isOpen, setIsOpen] = useState(this.isOpen);

      // 注册组件引用，用于外部更新状态
      useEffect(() => {
        this.componentRef = {
          setTitle,
          setMessage,
          setConfirmText,
          setCancelText,
          setConfirmType,
          setIsOpen,
          setAnimationState
        };

        return () => {
          // 清理引用
          this.componentRef = {};
        };
      }, []);

      // 点击外部关闭弹窗
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
            // 先关闭动画状态，触发退出动画
            setAnimationState(false);

            // 延迟执行取消回调，等待退出动画完成
            setTimeout(() => {
              if (this.cancelHandler) {
                this.cancelHandler();
              }
            }, 500); // 等待退出动画完成
          }
        };

        if (isOpen) {
          document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isOpen]);

      // 当isOpen变化时更新isVisible和动画状态
      useEffect(() => {
        if (isOpen) {
          // 先显示弹窗，但保持动画状态为false
          setIsVisible(true);

          // 短暂延迟后开始动画
          const animationTimer = setTimeout(() => {
            setAnimationState(true);
          }, 50); // 非常短的延迟，让浏览器有时间渲染初始状态

          return () => clearTimeout(animationTimer);
        } else {
          // 先关闭动画状态
          setAnimationState(false);

          // 延迟隐藏弹窗，等待动画完成
          // 考虑到所有元素的动画时间和延迟，使用1000ms确保所有退出动画完成
          const timer = setTimeout(() => {
            setIsVisible(false);
          }, 1000); // 动画持续时间增加到1000ms，确保所有退出动画完成

          return () => clearTimeout(timer);
        }
      }, [isOpen]);

      // 添加动画关键帧到文档
      useEffect(() => {
        // 创建style元素
        const styleElement = document.createElement('style');
        styleElement.innerHTML = this.animationKeyframes;

        // 确保先移除可能存在的旧样式
        const existingStyles = document.querySelectorAll('style[data-confirm-dialog-animation]');
        existingStyles.forEach(el => el.remove());

        // 添加标识，便于后续清理
        styleElement.setAttribute('data-confirm-dialog-animation', 'true');
        document.head.appendChild(styleElement);

        // 清理函数
        return () => {
          if (document.head.contains(styleElement)) {
            document.head.removeChild(styleElement);
          }
        };
      }, []);

      // 获取确认按钮样式
      const getConfirmButtonStyle = () => {
        switch (confirmType) {
          case 'danger':
            return {
              backgroundColor: 'var(--color-danger)',
              color: 'white'
            };
          case 'warning':
            return {
              backgroundColor: 'var(--color-warning)',
              color: 'white'
            };
          case 'primary':
          default:
            return {
              backgroundColor: 'var(--color-primary)',
              color: 'white'
            };
        }
      };

      // 处理确认和取消事件
      const handleConfirm = () => {
        // 先关闭动画状态，触发退出动画
        setAnimationState(false);

        // 延迟执行确认回调，等待退出动画完成
        setTimeout(() => {
          if (this.confirmHandler) {
            this.confirmHandler();
          }
        }, 500); // 等待退出动画完成
      };

      const handleCancel = () => {
        // 先关闭动画状态，触发退出动画
        setAnimationState(false);

        // 延迟执行取消回调，等待退出动画完成
        setTimeout(() => {
          if (this.cancelHandler) {
            this.cancelHandler();
          }
        }, 500); // 等待退出动画完成
      };

      // 如果弹窗不可见，则不渲染
      if (!isVisible && !isOpen) return null;

      // 获取动画样式
      const fadeStyle = fadeAnimation.getStyle();

      return (
        <div
          className="fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-30 transition-all duration-500 backdrop-blur-sm"
          style={{
            ...fadeStyle,
            backdropFilter: animationState ? 'blur(8px)' : 'blur(0px)',
            transition: 'all 0.5s cubic-bezier(0.16, 1, 0.3, 1)'
          }}
        >
          {/* 弹窗容器 - 仅作为参考点 */}
          <div
            ref={dialogRef}
            className="w-80 max-w-full overflow-visible relative"
            style={{
              height: '220px',
              perspective: '1000px'
            }}
          >
            {/* 标题部分 - 从上方飞入/飞出 */}
            <div
              className="absolute rounded-t-xl overflow-hidden"
              style={{
                width: '100%',
                height: '50px',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
                top: '0',
                left: '0',
                opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                animation: animationState
                  ? 'dialogSlideInFromTop 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.15s forwards'
                  : isVisible && !animationState
                    ? 'dialogSlideOutToTop 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.3s forwards'
                    : 'none',
                zIndex: 10
              }}
            >
              <div className="w-full h-full flex items-center justify-center">
                <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>
                  {title}
                </h2>
              </div>
            </div>

            {/* 消息部分 - 从右侧飞入/飞出 */}
            <div
              className="absolute overflow-hidden"
              style={{
                width: '100%',
                height: '120px',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                top: '50px',
                left: '0',
                opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                animation: animationState
                  ? 'dialogSlideInFromRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s forwards'
                  : isVisible && !animationState
                    ? 'dialogSlideOutToRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s forwards'
                    : 'none',
                zIndex: 10
              }}
            >
              <div className="w-full h-full flex items-center justify-center p-4">
                <p className="text-base" style={{ color: 'var(--color-text-primary)' }}>
                  {message}
                </p>
              </div>
            </div>

            {/* 取消按钮 - 从左下方飞入/飞出 */}
            <div
              className="absolute rounded-bl-xl overflow-hidden"
              style={{
                width: '50%',
                height: '50px',
                backgroundColor: 'rgba(240, 240, 240, 0.95)',
                backdropFilter: 'blur(5px)',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
                bottom: '0',
                left: '0',
                opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                animation: animationState
                  ? 'dialogSlideInFromBottomLeft 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.25s forwards'
                  : isVisible && !animationState
                    ? 'dialogSlideOutToBottomLeft 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s forwards'
                    : 'none',
                zIndex: 10
              }}
            >
              <button
                onClick={handleCancel}
                className="w-full h-full flex items-center justify-center font-medium"
                style={{ color: 'var(--color-text-primary)' }}
              >
                {cancelText}
              </button>
            </div>

            {/* 确认按钮 - 从右下方飞入/飞出 */}
            <div
              className="absolute rounded-br-xl overflow-hidden"
              style={{
                width: '50%',
                height: '50px',
                ...getConfirmButtonStyle(),
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
                bottom: '0',
                right: '0',
                opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                animation: animationState
                  ? 'dialogSlideInFromBottomRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.3s forwards'
                  : isVisible && !animationState
                    ? 'dialogSlideOutToBottomRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s forwards'
                    : 'none',
                zIndex: 10
              }}
            >
              <button
                onClick={handleConfirm}
                className="w-full h-full flex items-center justify-center font-medium"
                style={{ color: 'white' }}
              >
                {confirmText}
              </button>
            </div>

            {/* 内部结构 - 根据动画状态显示或隐藏 */}
            <>
                {/* 左侧边框 - 从左侧飞入/飞出 */}
                <div
                  className="absolute"
                  style={{
                    width: '5px',
                    height: '120px',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '4px 0 15px rgba(0, 0, 0, 0.1)',
                    top: '50px',
                    left: '0',
                    opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                    animation: animationState
                      ? 'dialogSlideInFromLeft 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.05s forwards'
                      : isVisible && !animationState
                        ? 'dialogSlideOutToLeft 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0s forwards'
                        : 'none',
                    zIndex: 9
                  }}
                />

                {/* 右侧边框 - 从右侧飞入/飞出 */}
                <div
                  className="absolute"
                  style={{
                    width: '5px',
                    height: '120px',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '-4px 0 15px rgba(0, 0, 0, 0.1)',
                    top: '50px',
                    right: '0',
                    opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                    animation: animationState
                      ? 'dialogSlideInFromRight 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.05s forwards'
                      : isVisible && !animationState
                        ? 'dialogSlideOutToRight 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0s forwards'
                        : 'none',
                    zIndex: 9
                  }}
                />

                {/* 中间分隔线 - 从中间展开/收缩 */}
                <div
                  className="absolute"
                  style={{
                    width: animationState || (isVisible && !animationState) ? '100%' : '0%',
                    height: '2px',
                    backgroundColor: 'rgba(230, 230, 230, 0.95)',
                    top: '170px',
                    left: '0',
                    opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                    animation: animationState
                      ? 'dialogExpandWidth 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s forwards'
                      : isVisible && !animationState
                        ? 'dialogCollapseWidth 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0s forwards'
                        : 'none',
                    zIndex: 11
                  }}
                />

                {/* 按钮分隔线 - 从中间展开/收缩 */}
                <div
                  className="absolute"
                  style={{
                    width: '2px',
                    height: animationState || (isVisible && !animationState) ? '50px' : '0px',
                    backgroundColor: 'rgba(230, 230, 230, 0.95)',
                    bottom: '0',
                    left: '50%',
                    opacity: animationState || (isVisible && !animationState) ? 1 : 0,
                    animation: animationState
                      ? 'dialogExpandHeight 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s forwards'
                      : isVisible && !animationState
                        ? 'dialogCollapseHeight 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0s forwards'
                        : 'none',
                    zIndex: 11
                  }}
                />
            </>
          </div>
        </div>
      );
    };

    return <ConfirmDialog />;
  }
}
