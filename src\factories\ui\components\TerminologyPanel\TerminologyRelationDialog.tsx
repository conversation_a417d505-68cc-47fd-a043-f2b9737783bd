"use client";

import React, { useState, useEffect } from 'react';
import { Terminology } from '@/lib/db/dexie';

interface TerminologyRelationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (relatedTerminologyIds: string[]) => void;
  terminology: Terminology | null;
  allTerminologies: Terminology[];
}

/**
 * 术语关联对话框组件
 */
export const TerminologyRelationDialog: React.FC<TerminologyRelationDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  terminology,
  allTerminologies
}) => {
  // 已选择的关联术语ID列表
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);
  
  // 搜索查询
  const [searchQuery, setSearchQuery] = useState('');
  
  // 过滤后的术语列表
  const [filteredTerminologies, setFilteredTerminologies] = useState<Terminology[]>([]);

  // 初始化已选择的关联术语
  useEffect(() => {
    if (terminology && terminology.relatedTerminologyIds) {
      setSelectedTerminologyIds(terminology.relatedTerminologyIds);
    } else {
      setSelectedTerminologyIds([]);
    }
  }, [terminology]);

  // 过滤术语列表
  useEffect(() => {
    if (!allTerminologies || !terminology) return;
    
    // 排除当前术语
    let filtered = allTerminologies.filter(t => t.id !== terminology.id);
    
    // 应用搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(query) || 
        (t.description && t.description.toLowerCase().includes(query))
      );
    }
    
    setFilteredTerminologies(filtered);
  }, [allTerminologies, terminology, searchQuery]);

  // 处理术语选择
  const handleTerminologySelection = (terminologyId: string) => {
    setSelectedTerminologyIds(prev => {
      if (prev.includes(terminologyId)) {
        return prev.filter(id => id !== terminologyId);
      } else {
        return [...prev, terminologyId];
      }
    });
  };

  // 处理保存
  const handleSave = () => {
    onSave(selectedTerminologyIds);
    onClose();
  };

  // 获取术语类别的中文名称
  const getCategoryLabel = (category: string): string => {
    const categoryMap: Record<string, string> = {
      'item': '物品/道具',
      'skill': '技能/能力',
      'organization': '组织/势力',
      'location': '地点/区域',
      'concept': '概念/规则',
      'event': '事件/历史',
      'system': '系统/机制',
      'creature': '生物/种族',
      'other': '其他'
    };
    
    return categoryMap[category] || category;
  };

  if (!isOpen || !terminology) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[600px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>
            关联术语 - {terminology.name}
          </h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          {/* 搜索框 */}
          <div className="mb-4 relative">
            <input
              type="text"
              placeholder="搜索术语..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                borderColor: 'rgba(139, 69, 19, 0.2)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
              }}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          {/* 已选择的术语 */}
          {selectedTerminologyIds.length > 0 && (
            <div className="mb-4">
              <h3 className="text-sm font-medium mb-2 text-gray-700">已选择的术语</h3>
              <div className="flex flex-wrap gap-2">
                {selectedTerminologyIds.map(id => {
                  const term = allTerminologies.find(t => t.id === id);
                  if (!term) return null;
                  
                  return (
                    <div
                      key={id}
                      className="flex items-center bg-blue-50 px-3 py-1 rounded-full"
                      style={{
                        backgroundColor: 'rgba(139, 69, 19, 0.1)',
                        color: 'var(--color-primary)'
                      }}
                    >
                      <span className="text-sm">{term.name}</span>
                      <button
                        type="button"
                        onClick={() => handleTerminologySelection(id)}
                        className="ml-2 text-gray-500 hover:text-red-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          
          {/* 术语列表 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden"
            style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
          >
            {filteredTerminologies.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                没有找到匹配的术语
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {filteredTerminologies.map(term => (
                  <div
                    key={term.id}
                    className={`p-3 flex items-center hover:bg-gray-50 cursor-pointer transition-colors ${
                      selectedTerminologyIds.includes(term.id || '') ? 'bg-blue-50' : ''
                    }`}
                    style={
                      selectedTerminologyIds.includes(term.id || '')
                        ? { backgroundColor: 'rgba(139, 69, 19, 0.1)' }
                        : {}
                    }
                    onClick={() => term.id && handleTerminologySelection(term.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedTerminologyIds.includes(term.id || '')}
                      onChange={() => {}} // 通过父元素的onClick处理
                      className="mr-3 h-4 w-4"
                      style={{ accentColor: 'var(--color-primary)' }}
                    />
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="font-medium" style={{ color: 'var(--color-primary)' }}>{term.name}</h4>
                        <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-600">
                          {getCategoryLabel(term.category || 'other')}
                        </span>
                      </div>
                      {term.description && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">{term.description}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'rgba(210, 180, 140, 0.2)',
              color: 'var(--color-primary)',
              border: '1px solid var(--color-secondary)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
            }}
            onClick={handleSave}
          >
            保存关联
          </button>
        </div>
      </div>
    </div>
  );
};
