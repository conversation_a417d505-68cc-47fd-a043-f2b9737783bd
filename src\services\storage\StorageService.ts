import { userSettingsService, userStateService } from './DataService';
import { historyService } from './HistoryService';
import { localStorageMigrator } from './LocalStorageMigrator';

/**
 * 统一存储服务 - 提供简化的API接口
 * 自动处理localStorage迁移和数据访问
 */
export class StorageService {
  private migrationPromise: Promise<void> | null = null;

  /**
   * 初始化存储服务（自动迁移）
   */
  async initialize(): Promise<void> {
    if (this.migrationPromise) {
      return this.migrationPromise;
    }

    this.migrationPromise = this.performMigration();
    return this.migrationPromise;
  }

  /**
   * 执行迁移
   */
  private async performMigration(): Promise<void> {
    try {
      const result = await localStorageMigrator.migrateAll();
      
      if (result.success) {
        console.log(`✅ 存储服务初始化完成，迁移了 ${result.migratedCount} 条数据`);
      } else {
        console.warn(`⚠️ 存储服务初始化完成，但有 ${result.errors.length} 个错误`);
        result.errors.forEach(error => console.error('迁移错误:', error));
      }
    } catch (error) {
      console.error('❌ 存储服务初始化失败:', error);
    }
  }

  // ==================== 用户设置相关 ====================

  /**
   * 获取API设置
   */
  async getAPISettings(): Promise<any> {
    await this.initialize();
    return userSettingsService.get('api', 'settings', {});
  }

  /**
   * 保存API设置
   */
  async setAPISettings(settings: any): Promise<void> {
    await this.initialize();
    return userSettingsService.set('api', 'settings', settings);
  }

  /**
   * 获取宠物偏好
   */
  async getPetPreferences(): Promise<any> {
    await this.initialize();
    return userSettingsService.get('pet', 'preferences', {
      petType: 'cat',
      animationLevel: 'medium',
      showPet: true
    });
  }

  /**
   * 保存宠物偏好
   */
  async setPetPreferences(preferences: any): Promise<void> {
    await this.initialize();
    return userSettingsService.set('pet', 'preferences', preferences);
  }

  /**
   * 获取AI标注偏好
   */
  async getAnnotationPreferences(): Promise<any> {
    await this.initialize();
    return userSettingsService.get('annotation', 'preferences', {});
  }

  /**
   * 保存AI标注偏好
   */
  async setAnnotationPreferences(preferences: any): Promise<void> {
    await this.initialize();
    return userSettingsService.set('annotation', 'preferences', preferences);
  }

  /**
   * 获取双AI配置
   */
  async getDualAIConfig(): Promise<any> {
    await this.initialize();
    return userSettingsService.get('dual-ai', 'config', {});
  }

  /**
   * 保存双AI配置
   */
  async setDualAIConfig(config: any): Promise<void> {
    await this.initialize();
    return userSettingsService.set('dual-ai', 'config', config);
  }

  // ==================== 用户状态相关 ====================

  /**
   * 获取AI助手选择的章节
   */
  async getSelectedChapters(bookId: string): Promise<string[]> {
    await this.initialize();
    return userStateService.get(bookId, 'assistant', 'selected-chapters', []);
  }

  /**
   * 保存AI助手选择的章节
   */
  async setSelectedChapters(bookId: string, chapterIds: string[]): Promise<void> {
    await this.initialize();
    return userStateService.set(bookId, 'assistant', 'selected-chapters', chapterIds);
  }

  /**
   * 获取AI助手选择的人物
   */
  async getSelectedCharacters(bookId: string): Promise<string[]> {
    await this.initialize();
    return userStateService.get(bookId, 'assistant', 'selected-characters', []);
  }

  /**
   * 保存AI助手选择的人物
   */
  async setSelectedCharacters(bookId: string, characterIds: string[]): Promise<void> {
    await this.initialize();
    return userStateService.set(bookId, 'assistant', 'selected-characters', characterIds);
  }

  /**
   * 获取选择的框架
   */
  async getSelectedFrameworks(bookId: string): Promise<any[]> {
    await this.initialize();
    return userStateService.get(bookId, 'framework', 'selected-frameworks', []);
  }

  /**
   * 保存选择的框架
   */
  async setSelectedFrameworks(bookId: string, frameworks: any[]): Promise<void> {
    await this.initialize();
    return userStateService.set(bookId, 'framework', 'selected-frameworks', frameworks);
  }

  /**
   * 获取折叠的节点
   */
  async getCollapsedNodes(bookId: string): Promise<string[]> {
    await this.initialize();
    return userStateService.get(bookId, 'layout', 'collapsed-nodes', []);
  }

  /**
   * 保存折叠的节点
   */
  async setCollapsedNodes(bookId: string, nodeIds: string[]): Promise<void> {
    await this.initialize();
    return userStateService.set(bookId, 'layout', 'collapsed-nodes', nodeIds);
  }

  // ==================== 历史记录相关 ====================

  /**
   * 添加书名生成历史
   */
  async addTitleHistory(title: any): Promise<void> {
    await this.initialize();
    return historyService.add(null, 'title', title);
  }

  /**
   * 获取书名生成历史
   */
  async getTitleHistory(limit: number = 50): Promise<any[]> {
    await this.initialize();
    const records = await historyService.getHistory(null, 'title', limit);
    return records.map(r => r.content);
  }

  /**
   * 添加简介生成历史
   */
  async addSynopsisHistory(synopsis: any): Promise<void> {
    await this.initialize();
    return historyService.add(null, 'synopsis', synopsis);
  }

  /**
   * 获取简介生成历史
   */
  async getSynopsisHistory(limit: number = 50): Promise<any[]> {
    await this.initialize();
    const records = await historyService.getHistory(null, 'synopsis', limit);
    return records.map(r => r.content);
  }

  /**
   * 添加写作历史
   */
  async addWritingHistory(bookId: string, content: any): Promise<void> {
    await this.initialize();
    return historyService.add(bookId, 'writing', content);
  }

  /**
   * 获取写作历史
   */
  async getWritingHistory(bookId: string, limit: number = 50): Promise<any[]> {
    await this.initialize();
    const records = await historyService.getHistory(bookId, 'writing', limit);
    return records.map(r => r.content);
  }

  /**
   * 添加改写历史
   */
  async addRewriteHistory(bookId: string, content: any): Promise<void> {
    await this.initialize();
    return historyService.add(bookId, 'rewrite', content);
  }

  /**
   * 获取改写历史
   */
  async getRewriteHistory(bookId: string, limit: number = 50): Promise<any[]> {
    await this.initialize();
    const records = await historyService.getHistory(bookId, 'rewrite', limit);
    return records.map(r => r.content);
  }

  // ==================== 通用方法 ====================

  /**
   * 清理历史记录
   */
  async cleanupHistory(bookId: string | null, type: string, keepCount: number = 100): Promise<void> {
    await this.initialize();
    return historyService.cleanup(bookId, type, keepCount);
  }

  /**
   * 导出所有数据
   */
  async exportAllData(): Promise<{
    userSettings: any[];
    userStates: any[];
    historyRecords: any[];
  }> {
    await this.initialize();
    
    // 这里需要实现导出逻辑
    // 暂时返回空数据
    return {
      userSettings: [],
      userStates: [],
      historyRecords: []
    };
  }

  /**
   * 强制重新迁移
   */
  async forceMigrate(): Promise<void> {
    this.migrationPromise = null;
    const result = await localStorageMigrator.forceMigrate();
    console.log('强制迁移结果:', result);
  }
}

// 导出单例实例
export const storageService = new StorageService();
