"use client";

import React from 'react';
import { CircleButtonSize } from '@/factories/ui/interfaces/ICircleButtonComponent';

interface CircleButtonProps {
  icon: React.ReactNode;
  text?: string;
  color?: string;
  size?: CircleButtonSize;
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
}

/**
 * 圆形按钮适配器组件
 */
export const CircleButton: React.FC<CircleButtonProps> = ({
  icon,
  text = '',
  color = 'var(--color-info)',
  size = 'medium',
  disabled = false,
  onClick,
  className
}) => {
  // 获取按钮尺寸
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return {
          width: '32px',
          height: '32px',
          fontSize: '0.875rem',
        };
      case 'medium':
        return {
          width: '40px',
          height: '40px',
          fontSize: '1rem',
        };
      case 'large':
        return {
          width: '48px',
          height: '48px',
          fontSize: '1.125rem',
        };
      default:
        return {
          width: '40px',
          height: '40px',
          fontSize: '1rem',
        };
    }
  };

  // 处理点击事件
  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const buttonSize = getButtonSize();
  const combinedClassName = `rounded-full flex items-center justify-center transition-all duration-200 ${className || ''}`;

  return (
    <button
      className={combinedClassName}
      style={{
        ...buttonSize,
        backgroundColor: disabled ? `${color}80` : color,
        color: 'white',
        border: 'none',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.7 : 1,
      }}
      onClick={handleClick}
      disabled={disabled}
      title={text}
    >
      {icon}
    </button>
  );
};


