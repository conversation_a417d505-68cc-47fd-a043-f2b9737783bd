"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { CategoryPrompt } from '../../../types/ai-persona';

interface PromptCardProps {
  prompt: CategoryPrompt;
  onEdit: (prompt: CategoryPrompt) => void;
  onDelete: (promptId: string) => void;
  onUse?: (prompt: CategoryPrompt) => void;
}

/**
 * 提示词卡片组件
 * 用于显示单个提示词的信息，支持编辑、删除、使用等操作
 */
const PromptCard: React.FC<PromptCardProps> = ({
  prompt,
  onEdit,
  onDelete,
  onUse
}) => {

  // 获取显示名称：优先显示name，如果name为空则显示content前20字符
  const getDisplayName = () => {
    if (prompt.name && prompt.name.trim()) {
      return prompt.name;
    }
    return prompt.content.length > 20
      ? prompt.content.substring(0, 20) + '...'
      : prompt.content;
  };

  // 获取内容预览：如果有name则显示完整content，否则显示剩余部分
  const getContentPreview = () => {
    if (prompt.name && prompt.name.trim()) {
      return prompt.content.length > 60
        ? prompt.content.substring(0, 60) + '...'
        : prompt.content;
    }
    // 如果没有name，则显示20字符后的内容
    if (prompt.content.length > 20) {
      const remaining = prompt.content.substring(20);
      return remaining.length > 40
        ? remaining.substring(0, 40) + '...'
        : remaining;
    }
    return '';
  };

  // 处理编辑按钮点击
  const handleEditClick = () => {
    onEdit(prompt);
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="group p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-750 transition-all duration-200 relative"
    >
      {/* 显示模式 */}
      <div className="flex flex-col h-full">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                {getDisplayName()}
              </h4>
              {getContentPreview() && (
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                  {getContentPreview()}
                </p>
              )}
            </div>

            {/* 操作按钮组 */}
            <div className="flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
              {onUse && (
                <button
                  onClick={() => onUse(prompt)}
                  className="p-1 text-purple-500 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded transition-colors"
                  title="使用此提示词"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              )}

              <button
                onClick={handleEditClick}
                className="p-1 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors"
                title="编辑提示词"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>

              <button
                onClick={() => onDelete(prompt.id)}
                className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
                title="删除提示词"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>

          {/* 底部信息 */}
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mt-auto pt-2">
            <span>
              {prompt.createdAt ? new Date(prompt.createdAt).toLocaleDateString() : ''}
            </span>
            <span className="text-purple-600 dark:text-purple-400">
              #{prompt.order + 1}
            </span>
          </div>
        </div>
    </motion.div>
  );
};

export default PromptCard;
