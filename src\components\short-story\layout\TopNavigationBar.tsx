"use client";

import React from 'react';
import { CreationStep } from '../stores/shortStoryStore';

interface TopNavigationBarProps {
  currentStep: CreationStep;
  isGenerating: boolean;
  onToggleRightPanel: () => void;
}

/**
 * 顶部导航栏组件
 * 显示创作进度、步骤导航和控制按钮
 */
export const TopNavigationBar: React.FC<TopNavigationBarProps> = ({
  currentStep,
  isGenerating,
  onToggleRightPanel
}) => {
  // 简化的步骤配置
  const currentStepInfo = {
    key: 'editing',
    label: '短篇编辑',
    icon: '✍️',
    description: '自由编辑短篇内容'
  };

  // 固定进度为100%（编辑模式）
  const progressPercent = 100;

  return (
    <div className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
      {/* 左侧：标题和进度 */}
      <div className="flex items-center space-x-6">
        {/* 标题 */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">AI</span>
          </div>
          <h1 className="text-lg font-semibold text-gray-800">短篇创作工作区</h1>
        </div>

        {/* 进度指示器 */}
        <div className="flex items-center space-x-4">
          {/* 进度条 */}
          <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-out"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
          
          {/* 当前步骤信息 */}
          <div className="flex items-center space-x-2">
            <span className="text-lg">{currentStepInfo.icon}</span>
            <div>
              <div className="text-sm font-medium text-gray-800">
                {currentStepInfo.label}
              </div>
              <div className="text-xs text-gray-500">
                {currentStepInfo.description}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 中间：模式指示 */}
      <div className="hidden lg:flex items-center space-x-4">
        <div className="flex items-center space-x-2 px-4 py-2 bg-green-50 rounded-lg border border-green-200">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-green-700">自由编辑模式</span>
        </div>

        <div className="text-sm text-gray-500">
          直接编辑 • 实时保存 • 一键发送
        </div>
      </div>

      {/* 右侧：控制按钮 */}
      <div className="flex items-center space-x-3">
        {/* 生成状态指示器 */}
        {isGenerating && (
          <div className="flex items-center space-x-2 px-3 py-1.5 bg-blue-50 rounded-lg">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-blue-600 font-medium">AI创作中...</span>
          </div>
        )}

        {/* 节奏面板切换按钮 */}
        <button
          onClick={onToggleRightPanel}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          title="切换节奏控制面板"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </button>

        {/* 更多操作菜单 */}
        <div className="relative">
          <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};
