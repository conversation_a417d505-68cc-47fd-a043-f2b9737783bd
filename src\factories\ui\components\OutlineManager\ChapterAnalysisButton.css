/* 章节分析按钮样式 */
.chapter-analysis-button {
  /* 按钮基础样式 */
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  cursor: pointer;

  /* 阴影效果 */
  box-shadow:
    0 4px 12px rgba(139, 92, 246, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Panel中的按钮样式 */
.chapter-analysis-panel {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
}

/* 悬停效果 */
.chapter-analysis-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 6px 20px rgba(139, 92, 246, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.chapter-analysis-button:active {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

/* 禁用状态 */
.chapter-analysis-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.chapter-analysis-button.disabled:hover {
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 图标样式 */
.chapter-analysis-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.chapter-analysis-button:hover .chapter-analysis-icon {
  transform: rotate(5deg);
}

/* 活跃状态 */
.chapter-analysis-button.active {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  box-shadow:
    0 6px 20px rgba(139, 92, 246, 0.6),
    0 4px 8px rgba(0, 0, 0, 0.2);
  animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% {
    box-shadow:
      0 6px 20px rgba(139, 92, 246, 0.6),
      0 4px 8px rgba(0, 0, 0, 0.2);
  }
  100% {
    box-shadow:
      0 8px 25px rgba(139, 92, 246, 0.8),
      0 6px 12px rgba(0, 0, 0, 0.25);
  }
}

/* 活跃状态指示器 */
.chapter-analysis-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10B981;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chapter-analysis-indicator .pulse-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  animation: pulse 2s infinite;
}

/* 悬停提示 */
.chapter-analysis-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.chapter-analysis-button:hover .chapter-analysis-tooltip {
  opacity: 1;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 按钮组合样式 - 当与其他按钮一起使用时 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.button-group .chapter-analysis-button,
.button-group .framework-extract-button,
.button-group .assistant-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chapter-analysis-button {
    width: 44px;
    height: 44px;
  }
  
  .chapter-analysis-icon {
    width: 20px;
    height: 20px;
  }
  
  .chapter-analysis-tooltip {
    font-size: 11px;
    padding: 3px 6px;
  }
}

/* 加载状态 */
.chapter-analysis-button.loading {
  pointer-events: none;
}

.chapter-analysis-button.loading .chapter-analysis-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
