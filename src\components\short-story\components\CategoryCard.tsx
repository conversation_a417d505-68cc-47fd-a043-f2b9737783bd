"use client";

import React, { useState, useEffect, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PersonaCategory, CategoryPrompt } from '../../../types/ai-persona';

interface CategoryCardProps {
  category: PersonaCategory;
  onUpdateCategory: (updates: Partial<PersonaCategory>) => void;
  onDeleteCategory: () => void;
  onAddPrompt: (content: string) => void;
  onUpdatePrompt: (promptId: string, content: string) => void;
  onDeletePrompt: (promptId: string) => void;
  onUsePrompt?: (prompt: CategoryPrompt) => void;
  index: number;
}

const CategoryCard = forwardRef<HTMLDivElement, CategoryCardProps>(({
  category,
  onUpdateCategory,
  onDeleteCategory,
  onAddPrompt,
  onUpdatePrompt,
  onDeletePrompt,
  onUsePrompt,
  index
}, ref) => {
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [newPromptContent, setNewPromptContent] = useState('');
  const [showAddPrompt, setShowAddPrompt] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null);
  const [editingPromptContent, setEditingPromptContent] = useState('');

  // 同步category.name到editedName状态
  useEffect(() => {
    setEditedName(category.name);
  }, [category.name]);

  // 处理分类名称编辑
  const handleNameEdit = () => {
    setIsEditingName(true);
    setEditedName(category.name);
  };

  const handleNameSave = () => {
    if (editedName.trim() && editedName.trim() !== category.name) {
      onUpdateCategory({ name: editedName.trim() });
    }
    setIsEditingName(false);
  };

  const handleNameCancel = () => {
    setEditedName(category.name);
    setIsEditingName(false);
  };

  const handleNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSave();
    } else if (e.key === 'Escape') {
      handleNameCancel();
    }
  };

  // 处理添加提示词
  const handleAddPromptSubmit = () => {
    if (newPromptContent.trim()) {
      onAddPrompt(newPromptContent.trim());
      setNewPromptContent('');
      setShowAddPrompt(false);
    }
  };

  const handleAddPromptCancel = () => {
    setNewPromptContent('');
    setShowAddPrompt(false);
  };

  const handleAddPromptKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddPromptSubmit();
    } else if (e.key === 'Escape') {
      handleAddPromptCancel();
    }
  };

  // 处理提示词编辑
  const handlePromptEdit = (prompt: CategoryPrompt) => {
    setEditingPromptId(prompt.id);
    setEditingPromptContent(prompt.content);
  };

  const handlePromptEditSave = (promptId: string) => {
    if (editingPromptContent.trim() && editingPromptContent.trim() !== category.prompts.find(p => p.id === promptId)?.content) {
      onUpdatePrompt(promptId, editingPromptContent.trim());
    }
    setEditingPromptId(null);
    setEditingPromptContent('');
  };

  const handlePromptEditCancel = () => {
    setEditingPromptId(null);
    setEditingPromptContent('');
  };

  const handlePromptEditKeyDown = (e: React.KeyboardEvent, promptId: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handlePromptEditSave(promptId);
    } else if (e.key === 'Escape') {
      handlePromptEditCancel();
    }
  };

  // 格式化创建时间
  const formatDate = (date: Date | string): string => {
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    return validDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      ref={ref}
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{
        duration: 0.2,
        delay: index * 0.05
      }}
      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
    >
      {/* 卡片头部 */}
      <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          {/* 分类名称 */}
          <div className="flex-1">
            {isEditingName ? (
              <input
                type="text"
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                onBlur={handleNameSave}
                onKeyDown={handleNameKeyDown}
                className="w-full px-2 py-1 text-lg font-semibold bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-purple-500"
                autoFocus
              />
            ) : (
              <button
                onClick={handleNameEdit}
                className="text-left text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
              >
                {category.name}
              </button>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2 ml-4">
            {/* 添加提示词按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowAddPrompt(true)}
              className="p-2 text-green-500 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-full transition-colors"
              title="添加提示词"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </motion.button>

            {/* 展开/折叠按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-full transition-colors"
              title={isExpanded ? "折叠" : "展开"}
            >
              <motion.svg
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </motion.button>

            {/* 删除分类按钮 */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onDeleteCategory}
              className="p-2 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-full transition-colors"
              title="删除分类"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </motion.button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-xs font-medium">
              {category.prompts.length} 个提示词
            </span>
            <span className="text-gray-500 dark:text-gray-400 text-xs">
              创建于 {formatDate(category.createdAt)}
            </span>
          </div>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="p-4">
        {/* 添加提示词表单 */}
        <AnimatePresence>
          {showAddPrompt && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
            >
              <div className="space-y-3">
                <textarea
                  value={newPromptContent}
                  onChange={(e) => setNewPromptContent(e.target.value)}
                  onKeyDown={handleAddPromptKeyDown}
                  placeholder="输入提示词内容..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm resize-none"
                  rows={3}
                  autoFocus
                />
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={handleAddPromptCancel}
                    className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleAddPromptSubmit}
                    disabled={!newPromptContent.trim()}
                    className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    添加
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 提示词预览 */}
        {category.prompts.length === 0 ? (
          <div className="text-center py-6">
            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">暂无提示词</p>
            <button
              onClick={() => setShowAddPrompt(true)}
              className="text-purple-600 dark:text-purple-400 text-sm hover:underline"
            >
              添加第一个提示词
            </button>
          </div>
        ) : (
          <div className="space-y-2">
            {/* 调试信息 */}
            {console.log('CategoryCard渲染:', {
              categoryName: category.name,
              totalPrompts: category.prompts.length,
              isExpanded: isExpanded,
              sliceEnd: isExpanded ? undefined : 3,
              actualShown: category.prompts.slice(0, isExpanded ? undefined : 3).length
            })}

            {/* 显示前3个提示词 */}
            {category.prompts.slice(0, isExpanded ? undefined : 3).map((prompt, promptIndex) => (
              <div
                key={prompt.id}
                className="group p-2 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {editingPromptId === prompt.id ? (
                      <textarea
                        value={editingPromptContent}
                        onChange={(e) => setEditingPromptContent(e.target.value)}
                        onKeyDown={(e) => handlePromptEditKeyDown(e, prompt.id)}
                        onBlur={() => handlePromptEditSave(prompt.id)}
                        className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-600 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                        rows={2}
                        autoFocus
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-gray-100">
                        {prompt.content}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    {/* 使用提示词按钮 */}
                    {onUsePrompt && (
                      <button
                        onClick={() => onUsePrompt(prompt)}
                        className="p-1 text-purple-500 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded transition-colors"
                        title="使用此提示词"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>
                    )}

                    {/* 编辑按钮 */}
                    {editingPromptId === prompt.id ? (
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handlePromptEditSave(prompt.id)}
                          className="p-1 text-green-500 hover:bg-green-100 dark:hover:bg-green-900/20 rounded transition-colors"
                          title="保存"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                        <button
                          onClick={handlePromptEditCancel}
                          className="p-1 text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-900/20 rounded transition-colors"
                          title="取消"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handlePromptEdit(prompt)}
                        className="p-1 text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors"
                        title="编辑"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    )}

                    {/* 删除按钮 */}
                    <button
                      onClick={() => {
                        if (window.confirm('确定要删除这个提示词吗？')) {
                          onDeletePrompt(prompt.id);
                        }
                      }}
                      className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors"
                      title="删除"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {/* 展开/折叠按钮 */}
            {console.log('展开按钮条件检查:', {
              promptsLength: category.prompts.length,
              shouldShow: category.prompts.length > 3,
              isExpanded: isExpanded
            })}
            {category.prompts.length > 3 && (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('展开按钮点击，当前状态:', isExpanded, '提示词数量:', category.prompts.length);
                  const newState = !isExpanded;
                  console.log('设置新状态:', newState);
                  setIsExpanded(newState);
                }}
                className="w-full py-2 text-sm text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors flex items-center justify-center space-x-1"
              >
                <span>
                  {isExpanded ? '收起' : `显示全部 ${category.prompts.length} 个提示词`}
                </span>
                <svg
                  className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
});

CategoryCard.displayName = 'CategoryCard';

export default CategoryCard;
