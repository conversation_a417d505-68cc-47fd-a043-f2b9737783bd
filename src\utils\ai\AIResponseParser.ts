"use client";

import { DialogueItem, PlotPoint } from '@/factories/ui/types/outline';

/**
 * AI响应解析器
 * 用于处理AI返回的文本并解析为JSON对象，以及验证和修正数据字段
 *
 * 简化版实现：直接提取完整JSON + 字段验证修正
 */
export class AIResponseParser {
  /**
   * 解析AI响应文本为JSON对象
   * @param response AI响应文本
   * @param defaultValue 解析失败时返回的默认值
   * @returns 解析后的JSON对象
   */
  static parseJSON<T>(response: string, defaultValue: T): T {
    // 如果响应为空，直接返回默认值
    if (!response || typeof response !== 'string') {
      return defaultValue;
    }

    try {
      // 预处理响应文本，移除可能干扰JSON解析的内容
      let cleanedResponse = this.preprocessResponse(response);

      // 记录原始响应和预处理后的响应，便于调试
      console.log('原始响应:', response);
      console.log('预处理后的响应:', cleanedResponse);

      // 1. 优先提取Markdown代码块中的JSON（特别关注```json标记）
      const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/i;
      const jsonBlockMatch = cleanedResponse.match(jsonBlockRegex);

      if (jsonBlockMatch && jsonBlockMatch[1]) {
        const blockContent = jsonBlockMatch[1].trim();
        try {
          return JSON.parse(blockContent);
        } catch (e) {
          // 代码块中的内容不是有效的JSON，继续处理
          console.log('代码块中的内容不是有效的JSON，继续尝试其他方法');
        }
      }

      // 2. 尝试直接解析整个文本
      try {
        return JSON.parse(cleanedResponse);
      } catch (e) {
        // 不是有效的JSON，继续处理
        console.log('整个文本不是有效的JSON，继续尝试其他方法');
      }

      // 3. 提取文本中的第一个完整JSON对象
      const firstBrace = cleanedResponse.indexOf('{');
      const lastBrace = cleanedResponse.lastIndexOf('}');

      if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
        const jsonCandidate = cleanedResponse.substring(firstBrace, lastBrace + 1);
        try {
          return JSON.parse(jsonCandidate);
        } catch (e) {
          // JSON解析失败，尝试修复常见语法错误
          console.log('提取的JSON对象解析失败，尝试修复语法错误');
          const fixedJson = this.fixJSONSyntaxErrors(jsonCandidate);
          try {
            return JSON.parse(fixedJson);
          } catch (e2) {
            const errorMsg = this.getDetailedErrorMessage(e2, fixedJson);
            console.log('修复后的JSON仍然解析失败:', errorMsg);
          }
        }
      }

      // 4. 尝试从原始响应中直接提取JSON代码块，跳过预处理步骤
      // 这是为了处理预处理可能意外移除了有效内容的情况
      const originalJsonBlockMatch = response.match(jsonBlockRegex);
      if (originalJsonBlockMatch && originalJsonBlockMatch[1]) {
        const blockContent = originalJsonBlockMatch[1].trim();
        try {
          return JSON.parse(blockContent);
        } catch (e) {
          console.log('原始响应中的代码块解析失败');
        }
      }

      // 5. 尝试从原始响应中提取JSON对象
      const originalFirstBrace = response.indexOf('{');
      const originalLastBrace = response.lastIndexOf('}');

      if (originalFirstBrace !== -1 && originalLastBrace !== -1 && originalFirstBrace < originalLastBrace) {
        const jsonCandidate = response.substring(originalFirstBrace, originalLastBrace + 1);
        try {
          return JSON.parse(jsonCandidate);
        } catch (e) {
          console.log('原始响应中的JSON对象解析失败');
        }
      }

      // 6. 所有尝试都失败，返回默认值
      const errorMsg = this.getDetailedErrorMessage(null, response);
      console.warn('无法解析AI响应为JSON:', errorMsg);
      console.warn('原始响应内容:', response.substring(0, 500));
      return defaultValue;
    } catch (error) {
      const errorMsg = this.getDetailedErrorMessage(error, response);
      console.error('解析AI响应时发生异常:', errorMsg);
      console.error('原始响应内容:', response.substring(0, 500));
      return defaultValue;
    }
  }

  /**
   * 修复JSON语法错误
   * @param jsonText 包含语法错误的JSON文本
   * @returns 修复后的JSON文本
   */
  private static fixJSONSyntaxErrors(jsonText: string): string {
    let fixed = jsonText;

    // 修复常见的AI生成错误
    // 1. 修复 "type":. "value" 这种错误（多了点号和引号）
    fixed = fixed.replace(/"([^"]+)"\s*:\s*\.\s*":\s*"([^"]+)"/g, '"$1": "$2"');

    // 2. 修复 "type":- "value" 这种错误（多了减号）
    fixed = fixed.replace(/"([^"]+)"\s*:\s*-\s*"([^"]+)"/g, '"$1": "$2"');

    // 3. 修复多余的冒号（如 "type": ": "value"）
    fixed = fixed.replace(/:\s*":\s*"/g, ': "');

    // 4. 修复数组格式错误
    fixed = fixed.replace(/\[\s*"([^"]*)",\s*\]/g, '["$1"]');
    fixed = fixed.replace(/\[\s*,/g, '[');
    fixed = fixed.replace(/,\s*\]/g, ']');

    // 5. 修复对象格式错误
    fixed = fixed.replace(/{\s*,/g, '{');
    fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

    // 6. 修复引号问题
    fixed = fixed.replace(/'/g, '"');

    // 7. 修复缺失的逗号
    fixed = fixed.replace(/}(\s*){/g, '},$1{');
    fixed = fixed.replace(/](\s*){/g, '],$1{');
    fixed = fixed.replace(/"(\s*)"([^:,}\]]+)":/g, '"$1"$2":');

    // 8. 修复数值格式错误
    fixed = fixed.replace(/:\s*"(\d+)"/g, ': $1');
    fixed = fixed.replace(/:\s*"(true|false)"/g, ': $1');

    // 9. 修复字段名缺少引号
    fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

    console.log('🔧 JSON语法修复:', jsonText.substring(0, 100), '→', fixed.substring(0, 100));
    return fixed;
  }

  /**
   * 获取详细的错误信息
   * @param error 错误对象
   * @param jsonText JSON文本
   * @returns 详细的错误信息
   */
  private static getDetailedErrorMessage(error: any, jsonText: string): string {
    if (!error) {
      return 'JSON格式无法识别：请确保返回的是有效的JSON格式';
    }

    const errorMsg = error.message || '';

    if (errorMsg.includes('Unexpected token')) {
      const match = errorMsg.match(/Unexpected token (.+) in JSON at position (\d+)/);
      if (match) {
        const token = match[1];
        const position = parseInt(match[2]);
        const context = jsonText.substring(Math.max(0, position - 20), position + 20);
        return `JSON格式错误：在位置${position}发现意外字符"${token}"，上下文："${context}"`;
      }
      return 'JSON格式错误：发现意外的字符，请检查括号和引号是否匹配';
    }

    if (errorMsg.includes('Unexpected end')) {
      return 'JSON格式不完整：可能缺少结束括号或引号';
    }

    if (jsonText.includes('```')) {
      return 'JSON包含代码块标记：请移除```标记后重试';
    }

    if (errorMsg.includes('Unexpected string')) {
      return 'JSON字符串格式错误：可能缺少逗号或引号不匹配';
    }

    if (errorMsg.includes('Expected property name')) {
      return 'JSON属性名错误：属性名必须用双引号包围';
    }

    return `JSON解析失败：${errorMsg}。请检查格式是否正确`;
  }

  /**
   * 预处理响应文本
   * @param response 原始响应文本
   * @returns 预处理后的文本
   */
  private static preprocessResponse(response: string): string {
    let processed = response;

    // 移除<think>标记块 - 更严格的处理，确保即使标记不完整也能处理
    processed = processed.replace(/<think>[\s\S]*?<\/think>/g, '');

    // 如果还有未闭合的<think>标签，也移除它及其后面的内容
    const thinkStart = processed.indexOf('<think>');
    if (thinkStart !== -1) {
      const thinkEnd = processed.indexOf('</think>', thinkStart);
      if (thinkEnd === -1) {
        // 没有找到闭合标签，移除<think>及其后面的所有内容
        processed = processed.substring(0, thinkStart);
      }
    }

    // 移除可能的HTML标签
    processed = processed.replace(/<[^>]*>/g, '');

    // 移除常见的非JSON前缀文本
    processed = processed.replace(/^(以下是|这是|返回的|JSON格式的|结果是|输出为|输出结果为|输出如下|以下为|以下内容为)[：:]\s*/i, '');

    // 移除常见的非JSON后缀文本
    processed = processed.replace(/\s*(这就是|以上是|以上为|这是|这些是|这些就是)[^{]*$/i, '');

    // 确保处理后的文本不为空
    if (processed.trim() === '') {
      console.warn('预处理后的文本为空，返回原始响应');
      return response;
    }

    return processed;
  }

  /**
   * 从AI响应中提取纯文本内容
   * @param response AI响应文本
   * @returns 提取的纯文本内容
   */
  static extractText(response: string): string {
    if (!response || typeof response !== 'string') {
      return '';
    }

    try {
      // 移除<think>标记块 - 使用与preprocessResponse相同的逻辑
      let cleanedText = response.replace(/<think>[\s\S]*?<\/think>/g, '');

      // 如果还有未闭合的<think>标签，也移除它及其后面的内容
      const thinkStart = cleanedText.indexOf('<think>');
      if (thinkStart !== -1) {
        const thinkEnd = cleanedText.indexOf('</think>', thinkStart);
        if (thinkEnd === -1) {
          // 没有找到闭合标签，移除<think>及其后面的所有内容
          cleanedText = cleanedText.substring(0, thinkStart);
        }
      }

      // 移除Markdown代码块
      cleanedText = cleanedText.replace(/```[\s\S]*?```/g, '');

      // 移除HTML标签
      cleanedText = cleanedText.replace(/<[^>]*>/g, '');

      // 移除多余的空白字符
      cleanedText = cleanedText.trim();

      // 确保处理后的文本不为空
      if (cleanedText === '') {
        console.warn('提取文本后的内容为空，返回原始响应');
        return response;
      }

      return cleanedText;
    } catch (error) {
      console.error('提取文本时发生异常:', error);
      return response;
    }
  }

  /**
   * 根据剧情点内容动态计算字数控制范围
   */
  private static calculateDynamicWordCount(content: string): { min: number; max: number; target: number } {
    const currentLength = content ? content.length : 0;

    if (currentLength === 0) {
      // 如果没有内容，使用默认的100字左右
      return { min: 80, max: 120, target: 100 };
    }

    // 基于当前内容长度，设置合理的字数范围
    // 目标是保持在100字左右，但允许根据实际内容调整
    const target = Math.max(100, Math.min(150, currentLength + 20)); // 在当前基础上略微增加
    const min = Math.max(80, target - 20);
    const max = Math.min(180, target + 30);

    return { min, max, target };
  }

  /**
   * 验证和修正节点数据字段
   */
  static validateAndFixFields(nodeData: any): any {
    if (!nodeData) return nodeData;

    // 🔥 修正剧情节点的plotPoints字段
    if (nodeData.plotPoints && Array.isArray(nodeData.plotPoints)) {
      nodeData.plotPoints = this.validateAndFixPlotPoints(nodeData.plotPoints);
    }

    // 修正对话节点的dialogueContent字段
    if (nodeData.dialogueContent && Array.isArray(nodeData.dialogueContent)) {
      nodeData.dialogueContent = this.validateDialogueContent(nodeData.dialogueContent);
    }

    // 🔥 修正participants字段格式
    if (nodeData.participants && Array.isArray(nodeData.participants)) {
      nodeData.participants = this.validateParticipants(nodeData.participants);
    }

    return nodeData;
  }

  /**
   * 验证和修正plotPoints字段
   */
  static validateAndFixPlotPoints(plotPoints: any[]): PlotPoint[] {
    if (!Array.isArray(plotPoints)) return [];

    return plotPoints.map((point, index) => {
      // 自动修正常见的错误字段名
      const correctedPoint: PlotPoint = {
        id: point.id || `point_${String(index + 1).padStart(3, '0')}`,
        order: typeof point.order === 'number' ? point.order : (index + 1),
        content: point.content || point.point || point.description || point.brief || point.text || '',
        type: point.type || 'setup',

        // 🔥 新增：处理avoidWriting和shouldWriting字段
        avoidWriting: point.avoidWriting || point.avoid || point.avoidance || '',
        shouldWriting: point.shouldWriting || point.should || point.recommended || '',

        // 🔥 新增：处理styleMethod字段
        styleMethod: point.styleMethod ? {
          technique: point.styleMethod.technique || '直接描写法',
          style: point.styleMethod.style || '客观叙述',
          tone: point.styleMethod.tone || '中性',
          perspective: point.styleMethod.perspective || '第三人称',
          emphasis: point.styleMethod.emphasis || '行动和对话并重'
        } : undefined,

        // 🔥 新增：处理formatSpecs字段
        formatSpecs: point.formatSpecs ? {
          wordCount: point.formatSpecs.wordCount || this.calculateDynamicWordCount(point.content || ''),
          paragraphRules: {
            maxSentencesPerParagraph: point.formatSpecs.paragraphRules?.maxSentencesPerParagraph || 4,
            paragraphBreakRules: point.formatSpecs.paragraphRules?.paragraphBreakRules || '逻辑完整后分段',
            conflictHandling: point.formatSpecs.paragraphRules?.conflictHandling || '冲突升级时必须换行强调',
            actionDialogueFlow: point.formatSpecs.paragraphRules?.actionDialogueFlow || '严格执行对话→行动→对话节奏',
            mandatoryBreaks: point.formatSpecs.paragraphRules?.mandatoryBreaks || '情绪转折、场景切换、说话人变化必须换行'
          },
          punctuationRules: {
            dialogueFormat: point.formatSpecs.punctuationRules?.dialogueFormat || '「」',
            emphasisFormat: point.formatSpecs.punctuationRules?.emphasisFormat || '适度使用',
            pauseFormat: point.formatSpecs.punctuationRules?.pauseFormat || '自然停顿',
            conflictPunctuation: point.formatSpecs.punctuationRules?.conflictPunctuation || '冲突场面用短句+换行制造紧张感',
            naturalFlow: point.formatSpecs.punctuationRules?.naturalFlow || '标点服务于阅读节奏'
          },
          // lineBreakRules已废弃，保留解析能力以确保向后兼容性
          ...(point.formatSpecs.lineBreakRules && (() => {
            console.warn('⚠️ lineBreakRules字段已废弃，将在未来版本中移除');
            return {
              lineBreakRules: {
                sceneTransition: point.formatSpecs.lineBreakRules?.sceneTransition || '明确分隔',
                timeTransition: point.formatSpecs.lineBreakRules?.timeTransition || '清晰标记',
                speakerChange: point.formatSpecs.lineBreakRules?.speakerChange || '独立成行',
                conflictEscalation: point.formatSpecs.lineBreakRules?.conflictEscalation || '冲突升级强制换行',
                actionEmphasis: point.formatSpecs.lineBreakRules?.actionEmphasis || '关键行动前后换行',
                emotionShift: point.formatSpecs.lineBreakRules?.emotionShift || '情绪转折点必须换行',
                prohibitedMerging: point.formatSpecs.lineBreakRules?.prohibitedMerging || '严禁将冲突行动与平常描写合并'
              }
            };
          })())
        } : undefined
      };

      // 添加新字段的默认值
      if (!correctedPoint.styleMethod && point.styleMethod) {
        correctedPoint.styleMethod = {
          technique: point.styleMethod.technique || '直接描写',
          style: point.styleMethod.style || '客观叙述',
          tone: point.styleMethod.tone || '中性',
          perspective: point.styleMethod.perspective || '第三人称',
          emphasis: point.styleMethod.emphasis || '行动和对话并重'
        };
      }

      if (!correctedPoint.formatSpecs && point.formatSpecs) {
        const dynamicWordCount = this.calculateDynamicWordCount(correctedPoint.content || '');
        correctedPoint.formatSpecs = {
          wordCount: {
            min: point.formatSpecs.wordCount?.min || dynamicWordCount.min,
            max: point.formatSpecs.wordCount?.max || dynamicWordCount.max,
            target: point.formatSpecs.wordCount?.target || dynamicWordCount.target
          },
          paragraphRules: {
            maxSentencesPerParagraph: point.formatSpecs.paragraphRules?.maxSentencesPerParagraph || 4,
            paragraphBreakRules: point.formatSpecs.paragraphRules?.paragraphBreakRules || '逻辑完整后分段',
            conflictHandling: point.formatSpecs.paragraphRules?.conflictHandling || '冲突升级时必须换行强调，禁止用段落分隔弱化冲突感',
            actionDialogueFlow: point.formatSpecs.paragraphRules?.actionDialogueFlow || '严格执行对话→行动→对话节奏，行动不独立成段',
            mandatoryBreaks: point.formatSpecs.paragraphRules?.mandatoryBreaks || '情绪转折、场景切换、说话人变化必须换行'
          },
          punctuationRules: {
            dialogueFormat: point.formatSpecs.punctuationRules?.dialogueFormat || '「」',
            emphasisFormat: point.formatSpecs.punctuationRules?.emphasisFormat || '适度使用',
            pauseFormat: point.formatSpecs.punctuationRules?.pauseFormat || '自然停顿',
            conflictPunctuation: point.formatSpecs.punctuationRules?.conflictPunctuation || '冲突场面用短句+换行制造紧张感，不依赖标点符号强调',
            naturalFlow: point.formatSpecs.punctuationRules?.naturalFlow || '标点服务于阅读节奏，避免为了强调而强调'
          },
          // lineBreakRules已废弃，保留解析能力以确保向后兼容性
          ...(point.formatSpecs.lineBreakRules && (() => {
            console.warn('⚠️ lineBreakRules字段已废弃，将在未来版本中移除');
            return {
              lineBreakRules: {
                sceneTransition: point.formatSpecs.lineBreakRules?.sceneTransition || '明确分隔',
                timeTransition: point.formatSpecs.lineBreakRules?.timeTransition || '清晰标记',
                speakerChange: point.formatSpecs.lineBreakRules?.speakerChange || '独立成行',
                conflictEscalation: point.formatSpecs.lineBreakRules?.conflictEscalation || '冲突升级强制换行：每个冲突动作独立行，增强视觉冲击力',
                actionEmphasis: point.formatSpecs.lineBreakRules?.actionEmphasis || '关键行动前后换行：重要动作要有视觉突出效果',
                emotionShift: point.formatSpecs.lineBreakRules?.emotionShift || '情绪转折点必须换行：从平静到愤怒要有明显视觉分割',
                prohibitedMerging: point.formatSpecs.lineBreakRules?.prohibitedMerging || '严禁将冲突行动与平常描写合并在同一行'
              }
            };
          })())
        };
      }

      // 验证和修正字段完整性
      if (!correctedPoint.content || correctedPoint.content.length < 10) {
        correctedPoint.content = `剧情点${index + 1}：需要补充具体内容`;
        console.log('🔧 plotPoints字段修正: content字段过短，已补充默认内容');
      }

      // 🔥 更新：处理新的字段结构
      if ((correctedPoint as any).writingGuidance && (!correctedPoint.avoidWriting || !correctedPoint.shouldWriting)) {
        // 如果有旧的writingGuidance但没有新字段，尝试拆分
        const guidance = (correctedPoint as any).writingGuidance;
        if (guidance.includes('严禁') || guidance.includes('避免')) {
          const parts = guidance.split(/而是|应该|直接写/);
          if (parts.length >= 2) {
            correctedPoint.avoidWriting = parts[0].replace(/🚫|严禁使用|避免/g, '').trim();
            correctedPoint.shouldWriting = parts[1].trim();
          }
        }
        console.log('🔧 plotPoints字段修正: 已将writingGuidance拆分为avoidWriting和shouldWriting');
        // 删除旧的writingGuidance字段
        delete (correctedPoint as any).writingGuidance;
      }

      // 为新字段提供默认值
      if (!correctedPoint.avoidWriting || correctedPoint.avoidWriting.length < 10) {
        correctedPoint.avoidWriting = `避免'一丝xx'、'几分xx'等模糊表达；避免比喻；避免主观描述`;
        console.log('🔧 plotPoints字段修正: avoidWriting字段过短，已补充默认指导');
      }

      if (!correctedPoint.shouldWriting || correctedPoint.shouldWriting.length < 10) {
        correctedPoint.shouldWriting = `具体的角色行动和对话内容，客观的动作描述`;
        console.log('🔧 plotPoints字段修正: shouldWriting字段过短，已补充默认指导');
      }

      // 验证type字段值
      const validTypes: ('setup' | 'conflict' | 'resolution' | 'twist')[] = ['setup', 'conflict', 'resolution', 'twist'];
      if (!correctedPoint.type || !validTypes.includes(correctedPoint.type as any)) {
        correctedPoint.type = 'setup';
        console.log('🔧 plotPoints字段修正: type字段值无效，已设置为setup');
      }

      // 记录字段修正日志
      if (point.point && !point.content) {
        console.log('🔧 plotPoints字段修正: point → content:', point.point);
      }
      if (point.description && !point.content) {
        console.log('🔧 plotPoints字段修正: description → content:', point.description);
      }
      if (point.guidance && !(correctedPoint as any).writingGuidance) {
        console.log('🔧 plotPoints字段修正: guidance → writingGuidance:', point.guidance);
      }

      return correctedPoint;
    });
  }

  /**
   * 验证和修正participants字段
   */
  private static validateParticipants(participants: any[]): string[] {
    return participants.map((participant, index) => {
      if (typeof participant === 'string') {
        return participant.trim();
      }

      if (typeof participant === 'object' && participant !== null) {
        // 处理错误的对象格式，如 {"characterName":"何雨梁","brief":"..."}
        const name = participant.characterName || participant.name || participant.speaker || participant.character;
        if (typeof name === 'string' && name.trim()) {
          console.log('🔧 participants字段修正: 对象格式 → 字符串格式:', name);
          return name.trim();
        }

        // 如果无法提取名称，使用JSON字符串作为备份
        console.log('⚠️ participants对象无法提取角色名，使用JSON:', participant);
        return JSON.stringify(participant);
      }

      // 其他类型转换为字符串
      return String(participant || `角色${index + 1}`);
    });
  }

  /**
   * 验证和修正对话内容数组
   */
  private static validateDialogueContent(dialogueContent: any[]): DialogueItem[] {
    return dialogueContent.map((item, index) => {
      const correctedItem: DialogueItem = {
        id: String(item?.id || `dialogue_${Date.now()}_${index}`),
        speaker: this.extractStringValue(item, ['speaker', 'name', 'character'], '未知角色'),
        content: this.extractStringValue(item, ['content', 'text'], ''),
        emotion: this.extractStringValue(item, ['emotion', 'feature', 'mood'], ''),
        action: this.extractStringValue(item, ['action', 'gesture'], '')
      };

      // 记录字段修正日志
      if (item?.name && !item?.speaker) {
        console.log('🔧 字段修正: name → speaker:', item.name);
      }
      if (item?.feature && !item?.emotion) {
        console.log('🔧 字段修正: feature → emotion:', item.feature);
      }
      if (item?.character && !item?.speaker) {
        console.log('🔧 字段修正: character → speaker:', item.character);
      }

      return correctedItem;
    });
  }

  /**
   * 从对象中提取字符串值
   * 支持多个候选字段名，并处理对象类型的值
   */
  private static extractStringValue(obj: any, fields: string[], defaultValue: string): string {
    for (const field of fields) {
      const value = obj?.[field];

      if (typeof value === 'string' && value.trim()) {
        return value.trim();
      }

      if (typeof value === 'object' && value !== null) {
        // 尝试提取对象中的有用信息
        const extractedValue = value.name || value.title || value.value || value.text;
        if (typeof extractedValue === 'string' && extractedValue.trim()) {
          console.log(`🔧 对象字段提取: ${field}.${Object.keys(value)[0]} → ${extractedValue}`);
          return extractedValue.trim();
        }
        // 如果无法提取有用信息，使用JSON字符串作为备份
        console.log(`⚠️ 对象字段无法提取，使用JSON: ${field} →`, value);
        return JSON.stringify(value);
      }
    }

    return defaultValue;
  }

  /**
   * 验证节点changes数组中的数据
   */
  static validateChanges(changes: any[]): any[] {
    if (!Array.isArray(changes)) return changes;

    return changes.map(change => {
      if (change?.data) {
        change.data = this.validateAndFixFields(change.data);
      }
      return change;
    });
  }

  /**
   * 验证完整的AI响应
   */
  static validateAIResponse(response: any): any {
    if (!response) return response;

    // 验证changes数组
    if (response.changes && Array.isArray(response.changes)) {
      response.changes = this.validateChanges(response.changes);
    }

    return response;
  }
}
