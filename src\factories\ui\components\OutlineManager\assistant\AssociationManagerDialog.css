/* 关联管理对话框样式 - 使用Portal挂载到body */
.association-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9998; /* 比GuideModal稍低一点 */
  animation: overlayFadeIn 0.3s ease-out;
  padding: 20px;
  box-sizing: border-box;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.association-manager-dialog {
  background: white;
  border-radius: 12px;
  width: 600px;
  height: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  animation: dialogSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.dialog-stats {
  font-size: 14px;
  color: #6b7280;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.dialog-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dialog-close:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 对话框内容 */
.dialog-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧类型选择器 */
.type-selector {
  width: 160px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  border-radius: 0;
}

.type-button:hover {
  background: #f3f4f6;
}

.type-button.active {
  background: #3b82f6;
  color: white;
}

.type-button.active .type-count {
  background: rgba(255, 255, 255, 0.2);
}

.type-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.type-label {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.type-count {
  font-size: 12px;
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 搜索和操作栏 */
.content-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.toolbar-btn {
  padding: 6px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 范围选择器 */
.range-selector {
  padding: 12px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.range-toggle {
  padding: 6px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.range-toggle.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.range-input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.range-apply {
  padding: 4px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.range-apply:hover:not(:disabled) {
  background: #2563eb;
}

.range-apply:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 内容列表 */
.content-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 内容项 */
.content-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  animation: itemSlideIn 0.3s ease-out;
  animation-fill-mode: both;
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.content-item:hover {
  background: #f8fafc;
}

.content-item.selected {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.content-item.selected:hover {
  background: #dbeafe;
}

.item-checkbox {
  margin-top: 2px;
}

.item-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.item-order {
  color: #6b7280;
  font-weight: normal;
}

.item-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
}

.dialog-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dialog-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.dialog-btn.secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.dialog-btn.primary {
  background: #3b82f6;
  color: white;
}

.dialog-btn.primary:hover {
  background: #2563eb;
  filter: drop-shadow(0 0 8px #3b82f6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .association-manager-dialog {
    width: 90vw;
    height: 80vh;
    margin: 20px;
  }

  .type-selector {
    width: 120px;
  }

  .type-button {
    padding: 10px 12px;
  }

  .type-label {
    font-size: 12px;
  }

  .content-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .toolbar-actions {
    justify-content: center;
  }

  .range-inputs {
    flex-wrap: wrap;
    justify-content: center;
  }
}
