/**
 * 章节内容AI分析服务
 * 通过AI分析章节内容并生成JSON格式的剧情点示例
 */

import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

export interface PlotPointExample {
  id: string;
  order: number;
  content: string;
  type: 'setup' | 'conflict' | 'resolution' | 'twist';
  avoidWriting: string;
  shouldWriting: string;
  // 支持的扩展字段，与系统PlotPoint接口保持一致
  styleMethod?: {
    technique: string;
    style: string;
    tone: string;
    perspective: string;
    emphasis: string;
  };
  formatSpecs?: {
    wordCount: {
      min: number;
      max: number;
      target: number;
    };
    paragraphRules: {
      maxSentencesPerParagraph: number;
      paragraphBreakRules: string;
    };
    punctuationRules: {
      dialogueFormat: string;
      emphasisFormat: string;
      pauseFormat: string;
    };

  };
}

// MessageBuilder兼容的完整结构
export interface MessageBuilderResult {
  message: string;
  changes: Array<{
    type: 'create';
    nodeId: string;
    data: ChapterData | PlotData;
    parentId?: string;
  }>;
  metadata: {
    operationType: 'create';
    confidence: number;
  };
}

export interface ChapterData {
  title: string;
  type: 'chapter';
  description: string;
  creativeNotes: string;
  chapterStyle: string;
  chapterTechniques: string[];
  chapterGoals: string[];
  rhythmPhase: string;
  rhythmGuidance: string;
}

export interface PlotData {
  title: string;
  type: 'plot';
  description: string;
  creativeNotes: string;
  plotPoints: PlotPointExample[];
  plotType: string;
  relatedCharacters: string[];
  conflictLevel: number;
  suspenseElements: string[];
}

export interface ChapterAnalysisResult {
  // 保持原有的简化结构用于内部处理
  plotPoints: PlotPointExample[];
  overallStyle: string;
  mainCharacters: string[];
  conflictLevel: number;
  themes: string[];
  writingTechniques: string[];
  // 章节相关字段
  chapterTitle?: string;
  chapterDescription?: string;
  chapterStyle?: string;
  chapterTechniques?: string[];
  chapterGoals?: string[];
  rhythmPhase?: string;
  rhythmGuidance?: string;
  // 剧情节点相关字段
  plotTitle?: string;
  plotDescription?: string;
  plotType?: string;
  suspenseElements?: string[];
  relatedCharacters?: string[];
  creativeNotes?: string;
  analysisMetadata: {
    segmentCount: number;
    totalWords: number;
    processingTime: number;
    analysisDepth: string;
  };
  success: boolean;
  error?: string;
}

/**
 * 分段配置接口
 */
export interface SegmentConfig {
  mode: 'smart' | 'fixed' | 'manual';
  maxLength: number;
  minLength: number;
  overlapRatio: number;
  preserveContext: boolean;
}

/**
 * 分析配置接口
 */
export interface AnalysisConfig {
  segmentConfig: SegmentConfig;
  analysisDepth: 'quick' | 'standard' | 'deep';
  outputFormat: 'standard' | 'extended' | 'custom';
  includeACE: boolean;
  selectedFrameworks: any[];
  // 新增：分批处理配置
  batchProcessing?: {
    enabled: boolean;
    maxPlotPointsPerBatch: number;
    enableContinuation: boolean;
  };
}

/**
 * 分批处理结果接口
 */
export interface BatchProcessingResult {
  isPartial: boolean;
  continuationToken?: string;
  currentBatch: number;
  totalBatches?: number;
  accumulatedResult: ChapterAnalysisResult;
}

export class ChapterAnalysisService {
  private apiKey: string;
  private baseURL: string;
  private apiSettings: any;

  constructor(apiKey?: string, baseURL: string = 'https://api.openai.com/v1') {
    this.apiKey = apiKey || '';
    this.baseURL = baseURL;

    // 初始化API设置
    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();
  }

  /**
   * 通过AI分析章节内容，生成JSON格式的剧情点示例（支持分批处理）
   */
  async analyzeChapterContent(
    content: string,
    aceFrameworks: any[] = [],
    onChunk?: (chunk: string) => void,
    config?: AnalysisConfig,
    previousResult?: ChapterAnalysisResult
  ): Promise<ChapterAnalysisResult> {
    const startTime = Date.now();

    try {
      // 获取AI配置
      if (!this.apiKey) {
        const provider = this.apiSettings.getCurrentProvider() || 'openai';
        this.apiKey = this.apiSettings.getAPIKey(provider) || '';
        this.baseURL = this.apiSettings.getAPIEndpoint(provider) || 'https://api.openai.com/v1';
      }

      if (!this.apiKey) {
        throw new Error('请先在设置中配置AI API密钥');
      }

      onChunk?.('开始预处理文本内容...\n');

      // 预处理内容
      const cleanContent = this.preprocessContent(content);

      // 分段处理
      const segments = this.segmentContent(cleanContent, config?.segmentConfig);

      onChunk?.(`内容已分为 ${segments.length} 个段落\n`);

      // 1. 构建消息数组（包含分段内容和ACE框架）
      const messages = this.buildSegmentedMessages(segments, aceFrameworks, config, previousResult);

      onChunk?.('正在调用AI进行内容分析...\n');

      // 2. 调用AI分析
      const response = await this.callAIAnalysis(messages, onChunk);

      onChunk?.('正在解析分析结果...\n');

      // 4. 解析AI响应
      const result = this.parseAIResponse(response, {
        segmentCount: segments.length,
        totalWords: cleanContent.length,
        processingTime: Date.now() - startTime,
        analysisDepth: config?.analysisDepth || 'standard'
      }, previousResult);

      // 检查是否需要继续处理
      const needsContinuation = this.checkNeedsContinuation(response);

      if (needsContinuation) {
        onChunk?.(`🔄 检测到需要继续处理，已生成 ${result.plotPoints.length} 个剧情点，继续分析剩余内容...\n`);

        // 递归调用继续处理
        const continuationResult = await this.analyzeChapterContent(
          content,
          aceFrameworks,
          onChunk,
          config,
          { ...result, success: true }
        );

        return continuationResult;
      }

      onChunk?.('分析完成！\n');

      return {
        ...result,
        success: true
      };
    } catch (error) {
      console.error('AI章节内容分析失败:', error);
      return {
        plotPoints: [],
        overallStyle: '',
        mainCharacters: [],
        conflictLevel: 1,
        themes: [],
        writingTechniques: [],
        // 默认的剧情节点字段
        plotType: 'conflict',
        suspenseElements: [],
        chapterGoals: [],
        chapterStyle: '',
        chapterTechniques: [],
        rhythmPhase: 'setup',
        rhythmGuidance: '',
        relatedCharacters: [],
        creativeNotes: '',
        analysisMetadata: {
          segmentCount: 0,
          totalWords: content.length,
          processingTime: Date.now() - startTime,
          analysisDepth: config?.analysisDepth || 'standard'
        },
        success: false,
        error: error instanceof Error ? error.message : '分析失败'
      };
    }
  }

  /**
   * 预处理文本内容
   */
  private preprocessContent(content: string): string {
    // 清理多余空格和换行
    let cleaned = content.replace(/\s+/g, ' ').trim();

    // 规范化段落
    cleaned = cleaned.replace(/。\s*/g, '。\n');
    cleaned = cleaned.replace(/！\s*/g, '！\n');
    cleaned = cleaned.replace(/？\s*/g, '？\n');

    // 移除多余的换行
    cleaned = cleaned.replace(/\n+/g, '\n');

    return cleaned;
  }

  /**
   * 分段处理 - 按25个句子分段
   */
  private segmentContent(content: string, _config?: SegmentConfig): string[] {
    const segments: string[] = [];

    // 按句子分割（句号、感叹号、问号）
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0);

    // 每25个句子为一段
    const sentencesPerSegment = 20;

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      const segment = segmentSentences.join('。') + '。';

      // 确保段落有足够的内容
      if (segment.trim().length > 50) {
        segments.push(segment.trim());
      }
    }

    return segments;
  }

  /**
   * 构建分段消息数组 - 按段落构建消息
   */
  private buildSegmentedMessages(segments: string[], aceFrameworks: any[], _config?: AnalysisConfig, previousResult?: ChapterAnalysisResult): any[] {
    const messages = [
      {
        role: 'system',
        content: `你是专业的剧情分析专家，具备以下能力：

1. 深度理解小说章节内容的剧情结构
2. 精准提取关键剧情点和写作技巧
3. 生成标准化的JSON格式分析结果
4. 运用ACE创作框架进行专业分析

分析规则（强制执行）：
- 严格按照指定的JSON格式输出，违反格式将被拒绝
- 剧情点数量要求：最多提取60个剧情点，避免出现重复，和超过60个，禁止超过60个
- 字数强制要求：avoidWriting和shouldWriting各不少于80字，content不少于30字
- 质量检查标准：每个字段必须包含具体详细的分析内容，不得使用简短概括
- avoidWriting：必须详细分析原文中没有出现的不良写作方式（80字+）
- shouldWriting：必须详细提取原文中已有的优秀表达方式（80字+）
- 数组字段要求：themes(3-6个)、writingTechniques(5-8个)
- writingGuidance要求：综合写作指导，必须60字以上
- 不要试图"改进"或"提升"原文，只需如实提取和分析
- 确保JSON语法完全正确
- 剧情点类型限定为：setup/conflict/resolution/twist
- 内容完整性检查：必须覆盖原文的每个重要剧情发展


 剧情点数量要求：最多提取60个剧情点，避免出现重复，和超过60个，禁止超过60个

你将接收多个文本段落，必须逐段细致分析并提取完整的剧情结构。`
      }
    ];

    // 如果有之前的结果，添加完整的JSON结构作为上下文
    if (previousResult && previousResult.plotPoints && previousResult.plotPoints.length > 0) {
      // 构建完整的之前结果JSON结构
      const previousJSON = {
        message: `已生成 ${previousResult.plotPoints.length} 个剧情点`,
        changes: [
          {
            type: "create",
            nodeId: "chapter_previous",
            data: {
              title: "已处理章节",
              type: "chapter",
              description: previousResult.overallStyle || "章节描述",
              creativeNotes: previousResult.creativeNotes || "创作指导",
              chapterStyle: previousResult.chapterStyle || "写作风格",
              chapterTechniques: previousResult.chapterTechniques || [],
              chapterGoals: previousResult.chapterGoals || [],
              rhythmPhase: previousResult.rhythmPhase || "setup",
              rhythmGuidance: previousResult.rhythmGuidance || "节奏指导"
            },
            parentId: null
          },
          {
            type: "create",
            nodeId: "plot_previous",
            data: {
              title: "已处理剧情",
              type: "plot",
              description: "之前已分析的剧情内容",
              creativeNotes: "基于之前分析的创作指导",
              plotPoints: previousResult.plotPoints,
              plotType: previousResult.plotType || "conflict",
              relatedCharacters: previousResult.relatedCharacters || previousResult.mainCharacters || [],
              conflictLevel: previousResult.conflictLevel || 3,
              suspenseElements: previousResult.suspenseElements || [],

            },
            parentId: "chapter_previous"
          }
        ],
        metadata: {
          operationType: "continue",
          confidence: 0.95,
          previousPlotPointCount: previousResult.plotPoints.length
        }
      };

      messages.push({
        role: 'user',
        content: `【继续处理模式】
以下是之前已生成的完整JSON结构，包含 ${previousResult.plotPoints.length} 个剧情点。
请在此基础上继续分析剩余内容，从第${previousResult.plotPoints.length + 1}个剧情点开始继续。

之前的完整结果：
\`\`\`json
${JSON.stringify(previousJSON, null, 2)}
\`\`\`

请确保新生成的剧情点与已有内容保持连贯性和逻辑性。`
      });
    }

    // 添加ACE框架学习消息（如果有）
    if (aceFrameworks && aceFrameworks.length > 0) {
      messages.push({
        role: 'user',
        content: `以下是${aceFrameworks.length}个ACE创作框架，请学习并在分析中应用这些专业技巧：`
      });

      aceFrameworks.forEach((framework, index) => {
        messages.push({
          role: 'user',
          content: `ACE框架${index + 1}：${this.extractFrameworkContent(framework)}`
        });
      });
    }

    // MessageBuilder完整输出模板（必须严格按照此格式输出）
    const messageBuilderTemplate = {
      "message": "基于原文内容分析，创建章节和剧情节点",
      "changes": [
        {
          "type": "create",
          "nodeId": "chapter_extracted_001",
          "data": {
            "title": "[从原文提取或生成的章节标题]",
            "type": "chapter",
            "description": "[章节的剧情概要和主要内容发展，必须100字以上]",
            "creativeNotes": "[章节的创作指导和写作要点，必须80字以上]",
            "chapterStyle": "[写作风格类型]",
            "chapterTechniques": ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
            "chapterGoals": ["[章节目标1]", "[章节目标2]", "[章节目标3]"],
            "rhythmPhase": "[节奏阶段]",
            "rhythmGuidance": "[基于节奏阶段的具体创作指导，必须60字以上]"
          }
        },
        {
          "type": "create",
          "nodeId": "plot_extracted_001",
          "parentId": "chapter_extracted_001",
          "data": {
            "title": "[剧情节点标题]",
            "type": "plot",
            "description": "[剧情节点的核心内容和发展方向，必须80字以上]",
            "creativeNotes": "[剧情节点的创作要点和写作指导，必须80字以上]",
            "plotPoints": [
              {
                "id": "point_001",
                "order": 1,
                "content": "[从原文提取的剧情点内容]",
                "type": "setup",
                "avoidWriting": "[分析原文没有的不良写作方式，80字以上]",
                "shouldWriting": "[提取原文已有的优秀表达方式，80字以上]",
                "styleMethod": {
                  "technique": "[写作技巧]",
                  "style": "[写作风格]",
                  "tone": "[语调]",
                  "perspective": "[叙述视角]",
                  "emphasis": "[强调重点]"
                },
                "formatSpecs": {
                  "wordCount": {
                    "min": "[根据剧情点内容长度动态计算，通常80-100]",
                    "max": "[根据剧情点内容长度动态计算，通常120-180]",
                    "target": "[根据剧情点内容长度动态计算，通常100-150]"
                  },
                  "paragraphRules": {
                    "maxSentencesPerParagraph": 4,
                    "paragraphBreakRules": "[分段规则]"
                  },
                  "punctuationRules": {
                    "dialogueFormat": "「」",
                    "emphasisFormat": "[强调格式]",
                    "pauseFormat": "[停顿格式]"
                  },

                }
              }
            ],
            "plotType": "[冲突类型]",
            "relatedCharacters": ["[角色1]", "[角色2]", "[角色3]"],
            "conflictLevel": 3,
            "suspenseElements": ["[悬念要素1]", "[悬念要素2]"]
          }
        }
      ],
      "metadata": {
        "needsContinuation": false,
        "operationType": "create",
        "confidence": 0.95
      }
    };

    messages.push({
      role: 'user',
      content: `输出格式要求：

必须严格按照以下MessageBuilder格式输出，不得有任何偏差：

${JSON.stringify(messageBuilderTemplate, null, 2)}

MessageBuilder格式要求（严格执行）：

【顶层结构】
- message: 操作描述，说明分析和创建的内容
- changes: 变更数组，包含章节和剧情节点的创建
- metadata: 元数据，包含operationType和confidence

【章节节点 (type: "chapter")】
- title: 章节标题，必须15-30字
- type: 固定为"chapter"
- description: 章节剧情概要，必须100字以上
- creativeNotes: 章节创作指导，必须80字以上
- chapterStyle: 写作风格类型，必须明确具体
- chapterTechniques: 写作手法数组，必须3个技巧
- chapterGoals: 章节目标数组，必须3个目标
- rhythmPhase: 节奏阶段，必须是具体值
- rhythmGuidance: 节奏创作指导，必须60字以上

【剧情节点 (type: "plot")】
- title: 剧情节点标题，必须10-25字
- type: 固定为"plot"
- description: 剧情核心内容，必须80字以上
- creativeNotes: 创作要点和写作指导，必须80字以上
- plotType: 冲突类型，必须是具体值
- relatedCharacters: 相关角色数组，必须3-8个
- conflictLevel: 1-5的整数
- suspenseElements: 悬念要素数组，必须2-4个


【剧情点数组 (plotPoints)】
- 剧情点数量要求：最多提取60个剧情点，避免出现重复，和超过60个，禁止超过60个
- 剧情点密度控制：根据实际剧情点内容长度动态调整字数范围，避免概括性描述，要求具体细节
- 动态字数控制机制：
  * 基础目标：每个剧情点100字左右，但会根据实际内容长度智能调整
  * 计算规则：在当前内容基础上适当增加，确保有足够空间添加具体细节
  * 范围控制：最小80字，最大180字，目标通常100-150字
- 密度精细化要求：
  * 禁止概括性表达："主角遇到困难"、"发生冲突"、"情况好转"等
  * 要求具体化：描述具体动作、对话、场景细节、人物反应、感官体验
  * 一个剧情点只描述一个具体的小场景或小动作，不要概括整个情节
  * 示例：❌"主角与敌人战斗获胜" ✅"主角挥剑格挡，剑刃碰撞火花四溅，震得虎口发麻，后退三步"
- id: 唯一标识符
- order: 顺序编号
- content: 剧情点描述，80-120字，目标100字
- type: setup/conflict/resolution/twist之一
- avoidWriting: 避免的写作方式，80字以上
- shouldWriting: 推荐的写作方式，80字以上
- styleMethod: 包含technique/style/tone/perspective/emphasis五个子字段
- formatSpecs: 包含wordCount(min:80/max:120/target:100)/paragraphRules/punctuationRules三个子字段
- consequences: 剧情点后果，30字以上
- nextPlotHint: 下一剧情点提示，20字以上
- specificDescription: 具体场景描述，60字以上
- styleMethod: 包含technique/style/tone/perspective/emphasis五个子字段
- formatSpecs: 包含wordCount/paragraphRules/punctuationRules三个子字段

所有字段都必须达到最低字数要求，输出必须是完整可解析的MessageBuilder格式JSON

接下来你将收到${segments.length}个文本段落，请逐个接收并在最后统一分析输出JSON。`
    });

    // 分段发送内容，每个段落单独发送
    segments.forEach((segment, index) => {
      messages.push({
        role: 'user',
        content: `【段落 ${index + 1}/${segments.length}】
${segment}`
      });

      messages.push({
        role: 'assistant',
        content: `我已接收第${index + 1}个段落的内容，理解了其中的剧情发展和写作特点。`
      });
    });

    // 计算总字数
    const totalWords = segments.join('').length;

    // 最终分析请求
    messages.push({
      role: 'user',
      content: `【开始完整分析】
现在请基于以上${segments.length}个段落的内容（约${totalWords}字），按照MessageBuilder格式输出完整的分析结果。

强制执行标准：
1. 剧情点数量：必须提取至少${Math.max(15, Math.floor(totalWords / 200))}个剧情点（按3000字15个剧情点的比例计算）， 剧情点数量要求：最多提取60个剧情点，避免出现重复，和超过60个，必须不要超过60个
2. 字数检查：每个avoidWriting和shouldWriting必须80字以上
3. 内容检查：每个content必须30-80字，详细描述具体剧情
4. MessageBuilder格式：必须包含message、changes、metadata三个顶层字段
5. 章节节点：必须包含完整的章节信息和创作指导
6. 剧情节点：必须包含完整的剧情点数组和相关信息
7. 覆盖检查：必须覆盖原文的每个重要剧情发展，不得遗漏
8. 质量检查：avoidWriting分析原文没有的不良方式，shouldWriting提取原文已有的优秀方式

【重要】分批处理规则：
 剧情点数量要求：最多提取60个剧情点，避免出现重复，和超过60个，禁止超过60个
注意：输出必须是完整可解析的MessageBuilder格式JSON，不符合格式的输出将被拒绝。

请严格按照系统要求的MessageBuilder格式输出完整的分析结果。`
    });

    return messages;
  }



  /**
   * 提取ACE框架内容
   */
  private extractFrameworkContent(framework: any): string {
    if (typeof framework === 'string') {
      return framework;
    }

    if (framework && typeof framework === 'object') {
      return framework.content || framework.text || JSON.stringify(framework);
    }

    return String(framework);
  }

  /**
   * 检查AI响应是否需要继续处理
   */
  private checkNeedsContinuation(response: string): boolean {
    try {
      // 使用AIResponseParser解析JSON
      const defaultValue: any = {
        metadata: {}
      };

      const parsed = AIResponseParser.parseJSON(response, defaultValue);

      // 如果解析失败（返回默认值），使用字段对称解析
      if (parsed === defaultValue) {
        console.warn('JSON解析失败，使用字段对称解析');
        return this.checkIncompleteResponse(response);
      }

      // 检查metadata中的needsContinuation字段
      if (parsed.metadata && parsed.metadata.needsContinuation === true) {
        return true;
      }

      // 如果没有找到needsContinuation字段，检查是否为不完整响应
      if (!parsed.metadata || parsed.metadata.needsContinuation === undefined) {
        console.warn('未找到needsContinuation字段，使用字段对称解析');
        return this.checkIncompleteResponse(response);
      }

      return false;
    } catch (error) {
      console.error('检查继续标识失败:', error);
      // JSON解析失败，使用字段对称解析
      return this.checkIncompleteResponse(response);
    }
  }

  /**
   * 检查不完整响应并使用字段对称解析
   */
  private checkIncompleteResponse(response: string): boolean {
    // 检查是否包含大量剧情点但结构不完整
    const plotPointMatches = response.match(/"id":\s*"point_\d+"/g);
    const plotPointCount = plotPointMatches ? plotPointMatches.length : 0;

    // 检查JSON结构的完整性
    const hasMessage = response.includes('"message"');
    const hasChanges = response.includes('"changes"');
    const hasMetadata = response.includes('"metadata"');
    const hasNeedsContinuation = response.includes('"needsContinuation"');

    // 如果有大量剧情点但缺少needsContinuation字段，认为需要继续
    if (plotPointCount >= 60 && !hasNeedsContinuation) {
      console.log(`检测到${plotPointCount}个剧情点但缺少needsContinuation字段，判断需要继续处理`);
      return true;
    }

    // 如果JSON结构不完整，也认为需要继续
    if (plotPointCount > 0 && (!hasMessage || !hasChanges || !hasMetadata)) {
      console.log('检测到不完整的JSON结构，判断需要继续处理');
      return true;
    }

    return false;
  }

  /**
   * 调用AI分析
   */
  private async callAIAnalysis(messages: any[], onChunk?: (chunk: string) => void): Promise<string> {
    // 获取当前模型和配置
    const currentModel = this.apiSettings.getCurrentModel() || 'gpt-4';
    const maxTokens = this.apiSettings.getMaxTokens?.() || 6000;
    const streamingEnabled = this.apiSettings.getStreamingEnabled?.() ?? true;

    // 获取AI配置（包括top_p和top_k）
    let aiConfig: any = {};
    try {
      const { configService } = await import('@/services/configService');
      aiConfig = await configService.getAIConfig();
    } catch (error) {
      console.warn('无法获取AI配置，使用默认值', error);
    }

    // 构建请求体
    const requestBody: any = {
      model: currentModel,
      messages: messages,
      temperature: aiConfig.temperature || 0.7,
      max_tokens: maxTokens,
      stream: streamingEnabled && !!onChunk
    };

    // 添加top_p参数支持
    if (aiConfig.topP !== undefined) {
      requestBody.top_p = aiConfig.topP;
    }

    // 添加top_k参数支持（注意：OpenAI API可能不支持，但为兼容性保留）
    if (aiConfig.topK !== undefined) {
      requestBody.top_k = aiConfig.topK;
    }

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`AI分析请求失败: ${response.status} ${response.statusText}`);
    }

    if (onChunk) {
      // 流式响应处理
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content || '';
                if (content) {
                  fullResponse += content;
                  onChunk(content);
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      }

      return fullResponse;
    } else {
      // 非流式响应处理
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '';
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string, metadata: any, previousResult?: ChapterAnalysisResult): Omit<ChapterAnalysisResult, 'success' | 'error'> {
    try {
      // 使用AIResponseParser解析JSON，提供默认值
      const defaultValue: any = {
        message: '',
        changes: [],
        metadata: {}
      };

      const parsed = AIResponseParser.parseJSON(response, defaultValue);

      // 检查是否是MessageBuilder格式
      let chapterData: any = {};
      let plotData: any = {};

      if (parsed.changes && Array.isArray(parsed.changes)) {
        // MessageBuilder格式：从changes数组中提取数据
        const chapterChange = parsed.changes.find((change: any) => change.data?.type === 'chapter');
        const plotChange = parsed.changes.find((change: any) => change.data?.type === 'plot');

        if (chapterChange?.data) {
          chapterData = chapterChange.data;
        }
        if (plotChange?.data) {
          plotData = plotChange.data;
        }
      } else {
        // 直接格式：使用parsed作为数据源
        chapterData = parsed;
        plotData = parsed;
      }

      // 当前批次的剧情点
      const currentPlotPoints = this.validatePlotPoints(plotData.plotPoints || []);

      // 如果有之前的结果，合并剧情点
      const allPlotPoints = previousResult && previousResult.plotPoints
        ? [...previousResult.plotPoints, ...currentPlotPoints]
        : currentPlotPoints;

      return {
        plotPoints: allPlotPoints,
        overallStyle: chapterData.overallStyle || plotData.overallStyle || (previousResult?.overallStyle) || '',
        mainCharacters: Array.isArray(plotData.relatedCharacters) ? plotData.relatedCharacters :
                       Array.isArray(chapterData.mainCharacters) ? chapterData.mainCharacters :
                       previousResult?.mainCharacters || [],
        conflictLevel: Math.max(1, Math.min(5, plotData.conflictLevel || previousResult?.conflictLevel || 3)),

        themes: Array.isArray(chapterData.themes) ? chapterData.themes : previousResult?.themes || [],
        writingTechniques: Array.isArray(chapterData.chapterTechniques) ? chapterData.chapterTechniques : previousResult?.writingTechniques || [],
        // 章节相关字段
        chapterTitle: chapterData.title || '',
        chapterDescription: chapterData.description || '',
        chapterStyle: chapterData.chapterStyle || '',
        chapterTechniques: Array.isArray(chapterData.chapterTechniques) ? chapterData.chapterTechniques : [],
        chapterGoals: Array.isArray(chapterData.chapterGoals) ? chapterData.chapterGoals : [],
        rhythmPhase: ['setup', 'conflict', 'climax', 'transition'].includes(chapterData.rhythmPhase) ? chapterData.rhythmPhase : 'setup',
        rhythmGuidance: chapterData.rhythmGuidance || '',
        // 剧情节点相关字段
        plotTitle: plotData.title || '',
        plotDescription: plotData.description || '',
        plotType: ['conflict', 'twist', 'climax', 'resolution'].includes(plotData.plotType) ? plotData.plotType : 'conflict',
        suspenseElements: Array.isArray(plotData.suspenseElements) ? plotData.suspenseElements : [],
        relatedCharacters: Array.isArray(plotData.relatedCharacters) ? plotData.relatedCharacters : [],
        creativeNotes: plotData.creativeNotes || chapterData.creativeNotes || '',
        analysisMetadata: metadata
      };
    } catch (error) {
      console.error('解析AI响应失败:', error);
      return {
        plotPoints: [],
        overallStyle: '',
        mainCharacters: [],
        conflictLevel: 1,
        themes: [],
        writingTechniques: [],
        // 章节相关字段默认值
        chapterTitle: '',
        chapterDescription: '',
        chapterStyle: '',
        chapterTechniques: [],
        chapterGoals: [],
        rhythmPhase: 'setup',
        rhythmGuidance: '',
        // 剧情节点相关字段默认值
        plotTitle: '',
        plotDescription: '',
        plotType: 'conflict',
        suspenseElements: [],
        relatedCharacters: [],
        creativeNotes: '',
        analysisMetadata: metadata
      };
    }
  }

  /**
   * 根据剧情点内容动态计算字数控制范围
   */
  private calculateDynamicWordCount(content: string): { min: number; max: number; target: number } {
    const currentLength = content ? content.length : 0;

    if (currentLength === 0) {
      // 如果没有内容，使用默认的100字左右
      return { min: 80, max: 120, target: 100 };
    }

    // 基于当前内容长度，设置合理的字数范围
    // 目标是保持在100字左右，但允许根据实际内容调整
    const target = Math.max(100, Math.min(150, currentLength + 20)); // 在当前基础上略微增加
    const min = Math.max(80, target - 20);
    const max = Math.min(180, target + 30);

    return { min, max, target };
  }

  /**
   * 验证和修复剧情点数据
   */
  private validatePlotPoints(plotPoints: any[]): PlotPointExample[] {
    if (!Array.isArray(plotPoints)) {
      return [];
    }

    return plotPoints.map((point, index) => ({
      id: point.id || `point_${String(index + 1).padStart(3, '0')}`,
      order: point.order || index + 1,
      content: point.content || '剧情点内容',
      type: ['setup', 'conflict', 'resolution', 'twist'].includes(point.type) ? point.type : 'setup',
      avoidWriting: point.avoidWriting || '避免模糊表达和夸张比喻',
      shouldWriting: point.shouldWriting || '使用具体动作和客观叙述',
      // 处理支持的扩展字段
      styleMethod: point.styleMethod ? {
        technique: point.styleMethod.technique || '直接描写',
        style: point.styleMethod.style || '客观叙述',
        tone: point.styleMethod.tone || '中性',
        perspective: point.styleMethod.perspective || '第三人称',
        emphasis: point.styleMethod.emphasis || '行动和对话并重'
      } : undefined,
      formatSpecs: point.formatSpecs ? {
        wordCount: {
          min: point.formatSpecs.wordCount?.min || this.calculateDynamicWordCount(point.content).min,
          max: point.formatSpecs.wordCount?.max || this.calculateDynamicWordCount(point.content).max,
          target: point.formatSpecs.wordCount?.target || this.calculateDynamicWordCount(point.content).target
        },
        paragraphRules: {
          maxSentencesPerParagraph: point.formatSpecs.paragraphRules?.maxSentencesPerParagraph || 4,
          paragraphBreakRules: point.formatSpecs.paragraphRules?.paragraphBreakRules || '逻辑完整后分段'
        },
        punctuationRules: {
          dialogueFormat: point.formatSpecs.punctuationRules?.dialogueFormat || '「」',
          emphasisFormat: point.formatSpecs.punctuationRules?.emphasisFormat || '适度使用',
          pauseFormat: point.formatSpecs.punctuationRules?.pauseFormat || '自然停顿'
        },

      } : undefined
    }));
  }

  /**
   * 简化的分批处理检查（已废弃，使用递归调用替代）
   */
  private async analyzeBatchContent(): Promise<ChapterAnalysisResult> {
    // 返回空结果，实际使用递归调用实现
    return {
      plotPoints: [],
      overallStyle: '',
      mainCharacters: [],
      conflictLevel: 1,

      themes: [],
      writingTechniques: [],
      plotType: 'conflict',
      suspenseElements: [],
      chapterGoals: [],
      chapterStyle: '',
      chapterTechniques: [],
      rhythmPhase: 'setup',
      rhythmGuidance: '',
      relatedCharacters: [],
      creativeNotes: '',
      analysisMetadata: {
        segmentCount: 0,
        totalWords: 0,
        processingTime: 0,
        analysisDepth: 'standard'
      },
      success: false,
      error: '已废弃的方法'
    };
  }
}
