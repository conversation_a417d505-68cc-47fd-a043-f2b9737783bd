"use client";

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { AIContinueAdapter } from '@/adapters/ai/AIContinueAdapter';
import { createAnimationFactory } from '@/factories/animation';
import { PromptCategory } from '@/lib/db/dexie';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import CustomSelectorDialog from './CustomSelectorDialog';
import AIContinuePreview from './AIContinuePreview';
import { aiServiceProvider } from '@/services/ai/AIServiceProvider';
import { ConversationMessage } from '@/factories/ai/services/AIWritingService';
import { dialogContinuationManager } from '@/utils/ai/DialogContinuationManager';
import { UnifiedAssociationButton } from '@/components/ui/UnifiedAssociationButton';
import { AssociationOverview } from '@/components/ui/AssociationOverview';
import { OutlineManagementButton } from '@/components/ui/OutlineManagementButton';

// 选择器项目接口
interface SelectorItem {
  id: string;
  name: string;
  description?: string;
  disabled?: boolean;
  content?: string;  // 章节内容
  order?: number;    // 章节顺序
}

interface AIContinueDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertContent: (content: string) => void;
  initialContext?: string;
  initialContextMessages?: ConversationMessage[]; // 新增：预构建的上下文消息
  bookId?: string;
  currentChapterId?: string; // 当前章节ID，用于禁用当前章节的选择
}

/**
 * AI续写对话框组件
 * 用于使用AI续写小说内容
 */
export const AIContinueDialog: React.FC<AIContinueDialogProps> = ({
  isOpen,
  onClose,
  onInsertContent,
  initialContext = '',
  initialContextMessages = [],
  bookId = '',
  currentChapterId = ''
}) => {
  // 创建AI续写适配器
  const [adapter] = useState(() => {
    // 创建适配器实例
    const adapterInstance = new AIContinueAdapter();

    // 调试信息
    console.log('创建的适配器实例:', adapterInstance);
    console.log('适配器方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(adapterInstance)));

    return adapterInstance;
  });

  // 续写参数
  const [context, setContext] = useState(initialContext);
  const [continueRequirements, setContinueRequirements] = useState(() => {
    // 从localStorage加载保存的续写要求
    const savedReqs = localStorage.getItem(`ai-continue-requirements-${bookId}`);
    return savedReqs || '';
  });
  const [continueStyle, setContinueStyle] = useState(() => {
    // 从localStorage加载保存的续写风格
    const savedStyle = localStorage.getItem(`ai-continue-style-${bookId}`);
    return savedStyle || '';
  });
  const [futurePlot, setFuturePlot] = useState(() => {
    // 从localStorage加载保存的后续剧情
    const savedPlot = localStorage.getItem(`ai-continue-plot-${bookId}`);
    return savedPlot || '';
  });

  // 提示词模板管理状态
  const [isPromptTemplateManagerOpen, setIsPromptTemplateManagerOpen] = useState(false);
  const [promptTemplateCategory, setPromptTemplateCategory] = useState<PromptCategory>(PromptCategory.CONTINUE_REQUIREMENTS);

  // 续写结果 - 使用localStorage持久化
  const [continuedContent, setContinuedContent] = useState<string>(() => {
    // 从localStorage加载之前生成的内容
    const savedContent = localStorage.getItem(`ai-continue-content-${bookId}`);
    return savedContent || '';
  });
  const [isContinuing, setIsContinuing] = useState(false);

  // 对话历史状态 - 使用localStorage持久化
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>(() => {
    // 从localStorage加载对话历史
    const savedHistory = localStorage.getItem(`ai-continue-history-${bookId}`);
    return savedHistory ? JSON.parse(savedHistory) : [];
  });

  // 新增：续写历史记录状态
  const [continueHistory, setContinueHistory] = useState<Array<{
    id: string;
    content: string;
    timestamp: number;
    status: 'generating' | 'completed' | 'error';
    isActive: boolean;
  }>>(() => {
    // 从localStorage加载续写历史
    const savedContinueHistory = localStorage.getItem(`ai-continue-bubble-history-${bookId}`);
    return savedContinueHistory ? JSON.parse(savedContinueHistory) : [];
  });
  const [currentContinueId, setCurrentContinueId] = useState<string | null>(null);

  // 关联元素数据
  const [characters, setCharacters] = useState<SelectorItem[]>([]);
  const [terminologies, setTerminologies] = useState<SelectorItem[]>([]);
  const [worldBuildings, setWorldBuildings] = useState<SelectorItem[]>([]);
  const [chapters, setChapters] = useState<SelectorItem[]>([]);
  const [outlines, setOutlines] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 使用props中的currentChapterId

  // 选中的关联元素 - 使用localStorage持久化
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-continue-selected-characters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-continue-selected-terminologies-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-continue-selected-worldbuildings-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-continue-selected-chapters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedOutlineNodeIds, setSelectedOutlineNodeIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-continue-selected-outlines-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });



  // 弹窗状态
  const [isCharacterSelectorOpen, setIsCharacterSelectorOpen] = useState(false);
  const [isTerminologySelectorOpen, setIsTerminologySelectorOpen] = useState(false);
  const [isWorldBuildingSelectorOpen, setIsWorldBuildingSelectorOpen] = useState(false);
  const [isChapterSelectorOpen, setIsChapterSelectorOpen] = useState(false);

  // 对话框引用
  const dialogRef = useRef<HTMLDivElement>(null);

  // 解析上下文信息的函数
  const parseContextInfo = (contextText: string) => {
    if (!contextText) {
      // 当上下文为空时，检查是否有跨章节的前后章节
      // 这种情况通常发生在空章节中间插入内容
      console.log('上下文为空，检查是否为跨章节衔接模式');

      // 如果当前章节ID存在，尝试判断是否有前后章节
      if (currentChapterId && chapters.length > 0) {
        console.log('开始跨章节检查:', {
          currentChapterId,
          chaptersCount: chapters.length,
          chapters: chapters.map(ch => ({
            id: ch.id,
            name: ch.name,
            order: ch.order,
            contentLength: ch.content?.length || 0
          }))
        });

        const currentChapter = chapters.find(ch => ch.id === currentChapterId);
        if (currentChapter) {
          const currentOrder = currentChapter.order || 0;
          const hasBeforeChapters = chapters.some(ch => (ch.order || 0) < currentOrder && ch.content && ch.content.length > 0);
          const hasAfterChapters = chapters.some(ch => (ch.order || 0) > currentOrder && ch.content && ch.content.length > 0);

          // 计算前后章节的总字数
          const beforeChaptersLength = chapters
            .filter(ch => (ch.order || 0) < currentOrder && ch.content)
            .reduce((total, ch) => total + (ch.content?.length || 0), 0);
          const afterChaptersLength = chapters
            .filter(ch => (ch.order || 0) > currentOrder && ch.content)
            .reduce((total, ch) => total + (ch.content?.length || 0), 0);

          console.log('跨章节检查结果:', {
            currentChapter: {
              id: currentChapter.id,
              name: currentChapter.name,
              order: currentOrder
            },
            hasBeforeChapters,
            hasAfterChapters,
            beforeChaptersLength,
            afterChaptersLength,
            beforeChapters: chapters.filter(ch => (ch.order || 0) < currentOrder).map(ch => ({
              name: ch.name,
              order: ch.order,
              contentLength: ch.content?.length || 0
            })),
            afterChapters: chapters.filter(ch => (ch.order || 0) > currentOrder).map(ch => ({
              name: ch.name,
              order: ch.order,
              contentLength: ch.content?.length || 0
            }))
          });

          return {
            hasBeforeContext: hasBeforeChapters,
            hasAfterContext: hasAfterChapters,
            beforeContextLength: beforeChaptersLength,
            afterContextLength: afterChaptersLength
          };
        } else {
          console.log('未找到当前章节:', currentChapterId);
        }
      } else {
        console.log('跨章节检查条件不满足:', {
          hasCurrentChapterId: !!currentChapterId,
          chaptersLength: chapters.length
        });
      }

      return {
        hasBeforeContext: false,
        hasAfterContext: false,
        beforeContextLength: 0,
        afterContextLength: 0
      };
    }

    try {
      // 检查是否包含前文和后文标记（支持多种格式）
      const hasBeforeContextMarker = /【第\d+章前文内容】|【当前章节前文】|【章节前文】/.test(contextText);
      const hasAfterContextMarker = /【第\d+章后文内容】|【当前章节后文】|【章节后文】/.test(contextText);

      console.log('上下文标记检查:', {
        hasBeforeContextMarker,
        hasAfterContextMarker,
        contextLength: contextText.length,
        contextPreview: contextText.substring(0, 200) + '...'
      });

      // 尝试提取前文内容
      let beforeText = '';
      let beforeContextLength = 0;

      // 匹配前文标记并提取内容
      const beforeMatches = contextText.match(/【第\d+章前文内容】[\s\S]*?(?=【第\d+章后文内容】|【第\d+章完整内容】|$)/);
      if (beforeMatches) {
        beforeText = beforeMatches[0].replace(/【第\d+章前文内容】[^】]*】?/, '').trim();
        beforeContextLength = beforeText.length;
      } else {
        // 尝试其他前文标记格式
        const altBeforeMatches = contextText.match(/【当前章节前文】[\s\S]*?(?=【当前章节后文】|【第\d+章完整内容】|$)/);
        if (altBeforeMatches) {
          beforeText = altBeforeMatches[0].replace(/【当前章节前文】/, '').trim();
          beforeContextLength = beforeText.length;
        }
      }

      // 尝试提取后文内容
      let afterText = '';
      let afterContextLength = 0;

      // 匹配后文标记并提取内容
      const afterMatches = contextText.match(/【第\d+章后文内容】[\s\S]*?(?=【第\d+章完整内容】|$)/);
      if (afterMatches) {
        afterText = afterMatches[0].replace(/【第\d+章后文内容】[^】]*】?/, '').trim();
        afterContextLength = afterText.length;
      } else {
        // 尝试其他后文标记格式
        const altAfterMatches = contextText.match(/【当前章节后文】[\s\S]*?(?=【第\d+章完整内容】|$)/);
        if (altAfterMatches) {
          afterText = altAfterMatches[0].replace(/【当前章节后文】/, '').trim();
          afterContextLength = afterText.length;
        }
      }

      // 如果没有找到明确的前文后文标记，但有完整章节标记，说明有跨章节内容
      if (!hasBeforeContextMarker && !hasAfterContextMarker) {
        const hasChapterContent = /【第\d+章完整内容】/.test(contextText);
        if (hasChapterContent) {
          // 计算所有章节内容的总长度作为前文或后文
          const chapterMatches = contextText.match(/【第\d+章完整内容[^】]*】[\s\S]*?(?=【第\d+章完整内容】|$)/g);
          if (chapterMatches) {
            const totalLength = chapterMatches.reduce((total, match) => {
              const content = match.replace(/【第\d+章完整内容[^】]*】[^】]*】?/, '').trim();
              return total + content.length;
            }, 0);

            return {
              hasBeforeContext: true,
              hasAfterContext: false,
              beforeContextLength: totalLength,
              afterContextLength: 0
            };
          }
        }
      }

      console.log('上下文解析结果:', {
        hasBeforeContext: hasBeforeContextMarker || beforeContextLength > 0,
        hasAfterContext: hasAfterContextMarker || afterContextLength > 0,
        beforeContextLength,
        afterContextLength
      });

      return {
        hasBeforeContext: hasBeforeContextMarker || beforeContextLength > 0,
        hasAfterContext: hasAfterContextMarker || afterContextLength > 0,
        beforeContextLength,
        afterContextLength
      };
    } catch (error) {
      console.error('解析上下文信息时出错:', error);
      return {
        hasBeforeContext: false,
        hasAfterContext: false,
        beforeContextLength: 0,
        afterContextLength: 0
      };
    }
  };

  // 提取衔接关键信息的函数
  const extractConnectionKeyInfo = useCallback((contextText: string) => {
    if (!contextText) return { beforeContextEnd: '', afterContextStart: '' };

    let beforeContextEnd = '';
    let afterContextStart = '';

    try {
      // 提取前文结尾（最后3句话）
      const beforeMatches = contextText.match(/【第\d+章的?前文[^】]*】[\s\S]*?(?=【第\d+章的?后文|【第\d+章完整内容】|$)/);
      if (beforeMatches) {
        const beforeContent = beforeMatches[0].replace(/【第\d+章的?前文[^】]*】/, '').trim();
        const sentences = beforeContent.split(/[。！？.!?]/).filter(s => s.trim());
        if (sentences.length > 0) {
          const lastSentences = sentences.slice(-3); // 取最后3句
          beforeContextEnd = lastSentences.join('。') + '。';
        }
      } else {
        // 尝试从完整章节内容中提取结尾
        const chapterMatches = contextText.match(/【第\d+章完整内容[^】]*】[\s\S]*?(?=【第\d+章完整内容】|$)/);
        if (chapterMatches) {
          const chapterContent = chapterMatches[0].replace(/【第\d+章完整内容[^】]*】[^】]*】?/, '').trim();
          const sentences = chapterContent.split(/[。！？.!?]/).filter(s => s.trim());
          if (sentences.length > 0) {
            const lastSentences = sentences.slice(-3); // 取最后3句
            beforeContextEnd = lastSentences.join('。') + '。';
          }
        }
      }

      // 提取后文开头（前3句话）
      const afterMatches = contextText.match(/【第\d+章的?后文[^】]*】[\s\S]*?(?=【第\d+章完整内容】|$)/);
      if (afterMatches) {
        const afterContent = afterMatches[0].replace(/【第\d+章的?后文[^】]*】/, '').trim();
        const sentences = afterContent.split(/[。！？.!?]/).filter(s => s.trim());
        if (sentences.length > 0) {
          const firstSentences = sentences.slice(0, 3); // 取前3句
          afterContextStart = firstSentences.join('。') + '。';
        }
      }

    } catch (error) {
      console.error('提取衔接关键信息时出错:', error);
    }

    return { beforeContextEnd, afterContextStart };
  }, []);

  // 使用useMemo缓存上下文解析结果
  const contextInfo = useMemo(() => parseContextInfo(context), [context, currentChapterId, chapters]);

  // 使用useMemo缓存衔接关键信息
  const connectionKeyInfo = useMemo(() => extractConnectionKeyInfo(context), [context, extractConnectionKeyInfo]);

  // 当初始上下文变化时更新状态
  useEffect(() => {
    setContext(initialContext);
  }, [initialContext]);

  // 当对话框打开时加载数据
  useEffect(() => {
    if (isOpen) {
      // 加载关联元素
      loadRelatedElements();

      // 从localStorage加载之前生成的内容（如果没有在初始化时加载）
      if (!continuedContent) {
        const savedContent = localStorage.getItem(`ai-continue-content-${bookId}`);
        if (savedContent) {
          setContinuedContent(savedContent);
        }
      }
    }
  }, [isOpen, bookId, continuedContent]);

  // 监听关联元素选择变化并保存到localStorage
  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-continue-selected-characters-${bookId}`, JSON.stringify(selectedCharacterIds));
    }
  }, [selectedCharacterIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-continue-selected-terminologies-${bookId}`, JSON.stringify(selectedTerminologyIds));
    }
  }, [selectedTerminologyIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-continue-selected-worldbuildings-${bookId}`, JSON.stringify(selectedWorldBuildingIds));
    }
  }, [selectedWorldBuildingIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-continue-selected-chapters-${bookId}`, JSON.stringify(selectedChapterIds));
    }
  }, [selectedChapterIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-continue-selected-outlines-${bookId}`, JSON.stringify(selectedOutlineNodeIds));
    }
  }, [selectedOutlineNodeIds, bookId]);



  /**
   * 加载关联元素（人物、术语、世界观、章节）
   */
  const loadRelatedElements = async () => {
    setIsLoading(true);

    try {
      // 加载人物
      try {
        const { characterRepository } = await import('@/lib/db/repositories');
        const charactersData = await characterRepository.getAllByBookId(bookId);
        // 转换为SelectorItem格式
        const characterItems: SelectorItem[] = charactersData.map(char => ({
          id: char.id || '',
          name: char.name || '',
          description: char.description
        })).filter(item => item.id); // 过滤掉没有id的项
        setCharacters(characterItems);
      } catch (error) {
        console.error('加载人物数据失败:', error);
      }

      // 加载术语
      try {
        const { terminologyRepository } = await import('@/lib/db/repositories');
        const terminologiesData = await terminologyRepository.getAllByBookId(bookId);
        // 转换为SelectorItem格式
        const terminologyItems: SelectorItem[] = terminologiesData.map(term => ({
          id: term.id || '',
          name: term.name || '',
          description: term.description
        })).filter(item => item.id); // 过滤掉没有id的项
        setTerminologies(terminologyItems);
      } catch (error) {
        console.error('加载术语数据失败:', error);
      }

      // 加载世界观
      try {
        const { worldBuildingRepository } = await import('@/lib/db/repositories');
        const worldBuildingsData = await worldBuildingRepository.getAllByBookId(bookId);
        // 转换为SelectorItem格式
        const worldBuildingItems: SelectorItem[] = worldBuildingsData.map(wb => ({
          id: wb.id || '',
          name: wb.name || '',
          description: wb.description
        })).filter(item => item.id); // 过滤掉没有id的项
        setWorldBuildings(worldBuildingItems);
      } catch (error) {
        console.error('加载世界观数据失败:', error);
      }

      // 加载章节 - 使用多种方法尝试获取章节数据
      try {
        console.log('开始加载章节数据, bookId =', bookId);
        let chaptersData: any[] = [];

        // 尝试方法1: 使用 src/lib/db/repositories/chapterRepository.ts
        try {
          const { chapterRepository } = await import('@/lib/db/repositories');
          const data = await chapterRepository.getAllByBookId(bookId);
          console.log('通过 lib/db/repositories/chapterRepository 获取到章节数据:', data);

          if (data && data.length > 0) {
            chaptersData = data;
          }
        } catch (error) {
          console.error('通过 lib/db/repositories/chapterRepository 获取章节数据失败:', error);
        }

        // 如果方法1失败，尝试方法2: 使用 src/db/chapterRepository.ts
        if (chaptersData.length === 0) {
          try {
            const { ChapterRepository } = await import('@/db/chapterRepository');
            const chapterRepo = new ChapterRepository();
            const data = await chapterRepo.getChaptersByBookId(bookId);
            console.log('通过 db/chapterRepository 获取到章节数据:', data);

            if (data && data.length > 0) {
              chaptersData = data;
            }
          } catch (error) {
            console.error('通过 db/chapterRepository 获取章节数据失败:', error);
          }
        }

        // 如果方法2失败，尝试方法3: 直接从数据库获取
        if (chaptersData.length === 0) {
          try {
            // 尝试使用 NovelDatabase
            const { db: novelDb } = await import('@/lib/db/dexie');
            const data = await novelDb.chapters.where('bookId').equals(bookId).toArray();
            console.log('通过 NovelDatabase 直接查询获取到章节数据:', data);

            if (data && data.length > 0) {
              chaptersData = data;
            }
          } catch (error) {
            console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
          }
        }

        // 如果方法3失败，尝试方法4: 使用 AppDatabase
        if (chaptersData.length === 0) {
          try {
            const { db: appDb } = await import('@/db/database');
            const data = await appDb.chapters.where('bookId').equals(bookId).toArray();
            console.log('通过 AppDatabase 直接查询获取到章节数据:', data);

            if (data && data.length > 0) {
              chaptersData = data;
            }
          } catch (error) {
            console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
          }
        }

        // 如果获取到了章节数据
        if (chaptersData.length > 0) {
          // 按章节顺序排序
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 0;
            const orderB = b.order !== undefined ? b.order : 0;
            return orderA - orderB;
          });

          // 转换为SelectorItem格式
          const chapterItems: SelectorItem[] = sortedChapters.map((chapter, index) => {
            const chapterNumber = chapter.order !== undefined ? chapter.order + 1 : index + 1;
            const isCurrentChapter = chapter.id === currentChapterId;

            return {
              id: chapter.id || '',
              name: chapter.title || `第${chapterNumber}章`,
              description: chapter.content ? `${chapter.content.substring(0, 100)}...` : '', // 显示章节内容的前100个字符
              disabled: isCurrentChapter, // 禁用当前章节，因为已经通过上下文获取机制获取了当前章节内容
              content: chapter.content || '', // 保存完整章节内容
              order: chapter.order !== undefined ? chapter.order : index // 保存章节顺序
            };
          }).filter(item => item.id); // 过滤掉没有id的项

          // 从选中的章节ID中移除当前章节ID
          if (currentChapterId && selectedChapterIds.includes(currentChapterId)) {
            setSelectedChapterIds(selectedChapterIds.filter(id => id !== currentChapterId));
          }

          console.log('成功加载章节数据，共', chapterItems.length, '章');
          setChapters(chapterItems);
        } else {
          console.warn('未能获取到任何章节数据');
          setChapters([]);
        }
      } catch (error) {
        console.error('加载章节数据失败:', error);
        setChapters([]);
      }

      // 加载大纲
      try {
        const { outlineRepository } = await import('@/lib/db/repositories');
        const outlinesData = await outlineRepository.getAllByBookId(bookId);
        setOutlines(outlinesData);
      } catch (error) {
        console.error('加载大纲数据失败:', error);
      }
    } catch (error) {
      console.error('加载关联元素失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 监听输入变化，保存到localStorage
  useEffect(() => {
    if (continueRequirements) {
      localStorage.setItem(`ai-continue-requirements-${bookId}`, continueRequirements);
    }
  }, [continueRequirements, bookId]);

  useEffect(() => {
    if (continueStyle) {
      localStorage.setItem(`ai-continue-style-${bookId}`, continueStyle);
    }
  }, [continueStyle, bookId]);

  useEffect(() => {
    if (futurePlot) {
      localStorage.setItem(`ai-continue-plot-${bookId}`, futurePlot);
    }
  }, [futurePlot, bookId]);

  // 保存对话历史到localStorage
  useEffect(() => {
    if (bookId && conversationHistory.length > 0) {
      localStorage.setItem(`ai-continue-history-${bookId}`, JSON.stringify(conversationHistory));
    }
  }, [conversationHistory, bookId]);

  // 保存生成的内容到localStorage
  useEffect(() => {
    if (bookId && continuedContent) {
      localStorage.setItem(`ai-continue-content-${bookId}`, continuedContent);
    }
  }, [continuedContent, bookId]);

  // 保存续写历史到localStorage
  useEffect(() => {
    if (bookId && continueHistory.length > 0) {
      localStorage.setItem(`ai-continue-bubble-history-${bookId}`, JSON.stringify(continueHistory));
    }
  }, [continueHistory, bookId]);

  // 处理提示词模板选择
  const handleSelectTemplate = (template: any) => {
    console.log('选择模板:', template);
    // 根据当前选择的提示词类型，更新对应的状态
    switch (promptTemplateCategory) {
      case PromptCategory.CONTINUE_REQUIREMENTS:
        setContinueRequirements(template.content);
        break;
      case PromptCategory.CONTINUE_STYLE:
        setContinueStyle(template.content);
        break;
      default:
        break;
    }
    setIsPromptTemplateManagerOpen(false);
  };

  /**
   * 处理统一关联管理的变化
   */
  const handleAssociationsChange = (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    setSelectedChapterIds(associations.chapterIds);
    setSelectedCharacterIds(associations.characterIds);
    setSelectedTerminologyIds(associations.terminologyIds);
    setSelectedWorldBuildingIds(associations.worldBuildingIds);
  };

  // 处理流式输出的回调函数
  const handleStreamChunk = (chunk: string) => {
    setContinuedContent(prev => prev + chunk);
  };

  // 处理续写
  const handleContinue = async () => {
    // 调试信息：检查adapter对象
    console.log('adapter类型:', adapter);
    console.log('adapter方法:', Object.getOwnPropertyNames(adapter));
    console.log('adapter原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(adapter)));

    // 准备关联元素数据
    const selectedCharacters = characters.filter(char => char.id && selectedCharacterIds.includes(char.id));
    const selectedTerms = terminologies.filter(term => term.id && selectedTerminologyIds.includes(term.id));
    const selectedWorldViews = worldBuildings.filter(wb => wb.id && selectedWorldBuildingIds.includes(wb.id));
    const selectedChaptersData = chapters.filter(chapter => chapter.id && selectedChapterIds.includes(chapter.id) && !chapter.disabled);

    // 获取当前章节信息
    console.log('当前章节ID:', currentChapterId);
    console.log('所有章节:', chapters);

    // 查找当前章节
    const currentChapter = chapters.find(chapter => chapter.id === currentChapterId);
    console.log('当前章节:', currentChapter);

    // 如果找到当前章节，设置章节名称
    if (currentChapter) {
      console.log('找到当前章节:', currentChapter.name);

      // 创建一个包含完整信息的当前章节对象
      const currentChapterInfo = {
        id: currentChapter.id,
        name: currentChapter.name,
        description: currentChapter.description,
        content: currentChapter.content,
        order: currentChapter.order
      };

      // 设置当前章节信息
      try {
        adapter.setCurrentChapter(currentChapterInfo);
        console.log('成功设置当前章节信息:', currentChapterInfo);
      } catch (error) {
        console.error('设置当前章节信息失败:', error);
      }
    } else {
      console.warn('未找到当前章节，ID:', currentChapterId);
    }

    console.log('关联元素数据:', {
      selectedCharacters,
      selectedTerms,
      selectedWorldViews,
      selectedChaptersData
    });

    // 设置适配器参数
    adapter.setContext(context);
    adapter.setContinueRequirements(continueRequirements);
    adapter.setContinueStyle(continueStyle);
    adapter.setFuturePlot(futurePlot);

    // 设置关联元素数据 - 转换为适配器期望的格式，并添加安全检查
    const mappedCharacters = selectedCharacters.map(({ id, name, description }) => ({ id, name, description }));
    const mappedTerms = selectedTerms.map(({ id, name, description }) => ({ id, name, description }));
    const mappedWorldViews = selectedWorldViews.map(({ id, name, description }) => ({ id, name, description }));
    // 确保包含章节内容
    const mappedChapters = selectedChaptersData.map(chapter => ({
      id: chapter.id,
      name: chapter.name,
      description: chapter.description,
      content: chapter.content,
      order: chapter.order
    }));

    // 使用安全的方法调用，检查方法是否存在
    try {
      // 尝试直接调用方法
      adapter.setCharacters(mappedCharacters);
      console.log('成功设置人物数据');
    } catch (error) {
      console.error('设置人物数据失败:', error);
    }

    try {
      adapter.setTerminologies(mappedTerms);
      console.log('成功设置术语数据');
    } catch (error) {
      console.error('设置术语数据失败:', error);
    }

    try {
      adapter.setWorldBuildings(mappedWorldViews);
      console.log('成功设置世界观数据');
    } catch (error) {
      console.error('设置世界观数据失败:', error);
    }

    try {
      adapter.setChapters(mappedChapters);
      console.log('成功设置章节数据');
    } catch (error) {
      console.error('设置章节数据失败:', error);
    }

    try {
      adapter.setSelectedCharacterIds(selectedCharacterIds);
      adapter.setSelectedTerminologyIds(selectedTerminologyIds);
      adapter.setSelectedWorldBuildingIds(selectedWorldBuildingIds);
      adapter.setSelectedChapterIds(selectedChapterIds);
      adapter.setCurrentChapterId(currentChapterId);
      console.log('成功设置选中的ID数据');
    } catch (error) {
      console.error('设置选中的ID数据失败:', error);
    }

    if (bookId) {
      adapter.setBookId(bookId);
    }

    setIsContinuing(true);

    // 创建新的续写记录
    const newContinueId = `continue-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const newContinueRecord = {
      id: newContinueId,
      content: '',
      timestamp: Date.now(),
      status: 'generating' as const,
      isActive: true
    };

    // 将之前的记录设为非活跃状态，添加新记录
    setContinueHistory(prev => [
      ...prev.map(record => ({ ...record, isActive: false })),
      newContinueRecord
    ]);
    setCurrentContinueId(newContinueId);

    // 清空当前内容，准备接收新的内容
    setContinuedContent('');

    // 不再清空对话历史中的助手消息，保留完整上下文
    // 这样可以确保关联元素信息在对话历史中被保留
    // 如果需要重置对话，应该使用onRestart方法

    try {
      // 使用DialogContinuationManager处理对话历史
      const result = await dialogContinuationManager.handleContinuation({
        currentContent: '',  // 使用空字符串作为当前内容，确保完全重新生成
        continuePrompt: continueRequirements || '请继续上述内容，保持一致的风格和情节走向。',
        continueMode: 'continue',
        bookId: bookId || '',
        conversationHistory: conversationHistory,
        serviceType: 'writing',

        // 上下文信息 - 优先使用预构建的消息
        context: context,
        contextMessages: initialContextMessages.length > 0 ? initialContextMessages : undefined,

        // 直接传递上下文状态信息（避免重复解析）
        hasBeforeContext: contextInfo.hasBeforeContext,
        hasAfterContext: contextInfo.hasAfterContext,
        beforeContextLength: contextInfo.beforeContextLength,
        afterContextLength: contextInfo.afterContextLength,

        // 传递衔接关键信息（确保连贯性）
        beforeContextEnd: connectionKeyInfo.beforeContextEnd,
        afterContextStart: connectionKeyInfo.afterContextStart,

        // 【修复】用户指令消息组 - 传递所有三个字段
        continueRequirements: continueRequirements,
        continueStyle: continueStyle,
        futurePlot: futurePlot,

        // 关联元素 - 确保每次都传递完整的关联元素信息
        chapters: mappedChapters,
        selectedChapterIds: selectedChapterIds,
        characters: mappedCharacters,
        selectedCharacterIds: selectedCharacterIds,
        terminologies: mappedTerms,
        selectedTerminologyIds: selectedTerminologyIds,
        worldBuildings: mappedWorldViews,
        selectedWorldBuildingIds: selectedWorldBuildingIds,

        // 大纲关联元素
        outlines: outlines,
        selectedOutlineNodeIds: selectedOutlineNodeIds,
        outlineContextMode: 'hierarchy',

        currentChapterId: currentChapterId
      });

      // 更新对话历史
      setConversationHistory(result.updatedHistory);

      // 始终使用流式输出
      let generatedText = '';
      // 创建一个临时变量来存储新内容
      let newContent = '';

      // 直接使用DialogContinuationManager构建的消息进行流式请求
      await aiServiceProvider.sendStreamingRequest(
        result.messages,
        (chunk) => {
          generatedText += chunk;
          newContent += chunk;
          // 更新当前显示的内容
          setContinuedContent(newContent);

          // 更新续写历史记录
          setContinueHistory(prev => prev.map(record =>
            record.id === newContinueId
              ? { ...record, content: newContent }
              : record
          ));

          handleStreamChunk(chunk);
        },
        {
          temperature: 0.7,
          streaming: true
        }
      );

      // 保存生成的内容到对话历史
      if (result.updatedHistory.length > 0) {
        const newHistory = [...result.updatedHistory];
        newHistory.push({
          role: 'assistant',
          content: generatedText
        });
        setConversationHistory(newHistory);
      }

      // 更新续写记录状态为完成
      setContinueHistory(prev => prev.map(record =>
        record.id === newContinueId
          ? { ...record, status: 'completed' as const }
          : record
      ));
    } catch (error: any) {
      console.error('续写内容失败', error);
      setContinuedContent(`续写失败: ${error.message || '未知错误'}`);

      // 更新续写记录状态为错误
      setContinueHistory(prev => prev.map(record =>
        record.id === newContinueId
          ? { ...record, status: 'error' as const, content: `续写失败: ${error.message || '未知错误'}` }
          : record
      ));
    } finally {
      setIsContinuing(false);
    }
  };

  // 取消续写
  const handleCancel = () => {
    adapter.cancelContinue();
    setIsContinuing(false);
  };

  // 删除续写记录
  const handleDeleteContinue = (continueId: string) => {
    setContinueHistory(prev => prev.filter(record => record.id !== continueId));

    // 如果删除的是当前活跃的续写，清空当前内容
    if (continueId === currentContinueId) {
      setContinuedContent('');
      setCurrentContinueId(null);
    }
  };

  // 插入到编辑器
  const handleInsert = () => {
    if (continuedContent) {
      onInsertContent(continuedContent);
      onClose();
    }
  };

  // 如果对话框未打开，不渲染内容
  if (!isOpen) return null;

  // 创建动画工厂
  const animationFactory = createAnimationFactory();
  const fadeAnimation = animationFactory.createFadeAnimation('none', 400, 0, true);
  const scaleAnimation = animationFactory.createScaleAnimation(0.9, 1.0, 400, 0, true);

  // 获取动画样式
  const fadeStyle = fadeAnimation.getStyle();
  const scaleStyle = scaleAnimation.getStyle();

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-opacity duration-300"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(5px)',
        ...fadeStyle
      }}
      onClick={onClose}
    >
      <div
        ref={dialogRef}
        className="bg-white rounded-xl shadow-2xl w-4/5 max-w-6xl max-h-[90vh] flex flex-col overflow-hidden"
        style={{
          ...scaleStyle
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 对话框标题 */}
        <div className="p-5 border-b flex justify-between items-center bg-gradient-to-r from-blue-50 to-indigo-50">
          <h2 className="text-xl font-bold text-gray-800">AI续写</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 - 双栏布局 */}
        <div className="flex-1 overflow-hidden p-5 flex" style={{ minHeight: '600px' }}>
          {/* 左侧栏：输入参数 */}
          <div className="w-1/2 pr-4 overflow-y-auto">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  上下文内容（自动获取）
                </label>
                <div className="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 h-32 overflow-auto whitespace-pre-wrap">
                  {context || '根据当前光标位置自动获取上下文内容...'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  上下文内容根据编辑器中的光标位置自动获取，包含光标前后的文本。
                </p>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    续写要求
                  </label>
                  <button
                    className="text-xs text-blue-600 hover:text-blue-800"
                    onClick={() => {
                      console.log('点击续写要求管理模板按钮');
                      setPromptTemplateCategory(PromptCategory.CONTINUE_REQUIREMENTS);
                      setIsPromptTemplateManagerOpen(true);
                      console.log('设置模板管理器状态:', { category: PromptCategory.CONTINUE_REQUIREMENTS, isOpen: true });
                    }}
                  >
                    管理模板
                  </button>
                </div>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  rows={3}
                  value={continueRequirements}
                  onChange={(e) => setContinueRequirements(e.target.value)}
                  placeholder="请输入续写要求..."
                />
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    续写风格
                  </label>
                  <button
                    className="text-xs text-blue-600 hover:text-blue-800"
                    onClick={() => {
                      console.log('点击续写风格管理模板按钮');
                      setPromptTemplateCategory(PromptCategory.CONTINUE_STYLE);
                      setIsPromptTemplateManagerOpen(true);
                      console.log('设置模板管理器状态:', { category: PromptCategory.CONTINUE_STYLE, isOpen: true });
                    }}
                  >
                    管理模板
                  </button>
                </div>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  rows={3}
                  value={continueStyle}
                  onChange={(e) => setContinueStyle(e.target.value)}
                  placeholder="请输入续写风格..."
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    后续剧情
                  </label>
                  {/* 独立大纲管理按钮 */}
                  <OutlineManagementButton
                    bookId={bookId}
                    selectedOutlineNodeIds={selectedOutlineNodeIds}
                    onOutlineNodesChange={setSelectedOutlineNodeIds}
                    variant="compact"
                    className="text-xs"
                  />
                </div>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  rows={3}
                  value={futurePlot}
                  onChange={(e) => setFuturePlot(e.target.value)}
                  placeholder="请输入后续剧情发展方向..."
                />
                {/* 大纲节点选择提示 */}
                {selectedOutlineNodeIds.length > 0 && (
                  <div className="mt-2 text-xs text-blue-600">
                    已选择 {selectedOutlineNodeIds.length} 个大纲节点作为剧情参考
                  </div>
                )}
              </div>

              {/* 关联元素选择器 */}
              <div className="mt-4 border-t pt-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-700">关联元素</h3>
                  {/* 统一关联管理按钮 */}
                  <UnifiedAssociationButton
                    bookId={bookId}
                    selectedChapterIds={selectedChapterIds}
                    selectedCharacterIds={selectedCharacterIds}
                    selectedTerminologyIds={selectedTerminologyIds}
                    selectedWorldBuildingIds={selectedWorldBuildingIds}
                    onAssociationsChange={handleAssociationsChange}
                    variant="compact"
                    className="text-xs"
                  />
                </div>

                {/* 关联内容概览 */}
                <AssociationOverview
                  chapterCount={selectedChapterIds.length}
                  characterCount={selectedCharacterIds.length}
                  terminologyCount={selectedTerminologyIds.length}
                  worldBuildingCount={selectedWorldBuildingIds.length}
                />

                {/* 操作提示 */}
                <div className="mt-3 text-xs text-gray-500 text-center">
                  💡 点击"统一管理"按钮来选择和管理所有类型的关联内容
                </div>
              </div>
            </div>
          </div>

          {/* 右侧栏：预览内容 */}
          <div className="w-1/2 pl-4 flex flex-col">
            <AIContinuePreview
              streamResponse={continuedContent}
              isLoading={isContinuing}
              generatedContent={continuedContent}
              bookId={bookId}
              onApply={handleInsert}
              onRestart={() => {
                // 重新生成当前内容，不清空历史
                if (continuedContent) {
                  // 保存当前参数，重新生成
                  handleContinue();
                }
              }}
              onStartNew={() => {
                // 开始新的续写过程，完全重置状态
                setContinuedContent('');

                // 完全清空对话历史，确保重新开始时不会有历史消息干扰
                setConversationHistory([]);

                // 清空续写历史记录
                setContinueHistory([]);
                setCurrentContinueId(null);

                // 清除localStorage中保存的内容，确保完全重置
                if (bookId) {
                  localStorage.removeItem(`ai-continue-content-${bookId}`);
                  localStorage.removeItem(`ai-continue-history-${bookId}`);
                  localStorage.removeItem(`ai-continue-bubble-history-${bookId}`);
                }

                // 延迟一点时间再开始续写，确保状态已完全重置
                setTimeout(() => {
                  // 重新调用handleContinue方法，这将重新构建消息序列
                  // 包括系统提示词、上下文信息和关联元素（人物、术语、世界观等）
                  handleContinue();
                }, 100);
              }}
              // 新增：传递上下文信息用于智能提示
              hasBeforeContext={contextInfo.hasBeforeContext}
              hasAfterContext={contextInfo.hasAfterContext}
              beforeContextLength={contextInfo.beforeContextLength}
              afterContextLength={contextInfo.afterContextLength}
              // 传递续写历史记录，按时间戳排序
              messages={continueHistory
                .sort((a, b) => a.timestamp - b.timestamp)
                .map((record) => ({
                  role: 'assistant',
                  content: record.content,
                  timestamp: record.timestamp,
                  status: record.status,
                  id: record.id,
                  isActive: record.isActive
                }))
              }
              onInsertBubble={(content) => {
                onInsertContent(content);
                onClose();
              }}
              onDeleteContinue={handleDeleteContinue}
              onClearAll={() => {
                // 清空所有续写记录
                setContinueHistory([]);
                setCurrentContinueId(null);
                setContinuedContent('');

                // 清除localStorage中的续写历史
                if (bookId) {
                  localStorage.removeItem(`ai-continue-history-${bookId}`);
                  localStorage.removeItem(`ai-continue-content-${bookId}`);
                  localStorage.removeItem(`ai-continue-bubble-history-${bookId}`);
                }
              }}
              onContinue={async (userInput) => {
                // 继续对话功能
                if (continuedContent) {
                  // 设置正在处理状态
                  setIsContinuing(true);

                  // 保存当前内容，用于构建对话历史
                  const previousContent = continuedContent;

                  // 清空当前内容，准备接收新的内容
                  setContinuedContent('');

                  // 不清空对话历史中的助手消息，保留完整上下文
                  // 这样可以确保关联元素信息在对话历史中被保留

                  try {
                    // 准备关联元素数据
                    const selectedCharacters = characters.filter(char => char.id && selectedCharacterIds.includes(char.id));
                    const selectedTerms = terminologies.filter(term => term.id && selectedTerminologyIds.includes(term.id));
                    const selectedWorldViews = worldBuildings.filter(wb => wb.id && selectedWorldBuildingIds.includes(wb.id));
                    const selectedChaptersData = chapters.filter(chapter => chapter.id && selectedChapterIds.includes(chapter.id) && !chapter.disabled);

                    // 转换为适配器期望的格式
                    const mappedCharacters = selectedCharacters.map(({ id, name, description }) => ({ id, name, description }));
                    const mappedTerms = selectedTerms.map(({ id, name, description }) => ({ id, name, description }));
                    const mappedWorldViews = selectedWorldViews.map(({ id, name, description }) => ({ id, name, description }));
                    const mappedChapters = selectedChaptersData.map(chapter => ({
                      id: chapter.id,
                      name: chapter.name,
                      description: chapter.description,
                      content: chapter.content,
                      order: chapter.order
                    }));

                    // 使用DialogContinuationManager处理对话历史
                    const result = await dialogContinuationManager.handleContinuation({
                      currentContent: previousContent,
                      continuePrompt: userInput,
                      continueMode: 'continue',
                      bookId: bookId || '',
                      conversationHistory: conversationHistory,
                      serviceType: 'writing',

                      // 上下文信息 - 优先使用预构建的消息
                      context: context,
                      contextMessages: initialContextMessages.length > 0 ? initialContextMessages : undefined,

                      // 直接传递上下文状态信息（避免重复解析）
                      hasBeforeContext: contextInfo.hasBeforeContext,
                      hasAfterContext: contextInfo.hasAfterContext,
                      beforeContextLength: contextInfo.beforeContextLength,
                      afterContextLength: contextInfo.afterContextLength,

                      // 传递衔接关键信息（确保连贯性）
                      beforeContextEnd: connectionKeyInfo.beforeContextEnd,
                      afterContextStart: connectionKeyInfo.afterContextStart,

                      // 【修复】用户指令消息组 - 传递所有三个字段
                      continueRequirements: continueRequirements,
                      continueStyle: continueStyle,
                      futurePlot: futurePlot,

                      // 关联元素 - 确保每次都传递完整的关联元素信息
                      chapters: mappedChapters,
                      selectedChapterIds: selectedChapterIds,
                      characters: mappedCharacters,
                      selectedCharacterIds: selectedCharacterIds,
                      terminologies: mappedTerms,
                      selectedTerminologyIds: selectedTerminologyIds,
                      worldBuildings: mappedWorldViews,
                      selectedWorldBuildingIds: selectedWorldBuildingIds,

                      // 大纲关联元素
                      outlines: outlines,
                      selectedOutlineNodeIds: selectedOutlineNodeIds,
                      outlineContextMode: 'hierarchy',

                      currentChapterId: currentChapterId
                    });

                    // 更新对话历史
                    setConversationHistory(result.updatedHistory);

                    // 使用流式输出
                    let generatedText = '';
                    // 创建一个临时变量来存储新内容
                    let newContent = '';

                    await aiServiceProvider.sendStreamingRequest(
                      result.messages,
                      (chunk) => {
                        generatedText += chunk;
                        newContent += chunk;
                        // 更新当前显示的内容
                        setContinuedContent(newContent);
                      },
                      {
                        temperature: 0.7,
                        streaming: true
                      }
                    );

                    // 保存生成的内容到对话历史
                    if (result.updatedHistory.length > 0) {
                      const newHistory = [...result.updatedHistory];
                      newHistory.push({
                        role: 'assistant',
                        content: generatedText
                      });
                      setConversationHistory(newHistory);
                    }
                  } catch (error: any) {
                    console.error('继续对话失败', error);
                    setContinuedContent(`继续对话失败: ${error.message || '未知错误'}`);
                  } finally {
                    setIsContinuing(false);
                  }
                }
              }}
            />
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="p-5 border-t flex justify-between items-center bg-gradient-to-r from-gray-50 to-indigo-50">
          <div>
            <button
              className="px-5 py-2.5 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
              onClick={onClose}
              disabled={isContinuing}
            >
              取消
            </button>
          </div>

          <div className="flex space-x-3">
            {isContinuing ? (
              <button
                className="px-4 py-2.5 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors shadow-sm"
                onClick={handleCancel}
              >
                取消生成
              </button>
            ) : (
              <button
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-colors shadow-md font-medium"
                onClick={handleContinue}
                disabled={isContinuing || !context}
              >
                {isContinuing ? (
                  <div className="flex items-center">
                    <svg className="animate-spin mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    续写中...
                  </div>
                ) : (
                  continueHistory.length > 0 ? '重新续写' : '开始续写'
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptTemplateManagerOpen}
        onClose={() => setIsPromptTemplateManagerOpen(false)}
        category={promptTemplateCategory}
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={promptTemplateCategory === PromptCategory.CONTINUE_REQUIREMENTS ? continueRequirements : continueStyle}
      />

      {/* 人物选择器 */}
      <CustomSelectorDialog
        isOpen={isCharacterSelectorOpen}
        onClose={() => setIsCharacterSelectorOpen(false)}
        onConfirm={() => setIsCharacterSelectorOpen(false)}
        title="选择关联人物"
        items={characters}
        selectedIds={selectedCharacterIds}
        onSelect={setSelectedCharacterIds}
        isLoading={isLoading}
        loadingText="正在加载人物数据..."
        emptyText="未找到任何人物，请先创建人物"
      />

      {/* 术语选择器 */}
      <CustomSelectorDialog
        isOpen={isTerminologySelectorOpen}
        onClose={() => setIsTerminologySelectorOpen(false)}
        onConfirm={() => setIsTerminologySelectorOpen(false)}
        title="选择关联术语"
        items={terminologies}
        selectedIds={selectedTerminologyIds}
        onSelect={setSelectedTerminologyIds}
        isLoading={isLoading}
        loadingText="正在加载术语数据..."
        emptyText="未找到任何术语，请先创建术语"
      />

      {/* 世界观选择器 */}
      <CustomSelectorDialog
        isOpen={isWorldBuildingSelectorOpen}
        onClose={() => setIsWorldBuildingSelectorOpen(false)}
        onConfirm={() => setIsWorldBuildingSelectorOpen(false)}
        title="选择关联世界观"
        items={worldBuildings}
        selectedIds={selectedWorldBuildingIds}
        onSelect={setSelectedWorldBuildingIds}
        isLoading={isLoading}
        loadingText="正在加载世界观数据..."
        emptyText="未找到任何世界观，请先创建世界观"
      />

      {/* 章节选择器 - 启用范围选择功能 */}
      <CustomSelectorDialog
        isOpen={isChapterSelectorOpen}
        onClose={() => setIsChapterSelectorOpen(false)}
        onConfirm={() => setIsChapterSelectorOpen(false)}
        title="选择关联章节"
        items={chapters}
        selectedIds={selectedChapterIds}
        onSelect={setSelectedChapterIds}
        isLoading={isLoading}
        loadingText="正在加载章节数据..."
        emptyText="未找到任何章节，请确保已创建章节"
        showRangeSelect={true}
      />
    </div>
  );
};
