"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

export type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface NotificationProps {
  message: string;
  type?: NotificationType;
  duration?: number;
  onClose?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

/**
 * 通知组件
 * 用于显示操作成功或失败的消息
 */
const Notification: React.FC<NotificationProps> = ({
  message,
  type = 'info',
  duration = 3000,
  onClose,
  position = 'top-center'
}) => {
  const [visible, setVisible] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // 组件挂载后设置状态
    setMounted(true);
    
    // 短暂延迟后显示通知，添加进入动画效果
    const showTimer = setTimeout(() => {
      setVisible(true);
    }, 10);
    
    // 设置自动关闭定时器
    const closeTimer = setTimeout(() => {
      setVisible(false);
      
      // 等待退出动画完成后卸载组件
      setTimeout(() => {
        if (onClose) onClose();
      }, 300);
    }, duration);
    
    // 清理定时器
    return () => {
      clearTimeout(showTimer);
      clearTimeout(closeTimer);
    };
  }, [duration, onClose]);
  
  // 如果组件未挂载，不渲染任何内容
  if (!mounted) return null;
  
  // 根据类型获取图标和颜色
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          icon: '✓',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-500',
          textColor: 'text-green-700',
          iconBg: 'bg-green-500'
        };
      case 'error':
        return {
          icon: '✕',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-500',
          textColor: 'text-red-700',
          iconBg: 'bg-red-500'
        };
      case 'warning':
        return {
          icon: '!',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-500',
          textColor: 'text-yellow-700',
          iconBg: 'bg-yellow-500'
        };
      default: // info
        return {
          icon: 'i',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-500',
          textColor: 'text-blue-700',
          iconBg: 'bg-blue-500'
        };
    }
  };
  
  // 获取位置样式
  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default: // top-center
        return 'top-4 left-1/2 transform -translate-x-1/2';
    }
  };
  
  const styles = getTypeStyles();
  const positionStyles = getPositionStyles();
  
  // 使用Portal渲染到body
  return createPortal(
    <div 
      className={`fixed ${positionStyles} z-50 transition-all duration-300 ease-in-out ${
        visible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-[-20px]'
      }`}
    >
      <div className={`flex items-center p-4 rounded-lg shadow-lg border-l-4 ${styles.bgColor} ${styles.borderColor} max-w-md`}>
        <div className={`flex-shrink-0 w-8 h-8 mr-3 rounded-full ${styles.iconBg} text-white flex items-center justify-center font-bold`}>
          {styles.icon}
        </div>
        <div className={`flex-grow ${styles.textColor}`}>
          {message}
        </div>
        <button 
          onClick={() => {
            setVisible(false);
            setTimeout(() => {
              if (onClose) onClose();
            }, 300);
          }}
          className="ml-4 text-gray-400 hover:text-gray-600 focus:outline-none"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path 
              fillRule="evenodd" 
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>,
    document.body
  );
};

export default Notification;
