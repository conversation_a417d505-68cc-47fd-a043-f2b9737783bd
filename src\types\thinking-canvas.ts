/**
 * 思考画布相关的类型定义
 */

/**
 * 对话设计数据结构
 */
export interface DialogueDesign {
  /** 角色列表 */
  characters: string[];
  /** 对话推进逻辑 */
  dialogueFlow: string;
  /** 情感基调 */
  emotionalTone: string;
  /** 对话节奏 */
  pacing: string;
  /** 对话技巧 */
  techniques: string[];
  /** 对话目的 */
  purpose?: string;
  /** 冲突点 */
  conflictPoints?: string[];
}

/**
 * 剧情节奏数据结构
 */
export interface PlotRhythm {
  /** 紧张点设计 */
  tensionPoints: string[];
  /** 高潮时刻 */
  climaxMoments: string[];
  /** 节奏策略 */
  pacingStrategy: string;
  /** 读者爽感设计 */
  readerEngagement: string;
  /** 节奏模式 */
  rhythmPattern: string;
  /** 情感波动 */
  emotionalWaves?: string[];
  /** 悬念布局 */
  suspenseLayout?: string;
}

/**
 * 思考画布完整数据结构
 */
export interface ThinkingCanvasData {
  /** 唯一标识 */
  id: string;
  /** 思考主题 */
  title: string;
  /** 对话设计 */
  dialogueDesign: DialogueDesign;
  /** 剧情节奏 */
  plotRhythm: PlotRhythm;
  /** AI建议列表 */
  aiSuggestions: string[];
  /** 用户备注 */
  userNotes: string;
  /** 最后修改时间 */
  lastModified: string;
  /** 创建时间 */
  createdAt: string;
  /** 关联的框架ID（可选） */
  relatedFrameworkId?: string;
  /** 关联的大纲节点ID（可选） */
  relatedOutlineNodeId?: string;
}

/**
 * 思考画布编辑状态
 */
export interface ThinkingCanvasEditState {
  /** 是否正在编辑 */
  isEditing: boolean;
  /** 当前编辑的区域 */
  editingSection: 'dialogue' | 'plot' | 'notes' | null;
  /** 是否有未保存的更改 */
  hasUnsavedChanges: boolean;
  /** 是否正在保存 */
  isSaving: boolean;
  /** 是否正在生成AI内容 */
  isGeneratingAI: boolean;
}

/**
 * AI生成请求参数
 */
export interface ThinkingCanvasAIRequest {
  /** 当前思考内容 */
  currentData: ThinkingCanvasData;
  /** 生成类型 */
  generateType: 'dialogue' | 'plot' | 'both' | 'suggestions';
  /** 上下文信息 */
  context?: {
    /** 相关框架信息 */
    framework?: any;
    /** 相关大纲信息 */
    outline?: any;
    /** 用户输入的额外要求 */
    userRequirements?: string;
  };
}

/**
 * AI生成响应数据
 */
export interface ThinkingCanvasAIResponse {
  /** 生成的对话设计（如果请求了） */
  dialogueDesign?: Partial<DialogueDesign>;
  /** 生成的剧情节奏（如果请求了） */
  plotRhythm?: Partial<PlotRhythm>;
  /** 生成的建议列表 */
  suggestions?: string[];
  /** 生成的解释说明 */
  explanation?: string;
  /** 生成是否成功 */
  success: boolean;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 思考画布模板
 */
export interface ThinkingCanvasTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板数据 */
  template: Omit<ThinkingCanvasData, 'id' | 'createdAt' | 'lastModified'>;
  /** 模板类型 */
  type: 'dialogue' | 'plot' | 'comprehensive';
  /** 适用场景 */
  scenarios: string[];
}

/**
 * 思考画布配置选项
 */
export interface ThinkingCanvasConfig {
  /** 是否启用自动保存 */
  autoSave: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval: number;
  /** 是否启用AI协作 */
  enableAICollaboration: boolean;
  /** 默认模板ID */
  defaultTemplateId?: string;
  /** 是否显示快捷键提示 */
  showShortcutHints: boolean;
}

/**
 * 思考画布事件类型
 */
export type ThinkingCanvasEvent =
  | { type: 'OPEN'; payload: { templateId?: string } }
  | { type: 'CLOSE' }
  | { type: 'SAVE'; payload: ThinkingCanvasData }
  | { type: 'LOAD'; payload: { id: string } }
  | { type: 'GENERATE_AI'; payload: ThinkingCanvasAIRequest }
  | { type: 'UPDATE_DIALOGUE'; payload: Partial<DialogueDesign> }
  | { type: 'UPDATE_PLOT'; payload: Partial<PlotRhythm> }
  | { type: 'UPDATE_NOTES'; payload: string }
  | { type: 'RESET' };

/**
 * 新的简化思考画布数据结构（用于画布管理器）
 */
export interface ThinkingCanvas {
  /** 唯一标识 */
  id: string;
  /** 画布标题 */
  title: string;
  /** 画布内容（Markdown格式） */
  content: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 标签列表 */
  tags: string[];
  /** 使用的模板ID */
  template?: string;
  /** 元数据 */
  metadata: {
    /** 字数统计 */
    wordCount: number;
    /** 编辑历史 */
    editHistory: EditRecord[];
    /** 是否收藏 */
    isStarred: boolean;
  };
}

/**
 * 编辑记录
 */
export interface EditRecord {
  /** 时间戳 */
  timestamp: string;
  /** 操作类型 */
  action: 'create' | 'update' | 'delete';
  /** 内容快照（可选） */
  content?: string;
}

/**
 * 画布管理器状态
 */
export interface CanvasManagerState {
  /** 所有画布列表 */
  canvases: ThinkingCanvas[];
  /** 当前选中的画布ID */
  currentCanvasId: string | null;
  /** 编辑模式 */
  editMode: 'edit' | 'preview';
  /** 左侧边栏是否折叠 */
  sidebarCollapsed: boolean;
  /** 是否正在保存 */
  isSaving: boolean;
  /** 搜索查询 */
  searchQuery: string;
  /** 排序方式 */
  sortBy: 'date' | 'name' | 'updated';
}

/**
 * 画布管理器操作类型
 */
export type CanvasManagerAction =
  | { type: 'CREATE_CANVAS'; payload: { title: string; template?: string } }
  | { type: 'DELETE_CANVAS'; payload: { id: string } }
  | { type: 'UPDATE_CANVAS'; payload: { id: string; updates: Partial<ThinkingCanvas> } }
  | { type: 'SWITCH_CANVAS'; payload: { id: string } }
  | { type: 'SET_EDIT_MODE'; payload: { mode: 'edit' | 'preview' } }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SEARCH_QUERY'; payload: { query: string } }
  | { type: 'SET_SORT_BY'; payload: { sortBy: 'date' | 'name' | 'updated' } }
  | { type: 'LOAD_CANVASES'; payload: { canvases: ThinkingCanvas[] } }
  | { type: 'SET_SAVING'; payload: { isSaving: boolean } };
