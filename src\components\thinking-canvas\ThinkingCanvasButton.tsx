"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface ThinkingCanvasButtonProps {
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  variant?: 'default' | 'compact';
}

/**
 * 思考画布触发按钮组件
 * 使用脑图样式的SVG图标，支持悬停动画效果
 */
const ThinkingCanvasButton: React.FC<ThinkingCanvasButtonProps> = ({
  onClick,
  disabled = false,
  className = '',
  variant = 'default'
}) => {
  const isCompact = variant === 'compact';

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={`
        inline-flex items-center
        ${isCompact ? 'space-x-1 px-2 py-1 text-xs' : 'space-x-2 px-4 py-2 text-sm'}
        bg-gradient-to-r from-purple-500 to-indigo-600
        hover:from-purple-600 hover:to-indigo-700
        text-white font-medium
        ${isCompact ? 'rounded-md' : 'rounded-lg'}
        transition-all duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        ${isCompact ? 'shadow-sm hover:shadow-md' : 'shadow-md hover:shadow-lg'}
        ${className}
      `}
      whileHover={{ y: isCompact ? -1 : -2, scale: isCompact ? 1.01 : 1.02 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0, y: isCompact ? 5 : 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 思考画布图标 - 脑图样式 */}
      <motion.div
        className={isCompact ? "w-3 h-3" : "w-5 h-5"}
        whileHover="hover"
        initial="initial"
      >
        <svg
          width={isCompact ? "12" : "20"}
          height={isCompact ? "12" : "20"}
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* 中心节点 */}
          <motion.circle
            cx="12"
            cy="12"
            r="3"
            fill="currentColor"
            variants={{
              initial: { scale: 1 },
              hover: { scale: 1.2 }
            }}
            transition={{ duration: 0.2 }}
          />
          
          {/* 分支线条 - 上方 */}
          <motion.path
            d="M12 9 L12 3"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.1 }}
          />
          
          {/* 分支线条 - 右上 */}
          <motion.path
            d="M14.5 10.5 L19 6"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.15 }}
          />
          
          {/* 分支线条 - 右下 */}
          <motion.path
            d="M14.5 13.5 L19 18"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.2 }}
          />
          
          {/* 分支线条 - 下方 */}
          <motion.path
            d="M12 15 L12 21"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.25 }}
          />
          
          {/* 分支线条 - 左下 */}
          <motion.path
            d="M9.5 13.5 L5 18"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.3 }}
          />
          
          {/* 分支线条 - 左上 */}
          <motion.path
            d="M9.5 10.5 L5 6"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            variants={{
              initial: { pathLength: 0.7 },
              hover: { pathLength: 1 }
            }}
            transition={{ duration: 0.3, delay: 0.35 }}
          />
          
          {/* 分支节点 */}
          <motion.circle
            cx="12"
            cy="3"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.1 }}
          />
          <motion.circle
            cx="19"
            cy="6"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.15 }}
          />
          <motion.circle
            cx="19"
            cy="18"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.2 }}
          />
          <motion.circle
            cx="12"
            cy="21"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.25 }}
          />
          <motion.circle
            cx="5"
            cy="18"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.3 }}
          />
          <motion.circle
            cx="5"
            cy="6"
            r="1.5"
            fill="currentColor"
            variants={{
              initial: { scale: 0.8, opacity: 0.7 },
              hover: { scale: 1, opacity: 1 }
            }}
            transition={{ duration: 0.2, delay: 0.35 }}
          />
        </svg>
      </motion.div>

      <span className={`font-medium ${isCompact ? 'text-xs' : 'text-sm'}`}>
        {isCompact ? '思考' : '思考画布'}
      </span>
    </motion.button>
  );
};

export default ThinkingCanvasButton;
