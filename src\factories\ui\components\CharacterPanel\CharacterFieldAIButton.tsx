"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import CharacterAIAdapter from '@/adapters/ai/CharacterAIAdapter';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { createAIFactory } from '@/factories/ai/AIFactory';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import { getBookChapters } from '@/utils/chapterAssociation';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface CharacterFieldAIButtonProps {
  character: Character;
  fieldName: string;
  fieldDisplayName: string;
  onSave: (character: Character) => void;
  bookId?: string; // 可选的书籍ID，用于获取章节列表
  mode?: 'generate' | 'update'; // 按钮模式：生成或更新，默认为生成
}

/**
 * 人物字段AI功能按钮组件
 * 用于显示单个字段的AI功能按钮和处理AI功能
 */
export const CharacterFieldAIButton: React.FC<CharacterFieldAIButtonProps> = ({
  character,
  fieldName,
  fieldDisplayName,
  onSave,
  bookId,
  mode = 'generate' // 默认为生成模式
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);

  // 加载章节列表
  useEffect(() => {
    if (bookId) {
      loadChapters();
    }
  }, [bookId]);

  // 加载章节列表
  const loadChapters = async () => {
    if (!bookId) return;

    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 chapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 CharacterAIAdapter 的 getChapterList 方法
      try {
        const characterAIAdapter = new CharacterAIAdapter();
        const chapterList = await characterAIAdapter.getChapterList(bookId);
        console.log('通过 CharacterAIAdapter.getChapterList 获取到章节数据:', chapterList);

        if (chapterList && chapterList.length > 0) {
          setChapters(chapterList);
          console.log('加载了章节:', chapterList.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 CharacterAIAdapter.getChapterList 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      console.log('所有方法都无法获取章节数据');
      setChapters([]);
    } catch (error) {
      console.error('加载章节列表失败:', error);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  const handleAIClick = async (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发父组件的事件处理
    e.preventDefault();
    e.stopPropagation();

    if (isLoading) return; // 防止重复点击

    // 创建AI适配器
    const aiAdapter = new CharacterAIAdapter();

    // 获取API设置
    const settingsFactory = createSettingsFactory();
    const apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 检查API密钥是否已设置
    const currentProvider = apiSettings.getCurrentProvider();
    const apiKey = apiSettings.getAPIKey(currentProvider);
    const currentModel = apiSettings.getCurrentModel();
    const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

    console.log('当前API设置:', {
      provider: currentProvider,
      model: currentModel,
      hasApiKey: !!apiKey,
      endpoint: apiEndpoint
    });

    if (!apiKey) {
      alert(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      return;
    }

    // 如果是自定义提供商，还需要检查API端点
    if (currentProvider === 'custom' && !apiEndpoint) {
      alert('请先在设置中配置自定义API端点');
      return;
    }

    // 创建一个包含事件处理器的弹出对话框
    const promptDialogEl = document.createElement('div');
    promptDialogEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    promptDialogEl.style.zIndex = '9999'; // 确保在最上层

    // 根据模式生成不同的对话框内容
    const dialogTitle = mode === 'generate' ? '自定义AI生成要求' : '自定义AI更新要求';
    const actionButtonText = mode === 'generate' ? '生成' : '更新';
    const placeholderText = mode === 'generate'
      ? `例如：请生成一个具有神秘感的${fieldDisplayName}，包含...`
      : `例如：请根据章节内容更新${fieldDisplayName}，关注...`;

    // 构建对话框HTML
    let dialogHTML = `
      <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full">
        <h3 class="text-xl font-bold mb-4 text-purple-700">${dialogTitle}</h3>
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">您可以输入特定的要求来指导AI${mode === 'generate' ? '生成' : '更新'}${fieldDisplayName}，或者保留为空使用默认提示词。</p>
          <textarea id="custom-prompt" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows="6" placeholder="${placeholderText}"></textarea>
        </div>
    `;

    // 如果有书籍ID，添加章节选择（无论是生成还是更新模式）
    if (bookId && chapters.length > 0) {
      dialogHTML += `
        <div class="mb-4">
          <p class="text-sm font-medium text-gray-700 mb-2">选择关联章节（可选）</p>
          <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
            ${chapters.map((chapter) => `
              <div class="flex items-center mb-1">
                <input type="checkbox" id="chapter-${chapter.id}" class="chapter-checkbox mr-2" value="${chapter.id}">
                <label for="chapter-${chapter.id}" class="text-sm">${chapter.title || (chapter.order !== undefined ? `第${chapter.order + 1}章` : '无标题章节')}</label>
              </div>
            `).join('')}
          </div>
          <p class="text-xs text-gray-500 mt-1">选择章节可以帮助AI${mode === 'generate' ? '生成' : '更新'}更准确的人物信息</p>
        </div>
      `;
    }

    // 添加底部按钮
    dialogHTML += `
        <div class="text-xs text-gray-500 mb-4">
          使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
        </div>
        <div class="flex justify-end space-x-2">
          <button id="prompt-cancel-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
            取消
          </button>
          <button id="prompt-generate-btn" class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
            ${actionButtonText}
          </button>
        </div>
      </div>
    `;

    promptDialogEl.innerHTML = dialogHTML;

    // 阻止对话框上的点击事件冒泡
    promptDialogEl.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    document.body.appendChild(promptDialogEl);

    // 取消按钮
    document.getElementById('prompt-cancel-btn')?.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      promptDialogEl.remove();
    });

    // 生成/更新按钮
    document.getElementById('prompt-generate-btn')?.addEventListener('click', async (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      const customPrompt = (document.getElementById('custom-prompt') as HTMLTextAreaElement)?.value;

      // 获取选中的章节ID（无论是生成还是更新模式）
      let selectedChapters: string[] = [];
      if (bookId) {
        const checkboxes = document.querySelectorAll('.chapter-checkbox:checked') as NodeListOf<HTMLInputElement>;
        selectedChapters = Array.from(checkboxes).map(checkbox => checkbox.value);
      }

      // 保存选中的章节ID
      setSelectedChapterIds(selectedChapters);

      promptDialogEl.remove();

      // 显示加载中，并准备流式输出
      setIsLoading(true);
      let loadingEl = document.createElement('div');
      loadingEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      loadingEl.style.zIndex = '9999'; // 确保在最上层

      // 阻止加载对话框上的点击事件冒泡
      loadingEl.addEventListener('click', (e) => {
        e.stopPropagation();
      });

      // 创建一个用于流式输出的容器
      loadingEl.innerHTML = `
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full">
          <h3 class="text-xl font-bold mb-4 text-purple-700">AI正在生成${fieldDisplayName}</h3>
          <div id="streaming-content" class="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
            <p id="streaming-text" class="whitespace-pre-wrap"></p>
          </div>
          <div class="flex items-center mb-4">
            <div class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-purple-500 mr-2"></div>
            <p class="text-sm text-gray-600">正在生成中...</p>
          </div>
          <div class="text-xs text-gray-500 mb-2">
            使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
          </div>
          <button id="ai-cancel-btn" class="w-full p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
            取消
          </button>
        </div>
      `;
      document.body.appendChild(loadingEl);

      // 取消按钮
      document.getElementById('ai-cancel-btn')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡

        // 取消AI请求
        aiAdapter.cancelRequest();

        // 取消请求已处理

        // 关闭对话框
        loadingEl.remove();
        setIsLoading(false);
      });

    try {
      // 获取API设置
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();

      // 准备更新流式输出的UI
      const streamingTextElement = document.getElementById('streaming-text');

      // 生成字段内容
      let content = '';

      // 根据不同字段调用不同的AI方法，传递自定义提示词
      try {
        console.log(`开始${mode === 'generate' ? '生成' : '更新'}${fieldName}，使用${customPrompt ? '自定义提示词' : '默认提示词'}`);

        // 确保启用流式输出
        apiSettings.setStreamingEnabled(true);

        // 创建AI工厂和发送组件
        const aiFactory = createAIFactory();
        const aiSender = aiFactory.createAISenderComponent();

        // 流式输出回调函数
        const onStreamChunk = (chunk: string) => {
          content += chunk;
          if (streamingTextElement) {
            streamingTextElement.textContent = content;

            // 自动滚动到底部
            const streamingContent = document.getElementById('streaming-content');
            if (streamingContent) {
              streamingContent.scrollTop = streamingContent.scrollHeight;
            }
          }
        };

        // 获取API设置
        const currentProvider = apiSettings.getCurrentProvider();
        const currentModel = apiSettings.getCurrentModel();
        const apiKey = apiSettings.getAPIKey(currentProvider);
        const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

        // 创建预设的用户提示词
        let presetUserPrompt = '';
        let systemMessage = '';

        // 获取章节内容（如果有选择章节）
        let chapterContent = '';
        if (selectedChapterIds.length > 0 && bookId) {
          try {
            // 获取选中章节的内容
            const characterAIAdapter = new CharacterAIAdapter();

            // 保存章节关联
            if (character.id) {
              await characterAIAdapter.saveCharacterChapterAssociation(character, selectedChapterIds);
            }

            // 获取章节内容
            for (const chapterId of selectedChapterIds) {
              const chapter = chapters.find(c => c.id === chapterId);
              if (chapter) {
                chapterContent += `# ${chapter.title || (chapter.order !== undefined ? `第${chapter.order + 1}章` : '无标题章节')}\n\n${chapter.content || ''}\n\n`;
              }
            }

            console.log(`已加载${selectedChapterIds.length}个章节的内容，总长度: ${chapterContent.length}`);
          } catch (error) {
            console.error('获取章节内容失败:', error);
          }
        }

        // 构建通用的人物信息字符串函数
        const buildCharacterInfo = (isAppearance = false) => {
          if (isAppearance) {
            return `
人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `已有外貌描述: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
`;
          } else {
            return `
人物名称: ${character.name}
${character.alias && character.alias.length > 0 ? `别名: ${character.alias.join(', ')}` : ''}
${character.description ? `简要描述: ${character.description}` : ''}
${character.appearance ? `外貌: ${character.appearance}` : ''}
${character.personality ? `性格: ${character.personality}` : ''}
${character.background ? `背景: ${character.background}` : ''}
${character.goals ? `目标: ${character.goals}` : ''}
${character.characterArchetype ? `角色原型: ${character.characterArchetype}` : ''}
${character.growthArc ? `成长弧线: ${character.growthArc}` : ''}
${character.hiddenMotivation ? `隐藏动机: ${character.hiddenMotivation}` : ''}
${character.secretHistory ? `秘密历史: ${character.secretHistory}` : ''}
${character.innerConflicts ? `内心冲突: ${character.innerConflicts}` : ''}
${character.symbolism ? `象征意义: ${character.symbolism}` : ''}
`;
          }
        };

        // 根据字段类型和模式构建不同的提示词
        switch(fieldName) {
          case 'description':
            if (mode === 'generate') {
              presetUserPrompt = customPrompt || `请为以下小说人物生成一段详细的描述，包括外貌、性格、背景等方面。请直接生成一段连贯、生动的人物描述，突出这个角色的独特之处和在故事中的作用。不要解释你的思考过程，直接给出结果。`;
              systemMessage = `你是一位专业的人物描写专家，擅长清晰准确地描述小说角色。请提供简洁、实用的人物描述，避免过度修饰和华丽的表达。直接给出结果，不要解释你的思考过程。`;
            } else { // update模式
              presetUserPrompt = customPrompt || `请根据以下章节内容，为小说人物${character.name}更新描述信息。请分析章节中关于该角色的新信息，并将其整合到现有描述中。生成的内容应该是对现有描述的补充和扩展，而不是替换。请直接给出更新后的完整描述，不要解释你的思考过程。`;
              systemMessage = `你是一位专业的人物描写专家，擅长从文本中提取人物信息并更新人物描述。请分析提供的章节内容，找出关于人物的新信息，并将其整合到现有描述中。请提供完整的更新后描述，避免重复已有信息，重点关注新发现的特征和发展。直接给出结果，不要解释你的思考过程。`;
            }
            break;

          case 'appearance':
            if (mode === 'generate') {
              presetUserPrompt = customPrompt || `请为以下小说人物生成一段详细的外貌描述。请直接生成一段生动、具体的外貌描述，包括但不限于：身高体型、面部特征、发型发色、眼睛、服饰风格、特殊标志等。描述应当与人物的性格和角色原型相符。不要解释你的思考过程，直接给出结果。`;
              systemMessage = `你是一位专业的人物外貌描写专家，擅长清晰准确地描述角色的外在特征。请提供简洁、实用的外貌描述，避免过度修饰和华丽的表达。直接给出结果，不要解释你的思考过程。`;
            } else { // update模式
              presetUserPrompt = customPrompt || `请根据以下章节内容，为小说人物${character.name}更新外貌描述。请分析章节中关于该角色外貌的新信息，并将其整合到现有描述中。生成的内容应该是对现有外貌描述的补充和扩展，而不是替换。请直接给出更新后的完整外貌描述，不要解释你的思考过程。`;
              systemMessage = `你是一位专业的人物外貌描写专家，擅长从文本中提取人物外貌信息并更新描述。请分析提供的章节内容，找出关于人物外貌的新信息，并将其整合到现有描述中。请提供完整的更新后外貌描述，避免重复已有信息，重点关注新发现的外貌特征。直接给出结果，不要解释你的思考过程。`;
            }
            break;

          default:
            if (mode === 'generate') {
              presetUserPrompt = customPrompt || `请为以下小说人物生成一段详细的${fieldDisplayName}。请直接生成一段连贯、生动的${fieldDisplayName}描述，不要解释你的思考过程，直接给出结果。`;
              systemMessage = `你是一位专业的人物描写专家，擅长清晰准确地描述小说角色的${fieldDisplayName}。请提供简洁、实用的描述，避免过度修饰和华丽的表达。直接给出结果，不要解释你的思考过程。`;
            } else { // update模式
              presetUserPrompt = customPrompt || `请根据以下章节内容，为小说人物${character.name}更新${fieldDisplayName}。请分析章节中关于该角色${fieldDisplayName}的新信息，并将其整合到现有描述中。生成的内容应该是对现有${fieldDisplayName}的补充和扩展，而不是替换。请直接给出更新后的完整${fieldDisplayName}，不要解释你的思考过程。`;
              systemMessage = `你是一位专业的人物描写专家，擅长从文本中提取人物信息并更新${fieldDisplayName}。请分析提供的章节内容，找出关于人物${fieldDisplayName}的新信息，并将其整合到现有描述中。请提供完整的更新后${fieldDisplayName}，避免重复已有信息，重点关注新发现的特征和发展。直接给出结果，不要解释你的思考过程。`;
            }
        }

        // 使用MessageBuilder构建多角色消息数组，以便更精准地控制上下文
        const messageBuilder = createMessageBuilder()
          // 系统消息，设置整体的行为和风格
          .addSystemMessage(systemMessage)

          // 助手消息，展示人物已有的信息
          .addAssistantInfoMessage(fieldName === 'appearance' ? buildCharacterInfo(true) : buildCharacterInfo(false));

        // 如果有章节内容，添加章节内容（无论是生成还是更新模式）
        if (chapterContent) {
          messageBuilder.addUserMessage(`以下是关于人物${character.name}的章节内容，请${mode === 'generate' ? '参考这些内容生成人物信息' : '从中提取相关信息'}：\n\n${chapterContent}`);
          messageBuilder.addAssistantMessage(`我已分析章节内容，找到了关于${character.name}的相关信息。`);
        }

        // 添加用户消息，设置具体的生成要求
        messageBuilder.addUserMessage(customPrompt || presetUserPrompt);

        // 助手消息，确认任务
        messageBuilder.addAssistantConfirmMessage(
          mode === 'generate' ? fieldDisplayName : `更新${fieldDisplayName}`,
          character.name
        );

        // 用户最终指令
        messageBuilder.addUserDirectGenerateMessage(
          mode === 'generate' ? fieldDisplayName : `更新后的${fieldDisplayName}`
        );

        // 构建消息数组
        const messages = messageBuilder.build();

        // 使用流式请求
        const result = await aiSender.sendStreamingRequest(
          "", // 不使用单独的prompt，而是使用messages数组
          onStreamChunk,
          {
            provider: currentProvider,
            model: currentModel,
            apiKey: apiKey,
            apiEndpoint: apiEndpoint,
            streaming: true,
            messages: messages
          }
        );

        // 获取完整内容
        content = result.text;

        console.log(`成功生成${fieldName}内容`);

        // 流式输出已完成

        // 确保最终内容显示在流式输出区域
        if (streamingTextElement) {
          streamingTextElement.textContent = content;

          // 自动滚动到底部
          const streamingContent = document.getElementById('streaming-content');
          if (streamingContent) {
            streamingContent.scrollTop = streamingContent.scrollHeight;
          }
        }
      } catch (error) {
        console.error(`生成${fieldName}失败:`, error);
        throw error;
      }

      // 显示结果预览
      loadingEl.innerHTML = `
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl">
          <h3 class="text-xl font-bold mb-4 text-purple-700">AI生成的${fieldDisplayName}</h3>
          <div class="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
            <p class="whitespace-pre-wrap">${content}</p>
          </div>
          <div class="text-xs text-gray-500 mb-2">
            使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
          </div>
          <div class="flex justify-end space-x-2">
            <button id="ai-apply-btn" class="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
              应用
            </button>
            <button id="ai-copy-btn" class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
              复制
            </button>
            <button id="ai-result-close" class="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
              关闭
            </button>
          </div>
        </div>
      `;

      // 应用按钮
      document.getElementById('ai-apply-btn')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡

        // 根据模式决定是替换还是追加内容
        let newContent = content;

        // 如果是更新模式，则追加内容而不是替换
        if (mode === 'update') {
          // 获取现有内容（使用类型安全的方式）
          let existingContent = '';

          // 根据字段名获取现有内容
          switch(fieldName) {
            case 'description':
              existingContent = character.description || '';
              break;
            case 'appearance':
              existingContent = character.appearance || '';
              break;
            case 'personality':
              existingContent = character.personality || '';
              break;
            case 'background':
              existingContent = character.background || '';
              break;
            case 'goals':
              existingContent = character.goals || '';
              break;
            case 'characterArchetype':
              existingContent = character.characterArchetype || '';
              break;
            case 'growthArc':
              existingContent = character.growthArc || '';
              break;
            case 'hiddenMotivation':
              existingContent = character.hiddenMotivation || '';
              break;
            case 'secretHistory':
              existingContent = character.secretHistory || '';
              break;
            case 'innerConflicts':
              existingContent = character.innerConflicts || '';
              break;
            case 'symbolism':
              existingContent = character.symbolism || '';
              break;
            default:
              // 对于其他字段，尝试安全地获取
              existingContent = (character as any)[fieldName] || '';
          }

          // 如果有现有内容，则追加
          if (existingContent) {
            // 检查现有内容是否以换行符结束，如果没有则添加换行符
            const separator = existingContent.endsWith('\n') ? '\n' : '\n\n';

            // 追加新内容
            newContent = existingContent + separator + content;
          }
        }

        // 更新字段
        const updatedCharacter = {
          ...character,
          [fieldName]: newContent
        };

        // 保存更新后的人物
        onSave(updatedCharacter);

        // 关闭对话框
        loadingEl.remove();
        setIsLoading(false);
      });

      // 复制按钮
      document.getElementById('ai-copy-btn')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡

        navigator.clipboard.writeText(content)
          .then(() => alert('已复制到剪贴板'))
          .catch(() => alert('复制失败，请手动复制'));
      });

      // 关闭按钮
      document.getElementById('ai-result-close')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡

        loadingEl.remove();
        setIsLoading(false);
      });
    } catch (error: any) {
      console.error('AI生成失败:', error);

      // 错误处理已完成

      // 确保loadingEl存在
      if (!document.body.contains(loadingEl)) {
        // 如果loadingEl不存在，创建一个新的
        const newLoadingEl = document.createElement('div');
        newLoadingEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        document.body.appendChild(newLoadingEl);
        loadingEl = newLoadingEl;
      }

      // 显示错误消息
      loadingEl.innerHTML = `
        <div class="bg-white p-6 rounded-lg shadow-xl">
          <div class="mx-auto mb-4 text-red-500 flex justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <p class="text-center">生成${fieldDisplayName}失败，请重试</p>
          <p class="text-center text-sm text-red-500 mt-2">${error.message || '未知错误'}</p>
          <button id="ai-error-close" class="mt-4 w-full p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
            关闭
          </button>
        </div>
      `;

      document.getElementById('ai-error-close')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡
        loadingEl.remove();
        setIsLoading(false);
      });
    } finally {
      // 确保在所有情况下都重置加载状态
      if (!document.body.contains(loadingEl)) {
        setIsLoading(false);
      }
    }
    });
  };

  // 根据模式渲染不同的按钮
  if (mode === 'update') {
    // 更新模式：渲染更新按钮
    return (
      <button
        className="ml-2 p-1 text-blue-600 hover:text-blue-800 transition-colors"
        onClick={handleAIClick}
        title={`使用AI更新${fieldDisplayName}`}
        onMouseDown={(e) => e.stopPropagation()} // 阻止鼠标按下事件冒泡
        onMouseUp={(e) => e.stopPropagation()} // 阻止鼠标释放事件冒泡
        disabled={isLoading}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </button>
    );
  } else {
    // 生成模式：渲染生成按钮
    return (
      <button
        className="ml-2 p-1 text-purple-600 hover:text-purple-800 transition-colors"
        onClick={handleAIClick}
        title={`使用AI生成${fieldDisplayName}`}
        onMouseDown={(e) => e.stopPropagation()} // 阻止鼠标按下事件冒泡
        onMouseUp={(e) => e.stopPropagation()} // 阻止鼠标释放事件冒泡
        disabled={isLoading}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      </button>
    );
  }
};

export default CharacterFieldAIButton;
