"use client";

import React, { useState, useEffect } from 'react';

interface ChapterBookIconProps {
  isActive?: boolean;
  isAnimating?: boolean;
  animationDirection?: 'in' | 'out';
  size?: number;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 章节书籍图标组件
 * 基于book-bookmark-minimalistic设计，支持3D翻页动画
 */
export const ChapterBookIcon: React.FC<ChapterBookIconProps> = ({
  isActive = false,
  isAnimating = false,
  animationDirection = 'in',
  size = 24,
  className = '',
  style = {}
}) => {
  const [animationKey, setAnimationKey] = useState(0);

  // 触发动画重新开始
  useEffect(() => {
    if (isAnimating) {
      setAnimationKey(prev => prev + 1);
    }
  }, [isAnimating, animationDirection]);

  return (
    <div
      className={`chapter-book-icon-container ${className}`}
      style={{
        width: size,
        height: size,
        perspective: '1000px',
        transformStyle: 'preserve-3d',
        ...style
      }}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={`
          chapter-book-icon
          ${isAnimating ? `flipping-${animationDirection}` : ''}
          ${isActive ? 'active' : ''}
        `}
        style={{
          transformOrigin: 'left center',
          transition: 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          transform: isAnimating 
            ? animationDirection === 'out' 
              ? 'rotateY(-90deg)' 
              : 'rotateY(0deg)'
            : 'rotateY(0deg)'
        }}
      >
        {/* 书本主体 - 基于book-bookmark-minimalistic */}
        <path
          d="M10 22C7.17157 22 5.75736 22 4.87868 21.1213C4 20.2426 4 18.8284 4 16V8C4 5.17157 4 3.75736 4.87868 2.87868C5.75736 2 7.17157 2 10 2H14C16.8284 2 18.2426 2 19.1213 2.87868C20 3.75736 20 5.17157 20 8M14 22C16.8284 22 18.2426 22 19.1213 21.1213C20 20.2426 20 18.8284 20 16V12"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          className="book-outline"
        />
        
        {/* 书页效果线 */}
        <path
          d="M19.8978 16H7.89778C6.96781 16 6.50282 16 6.12132 16.1022C5.08604 16.3796 4.2774 17.1883 4 18.2235"
          stroke="currentColor"
          strokeWidth="1.5"
          className="book-page"
        />
        
        {/* 书脊线 */}
        <path
          d="M7 16V9M7 2.5V5"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          className="book-spine"
        />
        
        {/* 书签 - 当前章节时显示 */}
        {isActive && (
          <g className="bookmark-indicator">
            <path
              d="M13 16V19.5309C13 19.8065 13 19.9443 12.9051 20C12.8103 20.0557 12.6806 19.9941 12.4211 19.8708L11.1789 19.2808C11.0911 19.2391 11.0472 19.2182 11 19.2182C10.9528 19.2182 10.9089 19.2391 10.8211 19.2808L9.57889 19.8708C9.31943 19.9941 9.18971 20.0557 9.09485 20C9 19.9443 9 19.8065 9 19.5309V16.45"
              stroke="#fbbf24"
              strokeWidth="1.5"
              strokeLinecap="round"
              fill="#fbbf24"
              opacity="0.8"
              className="bookmark-ribbon"
            />
            
            {/* 书签光泽效果 */}
            <path
              d="M10.5 16.5L11.5 17.5"
              stroke="#fff"
              strokeWidth="0.5"
              strokeLinecap="round"
              opacity="0.6"
              className="bookmark-shine"
            />
          </g>
        )}
        
        {/* 页面内容线条 - 增强书籍感 */}
        <g className="page-content" opacity="0.4">
          <line x1="9" y1="6" x2="15" y2="6" stroke="currentColor" strokeWidth="0.5" />
          <line x1="9" y1="8" x2="13" y2="8" stroke="currentColor" strokeWidth="0.5" />
          <line x1="9" y1="10" x2="16" y2="10" stroke="currentColor" strokeWidth="0.5" />
        </g>
        
        {/* 3D阴影效果 */}
        <defs>
          <filter id="book-shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow
              dx="2"
              dy="4"
              stdDeviation="3"
              floodColor="rgba(0,0,0,0.2)"
              className="book-shadow-filter"
            />
          </filter>
          
          {/* 书签渐变 */}
          <linearGradient id="bookmark-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fbbf24" stopOpacity="1" />
            <stop offset="50%" stopColor="#f59e0b" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#d97706" stopOpacity="0.6" />
          </linearGradient>
        </defs>
      </svg>
      
      {/* 翻页时的纸质纹理背景 */}
      {isAnimating && (
        <div
          className="paper-texture-overlay"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%)',
            backgroundSize: '4px 4px',
            opacity: 0.3,
            pointerEvents: 'none',
            borderRadius: '4px'
          }}
        />
      )}
    </div>
  );
};

/**
 * 章节翻页动画Hook
 * 控制章节切换时的3D翻页效果
 */
export const useChapterFlipAnimation = () => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [direction, setDirection] = useState<'next' | 'prev'>('next');
  const [currentChapterId, setCurrentChapterId] = useState<string | null>(null);

  const triggerFlip = (newChapterId: string, oldChapterId: string | null, chapterList: any[]) => {
    if (isAnimating || newChapterId === oldChapterId) return;

    // 确定翻页方向
    if (oldChapterId && chapterList.length > 0) {
      const oldIndex = chapterList.findIndex(ch => ch.id === oldChapterId);
      const newIndex = chapterList.findIndex(ch => ch.id === newChapterId);
      setDirection(newIndex > oldIndex ? 'next' : 'prev');
    }

    setIsAnimating(true);
    
    // 动画序列：先翻出，再翻入
    setTimeout(() => {
      setCurrentChapterId(newChapterId);
    }, 300); // 翻页中点

    setTimeout(() => {
      setIsAnimating(false);
    }, 600); // 动画结束
  };

  return {
    isAnimating,
    direction,
    currentChapterId,
    triggerFlip
  };
};
