import {
  IAIFactory,
  IAIWritingComponent,
  IAIBookAnalysisComponent,
  IAIRewriteComponent,
  IAIContinueComponent,
  IAISenderComponent,
  IAICharacterComponent
} from './interfaces';

import {
  DefaultAIWritingComponent,
  DefaultAIBookAnalysisComponent,
  DefaultAIRewriteComponent,
  DefaultAIContinueComponent,
  DefaultAISenderComponent,
  DefaultAICharacterComponent
} from './components';

/**
 * 默认AI工厂实现
 * 创建各种AI功能组件的默认实现
 */
class DefaultAIFactory implements IAIFactory {
  /**
   * 创建AI写作组件
   * @returns AI写作组件
   */
  createAIWritingComponent(): IAIWritingComponent {
    return new DefaultAIWritingComponent();
  }

  /**
   * 创建AI拆书组件
   * @returns AI拆书组件
   */
  createAIBookAnalysisComponent(): IAIBookAnalysisComponent {
    return new DefaultAIBookAnalysisComponent();
  }

  /**
   * 创建AI选中改写组件
   * @returns AI选中改写组件
   */
  createAIRewriteComponent(): IAIRewriteComponent {
    return new DefaultAIRewriteComponent();
  }

  /**
   * 创建AI续写组件
   * @returns AI续写组件
   */
  createAIContinueComponent(): IAIContinueComponent {
    return new DefaultAIContinueComponent();
  }

  /**
   * 创建AI发送组件
   * @returns AI发送组件
   */
  createAISenderComponent(): IAISenderComponent {
    return new DefaultAISenderComponent();
  }

  /**
   * 创建AI人物组件
   * @returns AI人物组件
   */
  createAICharacterComponent(): IAICharacterComponent {
    const senderComponent = this.createAISenderComponent();
    return new DefaultAICharacterComponent(senderComponent);
  }
}

/**
 * 创建AI工厂
 * @returns AI工厂实例
 */
export function createAIFactory(): IAIFactory {
  return new DefaultAIFactory();
}
