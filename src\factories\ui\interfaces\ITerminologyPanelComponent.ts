import { Terminology } from '@/lib/db/dexie';

/**
 * 术语面板组件接口
 */
export interface ITerminologyPanelComponent {
  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void;

  /**
   * 设置面板是否打开
   * @param isOpen 是否打开
   */
  setIsOpen(isOpen: boolean): void;

  /**
   * 设置关闭回调
   * @param handler 关闭回调函数
   */
  onClose(handler: () => void): void;

  /**
   * 设置创建回调
   * @param handler 创建回调函数
   */
  onCreate(handler: (terminology: Terminology) => void): void;

  /**
   * 设置更新回调
   * @param handler 更新回调函数
   */
  onUpdate(handler: (terminology: Terminology) => void): void;

  /**
   * 设置删除回调
   * @param handler 删除回调函数
   */
  onDelete(handler: (terminologyId: string) => void): void;

  /**
   * 设置类名
   * @param className 类名
   */
  setClassName(className: string): void;

  /**
   * 渲染组件
   */
  render(): React.ReactNode;
}
