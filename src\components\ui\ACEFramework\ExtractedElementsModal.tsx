"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ExtendedACEFramework, ExtractedElement } from '../../../types/ACEFrameworkTypes';
import { ACEFrameworkManager } from '../../../services/ACEFrameworkManager';

// 添加CSS样式支持
const styles = `
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

// 注入样式
if (typeof document !== 'undefined' && !document.getElementById('extracted-elements-modal-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'extracted-elements-modal-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

interface ExtractedElementsModalProps {
  isOpen: boolean;
  framework: ExtendedACEFramework | null;
  onClose: () => void;
  onElementsSelected?: (elements: any[]) => void;
  onElementsDeleted?: () => void; // 添加删除回调
}

/**
 * 拆解元素详情查看对话框
 * 用于查看和管理拆解元素的详细信息
 */
export const ExtractedElementsModal: React.FC<ExtractedElementsModalProps> = ({
  isOpen,
  framework,
  onClose,
  onElementsSelected,
  onElementsDeleted
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  const [fullExtractedElements, setFullExtractedElements] = useState<ExtractedElement[]>([]);
  const [showElementDetail, setShowElementDetail] = useState(false);
  const [selectedElementForDetail, setSelectedElementForDetail] = useState<ExtractedElement | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 加载完整的拆解元素数据
  useEffect(() => {
    if (isOpen && framework) {
      const allElements = ACEFrameworkManager.getExtractedElements();
      // 根据框架名称过滤对应的元素
      const frameworkCategory = framework.name.replace('拆解元素 - ', '');
      const filteredElements = allElements.filter(el => el.category === frameworkCategory);
      setFullExtractedElements(filteredElements);
    }
  }, [isOpen, framework]);

  if (!isOpen || !framework) return null;

  // 按分类分组元素
  const groupedElements = fullExtractedElements.reduce((groups, element) => {
    const category = element.category || '未分类';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(element);
    return groups;
  }, {} as Record<string, ExtractedElement[]>);

  // 获取所有分类
  const categories = ['all', ...Object.keys(groupedElements)];

  // 过滤元素
  const filteredElements = fullExtractedElements.filter(element => {
    const matchesSearch = !searchQuery ||
      element.elements.join(', ').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (element.category && element.category.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || element.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // 获取分类显示名称
  const getCategoryDisplayName = (category: string): string => {
    const categoryNames: Record<string, string> = {
      'all': '🔍 全部',
      '关键词': '🔥 关键词',
      '技巧': '✨ 写作技巧',
      '结构': '🏗️ 故事结构',
      '风格': '🎨 文体风格',
      '创意': '💡 创意元素',
      '场景': '🌍 场景设定',
      '名词': '📝 专业名词',
      '未分类': '❓ 未分类'
    };

    return categoryNames[category] || `📋 ${category}`;
  };

  // 处理元素选择
  const handleElementSelection = (elementIndex: number) => {
    const elementId = `${elementIndex}`;
    setSelectedElements(prev => {
      if (prev.includes(elementId)) {
        return prev.filter(id => id !== elementId);
      } else {
        return [...prev, elementId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedElements(filteredElements.map((_, index) => `${index}`));
    } else {
      setSelectedElements([]);
    }
  };

  // 保存选中的元素
  const handleSave = () => {
    if (onElementsSelected && selectedElements.length > 0) {
      const selectedElementsData = selectedElements.map(id => {
        const index = parseInt(id);
        return filteredElements[index];
      }).filter(Boolean);

      onElementsSelected(selectedElementsData);
    }
    onClose();
  };

  // 查看元素详情
  const handleViewElementDetail = (element: ExtractedElement, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止卡片选择事件
    setSelectedElementForDetail(element);
    setShowElementDetail(true);
  };

  // 删除选中的元素
  const handleDeleteSelected = async () => {
    if (selectedElements.length === 0) return;

    // 确认删除
    const confirmed = window.confirm(`确定要删除选中的 ${selectedElements.length} 个元素吗？此操作不可撤销。`);
    if (!confirmed) return;

    setIsDeleting(true);
    try {
      // 获取要删除的元素
      const elementsToDelete = selectedElements.map(id => {
        const index = parseInt(id);
        return filteredElements[index];
      }).filter(Boolean);

      // 从localStorage中删除这些元素
      const allElements = ACEFrameworkManager.getExtractedElements();
      const remainingElements = allElements.filter(element =>
        !elementsToDelete.some(deleteElement => deleteElement.id === element.id)
      );

      // 保存更新后的元素列表
      localStorage.setItem('ace-extracted-elements', JSON.stringify(remainingElements));

      // 重新加载数据
      const frameworkCategory = framework?.name.replace('拆解元素 - ', '') || '';
      const updatedFilteredElements = remainingElements.filter(el => el.category === frameworkCategory);
      setFullExtractedElements(updatedFilteredElements);

      // 清空选择
      setSelectedElements([]);

      // 通知外部组件更新
      if (onElementsDeleted) {
        onElementsDeleted();
      }

      console.log(`✅ 成功删除 ${elementsToDelete.length} 个元素`);
    } catch (error) {
      console.error('❌ 删除元素失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return createPortal(
    <div className="fixed inset-0 z-[60] flex items-center justify-center">
      {/* 背景遮罩 */}
      <motion.div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />
      
      {/* 弹窗内容 */}
      <motion.div 
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-4xl max-h-[85vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              拆解元素详情 - {framework.name}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              查看AI智能拆解的 {fullExtractedElements.length} 个创作元素
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和过滤 */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="搜索元素名称或分类..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            {/* 分类过滤 */}
            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-100"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {getCategoryDisplayName(category)}
                  </option>
                ))}
              </select>
            </div>

            {/* 操作按钮组 */}
            <div className="flex items-center space-x-3">
              {/* 全选控制 */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="selectAllElements"
                  checked={filteredElements.length > 0 && selectedElements.length === filteredElements.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <label htmlFor="selectAllElements" className="text-sm text-gray-600 dark:text-gray-400">
                  全选
                </label>
              </div>

              {/* 删除按钮 */}
              {selectedElements.length > 0 && (
                <button
                  onClick={handleDeleteSelected}
                  disabled={isDeleting}
                  className="flex items-center px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title={`删除选中的 ${selectedElements.length} 个元素`}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      删除中...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      删除 ({selectedElements.length})
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 元素列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredElements.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <svg className="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20.4a7.962 7.962 0 01-8-7.933V6.09a6.06 6.06 0 016-6.09c1.98 0 3.5.918 4.926 2.149a6.06 6.06 0 014.074 5.75v6.222z" />
                </svg>
              </div>
              <p className="text-gray-500 dark:text-gray-400">没有找到匹配的元素</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <AnimatePresence>
                {filteredElements.map((element, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    className={`relative p-4 rounded-lg border cursor-pointer transition-all duration-200 h-56 min-h-56 max-h-56 flex flex-col ${
                      selectedElements.includes(`${index}`)
                        ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 shadow-md'
                        : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-sm'
                    }`}
                    onClick={() => handleElementSelection(index)}
                  >
                    {/* 右上角按钮组 */}
                    <div className="absolute top-3 right-3 flex items-center space-x-1">
                      {/* 详情按钮 */}
                      <button
                        onClick={(e) => handleViewElementDetail(element, e)}
                        className="p-1 rounded-full text-gray-400 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                        title="查看详情"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>

                      {/* 单个删除按钮 */}
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();
                          if (isDeleting) return;

                          // 确认删除
                          const confirmed = window.confirm(`确定要删除元素"${element.elements.join(', ')}"吗？此操作不可撤销。`);
                          if (!confirmed) return;

                          setIsDeleting(true);
                          try {
                            // 直接删除当前元素
                            const elementToDelete = element;
                            const allElements = ACEFrameworkManager.getExtractedElements();
                            const remainingElements = allElements.filter(el => el.id !== elementToDelete.id);

                            // 保存更新后的元素列表
                            localStorage.setItem('ace-extracted-elements', JSON.stringify(remainingElements));

                            // 重新加载数据
                            const frameworkCategory = framework?.name.replace('拆解元素 - ', '') || '';
                            const updatedFilteredElements = remainingElements.filter(el => el.category === frameworkCategory);
                            setFullExtractedElements(updatedFilteredElements);

                            // 如果当前元素被选中，从选择列表中移除
                            setSelectedElements(prev => prev.filter(id => id !== `${index}`));

                            // 通知外部组件更新
                            if (onElementsDeleted) {
                              onElementsDeleted();
                            }

                            console.log(`✅ 成功删除元素: ${elementToDelete.elements.join(', ')}`);
                          } catch (error) {
                            console.error('❌ 删除元素失败:', error);
                          } finally {
                            setIsDeleting(false);
                          }
                        }}
                        disabled={isDeleting}
                        className="p-1 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors disabled:opacity-50"
                        title="删除此元素"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>

                      {/* 选择框 */}
                      <input
                        type="checkbox"
                        checked={selectedElements.includes(`${index}`)}
                        onChange={() => handleElementSelection(index)}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>

                    {/* 分类标签 - 左上角 */}
                    <div className="absolute top-3 left-3">
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        {element.category}
                      </span>
                    </div>

                    {/* 主要内容 */}
                    <div className="flex-1 flex flex-col pt-8 h-full overflow-hidden">
                      {/* 元素名称 - 固定高度 */}
                      <div className="h-10 mb-2 flex items-start">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2">
                          {element.elements.join(', ')}
                        </h3>
                      </div>

                      {/* 描述信息 - 固定高度 */}
                      <div className="h-12 mb-3 overflow-hidden">
                        {element.sourceText && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed line-clamp-3">
                            {element.sourceText}
                          </div>
                        )}
                      </div>

                      {/* 底部信息 - 固定高度 */}
                      <div className="mt-auto space-y-2 h-16 flex flex-col justify-end">
                        {/* 置信度 */}
                        {element.confidence && (
                          <div>
                            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                              <span>置信度</span>
                              <span>{Math.round(element.confidence * 100)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                              <div
                                className="bg-purple-600 h-1 rounded-full transition-all duration-300"
                                style={{ width: `${element.confidence * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        )}

                        {/* 标签 */}
                        {element.tags && element.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {element.tags.slice(0, 3).map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                              >
                                {tag}
                              </span>
                            ))}
                            {element.tags.length > 3 && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                +{element.tags.length - 3}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div>
              显示 {filteredElements.length} / {fullExtractedElements.length} 个元素
            </div>
            <div className="flex items-center space-x-4">
              {framework.extractedAt && (
                <span>
                  提取时间: {new Date(framework.extractedAt).toLocaleString()}
                </span>
              )}
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  关闭
                </button>
                {selectedElements.length > 0 && onElementsSelected && (
                  <button
                    onClick={handleSave}
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                  >
                    保存选中 ({selectedElements.length})
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 元素详情子对话框 */}
      <AnimatePresence>
        {showElementDetail && selectedElementForDetail && (
          <motion.div
            className="fixed inset-0 z-[70] flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {/* 背景遮罩 */}
            <motion.div
              className="absolute inset-0 bg-black bg-opacity-60"
              onClick={() => setShowElementDetail(false)}
            />

            {/* 详情对话框内容 */}
            <motion.div
              className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col m-4"
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ duration: 0.2 }}
            >
              {/* 详情头部 */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    元素详情
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    查看完整的元素信息和描述
                  </p>
                </div>
                <button
                  onClick={() => setShowElementDetail(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 详情内容 */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-6">
                  {/* 元素名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      元素名称
                    </label>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="text-gray-900 dark:text-gray-100 font-medium">
                        {selectedElementForDetail.elements.join(', ')}
                      </p>
                    </div>
                  </div>

                  {/* 分类 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      分类
                    </label>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        {selectedElementForDetail.category}
                      </span>
                    </div>
                  </div>

                  {/* 完整描述 */}
                  {selectedElementForDetail.sourceText && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        详细描述
                      </label>
                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                          {selectedElementForDetail.sourceText}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* 置信度 */}
                  {selectedElementForDetail.confidence && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        置信度
                      </label>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600 dark:text-gray-400">AI分析置信度</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {Math.round(selectedElementForDetail.confidence * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${selectedElementForDetail.confidence * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 标签 */}
                  {selectedElementForDetail.tags && selectedElementForDetail.tags.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        标签
                      </label>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex flex-wrap gap-2">
                          {selectedElementForDetail.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 提取时间 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      提取时间
                    </label>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {new Date(selectedElementForDetail.extractedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 详情底部 */}
              <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex justify-end">
                  <button
                    onClick={() => setShowElementDetail(false)}
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>,
    document.body
  );
};

export default ExtractedElementsModal;
