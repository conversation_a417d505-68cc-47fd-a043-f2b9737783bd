"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import createCharacterExtractorAIAdapter, { CharacterExtractionResult } from '@/adapters/ai/CharacterExtractorAIAdapter';
import createChapterSegmenter from '@/utils/ai/ChapterSegmenter';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  terminologyIds?: string[];
  worldBuildingIds?: string[];
  summary?: string;
  notes?: string;
}

interface CharacterExtractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  existingCharacters: Character[];
  onCreateCharacter: (characterInfo: any) => Promise<void>;
  onUpdateCharacter: (character: Character, characterInfo: any) => Promise<void>;
  bookId: string;
}

/**
 * 人物提取对话框组件
 * 用于从章节内容中提取人物信息
 */
const CharacterExtractorDialog: React.FC<CharacterExtractorDialogProps> = ({
  isOpen,
  onClose,
  existingCharacters,
  onCreateCharacter,
  onUpdateCharacter,
  bookId
}) => {
  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);

  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 章节搜索
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredChapters, setFilteredChapters] = useState<GenericChapter[]>([]);

  // 提取设置
  const [maxCharacters, setMaxCharacters] = useState<number>(5);
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // 提取状态
  const [isLoading, setIsLoading] = useState(false);
  const [extractedCharacters, setExtractedCharacters] = useState<{ [name: string]: CharacterExtractionResult }>({});
  const [error, setError] = useState<string | null>(null);

  // 选中的提取结果
  const [selectedResults, setSelectedResults] = useState<{ [name: string]: boolean }>({});

  // 已创建的人物集合
  const [createdCharacters, setCreatedCharacters] = useState<Set<string>>(new Set());

  // 创建人物提取AI适配器
  const characterExtractorAIAdapter = createCharacterExtractorAIAdapter();

  // 创建章节分段处理工具
  const segmenter = createChapterSegmenter();

  // 当对话框打开时，加载章节数据并重置状态
  useEffect(() => {
    if (isOpen) {
      // 重置状态
      setSelectedChapterIds([]);
      setMaxCharacters(5);
      setCustomPrompt('');
      setExtractedCharacters({});
      setError(null);
      setSelectedResults({});
      setSearchQuery('');
      setCreatedCharacters(new Set()); // 重置已创建人物集合

      // 加载章节数据
      loadChapters();
    }
  }, [isOpen, bookId]);

  // 当章节数据或搜索查询变化时，更新过滤后的章节列表
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredChapters(chapters);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = chapters.filter(chapter => {
        const title = (chapter.title || '').toLowerCase();
        const content = (chapter.content || '').toLowerCase();
        const chapterNumber = chapter.chapterNumber?.toString() || '';

        return title.includes(query) ||
               content.includes(query) ||
               chapterNumber.includes(query);
      });
      setFilteredChapters(filtered);
    }
  }, [chapters, searchQuery]);

  // 加载章节数据
  const loadChapters = async () => {
    setIsLoadingChapters(true);

    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 chapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
    } catch (error) {
      console.error('加载章节数据失败:', error);
      setChapters([]);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  // 当提取结果变化时，初始化选中状态
  useEffect(() => {
    const initialSelectedResults: { [name: string]: boolean } = {};
    Object.keys(extractedCharacters).forEach(name => {
      initialSelectedResults[name] = true;
    });
    setSelectedResults(initialSelectedResults);
  }, [extractedCharacters]);

  /**
   * 处理章节选择
   * @param chapterId 章节ID
   */
  const handleChapterSelect = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  /**
   * 处理全选章节
   */
  const handleSelectAllChapters = () => {
    if (selectedChapterIds.length === chapters.length) {
      setSelectedChapterIds([]);
    } else {
      setSelectedChapterIds(chapters.map(chapter => chapter.id!));
    }
  };

  /**
   * 处理范围选择
   * @param mode 模式：'select'选择，'deselect'取消选择
   */
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    // 验证输入
    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      setError('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      setError('起始章节不能大于结束章节');
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order :
                    (a.chapterNumber !== undefined ? a.chapterNumber : 999999);
      const orderB = b.order !== undefined ? b.order :
                    (b.chapterNumber !== undefined ? b.chapterNumber : 999999);
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  /**
   * 提取人物信息
   */
  const extractCharacters = async (e?: React.MouseEvent) => {
    // 如果有事件对象，阻止默认行为
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (selectedChapterIds.length === 0) {
      setError('请选择至少一个章节');
      return;
    }

    setIsLoading(true);
    setError(null);
    setExtractedCharacters({});
    setCreatedCharacters(new Set()); // 重置已创建人物集合

    try {
      console.log('开始提取人物信息，选中的章节ID:', selectedChapterIds);
      console.log('当前章节数据:', chapters);

      // 获取选中章节的内容
      const selectedChapters = chapters.filter(chapter => {
        const result = selectedChapterIds.includes(chapter.id!);
        if (!result) {
          console.log('章节未被选中:', chapter.id, chapter.title);
        }
        return result;
      });

      console.log('选中的章节:', selectedChapters);

      // 合并章节内容
      let combinedContent = '';
      for (const chapter of selectedChapters) {
        if (chapter.content) {
          combinedContent += `# ${chapter.title || '无标题章节'}\n\n${chapter.content}\n\n`;
          console.log(`添加章节内容: ${chapter.title}, 内容长度: ${chapter.content.length}`);
        } else {
          console.warn(`章节 ${chapter.id} (${chapter.title}) 没有内容`);
        }
      }

      if (!combinedContent) {
        setError('选中的章节内容为空，无法提取人物信息');
        setIsLoading(false);
        return;
      }

      // 构建自定义提示词
      let finalPrompt = '';
      if (customPrompt) {
        finalPrompt = `${customPrompt}\n\n`;
      }

      if (maxCharacters > 0) {
        finalPrompt += `请从文本中提取最多${maxCharacters}个主要人物。`;
      }

      // 分段处理章节内容
      const segments = segmenter.segmentChapter(combinedContent);

      // 从每个段落中提取人物信息
      const allResults: { [name: string]: CharacterExtractionResult } = {};

      console.log('开始处理章节段落，总段数:', segments.length);

      // 完全禁用流式处理，避免自动刷新页面
      // 注意：我们不再使用流式处理功能

      // 显示处理中消息
      setError(`正在处理 ${segments.length} 个章节段落，请稍候...`);

      // 使用 setTimeout 延迟处理，避免阻塞UI
      setTimeout(async () => {
        try {
          for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            console.log(`处理第 ${i + 1}/${segments.length} 段，长度: ${segment.length}`);

            // 更新处理进度
            setError(`正在处理第 ${i + 1}/${segments.length} 个章节段落，请稍候...`);

            try {
              // 从段落中提取人物信息
              const result = await characterExtractorAIAdapter.extractCharactersFromChapter(
                segment,
                { maxSegmentLength: 3000 }
              );

              console.log(`第 ${i + 1} 段提取结果:`, result);
              console.log(`提取到的人物数量:`, Object.keys(result).length);

              // 合并结果
              for (const [name, info] of Object.entries(result)) {
                console.log(`处理人物: ${name}`, info);

                if (!allResults[name]) {
                  allResults[name] = {
                    newInfo: {},
                    updateReasons: {}
                  };
                }

                // 合并新信息
                for (const [field, value] of Object.entries(info.newInfo)) {
                  allResults[name].newInfo[field] = value;
                }

                // 合并更新原因
                if (info.updateReasons) {
                  for (const [field, reason] of Object.entries(info.updateReasons)) {
                    allResults[name].updateReasons = allResults[name].updateReasons || {};
                    allResults[name].updateReasons[field] = reason;
                  }
                }
              }
            } catch (error) {
              console.error(`处理第 ${i + 1} 段时出错:`, error);
            }
          }

          console.log('所有段落处理完成，最终结果:', allResults);

          // 如果设置了最大人物数量，只保留指定数量的人物
          if (maxCharacters > 0 && Object.keys(allResults).length > maxCharacters) {
            const sortedNames = Object.keys(allResults).sort((a, b) => {
              const aInfoCount = Object.keys(allResults[a].newInfo).length;
              const bInfoCount = Object.keys(allResults[b].newInfo).length;
              return bInfoCount - aInfoCount;
            });

            const limitedResults: { [name: string]: CharacterExtractionResult } = {};
            for (let i = 0; i < Math.min(maxCharacters, sortedNames.length); i++) {
              limitedResults[sortedNames[i]] = allResults[sortedNames[i]];
            }

            setExtractedCharacters(limitedResults);
          } else {
            setExtractedCharacters(allResults);
          }

          // 如果没有提取到人物信息
          if (Object.keys(allResults).length === 0) {
            setError('未从章节中提取到人物信息');
          } else {
            setError(null); // 清除处理中消息
          }
        } catch (error: any) {
          console.error('提取人物信息失败:', error);
          setError('提取人物信息失败: ' + (error.message || '未知错误'));
        } finally {
          setIsLoading(false);
        }
      }, 100); // 延迟100毫秒执行
    } catch (error: any) {
      console.error('准备提取人物信息失败:', error);
      setError('准备提取人物信息失败: ' + (error.message || '未知错误'));
      setIsLoading(false);
    }
  };

  /**
   * 取消提取
   */
  const cancelExtraction = () => {
    characterExtractorAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  /**
   * 切换结果选中状态
   * @param name 人物名称
   */
  const toggleResultSelection = (name: string) => {
    setSelectedResults(prev => ({
      ...prev,
      [name]: !prev[name]
    }));
  };

  /**
   * 全选/取消全选结果
   */
  const toggleSelectAllResults = () => {
    const allSelected = Object.values(selectedResults).every(selected => selected);

    const newSelectedResults: { [name: string]: boolean } = {};
    Object.keys(extractedCharacters).forEach(name => {
      newSelectedResults[name] = !allSelected;
    });

    setSelectedResults(newSelectedResults);
  };

  /**
   * 检查人物是否已存在
   * @param name 人物名称
   * @returns 是否已存在
   */
  const characterExists = (name: string): boolean => {
    return existingCharacters.some(
      c => c.name === name || (c.alias && c.alias.includes(name))
    );
  };

  /**
   * 应用选中的结果
   */
  const applySelectedResults = async (e?: React.MouseEvent) => {
    // 如果有事件对象，阻止默认行为
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    const selectedNames = Object.entries(selectedResults)
      .filter(([_, selected]) => selected)
      .map(([name]) => name);

    if (selectedNames.length === 0) {
      setError('请选择至少一个人物');
      return;
    }

    setIsLoading(true);
    setError(null); // 清除之前的错误信息

    // 定义处理项的类型
    interface CharacterToProcess {
      name: string;
      info: CharacterExtractionResult;
      existingCharacter: Character | undefined;
    }

    try {
      // 创建一个临时数组来存储需要创建或更新的人物信息
      const charactersToProcess: CharacterToProcess[] = [];

      // 收集所有需要处理的人物信息
      for (const name of selectedNames) {
        const info = extractedCharacters[name];

        // 如果启用了流式处理，检查是否已经创建过
        if (createdCharacters && createdCharacters.has(name)) {
          console.log(`跳过已经通过流式处理创建/更新的人物: ${name}`);
          continue;
        }

        // 检查人物是否已存在
        const existingCharacter = existingCharacters.find(
          c => c.name === name || (c.alias && c.alias.includes(name))
        );

        charactersToProcess.push({
          name,
          info,
          existingCharacter
        });
      }

      // 显示处理信息
      console.log(`准备处理 ${charactersToProcess.length} 个人物信息`);

      // 立即显示处理中消息
      setError(`正在处理 ${charactersToProcess.length} 个人物信息，请稍候...`);

      // 使用 setTimeout 延迟处理，避免阻塞UI和可能的页面刷新
      setTimeout(async () => {
        try {
          let successCount = 0;

          // 处理每个选中的人物
          for (const { name, info, existingCharacter } of charactersToProcess) {
            try {
              let success = false;

              if (existingCharacter) {
                // 更新现有人物
                console.log(`更新现有人物 ${name}:`, info);

                // 创建符合接收端期望格式的更新信息对象
                const updateCharacterInfo = {
                  name,
                  newInfo: {
                    name,
                    description: info.newInfo?.description || '',
                    appearance: info.newInfo?.appearance || '',
                    personality: info.newInfo?.personality || '',
                    background: info.newInfo?.background || '',
                    goals: info.newInfo?.goals || '',
                    characterArchetype: info.newInfo?.characterArchetype || '',
                    growthArc: info.newInfo?.growthArc || '',
                    hiddenMotivation: info.newInfo?.hiddenMotivation || '',
                    secretHistory: info.newInfo?.secretHistory || '',
                    innerConflicts: info.newInfo?.innerConflicts || '',
                    symbolism: info.newInfo?.symbolism || '',
                    relationships: info.newInfo?.relationships || ''
                  }
                };

                console.log(`更新人物信息对象:`, updateCharacterInfo);
                console.log('AI提取的原始数据:', info.newInfo);

                // 使用 setTimeout 延迟执行，避免立即触发页面刷新
                setTimeout(async () => {
                  try {
                    await onUpdateCharacter(existingCharacter, updateCharacterInfo);
                    console.log(`成功更新人物 ${name}`);
                  } catch (err) {
                    console.error(`延迟更新人物 ${name} 失败:`, err);
                  }
                }, 0);

                success = true;
              } else {
                // 创建新人物
                console.log(`创建新人物 ${name}:`, info);

                // 创建符合接收端期望格式的人物信息对象
                const newCharacterInfo = {
                  name,
                  newInfo: {
                    name,
                    description: info.newInfo?.description || '',
                    appearance: info.newInfo?.appearance || '',
                    personality: info.newInfo?.personality || '',
                    background: info.newInfo?.background || '',
                    goals: info.newInfo?.goals || '',
                    characterArchetype: info.newInfo?.characterArchetype || '',
                    growthArc: info.newInfo?.growthArc || '',
                    hiddenMotivation: info.newInfo?.hiddenMotivation || '',
                    secretHistory: info.newInfo?.secretHistory || '',
                    innerConflicts: info.newInfo?.innerConflicts || '',
                    symbolism: info.newInfo?.symbolism || '',
                    relationships: info.newInfo?.relationships || ''
                  }
                };

                console.log('完整的新人物信息:', newCharacterInfo);
                console.log('AI提取的原始数据:', info.newInfo);

                // 使用 setTimeout 延迟执行，避免立即触发页面刷新
                setTimeout(async () => {
                  try {
                    await onCreateCharacter(newCharacterInfo);
                    console.log(`成功创建人物 ${name}`);
                  } catch (err) {
                    console.error(`延迟创建人物 ${name} 失败:`, err);
                  }
                }, 0);

                success = true;
              }

              if (success) {
                // 更新已创建的人物集合
                setCreatedCharacters(prev => {
                  const newSet = new Set(prev);
                  newSet.add(name);
                  return newSet;
                });

                successCount++;
              }
            } catch (itemError) {
              console.error(`处理人物 ${name} 时出错:`, itemError);
              // 继续处理下一个人物，不中断整个过程
            }
          }

          // 显示成功消息，不自动关闭对话框
          setError(`成功应用了 ${successCount} 个人物信息。您可以继续操作或手动关闭对话框。`);
        } catch (error: any) {
          console.error('应用结果失败:', error);
          setError('应用结果失败: ' + (error.message || '未知错误'));
        } finally {
          setIsLoading(false);
        }
      }, 100); // 延迟100毫秒执行
    } catch (error: any) {
      console.error('准备应用结果失败:', error);
      setError('准备应用结果失败: ' + (error.message || '未知错误'));
      setIsLoading(false);
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <h2 className="text-xl font-bold mb-4 text-purple-700">从章节提取人物信息</h2>

        {/* 主体内容 */}
        <div className="flex-1 overflow-auto">
          {/* 章节选择 */}
          {!isLoading && Object.keys(extractedCharacters).length === 0 && (
            <div className="mb-6">
              <div className="mb-2">
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold">选择章节</h3>
                  <button
                    className="text-sm text-blue-600 hover:text-blue-800"
                    onClick={handleSelectAllChapters}
                  >
                    {selectedChapterIds.length === chapters.length ? '取消全选' : '全选'}
                  </button>
                </div>

                {/* 搜索框 */}
                <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                  <span className="text-sm text-gray-600">搜索章节:</span>
                  <div className="flex-1">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="输入章节标题或内容关键词"
                      className="w-full p-1 text-sm border rounded"
                    />
                  </div>
                  {searchQuery && (
                    <button
                      className="px-2 py-1 text-sm text-gray-600 hover:text-gray-800"
                      onClick={() => setSearchQuery('')}
                    >
                      清除
                    </button>
                  )}
                  <div className="text-xs text-gray-500">
                    {searchQuery ? `找到: ${filteredChapters.length}/${chapters.length}` : ''}
                  </div>
                </div>

                {/* 范围选择 */}
                <div className="mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                  <span className="text-sm text-gray-600">范围选择:</span>
                  <div className="flex items-center">
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      min="1"
                      className="w-16 p-1 text-sm border rounded"
                    />
                    <span className="mx-1">-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      min="1"
                      className="w-16 p-1 text-sm border rounded"
                    />
                  </div>
                  <div className="flex space-x-1">
                    <button
                      className="px-2 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                      onClick={() => handleRangeSelect('select')}
                      disabled={!rangeStart || !rangeEnd}
                    >
                      选择
                    </button>
                    <button
                      className="px-2 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
                      onClick={() => handleRangeSelect('deselect')}
                      disabled={!rangeStart || !rangeEnd}
                    >
                      取消选择
                    </button>
                  </div>
                  <div className="text-xs text-gray-500">
                    已选: {selectedChapterIds.length}/{chapters.length}
                  </div>
                </div>
              </div>

              <div className="max-h-60 overflow-y-auto border rounded-lg p-2">
                {isLoadingChapters ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-700 mx-auto mb-2"></div>
                    <p className="text-gray-500">正在加载章节数据...</p>
                  </div>
                ) : chapters.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    未找到章节数据，请确保已创建章节
                  </div>
                ) : filteredChapters.length === 0 && searchQuery ? (
                  <div className="p-4 text-center text-gray-500">
                    没有找到匹配"{searchQuery}"的章节
                  </div>
                ) : (
                  filteredChapters.map(chapter => (
                    <div
                      key={chapter.id}
                      className={`flex items-center p-2 rounded-md transition-all duration-200 ${
                        selectedChapterIds.includes(chapter.id!)
                          ? 'bg-purple-100 border border-purple-300'
                          : 'hover:bg-gray-100 border border-transparent'
                      }`}
                    >
                      <input
                        type="checkbox"
                        id={`chapter-${chapter.id}`}
                        checked={selectedChapterIds.includes(chapter.id!)}
                        onChange={() => handleChapterSelect(chapter.id!)}
                        className="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 rounded"
                      />
                      <label
                        htmlFor={`chapter-${chapter.id}`}
                        className={`flex-1 cursor-pointer ${
                          selectedChapterIds.includes(chapter.id!) ? 'font-medium text-purple-800' : 'text-gray-700'
                        }`}
                      >
                        {chapter.title || `章节 ${chapter.chapterNumber || '未编号'}`}
                      </label>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* 提取设置 */}
          {!isLoading && Object.keys(extractedCharacters).length === 0 && (
            <div className="mb-6">
              <h3 className="font-semibold mb-2">提取设置</h3>

              <div className="space-y-4">
                <div>
                  <label htmlFor="max-characters" className="block text-sm font-medium text-gray-700 mb-1">
                    最大人物数量
                  </label>
                  <input
                    type="number"
                    id="max-characters"
                    value={maxCharacters}
                    onChange={e => setMaxCharacters(parseInt(e.target.value) || 0)}
                    min="0"
                    max="20"
                    className="w-full p-2 border rounded-lg"
                    placeholder="0表示不限制"
                  />
                  <p className="text-xs text-gray-500 mt-1">设置为0表示不限制人物数量</p>
                </div>

                <div>
                  <label htmlFor="custom-prompt" className="block text-sm font-medium text-gray-700 mb-1">
                    自定义提示词（可选）
                  </label>
                  <textarea
                    id="custom-prompt"
                    value={customPrompt}
                    onChange={e => setCustomPrompt(e.target.value)}
                    className="w-full p-2 border rounded-lg"
                    rows={3}
                    placeholder="输入自定义提示词，例如：请关注主角的性格特点和成长变化"
                  />
                </div>
              </div>
            </div>
          )}

          {/* 提取结果 */}
          {!isLoading && Object.keys(extractedCharacters).length > 0 && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">提取结果</h3>
                <button
                  className="text-sm text-blue-600 hover:text-blue-800"
                  onClick={toggleSelectAllResults}
                >
                  {Object.values(selectedResults).every(selected => selected) ? '取消全选' : '全选'}
                </button>
              </div>

              <div className="space-y-4 max-h-[60vh] overflow-y-auto pr-2">
                {Object.entries(extractedCharacters).map(([name, info]) => (
                  <div key={name} className="border p-4 rounded-lg">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={`result-${name}`}
                        checked={selectedResults[name] || false}
                        onChange={() => toggleResultSelection(name)}
                        className="mr-2"
                      />
                      <label htmlFor={`result-${name}`} className="font-bold text-lg cursor-pointer">
                        {name} {characterExists(name) && <span className="text-sm text-blue-600">(已存在)</span>}
                      </label>
                    </div>

                    {/* 人物信息预览 */}
                    <div className="mt-2 space-y-1 ml-6">
                      {Object.entries(info.newInfo).map(([field, value]) => (
                        <p key={field} className="text-sm">
                          <span className="font-semibold">{field}:</span> {value.length > 100 ? value.substring(0, 100) + '...' : value}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 消息提示（错误或成功） */}
        {error && (
          <div className={`mt-4 p-3 rounded-lg ${
            error.startsWith('成功')
              ? 'bg-green-100 text-green-700' // 成功消息使用绿色
              : 'bg-red-100 text-red-700'     // 错误消息使用红色
          }`}>
            {error}
          </div>
        )}

        {/* 底部按钮 */}
        <div className="mt-6 flex justify-end space-x-3">
          {/* 取消/关闭按钮 */}
          <button
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            disabled={isLoading}
          >
            {error && error.startsWith('成功') ? '关闭' : '取消'}
          </button>

          {/* 根据当前状态显示不同的操作按钮 */}
          {!isLoading && Object.keys(extractedCharacters).length === 0 ? (
            // 提取人物按钮
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              onClick={(e) => extractCharacters(e)}
              disabled={selectedChapterIds.length === 0}
            >
              提取人物
            </button>
          ) : !isLoading && Object.keys(extractedCharacters).length > 0 ? (
            // 如果已经成功应用了结果，显示完成按钮；否则显示应用按钮
            error && error.startsWith('成功') ? (
              <button
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onClose();
                }}
              >
                完成
              </button>
            ) : (
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={(e) => applySelectedResults(e)}
                disabled={Object.values(selectedResults).every(selected => !selected)}
              >
                应用选中结果
              </button>
            )
          ) : (
            // 取消提取按钮
            <button
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                cancelExtraction();
              }}
            >
              取消提取
            </button>
          )}
        </div>

        {/* 加载中状态 */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700 mx-auto mb-4"></div>
              <p className="text-purple-700">正在提取人物信息...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CharacterExtractorDialog;
