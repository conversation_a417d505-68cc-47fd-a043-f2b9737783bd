"use client";

import React, { useState } from 'react';
import { SegmentStructure, CoreMystery } from '@/factories/ai/services/types/ShortStoryTypes';

interface ShortStoryTabbedViewProps {
  coreMystery: CoreMystery | null;
  segments: SegmentStructure[];
  fullText: string;
  isLoading: boolean;
  currentSegmentIndex: number;
  streamingContent: string;
  onGenerateSegment: (segmentIndex: number) => void;
  onGeneratePhase: (phaseSegments: SegmentStructure[]) => void;
}

type TabType = 'structure' | 'generation' | 'preview';

/**
 * 短篇创作三标签页视图组件
 * 包含：结构预览、段落生成、完整正文预览
 */
export const ShortStoryTabbedView: React.FC<ShortStoryTabbedViewProps> = ({
  coreMystery,
  segments,
  fullText,
  isLoading,
  currentSegmentIndex,
  streamingContent,
  onGenerateSegment,
  onGeneratePhase
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('structure');

  // 按阶段分组段落（4个阶段）
  const getPhaseSegments = (phase: string) => {
    const totalSegments = segments.length;
    const phaseMap: Record<string, (total: number) => number[]> = {
      '铺垫期': (total) => Array.from({ length: Math.ceil(total * 0.25) }, (_, i) => i + 1),
      '挤压期': (total) => {
        const start = Math.ceil(total * 0.25) + 1;
        const end = Math.ceil(total * 0.75);
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
      },
      '高潮期': (total) => {
        const start = Math.ceil(total * 0.75) + 1;
        const end = Math.ceil(total * 0.95);
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
      },
      '结局期': (total) => {
        const start = Math.ceil(total * 0.95) + 1;
        return Array.from({ length: total - start + 1 }, (_, i) => start + i);
      }
    };
    
    return segments.filter(seg => 
      phaseMap[phase]?.(totalSegments)?.includes(seg.segmentNumber) || false
    );
  };

  // 计算生成进度
  const generatedCount = segments.filter(seg => seg.content).length;
  const totalCount = segments.length;
  const progress = totalCount > 0 ? (generatedCount / totalCount) * 100 : 0;

  return (
    <div className="flex-1 bg-gray-50 rounded-xl overflow-hidden flex flex-col">
      {/* 标签页导航 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveTab('structure')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'structure'
                ? 'bg-amber-100 text-amber-800 border border-amber-200'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
          >
            📋 结构预览
          </button>
          <button
            onClick={() => setActiveTab('generation')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'generation'
                ? 'bg-amber-100 text-amber-800 border border-amber-200'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
          >
            ⚡ 段落生成 ({generatedCount}/{totalCount})
          </button>
          <button
            onClick={() => setActiveTab('preview')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'preview'
                ? 'bg-amber-100 text-amber-800 border border-amber-200'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
          >
            📖 完整预览
          </button>
        </div>
        
        {/* 进度条 */}
        {totalCount > 0 && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
              <span>生成进度</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-amber-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 标签页内容 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'structure' && (
          <StructureTab 
            coreMystery={coreMystery}
            segments={segments}
            getPhaseSegments={getPhaseSegments}
          />
        )}
        
        {activeTab === 'generation' && (
          <GenerationTab
            segments={segments}
            isLoading={isLoading}
            currentSegmentIndex={currentSegmentIndex}
            streamingContent={streamingContent}
            onGenerateSegment={onGenerateSegment}
            onGeneratePhase={onGeneratePhase}
            getPhaseSegments={getPhaseSegments}
          />
        )}
        
        {activeTab === 'preview' && (
          <PreviewTab
            segments={segments}
            fullText={fullText}
          />
        )}
      </div>
    </div>
  );
};

// 结构预览标签页
const StructureTab: React.FC<{
  coreMystery: CoreMystery | null;
  segments: SegmentStructure[];
  getPhaseSegments: (phase: string) => SegmentStructure[];
}> = ({ coreMystery, segments, getPhaseSegments }) => {
  return (
    <div className="h-full overflow-y-auto p-6">
      {/* 核心悬念展示 */}
      {coreMystery && (
        <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-amber-500">
          <h4 className="font-semibold text-gray-800 mb-2">📖 核心悬念</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium text-amber-600">标题:</span> {coreMystery.title}</div>
            <div><span className="font-medium text-amber-600">核心问题:</span> {coreMystery.coreQuestion}</div>
            <div><span className="font-medium text-green-600">透露的一半:</span> {coreMystery.revealedHalf}</div>
            <div><span className="font-medium text-red-600">隐藏的一半:</span> {coreMystery.hiddenHalf}</div>
          </div>
        </div>
      )}

      {/* 四阶段结构展示 */}
      <div className="space-y-6">
        {['铺垫期', '挤压期', '高潮期', '结局期'].map((phase, phaseIndex) => {
          const phaseSegments = getPhaseSegments(phase);
          if (phaseSegments.length === 0) return null;

          const phaseColors = [
            'border-blue-400 bg-blue-50',
            'border-orange-400 bg-orange-50', 
            'border-red-400 bg-red-50',
            'border-green-400 bg-green-50'
          ];

          return (
            <div key={phase} className={`border-l-4 ${phaseColors[phaseIndex]} p-4 rounded-r-lg`}>
              <h5 className="font-semibold text-gray-800 mb-3 flex items-center">
                <span className="mr-2">{['🌱', '⚡', '🔥', '🌟'][phaseIndex]}</span>
                {phase} ({phaseSegments.length}段)
              </h5>
              
              <div className="grid gap-3">
                {phaseSegments.map((segment) => (
                  <div 
                    key={segment.segmentNumber}
                    className="bg-white rounded-lg p-3 border border-gray-200"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
                        第{segment.segmentNumber}段
                      </span>
                      <span className="text-xs text-gray-500">
                        {segment.purpose}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs">
                      <div className="flex items-center space-x-1">
                        <span className="text-gray-500">信息量:</span>
                        <span className="text-blue-600 font-medium">
                          {segment.informationLevel}/10
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <span className="text-gray-500">紧张感:</span>
                        <span className="text-red-600 font-medium">
                          {segment.tensionLevel}/10
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 段落生成标签页
const GenerationTab: React.FC<{
  segments: SegmentStructure[];
  isLoading: boolean;
  currentSegmentIndex: number;
  streamingContent: string;
  onGenerateSegment: (segmentIndex: number) => void;
  onGeneratePhase: (phaseSegments: SegmentStructure[]) => void;
  getPhaseSegments: (phase: string) => SegmentStructure[];
}> = ({ segments, isLoading, currentSegmentIndex, streamingContent, onGenerateSegment, onGeneratePhase, getPhaseSegments }) => {
  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="space-y-6">
        {['铺垫期', '挤压期', '高潮期', '结局期'].map((phase, phaseIndex) => {
          const phaseSegments = getPhaseSegments(phase);
          if (phaseSegments.length === 0) return null;

          const phaseColors = [
            'border-blue-400 bg-blue-50',
            'border-orange-400 bg-orange-50', 
            'border-red-400 bg-red-50',
            'border-green-400 bg-green-50'
          ];

          return (
            <div key={phase} className={`border-l-4 ${phaseColors[phaseIndex]} p-4 rounded-r-lg`}>
              <h5 className="font-semibold text-gray-800 mb-3 flex items-center">
                <span className="mr-2">{['🌱', '⚡', '🔥', '🌟'][phaseIndex]}</span>
                {phase} - 阶段生成
              </h5>
              
              {/* 阶段生成按钮 */}
              <div className="mb-4">
                <button
                  onClick={() => {
                    // 生成整个阶段的所有段落
                    const unfinishedSegments = phaseSegments.filter(seg => !seg.content);
                    if (unfinishedSegments.length > 0) {
                      onGeneratePhase(unfinishedSegments);
                    }
                  }}
                  disabled={isLoading || phaseSegments.every(seg => seg.content)}
                  className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  {phaseSegments.every(seg => seg.content) ? '✅ 阶段已完成' : `🚀 生成${phase}全部段落`}
                </button>
              </div>

              <div className="space-y-3">
                {phaseSegments.map((segment) => (
                  <div
                    key={segment.segmentNumber}
                    className={`rounded-lg p-4 border ${
                      segment.paymentHookFlag
                        ? 'bg-gradient-to-r from-red-50 to-orange-50 border-red-200'
                        : 'bg-white border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          segment.paymentHookFlag
                            ? 'bg-red-100 text-red-700'
                            : 'bg-gray-100 text-gray-700'
                        }`}>
                          第{segment.segmentNumber}段
                        </span>
                        {segment.paymentHookFlag && (
                          <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full font-medium">
                            💰 付费卡点
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {segment.purpose}
                        </span>
                      </div>

                      {segment.content ? (
                        <span className="text-xs text-green-600 flex items-center">
                          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          已生成 ({segment.content?.length || 0}字)
                          {segment.paymentHookFlag && (
                            <span className="ml-2 text-red-600">💰</span>
                          )}
                        </span>
                      ) : (
                        <button
                          onClick={() => onGenerateSegment(segment.segmentNumber - 1)}
                          disabled={isLoading}
                          className={`px-3 py-1 text-xs text-white rounded transition-colors disabled:bg-gray-300 ${
                            segment.paymentHookFlag
                              ? 'bg-red-500 hover:bg-red-600'
                              : 'bg-amber-500 hover:bg-amber-600'
                          }`}
                        >
                          {isLoading && currentSegmentIndex === segment.segmentNumber - 1 ? '生成中...' :
                           segment.paymentHookFlag ? '🎯 生成钩子' : '生成内容'}
                        </button>
                      )}
                    </div>

                    {/* 付费卡点特殊提示 */}
                    {segment.paymentHookFlag && (
                      <div className="mb-3 p-2 bg-red-100 border border-red-200 rounded text-xs">
                        <span className="text-red-700 font-medium">
                          🔥 最强剧情释放点 - 制造付费冲动的关键段落
                        </span>
                      </div>
                    )}

                    {/* 显示生成的内容或流式内容 */}
                    {segment.content && (
                      <div className={`text-sm text-gray-700 p-3 rounded border-l-2 whitespace-pre-wrap ${
                        segment.paymentHookFlag
                          ? 'bg-red-50 border-red-400'
                          : 'bg-gray-50 border-amber-400'
                      }`}>
                        {segment.content}
                      </div>
                    )}

                    {/* 显示正在生成的流式内容 */}
                    {isLoading && currentSegmentIndex === segment.segmentNumber - 1 && streamingContent && (
                      <div className={`text-sm text-gray-700 p-3 rounded border-l-2 ${
                        segment.paymentHookFlag
                          ? 'bg-red-100 border-red-400'
                          : 'bg-blue-50 border-blue-400'
                      }`}>
                        <div className="flex items-center mb-2">
                          <div className={`animate-spin rounded-full h-3 w-3 border-b-2 mr-2 ${
                            segment.paymentHookFlag ? 'border-red-500' : 'border-blue-500'
                          }`}></div>
                          <span className={`text-xs ${
                            segment.paymentHookFlag ? 'text-red-600' : 'text-blue-600'
                          }`}>
                            {segment.paymentHookFlag ? '正在制造最强钩子...' : '正在生成...'}
                          </span>
                        </div>
                        <div className="whitespace-pre-wrap">{streamingContent}</div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 完整预览标签页
const PreviewTab: React.FC<{
  segments: SegmentStructure[];
  fullText: string;
}> = ({ segments, fullText }) => {
  const generatedSegments = segments.filter(seg => seg.content);
  const totalWordCount = generatedSegments.reduce((sum, seg) => sum + (seg.content?.length || 0), 0);

  return (
    <div className="h-full overflow-y-auto p-6">
      {/* 统计信息 */}
      <div className="bg-white rounded-lg p-4 mb-6 border border-gray-200">
        <h4 className="font-semibold text-gray-800 mb-2">📊 创作统计</h4>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{generatedSegments.length}</div>
            <div className="text-gray-500">已生成段落</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{totalWordCount}</div>
            <div className="text-gray-500">总字数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-amber-600">
              {generatedSegments.length > 0 ? Math.round(totalWordCount / generatedSegments.length) : 0}
            </div>
            <div className="text-gray-500">平均段落字数</div>
          </div>
        </div>
      </div>

      {/* 完整正文 */}
      {fullText ? (
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <h4 className="font-semibold text-gray-800 mb-4">📖 完整正文</h4>
          <div className="prose max-w-none text-gray-700 leading-relaxed whitespace-pre-wrap">
            {fullText}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg p-6 border border-gray-200 text-center">
          <div className="text-gray-400 text-4xl mb-4">📝</div>
          <p className="text-gray-500">暂无生成内容</p>
          <p className="text-sm text-gray-400 mt-1">请先在"段落生成"标签页中生成内容</p>
        </div>
      )}
    </div>
  );
};
