"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { OutlineNode } from './OutlineNode';
import { Outline, OutlineNodeType } from '../../types/outline';
import DeleteConfirmDialog from './DeleteConfirmDialog';
import { findNode } from '../../utils/outlineUtils';

// 节点类型中文映射函数
const getNodeTypeLabel = (type: string): string => {
  switch (type) {
    case 'volume':
      return '总纲/卷';
    case 'event':
      return '事件刚';
    case 'chapter':
      return '章节';
    case 'plot':
      return '剧情节点';
    case 'dialogue':
      return '对话设计';
    case 'synopsis':
      return '核心故事梗概';
    default:
      return type || '未知';
  }
};

interface OutlineTreeProps {
  outline: Outline | null;
  bookId?: string;
  onChange: (outline: Outline) => void;
}

/**
 * 大纲树组件
 * 使用递归渲染确保所有节点在同一个容器中
 */
export const OutlineTree: React.FC<OutlineTreeProps> = ({ outline, bookId, onChange }) => {
  // 删除确认弹窗状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<OutlineNodeType | null>(null);

  // 组件挂载时记录日志
  useEffect(() => {
    console.log('大纲树组件挂载, bookId:', bookId);
    console.log('大纲数据:', outline);

    return () => {
      console.log('大纲树组件卸载');
    };
  }, []);

  // 当大纲数据变化时记录日志
  useEffect(() => {
    console.log('大纲数据变化:', outline);
  }, [outline]);

  // 处理节点变更
  const handleNodeChange = (nodeId: string, updates: Partial<OutlineNodeType>) => {
    if (!outline) return;

    const updateNode = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, ...updates };
        }
        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: updateNode(node.children)
          };
        }
        return node;
      });
    };

    const updatedOutline: Outline = {
      ...outline,
      nodes: updateNode(outline.nodes)
    };

    onChange(updatedOutline);

    // 自动保存大纲数据
    if (bookId) {
      try {
        // 导入 outlineService
        import('@/factories/api/outlineService').then(({ outlineService }) => {
          console.log('节点变更后自动保存大纲数据...');
          outlineService.saveOutline(bookId, updatedOutline)
            .then(() => console.log('大纲数据自动保存成功'))
            .catch(error => console.error('大纲数据自动保存失败:', error));
        });
      } catch (error) {
        console.error('导入 outlineService 失败:', error);
      }
    }
  };

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    if (!outline?.nodes) {
      console.warn('handleNodeDelete: 没有大纲数据');
      return;
    }

    // 找到要删除的节点数据
    const nodeToDeleteData = findNode(outline.nodes, nodeId);
    if (!nodeToDeleteData) {
      console.warn('handleNodeDelete: 未找到要删除的节点:', nodeId);
      return;
    }

    // 设置删除确认弹窗状态
    setNodeToDelete(nodeToDeleteData);
    setDeleteConfirmOpen(true);
  }, [outline]);

  // 处理确认删除
  const handleConfirmDelete = useCallback(async () => {
    if (!nodeToDelete) return;

    const nodeId = nodeToDelete.id;

    const deleteNode = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.filter(node => {
        if (node.id === nodeId) {
          return false;
        }
        if (node.children && node.children.length > 0) {
          node.children = deleteNode(node.children);
        }
        return true;
      });
    };

    const updatedOutline = {
      ...outline,
      nodes: deleteNode(outline.nodes)
    };

    onChange(updatedOutline);

    // 自动保存大纲数据
    if (bookId) {
      try {
        // 导入 outlineService
        import('@/factories/api/outlineService').then(({ outlineService }) => {
          console.log('删除节点后自动保存大纲数据...');
          outlineService.saveOutline(bookId, updatedOutline)
            .then(() => console.log('大纲数据自动保存成功'))
            .catch(error => console.error('大纲数据自动保存失败:', error));
        });
      } catch (error) {
        console.error('导入 outlineService 失败:', error);
      }
    }

    // 关闭弹窗
    setDeleteConfirmOpen(false);
    setNodeToDelete(null);
  }, [nodeToDelete, outline, onChange, bookId]);

  // 处理取消删除
  const handleCancelDelete = useCallback(() => {
    setDeleteConfirmOpen(false);
    setNodeToDelete(null);
  }, []);

  // 处理添加子节点
  const handleAddChild = (parentId: string) => {
    let updatedOutline: any;

    // 处理根节点添加
    if (parentId === 'root') {
      const newNode: OutlineNodeType = {
        id: `node-${Date.now()}`,
        title: `新${getNodeTypeLabel('event')}`,
        type: 'event' as const,
        children: []
      };

      updatedOutline = {
        ...outline,
        nodes: [...outline.nodes, newNode]
      };

      onChange(updatedOutline);

      // 自动保存大纲数据
      if (bookId) {
        try {
          // 导入 outlineService
          import('@/factories/api/outlineService').then(({ outlineService }) => {
            console.log('添加根节点后自动保存大纲数据...');
            outlineService.saveOutline(bookId, updatedOutline)
              .then(() => console.log('大纲数据自动保存成功'))
              .catch(error => console.error('大纲数据自动保存失败:', error));
          });
        } catch (error) {
          console.error('导入 outlineService 失败:', error);
        }
      }

      return;
    }

    // 处理子节点添加
    const addChild = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.map(node => {
        if (node.id === parentId) {
          const newChild: OutlineNodeType = {
            id: `node-${Date.now()}`,
            title: `新${getNodeTypeLabel('plot')}`,
            type: 'plot' as const,
            children: []
          };
          return {
            ...node,
            children: [...(node.children || []), newChild],
            expanded: true
          };
        }
        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: addChild(node.children)
          };
        }
        return node;
      });
    };

    updatedOutline = {
      ...outline,
      nodes: addChild(outline.nodes)
    };

    onChange(updatedOutline);

    // 自动保存大纲数据
    if (bookId) {
      try {
        // 导入 outlineService
        import('@/factories/api/outlineService').then(({ outlineService }) => {
          console.log('添加子节点后自动保存大纲数据...');
          outlineService.saveOutline(bookId, updatedOutline)
            .then(() => console.log('大纲数据自动保存成功'))
            .catch(error => console.error('大纲数据自动保存失败:', error));
        });
      } catch (error) {
        console.error('导入 outlineService 失败:', error);
      }
    }
  };

  // 递归渲染节点函数
  const renderNodes = (nodes: OutlineNodeType[], level: number) => {
    try {
      // 确保nodes是数组
      if (!Array.isArray(nodes)) {
        console.error('renderNodes: nodes不是数组:', nodes);
        return null;
      }

      return nodes.map(node => {
        // 确保node是有效对象
        if (!node || typeof node !== 'object') {
          console.error('renderNodes: 无效的节点对象:', node);
          return null;
        }

        // 确保node.id存在
        if (!node.id) {
          console.error('renderNodes: 节点缺少ID:', node);
          node.id = `node-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        }

        // 确保node.type存在
        if (!node.type) {
          console.warn('renderNodes: 节点缺少类型，设置为默认类型:', node);
          node.type = 'chapter';
        }

        // 确保node.children是数组
        if (node.children && !Array.isArray(node.children)) {
          console.warn('renderNodes: 节点的children不是数组，重置为空数组:', node);
          node.children = [];
        }

        return (
          <React.Fragment key={node.id}>
            <OutlineNode
              node={node}
              level={level}
              bookId={bookId}
              onNodeChange={handleNodeChange}
              onNodeDelete={handleNodeDelete}
              onAddChild={handleAddChild}
            />
            {node.children && node.children.length > 0 && (
              <div
                className={`ml-6 pl-2 relative overflow-hidden transition-all duration-300 ease-out
                  ${node.expanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}
                  ${level < 2 ? 'border-l-2' : 'border-l'} border-[var(--outline-${
                  node.type === 'volume' ? 'volume' : node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'
                })] border-opacity-30`}
                style={{
                  transform: 'translateZ(0)', // 启用GPU加速
                }}
              >
                {/* 连接线 */}
                <div
                  className={`absolute left-0 top-0 w-4 h-4 border-b border-l border-[var(--outline-${
                    node.type === 'chapter' ? 'primary' : node.type === 'plot' ? 'secondary' : 'info'
                  })] border-opacity-30 transition-all duration-300`}
                  style={{ transform: 'translateY(-50%)' }}
                ></div>
                {renderNodes(node.children, level + 1)}
              </div>
            )}
          </React.Fragment>
        );
      });
    } catch (error) {
      console.error('renderNodes: 渲染节点时出错:', error);
      return null;
    }
  };

  return (
    <>
      <div className="outline-tree overflow-auto p-2">
        {!outline || !outline.nodes || outline.nodes.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-10 text-gray-500 bg-white rounded-xl border border-dashed border-gray-300 shadow-sm">
          <div className="w-20 h-20 mb-4 bg-gradient-to-br from-[var(--outline-primary-light)] to-[var(--outline-primary)] rounded-full flex items-center justify-center text-white shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-medium text-gray-800 mb-2">还没有大纲内容</h3>
          <p className="text-center mb-6 text-gray-600 max-w-md">
            大纲是规划和组织作品结构的工具。添加章节、场景和笔记，构建作品的骨架，帮助你更清晰地规划创作过程。
          </p>
          <div className="flex space-x-4">
            <button
              className="px-5 py-2.5 bg-gradient-to-r from-[var(--outline-primary-light)] to-[var(--outline-primary)] text-white rounded-lg hover:shadow-lg transition-all duration-300 flex items-center shadow-md hover:scale-105 font-medium"
              onClick={() => handleAddChild('root')}
              style={{
                transform: 'translateZ(0)' // 启用GPU加速
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              添加章节
            </button>
            <button
              className="px-5 py-2.5 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:shadow-md transition-all duration-300 flex items-center font-medium text-gray-700"
              onClick={() => {
                // 导入模板大纲的逻辑
                console.log('导入模板大纲');
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
              导入模板
            </button>
          </div>
        </div>
      ) : (
        <div className="outline-nodes-container space-y-1">
          {renderNodes(outline.nodes, 0)}
        </div>
      )}
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        isOpen={deleteConfirmOpen}
        nodeData={nodeToDelete}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        showRecoveryHint={true}
      />
    </>
  );
};
