"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { PhaseType } from '../../../types/ai-persona';

// 定义ChatMessage接口
interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

// 定义AIPersona接口
interface AIPersona {
  id?: string;
  name?: string;
  [key: string]: any;
}
import { PrefixOption } from '../../../services/ai/PrefixMessageAIService';
import AIGeneratedPrefixPanel from './AIGeneratedPrefixPanel';
import { SavedPrefixTemplatesPanel } from './SavedPrefixTemplatesPanel';
import { AIGeneratedPrefixStorageService } from '../../../services/ai/AIGeneratedPrefixStorageService';
import BannedWordsPanel from './BannedWordsPanel';


interface MessagePrefixTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: 'ace_framework' | 'persona_enhancement' | 'format_specification' | 'context_enhancement' | 'custom';
  phase?: PhaseType;
}

interface MessagePrefixSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  phase: PhaseType;
  currentPrefixes: string[];
  onPrefixesChange: (prefixes: string[]) => void;
  persona?: AIPersona | null;
  content?: string;
  history?: ChatMessage[];
  userInput?: string;
}

// 预设的前置消息模板
const PREFIX_TEMPLATES: MessagePrefixTemplate[] = [
  {
    id: 'ace-framework-intro',
    name: 'ACE框架 - 导语强化',
    description: '基于ACE框架的导语创作指导',
    content: `1. [ACE框架导语要求1] = [开头必须有强烈的冲击力和悬念]
2. [ACE框架导语要求2] = [在50-150字内建立有效的戏剧冲突]
3. [ACE框架导语要求3] = [运用反差对比或关键疑问抓住读者]
4. [ACE框架导语要求4] = [避免平铺直叙，直接从冲突点开始]`,
    category: 'ace_framework',
    phase: 'intro'
  },
  {
    id: 'ace-framework-setup',
    name: 'ACE框架 - 铺垫期强化',
    description: '基于ACE框架的铺垫期创作指导',
    content: `1. [ACE框架铺垫期要求1] = [主角必须受到明显的羞辱、嘲讽或贬低]
2. [ACE框架铺垫期要求2] = [每段都要有实质性的情绪拉升]
3. [ACE框架铺垫期要求3] = [为读者积累愤怒和不满情绪]
4. [ACE框架铺垫期要求4] = [设置明确的反击动机和期待]`,
    category: 'ace_framework',
    phase: 'setup'
  },
  {
    id: 'persona-professional',
    name: '人设强化 - 专业性',
    description: '强调AI助手的专业性和权威性',
    content: `1. [专业要求1] = [请以资深创作导师的身份回复]
2. [专业要求2] = [提供具体可操作的修改建议]
3. [专业要求3] = [分析当前内容的优缺点]
4. [专业要求4] = [给出明确的改进方向]`,
    category: 'persona_enhancement'
  },
  {
    id: 'format-structured',
    name: '格式规范 - 结构化回复',
    description: '要求AI以结构化的方式回复',
    content: `1. [回复格式要求1] = [请按以下结构回复：【现状分析】- 分析当前内容效果]
2. [回复格式要求2] = [【具体建议】- 提供3-5个具体修改建议]
3. [回复格式要求3] = [【示例展示】- 给出修改前后的对比示例]
4. [回复格式要求4] = [【下一步】- 建议下一步的创作方向]`,
    category: 'format_specification'
  },
  {
    id: 'context-word-count',
    name: '上下文增强 - 字数感知',
    description: '让AI更好地感知当前创作进度',
    content: `1. [当前创作状态1] = [请特别关注当前字数是否符合阶段要求]
2. [当前创作状态2] = [分析节奏是否合适当前阶段]
3. [当前创作状态3] = [考虑是否需要为下一阶段做准备]`,
    category: 'context_enhancement'
  },
  {
    id: 'context-reader-emotion',
    name: '上下文增强 - 读者情绪',
    description: '强调对读者情绪的关注',
    content: `1. [读者情绪考量1] = [请分析当前内容对读者情绪的影响]
2. [读者情绪考量2] = [评估读者的期待和满足感]
3. [读者情绪考量3] = [建议如何更好地调动读者情绪]`,
    category: 'context_enhancement'
  }
];

const MessagePrefixSettings: React.FC<MessagePrefixSettingsProps> = ({
  isOpen,
  onClose,
  phase,
  currentPrefixes,
  onPrefixesChange,
  persona = null,
  content = '',
  history = [],
  userInput = ''
}) => {
  const [selectedPrefixes, setSelectedPrefixes] = useState<string[]>(currentPrefixes);
  const [customPrefix, setCustomPrefix] = useState('');
  const [activeTab, setActiveTab] = useState<'templates' | 'custom' | 'ai_generated' | 'saved_templates' | 'worldbook_import' | 'banned_words'>('templates');
  const [aiGeneratedOptions, setAiGeneratedOptions] = useState<PrefixOption[]>([]);
  const [showSavedTemplatesPanel, setShowSavedTemplatesPanel] = useState(false);
  const [storageService] = useState(() => AIGeneratedPrefixStorageService.getInstance());
  const [bannedWordsText, setBannedWordsText] = useState<string>('');

  useEffect(() => {
    setSelectedPrefixes(currentPrefixes);
  }, [currentPrefixes]);

  if (!isOpen) return null;

  // 过滤适合当前阶段的模板
  const relevantTemplates = PREFIX_TEMPLATES.filter(template =>
    !template.phase || template.phase === phase
  );

  // 按类别分组模板
  const templatesByCategory = relevantTemplates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, MessagePrefixTemplate[]>);

  const categoryNames = {
    ace_framework: 'ACE框架',
    persona_enhancement: '人设强化',
    format_specification: '格式规范',
    context_enhancement: '上下文增强',
    custom: '自定义'
  };

  const handleTogglePrefix = (content: string) => {
    const newPrefixes = selectedPrefixes.includes(content)
      ? selectedPrefixes.filter(p => p !== content)
      : [...selectedPrefixes, content];
    setSelectedPrefixes(newPrefixes);
  };

  const handleAddCustomPrefix = () => {
    if (customPrefix.trim() && !selectedPrefixes.includes(customPrefix.trim())) {
      const newPrefixes = [...selectedPrefixes, customPrefix.trim()];
      setSelectedPrefixes(newPrefixes);
      setCustomPrefix('');
    }
  };

  const handleSave = () => {
    onPrefixesChange(selectedPrefixes);
    onClose();
  };

  const handleRemovePrefix = (prefix: string) => {
    const newPrefixes = selectedPrefixes.filter(p => p !== prefix);
    setSelectedPrefixes(newPrefixes);
  };

  // 处理AI生成的选项
  const handleAIOptionsGenerated = (options: PrefixOption[]) => {
    setAiGeneratedOptions(options);
  };

  const handleAIOptionsSelected = (options: PrefixOption[]) => {
    const optionContents = options.map(option => option.content);
    // 过滤掉已存在的前置消息，避免重复添加
    const uniqueNewContents = optionContents.filter(content =>
      !selectedPrefixes.includes(content)
    );

    if (uniqueNewContents.length > 0) {
      const newPrefixes = [...selectedPrefixes, ...uniqueNewContents];
      setSelectedPrefixes(newPrefixes);
      console.log(`✅ 添加了 ${uniqueNewContents.length} 个新的前置消息，跳过了 ${optionContents.length - uniqueNewContents.length} 个重复项`);
    } else {
      console.log('⚠️ 所有选择的前置消息都已存在，未添加任何新项');
    }
  };

  // 处理从保存的模板中选择
  const handleSavedTemplatesSelected = (prefixes: string[]) => {
    // 过滤掉已存在的前置消息，避免重复添加
    const uniqueNewContents = prefixes.filter(content =>
      !selectedPrefixes.includes(content)
    );

    if (uniqueNewContents.length > 0) {
      const newPrefixes = [...selectedPrefixes, ...uniqueNewContents];
      setSelectedPrefixes(newPrefixes);
      console.log(`✅ 从模板库添加了 ${uniqueNewContents.length} 个前置消息`);
    } else {
      console.log('⚠️ 所有选择的模板都已存在，未添加任何新项');
    }
    setShowSavedTemplatesPanel(false);
  };

  // 保存当前选择的前置消息到模板库
  const handleSaveSelectedPrefixes = () => {
    if (selectedPrefixes.length === 0) {
      alert('⚠️ 没有选择任何前置消息');
      return;
    }

    // 将选择的前置消息转换为PrefixOption格式
    const prefixOptions: PrefixOption[] = selectedPrefixes.map((content, index) => ({
      id: `selected_${Date.now()}_${index}`,
      content: content,
      category: 'context' as const, // 默认为上下文增强类别
      description: '用户自定义的前置消息组合',
      useCase: `适用于${phase || '通用'}阶段的创作指导`,
      reasoning: '基于用户实际使用经验选择的前置消息组合',
      confidence: 0.8,
      tags: ['用户选择', '自定义组合']
    }));

    const savedPrefixes = storageService.savePrefixOptions(prefixOptions, phase);
    if (savedPrefixes.length > 0) {
      alert(`✅ 成功保存了 ${savedPrefixes.length} 个前置消息到模板库！`);
    } else {
      alert('⚠️ 所有前置消息都已存在于模板库中');
    }
  };

  // 处理禁用词汇表变化
  const handleBannedWordsChange = (newBannedWordsText: string) => {
    setBannedWordsText(newBannedWordsText);

    // 移除现有的禁用词汇表前置消息
    const filteredPrefixes = selectedPrefixes.filter(prefix =>
      !prefix.startsWith('[禁用词汇表]')
    );

    // 如果有新的禁用词汇表，添加到前置消息中
    if (newBannedWordsText.trim()) {
      const newPrefixes = [...filteredPrefixes, newBannedWordsText];
      setSelectedPrefixes(newPrefixes);
    } else {
      setSelectedPrefixes(filteredPrefixes);
    }
  };



  // 使用Portal渲染到document.body，避免父级容器限制
  const modalContent = (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col max-h-[90vh]">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">消息前置设置</h2>
          <button
            onClick={onClose}
            className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 说明文字 */}
        <div className="p-4 bg-blue-50 border-b border-gray-200">
          <p className="text-sm text-blue-800">
            💡 <strong>前置消息原理：</strong>AI对最后一条消息的权重最高，前置消息会在后台自动添加到你的问题前面，
            但不会在聊天界面中显示。这样既保持界面简洁，又能让AI获得更准确的指导要求。
          </p>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* 左侧：模板选择 */}
          <div className="w-2/3 border-r border-gray-200 flex flex-col">
            {/* 标签页 */}
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setActiveTab('ai_generated')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'ai_generated'
                    ? 'text-purple-600 border-b-2 border-purple-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                🤖 AI智能生成
              </button>
              <button
                onClick={() => setShowSavedTemplatesPanel(true)}
                className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                📚 保存的模板
              </button>
              <button
                onClick={() => setActiveTab('templates')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'templates'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                预设模板
              </button>
              <button
                onClick={() => setActiveTab('banned_words')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'banned_words'
                    ? 'text-red-600 border-b-2 border-red-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                🚫 禁用词汇表
              </button>
              <button
                onClick={() => setActiveTab('custom')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'custom'
                    ? 'text-green-600 border-b-2 border-green-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                自定义
              </button>
            </div>

            {/* 内容区域 */}
            <div className="flex-1 overflow-y-auto p-4">
              {activeTab === 'ai_generated' ? (
                <AIGeneratedPrefixPanel
                  phase={phase}
                  persona={persona}
                  content={content}
                  history={history}
                  userInput={userInput}
                  onOptionsGenerated={handleAIOptionsGenerated}
                  onOptionsSelected={handleAIOptionsSelected}
                />
              ) : activeTab === 'templates' ? (
                <div className="space-y-4">
                  {Object.entries(templatesByCategory).map(([category, templates]) => (
                    <div key={category}>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">
                        {categoryNames[category as keyof typeof categoryNames]}
                      </h3>
                      <div className="space-y-2">
                        {templates.map((template) => (
                          <div
                            key={template.id}
                            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                              selectedPrefixes.includes(template.content)
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => handleTogglePrefix(template.content)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-800">{template.name}</h4>
                                <p className="text-xs text-gray-600 mt-1">{template.description}</p>
                              </div>
                              <div className="ml-2">
                                {selectedPrefixes.includes(template.content) ? (
                                  <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                ) : (
                                  <div className="w-4 h-4 border border-gray-300 rounded"></div>
                                )}
                              </div>
                            </div>
                            <div className="mt-2 text-xs text-gray-700 bg-gray-50 p-2 rounded">
                              {template.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : activeTab === 'banned_words' ? (
                <BannedWordsPanel
                  onBannedWordsChange={handleBannedWordsChange}
                  initialBannedWords={bannedWordsText}
                  currentChatMessages={history}
                />
              ) : (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">添加自定义前置消息</h3>
                  <div className="space-y-3">
                    <textarea
                      value={customPrefix}
                      onChange={(e) => setCustomPrefix(e.target.value)}
                      placeholder="输入自定义的前置消息内容..."
                      className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none text-sm"
                      rows={6}
                    />
                    <button
                      onClick={handleAddCustomPrefix}
                      disabled={!customPrefix.trim()}
                      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
                    >
                      添加到前置列表
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：已选择的前置消息 */}
          <div className="w-1/3 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-700">
                  已选择的前置消息 ({selectedPrefixes.length})
                </h3>
                {selectedPrefixes.length > 0 && (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSaveSelectedPrefixes}
                      className="px-2 py-1 text-xs text-purple-600 border border-purple-300 rounded hover:bg-purple-50 transition-colors"
                      title="保存当前选择到模板库"
                    >
                      💾 保存到模板
                    </button>
                    <button
                      onClick={() => setSelectedPrefixes([])}
                      className="px-2 py-1 text-xs text-gray-500 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                      title="清空所有选择"
                    >
                      🗑️ 清空
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              {selectedPrefixes.length === 0 ? (
                <div className="text-center text-gray-500 text-sm mt-8">
                  <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  暂无选择的前置消息
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedPrefixes.map((prefix, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 text-xs text-gray-700">
                          {prefix}
                        </div>
                        <button
                          onClick={() => handleRemovePrefix(prefix)}
                          className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
                          title="移除"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            前置消息会在后台自动添加到你的问题前面，不会在聊天界面显示，但能有效引导AI的回复方向
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // 使用Portal渲染到document.body
  return typeof window !== 'undefined' ? (
    <>
      {createPortal(modalContent, document.body)}
      {showSavedTemplatesPanel && (
        createPortal(
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000] p-4">
            <SavedPrefixTemplatesPanel
              currentPhase={phase}
              onPrefixesSelected={handleSavedTemplatesSelected}
              onClose={() => setShowSavedTemplatesPanel(false)}
            />
          </div>,
          document.body
        )
      )}

    </>
  ) : null;
};

export default MessagePrefixSettings;
