/**
 * AI助手功能测试文件
 * 用于验证新增的@章节选择和关联功能是否正常工作
 */

import { aiAssistantDataService } from './services/aiAssistantDataService';
import { aiAssistantContextRepository } from './lib/db/repositories/aiAssistantContextRepository';
import { aiAssistantTemplateRepository } from './lib/db/repositories/aiAssistantTemplateRepository';
import { AIAssistantContextType } from './lib/db/dexie';

/**
 * 测试AI助手数据服务
 */
export async function testAIAssistantDataService() {
  console.log('🧪 开始测试AI助手数据服务...');
  
  const testBookId = 'test-book-123';
  
  try {
    // 测试搜索功能
    console.log('📝 测试搜索功能...');
    const searchResults = await aiAssistantDataService.searchMentionItems(
      testBookId,
      '测试',
      undefined,
      10
    );
    console.log('搜索结果:', searchResults);
    
    // 测试记录使用统计
    console.log('📊 测试记录使用统计...');
    await aiAssistantDataService.recordUsage(
      testBookId,
      AIAssistantContextType.CHARACTER,
      'test-character-1',
      '测试人物'
    );
    console.log('使用统计记录成功');
    
    // 测试获取最近使用的内容
    console.log('🕒 测试获取最近使用的内容...');
    const recentItems = await aiAssistantDataService.getRecentlyUsedItems(testBookId, 5);
    console.log('最近使用的内容:', recentItems);
    
    // 测试获取最常用的内容
    console.log('🔥 测试获取最常用的内容...');
    const popularItems = await aiAssistantDataService.getMostUsedItems(testBookId, 5);
    console.log('最常用的内容:', popularItems);
    
    console.log('✅ AI助手数据服务测试完成');
    return true;
  } catch (error) {
    console.error('❌ AI助手数据服务测试失败:', error);
    return false;
  }
}

/**
 * 测试AI助手关联上下文仓库
 */
export async function testAIAssistantContextRepository() {
  console.log('🧪 开始测试AI助手关联上下文仓库...');
  
  const testBookId = 'test-book-123';
  
  try {
    // 测试保存关联上下文
    console.log('💾 测试保存关联上下文...');
    const context = await aiAssistantContextRepository.saveContext({
      bookId: testBookId,
      contextType: AIAssistantContextType.CHARACTER,
      contextId: 'test-character-1',
      contextTitle: '测试人物',
      isActive: true,
      priority: 1
    });
    console.log('保存的关联上下文:', context);
    
    // 测试获取活跃的关联上下文
    console.log('🔍 测试获取活跃的关联上下文...');
    const activeContexts = await aiAssistantContextRepository.getActiveContexts(testBookId);
    console.log('活跃的关联上下文:', activeContexts);
    
    // 测试搜索关联上下文
    console.log('🔎 测试搜索关联上下文...');
    const searchResults = await aiAssistantContextRepository.searchContexts(
      testBookId,
      '测试',
      AIAssistantContextType.CHARACTER
    );
    console.log('搜索结果:', searchResults);
    
    // 测试增加使用次数
    console.log('📈 测试增加使用次数...');
    await aiAssistantContextRepository.incrementUsage(context.id!);
    console.log('使用次数增加成功');
    
    // 测试获取最常用的关联上下文
    console.log('🏆 测试获取最常用的关联上下文...');
    const mostUsed = await aiAssistantContextRepository.getMostUsedContexts(testBookId, 5);
    console.log('最常用的关联上下文:', mostUsed);
    
    console.log('✅ AI助手关联上下文仓库测试完成');
    return true;
  } catch (error) {
    console.error('❌ AI助手关联上下文仓库测试失败:', error);
    return false;
  }
}

/**
 * 测试AI助手模板仓库
 */
export async function testAIAssistantTemplateRepository() {
  console.log('🧪 开始测试AI助手模板仓库...');
  
  const testBookId = 'test-book-123';
  
  try {
    // 测试创建模板
    console.log('🆕 测试创建模板...');
    const template = await aiAssistantTemplateRepository.createTemplate({
      bookId: testBookId,
      name: '测试模板',
      description: '这是一个测试模板',
      contextIds: ['test-context-1', 'test-context-2'],
      isDefault: false,
      usageCount: 0
    });
    console.log('创建的模板:', template);
    
    // 测试获取模板
    console.log('📋 测试获取模板...');
    const templates = await aiAssistantTemplateRepository.getTemplates(testBookId);
    console.log('所有模板:', templates);
    
    // 测试增加模板使用次数
    console.log('📊 测试增加模板使用次数...');
    await aiAssistantTemplateRepository.incrementUsage(template.id!);
    console.log('模板使用次数增加成功');
    
    // 测试搜索模板
    console.log('🔍 测试搜索模板...');
    const searchResults = await aiAssistantTemplateRepository.searchTemplates(testBookId, '测试');
    console.log('搜索结果:', searchResults);
    
    // 测试设置默认模板
    console.log('⭐ 测试设置默认模板...');
    await aiAssistantTemplateRepository.setAsDefault(template.id!, testBookId);
    console.log('设置默认模板成功');
    
    // 测试获取默认模板
    console.log('🎯 测试获取默认模板...');
    const defaultTemplates = await aiAssistantTemplateRepository.getDefaultTemplates(testBookId);
    console.log('默认模板:', defaultTemplates);
    
    console.log('✅ AI助手模板仓库测试完成');
    return true;
  } catch (error) {
    console.error('❌ AI助手模板仓库测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行AI助手功能测试套件...');
  
  const results = {
    dataService: false,
    contextRepository: false,
    templateRepository: false
  };
  
  // 运行数据服务测试
  results.dataService = await testAIAssistantDataService();
  
  // 运行关联上下文仓库测试
  results.contextRepository = await testAIAssistantContextRepository();
  
  // 运行模板仓库测试
  results.templateRepository = await testAIAssistantTemplateRepository();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('- 数据服务:', results.dataService ? '✅ 通过' : '❌ 失败');
  console.log('- 关联上下文仓库:', results.contextRepository ? '✅ 通过' : '❌ 失败');
  console.log('- 模板仓库:', results.templateRepository ? '✅ 通过' : '❌ 失败');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 总体结果:', allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败');
  
  return results;
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以通过控制台调用测试
  (window as any).testAIAssistantFeatures = {
    runAllTests,
    testAIAssistantDataService,
    testAIAssistantContextRepository,
    testAIAssistantTemplateRepository
  };
  
  console.log('🔧 AI助手功能测试工具已加载到 window.testAIAssistantFeatures');
  console.log('💡 使用方法: window.testAIAssistantFeatures.runAllTests()');
}
