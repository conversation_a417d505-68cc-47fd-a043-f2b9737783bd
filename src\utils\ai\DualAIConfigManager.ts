"use client";

import {
  DualAIConfig,
  getDefaultDualAIConfig,
  DEFAULT_SYSTEM_PROMPTS,
  AIConfigState,
  ConnectionStatus
} from '@/types/DualAIConfig';

/**
 * 双AI配置管理器
 */
export class DualAIConfigManager {
  private static readonly STORAGE_KEY = 'dual-ai-config';
  private static readonly STATE_KEY = 'dual-ai-state';
  private static readonly ENCRYPTION_KEY = 'ai-novel-platform-key';

  /**
   * 保存配置
   */
  static save(config: DualAIConfig): void {
    try {
      // 添加时间戳
      const configWithTimestamp = {
        ...config,
        updatedAt: new Date().toISOString()
      };

      // 加密敏感信息
      const encryptedConfig = this.encrypt(configWithTimestamp);

      // 保存到localStorage
      localStorage.setItem(this.STORAGE_KEY, encryptedConfig);

      console.log('✅ 双AI配置已保存');
    } catch (error) {
      console.error('❌ 保存配置失败:', error);
      throw new Error('配置保存失败');
    }
  }

  /**
   * 加载配置
   */
  static load(): DualAIConfig | null {
    try {
      const encrypted = localStorage.getItem(this.STORAGE_KEY);
      if (!encrypted) {
        console.log('📝 未找到保存的配置，创建并保存默认配置');
        const defaultConfig = this.getDefault();
        this.save(defaultConfig);
        return defaultConfig;
      }

      console.log('🔍 DualAIConfigManager: 开始解密配置...');
      const config = this.decrypt(encrypted);

      // 验证配置完整性
      if (this.validateConfig(config)) {
        // 🔥 关键修改：每次加载时都从API设置获取最新的共享配置
        config.shared = this.getSharedConfigFromAPISettings();
        console.log('✅ 双AI配置加载成功，已同步API设置');
        return config;
      } else {
        console.warn('⚠️ 配置验证失败，创建新的默认配置');
        // 清除损坏的配置数据
        localStorage.removeItem(this.STORAGE_KEY);
        const defaultConfig = this.getDefault();
        this.save(defaultConfig);
        return defaultConfig;
      }
    } catch (error) {
      console.error('❌ 配置加载失败:', error);
      console.log('🧹 清除损坏的配置数据，创建新的默认配置');

      // 清除损坏的配置数据，避免重复出现解密错误
      try {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.STATE_KEY);
        console.log('✅ 已清除损坏的配置数据');

        // 创建并保存默认配置
        const defaultConfig = this.getDefault();
        this.save(defaultConfig);
        console.log('✅ 已创建新的默认配置');
        return defaultConfig;
      } catch (clearError) {
        console.error('❌ 清除配置数据失败:', clearError);
        return null;
      }
    }
  }

  /**
   * 从旧配置迁移
   */
  static migrate(oldConfig: any): DualAIConfig {
    console.log('🔄 开始配置迁移...');

    const migratedConfig: DualAIConfig = {
      mode: 'single', // 默认单AI模式
      shared: this.getSharedConfigFromAPISettings(), // 直接从API设置获取
      models: {
        outline: {
          url: oldConfig.url || oldConfig.apiEndpoint || '',
          apiKey: oldConfig.apiKey || '',
          modelName: oldConfig.model || oldConfig.modelName || 'gemini-2.5-pro-exp-03-25',
          systemPrompt: DEFAULT_SYSTEM_PROMPTS.outline,
          enabled: true,
        },
        dialogue: {
          url: oldConfig.url || oldConfig.apiEndpoint || '',
          apiKey: oldConfig.apiKey || '',
          modelName: oldConfig.model || oldConfig.modelName || 'gemini-2.5-pro-exp-03-25',
          systemPrompt: DEFAULT_SYSTEM_PROMPTS.dialogue,
          enabled: true,
        },
      },
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    console.log('✅ 配置迁移完成');
    return migratedConfig;
  }

  /**
   * 从API设置获取共享配置
   */
  static getSharedConfigFromAPISettings(): any {
    try {
      // 尝试从localStorage获取API设置
      const apiSettings = localStorage.getItem('api_settings');
      if (apiSettings) {
        const settings = JSON.parse(apiSettings);
        return {
          temperature: 0.7, // 可以后续从API设置获取
          maxTokens: settings.maxTokens || 80000,
          topP: 1,
          topK: settings.topK || 40, // 新增topK参数，默认值40
          frequencyPenalty: 0,
          presencePenalty: 0,
          streaming: settings.streamingEnabled ?? true,
        };
      }
    } catch (error) {
      console.warn('无法从API设置获取配置，使用默认值', error);
    }

    // 回退到默认值
    return {
      temperature: 0.7,
      maxTokens: 80000,
      topP: 1,
      topK: 40, // 新增topK参数，默认值40
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    };
  }

  /**
   * 获取默认配置
   */
  static getDefault(): DualAIConfig {
    return getDefaultDualAIConfig(); // 直接使用动态函数
  }

  /**
   * 保存状态
   */
  static saveState(state: AIConfigState): void {
    try {
      const stateWithTimestamp = {
        ...state,
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(this.STATE_KEY, JSON.stringify(stateWithTimestamp));
    } catch (error) {
      console.error('❌ 保存状态失败:', error);
    }
  }

  /**
   * 加载状态
   */
  static loadState(): AIConfigState | null {
    try {
      const stateJson = localStorage.getItem(this.STATE_KEY);
      if (!stateJson) return null;

      return JSON.parse(stateJson);
    } catch (error) {
      console.error('❌ 加载状态失败:', error);
      return null;
    }
  }

  /**
   * 创建默认状态
   */
  static createDefaultState(config: DualAIConfig): AIConfigState {
    return {
      currentMode: config.mode,
      activeConfig: config,
      connectionStatus: {
        outline: 'disconnected',
        dialogue: 'disconnected'
      },
      usage: {
        outline: { calls: 0, tokens: 0, cost: 0 },
        dialogue: { calls: 0, tokens: 0, cost: 0 }
      },
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 更新连接状态
   */
  static updateConnectionStatus(
    modelType: 'outline' | 'dialogue',
    status: ConnectionStatus
  ): void {
    const state = this.loadState();
    if (state) {
      state.connectionStatus[modelType] = status;
      this.saveState(state);
    }
  }

  /**
   * 导出配置
   */
  static export(): string {
    const config = this.load();
    if (!config) {
      throw new Error('没有可导出的配置');
    }

    // 移除敏感信息
    const exportConfig = {
      ...config,
      models: {
        outline: {
          ...config.models.outline,
          apiKey: '***' // 隐藏API密钥
        },
        dialogue: {
          ...config.models.dialogue,
          apiKey: '***' // 隐藏API密钥
        }
      }
    };

    return JSON.stringify(exportConfig, null, 2);
  }

  /**
   * 导入配置
   */
  static import(configJson: string): DualAIConfig {
    try {
      const config = JSON.parse(configJson);

      if (!this.validateConfig(config)) {
        throw new Error('配置格式无效');
      }

      // 如果API密钥被隐藏，需要用户重新输入
      if (config.models.outline.apiKey === '***') {
        config.models.outline.apiKey = '';
      }
      if (config.models.dialogue.apiKey === '***') {
        config.models.dialogue.apiKey = '';
      }

      return config;
    } catch (error) {
      throw new Error('配置导入失败: ' + (error as Error).message);
    }
  }

  /**
   * 验证配置
   */
  private static validateConfig(config: any): config is DualAIConfig {
    if (!config || typeof config !== 'object') return false;

    // 检查必需字段
    if (!config.mode || !config.shared || !config.models) return false;
    if (!config.models.outline || !config.models.dialogue) return false;

    // 检查共享配置
    const shared = config.shared;
    if (typeof shared.temperature !== 'number' ||
        typeof shared.maxTokens !== 'number') return false;

    // 检查模型配置
    const outline = config.models.outline;
    const dialogue = config.models.dialogue;

    if (!outline.modelName || !dialogue.modelName) return false;

    return true;
  }

  /**
   * 简单加密（实际项目中应使用更安全的加密方法）
   */
  private static encrypt(config: DualAIConfig): string {
    try {
      const jsonString = JSON.stringify(config);
      // 使用自定义编码替代btoa，避免中文字符问题
      return this.customBase64Encode(jsonString);
    } catch (error) {
      throw new Error('配置加密失败');
    }
  }

  /**
   * 简单解密
   */
  private static decrypt(encrypted: string): DualAIConfig {
    try {
      console.log('🔍 DualAIConfigManager: 开始解密配置数据...');
      const jsonString = this.customBase64Decode(encrypted);
      const config = JSON.parse(jsonString);
      console.log('✅ DualAIConfigManager: 配置解密成功');
      return config;
    } catch (error) {
      console.error('❌ DualAIConfigManager: 配置解密失败，可能是数据损坏:', error);
      console.log('🔍 加密数据预览:', encrypted.substring(0, 100) + '...');

      // 不抛出异常，而是抛出一个特殊的错误，让上层处理
      throw new Error('配置解密失败: ' + (error as Error).message);
    }
  }

  /**
   * 自定义Base64编码，支持Unicode字符
   */
  private static customBase64Encode(str: string): string {
    try {
      // 先转换为UTF-8字节序列，再进行Base64编码
      const utf8Bytes = new TextEncoder().encode(str);
      const binaryString = Array.from(utf8Bytes, byte => String.fromCharCode(byte)).join('');
      return btoa(binaryString);
    } catch (error) {
      // 如果btoa仍然失败，使用简单的哈希编码
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(36);
    }
  }

  /**
   * 自定义Base64解码，支持Unicode字符
   */
  private static customBase64Decode(encoded: string): string {
    try {
      console.log('🔍 DualAIConfigManager: 开始Base64解码...');
      const binaryString = atob(encoded);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const decoded = new TextDecoder().decode(bytes);
      console.log('✅ DualAIConfigManager: Base64解码成功');
      return decoded;
    } catch (error) {
      console.error('❌ DualAIConfigManager: Base64解码失败:', error);
      console.log('🔍 编码数据格式检查:', {
        length: encoded.length,
        isValidBase64: /^[A-Za-z0-9+/]*={0,2}$/.test(encoded),
        preview: encoded.substring(0, 50) + '...'
      });

      // 抛出错误而不是返回默认配置，让上层决定如何处理
      throw new Error('Base64解码失败: ' + (error as Error).message);
    }
  }

  /**
   * 清除所有配置
   */
  static clear(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.STATE_KEY);
    console.log('🗑️ 双AI配置已清除');
  }

  /**
   * 检查配置是否存在
   */
  static exists(): boolean {
    return !!localStorage.getItem(this.STORAGE_KEY);
  }

  /**
   * 获取配置摘要信息
   */
  static getSummary(): {
    hasConfig: boolean;
    mode?: 'single' | 'dual';
    outlineConfigured?: boolean;
    dialogueConfigured?: boolean;
    lastUpdated?: string;
  } {
    const config = this.load();

    if (!config) {
      return { hasConfig: false };
    }

    return {
      hasConfig: true,
      mode: config.mode,
      outlineConfigured: !!(config.models.outline.url && config.models.outline.apiKey),
      dialogueConfigured: !!(config.models.dialogue.url && config.models.dialogue.apiKey),
      lastUpdated: config.updatedAt
    };
  }
}

/**
 * 配置管理器单例
 */
export const dualAIConfigManager = DualAIConfigManager;
