import React from 'react';

interface PhaseStrategyPanelProps {
  phaseStrategy: any;
  isLoading: boolean;
  onConfirm: () => void;
  onRegenerate: () => void;
}

export const PhaseStrategyPanel: React.FC<PhaseStrategyPanelProps> = ({
  phaseStrategy,
  isLoading,
  onConfirm,
  onRegenerate
}) => {
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-gray-600">AI正在策划各阶段效果...</p>
          <p className="text-sm text-gray-500 mt-2">分析"藏一半说一半"的节奏布局</p>
        </div>
      </div>
    );
  }

  if (!phaseStrategy) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p>暂无阶段策划数据</p>
        </div>
      </div>
    );
  }

  const phases = [
    { key: 'setupPhase', name: '铺垫期', color: 'bg-blue-50 border-blue-200', icon: '🌱' },
    { key: 'compressionPhase', name: '挤压期', color: 'bg-orange-50 border-orange-200', icon: '⚡' },
    { key: 'climaxPhase', name: '高潮期', color: 'bg-red-50 border-red-200', icon: '💥' },
    { key: 'resolutionPhase', name: '结局期', color: 'bg-green-50 border-green-200', icon: '🎯' }
  ];

  return (
    <div className="flex-1 flex flex-col">
      {/* 标题 */}
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">阶段效果策划</h3>
        <p className="text-gray-600">AI已为您制定了四个阶段的具体效果策划，包括"藏一半说一半"的节奏安排</p>
      </div>

      {/* 阶段策划列表 */}
      <div className="flex-1 overflow-y-auto space-y-4">
        {phases.map((phase) => {
          const phaseData = phaseStrategy[phase.key];
          if (!phaseData) return null;

          return (
            <div key={phase.key} className={`border rounded-lg p-4 ${phase.color}`}>
              <div className="flex items-center mb-3">
                <span className="text-2xl mr-2">{phase.icon}</span>
                <h4 className="text-lg font-semibold text-gray-800">{phase.name}</h4>
              </div>

              <div className="space-y-3 text-sm">
                {/* 阶段目标 */}
                <div>
                  <span className="font-medium text-gray-700">🎯 阶段目标：</span>
                  <p className="text-gray-600 mt-1">{phaseData.phaseGoal}</p>
                </div>

                {/* 说一半策略 */}
                <div>
                  <span className="font-medium text-gray-700">📢 说一半策略：</span>
                  <p className="text-gray-600 mt-1">{phaseData.revealStrategy}</p>
                </div>

                {/* 藏一半策略 */}
                <div>
                  <span className="font-medium text-gray-700">🤫 藏一半策略：</span>
                  <p className="text-gray-600 mt-1">{phaseData.hideStrategy}</p>
                </div>

                {/* 剧情密度战术 */}
                {phaseData.plotDensityTactics && phaseData.plotDensityTactics.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700">🔥 剧情密度战术：</span>
                    <ul className="text-gray-600 mt-1 ml-4">
                      {phaseData.plotDensityTactics.map((tactic: string, index: number) => (
                        <li key={index} className="list-disc">{tactic}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* 目标情绪 */}
                <div>
                  <span className="font-medium text-gray-700">💭 目标情绪：</span>
                  <p className="text-gray-600 mt-1">{phaseData.emotionalTarget}</p>
                </div>

                {/* 关键技巧 */}
                {phaseData.keyTechniques && phaseData.keyTechniques.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700">⚡ 关键技巧：</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {phaseData.keyTechniques.map((technique: string, index: number) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-white bg-opacity-60 rounded text-xs text-gray-700"
                        >
                          {technique}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 付费卡点（仅挤压期显示） */}
                {phase.key === 'compressionPhase' && phaseData.paymentHook && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center mb-2">
                      <span className="text-lg mr-2">💰</span>
                      <span className="font-medium text-red-700">付费卡点设计</span>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-red-600">📍 位置：</span>
                        <span className="text-red-700">{phaseData.paymentHook.position}</span>
                      </div>
                      <div>
                        <span className="font-medium text-red-600">🎣 钩子策略：</span>
                        <p className="text-red-700 mt-1">{phaseData.paymentHook.hookStrategy}</p>
                      </div>
                      <div>
                        <span className="font-medium text-red-600">⛰️ 悬崖设计：</span>
                        <p className="text-red-700 mt-1">{phaseData.paymentHook.cliffhangerDesign}</p>
                      </div>
                      <div>
                        <span className="font-medium text-red-600">🧠 心理操控：</span>
                        <p className="text-red-700 mt-1">{phaseData.paymentHook.readerPsychology}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-between items-center mt-6 pt-4 border-t">
        <button
          onClick={onRegenerate}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          disabled={isLoading}
        >
          🔄 重新策划
        </button>
        
        <button
          onClick={onConfirm}
          className="px-6 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors font-medium"
          disabled={isLoading}
        >
          确认策划，生成结构 →
        </button>
      </div>
    </div>
  );
};
