"use client";

import { FieldNameMappings } from '@/utils/field/FieldNameMappings';

/**
 * 字段识别器
 * 负责识别和标准化字段名称
 * 增强版：提供更宽容的字段匹配能力
 */
export class FieldRecognizer {
  private standardFields: Map<string, string>;
  private keywordMappings: Map<string, string>;
  private wordMappings: Map<string, string[]>; // 新增：单词到字段的映射

  constructor() {
    // 初始化标准字段映射
    this.standardFields = new Map<string, string>();
    this.keywordMappings = new Map<string, string>();
    this.wordMappings = new Map<string, string[]>();

    // 加载字段名称映射
    this.loadFieldMappings();
  }

  /**
   * 加载字段名称映射
   */
  private loadFieldMappings(): void {
    // 遍历FieldNameMappings，构建标准字段映射和关键词映射
    for (const [key, value] of Object.entries(FieldNameMappings)) {
      const normalizedKey = key.toLowerCase().trim();
      this.standardFields.set(normalizedKey, value);

      // 所有字段都添加到关键词映射中，增加匹配机会
      this.keywordMappings.set(normalizedKey, value);

      // 将字段拆分为单词，建立单词到字段的映射
      const words = this.splitIntoWords(normalizedKey);
      for (const word of words) {
        if (word.length > 2) { // 忽略太短的单词
          if (!this.wordMappings.has(word)) {
            this.wordMappings.set(word, []);
          }
          const fields = this.wordMappings.get(word);
          if (fields && !fields.includes(value)) {
            fields.push(value);
          }
        }
      }
    }
  }

  /**
   * 将字符串拆分为单词
   * @param str 要拆分的字符串
   * @returns 单词数组
   */
  private splitIntoWords(str: string): string[] {
    // 处理中文
    if (/[\u4e00-\u9fa5]/.test(str)) {
      // 中文按字符拆分
      return Array.from(str).filter(char => char.trim().length > 0);
    }

    // 英文按空格和常见分隔符拆分
    return str.split(/[\s_\-\/\.]+/).filter(word => word.length > 0);
  }

  /**
   * 识别字段名称
   * @param fieldName 原始字段名称
   * @returns 标准化后的字段名称
   */
  recognizeField(fieldName: string): string {
    // 1. 尝试精确匹配
    const exactResult = this.exactMatch(fieldName);
    if (exactResult) {
      return exactResult;
    }

    // 2. 尝试关键词匹配
    const keywordResult = this.keywordMatch(fieldName);
    if (keywordResult) {
      return keywordResult;
    }

    // 3. 尝试单词匹配（新增）
    const wordResult = this.wordMatch(fieldName);
    if (wordResult) {
      return wordResult;
    }

    // 4. 尝试模糊匹配
    const fuzzyResult = this.fuzzyMatch(fieldName);
    if (fuzzyResult) {
      return fuzzyResult;
    }

    // 5. 如果所有匹配都失败，返回原始字段名
    return fieldName;
  }

  /**
   * 精确匹配
   * @param fieldName 字段名称
   * @returns 匹配的标准字段名称
   */
  private exactMatch(fieldName: string): string | null {
    const normalizedName = fieldName.toLowerCase().trim();

    if (this.standardFields.has(normalizedName)) {
      return this.standardFields.get(normalizedName);
    }

    return null;
  }

  /**
   * 关键词匹配
   * @param fieldName 字段名称
   * @returns 匹配的标准字段名称
   */
  private keywordMatch(fieldName: string): string | null {
    const normalizedName = fieldName.toLowerCase().trim();

    // 优先匹配中文关键词
    const chineseKeys = Array.from(this.keywordMappings.keys()).filter(key => /[\u4e00-\u9fa5]/.test(key));
    for (const key of chineseKeys) {
      // 增强：支持部分匹配，只要包含关键词的80%以上的字符即可
      if (this.partialMatch(normalizedName, key, 0.8)) {
        return this.keywordMappings.get(key);
      }
    }

    // 然后匹配英文关键词
    const englishKeys = Array.from(this.keywordMappings.keys()).filter(key => !/[\u4e00-\u9fa5]/.test(key));
    for (const key of englishKeys) {
      // 增强：支持部分匹配
      if (this.partialMatch(normalizedName, key, 0.8)) {
        return this.keywordMappings.get(key);
      }
    }

    return null;
  }

  /**
   * 部分匹配检查
   * @param source 源字符串
   * @param target 目标字符串
   * @param threshold 匹配阈值（0-1）
   * @returns 是否匹配
   */
  private partialMatch(source: string, target: string, threshold: number): boolean {
    // 完全包含
    if (source.includes(target)) {
      return true;
    }

    // 计算有多少字符匹配
    let matchCount = 0;
    for (const char of target) {
      if (source.includes(char)) {
        matchCount++;
      }
    }

    // 计算匹配率
    const matchRate = matchCount / target.length;
    return matchRate >= threshold;
  }

  /**
   * 单词匹配（新增）
   * @param fieldName 字段名称
   * @returns 匹配的标准字段名称
   */
  private wordMatch(fieldName: string): string | null {
    const normalizedName = fieldName.toLowerCase().trim();
    const words = this.splitIntoWords(normalizedName);

    // 统计每个可能的字段匹配的单词数
    const fieldScores = new Map<string, number>();

    for (const word of words) {
      if (word.length > 2 && this.wordMappings.has(word)) {
        const fields = this.wordMappings.get(word);
        if (fields) {
          for (const field of fields) {
            const currentScore = fieldScores.get(field) || 0;
            fieldScores.set(field, currentScore + 1);
          }
        }
      }
    }

    // 找出匹配单词最多的字段
    let bestField = null;
    let bestScore = 0;

    for (const [field, score] of fieldScores.entries()) {
      if (score > bestScore) {
        bestScore = score;
        bestField = field;
      }
    }

    // 至少要匹配一个单词
    return bestScore > 0 ? bestField : null;
  }

  /**
   * 模糊匹配
   * @param fieldName 字段名称
   * @returns 匹配的标准字段名称
   */
  private fuzzyMatch(fieldName: string): string | null {
    const normalizedName = fieldName.toLowerCase().trim();
    const standardFieldNames = Array.from(this.standardFields.keys());

    let bestMatch = null;
    let bestScore = 0;
    const threshold = 0.6; // 降低相似度阈值，增加匹配宽容度

    for (const standardField of standardFieldNames) {
      const score = this.calculateSimilarity(normalizedName, standardField);
      if (score > threshold && score > bestScore) {
        bestMatch = this.standardFields.get(standardField);
        bestScore = score;
      }
    }

    return bestMatch;
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度得分（0-1）
   */
  private calculateSimilarity(str1: string, str2: string): number {
    // 简单实现，可以替换为更复杂的算法
    if (str1 === str2) return 1.0;
    if (str1.length === 0 || str2.length === 0) return 0.0;

    // 计算最长公共子序列
    const lcs = this.longestCommonSubsequence(str1, str2);
    return (2.0 * lcs) / (str1.length + str2.length);
  }

  /**
   * 计算最长公共子序列长度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 最长公共子序列长度
   */
  private longestCommonSubsequence(str1: string, str2: string): number {
    const m = str1.length;
    const n = str2.length;
    const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));

    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }

    return dp[m][n];
  }
}
