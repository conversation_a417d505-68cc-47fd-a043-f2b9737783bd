"use client";

import React, { useState, useRef, useEffect } from 'react';

interface Option {
  value: string;
  label: string;
  isGroupTitle?: boolean;
  mainCategory?: string;
  mainCategoryLabel?: string;
}

interface CustomDropdownProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  maxHeight?: number; // 最大高度，默认为300px
  enableSearch?: boolean; // 是否启用搜索，默认为true
  placeholder?: string; // 占位符文本
}

export const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  value,
  onChange,
  className = '',
  maxHeight = 300,
  enableSearch = true,
  placeholder = '请选择'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(options);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 获取当前选中选项的标签
  const selectedOption = options.find(option => option.value === value);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 当下拉框打开时，聚焦搜索框
  useEffect(() => {
    if (isOpen && enableSearch && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, enableSearch]);

  // 当下拉框打开/关闭时，重置搜索
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
    }
  }, [isOpen]);

  // 过滤选项
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOptions(options);
    } else {
      const query = searchQuery.toLowerCase();

      // 过滤非分组标题的选项
      const matchedOptions = options.filter(option =>
        !option.isGroupTitle && (
          option.label.toLowerCase().includes(query) ||
          option.value.toLowerCase().includes(query) ||
          (option.mainCategoryLabel && option.mainCategoryLabel.toLowerCase().includes(query))
        )
      );

      setFilteredOptions(matchedOptions);
    }
  }, [options, searchQuery]);

  // 处理选项点击
  const handleOptionClick = (option: Option) => {
    // 如果是分组标题，不做任何操作
    if (option.isGroupTitle) return;

    onChange(option.value);
    setIsOpen(false);
  };

  // 处理搜索输入
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      e.preventDefault();

      // 过滤掉分组标题，只保留可选择的选项
      const selectableOptions = filteredOptions.filter(option => !option.isGroupTitle);
      if (selectableOptions.length === 0) return;

      const currentIndex = value
        ? selectableOptions.findIndex(option => option.value === value)
        : -1;

      let nextIndex;
      if (e.key === 'ArrowDown') {
        nextIndex = currentIndex < selectableOptions.length - 1 ? currentIndex + 1 : 0;
      } else {
        nextIndex = currentIndex > 0 ? currentIndex - 1 : selectableOptions.length - 1;
      }

      onChange(selectableOptions[nextIndex].value);
    } else if (e.key === 'Enter' && isOpen) {
      setIsOpen(false);
    }
  };

  return (
    <div
      ref={dropdownRef}
      className={`relative ${className}`}
      onKeyDown={handleKeyDown}
    >
      <button
        type="button"
        className="appearance-none pl-3 pr-8 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary w-full text-left"
        style={{
          backgroundColor: 'rgba(240, 245, 250, 0.7)',
          borderColor: 'var(--color-secondary)',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          transition: 'all 0.3s ease'
        }}
        onClick={() => setIsOpen(!isOpen)}
      >
        {selectedOption?.label || placeholder}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 absolute right-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className="absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden flex flex-col"
          style={{
            animation: 'dropdownFadeIn 0.2s ease forwards',
            maxHeight: `${maxHeight}px`
          }}
        >
          <style jsx>{`
            @keyframes dropdownFadeIn {
              from { opacity: 0; transform: translateY(-10px); }
              to { opacity: 1; transform: translateY(0); }
            }

            .option-item {
              transition: all 0.2s ease;
            }

            .option-item:hover {
              background-color: rgba(235, 245, 255, 0.9);
              transform: translateX(5px);
            }
          `}</style>

          {/* 搜索框 */}
          {enableSearch && (
            <div className="sticky top-0 p-2 bg-white border-b border-gray-100 z-10">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder="搜索..."
                  className="w-full px-3 py-1.5 pl-8 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          )}

          {/* 选项列表 */}
          <div className="overflow-y-auto" style={{ maxHeight: enableSearch ? `${maxHeight - 50}px` : `${maxHeight}px` }}>
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 text-center">
                没有找到匹配项
              </div>
            ) : (
              filteredOptions.map((option) => (
                option.isGroupTitle ? (
                  // 渲染分组标题
                  <div
                    key={option.value}
                    className="px-3 py-2 text-xs font-semibold text-gray-500 bg-gray-50 uppercase tracking-wider"
                  >
                    {option.label}
                  </div>
                ) : (
                  // 渲染选项
                  <div
                    key={option.value}
                    className={`option-item px-3 py-2 cursor-pointer text-sm ${option.value === value ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                    onClick={() => handleOptionClick(option)}
                  >
                    {option.label}
                    {option.mainCategoryLabel && (
                      <span className="ml-2 text-xs text-gray-400">
                        {option.mainCategoryLabel}
                      </span>
                    )}
                  </div>
                )
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};
