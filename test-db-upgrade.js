// 临时测试文件 - 验证数据库升级
// 这个文件用于测试数据库表结构升级是否正常工作

import { db } from './src/lib/db/dexie.js';

async function testDatabaseUpgrade() {
  try {
    console.log('🔍 开始测试数据库升级...');
    
    // 打开数据库连接
    await db.open();
    console.log('✅ 数据库连接成功');
    
    // 检查数据库版本
    console.log('📊 数据库版本:', db.verno);
    
    // 检查所有表是否存在
    const tables = db.tables.map(table => table.name);
    console.log('📋 数据库表列表:', tables);
    
    // 检查worldBookPrefixes表是否存在
    const hasWorldBookTable = tables.includes('worldBookPrefixes');
    console.log('🌍 worldBookPrefixes表存在:', hasWorldBookTable);
    
    if (hasWorldBookTable) {
      // 测试表结构
      const count = await db.worldBookPrefixes.count();
      console.log('📊 worldBookPrefixes表记录数:', count);
      
      // 测试插入一条测试数据
      const testData = {
        id: 'test-worldbook-prefix',
        content: '测试世界书前置消息内容',
        category: 'custom',
        description: '测试描述',
        useCase: '测试用例',
        reasoning: '测试推理',
        confidence: 0.8,
        tags: ['测试', '世界书'],
        createdAt: new Date(),
        updatedAt: new Date(),
        usageCount: 0,
        isFavorite: false,
        worldBookSource: 'test-worldbook',
        originalUid: 1,
        isConstant: false,
        originalOrder: 0,
        originalKeys: ['测试关键词'],
        originalKeysSecondary: [],
        originalPosition: 0,
        triggerProbability: 100,
        isSelective: false
      };
      
      await db.worldBookPrefixes.add(testData);
      console.log('✅ 测试数据插入成功');
      
      // 验证数据是否正确插入
      const inserted = await db.worldBookPrefixes.get('test-worldbook-prefix');
      console.log('📝 插入的数据:', inserted ? '存在' : '不存在');
      
      // 清理测试数据
      await db.worldBookPrefixes.delete('test-worldbook-prefix');
      console.log('🧹 测试数据清理完成');
    }
    
    console.log('✅ 数据库升级测试完成');
    return true;
    
  } catch (error) {
    console.error('❌ 数据库升级测试失败:', error);
    return false;
  } finally {
    await db.close();
  }
}

// 运行测试
testDatabaseUpgrade().then(success => {
  if (success) {
    console.log('🎉 数据库升级测试通过');
  } else {
    console.log('💥 数据库升级测试失败');
  }
});
