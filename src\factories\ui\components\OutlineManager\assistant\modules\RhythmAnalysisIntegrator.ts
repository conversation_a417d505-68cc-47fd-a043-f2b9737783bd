/**
 * 节奏分析集成模块
 * 负责集成RhythmAnalysisService的功能到OutlineAI中
 */

import { ConversationMessage } from '../types/SharedTypes';

// 导入节奏分析相关类型
export interface ChapterSummary {
  title: string;
  content: string;
  order: number;
  wordCount: number;
  rhythmType: 'fast' | 'slow' | 'balanced';
  conflictLevel: number;
  emotionalIntensity: number;
  keyEvents: string[];
  summary?: string;
}

export interface SmartRhythmAnalysis {
  chapterCount: number;
  rhythmPattern: {
    overallPattern: string;
    currentTrend: string;
  };
  nextChapterSuggestion: string;
  specificGuidance: string;
  contentAnalysis: {
    strengths: string[];
    weaknesses: string[];
    suggestions: string[];
  };
}

export class RhythmAnalysisIntegrator {
  private static instance: RhythmAnalysisIntegrator;

  private constructor() {}

  public static getInstance(): RhythmAnalysisIntegrator {
    if (!RhythmAnalysisIntegrator.instance) {
      RhythmAnalysisIntegrator.instance = new RhythmAnalysisIntegrator();
    }
    return RhythmAnalysisIntegrator.instance;
  }

  /**
   * 检查节奏分析开关状态
   */
  checkRhythmAnalysisEnabled(): boolean {
    try {
      const saved = localStorage.getItem('rhythmAnalysisEnabled');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  }

  /**
   * 构建30章节奏规划智慧消息
   */
  build30ChapterRhythmWisdom(): string {
    return `**📊 30章节奏规划智慧**：
我具备专业的30章节奏规划能力，基于三层循环结构提供精确的节奏指导：

**30章三层循环架构**：
- 第1-10章（建立期）：3个微循环，重点建立世界观和角色
- 第11-20章（发展期）：3个微循环，重点推进冲突和成长
- 第21-30章（高潮期）：3个微循环，重点解决冲突和收尾

**具体节奏量化标准**：

**快节奏章节**（对话占比≥60%，场景转换≥3次/章，时间跨度≤2小时）：
- 适用场景：战斗、追逐、对峙、危机处理
- 具体指导：每段落包含动作动词，每500字设置悬念点
- 商业价值：提升读者兴奋度，增强付费转化

**慢节奏章节**（描写占比≥40%，心理活动≥30%，单场景深入）：
- 适用场景：情感铺垫、世界观建立、角色成长
- 具体指导：重要元素≥3个细节描写，深入挖掘内心
- 商业价值：增强代入感，提升读者粘性

**平节奏章节**（对话、叙述、描写各占1/3，承上启下功能）：
- 适用场景：章节过渡、信息整理、节奏调节
- 具体指导：平衡内容构成，适中信息密度
- 商业价值：给读者喘息空间，维持阅读舒适度

**智能节奏判断原则**：
我会自动识别当前章节数量和内容特征，判断在30章规划中的位置，基于前面章节的节奏分布，为下一章提供具体的量化指标，而非抽象建议。`;
  }

  /**
   * 分析章节节奏（简化版本）
   */
  analyzeChapterRhythm(existingChapters: any[]): {
    currentCycle: number;
    nextSuggestedPhase: 'setup' | 'conflict' | 'climax' | 'transition';
    rhythmSuggestion: string;
    rhythmAnalysis: string;
  } {
    const chapterCount = existingChapters.length;

    // 简单的4章循环模式
    const cyclePosition = chapterCount % 4;
    const nextPhase = ['setup', 'conflict', 'climax', 'transition'][cyclePosition] as 'setup' | 'conflict' | 'climax' | 'transition';

    const suggestions = {
      setup: '建立新的情节线，介绍关键角色或设定，为后续冲突做铺垫',
      conflict: '引入主要冲突，提升紧张感，展现角色能力和决心',
      climax: '达到情节高潮，解决当前主要矛盾，给读者强烈的爽感',
      transition: '处理冲突后果，调整角色关系，为下一个循环做准备'
    };

    const analysisMap = {
      setup: '当前处于铺垫阶段，需要稳扎稳打地建立基础',
      conflict: '进入冲突升级期，需要加快节奏，增强张力',
      climax: '到达爽点释放期，需要集中火力，给读者强烈冲击',
      transition: '处于过渡调整期，需要适当放缓，为下轮积蓄力量'
    };

    return {
      currentCycle: Math.floor(chapterCount / 4) + 1,
      nextSuggestedPhase: nextPhase,
      rhythmSuggestion: suggestions[nextPhase],
      rhythmAnalysis: analysisMap[nextPhase]
    };
  }

  /**
   * 构建包含节奏分析的用户消息
   */
  buildUserMessageWithRhythm(userMessage: string, outline: any): string {
    // 如果没有大纲数据，直接返回原消息
    if (!outline?.nodes) {
      return userMessage;
    }

    // 提取章节信息
    const chapters = outline.nodes.filter((node: any) => node.type === 'chapter');
    if (chapters.length === 0) {
      return userMessage;
    }

    const rhythmAnalysis = this.analyzeChapterRhythm(chapters);

    const rhythmMessage = `

【智能节奏分析】：
当前已有 ${chapters.length} 个章节，处于第 ${rhythmAnalysis.currentCycle} 个微循环。
${rhythmAnalysis.rhythmAnalysis}

**具体节奏建议**：${rhythmAnalysis.rhythmSuggestion}

**量化创作指标**：
- 建议节奏类型：${rhythmAnalysis.nextSuggestedPhase}
- 对话占比：${this.getDialogueRatioForPhase(rhythmAnalysis.nextSuggestedPhase)}
- 场景转换：${this.getSceneChangesForPhase(rhythmAnalysis.nextSuggestedPhase)}
- 时间跨度：${this.getTimeSpanForPhase(rhythmAnalysis.nextSuggestedPhase)}

请在创建新章节时严格按照这些量化指标，确保节奏符合30章整体规划。如果创建章节节点，请在rhythmPhase字段中标记为"${rhythmAnalysis.nextSuggestedPhase}"，并在rhythmGuidance字段中提供基于当前节奏阶段的具体创作指导。`;

    return userMessage + rhythmMessage;
  }

  /**
   * 构建基于独立分析结果的用户消息
   */
  buildUserMessageWithRhythmAnalysis(
    userMessage: string,
    rhythmAnalysis: SmartRhythmAnalysis,
    userRequirements?: string
  ): string {
    let enhancedMessage = userMessage;

    // 添加节奏分析结果
    enhancedMessage += `

【专业节奏分析结果】：
当前章节数：${rhythmAnalysis.chapterCount}
整体节奏模式：${rhythmAnalysis.rhythmPattern.overallPattern}
当前趋势：${rhythmAnalysis.rhythmPattern.currentTrend}

**下一章具体建议**：
${rhythmAnalysis.nextChapterSuggestion}

**量化创作指导**：
${rhythmAnalysis.specificGuidance}

**商业价值考虑**：
- 优势：${rhythmAnalysis.contentAnalysis.strengths.join('、')}
- 改进点：${rhythmAnalysis.contentAnalysis.weaknesses.join('、')}
- 建议：${rhythmAnalysis.contentAnalysis.suggestions.join('、')}`;

    // 如果有用户特殊要求，添加到消息中
    if (userRequirements && userRequirements.trim()) {
      enhancedMessage += `

【用户特殊要求】：
${userRequirements}

请在节奏分析的基础上，特别注意满足用户的这些具体要求。`;
    }

    return enhancedMessage;
  }

  /**
   * 递归查找所有章节节点
   */
  findAllChaptersRecursively(nodes: any[]): ChapterSummary[] {
    const chapters: ChapterSummary[] = [];

    const traverse = (nodeList: any[]) => {
      for (const node of nodeList) {
        if (node.type === 'chapter') {
          // 获取更完整的章节内容
          const chapterContent = node.description || node.content || '';
          const chapterSummary = node.summary || '';

          // 合并章节的所有可用内容
          let fullContent = '';
          if (chapterSummary) {
            fullContent += `章节摘要：${chapterSummary}\n\n`;
          }
          if (chapterContent) {
            fullContent += `章节内容：${chapterContent}`;
          }

          chapters.push({
            title: node.title || '未命名章节',
            content: fullContent || '暂无内容',
            order: chapters.length + 1,
            wordCount: fullContent.length,
            rhythmType: 'balanced',
            conflictLevel: 3,
            emotionalIntensity: 3,
            keyEvents: []
          });
        }

        // 递归查找子节点
        if (node.children && Array.isArray(node.children)) {
          traverse(node.children);
        }
      }
    };

    traverse(nodes);
    return chapters;
  }

  /**
   * 获取对话占比建议
   */
  private getDialogueRatioForPhase(phase: string): string {
    const ratios = {
      setup: '40-50%（重点建立角色和背景）',
      conflict: '60-70%（通过对话展现冲突）',
      climax: '70-80%（激烈对抗和情感爆发）',
      transition: '30-40%（更多叙述和总结）'
    };
    return ratios[phase as keyof typeof ratios] || '50%';
  }

  /**
   * 获取场景转换建议
   */
  private getSceneChangesForPhase(phase: string): string {
    const changes = {
      setup: '1-2次（稳定建立场景）',
      conflict: '2-3次（增加紧张感）',
      climax: '3-4次（快速切换增强冲击）',
      transition: '1-2次（平缓过渡）'
    };
    return changes[phase as keyof typeof changes] || '2次';
  }

  /**
   * 获取时间跨度建议
   */
  private getTimeSpanForPhase(phase: string): string {
    const spans = {
      setup: '数小时到1天（充分展开）',
      conflict: '1-3小时（集中冲突）',
      climax: '30分钟-1小时（高强度）',
      transition: '数小时到数天（缓冲调整）'
    };
    return spans[phase as keyof typeof spans] || '数小时';
  }

  /**
   * 获取书籍的章节内容用于智能分析
   */
  async getBookChaptersForAnalysis(bookId: string): Promise<ChapterSummary[]> {
    try {
      const { db } = await import('@/lib/db/dexie');
      const chapters = await db.chapters
        .where('bookId')
        .equals(bookId)
        .toArray();

      // 按order字段排序
      chapters.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

      console.log(`📚 获取到 ${chapters.length} 个章节用于分析`);

      // 过滤有效章节并提取关键信息
      return chapters
        .filter((chapter: any) => chapter.content && chapter.content.trim().length > 100)
        .map((chapter: any) => ({
          order: chapter.order,
          title: chapter.title,
          wordCount: chapter.wordCount || chapter.content.length,
          content: chapter.content,
          summary: chapter.summary || '',
          rhythmType: 'balanced' as const,
          conflictLevel: 3,
          emotionalIntensity: 3,
          keyEvents: []
        }));
    } catch (error) {
      console.error('获取章节内容失败:', error);
      return [];
    }
  }

  /**
   * 获取当前书籍ID
   */
  getCurrentBookId(): string | null {
    try {
      // 从URL或其他地方获取当前书籍ID
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('bookId');
    } catch (error) {
      console.error('获取当前书籍ID失败:', error);
      return null;
    }
  }

  /**
   * 验证节奏分析配置
   */
  validateRhythmAnalysis(): {
    isEnabled: boolean;
    hasValidData: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    const isEnabled = this.checkRhythmAnalysisEnabled();
    let hasValidData = false;

    if (!isEnabled) {
      issues.push('节奏分析功能未启用');
      recommendations.push('在设置中启用节奏分析功能以获得智能节奏建议');
    }

    // 检查是否有足够的章节数据
    const bookId = this.getCurrentBookId();
    if (bookId) {
      // 这里可以添加更详细的数据验证逻辑
      hasValidData = true;
    } else {
      issues.push('无法获取当前书籍信息');
      recommendations.push('确保在正确的书籍上下文中使用节奏分析功能');
    }

    return {
      isEnabled,
      hasValidData,
      issues,
      recommendations
    };
  }
}
