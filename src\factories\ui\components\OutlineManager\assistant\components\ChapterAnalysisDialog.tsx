/**
 * 章节分析对话框
 * 用于分析章节内容并生成JSON示例
 */

import React, { useState, useEffect } from 'react';
import { ChapterAnalysisService, ChapterAnalysisResult, PlotPointExample } from '../services/ChapterAnalysisService';
import { ExampleInjectionManager, ExampleConfig } from '../services/ExampleInjectionManager';

interface ChapterAnalysisDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExampleGenerated?: (example: ExampleConfig) => void;
  availableChapters?: any[];
  aceFrameworks?: any[];
  apiKey?: string;
  baseURL?: string;
}

export const ChapterAnalysisDialog: React.FC<ChapterAnalysisDialogProps> = ({
  isOpen,
  onClose,
  onExampleGenerated,
  availableChapters = [],
  aceFrameworks = [],
  apiKey = '',
  baseURL = 'https://api.openai.com/v1'
}) => {
  const [selectedChapter, setSelectedChapter] = useState<any>(null);
  const [chapterContent, setChapterContent] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<ChapterAnalysisResult | null>(null);
  const [exampleName, setExampleName] = useState('');
  const [analysisLog, setAnalysisLog] = useState('');
  const [step, setStep] = useState<'select' | 'analyze' | 'preview' | 'save'>('select');

  // 重置状态
  const resetState = () => {
    setSelectedChapter(null);
    setChapterContent('');
    setAnalysisResult(null);
    setExampleName('');
    setAnalysisLog('');
    setStep('select');
  };

  // 关闭对话框
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 选择章节
  const handleChapterSelect = (chapter: any) => {
    setSelectedChapter(chapter);
    setChapterContent(chapter.content || chapter.description || '');
    setStep('analyze');
  };

  // 开始分析
  const handleStartAnalysis = async () => {
    if (!chapterContent.trim()) {
      alert('请输入章节内容');
      return;
    }

    if (!apiKey.trim()) {
      alert('请配置API密钥');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisLog('开始分析章节内容...\n');

    try {
      const analysisService = new ChapterAnalysisService(apiKey, baseURL);
      
      const result = await analysisService.analyzeChapterContent(
        chapterContent,
        aceFrameworks,
        (chunk: string) => {
          setAnalysisLog(prev => prev + chunk);
        }
      );

      if (result.success) {
        setAnalysisResult(result);
        setExampleName(`章节分析_${new Date().toLocaleString()}`);
        setStep('preview');
        setAnalysisLog(prev => prev + '\n\n✅ 分析完成！');
      } else {
        setAnalysisLog(prev => prev + `\n\n❌ 分析失败: ${result.error}`);
      }
    } catch (error) {
      console.error('分析失败:', error);
      setAnalysisLog(prev => prev + `\n\n❌ 分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 保存示例
  const handleSaveExample = () => {
    if (!analysisResult || !exampleName.trim()) {
      alert('请输入示例名称');
      return;
    }

    try {
      const savedExample = ExampleInjectionManager.saveExample(
        exampleName,
        analysisResult.plotPoints,
        analysisResult.overallStyle,
        analysisResult.mainCharacters,
        analysisResult.conflictLevel,
        analysisResult.emotionalTone
      );

      setStep('save');
      onExampleGenerated?.(savedExample);
      
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (error) {
      console.error('保存示例失败:', error);
      alert('保存示例失败');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">章节内容分析</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {step === 'select' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">选择要分析的章节</h3>
              
              {availableChapters.length > 0 ? (
                <div className="grid gap-3">
                  {availableChapters.map((chapter, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleChapterSelect(chapter)}
                    >
                      <h4 className="font-medium">{chapter.title || `章节 ${index + 1}`}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {(chapter.description || chapter.content || '').substring(0, 100)}...
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">暂无可分析的章节</p>
                  <p className="text-sm text-gray-400 mt-2">
                    请先创建一些章节内容，然后再进行分析
                  </p>
                </div>
              )}

              <div className="mt-6">
                <h4 className="font-medium mb-2">或者直接输入章节内容：</h4>
                <textarea
                  value={chapterContent}
                  onChange={(e) => setChapterContent(e.target.value)}
                  placeholder="请输入要分析的章节内容..."
                  className="w-full h-32 p-3 border rounded-lg resize-none"
                />
                {chapterContent.trim() && (
                  <button
                    onClick={() => setStep('analyze')}
                    className="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    分析此内容
                  </button>
                )}
              </div>
            </div>
          )}

          {step === 'analyze' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">分析章节内容</h3>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">章节内容预览：</h4>
                <div className="text-sm text-gray-700 max-h-32 overflow-y-auto">
                  {chapterContent.substring(0, 500)}
                  {chapterContent.length > 500 && '...'}
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">ACE框架应用：</h4>
                <p className="text-sm text-gray-700">
                  将应用 {aceFrameworks.length} 个ACE框架进行分析
                </p>
              </div>

              {analysisLog && (
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-40 overflow-y-auto">
                  <pre className="whitespace-pre-wrap">{analysisLog}</pre>
                </div>
              )}

              <div className="flex gap-3">
                <button
                  onClick={handleStartAnalysis}
                  disabled={isAnalyzing}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                >
                  {isAnalyzing ? '分析中...' : '开始分析'}
                </button>
                <button
                  onClick={() => setStep('select')}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  返回选择
                </button>
              </div>
            </div>
          )}

          {step === 'preview' && analysisResult && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">分析结果预览</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium">整体风格</h4>
                  <p className="text-sm mt-1">{analysisResult.overallStyle}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-medium">情感基调</h4>
                  <p className="text-sm mt-1">{analysisResult.emotionalTone}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-medium">主要角色</h4>
                  <p className="text-sm mt-1">{analysisResult.mainCharacters.join(', ')}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-medium">冲突等级</h4>
                  <p className="text-sm mt-1">{analysisResult.conflictLevel}/5</p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">提取的剧情点 ({analysisResult.plotPoints.length}个)</h4>
                <div className="space-y-3">
                  {analysisResult.plotPoints.map((point, index) => (
                    <div key={index} className="bg-white p-3 rounded border">
                      <h5 className="font-medium text-sm">剧情点 {point.order}</h5>
                      <p className="text-sm text-gray-700 mt-1">{point.content}</p>
                      <div className="mt-2 text-xs text-gray-500">
                        <p><strong>避免：</strong>{point.avoidWriting}</p>
                        <p><strong>推荐：</strong>{point.shouldWriting}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">示例名称</label>
                  <input
                    type="text"
                    value={exampleName}
                    onChange={(e) => setExampleName(e.target.value)}
                    placeholder="为这个示例起个名字..."
                    className="w-full p-2 border rounded"
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={handleSaveExample}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    保存示例
                  </button>
                  <button
                    onClick={() => setStep('analyze')}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    重新分析
                  </button>
                </div>
              </div>
            </div>
          )}

          {step === 'save' && (
            <div className="text-center py-8">
              <div className="text-green-500 text-4xl mb-4">✅</div>
              <h3 className="text-lg font-medium">示例保存成功！</h3>
              <p className="text-gray-600 mt-2">
                示例已保存，可以在示例管理中查看和使用
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
