/**
 * 书名生成响应解析器
 * 处理AI返回的书名生成结果
 */

import { BookTitle, AITitleResponse, TitleFramework } from '../types/BrainstormTypes';

export class TitleResponseParser {
  /**
   * 解析AI书名生成响应
   */
  static parseGenerationResponse(
    responseText: string,
    usedKeywords: string[],
    usedFramework?: TitleFramework,
    customFramework?: string
  ): BookTitle[] {
    try {
      console.log('🔍 开始解析书名生成响应:', {
        responseLength: responseText.length,
        usedKeywords,
        hasFramework: !!usedFramework,
        hasCustomFramework: !!customFramework
      });

      // 尝试解析JSON
      const parsed = this.tryParseJSON(responseText);
      if (!parsed || !parsed.titles || !Array.isArray(parsed.titles)) {
        throw new Error('响应格式不正确：缺少titles数组');
      }

      const titles: BookTitle[] = [];
      
      for (let i = 0; i < parsed.titles.length; i++) {
        const titleData = parsed.titles[i];
        
        try {
          const bookTitle = this.createBookTitle(
            titleData,
            usedKeywords,
            usedFramework,
            customFramework
          );
          titles.push(bookTitle);
        } catch (error) {
          console.warn(`⚠️ 解析第${i + 1}个书名失败:`, error);
          // 继续处理其他书名
        }
      }

      console.log('✅ 书名解析完成:', {
        totalParsed: titles.length,
        averageScore: titles.reduce((sum, t) => sum + t.aiScore, 0) / titles.length
      });

      return titles;

    } catch (error: any) {
      console.error('❌ 书名响应解析失败:', error);
      
      // 尝试从文本中提取书名
      return this.fallbackExtraction(responseText, usedKeywords, usedFramework, customFramework);
    }
  }

  /**
   * 尝试解析JSON，支持容错处理
   */
  private static tryParseJSON(text: string): any {
    // 清理文本
    let cleanText = text.trim();
    
    // 移除可能的markdown代码块标记
    cleanText = cleanText.replace(/```json\s*/g, '').replace(/```\s*/g, '');
    
    // 查找JSON对象
    const jsonMatch = cleanText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleanText = jsonMatch[0];
    }

    try {
      return JSON.parse(cleanText);
    } catch (error) {
      // 尝试修复常见的JSON错误
      const fixedText = this.fixCommonJSONErrors(cleanText);
      try {
        return JSON.parse(fixedText);
      } catch (secondError) {
        throw new Error(`JSON解析失败: ${secondError}`);
      }
    }
  }

  /**
   * 修复常见的JSON错误
   */
  private static fixCommonJSONErrors(text: string): string {
    return text
      // 修复尾随逗号
      .replace(/,(\s*[}\]])/g, '$1')
      // 修复单引号
      .replace(/'/g, '"')
      // 修复未转义的引号
      .replace(/([^\\])"/g, '$1\\"');
  }

  /**
   * 创建BookTitle对象
   */
  private static createBookTitle(
    titleData: any,
    usedKeywords: string[],
    usedFramework?: TitleFramework,
    customFramework?: string
  ): BookTitle {
    if (!titleData.title || typeof titleData.title !== 'string') {
      throw new Error('书名文本缺失或格式错误');
    }

    const title = titleData.title.trim();
    if (title.length === 0) {
      throw new Error('书名不能为空');
    }

    // 验证和处理评分
    let aiScore = 7.0; // 默认评分
    if (typeof titleData.score === 'number') {
      aiScore = Math.max(1, Math.min(10, titleData.score));
    } else if (typeof titleData.score === 'string') {
      const parsed = parseFloat(titleData.score);
      if (!isNaN(parsed)) {
        aiScore = Math.max(1, Math.min(10, parsed));
      }
    }

    // 提取关键词
    const extractedKeywords = this.extractKeywords(titleData, title, usedKeywords);

    // 检测框架
    const detectedFramework = this.detectFramework(titleData, usedFramework, customFramework);

    return {
      id: `title_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title,
      aiScore,
      reason: titleData.reason || '暂无创作说明',
      extractedKeywords,
      detectedFramework,
      frameworkMatch: usedFramework,
      isFavorited: false,
      createdAt: new Date()
    };
  }

  /**
   * 提取关键词
   */
  private static extractKeywords(titleData: any, title: string, usedKeywords: string[]): string[] {
    const keywords: string[] = [];

    // 从AI响应中提取
    if (Array.isArray(titleData.keywords)) {
      keywords.push(...titleData.keywords.filter(k => typeof k === 'string' && k.trim()));
    }

    // 从书名中自动提取使用的关键词
    for (const keyword of usedKeywords) {
      if (title.includes(keyword) && !keywords.includes(keyword)) {
        keywords.push(keyword);
      }
    }

    return keywords;
  }

  /**
   * 检测框架模式
   */
  private static detectFramework(
    titleData: any,
    usedFramework?: TitleFramework,
    customFramework?: string
  ): string | undefined {
    if (titleData.framework && typeof titleData.framework === 'string') {
      return titleData.framework;
    }

    if (usedFramework) {
      return usedFramework.pattern;
    }

    if (customFramework) {
      return customFramework;
    }

    return undefined;
  }

  /**
   * 降级提取：从纯文本中提取书名
   */
  private static fallbackExtraction(
    text: string,
    usedKeywords: string[],
    usedFramework?: TitleFramework,
    customFramework?: string
  ): BookTitle[] {
    console.log('🔄 启用降级提取模式');

    const titles: BookTitle[] = [];
    const lines = text.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmed = line.trim();
      
      // 跳过明显不是书名的行
      if (trimmed.length < 2 || trimmed.length > 20 || 
          trimmed.includes('JSON') || trimmed.includes('{') || trimmed.includes('}')) {
        continue;
      }

      // 清理可能的序号和标点
      const cleaned = trimmed.replace(/^\d+[.\s]*/, '').replace(/[《》""'']/g, '').trim();
      
      if (cleaned.length >= 2 && cleaned.length <= 15) {
        titles.push({
          id: `fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: cleaned,
          aiScore: 6.0, // 降级提取的默认评分
          reason: '通过文本解析提取',
          extractedKeywords: usedKeywords.filter(k => cleaned.includes(k)),
          detectedFramework: usedFramework?.pattern || customFramework,
          frameworkMatch: usedFramework,
          isFavorited: false,
          createdAt: new Date()
        });

        if (titles.length >= 5) break; // 最多提取5个
      }
    }

    console.log(`🔄 降级提取完成，提取到${titles.length}个书名`);
    return titles;
  }

  /**
   * 流式解析：处理流式响应
   */
  static tryParseStreamingResponse(
    streamText: string,
    usedKeywords: string[],
    usedFramework?: TitleFramework,
    customFramework?: string,
    onTitleParsed?: (title: BookTitle) => void
  ): BookTitle[] {
    const titles: BookTitle[] = [];

    try {
      // 尝试解析部分JSON
      const partialData = this.extractPartialJSON(streamText);

      if (partialData && partialData.titles && Array.isArray(partialData.titles)) {
        for (const titleData of partialData.titles) {
          if (titleData.title && typeof titleData.title === 'string') {
            const title = this.createBookTitle(titleData, usedKeywords, usedFramework, customFramework);
            titles.push(title);
            onTitleParsed?.(title);
          }
        }
      }
    } catch (error) {
      // 流式解析失败是正常的，不需要报错
    }

    return titles;
  }

  /**
   * 提取部分JSON
   */
  private static extractPartialJSON(text: string): any {
    // 查找可能的JSON片段
    const jsonStart = text.indexOf('{');
    if (jsonStart === -1) return null;

    let braceCount = 0;
    let jsonEnd = -1;

    for (let i = jsonStart; i < text.length; i++) {
      if (text[i] === '{') braceCount++;
      if (text[i] === '}') {
        braceCount--;
        if (braceCount === 0) {
          jsonEnd = i;
          break;
        }
      }
    }

    if (jsonEnd === -1) return null;

    const jsonText = text.substring(jsonStart, jsonEnd + 1);
    return this.tryParseJSON(jsonText);
  }
}
