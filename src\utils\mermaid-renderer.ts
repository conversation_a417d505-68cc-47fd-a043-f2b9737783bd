/**
 * Mermaid渲染工具
 * 集成render-mermaid功能到短篇创作系统
 */

interface RenderMermaidOptions {
  diagram_definition: string;
  title?: string;
}

/**
 * 渲染Mermaid图表
 * 这是一个简化的实现，实际应该调用render-mermaid工具
 */
export async function renderMermaid(options: RenderMermaidOptions): Promise<void> {
  try {
    // 这里应该调用实际的render-mermaid工具
    // 目前先模拟实现
    console.log('渲染Mermaid图表:', options);
    
    // 模拟异步渲染过程
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 实际实现应该是：
    // const { renderMermaid } = await import('render-mermaid');
    // await renderMermaid(options);
    
  } catch (error) {
    console.error('Mermaid渲染失败:', error);
    throw new Error(`图表渲染失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 验证Mermaid语法
 */
export function validateMermaidSyntax(code: string): { isValid: boolean; error?: string } {
  try {
    const trimmedCode = code.trim();
    
    if (!trimmedCode) {
      return { isValid: false, error: '代码不能为空' };
    }
    
    // 基本语法检查
    const validKeywords = [
      'graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 
      'stateDiagram', 'gantt', 'pie', 'journey', 'gitgraph'
    ];
    
    const hasValidKeyword = validKeywords.some(keyword => 
      trimmedCode.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (!hasValidKeyword) {
      return { 
        isValid: false, 
        error: '未识别到有效的Mermaid图表类型' 
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : '未知错误' 
    };
  }
}
