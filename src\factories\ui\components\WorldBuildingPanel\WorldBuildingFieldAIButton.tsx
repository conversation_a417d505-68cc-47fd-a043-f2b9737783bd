"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding, PromptTemplate, PromptCategory } from '@/lib/db/dexie';
import { createAIFactory } from '@/factories/ai/AIFactory';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { AIWorldBuildingExtractorAdapter } from '@/adapters/ai/AIWorldBuildingExtractorAdapter';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { getAssociatedChapterContent, saveChapterAssociation } from '@/utils/chapterAssociation';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorldBuildingFieldAIButtonProps {
  worldBuilding: WorldBuilding;
  fieldName: string;
  fieldDisplayName: string;
  onSave: (worldBuilding: WorldBuilding) => void;
  bookId?: string; // 可选的书籍ID，用于获取章节列表
  mode?: 'generate' | 'update'; // 按钮模式：生成或更新，默认为生成
}

/**
 * 世界观字段AI功能按钮组件
 * 用于显示单个字段的AI功能按钮和处理AI功能
 */
export const WorldBuildingFieldAIButton: React.FC<WorldBuildingFieldAIButtonProps> = ({
  worldBuilding,
  fieldName,
  fieldDisplayName,
  onSave,
  bookId: propBookId,
  mode = 'generate' // 默认为生成模式
}) => {
  // 使用worldBuilding.bookId作为默认值，如果propBookId存在则使用propBookId
  const bookId = propBookId || worldBuilding.bookId;

  const [isLoading, setIsLoading] = useState(false);
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);
  // 现有世界观元素列表
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);
  // 选中的关联世界观元素
  const [selectedRelatedWorldBuildings, setSelectedRelatedWorldBuildings] = useState<string[]>([]);
  // 世界观加载状态
  const [isLoadingWorldBuildings, setIsLoadingWorldBuildings] = useState(false);

  // 加载章节列表和世界观元素
  useEffect(() => {
    if (bookId) {
      loadChapters();
      loadWorldBuildings();
    } else {
      console.warn('WorldBuildingFieldAIButton: bookId不存在，无法加载章节列表和世界观元素');
    }
  }, [bookId]);

  // 加载世界观元素
  const loadWorldBuildings = async () => {
    if (!bookId) return;

    setIsLoadingWorldBuildings(true);
    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 获取书籍的所有世界观元素
      const bookWorldBuildings = await worldBuildingRepository.getAllByBookId(bookId);

      // 排除当前正在编辑的世界观元素
      const filteredWorldBuildings = bookWorldBuildings.filter(wb => wb.id !== worldBuilding.id);

      // 按名称排序
      const sortedWorldBuildings = [...filteredWorldBuildings].sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      setWorldBuildings(sortedWorldBuildings);
    } catch (error) {
      console.error('加载世界观元素失败:', error);
    } finally {
      setIsLoadingWorldBuildings(false);
    }
  };

  // 加载章节列表
  const loadChapters = async () => {
    if (!bookId) return;

    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 src/db/chapterRepository.ts
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
    } catch (error) {
      console.error('加载章节数据失败:', error);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  const handleAIClick = async (e: React.MouseEvent, promptContent?: string) => {
    // 阻止事件冒泡，防止触发父组件的事件处理
    e.preventDefault();
    e.stopPropagation();

    if (isLoading) return; // 防止重复点击

    // 如果提供了promptContent参数，使用它而不是customPrompt状态
    const currentPrompt = promptContent !== undefined ? promptContent : customPrompt;

    // 创建AI适配器
    const aiAdapter = new AIWorldBuildingExtractorAdapter();

    // 获取API设置
    const settingsFactory = createSettingsFactory();
    const apiSettings = settingsFactory.createAPISettingsDialogComponent();

    // 检查API密钥是否已设置
    const currentProvider = apiSettings.getCurrentProvider();
    const apiKey = apiSettings.getAPIKey(currentProvider);
    const currentModel = apiSettings.getCurrentModel();
    const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

    console.log('当前API设置:', {
      provider: currentProvider,
      model: currentModel,
      hasApiKey: !!apiKey,
      endpoint: apiEndpoint
    });

    if (!apiKey) {
      alert(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
      return;
    }

    // 创建提示词对话框
    const promptDialogEl = document.createElement('div');
    promptDialogEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

    // 设置对话框标题和按钮文本
    const dialogTitle = mode === 'generate' ? `使用AI生成${fieldDisplayName}` : `使用AI更新${fieldDisplayName}`;
    const actionButtonText = mode === 'generate' ? '生成' : '更新';
    const placeholderText = mode === 'generate'
      ? `请输入特定要求，例如：\n- 详细描述这个${fieldDisplayName}的特点\n- 使用更生动的语言\n- 添加更多细节`
      : `请输入特定要求，例如：\n- 根据章节内容更新${fieldDisplayName}\n- 添加更多细节\n- 保持一致的风格`;

    // 构建对话框HTML
    let dialogHTML = `
      <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full">
        <h3 class="text-xl font-bold mb-4 text-purple-700">${dialogTitle}</h3>
        <div class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <p class="text-sm text-gray-600">您可以输入特定的要求来指导AI${mode === 'generate' ? '生成' : '更新'}${fieldDisplayName}，或者保留为空使用默认提示词。</p>
            <div class="flex space-x-2">
              <button id="save-template-btn" class="px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                保存模板
              </button>
              <button id="manage-templates-btn" class="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                管理模板
              </button>
            </div>
          </div>
          <textarea id="custom-prompt" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows="6" placeholder="${placeholderText}">${currentPrompt}</textarea>
          <div class="mt-2 text-xs text-gray-500">
            ${currentPrompt ? '已加载提示词模板，可以直接使用或修改。' : '输入提示词或使用模板。'}
          </div>
        </div>
    `;

    // 如果有书籍ID，添加章节选择器
    if (bookId) {
      dialogHTML += `
        <div class="mb-4">
          <p class="text-sm font-medium text-gray-700 mb-2">选择相关章节（可选）</p>
          <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-md p-2">
      `;

      if (isLoadingChapters) {
        dialogHTML += `<p class="text-sm text-gray-500 p-2">加载章节中...</p>`;
      } else if (chapters.length === 0) {
        dialogHTML += `<p class="text-sm text-gray-500 p-2">没有可用的章节</p>`;
      } else {
        dialogHTML += `
          <div class="mb-2">
            <label class="inline-flex items-center">
              <input type="checkbox" id="select-all-chapters" class="form-checkbox h-4 w-4 text-blue-600">
              <span class="ml-2 text-sm font-medium text-gray-700">全选</span>
            </label>
          </div>
          <div class="space-y-1">
        `;

        chapters.forEach((chapter, index) => {
          const chapterTitle = chapter.title || `章节 ${chapter.order !== undefined ? chapter.order + 1 : index + 1}`;
          dialogHTML += `
            <label class="flex items-center">
              <input type="checkbox" value="${chapter.id}" class="chapter-checkbox form-checkbox h-4 w-4 text-blue-600">
              <span class="ml-2 text-sm text-gray-700">${chapterTitle}</span>
            </label>
          `;
        });

        dialogHTML += `</div>`;
      }

      dialogHTML += `</div></div>`;

      // 添加关联世界观元素选择器
      dialogHTML += `
        <div class="mb-4">
          <p class="text-sm font-medium text-gray-700 mb-2">关联世界观元素（可选）</p>
          <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-md p-2">
      `;

      if (isLoadingWorldBuildings) {
        dialogHTML += `<p class="text-sm text-gray-500 p-2">加载世界观元素中...</p>`;
      } else if (worldBuildings.length === 0) {
        dialogHTML += `<p class="text-sm text-gray-500 p-2">没有可用的世界观元素</p>`;
      } else {
        dialogHTML += `<div class="space-y-1">`;

        worldBuildings.forEach((wb) => {
          dialogHTML += `
            <label class="flex items-center">
              <input type="checkbox" value="${wb.name}" class="worldbuilding-checkbox form-checkbox h-4 w-4 text-blue-600">
              <span class="ml-2 text-sm text-gray-700">
                ${wb.name} <span class="text-xs text-gray-500">(${wb.category || '未分类'})</span>
              </span>
            </label>
          `;
        });

        dialogHTML += `</div>`;
      }

      dialogHTML += `</div></div>`;
    }

    // 添加底部按钮
    dialogHTML += `
        <div class="text-xs text-gray-500 mb-4">
          使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
        </div>
        <div class="flex justify-end space-x-2">
          <button id="prompt-cancel-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
            取消
          </button>
          <button id="prompt-generate-btn" class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
            ${actionButtonText}
          </button>
        </div>
      </div>
    `;

    promptDialogEl.innerHTML = dialogHTML;

    // 阻止对话框上的点击事件冒泡
    promptDialogEl.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    document.body.appendChild(promptDialogEl);

    // 取消按钮
    document.getElementById('prompt-cancel-btn')?.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      promptDialogEl.remove();
    });

    // 保存模板按钮
    document.getElementById('save-template-btn')?.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡

      // 获取当前输入的提示词
      const promptTextarea = document.getElementById('custom-prompt') as HTMLTextAreaElement;
      if (!promptTextarea || !promptTextarea.value.trim()) {
        alert('请先输入提示词内容');
        return;
      }

      // 保存当前提示词
      setCustomPrompt(promptTextarea.value);

      // 创建保存模板表单
      const saveFormEl = document.createElement('div');
      saveFormEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      saveFormEl.innerHTML = `
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
          <h3 class="text-xl font-bold mb-4 text-green-700">保存提示词模板</h3>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              模板名称 <span class="text-red-500">*</span>
            </label>
            <input
              id="template-name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="输入模板名称..."
            />
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              模板描述
            </label>
            <input
              id="template-description"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="输入模板描述（可选）..."
            />
          </div>
          <div class="flex justify-end space-x-2">
            <button id="save-form-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
              取消
            </button>
            <button id="save-form-submit" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
              保存
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(saveFormEl);

      // 取消按钮
      document.getElementById('save-form-cancel')?.addEventListener('click', () => {
        saveFormEl.remove();
      });

      // 提交按钮
      document.getElementById('save-form-submit')?.addEventListener('click', async () => {
        const nameInput = document.getElementById('template-name') as HTMLInputElement;
        const descriptionInput = document.getElementById('template-description') as HTMLInputElement;

        if (!nameInput || !nameInput.value.trim()) {
          alert('请输入模板名称');
          return;
        }

        try {
          // 导入 promptTemplateRepository
          const { promptTemplateRepository } = await import('@/lib/db/repositories');

          // 创建新模板
          const newTemplate: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
            category: PromptCategory.WORLD_BUILDING,
            name: nameInput.value.trim(),
            content: promptTextarea.value,
            description: descriptionInput?.value.trim() || undefined
          };

          // 保存模板
          await promptTemplateRepository.create(newTemplate);

          // 关闭表单
          saveFormEl.remove();

          // 显示成功消息
          alert('提示词模板保存成功');
        } catch (error) {
          console.error('保存提示词模板失败:', error);
          alert('保存提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
        }
      });
    });

    // 管理模板按钮
    document.getElementById('manage-templates-btn')?.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡

      // 获取当前输入的提示词
      const promptTextarea = document.getElementById('custom-prompt') as HTMLTextAreaElement;
      if (promptTextarea) {
        setCustomPrompt(promptTextarea.value);
      }

      // 关闭提示词对话框
      promptDialogEl.remove();

      // 打开提示词模板管理器
      setIsPromptManagerOpen(true);
    });

    // 全选章节复选框
    const selectAllCheckbox = document.getElementById('select-all-chapters') as HTMLInputElement;
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', (e) => {
        const isChecked = (e.target as HTMLInputElement).checked;
        const chapterCheckboxes = document.querySelectorAll('.chapter-checkbox') as NodeListOf<HTMLInputElement>;
        chapterCheckboxes.forEach(checkbox => {
          checkbox.checked = isChecked;
        });
      });
    }

    // 生成/更新按钮
    document.getElementById('prompt-generate-btn')?.addEventListener('click', async (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      const promptValue = (document.getElementById('custom-prompt') as HTMLTextAreaElement)?.value;
      setCustomPrompt(promptValue); // 保存提示词，以便下次打开时显示

      // 获取选中的章节ID（无论是生成还是更新模式）
      let selectedChapters: string[] = [];
      if (bookId) {
        const checkboxes = document.querySelectorAll('.chapter-checkbox:checked') as NodeListOf<HTMLInputElement>;
        selectedChapters = Array.from(checkboxes).map(checkbox => checkbox.value);
      }

      // 获取选中的关联世界观元素
      let relatedWorldBuildings: string[] = [];
      if (bookId) {
        const worldBuildingCheckboxes = document.querySelectorAll('.worldbuilding-checkbox:checked') as NodeListOf<HTMLInputElement>;
        relatedWorldBuildings = Array.from(worldBuildingCheckboxes).map(checkbox => {
          // 获取数据属性中的ID和值（名称）
          const id = checkbox.dataset.id || '';
          const name = checkbox.value;
          // 返回 "id:name" 格式
          return `${id}:${name}`;
        });
      }

      // 保存选中的关联世界观元素
      setSelectedRelatedWorldBuildings(relatedWorldBuildings);

      // 关闭提示词对话框
      promptDialogEl.remove();

      // 显示加载对话框
      setIsLoading(true);
      const loadingEl = document.createElement('div');
      loadingEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      loadingEl.innerHTML = `
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full">
          <h3 class="text-xl font-bold mb-4 text-purple-700">AI正在${mode === 'generate' ? '生成' : '更新'}${fieldDisplayName}</h3>
          <div id="streaming-content" class="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
            <p id="streaming-text" class="whitespace-pre-wrap">正在处理...</p>
          </div>
          <button id="ai-cancel-btn" class="w-full p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
            取消
          </button>
        </div>
      `;
      document.body.appendChild(loadingEl);

      // 取消按钮
      document.getElementById('ai-cancel-btn')?.addEventListener('click', (e) => {
        e.stopPropagation(); // 阻止事件冒泡

        // 取消AI请求
        aiAdapter.cancelRequest();

        // 关闭对话框
        loadingEl.remove();
        setIsLoading(false);
      });

      try {
        // 获取API设置
        const settingsFactory = createSettingsFactory();
        const apiSettings = settingsFactory.createAPISettingsDialogComponent();
        const currentProvider = apiSettings.getCurrentProvider();
        const currentModel = apiSettings.getCurrentModel();

        // 准备更新流式输出的UI
        const streamingTextElement = document.getElementById('streaming-text');

        // 生成字段内容
        let content = '';

        // 根据不同字段调用不同的AI方法，传递自定义提示词
        try {
          console.log(`开始${mode === 'generate' ? '生成' : '更新'}${fieldName}，使用${customPrompt ? '自定义提示词' : '默认提示词'}`);

          // 确保启用流式输出
          apiSettings.setStreamingEnabled(true);

          // 创建AI工厂和发送组件
          const aiFactory = createAIFactory();
          const aiSender = aiFactory.createAISenderComponent();

          // 流式输出回调函数
          const onStreamChunk = (chunk: string) => {
            content += chunk;
            if (streamingTextElement) {
              streamingTextElement.textContent = content;

              // 自动滚动到底部
              const streamingContent = document.getElementById('streaming-content');
              if (streamingContent) {
                streamingContent.scrollTop = streamingContent.scrollHeight;
              }
            }
          };

          // 创建世界观AI适配器
          const worldBuildingAIAdapter = new AIWorldBuildingExtractorAdapter();

          // 处理章节关联和内容获取
          if (bookId) {
            try {
              if (selectedChapters.length > 0 && worldBuilding.id) {
                // 如果选择了章节，保存章节关联
                await saveChapterAssociation(worldBuilding.id, 'worldbuilding', selectedChapters);
                console.log('已保存世界观与章节的关联关系');
              }
            } catch (error) {
              console.error('保存章节关联关系失败:', error);
            }
          }

          // 根据模式和字段类型选择不同的AI方法
          if (bookId && worldBuilding.id) {
            try {
              if (mode === 'generate') {
                // 生成模式：使用关联章节内容生成字段
                content = await worldBuildingAIAdapter.generateFieldWithAssociatedChapters(
                  worldBuilding,
                  fieldName,
                  bookId,
                  selectedRelatedWorldBuildings,
                  promptValue || customPrompt
                );
                console.log(`使用关联章节生成字段内容完成，内容长度: ${content.length}`);
              } else {
                // 更新模式：使用关联章节内容更新字段
                const updateSuggestions = await worldBuildingAIAdapter.updateWorldBuildingWithAssociatedChapters(
                  worldBuilding,
                  bookId,
                  {
                    relatedWorldBuildings: selectedRelatedWorldBuildings
                  }
                );

                // 打印更新建议，便于调试
                console.log('获取到的更新建议:', updateSuggestions);
                console.log('当前字段名:', fieldName);

                // 查找与当前字段相关的更新建议
                let relevantSuggestion;

                if (fieldName === 'description') {
                  // 对于描述字段，直接查找字段名为'description'的建议
                  relevantSuggestion = updateSuggestions.find(suggestion => suggestion.field === 'description');
                } else if (fieldName.startsWith('attributes.')) {
                  // 对于属性字段，提取属性名并查找匹配的建议
                  const attributeName = fieldName.split('.')[1];
                  relevantSuggestion = updateSuggestions.find(suggestion =>
                    suggestion.field === fieldName ||
                    suggestion.field === `attributes.${attributeName}`
                  );

                  // 如果没有找到精确匹配，尝试查找字段名为属性名的建议
                  if (!relevantSuggestion) {
                    relevantSuggestion = updateSuggestions.find(suggestion =>
                      suggestion.field === attributeName
                    );
                  }
                }

                if (relevantSuggestion) {
                  content = relevantSuggestion.suggestedValue;
                  console.log(`找到字段 ${fieldName} 的更新建议:`, relevantSuggestion);
                  console.log(`更新建议内容长度: ${content.length}`);
                } else {
                  // 如果没有找到相关的更新建议，使用生成模式
                  content = await worldBuildingAIAdapter.generateFieldWithAssociatedChapters(
                    worldBuilding,
                    fieldName,
                    bookId,
                    selectedRelatedWorldBuildings,
                    promptValue || customPrompt
                  );
                  console.log(`未找到更新建议，改用生成模式，内容长度: ${content.length}`);
                }
              }

              // 更新流式输出
              if (streamingTextElement) {
                streamingTextElement.textContent = content;
              }

              // 成功获取内容后，显示结果预览（包含应用按钮）
              loadingEl.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl">
                  <h3 class="text-xl font-bold mb-4 text-purple-700">AI生成的${fieldDisplayName}</h3>
                  <div class="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
                    <p class="whitespace-pre-wrap">${content}</p>
                  </div>
                  <div class="text-xs text-gray-500 mb-2">
                    使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
                  </div>
                  <div class="flex justify-end space-x-2">
                    <button id="ai-apply-btn" class="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                      应用
                    </button>
                    <button id="ai-copy-btn" class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                      复制
                    </button>
                    <button id="ai-result-close" class="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                      关闭
                    </button>
                  </div>
                </div>
              `;

              // 添加按钮事件监听器
              // 应用按钮
              document.getElementById('ai-apply-btn')?.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡

                // 根据模式决定是替换还是追加内容
                let newContent = content;

                // 如果是更新模式，则追加内容而不是替换
                if (mode === 'update') {
                  // 获取现有内容（使用类型安全的方式）
                  let existingContent = '';
                  if (fieldName === 'description') {
                    existingContent = worldBuilding.description || '';
                  } else if (fieldName.startsWith('attributes.')) {
                    const attributeName = fieldName.split('.')[1];
                    existingContent = worldBuilding.attributes?.[attributeName] || '';
                  }

                  // 如果现有内容不为空，则追加新内容（带补充标记）
                  if (existingContent.trim()) {
                    // 查找当前内容中的所有补充标记，确定下一个编号
                    let nextNumber = 1;
                    if (existingContent) {
                      const regex = /【补充(\d+)】/g;
                      let match;
                      while ((match = regex.exec(existingContent)) !== null) {
                        const num = parseInt(match[1], 10);
                        if (!isNaN(num) && num >= nextNumber) {
                          nextNumber = num + 1;
                        }
                      }
                    }

                    const appendPrefix = `【补充${nextNumber}】`;
                    newContent = `${existingContent}\n\n${appendPrefix}\n${content}`;
                  }
                }

                // 更新世界观对象
                const updatedWorldBuilding = { ...worldBuilding };
                if (fieldName === 'description') {
                  updatedWorldBuilding.description = newContent;
                } else if (fieldName.startsWith('attributes.')) {
                  const attributeName = fieldName.split('.')[1];
                  updatedWorldBuilding.attributes = {
                    ...updatedWorldBuilding.attributes,
                    [attributeName]: newContent
                  };
                }

                // 保存更新后的世界观
                onSave(updatedWorldBuilding);

                // 关闭对话框
                loadingEl.remove();
                setIsLoading(false);
              });

              // 复制按钮
              document.getElementById('ai-copy-btn')?.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡

                // 复制内容到剪贴板
                navigator.clipboard.writeText(content)
                  .then(() => {
                    alert('内容已复制到剪贴板');
                  })
                  .catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败: ' + err);
                  });
              });

              // 关闭按钮
              document.getElementById('ai-result-close')?.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                loadingEl.remove();
                setIsLoading(false);
              });

              // 不再执行后面的标准AI方法
              return;
            } catch (error) {
              console.error(`使用关联章节${mode === 'generate' ? '生成' : '更新'}字段内容失败:`, error);
              console.log('回退到标准AI方法');
            }
          }

          // 如果上面的方法失败或不适用，使用标准方法
          console.log('使用标准AI方法（无关联章节）');

          // 获取章节内容（标准方法）
          let chapterContent = '';
          if (bookId) {
            try {
              if (selectedChapters.length > 0) {
                // 获取选中章节的内容
                for (const chapterId of selectedChapters) {
                  const chapter = chapters.find(c => c.id === chapterId);
                  if (chapter) {
                    chapterContent += `# ${chapter.title || (chapter.order !== undefined ? `第${chapter.order + 1}章` : '无标题章节')}\n\n${chapter.content || ''}\n\n`;
                  }
                }
                console.log(`已加载${selectedChapters.length}个选中章节的内容，总长度: ${chapterContent.length}`);
              } else if (worldBuilding.id) {
                // 如果没有选择章节但有世界观ID，尝试获取关联章节内容
                chapterContent = await getAssociatedChapterContent(worldBuilding.id, 'worldbuilding', bookId);
                if (chapterContent) {
                  console.log(`已加载关联章节内容，总长度: ${chapterContent.length}`);
                } else {
                  console.log('没有找到关联章节内容，使用默认内容');
                  // 即使没有关联章节，也继续处理，使用一个默认内容
                  chapterContent = `# 默认章节\n\n这是一个默认章节内容，用于在没有关联章节时进行处理。`;
                }
              } else {
                // 如果既没有选择章节也没有世界观ID，使用默认内容
                console.log('没有选择章节且没有世界观ID，使用默认内容');
                chapterContent = `# 默认章节\n\n这是一个默认章节内容，用于在没有章节时进行处理。`;
              }
            } catch (error) {
              console.error('获取章节内容失败:', error);
            }
          }

          // 构建系统消息
          const systemMessage = `你是一位专业的小说世界观分析师，擅长从文本中提取和分析世界观元素。你需要${mode === 'generate' ? '生成' : '更新'}关于"${worldBuilding.name}"的${fieldDisplayName}。请提供详细、生动且符合小说风格的内容。`;

          // 构建世界观信息
          const buildWorldBuildingInfo = () => {
            let info = `世界观元素: ${worldBuilding.name}\n`;
            if (worldBuilding.category) {
              info += `类别: ${worldBuilding.category}\n`;
            }
            if (worldBuilding.description) {
              info += `描述: ${worldBuilding.description}\n`;
            }

            // 添加其他属性
            if (worldBuilding.attributes) {
              for (const [key, value] of Object.entries(worldBuilding.attributes)) {
                // 跳过当前正在生成/更新的字段
                if (fieldName.startsWith('attributes.') && key === fieldName.split('.')[1]) {
                  continue;
                }
                info += `${key}: ${value}\n`;
              }
            }

            return info;
          };

          // 构建用户提示词
          let presetUserPrompt = '';
          if (fieldName === 'description') {
            presetUserPrompt = `请为世界观元素"${worldBuilding.name}"生成一段详细的描述。描述应该包含其主要特征、在故事中的作用和重要性。`;
          } else if (fieldName.startsWith('attributes.')) {
            // 获取属性名称，但不需要在这里使用
            // const attributeName = fieldName.split('.')[1];
            presetUserPrompt = `请为世界观元素"${worldBuilding.name}"的"${fieldDisplayName}"生成内容。内容应该详细、具体，并与世界观的其他属性保持一致。`;
          } else {
            presetUserPrompt = `请为世界观元素"${worldBuilding.name}"的"${fieldDisplayName}"生成内容。`;
          }

          // 使用MessageBuilder构建多角色消息数组，以便更精准地控制上下文
          const messageBuilder = createMessageBuilder()
            // 系统消息，设置整体的行为和风格
            .addSystemMessage(systemMessage)

            // 助手消息，展示世界观已有的信息
            .addAssistantMessage(`以下是关于世界观元素"${worldBuilding.name}"的现有信息：\n\n${buildWorldBuildingInfo()}`);

          // 如果有章节内容，添加章节内容（无论是生成还是更新模式）
          if (chapterContent) {
            messageBuilder.addUserMessage(`以下是关于世界观元素"${worldBuilding.name}"的章节内容，请${mode === 'generate' ? '参考这些内容生成世界观信息' : '从中提取相关信息'}：\n\n${chapterContent}`);
            messageBuilder.addAssistantMessage(`我已分析章节内容，找到了关于"${worldBuilding.name}"的相关信息。`);
          }

          // 添加用户消息，设置具体的生成要求
          messageBuilder.addUserMessage(promptValue || customPrompt || presetUserPrompt);

          // 助手消息，确认任务
          messageBuilder.addAssistantMessage(`我将为世界观元素"${worldBuilding.name}"${mode === 'generate' ? '生成' : '更新'}${fieldDisplayName}。`);

          // 构建消息数组
          const messages = messageBuilder.build();

          // 调用AI模型
          const result = await aiSender.sendRequest('', {
            messages,
            provider: currentProvider,
            model: currentModel,
            apiKey: apiKey,
            apiEndpoint: apiEndpoint,
            temperature: 0.7,
            max_tokens: 1000,
            stream: true,
            onStreamChunk
          });

          // 更新最终内容
          content = result.text;
          console.log(`AI${mode === 'generate' ? '生成' : '更新'}完成，内容长度: ${content.length}`);
        } catch (error) {
          console.error(`AI${mode === 'generate' ? '生成' : '更新'}失败:`, error);
          if (streamingTextElement) {
            streamingTextElement.textContent = `AI${mode === 'generate' ? '生成' : '更新'}失败: ${error instanceof Error ? error.message : String(error)}`;
          }
          return;
        }

        // 显示结果预览
        loadingEl.innerHTML = `
          <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl">
            <h3 class="text-xl font-bold mb-4 text-purple-700">AI生成的${fieldDisplayName}</h3>
            <div class="max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
              <p class="whitespace-pre-wrap">${content}</p>
            </div>
            <div class="text-xs text-gray-500 mb-2">
              使用${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}提供商，模型: ${currentModel}
            </div>
            <div class="flex justify-end space-x-2">
              <button id="ai-apply-btn" class="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                应用
              </button>
              <button id="ai-copy-btn" class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                复制
              </button>
              <button id="ai-result-close" class="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                关闭
              </button>
            </div>
          </div>
        `;

        // 应用按钮
        document.getElementById('ai-apply-btn')?.addEventListener('click', (e) => {
          e.stopPropagation(); // 阻止事件冒泡

          // 根据模式决定是替换还是追加内容
          let newContent = content;

          // 如果是更新模式，则追加内容而不是替换
          if (mode === 'update') {
            // 获取现有内容（使用类型安全的方式）
            let existingContent = '';
            if (fieldName === 'description') {
              existingContent = worldBuilding.description || '';
            } else if (fieldName.startsWith('attributes.')) {
              const attributeName = fieldName.split('.')[1];
              existingContent = worldBuilding.attributes?.[attributeName] || '';
            }

            // 如果现有内容不为空，则追加新内容（带补充标记）
            if (existingContent.trim()) {
              // 查找当前内容中的所有补充标记，确定下一个编号
              let nextNumber = 1;
              if (existingContent) {
                const regex = /【补充(\d+)】/g;
                let match;
                while ((match = regex.exec(existingContent)) !== null) {
                  const num = parseInt(match[1], 10);
                  if (!isNaN(num) && num >= nextNumber) {
                    nextNumber = num + 1;
                  }
                }
              }

              const appendPrefix = `【补充${nextNumber}】`;
              newContent = `${existingContent}\n\n${appendPrefix}\n${content}`;
            }
          }

          // 更新世界观对象
          const updatedWorldBuilding = { ...worldBuilding };
          if (fieldName === 'description') {
            updatedWorldBuilding.description = newContent;
          } else if (fieldName.startsWith('attributes.')) {
            const attributeName = fieldName.split('.')[1];
            updatedWorldBuilding.attributes = {
              ...updatedWorldBuilding.attributes,
              [attributeName]: newContent
            };
          }

          // 保存更新后的世界观
          onSave(updatedWorldBuilding);

          // 关闭对话框
          loadingEl.remove();
          setIsLoading(false);
        });

        // 复制按钮
        document.getElementById('ai-copy-btn')?.addEventListener('click', (e) => {
          e.stopPropagation(); // 阻止事件冒泡

          // 复制内容到剪贴板
          navigator.clipboard.writeText(content)
            .then(() => {
              alert('内容已复制到剪贴板');
            })
            .catch(err => {
              console.error('复制失败:', err);
              alert('复制失败: ' + err);
            });
        });

        // 关闭按钮
        document.getElementById('ai-result-close')?.addEventListener('click', (e) => {
          e.stopPropagation(); // 阻止事件冒泡
          loadingEl.remove();
          setIsLoading(false);
        });
      } catch (error) {
        console.error(`AI${mode === 'generate' ? '生成' : '更新'}失败:`, error);
        alert(`AI${mode === 'generate' ? '生成' : '更新'}失败: ${error instanceof Error ? error.message : String(error)}`);
        loadingEl.remove();
        setIsLoading(false);
      }
    });
  };

  // 处理提示词模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    // 设置提示词内容（这是异步的，不会立即更新）
    setCustomPrompt(template.content);

    // 关闭模板管理器
    setIsPromptManagerOpen(false);

    // 自动打开AI对话框，直接传递模板内容，不依赖状态更新
    setTimeout(() => {
      console.log('自动打开AI对话框，使用模板:', template.name);
      try {
        // 创建一个新的鼠标事件
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });

        // 调用handleAIClick函数，直接传递模板内容
        handleAIClick(clickEvent as any, template.content);
      } catch (error) {
        console.error('自动打开AI对话框失败:', error);
        // 如果自动打开失败，提示用户手动点击
        alert(`已选择模板"${template.name}"，请再次点击AI按钮使用该模板。`);
      }
    }, 100); // 短延时，只是为了确保UI更新
  };

  // 根据模式渲染不同的按钮
  return (
    <>
      {mode === 'update' ? (
        // 更新模式：渲染更新按钮
        <button
          className="ml-2 p-1 text-blue-600 hover:text-blue-800 transition-colors"
          onClick={handleAIClick}
          title={`使用AI更新${fieldDisplayName}`}
          onMouseDown={(e) => e.stopPropagation()} // 阻止鼠标按下事件冒泡
          onMouseUp={(e) => e.stopPropagation()} // 阻止鼠标释放事件冒泡
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      ) : (
        // 生成模式：渲染生成按钮
        <button
          className="ml-2 p-1 text-purple-600 hover:text-purple-800 transition-colors"
          onClick={handleAIClick}
          title={`使用AI生成${fieldDisplayName}`}
          onMouseDown={(e) => e.stopPropagation()} // 阻止鼠标按下事件冒泡
          onMouseUp={(e) => e.stopPropagation()} // 阻止鼠标释放事件冒泡
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </button>
      )}

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.WORLD_BUILDING}
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />
    </>
  );
};
