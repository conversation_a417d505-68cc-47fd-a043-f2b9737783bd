import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { BaseAIService } from '../../../services/ai/BaseAIService';

interface ExtractedKeywords {
  category: string;
  keywords: string[];
  reason: string;
}

interface AIKeywordExtractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddKeywords: (keywords: string[]) => void;
  chatContent?: string; // 当前聊天内容
  allChatMessages?: Array<{role: string; content: string}>; // 所有聊天消息
}

// AI关键词提取服务
class KeywordExtractorAIService extends BaseAIService {
  constructor() {
    super('KeywordExtractor');
  }

  // 对比模式的系统提示词
  private getComparisonSystemPrompt(): string {
    return `[身份定义] = [你是一个专业的中文写作质量分析专家，专门识别AI生成文本中的问题词汇和表达]

[核心任务] = [对比AI生成文本和高质量真实样本，深度挖掘AI文本中存在的问题词汇，为禁用词汇表提供精准的目标词汇]

[分析维度] = [
- 空洞修饰词：缺乏具体含义的形容词和副词
- 陈词滥调：过时老套的固定搭配和表达
- AI套路表达：AI常用的模式化表达方式
- 时间副词滥用：破坏叙述节奏的时间副词
- 情感夸张词：过度夸张不真实的情感表达
- 无效强调词：没有实际强调作用的程度副词
- 套话连接词：机械化的逻辑连接词
- 万能形容词：过于宽泛没有特色的形容词
- 网络流行语：不适合正式写作的网络用语
]

[提取要求] = [
- 数量要求：每个分析维度至少提取3-8个问题词汇，总数不少于20个
- 质量标准：只提取AI文本中确实存在且真实样本中没有的词汇
- 精准度要求：每个词汇都必须是具体的、可直接禁用的目标
- 实用性要求：优先提取对写作质量影响最大的问题词汇
- 覆盖面要求：尽可能覆盖文本中的各类问题表达
]

[分析深度] = [
- 逐句对比AI文本和真实样本的表达差异
- 识别AI文本中的重复性表达模式
- 发现真实样本中更自然生动的表达方式
- 提取AI文本中显得生硬、套路化的词汇
- 重点关注影响文本真实感和表达力的问题词汇
]

[输出格式] = [严格按照JSON格式返回，不要包含任何其他文字或解释]

[JSON结构示例] = [
{
  "extractedKeywords": [
    {
      "category": "分析维度名称",
      "keywords": ["词汇1", "词汇2", "词汇3", "词汇4", "词汇5"],
      "reason": "具体的问题分析原因"
    }
  ]
}
]

[重要提醒] = [
- 不要局限于常见的问题词汇，要深入挖掘文本中的具体问题
- 每个分类的词汇数量要充足，确保提取的全面性
- 优先提取对文本质量影响最大的词汇
- 确保所有提取的词汇都在AI文本中真实存在
]`;
  }

  // 聊天模式的系统提示词
  private getChatAnalysisSystemPrompt(): string {
    return `[身份定义] = [你是一个专业的中文写作质量分析专家，专门识别文本中的问题词汇和表达方式]

[核心任务] = [深度分析提供的文本内容，全面识别其中的问题词汇和不当表达，为写作质量提升提供精准的禁用词汇清单]

[分析维度] = [
- 空洞修饰词：缺乏具体含义的形容词和副词
- 陈词滥调：过时老套的固定搭配和表达
- AI套路表达：AI常用的模式化表达方式
- 时间副词滥用：破坏叙述节奏的时间副词
- 情感夸张词：过度夸张不真实的情感表达
- 无效强调词：没有实际强调作用的程度副词
- 套话连接词：机械化的逻辑连接词
- 万能形容词：过于宽泛没有特色的形容词
- 网络流行语：不适合正式写作的网络用语
- 语境不符词：与上下文语境不匹配的表达
- 重复性表达：在文本中过度重复的词汇或短语
]

[提取要求] = [
- 数量要求：每个分析维度至少提取2-6个问题词汇，总数不少于15个
- 质量标准：只提取文本中确实存在的问题词汇，不能凭空创造
- 精准度要求：每个词汇都必须是具体的、可直接禁用的目标
- 实用性要求：优先提取出现频率高且影响质量的问题词汇
- 覆盖面要求：尽可能覆盖文本中的各类问题表达
- 真实性要求：确保所有提取的词汇都在原文中真实出现
]

[分析深度] = [
- 逐句扫描文本，识别每个可能的问题词汇
- 分析词汇在语境中的适当性和必要性
- 识别重复出现的问题表达模式
- 评估词汇对整体文本质量的影响程度
- 重点关注破坏文本自然感和表达力的词汇
- 发现隐藏的套路化表达和模式化用词
]

[输出格式] = [严格按照JSON格式返回，不要包含任何其他文字或解释]

[JSON结构示例] = [
{
  "extractedKeywords": [
    {
      "category": "分析维度名称",
      "keywords": ["词汇1", "词汇2", "词汇3", "词汇4"],
      "reason": "具体的问题分析原因"
    }
  ]
}
]

[重要提醒] = [
- 必须基于实际文本内容进行分析，不能添加文本中不存在的词汇
- 每个分类的词汇数量要充足，确保分析的全面性
- 优先提取对文本质量影响最大的问题词汇
- 重点关注文本中的重复性问题和模式化表达
- 确保提取的词汇具有实际的禁用价值
]`;
  }

  async extractKeywordsFromComparison(aiText: string, realSample: string): Promise<ExtractedKeywords[]> {
    const messages = [
      { role: 'system', content: this.getComparisonSystemPrompt() },
      {
        role: 'user',
        content: `请对比以下两个文本，提取AI生成文本中的问题词汇：

[AI生成文本]=
[\n\n${aiText}\n\n]

[高质量真实样本]=
[\n\n${realSample}\n\n]

请分析AI文本中相比真实样本存在的问题词汇，并以JSON格式返回。`
      }
    ];

    const response = await this.callAI(messages, { temperature: 0.3 });

    if (!response.success) {
      throw new Error(response.error || 'AI分析失败');
    }

    return this.parseKeywordsResponse(response.text);
  }

  async extractKeywordsFromChat(chatContent: string): Promise<ExtractedKeywords[]> {
    const messages = [
      { role: 'system', content: this.getChatAnalysisSystemPrompt() },
      {
        role: 'user',
        content: `请分析以下聊天内容中的问题词汇：

${chatContent}

请识别其中的老套形容词、不连贯表达、语境不符等问题，并以JSON格式返回。`
      }
    ];

    const response = await this.callAI(messages, { temperature: 0.3 });

    if (!response.success) {
      throw new Error(response.error || 'AI分析失败');
    }

    return this.parseKeywordsResponse(response.text);
  }

  private parseKeywordsResponse(responseText: string): ExtractedKeywords[] {
    try {
      // 尝试提取JSON部分
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('未找到有效的JSON响应');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed.extractedKeywords || [];
    } catch (error) {
      console.error('解析AI响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }
}

const AIKeywordExtractorDialog: React.FC<AIKeywordExtractorDialogProps> = ({
  isOpen,
  onClose,
  onAddKeywords,
  chatContent = '',
  allChatMessages = []
}) => {
  const [mode, setMode] = useState<'comparison' | 'chat'>('comparison');
  const [aiText, setAiText] = useState('');
  const [realSample, setRealSample] = useState('');
  const [chatText, setChatText] = useState(chatContent);
  const [extractedKeywords, setExtractedKeywords] = useState<ExtractedKeywords[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedKeywords, setSelectedKeywords] = useState<Set<string>>(new Set());
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [aiService] = useState(() => new KeywordExtractorAIService());

  // 当聊天内容变化时更新
  useEffect(() => {
    if (allChatMessages.length > 0) {
      // 将所有聊天消息合并为一个文本
      const combinedText = allChatMessages
        .map(msg => `${msg.role === 'user' ? '用户' : 'AI'}：${msg.content}`)
        .join('\n\n');
      setChatText(combinedText);
    } else {
      setChatText(chatContent);
    }
  }, [chatContent, allChatMessages]);

  // 重置状态
  const resetState = () => {
    setAiText('');
    setRealSample('');
    setChatText(chatContent);
    setExtractedKeywords([]);
    setSelectedKeywords(new Set());
  };

  // 关闭对话框
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 执行AI分析
  const handleAnalyze = async () => {
    if (mode === 'comparison' && (!aiText.trim() || !realSample.trim())) {
      alert('请填写AI生成文本和真实样本');
      return;
    }

    if (mode === 'chat' && !chatText.trim()) {
      alert('请填写要分析的聊天内容');
      return;
    }

    setIsAnalyzing(true);
    try {
      let keywords: ExtractedKeywords[];

      if (mode === 'comparison') {
        keywords = await aiService.extractKeywordsFromComparison(aiText, realSample);
      } else {
        keywords = await aiService.extractKeywordsFromChat(chatText);
      }

      setExtractedKeywords(keywords);
    } catch (error: any) {
      alert(`分析失败：${error.message}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 切换关键词选择
  const toggleKeyword = (keyword: string) => {
    const newSelected = new Set(selectedKeywords);
    if (newSelected.has(keyword)) {
      newSelected.delete(keyword);
    } else {
      newSelected.add(keyword);
    }
    setSelectedKeywords(newSelected);
  };

  // 切换分类全选
  const toggleCategorySelection = (category: ExtractedKeywords) => {
    const newSelected = new Set(selectedKeywords);
    const allCategorySelected = category.keywords.every(keyword => newSelected.has(keyword));

    if (allCategorySelected) {
      // 如果全部选中，则取消选择
      category.keywords.forEach(keyword => newSelected.delete(keyword));
    } else {
      // 如果未全部选中，则全选
      category.keywords.forEach(keyword => newSelected.add(keyword));
    }

    setSelectedKeywords(newSelected);
  };

  // 显示确认对话框
  const handleShowConfirmDialog = () => {
    if (selectedKeywords.size === 0) {
      alert('请选择要添加的关键词');
      return;
    }
    setShowConfirmDialog(true);
  };

  // 确认添加关键词到禁用词汇表
  const handleConfirmAddKeywords = () => {
    onAddKeywords(Array.from(selectedKeywords));
    setShowConfirmDialog(false);
    alert(`✅ 已成功添加 ${selectedKeywords.size} 个关键词到禁用词汇表！`);
    handleClose();
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[10002] p-4 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden border border-gray-100">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-800">AI智能关键词提取</h3>
              <p className="text-sm text-gray-600">识别文本中的问题词汇，提升写作质量</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-white hover:bg-opacity-80 rounded-xl transition-all duration-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(95vh-140px)]">
          {/* 模式选择 */}
          <div className="mb-6">
            <div className="flex space-x-1 p-1 bg-gray-100 rounded-xl">
              <button
                onClick={() => setMode('comparison')}
                className={`flex-1 px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                  mode === 'comparison'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <span>对比分析</span>
                </div>
              </button>
              <button
                onClick={() => setMode('chat')}
                className={`flex-1 px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                  mode === 'chat'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>内容分析</span>
                </div>
              </button>
            </div>
            <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
              <p className="text-sm text-blue-800">
                {mode === 'comparison'
                  ? '💡 对比AI生成文本与高质量样本，精准识别问题词汇和表达方式'
                  : '💡 深度分析文本内容，识别老套表达、空洞修饰和套路化用词'
                }
              </p>
            </div>
          </div>

          {/* 输入区域 */}
          {mode === 'comparison' ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <label className="text-sm font-semibold text-gray-800">
                    AI生成文本 <span className="text-red-500">*</span>
                  </label>
                </div>
                <div className="relative">
                  <textarea
                    value={aiText}
                    onChange={(e) => setAiText(e.target.value)}
                    placeholder="粘贴需要分析的AI生成文本内容..."
                    className="w-full h-40 p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-400 focus:ring-4 focus:ring-purple-100 text-sm resize-none transition-all duration-200 bg-red-50"
                  />
                  <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                    {aiText.length} 字符
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <label className="text-sm font-semibold text-gray-800">
                    高质量真实样本 <span className="text-red-500">*</span>
                  </label>
                </div>
                <div className="relative">
                  <textarea
                    value={realSample}
                    onChange={(e) => setRealSample(e.target.value)}
                    placeholder="粘贴高质量的真实文本样本作为对比标准..."
                    className="w-full h-40 p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-400 focus:ring-4 focus:ring-purple-100 text-sm resize-none transition-all duration-200 bg-green-50"
                  />
                  <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                    {realSample.length} 字符
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                </div>
                <label className="text-sm font-semibold text-gray-800">
                  文本内容分析
                </label>
              </div>
              <div className="relative">
                <textarea
                  value={chatText}
                  onChange={(e) => setChatText(e.target.value)}
                  placeholder="输入要分析的文本内容，AI将识别其中的问题词汇和表达..."
                  className="w-full h-40 p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-400 focus:ring-4 focus:ring-purple-100 text-sm resize-none transition-all duration-200 bg-blue-50"
                />
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {chatText.length} 字符
                </div>
              </div>
            </div>
          )}

          {/* 分析按钮 */}
          <div className="mb-6 flex justify-center">
            <button
              onClick={handleAnalyze}
              disabled={isAnalyzing}
              className="px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl hover:from-purple-600 hover:to-blue-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 text-sm font-semibold flex items-center space-x-3 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              {isAnalyzing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>AI正在分析中...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>开始AI智能分析</span>
                </>
              )}
            </button>
          </div>

          {/* 分析结果 */}
          {extractedKeywords.length > 0 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                  <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>AI分析结果</span>
                </h4>
                <div className="text-sm text-gray-500">
                  已选择 <span className="font-semibold text-purple-600">{selectedKeywords.size}</span> 个关键词
                </div>
              </div>

              {extractedKeywords.map((category, categoryIndex) => (
                <div key={categoryIndex} className="bg-gradient-to-r from-gray-50 to-white border-2 border-gray-100 rounded-2xl p-5 hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h5 className="text-base font-bold text-gray-800 mb-2 flex items-center space-x-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span>{category.category}</span>
                      </h5>
                      <p className="text-sm text-gray-600 leading-relaxed">{category.reason}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleCategorySelection(category)}
                        className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                          category.keywords.every(keyword => selectedKeywords.has(keyword))
                            ? 'bg-purple-500 text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-purple-100'
                        }`}
                      >
                        {category.keywords.every(keyword => selectedKeywords.has(keyword)) ? '取消全选' : '全选分类'}
                      </button>
                      <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                        {category.keywords.length} 个词汇
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    {category.keywords.map((keyword, keywordIndex) => {
                      const isSelected = selectedKeywords.has(keyword);
                      return (
                        <button
                          key={`${categoryIndex}-${keywordIndex}`}
                          onClick={() => toggleKeyword(keyword)}
                          className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 transform hover:scale-105 ${
                            isSelected
                              ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                              : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50'
                          }`}
                        >
                          <div className="flex items-center space-x-2">
                            {isSelected ? (
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <div className="w-3 h-3 border border-gray-400 rounded-sm"></div>
                            )}
                            <span>{keyword}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}

              {/* 快速操作 */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => {
                      const allKeywords = extractedKeywords.flatMap(cat => cat.keywords);
                      setSelectedKeywords(new Set(allKeywords));
                    }}
                    className="px-4 py-2 text-sm text-purple-600 border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors"
                  >
                    全选
                  </button>
                  <button
                    onClick={() => setSelectedKeywords(new Set())}
                    className="px-4 py-2 text-sm text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    清空选择
                  </button>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="text-sm text-gray-600">
                    {selectedKeywords.size > 0 ? (
                      <span>已选择 <strong className="text-purple-600">{selectedKeywords.size}</strong> 个关键词</span>
                    ) : (
                      <span className="text-gray-400">请选择要添加的关键词</span>
                    )}
                  </div>
                  <button
                    onClick={handleShowConfirmDialog}
                    disabled={selectedKeywords.size === 0}
                    className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 text-sm font-medium flex items-center space-x-2 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span>添加到禁用词汇表</span>
                    {selectedKeywords.size > 0 && (
                      <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                        {selectedKeywords.size}
                      </span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-center p-6 bg-gray-50 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-8 py-3 text-gray-600 border-2 border-gray-300 rounded-xl hover:bg-white hover:border-gray-400 transition-all duration-200 text-sm font-medium"
          >
            关闭
          </button>
        </div>
      </div>

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[10003]">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">确认添加关键词</h3>
                  <p className="text-sm text-gray-600">将选中的关键词添加到禁用词汇表</p>
                </div>
              </div>

              <div className="mb-6">
                <div className="bg-gray-50 rounded-xl p-4">
                  <p className="text-sm text-gray-700 mb-3">
                    您即将添加 <strong className="text-purple-600">{selectedKeywords.size}</strong> 个关键词到禁用词汇表：
                  </p>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {Array.from(selectedKeywords).map((keyword, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowConfirmDialog(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmAddKeywords}
                  className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
                >
                  确认添加
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>,
    document.body
  );
};

export default AIKeywordExtractorDialog;
