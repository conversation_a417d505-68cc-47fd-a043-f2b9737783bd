"use client";

import React from 'react';

interface AssociationCardProps {
  icon: string;
  count: number;
  label: string;
  color: 'blue' | 'green' | 'orange' | 'purple' | 'red';
}

const AssociationCard: React.FC<AssociationCardProps> = ({ icon, count, label, color }) => {
  const getColorClasses = () => {
    const colorMap = {
      blue: count > 0 ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-gray-50 border-gray-200 text-gray-500',
      green: count > 0 ? 'bg-green-50 border-green-200 text-green-700' : 'bg-gray-50 border-gray-200 text-gray-500',
      orange: count > 0 ? 'bg-orange-50 border-orange-200 text-orange-700' : 'bg-gray-50 border-gray-200 text-gray-500',
      purple: count > 0 ? 'bg-purple-50 border-purple-200 text-purple-700' : 'bg-gray-50 border-gray-200 text-gray-500',
      red: count > 0 ? 'bg-red-50 border-red-200 text-red-700' : 'bg-gray-50 border-gray-200 text-gray-500'
    };
    return colorMap[color];
  };

  return (
    <div className={`
      relative p-3 rounded-lg border transition-all duration-200
      hover:shadow-md hover:-translate-y-0.5 cursor-default
      ${getColorClasses()}
    `}>
      <div className="flex flex-col items-center text-center">
        <div className="text-lg mb-1">{icon}</div>
        <div className="text-lg font-semibold mb-1 association-count" data-count={count}>
          {count}
        </div>
        <div className="text-xs font-medium">{label}</div>
      </div>
    </div>
  );
};

interface AssociationOverviewProps {
  chapterCount: number;
  characterCount: number;
  terminologyCount: number;
  worldBuildingCount: number;
}

/**
 * 关联内容概览组件
 * 显示各类型关联内容的统计信息
 */
export const AssociationOverview: React.FC<AssociationOverviewProps> = ({
  chapterCount,
  characterCount,
  terminologyCount,
  worldBuildingCount
}) => {
  const totalCount = chapterCount + characterCount + terminologyCount + worldBuildingCount;

  // 空状态显示
  if (totalCount === 0) {
    return (
      <div className="text-center py-6 text-gray-500 association-empty-state">
        <div className="text-3xl mb-3 association-empty-icon">🔗</div>
        <div className="text-sm font-medium mb-1">还没有关联内容</div>
        <div className="text-xs text-gray-400">
          添加章节、人物、术语等内容，让AI创作更精准
        </div>
      </div>
    );
  }

  // 有内容时的网格显示
  return (
    <div className="association-overview">
      <div className="grid grid-cols-2 gap-3 mb-3">
        <AssociationCard
          icon="📖"
          count={chapterCount}
          label="章节"
          color="blue"
        />
        <AssociationCard
          icon="👤"
          count={characterCount}
          label="人物"
          color="green"
        />
      </div>
      <div className="grid grid-cols-2 gap-3 mb-3">
        <AssociationCard
          icon="📝"
          count={terminologyCount}
          label="术语"
          color="orange"
        />
        <AssociationCard
          icon="🌍"
          count={worldBuildingCount}
          label="世界观"
          color="purple"
        />
      </div>

      {/* 总计显示 */}
      <div className="text-center text-xs text-gray-600 py-2 bg-gray-50 rounded-lg">
        <span className="font-medium">总计 {totalCount} 个关联内容</span>
      </div>
    </div>
  );
};

export default AssociationOverview;
