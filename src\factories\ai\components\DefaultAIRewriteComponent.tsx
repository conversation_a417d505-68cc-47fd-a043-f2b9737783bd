"use client";

import React, { useState, useEffect } from 'react';
import { IAIRewriteComponent } from '../interfaces';
import { aiRewriteService, RewriteContentParams } from '../services/AIRewriteService';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';

/**
 * 默认AI选中改写组件实现
 */
export class DefaultAIRewriteComponent implements IAIRewriteComponent {
  private selectedText: string = '';
  private rewriteRequirements: string = '';
  private plot: string = '';
  private beforeContext: string = '';
  private afterContext: string = '';
  private conversationHistory: any[] = [];
  private apiSettings: any;
  private onRewrittenCallback: ((text: string) => void) | null = null;

  // 大纲关联数据
  private outlines: any[] = [];
  private selectedOutlineIds: string[] = [];
  private selectedOutlineNodeIds: string[] = [];
  private outlineContextMode: 'selected' | 'hierarchy' | 'full' = 'hierarchy';

  // 其他关联数据
  private chapters: any[] = [];
  private selectedChapterIds: string[] = [];
  private characters: any[] = [];
  private selectedCharacterIds: string[] = [];
  private terminologies: any[] = [];
  private selectedTerminologyIds: string[] = [];
  private worldBuildings: any[] = [];
  private selectedWorldBuildingIds: string[] = [];
  private bookId: string = '';

  constructor() {
    // 获取API设置
    const settingsFactory = createSettingsFactory();
    this.apiSettings = settingsFactory.createAPISettingsDialogComponent();
  }

  /**
   * 设置选中的文本
   * @param text 选中的文本
   */
  setSelectedText(text: string): void {
    this.selectedText = text;
  }

  /**
   * 设置改写要求
   * @param requirements 改写要求
   */
  setRewriteRequirements(requirements: string): void {
    this.rewriteRequirements = requirements;
  }

  /**
   * 设置剧情
   * @param plot 剧情
   */
  setPlot(plot: string): void {
    this.plot = plot;
  }

  /**
   * 设置上下文
   * @param before 选中文本前的内容
   * @param after 选中文本后的内容
   */
  setContext(before: string, after: string): void {
    this.beforeContext = before;
    this.afterContext = after;
  }

  /**
   * 设置改写完成回调
   * @param callback 回调函数
   */
  setOnRewrittenCallback(callback: (text: string) => void): void {
    this.onRewrittenCallback = callback;
  }

  /**
   * 设置大纲数据
   * @param outlines 大纲数据
   */
  setOutlines(outlines: any[]): void {
    this.outlines = outlines;
  }

  /**
   * 设置选中的大纲ID
   * @param ids 选中的大纲ID
   */
  setSelectedOutlineIds(ids: string[]): void {
    this.selectedOutlineIds = ids;
  }

  /**
   * 设置选中的大纲节点ID
   * @param ids 选中的大纲节点ID
   */
  setSelectedOutlineNodeIds(ids: string[]): void {
    this.selectedOutlineNodeIds = ids;
  }

  /**
   * 设置大纲上下文模式
   * @param mode 上下文模式
   */
  setOutlineContextMode(mode: 'selected' | 'hierarchy' | 'full'): void {
    this.outlineContextMode = mode;
  }

  /**
   * 设置章节数据
   * @param chapters 章节数据
   */
  setChapters(chapters: any[]): void {
    this.chapters = chapters;
  }

  /**
   * 设置选中的章节ID
   * @param ids 选中的章节ID
   */
  setSelectedChapterIds(ids: string[]): void {
    this.selectedChapterIds = ids;
  }

  /**
   * 设置人物数据
   * @param characters 人物数据
   */
  setCharacters(characters: any[]): void {
    this.characters = characters;
  }

  /**
   * 设置选中的人物ID
   * @param ids 选中的人物ID
   */
  setSelectedCharacterIds(ids: string[]): void {
    this.selectedCharacterIds = ids;
  }

  /**
   * 设置术语数据
   * @param terminologies 术语数据
   */
  setTerminologies(terminologies: any[]): void {
    this.terminologies = terminologies;
  }

  /**
   * 设置选中的术语ID
   * @param ids 选中的术语ID
   */
  setSelectedTerminologyIds(ids: string[]): void {
    this.selectedTerminologyIds = ids;
  }

  /**
   * 设置世界观数据
   * @param worldBuildings 世界观数据
   */
  setWorldBuildings(worldBuildings: any[]): void {
    this.worldBuildings = worldBuildings;
  }

  /**
   * 设置选中的世界观ID
   * @param ids 选中的世界观ID
   */
  setSelectedWorldBuildingIds(ids: string[]): void {
    this.selectedWorldBuildingIds = ids;
  }

  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void {
    this.bookId = bookId;
  }

  /**
   * 改写文本
   * @returns 改写后的文本
   */
  async rewrite(): Promise<string> {
    console.log('改写文本', {
      selectedText: this.selectedText,
      rewriteRequirements: this.rewriteRequirements,
      plot: this.plot,
      beforeContext: this.beforeContext.substring(0, 100) + '...',
      afterContext: this.afterContext.substring(0, 100) + '...'
    });

    // 获取API设置
    const currentProvider = this.apiSettings.getCurrentProvider();
    const currentModel = this.apiSettings.getCurrentModel();
    const apiKey = this.apiSettings.getAPIKey(currentProvider);
    const apiEndpoint = this.apiSettings.getAPIEndpoint(currentProvider);

    if (!apiKey) {
      throw new Error(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
    }

    // 创建改写参数
    const params: RewriteContentParams = {
      provider: currentProvider,
      model: currentModel,
      apiKey: apiKey,
      apiEndpoint: apiEndpoint,
      selectedText: this.selectedText,
      rewriteRequirements: this.rewriteRequirements,
      plot: this.plot,
      beforeContext: this.beforeContext,
      afterContext: this.afterContext,
      chapters: this.chapters,
      selectedChapterIds: this.selectedChapterIds,
      characters: this.characters,
      selectedCharacterIds: this.selectedCharacterIds,
      terminologies: this.terminologies,
      selectedTerminologyIds: this.selectedTerminologyIds,
      worldBuildings: this.worldBuildings,
      selectedWorldBuildingIds: this.selectedWorldBuildingIds,
      // 新增：大纲关联参数
      outlines: this.outlines,
      selectedOutlineIds: this.selectedOutlineIds,
      selectedOutlineNodeIds: this.selectedOutlineNodeIds,
      outlineContextMode: this.outlineContextMode,
      bookId: this.bookId,
      conversationHistory: this.conversationHistory
    };

    // 创建结果变量
    let rewrittenText = '';

    // 调用AI改写服务
    const result = await aiRewriteService.rewriteContent(params, {
      onStart: () => {
        console.log('开始改写内容');
      },
      onStreamChunk: (chunk: string) => {
        rewrittenText += chunk;
      },
      onComplete: (result) => {
        console.log('改写内容完成', result);
        // 更新对话历史
        if (result.conversationHistory) {
          this.conversationHistory = result.conversationHistory;
        }
      },
      onError: (error) => {
        console.error('改写内容失败', error);
      }
    });

    return result.text;
  }

  /**
   * 渲染组件UI
   */
  render(): React.ReactNode {
    // 使用函数组件包装类组件的渲染逻辑
    const AIRewriteComponent = () => {
      const [selectedText, setSelectedText] = useState(this.selectedText);
      const [rewriteRequirements, setRewriteRequirements] = useState(this.rewriteRequirements);
      const [plot, setPlot] = useState(this.plot);
      const [rewrittenContent, setRewrittenContent] = useState('');
      const [isRewriting, setIsRewriting] = useState(false);
      const [streamingOutput, setStreamingOutput] = useState('');

      // 当组件属性变化时更新状态
      useEffect(() => {
        setSelectedText(this.selectedText);
      }, [this.selectedText]);

      const handleRewrite = async () => {
        this.setSelectedText(selectedText);
        this.setRewriteRequirements(rewriteRequirements);
        this.setPlot(plot);

        // 重置流式输出
        setStreamingOutput('');
        setIsRewriting(true);

        try {
          // 调用AI服务
          const result = await aiRewriteService.rewriteContent(
            {
              provider: this.apiSettings.getCurrentProvider(),
              model: this.apiSettings.getCurrentModel(),
              apiKey: this.apiSettings.getAPIKey(this.apiSettings.getCurrentProvider()),
              apiEndpoint: this.apiSettings.getAPIEndpoint(this.apiSettings.getCurrentProvider()),
              selectedText: selectedText,
              rewriteRequirements: rewriteRequirements,
              plot: plot,
              beforeContext: this.beforeContext,
              afterContext: this.afterContext,
              chapters: this.chapters,
              selectedChapterIds: this.selectedChapterIds,
              characters: this.characters,
              selectedCharacterIds: this.selectedCharacterIds,
              terminologies: this.terminologies,
              selectedTerminologyIds: this.selectedTerminologyIds,
              worldBuildings: this.worldBuildings,
              selectedWorldBuildingIds: this.selectedWorldBuildingIds,
              // 新增：大纲关联参数
              outlines: this.outlines,
              selectedOutlineIds: this.selectedOutlineIds,
              selectedOutlineNodeIds: this.selectedOutlineNodeIds,
              outlineContextMode: this.outlineContextMode,
              bookId: this.bookId,
              conversationHistory: this.conversationHistory
            },
            {
              onStart: () => {
                console.log('开始改写内容');
              },
              onStreamChunk: (chunk: string) => {
                // 更新流式输出
                setStreamingOutput(prev => prev + chunk);
              },
              onComplete: (result) => {
                console.log('改写内容完成', result);
                // 更新对话历史
                if (result.conversationHistory) {
                  this.conversationHistory = result.conversationHistory;
                }
                // 设置最终内容
                setRewrittenContent(result.text);
              },
              onError: (error) => {
                console.error('改写内容失败', error);
                alert(`改写失败: ${error.message}`);
              }
            }
          );
        } catch (error: any) {
          console.error('改写内容失败', error);
          alert(`改写失败: ${error.message}`);
        } finally {
          setIsRewriting(false);
        }
      };

      // 处理替换选中文本
      const handleReplaceText = () => {
        // 通过回调函数通知父组件替换选中文本
        if (this.onRewrittenCallback) {
          this.onRewrittenCallback(rewrittenContent);
        } else {
          alert('内容已生成，但未设置回调函数处理替换操作');
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">AI选中改写</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 左侧面板 - 输入区域 */}
            <div className="md:col-span-1">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  改写要求
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                  value={rewriteRequirements}
                  onChange={(e) => setRewriteRequirements(e.target.value)}
                  placeholder="请输入改写要求..."
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  剧情方向
                </label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                  value={plot}
                  onChange={(e) => setPlot(e.target.value)}
                  placeholder="请输入剧情方向..."
                />
              </div>

              <button
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
                onClick={handleRewrite}
                disabled={isRewriting || !selectedText || !rewriteRequirements}
              >
                {isRewriting ? '改写中...' : '改写内容'}
              </button>
            </div>

            {/* 中间面板 - 选中文本 */}
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选中的文本
              </label>
              <textarea
                className="w-full p-2 border border-gray-300 rounded h-64"
                value={selectedText}
                onChange={(e) => setSelectedText(e.target.value)}
                placeholder="请选择要改写的文本..."
                readOnly={!!this.selectedText}
              />
            </div>

            {/* 右侧面板 - 改写结果 */}
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                改写结果
              </label>
              <div className="p-3 bg-gray-50 rounded border border-gray-200 h-64 overflow-auto whitespace-pre-wrap">
                {isRewriting ? (
                  // 流式输出
                  <>
                    {streamingOutput || '正在生成改写内容...'}
                    <span className="inline-block animate-pulse">▌</span>
                  </>
                ) : (
                  // 最终结果
                  rewrittenContent || '改写结果将显示在这里...'
                )}
              </div>

              {rewrittenContent && !isRewriting && (
                <button
                  className="mt-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                  onClick={handleReplaceText}
                >
                  替换选中文本
                </button>
              )}
            </div>
          </div>
        </div>
      );
    };

    return <AIRewriteComponent />;
  }
}
