/**
 * 剧情点类型
 * 表示剧情节点中的一个具体剧情点
 */
export interface PlotPoint {
  /**
   * 剧情点唯一标识符
   */
  id: string;

  /**
   * 剧情点顺序
   */
  order: number;

  /**
   * 剧情点内容
   */
  content: string;

  /**
   * 避免描写（可选）
   * 明确列出在该剧情点中应该避免的写作方式和表达
   * 例如："避免使用'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'他xx他如何如何'的主观描述"
   */
  avoidWriting?: string;

  /**
   * 应该描写（可选）
   * 直接提供该剧情点的具体剧情内容示例，避免使用"他xx他如何如何"的主观描述
   * 应该是具体的场景描写，让读者能够直接代入，而不是通过"他"等主观词语
   * 例如："背靠着冰冷的断墙残垣，粗重地喘息，带着硝烟和血腥气的灼热气息喷在冰冷的石面上。"
   */
  shouldWriting?: string;

  /**
   * 剧情点类型（可选）
   */
  type?: 'setup' | 'conflict' | 'resolution' | 'twist';

  /**
   * 写作风格方法指导（可选）
   * 指导每个剧情点应该具有的具体写作风格和方法
   */
  styleMethod?: {
    /**
     * 写作技巧
     * 如："对比手法"、"细节描写"、"心理刻画"、"对话推进"
     */
    technique: string;

    /**
     * 风格特征
     * 如："简洁明快"、"细腻深沉"、"幽默诙谐"、"紧张激烈"
     */
    style: string;

    /**
     * 语调要求
     * 如："严肃"、"轻松"、"紧张"、"温馨"、"中性"
     */
    tone: string;

    /**
     * 视角要求
     * 如："第一人称"、"第三人称全知"、"第三人称限制视角"
     */
    perspective: string;

    /**
     * 重点强调
     * 如："动作描写"、"对话为主"、"环境渲染"、"心理活动"
     */
    emphasis: string;
  };

  /**
   * 格式规范（可选）
   * 控制每个剧情点的篇幅长度和格式要求
   */
  formatSpecs?: {
    /**
     * 字数要求
     */
    wordCount: {
      /**
       * 最少字数
       */
      min: number;

      /**
       * 最多字数
       */
      max: number;

      /**
       * 目标字数
       */
      target: number;
    };

    /**
     * 段落规范
     */
    paragraphRules: {
      /**
       * 每段最多句数
       */
      maxSentencesPerParagraph: number;

      /**
       * 分段规则
       * 如："动作完成后分段"、"对话独立成段"、"逻辑完整后分段"
       */
      paragraphBreakRules: string;

      /**
       * 冲突处理规范（可选）
       * 如："冲突升级时必须换行强调"、"禁止用段落分隔弱化冲突感"
       */
      conflictHandling?: string;

      /**
       * 对话行动流程（可选）
       * 如："严格执行对话→行动→对话节奏"、"行动不独立成段"
       */
      actionDialogueFlow?: string;

      /**
       * 强制换行要求（可选）
       * 如："情绪转折、场景切换、说话人变化必须换行"
       */
      mandatoryBreaks?: string;
    };

    /**
     * 标点规范
     */
    punctuationRules: {
      /**
       * 对话格式
       * 如："「」"、'""'、"『』"
       */
      dialogueFormat: string;

      /**
       * 强调格式
       * 如："加粗"、"斜体"、"适度使用"
       */
      emphasisFormat: string;

      /**
       * 停顿格式
       * 如："省略号表示停顿"、"自然停顿"、"逗号分隔"
       */
      pauseFormat: string;

      /**
       * 冲突标点规范（可选）
       * 如："冲突场面用短句+换行制造紧张感"、"不依赖标点符号强调"
       */
      conflictPunctuation?: string;

      /**
       * 自然流畅要求（可选）
       * 如："标点服务于阅读节奏"、"避免为了强调而强调"
       */
      naturalFlow?: string;
    };

    /**
     * 换行规范
     */
    lineBreakRules: {
      /**
       * 场景转换换行规则
       * 如："空行分隔"、"明确分隔"、"自然过渡"
       */
      sceneTransition: string;

      /**
       * 时间转换换行规则
       * 如："时间标记后换行"、"清晰标记"、"段落分隔"
       */
      timeTransition: string;

      /**
       * 说话人变化换行规则
       * 如："说话人变化时换行"、"独立成行"、"对话分段"
       */
      speakerChange: string;

      /**
       * 冲突升级换行（可选）
       * 如："冲突升级强制换行"、"每个冲突动作独立行，增强视觉冲击力"
       */
      conflictEscalation?: string;

      /**
       * 行动强调换行（可选）
       * 如："关键行动前后换行"、"重要动作要有视觉突出效果"
       */
      actionEmphasis?: string;

      /**
       * 情绪转折换行（可选）
       * 如："情绪转折点必须换行"、"从平静到愤怒要有明显视觉分割"
       */
      emotionShift?: string;

      /**
       * 禁止合并规范（可选）
       * 如："严禁将冲突行动与平常描写合并在同一行"
       */
      prohibitedMerging?: string;
    };
  };
}

/**
 * 对话项类型
 * 表示对话节点中的一个具体对话
 */
export interface DialogueItem {
  /**
   * 对话唯一标识符
   */
  id: string;

  /**
   * 说话者
   */
  speaker: string;

  /**
   * 对话内容
   */
  content: string;

  /**
   * 情感状态（可选）
   */
  emotion?: string;

  /**
   * 动作描述（可选）
   */
  action?: string;
}

/**
 * 剧情类型枚举
 */
export type PlotType = 'conflict' | 'twist' | 'climax' | 'resolution';

/**
 * 大纲节点类型
 * 表示大纲中的一个节点，可以是章节、剧情节点或对话节点
 */
export interface OutlineNodeType {
  /**
   * 节点唯一标识符
   */
  id: string;

  /**
   * 节点标题
   */
  title: string;

  /**
   * 节点描述（可选）
   */
  description?: string;

  /**
   * 创作建议（可选）
   * AI生成的针对此节点的创作指导，包含台词设计、心理描写、节奏控制等建议
   */
  creativeNotes?: string;

  /**
   * 节点类型
   * - volume: 总纲/卷 - 最高层级，包含多个章节，形成完整的故事循环（保留兼容性）
   * - event: 事件刚 - 最高层级，包含多个章节，以事件为单位组织剧情
   * - phaseGroup: 阶段分组 - 循环阶段的分组节点，包含同一阶段的章节
   * - chapter: 章节 - 包括内容的具体风格、手法，多个章节组成大纲
   * - plot: 剧情节点 - 单个章节内的具体剧情点列出
   * - dialogue: 对话节点 - 专注于对话设计
   * - synopsis: 核心故事梗概 - 整个故事的核心内容描述，不参与AI写作关联
   */
  type: 'volume' | 'event' | 'phaseGroup' | 'chapter' | 'plot' | 'dialogue' | 'synopsis';

  // ========== 总纲/卷特定字段 ==========
  /**
   * 卷主题（总纲专用）
   * 描述本卷的核心主题和故事弧线
   */
  volumeTheme?: string;

  /**
   * 卷弧线（总纲专用）
   * 本卷的整体故事发展弧线描述
   */
  volumeArc?: string;

  /**
   * 预期章节数（总纲专用）
   * 本卷计划包含的章节数量
   */
  chapterCount?: number;

  // ========== 事件刚特定字段 ==========
  /**
   * 事件起始描述（事件刚专用）
   * 描述事件的起始状态和触发背景
   */
  eventStart?: string;

  /**
   * 事件结束描述（事件刚专用）
   * 描述事件的结束状态和最终结果
   */
  eventEnd?: string;

  /**
   * 事件触发条件（事件刚专用）
   * 描述导致事件发生的具体条件和原因
   */
  eventTrigger?: string;

  /**
   * 事件结果影响（事件刚专用）
   * 描述事件结束后对后续剧情的影响
   */
  eventConsequence?: string;

  /**
   * 事件影响范围（事件刚专用）
   * 描述事件影响的人物、地点、时间范围
   */
  eventScope?: string;

  /**
   * 循环法模板（总纲专用）
   * 应用的微节奏循环模式，如"4章循环"、"自定义循环"等
   */
  cycleTemplate?: string;

  /**
   * 目标字数（总纲专用）
   * 本卷的目标总字数
   */
  targetWordCount?: number;

  /**
   * 节奏策略（总纲专用）
   * 整体的节奏控制策略和规划
   */
  rhythmStrategy?: string;

  /**
   * 循环阶段规划（总纲专用）
   * 各个循环阶段的具体规划
   */
  cyclePhases?: string[];

  /**
   * 循环阶段与章节映射（总纲专用）
   * 记录每个循环阶段对应的章节ID列表
   */
  cycleChapterMappings?: Array<{
    phase: string;
    chapterIds: string[];
  }>;

  // ========== 阶段分组特定字段 ==========
  /**
   * 阶段名称（阶段分组专用）
   * 循环阶段的名称，如："开篇建立"、"冲突发展"、"高潮爽点"、"过渡转折"
   */
  phaseName?: string;

  /**
   * 阶段描述（阶段分组专用）
   * 该阶段的详细描述和作用说明
   */
  phaseDescription?: string;

  /**
   * 阶段位置（阶段分组专用）
   * 在循环中的位置索引，从0开始
   */
  phasePosition?: number;

  /**
   * 阶段写作指导（阶段分组专用）
   * 该阶段的写作指导和要求
   */
  phaseWritingGuidance?: string;

  /**
   * 阶段创作要求（阶段分组专用）
   * 该阶段需要完成的具体要求列表
   */
  phaseCreativeRequirements?: string[];

  /**
   * 所属卷纲ID（阶段分组专用）
   * 指向父级卷纲节点的ID
   */
  parentVolumeId?: string;

  // ========== 章节特定字段 ==========
  /**
   * 写作风格（章节专用）
   * 描述本章节的写作风格，如：悬疑紧张、温馨治愈、激烈战斗等
   */
  chapterStyle?: string;

  /**
   * 写作手法（章节专用）
   * 本章节使用的写作技巧，如：倒叙、插叙、对比、象征等
   */
  chapterTechniques?: string[];

  /**
   * 章节目标（章节专用）
   * 本章节要达成的目标，如：推进主线剧情、展现角色成长、营造氛围等
   */
  chapterGoals?: string;

  /**
   * 节奏阶段（章节专用）
   * 当前章节在节奏分析中的阶段，如：setup、conflict、climax、transition
   */
  rhythmPhase?: string;

  /**
   * 节奏指导（章节专用）
   * 基于节奏阶段的具体创作指导
   */
  rhythmGuidance?: string;

  // ========== 章节循环法相关字段 ==========
  /**
   * 循环阶段（章节专用）
   * 当前章节在循环法中的阶段，如："开篇建立"、"冲突发展"、"高潮爽点"、"过渡转折"
   */
  cyclePhase?: string;

  /**
   * 阶段索引（章节专用）
   * 在循环中的位置索引，用于计算和排序
   */
  phaseIndex?: number;

  /**
   * 所属卷纲ID（章节专用）
   * 指向父级卷纲节点的ID，用于建立关联关系
   */
  volumeId?: string;

  /**
   * 阶段指导（章节专用）
   * 该循环阶段的具体写作指导和要求
   */
  phaseGuidance?: string;

  /**
   * 阶段要求（章节专用）
   * 该阶段需要完成的具体要求列表
   */
  phaseRequirements?: string[];

  // ========== 剧情节点特定字段 ==========
  /**
   * 剧情点列表（剧情节点专用）
   * 结构化的剧情点，支持排序和分类
   */
  plotPoints?: PlotPoint[];

  /**
   * 剧情类型（剧情节点专用）
   * 该剧情节点的主要类型
   */
  plotType?: PlotType;

  /**
   * 关联角色（剧情节点专用）
   * 参与此剧情的角色ID列表
   */
  relatedCharacters?: string[];

  /**
   * 冲突强度（剧情节点专用）
   * 该剧情节点的冲突强度等级，1-5级
   */
  conflictLevel?: number;

  /**
   * 悬念要素（剧情节点专用）
   * 该剧情节点包含的悬念要素列表
   */
  suspenseElements?: string[];

  // ========== 核心故事梗概特定字段 ==========
  /**
   * 脑洞（核心故事梗概专用）
   * 描述故事的核心创意和独特想法
   */
  synopsisBrainhole?: string;

  /**
   * 类型（核心故事梗概专用）
   * 故事的类型标签，如：悬疑、言情、科幻、历史等
   */
  synopsisGenre?: string;

  /**
   * 开头（核心故事梗概专用）
   * 虎头开局，强冲击力场景+悬念设置+读者期待
   */
  synopsisOpening?: string;

  /**
   * 梗概（核心故事梗概专用）
   * 对核心梗的使用，对核心的概况，核心极致
   */
  synopsisCoreOutline?: string;

  /**
   * 结尾（核心故事梗概专用）
   * 细思极恐式结尾，反转揭秘+留恋设想+后续期待
   */
  synopsisEnding?: string;

  /**
   * 故事（核心故事梗概专用）
   * 准备讲一个完整的什么故事
   */
  synopsisStoryDescription?: string;

  /**
   * 引用（核心故事梗概专用）
   * 引用了那些ACE框架的灵感
   */
  synopsisAceReferences?: string;

  // ========== 对话设计特定字段 ==========
  /**
   * 对话场景（对话节点专用）
   * 对话发生的场景描述
   */
  dialogueScene?: string;

  /**
   * 参与角色（对话节点专用）
   * 参与对话的角色列表
   */
  participants?: string[];

  /**
   * 对话目的（对话节点专用）
   * 这段对话要达成的目的，如：信息传递、情感表达、冲突升级等
   */
  dialoguePurpose?: string;

  /**
   * 对话内容（对话节点专用）
   * 结构化的对话内容
   */
  dialogueContent?: DialogueItem[];

  /**
   * 个性化对话标记（对话节点专用）
   * 简单标记是否需要个性化对话处理
   */
  personalizedDialogue?: boolean;

  /**
   * 子节点数组（可选）
   */
  children?: OutlineNodeType[];

  /**
   * 是否展开（可选）
   */
  expanded?: boolean;

  /**
   * 节点在画布中的位置（可选）
   * 由拖拽操作设置，用于保持布局稳定
   */
  position?: {
    x: number;
    y: number;
  };

  /**
   * 前一个同级节点ID（可选）
   * 用于维护节点间的顺序关系
   */
  prevSiblingId?: string;

  /**
   * 后一个同级节点ID（可选）
   * 用于维护节点间的顺序关系
   */
  nextSiblingId?: string;

  /**
   * 父节点ID（可选）
   * 用于快速查找父节点
   */
  parentId?: string;

  /**
   * 关联的人物ID数组（可选）
   */
  relatedCharacterIds?: string[];

  /**
   * 关联的世界观ID数组（可选）
   */
  relatedWorldBuildingIds?: string[];

  /**
   * 关联的术语ID数组（可选）
   */
  relatedTerminologyIds?: string[];

  /**
   * 额外元数据（可选）
   * 可以包含关联强度、关联备注等信息
   */
  metadata?: Record<string, any>;

  /**
   * 是否为新创建的节点（可选）
   * 用于添加创建动画效果
   */
  isNew?: boolean;

  /**
   * 是否折叠子节点（可选）
   * 用于性能优化，折叠后子节点不渲染
   */
  isCollapsed?: boolean;

  /**
   * 子节点数量统计（可选）
   * 用于在折叠状态下显示子节点信息
   */
  childrenCount?: number;

  /**
   * 可见子节点数量（可选）
   * 用于统计当前可见的子节点数量
   */
  visibleChildrenCount?: number;

  /**
   * 折叠的子节点类型统计（可选）
   * 用于在折叠状态下显示子节点类型分布
   */
  collapsedChildrenTypes?: { [key: string]: number };
}

/**
 * 大纲类型
 * 表示整个大纲的数据结构
 */
export interface Outline {
  /**
   * 大纲唯一标识符
   */
  id: string;

  /**
   * 关联的作品ID
   */
  workId: string;

  /**
   * 大纲标题
   */
  title: string;

  /**
   * 根节点数组
   */
  nodes: OutlineNodeType[];

  /**
   * 最后修改时间
   */
  lastModified: Date;

  /**
   * 版本号
   */
  version: number;
}

/**
 * 大纲版本类型
 * 表示大纲的一个历史版本
 */
export interface OutlineVersion {
  /**
   * 版本唯一标识符
   */
  id: string;

  /**
   * 关联的大纲ID
   */
  outlineId: string;

  /**
   * 版本号
   */
  version: number;

  /**
   * 大纲数据
   */
  data: Outline;

  /**
   * 创建时间
   */
  createdAt: Date;

  /**
   * 创建者
   */
  createdBy: string;

  /**
   * 版本注释（可选）
   */
  comment?: string;
}
