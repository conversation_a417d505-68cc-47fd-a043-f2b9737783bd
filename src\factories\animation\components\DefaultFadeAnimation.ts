import { IFadeAnimation, FadeDirection } from '../interfaces';

/**
 * 默认淡入淡出动画实现
 */
export class DefaultFadeAnimation implements IFadeAnimation {
  private direction: FadeDirection = 'none';
  private duration: number = 300;
  private delay: number = 0;
  private visible: boolean = true;
  
  /**
   * 设置动画方向
   * @param direction 方向
   */
  setDirection(direction: FadeDirection): void {
    this.direction = direction;
  }
  
  /**
   * 设置动画持续时间
   * @param duration 持续时间（毫秒）
   */
  setDuration(duration: number): void {
    this.duration = duration;
  }
  
  /**
   * 设置动画延迟
   * @param delay 延迟时间（毫秒）
   */
  setDelay(delay: number): void {
    this.delay = delay;
  }
  
  /**
   * 设置是否显示
   * @param visible 是否显示
   */
  setVisible(visible: boolean): void {
    this.visible = visible;
  }
  
  /**
   * 获取CSS类名
   */
  getClassName(): string {
    const baseClass = 'transition-all';
    const visibilityClass = this.visible ? 'opacity-100' : 'opacity-0';
    
    let transformClass = '';
    if (!this.visible) {
      switch (this.direction) {
        case 'up':
          transformClass = 'translate-y-4';
          break;
        case 'down':
          transformClass = '-translate-y-4';
          break;
        case 'left':
          transformClass = 'translate-x-4';
          break;
        case 'right':
          transformClass = '-translate-x-4';
          break;
        default:
          transformClass = '';
          break;
      }
    }
    
    return `${baseClass} ${visibilityClass} ${transformClass}`;
  }
  
  /**
   * 获取CSS样式
   */
  getStyle(): React.CSSProperties {
    return {
      transitionProperty: 'opacity, transform',
      transitionDuration: `${this.duration}ms`,
      transitionTimingFunction: 'ease-out',
      transitionDelay: `${this.delay}ms`,
    };
  }
}
