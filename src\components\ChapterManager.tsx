"use client";

import React, { useState, useEffect } from 'react';
import { dataRepairUtils } from '@/lib/utils/dataRepair';
import { ChapterRepository } from '@/db/chapterRepository';
import { Chapter } from '@/db/database';

interface ChapterManagerProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  onChaptersUpdated: (chapters: Chapter[]) => void;
}

interface DuplicateInfo {
  order: number;
  chapters: Chapter[];
}

export function ChapterManager({ bookId, isOpen, onClose, onChaptersUpdated }: ChapterManagerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [duplicates, setDuplicates] = useState<DuplicateInfo[]>([]);
  const [integrityReport, setIntegrityReport] = useState<any>(null);
  const [repairResults, setRepairResults] = useState<string[]>([]);
  const chapterRepository = new ChapterRepository();

  // 检测重复章节
  const detectDuplicates = async () => {
    setIsLoading(true);
    try {
      const result = await dataRepairUtils.detectDuplicateChapters(bookId);
      setDuplicates(result.duplicates);
      
      const integrity = await dataRepairUtils.checkDataIntegrity(bookId);
      setIntegrityReport(integrity);
    } catch (error) {
      console.error('检测重复章节失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 删除指定章节
  const deleteChapter = async (chapterId: string) => {
    try {
      const success = await chapterRepository.deleteChapter(chapterId);
      if (success) {
        // 重新检测
        await detectDuplicates();
        
        // 更新父组件的章节列表
        const updatedChapters = await chapterRepository.getChaptersByBookId(bookId);
        onChaptersUpdated(updatedChapters);
      }
    } catch (error) {
      console.error('删除章节失败:', error);
      alert('删除章节失败，请重试');
    }
  };

  // 自动修复
  const autoRepair = async () => {
    setIsLoading(true);
    try {
      const result = await dataRepairUtils.autoRepairData(bookId);
      setRepairResults(result.results);
      
      // 重新检测
      await detectDuplicates();
      
      // 更新父组件的章节列表
      const updatedChapters = await chapterRepository.getChaptersByBookId(bookId);
      onChaptersUpdated(updatedChapters);
      
      alert(result.message);
    } catch (error) {
      console.error('自动修复失败:', error);
      alert('自动修复失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件打开时检测重复章节
  useEffect(() => {
    if (isOpen) {
      detectDuplicates();
    }
  }, [isOpen, bookId]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800">章节管理</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">检测中...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 数据完整性报告 */}
            {integrityReport && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">数据完整性报告</h3>
                <div className="flex items-center mb-2">
                  <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                    integrityReport.isHealthy ? 'bg-green-500' : 'bg-red-500'
                  }`}></span>
                  <span className="text-sm">
                    {integrityReport.isHealthy ? '数据健康' : '发现问题'}
                  </span>
                  <span className="ml-4 text-sm text-gray-600">
                    总章节数: {integrityReport.totalChapters}
                  </span>
                </div>
                {integrityReport.issues.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-red-600 mb-1">发现的问题:</p>
                    <ul className="text-sm text-red-600 space-y-1">
                      {integrityReport.issues.map((issue: string, index: number) => (
                        <li key={index}>• {issue}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* 重复章节列表 */}
            {duplicates.length > 0 ? (
              <div>
                <h3 className="font-medium text-gray-800 mb-4">发现重复章节</h3>
                <div className="space-y-4">
                  {duplicates.map((duplicate, index) => (
                    <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                      <h4 className="font-medium text-red-800 mb-3">
                        第 {duplicate.order + 1} 章 (顺序: {duplicate.order}) - {duplicate.chapters.length} 个重复
                      </h4>
                      <div className="space-y-2">
                        {duplicate.chapters.map((chapter, chapterIndex) => (
                          <div key={chapter.id} className="flex items-center justify-between bg-white p-3 rounded border">
                            <div className="flex-1">
                              <div className="font-medium">{chapter.title}</div>
                              <div className="text-sm text-gray-600">
                                创建时间: {new Date(chapter.createdAt).toLocaleString()}
                              </div>
                              <div className="text-sm text-gray-600">
                                字数: {chapter.wordCount} | ID: {chapter.id}
                              </div>
                              {chapter.content && (
                                <div className="text-sm text-gray-500 mt-1">
                                  内容预览: {chapter.content.substring(0, 100)}...
                                </div>
                              )}
                            </div>
                            <button
                              onClick={() => deleteChapter(chapter.id!)}
                              className="ml-4 px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
                            >
                              删除
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-600">
                <svg className="w-16 h-16 mx-auto mb-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p>没有发现重复章节</p>
              </div>
            )}

            {/* 修复结果 */}
            {repairResults.length > 0 && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-800 mb-2">修复结果</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  {repairResults.map((result, index) => (
                    <li key={index}>• {result}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-between pt-4 border-t">
              <button
                onClick={detectDuplicates}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                重新检测
              </button>
              
              {(duplicates.length > 0 || (integrityReport && !integrityReport.isHealthy)) && (
                <button
                  onClick={autoRepair}
                  disabled={isLoading}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                >
                  自动修复
                </button>
              )}
              
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                关闭
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
