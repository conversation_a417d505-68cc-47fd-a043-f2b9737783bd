import { userSettingsService, userStateService } from './DataService';
import { historyService } from './HistoryService';

/**
 * localStorage迁移结果
 */
export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  errors: string[];
  details: {
    userSettings: number;
    userStates: number;
    historyRecords: number;
  };
}

/**
 * localStorage数据迁移器
 * 将localStorage中的数据迁移到IndexedDB
 */
export class LocalStorageMigrator {
  private readonly MIGRATION_FLAG = 'localStorage-migrated-v1';
  
  /**
   * 检查是否已经迁移过
   */
  async isMigrated(): Promise<boolean> {
    try {
      const flag = await userSettingsService.get('system', 'migration-flag');
      return flag === this.MIGRATION_FLAG;
    } catch {
      return false;
    }
  }

  /**
   * 执行完整迁移
   */
  async migrateAll(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedCount: 0,
      errors: [],
      details: {
        userSettings: 0,
        userStates: 0,
        historyRecords: 0
      }
    };

    try {
      console.log('🔄 开始localStorage数据迁移...');

      // 检查是否已迁移
      if (await this.isMigrated()) {
        console.log('✅ 数据已经迁移过，跳过迁移');
        result.success = true;
        return result;
      }

      // 迁移用户设置
      const settingsResult = await this.migrateUserSettings();
      result.details.userSettings = settingsResult.count;
      result.errors.push(...settingsResult.errors);

      // 迁移用户状态
      const statesResult = await this.migrateUserStates();
      result.details.userStates = statesResult.count;
      result.errors.push(...statesResult.errors);

      // 迁移历史记录
      const historyResult = await this.migrateHistoryRecords();
      result.details.historyRecords = historyResult.count;
      result.errors.push(...historyResult.errors);

      result.migratedCount = result.details.userSettings + 
                            result.details.userStates + 
                            result.details.historyRecords;

      // 标记迁移完成
      if (result.errors.length === 0) {
        await userSettingsService.set('system', 'migration-flag', this.MIGRATION_FLAG);
        result.success = true;
        
        // 可选：清理localStorage（谨慎操作）
        // await this.cleanupLocalStorage();
        
        console.log(`✅ 迁移完成，共迁移 ${result.migratedCount} 条数据`);
      } else {
        console.warn(`⚠️ 迁移完成但有错误，共迁移 ${result.migratedCount} 条数据，错误数: ${result.errors.length}`);
      }

    } catch (error) {
      console.error('❌ 迁移过程发生错误:', error);
      result.errors.push(`迁移过程错误: ${error}`);
    }

    return result;
  }

  /**
   * 迁移用户设置
   */
  private async migrateUserSettings(): Promise<{ count: number; errors: string[] }> {
    const result = { count: 0, errors: [] as string[] };

    // 定义需要迁移的设置映射
    const settingsMap = [
      // API设置
      { key: 'api_settings', category: 'api', targetKey: 'settings' },
      { key: 'dual-ai-config', category: 'dual-ai', targetKey: 'config' },
      { key: 'dual-ai-state', category: 'dual-ai', targetKey: 'state' },
      
      // 宠物设置
      { key: 'writing-pet-preferences', category: 'pet', targetKey: 'preferences' },
      
      // AI标注偏好
      { key: 'ai-annotation-preferences', category: 'annotation', targetKey: 'preferences' },
      { key: 'ai-annotation-history', category: 'annotation', targetKey: 'history' },
      
      // 推荐增强器配置
      { key: 'recommendation-enhancer-config', category: 'recommendation', targetKey: 'config' },
      
      // 其他全局设置
      { key: 'aiConfig', category: 'ai', targetKey: 'config' },
    ];

    for (const mapping of settingsMap) {
      try {
        const value = localStorage.getItem(mapping.key);
        if (value) {
          const parsedValue = JSON.parse(value);
          await userSettingsService.set(mapping.category, mapping.targetKey, parsedValue);
          result.count++;
          console.log(`✅ 迁移设置: ${mapping.key} -> ${mapping.category}:${mapping.targetKey}`);
        }
      } catch (error) {
        const errorMsg = `迁移设置失败 ${mapping.key}: ${error}`;
        result.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return result;
  }

  /**
   * 迁移用户状态
   */
  private async migrateUserStates(): Promise<{ count: number; errors: string[] }> {
    const result = { count: 0, errors: [] as string[] };

    // 获取所有localStorage键
    const allKeys = Object.keys(localStorage);
    
    // 定义状态键模式
    const statePatterns = [
      // AI助手相关状态
      { pattern: /^ai-assistant-selected-chapters-(.+)$/, category: 'assistant', key: 'selected-chapters' },
      { pattern: /^ai-assistant-selected-characters-(.+)$/, category: 'assistant', key: 'selected-characters' },
      { pattern: /^ai-assistant-selected-terminologies-(.+)$/, category: 'assistant', key: 'selected-terminologies' },
      { pattern: /^ai-assistant-selected-worldbuildings-(.+)$/, category: 'assistant', key: 'selected-worldbuildings' },
      { pattern: /^ai-assistant-last-updated-(.+)$/, category: 'assistant', key: 'last-updated' },
      { pattern: /^assistant-selected-chapters-(.+)$/, category: 'assistant', key: 'selected-chapters-v2' },
      
      // 框架相关状态
      { pattern: /^outline-ai-framework-(.+)$/, category: 'framework', key: 'selected-framework' },
      { pattern: /^outline-ai-frameworks-(.+)$/, category: 'framework', key: 'selected-frameworks' },
      
      // 布局相关状态
      { pattern: /^collapsed-nodes-(.+)$/, category: 'layout', key: 'collapsed-nodes' },
      { pattern: /^outlineLayoutHistory$/, category: 'layout', key: 'history', bookId: null },
      
      // 世界观相关状态
      { pattern: /^worldbuilding-chapter-configs-(.+)$/, category: 'worldbuilding', key: 'chapter-configs' },
      
      // 其他特殊状态
      { pattern: /^book-title-special-requirements$/, category: 'brainstorm', key: 'title-requirements', bookId: null },
    ];

    for (const key of allKeys) {
      for (const pattern of statePatterns) {
        const match = key.match(pattern.pattern);
        if (match) {
          try {
            const value = localStorage.getItem(key);
            if (value) {
              const parsedValue = JSON.parse(value);
              const bookId = pattern.bookId !== undefined ? pattern.bookId : match[1];
              
              await userStateService.set(bookId, pattern.category, pattern.key, parsedValue);
              result.count++;
              console.log(`✅ 迁移状态: ${key} -> ${bookId}:${pattern.category}:${pattern.key}`);
            }
          } catch (error) {
            const errorMsg = `迁移状态失败 ${key}: ${error}`;
            result.errors.push(errorMsg);
            console.error(errorMsg);
          }
          break; // 匹配到一个模式就跳出
        }
      }
    }

    return result;
  }

  /**
   * 迁移历史记录
   */
  private async migrateHistoryRecords(): Promise<{ count: number; errors: string[] }> {
    const result = { count: 0, errors: [] as string[] };

    // 定义历史记录映射
    const historyMap = [
      // 全局历史记录
      { key: 'book-title-history', type: 'title', bookId: null },
      { key: 'synopsis-history', type: 'synopsis', bookId: null },
      { key: 'book-title-keywords', type: 'title-keywords', bookId: null },
      { key: 'book-title-frameworks', type: 'title-frameworks', bookId: null },
      { key: 'synopsis-frameworks', type: 'synopsis-frameworks', bookId: null },
      
      // 思考画布（特殊处理）
      { key: 'thinking-canvases', type: 'thinking-canvas', bookId: null },
    ];

    for (const mapping of historyMap) {
      try {
        const value = localStorage.getItem(mapping.key);
        if (value) {
          const parsedValue = JSON.parse(value);
          
          if (Array.isArray(parsedValue)) {
            // 数组类型的历史记录，逐个添加
            for (const item of parsedValue) {
              await historyService.add(mapping.bookId, mapping.type, item);
              result.count++;
            }
          } else {
            // 单个对象，直接添加
            await historyService.add(mapping.bookId, mapping.type, parsedValue);
            result.count++;
          }
          
          console.log(`✅ 迁移历史: ${mapping.key} -> ${mapping.type} (${Array.isArray(parsedValue) ? parsedValue.length : 1} 条)`);
        }
      } catch (error) {
        const errorMsg = `迁移历史失败 ${mapping.key}: ${error}`;
        result.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    // 迁移书籍相关的历史记录
    const allKeys = Object.keys(localStorage);
    const bookHistoryPatterns = [
      { pattern: /^ai-writing-history-(.+)$/, type: 'writing' },
      { pattern: /^ai-rewrite-history-(.+)$/, type: 'rewrite' },
    ];

    for (const key of allKeys) {
      for (const pattern of bookHistoryPatterns) {
        const match = key.match(pattern.pattern);
        if (match) {
          try {
            const value = localStorage.getItem(key);
            if (value) {
              const parsedValue = JSON.parse(value);
              const bookId = match[1];
              
              if (Array.isArray(parsedValue)) {
                for (const item of parsedValue) {
                  await historyService.add(bookId, pattern.type, item);
                  result.count++;
                }
              } else {
                await historyService.add(bookId, pattern.type, parsedValue);
                result.count++;
              }
              
              console.log(`✅ 迁移书籍历史: ${key} -> ${bookId}:${pattern.type}`);
            }
          } catch (error) {
            const errorMsg = `迁移书籍历史失败 ${key}: ${error}`;
            result.errors.push(errorMsg);
            console.error(errorMsg);
          }
          break;
        }
      }
    }

    return result;
  }

  /**
   * 清理localStorage（可选，谨慎使用）
   */
  private async cleanupLocalStorage(): Promise<void> {
    // 这里可以选择性地清理已迁移的localStorage数据
    // 为了安全起见，暂时不自动清理
    console.log('💡 localStorage数据保留，可手动清理');
  }

  /**
   * 强制重新迁移
   */
  async forceMigrate(): Promise<MigrationResult> {
    await userSettingsService.remove('system', 'migration-flag');
    return this.migrateAll();
  }
}

// 导出单例实例
export const localStorageMigrator = new LocalStorageMigrator();
