"use client";

import React, { useState } from 'react';

interface MentionUsageGuideProps {
  isVisible: boolean;
  onClose: () => void;
}

/**
 * @功能使用指南组件
 * 帮助用户了解如何使用@功能
 */
const MentionUsageGuide: React.FC<MentionUsageGuideProps> = ({
  isVisible,
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "什么是@功能？",
      content: (
        <div className="guide-content">
          <p>@功能让您可以在AI对话中快速引用作品中的各种元素：</p>
          <ul>
            <li>👤 <strong>人物角色</strong> - 引用人物信息和设定</li>
            <li>🌍 <strong>世界观</strong> - 引用世界设定和背景</li>
            <li>📚 <strong>术语</strong> - 引用专有名词和概念</li>
            <li>📖 <strong>章节</strong> - 引用具体的章节内容</li>
            <li>📝 <strong>大纲节点</strong> - 引用大纲中的场景和笔记</li>
          </ul>
          <p>这样AI就能更好地理解您的作品背景，提供更准确的建议。</p>
        </div>
      )
    },
    {
      title: "如何使用@功能？",
      content: (
        <div className="guide-content">
          <div className="step-demo">
            <div className="demo-input">
              <span>在对话中输入 </span>
              <code>@</code>
              <span> 符号</span>
            </div>
            <div className="demo-arrow">↓</div>
            <div className="demo-selector">
              <div className="demo-item">👤 张三 (人物)</div>
              <div className="demo-item">🌍 魔法世界 (世界观)</div>
              <div className="demo-item">📚 魔法系统 (术语)</div>
            </div>
          </div>
          <p>选择器会自动弹出，显示可引用的内容。您可以：</p>
          <ul>
            <li>🔍 输入关键词搜索</li>
            <li>⌨️ 使用方向键导航</li>
            <li>✅ 按Enter确认选择</li>
            <li>⚡ 按Tab快速选择第一项</li>
          </ul>
        </div>
      )
    },
    {
      title: "如何创建可@的内容？",
      content: (
        <div className="guide-content">
          <p>要使用@功能，您需要先创建相应的数据：</p>
          <div className="creation-steps">
            <div className="creation-item">
              <div className="creation-icon">👤</div>
              <div className="creation-info">
                <h4>创建人物</h4>
                <p>在人物管理面板中添加角色信息</p>
              </div>
            </div>
            <div className="creation-item">
              <div className="creation-icon">🌍</div>
              <div className="creation-info">
                <h4>构建世界观</h4>
                <p>在世界观面板中添加设定和背景</p>
              </div>
            </div>
            <div className="creation-item">
              <div className="creation-icon">📚</div>
              <div className="creation-info">
                <h4>定义术语</h4>
                <p>在术语面板中添加专有名词解释</p>
              </div>
            </div>
            <div className="creation-item">
              <div className="creation-icon">📖</div>
              <div className="creation-info">
                <h4>编写章节</h4>
                <p>在章节编辑器中创建内容</p>
              </div>
            </div>
          </div>
          <div className="tip">
            💡 <strong>提示：</strong>创建数据后，它们会自动出现在@选择器中
          </div>
        </div>
      )
    },
    {
      title: "使用技巧",
      content: (
        <div className="guide-content">
          <div className="tips-grid">
            <div className="tip-item">
              <div className="tip-icon">🔍</div>
              <h4>智能搜索</h4>
              <p>支持模糊搜索和拼音搜索，输入部分关键词即可找到内容</p>
            </div>
            <div className="tip-item">
              <div className="tip-icon">📊</div>
              <h4>使用统计</h4>
              <p>经常使用的内容会优先显示，提升选择效率</p>
            </div>
            <div className="tip-item">
              <div className="tip-icon">⚡</div>
              <h4>快捷键</h4>
              <p>使用Tab、Enter、方向键等快捷键快速操作</p>
            </div>
            <div className="tip-item">
              <div className="tip-icon">🎯</div>
              <h4>精确引用</h4>
              <p>@引用的内容会被AI准确理解，提供更相关的回答</p>
            </div>
          </div>
          <div className="final-tip">
            <strong>开始使用：</strong>现在就在AI对话中输入 <code>@</code> 试试吧！
          </div>
        </div>
      )
    }
  ];

  if (!isVisible) return null;

  const currentStepData = steps[currentStep];

  return (
    <div className="mention-usage-guide">
      <div className="guide-overlay" onClick={onClose}></div>
      <div className="guide-content-wrapper">
        <div className="guide-header">
          <h3>@功能使用指南</h3>
          <button className="guide-close" onClick={onClose}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="guide-body">
          <div className="guide-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
            <span className="progress-text">
              {currentStep + 1} / {steps.length}
            </span>
          </div>

          <div className="guide-step">
            <h4>{currentStepData.title}</h4>
            {currentStepData.content}
          </div>
        </div>

        <div className="guide-footer">
          <button
            className="guide-button secondary"
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            上一步
          </button>
          
          {currentStep < steps.length - 1 ? (
            <button
              className="guide-button primary"
              onClick={() => setCurrentStep(currentStep + 1)}
            >
              下一步
            </button>
          ) : (
            <button
              className="guide-button primary"
              onClick={onClose}
            >
              开始使用
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MentionUsageGuide;
