/**
 * AI创意助手服务
 * 提供AI创意生成和聊天功能
 */

import { BrainstormPromptBuilder, BrainstormParams } from './brainstorm/builders/BrainstormPromptBuilder';
import { AssociationData } from '@/components/brainstorm/types';
import { configService } from '@/services/configService';
import { ToolCallHandler, ToolCallInstruction } from './brainstorm/ToolCallHandler';
import { GlobalJSONFilter, StreamingJSONFilter } from './brainstorm/StreamingJSONFilter';
import { ToolCallResult, BookTitleResult } from '@/components/ui/ToolCallResultBubble';
import { RecommendationEnhancer, EnhancedRecommendationData } from '@/utils/ai/RecommendationEnhancer';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';

export interface BrainstormResponse {
  success: boolean;
  content: string;
  error?: string;
  toolCallResult?: ToolCallResult;
}

export interface CreativitySolution {
  title: string;
  concept: string;
  highlights: string[];
  implementation: string;
  marketPotential: string;
  development: string;
  score: number;
}

export interface CreativityResult {
  solutions: CreativitySolution[];
  summary: string;
  recommendations: string;
}

export class BrainstormAIService extends UnifiedAIService {
  constructor() {
    super(AIServiceType.BRAINSTORM);
  }

  /**
   * 检测AI响应中的工具调用指令
   */
  private detectToolCall(content: string): ToolCallInstruction | null {
    try {
      // 匹配JSON代码块中的工具调用指令
      const toolCallPattern = /```json\s*(\{[\s\S]*?"action"[\s\S]*?\})\s*```/;
      const match = content.match(toolCallPattern);

      if (match) {
        const instruction = JSON.parse(match[1]);
        if (instruction.action && instruction.parameters) {
          return {
            ...instruction,
            requestId: Date.now().toString()
          };
        }
      }

      // 也尝试匹配没有代码块的JSON
      const directJsonPattern = /\{[\s\S]*?"action"[\s\S]*?\}/;
      const directMatch = content.match(directJsonPattern);

      if (directMatch) {
        const instruction = JSON.parse(directMatch[0]);
        if (instruction.action && instruction.parameters) {
          return {
            ...instruction,
            requestId: Date.now().toString()
          };
        }
      }

      // 🌍 新增：检测世界观管理关键词
      const worldViewKeywords = [
        '世界观', '世界设定', '背景设定', '世界构建',
        '魔法体系', '科技体系', '政治制度', '文化设定'
      ];

      const managementKeywords = ['管理', '查看', '选择', '列表', '发送', '使用', '引用'];
      const creationKeywords = ['创建', '生成', '设计', '构建'];

      const hasWorldViewKeyword = worldViewKeywords.some(keyword => content.includes(keyword));
      const hasManagementKeyword = managementKeywords.some(keyword => content.includes(keyword));
      const hasCreationKeyword = creationKeywords.some(keyword => content.includes(keyword));

      if (hasWorldViewKeyword) {
        if (hasManagementKeyword) {
          // 检测到世界观管理需求
          return {
            action: 'manage_worldview',
            parameters: {
              action: 'list',
              bookId: this.extractBookIdFromContext()
            },
            requestId: Date.now().toString()
          };
        } else if (hasCreationKeyword) {
          // 检测到世界观创建需求
          const worldType = this.detectWorldType(content);
          const complexity = this.detectComplexity(content);
          const keywords = this.extractKeywordsFromContent(content);

          return {
            action: 'create_worldview',
            parameters: {
              worldType,
              complexity,
              keywords,
              requirements: content
            },
            requestId: Date.now().toString()
          };
        }
      }

    } catch (error) {
      console.log('工具调用指令解析失败:', error);
    }

    return null;
  }



  /**
   * 检测用户输入是否包含书名相关关键词
   */
  private detectBookTitleKeywords(userInput: string): boolean {
    const bookTitleKeywords = [
      '书名', '标题', '取名', '起名', '命名', '叫什么', '什么名字',
      '生成书名', '推荐书名', '书的名字', '小说名', '作品名',
      '题目', '名称', '书籍名称'
    ];

    return bookTitleKeywords.some(keyword =>
      userInput.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 检测用户输入是否包含简介相关关键词
   */
  private detectSynopsisKeywords(userInput: string): boolean {
    const synopsisKeywords = [
      '简介', '梗概', '内容简介', '作品简介', '故事简介',
      '剧情简介', '小说简介', '书籍简介', '概述', '摘要',
      '大纲', '故事大纲', '情节概述', '内容概要',
      '生成简介', '写简介', '简介生成', '创作简介',
      'generate_synopsis', 'synopsis', '简述', '介绍'
    ];

    return synopsisKeywords.some(keyword =>
      userInput.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 生成关键词和框架推荐信息（传递给AI）
   */
  private async generateRecommendationsForAI(): Promise<string> {
    try {
      // 动态导入关键词和框架数据
      const { PRESET_KEYWORDS, PRESET_FRAMEWORKS } = await import('./brainstorm/index');

      // 🔥 修复：合并预制关键词和用户保存的关键词
      let allKeywords = [...PRESET_KEYWORDS];

      // 读取用户保存的关键词
      try {
        const savedKeywords = localStorage.getItem('book-title-keywords');
        if (savedKeywords) {
          const userKeywords = JSON.parse(savedKeywords);
          console.log('🔍 读取到用户保存的关键词:', userKeywords.length, '个');
          allKeywords = [...allKeywords, ...userKeywords];
        }
      } catch (error) {
        console.error('读取用户关键词失败:', error);
      }

      // 提取所有关键词，按热度排序
      const hotKeywords = allKeywords
        .sort((a, b) => (b.hotness || 0) - (a.hotness || 0))
        .map(k => k.text);

      // 🔥 修复：合并预制框架和用户保存的框架
      let allFrameworks = [...PRESET_FRAMEWORKS];

      // 读取用户保存的框架
      try {
        const savedFrameworks = localStorage.getItem('book-title-frameworks');
        if (savedFrameworks) {
          const userFrameworks = JSON.parse(savedFrameworks);
          console.log('🔍 读取到用户保存的框架:', userFrameworks.length, '个');
          allFrameworks = [...allFrameworks, ...userFrameworks];
        }
      } catch (error) {
        console.error('读取用户框架失败:', error);
      }

      // 提取所有框架，按效果排序
      const topFrameworks = allFrameworks
        .sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0));

      return `[系统提供的书名创作资源]
可用热门关键词：${hotKeywords.join('、')}

可用高效框架：
${topFrameworks.map(f => `• ${f.name}(${f.pattern})`).join('\n')}

请根据用户需求，优先使用这些关键词和框架来调用书名生成工具。`;
    } catch (error) {
      console.error('生成AI推荐信息失败:', error);
      return '';
    }
  }

  /**
   * 生成关键词和框架推荐信息（显示给用户）
   */
  private async generateRecommendationsForUser(): Promise<string> {
    try {
      // 动态导入关键词和框架数据
      const { PRESET_KEYWORDS, PRESET_FRAMEWORKS } = await import('./brainstorm/index');

      // 🔥 修复：合并预制关键词和用户保存的关键词
      let allKeywords = [...PRESET_KEYWORDS];

      // 读取用户保存的关键词
      try {
        const savedKeywords = localStorage.getItem('book-title-keywords');
        if (savedKeywords) {
          const userKeywords = JSON.parse(savedKeywords);
          allKeywords = [...allKeywords, ...userKeywords];
        }
      } catch (error) {
        console.error('读取用户关键词失败:', error);
      }

      // 🔥 修复：优先显示用户关键词，减少数量限制
      // 分离用户关键词和预设关键词
      const userKeywords = allKeywords.filter(k => (k as any).source === 'user-saved' || (k as any).source === 'analysis');
      const presetKeywords = allKeywords.filter(k => !(k as any).source || (k as any).source === 'preset');

      // 优先排序：用户关键词在前，预设关键词适当限制
      const prioritizedKeywords = [
        ...userKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)), // 用户关键词不限制
        ...presetKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)).slice(0, 15) // 预设关键词限制15个
      ];

      const hotKeywords = prioritizedKeywords.map(k => k.text);

      // 🔥 修复：合并预制框架和用户保存的框架
      let allFrameworks = [...PRESET_FRAMEWORKS];

      // 读取用户保存的框架
      try {
        const savedFrameworks = localStorage.getItem('book-title-frameworks');
        if (savedFrameworks) {
          const userFrameworks = JSON.parse(savedFrameworks);
          allFrameworks = [...allFrameworks, ...userFrameworks];
        }
      } catch (error) {
        console.error('读取用户框架失败:', error);
      }

      // 🔥 修复：优先显示用户框架，减少数量限制
      // 分离用户框架和预设框架
      const userFrameworks = allFrameworks.filter(f => f.id && (f.id.includes('custom_') || f.id.includes('analysis_')));
      const presetFrameworks = allFrameworks.filter(f => !f.id || (!f.id.includes('custom_') && !f.id.includes('analysis_')));

      // 🔥 移除数量限制：用户框架在前，预设框架也不限制
      const prioritizedFrameworks = [
        ...userFrameworks.sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0)), // 用户框架不限制
        ...presetFrameworks.sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0)) // 🔥 移除预设框架限制，完整展示ACE功能
      ];

      const topFrameworks = prioritizedFrameworks;

      return `📚 **书名创作资源推荐**

🔥 **热门关键词**：${hotKeywords.join('、')}

🏗️ **高效框架模板**：
${topFrameworks.map(f => `• **${f.name}**：${f.pattern}`).join('\n')}

💡 **使用提示**：您可以直接告诉我使用这些关键词和框架来生成书名，我会自动调用专业的书名生成工具为您创作！

---

`;
    } catch (error) {
      console.error('生成书名用户推荐信息失败:', error);
      return '';
    }
  }

  /**
   * 生成简介相关的关键词和框架推荐信息（传递给AI）
   */
  private async generateSynopsisRecommendationsForAI(): Promise<string> {
    try {
      // 动态导入关键词数据
      const { PRESET_KEYWORDS } = await import('./brainstorm/index');

      // 🔥 修复：合并预制关键词和用户保存的关键词（简介也需要读取用户关键词）
      let allKeywords = [...PRESET_KEYWORDS];

      // 读取用户保存的关键词
      try {
        const savedKeywords = localStorage.getItem('book-title-keywords');
        if (savedKeywords) {
          const userKeywords = JSON.parse(savedKeywords);
          console.log('🔍 简介生成读取到用户保存的关键词:', userKeywords.length, '个');
          allKeywords = [...allKeywords, ...userKeywords];
        }
      } catch (error) {
        console.error('简介生成读取用户关键词失败:', error);
      }

      // 选择适合简介的热门关键词，不再限制数量
      const synopsisKeywords = allKeywords
        .sort((a, b) => (b.hotness || 0) - (a.hotness || 0))
        .map(k => k.text);

      // 读取用户实际分析提取的简介框架
      let availableFrameworks: any[] = [];
      try {
        const savedFrameworks = localStorage.getItem('synopsis-frameworks');
        if (savedFrameworks) {
          const frameworks = JSON.parse(savedFrameworks);
          availableFrameworks = frameworks; // 🔥 修复：不再限制框架数量
        }
      } catch (error) {
        console.error('读取简介框架失败:', error);
      }

      // 如果有用户框架，使用用户框架；否则提示没有框架
      if (availableFrameworks.length > 0) {
        return `[系统提供的简介创作资源]
可用热门关键词：${synopsisKeywords.join('、')}

可用简介框架：
${availableFrameworks.map(f => `• ${f.name}(${f.pattern || f.description || '用户分析提取的框架'})`).join('\n')}

请根据用户需求，优先使用这些关键词和框架来调用简介生成工具。`;
      } else {
        return `[系统提供的简介创作资源]
可用热门关键词：${synopsisKeywords.join('、')}

可用简介框架：暂无用户分析的框架数据，建议用户先使用简介分析功能提取框架

请根据用户需求，使用这些关键词来调用简介生成工具，并建议用户先分析简介提取框架。`;
      }
    } catch (error) {
      console.error('生成简介AI推荐信息失败:', error);
      return '';
    }
  }

  /**
   * 生成增强的推荐信息（包含完整的写作技巧和框架分析）
   */
  private async generateEnhancedRecommendations(type: 'book-title' | 'synopsis' | 'combined'): Promise<EnhancedRecommendationData | null> {
    try {
      console.log('🔍 生成增强推荐信息，类型:', type);

      // 动态导入数据
      const { PRESET_KEYWORDS, PRESET_FRAMEWORKS } = await import('./brainstorm/index');

      // 🔥 合并所有关键词数据（预制 + 用户保存）
      let allKeywords = [...PRESET_KEYWORDS];

      // 读取书名关键词
      try {
        const savedBookTitleKeywords = localStorage.getItem('book-title-keywords');
        if (savedBookTitleKeywords) {
          const userKeywords = JSON.parse(savedBookTitleKeywords);
          allKeywords = [...allKeywords, ...userKeywords];
        }
      } catch (error) {
        console.error('读取书名关键词失败:', error);
      }

      // 读取简介关键词
      try {
        const savedSynopsisKeywords = localStorage.getItem('synopsis-keywords');
        if (savedSynopsisKeywords) {
          const synopsisKeywords = JSON.parse(savedSynopsisKeywords);
          allKeywords = [...allKeywords, ...synopsisKeywords];
        }
      } catch (error) {
        console.error('读取简介关键词失败:', error);
      }

      // 去重处理
      const uniqueKeywords = allKeywords.filter((keyword, index, self) =>
        index === self.findIndex(k => k.text === keyword.text)
      );

      // 处理关键词数据
      const enhancedKeywords = uniqueKeywords.map(k => ({
        text: k.text,
        category: (k as any).category || 'general',
        hotness: (k as any).hotness || 0,
        source: (k as any).source || 'preset' as 'preset' | 'user-saved' | 'analysis'
      }));

      // 合并框架数据
      let allFrameworks = [...PRESET_FRAMEWORKS];
      let synopsisFrameworks: any[] = [];

      // 读取书名框架
      try {
        const savedFrameworks = localStorage.getItem('book-title-frameworks');
        if (savedFrameworks) {
          const userFrameworks = JSON.parse(savedFrameworks);
          allFrameworks = [...allFrameworks, ...userFrameworks];
        }
      } catch (error) {
        console.error('读取书名框架失败:', error);
      }

      // 读取简介框架（包含完整的分析数据）
      try {
        const savedSynopsisFrameworks = localStorage.getItem('synopsis-frameworks');
        if (savedSynopsisFrameworks) {
          synopsisFrameworks = JSON.parse(savedSynopsisFrameworks);
        }
      } catch (error) {
        console.error('读取简介框架失败:', error);
      }

      // 🔥 合并所有框架数据（书名 + 简介）
      let targetFrameworks = [...allFrameworks, ...synopsisFrameworks];

      // 去重处理
      const uniqueFrameworks = targetFrameworks.filter((framework, index, self) =>
        index === self.findIndex(f => f.id === framework.id || f.name === framework.name)
      );

      // 处理框架数据，包含完整的分析字段
      const enhancedFrameworks = uniqueFrameworks.map(f => ({
        id: f.id,
        name: f.name,
        pattern: f.pattern,
        description: f.description,
        effectiveness: f.effectiveness || 8,
        examples: f.examples || [],

        // 🔥 包含完整的写作技巧分析
        writingTechniques: f.writingTechniques || [],

        // 🔥 包含完整的风格特征
        styleCharacteristics: f.styleCharacteristics || {},

        // 🔥 包含可复用模板
        reusableTemplates: f.reusableTemplates || []
      }));

      const enhancedData: EnhancedRecommendationData = {
        id: `enhanced_${type}_${Date.now()}`,
        type,
        timestamp: new Date(),
        keywords: enhancedKeywords,
        frameworks: enhancedFrameworks,
        analysisConfidence: 0.85,
        usageCount: 0
      };

      console.log('✅ 增强推荐信息生成完成:', {
        type,
        keywordCount: enhancedKeywords.length,
        frameworkCount: enhancedFrameworks.length,
        hasAnalysisData: enhancedFrameworks.some(f => f.writingTechniques?.length > 0)
      });

      return enhancedData;
    } catch (error) {
      console.error('生成增强推荐信息失败:', error);
      return null;
    }
  }

  /**
   * 生成AI可见的持久化推荐信息
   */
  private async generatePersistentRecommendationsForAI(): Promise<string> {
    try {
      if (!RecommendationEnhancer.isEnabled()) {
        return '';
      }

      console.log('🔍 生成AI持久化推荐信息');

      // 获取现有的持久化数据
      let persistentData = RecommendationEnhancer.getPersistentRecommendations();

      if (persistentData.length === 0) {
        // 🔥 如果没有持久化数据，生成统一的创作资源
        const combinedData = await this.generateEnhancedRecommendations('combined');

        if (combinedData) {
          RecommendationEnhancer.saveRecommendationData(combinedData);
          persistentData = [combinedData];
        }
      }

      // 构建AI可见的推荐信息
      let aiRecommendations = '[系统持久化创作资源库]\n\n';

      for (const data of persistentData) {
        aiRecommendations += '📚 **创作资源库**\n';

        // 🔥 修复：优先显示用户保存的关键词，不限制数量
        // 分离用户关键词和预设关键词
        const userKeywords = data.keywords.filter(k => k.source === 'user-saved' || k.source === 'analysis');
        const presetKeywords = data.keywords.filter(k => k.source === 'preset' || !k.source);

        // 优先排序：用户关键词在前，预设关键词在后
        const prioritizedKeywords = [
          ...userKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)),
          ...presetKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)) 
        ];

        const topKeywords = prioritizedKeywords.map(k => k.text);

        // 🔥 添加用户关键词统计信息
        const userKeywordCount = userKeywords.length;
        const totalKeywordCount = topKeywords.length;

        aiRecommendations += `可用关键词（用户保存: ${userKeywordCount}个，总计: ${totalKeywordCount}个）：${topKeywords.join('、')}\n\n`;

        // 🔥 修复：优先显示用户保存的框架，不限制数量
        // 分离用户框架和预设框架
        const userFrameworks = data.frameworks.filter(f => f.id && (f.id.includes('custom_') || f.id.includes('analysis_')));
        const presetFrameworks = data.frameworks.filter(f => !f.id || (!f.id.includes('custom_') && !f.id.includes('analysis_')));

        // 🔥 移除数量限制：用户框架在前，预设框架也不限制
        const prioritizedFrameworks = [
          ...userFrameworks.sort((a, b) => b.effectiveness - a.effectiveness),
          ...presetFrameworks.sort((a, b) => b.effectiveness - a.effectiveness) // 🔥 移除预设框架限制，完整展示ACE功能
        ];

        const topFrameworks = prioritizedFrameworks;

        if (topFrameworks.length > 0) {
          aiRecommendations += '可用框架模式：\n';

          for (const framework of topFrameworks) {
            aiRecommendations += `• ${framework.name}`;
            if (framework.pattern) {
              aiRecommendations += `(${framework.pattern})`;
            }

            // 🔥 添加写作技巧信息 - 移除数量限制
            if (framework.writingTechniques && framework.writingTechniques.length > 0) {
              const techniques = framework.writingTechniques.map(t => t.name);
              aiRecommendations += ` [技巧: ${techniques.join('、')}]`;
            }

            // 🔥 添加风格特征信息 - 移除数量限制
            if (framework.styleCharacteristics) {
              const style = framework.styleCharacteristics;
              const features = [];

              if (style.layoutTechniques?.paragraphStructure?.length > 0) {
                features.push(`排版: ${style.layoutTechniques.paragraphStructure.join('、')}`);
              }

              if (style.omissionAndEmphasis?.emphasizedElements?.length > 0) {
                features.push(`强调: ${style.omissionAndEmphasis.emphasizedElements.join('、')}`);
              }

              if (style.coolPointLayout?.primaryCoolPoints?.length > 0) {
                features.push(`爽点: ${style.coolPointLayout.primaryCoolPoints.join('、')}`);
              }

              if (style.creativeConcept?.coreCreativity?.length > 0) {
                features.push(`创意: ${style.creativeConcept.coreCreativity.join('、')}`);
              }

              if (features.length > 0) {
                aiRecommendations += ` [${features.join(' | ')}]`;
              }
            }

            aiRecommendations += '\n';
          }
          aiRecommendations += '\n';
        }
      }

      aiRecommendations += '请根据用户需求，优先使用这些资源库中的关键词、框架和写作技巧来提供创作建议和调用相应工具。';

      console.log('✅ AI持久化推荐信息生成完成，长度:', aiRecommendations.length);
      return aiRecommendations;

    } catch (error) {
      console.error('生成AI持久化推荐信息失败:', error);
      return '';
    }
  }

  /**
   * 生成简介相关的关键词和框架推荐信息（显示给用户）
   */
  private async generateSynopsisRecommendationsForUser(): Promise<string> {
    try {
      // 动态导入关键词数据
      const { PRESET_KEYWORDS } = await import('./brainstorm/index');

      // 🔥 修复：合并预制关键词和用户保存的关键词（简介用户显示也需要读取用户关键词）
      let allKeywords = [...PRESET_KEYWORDS];

      // 读取用户保存的关键词
      try {
        const savedKeywords = localStorage.getItem('book-title-keywords');
        if (savedKeywords) {
          const userKeywords = JSON.parse(savedKeywords);
          allKeywords = [...allKeywords, ...userKeywords];
        }
      } catch (error) {
        console.error('简介用户显示读取用户关键词失败:', error);
      }

      // 🔥 修复：优先显示用户关键词，减少数量限制
      // 分离用户关键词和预设关键词
      const userKeywords = allKeywords.filter(k => (k as any).source === 'user-saved' || (k as any).source === 'analysis');
      const presetKeywords = allKeywords.filter(k => !(k as any).source || (k as any).source === 'preset');

      // 🔥 移除数量限制：用户关键词在前，预设关键词也不限制
      const prioritizedKeywords = [
        ...userKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)), // 用户关键词不限制
        ...presetKeywords.sort((a, b) => (b.hotness || 0) - (a.hotness || 0)) // 🔥 移除预设关键词限制，完整展示ACE功能
      ];

      const hotKeywords = prioritizedKeywords.map(k => k.text);

      // 🔥 修复：读取简介框架，不限制数量
      let availableFrameworks: string[] = [];
      try {
        const savedFrameworks = localStorage.getItem('synopsis-frameworks');
        if (savedFrameworks) {
          const frameworks = JSON.parse(savedFrameworks);
          console.log('🔍 读取到的简介框架数据:', frameworks);
          availableFrameworks = frameworks
            // 🔥 移除数量限制，显示所有用户分析的框架
            .map((f: any) => f.name)
            .filter((name: string) => name && name.trim()); // 过滤空名称
        }
      } catch (error) {
        console.error('读取简介框架失败:', error);
      }

      // 如果没有存储的框架，提示用户去分析
      if (availableFrameworks.length === 0) {
        return `📖 **简介创作资源推荐**

🔥 **热门关键词**：${hotKeywords.join('、')}

🏗️ **可用简介框架**：
暂无框架数据，请先使用简介分析功能分析一些优秀简介来提取框架模式

💡 **使用提示**：
1. 先去简介生成页面使用"简介分析"功能分析优秀简介
2. 提取框架后，这里就会显示您的专属框架库
3. 然后您就可以使用这些框架来生成新简介了！

---

`;
      }

      return `📖 **简介创作资源推荐**

🔥 **热门关键词**：${hotKeywords.join('、')}

🏗️ **可用简介框架**：
${availableFrameworks.map(name => `• **${name}**`).join('\n')}

💡 **使用提示**：您可以直接告诉我使用这些关键词和框架来生成简介，我会自动调用专业的简介生成工具为您创作！

---

`;
    } catch (error) {
      console.error('生成简介用户推荐信息失败:', error);
      return '';
    }
  }

  /**
   * 执行工具调用
   */
  private async executeToolCall(instruction: ToolCallInstruction, associationData?: AssociationData): Promise<string> {
    const toolHandler = new ToolCallHandler(associationData);
    return await toolHandler.executeToolCall(instruction);
  }

  /**
   * 解析工具调用结果为结构化数据
   */
  private parseToolCallResult(toolCall: ToolCallInstruction, result: string): ToolCallResult | null {
    try {
      console.log('🔍 开始解析工具调用结果:', {
        action: toolCall.action,
        resultLength: result.length,
        resultPreview: result.substring(0, 200) + '...'
      });

      if (toolCall.action === 'generate_book_titles') {
        // 解析书名生成结果
        const lines = result.split('\n').filter(line => line.trim());
        const titles: BookTitleResult[] = [];

        console.log('📝 解析书名生成结果，行数:', lines.length);

        for (const line of lines) {
          // 匹配格式：**序号. 书名** (评分: X.X分) - 理由
          let match = line.match(/^\*\*(\d+)\.\s*(.+?)\*\*\s*\(评分:\s*([\d.]+)分\)\s*-\s*(.+)$/);
          if (match) {
            const [, , title, scoreStr, reason] = match;
            const score = parseFloat(scoreStr) || 7.0;

            titles.push({
              title: title.trim(),
              score: score,
              reason: reason.trim(),
              extractedKeywords: this.extractKeywordsFromReason(reason),
              detectedFramework: toolCall.parameters?.framework || undefined
            });
            console.log('✅ 解析到书名（格式1）:', title.trim());
            continue;
          }

          // 匹配格式：序号. 书名 (评分: X.X分) - 理由
          match = line.match(/^\d+\.\s*(.+?)\s*\(评分:\s*([\d.]+)分\)\s*-\s*(.+)$/);
          if (match) {
            const [, title, scoreStr, reason] = match;
            const score = parseFloat(scoreStr) || 7.0;

            titles.push({
              title: title.trim(),
              score: score,
              reason: reason.trim(),
              extractedKeywords: this.extractKeywordsFromReason(reason),
              detectedFramework: toolCall.parameters?.framework || undefined
            });
            console.log('✅ 解析到书名（格式2）:', title.trim());
            continue;
          }

          // 匹配格式：**序号. 书名**
          match = line.match(/^\*\*(\d+)\.\s*(.+?)\*\*/);
          if (match) {
            titles.push({
              title: match[2].trim(),
              score: 7.0,
              reason: '基于AI创意生成',
              extractedKeywords: [],
              detectedFramework: toolCall.parameters?.framework || undefined
            });
            console.log('✅ 解析到书名（格式3）:', match[2].trim());
            continue;
          }

          // 简单格式：序号. 书名
          const simpleMatch = line.match(/^\d+\.\s*(.+)$/);
          if (simpleMatch) {
            titles.push({
              title: simpleMatch[1].trim(),
              score: 7.0,
              reason: '基于AI创意生成',
              extractedKeywords: [],
              detectedFramework: toolCall.parameters?.framework || undefined
            });
            console.log('✅ 解析到书名（格式4）:', simpleMatch[1].trim());
          }
        }

        console.log('📊 书名解析完成，总数:', titles.length);

        if (titles.length > 0) {
          const toolCallResult = {
            toolType: 'generate_book_titles' as const,
            titles: titles,
            metadata: {
              keywords: toolCall.parameters?.keywords || [],
              framework: toolCall.parameters?.framework || '',
              requirements: toolCall.parameters?.requirements || '',
              count: titles.length
            }
          };
          console.log('✅ 工具调用结果构建完成:', toolCallResult);
          return toolCallResult;
        } else {
          console.log('⚠️ 未解析到任何书名');
        }
      } else if (toolCall.action === 'generate_synopsis') {
        // 解析简介生成结果
        console.log('📝 解析简介生成结果');

        // 尝试从结果中提取简介内容
        let synopsisContent = '';
        let synopsisData: any = {};

        // 首先尝试从JSON格式中提取完整的简介内容
        try {
          // 尝试解析JSON格式的响应 - 使用贪婪匹配确保获取完整JSON
          const jsonMatch = result.match(/```json\s*(\{[\s\S]*\})\s*```/);
          if (jsonMatch) {
            const jsonData = JSON.parse(jsonMatch[1]);
            if (jsonData.synopsis && jsonData.synopsis.content) {
              synopsisContent = jsonData.synopsis.content;
              console.log('✅ 从JSON格式中提取完整简介正文:', synopsisContent.substring(0, 100) + '...');
              console.log('📊 简介内容长度:', synopsisContent.length, '字符');

              // 🔥 重要：如果成功从JSON中提取到content，直接使用，不再进行其他提取
              // 构建简介数据对象，只使用JSON中的数据
              synopsisData = {
                content: synopsisContent,
                tagline: jsonData.synopsis.tagline,
                hook: jsonData.synopsis.hook,
                core_conflict: jsonData.synopsis.core_conflict,
                selling_points: jsonData.synopsis.selling_points,
                target_audience: jsonData.synopsis.target_audience,
                score: jsonData.synopsis.score || 8.0,
                wordCount: jsonData.synopsis.wordCount || synopsisContent.length,
                reason: jsonData.synopsis.reason || '通过AI工具调用生成',
                sections: jsonData.synopsis.sections
              };

              console.log('✅ 从JSON完整构建简介数据对象');

              // 直接返回结果，不再进行格式化文本解析
              const synopsisResult = {
                toolType: 'generate_synopsis' as const,
                synopsis: synopsisData,
                metadata: {
                  keywords: toolCall.parameters?.keywords || [],
                  framework: toolCall.parameters?.framework || '',
                  requirements: toolCall.parameters?.requirements || '',
                  length: synopsisData.wordCount || synopsisContent.length
                }
              };

              console.log('✅ 简介生成结果构建完成（JSON路径）:', {
                hasContent: !!synopsisData.content,
                hasTagline: !!synopsisData.tagline,
                hasHook: !!synopsisData.hook,
                hasSections: !!synopsisData.sections,
                wordCount: synopsisData.wordCount
              });
              return synopsisResult;
            }
          }
        } catch (jsonError) {
          console.log('📝 JSON解析失败，尝试其他方式');
        }

        // 如果JSON解析失败，尝试从格式化的结果中提取简介正文
        if (!synopsisContent) {
          console.log('🔍 尝试从格式化文本中提取简介内容...');

          // 方法1：尝试提取 "content": "..." 字段的完整内容
          const contentFieldMatch = result.match(/"content":\s*"((?:[^"\\]|\\.)*)"/);
          if (contentFieldMatch) {
            // 处理转义字符
            synopsisContent = contentFieldMatch[1]
              .replace(/\\n/g, '\n')
              .replace(/\\"/g, '"')
              .replace(/\\\\/g, '\\')
              .trim();
            console.log('✅ 从content字段提取完整简介正文:', synopsisContent.substring(0, 100) + '...');
            console.log('📊 提取内容长度:', synopsisContent.length, '字符');
          } else {
            // 方法2：使用更宽松的正则表达式，匹配到下一个标题或结尾
            const contentMatch = result.match(/\*\*📝 简介正文\*\*\n\n([\s\S]*?)(?=\n\n\*\*|$)/);
            if (contentMatch) {
              synopsisContent = contentMatch[1].trim();
              console.log('✅ 从格式化结果中提取简介正文:', synopsisContent.substring(0, 100) + '...');
            } else {
              // 方法3：如果没有找到格式化内容，检查内容长度并智能提取
              const fullContent = result.trim();
              if (fullContent.length > 50) {
                // 尝试从完整响应中提取主要内容
                const cleanedContent = fullContent
                  .replace(/🏷️ \*\*标语\*\*：.*?\n/g, '')
                  .replace(/🎣 \*\*开头钩子\*\*：.*?\n/g, '')
                  .replace(/⚔️ \*\*核心冲突\*\*：.*?\n/g, '')
                  .replace(/✨ \*\*主要卖点\*\*：[\s\S]*?(?=\n\n\*\*|$)/g, '')
                  .replace(/🎯 \*\*目标读者\*\*：.*?\n/g, '')
                  .replace(/📑 \*\*段落分解\*\*：[\s\S]*?(?=\n\n\*\*|$)/g, '')
                  .replace(/📊 \*\*统计信息\*\*：[\s\S]*?(?=\n\n\*\*|$)/g, '')
                  .replace(/💭 \*\*创作理由\*\*：[\s\S]*?(?=\n\n\*\*|$)/g, '')
                  .replace(/\*\*.*?\*\*：?/g, '') // 移除所有格式化标题
                  .replace(/[📝🏷️🎣⚔️✨🎯📑📊💭]/g, '') // 移除emoji
                  .replace(/\n\s*\n/g, '\n') // 清理多余换行
                  .trim();

                if (cleanedContent.length > synopsisContent.length) {
                  synopsisContent = cleanedContent;
                  console.log('✅ 从完整内容中智能提取简介正文:', synopsisContent.substring(0, 100) + '...');
                }
              }

              if (!synopsisContent) {
                synopsisContent = result.trim();
                console.log('⚠️ 未找到格式化简介，使用完整结果');
              }
            }
          }

          console.log('📊 最终提取的简介内容长度:', synopsisContent.length, '字符');
        }

        // 尝试提取其他信息
        const taglineMatch = result.match(/🏷️ \*\*标语\*\*：(.*?)\n/);
        const hookMatch = result.match(/🎣 \*\*开头钩子\*\*：(.*?)\n/);
        const conflictMatch = result.match(/⚔️ \*\*核心冲突\*\*：(.*?)\n/);
        const scoreMatch = result.match(/AI评分：([\d.]+)\/10/);
        const wordCountMatch = result.match(/字数：(\d+)字/);

        // 构建简介数据对象
        synopsisData = {
          content: synopsisContent,
          tagline: taglineMatch ? taglineMatch[1].trim() : undefined,
          hook: hookMatch ? hookMatch[1].trim() : undefined,
          core_conflict: conflictMatch ? conflictMatch[1].trim() : undefined,
          score: scoreMatch ? parseFloat(scoreMatch[1]) : 8.0,
          wordCount: wordCountMatch ? parseInt(wordCountMatch[1]) : synopsisContent.length,
          reason: '通过AI工具调用生成'
        };

        // 尝试提取卖点
        const sellingPointsMatch = result.match(/✨ \*\*主要卖点\*\*：\n([\s\S]*?)\n\n/);
        if (sellingPointsMatch) {
          const points = sellingPointsMatch[1]
            .split('\n')
            .filter(line => line.match(/^\d+\./))
            .map(line => line.replace(/^\d+\.\s*/, '').trim());
          synopsisData.selling_points = points;
        }

        // 尝试提取段落分解
        const sectionsMatch = result.match(/📑 \*\*段落分解\*\*：\n([\s\S]*?)📊/);
        if (sectionsMatch) {
          const sectionText = sectionsMatch[1];
          const sections: any[] = [];
          const sectionMatches = sectionText.matchAll(/\*\*(\d+)\. (.*?)\*\* \((\d+)字\)\n([\s\S]*?)(?=\*\*\d+\.|$)/g);

          for (const match of sectionMatches) {
            const [, index, name, wordCount, content] = match;
            const functionMatch = content.match(/\*功能：(.*?)\*/);

            sections.push({
              name: name.trim(),
              content: content.replace(/\*功能：.*?\*\n?/, '').trim(),
              wordCount: parseInt(wordCount),
              function: functionMatch ? functionMatch[1].trim() : undefined
            });
          }

          if (sections.length > 0) {
            synopsisData.sections = sections;
          }
        }

        const synopsisResult = {
          toolType: 'generate_synopsis' as const,
          synopsis: synopsisData,
          metadata: {
            keywords: toolCall.parameters?.keywords || [],
            framework: toolCall.parameters?.framework || '',
            requirements: toolCall.parameters?.requirements || '',
            length: synopsisData.wordCount || synopsisContent.length
          }
        };

        console.log('✅ 简介生成结果构建完成:', {
          hasContent: !!synopsisData.content,
          hasTagline: !!synopsisData.tagline,
          hasHook: !!synopsisData.hook,
          hasSections: !!synopsisData.sections,
          wordCount: synopsisData.wordCount
        });
        return synopsisResult;
      }

      if (toolCall.action === 'analyze_framework') {
        // 解析框架分析结果
        const lines = result.split('\n').filter(line => line.trim());

        // 提取框架信息
        let frameworkName = '';
        let frameworkPattern = '';
        let description = '';
        let examples: string[] = [];
        let confidence = 0.8;

        for (const line of lines) {
          if (line.includes('**框架名称**：')) {
            frameworkName = line.replace('**框架名称**：', '').trim();
          } else if (line.includes('**框架模式**：')) {
            frameworkPattern = line.replace('**框架模式**：', '').replace(/`/g, '').trim();
          } else if (line.includes('**框架特点**：')) {
            description = line.replace('**框架特点**：', '').trim();
          } else if (line.includes('**置信度**：')) {
            const confidenceMatch = line.match(/(\d+\.?\d*)%/);
            if (confidenceMatch) {
              confidence = parseFloat(confidenceMatch[1]) / 100;
            }
          } else if (line.match(/^\d+\.\s+/)) {
            // 提取示例
            const example = line.replace(/^\d+\.\s+/, '').trim();
            if (example) {
              examples.push(example);
            }
          }
        }

        if (frameworkName && frameworkPattern) {
          return {
            toolType: 'analyze_framework',
            framework: {
              name: frameworkName,
              pattern: frameworkPattern,
              examples: examples.slice(0, 3), // 最多显示3个示例
              confidence: confidence
            },
            metadata: {
              description: description,
              confidence: confidence,
              exampleCount: examples.length
            }
          };
        }
      } else if (toolCall.action === 'create_worldview') {
        // 解析世界观创建结果
        console.log('🌍 解析世界观创建结果');

        const worldviewData = this.parseWorldviewCreationResult(result);

        if (worldviewData) {
          const worldviewResult = {
            toolType: 'create_worldview' as const,
            worldview: worldviewData,
            metadata: {
              elementCount: Object.keys(worldviewData.elements || {}).length,
              worldType: worldviewData.type || toolCall.parameters?.worldType || 'fantasy',
              complexity: worldviewData.complexity || toolCall.parameters?.complexity || 'medium',
              keywords: toolCall.parameters?.keywords || []
            }
          };

          console.log('✅ 世界观创建结果构建完成:', {
            hasName: !!worldviewData.name,
            hasDescription: !!worldviewData.description,
            elementCount: Object.keys(worldviewData.elements || {}).length,
            worldType: worldviewResult.metadata.worldType
          });

          return worldviewResult;
        }
      }

      return null;
    } catch (error) {
      console.error('解析工具调用结果失败:', error);
      return null;
    }
  }

  /**
   * 从理由中提取关键词
   */
  private extractKeywordsFromReason(reason: string): string[] {
    const keywords: string[] = [];

    // 常见的关键词模式
    const patterns = [
      /体现了(.+?)的/g,
      /突出(.+?)特色/g,
      /展现(.+?)风格/g,
      /包含(.+?)元素/g
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(reason)) !== null) {
        const keyword = match[1].trim();
        if (keyword && !keywords.includes(keyword)) {
          keywords.push(keyword);
        }
      }
    }

    return keywords.slice(0, 3); // 最多返回3个关键词
  }

  /**
   * 解析世界观创建结果
   */
  private parseWorldviewCreationResult(result: string): any {
    try {
      console.log('🔍 开始解析世界观创建结果...');

      // 初始化世界观数据
      let worldviewData: any = {
        name: '',
        type: 'fantasy',
        description: '',
        complexity: 'medium',
        elements: {},
        score: 8.0
      };

      // 方法1：尝试从专业世界观创建系统的结果中提取
      const professionalMatch = result.match(/🌍 \*\*专业世界观创建完成\*\*/);
      if (professionalMatch) {
        console.log('✅ 检测到专业世界观创建结果');

        // 提取创建参数
        const typeMatch = result.match(/类型：(\w+)/);
        const complexityMatch = result.match(/复杂度：(\w+)/);
        const keywordsMatch = result.match(/关键词：([^\n]+)/);

        if (typeMatch) worldviewData.type = typeMatch[1];
        if (complexityMatch) worldviewData.complexity = complexityMatch[1];

        // 提取世界观元素
        const elementMatches = result.matchAll(/🏷️ \*\*([^*]+)\*\*\n([\s\S]*?)(?=🏷️|\n\n💡|$)/g);

        for (const match of elementMatches) {
          const [, elementName, elementContent] = match;

          // 解析元素详情
          const elementData: any = {
            name: elementName.trim(),
            description: ''
          };

          // 提取类别
          const categoryMatch = elementContent.match(/📂 类别：([^\n]+)/);
          if (categoryMatch) {
            elementData.category = categoryMatch[1].trim();
          }

          // 提取描述
          const descMatch = elementContent.match(/📝 描述：([^\n]+)/);
          if (descMatch) {
            elementData.description = descMatch[1].trim();
          }

          // 提取重要性
          const importanceMatch = elementContent.match(/⭐ 重要性：(\d+)\/5/);
          if (importanceMatch) {
            elementData.importance = parseInt(importanceMatch[1]);
          }

          // 提取象征意义
          const symbolMatch = elementContent.match(/🎭 象征意义：([^\n]+)/);
          if (symbolMatch) {
            elementData.symbolism = symbolMatch[1].trim();
          }

          worldviewData.elements[elementName.trim()] = elementData;
        }

        // 转换为ToolCallResultBubble期望的格式
        const elementNames = Object.keys(worldviewData.elements);
        if (elementNames.length > 0) {
          worldviewData.name = `${worldviewData.type}世界观：${elementNames[0]}`;
          worldviewData.description = `一个${worldviewData.complexity}复杂度的${worldviewData.type}世界，包含${elementNames.join('、')}等元素。`;
        }

        // 转换elements格式为WorldViewResult期望的格式
        const convertedElements: any = {};
        for (const [elementName, elementData] of Object.entries(worldviewData.elements)) {
          const data = elementData as any;
          const category = data.category || elementName;

          // 根据类别映射到标准字段
          if (category.includes('地理') || category.includes('环境') || elementName.includes('地理')) {
            convertedElements.geography = convertedElements.geography || [];
            convertedElements.geography.push(data.description || elementName);
          } else if (category.includes('种族') || elementName.includes('种族')) {
            convertedElements.races = convertedElements.races || [];
            convertedElements.races.push(data.description || elementName);
          } else if (category.includes('魔法') || elementName.includes('魔法')) {
            convertedElements.magic_system = convertedElements.magic_system || [];
            convertedElements.magic_system.push(data.description || elementName);
          } else if (category.includes('科技') || elementName.includes('科技')) {
            convertedElements.technology = convertedElements.technology || [];
            convertedElements.technology.push(data.description || elementName);
          } else if (category.includes('政治') || elementName.includes('政治')) {
            convertedElements.politics = convertedElements.politics || [];
            convertedElements.politics.push(data.description || elementName);
          } else if (category.includes('文化') || elementName.includes('文化')) {
            convertedElements.culture = convertedElements.culture || [];
            convertedElements.culture.push(data.description || elementName);
          } else if (category.includes('历史') || elementName.includes('历史')) {
            convertedElements.history = convertedElements.history || [];
            convertedElements.history.push(data.description || elementName);
          } else {
            // 默认归类到文化
            convertedElements.culture = convertedElements.culture || [];
            convertedElements.culture.push(data.description || elementName);
          }
        }

        worldviewData.elements = convertedElements;
        worldviewData.tags = elementNames.slice(0, 5); // 使用元素名称作为标签
        worldviewData.summary = worldviewData.description;

        console.log('✅ 专业世界观解析完成:', {
          name: worldviewData.name,
          elementCount: elementNames.length,
          type: worldviewData.type,
          convertedElements: Object.keys(convertedElements)
        });

        return worldviewData;
      }

      // 方法2：尝试从JSON格式中提取
      const jsonMatch = result.match(/```json\s*(\{[\s\S]*\})\s*```/);
      if (jsonMatch) {
        try {
          const jsonData = JSON.parse(jsonMatch[1]);
          console.log('✅ 从JSON格式中提取世界观数据');

          worldviewData.name = jsonData.name || '未命名世界观';
          worldviewData.type = jsonData.type || 'fantasy';
          worldviewData.description = jsonData.description || '';
          worldviewData.complexity = jsonData.complexity || 'medium';
          worldviewData.score = jsonData.score || 8.0;

          // 转换elements格式
          if (jsonData.elements) {
            for (const [key, value] of Object.entries(jsonData.elements)) {
              if (Array.isArray(value)) {
                worldviewData.elements[key] = {
                  name: key,
                  description: (value as string[]).join('、'),
                  category: key
                };
              }
            }
          }

          return worldviewData;
        } catch (jsonError) {
          console.log('📝 JSON解析失败，尝试其他方式');
        }
      }

      // 方法3：从格式化文本中提取基本信息
      console.log('🔍 尝试从格式化文本中提取世界观信息...');

      // 提取基本信息
      const nameMatch = result.match(/世界观名称[：:]\s*([^\n]+)/);
      const typeMatch = result.match(/世界类型[：:]\s*(\w+)/);
      const descMatch = result.match(/描述[：:]\s*([^\n]+)/);

      if (nameMatch) worldviewData.name = nameMatch[1].trim();
      if (typeMatch) worldviewData.type = typeMatch[1].trim();
      if (descMatch) worldviewData.description = descMatch[1].trim();

      // 如果没有找到名称，生成一个默认名称
      if (!worldviewData.name) {
        worldviewData.name = `${worldviewData.type}世界观`;
      }

      // 如果没有找到描述，使用结果的前100个字符
      if (!worldviewData.description && result.length > 50) {
        worldviewData.description = result.substring(0, 100).replace(/[🌍📝✨]/g, '').trim() + '...';
      }

      console.log('✅ 基础世界观解析完成:', {
        name: worldviewData.name,
        type: worldviewData.type,
        hasDescription: !!worldviewData.description
      });

      return worldviewData;

    } catch (error) {
      console.error('❌ 世界观解析失败:', error);

      // 返回默认世界观数据
      return {
        name: '未命名世界观',
        type: 'fantasy',
        description: '世界观创建完成，但解析过程中遇到问题。',
        complexity: 'medium',
        elements: {},
        score: 7.0
      };
    }
  }

  /**
   * 生成创意方案
   */
  async generateCreativity(params: BrainstormParams): Promise<CreativityResult> {
    try {
      await this.ensureAISender();

      console.log('🎨 开始生成创意方案...');

      // 获取API配置
      const aiConfig = await configService.getAIConfig();
      console.log('📋 API配置详情:', {
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        model: aiConfig.model,
        hasApiKey: !!aiConfig.apiKey
      });

      // 构建提示词
      const messages = BrainstormPromptBuilder.buildCreativityPrompt(params);

      console.log('🔍 BrainstormAIService: 发送AI创意生成请求...');

      // 使用统一的AI调用方法
      const response = await this.callAI(messages, {
        streaming: false // 确保不使用流式输出
      });

      if (!response.success) {
        throw new Error(response.error || 'AI请求失败');
      }

      console.log('✅ AI创意生成完成');

      // 解析JSON响应
      const result = this.parseCreativityResponse(response.text);
      return result;

    } catch (error) {
      console.error('❌ 创意生成失败:', error);
      throw error;
    }
  }

  /**
   * AI聊天功能（流式响应）
   */
  async sendChatMessage(
    userInput: string,
    associationData?: AssociationData,
    onChunk?: (chunk: string) => void,
    messageHistory?: Array<{ role: string; content: string }>
  ): Promise<BrainstormResponse> {
    try {
      console.log('💬 BrainstormAIService: 开始AI聊天...');

      // 🔥 新增：检查是否启用持久化推荐
      let persistentRecommendations: string | null = null;
      if (RecommendationEnhancer.isEnabled()) {
        persistentRecommendations = await this.generatePersistentRecommendationsForAI();
        console.log('🔍 持久化推荐已启用，AI将获得完整的创作资源库');
      }

      // 检测是否包含书名或简介相关关键词
      const isBookTitleRequest = this.detectBookTitleKeywords(userInput);
      const isSynopsisRequest = this.detectSynopsisKeywords(userInput);
      let recommendations: string | null = null;
      let recommendationsAdded = false;

      // 如果没有启用持久化推荐，则使用原有的关键词检测机制
      if (!RecommendationEnhancer.isEnabled()) {
        // 如果检测到书名相关请求，获取AI推荐信息
        if (isBookTitleRequest) {
          // 🔧 修复：移除用户推荐信息的自动显示，避免与AI回复重复
          // 只获取传递给AI的推荐信息，让AI统一生成回复
          recommendations = await this.generateRecommendationsForAI();
          console.log('🔍 检测到书名生成请求，已加载书名推荐信息');
        }
        // 如果检测到简介相关请求，获取AI推荐信息（不显示给用户）
        else if (isSynopsisRequest) {
          // 获取传递给AI的推荐信息
          recommendations = await this.generateSynopsisRecommendationsForAI();
          console.log('📝 检测到简介生成请求，已加载简介推荐信息');
        }
      } else {
        // 如果启用了持久化推荐，使用持久化数据
        recommendations = persistentRecommendations;
        console.log('🔍 使用持久化推荐数据，AI将获得完整的创作资源库');
      }

      // 构建聊天提示词（使用新的推荐信息方法）
      let messages = BrainstormPromptBuilder.buildChatPromptWithRecommendations(
        userInput,
        recommendations,
        associationData
      );

      // 如果有消息历史，将其插入到系统消息和当前用户消息之间
      if (messageHistory && messageHistory.length > 0) {
        console.log('📜 包含消息历史，历史消息数量:', messageHistory.length);

        // 获取系统消息（通常是第一条）
        const systemMessage = messages[0];
        const currentUserMessages = messages.slice(1);

        // 重新构建消息序列：系统消息 + 历史消息 + 当前消息
        messages = [
          systemMessage,
          ...messageHistory,
          ...currentUserMessages
        ];

        console.log('✅ 消息历史已整合，总消息数量:', messages.length);
      } else {
        console.log('📝 无消息历史，使用新对话模式');
      }

      console.log('🔍 发送AI聊天请求...', {
        isBookTitleRequest,
        hasRecommendations: !!recommendations
      });

      let fullResponse = '';
      let rawResponse = ''; // 保存原始响应用于工具调用检测

      // 如果提供了流式回调，使用流式请求
      if (onChunk) {
        // 重置JSON过滤器状态
        GlobalJSONFilter.reset();

        await this.callAIStreaming(
          messages,
          (chunk: string) => {
            rawResponse += chunk; // 保存原始响应
            fullResponse += chunk;
            // 使用新的JSON过滤器实时过滤
            const cleanedChunk = GlobalJSONFilter.filter(chunk);
            if (cleanedChunk.trim()) {
              onChunk(cleanedChunk);
            }
          },
          {
            streaming: true
          }
        );
      } else {
        // 否则使用普通请求
        const response = await this.callAI(messages, {
          streaming: false
        });

        if (!response.success) {
          throw new Error(response.error || 'AI请求失败');
        }

        fullResponse = response.text;
        rawResponse = response.text; // 保存原始响应
      }

      console.log('✅ AI聊天完成');

      // 检测并执行工具调用（使用原始响应）
      const toolCall = this.detectToolCall(rawResponse);
      let toolCallResult: ToolCallResult | undefined;

      if (toolCall) {
        console.log('🔧 检测到工具调用:', toolCall.action);

        try {
          // 执行工具调用
          const toolResult = await this.executeToolCall(toolCall, associationData);

          // 解析工具调用结果为结构化数据
          toolCallResult = this.parseToolCallResult(toolCall, toolResult) || undefined;

          // 工具调用状态提示（简洁的状态更新）
          if (onChunk) {
            onChunk('\n🔧 工具调用执行完成\n');
          }

          console.log('✅ 工具调用执行完成');
        } catch (error) {
          console.error('❌ 工具调用执行失败:', error);
          const errorMessage = `\n\n❌ 工具调用执行失败：${error instanceof Error ? error.message : '未知错误'}\n`;
          if (onChunk) {
            onChunk(errorMessage);
          }
          fullResponse += errorMessage;
        }
      }

      // 使用新的JSON过滤器进行最终清理（使用原始响应进行清理）
      const cleanedResponse = StreamingJSONFilter.cleanFinalResponse(rawResponse);

      return {
        success: true,
        content: cleanedResponse,
        toolCallResult: toolCallResult
      };

    } catch (error) {
      console.error('❌ AI聊天失败:', error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 解析创意响应
   */
  private parseCreativityResponse(responseText: string): CreativityResult {
    try {
      console.log('🔍 开始解析AI响应:', responseText.substring(0, 200) + '...');

      // 多种JSON提取策略
      let jsonText = '';

      // 策略1：寻找完整的JSON对象
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonText = jsonMatch[0];
        console.log('✅ 策略1成功：找到完整JSON对象');
      } else {
        // 策略2：寻找solutions数组
        const solutionsMatch = responseText.match(/"solutions"\s*:\s*\[[\s\S]*?\]/);
        if (solutionsMatch) {
          jsonText = `{${solutionsMatch[0]}, "summary": "已生成创意方案"}`;
          console.log('✅ 策略2成功：找到solutions数组');
        } else {
          throw new Error('未找到有效JSON格式');
        }
      }

      // 清理可能的格式问题
      jsonText = jsonText
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .replace(/\n\s*\n/g, '\n')
        .trim();

      console.log('🧹 清理后的JSON:', jsonText.substring(0, 300) + '...');

      const parsed = JSON.parse(jsonText);

      // 验证响应格式
      if (!parsed.solutions || !Array.isArray(parsed.solutions)) {
        throw new Error('响应格式不正确：缺少solutions数组');
      }

      // 确保每个方案都有必要的字段
      const validSolutions = parsed.solutions.map((solution: any) => ({
        title: solution.title || '未命名方案',
        concept: solution.concept || '暂无描述',
        highlights: Array.isArray(solution.highlights) ? solution.highlights : [],
        implementation: solution.implementation || '暂无实施建议',
        marketPotential: solution.marketPotential || '暂无分析',
        development: solution.development || '暂无发展思路',
        score: typeof solution.score === 'number' ? solution.score : 7.0
      }));

      console.log('✅ 成功解析创意响应，方案数量:', validSolutions.length);

      return {
        solutions: validSolutions,
        summary: parsed.summary || '已生成创意方案',
        recommendations: parsed.recommendations || '请根据需要选择合适的方案'
      };

    } catch (error) {
      console.error('❌ 解析创意响应失败:', error);
      console.log('📄 原始响应文本:', responseText);

      // 返回默认结果
      return {
        solutions: [{
          title: '创意方案',
          concept: responseText.substring(0, 200) + '...',
          highlights: ['创新性', '实用性', '市场潜力'],
          implementation: '请根据具体需求进行调整和完善',
          marketPotential: '具有一定的市场潜力',
          development: '可以进一步深化和扩展',
          score: 7.5
        }],
        summary: '已生成基础创意方案',
        recommendations: '建议根据实际需求进行调整'
      };
    }
  }

  /**
   * 生成简单的脑洞内容（用于原有的脑洞生成功能）
   */
  async generateBrainstorm(request: any): Promise<any[]> {
    try {
      const params: BrainstormParams = {
        userInput: request.input || '请生成创意内容',
        creativityType: 'general',
        associationData: request.associationData
      };

      const result = await this.generateCreativity(params);

      // 转换为原有格式
      return result.solutions.map((solution, index) => ({
        id: (index + 1).toString(),
        type: request.type || 'creativity',
        content: `${solution.title}\n\n${solution.concept}\n\n实施建议：${solution.implementation}`,
        score: solution.score,
        metadata: {
          keywords: solution.highlights,
          createdAt: new Date(),
          marketPotential: solution.marketPotential,
          development: solution.development
        }
      }));

    } catch (error) {
      console.error('生成脑洞内容失败:', error);
      
      // 返回默认内容
      return [{
        id: '1',
        type: request.type || 'creativity',
        content: `基于"${request.input}"生成的创意内容`,
        score: 7.0,
        metadata: {
          keywords: ['创意', '灵感', '构思'],
          createdAt: new Date()
        }
      }];
    }
  }

  /**
   * 从上下文中提取bookId
   */
  private extractBookIdFromContext(): string | undefined {
    // 这里可以从当前上下文、URL参数或全局状态中获取bookId
    // 暂时返回undefined，实际实现时需要根据具体的上下文获取方式
    try {
      // 尝试从localStorage获取当前项目ID
      const currentProject = localStorage.getItem('currentProject');
      if (currentProject) {
        const project = JSON.parse(currentProject);
        return project.id;
      }
    } catch (error) {
      console.warn('获取当前项目ID失败:', error);
    }
    return undefined;
  }

  /**
   * 检测世界观类型
   */
  private detectWorldType(content: string): 'fantasy' | 'sci-fi' | 'modern' | 'historical' | 'mixed' {
    const fantasyKeywords = ['魔法', '修仙', '玄幻', '仙侠', '魔幻', '奇幻'];
    const sciFiKeywords = ['科幻', '未来', '太空', '机器人', '人工智能', '星际'];
    const modernKeywords = ['都市', '现代', '当代', '职场', '校园'];
    const historicalKeywords = ['古代', '历史', '穿越', '宫廷', '武侠', '古风'];

    if (fantasyKeywords.some(keyword => content.includes(keyword))) {
      return 'fantasy';
    }
    if (sciFiKeywords.some(keyword => content.includes(keyword))) {
      return 'sci-fi';
    }
    if (modernKeywords.some(keyword => content.includes(keyword))) {
      return 'modern';
    }
    if (historicalKeywords.some(keyword => content.includes(keyword))) {
      return 'historical';
    }
    return 'fantasy'; // 默认为玄幻
  }

  /**
   * 检测复杂度
   */
  private detectComplexity(content: string): 'simple' | 'medium' | 'complex' {
    const simpleKeywords = ['简单', '基础', '入门'];
    const complexKeywords = ['复杂', '详细', '完整', '深入', '全面'];

    if (simpleKeywords.some(keyword => content.includes(keyword))) {
      return 'simple';
    }
    if (complexKeywords.some(keyword => content.includes(keyword))) {
      return 'complex';
    }
    return 'medium'; // 默认为中等
  }

  /**
   * 从内容中提取关键词
   */
  private extractKeywordsFromContent(content: string): string[] {
    // 简单的关键词提取逻辑
    const keywords: string[] = [];

    // 提取一些常见的世界观关键词
    const worldViewKeywords = [
      '魔法', '科技', '政治', '文化', '种族', '地理', '历史',
      '宗教', '经济', '军事', '社会', '法律', '教育', '艺术'
    ];

    worldViewKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        keywords.push(keyword);
      }
    });

    return keywords;
  }
}
