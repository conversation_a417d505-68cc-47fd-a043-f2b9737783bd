"use client";

import React, { useState, useEffect } from 'react';
import { AIContinueDialog } from './AIContinueDialog';

interface AIContinueButtonProps {
  context?: string;
  bookId?: string;
  onInsertContent: (content: string) => void;
  buttonText?: string;
  buttonClassName?: string;
  buttonIcon?: React.ReactNode;
  buttonSize?: 'small' | 'medium' | 'large';
  buttonType?: 'primary' | 'secondary' | 'outline' | 'text';
}

/**
 * AI续写按钮组件
 * 点击后打开AI续写对话框
 */
export const AIContinueButton: React.FC<AIContinueButtonProps> = ({
  context = '',
  bookId = '',
  onInsertContent,
  buttonText = 'AI续写',
  buttonClassName = '',
  buttonIcon,
  buttonSize = 'medium',
  buttonType = 'primary'
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentContext, setCurrentContext] = useState(context);
  
  // 当context属性变化时更新状态
  useEffect(() => {
    setCurrentContext(context);
  }, [context]);
  
  // 打开对话框
  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };
  
  // 关闭对话框
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };
  
  // 处理内容插入
  const handleInsertContent = (content: string) => {
    onInsertContent(content);
    setIsDialogOpen(false);
  };
  
  // 根据buttonSize确定按钮尺寸样式
  const sizeStyles = {
    small: 'px-2 py-1 text-sm',
    medium: 'px-4 py-2',
    large: 'px-6 py-3 text-lg'
  };
  
  // 根据buttonType确定按钮类型样式
  const typeStyles = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-purple-600 hover:bg-purple-700 text-white',
    outline: 'bg-white border border-blue-600 text-blue-600 hover:bg-blue-50',
    text: 'bg-transparent text-blue-600 hover:bg-blue-50'
  };
  
  // 合并所有样式
  const buttonStyle = `
    ${sizeStyles[buttonSize]} 
    ${typeStyles[buttonType]} 
    rounded-lg transition-colors duration-200 flex items-center justify-center
    ${buttonClassName}
  `;
  
  // 默认图标
  const defaultIcon = (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-5 w-5 mr-1.5" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" 
      />
    </svg>
  );
  
  return (
    <>
      <button
        className={buttonStyle}
        onClick={handleOpenDialog}
        title="使用AI续写内容"
      >
        {buttonIcon || defaultIcon}
        {buttonText}
      </button>
      
      <AIContinueDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        onInsertContent={handleInsertContent}
        initialContext={currentContext}
        bookId={bookId}
      />
    </>
  );
};
