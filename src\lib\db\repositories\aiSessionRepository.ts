import { v4 as uuidv4 } from 'uuid';
import { db, AISession, AIFeatureType } from '../dexie';

export interface IAISessionRepository {
  getById(id: string): Promise<AISession | undefined>;
  getByBookAndFeature(bookId: string, feature: AIFeatureType): Promise<AISession[]>;
  getByChapterAndFeature(chapterId: string, feature: AIFeatureType): Promise<AISession[]>;
  create(session: Omit<AISession, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, session: Partial<AISession>): Promise<void>;
  delete(id: string): Promise<void>;
  deleteByBookId(bookId: string): Promise<void>;
}

export class AISessionRepository implements IAISessionRepository {
  async getById(id: string): Promise<AISession | undefined> {
    return await db.aiSessions.get(id);
  }

  async getByBookAndFeature(bookId: string, feature: AIFeatureType): Promise<AISession[]> {
    return await db.aiSessions
      .where('bookId')
      .equals(bookId)
      .and(session => session.feature === feature)
      .reverse()
      .sortBy('updatedAt');
  }

  async getByChapterAndFeature(chapterId: string, feature: AIFeatureType): Promise<AISession[]> {
    return await db.aiSessions
      .where('chapterId')
      .equals(chapterId)
      .and(session => session.feature === feature)
      .reverse()
      .sortBy('updatedAt');
  }

  async create(session: Omit<AISession, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();
    
    await db.aiSessions.add({
      ...session,
      id,
      createdAt: now,
      updatedAt: now
    });
    
    return id;
  }

  async update(id: string, session: Partial<AISession>): Promise<void> {
    await db.aiSessions.update(id, {
      ...session,
      updatedAt: new Date()
    });
  }

  async delete(id: string): Promise<void> {
    await db.aiSessions.delete(id);
  }

  async deleteByBookId(bookId: string): Promise<void> {
    await db.aiSessions
      .where('bookId')
      .equals(bookId)
      .delete();
  }
}

// 创建并导出仓库实例
export const aiSessionRepository = new AISessionRepository();
