"use client";

import React, { useState } from 'react';
import { AIContinueButton } from '@/adapters/ui';

/**
 * AI续写按钮示例组件
 * 展示如何在编辑器中使用AI续写按钮
 */
const AIContinueButtonExample: React.FC = () => {
  const [editorContent, setEditorContent] = useState<string>(
    '这是一个小说的开头，讲述了一个年轻人在森林中迷路的故事。\n\n' +
    '天色渐暗，林间的雾气开始弥漫。杰克握紧了手中的指南针，但指针似乎失去了方向，只是不停地颤抖着。他已经在这片森林中徘徊了三个小时，却始终找不到来时的路。'
  );
  
  // 处理内容插入
  const handleInsertContent = (content: string) => {
    setEditorContent(prev => prev + '\n\n' + content);
  };
  
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">AI续写按钮示例</h1>
      
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">编辑器内容</h2>
          <div className="flex space-x-2">
            <AIContinueButton
              context={editorContent}
              bookId="example-book-id"
              onInsertContent={handleInsertContent}
              buttonSize="medium"
              buttonType="primary"
            />
            
            <AIContinueButton
              context={editorContent}
              bookId="example-book-id"
              onInsertContent={handleInsertContent}
              buttonText="智能续写"
              buttonSize="small"
              buttonType="outline"
            />
          </div>
        </div>
        
        <div className="border rounded-lg p-4 bg-white">
          <textarea
            className="w-full h-64 p-2 border rounded"
            value={editorContent}
            onChange={(e) => setEditorContent(e.target.value)}
          />
        </div>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-lg border">
        <h3 className="font-medium mb-2">使用说明</h3>
        <ul className="list-disc pl-5 space-y-1 text-sm">
          <li>点击"AI续写"按钮打开续写对话框</li>
          <li>在对话框中可以修改上下文、添加续写要求和风格</li>
          <li>点击"开始续写"生成内容</li>
          <li>点击"插入到编辑器"将生成的内容添加到编辑器中</li>
        </ul>
      </div>
    </div>
  );
};

export default AIContinueButtonExample;
