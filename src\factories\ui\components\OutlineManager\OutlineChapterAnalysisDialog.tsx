"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Outline } from '../../types/outline';
import { Chapter } from '@/lib/db/dexie';
import { chapterRepository } from '@/lib/db/repositories';
import ChapterSelector from './ChapterSelector';
import { ChapterAnalysisService, ChapterAnalysisResult } from './assistant/services/ChapterAnalysisService';
import { ExampleInjectionManager, ExampleConfig } from './assistant/services/ExampleInjectionManager';
import { configService } from '@/services/configService';

interface OutlineChapterAnalysisDialogProps {
  isOpen: boolean;
  onClose: () => void;
  outline: Outline | null;
  bookId: string;
  buttonPosition?: { x: number; y: number };
}

type DialogStep = 'selection' | 'analysis' | 'result';

const OutlineChapterAnalysisDialog: React.FC<OutlineChapterAnalysisDialogProps> = ({
  isOpen,
  onClose,
  outline,
  bookId,
  buttonPosition
}) => {
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<DialogStep>('selection');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<ChapterAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisLog, setAnalysisLog] = useState('');
  const [exampleName, setExampleName] = useState('');
  const [chapters, setChapters] = useState<Chapter[]>([]);

  // 加载章节数据
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
    }
  }, [isOpen, bookId]);

  const loadChapters = async () => {
    try {
      const chapterData = await chapterRepository.getAllByBookId(bookId);
      setChapters(chapterData);
    } catch (error) {
      console.error('加载章节失败:', error);
      setError('加载章节数据失败');
    }
  };

  // 重置状态
  const resetState = useCallback(() => {
    setSelectedChapterIds([]);
    setCurrentStep('selection');
    setIsAnalyzing(false);
    setAnalysisResult(null);
    setError(null);
    setAnalysisProgress(0);
    setAnalysisLog('');
    setExampleName('');
  }, []);

  // 关闭对话框
  const handleClose = useCallback(() => {
    resetState();
    onClose();
  }, [resetState, onClose]);

  // 开始分析
  const handleStartAnalysis = useCallback(async () => {
    if (selectedChapterIds.length === 0) {
      setError('请选择要分析的章节');
      return;
    }

    setIsAnalyzing(true);
    setCurrentStep('analysis');
    setError(null);
    setAnalysisProgress(0);
    setAnalysisLog('开始分析章节内容...\n');

    try {
      // 获取选中的章节
      const selectedChapters = selectedChapterIds
        .map(id => chapters.find(chapter => chapter.id === id))
        .filter(chapter => chapter !== undefined) as Chapter[];

      if (selectedChapters.length === 0) {
        throw new Error('未找到选中的章节');
      }

      // 合并章节内容
      const combinedContent = selectedChapters
        .map(chapter => `【${chapter.title}】\n${chapter.content || ''}`)
        .join('\n\n');

      setAnalysisLog(prev => prev + `正在分析 ${selectedChapters.length} 个章节...\n`);

      // 获取AI配置
      const aiConfig = await configService.getAIConfig();

      if (!aiConfig.apiKey) {
        throw new Error('请先配置AI API密钥');
      }

      // 创建分析服务
      const analysisService = new ChapterAnalysisService(
        aiConfig.apiKey,
        'https://api.openai.com/v1' // 使用默认URL
      );

      // 执行分析
      const result = await analysisService.analyzeChapterContent(
        combinedContent,
        [], // TODO: 传入ACE框架
        (chunk: string) => {
          setAnalysisLog(prev => prev + chunk);
        }
      );

      if (result.success) {
        setAnalysisResult(result);
        setExampleName(`章节分析_${selectedChapters.map(c => c.title).join('_')}_${new Date().toLocaleString()}`);
        setCurrentStep('result');
        setAnalysisLog(prev => prev + '\n\n✅ 分析完成！');
      } else {
        throw new Error(result.error || '分析失败');
      }
    } catch (error) {
      console.error('分析失败:', error);
      setError(error instanceof Error ? error.message : '分析失败');
      setAnalysisLog(prev => prev + `\n\n❌ 分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(100);
    }
  }, [selectedChapterIds, chapters]);

  // 保存示例
  const handleSaveExample = useCallback(() => {
    if (!analysisResult || !exampleName.trim()) {
      setError('请输入示例名称');
      return;
    }

    try {
      const savedExample = ExampleInjectionManager.saveExample(
        exampleName,
        analysisResult.plotPoints,
        analysisResult.overallStyle,
        analysisResult.mainCharacters,
        analysisResult.conflictLevel,
        analysisResult.emotionalTone
      );

      // 自动激活新保存的示例
      ExampleInjectionManager.activateExample(savedExample.id);

      setAnalysisLog(prev => prev + '\n\n✅ 示例已保存并激活！');
      
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (error) {
      console.error('保存示例失败:', error);
      setError('保存示例失败');
    }
  }, [analysisResult, exampleName, handleClose]);

  // 获取可选择的章节
  const getSelectableChapters = (): Chapter[] => {
    return chapters;
  };

  if (!isOpen) return null;

  const dialogContent = (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* 对话框内容 */}
          <motion.div
            initial={{ 
              opacity: 0, 
              scale: 0.9,
              x: buttonPosition ? buttonPosition.x - window.innerWidth / 2 : 0,
              y: buttonPosition ? buttonPosition.y - window.innerHeight / 2 : 0
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.9,
              x: buttonPosition ? buttonPosition.x - window.innerWidth / 2 : 0,
              y: buttonPosition ? buttonPosition.y - window.innerHeight / 2 : 0
            }}
            transition={{ 
              type: "spring", 
              damping: 25, 
              stiffness: 300,
              duration: 0.3
            }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800">章节内容分析</h2>
                  <p className="text-sm text-gray-600">分析章节内容并生成JSON示例</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容区域 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              {currentStep === 'selection' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">选择要分析的章节</h3>
                    <ChapterSelector
                      chapters={getSelectableChapters()}
                      selectedIds={selectedChapterIds}
                      onSelectionChange={setSelectedChapterIds}
                      maxSelection={10}
                      showRangeSelection={true}
                    />
                  </div>

                  {selectedChapterIds.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-800 mb-2">已选择章节</h4>
                      <p className="text-sm text-blue-600">
                        将分析 {selectedChapterIds.length} 个章节的内容，提取剧情点结构
                      </p>
                    </div>
                  )}
                </div>
              )}

              {currentStep === 'analysis' && (
                <div className="space-y-6">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-800">正在分析章节内容</h3>
                    <p className="text-sm text-gray-600 mt-2">请稍候，AI正在分析您的章节内容...</p>
                  </div>

                  {analysisLog && (
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto">
                      <pre className="whitespace-pre-wrap">{analysisLog}</pre>
                    </div>
                  )}
                </div>
              )}

              {currentStep === 'result' && analysisResult && (
                <div className="space-y-6">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-800">分析完成</h3>
                    <p className="text-sm text-gray-600 mt-2">成功提取了 {analysisResult.plotPoints.length} 个剧情点</p>
                  </div>

                  {/* 分析结果展示 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-800">整体风格</h4>
                      <p className="text-sm text-blue-600 mt-1">{analysisResult.overallStyle}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-800">情感基调</h4>
                      <p className="text-sm text-green-600 mt-1">{analysisResult.emotionalTone}</p>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-medium text-yellow-800">主要角色</h4>
                      <p className="text-sm text-yellow-600 mt-1">{analysisResult.mainCharacters.join(', ')}</p>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <h4 className="font-medium text-red-800">冲突等级</h4>
                      <p className="text-sm text-red-600 mt-1">{analysisResult.conflictLevel}/5</p>
                    </div>
                  </div>

                  {/* 剧情点列表 */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-3">提取的剧情点</h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {analysisResult.plotPoints.map((point, index) => (
                        <div key={index} className="bg-white p-3 rounded border">
                          <h5 className="font-medium text-sm">剧情点 {point.order}</h5>
                          <p className="text-sm text-gray-700 mt-1">{point.content}</p>
                          <div className="mt-2 text-xs text-gray-500">
                            <p><strong>避免：</strong>{point.avoidWriting}</p>
                            <p><strong>推荐：</strong>{point.shouldWriting}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 示例名称输入 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">示例名称</label>
                    <input
                      type="text"
                      value={exampleName}
                      onChange={(e) => setExampleName(e.target.value)}
                      placeholder="为这个示例起个名字..."
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}
            </div>

            {/* 底部操作按钮 */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                {currentStep === 'selection' && (
                  <span>选择章节进行分析</span>
                )}
                {currentStep === 'analysis' && (
                  <span>分析进行中...</span>
                )}
                {currentStep === 'result' && (
                  <span>分析完成，可以保存示例</span>
                )}
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  {currentStep === 'result' ? '关闭' : '取消'}
                </button>
                
                {currentStep === 'selection' && (
                  <button
                    onClick={handleStartAnalysis}
                    disabled={selectedChapterIds.length === 0}
                    className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>开始分析</span>
                  </button>
                )}

                {currentStep === 'result' && (
                  <button
                    onClick={handleSaveExample}
                    disabled={!exampleName.trim()}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>保存示例</span>
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );

  return createPortal(dialogContent, document.body);
};

export default OutlineChapterAnalysisDialog;
