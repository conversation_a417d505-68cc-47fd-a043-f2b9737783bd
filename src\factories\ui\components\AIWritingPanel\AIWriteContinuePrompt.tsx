"use client";

import React, { useState } from 'react';

interface AIWriteContinuePromptProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (prompt: string) => void;
  initialPrompt?: string;
  mode?: 'continue' | 'rewrite' | 'analyze' | 'new';
}

/**
 * AI写作继续对话提示组件
 * 用于输入继续对话的提示
 */
const AIWriteContinuePrompt: React.FC<AIWriteContinuePromptProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialPrompt = '',
  mode = 'continue'
}) => {
  const [prompt, setPrompt] = useState(initialPrompt);

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim()) {
      onSubmit(prompt);
      setPrompt('');
    }
  };

  // 根据不同的模式显示不同的标题和提示文本
  const getTitle = () => {
    switch (mode) {
      case 'continue':
        return '继续创作';
      case 'rewrite':
        return '重写内容';
      case 'analyze':
        return '分析内容';
      default:
        return '继续对话';
    }
  };

  const getPlaceholder = () => {
    switch (mode) {
      case 'continue':
        return '请输入继续创作的要求，AI将在已有内容的基础上继续创作...';
      case 'rewrite':
        return '请输入重写的要求，AI将重写已有内容，但保持核心情节不变...';
      case 'analyze':
        return '请输入分析的要求，AI将对已有内容进行分析和评价...';
      default:
        return '请输入继续对话的提示词...';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-50 transition-all duration-300 ease-in-out">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-medium text-gray-900">{getTitle()}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex items-end space-x-3">
          <div className="flex-1">
            <label htmlFor="continue-prompt" className="block text-sm font-medium text-gray-700 mb-1">
              请输入您的指示
            </label>
            <textarea
              id="continue-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              rows={3}
              placeholder={getPlaceholder()}
              autoFocus
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm flex items-center h-10"
            disabled={!prompt.trim()}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
            发送
          </button>
        </form>
      </div>
    </div>
  );
};

export default AIWriteContinuePrompt;
