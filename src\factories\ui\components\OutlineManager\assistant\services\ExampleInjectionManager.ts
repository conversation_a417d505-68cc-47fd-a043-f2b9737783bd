/**
 * 示例注入管理器
 * 管理AI生成的JSON示例的存储、注入和替换
 */

import { PlotPointExample } from './ChapterAnalysisService';

export interface ExampleConfig {
  id: string;
  name: string;
  plotPoints: PlotPointExample[];
  overallStyle: string;
  mainCharacters: string[];
  conflictLevel: number;
  // 新增的扩展字段
  chapterTitle?: string;
  chapterDescription?: string;
  chapterStyle?: string;
  chapterTechniques?: string[];
  chapterGoals?: string[];
  rhythmPhase?: string;
  rhythmGuidance?: string;
  plotTitle?: string;
  plotDescription?: string;
  plotType?: string;
  suspenseElements?: string[];
  relatedCharacters?: string[];
  creativeNotes?: string;
  themes?: string[];
  writingTechniques?: string[];
  createdAt: Date;
  isActive: boolean;
}

export class ExampleInjectionManager {
  private static readonly STORAGE_KEY = 'ai_plot_examples';
  private static readonly ACTIVE_EXAMPLE_KEY = 'active_plot_example';

  /**
   * 保存新的示例配置
   */
  static saveExample(
    name: string,
    plotPoints: PlotPointExample[],
    overallStyle: string,
    mainCharacters: string[],
    conflictLevel: number,
    extendedFields?: {
      chapterTitle?: string;
      chapterDescription?: string;
      chapterStyle?: string;
      chapterTechniques?: string[];
      chapterGoals?: string[];
      rhythmPhase?: string;
      rhythmGuidance?: string;
      plotTitle?: string;
      plotDescription?: string;
      plotType?: string;
      suspenseElements?: string[];
      relatedCharacters?: string[];
      creativeNotes?: string;
      themes?: string[];
      writingTechniques?: string[];
    }
  ): ExampleConfig {
    const example: ExampleConfig = {
      id: `example_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`,
      name,
      plotPoints,
      overallStyle,
      mainCharacters,
      conflictLevel,

      // 添加扩展字段
      ...extendedFields,
      createdAt: new Date(),
      isActive: false
    };

    const examples = this.getAllExamples();
    examples.push(example);
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(examples));
    
    return example;
  }

  /**
   * 获取所有示例配置
   */
  static getAllExamples(): ExampleConfig[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const examples = JSON.parse(stored);
      return examples.map((ex: any) => ({
        ...ex,
        createdAt: new Date(ex.createdAt)
      }));
    } catch (error) {
      console.error('获取示例配置失败:', error);
      return [];
    }
  }

  /**
   * 激活指定的示例配置
   */
  static activateExample(exampleId: string): boolean {
    try {
      const examples = this.getAllExamples();
      const targetExample = examples.find(ex => ex.id === exampleId);
      
      if (!targetExample) {
        console.error('未找到指定的示例配置:', exampleId);
        return false;
      }

      // 取消所有示例的激活状态
      examples.forEach(ex => ex.isActive = false);
      
      // 激活目标示例
      targetExample.isActive = true;
      
      // 保存更新后的配置
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(examples));
      
      // 保存当前激活的示例ID
      localStorage.setItem(this.ACTIVE_EXAMPLE_KEY, exampleId);
      
      return true;
    } catch (error) {
      console.error('激活示例配置失败:', error);
      return false;
    }
  }

  /**
   * 获取当前激活的示例配置
   */
  static getActiveExample(): ExampleConfig | null {
    try {
      const activeId = localStorage.getItem(this.ACTIVE_EXAMPLE_KEY);
      if (!activeId) return null;
      
      const examples = this.getAllExamples();
      return examples.find(ex => ex.id === activeId && ex.isActive) || null;
    } catch (error) {
      console.error('获取激活示例配置失败:', error);
      return null;
    }
  }

  /**
   * 删除示例配置
   */
  static deleteExample(exampleId: string): boolean {
    try {
      const examples = this.getAllExamples();
      const filteredExamples = examples.filter(ex => ex.id !== exampleId);
      
      if (filteredExamples.length === examples.length) {
        console.error('未找到要删除的示例配置:', exampleId);
        return false;
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredExamples));
      
      // 如果删除的是当前激活的示例，清除激活状态
      const activeId = localStorage.getItem(this.ACTIVE_EXAMPLE_KEY);
      if (activeId === exampleId) {
        localStorage.removeItem(this.ACTIVE_EXAMPLE_KEY);
      }
      
      return true;
    } catch (error) {
      console.error('删除示例配置失败:', error);
      return false;
    }
  }

  /**
   * 停用当前示例，恢复默认示例
   */
  static deactivateCurrentExample(): boolean {
    try {
      const examples = this.getAllExamples();
      examples.forEach(ex => ex.isActive = false);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(examples));
      localStorage.removeItem(this.ACTIVE_EXAMPLE_KEY);
      
      return true;
    } catch (error) {
      console.error('停用示例配置失败:', error);
      return false;
    }
  }

  /**
   * 验证JSON对象的格式正确性
   */
  private static validateJSON(obj: any): boolean {
    try {
      // 尝试序列化和反序列化来验证JSON格式
      const jsonString = JSON.stringify(obj);
      JSON.parse(jsonString);
      return true;
    } catch (error) {
      console.error('JSON验证失败:', error);
      return false;
    }
  }

  /**
   * 获取用于MessageBuilder的JSON示例
   */
  static getMessageBuilderExample(): any {
    const activeExample = this.getActiveExample();

    if (!activeExample || !activeExample.plotPoints.length) {
      return null; // 返回null表示使用默认示例
    }

    // 转换为MessageBuilder格式
    const exampleData = {
      message: "为您创建了[章节标题]",
      changes: [
        {
          type: "create",
          nodeId: "chapter_{timestamp}_{sequence}",
          data: {
            title: "[章节标题]",
            type: "chapter",
            description: "[章节的剧情概要和主要内容发展]",
            creativeNotes: "[章节的创作指导和写作要点，包含核心冲突和情感基调]",
            chapterStyle: activeExample.overallStyle,
            chapterTechniques: ["[写作手法1]", "[写作手法2]", "[写作手法3]"],
            chapterGoals: ["[章节目标1]", "[章节目标2]", "[章节目标3]"],
            rhythmPhase: "[节奏阶段]",
            rhythmGuidance: "[基于节奏阶段的具体创作指导]"
          },
          parentId: "[父节点ID]"
        },
        {
          type: "create",
          nodeId: "plot_{timestamp}_{sequence}",
          data: {
            title: "[剧情节点标题]",
            type: "plot",
            description: "[剧情节点的核心内容和发展方向]",
            creativeNotes: "[剧情节点的创作要点和写作指导]",
            plotPoints: activeExample.plotPoints,
            plotType: "[冲突类型]",
            relatedCharacters: activeExample.mainCharacters,
            conflictLevel: activeExample.conflictLevel,
            suspenseElements: ["[悬念要素1]", "[悬念要素2]", "[悬念要素3]"],
            emotionalTone: "[情感基调描述]"
          },
          parentId: "[父节点ID]"
        }
      ],
      metadata: {
        operationType: "create",
        confidence: 0.95,
      }
    };

    // 验证生成的JSON格式
    if (!this.validateJSON(exampleData)) {
      console.error('动态示例JSON格式验证失败，使用默认示例');
      return null; // 返回null使用默认示例
    }

    return exampleData;
  }

  /**
   * 导出示例配置
   */
  static exportExamples(): string {
    const examples = this.getAllExamples();
    return JSON.stringify(examples, null, 2);
  }

  /**
   * 导入示例配置
   */
  static importExamples(jsonData: string): boolean {
    try {
      const importedExamples = JSON.parse(jsonData);
      
      if (!Array.isArray(importedExamples)) {
        throw new Error('导入数据格式错误');
      }
      
      // 验证数据格式
      for (const example of importedExamples) {
        if (!example.id || !example.name || !example.plotPoints) {
          throw new Error('导入数据缺少必要字段');
        }
      }
      
      // 合并现有示例
      const existingExamples = this.getAllExamples();
      const mergedExamples = [...existingExamples];
      
      for (const importedExample of importedExamples) {
        // 检查是否已存在相同ID的示例
        const existingIndex = mergedExamples.findIndex(ex => ex.id === importedExample.id);
        if (existingIndex >= 0) {
          // 更新现有示例
          mergedExamples[existingIndex] = {
            ...importedExample,
            createdAt: new Date(importedExample.createdAt)
          };
        } else {
          // 添加新示例
          mergedExamples.push({
            ...importedExample,
            createdAt: new Date(importedExample.createdAt)
          });
        }
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mergedExamples));
      
      return true;
    } catch (error) {
      console.error('导入示例配置失败:', error);
      return false;
    }
  }
}
