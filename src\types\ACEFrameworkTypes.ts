/**
 * ACE框架扩展类型定义
 * 支持简介关键词、简介框架、大纲框架三种分类
 */

// 框架分类类型
export type ACEFrameworkCategory = 'synopsis-keywords' | 'synopsis-framework' | 'outline-framework' | 'extracted-elements';

// 分类信息接口
export interface FrameworkCategoryInfo {
  id: ACEFrameworkCategory;
  name: string;
  description: string;
  icon: string;
  color: string;
}

// 拆解元素接口
export interface ExtractedElement {
  id: string;
  category: string;
  elements: string[];
  sourceText: string;
  extractedAt: Date;
  confidence?: number;
  tags?: string[];
}

// 扩展的ACE框架接口
export interface ExtendedACEFramework {
  id: string;
  name: string;
  category: ACEFrameworkCategory;
  pattern?: string;
  description: string;
  effectiveness: number;
  examples: string[];
  
  // 简介关键词特有字段
  keywordElements?: Array<{
    text: string;
    hotness: number;
    tags: string[];
    frequency?: number;
  }>;
  
  // 简介框架特有字段
  synopsisStructure?: {
    openingTechniques: string[];
    conflictPresentation: string[];
    characterIntroduction: string[];
    worldBuilding: string[];
    hookStrategies: string[];
  };

  // 简介框架的结构分析数据
  structureAnalysis?: {
    opening: string;
    development: string;
    ending: string;
  };

  // 可复用模板
  reusableTemplates?: Array<{
    id: string;
    name: string;
    pattern: string;
    description: string;
    techniques: string[];
    effectiveness: number;
  }>;
  
  // 大纲框架字段（保持兼容）
  writingTechniques?: Array<{
    id: string;
    name: string;
    category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
    description: string;
    examples: string[];
    techniqueType: string;
    effectiveness: number;
  }>;
  
  styleCharacteristics?: {
    layoutTechniques?: {
      paragraphStructure?: string[];
      lineBreakStrategy?: string[];
      rhythmControl?: string[];
      visualImpact?: number;
    };
    omissionAndEmphasis?: {
      omittedElements?: string[];
      emphasizedElements?: string[];
      contrastTechniques?: string[];
      suspensePoints?: string[];
    };
  };
  
  plotAnalysis?: any;
  
  // 拆解元素特有字段
  extractedElements?: Array<{
    text: string;
    category: string;
    confidence: number;
    tags: string[];
    sourceText?: string;
  }>;
  sourceText?: string;
  extractionMethod?: 'manual' | 'ai-extracted';
  extractedAt?: Date;
  confidence?: number;

  // 通用元数据
  createdAt?: Date;
  updatedAt?: Date;
  usageCount?: number;
  tags?: string[];
}

// 框架分类常量
export const FRAMEWORK_CATEGORIES: FrameworkCategoryInfo[] = [
  {
    id: 'synopsis-keywords',
    name: '市场关键词',
    description: '高热度市场关键词元素，提升作品商业价值和读者吸引力',
    icon: '🔥',
    color: '#FF6B6B'
  },
  {
    id: 'synopsis-framework',
    name: '核心剧情框架',
    description: '核心剧情结构模板和专业构建技巧',
    icon: '📝',
    color: '#4ECDC4'
  },
  {
    id: 'outline-framework',
    name: '大纲框架',
    description: '故事大纲和剧情结构框架',
    icon: '📚',
    color: '#45B7D1'
  },
  {
    id: 'extracted-elements',
    name: '拆解元素',
    description: 'AI智能拆解的文本元素，包括关键词、概念、技巧等',
    icon: '🔍',
    color: '#9333EA'
  }
];

// 获取分类信息的工具函数
export const getCategoryInfo = (category: ACEFrameworkCategory): FrameworkCategoryInfo => {
  return FRAMEWORK_CATEGORIES.find(cat => cat.id === category) || FRAMEWORK_CATEGORIES[0];
};

// 分类过滤函数
export const filterFrameworksByCategory = (
  frameworks: ExtendedACEFramework[], 
  category: ACEFrameworkCategory
): ExtendedACEFramework[] => {
  return frameworks.filter(framework => framework.category === category);
};

// 框架数据验证函数
export const validateFramework = (framework: any): framework is ExtendedACEFramework => {
  return (
    framework &&
    typeof framework.id === 'string' &&
    typeof framework.name === 'string' &&
    typeof framework.category === 'string' &&
    FRAMEWORK_CATEGORIES.some(cat => cat.id === framework.category)
  );
};

// 预置简介关键词框架数据
export const PRESET_SYNOPSIS_KEYWORDS: ExtendedACEFramework[] = [
  {
    id: 'keywords-opening-flow',
    name: '开局流关键词',
    category: 'synopsis-keywords',
    description: '现代网文开局常用的吸睛词汇',
    effectiveness: 9,
    examples: ['开局拐跑', '退婚流', '觉醒系统'],
    keywordElements: [
      { text: '开局', hotness: 10, tags: ['开局流', '热门'] },
      { text: '拐跑', hotness: 9, tags: ['开局流', '震撼'] },
      { text: '退婚', hotness: 9, tags: ['开局流', '经典'] },
      { text: '觉醒', hotness: 8, tags: ['开局流', '转折'] },
      { text: '系统', hotness: 8, tags: ['开局流', '金手指'] }
    ]
  },
  {
    id: 'keywords-emotion-reaction',
    name: '情绪反应词',
    category: 'synopsis-keywords',
    description: '表达强烈情绪反应的网络用语',
    effectiveness: 8,
    examples: ['急眼了', '人麻了', '傻眼'],
    keywordElements: [
      { text: '急眼了', hotness: 10, tags: ['情绪', '网络用语'] },
      { text: '人麻了', hotness: 9, tags: ['情绪', '网络用语'] },
      { text: '傻眼', hotness: 8, tags: ['情绪', '惊讶'] },
      { text: '后悔了', hotness: 8, tags: ['情绪', '反转'] },
      { text: '慌了', hotness: 7, tags: ['情绪', '紧张'] }
    ]
  },
  {
    id: 'keywords-time-span',
    name: '时间跨度词',
    category: 'synopsis-keywords',
    description: '表达时间积累和成长的关键词',
    effectiveness: 8,
    examples: ['挂机百年', '闭关万年', '重活一世'],
    keywordElements: [
      { text: '挂机百年', hotness: 9, tags: ['时间', '积累'] },
      { text: '闭关万年', hotness: 8, tags: ['时间', '修炼'] },
      { text: '沉睡千年', hotness: 7, tags: ['时间', '觉醒'] },
      { text: '重活一世', hotness: 8, tags: ['时间', '重生'] },
      { text: '穿越回来', hotness: 7, tags: ['时间', '穿越'] }
    ]
  }
];

// 预置简介框架数据
export const PRESET_SYNOPSIS_FRAMEWORKS: ExtendedACEFramework[] = [
  {
    id: 'synopsis-conflict-reveal',
    name: '冲突揭示框架',
    category: 'synopsis-framework',
    pattern: '{主角身份} + {意外事件} + {隐藏真相} + {情感冲击}',
    description: '通过逐步揭示冲突来构建简介张力',
    effectiveness: 9,
    examples: [
      '平凡学生意外获得神秘力量，却发现这是一场针对他的阴谋',
      '退休老兵重返战场，只为寻找失踪多年的战友真相'
    ],
    synopsisStructure: {
      openingTechniques: ['身份反差', '意外事件', '命运转折'],
      conflictPresentation: ['隐藏真相', '道德冲突', '生死抉择'],
      characterIntroduction: ['平凡开局', '特殊能力', '内心挣扎'],
      worldBuilding: ['现实基础', '超自然元素', '规则暗示'],
      hookStrategies: ['悬念设置', '情感共鸣', '好奇心激发']
    }
  },
  {
    id: 'synopsis-reversal-pattern',
    name: '反转模式框架',
    category: 'synopsis-framework',
    pattern: '{表面现象} + {真实情况} + {反转冲击} + {新的挑战}',
    description: '利用认知反转创造简介吸引力',
    effectiveness: 8,
    examples: [
      '人人敬仰的英雄，实际上是最大的反派',
      '看似废柴的主角，其实拥有最强的隐藏能力'
    ],
    synopsisStructure: {
      openingTechniques: ['常规设定', '表面印象', '读者预期'],
      conflictPresentation: ['真相揭露', '认知颠覆', '价值冲突'],
      characterIntroduction: ['刻板印象', '隐藏特质', '真实面目'],
      worldBuilding: ['表层规则', '深层真相', '隐秘体系'],
      hookStrategies: ['预期打破', '认知失调', '真相渴望']
    }
  }
];
