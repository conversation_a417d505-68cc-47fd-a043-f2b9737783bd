"use client";

/**
 * 多角色消息构建器
 * 用于构建多角色消息数组，以便更精准地控制AI生成内容
 */
export class MessageBuilder {
  private messages: Array<{ role: string; content: string; isSystemGenerated?: boolean; isContextMessage?: boolean }> = [];

  /**
   * 添加系统消息
   * @param content 消息内容
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addSystemMessage(content: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    this.messages.push({
      role: 'system',
      content,
      isSystemGenerated,
      isContextMessage
    });
    return this;
  }

  /**
   * 添加助手消息
   * @param content 消息内容
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantMessage(content: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    this.messages.push({
      role: 'assistant',
      content,
      isSystemGenerated,
      isContextMessage
    });
    return this;
  }

  /**
   * 添加用户消息
   * @param content 消息内容
   * @param importantRequest 重要请求（会追加到消息末尾）
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addUserMessage(content: string, importantRequest?: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    let finalContent = content;

    // 如果有重要请求，追加到消息末尾
    if (importantRequest) {
      finalContent += `\n\n重要请求：${importantRequest}`;
    }

    this.messages.push({
      role: 'user',
      content: finalContent,
      isSystemGenerated,
      isContextMessage
    });
    return this;
  }

  /**
   * 添加助手展示信息消息
   * 这是一个特殊的助手消息，用于展示已有信息
   * @param info 要展示的信息
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantInfoMessage(info: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    return this.addAssistantMessage(`以下是已有信息：\n\n${info}`, isSystemGenerated, isContextMessage);
  }

  /**
   * 添加助手确认任务消息
   * 这是一个特殊的助手消息，用于确认任务
   * @param taskName 任务名称
   * @param targetName 目标名称
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addAssistantConfirmMessage(taskName: string, targetName: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    return this.addAssistantMessage(`我将为您生成一段关于"${targetName}"的${taskName}。我会直接给出结果，不会解释我的思考过程。`, isSystemGenerated, isContextMessage);
  }

  /**
   * 添加用户直接生成指令消息
   * 这是一个特殊的用户消息，用于要求直接生成内容
   * @param contentType 内容类型
   * @param isSystemGenerated 是否为系统生成的预设消息
   * @param isContextMessage 是否为上下文消息
   * @returns 当前构建器实例，支持链式调用
   */
  addUserDirectGenerateMessage(contentType: string, isSystemGenerated: boolean = true, isContextMessage: boolean = false): MessageBuilder {
    return this.addUserMessage(`请直接生成${contentType}，不要有任何解释或前言后语，直接开始描述。`, undefined, isSystemGenerated, isContextMessage);
  }

  /**
   * 获取构建的消息数组
   * @returns 消息数组
   */
  build(): Array<{ role: string; content: string }> {
    return [...this.messages];
  }

  /**
   * 清空消息数组
   * @returns 当前构建器实例，支持链式调用
   */
  clear(): MessageBuilder {
    this.messages = [];
    return this;
  }

  /**
   * 创建一个新的消息构建器实例
   * @returns 新的消息构建器实例
   */
  static create(): MessageBuilder {
    return new MessageBuilder();
  }
}

/**
 * 创建一个新的消息构建器实例
 * @returns 新的消息构建器实例
 */
export function createMessageBuilder(): MessageBuilder {
  return MessageBuilder.create();
}

export default createMessageBuilder;
