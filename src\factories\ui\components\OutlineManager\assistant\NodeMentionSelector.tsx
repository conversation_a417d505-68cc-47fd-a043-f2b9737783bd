"use client";

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { OutlineNodeType } from '../../../types/outline';
import { aiAssistantDataService, MentionSearchItem } from '@/services/aiAssistantDataService';
import { AIAssistantContextType } from '@/lib/db/dexie';
import DataStatusIndicator from './DataStatusIndicator';
import MentionDebugPanel from './MentionDebugPanel';
import { useDataSync, useDataValidation, useSearchOptimization } from './useDataSync';
import './NodeMentionSelector.css';
import './DataStatusIndicator.css';
import './MentionDebugPanel.css';


// 调试工具已移除，问题已修复

interface NodeMentionSelectorProps {
  outline: any;
  bookId: string; // 新增书籍ID参数
  query: string;
  onSelect: (nodeId: string, nodeTitle: string, nodeType?: string) => void;
  onClose: () => void;
}

interface FlatNode {
  id: string;
  title: string;
  type: string;
  level: number;
  path: string[];
}

/**
 * 节点提及选择器组件
 * 用于在输入@符号时显示可选择的节点列表
 * 支持章节、人物、世界观、术语等多种数据类型
 */
const NodeMentionSelector: React.FC<NodeMentionSelectorProps> = ({
  outline,
  bookId,
  query,
  onSelect,
  onClose
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchResults, setSearchResults] = useState<MentionSearchItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showTypeFilter, setShowTypeFilter] = useState(false);
  const [selectedTypes, setSelectedTypes] = useState<(AIAssistantContextType | 'outlineNode')[]>([]);
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // 使用数据同步Hook
  const { refreshData } = useDataSync(bookId, () => {
    // 数据变化时重新搜索
    if (query) {
      searchMultipleDataSources(query);
    }
  });

  // 使用数据验证Hook
  const { validateBookId, checkDataIntegrity } = useDataValidation(bookId);

  // 使用搜索优化Hook
  const { getCachedSearch, setCachedSearch, clearSearchCache } = useSearchOptimization();

  // 将嵌套的节点结构扁平化（保留原有功能）
  const flattenNodes = useCallback((nodes: OutlineNodeType[], level = 0, path: string[] = []): FlatNode[] => {
    const result: FlatNode[] = [];

    nodes.forEach(node => {
      const currentPath = [...path, node.title];
      result.push({
        id: node.id,
        title: node.title,
        type: node.type,
        level,
        path: currentPath
      });

      if (node.children && node.children.length > 0) {
        result.push(...flattenNodes(node.children, level + 1, currentPath));
      }
    });

    return result;
  }, []);

  // 搜索多数据源内容
  const searchMultipleDataSources = useCallback(async (searchQuery: string) => {
    if (!bookId) return;

    setIsLoading(true);
    try {
      let results: MentionSearchItem[] = [];

      if (!searchQuery.trim()) {
        // 如果没有查询词，显示最近使用的内容
        results = await aiAssistantDataService.getRecentlyUsedItems(bookId, 10);
      } else {
        // 搜索所有类型的内容
        const searchTypes = selectedTypes.length > 0 ? selectedTypes : undefined;
        results = await aiAssistantDataService.searchMentionItems(bookId, searchQuery, searchTypes, 15);
      }

      setSearchResults(results);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [bookId, selectedTypes]);

  // 防抖搜索
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchMultipleDataSources(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, searchMultipleDataSources]);

  // 过滤和排序节点（保留原有逻辑作为备用）
  const filteredNodes = useMemo(() => {
    if (!outline?.nodes) return [];

    const allNodes = flattenNodes(outline.nodes);

    if (!query.trim()) {
      return allNodes.slice(0, 10); // 限制显示数量
    }

    const lowerQuery = query.toLowerCase();

    return allNodes
      .filter(node =>
        node.title.toLowerCase().includes(lowerQuery) ||
        node.type.toLowerCase().includes(lowerQuery)
      )
      .sort((a, b) => {
        // 优先显示标题开头匹配的
        const aStartsWith = a.title.toLowerCase().startsWith(lowerQuery);
        const bStartsWith = b.title.toLowerCase().startsWith(lowerQuery);

        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;

        // 然后按层级排序
        if (a.level !== b.level) return a.level - b.level;

        // 最后按标题排序
        return a.title.localeCompare(b.title);
      })
      .slice(0, 8); // 限制显示数量
  }, [outline, query, flattenNodes]);

  // 获取节点类型的中文标签（提前定义）
  const getNodeTypeLabel = useCallback((type: string) => {
    switch (type) {
      case 'volume': return '总纲/卷';
      case 'event': return '事件刚';
      case 'chapter': return '章节';
      case 'plot': return '剧情节点';
      case 'dialogue': return '对话节点';
      case 'synopsis': return '核心故事梗概';
      case 'outlineNode': return '大纲节点';
      case AIAssistantContextType.CHAPTER: return '章节';
      case AIAssistantContextType.CHARACTER: return '人物';
      case AIAssistantContextType.TERMINOLOGY: return '术语';
      case AIAssistantContextType.WORLD_BUILDING: return '世界观';
      default: return type;
    }
  }, []);

  // 合并搜索结果和大纲节点
  const allResults = useMemo(() => {
    const results: (MentionSearchItem | FlatNode)[] = [...searchResults];

    // 如果启用了大纲节点搜索或没有其他结果，添加大纲节点
    if (selectedTypes.includes('outlineNode') || selectedTypes.length === 0 || searchResults.length === 0) {
      const outlineResults = filteredNodes.map(node => ({
        ...node,
        type: 'outlineNode' as const,
        description: `${getNodeTypeLabel(node.type)} • 层级 ${node.level + 1}`
      }));
      results.push(...outlineResults);
    }

    return results.slice(0, 12); // 限制总数量
  }, [searchResults, filteredNodes, selectedTypes, getNodeTypeLabel]);



  // 获取节点类型的图标
  const getNodeTypeIcon = useCallback((type: string) => {
    switch (type) {
      case 'volume':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
          </svg>
        );
      case 'event':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
        );
      case 'chapter':
      case AIAssistantContextType.CHAPTER:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        );
      case 'plot':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z"/>
          </svg>
        );
      case 'dialogue':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
          </svg>
        );
      case 'scene':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        );
      case 'outlineNode':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
        );
      case AIAssistantContextType.CHARACTER:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
        );
      case AIAssistantContextType.TERMINOLOGY:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H3.5C2.67 2 2 2.67 2 3.5v16C2 20.33 2.67 21 3.5 21h17c.83 0 1.5-.67 1.5-1.5v-16C22 2.67 21.33 2 20.5 2z"/>
          </svg>
        );
      case AIAssistantContextType.WORLD_BUILDING:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        );
      default:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
          </svg>
        );
    }
  }, []);

  // 处理节点选择
  const handleNodeSelect = useCallback((item: MentionSearchItem | FlatNode) => {
    // 记录使用统计
    if ('type' in item && item.type !== 'outlineNode' && bookId) {
      aiAssistantDataService.recordUsage(
        bookId,
        item.type as AIAssistantContextType,
        item.id,
        item.title
      ).catch(console.error);
    }

    onSelect(item.id, item.title, 'type' in item ? item.type : undefined);
  }, [onSelect, bookId]);

  // 重置选中索引当过滤结果改变时
  useEffect(() => {
    setSelectedIndex(0);
  }, [allResults]);

  // 处理全局键盘事件
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (allResults.length === 0) return;

      // 只处理选择器相关的键盘事件，不阻止其他事件
      switch (e.key) {
        case 'ArrowDown':
          // 只有在选择器显示时才处理
          if (document.querySelector('.node-mention-selector')) {
            e.preventDefault();
            setSelectedIndex(prev => (prev + 1) % allResults.length);
          }
          break;
        case 'ArrowUp':
          // 只有在选择器显示时才处理
          if (document.querySelector('.node-mention-selector')) {
            e.preventDefault();
            setSelectedIndex(prev => (prev - 1 + allResults.length) % allResults.length);
          }
          break;
        case 'Enter':
          // 只有在选择器显示且有选中项时才处理
          if (document.querySelector('.node-mention-selector') && allResults[selectedIndex]) {
            e.preventDefault();
            handleNodeSelect(allResults[selectedIndex]);
          }
          break;
        case 'Tab':
          // Tab键快速选择第一个匹配项
          if (document.querySelector('.node-mention-selector') && allResults[0]) {
            e.preventDefault();
            handleNodeSelect(allResults[0]);
          }
          break;
        // 移除Escape处理，让父组件处理
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, [allResults, selectedIndex, handleNodeSelect]);

  // 处理节点项键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent, item: MentionSearchItem | FlatNode) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleNodeSelect(item);
    }
  }, [handleNodeSelect]);

  // 处理创建数据
  const handleCreateData = useCallback((type: AIAssistantContextType) => {
    // 这里可以集成到具体的创建流程中
    console.log('创建数据类型:', type);
    // 暂时显示提示，后续可以集成到具体的创建对话框
    const typeName = getNodeTypeLabel(type);
    alert(`请在${typeName}管理面板中创建数据，创建后即可在@功能中使用`);
  }, [getNodeTypeLabel]);

  return (
    <div className="node-mention-selector">
      <div className="mention-header">
        <span>选择内容</span>
        <div className="header-actions">
          {/* 调试按钮 - 仅在开发环境显示 */}
          {process.env.NODE_ENV === 'development' && (
            <button
              className="debug-button"
              onClick={() => setShowDebugPanel(true)}
              title="打开调试面板"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5s-.96.06-1.42.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8z"/>
              </svg>
            </button>
          )}
          <button
            className="mention-close"
            onClick={onClose}
            aria-label="关闭选择器"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* 数据状态指示器 */}
      <DataStatusIndicator
        bookId={bookId}
        onCreateData={handleCreateData}
      />

      <div className="mention-list">
        {isLoading ? (
          <div className="mention-loading">
            <div className="loading-spinner"></div>
            <span>搜索中...</span>
          </div>
        ) : allResults.length === 0 ? (
          <div className="mention-empty">
            <div className="empty-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" opacity="0.3">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div className="empty-title">没有找到相关内容</div>
            <div className="empty-description">
              {query ? `没有找到包含"${query}"的内容` : '当前书籍还没有章节、人物、术语或世界观数据'}
            </div>
            <div className="empty-actions">
              <button
                className="create-data-btn"
                onClick={() => handleCreateData(AIAssistantContextType.CHARACTER)}
                title="创建示例数据"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
                创建数据
              </button>
            </div>
          </div>
        ) : (
          allResults.map((item, index) => {
            const isOutlineNode = 'level' in item;
            const marginLeft = isOutlineNode ? `${item.level * 16}px` : '0px';

            return (
              <div
                key={item.id}
                className={`mention-item ${index === selectedIndex ? 'keyboard-selected' : ''}`}
                onClick={() => handleNodeSelect(item)}
                onKeyDown={(e) => handleKeyDown(e, item)}
                tabIndex={0}
                role="button"
                aria-label={`选择${getNodeTypeLabel(item.type)}: ${item.title}`}
              >
                <div className="mention-item-icon" style={{ marginLeft }}>
                  {getNodeTypeIcon(item.type)}
                </div>

                <div className="mention-item-content">
                  <div className="mention-item-title">{item.title || '未知节点'}</div>
                  <div className="mention-item-meta">
                    <span className={`mention-item-type type-${item.type}`}>
                      {getNodeTypeLabel(item.type)}
                    </span>
                    {'description' in item && item.description && (
                      <span className="mention-item-description">
                        {item.description}
                      </span>
                    )}
                    {'metadata' in item && item.metadata && (
                      <div className="mention-item-details">
                        {/* 显示人物的关键信息 */}
                        {item.type === 'character' && item.metadata.personality && (
                          <span className="detail-item">
                            <strong>性格:</strong> {item.metadata.personality}
                          </span>
                        )}
                        {item.type === 'character' && item.metadata.background && (
                          <span className="detail-item">
                            <strong>背景:</strong> {item.metadata.background}
                          </span>
                        )}
                        {/* 显示术语的分类信息 */}
                        {item.type === 'terminology' && item.metadata.category && (
                          <span className="detail-item">
                            <strong>分类:</strong> {item.metadata.category}
                          </span>
                        )}
                        {/* 显示世界观的分类信息 */}
                        {item.type === 'worldBuilding' && item.metadata.category && (
                          <span className="detail-item">
                            <strong>分类:</strong> {item.metadata.category}
                          </span>
                        )}
                        {/* 显示章节的详细信息 */}
                        {item.type === 'chapter' && (
                          <div className="chapter-details">
                            {item.metadata.order && (
                              <span className="detail-item chapter-order">
                                <strong>第{item.metadata.order}章</strong>
                              </span>
                            )}
                            {item.metadata.wordCount && (
                              <span className="detail-item chapter-wordcount">
                                <strong>{item.metadata.wordCount}字</strong>
                              </span>
                            )}
                            {item.metadata.summary && (
                              <span className="detail-item chapter-summary">
                                <strong>概要:</strong> {item.metadata.summary}
                              </span>
                            )}
                            {item.metadata.content && (
                              <span className="detail-item chapter-preview">
                                <strong>内容预览:</strong> {item.metadata.content.substring(0, 100)}...
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    {isOutlineNode && 'level' in item && item.level && item.level > 0 && (
                      <span className="mention-item-path">
                        {item.path?.slice(0, -1).join(' > ')}
                      </span>
                    )}
                    {'usageCount' in item && item.usageCount && item.usageCount > 0 && (
                      <span className="mention-item-usage">
                        使用 {item.usageCount} 次
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      <div className="mention-footer">
        <small>↑↓ 选择 • <kbd>Enter</kbd> 确认 • <kbd>Tab</kbd> 快选</small>
      </div>

      {/* 取消提示 */}
      <div className="mention-cancel-hint">
        <kbd>Backspace</kbd> 删除@ • <kbd>Esc</kbd> 取消 • <kbd>Space</kbd> 结束
      </div>

      {/* 调试面板 */}
      <MentionDebugPanel
        bookId={bookId}
        isVisible={showDebugPanel}
        onClose={() => setShowDebugPanel(false)}
      />
    </div>
  );
};

export default NodeMentionSelector;
