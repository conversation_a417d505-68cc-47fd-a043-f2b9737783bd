"use client";

import React from 'react';
import IconBase, { IconBaseProps } from './IconBase';

/**
 * 书本图标 - 打开的书本
 * 支持页面翻动效果
 */
const BookIcon: React.FC<Omit<IconBaseProps, 'children'>> = (props) => {
  return (
    <IconBase {...props} className={`book-icon ${props.className || ''}`}>
      {/* 书本主体 */}
      <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
      <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" />
      {/* 书页分割线 */}
      <line x1="12" y1="2" x2="12" y2="22" strokeWidth="1" opacity="0.3" />
      {/* 左页内容线条 */}
      <line x1="6" y1="6" x2="11" y2="6" strokeWidth="1" opacity="0.5" />
      <line x1="6" y1="8" x2="10" y2="8" strokeWidth="1" opacity="0.5" />
      <line x1="6" y1="10" x2="11" y2="10" strokeWidth="1" opacity="0.5" />
      {/* 右页内容线条 */}
      <line x1="13" y1="6" x2="18" y2="6" strokeWidth="1" opacity="0.5" />
      <line x1="13" y1="8" x2="17" y2="8" strokeWidth="1" opacity="0.5" />
      <line x1="13" y1="10" x2="18" y2="10" strokeWidth="1" opacity="0.5" />
      {/* 书签 */}
      <path d="M16 2v6l2-1 2 1V2" strokeWidth="1" opacity="0.7" />
    </IconBase>
  );
};

export default BookIcon;
