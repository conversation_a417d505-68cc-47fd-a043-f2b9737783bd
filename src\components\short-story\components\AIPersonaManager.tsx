/**
 * AI人设管理界面
 * 支持编辑系统提示词、AI自我分析和人设完善
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PhaseType } from './PhaseAIAvatar';
import { AIPersonaConfig, DEFAULT_PERSONAS, CategoryPrompt } from '../../../types/ai-persona';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';
import { PersonaAnalysisService } from '../../../services/ai-persona/PersonaAnalysisService';
import { ChatMessage } from '../../../services/chat/ChatPersistenceService';
import { FeedbackCollectionService } from '../../../services/ai-feedback/FeedbackCollectionService';
import { VerticalAdjustmentPanel } from './PersonaPanel/VerticalAdjustmentPanel';
import { StyleSampleService } from '../../../services/ai/StyleSampleService';
import PersonaFolderManager from './PersonaFolderManager';
import PersonaVersionDialog from './PersonaVersionDialog';
import PersonaCategoryManager from './PersonaCategoryManager';

interface AIPersonaManagerProps {
  phase: PhaseType;
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
}

export const AIPersonaManager: React.FC<AIPersonaManagerProps> = ({
  phase,
  isOpen,
  onClose,
  messages
}) => {
  const [currentConfig, setCurrentConfig] = useState<AIPersonaConfig | null>(null);
  const [editedPrompt, setEditedPrompt] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStage, setAnalysisStage] = useState('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'edit' | 'analysis' | 'manage' | 'vertical' | 'optimize'>('edit');
  const [savedConfigs, setSavedConfigs] = useState<AIPersonaConfig[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveConfigName, setSaveConfigName] = useState('');

  // 优化人设相关状态
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [optimizationStage, setOptimizationStage] = useState('');
  const [optimizedPrompt, setOptimizedPrompt] = useState('');
  const [optimizationGoals, setOptimizationGoals] = useState<string[]>([]);
  const [showOptimizationResult, setShowOptimizationResult] = useState(false);
  const [originalPromptBackup, setOriginalPromptBackup] = useState('');
  const [customGoalInput, setCustomGoalInput] = useState('');
  const [savedOptimizationGoals, setSavedOptimizationGoals] = useState<string[]>([]);
  const [optimizationSummary, setOptimizationSummary] = useState('');
  const [optimizationChanges, setOptimizationChanges] = useState<string[]>([]);

  // 风格样本相关状态
  const [useSamples, setUseSamples] = useState(false);
  const [sampleStats, setSampleStats] = useState<{
    total: number;
    active: number;
    totalWords: number;
    activeWords: number;
  } | null>(null);

  // 新的文件夹式管理状态
  const [showFolderManager, setShowFolderManager] = useState(false);
  const [showVersionDialog, setShowVersionDialog] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [selectedPersonaForVersion, setSelectedPersonaForVersion] = useState<AIPersonaConfig | null>(null);
  const [selectedPersonaForCategory, setSelectedPersonaForCategory] = useState<AIPersonaConfig | null>(null);

  const personaStorageService = PersonaStorageService.getInstance();
  const personaAnalysisService = PersonaAnalysisService.getInstance();
  const feedbackService = FeedbackCollectionService.getInstance();
  const styleSampleService = StyleSampleService.getInstance();

  // 阶段配置
  const phaseConfig = {
    intro: { name: '导语阶段', color: '#3B82F6' },
    setup: { name: '铺垫期阶段', color: '#10B981' },
    compression: { name: '爆发情绪阶段', color: '#F59E0B' },
    climax: { name: '反转阶段', color: '#EF4444' },
    resolution: { name: '解气阶段', color: '#8B5CF6' },
    ending: { name: '结局阶段', color: '#EC4899' },
    // 兼容旧的命名
    buildup: { name: '铺垫阶段', color: '#10B981' },
    custom: { name: '自定义阶段', color: '#6B7280' }
  };

  // 初始化人设配置
  useEffect(() => {
    const loadPersonaConfig = async () => {
      try {
        const config = await personaStorageService.getPersonaConfig(phase);
        setCurrentConfig(config);
        setEditedPrompt(config.systemPrompt);
      } catch (error) {
        console.error('加载人设配置失败:', error);
        const defaultConfig = DEFAULT_PERSONAS[phase];
        setCurrentConfig(defaultConfig);
        setEditedPrompt(defaultConfig.systemPrompt);
      }
    };

    if (isOpen) {
      loadPersonaConfig();
      loadSavedConfigs();
      loadSavedOptimizationGoals();
      loadSampleStats();
    }
  }, [phase, isOpen]);

  // 加载样本统计信息
  const loadSampleStats = () => {
    try {
      const stats = styleSampleService.getSampleStats();
      setSampleStats(stats);
    } catch (error) {
      console.error('加载样本统计失败:', error);
    }
  };

  // 加载保存的优化目标
  const loadSavedOptimizationGoals = () => {
    try {
      const saved = localStorage.getItem(`optimization-goals-${phase}`);
      if (saved) {
        setSavedOptimizationGoals(JSON.parse(saved));
      }
    } catch (error) {
      console.error('加载优化目标失败:', error);
    }
  };

  // 保存优化目标
  const saveOptimizationGoals = (goals: string[]) => {
    try {
      localStorage.setItem(`optimization-goals-${phase}`, JSON.stringify(goals));
      setSavedOptimizationGoals(goals);
    } catch (error) {
      console.error('保存优化目标失败:', error);
    }
  };

  // 加载保存的配置列表
  const loadSavedConfigs = async () => {
    try {
      const versions = await personaStorageService.getVersionHistory(phase);
      setSavedConfigs(versions);
    } catch (error) {
      console.error('加载配置历史失败:', error);
    }
  };

  // 保存人设配置
  const handleSave = async () => {
    if (!currentConfig) return;

    try {
      const updatedConfig = {
        ...currentConfig,
        systemPrompt: editedPrompt
      };

      await personaStorageService.savePersonaConfig(updatedConfig);
      setCurrentConfig(updatedConfig);

      // 显示保存成功提示
      alert('人设配置已保存！');
    } catch (error) {
      console.error('保存人设配置失败:', error);
      alert('保存失败，请重试');
    }
  };

  // 重置为默认配置
  const handleReset = async () => {
    if (confirm('确定要重置为默认人设配置吗？')) {
      try {
        const defaultConfig = await personaStorageService.resetToDefault(phase);
        setCurrentConfig(defaultConfig);
        setEditedPrompt(defaultConfig.systemPrompt);
      } catch (error) {
        console.error('重置配置失败:', error);
        alert('重置失败，请重试');
      }
    }
  };

  // AI自我分析
  const handleAIAnalysis = async () => {
    if (!currentConfig || messages.length < 5) {
      alert('需要至少5条对话记录才能进行AI自我分析');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setAnalysisStage('准备分析...');

    try {
      // 收集反馈数据
      const feedbackData = messages
        .filter(msg => msg.type === 'ai')
        .map(aiMsg => {
          const feedback = feedbackService.getMessageFeedback(aiMsg.id);
          if (feedback) {
            // 找到对应的用户消息
            const aiIndex = messages.findIndex(m => m.id === aiMsg.id);
            const userMsg = aiIndex > 0 ? messages[aiIndex - 1] : null;

            if (userMsg && userMsg.type === 'user') {
              return {
                messageId: aiMsg.id,
                rating: feedback.rating,
                userInput: userMsg.content,
                aiResponse: aiMsg.content,
                comment: feedback.comment
              };
            }
          }
          return null;
        })
        .filter(Boolean) as Array<{messageId: string, rating: string, userInput: string, aiResponse: string, comment?: string}>;

      const result = await personaAnalysisService.analyzeConversation(
        messages,
        phase,
        currentConfig.systemPrompt,
        (progress, stage) => {
          setAnalysisProgress(progress);
          setAnalysisStage(stage);
        },
        feedbackData
      );

      setAnalysisResult(result);
      setActiveTab('analysis');
    } catch (error) {
      console.error('AI自我分析失败:', error);
      alert('AI分析失败，请检查网络连接或稍后重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 应用建议到系统提示词
  const applySuggestion = (suggestion: any) => {
    const currentPrompt = editedPrompt;
    const newPrompt = `${currentPrompt}\n\n【建议交互标记】\n${suggestion.content}`;
    setEditedPrompt(newPrompt);
  };

  // AI优化人设
  const handleAIOptimization = async () => {
    if (!currentConfig) return;

    try {
      setIsOptimizing(true);
      setOptimizationProgress(0);
      setOptimizationStage('准备优化...');
      setShowOptimizationResult(false);

      // 备份原始提示词
      setOriginalPromptBackup(editedPrompt);

      // 调用AI优化服务
      const result = await personaStorageService.optimizePersonaWithAI(
        phase,
        { ...currentConfig, systemPrompt: editedPrompt },
        optimizationGoals.length > 0 ? optimizationGoals : undefined,
        useSamples,
        (progress: number, stage: string) => {
          setOptimizationProgress(progress);
          setOptimizationStage(stage);
        }
      );

      setOptimizedPrompt(result.config.systemPrompt);
      setOptimizationSummary(result.summary);
      setOptimizationChanges(result.changes);
      setShowOptimizationResult(true);

    } catch (error) {
      console.error('AI优化失败:', error);
      alert('AI优化失败，请重试');
    } finally {
      setIsOptimizing(false);
    }
  };

  // 应用优化结果
  const handleApplyOptimization = () => {
    setEditedPrompt(optimizedPrompt);
    setShowOptimizationResult(false);
    alert('优化结果已应用到编辑器');
  };

  // 拒绝优化结果
  const handleRejectOptimization = () => {
    setShowOptimizationResult(false);
    setOptimizedPrompt('');
  };

  // 添加优化目标
  const addOptimizationGoal = (goal: string) => {
    if (goal.trim() && !optimizationGoals.includes(goal.trim())) {
      const newGoals = [...optimizationGoals, goal.trim()];
      setOptimizationGoals(newGoals);
    }
  };

  // 移除优化目标
  const removeOptimizationGoal = (index: number) => {
    const newGoals = optimizationGoals.filter((_, i) => i !== index);
    setOptimizationGoals(newGoals);
  };

  // 添加自定义优化目标
  const handleAddCustomGoal = () => {
    if (customGoalInput.trim()) {
      addOptimizationGoal(customGoalInput.trim());
      setCustomGoalInput('');
    }
  };

  // 保存当前优化目标为模板
  const handleSaveOptimizationGoals = () => {
    if (optimizationGoals.length > 0) {
      saveOptimizationGoals(optimizationGoals);
      alert('优化目标已保存为模板');
    }
  };

  // 加载保存的优化目标模板
  const handleLoadOptimizationGoals = () => {
    if (savedOptimizationGoals.length > 0) {
      setOptimizationGoals([...savedOptimizationGoals]);
    }
  };

  // 另存为新配置
  const handleSaveAs = () => {
    setShowSaveDialog(true);
    setSaveConfigName(`${phaseConfig[phase].name}人设 ${new Date().toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}`);
  };

  // 确认保存新配置
  const handleConfirmSave = async () => {
    if (!saveConfigName.trim()) {
      alert('请输入配置名称');
      return;
    }

    try {
      const newConfig: AIPersonaConfig = {
        id: `custom-${phase}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        phase,
        systemPrompt: editedPrompt,
        customizations: {
          personality: [],
          expertise: [],
          communicationStyle: '',
          responsePattern: ''
        },
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          isDefault: false,
          usageCount: 0
        }
      };

      await personaStorageService.savePersonaConfig(newConfig);
      await loadSavedConfigs();
      setShowSaveDialog(false);
      setSaveConfigName('');
      alert('配置保存成功！');
    } catch (error) {
      console.error('保存配置失败:', error);
      alert('保存失败，请重试');
    }
  };

  // 加载指定配置
  const handleLoadConfig = async (config: AIPersonaConfig) => {
    setCurrentConfig(config);
    setEditedPrompt(config.systemPrompt);
    setActiveTab('edit');
  };

  // 处理使用提示词
  const handleUsePrompt = (prompt: CategoryPrompt) => {
    // 将提示词内容替换当前编辑器的内容
    const replaceText = prompt.name ? `【${prompt.name}】\n${prompt.content}` : prompt.content;
    setEditedPrompt(replaceText);

    // 切换到编辑标签页
    setActiveTab('edit');

    // 关闭分类管理弹窗
    setShowCategoryManager(false);
    setSelectedPersonaForCategory(null);

    console.log('提示词已替换编辑器内容:', prompt);
  };

  // 删除配置
  const handleDeleteConfig = async (configId: string) => {
    if (confirm('确定要删除这个配置吗？')) {
      try {
        // 这里需要添加删除配置的方法到PersonaStorageService
        await loadSavedConfigs();
        alert('配置删除成功！');
      } catch (error) {
        console.error('删除配置失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 标题栏 */}
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{ borderBottomColor: phaseConfig[phase].color + '20' }}
          >
            <div className="flex items-center space-x-3">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: phaseConfig[phase].color }}
              />
              <h2 className="text-lg font-semibold text-gray-800">
                {phaseConfig[phase].name} AI人设管理
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 标签页 */}
          <div className="flex border-b">
            <button
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'edit'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('edit')}
            >
              编辑人设
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'analysis'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('analysis')}
            >
              AI自我分析
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'manage'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('manage')}
            >
              配置管理
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium transition-colors flex items-center space-x-1 ${
                activeTab === 'vertical'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('vertical')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span>回复优化</span>
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium transition-colors flex items-center space-x-1 ${
                activeTab === 'optimize'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('optimize')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>AI优化人设</span>
            </button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-hidden">
            {activeTab === 'edit' && (
              <div className="h-full p-4 flex flex-col">
                <div className="flex-1 flex flex-col space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      系统提示词
                    </label>
                    <textarea
                      value={editedPrompt}
                      onChange={(e) => setEditedPrompt(e.target.value)}
                      className="w-full h-64 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none font-mono text-sm"
                      placeholder="输入AI助手的系统提示词..."
                    />
                  </div>

                  <div className="text-xs text-gray-500">
                    字数: {editedPrompt.length} | 建议长度: 200-800字
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex space-x-2">
                    <button
                      onClick={handleReset}
                      className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                    >
                      重置默认
                    </button>
                    <button
                      onClick={handleAIAnalysis}
                      disabled={isAnalyzing || messages.length < 5}
                      className="px-4 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    >
                      {isAnalyzing ? '分析中...' : 'AI自我分析'}
                    </button>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={handleSaveAs}
                      className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                    >
                      另存为
                    </button>
                    <button
                      onClick={handleSave}
                      className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      保存配置
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'analysis' && (
              <div className="h-full p-4">
                {isAnalyzing ? (
                  <div className="flex flex-col items-center justify-center h-full space-y-4">
                    <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin" />
                    <div className="text-center">
                      <div className="text-lg font-medium text-gray-800">{analysisStage}</div>
                      <div className="text-sm text-gray-600 mt-1">{analysisProgress}%</div>
                    </div>
                  </div>
                ) : analysisResult ? (
                  <div className="space-y-4 h-full overflow-y-auto">
                    {/* 分析结果展示 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-medium text-green-800 mb-2">优势特征</h3>
                        <ul className="space-y-1">
                          {analysisResult.strengths.map((strength: string, index: number) => (
                            <li key={index} className="text-sm text-green-700">• {strength}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-orange-50 p-4 rounded-lg">
                        <h3 className="font-medium text-orange-800 mb-2">改进方向</h3>
                        <ul className="space-y-1">
                          {analysisResult.improvements.map((improvement: string, index: number) => (
                            <li key={index} className="text-sm text-orange-700">• {improvement}</li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* 具体建议 */}
                    <div>
                      <h3 className="font-medium text-gray-800 mb-3">具体改进建议</h3>
                      <div className="space-y-3">
                        {analysisResult.suggestions.map((suggestion: any, index: number) => (
                          <div key={index} className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-800">{suggestion.content}</div>
                                <div className="text-xs text-gray-500 mt-1">
                                  类型: {suggestion.type} | 影响: {suggestion.impact} | 置信度: {Math.round(suggestion.confidence * 100)}%
                                </div>
                              </div>
                              <button
                                onClick={() => applySuggestion(suggestion)}
                                className="ml-3 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                              >
                                应用
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <div className="text-center">
                      <div className="font-medium">暂无分析结果</div>
                      <div className="text-sm mt-1">点击"AI自我分析"开始分析</div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'vertical' && (
              <div className="h-full overflow-hidden">
                <VerticalAdjustmentPanel className="h-full" phase={phase} />
              </div>
            )}

            {activeTab === 'manage' && (
              <div className="h-full">
                <PersonaFolderManager
                  phase={phase}
                  onPersonaSelect={(persona) => {
                    handleLoadConfig(persona);
                  }}
                  onVersionManage={(persona) => {
                    setSelectedPersonaForVersion(persona);
                    setShowVersionDialog(true);
                  }}
                  onCategoryManage={(persona) => {
                    setSelectedPersonaForCategory(persona);
                    setShowCategoryManager(true);
                  }}
                />
              </div>
            )}

            {activeTab === 'optimize' && (
              <div className="h-full p-4">
                {isOptimizing ? (
                  <div className="flex flex-col items-center justify-center h-full space-y-4">
                    <div className="w-16 h-16 border-4 border-purple-200 border-t-purple-500 rounded-full animate-spin" />
                    <div className="text-center">
                      <div className="text-lg font-medium text-gray-800">{optimizationStage}</div>
                      <div className="text-sm text-gray-600 mt-1">{optimizationProgress}%</div>
                    </div>
                  </div>
                ) : showOptimizationResult ? (
                  <div className="space-y-4 h-full overflow-y-auto">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="font-medium text-green-800">AI优化完成</h3>
                      </div>
                      <p className="text-sm text-green-700 mb-3">
                        AI已经分析了你的人设提示词，并生成了优化版本。请仔细对比后决定是否应用。
                      </p>

                      {/* 优化摘要 */}
                      {optimizationSummary && (
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-green-800 mb-1">优化摘要</h4>
                          <p className="text-sm text-green-700 bg-green-100 rounded p-2">
                            {optimizationSummary}
                          </p>
                        </div>
                      )}

                      {/* 主要改进点 */}
                      {optimizationChanges.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-green-800 mb-2">主要改进点</h4>
                          <div className="space-y-1">
                            {optimizationChanges.map((change, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <svg className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                                <span className="text-sm text-green-700">{change}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {/* 原始提示词 */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-gray-800">原始提示词</h4>
                        <div className="bg-gray-50 border rounded-lg p-3 max-h-96 overflow-y-auto">
                          <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                            {originalPromptBackup}
                          </pre>
                        </div>
                      </div>

                      {/* 优化后提示词 */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-gray-800">AI优化版本</h4>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 max-h-96 overflow-y-auto">
                          <pre className="text-sm text-blue-700 whitespace-pre-wrap font-mono">
                            {optimizedPrompt}
                          </pre>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <button
                        onClick={handleRejectOptimization}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                      >
                        放弃优化
                      </button>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            // 创建备份
                            const backup: AIPersonaConfig = {
                              id: `backup-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                              phase,
                              systemPrompt: originalPromptBackup,
                              customizations: currentConfig?.customizations || {
                                personality: [],
                                expertise: [],
                                communicationStyle: '',
                                responsePattern: ''
                              },
                              metadata: {
                                createdAt: new Date(),
                                updatedAt: new Date(),
                                version: 1,
                                isDefault: false,
                                usageCount: 0
                              }
                            };
                            personaStorageService.savePersonaConfig(backup);
                            alert('原始版本已备份');
                          }}
                          className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
                        >
                          备份原版
                        </button>
                        <button
                          onClick={handleApplyOptimization}
                          className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                        >
                          应用优化
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6 h-full overflow-y-auto">
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <h3 className="font-medium text-purple-800">AI人设优化</h3>
                      </div>
                      <p className="text-sm text-purple-700">
                        让AI分析你当前的人设提示词，并提供优化建议。AI会保持原有风格的同时，提升表达效果和实用性。
                      </p>
                    </div>

                    {/* 优化目标设置 */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-800">优化目标 (可选)</h4>
                        <div className="flex space-x-2">
                          {savedOptimizationGoals.length > 0 && (
                            <button
                              onClick={handleLoadOptimizationGoals}
                              className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
                            >
                              加载模板
                            </button>
                          )}
                          {optimizationGoals.length > 0 && (
                            <button
                              onClick={handleSaveOptimizationGoals}
                              className="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
                            >
                              保存模板
                            </button>
                          )}
                        </div>
                      </div>

                      {/* 自定义优化目标输入 */}
                      <div className="space-y-2">
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            value={customGoalInput}
                            onChange={(e) => setCustomGoalInput(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleAddCustomGoal();
                              }
                            }}
                            placeholder="输入自定义优化目标..."
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                          />
                          <button
                            onClick={handleAddCustomGoal}
                            disabled={!customGoalInput.trim()}
                            className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
                          >
                            添加
                          </button>
                        </div>
                        <p className="text-xs text-gray-500">
                          例如：提升用户体验、增强个性化指导、融入语义框架概念、强化原创性引导等
                        </p>
                      </div>

                      {optimizationGoals.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-sm text-gray-600">当前优化目标:</div>
                          <div className="flex flex-wrap gap-2">
                            {optimizationGoals.map((goal, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full"
                              >
                                {goal}
                                <button
                                  onClick={() => removeOptimizationGoal(index)}
                                  className="ml-2 text-purple-500 hover:text-purple-700"
                                >
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 风格样本设置 */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-800">风格样本参考</h4>
                        <a
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            // 这里可以添加跳转到样本管理页面的逻辑
                            alert('样本管理功能开发中...');
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800 underline"
                        >
                          管理样本
                        </a>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <label className="flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={useSamples}
                                onChange={(e) => setUseSamples(e.target.checked)}
                                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                              />
                              <span className="ml-2 text-sm font-medium text-blue-800">
                                使用风格样本
                              </span>
                            </label>
                          </div>
                          <div className="flex-1">
                            <p className="text-xs text-blue-700 mb-2">
                              启用后，AI将参考您保存的风格样本来优化人设，使其更符合您的写作风格和表达习惯。
                            </p>

                            {sampleStats && (
                              <div className="text-xs text-blue-600">
                                {sampleStats.active > 0 ? (
                                  <div className="flex items-center space-x-4">
                                    <span>✓ 激活样本: {sampleStats.active} 个</span>
                                    <span>总字数: {sampleStats.activeWords} 字</span>
                                  </div>
                                ) : (
                                  <div className="text-orange-600">
                                    ⚠ 暂无激活的风格样本，建议先添加样本内容
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 当前提示词预览 */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-800">当前提示词预览</h4>
                      <div className="bg-gray-50 border rounded-lg p-3 h-32 overflow-y-auto">
                        <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                          {editedPrompt.substring(0, 500)}...
                        </pre>
                      </div>
                    </div>

                    {/* 开始优化按钮 */}
                    <div className="flex justify-center pt-4">
                      <button
                        onClick={handleAIOptimization}
                        disabled={!editedPrompt.trim()}
                        className="flex items-center space-x-2 px-8 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span>开始AI优化</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 保存配置对话框 */}
          {showSaveDialog && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
              <div className="bg-white rounded-lg p-6 w-96">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">保存配置</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      配置名称
                    </label>
                    <input
                      type="text"
                      value={saveConfigName}
                      onChange={(e) => setSaveConfigName(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="输入配置名称..."
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => setShowSaveDialog(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      取消
                    </button>
                    <button
                      onClick={handleConfirmSave}
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      保存
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 版本管理弹窗 */}
          <PersonaVersionDialog
            isOpen={showVersionDialog}
            onClose={() => {
              setShowVersionDialog(false);
              setSelectedPersonaForVersion(null);
            }}
            persona={selectedPersonaForVersion}
            onVersionChange={(version) => {
              // 版本切换后可以选择是否加载该版本
              console.log('版本已切换:', version);
            }}
            onCategoryManage={(persona) => {
              setShowVersionDialog(false);
              setSelectedPersonaForCategory(persona);
              setShowCategoryManager(true);
            }}
          />

          {/* 分类管理弹窗 */}
          <PersonaCategoryManager
            isOpen={showCategoryManager}
            onClose={() => {
              setShowCategoryManager(false);
              setSelectedPersonaForCategory(null);
            }}
            persona={selectedPersonaForCategory}
            onUsePrompt={handleUsePrompt}
          />
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
