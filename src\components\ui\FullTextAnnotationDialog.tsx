"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import AnnotationResultViewer from './AnnotationResultViewer';
import { TextSegment as ProcessingTextSegment, Sentence } from '@/factories/ai/services/TextProcessingService';
import { TextSegment } from '@/factories/ai/services/annotation/types/AnnotationTypes';
import { createTextProcessingService } from '@/factories/ai/services/TextProcessingService';
import { createAIAnnotationService } from '@/factories/ai/services/AIAnnotationService';
import ConfirmDialog from '@/factories/ui/components/common/ConfirmDialog';

interface FullTextAnnotationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  chapterContent: string;
  chapterTitle?: string;
  onExport?: (modifiedText: string) => void;
}

/**
 * 全文标注对话框组件
 * 提供全屏的文本标注和修改界面
 */
const FullTextAnnotationDialog: React.FC<FullTextAnnotationDialogProps> = ({
  isOpen,
  onClose,
  bookId,
  chapterContent,
  chapterTitle = '未命名章节',
  onExport
}) => {
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentSegment, setCurrentSegment] = useState(0);
  const [totalSegments, setTotalSegments] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [hasStarted, setHasStarted] = useState(false);
  const [userRequirements, setUserRequirements] = useState<string>('');
  const [showRequirementsInput, setShowRequirementsInput] = useState(true);
  const [showRestartConfirm, setShowRestartConfirm] = useState(false);
  const [currentText, setCurrentText] = useState<string>(chapterContent);
  const [showStreamingResults, setShowStreamingResults] = useState(false);
  const [processingMode, setProcessingMode] = useState<'paragraph' | 'fulltext'>('paragraph');

  // 初始化服务
  const textProcessingService = createTextProcessingService();

  // 初始化AI注释服务，使用统一的AI服务架构
  const aiAnnotationService = useMemo(() => {
    // 使用统一的AI服务工厂
    const { AIServiceFactory, AIServiceType } = require('@/services/ai/BaseAIService');
    return AIServiceFactory.getService(AIServiceType.ANNOTATION);
  }, []);

  // 转换ProcessingTextSegment为AnnotationTextSegment
  const convertToAnnotationSegments = useCallback((processingSegments: ProcessingTextSegment[]): TextSegment[] => {
    return processingSegments.map(segment => ({
      id: segment.id,
      content: segment.content,
      sentences: segment.sentences.map(sentence => ({
        id: sentence.id,
        text: sentence.text,
        processingStatus: 'pending' as const,
        lastProcessedAt: new Date()
      }))
    }));
  }, []);

  // 开始标注处理
  const startAnnotation = useCallback(async () => {
    if (!chapterContent || hasStarted) return;

    try {
      setIsProcessing(true);
      setError(null);
      setHasStarted(true);
      setProgress(0);
      setShowRequirementsInput(false);

      // 第一步：切分文本
      console.log('开始切分文本...');
      const processingSegments = textProcessingService.segmentTextByLength(chapterContent, 400);
      const annotationSegments = convertToAnnotationSegments(processingSegments);
      setSegments(annotationSegments);
      setTotalSegments(annotationSegments.length);

      // 启用流式结果显示
      setShowStreamingResults(true);

      console.log(`文本已切分为 ${annotationSegments.length} 个段落`);

      // 第二步：AI标注（根据处理模式选择不同的方法）
      console.log('开始AI标注，处理模式:', processingMode, '用户要求:', userRequirements);

      if (processingMode === 'fulltext') {
        // 全文处理模式：保持上下文连贯性
        await aiAnnotationService.annotateFullText(annotationSegments, {
          onStart: () => {
            console.log('AI全文标注开始');
          },
          onProgress: (progressPercent, current, total) => {
            setProgress(progressPercent);
            setCurrentSegment(current);
            setTotalSegments(total);
          },
          onSegmentComplete: (annotatedSegment) => {
            // 实时更新已完成的段落
            setSegments(prev => prev.map(seg =>
              seg.id === annotatedSegment.id ? annotatedSegment : seg
            ));
          },
          onComplete: (result) => {
            console.log('AI全文标注完成:', result);
            setSegments(result.segments);
            setIsProcessing(false);
            setShowStreamingResults(false); // 关闭流式显示
          },
          onError: (error) => {
            console.error('AI全文标注失败:', error);
            setError(error.message);
            setIsProcessing(false);
          }
        }, userRequirements);
      } else {
        // 段落式处理模式：独立处理每个段落
        await aiAnnotationService.annotateTextSegments(annotationSegments, {
          onStart: () => {
            console.log('AI段落式标注开始');
          },
          onProgress: (progressPercent, current, total) => {
            setProgress(progressPercent);
            setCurrentSegment(current);
            setTotalSegments(total);
          },
          onSegmentComplete: (annotatedSegment) => {
            // 实时更新已完成的段落
            setSegments(prev => prev.map(seg =>
              seg.id === annotatedSegment.id ? annotatedSegment : seg
            ));
          },
          onComplete: (result) => {
            console.log('AI段落式标注完成:', result);
            setSegments(result.segments);
            setIsProcessing(false);
            setShowStreamingResults(false); // 关闭流式显示
          },
          onError: (error) => {
            console.error('AI段落式标注失败:', error);
            setError(error.message);
            setIsProcessing(false);
          }
        }, userRequirements);
      }

    } catch (error: any) {
      console.error('标注过程失败:', error);
      setError(error.message || '标注过程中发生错误');
      setIsProcessing(false);
    }
  }, [chapterContent, hasStarted, textProcessingService, aiAnnotationService]);

  // 当对话框打开时重置状态或加载保存的数据
  useEffect(() => {
    if (isOpen) {
      // 尝试从localStorage加载保存的数据
      const savedKey = `annotation-${bookId}-${chapterTitle}`;
      const savedData = localStorage.getItem(savedKey);

      if (savedData) {
        try {
          const parsed = JSON.parse(savedData);
          setSegments(parsed.segments || []);
          setUserRequirements(parsed.userRequirements || '');
          setProcessingMode(parsed.processingMode || 'paragraph'); // 加载处理模式
          setHasStarted(parsed.segments && parsed.segments.length > 0);
          setShowRequirementsInput(!parsed.segments || parsed.segments.length === 0);
          console.log('已加载保存的标注数据，处理模式:', parsed.processingMode || 'paragraph');
        } catch (error) {
          console.error('加载保存数据失败:', error);
          // 如果加载失败，重置状态
          resetState();
        }
      } else {
        resetState();
      }
    }
  }, [isOpen, bookId, chapterTitle]);

  // 重置状态的辅助函数
  const resetState = () => {
    setHasStarted(false);
    setShowRequirementsInput(true);
    setUserRequirements('');
    setSegments([]);
    setError(null);
    setProgress(0);
    setShowStreamingResults(false);
    setProcessingMode('paragraph'); // 重置处理模式为默认值
  };

  // 处理句子接受
  const handleSentenceAccept = useCallback((segmentId: string, sentenceId: string) => {
    setSegments(prev => prev.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => {
            if (sentence.id === sentenceId) {
              return { ...sentence, isAccepted: true };
            }
            return sentence;
          })
        };
      }
      return segment;
    }));
  }, []);

  // 处理句子拒绝
  const handleSentenceReject = useCallback((segmentId: string, sentenceId: string) => {
    setSegments(prev => prev.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => {
            if (sentence.id === sentenceId) {
              return { ...sentence, isAccepted: false };
            }
            return sentence;
          })
        };
      }
      return segment;
    }));
  }, []);

  // 处理批量接受
  const handleBatchAccept = useCallback((segmentId: string) => {
    setSegments(prev => prev.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => ({
            ...sentence,
            isAccepted: sentence.aiSuggestion ? true : sentence.isAccepted
          }))
        };
      }
      return segment;
    }));
  }, []);

  // 处理批量拒绝
  const handleBatchReject = useCallback((segmentId: string) => {
    setSegments(prev => prev.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => ({
            ...sentence,
            isAccepted: false
          }))
        };
      }
      return segment;
    }));
  }, []);

  // 处理编辑建议
  const handleSentenceEdit = useCallback((segmentId: string, sentenceId: string, newSuggestion: string) => {
    setSegments(prev => prev.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          sentences: segment.sentences.map(sentence => {
            if (sentence.id === sentenceId) {
              return {
                ...sentence,
                aiSuggestion: newSuggestion,
                modificationType: 'modify' as const
              };
            }
            return sentence;
          })
        };
      }
      return segment;
    }));
  }, []);

  // 保存进度到localStorage
  const handleSave = useCallback((currentSegments: TextSegment[]) => {
    try {
      const savedKey = `annotation-${bookId}-${chapterTitle}`;
      const dataToSave = {
        segments: currentSegments,
        userRequirements,
        processingMode, // 保存处理模式
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(savedKey, JSON.stringify(dataToSave));
      console.log('标注进度已保存');

      // 显示保存成功提示
      setError(null);
      // 可以添加一个临时的成功提示
    } catch (error) {
      console.error('保存进度失败:', error);
      setError('保存进度失败，请稍后重试');
    }
  }, [bookId, chapterTitle, userRequirements]);

  // 重新开始标注（清除保存的数据）
  const handleRestart = useCallback(() => {
    setShowRestartConfirm(true);
  }, []);

  // 确认重新开始
  const confirmRestart = useCallback(() => {
    const savedKey = `annotation-${bookId}-${chapterTitle}`;
    localStorage.removeItem(savedKey);
    resetState();
    setCurrentText(chapterContent); // 重置为原始文本
    setShowRestartConfirm(false);
    console.log('已清除保存的标注数据，重新开始');
  }, [bookId, chapterTitle, chapterContent]);

  // 取消重新开始
  const cancelRestart = useCallback(() => {
    setShowRestartConfirm(false);
  }, []);

  // 处理segments更新（当用户应用修改时）
  const handleSegmentsUpdate = useCallback((updatedSegments: TextSegment[]) => {
    setSegments(updatedSegments);
    console.log('✅ Segments已更新:', {
      segmentsCount: updatedSegments.length,
      totalSentences: updatedSegments.reduce((sum, seg) => sum + seg.sentences.length, 0)
    });
  }, []);

  // 处理文本变更（当用户应用修改时）
  const handleTextChange = useCallback((newText: string) => {
    setCurrentText(newText);
    console.log('✅ 文本已更新:', {
      newLength: newText.length,
      preview: newText.substring(0, 100) + '...'
    });

    // 立即通知父组件文本已更改
    onExport?.(newText);
  }, [onExport]);

  // 处理开始分析选中的句子
  const handleStartAnalysis = useCallback(async (selectedSentenceIds: string[]) => {
    if (selectedSentenceIds.length === 0) {
      setError('请先选择需要分析的句子');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);
      setProgress(0);

      console.log('🎯 开始分析选中的句子:', {
        selectedCount: selectedSentenceIds.length,
        selectedIds: selectedSentenceIds
      });

      // 创建只包含选中句子的临时segments
      const selectedSegments = segments.map(segment => ({
        ...segment,
        sentences: segment.sentences.filter(sentence =>
          selectedSentenceIds.includes(sentence.id)
        )
      })).filter(segment => segment.sentences.length > 0);

      if (selectedSegments.length === 0) {
        setError('没有找到选中的句子');
        setIsProcessing(false);
        return;
      }

      // 使用AI注释服务处理选中的句子
      await aiAnnotationService.annotateTextSegments(selectedSegments, {
        onStart: () => {
          console.log('🚀 开始分析选中句子');
        },
        onProgress: (progressPercent, current, total) => {
          setProgress(progressPercent);
          setCurrentSegment(current);
          setTotalSegments(total);
        },
        onSegmentComplete: (annotatedSegment) => {
          // 更新对应的句子
          setSegments(prev => prev.map(segment => {
            if (segment.id === annotatedSegment.id) {
              return {
                ...segment,
                sentences: segment.sentences.map(sentence => {
                  const annotatedSentence = annotatedSegment.sentences.find(s => s.id === sentence.id);
                  return annotatedSentence || sentence;
                })
              };
            }
            return segment;
          }));
        },
        onComplete: (result) => {
          console.log('✅ 选中句子分析完成:', result);
          setIsProcessing(false);
        },
        onError: (error) => {
          console.error('❌ 选中句子分析失败:', error);
          setError(error.message);
          setIsProcessing(false);
        }
      }, userRequirements);

    } catch (error: any) {
      console.error('❌ 分析选中句子失败:', error);
      setError(error.message || '分析过程中发生错误');
      setIsProcessing(false);
    }
  }, [segments, aiAnnotationService, userRequirements]);

  // 导出修改后的文本
  const handleExport = useCallback(() => {
    // 使用当前文本状态，因为修改已经直接应用到文本中
    onExport?.(currentText);
  }, [currentText, onExport]);

  // 智能推荐处理模式
  const getRecommendedMode = () => {
    const paragraphCount = Math.ceil(chapterContent.length / 400);
    return paragraphCount <= 5 ? 'fulltext' : 'paragraph';
  };

  // 渲染处理模式选择器
  const renderModeSelector = () => {
    const recommendedMode = getRecommendedMode();
    const paragraphCount = Math.ceil(chapterContent.length / 400);

    return (
      <div className="space-y-4">
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">选择处理模式</h4>
          <p className="text-sm text-gray-600">
            根据您的文档长度（约{paragraphCount}个段落），我们推荐使用
            <span className="font-medium text-emerald-600">
              {recommendedMode === 'fulltext' ? '全文处理' : '段落式处理'}
            </span>
            模式
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 段落式处理 */}
          <motion.div
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
              processingMode === 'paragraph'
                ? 'border-emerald-500 bg-emerald-50'
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => setProcessingMode('paragraph')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {recommendedMode === 'paragraph' && (
              <motion.div
                className="absolute -top-2 -right-2 bg-emerald-500 text-white text-xs px-2 py-1 rounded-full"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                推荐
              </motion.div>
            )}

            <div className="flex items-start space-x-3">
              <div className={`w-5 h-5 rounded-full border-2 mt-0.5 transition-colors ${
                processingMode === 'paragraph'
                  ? 'border-emerald-500 bg-emerald-500'
                  : 'border-gray-300'
              }`}>
                {processingMode === 'paragraph' && (
                  <motion.div
                    className="w-full h-full rounded-full bg-white"
                    initial={{ scale: 0 }}
                    animate={{ scale: 0.4 }}
                    transition={{ type: "spring", stiffness: 300, damping: 25 }}
                  />
                )}
              </div>

              <div className="flex-1">
                <h5 className="font-medium text-gray-900 mb-1">段落式处理</h5>
                <p className="text-sm text-gray-600 mb-2">
                  每个段落独立处理，速度快，TOKEN消耗少
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div className="flex items-center space-x-1">
                    <span className="text-green-500">✓</span>
                    <span>处理速度快</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-green-500">✓</span>
                    <span>TOKEN消耗少</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-500">⚠</span>
                    <span>缺乏上下文连贯性</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* 全文处理 */}
          <motion.div
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
              processingMode === 'fulltext'
                ? 'border-emerald-500 bg-emerald-50'
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => setProcessingMode('fulltext')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {recommendedMode === 'fulltext' && (
              <motion.div
                className="absolute -top-2 -right-2 bg-emerald-500 text-white text-xs px-2 py-1 rounded-full"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                推荐
              </motion.div>
            )}

            <div className="flex items-start space-x-3">
              <div className={`w-5 h-5 rounded-full border-2 mt-0.5 transition-colors ${
                processingMode === 'fulltext'
                  ? 'border-emerald-500 bg-emerald-500'
                  : 'border-gray-300'
              }`}>
                {processingMode === 'fulltext' && (
                  <motion.div
                    className="w-full h-full rounded-full bg-white"
                    initial={{ scale: 0 }}
                    animate={{ scale: 0.4 }}
                    transition={{ type: "spring", stiffness: 300, damping: 25 }}
                  />
                )}
              </div>

              <div className="flex-1">
                <h5 className="font-medium text-gray-900 mb-1">全文处理</h5>
                <p className="text-sm text-gray-600 mb-2">
                  保持上下文连贯性，整体优化效果更好
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div className="flex items-center space-x-1">
                    <span className="text-green-500">✓</span>
                    <span>上下文连贯</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-green-500">✓</span>
                    <span>整体优化效果好</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-500">⚠</span>
                    <span>TOKEN消耗较多</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  };

  // 渲染用户要求输入界面
  const renderRequirementsInput = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center max-w-4xl p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          <div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-2">
              设置标注要求
            </h3>
            <p className="text-gray-600">
              请选择处理模式并描述您希望AI如何改进这段文本
            </p>
          </div>

          {/* 处理模式选择器 */}
          {renderModeSelector()}

          <div className="space-y-4">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">标注要求</h4>
              <textarea
                value={userRequirements}
                onChange={(e) => setUserRequirements(e.target.value)}
                placeholder="例如：减少内心独白，增强对话描写，提升文字的流畅度和可读性..."
                className="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>

            <div className="flex space-x-3">
              <motion.button
                type="button"
                onClick={startAnnotation}
                disabled={!chapterContent}
                className="flex-1 px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                开始标注
              </motion.button>

              <motion.button
                type="button"
                onClick={() => {
                  setUserRequirements('提升文字流畅度，减少冗余表达，增强对话的自然性');
                }}
                className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                使用默认要求
              </motion.button>
            </div>
          </div>

          <div className="text-xs text-gray-500 space-y-1">
            <p>💡 提示：具体的要求能帮助AI生成更精准的修改建议</p>
            <p>📝 留空将使用默认的文本优化策略</p>
            <p>🔄 您可以随时在处理过程中切换模式</p>
          </div>
        </motion.div>
      </div>
    </div>
  );

  // 渲染进度界面
  const renderProgressView = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center max-w-md">
        <motion.div
          className="w-16 h-16 mx-auto mb-6 border-4 border-emerald-200 border-t-emerald-600 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />

        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          正在处理文本标注
        </h3>

        <p className="text-gray-600 mb-6">
          AI正在分析您的文本并生成修改建议，请稍候...
        </p>

        <div className="space-y-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-emerald-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          <div className="text-sm text-gray-500">
            {isProcessing ? (
              <>处理进度: {currentSegment}/{totalSegments} 段落 ({Math.round(progress)}%)</>
            ) : (
              '准备中...'
            )}
          </div>
        </div>

        {error && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
            <button
              onClick={startAnnotation}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
            >
              重试
            </button>
          </div>
        )}
      </div>
    </div>
  );

  if (!isOpen) return null;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="fulltext-annotation-dialog"
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-2xl w-full h-full max-w-7xl max-h-[95vh] overflow-hidden"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            onClick={(e) => e.stopPropagation()}
          >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">全文标注</h2>
              <p className="text-sm text-gray-600 mt-1">{chapterTitle}</p>
            </div>
            <div className="flex items-center space-x-3">
              {!isProcessing && segments.length > 0 && (
                <>
                  <motion.button
                    type="button"
                    onClick={handleRestart}
                    className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    重新开始
                  </motion.button>
                  <motion.button
                    type="button"
                    onClick={handleExport}
                    className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    导出修改后文本
                  </motion.button>
                </>
              )}
              <button
                type="button"
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-hidden">
            {showRequirementsInput ? (
              renderRequirementsInput()
            ) : showStreamingResults ? (
              // 流式显示模式：显示进度 + 已完成的结果
              <div className="h-full flex flex-col">
                {/* 进度条区域 */}
                {isProcessing && (
                  <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <motion.div
                        className="w-6 h-6 border-2 border-emerald-200 border-t-emerald-600 rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            正在处理第 {currentSegment}/{totalSegments} 段落
                          </span>
                          <span className="text-sm text-gray-500">
                            {Math.round(progress)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div
                            className="bg-emerald-600 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${progress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 结果显示区域 */}
                <div className="flex-1 overflow-hidden">
                  <AnnotationResultViewer
                    segments={segments as any}
                    onSentenceReject={handleSentenceReject}
                    onBatchReject={handleBatchReject}
                    onSentenceEdit={handleSentenceEdit}
                    onTextChange={handleTextChange}
                    onSegmentsUpdate={handleSegmentsUpdate as any}
                    onSave={handleSave as any}
                    className="h-full"
                    enableSelectionMode={true}
                    onStartAnalysis={handleStartAnalysis}
                  />
                </div>
              </div>
            ) : isProcessing || segments.length === 0 ? (
              renderProgressView()
            ) : (
              <AnnotationResultViewer
                segments={segments as any}
                onSentenceReject={handleSentenceReject}
                onBatchReject={handleBatchReject}
                onSentenceEdit={handleSentenceEdit}
                onTextChange={handleTextChange}
                onSegmentsUpdate={handleSegmentsUpdate as any}
                onSave={handleSave as any}
                className="h-full"
                enableSelectionMode={true}
                onStartAnalysis={handleStartAnalysis}
              />
            )}
          </div>
          </motion.div>
        </motion.div>
      )}

      {/* 重新开始确认弹窗 */}
      {showRestartConfirm && (
        <ConfirmDialog
          key="restart-confirm-dialog"
          isOpen={showRestartConfirm}
          title="确认重新开始"
          message="确定要重新开始吗？这将清除当前的标注进度和所有修改。"
          confirmText="重新开始"
          cancelText="取消"
          confirmButtonClass="bg-red-500 hover:bg-red-600"
          onConfirm={confirmRestart}
          onCancel={cancelRestart}
        />
      )}
    </AnimatePresence>,
    document.body
  );
};

export default FullTextAnnotationDialog;
