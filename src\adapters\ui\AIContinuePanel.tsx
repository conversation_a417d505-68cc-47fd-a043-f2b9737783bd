"use client";

import React from 'react';
import { createAIContinuePanelFactory } from '@/factories/ui';

interface AIContinueDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertContent: (content: string) => void;
  initialContext?: string;
  bookId?: string;
  currentChapterId?: string; // 当前章节ID，用于禁用当前章节的选择
}

interface AIContinueButtonProps {
  context?: string;
  bookId?: string;
  onInsertContent: (content: string) => void;
  buttonText?: string;
  buttonClassName?: string;
  buttonIcon?: React.ReactNode;
  buttonSize?: 'small' | 'medium' | 'large';
  buttonType?: 'primary' | 'secondary' | 'outline' | 'text';
}

/**
 * AI续写面板适配器
 * 用于在编辑器中集成AI续写功能
 */
export class AIContinuePanelAdapter {
  /**
   * 创建AI续写对话框组件
   * @param props 组件属性
   * @returns AI续写对话框组件
   */
  createAIContinueDialog(props: AIContinueDialogProps) {
    const aiContinuePanelFactory = createAIContinuePanelFactory();
    return aiContinuePanelFactory.createAIContinueDialog(props);
  }

  /**
   * 创建AI续写按钮组件
   * @param props 组件属性
   * @returns AI续写按钮组件
   */
  createAIContinueButton(props: AIContinueButtonProps) {
    const aiContinuePanelFactory = createAIContinuePanelFactory();
    return aiContinuePanelFactory.createAIContinueButton(props);
  }
}

/**
 * 创建AI续写面板适配器实例
 * @returns AI续写面板适配器实例
 */
export function createAIContinuePanelAdapter() {
  return new AIContinuePanelAdapter();
}

/**
 * AI续写对话框组件
 * 用于在React应用中使用AI续写对话框
 */
export const AIContinueDialog: React.FC<AIContinueDialogProps> = (props) => {
  const adapter = createAIContinuePanelAdapter();
  return adapter.createAIContinueDialog(props);
};

/**
 * AI续写按钮组件
 * 用于在React应用中使用AI续写按钮
 */
export const AIContinueButton: React.FC<AIContinueButtonProps> = (props) => {
  const adapter = createAIContinuePanelAdapter();
  return adapter.createAIContinueButton(props);
};

export default createAIContinuePanelAdapter;
