/**
 * ACE框架展示网格组件
 * 右侧框架列表，支持多选和动画效果
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExtendedACEFramework, getCategoryInfo } from '../../../types/ACEFrameworkTypes';
import { ExtractedElementsModal } from './ExtractedElementsModal';

interface FrameworkGridProps {
  frameworks: ExtendedACEFramework[];
  selectedFrameworkIds: string[];
  onFrameworkToggle: (frameworkId: string) => void;
  onManageKeywords?: (framework: ExtendedACEFramework) => void;
  onFrameworksUpdated?: () => void; // 添加框架更新回调
  isLoading?: boolean;
  className?: string;
}

export const FrameworkGrid: React.FC<FrameworkGridProps> = ({
  frameworks,
  selectedFrameworkIds,
  onFrameworkToggle,
  onManageKeywords,
  onFrameworksUpdated,
  isLoading = false,
  className = ''
}) => {
  // 拆解元素详情对话框状态
  const [showExtractedElementsModal, setShowExtractedElementsModal] = useState(false);
  const [selectedExtractedFramework, setSelectedExtractedFramework] = useState<ExtendedACEFramework | null>(null);

  // 处理拆解元素查看
  const handleViewExtractedElements = (framework: ExtendedACEFramework) => {
    setSelectedExtractedFramework(framework);
    setShowExtractedElementsModal(true);
  };
  if (isLoading) {
    return (
      <div className={`flex-1 p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2 text-gray-500">
            <motion.div
              className="w-2 h-2 bg-blue-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
            />
            <motion.div
              className="w-2 h-2 bg-blue-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
            />
            <motion.div
              className="w-2 h-2 bg-blue-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
            />
            <span className="ml-2">加载框架中...</span>
          </div>
        </div>
      </div>
    );
  }

  if (frameworks.length === 0) {
    return (
      <div className={`flex-1 p-6 ${className}`}>
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <svg className="w-16 h-16 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-lg font-medium">暂无框架</p>
            <p className="text-sm mt-1">该分类下还没有可用的框架</p>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`flex-1 flex flex-col overflow-hidden ${className}`}>
        <div className="flex-1 overflow-y-auto p-6">
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            layout
          >
            <AnimatePresence mode="popLayout">
              {frameworks.map((framework, index) => (
                <motion.div
                  key={framework.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    type: "spring",
                    stiffness: 400,
                    damping: 25
                  }}
                >
                  <FrameworkCard
                    framework={framework}
                    isSelected={selectedFrameworkIds.includes(framework.id)}
                    onToggle={() => onFrameworkToggle(framework.id)}
                    onManageKeywords={onManageKeywords}
                    onViewExtractedElements={handleViewExtractedElements}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* 拆解元素详情对话框 */}
      <ExtractedElementsModal
        isOpen={showExtractedElementsModal}
        framework={selectedExtractedFramework}
        onClose={() => {
          setShowExtractedElementsModal(false);
          setSelectedExtractedFramework(null);
        }}
        onElementsDeleted={() => {
          // 通知外部组件刷新框架数据
          if (onFrameworksUpdated) {
            onFrameworksUpdated();
          }
        }}
      />
    </>
  );
};

interface FrameworkCardProps {
  framework: ExtendedACEFramework;
  isSelected: boolean;
  onToggle: () => void;
  onManageKeywords?: (framework: ExtendedACEFramework) => void;
  onViewExtractedElements?: (framework: ExtendedACEFramework) => void;
}

const FrameworkCard: React.FC<FrameworkCardProps> = ({
  framework,
  isSelected,
  onToggle,
  onManageKeywords,
  onViewExtractedElements
}) => {
  const categoryInfo = getCategoryInfo(framework.category);

  // 检查是否是用户关键词框架
  const isUserKeywordFramework = framework.category === 'synopsis-keywords' &&
    (framework.id === 'keywords-user-saved-title' || framework.id === 'keywords-user-saved-synopsis');

  // 检查是否是拆解元素框架
  const isExtractedElementFramework = framework.category === 'extracted-elements';

  return (
    <motion.div
      layout
      whileHover={{
        y: -4,
        scale: 1.02,
        transition: { type: "spring", stiffness: 400, damping: 25 }
      }}
      whileTap={{ scale: 0.98 }}
      className={`min-h-[200px] p-5 rounded-xl border cursor-pointer relative overflow-hidden ${
        isSelected
          ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 shadow-lg'
          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-sm hover:shadow-lg bg-white dark:bg-gray-900'
      } transition-all duration-300`}
      onClick={onToggle}
    >
      {/* 选中状态的光晕效果 */}
      {isSelected && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-blue-100/60 to-blue-200/40 dark:from-blue-900/40 dark:to-blue-800/30 rounded-xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, type: "spring", stiffness: 300, damping: 30 }}
        />
      )}

      {/* 选中状态的边框光效 */}
      {isSelected && (
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-blue-400 dark:border-blue-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          style={{
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.3), inset 0 0 20px rgba(59, 130, 246, 0.1)'
          }}
        />
      )}

      {/* 内容 */}
      <div className="relative z-10">
        {/* 头部：分类图标 + 管理按钮 + 选择状态 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <motion.span
              className="text-lg mr-2"
              animate={{ scale: isSelected ? 1.1 : 1 }}
              style={{ color: categoryInfo.color }}
            >
              {categoryInfo.icon}
            </motion.span>
            <span className="text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
              {categoryInfo.name}
            </span>

            {/* 用户关键词管理按钮 */}
            {isUserKeywordFramework && onManageKeywords && (
              <motion.button
                className="ml-2 p-1 rounded-md text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onManageKeywords(framework);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="管理关键词"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </motion.button>
            )}

            {/* 拆解元素管理按钮 */}
            {isExtractedElementFramework && onViewExtractedElements && (
              <motion.button
                className="ml-2 p-1 rounded-md text-gray-400 hover:text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onViewExtractedElements(framework);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="查看拆解元素详情"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </motion.button>
            )}
          </div>

          {/* 选择指示器 */}
          <motion.div
            className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
              isSelected
                ? 'bg-blue-500 border-blue-500'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            animate={{ scale: isSelected ? 1.1 : 1 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <AnimatePresence>
              {isSelected && (
                <motion.svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="white"
                  initial={{ scale: 0, rotate: -90 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 90 }}
                  transition={{ duration: 0.2 }}
                >
                  <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"/>
                </motion.svg>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* 框架名称 */}
        <h4 className={`font-semibold text-base mb-3 line-clamp-2 ${
          isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-900 dark:text-gray-100'
        }`}>
          {framework.name}
        </h4>

        {/* 框架描述 */}
        <p className={`text-sm leading-relaxed mb-4 line-clamp-3 ${
          isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'
        }`}>
          {framework.description}
        </p>

        {/* 拆解元素特殊显示 */}
        {isExtractedElementFramework && framework.extractedElements && framework.extractedElements.length > 0 && (
          <div className="mb-4">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">拆解元素预览:</div>
            <div className="flex flex-wrap gap-1">
              {framework.extractedElements.slice(0, 6).map((element, index) => (
                <span
                  key={index}
                  className={`inline-flex items-center px-2 py-1 rounded text-xs ${
                    isSelected
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  {element.text}
                </span>
              ))}
              {framework.extractedElements.length > 6 && (
                <span className={`text-xs ${
                  isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  +{framework.extractedElements.length - 6}个
                </span>
              )}
            </div>
          </div>
        )}

        {/* 效果评分和额外信息 */}
        <div className="flex items-center justify-between mt-auto">
          <div className="flex items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">效果:</span>
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <motion.svg
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(framework.effectiveness / 2)
                      ? 'text-yellow-400'
                      : 'text-gray-300 dark:text-gray-600'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  animate={{
                    scale: isSelected && i < Math.floor(framework.effectiveness / 2) ? 1.2 : 1,
                    rotate: isSelected && i < Math.floor(framework.effectiveness / 2) ? 5 : 0
                  }}
                  transition={{
                    duration: 0.3,
                    delay: i * 0.1,
                    type: "tween",
                    ease: "easeOut"
                  }}
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </motion.svg>
              ))}
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 font-medium">
                {framework.effectiveness}/10
              </span>
            </div>
          </div>

          {/* 示例数量或关键词数量 */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {isExtractedElementFramework && framework.extractedElements ? (
              <div className="flex items-center space-x-2">
                <span>{framework.extractedElements.length} 个元素</span>
                {framework.extractedAt && (
                  <span className="text-purple-500">
                    {new Date(framework.extractedAt).toLocaleDateString()}
                  </span>
                )}
              </div>
            ) : framework.keywordElements && framework.keywordElements.length > 0 ? (
              <span>{framework.keywordElements.length} 个关键词</span>
            ) : framework.examples && framework.examples.length > 0 ? (
              <span>{framework.examples.length} 个示例</span>
            ) : (
              <span>框架模板</span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FrameworkGrid;
