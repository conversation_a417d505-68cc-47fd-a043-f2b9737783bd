"use client";

import React, { useState, useEffect } from 'react';
import { useReactFlow } from 'reactflow';
import EnhancedSelect from '../../common/EnhancedSelect';

// 布局配置类型定义
export interface LayoutConfig {
  type: 'tree' | 'force' | 'radial' | 'grid' | 'horizontal' | 'vertical';
  nodeSpacing: number;
  levelSpacing: number;
  compactness: number;
  direction?: 'LR' | 'RL' | 'TB' | 'BT';
  alignSiblings?: boolean;
  centerContent?: boolean;
}

// 组件属性类型定义
interface LayoutControlPanelProps {
  onLayoutApply: (layoutConfig: LayoutConfig) => void;
  onPreview: (layoutConfig: LayoutConfig) => void;
  onCancel: () => void;
  currentLayout: LayoutConfig;
  isPreviewMode: boolean;
  layoutLocked?: boolean;
}

/**
 * 布局控制面板组件
 * 提供布局类型选择和参数调整功能
 */
const LayoutControlPanel: React.FC<LayoutControlPanelProps> = ({
  onLayoutApply,
  onPreview,
  onCancel,
  currentLayout,
  isPreviewMode,
  layoutLocked = false
}) => {
  // 使用当前布局配置初始化状态
  const [layoutConfig, setLayoutConfig] = useState<LayoutConfig>(currentLayout);
  // 是否保存为默认设置
  const [saveAsDefault, setSaveAsDefault] = useState(false);

  // 获取ReactFlow实例
  const { fitView } = useReactFlow();

  // 当前布局配置变化时更新状态
  useEffect(() => {
    setLayoutConfig(currentLayout);
  }, [currentLayout]);

  // 处理布局类型变更
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value as LayoutConfig['type'];

    // 根据布局类型设置默认方向
    let direction: 'LR' | 'RL' | 'TB' | 'BT' = 'TB';
    if (newType === 'horizontal') {
      direction = 'LR';
    } else if (newType === 'vertical') {
      direction = 'TB';
    }

    setLayoutConfig(prev => ({
      ...prev,
      type: newType,
      direction
    }));
  };

  // 处理方向变更
  const handleDirectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLayoutConfig(prev => ({
      ...prev,
      direction: e.target.value as 'LR' | 'RL' | 'TB' | 'BT'
    }));
  };

  // 处理滑块值变更
  const handleSliderChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    property: 'nodeSpacing' | 'levelSpacing' | 'compactness'
  ) => {
    const rawValue = parseInt(e.target.value);
    // 对于紧凑度，需要转换为0-1之间的小数
    const value = property === 'compactness' ? rawValue / 100 : rawValue;

    console.log(`🎛️ 调整${property}:`, { rawValue, value });

    setLayoutConfig(prev => ({
      ...prev,
      [property]: value
    }));
  };

  // 处理复选框变更
  const handleCheckboxChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    property: 'alignSiblings' | 'centerContent'
  ) => {
    const checked = e.target.checked;
    setLayoutConfig(prev => ({
      ...prev,
      [property]: checked
    }));
  };

  // 应用布局
  const handleApply = () => {
    onLayoutApply(layoutConfig);
    // 如果选择了保存为默认，可以在这里处理
    // 例如，保存到localStorage或发送到服务器
    if (saveAsDefault) {
      localStorage.setItem('outlineLayoutConfig', JSON.stringify(layoutConfig));
    }
  };

  // 预览布局
  const handlePreview = () => {
    onPreview(layoutConfig);
  };

  // 渲染布局类型选择器
  const renderLayoutTypeSelector = () => {
    const layoutTypeOptions = [
      {
        value: 'tree',
        label: '树形布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M12 5H7M12 5H17M12 19H7M12 19H17M7 5V19M17 5V19" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '适合层次结构的数据展示'
      },
      {
        value: 'force',
        label: '力导向布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5"/>
            <circle cx="6" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
            <circle cx="18" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
            <circle cx="6" cy="16" r="2" stroke="currentColor" strokeWidth="1.5"/>
            <circle cx="18" cy="16" r="2" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="8" y1="8" x2="10" y2="10" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="16" y1="8" x2="14" y2="10" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="8" y1="16" x2="10" y2="14" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="16" y1="16" x2="14" y2="14" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '自然分布的网络关系图'
      },
      {
        value: 'radial',
        label: '放射状布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="12" y1="5" x2="12" y2="9" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="12" y1="15" x2="12" y2="19" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="5" y1="12" x2="9" y2="12" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="15" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="7" y1="7" x2="10" y2="10" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="14" y1="14" x2="17" y2="17" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="7" y1="17" x2="10" y2="14" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="14" y1="10" x2="17" y2="7" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '以中心点向外辐射的布局'
      },
      {
        value: 'grid',
        label: '网格布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="5" y="5" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="10" y="5" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="15" y="5" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="5" y="10" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="10" y="10" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="15" y="10" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="5" y="15" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="10" y="15" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="15" y="15" width="4" height="4" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '整齐的网格排列方式'
      },
      {
        value: 'horizontal',
        label: '水平布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="4" y="8" width="4" height="8" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="10" y="8" width="4" height="8" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="16" y="8" width="4" height="8" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="8" y1="12" x2="10" y2="12" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="14" y1="12" x2="16" y2="12" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '从左到右的水平排列'
      },
      {
        value: 'vertical',
        label: '垂直布局',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="8" y="4" width="8" height="4" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="8" y="10" width="8" height="4" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <rect x="8" y="16" width="8" height="4" rx="1" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="12" y1="8" x2="12" y2="10" stroke="currentColor" strokeWidth="1.5"/>
            <line x1="12" y1="14" x2="12" y2="16" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        ),
        description: '从上到下的垂直排列'
      }
    ];

    return (
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">布局类型</label>
        <EnhancedSelect
          options={layoutTypeOptions}
          value={layoutConfig.type}
          onChange={(value) => handleTypeChange({ target: { value } } as any)}
          className="w-full"
        />
      </div>
    );
  };

  // 渲染方向选择器（仅对特定布局类型显示）
  const renderDirectionSelector = () => {
    // 只有树形、水平和垂直布局需要方向选择
    if (!['tree', 'horizontal', 'vertical'].includes(layoutConfig.type)) {
      return null;
    }

    const directionOptions = [
      {
        value: 'LR',
        label: '从左到右',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 12H19M19 12L14 7M19 12L14 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )
      },
      {
        value: 'RL',
        label: '从右到左',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M5 12L10 7M5 12L10 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )
      },
      {
        value: 'TB',
        label: '从上到下',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M12 19L7 14M12 19L17 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )
      },
      {
        value: 'BT',
        label: '从下到上',
        preview: (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 19V5M12 5L7 10M12 5L17 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )
      }
    ];

    return (
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">布局方向</label>
        <EnhancedSelect
          options={directionOptions}
          value={layoutConfig.direction || 'TB'}
          onChange={(value) => handleDirectionChange({ target: { value } } as any)}
          className="w-full"
        />
      </div>
    );
  };

  // 渲染布局参数控制
  const renderLayoutParameters = () => {
    return (
      <div className="mb-4">
        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <label className="text-sm font-medium">节点间距</label>
            <span className="text-xs text-gray-500">{layoutConfig.nodeSpacing}px</span>
          </div>
          <input
            type="range"
            min="30"
            max="200"
            value={layoutConfig.nodeSpacing}
            onChange={(e) => handleSliderChange(e, 'nodeSpacing')}
            className="w-full"
          />
        </div>

        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <label className="text-sm font-medium">层级间距</label>
            <span className="text-xs text-gray-500">{layoutConfig.levelSpacing}px</span>
          </div>
          <input
            type="range"
            min="50"
            max="300"
            value={layoutConfig.levelSpacing}
            onChange={(e) => handleSliderChange(e, 'levelSpacing')}
            className="w-full"
          />
        </div>

        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <label className="text-sm font-medium">紧凑度</label>
            <div className="text-xs text-gray-500">
              <span>{(layoutConfig.compactness * 100).toFixed(0)}%</span>
              <span className="text-gray-400 ml-1">
                (系数: {(0.5 + (1 - layoutConfig.compactness) * 1.5).toFixed(1)})
              </span>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={layoutConfig.compactness * 100}
            onChange={(e) => handleSliderChange(e, 'compactness')}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>松散 (2.0x)</span>
            <span>紧凑 (0.5x)</span>
          </div>
        </div>
      </div>
    );
  };

  // 渲染额外选项
  const renderExtraOptions = () => {
    return (
      <div className="mb-4">
        <div className="flex items-center mb-2">
          <input
            type="checkbox"
            id="alignSiblings"
            checked={layoutConfig.alignSiblings}
            onChange={(e) => handleCheckboxChange(e, 'alignSiblings')}
            className="mr-2"
          />
          <label htmlFor="alignSiblings" className="text-sm">对齐同级节点</label>
        </div>

        <div className="flex items-center mb-2">
          <input
            type="checkbox"
            id="centerContent"
            checked={layoutConfig.centerContent}
            onChange={(e) => handleCheckboxChange(e, 'centerContent')}
            className="mr-2"
          />
          <label htmlFor="centerContent" className="text-sm">居中内容</label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="saveAsDefault"
            checked={saveAsDefault}
            onChange={(e) => setSaveAsDefault(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="saveAsDefault" className="text-sm">保存为默认设置</label>
        </div>
      </div>
    );
  };

  // 渲染操作按钮
  const renderActionButtons = () => {
    return (
      <div className="flex justify-end space-x-2">
        <button
          className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          onClick={onCancel}
        >
          取消
        </button>
        {!layoutLocked && (
          <button
            className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
            onClick={handlePreview}
          >
            {isPreviewMode ? '更新预览' : '预览'}
          </button>
        )}
        <button
          className={`px-3 py-1 rounded-md ${
            layoutLocked
              ? 'bg-orange-600 text-white hover:bg-orange-700'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
          onClick={handleApply}
          title={layoutLocked ? '强制应用布局将清除所有手动定位' : '应用布局'}
        >
          {layoutLocked ? '强制应用' : '应用'}
        </button>
      </div>
    );
  };

  return (
    <div className="layout-control-panel bg-white rounded-lg shadow-lg w-64 absolute right-0 top-full mt-2 z-50 max-h-[70vh] md:max-h-[75vh] lg:max-h-[70vh] flex flex-col">
      {/* 固定头部 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium">布局设置</h3>

        {/* 布局锁定状态提示 */}
        {layoutLocked && (
          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-md">
            <div className="flex items-center space-x-2 text-orange-700">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium">布局已锁定</span>
            </div>
            <p className="text-xs text-orange-600 mt-1">
              检测到手动调整的节点位置。应用布局将清除所有手动定位。
            </p>
          </div>
        )}
      </div>

      {/* 可滚动内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {renderLayoutTypeSelector()}
        {renderDirectionSelector()}
        {renderLayoutParameters()}
        {renderExtraOptions()}
      </div>

      {/* 固定底部操作按钮 */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200">
        {renderActionButtons()}
      </div>
    </div>
  );
};

export default LayoutControlPanel;
