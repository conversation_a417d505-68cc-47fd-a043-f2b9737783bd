/**
 * AI注释服务的类型定义
 * 包含所有接口、类型和枚举定义
 */

/**
 * 文本段落接口
 */
export interface TextSegment {
  id: string;
  content: string;
  sentences: Sentence[];
}

/**
 * 句子接口
 */
export interface Sentence {
  id: string;
  text: string;
  aiSuggestion?: string;
  modifiedText?: string;
  modificationType?: ModificationType;
  category?: ProblemCategory;
  severity?: SeverityLevel;
  impact?: ImpactLevel;
  confidence?: number;
  alternatives?: Alternative[];
  tags?: string[];
  processingStatus?: ProcessingStatus;
  processingError?: string;
  lastProcessedAt?: Date;
  reason?: string;
  // 新增merge相关字段
  isMergeOperation?: boolean;                   // 是否为merge操作的源句子
  isMergeTarget?: boolean;                      // 是否为merge操作的目标句子
  mergeSourceIndex?: number;                    // merge源句子的索引
  isDeleted?: boolean;                          // 是否已被删除（用于UI显示）
  // 🔧 新增hasCreation相关字段
  hasCreation?: boolean;                        // 是否有独立的创建操作
  createdSentences?: string[];                  // 创建的新句子数组
  insertPosition?: 'before' | 'after';         // 插入位置
  contentType?: 'dialogue' | 'description' | 'action' | 'emotion'; // 内容类型
  insertMode?: 'single' | 'batch';             // 插入模式
  contextHint?: string;                         // 创建理由和上下文说明
}

/**
 * 修改类型枚举
 */
export type ModificationType =
  | 'modify'       // 修改句子内容
  | 'delete'       // 删除句子
  | 'split'        // 拆分为多个句子
  | 'merge'        // 与下一句合并
  | 'enhance'      // 增强表达效果
  | 'simplify'     // 简化表达
  | 'reorder'      // 调整句子顺序
  | 'transition'   // 添加过渡句
  | 'bridge'       // 句间桥接
  | 'coherence'    // 连贯性优化
  | 'flow'         // 流畅度改进
  | 'consistency'  // 一致性调整
  | 'keep';        // 保持原样

/**
 * 问题分类枚举
 */
export type ProblemCategory =
  | 'grammar'      // 语法问题
  | 'style'        // 风格问题
  | 'logic'        // 逻辑问题
  | 'expression'   // 表达问题
  | 'structure'    // 结构问题
  | 'clarity'      // 清晰度问题
  | 'coherence'    // 连贯性问题
  | 'flow'         // 流畅度问题
  | 'transition'   // 过渡问题
  | 'consistency'; // 一致性问题

/**
 * 严重程度枚举
 */
export type SeverityLevel =
  | 'low'         // 轻微问题
  | 'medium'      // 中等问题
  | 'high';       // 严重问题

/**
 * 影响程度枚举
 */
export type ImpactLevel =
  | 'minor'       // 轻微影响
  | 'moderate'    // 中等影响
  | 'significant'; // 显著影响

/**
 * 处理状态枚举
 */
export type ProcessingStatus =
  | 'pending'     // 等待处理
  | 'processing'  // 处理中
  | 'completed'   // 处理完成
  | 'failed';     // 处理失败

/**
 * 替代方案接口
 */
export interface Alternative {
  text: string;
  style: 'formal' | 'casual' | 'literary';
  reason: string;
}



/**
 * 句子上下文接口
 */
export interface SentenceContext {
  previousSentence?: string;
  nextSentence?: string;
  segmentContext?: string;
}

/**
 * 注释回调函数接口
 */
export interface AnnotationCallbacks {
  onStart?: () => void;
  onProgress?: (progress: number, current: number, total: number) => void;
  onSegmentComplete?: (segment: TextSegment) => void;
  onComplete?: (result: AnnotationResult) => void;
  onError?: (error: any) => void;
}

/**
 * 注释结果接口
 */
export interface AnnotationResult {
  segments: TextSegment[];
  success: boolean;
  totalProcessed: number;
  totalSuggestions: number;
  error?: string;
}

/**
 * AI建议响应接口
 */
export interface AISuggestionResponse {
  suggestions: SuggestionItem[];
}

/**
 * 建议项接口
 */
export interface SuggestionItem {
  sentenceIndex: number;
  originalText: string;
  modificationType: ModificationType;
  modifiedText?: string;
  suggestion: string;
  reason: string;
  category: ProblemCategory;
  severity: SeverityLevel;
  impact: ImpactLevel;
  confidence: number;
  alternatives?: Alternative[];
  tags?: string[];
}

/**
 * AI注释服务接口
 */
export interface AIAnnotationServiceInterface {
  /**
   * 标注文本段落
   */
  annotateTextSegments(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks,
    userRequirements?: string,
    coherenceMode?: boolean
  ): Promise<AnnotationResult>;

  /**
   * 全文一次性处理
   */
  annotateFullText(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks,
    userRequirements?: string
  ): Promise<AnnotationResult>;
}

/**
 * 消息构建器接口
 */
export interface MessageBuilderInterface {
  addSystemMessage(content: string): void;
  addUserMessage(content: string, name?: string, isInternal?: boolean, isHidden?: boolean): void;
  addAssistantMessage(content: string, isInternal?: boolean, isHidden?: boolean): void;
  build(): any[];
}


