"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { createTextareaField, createShortTextareaField, getCategoryLabel } from '../CategoryFieldsUtils';
import { WorldBuildingFieldAIButton } from '../WorldBuildingFieldAIButton';

interface CategoryFieldsEditProps {
  editingWorldBuilding: WorldBuilding;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

/**
 * 类别特定字段编辑组件
 */
export const CategoryFieldsEdit: React.FC<CategoryFieldsEditProps> = ({
  editingWorldBuilding,
  handleInputChange
}) => {
  if (!editingWorldBuilding) return null;

  const category = editingWorldBuilding.category || '';

  // 根据不同类别返回特定字段
  switch (category) {
    case 'geography':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 地理元素包括国家、城市、山脉、河流、森林等地点。详细描述这些地点的特征和在故事中的作用。
            </p>
          </div>
          {createTextareaField(
            'description',
            '地理描述',
            '描述这个地点的主要特征、外观和氛围...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'significance',
            '在故事中的意义',
            '例如：主角的家乡，重要战役发生地，隐藏宝藏的所在...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'inhabitants',
            '居民/生物',
            '例如：人类村民，精灵族，龙族栖息地...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'history':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 历史元素包括重大事件、战争、条约、王朝更替等。详细描述这些事件的经过和对世界的影响。
            </p>
          </div>
          {createTextareaField(
            'event',
            '事件描述',
            '描述这个历史事件的经过、原因和结果...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'timeframe',
            '时间框架',
            '例如：第三纪元末期，主角出生前十年，王国建立初期...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'keyFigures',
            '关键人物',
            '例如：国王阿瑟，将军莱昂，女巫摩根...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'impact',
            '对当前世界的影响',
            '例如：形成了现在的国界，导致魔法消失，创造了新的信仰...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'culture':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 文化元素包括习俗、传统、价值观、艺术形式等。详细描述这些文化特征如何影响故事中的人物和社会。
            </p>
          </div>
          {createTextareaField(
            'overview',
            '文化概述',
            '描述这种文化的核心特征、价值观和生活方式...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'customs',
            '重要习俗',
            '例如：成年礼，婚礼传统，季节性庆典...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'beliefs',
            '信仰与禁忌',
            '例如：多神信仰，自然崇拜，禁止在满月时外出...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'arts',
            '艺术与表达',
            '例如：史诗歌谣，壁画，舞蹈仪式，音乐传统...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'race':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 种族元素包括人类、精灵、矮人、兽人等不同种族。详细描述这些种族的特征、能力和社会结构。
            </p>
          </div>
          {createTextareaField(
            'characteristics',
            '种族特征',
            '描述这个种族的外观、能力和独特之处...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'society',
            '社会结构',
            '例如：部落制，君主制，议会制，氏族制...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'relations',
            '与其他种族的关系',
            '例如：与精灵族联盟，与矮人族敌对，与人类保持距离...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'specialAbilities',
            '特殊能力',
            '例如：夜视，水下呼吸，火焰抗性，自然魔法亲和力...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'magic':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 魔法系统元素包括魔法规则、能量来源、施法方式等。详细描述这个魔法系统的原理和限制。
            </p>
          </div>
          {createTextareaField(
            'principles',
            '魔法原理',
            '描述这个魔法系统的基本原理、来源和运作方式...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'limitations',
            '限制与代价',
            '例如：消耗施法者生命力，需要特定材料，有使用次数限制...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'practitioners',
            '习得方式',
            '例如：天赋觉醒，师徒传承，古籍学习，血脉传承...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'commonSpells',
            '常见法术',
            '例如：火球术，隐身术，治愈术，传送术...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'organization':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 组织元素包括政府、军队、公会、秘密结社等。详细描述这些组织的结构、目的和影响力。
            </p>
          </div>
          {createTextareaField(
            'purpose',
            '组织目的',
            '描述这个组织的成立目的、使命和活动范围...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'structure',
            '组织结构',
            '例如：金字塔结构，分为三个等级，由长老会领导...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'keyMembers',
            '关键成员',
            '例如：领袖艾伦，副官莉莉丝，首席顾问马库斯...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'resources',
            '资源与影响力',
            '例如：控制北方贸易，拥有古代魔法知识，影响王室决策...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'item':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 物品元素包括神器、魔法物品、重要道具等。详细描述这些物品的特性、来源和在故事中的作用。
            </p>
          </div>
          {createTextareaField(
            'description',
            '物品描述',
            '描述这个物品的外观、材质和特点...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'origin',
            '来源/历史',
            '例如：古代文明遗物，神灵赐予，大师匠心之作...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'powers',
            '能力/功能',
            '例如：增强使用者力量，预知未来，储存魔法能量...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'significance',
            '在故事中的意义',
            '例如：主角的传家宝，打败反派的关键，引发冲突的原因...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'religion':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 宗教元素包括信仰体系、神灵、仪式等。详细描述这些宗教的教义、组织结构和社会影响。
            </p>
          </div>
          {createTextareaField(
            'beliefs',
            '核心信仰',
            '描述这个宗教的核心教义、神灵体系和世界观...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'practices',
            '仪式与实践',
            '例如：日出祈祷，季节性祭祀，成年洗礼...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'organization',
            '宗教组织',
            '例如：神殿体系，祭司阶级，修道院网络...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'influence',
            '社会影响',
            '例如：影响王室决策，掌控教育系统，调解民间纠纷...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    case 'politics':
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 政治元素包括政治体系、权力结构、派系等。详细描述这些政治元素的特点、关键人物和当前冲突。
            </p>
          </div>
          {createTextareaField(
            'system',
            '政治体系',
            '描述这个政治体系的结构、运作方式和特点...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'keyFigures',
            '关键人物',
            '例如：国王阿瑟，首相托马斯，反对派领袖莱昂...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'factions',
            '派系与利益集团',
            '例如：皇室派，改革派，商人联盟，军事贵族...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'conflicts',
            '当前冲突',
            '例如：王位继承争端，边境领土纠纷，资源分配不均...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );

    // 其他类别使用通用字段
    default:
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-700">
              <span className="font-medium">提示：</span> 请详细描述这个世界观元素的特征和在故事中的作用。如果您认为需要添加特定类别，请联系开发团队。
            </p>
          </div>
          {createTextareaField(
            'mainFeatures',
            '主要特征',
            '描述这个世界元素的主要特征和重要性...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'relevance',
            '与故事的关联',
            '例如：影响主角决策，推动情节发展，丰富世界背景...',
            editingWorldBuilding,
            handleInputChange
          )}
          {createShortTextareaField(
            'uniqueAspects',
            '独特之处',
            '例如：与众不同的特性，在世界中的独特地位...',
            editingWorldBuilding,
            handleInputChange
          )}
        </div>
      );
  }
};
