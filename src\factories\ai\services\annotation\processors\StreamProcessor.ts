/**
 * AI注释流式处理器
 * 负责处理AI返回的流式响应，实时更新UI状态
 */

import { TextSegment, Sentence, AnnotationCallbacks } from '../types/AnnotationTypes';
import { ResponseParser } from './ResponseParser';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

export class StreamProcessor {
  /**
   * 处理流式响应 - 增强版多层解析策略
   * @param streamResponse 流式响应文本
   * @param updatedSentences 更新的句子数组
   * @param callbacks 回调函数
   */
  static processStreamingResponse(
    streamResponse: string,
    updatedSentences: Sentence[],
    callbacks: AnnotationCallbacks
  ): void {
    try {
      console.log('🔄 开始增强流式解析，响应长度:', streamResponse.length);

      let hasUpdates = false;
      let processedSuggestions: any[] = [];

      // 策略1：尝试解析单个建议对象（优先级最高）
      console.log('🔧 策略1：单个建议对象解析');
      const singleSuggestions = ResponseParser.extractSingleSuggestions(streamResponse);

      if (singleSuggestions.length > 0) {
        console.log('✅ 策略1成功，找到单个建议:', singleSuggestions.length, '个');
        processedSuggestions = singleSuggestions;
        hasUpdates = true;
      } else {
        // 策略2：增强字段匹配解析
        console.log('🔧 策略2：增强字段匹配解析');
        const fieldResults = ResponseParser.extractAllFieldsFromText(streamResponse);

        if (fieldResults.length > 0) {
          console.log('✅ 策略2成功，找到字段匹配建议:', fieldResults.length, '个');
          processedSuggestions = fieldResults;
          hasUpdates = true;
        } else {
          // 策略3：尝试完整JSON解析（兜底方案）
          console.log('🔧 策略3：完整JSON解析（兜底方案）');
          const jsonResult = this.tryParseCompleteJSON(streamResponse);

          if (jsonResult && jsonResult.suggestions && jsonResult.suggestions.length > 0) {
            console.log('✅ 策略3成功，找到完整JSON建议:', jsonResult.suggestions.length, '个');
            processedSuggestions = jsonResult.suggestions;
            hasUpdates = true;
          }
        }
      }

      // 应用解析到的建议
      if (hasUpdates && processedSuggestions.length > 0) {
        console.log('📝 开始应用解析到的建议，数量:', processedSuggestions.length);

        processedSuggestions.forEach((suggestion: any, suggestionIndex: number) => {
          const index = suggestion.sentenceIndex;
          if (index >= 0 && index < updatedSentences.length) {
            updatedSentences[index] = {
              ...updatedSentences[index],
              aiSuggestion: suggestion.suggestion || suggestion.modifiedText || '',
              modifiedText: suggestion.modifiedText || suggestion.suggestion || '',
              modificationType: suggestion.modificationType || 'keep',
              category: suggestion.category || 'style',
              severity: suggestion.severity || 'medium',
              impact: suggestion.impact || 'moderate',
              confidence: suggestion.confidence || 0.8,
              alternatives: suggestion.alternatives || [],
              tags: suggestion.tags || [],
              reason: suggestion.reason || '',
              processingStatus: 'completed' as const,
              lastProcessedAt: new Date()
            } as any;

            console.log(`✅ 应用建议到句子 ${index + 1}:`, suggestion.modificationType);
          } else {
            console.warn(`⚠️ 句子索引超出范围: ${index}, 最大索引: ${updatedSentences.length - 1}`);
          }
        });

        // 通知UI更新
        console.log('📊 流式解析有更新，通知UI');
      }

      if (!hasUpdates) {
        console.log('⚠️ 所有流式解析策略都未找到有效建议，继续等待更多数据');
      } else {
        console.log('🎉 流式解析成功，共处理建议:', processedSuggestions.length, '个');
      }
    } catch (error) {
      console.error('❌ 增强流式解析异常:', error);
      console.log('⚠️ 解析失败，继续等待更多数据');
    }
  }

  /**
   * 尝试完整JSON解析（兜底方案）
   * @param text 响应文本
   * @returns 解析结果或null
   */
  private static tryParseCompleteJSON(text: string): any | null {
    try {
      // 使用AIResponseParser进行完整JSON解析
      const { AIResponseParser } = require('@/utils/ai/AIResponseParser');
      const defaultValue = { suggestions: [] };
      const result = AIResponseParser.parseJSON(text, defaultValue);

      if (result && result.suggestions && Array.isArray(result.suggestions) && result.suggestions.length > 0) {
        console.log('✅ 完整JSON解析成功，找到建议:', result.suggestions.length, '个');
        return result;
      }

      return null;
    } catch (error) {
      console.log('⚠️ 完整JSON解析失败:', error);
      return null;
    }
  }

  /**
   * 处理全文流式响应
   * @param streamResponse 流式响应文本
   * @param processingSegments 处理中的段落数组
   * @param callbacks 回调函数
   */
  static processFullTextStreaming(
    streamResponse: string,
    processingSegments: TextSegment[],
    callbacks: AnnotationCallbacks
  ): void {
    try {
      console.log('🔄 开始全文流式解析，响应长度:', streamResponse.length);

      // 获取所有句子的扁平数组
      const allSentences = processingSegments.flatMap(seg => seg.sentences);

      // 策略1：尝试解析单个建议对象
      const singleSuggestions = ResponseParser.extractSingleSuggestions(streamResponse);
      let hasUpdates = false;

      // 处理找到的单个建议
      singleSuggestions.forEach((suggestion: any) => {
        const globalIndex = suggestion.sentenceIndex;
        if (globalIndex >= 0 && globalIndex < allSentences.length) {
          // 找到对应的段落和句子
          const { segmentIndex, sentenceIndex } = ResponseParser.findSentenceLocation(processingSegments, globalIndex);

          if (segmentIndex >= 0 && sentenceIndex >= 0) {
            processingSegments[segmentIndex].sentences[sentenceIndex] = {
              ...processingSegments[segmentIndex].sentences[sentenceIndex],
              aiSuggestion: suggestion.suggestion || '',
              modifiedText: suggestion.modifiedText || suggestion.suggestion || '',
              modificationType: suggestion.modificationType || 'keep',
              category: suggestion.category,
              severity: suggestion.severity,
              impact: suggestion.impact,
              confidence: suggestion.confidence,
              alternatives: suggestion.alternatives || [],
              tags: suggestion.tags || [],
              // 新增连贯性相关字段
              transitionSuggestion: suggestion.transitionSuggestion || null,
              coherenceScore: suggestion.coherenceScore || null,
              flowIssues: suggestion.flowIssues || [],
              contextRelevance: suggestion.contextRelevance || null,
              processingStatus: 'completed' as const,
              lastProcessedAt: new Date()
            } as any;
            hasUpdates = true;
            console.log(`✅ 全文流式解析到句子 ${globalIndex + 1} 的建议:`, suggestion.modificationType);
          }
        }
      });

      // 策略2：如果没有找到单个建议，尝试增强字段匹配解析
      if (singleSuggestions.length === 0) {
        console.log('🔧 尝试全文增强字段匹配解析');
        const fieldResults = ResponseParser.extractAllFieldsFromText(streamResponse);

        if (fieldResults.length > 0) {
          console.log('✅ 全文增强字段匹配解析成功，找到建议:', fieldResults.length);

          fieldResults.forEach((suggestion: any) => {
            const globalIndex = suggestion.sentenceIndex;
            if (globalIndex >= 0 && globalIndex < allSentences.length) {
              const { segmentIndex, sentenceIndex } = ResponseParser.findSentenceLocation(processingSegments, globalIndex);

              if (segmentIndex >= 0 && sentenceIndex >= 0) {
                processingSegments[segmentIndex].sentences[sentenceIndex] = {
                  ...processingSegments[segmentIndex].sentences[sentenceIndex],
                  aiSuggestion: suggestion.suggestion || '',
                  modifiedText: suggestion.modifiedText || suggestion.suggestion || '',
                  modificationType: suggestion.modificationType || 'keep',
                  category: suggestion.category,
                  severity: suggestion.severity,
                  impact: suggestion.impact,
                  confidence: suggestion.confidence,
                  alternatives: suggestion.alternatives || [],
                  tags: suggestion.tags || [],
                  // 新增连贯性相关字段
                  transitionSuggestion: suggestion.transitionSuggestion || null,
                  coherenceScore: suggestion.coherenceScore || null,
                  flowIssues: suggestion.flowIssues || [],
                  contextRelevance: suggestion.contextRelevance || null,
                  processingStatus: 'completed' as const,
                  lastProcessedAt: new Date()
                } as any;
                hasUpdates = true;
                console.log(`✅ 全文字段匹配解析到句子 ${globalIndex + 1} 的建议:`, suggestion.modificationType);
              }
            }
          });
        }
      }

      // 如果有更新，通知UI
      if (hasUpdates) {
        processingSegments.forEach(segment => {
          callbacks.onSegmentComplete?.(segment);
        });
      }

      // 更新进度
      if (hasUpdates && callbacks.onProgress) {
        const processedCount = allSentences.filter(s => s.aiSuggestion).length;
        const totalCount = allSentences.length;
        callbacks.onProgress((processedCount / totalCount) * 100, processedCount, totalCount);
        console.log(`📊 全文进度更新: ${processedCount}/${totalCount} (${Math.round((processedCount / totalCount) * 100)}%)`);
      }

      if (!hasUpdates) {
        console.log('⚠️ 全文解析策略都未找到有效建议，继续等待更多数据');
      }
    } catch (error) {
      console.error('❌ 全文流式解析异常:', error);
      console.log('⚠️ 全文解析失败，继续等待更多数据');
    }
  }

  /**
   * 更新处理进度
   * @param segments 段落数组
   * @param callbacks 回调函数
   */
  static updateProgress(
    segments: TextSegment[],
    callbacks: AnnotationCallbacks
  ): void {
    try {
      const allSentences = segments.flatMap(seg => seg.sentences);
      const processedCount = allSentences.filter(s => s.aiSuggestion).length;
      const totalCount = allSentences.length;

      if (callbacks.onProgress) {
        callbacks.onProgress((processedCount / totalCount) * 100, processedCount, totalCount);
        console.log(`📊 进度更新: ${processedCount}/${totalCount} (${Math.round((processedCount / totalCount) * 100)}%)`);
      }
    } catch (error) {
      console.error('❌ 进度更新失败:', error);
    }
  }
}
