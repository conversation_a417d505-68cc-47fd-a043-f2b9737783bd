/* 预览节点样式 */
.preview-node-container {
  position: relative;
  overflow: hidden;
}

.preview-node-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.preview-node-container:hover::before {
  left: 100%;
}

.preview-node-container:hover {
  border-style: solid;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.05) !important;
}

/* 闪烁动画 */
@keyframes shimmer {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 呼吸动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

.preview-node-container.breathing {
  animation: breathe 2s ease-in-out infinite;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-node-container {
    min-width: 180px;
    max-width: 240px;
    padding: 10px 12px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .preview-node-container {
    background: rgba(30, 30, 30, 0.9);
    color: #ffffff;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .preview-node-container {
    border-width: 3px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .preview-node-container,
  .preview-node-container::before {
    animation: none;
    transition: none;
  }
}
