"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Notification, { NotificationType } from './Notification';

interface NotificationItem {
  id: string;
  message: string;
  type: NotificationType;
  duration: number;
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

// 创建一个全局通知事件
type NotificationEvent = {
  message: string;
  type?: NotificationType;
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
};

// 全局事件名称
const NOTIFICATION_EVENT = 'app:notification';

// 创建自定义事件类型
declare global {
  interface WindowEventMap {
    [NOTIFICATION_EVENT]: CustomEvent<NotificationEvent>;
  }
}

/**
 * 通知管理器组件
 * 用于管理和显示多个通知
 */
const NotificationManager: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // 添加通知
  const addNotification = useCallback((notification: NotificationEvent) => {
    const id = Math.random().toString(36).substring(2, 9);
    
    setNotifications(prev => [
      ...prev,
      {
        id,
        message: notification.message,
        type: notification.type || 'info',
        duration: notification.duration || 3000,
        position: notification.position || 'top-center'
      }
    ]);
    
    return id;
  }, []);

  // 移除通知
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  // 监听通知事件
  useEffect(() => {
    const handleNotification = (event: CustomEvent<NotificationEvent>) => {
      addNotification(event.detail);
    };

    // 添加事件监听器
    window.addEventListener(NOTIFICATION_EVENT, handleNotification as EventListener);

    // 清理事件监听器
    return () => {
      window.removeEventListener(NOTIFICATION_EVENT, handleNotification as EventListener);
    };
  }, [addNotification]);

  return (
    <>
      {notifications.map(notification => (
        <Notification
          key={notification.id}
          message={notification.message}
          type={notification.type}
          duration={notification.duration}
          position={notification.position}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </>
  );
};

// 辅助函数，用于触发通知
export const showNotification = (
  message: string,
  type: NotificationType = 'info',
  duration: number = 3000,
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center' = 'top-center'
) => {
  const event = new CustomEvent<NotificationEvent>(NOTIFICATION_EVENT, {
    detail: { message, type, duration, position }
  });
  
  window.dispatchEvent(event);
};

export default NotificationManager;
