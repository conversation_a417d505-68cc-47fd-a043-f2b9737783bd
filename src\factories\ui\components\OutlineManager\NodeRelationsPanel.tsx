"use client";

import React, { useState, useEffect } from 'react';
import { useOutlineManager } from '../../hooks/useOutlineManager';
import { OutlineNodeType } from '../../types/outline';

interface NodeRelationsPanelProps {
  node: OutlineNodeType;
}

/**
 * 节点关联面板组件
 * 用于显示和管理节点的关联元素
 */
export const NodeRelationsPanel: React.FC<NodeRelationsPanelProps> = ({ node }) => {
  const { addNodeRelation, removeNodeRelation, getNodeRelations } = useOutlineManager();
  
  const [characters, setCharacters] = useState<any[]>([]);
  const [worldBuildings, setWorldBuildings] = useState<any[]>([]);
  const [terminologies, setTerminologies] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'character' | 'worldBuilding' | 'terminology'>('character');
  
  // 加载关联元素
  useEffect(() => {
    const loadRelations = async () => {
      setIsLoading(true);
      try {
        const relations = await getNodeRelations(node.id);
        setCharacters(relations.characters);
        setWorldBuildings(relations.worldBuildings);
        setTerminologies(relations.terminologies);
      } catch (error) {
        console.error('加载关联元素失败:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadRelations();
  }, [node.id, getNodeRelations]);
  
  // 处理移除关联
  const handleRemoveRelation = async (relationType: 'character' | 'worldBuilding' | 'terminology', relationId: string) => {
    try {
      await removeNodeRelation(node.id, relationType, relationId);
      
      // 更新本地状态
      switch (relationType) {
        case 'character':
          setCharacters(prev => prev.filter(item => item.id !== relationId));
          break;
        case 'worldBuilding':
          setWorldBuildings(prev => prev.filter(item => item.id !== relationId));
          break;
        case 'terminology':
          setTerminologies(prev => prev.filter(item => item.id !== relationId));
          break;
      }
    } catch (error) {
      console.error('移除关联失败:', error);
    }
  };
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">关联元素</h3>
      
      {/* 标签页 */}
      <div className="flex border-b mb-4">
        <button
          className={`px-4 py-2 ${activeTab === 'character' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('character')}
        >
          人物 ({characters.length})
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'worldBuilding' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('worldBuilding')}
        >
          世界观 ({worldBuildings.length})
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'terminology' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('terminology')}
        >
          术语 ({terminologies.length})
        </button>
      </div>
      
      {/* 内容区域 */}
      <div className="mt-4">
        {isLoading ? (
          <div className="text-center py-4">加载中...</div>
        ) : (
          <>
            {/* 人物标签页 */}
            {activeTab === 'character' && (
              <div>
                {characters.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">暂无关联人物</div>
                ) : (
                  <div className="space-y-2">
                    {characters.map(character => (
                      <div key={character.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium">{character.name}</div>
                          {character.alias && character.alias.length > 0 && (
                            <div className="text-xs text-gray-500">别名: {character.alias.join(', ')}</div>
                          )}
                        </div>
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleRemoveRelation('character', character.id)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* 世界观标签页 */}
            {activeTab === 'worldBuilding' && (
              <div>
                {worldBuildings.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">暂无关联世界观</div>
                ) : (
                  <div className="space-y-2">
                    {worldBuildings.map(worldBuilding => (
                      <div key={worldBuilding.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium">{worldBuilding.name}</div>
                          <div className="text-xs text-gray-500">{worldBuilding.category}</div>
                        </div>
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleRemoveRelation('worldBuilding', worldBuilding.id)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* 术语标签页 */}
            {activeTab === 'terminology' && (
              <div>
                {terminologies.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">暂无关联术语</div>
                ) : (
                  <div className="space-y-2">
                    {terminologies.map(terminology => (
                      <div key={terminology.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium">{terminology.name}</div>
                          <div className="text-xs text-gray-500">{terminology.category}</div>
                        </div>
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleRemoveRelation('terminology', terminology.id)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
