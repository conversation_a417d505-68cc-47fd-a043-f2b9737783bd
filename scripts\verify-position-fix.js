/**
 * 验证大纲画布位置修复的脚本
 * 这个脚本模拟了transformOutlineToFlow函数的核心逻辑
 */

// 模拟的大纲数据
const mockOutline = {
  id: 'test-outline',
  workId: 'test-work',
  title: '测试大纲',
  nodes: [
    {
      id: 'node-1',
      title: '节点1',
      type: 'chapter',
      position: { x: 100, y: 200 }, // 手动设置的位置
      children: []
    },
    {
      id: 'node-2',
      title: '节点2',
      type: 'chapter',
      // 没有position，应该使用计算的默认位置
      children: []
    },
    {
      id: 'node-3',
      title: '节点3',
      type: 'chapter',
      position: { x: null, y: 200 }, // 无效的位置
      children: []
    }
  ],
  lastModified: new Date(),
  version: 1
};

// 模拟transformOutlineToFlow的核心位置处理逻辑
function processNodePosition(node, nodeIndex) {
  console.log(`\n处理节点: ${node.id} (${node.title})`);
  
  let position;
  
  if (node.position && typeof node.position.x === 'number' && typeof node.position.y === 'number') {
    // 使用保存的位置信息（手动拖拽或自动布局设置的位置）
    position = { ...node.position };
    console.log(`✅ 节点 ${node.id} 使用保存的位置:`, position);
  } else {
    // 计算默认位置（仅在没有保存位置时使用）
    position = {
      x: nodeIndex * 300,
      y: 0
    };
    console.log(`⚠️  节点 ${node.id} 使用计算的默认位置:`, position);
  }
  
  return position;
}

// 验证修复效果
function verifyPositionFix() {
  console.log('🔍 开始验证大纲画布位置修复...\n');
  
  console.log('📋 测试数据:');
  mockOutline.nodes.forEach((node, index) => {
    console.log(`  - ${node.id}: ${node.title}, position:`, node.position || '未设置');
  });
  
  console.log('\n🔧 处理结果:');
  const results = mockOutline.nodes.map((node, index) => {
    const position = processNodePosition(node, index);
    return {
      nodeId: node.id,
      title: node.title,
      originalPosition: node.position,
      finalPosition: position,
      usedSavedPosition: !!(node.position && typeof node.position.x === 'number' && typeof node.position.y === 'number')
    };
  });
  
  console.log('\n📊 验证结果总结:');
  results.forEach(result => {
    const status = result.usedSavedPosition ? '✅ 保持手动位置' : '⚠️  使用默认位置';
    console.log(`  ${result.nodeId}: ${status}`);
    console.log(`    原始位置: ${JSON.stringify(result.originalPosition)}`);
    console.log(`    最终位置: ${JSON.stringify(result.finalPosition)}`);
  });
  
  // 验证预期结果
  console.log('\n🎯 预期验证:');
  
  // 节点1应该保持手动设置的位置
  const node1Result = results.find(r => r.nodeId === 'node-1');
  if (node1Result.usedSavedPosition && node1Result.finalPosition.x === 100 && node1Result.finalPosition.y === 200) {
    console.log('✅ 节点1: 正确保持了手动设置的位置');
  } else {
    console.log('❌ 节点1: 未能保持手动设置的位置');
  }
  
  // 节点2应该使用默认位置
  const node2Result = results.find(r => r.nodeId === 'node-2');
  if (!node2Result.usedSavedPosition && node2Result.finalPosition.x === 300 && node2Result.finalPosition.y === 0) {
    console.log('✅ 节点2: 正确使用了计算的默认位置');
  } else {
    console.log('❌ 节点2: 默认位置计算错误');
  }
  
  // 节点3应该使用默认位置（因为原位置无效）
  const node3Result = results.find(r => r.nodeId === 'node-3');
  if (!node3Result.usedSavedPosition && node3Result.finalPosition.x === 600 && node3Result.finalPosition.y === 0) {
    console.log('✅ 节点3: 正确处理了无效位置，使用默认位置');
  } else {
    console.log('❌ 节点3: 无效位置处理错误');
  }
  
  console.log('\n🎉 验证完成！');
  console.log('修复后的逻辑确保：');
  console.log('1. 有效的手动位置会被保持');
  console.log('2. 无效或缺失的位置会使用合理的默认值');
  console.log('3. 不会意外重置用户的手动调整');
}

// 运行验证
verifyPositionFix();
