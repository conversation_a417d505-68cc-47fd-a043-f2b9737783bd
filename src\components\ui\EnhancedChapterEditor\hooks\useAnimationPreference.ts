"use client";

import { useState, useEffect } from 'react';

interface AnimationPreference {
  animationsEnabled: boolean;
  reducedMotion: boolean;
  animationIntensity: 'low' | 'medium' | 'high';
}

/**
 * 动画偏好设置Hook
 * 管理用户的动画偏好设置，包括系统级的减少动画设置
 */
export const useAnimationPreference = () => {
  const [preferences, setPreferences] = useState<AnimationPreference>({
    animationsEnabled: true,
    reducedMotion: false,
    animationIntensity: 'medium'
  });

  useEffect(() => {
    // 检查系统级的减少动画设置
    const checkReducedMotion = () => {
      if (typeof window !== 'undefined') {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        return mediaQuery.matches;
      }
      return false;
    };

    // 从本地存储加载用户偏好
    const loadPreferences = () => {
      if (typeof window !== 'undefined') {
        try {
          const stored = localStorage.getItem('enhanced-editor-animation-preferences');
          if (stored) {
            const parsed = JSON.parse(stored);
            return {
              animationsEnabled: parsed.animationsEnabled ?? true,
              reducedMotion: checkReducedMotion(),
              animationIntensity: parsed.animationIntensity ?? 'medium'
            };
          }
        } catch (error) {
          console.warn('Failed to load animation preferences:', error);
        }
      }
      
      return {
        animationsEnabled: true,
        reducedMotion: checkReducedMotion(),
        animationIntensity: 'medium' as const
      };
    };

    // 初始化偏好设置
    const initialPreferences = loadPreferences();
    setPreferences(initialPreferences);

    // 监听系统减少动画设置的变化
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      const handleChange = (e: MediaQueryListEvent) => {
        setPreferences(prev => ({
          ...prev,
          reducedMotion: e.matches
        }));
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);

  // 保存偏好设置到本地存储
  const savePreferences = (newPreferences: Partial<AnimationPreference>) => {
    const updated = { ...preferences, ...newPreferences };
    setPreferences(updated);

    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('enhanced-editor-animation-preferences', JSON.stringify({
          animationsEnabled: updated.animationsEnabled,
          animationIntensity: updated.animationIntensity
          // 不保存 reducedMotion，因为它来自系统设置
        }));
      } catch (error) {
        console.warn('Failed to save animation preferences:', error);
      }
    }
  };

  // 切换动画开关
  const toggleAnimations = () => {
    savePreferences({ animationsEnabled: !preferences.animationsEnabled });
  };

  // 设置动画强度
  const setAnimationIntensity = (intensity: 'low' | 'medium' | 'high') => {
    savePreferences({ animationIntensity: intensity });
  };

  // 检查是否应该显示动画
  const shouldShowAnimations = () => {
    return preferences.animationsEnabled && !preferences.reducedMotion;
  };

  // 获取当前有效的动画强度
  const getEffectiveIntensity = () => {
    if (!shouldShowAnimations()) return 'low';
    return preferences.animationIntensity;
  };

  return {
    ...preferences,
    toggleAnimations,
    setAnimationIntensity,
    savePreferences,
    shouldShowAnimations,
    getEffectiveIntensity
  };
};
