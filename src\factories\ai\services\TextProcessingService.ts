"use client";

// 导入标注相关的类型定义
export interface TextSegment {
  id: string;
  content: string;
  sentences: Sentence[];
  startIndex: number;
  endIndex: number;
  // 选择模式相关字段
  selectionMode?: boolean; // 是否处于选择模式
  selectedCount?: number; // 已选择句子数量
}

export interface Sentence {
  id: string;
  text: string;
  aiSuggestion?: string; // 建议说明和理由
  modifiedText?: string; // 具体修改后的文本
  modificationType?: 'modify' | 'delete' | 'split' | 'merge' | 'enhance' | 'create' | 'simplify' | 'reorder' | 'keep';
  isAccepted?: boolean;
  // 句子选择功能
  isSelected?: boolean; // 是否被选中进行处理
  selectionReason?: string; // 选择原因（可选）
  // 增强字段（可选）
  category?: SuggestionCategory;
  severity?: 'low' | 'medium' | 'high';
  impact?: 'minor' | 'moderate' | 'significant';
  confidence?: number;
  alternatives?: Array<{
    text: string;
    style?: string;
    reason?: string;
  }>;
  tags?: string[];
  // 独立的句子创建功能
  hasCreation?: boolean;
  createdSentences?: string[];
  insertPosition?: 'before' | 'after';
  contentType?: 'dialogue' | 'description' | 'action' | 'emotion';
  insertMode?: 'single' | 'batch';
  contextHint?: string;
}

// 增强的建议类型定义
export type SuggestionType =
  | 'modify'     // 修改
  | 'delete'     // 删除
  | 'split'      // 拆分
  | 'merge'      // 合并
  | 'reorder'    // 重排
  | 'enhance'    // 增强
  | 'create'     // 创建新句子
  | 'simplify'   // 简化
  | 'keep';      // 保持

export type SuggestionCategory =
  | 'grammar'    // 语法修正
  | 'style'      // 风格调整
  | 'logic'      // 逻辑优化
  | 'expression' // 表达增强
  | 'structure'  // 结构调整
  | 'clarity';   // 清晰度提升

export interface SuggestionOption {
  id: string; // 唯一标识
  type: SuggestionType;
  text?: string; // 修改后的文本
  reason: string; // 修改理由
  confidence: number; // 该建议的置信度 0-1
  style?: string; // 风格标签
  tags?: string[]; // 特性标签
  estimatedImpact?: 'minor' | 'moderate' | 'significant'; // 预估影响程度
}

export interface EnhancedSentenceSuggestion {
  sentenceIndex: number;
  originalText: string;
  analysisResult: {
    issues: string[]; // 识别的问题
    confidence: number; // 置信度 0-1
    category: SuggestionCategory; // 建议分类
    severity: 'low' | 'medium' | 'high'; // 问题严重程度
  };
  suggestions: SuggestionOption[]; // 多个建议选项
  recommendedAction: string; // 推荐操作
  priority: 'low' | 'medium' | 'high'; // 优先级
  selectedSuggestionIndex: number; // 用户选择的建议索引
  userModified: boolean; // 用户是否手动修改过
  lastAccessed?: number; // 最后访问时间（用于缓存）
}

// 增强的句子接口，支持多选项建议
export interface EnhancedSentence extends Sentence {
  enhancedSuggestion?: EnhancedSentenceSuggestion;
  suggestions?: SuggestionOption[];
  selectedSuggestionIndex?: number;
  analysisResult?: {
    issues: string[];
    confidence: number;
    category: SuggestionCategory;
    severity: 'low' | 'medium' | 'high';
  };
}

/**
 * 文本处理服务接口
 */
export interface TextProcessingServiceInterface {
  /**
   * 计算实际字数
   * @param text 文本内容
   * @returns 字数
   */
  calculateActualWordCount(text: string): number;

  /**
   * 智能分段文本
   * @param text 文本内容
   * @returns 分段后的文本数组
   */
  segmentText(text: string): string[];

  /**
   * 按指定长度切分文本（用于全文标注）
   * @param text 文本内容
   * @param maxLength 最大长度，默认400字
   * @returns 切分后的文本段落数组
   */
  segmentTextByLength(text: string, maxLength?: number): TextSegment[];

  /**
   * 将文本分割成句子
   * @param text 文本内容
   * @returns 句子数组
   */
  splitIntoSentences(text: string): Sentence[];

  /**
   * 在指定位置附近寻找语义边界
   * @param text 文本内容
   * @param position 目标位置
   * @param searchRange 搜索范围，默认50字符
   * @returns 语义边界位置
   */
  findSemanticBoundary(text: string, position: number, searchRange?: number): number;
}

/**
 * 文本处理服务实现
 */
export class TextProcessingService implements TextProcessingServiceInterface {
  /**
   * 计算实际字数
   * @param text 文本内容
   * @returns 字数
   */
  public calculateActualWordCount(text: string): number {
    // 移除Markdown标记
    const plainText = text.replace(/#+\s+/g, '') // 移除标题标记
                          .replace(/\*\*|\*|~~|__/g, '') // 移除加粗、斜体、删除线等标记
                          .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
                          .replace(/```[\s\S]*?```/g, '') // 移除代码块
                          .replace(/`.*?`/g, '') // 移除行内代码
                          .replace(/>\s+/g, '') // 移除引用
                          .replace(/\s+/g, ''); // 移除空白字符

    // 计算字符数
    return plainText.length;
  }

  /**
   * 智能分段文本
   * @param text 文本内容
   * @returns 分段后的文本数组
   */
  public segmentText(text: string): string[] {
    // 如果文本为空，返回空数组
    if (!text || text.trim().length === 0) {
      return [];
    }

    // 目标段落字数（约2000字左右）
    const targetSegmentWordCount = 2000;

    // 按段落分割文本
    const paragraphs = text.split(/\n\s*\n/);
    const segments: string[] = [];

    let currentSegment = '';
    let currentSegmentWordCount = 0;

    // 遍历段落，按照目标字数合并段落
    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim();
      if (!trimmedParagraph) continue;

      const paragraphWordCount = this.calculateActualWordCount(trimmedParagraph);

      // 如果当前段落加上新段落的字数小于目标字数，则合并
      if (currentSegmentWordCount + paragraphWordCount <= targetSegmentWordCount) {
        currentSegment += (currentSegment ? '\n\n' : '') + trimmedParagraph;
        currentSegmentWordCount += paragraphWordCount;
      } else {
        // 如果当前段落已经有内容，则添加到segments中
        if (currentSegment) {
          segments.push(currentSegment);
        }

        // 如果新段落的字数大于目标字数的一半，则单独作为一个段落
        if (paragraphWordCount > targetSegmentWordCount / 2) {
          segments.push(trimmedParagraph);
          currentSegment = '';
          currentSegmentWordCount = 0;
        } else {
          // 否则，开始一个新的段落
          currentSegment = trimmedParagraph;
          currentSegmentWordCount = paragraphWordCount;
        }
      }
    }

    // 添加最后一个段落
    if (currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  }

  /**
   * 按指定长度切分文本（用于全文标注）
   * @param text 文本内容
   * @param maxLength 最大长度，默认400字
   * @returns 切分后的文本段落数组
   */
  public segmentTextByLength(text: string, maxLength: number = 400): TextSegment[] {
    if (!text || text.trim().length === 0) {
      return [];
    }

    const segments: TextSegment[] = [];
    let currentIndex = 0;
    let segmentIndex = 0;

    while (currentIndex < text.length) {
      let endIndex = Math.min(currentIndex + maxLength, text.length);

      // 如果不是最后一段，尝试找到语义边界
      if (endIndex < text.length) {
        endIndex = this.findSemanticBoundary(text, endIndex);
      }

      const segmentContent = text.slice(currentIndex, endIndex).trim();

      if (segmentContent) {
        const segmentId = `segment_${Date.now()}_${segmentIndex.toString().padStart(3, '0')}`;
        const sentences = this.splitIntoSentencesWithSegmentId(segmentContent, segmentId);

        segments.push({
          id: segmentId,
          content: segmentContent,
          sentences: sentences,
          startIndex: currentIndex,
          endIndex: endIndex
        });

        segmentIndex++;
      }

      currentIndex = endIndex;
    }

    return segments;
  }



  /**
   * 将文本分割成句子
   * @param text 文本内容
   * @returns 句子数组
   */
  public splitIntoSentences(text: string): Sentence[] {
    return this.splitIntoSentencesWithSegmentId(text, null);
  }

  /**
   * 将文本分割成句子，并生成包含段落ID的唯一句子ID
   * @param text 文本内容
   * @param segmentId 段落ID，如果为null则使用时间戳
   * @returns 句子数组
   */
  public splitIntoSentencesWithSegmentId(text: string, segmentId: string | null): Sentence[] {
    if (!text || text.trim().length === 0) {
      return [];
    }

    console.log('🔍 开始分割句子（引号保护模式）:', {
      textLength: text.length,
      segmentId: segmentId,
      textPreview: text.substring(0, 50) + '...'
    });

    // 使用智能引号感知的句子分割算法
    const sentenceTexts = this.splitSentencesWithQuoteProtection(text);

    const sentences: Sentence[] = [];

    sentenceTexts.forEach((sentenceText, index) => {
      // 只移除开头的空白，保留结尾的换行符
      const trimmedText = sentenceText.replace(/^\s+/, '');

      if (trimmedText) {
        // 生成唯一的句子ID，包含段落信息
        const sentenceId = segmentId
          ? `${segmentId}-sentence-${index.toString().padStart(3, '0')}`
          : `sentence_${Date.now()}_${index.toString().padStart(3, '0')}`;

        console.log('✅ 创建句子（引号保护）:', {
          sentenceId,
          segmentId,
          localIndex: index,
          textPreview: trimmedText.substring(0, 30) + '...',
          hasQuotes: /[""''「」『』]/.test(trimmedText)
        });

        sentences.push({
          id: sentenceId,
          text: trimmedText,
          modificationType: 'keep'
        });
      }
    });

    console.log('📊 句子分割完成（引号保护）:', {
      segmentId,
      totalSentences: sentences.length,
      sentenceIds: sentences.map(s => s.id),
      quotedSentences: sentences.filter(s => /[""''「」『』]/.test(s.text)).length
    });

    return sentences;
  }

  /**
   * 智能引号感知的句子分割算法
   * 保护对话内容不被错误分割
   * @param text 文本内容
   * @returns 分割后的句子数组
   */
  private splitSentencesWithQuoteProtection(text: string): string[] {
    const sentences: string[] = [];
    let currentSentence = '';
    let quoteStack: string[] = []; // 跟踪引号嵌套
    let i = 0;

    // 定义引号配对关系
    const quoteMap: { [key: string]: string } = {
      '\u201c': '\u201d', // " → "
      '\u2018': '\u2019', // ' → '
      '\u300c': '\u300d', // 「 → 」
      '\u300e': '\u300f', // 『 → 』
      '"': '"',           // 标准双引号
      "'": "'"            // 标准单引号
    };

    // 获取引号的配对字符
    const getQuotePair = (quote: string): string => {
      return quoteMap[quote] || quote;
    };

    // 检查是否是引号字符
    const isQuoteChar = (char: string): boolean => {
      return /[\u201c\u201d\u2018\u2019\u300c\u300d\u300e\u300f"']/.test(char);
    };

    // 检查是否是句子结束标点
    const isSentenceEnd = (char: string): boolean => {
      return /[。！？…]/.test(char);
    };

    // 处理引号状态
    const handleQuoteState = (char: string) => {
      const expectedClosing = quoteStack[quoteStack.length - 1];

      if (expectedClosing && char === expectedClosing) {
        // 找到匹配的闭合引号
        quoteStack.pop();
      } else if (isQuoteChar(char)) {
        // 新的开始引号
        const closingQuote = getQuotePair(char);
        quoteStack.push(closingQuote);
      }
    };

    while (i < text.length) {
      const char = text[i];
      currentSentence += char;

      // 检查引号状态
      if (isQuoteChar(char)) {
        handleQuoteState(char);
      }

      // 检查句子结束标点
      if (isSentenceEnd(char)) {
        // 只有在引号外才分割句子
        if (quoteStack.length === 0) {
          // 检查下一个字符，如果是引号结束符，包含进来
          let nextIndex = i + 1;
          while (nextIndex < text.length && /[\u201d\u2019\u300d\u300f"'）】\]}]/.test(text[nextIndex])) {
            currentSentence += text[nextIndex];
            nextIndex++;
          }

          sentences.push(currentSentence.trim());
          currentSentence = '';
          i = nextIndex - 1; // 调整索引
        }
      }

      i++;
    }

    // 处理最后一个句子
    if (currentSentence.trim()) {
      sentences.push(currentSentence.trim());
    }

    console.log('📝 引号保护分割结果:', {
      totalSentences: sentences.length,
      quotedSentences: sentences.filter(s => /[\u201c\u201d\u2018\u2019\u300c\u300d\u300e\u300f"']/.test(s)).length,
      samples: sentences.slice(0, 3).map(s => s.substring(0, 30) + '...')
    });

    return sentences.filter(s => s.length > 0);
  }

  /**
   * 在指定位置附近寻找语义边界
   * @param text 文本内容
   * @param position 目标位置
   * @param searchRange 搜索范围，默认50字符
   * @returns 语义边界位置
   */
  public findSemanticBoundary(text: string, position: number, searchRange: number = 50): number {
    const startSearch = Math.max(0, position - searchRange);
    const endSearch = Math.min(text.length, position + searchRange);

    // 优先级顺序：段落结束 > 句子结束 > 标点符号 > 空格
    const boundaries = [
      { regex: /\n\s*\n/g, priority: 1 }, // 段落结束
      { regex: /[。！？…]+/g, priority: 2 }, // 句子结束
      { regex: /[，、；：]/g, priority: 3 }, // 标点符号
      { regex: /\s+/g, priority: 4 } // 空格
    ];

    let bestBoundary = position;
    let bestPriority = 5;

    for (const boundary of boundaries) {
      const searchText = text.slice(startSearch, endSearch);
      let match;

      while ((match = boundary.regex.exec(searchText)) !== null) {
        const boundaryPos = startSearch + match.index + match[0].length;
        const distance = Math.abs(boundaryPos - position);

        // 如果找到更高优先级的边界，或者同优先级但距离更近的边界
        if (boundary.priority < bestPriority ||
            (boundary.priority === bestPriority && distance < Math.abs(bestBoundary - position))) {
          bestBoundary = boundaryPos;
          bestPriority = boundary.priority;
        }
      }

      // 重置正则表达式的lastIndex
      boundary.regex.lastIndex = 0;
    }

    // 确保边界不超出文本范围
    return Math.min(Math.max(bestBoundary, 0), text.length);
  }
}

// 创建文本处理服务的工厂函数
export function createTextProcessingService(): TextProcessingServiceInterface {
  return new TextProcessingService();
}
