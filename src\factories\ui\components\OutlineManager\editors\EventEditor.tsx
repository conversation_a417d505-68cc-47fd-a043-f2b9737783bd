import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { OutlineNodeType } from '../../../types/outline';

interface EventEditorProps {
  node: OutlineNodeType;
  onChange?: (updatedNode: OutlineNodeType) => void;
  onSave?: (data: Partial<OutlineNodeType>) => void;
  onCancel?: () => void;
  allNodes?: OutlineNodeType[];
}

// 暴露给父组件的方法
export interface EventEditorRef {
  triggerSave: () => void;
}

// 章节接口定义
interface Chapter {
  id?: string;
  title: string;
  order: number;
  wordCount?: number;
}

export const EventEditor = forwardRef<EventEditorRef, EventEditorProps>(({
  node,
  onChange,
  onSave,
  onCancel,
  allNodes
}, ref) => {
  // 章节数据状态
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  const [formData, setFormData] = useState({
    title: node.title || '',
    description: node.description || '',
    creativeNotes: node.creativeNotes || '',
    eventStart: node.eventStart || '',
    eventEnd: node.eventEnd || '',
    eventTrigger: node.eventTrigger || '',
    eventConsequence: node.eventConsequence || '',
    eventScope: node.eventScope || '',
    chapterCount: node.chapterCount || 10,
    targetWordCount: node.targetWordCount || 200000,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 加载章节节点数据
  useEffect(() => {
    if (allNodes) {
      loadChapterNodes();
    }
  }, [allNodes]);

  const loadChapterNodes = () => {
    if (!allNodes) return;

    setIsLoadingChapters(true);
    try {
      // 从传入的所有节点中筛选出章节类型的节点
      const chapterNodes: Chapter[] = allNodes
        .filter(node => node.type === 'chapter')
        .map((node, index) => ({
          id: node.id,
          title: node.title || `第${index + 1}章`,
          order: index,
          wordCount: 0
        }));

      // 按标题中的章节号排序
      const sortedChapters = chapterNodes.sort((a, b) => {
        const aNum = parseInt(a.title.match(/第(\d+)章/)?.[1] || '0');
        const bNum = parseInt(b.title.match(/第(\d+)章/)?.[1] || '0');
        return aNum - bNum;
      });

      setChapters(sortedChapters);
      console.log('📚 加载到章节节点:', sortedChapters.length, '个');
    } catch (error) {
      console.error('加载章节节点失败:', error);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除相关错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // 实时更新（如果有onChange回调）
    if (onChange) {
      const updatedNode = { ...node, [field]: value };
      onChange(updatedNode);
    }
  };

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '事件标题不能为空';
    }

    if (!formData.eventStart.trim()) {
      newErrors.eventStart = '事件起始描述不能为空';
    }

    if (!formData.eventEnd.trim()) {
      newErrors.eventEnd = '事件结束描述不能为空';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理保存
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    const updatedData = {
      ...formData,
      type: 'event' as const,
    };

    console.log('💾 保存事件刚数据:', updatedData);

    if (onSave) {
      onSave(updatedData);
    }
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    triggerSave: handleSave
  }));

  return (
    <div className="space-y-6">
      {/* 基础信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">基础信息</h3>
        
        {/* 事件标题 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            事件标题 *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请输入事件的标题"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* 事件描述 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            事件描述
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请简要描述这个事件的概况"
          />
        </div>
      </div>

      {/* 事件设定 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">事件设定</h3>
        
        {/* 事件起始 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            事件起始 *
          </label>
          <textarea
            value={formData.eventStart}
            onChange={(e) => handleInputChange('eventStart', e.target.value)}
            rows={3}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.eventStart ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请描述事件的起始状态和触发背景"
          />
          {errors.eventStart && (
            <p className="mt-1 text-sm text-red-600">{errors.eventStart}</p>
          )}
        </div>

        {/* 事件结束 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            事件结束 *
          </label>
          <textarea
            value={formData.eventEnd}
            onChange={(e) => handleInputChange('eventEnd', e.target.value)}
            rows={3}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.eventEnd ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="请描述事件的结束状态和最终结果"
          />
          {errors.eventEnd && (
            <p className="mt-1 text-sm text-red-600">{errors.eventEnd}</p>
          )}
        </div>

        {/* 触发条件 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            触发条件
          </label>
          <textarea
            value={formData.eventTrigger}
            onChange={(e) => handleInputChange('eventTrigger', e.target.value)}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请描述导致事件发生的具体条件和原因"
          />
        </div>

        {/* 结果影响 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            结果影响
          </label>
          <textarea
            value={formData.eventConsequence}
            onChange={(e) => handleInputChange('eventConsequence', e.target.value)}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请描述事件结束后对后续剧情的影响"
          />
        </div>

        {/* 影响范围 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            影响范围
          </label>
          <textarea
            value={formData.eventScope}
            onChange={(e) => handleInputChange('eventScope', e.target.value)}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请描述事件影响的人物、地点、时间范围"
          />
        </div>
      </div>

      {/* 规划设定 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">规划设定</h3>
        
        {/* 预期章节数 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            预期章节数
          </label>
          <input
            type="number"
            value={formData.chapterCount}
            onChange={(e) => handleInputChange('chapterCount', parseInt(e.target.value) || 0)}
            min="1"
            max="100"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 目标字数 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            目标字数
          </label>
          <input
            type="number"
            value={formData.targetWordCount}
            onChange={(e) => handleInputChange('targetWordCount', parseInt(e.target.value) || 0)}
            min="1000"
            step="1000"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* 创作建议 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          创作建议
        </label>
        <textarea
          value={formData.creativeNotes}
          onChange={(e) => handleInputChange('creativeNotes', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="请输入事件的创作指导和整体规划要点"
        />
      </div>

      {/* 在onChange模式下不显示操作按钮，使用InlineNodeEditor统一的保存按钮 */}
      {!onChange && onSave && (
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            保存
          </button>
        </div>
      )}
    </div>
  );
});

// 设置displayName用于调试
EventEditor.displayName = 'EventEditor';
