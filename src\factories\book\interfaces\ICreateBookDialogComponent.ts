import { IUIComponent } from '@/factories/ui/interfaces';

/**
 * 创建书籍对话框组件接口
 */
export interface ICreateBookDialogComponent extends IUIComponent {
  /**
   * 设置是否显示对话框
   * @param isOpen 是否显示
   */
  setIsOpen(isOpen: boolean): void;
  
  /**
   * 设置创建书籍回调函数
   * @param callback 回调函数，参数为书籍标题和描述
   */
  onCreateBook(callback: (title: string, description: string) => void): void;
  
  /**
   * 设置取消回调函数
   * @param callback 回调函数
   */
  onCancel(callback: () => void): void;
}
