import { IAnimationComponent } from './IAnimationComponent';

/**
 * 淡入淡出动画方向
 */
export type FadeDirection = 'up' | 'down' | 'left' | 'right' | 'none';

/**
 * 淡入淡出动画接口
 */
export interface IFadeAnimation extends IAnimationComponent {
  /**
   * 设置动画方向
   * @param direction 方向
   */
  setDirection(direction: FadeDirection): void;
  
  /**
   * 设置动画持续时间
   * @param duration 持续时间（毫秒）
   */
  setDuration(duration: number): void;
  
  /**
   * 设置动画延迟
   * @param delay 延迟时间（毫秒）
   */
  setDelay(delay: number): void;
  
  /**
   * 设置是否显示
   * @param visible 是否显示
   */
  setVisible(visible: boolean): void;
}
