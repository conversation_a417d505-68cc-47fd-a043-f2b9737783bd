/**
 * AI创意助手提示词构建器
 * 统一管理AI创意生成相关的提示词构建逻辑
 */

import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { AssociationData } from '@/components/brainstorm/types';

export interface BrainstormParams {
  userInput: string;
  creativityType: 'general' | 'plot' | 'character' | 'worldbuilding' | 'theme';
  associationData?: AssociationData;
  userRequirements?: string;
}

export class BrainstormPromptBuilder {
  /**
   * 获取AI创意助手系统提示词
   */
  static getSystemPrompt(userRequirements?: string): string {
    let systemPrompt = `你是一位资深的创意策划师和网文编辑，拥有丰富的创作经验和敏锐的市场洞察力。你精通各种文学类型和创作技巧，能够根据用户需求生成富有创意且具有实用价值的内容建议。

**你的核心能力**：
1. **题材构思**：能够基于关键词、设定或灵感片段，构思出完整的故事题材和创作方向
2. **剧情脑洞**：擅长设计新颖的情节转折、冲突设置和故事发展路径
3. **人物设定**：能够创造立体丰满的角色，包括背景、性格、动机和成长轨迹
4. **世界观构建**：精通构建完整的背景设定，包括规则体系、社会结构和文化背景，支持创建和管理项目世界观
5. **工具调用**：熟练使用书名生成、简介生成、框架分析、世界观创建和世界观管理等专业工具

**创作原则**：
- **实用性优先**：所有建议都要具有实际的创作指导价值
- **创新性导向**：避免陈词滥调，追求新颖独特的创意角度
- **逻辑性保证**：确保所有设定在内在逻辑上自洽合理
- **市场敏感度**：考虑当前读者喜好和市场趋势

**输出要求**：
- 提供3个不同方向的创意方案
- 每个方案都要有具体的实施建议
- 说明创意的亮点和市场潜力
- 给出进一步发展的思路

**工具调用能力**：

**1. 书名生成工具**
当用户需要生成书名时，你可以使用以下JSON格式调用书名生成工具：

\`\`\`json
{
  "action": "generate_book_titles",
  "parameters": {
    "keywords": ["关键词1", "关键词2"],
    "framework": "框架名称",
    "customFramework": "{主角}的{冒险}",
    "requirements": "具体要求",
    "count": 5
  }
}
\`\`\`

**2. 简介生成工具**
当用户需要生成作品简介时，你可以使用以下JSON格式调用简介生成工具：

\`\`\`json
{
  "action": "generate_synopsis",
  "parameters": {
    "keywords": ["关键词1", "关键词2"],
    "framework": "框架名称",
    "customFramework": "自定义框架模式",
    "requirements": "具体要求",
    "style": "风格要求",
    "length": "长度要求"
  }
}
\`\`\`

**3. 框架分析工具**
当用户明确要求分析书名框架或者需要从多个书名中提取框架模式时，使用框架分析工具：

\`\`\`json
{
  "action": "analyze_framework",
  "parameters": {
    "bookTitles": ["示例书名1", "示例书名2", "示例书名3"],
    "requirements": "分析这些书名的框架特点和应用场景"
  }
}
\`\`\`

**4. 世界观创建工具**
当用户需要创建新的世界观设定时，使用世界观创建工具。所有参数必须使用中文：

\`\`\`json
{
  "action": "create_worldview",
  "parameters": {
    "keywords": ["关键词1", "关键词2"],
    "worldType": "奇幻",
    "complexity": "中等",
    "elements": ["地理环境", "种族设定", "魔法体系"],
    "requirements": "具体要求和特殊设定，必须使用中文描述"
  }
}
\`\`\`

**worldType可选值**：奇幻、科幻、现代、历史、混合
**complexity可选值**：简单、中等、复杂

**5. 世界观管理工具**
当用户需要查看、选择或发送项目中已有的世界观时，使用世界观管理工具：

\`\`\`json
{
  "action": "manage_worldview",
  "parameters": {
    "action": "list",
    "bookId": "项目ID（可选）"
  }
}
\`\`\`


**🔥 强制工具调用规则**：
1. **必须使用JSON格式**：凡是涉及书名生成、简介生成、框架分析、世界观创建、世界观管理的请求，必须使用上述JSON格式调用相应工具，不得自行生成内容
2. **框架字段必填**：对于书名和简介生成，framework或customFramework字段必须填写，不可留空
3. **框架选择优先级**：
   - 优先使用系统推荐的现有框架（从推荐信息中选择）
   - 如果用户指定了特定框架，使用用户指定的框架
   - 如果没有合适的现有框架，创建自定义框架模式
   - 绝对不允许framework和customFramework都为空

**智能工作流**：
1. **书名生成**：用户需要生成书名时，必须调用书名生成工具，从推荐框架中选择或创建自定义框架
2. **简介生成**：用户需要作品简介时，必须调用简介生成工具，从推荐框架中选择或创建自定义框架
3. **框架分析**：仅当用户明确要求分析现有书名的框架模式时使用，不要将用户提供的自定义框架当作分析对象
4. **世界观创建**：用户需要创建新世界观时，必须调用世界观创建工具，根据用户需求设置类型和复杂度
5. **世界观管理**：用户需要查看、选择或发送已有世界观时，必须调用世界观管理工具
6. **重要**：customFramework参数是用于指导生成的模板，不是需要分析的对象

**使用场景**：
- 用户明确要求生成书名或简介
- 讨论中需要具体的书名或简介示例
- 用户提供了关键词或框架信息
- 用户使用了新的框架模式需要先分析
- 用户需要基于现有内容生成配套的书名或简介
- 用户要求创建新的世界观设定
- 用户需要查看或管理项目中的世界观
- 用户想要发送世界观到AI对话中参考
- 讨论中涉及世界观构建和设定

**可用框架**：自定义模式（会自动分析保存）

**关键词推荐系统**：
- 基于项目数据自动推荐相关关键词
- 支持六大分类：人物、地点、概念、情感、动作、物品
- 智能匹配用户需求和项目背景
- 提供高频关键词和创新关键词建议

**纯净输出原则**：
- 直接提供创意内容，不要过多的解释性语言
- 重点突出实用的创作建议
- 避免空泛的理论阐述`;

    if (userRequirements) {
      systemPrompt += `\n\n**用户特殊要求**：\n${userRequirements}`;
    }

    return systemPrompt;
  }

  /**
   * 构建AI创意生成提示
   */
  static buildCreativityPrompt(params: BrainstormParams): any[] {
    const messageBuilder = new MessageBuilder();

    // 系统消息
    messageBuilder.addSystemMessage(this.getSystemPrompt(params.userRequirements));

    // 助手消息：确认理解任务
    messageBuilder.addAssistantMessage(
      '我将根据您的需求为您提供3个不同方向的创意方案，每个方案都包含具体的实施建议和发展思路。',
      true,
      true
    );

    // 添加项目数据上下文（如果有）
    if (params.associationData) {
      this.addProjectDataContext(messageBuilder, params.associationData);
    }

    // 构建主要生成指令
    let prompt = this.buildMainPrompt(params);

    // 用户消息：生成指令
    messageBuilder.addUserMessage(prompt);

    // JSON格式要求
    messageBuilder.addUserMessage(this.getJSONFormatRequirement());

    return messageBuilder.build();
  }

  /**
   * 添加项目数据上下文
   */
  private static addProjectDataContext(messageBuilder: MessageBuilder, data: AssociationData): void {
    if (data.characters.length > 0 || data.worldSettings.length > 0 || 
        data.glossary.length > 0 || data.outline.length > 0) {
      
      let contextMessage = '【项目数据参考】\n以下是当前项目的相关数据，可作为创意生成的参考：\n\n';

      if (data.characters.length > 0) {
        contextMessage += `**角色信息**（${data.characters.length}个）：\n`;
        data.characters.slice(0, 5).forEach(char => {
          contextMessage += `- ${char.name}：${char.description}\n`;
        });
        if (data.characters.length > 5) {
          contextMessage += `- 还有${data.characters.length - 5}个角色...\n`;
        }
        contextMessage += '\n';
      }

      if (data.worldSettings.length > 0) {
        contextMessage += `**世界观设定**（${data.worldSettings.length}个）：\n`;
        data.worldSettings.slice(0, 3).forEach(world => {
          contextMessage += `- ${world.name}：${world.description}\n`;
        });
        if (data.worldSettings.length > 3) {
          contextMessage += `- 还有${data.worldSettings.length - 3}个设定...\n`;
        }
        contextMessage += '\n';
      }

      if (data.glossary.length > 0) {
        contextMessage += `**术语词汇**（${data.glossary.length}个）：\n`;
        data.glossary.slice(0, 5).forEach(term => {
          contextMessage += `- ${term.term}：${term.definition}\n`;
        });
        if (data.glossary.length > 5) {
          contextMessage += `- 还有${data.glossary.length - 5}个术语...\n`;
        }
        contextMessage += '\n';
      }

      contextMessage += '请在生成创意时适当参考这些项目数据，但不要局限于此。';

      messageBuilder.addUserMessage(contextMessage, undefined, true, true);
      messageBuilder.addAssistantMessage(
        '我已理解项目数据背景，将在创意生成时适当融入这些元素，同时保持创新性和独特性。',
        true,
        true
      );
    }
  }

  /**
   * 构建主要提示指令
   */
  private static buildMainPrompt(params: BrainstormParams): string {
    let prompt = `请基于以下需求生成创意方案：\n\n`;
    prompt += `**用户需求**：${params.userInput}\n`;
    prompt += `**创意类型**：${this.getCreativityTypeDescription(params.creativityType)}\n\n`;

    prompt += `请提供3个不同方向的创意方案，每个方案都要：\n`;
    prompt += `1. 有明确的核心概念和亮点\n`;
    prompt += `2. 包含具体的实施建议\n`;
    prompt += `3. 说明市场潜力和读者吸引力\n`;
    prompt += `4. 给出进一步发展的思路\n\n`;

    prompt += `要求：\n`;
    prompt += `- 创意要新颖独特，避免常见套路\n`;
    prompt += `- 建议要具体可行，有实际指导价值\n`;
    prompt += `- 考虑当前市场趋势和读者喜好\n`;
    prompt += `- 保持内在逻辑的一致性`;

    return prompt;
  }

  /**
   * 获取创意类型描述
   */
  private static getCreativityTypeDescription(type: string): string {
    const descriptions: Record<string, string> = {
      'general': '通用创意（题材、剧情、设定等综合建议）',
      'plot': '剧情脑洞（情节发展、冲突设计、转折安排）',
      'character': '人物设定（角色背景、性格特征、成长轨迹）',
      'worldbuilding': '世界观构建（背景设定、规则体系、文化结构）',
      'theme': '题材构思（故事主题、创作方向、类型定位）'
    };
    return descriptions[type] || descriptions['general'];
  }

  /**
   * 获取JSON格式要求
   */
  static getJSONFormatRequirement(): string {
    return `请严格按照以下JSON格式返回结果：

{
  "solutions": [
    {
      "title": "方案标题",
      "concept": "核心概念描述",
      "highlights": ["亮点1", "亮点2", "亮点3"],
      "implementation": "具体实施建议",
      "marketPotential": "市场潜力分析",
      "development": "进一步发展思路",
      "score": 8.5
    }
  ],
  "summary": "整体创意总结",
  "recommendations": "推荐使用建议"
}

**格式要求**：
1. 必须返回3个创意方案
2. score为1-10的数字，保留一位小数
3. highlights要列出3个主要亮点
4. implementation要给出具体可行的建议
5. 只返回JSON，不要其他解释`;
  }

  /**
   * 构建对话提示（用于AI聊天功能）
   */
  static buildChatPrompt(userInput: string, associationData?: AssociationData): any[] {
    const messageBuilder = new MessageBuilder();

    // 使用完整的系统提示词，不简化
    messageBuilder.addSystemMessage(this.getSystemPrompt());

    // 添加项目数据上下文（如果有）
    if (associationData) {
      this.addProjectDataContext(messageBuilder, associationData);
    }

    // 用户输入
    messageBuilder.addUserMessage(userInput);

    return messageBuilder.build();
  }

  /**
   * 构建带推荐信息的对话提示
   */
  static buildChatPromptWithRecommendations(
    userInput: string,
    recommendations: string | null,
    associationData?: AssociationData
  ): any[] {
    const messageBuilder = new MessageBuilder();

    // 使用完整的系统提示词
    messageBuilder.addSystemMessage(this.getSystemPrompt());

    // 添加推荐信息（如果有）
    if (recommendations) {
      messageBuilder.addSystemMessage(`[系统资源推荐]\n${recommendations}`);
    }

    // 添加项目数据上下文（如果有）
    if (associationData) {
      this.addProjectDataContext(messageBuilder, associationData);
    }

    // 用户输入
    messageBuilder.addUserMessage(userInput);

    return messageBuilder.build();
  }
}
