/**
 * OutlineAIService模块化拆解 - 共享类型定义
 * 为各个模块提供统一的类型接口
 */

// 基础消息接口
export interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  isSystemGenerated?: boolean;
  isContextMessage?: boolean;
}

// 上下文链路接口
export interface ContextChain {
  hierarchy?: string;
  sequence?: string;
  relatedNodes?: string[];
  description?: string;
}

// 框架信息接口
export interface FrameworkInfo {
  frameworkPattern?: string;
  patternType?: string;
  frameworkVariables?: string[];
  description?: string;
  examples?: string[];
  // 新增：剧情分析字段
  plotAnalysis?: {
    storyStructure?: string;
    conflictDesign?: string;
    rhythmControl?: string;
    plotPoints?: string[];
    behaviorFrameworks?: string[];
    // 关键：plotPointsWithGuidance字段
    plotPointsWithGuidance?: Array<{
      content: string;              // 剧情点内容
      specificDescription: string;  // 该章节具体描写的内容
      avoidanceGuidance: string;    // 预测应该避免的描写
    }>;
  };
  // 新增：对话分析字段
  dialogueAnalysis?: {
    // 保留核心旧字段（标记为可选，向后兼容）
    dialogueStructure?: string;
    plotAdvancement?: string;
    writingTechniques?: string;
    toneCharacteristics?: string[];
    stylePatterns?: string[];
    literaryAnalysis?: string;

    // 新增：完整对话提取
    completeDialogues?: Array<{
      content: string;      // 对话内容
      speaker?: string;     // 说话人
      context: string;      // 上下文
      position: number;     // 位置
    }>;

    // 新增：风格分析
    styleAnalysis?: {
      dialogueStyle: string;        // 对话风格特征
      characterVoice: string;       // 角色语言特色
      emotionalTone: string;        // 情感基调
      technicalFeatures: string;   // 写作技巧特征
    };
  };
  // 新增：风格分析字段
  styleAnalysis?: {
    writingStyle?: string;
    expressionFeatures?: string;
    practicalMethods?: string;
    rhythmPatterns?: string[];
    pacingFramework?: string;
    outlineGuidance?: string;
  };
}

// 消息构建选项
export interface MessageBuildOptions {
  includeSystemPrompt?: boolean;
  includeExamples?: boolean;
  includeMaterialLibrary?: boolean;
  temperature?: number;
  maxTokens?: number;
}

// AI请求选项
export interface AIRequestOptions {
  temperature?: number;
  maxTokens?: number;
  onProgress?: (chunk: string) => void;
  bookId?: string;
  contextChains?: ContextChain[];
  selectedFramework?: FrameworkInfo;
  selectedFrameworks?: FrameworkInfo[];
}

// 大纲统计信息
export interface OutlineStats {
  totalNodes: number;
  chapters: number;
  scenes: number;
  sections: number;
  maxLevel: number;
}

// 节点查找结果
export interface NodeSearchResult {
  node: any;
  path: string;
  level: number;
}

// AI响应接口
export interface AIResponse {
  message: string;
  creativeNotes?: string;
  changes?: any[];
  metadata?: any;
  success: boolean;
  error?: string;
}

// 思考画布数据
export interface ThinkingCanvasData {
  id: string;
  title: string;
  content: string;
  mode: 'simple' | 'detailed' | 'custom';
  createdAt: Date;
}

// 思考画布选项
export interface ThinkingCanvasOptions {
  mode: 'simple' | 'detailed' | 'custom';
  userRequirements?: string;
  focusAreas?: string[];
}

// 扩展的AI响应接口
export interface OutlineAIResponseExtended extends AIResponse {
  thinkingCanvas?: ThinkingCanvasData;
  metadata?: {
    operationType: string;
    confidence: number;
    hasThinkingCanvas?: boolean;
    thinkingCanvasId?: string;
    workflowStep?: 'thinking' | 'generating' | 'completed';
    thinkingMode?: 'simple' | 'detailed' | 'custom';
  };
}

// 模块配置接口
export interface ModuleConfig {
  enabled: boolean;
  priority: number;
  timeout: number;
  retryCount: number;
  cacheEnabled: boolean;
  debugMode: boolean;
}

// 服务配置接口
export interface ServiceConfig {
  messageBuilder: ModuleConfig;
  frameworkInfoBuilder: ModuleConfig;
  contextProcessor: ModuleConfig;
  nodeAssociationHandler: ModuleConfig;
  aiRequestHandler: ModuleConfig;
}

// 服务状态接口
export interface ServiceStatus {
  isInitialized: boolean;
  activeModules: string[];
  lastError?: string;
  performance?: {
    averageResponseTime: number;
    totalRequests: number;
    successRate: number;
  };
}

// 大纲节点基础接口
export interface OutlineNode {
  id: string;
  title: string;
  type: 'volume' | 'event' | 'chapter' | 'scene' | 'section' | 'plot' | 'dialogue';
  content?: string;
  creativeNotes?: string;
  children?: OutlineNode[];
  parentId?: string;
  level?: number;
  order?: number;
}

// 双AI配置接口
export interface DualAIConfig {
  mode: 'single' | 'dual';
  outlineAI?: {
    enabled: boolean;
    model?: string;
    temperature?: number;
  };
  dialogueAI?: {
    enabled: boolean;
    model?: string;
    temperature?: number;
  };
}
