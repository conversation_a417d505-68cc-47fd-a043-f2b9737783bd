import { OutlineAIService } from './OutlineAIService';
import { DialogueAIService } from './DialogueAIService';
import { OutlineNodeType } from '../../types/outline';

/**
 * AI节点追加服务
 * 根据父节点类型智能生成对应的子节点
 */
export class AINodeAppender {
  private outlineAIService: OutlineAIService;
  private dialogueAIService: DialogueAIService;

  constructor() {
    this.outlineAIService = OutlineAIService.getInstance();
    this.dialogueAIService = new DialogueAIService();
  }

  /**
   * 根据父节点类型确定要生成的子节点类型
   */
  private getChildNodeType(parentNodeType: string): string | null {
    const nodeTypeMap: Record<string, string> = {
      'volume': 'chapter',    // 卷 → 章节（保留兼容性）
      'event': 'chapter',     // 事件刚 → 章节
      'chapter': 'plot',      // 章节 → 剧情点
      'plot': 'dialogue',     // 剧情点 → 对话
      'synopsis': 'plot'      // 🔥 修正：Synopsis → 剧情点（与模板内容一致）
    };

    return nodeTypeMap[parentNodeType] || 'plot'; // 默认生成剧情点而不是null
  }

  /**
   * 根据子节点类型获取对应的自定义需求
   */
  private getCustomRequirements(childType: string): string {
    const requirements: Record<string, string> = {
      'chapter': `章节创建需求=【
1. 章节结构完整，包含开头、发展、高潮、结尾
2. 情节推进有逻辑，与前后章节衔接自然
3. 场景描写生动，能够营造氛围
4. 人物行动符合性格，推动剧情发展
5. 字数控制在目标范围内
6. 章节标题吸引人且概括内容
】`,

      'plot': `剧情点创建需求=【
1. 剧情点具体明确，有清晰的起因、经过、结果
2. 冲突设置合理，能够推动故事发展
3. 人物动机清晰，行为逻辑合理
4. 包含具体的场景和环境描述
5. 避免"一丝xx"等模糊表达
6. 提供详细的写作指导和格式规范
】`,

      'dialogue': `对话创建需求=【
1. 对话20条以上，内容丰富有层次
2. 对话富有逻辑，每个人都有独特的表达形式
3. 具有口头禅，体现角色个性，但不要生搬硬套其他作
4. 对话要具有连贯性，不要过度跳脱剧情之外
5. 要返回对话数组格式，便于后续处理
6. 提供的句子学习，是指学习句子登习惯冲突表达，而不是重复人物使用他的对话，这样会被判定为套作
7. 具有相关节点，独有的元素特点，比如，各种元素等，比如金句，比如人生感悟，比如 其他的评论内容等等
】`,

      'synopsis': `Synopsis剧情点创建需求=【
1. 基于Synopsis的脑洞设定和开头设计生成剧情点
2. 🔥 连贯性核心：剧情点要承接开头的情节和悬念，确保情感和逻辑的自然过渡
3. 🔥 避免跳跃：如果从发现线索到采取行动，必须展现中间的思考、挣扎、决策过程
4. 🔥 情感驱动：展现角色的内心变化，从被动状态到主动状态的转变过程
5. 体现Synopsis的故事类型和风格特征
6. 包含具体的冲突和转折点，推动故事发展
7. 利用ACE框架的技巧和模式进行创作
8. 考虑结尾设计的方向，为结尾做铺垫
9. 避免"一丝xx"等模糊表达，要具体明确
10. 🔥 重要：建立开篇，继续开头内容，渲染中间剧情，确保每个步骤都有充分的铺垫
11. 🔥 重要：专注于Synopsis的核心脑洞，不要混入其他无关内容
12. 🔥 连贯性检查：确保剧情点之间有自然的因果关系，避免突兀的情节转换
】`
    };

    return requirements[childType] || `基础创建需求=【
1. 内容完整，逻辑清晰
2. 符合节点类型的特点和要求
3. 与父节点内容紧密相关
4. 质量优良，具有可读性
】`;
  }

  /**
   * 生成AI追加提示词
   */
  private generateAIPrompt(parentNode: OutlineNodeType, childType: string, customPrompt?: string, generateCount: number = 1): string {
    // 基础提示词模板
    const basePrompts: Record<string, (parent: OutlineNodeType) => string> = {
      'chapter': (parent) => {
        // 根据父节点类型生成不同的提示词
        if (parent.type === 'event') {
          return `基于事件刚"${parent.title}"的内容，为其智能生成${generateCount}个相关的章节。

事件刚的描述：${parent.description || '暂无描述'}
事件刚的创作要点：${parent.creativeNotes || '暂无要点'}
事件起始状态：${(parent as any).eventStart || '暂无起始状态'}
事件结束状态：${(parent as any).eventEnd || '暂无结束状态'}
事件触发条件：${(parent as any).eventTrigger || '暂无触发条件'}
事件影响范围：${(parent as any).eventScope || '暂无影响范围'}

请生成一个与此事件刚内容紧密相关的章节，确保：
1. 章节标题与事件刚的主题呼应
2. 章节内容推进事件的发展进程
3. 体现事件的触发条件和影响范围
4. 保持故事逻辑的连贯性
5. 利用已有的框架指导提升创作质量`;
        } else {
          return `基于卷"${parent.title}"的内容，为其智能生成${generateCount}个相关的章节。

卷的描述：${parent.description || '暂无描述'}
卷的创作要点：${parent.creativeNotes || '暂无要点'}

请生成一个与此卷内容紧密相关的章节，确保：
1. 章节标题与卷的主题呼应
2. 章节内容推进卷的整体剧情
3. 保持故事逻辑的连贯性
4. 利用已有的框架指导提升创作质量`;
        }
      },

      'plot': (parent) => `基于章节"${parent.title}"的内容，为其智能生成${generateCount}个相关的剧情点。

章节描述：${parent.description || '暂无描述'}
章节创作要点：${parent.creativeNotes || '暂无要点'}
章节风格：${(parent as any).chapterStyle || '暂无风格'}
章节目标：${(parent as any).chapterGoals || '暂无目标'}

🔥 连贯性核心要求：
1. 必须分析前一剧情点的结尾状态（情感、行动、环境），确保自然承接
2. 新剧情点必须从合理的起点开始，避免逻辑跳跃和突兀转换
3. 如果涉及重大决策或行动，必须展现完整的心理过程：发现→思考→挣扎→决定→行动
4. 确保情感驱动链条完整，展现角色从被动到主动的转变过程
5. 提供充分的铺垫和过渡，让每个情节发展都有合理的动机和背景

请生成一个与此章节内容紧密相关且逻辑连贯的剧情点，确保：
1. 剧情点推进章节的核心冲突，但要有充分的铺垫过程
2. 包含具体的行动和结果，以及导致行动的完整心理变化
3. 避免"一丝xx"等模糊表达，但要展现必要的情感过渡
4. 提供详细的avoidWriting 与shouldWriting，重点关注连贯性`,

      'dialogue': (parent) => `基于剧情点"${parent.title}"的内容，为其智能生成${generateCount}个相关的对话设计。

剧情点描述：${parent.description || '暂无描述'}
剧情点创作要点：${parent.creativeNotes || '暂无要点'}
剧情类型：${(parent as any).plotType || '暂无类型'}
相关角色：${(parent as any).relatedCharacters?.join('、') || '暂无角色'}

请生成与此剧情点紧密相关的对话设计，确保：
1. 对话推进剧情点的核心冲突
2. 体现角色的性格特征
3. 包含具体的对话内容
4. 符合聊天风格的表达方式`,

      'synopsis': (parent) => `基于Synopsis"${parent.title}"的内容，为其智能生成${generateCount}个相关的剧情点。

Synopsis脑洞设定：${(parent as any).synopsisBrainhole || '暂无脑洞设定'}
Synopsis故事类型：${(parent as any).synopsisGenre || '暂无类型'}
Synopsis开头设计：${(parent as any).synopsisOpening || '暂无开头设计'} 🔥
Synopsis核心梗概：${(parent as any).synopsisCoreOutline || '暂无梗概'}
Synopsis结尾设计：${(parent as any).synopsisEnding || '暂无结尾设计'} 🔥
Synopsis故事描述：${(parent as any).synopsisStoryDescription || '暂无描述'}
Synopsis ACE引用：${(parent as any).synopsisAceReferences || '暂无ACE引用'}

🔥 连贯性核心要求：
1. 必须仔细分析开头设计的结尾状态，确保剧情点从合理的起点开始
2. 如果开头涉及发现线索或遭遇事件，剧情点必须展现角色的完整反应过程
3. 避免直接跳跃到行动结果，必须展现思考、挣扎、决策的心理过程
4. 确保情感驱动链条完整：冲击→思考→挣扎→觉醒→决心→行动
5. 每个重大转折都要有充分的铺垫和合理的动机

请生成与此Synopsis内容紧密相关且逻辑连贯的剧情点，确保：
1. 剧情点体现Synopsis的核心脑洞和创意，但要有完整的发展过程
2. 推进Synopsis描述的故事发展，从开头设计向结尾设计自然过渡
3. 符合Synopsis设定的故事类型和风格，保持情感基调的一致性
4. 包含具体的冲突和转折点，但要展现导致冲突的完整背景
5. 利用ACE框架的技巧和模式，重点关注角色的成长弧线
6. 🔥 重要：如果有开头设计，生成的剧情点应该承接开头的情节和悬念，展现角色的心理变化
7. 🔥 重要：考虑结尾设计的方向，确保剧情点为结尾做铺垫，但要有渐进的发展过程
8. 🔥 连贯性检查：确保剧情点与开头设计之间有自然的因果关系和情感连接`
    };

    // 🔥 修复：当父节点是synopsis时，使用synopsis的专门模板来生成剧情点
    const actualPromptType = (parentNode.type === 'synopsis') ? 'synopsis' : childType;
    const actualRequirementType = (parentNode.type === 'synopsis') ? 'synopsis' : childType;

    // 获取基础提示词
    const basePrompt = basePrompts[actualPromptType]?.(parentNode) || `为节点"${parentNode.title}"生成${childType}类型的子节点`;

    // 如果有自定义提示词，则结合使用
    if (customPrompt && customPrompt.trim()) {
      // 根据实际需求类型获取对应的基础需求
      const customRequirements = this.getCustomRequirements(actualRequirementType);
      return `${basePrompt}

**必须要求：**
${customPrompt.trim()}

请在满足基础要求的同时，特别关注必须要求，确保生成的内容符合朋友的期望。

${customRequirements}
重点：

必须完整按照规范的JSON示例格式返回，不要私自改变结构，导致解析失败


`;
    }

    return basePrompt;
  }

  /**
   * 收集相关节点信息，构建完整的上下文
   */
  private collectRelatedNodes(parentNode: OutlineNodeType, outline: any): string[] {
    const relatedNodeIds: string[] = [];

    // 添加父节点
    relatedNodeIds.push(parentNode.id);

    // 递归查找所有节点
    const findAllNodes = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      let allNodes: OutlineNodeType[] = [];
      for (const node of nodes) {
        allNodes.push(node);
        if (node.children && node.children.length > 0) {
          allNodes = allNodes.concat(findAllNodes(node.children));
        }
      }
      return allNodes;
    };

    const allNodes = findAllNodes(outline.nodes || []);

    // 查找父节点的父节点（祖父节点）
    const findParentNode = (nodeId: string): OutlineNodeType | null => {
      for (const node of allNodes) {
        if (node.children) {
          for (const child of node.children) {
            if (child.id === nodeId) {
              return node;
            }
          }
        }
      }
      return null;
    };

    const grandParentNode = findParentNode(parentNode.id);
    if (grandParentNode) {
      relatedNodeIds.push(grandParentNode.id);
    }

    // 查找父节点的兄弟节点（同级节点）
    if (grandParentNode && grandParentNode.children) {
      for (const sibling of grandParentNode.children) {
        if (sibling.id !== parentNode.id) {
          relatedNodeIds.push(sibling.id);
        }
      }
    }

    // 🔥 新增：递归收集所有子节点及其子节点（深度递归展开）
    const collectAllChildren = (node: OutlineNodeType, depth: number = 0, maxDepth: number = 5): string[] => {
      const childIds: string[] = [];

      // 防止无限递归，设置最大深度限制
      if (depth >= maxDepth) {
        return childIds;
      }

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          childIds.push(child.id);
          // 递归收集子节点的子节点
          const grandChildren = collectAllChildren(child, depth + 1, maxDepth);
          childIds.push(...grandChildren);
        }
      }
      return childIds;
    };

    // 收集父节点的所有子节点（包括子节点的子节点）
    const allChildrenIds = collectAllChildren(parentNode);
    relatedNodeIds.push(...allChildrenIds);

    // 去重处理
    const uniqueRelatedNodeIds = [...new Set(relatedNodeIds)];



    return uniqueRelatedNodeIds;
  }

  /**
   * 计算节点的层级深度（用于调试）
   */
  private calculateNodeDepth(node: OutlineNodeType): number {
    if (!node.children || node.children.length === 0) {
      return 0;
    }

    let maxDepth = 0;
    for (const child of node.children) {
      const childDepth = this.calculateNodeDepth(child);
      maxDepth = Math.max(maxDepth, childDepth + 1);
    }

    return maxDepth;
  }

  /**
   * 收集相关章节内容，构建完整的剧情上下文
   */
  private async collectChapterContent(parentNode: OutlineNodeType, outline: any, bookId?: string): Promise<string[]> {
    if (!bookId) {
      return [];
    }

    try {
      // 查找相关的章节节点
      const chapterIds: string[] = [];

      // 如果父节点是章节，直接添加
      if (parentNode.type === 'chapter') {
        chapterIds.push(parentNode.id);
      }

      // 如果父节点是剧情点，查找其所属章节
      if (parentNode.type === 'plot') {
        const findAllNodes = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
          let allNodes: OutlineNodeType[] = [];
          for (const node of nodes) {
            allNodes.push(node);
            if (node.children && node.children.length > 0) {
              allNodes = allNodes.concat(findAllNodes(node.children));
            }
          }
          return allNodes;
        };

        const allNodes = findAllNodes(outline.nodes || []);

        // 查找父节点的父章节
        for (const node of allNodes) {
          if (node.type === 'chapter' && node.children) {
            for (const child of node.children) {
              if (child.id === parentNode.id) {
                chapterIds.push(node.id);
                break;
              }
            }
          }
        }
      }

      // 🔥 新增：如果父节点是卷或事件刚，查找其下的所有章节
      if (parentNode.type === 'volume' || parentNode.type === 'event') {
        if (parentNode.children && parentNode.children.length > 0) {
          for (const child of parentNode.children) {
            if (child.type === 'chapter') {
              chapterIds.push(child.id);
            }
          }
        }
      }

      if (chapterIds.length === 0) {
        return [];
      }

      // 从数据库加载章节内容
      const { aiAssistantDataService } = await import('@/services/aiAssistantDataService');
      const { AIAssistantContextType } = await import('@/lib/db/dexie');

      const chapterResults = await aiAssistantDataService.searchMentionItems(
        bookId, '', [AIAssistantContextType.CHAPTER], 100
      );

      const selectedChapters = chapterResults.filter(item =>
        chapterIds.includes(item.id)
      );

      return selectedChapters.map(chapter => chapter.id);

    } catch (error) {
      return [];
    }
  }

  /**
   * 执行AI追加操作
   */
  async appendChildNode(
    parentNode: OutlineNodeType,
    outline: any,
    onProgress?: (chunk: string) => void,
    options?: {
      bookId?: string;
      selectedFramework?: any;
      selectedFrameworks?: any[];
      customPrompt?: string;
      generateCount?: number;
      selectedChapterIds?: string[]; // 🔥 新增：AI助手中用户选择的章节ID
    }
  ): Promise<{
    success: boolean;
    changes?: any[];
    message?: string;
    error?: string;
  }> {
    try {
      // 确定要生成的子节点类型
      const childType = this.getChildNodeType(parentNode.type);
      if (!childType) {
        return {
          success: false,
          error: `节点类型"${parentNode.type}"不支持AI追加功能`
        };
      }

      // 生成AI提示词
      const generateCount = options?.generateCount || 1;
      const prompt = this.generateAIPrompt(parentNode, childType, options?.customPrompt, generateCount);

      // 收集相关节点信息，构建完整的上下文
      const relatedNodeIds = this.collectRelatedNodes(parentNode, outline);

      // 🔥 优先使用AI助手中用户选择的章节，如果没有则使用自动推断的章节
      let chapterIds: string[] = [];

      if (options?.selectedChapterIds && options.selectedChapterIds.length > 0) {
        // 使用AI助手中用户选择的章节
        chapterIds = options.selectedChapterIds;

      } else {
        // 回退到自动推断章节内容
        chapterIds = await this.collectChapterContent(parentNode, outline, options?.bookId);

      }

      // 合并节点ID和章节ID，构建完整的上下文信息
      const allContextIds = [...relatedNodeIds, ...chapterIds];



      // 根据子节点类型选择对应的AI服务
      if (childType === 'dialogue') {
        // 使用对话AI服务（已经有完整的ACE框架支持）
        const response = await this.dialogueAIService.createDialogueNode(
          prompt,
          allContextIds, // 传递完整的上下文信息（节点+章节）
          outline,
          onProgress,
          {
            bookId: options?.bookId,
            selectedFramework: options?.selectedFramework,
            selectedFrameworks: options?.selectedFrameworks
          }
        );

        return response;
      } else {
        // 使用大纲AI服务的完整ACE框架方法
        const response = await this.outlineAIService.sendStreamingRequest(
          prompt,
          allContextIds, // 传递完整的上下文信息（节点+章节）
          outline,
          onProgress || (() => {}),
          {
            bookId: options?.bookId,
            selectedFramework: options?.selectedFramework,
            selectedFrameworks: options?.selectedFrameworks
          }
        );

        return {
          success: response.success,
          changes: response.changes,
          message: response.message,
          error: response.error
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'AI追加节点时发生未知错误'
      };
    }
  }

  /**
   * 检查节点是否支持AI追加
   */
  canAppendChild(nodeType: string): boolean {
    return this.getChildNodeType(nodeType) !== null;
  }

  /**
   * 获取AI追加的描述文本
   */
  getAppendDescription(nodeType: string): string {
    const descriptions: Record<string, string> = {
      'volume': 'AI追加章节',
      'event': 'AI追加章节',
      'chapter': 'AI追加剧情点',
      'plot': 'AI追加对话',
      'synopsis': 'AI追加剧情点'  // 🔥 修正：Synopsis追加剧情点，与映射保持一致
    };

    return descriptions[nodeType] || 'AI追加';
  }
}

// 导出单例实例
export const aiNodeAppender = new AINodeAppender();
