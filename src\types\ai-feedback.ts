/**
 * AI反馈系统相关类型定义
 */

export type FeedbackRating = 'good' | 'average' | 'poor';

export interface MessageFeedback {
  id: string;
  messageId: string;
  sessionId: string;
  phase: string;
  rating: FeedbackRating;
  comment?: string; // 用户评论说明
  timestamp: Date;
  userContext: {
    userMessage: string;
    aiResponse: string;
    conversationLength: number;
    previousMessages: Array<{
      type: 'user' | 'ai';
      content: string;
      timestamp: Date;
    }>;
  };
  systemContext: {
    systemPrompt: string;
    personaConfig?: string;
    responseTime?: number;
    contentLength: number;
  };
}

export interface FeedbackAnalysis {
  phase: string;
  totalFeedbacks: number;
  ratingDistribution: {
    good: number;
    average: number;
    poor: number;
  };
  averageScore: number;
  commonPatterns: {
    poorPerformance: {
      userInputPatterns: string[];
      contextPatterns: string[];
      responsePatterns: string[];
    };
    goodPerformance: {
      userInputPatterns: string[];
      contextPatterns: string[];
      responsePatterns: string[];
    };
  };
  recommendations: string[];
}

export interface FeedbackStats {
  totalMessages: number;
  ratedMessages: number;
  ratingPercentage: number;
  phaseStats: Record<string, {
    total: number;
    good: number;
    average: number;
    poor: number;
    averageScore: number;
  }>;
  recentTrends: {
    last7Days: number;
    last30Days: number;
    improvement: boolean;
  };
}
