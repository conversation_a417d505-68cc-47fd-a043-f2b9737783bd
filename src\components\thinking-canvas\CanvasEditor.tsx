"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useThinkingCanvas } from '@/contexts/ThinkingCanvasContext';

// 防抖Hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// 编辑器工具栏
const EditorToolbar: React.FC<{
  editMode: 'edit' | 'preview';
  onModeChange: (mode: 'edit' | 'preview') => void;
  wordCount: number;
  isSaving: boolean;
}> = ({ editMode, onModeChange, wordCount, isSaving }) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <h2 className="font-medium text-gray-900">编辑器</h2>
        
        <div className="flex items-center space-x-1 text-xs text-gray-500">
          <span>{wordCount} 字</span>
          {isSaving && (
            <>
              <span>•</span>
              <span className="text-blue-600">保存中...</span>
            </>
          )}
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        {/* 编辑/预览切换 */}
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => onModeChange('edit')}
            className={`px-3 py-1 text-sm rounded-md transition-all ${
              editMode === 'edit'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            编辑
          </button>
          <button
            onClick={() => onModeChange('preview')}
            className={`px-3 py-1 text-sm rounded-md transition-all ${
              editMode === 'preview'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            预览
          </button>
        </div>
      </div>
    </div>
  );
};

// Markdown预览组件
const MarkdownPreview: React.FC<{ content: string }> = ({ content }) => {
  // 简单的Markdown渲染（可以后续集成react-markdown）
  const renderMarkdown = (text: string) => {
    return text
      .split('\n')
      .map((line, index) => {
        // 标题
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-2xl font-bold mb-4 text-gray-900">{line.slice(2)}</h1>;
        }
        if (line.startsWith('## ')) {
          return <h2 key={index} className="text-xl font-semibold mb-3 text-gray-900">{line.slice(3)}</h2>;
        }
        if (line.startsWith('### ')) {
          return <h3 key={index} className="text-lg font-medium mb-2 text-gray-900">{line.slice(4)}</h3>;
        }
        
        // 空行
        if (line.trim() === '') {
          return <br key={index} />;
        }
        
        // 普通段落
        return <p key={index} className="mb-2 text-gray-700 leading-relaxed">{line}</p>;
      });
  };
  
  return (
    <div className="prose max-w-none p-6">
      {content.trim() ? (
        renderMarkdown(content)
      ) : (
        <div className="text-center text-gray-500 py-12">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>暂无内容</p>
          <p className="text-sm mt-1">切换到编辑模式开始创作</p>
        </div>
      )}
    </div>
  );
};

// 主编辑器组件
const CanvasEditor: React.FC = () => {
  const { currentCanvas, updateCanvas, editMode, setEditMode, isSaving } = useThinkingCanvas();
  const [content, setContent] = useState('');
  const [localIsSaving, setLocalIsSaving] = useState(false);
  
  // 防抖内容，用于自动保存
  const debouncedContent = useDebounce(content, 1000);
  
  // 字数统计
  const wordCount = useMemo(() => {
    return content.length;
  }, [content]);
  
  // 当切换画布时，更新本地内容
  useEffect(() => {
    if (currentCanvas) {
      setContent(currentCanvas.content);
    } else {
      setContent('');
    }
  }, [currentCanvas?.id]);
  
  // 自动保存
  useEffect(() => {
    if (currentCanvas && debouncedContent !== currentCanvas.content) {
      setLocalIsSaving(true);
      updateCanvas(currentCanvas.id, { content: debouncedContent });
      
      // 模拟保存延迟
      setTimeout(() => {
        setLocalIsSaving(false);
      }, 500);
    }
  }, [debouncedContent, currentCanvas, updateCanvas]);
  
  // 处理内容变化
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
  }, []);
  
  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + S 保存
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (currentCanvas) {
          updateCanvas(currentCanvas.id, { content });
        }
      }
      
      // Ctrl/Cmd + E 切换编辑/预览模式
      if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        setEditMode(editMode === 'edit' ? 'preview' : 'edit');
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [editMode, setEditMode, content, currentCanvas, updateCanvas]);
  
  if (!currentCanvas) {
    return (
      <div className="flex-1 flex flex-col bg-white">
        <div className="h-12 border-b border-gray-200 flex items-center px-4">
          <h2 className="font-medium text-gray-500">选择一个画布</h2>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <svg className="w-20 h-20 mx-auto mb-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-lg font-medium mb-2">开始你的思考之旅</h3>
            <p className="text-sm text-gray-400 max-w-sm">
              从左侧选择一个现有画布，或者创建一个新的画布开始编辑
            </p>
            
            <div className="mt-6 text-xs text-gray-400 space-y-1">
              <p>💡 支持 Markdown 语法</p>
              <p>⌨️ Ctrl+S 保存，Ctrl+E 切换预览</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* 工具栏 */}
      <div className="h-12 border-b border-gray-200 flex items-center px-4">
        <EditorToolbar
          editMode={editMode}
          onModeChange={setEditMode}
          wordCount={wordCount}
          isSaving={isSaving || localIsSaving}
        />
      </div>
      
      {/* 画布标题 */}
      <div className="px-4 py-3 border-b border-gray-100 bg-gray-50">
        <h1 className="text-lg font-semibold text-gray-900">{currentCanvas.title}</h1>
        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
          <span>创建于 {new Date(currentCanvas.createdAt).toLocaleDateString()}</span>
          <span>•</span>
          <span>更新于 {new Date(currentCanvas.updatedAt).toLocaleDateString()}</span>
          {currentCanvas.tags.length > 0 && (
            <>
              <span>•</span>
              <div className="flex space-x-1">
                {currentCanvas.tags.map((tag, index) => (
                  <span key={index} className="px-1.5 py-0.5 bg-gray-200 text-gray-600 rounded text-xs">
                    {tag}
                  </span>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* 编辑器主体 */}
      <div className="flex-1 relative overflow-hidden">
        {editMode === 'edit' ? (
          <textarea
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            className="w-full h-full p-6 resize-none border-none outline-none text-gray-900 leading-relaxed"
            placeholder="开始你的思考...

支持 Markdown 语法：
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*

- 列表项
- 列表项

快捷键：
Ctrl+S 保存
Ctrl+E 切换预览模式"
            style={{ fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace' }}
          />
        ) : (
          <div className="h-full overflow-y-auto">
            <MarkdownPreview content={content} />
          </div>
        )}
      </div>
      
      {/* 状态栏 */}
      <div className="h-8 border-t border-gray-200 bg-gray-50 flex items-center justify-between px-4 text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>{wordCount} 字符</span>
          <span>•</span>
          <span>行 {content.split('\n').length}</span>
        </div>
        
        <div className="flex items-center space-x-4">
          {(isSaving || localIsSaving) && (
            <div className="flex items-center space-x-1 text-blue-600">
              <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span>保存中</span>
            </div>
          )}
          
          <span>Markdown 模式</span>
        </div>
      </div>
    </div>
  );
};

export default CanvasEditor;
