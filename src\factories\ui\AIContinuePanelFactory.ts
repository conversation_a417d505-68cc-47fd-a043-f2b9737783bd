import React from 'react';
import { AIContinueDialog, AIContinueButton } from './components/AIContinuePanel';

/**
 * AI续写面板工厂类
 * 用于创建AI续写相关的组件
 */
export class AIContinuePanelFactory {
  /**
   * 创建AI续写对话框组件
   * @param props 组件属性
   * @returns AI续写对话框组件
   */
  createAIContinueDialog(props: {
    isOpen: boolean;
    onClose: () => void;
    onInsertContent: (content: string) => void;
    initialContext?: string;
    bookId?: string;
    currentChapterId?: string; // 当前章节ID，用于禁用当前章节的选择
  }) {
    return React.createElement(AIContinueDialog, props);
  }

  /**
   * 创建AI续写按钮组件
   * @param props 组件属性
   * @returns AI续写按钮组件
   */
  createAIContinueButton(props: {
    context?: string;
    bookId?: string;
    onInsertContent: (content: string) => void;
    buttonText?: string;
    buttonClassName?: string;
    buttonIcon?: React.ReactNode;
    buttonSize?: 'small' | 'medium' | 'large';
    buttonType?: 'primary' | 'secondary' | 'outline' | 'text';
  }) {
    return React.createElement(AIContinueButton, props);
  }
}

/**
 * 创建AI续写面板工厂实例
 * @returns AI续写面板工厂实例
 */
export function createAIContinuePanelFactory() {
  return new AIContinuePanelFactory();
}

export default createAIContinuePanelFactory;
