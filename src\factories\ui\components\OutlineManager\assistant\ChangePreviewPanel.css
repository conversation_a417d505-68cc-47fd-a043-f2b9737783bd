/* 变更预览面板样式 */
.change-preview-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
}

/* 头部 */
.preview-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f5f5f5;
  color: #666;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.back-button:hover {
  background: #e0e0e0;
  color: #333;
}

.preview-title h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.preview-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

/* 统计信息 */
.preview-stats {
  display: flex;
  gap: 16px;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 内容区域 */
.preview-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.changes-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.select-all-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 变更列表 */
.changes-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.change-item {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
  transition: all 0.2s ease;
}

.change-item:hover {
  background: #f8f9fa;
}

.change-item.selected {
  background: #e8f4fd;
  border-left: 4px solid #1976d2;
}

.change-checkbox {
  display: flex;
  align-items: flex-start;
  padding-top: 2px;
  cursor: pointer;
}

.change-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.change-content {
  flex: 1;
  min-width: 0;
}

.change-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.change-type-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.change-target {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  
  /* 文本截断 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.change-details {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.change-detail {
  margin-bottom: 4px;
}

.change-detail strong {
  color: #333;
}

.delete-warning {
  color: #f44336;
  font-weight: 500;
}

/* 操作按钮 */
.preview-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  background: white;
}

.cancel-button,
.apply-button {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: #f5f5f5;
  color: #666;
}

.cancel-button:hover {
  background: #e0e0e0;
  color: #333;
}

.apply-button {
  background: #1976d2;
  color: white;
}

.apply-button:hover:not(:disabled) {
  background: #1565c0;
}

.apply-button:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
}

/* 滚动条样式 */
.changes-list::-webkit-scrollbar {
  width: 6px;
}

.changes-list::-webkit-scrollbar-track {
  background: transparent;
}

.changes-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.changes-list::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-header {
    padding: 12px 16px;
  }
  
  .preview-stats {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .changes-header {
    padding: 12px 16px;
  }
  
  .change-item {
    padding: 12px 16px;
  }
  
  .preview-actions {
    padding: 12px 16px;
    flex-direction: column;
  }
  
  .cancel-button,
  .apply-button {
    flex: none;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .change-preview-panel {
    background: #1e1e1e;
  }
  
  .preview-header,
  .preview-stats,
  .changes-header,
  .change-item,
  .preview-actions {
    background: #2d2d2d;
    border-color: #444;
  }
  
  .preview-title h3,
  .stats-value,
  .select-all-checkbox,
  .change-target {
    color: #fff;
  }
  
  .preview-title p,
  .stats-label,
  .change-details {
    color: #ccc;
  }
  
  .back-button {
    background: #444;
    color: #ccc;
  }
  
  .back-button:hover {
    background: #555;
    color: #fff;
  }
  
  .change-item:hover {
    background: #3a3a3a;
  }
  
  .change-item.selected {
    background: #1e3a5f;
    border-left-color: #64b5f6;
  }
  
  .cancel-button {
    background: #444;
    color: #ccc;
  }
  
  .cancel-button:hover {
    background: #555;
    color: #fff;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .change-item {
    border: 1px solid #ccc;
  }
  
  .change-item.selected {
    border: 2px solid #1976d2;
  }
  
  .change-type-badge {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .change-item,
  .back-button,
  .cancel-button,
  .apply-button {
    transition: none;
  }
}
