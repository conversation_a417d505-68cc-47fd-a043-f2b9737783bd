"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/adapters/ui';
import { BookImportExport } from '@/utils/bookImportExport';
import { BookRepository } from '@/lib/db/repositories/bookRepository';
import { chapterRepository } from '@/lib/db/repositories';
import { createAnimationFactory } from '@/factories/animation';

interface ImportBookDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
  onImportComplete?: () => void;
}

/**
 * 导入书籍对话框组件
 */
const ImportBookDialog: React.FC<ImportBookDialogProps> = ({
  isOpen = false,
  onClose,
  onImportComplete
}) => {
  const [isImporting, setIsImporting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importStatus, setImportStatus] = useState<string>('');
  const [importProgress, setImportProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 重置状态
  useEffect(() => {
    if (!isOpen) {
      setSelectedFile(null);
      setImportStatus('');
      setImportProgress(0);
    }
  }, [isOpen]);

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      // 检查文件类型
      if (file.name.endsWith('.md') || file.name.endsWith('.txt')) {
        setSelectedFile(file);
        setImportStatus(`已选择文件: ${file.name}`);
      } else {
        setImportStatus('错误: 仅支持 .md 和 .txt 格式的文件');
        setSelectedFile(null);
      }
    }
  };

  // 处理导入按钮点击
  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理导入确认
  const handleConfirmImport = async () => {
    if (!selectedFile) {
      setImportStatus('请先选择文件');
      return;
    }

    setIsImporting(true);
    setImportStatus('正在导入...');
    setImportProgress(10);

    try {
      // 读取文件内容
      const content = await readFileContent(selectedFile);
      setImportProgress(30);

      // 解析文件内容
      const { title, description, chapters } = BookImportExport.parseImportedContent(
        content,
        selectedFile.name
      );
      setImportProgress(50);

      // 创建新书籍
      const bookRepository = new BookRepository();

      const newBook = await bookRepository.createBook(title, description);
      setImportProgress(70);

      // 导入前检查是否有冲突章节
      const existingChapters = await chapterRepository.getAllByBookId(newBook.id!);
      if (existingChapters.length > 0) {
        console.warn('⚠️ 新创建的书籍已有章节，可能存在数据问题:', {
          bookId: newBook.id,
          existingCount: existingChapters.length,
          existingChapters: existingChapters.map(ch => ({ id: ch.id, title: ch.title, order: ch.order }))
        });

        // 清理可能的冲突章节
        for (const existingChapter of existingChapters) {
          await chapterRepository.delete(existingChapter.id!);
          console.log('🗑️ 清理冲突章节:', { id: existingChapter.id, title: existingChapter.title });
        }
      }

      console.log('📚 开始导入章节:', {
        bookId: newBook.id,
        chapterCount: chapters.length,
        bookTitle: title
      });

      // 创建章节 - 使用动态order值
      for (let i = 0; i < chapters.length; i++) {
        const chapter = chapters[i];

        // 获取正确的order值
        const currentOrder = await chapterRepository.getNextOrder(newBook.id!);

        console.log('📝 准备创建章节:', {
          index: i,
          title: chapter.title,
          order: currentOrder, // 使用动态order值
          contentLength: chapter.content?.length || 0,
          contentPreview: chapter.content?.substring(0, 100) || '无内容'
        });

        const chapterId = await chapterRepository.create({
          bookId: newBook.id!,
          title: chapter.title,
          content: chapter.content || '', // 确保content不为undefined
          order: currentOrder, // 使用动态获取的order值
          wordCount: 0, // 将由Repository自动计算
          characterIds: [],
          terminologyIds: [],
          worldBuildingIds: []
        });

        // 验证创建结果
        const verifyChapter = await chapterRepository.getById(chapterId);
        console.log('✅ 章节创建验证:', {
          id: chapterId,
          title: verifyChapter?.title,
          order: verifyChapter?.order,
          hasContent: !!verifyChapter?.content,
          contentLength: verifyChapter?.content?.length || 0
        });

        if (!verifyChapter?.content) {
          console.error('❌ 章节内容丢失:', { chapterId, title: chapter.title, order: currentOrder });
          throw new Error(`章节"${chapter.title}"内容导入失败`);
        }

        // 更新进度
        setImportProgress(70 + Math.floor((i + 1) / chapters.length * 30));
      }

      // 导入完成后特别验证第一章
      console.log('🔍 验证导入结果...');
      const allChapters = await chapterRepository.getAllByBookId(newBook.id!);
      const sortedChapters = allChapters.sort((a, b) => a.order - b.order);

      if (sortedChapters.length === 0) {
        throw new Error('导入失败：没有创建任何章节');
      }

      const firstChapter = sortedChapters[0];
      if (!firstChapter.content || firstChapter.content.trim() === '') {
        console.error('❌ 第一章验证失败:', {
          id: firstChapter.id,
          title: firstChapter.title,
          order: firstChapter.order,
          contentLength: firstChapter.content?.length || 0
        });
        throw new Error(`第一章"${firstChapter.title}"内容为空，导入失败`);
      }

      console.log('✅ 第一章验证通过:', {
        id: firstChapter.id,
        title: firstChapter.title,
        order: firstChapter.order,
        contentLength: firstChapter.content.length
      });

      console.log('📊 导入统计:', {
        totalChapters: sortedChapters.length,
        chaptersWithContent: sortedChapters.filter(ch => ch.content && ch.content.trim()).length,
        emptyChapters: sortedChapters.filter(ch => !ch.content || !ch.content.trim()).length
      });

      setImportStatus('导入成功!');
      setImportProgress(100);

      // 通知导入完成
      if (onImportComplete) {
        setTimeout(() => {
          onImportComplete();
        }, 1000);
      }
    } catch (error) {
      console.error('导入失败', error);
      setImportStatus(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsImporting(false);
    }
  };

  // 读取文件内容
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('读取文件失败'));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  // 创建动画工厂
  const animationFactory = createAnimationFactory();
  const fadeAnimation = animationFactory.createFadeAnimation('none', 300, 0, true);
  const scaleAnimation = animationFactory.createScaleAnimation(0.9, 1, 300, 0, true);

  // 获取动画样式
  const fadeStyle = fadeAnimation.getStyle();
  const scaleStyle = scaleAnimation.getStyle();

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-opacity duration-300"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        ...fadeStyle
      }}
      onClick={() => onClose && onClose()}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden"
        style={{
          ...scaleStyle
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 对话框标题 */}
        <div className="p-5 border-b flex justify-between items-center" style={{ backgroundColor: 'var(--color-primary-bg)' }}>
          <h2 className="text-xl font-bold" style={{ color: 'var(--color-primary)' }}>导入作品</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={() => onClose && onClose()}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-5">
          <p className="text-sm mb-4" style={{ color: 'var(--color-text-secondary)' }}>
            支持导入 Markdown (.md) 和文本 (.txt) 格式的文件。
            系统会自动识别"第X章"、数字编号（如001、002）等章节格式。
          </p>

          <div className="flex items-center justify-center w-full">
            <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-50"
              style={{ borderColor: 'var(--color-secondary)', backgroundColor: 'var(--color-primary-bg)' }}>
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <svg className="w-8 h-8 mb-4" style={{ color: 'var(--color-secondary)' }} aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                </svg>
                <p className="mb-2 text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                  <span className="font-semibold">点击选择文件</span> 或拖放文件
                </p>
                <p className="text-xs" style={{ color: 'var(--color-text-hint)' }}>支持 MD, TXT 格式</p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".md,.txt"
                onChange={handleFileChange}
                disabled={isImporting}
              />
            </label>
          </div>

          {importStatus && (
            <div className="mt-4">
              <p className={`text-sm ${importStatus.includes('错误') ? 'text-red-500' : ''}`}
                style={{ color: importStatus.includes('错误') ? 'red' : 'var(--color-text-secondary)' }}>
                {importStatus}
              </p>

              {isImporting && (
                <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                  <div
                    className="h-2.5 rounded-full"
                    style={{
                      width: `${importProgress}%`,
                      backgroundColor: 'var(--color-primary)'
                    }}
                  ></div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-5 border-t flex justify-between items-center" style={{ backgroundColor: 'var(--color-primary-bg)' }}>
          <Button
            text="取消"
            type="secondary"
            onClick={onClose}
            disabled={isImporting}
          />
          <Button
            text={selectedFile ? "确认导入" : "选择文件"}
            type="primary"
            onClick={selectedFile ? handleConfirmImport : handleImportClick}
            disabled={isImporting}
          />
        </div>
      </div>
    </div>
  );
};

export default ImportBookDialog;
