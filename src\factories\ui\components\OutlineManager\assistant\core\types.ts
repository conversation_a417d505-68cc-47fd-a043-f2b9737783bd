/**
 * OutlineAI服务核心类型定义
 * 统一管理所有模块间的接口和类型
 */

import { OutlineNodeType } from '../../../../types/outline';
import { ThinkingCanvasData } from '@/types/thinking-canvas';

/**
 * 扩展的AI响应接口，支持思考画布
 */
export interface OutlineAIResponseExtended {
  message: string;
  changes?: any[];
  metadata?: {
    operationType: string;
    confidence: number;
    hasThinkingCanvas?: boolean;
    thinkingCanvasId?: string;
    workflowStep?: 'thinking' | 'generating' | 'completed';
    thinkingMode?: 'simple' | 'detailed' | 'custom';
  };
  thinkingCanvas?: ThinkingCanvasData;
  success: boolean;
  error?: string;
}

/**
 * 思考画布生成选项
 */
export interface ThinkingCanvasOptions {
  mode: 'simple' | 'detailed' | 'custom';
  userRequirements?: string;
  focusAreas?: string[];
}

/**
 * 消息构建选项
 */
export interface MessageBuildOptions {
  isDualAIMode?: boolean;
  includeRhythmAnalysis?: boolean;
  includeMaterialLibrary?: boolean;
  selectedFramework?: any;
  selectedFrameworks?: any[];
  frameworkReuse?: any;
  referenceFramework?: any;
}

/**
 * AI请求处理选项
 */
export interface AIRequestOptions {
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  timeout?: number;
  retryCount?: number;
  selectedFramework?: any;
  selectedFrameworks?: any[];
  frameworkReuse?: any;
  referenceFramework?: any;
}

/**
 * 双AI协同选项
 */
export interface DualAIOptions {
  enableDialogueDesign?: boolean;
  dialogueAIConfig?: any;
  collaborationMode?: 'sequential' | 'parallel';
  dialogueThreshold?: number;
}

/**
 * 节奏分析选项
 */
export interface RhythmAnalysisOptions {
  enabled?: boolean;
  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
  includeChapterAnalysis?: boolean;
  includeConflictAnalysis?: boolean;
}

/**
 * 框架信息构建选项
 */
export interface FrameworkBuildOptions {
  includeChapterFramework?: boolean;
  includePlotFramework?: boolean;
  includeDialogueFramework?: boolean;
  maxFrameworkItems?: number;
  frameworkDetailLevel?: 'summary' | 'detailed' | 'comprehensive';
}

/**
 * 消息角色类型
 */
export type MessageRole = 'system' | 'user' | 'assistant';

/**
 * 消息接口
 */
export interface ConversationMessage {
  role: MessageRole;
  content: string;
}

/**
 * 节点变更操作类型
 */
export type NodeChangeType = 'create' | 'update' | 'delete';

/**
 * 节点变更接口
 */
export interface NodeChange {
  type: NodeChangeType;
  nodeId?: string;
  id?: string;
  data?: any;
  parentId?: string;
}

/**
 * 大纲节点基础接口
 */
export interface OutlineNode {
  nodeId: string;
  title: string;
  type: OutlineNodeType;
  description?: string;
  creativeNotes?: string;
  parentId?: string;
  order?: number;
  relatedCharacters?: string[];
  
  // 章节节点专用字段
  chapterStyle?: string;
  chapterTechniques?: string[];
  chapterGoals?: string;
  rhythmPhase?: 'setup' | 'conflict' | 'climax' | 'transition';
  rhythmGuidance?: string;
  
  // 剧情节点专用字段
  plotPoints?: PlotPoint[];
  plotType?: 'conflict' | 'twist' | 'climax' | 'resolution';
  conflictLevel?: number;
  suspenseElements?: string[];
  emotionalTone?: string;
  
  // 对话节点专用字段
  dialogueScene?: string;
  participants?: string[];
  dialoguePurpose?: string;
  dialogueContent?: DialogueLine[];

  // 核心故事梗概专用字段
  synopsisBrainhole?: string;
  synopsisGenre?: string;
  synopsisOpening?: string;
  synopsisCoreOutline?: string;
  synopsisEnding?: string;
  synopsisStoryDescription?: string;
  synopsisAceReferences?: string;
}

/**
 * 剧情点接口
 */
export interface PlotPoint {
  id: string;
  order?: number;
  content: string;
  avoidWriting?: string;
  specificDescription?: string;
  avoidanceGuidance?: string;
  type?: string;

  /**
   * 写作风格方法指导（可选）
   */
  styleMethod?: {
    technique: string;
    style: string;
    tone: string;
    perspective: string;
    emphasis: string;
  };

  /**
   * 格式规范（可选）
   */
  formatSpecs?: {
    wordCount: {
      min: number;
      max: number;
      target: number;
    };
    paragraphRules: {
      maxSentencesPerParagraph: number;
      paragraphBreakRules: string;
      conflictHandling?: string;        // 冲突处理规范
      actionDialogueFlow?: string;      // 对话行动流程规范
      mandatoryBreaks?: string;         // 强制换行要求
    };
    punctuationRules: {
      dialogueFormat: string;
      emphasisFormat: string;
      pauseFormat: string;
      conflictPunctuation?: string;     // 冲突标点规范
      naturalFlow?: string;             // 自然流畅要求
    };
    lineBreakRules: {
      sceneTransition: string;
      timeTransition: string;
      speakerChange: string;
      conflictEscalation?: string;      // 冲突升级换行规范
      actionEmphasis?: string;          // 行动强调换行规范
      emotionShift?: string;            // 情绪转折换行规范
      prohibitedMerging?: string;       // 禁止合并规范
    };
  };
}

/**
 * 对话行接口
 */
export interface DialogueLine {
  id: string;
  speaker: string;
  content: string;
  emotion?: string;
  action?: string;
}

/**
 * 大纲结构接口
 */
export interface OutlineStructure {
  nodes: OutlineNode[];
  relationships: OutlineRelationship[];
  metadata?: {
    totalNodes: number;
    chapterNodes: number;
    plotNodes: number;
    dialogueNodes: number;
    lastModified: Date;
  };
}

/**
 * 大纲关系接口
 */
export interface OutlineRelationship {
  parentId: string;
  childId: string;
  relationshipType: 'contains' | 'follows' | 'references';
}

/**
 * 书籍数据接口
 */
export interface BookData {
  id: string;
  title: string;
  chapters: ChapterData[];
  characters: CharacterData[];
  worldBuildings: WorldBuildingData[];
  terminologies: TerminologyData[];
}

/**
 * 章节数据接口
 */
export interface ChapterData {
  id: string;
  title: string;
  content: string;
  order: number;
  wordCount?: number;
  summary?: string;
}

/**
 * 角色数据接口
 */
export interface CharacterData {
  id: string;
  name: string;
  description: string;
  personality?: string;
  background?: string;
}

/**
 * 世界观数据接口
 */
export interface WorldBuildingData {
  id: string;
  title: string;
  description: string;
  category?: string;
}

/**
 * 术语数据接口
 */
export interface TerminologyData {
  id: string;
  term: string;
  definition: string;
  category?: string;
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AI_REQUEST_ERROR = 'AI_REQUEST_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误接口
 */
export interface ServiceError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
}

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  IDLE = 'IDLE',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

/**
 * 模块配置接口
 */
export interface ModuleConfig {
  enabled: boolean;
  priority: number;
  timeout?: number;
  retryCount?: number;
  cacheEnabled?: boolean;
  debugMode?: boolean;
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  messageBuilder: ModuleConfig;
  aiRequestHandler: ModuleConfig;
  dualAICoordinator: ModuleConfig;
  rhythmAnalysisIntegrator: ModuleConfig;
  frameworkInfoBuilder: ModuleConfig;
}
