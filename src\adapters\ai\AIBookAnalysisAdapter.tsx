"use client";

import React, { useState, useEffect } from 'react';
import { createAIFactory } from '@/factories/ai';
import { Book, IAIBookAnalysisComponent } from '@/factories/ai/interfaces';
import { getAllBookBreakdownPrompts, PromptTemplate } from '@/utils/ai/prompts/AIBookBreakdownPrompts';
import { dialogContinuationManager } from '@/utils/ai/DialogContinuationManager';
import { PromptCategory } from '@/lib/db/dexie';
import EnhancedMarkdown from '@/components/EnhancedMarkdown';
// 使用 promptTemplateRepository 替代 PromptManager

interface AIBookAnalysisAdapterProps {
  books?: Book[];
  onAnalysisComplete?: (analysisResult: string) => void;
}

/**
 * AI拆书适配器组件
 * 用于将AI拆书工厂组件集成到React应用中
 */
export const AIBookAnalysisAdapter: React.FC<AIBookAnalysisAdapterProps> = ({
  books,
  onAnalysisComplete
}) => {
  // 状态
  const [selectedBooks, setSelectedBooks] = useState<string[]>([]);
  const [selectedChapters, setSelectedChapters] = useState<{[bookId: string]: string[]}>({});
  const [analysisMode, setAnalysisMode] = useState<'single' | 'merged' | 'mixed' | 'sync'>('single');
  const [promptTemplate, setPromptTemplate] = useState('');
  const [analysisResult, setAnalysisResult] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [availableBooks, setAvailableBooks] = useState<Book[]>([]);
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);
  const [promptTemplates, setPromptTemplates] = useState<PromptTemplate[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [bookAnalysisComponent, setBookAnalysisComponent] = useState<IAIBookAnalysisComponent | null>(null);
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [selectedPromptTemplate, setSelectedPromptTemplate] = useState<PromptTemplate | null>(null);
  const [expandedBooks, setExpandedBooks] = useState<{[bookId: string]: boolean}>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);
  const [newTemplateContent, setNewTemplateContent] = useState('');
  // 用于跟踪是否已经显示过分析完成的通知
  const [hasShownCompletionNotice, setHasShownCompletionNotice] = useState(false);
  // 继续对话相关状态
  const [isContinueDialogOpen, setIsContinueDialogOpen] = useState(false);
  const [continuePrompt, setContinuePrompt] = useState('请基于上述分析，提供更深入的见解或建议');
  // 对话历史记录
  const [conversationHistory, setConversationHistory] = useState<{role: string, content: string}[]>([]);
  // 是否显示对话气泡界面
  const [showChatView, setShowChatView] = useState(false);

  // 初始化AI组件
  useEffect(() => {
    const aiFactory = createAIFactory();
    const component = aiFactory.createAIBookAnalysisComponent();
    setBookAnalysisComponent(component);

    // 设置初始值
    if (books && books.length > 0) {
      component.setBooks(books);
      // 设置已选择的书籍
      setSelectedBooks(books.map(book => book.id));
    }

    // 设置分析完成回调
    if (onAnalysisComplete) {
      component.setOnAnalysisComplete(onAnalysisComplete);
    }
  }, [books, onAnalysisComplete]);

  // 获取当前编辑器中的作品ID
  useEffect(() => {
    // 尝试从localStorage或URL参数中获取当前编辑器中的作品ID
    const getCurrentBookId = () => {
      try {
        // 尝试从localStorage获取
        const editorState = localStorage.getItem('editorState');
        if (editorState) {
          const parsedState = JSON.parse(editorState);
          if (parsedState && parsedState.currentBookId) {
            return parsedState.currentBookId;
          }
        }

        // 尝试从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const bookId = urlParams.get('bookId');
        if (bookId) {
          return bookId;
        }

        return null;
      } catch (error) {
        console.error('获取当前编辑器作品ID失败', error);
        return null;
      }
    };

    const bookId = getCurrentBookId();
    if (bookId) {
      setCurrentEditorBookId(bookId);
      console.log('当前编辑器作品ID:', bookId);
    }
  }, []);

  // 加载书籍列表
  useEffect(() => {
    const loadBooks = async () => {
      try {
        console.log('开始加载书籍和章节数据');

        // 如果已经提供了书籍，则使用提供的书籍
        if (books && books.length > 0) {
          console.log('使用提供的书籍数据:', books);
          setAvailableBooks(books);
          return;
        }

        // 尝试多种方法加载书籍和章节数据
        let userBooks: any[] = [];
        let booksWithChapters: Book[] = [];

        // 方法1: 使用 lib/db/repositories（新系统）
        try {
          console.log('🔍 尝试使用 lib/db/repositories 加载书籍');
          const { bookRepository } = await import('@/lib/db/repositories');
          userBooks = await bookRepository.getAll();
          console.log('✅ 通过 lib/db/repositories 获取到书籍数据:', userBooks);

          if (userBooks && userBooks.length > 0) {
            // 对于每本书，加载其章节
            booksWithChapters = await Promise.all(
              userBooks.map(async (book) => {
                console.log(`📖 加载书籍 "${book.title}" (ID: ${book.id}) 的章节`);

                try {
                  const { chapterRepository } = await import('@/lib/db/repositories');
                  const chapters = await chapterRepository.getAllByBookId(book.id!);
                  console.log(`✅ 获取到书籍 "${book.title}" 的章节数据:`, chapters);

                  // 转换为AI拆书组件需要的格式
                  return {
                    id: book.id!,
                    title: book.title,
                    chapters: chapters.map(chapter => ({
                      id: chapter.id!,
                      title: chapter.title,
                      content: chapter.content
                    }))
                  };
                } catch (chapterError) {
                  console.error(`❌ 加载书籍 "${book.title}" 的章节失败:`, chapterError);
                  return {
                    id: book.id!,
                    title: book.title,
                    chapters: []
                  };
                }
              })
            );

            console.log('🎉 成功加载书籍和章节数据:', booksWithChapters);
            setAvailableBooks(booksWithChapters);
            return;
          }
        } catch (error) {
          console.error('❌ 通过 lib/db/repositories 加载书籍失败:', error);
        }

        // 方法2: 尝试使用 db/bookRepository.ts（旧系统）
        if (userBooks.length === 0) {
          try {
            console.log('尝试使用 db/bookRepository 加载书籍');
            const { BookRepository } = await import('@/db/bookRepository');
            const bookRepo = new BookRepository();
            userBooks = await bookRepo.getAllBooks();
            console.log('通过 db/bookRepository 获取到书籍数据:', userBooks);

            if (userBooks && userBooks.length > 0) {
              // 对于每本书，加载其章节 - 使用新的数据库系统
              booksWithChapters = await Promise.all(
                userBooks.map(async (book) => {
                  console.log(`加载书籍 "${book.title}" (ID: ${book.id}) 的章节`);

                  try {
                    // 🔧 修复：使用新的数据库系统获取章节
                    const { chapterRepository } = await import('@/lib/db/repositories');
                    const chapters = await chapterRepository.getAllByBookId(book.id!);
                    console.log(`✅ 通过新数据库系统获取到书籍 "${book.title}" 的章节数据:`, chapters);

                    // 转换为AI拆书组件需要的格式
                    return {
                      id: book.id!,
                      title: book.title,
                      chapters: chapters.map(chapter => ({
                        id: chapter.id!,
                        title: chapter.title,
                        content: chapter.content
                      }))
                    };
                  } catch (chapterError) {
                    console.error(`❌ 加载书籍 "${book.title}" 的章节失败:`, chapterError);

                    // 🔧 降级：如果新系统失败，尝试旧系统
                    try {
                      console.log(`🔄 降级使用旧数据库系统获取书籍 "${book.title}" 的章节`);
                      const { ChapterRepository } = await import('@/db/chapterRepository');
                      const chapterRepo = new ChapterRepository();
                      const chapters = await chapterRepo.getChaptersByBookId(book.id!);
                      console.log(`📊 通过旧数据库系统获取到书籍 "${book.title}" 的章节数据:`, chapters);

                      return {
                        id: book.id!,
                        title: book.title,
                        chapters: chapters.map(chapter => ({
                          id: chapter.id!,
                          title: chapter.title,
                          content: chapter.content
                        }))
                      };
                    } catch (fallbackError) {
                      console.error(`❌ 旧数据库系统也失败:`, fallbackError);
                      return {
                        id: book.id!,
                        title: book.title,
                        chapters: []
                      };
                    }
                  }
                })
              );

              console.log('✅ 成功加载书籍和章节数据:', booksWithChapters);
              setAvailableBooks(booksWithChapters);
              return;
            }
          } catch (error) {
            console.error('❌ 通过 db/bookRepository 加载书籍失败:', error);
          }
        }

        // 如果所有方法都失败，设置为空数组
        console.warn('所有方法都无法获取书籍数据，设置为空数组');
        setAvailableBooks([]);
      } catch (error) {
        console.error('加载书籍失败', error);
        setErrorMessage('加载书籍失败');
      }
    };

    loadBooks();
  }, [books]);

  // 加载提示词模板
  const loadPromptTemplates = async () => {
    try {
      // 从提示词仓库加载模板
      const { promptTemplateRepository } = await import('@/lib/db/repositories');
      const savedTemplates = await promptTemplateRepository.getByCategory(PromptCategory.BOOK_ANALYSIS);

      // 合并预定义模板和保存的模板
      const predefinedTemplates = getAllBookBreakdownPrompts();
      const allTemplates = [...predefinedTemplates];

      // 添加保存的模板（如果有）
      if (savedTemplates && savedTemplates.length > 0) {
        savedTemplates.forEach(saved => {
          // 确保模板有tags属性
          if (!saved.tags) {
            saved.tags = ['用户自定义'];

            // 尝试根据模板名称推断分析模式
            if (saved.name.includes('单本')) {
              saved.tags.push('单本拆解');
            } else if (saved.name.includes('合并')) {
              saved.tags.push('合并拆解');
            } else if (saved.name.includes('混合')) {
              saved.tags.push('混合拆解');
            } else if (saved.name.includes('同步')) {
              saved.tags.push('同步拆解');
            }

            // 尝试保存更新后的标签
            try {
              promptTemplateRepository.update(saved.id!, { tags: saved.tags });
            } catch (e) {
              console.error('更新模板标签失败:', e);
            }
          }

          // 检查是否已存在相同ID的模板
          const existingIndex = allTemplates.findIndex(t => t.id === saved.id);
          if (existingIndex >= 0) {
            // 更新现有模板
            allTemplates[existingIndex] = {
              ...saved,
              category: PromptCategory.BOOK_ANALYSIS
            } as PromptTemplate;
          } else {
            // 添加新模板
            allTemplates.push({
              ...saved,
              category: PromptCategory.BOOK_ANALYSIS
            } as PromptTemplate);
          }
        });
      }

      setPromptTemplates(allTemplates);

      // 检查是否有上次选择的提示词模板ID
      const lastSelectedTemplateId = window.localStorage.getItem('last-selected-prompt-template-id');
      if (lastSelectedTemplateId) {
        // 查找对应的模板
        const lastTemplate = allTemplates.find(t => t.id === lastSelectedTemplateId);
        if (lastTemplate) {
          // 设置为当前选中的模板
          setSelectedPromptTemplate(lastTemplate as any);
          // 设置提示词内容
          setPromptTemplate(lastTemplate.content || '');
          console.log('已恢复上次选择的提示词模板:', lastTemplate.name);
        }
      }
    } catch (error) {
      console.error('加载提示词模板失败', error);
    }
  };

  useEffect(() => {
    loadPromptTemplates();
  }, []);

  // 保存分析结果和选择到localStorage
  const saveAnalysisToLocalStorage = () => {
    try {
      const dataToSave = {
        analysisResult,
        selectedBooks,
        selectedChapters,
        analysisMode,
        conversationHistory,
        showChatView,
        selectedPromptTemplateId: selectedPromptTemplate?.id || null,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('ai-book-analysis-state', JSON.stringify(dataToSave));
      console.log('分析状态已保存到localStorage');
    } catch (error) {
      console.error('保存分析状态失败:', error);
    }
  };

  // 从localStorage加载分析结果和选择
  const loadAnalysisFromLocalStorage = () => {
    try {
      const savedData = localStorage.getItem('ai-book-analysis-state');
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        // 恢复状态
        setAnalysisResult(parsedData.analysisResult || '');
        setSelectedBooks(parsedData.selectedBooks || []);
        setSelectedChapters(parsedData.selectedChapters || {});
        setAnalysisMode(parsedData.analysisMode || 'single');
        setConversationHistory(parsedData.conversationHistory || []);
        setShowChatView(parsedData.showChatView || false);

        // 保存上次选择的提示词模板ID，稍后在模板加载完成后设置
        if (parsedData.selectedPromptTemplateId) {
          // 在模板加载完成后，我们会根据这个ID设置选中的模板
          window.localStorage.setItem('last-selected-prompt-template-id', parsedData.selectedPromptTemplateId);
        }

        console.log('从localStorage加载了分析状态');
      }
    } catch (error) {
      console.error('加载分析状态失败:', error);
    }
  };

  // 清空分析结果和历史
  const clearAnalysisHistory = () => {
    if (window.confirm('确定要清空所有分析结果和历史记录吗？此操作不可撤销。')) {
      // 清空状态
      setAnalysisResult('');
      setConversationHistory([]);
      setShowChatView(false);

      // 清除localStorage中的数据
      localStorage.removeItem('ai-book-analysis-state');

      setSuccessMessage('已清空所有分析结果和历史记录');
      setTimeout(() => setSuccessMessage(null), 3000);
    }
  };

  // 初始化时加载保存的分析状态
  useEffect(() => {
    loadAnalysisFromLocalStorage();
  }, []);

  // 过滤当前分析模式的提示词模板
  const filteredPromptTemplates = promptTemplates.filter(template => {
    // 确保template存在
    if (!template) {
      return false;
    }

    // 如果没有tags属性，为其添加默认标签
    if (!template.tags) {
      template.tags = ['用户自定义'];
      // 尝试根据模板名称推断分析模式
      if (template.name.includes('单本')) {
        template.tags.push('单本拆解');
      } else if (template.name.includes('合并')) {
        template.tags.push('合并拆解');
      } else if (template.name.includes('混合')) {
        template.tags.push('混合拆解');
      } else if (template.name.includes('同步')) {
        template.tags.push('同步拆解');
      }
    }

    // 如果有搜索查询，先按搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const nameMatch = template.name.toLowerCase().includes(query);
      const descMatch = template.description && template.description.toLowerCase().includes(query);
      const contentMatch = template.content && template.content.toLowerCase().includes(query);
      const tagsMatch = template.tags && template.tags.some(tag => tag.toLowerCase().includes(query));

      if (!(nameMatch || descMatch || contentMatch || tagsMatch)) {
        return false;
      }
    }

    // 如果在提示词管理器中，不按分析模式过滤
    if (isPromptManagerOpen) {
      return true;
    }

    // 否则按当前分析模式过滤
    switch (analysisMode) {
      case 'single':
        return template.tags.includes('单本拆解');
      case 'merged':
        return template.tags.includes('合并拆解');
      case 'mixed':
        return template.tags.includes('混合拆解');
      case 'sync':
        return template.tags.includes('同步拆解');
      default:
        return true;
    }
  });

  // 处理分析
  const handleAnalyze = async () => {
    if (!bookAnalysisComponent) {
      setErrorMessage('AI组件未初始化');
      return;
    }

    if (selectedBooks.length === 0) {
      setErrorMessage('请至少选择一本书籍');
      return;
    }

    // 检查是否有选择章节
    const hasSelectedChapters = selectedBooks.some(bookId =>
      (selectedChapters[bookId] || []).length > 0
    );

    if (!hasSelectedChapters) {
      setErrorMessage('请至少选择一个章节进行分析');
      return;
    }

    if (!promptTemplate.trim()) {
      setErrorMessage('请输入提示词模板或选择一个已有模板');
      return;
    }

    // 获取选中的书籍，并过滤出选中的章节
    const selectedBooksData = availableBooks
      .filter(book => selectedBooks.includes(book.id))
      .map(book => {
        // 过滤出选中的章节
        const bookSelectedChapters = selectedChapters[book.id] || [];
        const filteredChapters = book.chapters.filter(chapter =>
          bookSelectedChapters.includes(chapter.id)
        );

        // 返回带有过滤后章节的书籍对象
        return {
          ...book,
          chapters: filteredChapters
        };
      });

    // 设置分析参数
    bookAnalysisComponent.setBooks(selectedBooksData);
    bookAnalysisComponent.setAnalysisMode(analysisMode);
    bookAnalysisComponent.setPromptTemplate(promptTemplate);

    // 设置回调函数
    bookAnalysisComponent.setOnAnalysisComplete((result) => {
      setAnalysisResult(result);

      // 只在分析完成且尚未显示通知时显示成功消息
      if (!isAnalyzing && !hasShownCompletionNotice) {
        setSuccessMessage('分析完成');
        setHasShownCompletionNotice(true);

        // 3秒后清除成功消息
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      }

      // 调用外部回调
      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }
    });

    setIsAnalyzing(true);
    setErrorMessage(null);
    setAnalysisResult(''); // 清空之前的分析结果
    setHasShownCompletionNotice(false); // 重置通知状态

    try {
      console.log('开始分析书籍:', {
        books: selectedBooksData.map(book => book.title),
        chaptersCount: selectedBooksData.reduce((sum, book) => sum + book.chapters.length, 0),
        analysisMode,
        promptTemplateLength: promptTemplate.length
      });

      // 获取当前分析模式的系统提示词
      const systemPrompt = getSystemPromptForMode(analysisMode);

      // 构建完整的提示词，包含系统提示词和用户选择的提示词模板
      let fullPrompt = systemPrompt;

      // 如果用户选择了提示词模板，添加到提示词中
      if (selectedPromptTemplate && selectedPromptTemplate.content) {
        fullPrompt += `\n\n用户选择的提示词模板：\n${selectedPromptTemplate.content}`;
      }

      // 如果用户输入了自定义提示词，添加到提示词中
      if (promptTemplate && (!selectedPromptTemplate || promptTemplate !== selectedPromptTemplate.content)) {
        fullPrompt += `\n\n用户自定义提示词：\n${promptTemplate}`;
      }

      // 设置完整的提示词
      bookAnalysisComponent.setPromptTemplate(fullPrompt);

      // 调用分析方法
      const result = await bookAnalysisComponent.analyze();

      console.log('分析完成，结果长度:', result.length);
      setAnalysisResult(result);

      // 将分析结果保存到本地存储
      if (result) {
        try {
          // 保存分析结果到localStorage
          const analysisResults = JSON.parse(localStorage.getItem('ai-book-analysis-results') || '[]');
          analysisResults.push({
            timestamp: new Date().toISOString(),
            result,
            books: selectedBooksData.map(book => book.title),
            analysisMode
          });
          localStorage.setItem('ai-book-analysis-results', JSON.stringify(analysisResults));

          // 更新对话历史
          if (conversationHistory.length === 0) {
            // 创建新的对话历史，包含系统提示词和AI回复
            const newHistory = [];

            // 添加系统提示词
            newHistory.push({
              role: 'system' as const,
              content: getSystemPromptForMode(analysisMode)
            });

            // 如果有选择的提示词模板，添加到系统提示词中
            if (selectedPromptTemplate && selectedPromptTemplate.content) {
              newHistory.push({
                role: 'system' as const,
                content: `用户选择的提示词模板：\n${selectedPromptTemplate.content}`
              });
            }

            // 添加AI的回复
            newHistory.push({
              role: 'assistant' as const,
              content: result
            });

            // 更新对话历史
            setConversationHistory(newHistory);
          }

          // 保存当前分析状态
          saveAnalysisToLocalStorage();
        } catch (error) {
          console.error('保存分析结果失败', error);
        }
      }

      // 分析完成后，设置状态以允许显示成功消息
      setIsAnalyzing(false);

      // 如果尚未显示过通知，则显示一次
      if (!hasShownCompletionNotice) {
        setSuccessMessage('分析完成');
        setHasShownCompletionNotice(true);

        // 3秒后清除成功消息
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      }

      // 保存分析状态到localStorage，确保退出后能恢复
      saveAnalysisToLocalStorage();
    } catch (error: any) {
      console.error('分析书籍失败', error);
      setErrorMessage(`分析失败: ${error.message || '未知错误'}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 处理取消分析
  const handleCancelAnalysis = () => {
    if (bookAnalysisComponent) {
      bookAnalysisComponent.cancelAnalysis();
    }
    setIsAnalyzing(false);
    // 重置通知状态
    setHasShownCompletionNotice(false);
  };

  // 切换书籍选择
  const toggleBookSelection = (bookId: string) => {
    setSelectedBooks(prev => {
      const newSelection = prev.includes(bookId)
        ? prev.filter(id => id !== bookId)
        : [...prev, bookId];

      // 如果取消选择书籍，也清除该书籍的章节选择
      if (!newSelection.includes(bookId)) {
        setSelectedChapters(prev => {
          const newChapters = { ...prev };
          delete newChapters[bookId];
          return newChapters;
        });
      } else {
        // 如果选择书籍，默认展开该书籍
        setExpandedBooks(prev => ({
          ...prev,
          [bookId]: true
        }));
      }

      return newSelection;
    });
  };

  // 状态：范围选择
  const [rangeSelectionMode, setRangeSelectionMode] = useState<{[bookId: string]: boolean}>({});
  const [rangeStartChapter, setRangeStartChapter] = useState<{[bookId: string]: string | null}>({});

  // 切换章节选择
  const toggleChapterSelection = (bookId: string, chapterId: string) => {
    // 如果当前书籍处于范围选择模式
    if (rangeSelectionMode[bookId]) {
      const book = availableBooks.find(b => b.id === bookId);
      if (!book) return;

      // 如果没有起始章节，设置当前章节为起始章节
      if (!rangeStartChapter[bookId]) {
        setRangeStartChapter(prev => ({
          ...prev,
          [bookId]: chapterId
        }));

        // 选中当前章节
        setSelectedChapters(prev => {
          const bookChapters = prev[bookId] || [];
          return {
            ...prev,
            [bookId]: [...bookChapters, chapterId]
          };
        });
      } else {
        // 已有起始章节，现在选择的是结束章节
        const startChapterId = rangeStartChapter[bookId];
        if (!startChapterId) return;

        // 获取章节索引
        const chapterIds = book.chapters.map(ch => ch.id);
        const startIndex = chapterIds.indexOf(startChapterId);
        const endIndex = chapterIds.indexOf(chapterId);

        if (startIndex === -1 || endIndex === -1) return;

        // 确定范围（支持正向和反向选择）
        const minIndex = Math.min(startIndex, endIndex);
        const maxIndex = Math.max(startIndex, endIndex);

        // 获取范围内的所有章节ID
        const rangeChapterIds = chapterIds.slice(minIndex, maxIndex + 1);

        // 更新选中的章节
        setSelectedChapters(prev => {
          const bookChapters = prev[bookId] || [];

          // 合并已选章节和范围内章节
          const newBookChapters = [...new Set([...bookChapters, ...rangeChapterIds])];

          return {
            ...prev,
            [bookId]: newBookChapters
          };
        });

        // 退出范围选择模式
        setRangeSelectionMode(prev => ({
          ...prev,
          [bookId]: false
        }));

        // 清除起始章节
        setRangeStartChapter(prev => ({
          ...prev,
          [bookId]: null
        }));
      }
    } else {
      // 普通选择模式
      setSelectedChapters(prev => {
        const bookChapters = prev[bookId] || [];
        const newBookChapters = bookChapters.includes(chapterId)
          ? bookChapters.filter(id => id !== chapterId)
          : [...bookChapters, chapterId];

        return {
          ...prev,
          [bookId]: newBookChapters
        };
      });
    }
  };

  // 切换范围选择模式
  const toggleRangeSelectionMode = (bookId: string) => {
    setRangeSelectionMode(prev => ({
      ...prev,
      [bookId]: !prev[bookId]
    }));

    // 如果退出范围选择模式，清除起始章节
    if (rangeSelectionMode[bookId]) {
      setRangeStartChapter(prev => ({
        ...prev,
        [bookId]: null
      }));
    }
  };

  // 全选/取消全选书籍的所有章节
  const toggleAllChapters = (bookId: string, select: boolean) => {
    const book = availableBooks.find(b => b.id === bookId);
    if (!book) return;

    setSelectedChapters(prev => {
      if (select) {
        // 全选
        return {
          ...prev,
          [bookId]: book.chapters.map(chapter => chapter.id)
        };
      } else {
        // 取消全选
        const newChapters = { ...prev };
        newChapters[bookId] = [];
        return newChapters;
      }
    });

    // 退出范围选择模式
    setRangeSelectionMode(prev => ({
      ...prev,
      [bookId]: false
    }));

    // 清除起始章节
    setRangeStartChapter(prev => ({
      ...prev,
      [bookId]: null
    }));
  };

  // 切换书籍展开/折叠状态
  const toggleBookExpanded = (bookId: string) => {
    setExpandedBooks(prev => ({
      ...prev,
      [bookId]: !prev[bookId]
    }));
  };

  // 处理选择提示词模板
  const handleSelectTemplate = (template: PromptTemplate) => {
    setPromptTemplate(template.content);
    setSelectedPromptTemplate(template);
    setIsPromptManagerOpen(false);
    setShowCustomPrompt(false);
    setSuccessMessage('已应用提示词模板');

    // 保存选择的模板ID到localStorage
    if (template.id) {
      window.localStorage.setItem('last-selected-prompt-template-id', template.id);
      console.log('已保存选择的提示词模板ID:', template.id);
    }

    // 保存当前分析状态
    saveAnalysisToLocalStorage();

    // 3秒后清除成功消息
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  // 切换到自定义提示词
  const switchToCustomPrompt = () => {
    setShowCustomPrompt(true);
    setSelectedPromptTemplate(null);
  };

  // 切换到选择提示词
  const switchToSelectPrompt = () => {
    setShowCustomPrompt(false);
    setIsPromptManagerOpen(true);
  };

  // 导出分析结果
  const exportAnalysisResult = () => {
    if (!analysisResult) return;

    try {
      // 创建Blob对象
      const blob = new Blob([analysisResult], { type: 'text/markdown' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `书籍分析结果_${new Date().toISOString().slice(0, 10)}.md`;

      // 触发下载
      document.body.appendChild(a);
      a.click();

      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setSuccessMessage('分析结果已导出');

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('导出分析结果失败', error);
      setErrorMessage('导出分析结果失败');
    }
  };

  // 打开继续对话弹窗
  const openContinueDialog = () => {
    setIsContinueDialogOpen(true);
  };

  // 获取当前分析模式的系统提示词
  const getSystemPromptForMode = (mode: 'single' | 'merged' | 'mixed' | 'sync'): string => {
    switch (mode) {
      case 'single':
        return `你是一位专业的文学分析专家，擅长对单本书籍进行深入分析。
请对提供的书籍进行全面分析，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考

请以Markdown格式输出，使用标题、列表等元素组织内容，使分析结构清晰。

你还可以使用HTML代码块来创建可视化图表，如人物关系图、情节发展曲线等。使用以下格式：

\`\`\`html-render 图表标题
<html代码>
\`\`\`

例如，创建一个人物关系图：

\`\`\`html-render 人物关系图
<!DOCTYPE html>
<html>
<head>
  <style>
    .node { fill: #69b3a2; }
    .link { stroke: #aaa; }
    .label { font-family: Arial; font-size: 12px; }
  </style>
</head>
<body>
  <svg width="500" height="300">
    <g transform="translate(250, 150)">
      <!-- 节点和连线的SVG代码 -->
      <line class="link" x1="-100" y1="0" x2="0" y2="0"></line>
      <line class="link" x1="0" y1="0" x2="100" y2="0"></line>
      <circle class="node" cx="-100" cy="0" r="20"></circle>
      <circle class="node" cx="0" cy="0" r="20"></circle>
      <circle class="node" cx="100" cy="0" r="20"></circle>
      <text class="label" x="-100" y="30" text-anchor="middle">角色A</text>
      <text class="label" x="0" y="30" text-anchor="middle">角色B</text>
      <text class="label" x="100" y="30" text-anchor="middle">角色C</text>
    </g>
  </svg>
</body>
</html>
\`\`\`

系统会自动将这些代码块渲染为可交互的图表，用户可以点击查看。`;

      case 'merged':
        return `你是一位专业的文学分析专家，擅长对多本书籍进行合并分析。
请对提供的多本书籍进行合并分析，将它们视为一个整体，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考

特别重要：在合并分析中，请特别关注并详细阐述这些作品的共同点和相似模式，这是整合分析的核心部分。

请以Markdown格式输出，使用标题、列表等元素组织内容，使分析结构清晰。

你还可以使用HTML代码块来创建可视化图表，如作品比较图、共同元素关系图等。使用以下格式：

\`\`\`html-render 图表标题
<html代码>
\`\`\`

例如，创建一个作品比较图：

\`\`\`html-render 作品比较图
<!DOCTYPE html>
<html>
<head>
  <style>
    .chart { font-family: Arial; }
    .bar { fill: steelblue; }
    .axis text { font-size: 12px; }
    .axis path, .axis line { fill: none; stroke: #000; }
    .x-label { font-size: 14px; text-anchor: middle; }
    .y-label { font-size: 14px; text-anchor: middle; }
  </style>
</head>
<body>
  <svg width="600" height="400" class="chart">
    <!-- 这里是图表的SVG代码 -->
    <!-- 可以使用条形图、雷达图等展示作品间的比较 -->
  </svg>
</body>
</html>
\`\`\`

系统会自动将这些代码块渲染为可交互的图表，用户可以点击查看。`;

      case 'mixed':
        return `你是一位专业的文学分析专家，擅长对多本书籍进行混合分析。
请对提供的多本书籍进行混合分析，既分析每本书的独特之处，又分析它们之间的关联，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考

特别重要：请专门创建一个"共同元素分析"章节，详细分析这些作品在人物、情节、风格、世界观和主题等方面的共同点和相似模式。这些共同点是混合创作的基础，也是理解作者创作倾向的关键。

请以Markdown格式输出，使用标题、列表等元素组织内容，使分析结构清晰。

你还可以使用HTML代码块来创建可视化图表，如元素融合图、故事线交织图等。使用以下格式：

\`\`\`html-render 图表标题
<html代码>
\`\`\`

例如，创建一个元素融合图：

\`\`\`html-render 元素融合图
<!DOCTYPE html>
<html>
<head>
  <style>
    .container { font-family: Arial; }
    .circle { fill-opacity: 0.5; stroke: #333; stroke-width: 1; }
    .label { font-size: 12px; text-anchor: middle; }
    .intersection { font-size: 11px; }
  </style>
</head>
<body>
  <svg width="600" height="400" class="container">
    <!-- 这里是维恩图或其他融合图表的SVG代码 -->
    <circle class="circle" cx="200" cy="200" r="100" fill="#ff9999"></circle>
    <circle class="circle" cx="300" cy="200" r="100" fill="#99ff99"></circle>
    <text class="label" x="150" y="200">作品A元素</text>
    <text class="label" x="350" y="200">作品B元素</text>
    <text class="intersection" x="250" y="200">共同元素</text>
  </svg>
</body>
</html>
\`\`\`

系统支持高级可视化功能，不限于示例中的简单图表。你可以创建复杂的交互式图表，如：
- 人物关系网络图（使用D3.js或其他可视化库）
- 情节发展时间线（带有交互式节点）
- 主题分布热力图
- 角色特性雷达图
- 情感分析曲线图
- 世界观地图（带有标记点和区域）
- 任何能帮助读者理解作品的可视化内容

系统会自动将这些代码块渲染为可交互的图表，用户可以点击查看，也可以在弹窗中全屏查看以获得更好的体验。`;

      case 'sync':
        return `你是一位专业的文学分析专家，擅长对多本书籍进行同步分析。
请对提供的多本书籍进行同步分析，为每本书提供独立的分析，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考

特别重要：在完成每部作品的独立分析后，请添加一个"作品共性分析"章节，深入探讨这些作品在各个维度上的共同特征和模式，这有助于理解创作规律和作者风格。

请以Markdown格式输出，使用标题、列表等元素组织内容，使分析结构清晰。

你还可以使用HTML代码块来创建可视化图表，如评分对比图、共同特征图等。使用以下格式：

\`\`\`html-render 图表标题
<html代码>
\`\`\`

例如，创建一个评分对比图：

\`\`\`html-render 作品评分对比图
<!DOCTYPE html>
<html>
<head>
  <style>
    .chart-container { font-family: Arial; }
    .bar { height: 30px; margin: 5px 0; }
    .bar-label { font-size: 12px; }
    .score-label { font-size: 12px; font-weight: bold; }
    .book-title { font-size: 14px; font-weight: bold; margin-top: 15px; }
  </style>
</head>
<body>
  <div class="chart-container" style="width: 600px;">
    <!-- 这里是评分对比图的HTML代码 -->
    <!-- 可以使用条形图、雷达图等展示不同作品在各维度的评分 -->
    <div class="book-title">作品A</div>
    <div style="display: flex; align-items: center;">
      <div style="width: 100px;">人物塑造</div>
      <div class="bar" style="width: 80%; background-color: #ff9999;"></div>
      <div class="score-label">8/10</div>
    </div>
    <!-- 其他评分项... -->
  </div>
</body>
</html>
\`\`\`

系统支持高级可视化功能，不限于示例中的简单图表。你可以创建复杂的交互式图表，如：
- 人物关系网络图（使用D3.js或其他可视化库）
- 情节发展时间线（带有交互式节点）
- 主题分布热力图
- 角色特性雷达图
- 情感分析曲线图
- 世界观地图（带有标记点和区域）
- 任何能帮助读者理解作品的可视化内容

系统会自动将这些代码块渲染为可交互的图表，用户可以点击查看，也可以在弹窗中全屏查看以获得更好的体验。`;

      default:
        return `你是一位专业的文学分析专家，擅长对书籍进行深入分析。
请对提供的书籍进行全面分析，包括但不限于：
1. 主要人物分析：性格特点、动机、成长轨迹、关系网络
2. 情节分析：主要情节线、冲突设置、高潮转折、结构安排
3. 写作风格分析：语言特点、叙事视角、修辞手法、情感基调
4. 世界观设定分析：背景设定、规则体系、文化元素、时空构建
5. 主题分析：核心主题、象征意义、哲学思考

请以Markdown格式输出，使用标题、列表等元素组织内容，使分析结构清晰。

你还可以使用HTML代码块来创建可视化图表，如人物关系图、情节发展曲线等。使用以下格式：

\`\`\`html-render 图表标题
<html代码>
\`\`\`

系统支持高级可视化功能，不限于示例中的简单图表。你可以创建复杂的交互式图表，如：
- 人物关系网络图（使用D3.js或其他可视化库）
- 情节发展时间线（带有交互式节点）
- 主题分布热力图
- 角色特性雷达图
- 情感分析曲线图
- 世界观地图（带有标记点和区域）
- 任何能帮助读者理解作品的可视化内容

系统会自动将这些代码块渲染为可交互的图表，用户可以点击查看，也可以在弹窗中全屏查看以获得更好的体验。`;
    }
  };

  // 继续对话
  const continueConversation = async () => {
    if (!analysisResult || isAnalyzing) return;

    // 关闭弹窗
    setIsContinueDialogOpen(false);

    try {
      // 导入对话继续管理器
      const { dialogContinuationManager } = await import('@/utils/ai/DialogContinuationManager');

      // 获取选中的书籍数据
      const selectedBooksData = availableBooks.filter(book => selectedBooks.includes(book.id));
      const bookId = selectedBooksData.length > 0 ? selectedBooksData[0].id : 'unknown';

      // 更新对话历史
      const updatedHistory = [...conversationHistory];

      // 如果是第一次对话，添加系统提示词和AI的初始消息
      if (updatedHistory.length === 0) {
        // 添加系统提示词
        const systemPrompt = getSystemPromptForMode(analysisMode);
        updatedHistory.push({
          role: 'system' as const,
          content: systemPrompt
        });

        // 如果有选择的提示词模板，添加到系统提示词中
        if (selectedPromptTemplate && selectedPromptTemplate.content) {
          updatedHistory.push({
            role: 'system' as const,
            content: `用户选择的提示词模板：\n${selectedPromptTemplate.content}`
          });
        }

        // 添加AI的初始消息
        if (analysisResult) {
          updatedHistory.push({
            role: 'assistant' as const,
            content: analysisResult
          });
        }
      }

      // 添加用户的新消息
      updatedHistory.push({
        role: 'user' as const,
        content: continuePrompt
      });

      // 更新对话历史状态
      setConversationHistory(updatedHistory);

      // 构建对话继续参数
      const params = {
        currentContent: analysisResult,
        continuePrompt: continuePrompt,
        continueMode: 'analyze' as const,
        bookId: bookId,
        conversationHistory: updatedHistory.map(msg => ({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: msg.content
        }))
      };

      console.log('继续对话参数:', {
        promptLength: params.conversationHistory.reduce((sum, msg) => sum + msg.content.length, 0),
        messageCount: params.conversationHistory.length,
        roles: params.conversationHistory.map(msg => msg.role)
      });

      // 处理继续对话
      const result = await dialogContinuationManager.handleContinuation(params);

      // 设置分析中状态
      setIsAnalyzing(true);
      setErrorMessage(null);
      setHasShownCompletionNotice(false);

      // 切换到对话气泡视图
      setShowChatView(true);

      // 调用AI服务
      if (bookAnalysisComponent) {
        try {
          // 确保设置书籍数据和分析模式
          bookAnalysisComponent.setBooks(selectedBooksData);
          bookAnalysisComponent.setAnalysisMode(analysisMode);

          // 使用分析组件的API
          bookAnalysisComponent.setPromptTemplate(result.messages.map(m => `${m.role}: ${m.content}`).join('\n\n'));
          const analysisResult = await bookAnalysisComponent.analyze();

          // 更新对话历史，添加AI的回复
          setConversationHistory(prev => {
            const newHistory = [...prev];
            newHistory.push({
              role: 'assistant' as const,
              content: analysisResult
            });
            return newHistory;
          });

          // 更新分析结果
          setAnalysisResult(analysisResult);

          // 设置分析完成
          setIsAnalyzing(false);

          // 保存到localStorage
          saveAnalysisToLocalStorage();
        } catch (error) {
          console.error('分析失败:', error);
          setErrorMessage('分析失败: ' + (error instanceof Error ? error.message : String(error)));
          setIsAnalyzing(false);
        }
      }
    } catch (error) {
      console.error('继续对话失败:', error);
      setErrorMessage('继续对话失败: ' + (error instanceof Error ? error.message : String(error)));
      setIsAnalyzing(false);
    }
  };

  // 状态：保存模板对话框
  const [isSaveTemplateDialogOpen, setIsSaveTemplateDialogOpen] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState('');

  // 状态：当前编辑器中的作品ID
  const [currentEditorBookId, setCurrentEditorBookId] = useState<string | null>(null);
  const [newTemplateDescription, setNewTemplateDescription] = useState('用户自定义模板');

  // 打开保存模板对话框
  const openSaveTemplateDialog = () => {
    if (!promptTemplate.trim() && !isEditMode) {
      setErrorMessage('提示词模板不能为空');
      return;
    }

    if (!isEditMode) {
      // 新建模式，设置默认名称
      setNewTemplateName(`${analysisMode === 'single' ? '单本拆解' :
                           analysisMode === 'merged' ? '合并拆解' :
                           analysisMode === 'mixed' ? '混合拆解' : '同步拆解'} ${new Date().toLocaleString()}`);
      setNewTemplateContent(promptTemplate);
    }

    setIsSaveTemplateDialogOpen(true);
  };

  // 保存当前提示词模板
  const saveCurrentTemplate = async () => {
    if (!promptTemplate.trim()) {
      setErrorMessage('提示词模板不能为空');
      return;
    }

    if (!newTemplateName.trim()) {
      setErrorMessage('模板名称不能为空');
      return;
    }

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 根据模板名称推断标签
      const tags = ['用户自定义'];

      // 根据模板名称推断分析模式标签
      if (newTemplateName.includes('单本')) {
        tags.push('单本拆解');
      } else if (newTemplateName.includes('合并')) {
        tags.push('合并拆解');
      } else if (newTemplateName.includes('混合')) {
        tags.push('混合拆解');
      } else if (newTemplateName.includes('同步')) {
        tags.push('同步拆解');
      } else {
        // 如果名称中没有包含模式关键词，则根据当前分析模式添加标签
        tags.push(
          analysisMode === 'single' ? '单本拆解' :
          analysisMode === 'merged' ? '合并拆解' :
          analysisMode === 'mixed' ? '混合拆解' : '同步拆解'
        );
      }

      if (isEditMode && editingTemplateId) {
        // 更新现有模板
        const templateUpdate: Partial<PromptTemplate> = {
          name: newTemplateName.trim(),
          content: promptTemplate,
          description: newTemplateDescription.trim() || '用户自定义模板',
          tags: tags
        };

        console.log('正在更新提示词模板:', editingTemplateId, templateUpdate);
        await promptTemplateRepository.update(editingTemplateId, templateUpdate);
        console.log('提示词模板更新成功');

        // 获取更新后的模板
        const updatedTemplate = await promptTemplateRepository.getById(editingTemplateId);

        // 更新模板列表
        if (updatedTemplate) {
          // 使用类型断言解决类型不兼容问题
          setPromptTemplates(prev =>
            prev.map(t => t.id === editingTemplateId ? updatedTemplate as any : t) as any
          );

          // 选择更新后的模板
          setSelectedPromptTemplate(updatedTemplate as any);
        }

        // 重置编辑状态
        setIsEditMode(false);
        setEditingTemplateId(null);
      } else {
        // 创建新模板
        const newTemplate: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
          category: PromptCategory.BOOK_ANALYSIS,
          name: newTemplateName.trim(),
          content: promptTemplate,
          description: newTemplateDescription.trim() || '用户自定义模板',
          tags: tags
        };

        console.log('正在保存提示词模板:', newTemplate);

        // 保存模板
        const templateId = await promptTemplateRepository.create(newTemplate);
        console.log('提示词模板保存成功, ID:', templateId);

        // 获取保存后的模板
        const savedTemplate = await promptTemplateRepository.getById(templateId);

        // 确保有标签信息
        const templateWithTags = savedTemplate || {
          ...newTemplate,
          id: templateId,
          createdAt: new Date(),
          updatedAt: new Date()
        } as PromptTemplate;

        // 更新模板列表
        setPromptTemplates(prev => [...prev, templateWithTags as any] as any);

        // 选择新保存的模板
        setSelectedPromptTemplate(templateWithTags as any);
      }

      // 关闭对话框
      setIsSaveTemplateDialogOpen(false);

      // 重置表单
      setNewTemplateName('');
      setNewTemplateDescription('用户自定义模板');

      setSuccessMessage(isEditMode ? '提示词模板已更新' : '提示词模板已保存');

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error(isEditMode ? '更新提示词模板失败' : '保存提示词模板失败', error);
      setErrorMessage(isEditMode ? '更新提示词模板失败' : '保存提示词模板失败');
    }
  };

  // 编辑提示词模板
  const editTemplate = (template: PromptTemplate) => {
    setNewTemplateName(template.name);
    setNewTemplateContent(template.content || '');
    setNewTemplateDescription(template.description || '');
    setEditingTemplateId(template.id!);
    setIsEditMode(true);
    setIsSaveTemplateDialogOpen(true);
    setPromptTemplate(template.content || '');
  };

  // 删除提示词模板
  const deleteTemplate = async (templateId: string) => {
    if (!window.confirm('确定要删除这个提示词模板吗？')) {
      return;
    }

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 删除模板
      await promptTemplateRepository.delete(templateId);
      console.log('提示词模板删除成功, ID:', templateId);

      // 更新模板列表
      setPromptTemplates(prev => prev.filter(t => t.id !== templateId));

      // 如果当前选中的模板被删除，清除选择
      if (selectedPromptTemplate && selectedPromptTemplate.id === templateId) {
        setSelectedPromptTemplate(null);
      }

      setSuccessMessage('提示词模板已删除');

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('删除提示词模板失败:', error);
      setErrorMessage('删除提示词模板失败');
    }
  };

  return (
    <div className="h-full flex flex-col bg-[var(--color-primary-bg)]">
      {/* 顶部消息区域 */}
      <div className="p-2">
        {/* 成功消息 - Toast样式 */}
        {successMessage && (
          <div className="mb-2 p-2 bg-[var(--color-success)] bg-opacity-10 text-[var(--color-success)] rounded-lg shadow-md border border-[var(--color-success)] border-opacity-20">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {successMessage}
            </div>
          </div>
        )}

        {/* 错误消息 - Toast样式 */}
        {errorMessage && (
          <div className="mb-2 p-2 bg-white text-[var(--color-danger)] rounded-lg shadow-md border border-[var(--color-danger)] border-opacity-20">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errorMessage}
            </div>
          </div>
        )}
      </div>

      {/* 主体内容区域 - 左右分栏 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧栏 - 章节选择、提示词管理和模式选择 */}
        <div className="w-1/3 border-r border-[var(--color-secondary)] border-opacity-30 flex flex-col overflow-hidden bg-[var(--color-sidebar-bg)]">
          {/* 章节选择区域 */}
          <div className="p-4 flex-1 overflow-auto">
            <h3 className="text-lg font-semibold mb-3 text-[var(--color-primary)]">章节选择</h3>

            {availableBooks.length > 0 ? (
              <div className="space-y-3">
                {availableBooks.map(book => (
                  <div
                    key={book.id}
                    className={`border ${
                      currentEditorBookId === book.id
                        ? 'border-[var(--color-primary)] border-opacity-80 shadow-md'
                        : 'border-[var(--color-secondary)] border-opacity-50 shadow-sm'
                    } rounded-xl overflow-hidden`}
                  >
                    {/* 书籍标题栏 */}
                    <div className={`flex items-center p-2 ${
                      currentEditorBookId === book.id
                        ? 'bg-[var(--color-primary)] bg-opacity-10 border-b border-[var(--color-primary)] border-opacity-40'
                        : 'bg-[var(--color-secondary)] bg-opacity-20 border-b border-[var(--color-secondary)] border-opacity-30'
                    }`}>
                      <input
                        type="checkbox"
                        id={`book-${book.id}`}
                        checked={selectedBooks.includes(book.id)}
                        onChange={() => toggleBookSelection(book.id)}
                        className="mr-2 accent-[var(--color-primary)]"
                      />
                      <label
                        htmlFor={`book-${book.id}`}
                        className="flex-1 font-medium cursor-pointer text-[var(--color-text-primary)]"
                        onClick={() => toggleBookExpanded(book.id)}
                      >
                        <span className="flex items-center">
                          {book.title}
                          <span
                            className={`ml-2 text-xs px-2 py-0.5 rounded-full border ${
                              // 检查是否为当前编辑器中的作品ID
                              currentEditorBookId === book.id
                                ? 'bg-[var(--color-primary)] text-white border-[var(--color-primary)]'
                                : 'bg-[var(--color-primary-bg)] text-[var(--color-primary)] border-[var(--color-primary)] border-opacity-30'
                            }`}
                            title={`完整ID: ${book.id}`}
                          >
                            {currentEditorBookId === book.id ? '当前作品' : `ID: ${book.id.substring(0, 6)}`}
                          </span>
                          <span className="ml-2 text-xs text-[var(--color-text-secondary)]">
                            {book.chapters.length} 章节
                          </span>
                        </span>
                      </label>
                      <button
                        className="text-[var(--color-primary)] hover:text-[var(--color-primary)] opacity-80 hover:opacity-100"
                        onClick={() => toggleBookExpanded(book.id)}
                      >
                        {expandedBooks[book.id] ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </button>
                    </div>

                    {/* 章节列表 */}
                    {selectedBooks.includes(book.id) && expandedBooks[book.id] && (
                      <div className={`p-2 ${
                        currentEditorBookId === book.id
                          ? 'bg-[var(--color-primary-bg)]'
                          : 'bg-[var(--color-white)]'
                      } rounded-b-xl`}>
                        <div className="flex justify-between items-center mb-2 text-sm">
                          <span className="text-[var(--color-text-secondary)] font-medium">章节列表</span>
                          <div className="space-x-2">
                            <button
                              className="text-xs text-[var(--color-primary)] hover:text-[var(--color-primary)] hover:underline"
                              onClick={() => toggleAllChapters(book.id, true)}
                            >
                              全选
                            </button>
                            <button
                              className="text-xs text-[var(--color-primary)] hover:text-[var(--color-primary)] hover:underline"
                              onClick={() => toggleAllChapters(book.id, false)}
                            >
                              取消全选
                            </button>
                            <button
                              className={`text-xs ${rangeSelectionMode[book.id]
                                ? 'text-[var(--color-danger)] font-medium'
                                : 'text-[var(--color-primary)]'} hover:underline`}
                              onClick={() => toggleRangeSelectionMode(book.id)}
                            >
                              {rangeSelectionMode[book.id] ? '退出范围选择' : '范围选择'}
                            </button>
                          </div>
                        </div>

                        {/* 范围选择提示 */}
                        {rangeSelectionMode[book.id] && (
                          <div className="mb-2 p-2 bg-[var(--color-primary)] bg-opacity-10 rounded-lg text-xs text-[var(--color-primary)]">
                            {!rangeStartChapter[book.id]
                              ? '请选择起始章节'
                              : '请选择结束章节，将自动选中范围内所有章节'}
                          </div>
                        )}

                        <div className="space-y-1 max-h-40 overflow-y-auto pl-2">
                          {book.chapters.length > 0 ? (
                            book.chapters.map(chapter => (
                              <div key={chapter.id} className="flex items-center">
                                <input
                                  type="checkbox"
                                  id={`chapter-${chapter.id}`}
                                  checked={(selectedChapters[book.id] || []).includes(chapter.id)}
                                  onChange={() => toggleChapterSelection(book.id, chapter.id)}
                                  className="mr-2 accent-[var(--color-primary)]"
                                />
                                <label
                                  htmlFor={`chapter-${chapter.id}`}
                                  className={`text-sm ${
                                    rangeSelectionMode[book.id] && rangeStartChapter[book.id] === chapter.id
                                      ? 'text-[var(--color-primary)] font-medium'
                                      : currentEditorBookId === book.id
                                        ? 'text-[var(--color-primary)] font-medium'
                                        : 'text-[var(--color-text-primary)]'
                                  } hover:text-[var(--color-primary)]`}
                                >
                                  {chapter.title}
                                  {rangeSelectionMode[book.id] && rangeStartChapter[book.id] === chapter.id &&
                                    ' (起始章节)'}
                                </label>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-2 text-[var(--color-text-hint)] text-sm">
                              该书籍暂无章节
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 bg-white rounded-xl text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[var(--color-text-hint)] mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <p className="text-[var(--color-text-secondary)]">暂无可用书籍</p>
                <p className="text-xs text-[var(--color-text-hint)] mt-1">请先创建或导入书籍</p>
              </div>
            )}
          </div>

          {/* 提示词管理区域 */}
          <div className="p-4 border-t border-[var(--color-secondary)] border-opacity-30">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-[var(--color-primary)]">提示词管理</h3>
              <div className="flex space-x-2">
                <button
                  className={`px-3 py-1 text-sm rounded-full transition-all ${!showCustomPrompt ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-30 text-[var(--color-text-primary)]'}`}
                  onClick={switchToSelectPrompt}
                >
                  选择提示词
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-full transition-all ${showCustomPrompt ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-30 text-[var(--color-text-primary)]'}`}
                  onClick={switchToCustomPrompt}
                >
                  自定义提示词
                </button>
              </div>
            </div>

            {/* 提示词内容区域 */}
            {showCustomPrompt ? (
              <div>
                <textarea
                  className="w-full p-3 border border-[var(--color-secondary)] border-opacity-50 rounded-xl mb-3 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                  rows={4}
                  value={promptTemplate}
                  onChange={(e) => setPromptTemplate(e.target.value)}
                  placeholder="请输入自定义提示词..."
                />
                <div className="flex justify-end">
                  <button
                    className="px-4 py-1.5 bg-[var(--color-success)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all"
                    onClick={openSaveTemplateDialog}
                  >
                    保存模板
                  </button>
                </div>
              </div>
            ) : (
              <div>
                {selectedPromptTemplate ? (
                  <div className="border border-[var(--color-secondary)] border-opacity-40 p-3 rounded-xl mb-3 bg-white shadow-sm">
                    <h4 className="font-medium text-[var(--color-primary)]">{selectedPromptTemplate.name}</h4>
                    <p className="text-xs text-[var(--color-text-secondary)] mb-2">{selectedPromptTemplate.description}</p>
                    <div className="text-xs bg-[var(--color-primary-bg)] p-3 rounded-lg max-h-24 overflow-y-auto">
                      {selectedPromptTemplate.content}
                    </div>
                  </div>
                ) : (
                  <div className="text-center p-6 bg-[var(--color-primary-bg)] rounded-xl">
                    <p className="text-[var(--color-text-hint)]">请点击"选择提示词"按钮选择提示词模板</p>
                  </div>
                )}
                <div className="flex justify-end">
                  <button
                    className="px-4 py-1.5 bg-[var(--color-info)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all"
                    onClick={() => setIsPromptManagerOpen(true)}
                  >
                    浏览模板
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 分析模式选择区域 */}
          <div className="p-4 border-t border-[var(--color-secondary)] border-opacity-30">
            <h3 className="text-lg font-semibold mb-3 text-[var(--color-primary)]">拆解模式</h3>
            <div className="grid grid-cols-2 gap-3 mb-4">
              <button
                className={`p-2 rounded-xl text-center transition-all ${analysisMode === 'single' ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-20 text-[var(--color-text-primary)] hover:bg-opacity-30'}`}
                onClick={() => setAnalysisMode('single')}
              >
                单章拆
              </button>
              <button
                className={`p-2 rounded-xl text-center transition-all ${analysisMode === 'merged' ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-20 text-[var(--color-text-primary)] hover:bg-opacity-30'}`}
                onClick={() => setAnalysisMode('merged')}
              >
                合并拆
              </button>
              <button
                className={`p-2 rounded-xl text-center transition-all ${analysisMode === 'mixed' ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-20 text-[var(--color-text-primary)] hover:bg-opacity-30'}`}
                onClick={() => setAnalysisMode('mixed')}
              >
                混合章节
              </button>
              <button
                className={`p-2 rounded-xl text-center transition-all ${analysisMode === 'sync' ? 'bg-[var(--color-primary)] text-white shadow-sm' : 'bg-[var(--color-secondary)] bg-opacity-20 text-[var(--color-text-primary)] hover:bg-opacity-30'}`}
                onClick={() => setAnalysisMode('sync')}
              >
                同步拆解
              </button>
            </div>

            {/* 分析按钮 */}
            {isAnalyzing ? (
              <button
                className="w-full px-4 py-3 bg-[var(--color-danger)] text-white rounded-xl hover:bg-opacity-90 flex items-center justify-center shadow-sm transition-all"
                onClick={handleCancelAnalysis}
              >
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                取消分析
              </button>
            ) : (
              <button
                className="w-full px-4 py-3 bg-[var(--color-primary)] text-white rounded-xl hover:bg-opacity-90 disabled:bg-opacity-50 disabled:cursor-not-allowed shadow-sm transition-all"
                onClick={handleAnalyze}
                disabled={selectedBooks.length === 0}
              >
                开始分析
              </button>
            )}
          </div>
        </div>

        {/* 右侧栏 - Markdown显示区域 */}
        <div className="w-2/3 flex flex-col overflow-hidden bg-[var(--color-editor-bg)]">
          <div className="p-4 border-b border-[var(--color-secondary)] border-opacity-30 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-[var(--color-primary)]">分析结果</h3>
            {analysisResult && !isAnalyzing && (
              <div className="flex space-x-2">
                <button
                  className="px-4 py-1.5 bg-[var(--color-info)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center"
                  onClick={() => {
                    navigator.clipboard.writeText(analysisResult)
                      .then(() => {
                        setSuccessMessage('已复制到剪贴板');
                        setTimeout(() => setSuccessMessage(null), 3000);
                      })
                      .catch(err => {
                        console.error('复制失败:', err);
                        setErrorMessage('复制失败');
                      });
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                  复制结果
                </button>
                <button
                  className="px-4 py-1.5 bg-[var(--color-primary)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center"
                  onClick={openContinueDialog}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  继续对话
                </button>
                <button
                  className="px-4 py-1.5 bg-[var(--color-success)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all"
                  onClick={exportAnalysisResult}
                >
                  导出结果
                </button>
                <button
                  className="px-4 py-1.5 bg-[var(--color-danger)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center"
                  onClick={clearAnalysisHistory}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  清空历史
                </button>
              </div>
            )}
          </div>
          <div className="flex-1 p-6 overflow-auto">
            {isAnalyzing && !analysisResult ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[var(--color-primary)] mb-6"></div>
                <span className="text-lg text-[var(--color-text-primary)]">分析中，请稍候...</span>
                <span className="text-sm text-[var(--color-text-hint)] mt-2">这可能需要一些时间，取决于内容的长度和复杂度</span>
              </div>
            ) : isAnalyzing && analysisResult ? (
              <div className="prose max-w-none font-[var(--font-family)]">
                {/* 流式输出内容 */}
                <div className="relative">
                  <div className="absolute top-0 right-0 p-2 z-10">
                    <div className="animate-pulse flex items-center">
                      <div className="h-3 w-3 bg-[var(--color-primary)] rounded-full mr-2"></div>
                      <span className="text-xs text-[var(--color-primary)]">生成中...</span>
                    </div>
                  </div>
                  {/* 使用增强的Markdown渲染组件 */}
                  <div className="pt-8">
                    <EnhancedMarkdown content={analysisResult} className="enhanced-markdown" htmlMode={false} allowScripts={false} />
                  </div>
                </div>
              </div>
            ) : analysisResult ? (
              showChatView ? (
                // 对话气泡界面
                <div className="flex flex-col space-y-4">
                  {conversationHistory.map((message, index) => (
                    <div
                      key={index}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-xl ${
                          message.role === 'user'
                            ? 'bg-[var(--color-primary)] text-white rounded-tr-none shadow-md'
                            : 'bg-[var(--color-bg-secondary)] text-[var(--color-text-primary)] rounded-tl-none shadow-sm border border-[var(--color-border)]'
                        }`}
                      >
                        {message.role === 'assistant' ? (
                          <EnhancedMarkdown content={message.content} className="enhanced-markdown" htmlMode={false} allowScripts={false} />
                        ) : (
                          <p className="whitespace-pre-wrap">{message.content}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                // 标准Markdown视图
                <div className="prose max-w-none font-[var(--font-family)]">
                  {/* 使用增强的Markdown渲染组件 */}
                  <EnhancedMarkdown content={analysisResult} className="enhanced-markdown" htmlMode={false} allowScripts={false} />
                </div>
              )
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 mb-6 text-[var(--color-secondary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-lg text-[var(--color-text-primary)]">分析结果将显示在这里</p>
                <p className="text-sm mt-2 text-[var(--color-text-hint)]">选择书籍和章节，然后点击"开始分析"按钮</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 提示词模板管理器 */}
      {isPromptManagerOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-[var(--color-primary-bg)] p-6 rounded-2xl w-full max-w-2xl max-h-[80vh] overflow-auto shadow-xl">
            <h3 className="text-xl font-bold mb-4 text-[var(--color-primary)]">提示词模板管理</h3>

            {/* 搜索框和操作按钮 */}
            <div className="flex items-center mb-4">
              <div className="relative flex-1 mr-2">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索提示词模板..."
                  className="w-full px-3 py-2 pl-10 border border-[var(--color-secondary)] border-opacity-50 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
              <button
                className="px-4 py-2 bg-[var(--color-primary)] text-white rounded-lg hover:bg-opacity-90 transition-all shadow-sm"
                onClick={openSaveTemplateDialog}
              >
                新建模板
              </button>
            </div>

            <div className="mb-6">
              <p className="text-sm text-[var(--color-text-secondary)] mb-4">
                选择一个提示词模板使用，或者点击编辑/删除按钮管理模板。
              </p>

              {filteredPromptTemplates.length === 0 ? (
                <div className="text-center p-8 bg-white rounded-xl">
                  <p className="text-[var(--color-text-hint)] italic">
                    {searchQuery ? `没有找到包含"${searchQuery}"的提示词模板` : '当前分析模式没有可用的提示词模板'}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredPromptTemplates.map(template => (
                    <div
                      key={template.id}
                      className="border border-[var(--color-secondary)] border-opacity-40 p-3 rounded-xl hover:bg-white transition-all shadow-sm hover:shadow-md"
                    >
                      <div className="flex justify-between items-start">
                        <div className="cursor-pointer" onClick={() => handleSelectTemplate(template)}>
                          <h4 className="font-medium text-[var(--color-primary)]">{template.name}</h4>
                          <p className="text-sm text-[var(--color-text-secondary)]">{template.description}</p>
                          <div className="mt-2 flex flex-wrap gap-1.5">
                            {template.tags && template.tags.map(tag => (
                              <span key={tag} className="text-xs bg-white border border-[var(--color-primary)] text-[var(--color-primary)] px-2 py-0.5 rounded-full">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            className="p-1 text-blue-500 hover:text-blue-700 transition-colors"
                            onClick={() => handleSelectTemplate(template)}
                            title="使用此模板"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </button>
                          <button
                            className="p-1 text-green-500 hover:text-green-700 transition-colors"
                            onClick={() => editTemplate(template)}
                            title="编辑此模板"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            className="p-1 text-red-500 hover:text-red-700 transition-colors"
                            onClick={() => deleteTemplate(template.id!)}
                            title="删除此模板"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-700 max-h-32 overflow-y-auto">
                        <pre className="whitespace-pre-wrap">{template.content}</pre>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-[var(--color-secondary)] text-[var(--color-text-primary)] rounded-lg hover:bg-opacity-80 transition-all shadow-sm"
                onClick={() => setIsPromptManagerOpen(false)}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 保存模板对话框 */}
      {isSaveTemplateDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-[var(--color-primary-bg)] p-6 rounded-2xl w-full max-w-md shadow-xl">
            <h3 className="text-xl font-bold mb-4 text-[var(--color-primary)]">
              {isEditMode ? '编辑提示词模板' : '保存提示词模板'}
            </h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-2">
                模板名称
              </label>
              <input
                type="text"
                className="w-full p-3 border border-[var(--color-secondary)] border-opacity-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                value={newTemplateName}
                onChange={(e) => setNewTemplateName(e.target.value)}
                placeholder="请输入模板名称"
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-2">
                模板描述
              </label>
              <input
                type="text"
                className="w-full p-3 border border-[var(--color-secondary)] border-opacity-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                value={newTemplateDescription}
                onChange={(e) => setNewTemplateDescription(e.target.value)}
                placeholder="请输入模板描述"
              />
            </div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-2">
                模板内容
              </label>
              <textarea
                className="w-full p-3 border border-[var(--color-secondary)] border-opacity-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                rows={6}
                value={isEditMode ? promptTemplate : newTemplateContent || promptTemplate}
                onChange={(e) => {
                  if (isEditMode) {
                    setPromptTemplate(e.target.value);
                  } else {
                    setNewTemplateContent(e.target.value);
                  }
                }}
                placeholder="请输入模板内容"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-[var(--color-secondary)] text-[var(--color-text-primary)] rounded-lg hover:bg-opacity-80 transition-all shadow-sm"
                onClick={() => {
                  setIsSaveTemplateDialogOpen(false);
                  if (isEditMode) {
                    setIsEditMode(false);
                    setEditingTemplateId(null);
                  }
                }}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-[var(--color-success)] text-white rounded-lg hover:bg-opacity-90 transition-all shadow-sm"
                onClick={saveCurrentTemplate}
              >
                {isEditMode ? '更新' : '保存'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 继续对话弹窗 */}
      {isContinueDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-[var(--color-primary-bg)] p-6 rounded-2xl w-full max-w-md shadow-xl">
            <h3 className="text-xl font-bold mb-4 text-[var(--color-primary)]">继续对话</h3>
            <div className="mb-6">
              <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-2">
                请输入您的问题或要求
              </label>
              <textarea
                className="w-full p-3 border border-[var(--color-secondary)] border-opacity-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-opacity-30"
                rows={4}
                value={continuePrompt}
                onChange={(e) => setContinuePrompt(e.target.value)}
                placeholder="请输入您想要AI继续分析的内容..."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-[var(--color-secondary)] text-[var(--color-text-primary)] rounded-lg hover:bg-opacity-80 transition-all shadow-sm"
                onClick={() => setIsContinueDialogOpen(false)}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-[var(--color-primary)] text-white rounded-lg hover:bg-opacity-90 transition-all shadow-sm"
                onClick={continueConversation}
              >
                发送
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};