"use client";

import { ConversationMessage } from '@/factories/ai/services/AIWritingService';
import { AIContinuePrompts } from '@/utils/ai/prompts/AIContinuePrompts';
import {
  DualAIConfig,
  AIModelType,
  AIFunctionType,
  AITaskType
} from '@/types/DualAIConfig';
import { DualAIService } from './DualAIService';

// 定义消息类型
interface Message {
  role: string;
  content: string;
}

/**
 * 对话继续模式
 */
export type ContinueMode = 'new' | 'continue' | 'rewrite' | 'analyze';

/**
 * 服务类型
 */
export type ServiceType = 'writing' | 'rewrite';

/**
 * 关联元素类型
 */
export interface RelatedElement {
  id?: string;
  name?: string;
  title?: string;
  content?: string;
  description?: string;
  [key: string]: any; // 允许其他属性
}

/**
 * 对话继续参数
 */
export interface DialogContinuationParams {
  // 当前内容
  currentContent: string;

  // 用户的继续提示
  continuePrompt: string;

  // 继续模式
  continueMode: ContinueMode;

  // 书籍ID（用于本地存储）
  bookId: string;

  // 现有对话历史
  conversationHistory?: ConversationMessage[];

  // 服务类型（writing 或 rewrite）
  serviceType?: ServiceType;

  // 关联元素
  chapters?: RelatedElement[];
  selectedChapterIds?: string[];
  characters?: RelatedElement[];
  selectedCharacterIds?: string[];
  terminologies?: RelatedElement[];
  selectedTerminologyIds?: string[];
  worldBuildings?: RelatedElement[];
  selectedWorldBuildingIds?: string[];
  currentChapterId?: string; // 当前章节ID，用于排除当前章节

  // 直接传递的上下文状态信息（避免重复解析）
  hasBeforeContext?: boolean;
  hasAfterContext?: boolean;
  beforeContextLength?: number;
  afterContextLength?: number;

  // 衔接关键信息（用于确保连贯性）
  beforeContextEnd?: string;  // 前文结尾几句话
  afterContextStart?: string; // 后文开头几句话

  // 大纲关联元素
  outlines?: any[];
  selectedOutlineIds?: string[];
  selectedOutlineNodeIds?: string[];
  outlineContextMode?: 'selected' | 'hierarchy' | 'full';

  // 上下文信息
  context?: string;
  beforeContext?: string;
  afterContext?: string;

  // 预构建的上下文消息（新增）
  contextMessages?: ConversationMessage[];

  // 选中的文本
  selectedText?: string;

  // 改写要求
  rewriteRequirements?: string;

  // 续写要求
  continueRequirements?: string;

  // 续写风格
  continueStyle?: string;

  // 剧情方向
  plot?: string;

  // 后续剧情
  futurePlot?: string;

  // 双AI协同相关字段
  // AI模型选择相关
  preferredAIModel?: AIModelType; // 首选AI模型：'outline' | 'dialogue'
  forceAIModel?: AIModelType;     // 强制使用特定AI模型
  aiTaskType?: AITaskType;        // AI任务类型：'structure' | 'dialogue' | 'mixed'
  aiFunctionType?: AIFunctionType; // AI功能类型，用于智能模型选择

  // 双AI协同配置
  enableDualAI?: boolean;         // 是否启用双AI模式
  dualAIConfig?: DualAIConfig;    // 双AI配置信息
}

/**
 * 对话继续结果
 */
export interface DialogContinuationResult {
  // 更新后的对话历史
  updatedHistory: ConversationMessage[];

  // 构建的消息
  messages: Message[];

  // 调试信息
  debug: {
    historyLength: number;
    messagesLength: number;
    relatedMessagesCount: number;
  };
}

/**
 * 对话继续管理器 - 全新简化版
 *
 * 这个类负责处理继续对话的逻辑，采用极简设计：
 * 1. 保留系统消息
 * 2. 添加必要的上下文信息
 * 3. 添加用户提示
 * 4. 构建API请求消息
 */
export class DialogContinuationManager {
  /**
   * 处理继续对话
   * @param params 继续对话参数
   * @returns 继续对话结果
   */
  public async handleContinuation(params: DialogContinuationParams): Promise<DialogContinuationResult> {
    console.log(`[DialogContinuationManager] 开始处理${params.serviceType}继续对话，模式:`, params.continueMode);

    // 创建一个新的历史数组
    let updatedHistory: ConversationMessage[] = [];

    // 1. 计算正确的上下文状态（用于系统提示词生成）
    let hasBeforeContextForSystem = !!(params.context || params.beforeContext || params.hasBeforeContext);
    let hasAfterContextForSystem = !!(params.afterContext || params.hasAfterContext);

    // 特殊处理：如果是跨章节衔接模式，强制设为衔接模式
    if (params?.context && params.context.includes('跨章节衔接模式')) {
      hasAfterContextForSystem = true;
      console.log('DialogContinuationManager 系统提示词：检测到跨章节衔接模式，使用衔接模式系统提示词');
    }

    // 2. 使用修正后的状态生成智能系统提示词
    const smartSystemPrompt = AIContinuePrompts.generateSmartSystemPrompt(
      hasBeforeContextForSystem,
      hasAfterContextForSystem
    );
    updatedHistory.push({
      role: 'system',
      content: smartSystemPrompt
    });

    // 2. 添加必要的上下文信息（前文、后文、章节等）
    this.addContextMessages(updatedHistory, params);

    // 3. 添加衔接关键信息（确保连贯性）
    this.addConnectionKeyInfo(updatedHistory, params, hasBeforeContextForSystem, hasAfterContextForSystem);

    // 3. 添加关联元素信息（人物、术语、世界观等）- 确保每次都添加，无论是初始化还是续写
    await this.addRelatedElementMessages(updatedHistory, params);

    // 4. 添加当前内容作为助手消息（放在上下文和关联元素之后）
    if (params.currentContent) {
      updatedHistory.push({
        role: 'assistant',
        content: params.currentContent
      });
    }

    // 5. 构建用户提示（传递params用于智能指令生成）
    const userPrompt = this.buildUserPrompt(params.continuePrompt, params.continueMode, params);

    // 6. 添加用户提示作为用户消息
    updatedHistory.push({
      role: 'user',
      content: userPrompt
    });

    // 7. 构建API请求消息
    const messages = updatedHistory.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // 8. 保存到本地存储
    this.saveToLocalStorage(params.bookId, params.serviceType || 'writing', updatedHistory);

    // 9. 返回结果
    return {
      updatedHistory,
      messages,
      debug: {
        historyLength: updatedHistory.length,
        messagesLength: messages.length,
        relatedMessagesCount: 0
      }
    };
  }
// ... (您的其他代码)

  /**
   * 添加上下文消息 (导演版)
   * @param history 对话历史
   * @param params 继续对话参数
   */
  private addContextMessages(history: ConversationMessage[], params: DialogContinuationParams): void {
    // 优先使用预构建的上下文消息 (导演版：优先使用完整的场记报告)
    if (params.contextMessages && params.contextMessages.length > 0) {
      params.contextMessages.forEach(msg => {
        history.push({
          role: msg.role,
          content: msg.content
        });
      });
      return;
    }

    // 兼容旧的 context 字符串格式（临时保留）(导演版：兼容旧的剧本草稿)
    if (params.context && params.context.trim()) {
      // 简单处理：将整个上下文作为一个消息
      history.push({
        role: 'user',
        content: params.context
      });
    }

    // 处理单独的前文和后文（如果有）(导演版：处理独立的已拍和待拍素材)
    if (params.beforeContext && params.beforeContext.trim()) {
      history.push({
        role: 'user',
        content: `【已拍板素材】\n${params.beforeContext}`
      });
    }

    if (params.afterContext && params.afterContext.trim()) {
      history.push({
        role: 'user',
        content: `【待拍板素材】\n${params.afterContext}`
      });
    }

    // 添加选中的文本（如果有）(导演版：添加核心创意/概念)
    if (params.selectedText && params.selectedText.trim()) {
      history.push({
        role: 'user',
        content: `【本场戏核心概念】\n\n${params.selectedText}`
      });
    }

    // 添加改写要求（如果有）(导演版：添加重拍/改写要求)
    if (params.rewriteRequirements && params.rewriteRequirements.trim()) {
      history.push({
        role: 'user',
        content: `【重拍/改写要求】\n\n${params.rewriteRequirements}`
      });
    }

    // 添加剧情方向（如果有）(导演版)
    if (params.plot && params.plot.trim()) {
      history.push({
        role: 'user',
        content: `【剧本走向】\n\n${params.plot}`
      });
    }
  }

  /**
   * 添加衔接关键信息（确保连贯性）(导演版：确保剪辑点流畅)
   * @param history 对话历史
   * @param params 继续对话参数
   * @param hasBeforeContext 是否有前文
   * @param hasAfterContext 是否有后文
   */
  private addConnectionKeyInfo(
    history: ConversationMessage[],
    params: DialogContinuationParams,
    hasBeforeContext: boolean,
    hasAfterContext: boolean
  ): void {
    // 只在续写或衔接模式时添加衔接关键信息 (导演版：只在顺拍或补拍时强调剪辑点)
    if (!hasBeforeContext && !hasAfterContext) {
      return;
    }

    // 添加前文结尾信息（用于衔接）(导演版：强调上一个镜头的收尾)
    if (hasBeforeContext && params.beforeContextEnd) {
      history.push({
        role: 'user',
        content: `【上个镜头的收尾】\n这是上一幕的最后几句台词/动作，确保新镜头能从这里自然接上：\n\n${params.beforeContextEnd}`
      });
      history.push({
        role: 'assistant',
        content: `收到。已确认上个镜头的收尾，将确保表演和情节的连贯性。`
      });
    }

    // 添加后文开头信息（用于衔接）(导演版：强调下一个镜头的开场)
    if (hasAfterContext && params.afterContextStart) {
      history.push({
        role: 'user',
        content: `【下个镜头的开场】\n这是下一幕的开场，确保当前的拍摄能无缝过渡到这里：\n\n${params.afterContextStart}`
      });
      history.push({
        role: 'assistant',
        content: `收到。已确认下个镜头的开场，将确保拍摄内容能与之完美衔接，实现无缝转场。`
      });
    }

    // 如果同时有前文和后文，添加特别强调 (导演版：强调补拍任务)
    if (hasBeforeContext && hasAfterContext && (params.beforeContextEnd || params.afterContextStart)) {
      history.push({
        role: 'user',
        content: `【补拍任务强调】\n这是一场补拍任务，需要连接前后两个已知镜头。特别注意：\n1. 从上个镜头的收尾自然开始\n2. 向下个镜头的开场流畅过渡\n3. 绝对禁止重拍已知镜头的内容\n4. 保持情节、表演和风格的绝对一致`
      });
      history.push({
        role: 'assistant',
        content: `完全明白。这是补拍任务，将严格按照要求创作中间的过渡戏份，确保剪辑流畅，天衣无缝。`
      });
    }
  }

  /**
   * 添加关联元素消息 (导演版)
   * @param history 对话历史
   * @param params 继续对话参数
   */
  private async addRelatedElementMessages(history: ConversationMessage[], params: DialogContinuationParams): Promise<void> {
    // 【新增】添加用户指令消息组（在关联元素之前，确保高权重）(导演版)
    // 1. 续写要求 -> 导演备忘录
    if (params.continueRequirements && params.continueRequirements.trim()) {
      history.push({
        role: 'user',
        content: `【导演备忘录】\n${params.continueRequirements}`
      });
      history.push({
        role: 'assistant',
        content: `收到导演备忘录，将严格按照这些要求执行拍摄。`
      });
    }

    // 2. 续写风格 -> 影片风格
    if (params.continueStyle && params.continueStyle.trim()) {
      history.push({
        role: 'user',
        content: `【影片风格】\n${params.continueStyle}`
      });
      history.push({
        role: 'assistant',
        content: `已明确影片风格，拍摄时将保持这种调性。`
      });
    }

    // 3. 剧情方向（支持两个字段：plot和futurePlot）-> 剧本走向
    const plotContent = params.futurePlot || params.plot;
    if (plotContent && plotContent.trim()) {
      history.push({
        role: 'user',
        content: `【剧本走向】\n${plotContent}`
      });
      history.push({
        role: 'assistant',
        content: `已明确剧本走向，将朝着这个方向推进情节。`
      });
    }
    // 添加关联人物（如果有）- 每个人物作为单独的消息 (导演版)
    if (params.characters && params.characters.length > 0 && params.selectedCharacterIds && params.selectedCharacterIds.length > 0) {
      const selectedCharacters = params.characters.filter(char =>
        char.id && params.selectedCharacterIds?.includes(char.id)
      );

      if (selectedCharacters.length > 0) {
        // 为每个人物添加单独的消息
        selectedCharacters.forEach(char => {
          history.push({
            role: 'user',
            content: `【演员档案】\n\n- ${char.name}${char.description ? ` (角色小传: ${char.description})` : ''}`
          });
        });
      }
    }

    // 添加关联术语（如果有）- 每个术语作为单独的消息 (导演版)
    if (params.terminologies && params.terminologies.length > 0 && params.selectedTerminologyIds && params.selectedTerminologyIds.length > 0) {
      const selectedTerms = params.terminologies.filter(term =>
        term.id && params.selectedTerminologyIds?.includes(term.id)
      );

      if (selectedTerms.length > 0) {
        // 为每个术语添加单独的消息
        selectedTerms.forEach(term => {
          history.push({
            role: 'user',
            content: `【关键道具/术语卡】\n\n- ${term.name}${term.description ? ` (说明: ${term.description})` : ''}`
          });
        });
      }
    }

    // 添加关联世界观（如果有）- 每个世界观作为单独的消息 (导演版)
    if (params.worldBuildings && params.worldBuildings.length > 0 && params.selectedWorldBuildingIds && params.selectedWorldBuildingIds.length > 0) {
      const selectedWorldViews = params.worldBuildings.filter(wb =>
        wb.id && params.selectedWorldBuildingIds?.includes(wb.id)
      );

      if (selectedWorldViews.length > 0) {
        // 为每个世界观添加单独的消息
        selectedWorldViews.forEach(wb => {
          history.push({
            role: 'user',
            content: `【世界观设定】\n\n- ${wb.name}${wb.description ? ` (详情: ${wb.description})` : ''}`
          });
        });
      }
    }

    // 添加关联章节（如果有）- 每个章节作为单独的消息，带有更多元数据 (导演版)
    if (params.chapters && params.chapters.length > 0 && params.selectedChapterIds && params.selectedChapterIds.length > 0) {
      const selectedChapters = params.chapters.filter(chapter =>
        chapter.id && params.selectedChapterIds?.includes(chapter.id) && chapter.id !== params.currentChapterId
      );

      if (selectedChapters.length > 0) {
        // 添加关联章节概述，强调完整内容的重要性 (导演版)
        history.push({
          role: 'user',
          content: `【参考场景概述】\n共有${selectedChapters.length}个参考场景与当前拍摄内容相关。请仔细审阅每个场景的完整剧本，理解其中的情节发展、人物互动和世界设定，确保你的拍摄与这些场景保持连贯。这些完整的参考场景对于维持影片的整体性至关重要。`
        });

        // 为每个章节添加单独的消息，包含更多元数据 (导演版)
        selectedChapters.forEach((chapter, index) => {
          // 计算章节内容的字数和段落数（如果有内容）
          let contentInfo = '';
          let contentMessage = '';
          if (chapter.content) {
            const paragraphs = chapter.content.split(/\n\n+/);
            const charCount = chapter.content.length;
            contentInfo = `\n剧本长度：${charCount}字\n分镜数：${paragraphs.length}`;

            // 保存完整的章节内容，稍后作为单独的消息发送
            contentMessage = chapter.content;
          }

          // 添加章节顺序信息（如果有）
          const orderInfo = chapter.order !== undefined ? `\n场次顺序：第${chapter.order + 1}场` : '';

          // 先发送章节元数据
          history.push({
            role: 'user',
            content: `【参考场景${index + 1}】\n场景名称：${chapter.name}${orderInfo}${contentInfo}${chapter.description ? `\n场景梗概：${chapter.description}` : ''}`
          });

          // 如果有章节内容，作为单独的消息发送完整内容
          if (contentMessage) {
            history.push({
              role: 'user',
              content: `【参考场景${index + 1} - 完整剧本】\n\n${contentMessage}`
            });
          }
        });
      }
    }
  
    // 添加关联大纲节点（如果有）- 使用统一的单节点单条目模式
    if (params.outlines && params.outlines.length > 0 && params.selectedOutlineNodeIds && params.selectedOutlineNodeIds.length > 0) {
      console.log('🔍 DialogContinuationManager 处理大纲数据:', {
        hasOutlines: !!params.outlines,
        outlinesLength: params.outlines?.length || 0,
        selectedNodeIds: params.selectedOutlineNodeIds
      });

      // 导入PromptHelperService来处理大纲数据（使用统一的单节点单条目模式）
      try {
        const { createPromptHelperService } = await import('../../factories/ai/services/PromptHelperService');
        const { MessageBuilder } = await import('../ai/MessageBuilder');

        const promptHelper = createPromptHelperService();
        const messageBuilder = new MessageBuilder();

        // 使用统一的大纲添加方法，与AI写作功能保持一致（单节点单条目模式）
        promptHelper.addSelectedOutlineNodes(
          messageBuilder,
          params.outlines,
          params.selectedOutlineNodeIds,
          params.outlineContextMode || 'hierarchy'
        );

        // 将构建的消息添加到历史记录中
        const outlineMessages = messageBuilder.build();
        for (const message of outlineMessages) {
          history.push({
            role: message.role as 'user' | 'assistant' | 'system',
            content: message.content
          });
        }

        console.log('✅ 大纲数据已添加到对话历史（统一模式）');
      } catch (error) {
        console.error('❌ 处理大纲数据失败:', error);
      }
    }
  }

  /**
   * 构建用户提示 - 使用智能指令
   * @param continuePrompt 继续提示
   * @param continueMode 继续模式
   * @param params 对话继续参数（用于智能指令生成）
   * @returns 构建后的用户提示
   */
  private buildUserPrompt(continuePrompt: string, continueMode: ContinueMode, params?: DialogContinuationParams): string {
    // 对于续写模式，使用智能指令
    if (continueMode === 'continue') {
      let hasBeforeContext = false;
      let hasAfterContext = false;
      let beforeContextLength = 0;
      let afterContextLength = 0;

      // 优先使用直接传递的状态信息（避免重复解析）
      if (params?.hasBeforeContext !== undefined || params?.hasAfterContext !== undefined) {
        hasBeforeContext = params.hasBeforeContext || false;
        hasAfterContext = params.hasAfterContext || false;
        beforeContextLength = params.beforeContextLength || 0;
        afterContextLength = params.afterContextLength || 0;

        // 特殊处理：如果上下文中明确标注了"跨章节衔接模式"，强制使用衔接模式
        if (params?.context && params.context.includes('跨章节衔接模式')) {
          hasAfterContext = true; // 强制设为衔接模式
          console.log('DialogContinuationManager 检测到跨章节衔接模式，强制使用衔接模式指令');
        }

        console.log('DialogContinuationManager 使用直接传递的状态信息:', {
          hasBeforeContext,
          hasAfterContext,
          beforeContextLength,
          afterContextLength,
          isForceConnectionMode: params?.context?.includes('跨章节衔接模式')
        });
      } else {
        // 如果没有直接传递状态信息，回退到解析逻辑
        console.log('DialogContinuationManager: 没有直接状态信息，使用默认值');
        hasBeforeContext = false;
        hasAfterContext = false;
        beforeContextLength = 0;
        afterContextLength = 0;
      }

      // 使用智能指令生成器，传递衔接关键信息
      const smartInstruction = AIContinuePrompts.generateSmartFinalInstruction(
        hasBeforeContext,
        hasAfterContext,
        beforeContextLength,
        afterContextLength,
        params?.beforeContextEnd,  // 前文结尾
        params?.afterContextStart  // 后文开头
      );

    // ... (您的其他代码)

      // 如果有用户自定义的续写要求，将其添加到智能指令前面 (导演版)
      // 这相当于制片人或监制亲临现场下达的最高优先级指令
      if (continuePrompt && continuePrompt.trim() && !continuePrompt.includes('请继续上述内容')) {
        return `【制片人特别指示】\n${continuePrompt}\n\n${smartInstruction}`;
      } else {
        return smartInstruction;
      }
    }

    // 对于其他模式，使用原有逻辑
    const hasContinuePrefix = continuePrompt.includes('请继续上述内容');
    const hasRewritePrefix = continuePrompt.includes('请重写上述内容');
    const hasAnalyzePrefix = continuePrompt.includes('请分析上述内容');

    switch (continueMode) {
      case 'continue':
        return hasContinuePrefix ? continuePrompt : `请继续上述内容，保持一致的风格和情节走向。${continuePrompt}`;
      case 'rewrite':
        return hasRewritePrefix ? continuePrompt : `请重写上述内容，但保持核心情节不变。${continuePrompt}`;
      case 'analyze':
        return hasAnalyzePrefix ? continuePrompt : `请分析上述内容，提供深入的文学分析和建议。${continuePrompt}`;
      default:
        return continuePrompt;
    }
  }

  /**
   * 保存到本地存储
   * @param bookId 书籍ID
   * @param serviceType 服务类型
   * @param history 对话历史
   */
  private saveToLocalStorage(bookId: string, serviceType: ServiceType, history: ConversationMessage[]): void {
    if (typeof localStorage !== 'undefined' && bookId) {
      const historyKey = serviceType === 'writing'
        ? `ai-writing-history-${bookId}`
        : `ai-rewrite-history-${bookId}`;

      localStorage.setItem(historyKey, JSON.stringify(history));
    }
  }
}

// 导出单例实例
export const dialogContinuationManager = new DialogContinuationManager();
