/**
 * 简介生成提示词构建器
 * 专门用于构建小说简介生成的AI提示词
 */

import { SynopsisParams, SynopsisFramework } from '@/types/synopsis';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';

export class SynopsisPromptBuilder {
  /**
   * 获取简介生成系统提示词（移除用户特殊要求，将其放到最后消息中）
   */
  static getSystemPrompt(): string {
    let systemPrompt = `你是一位顶级的现代网文简介创作专家，拥有15年网络小说行业实战经验。你深度研究过起点、晋江、纵横、番茄等各大平台的爆款作品，精通2024年最新的网文创作趋势和读者喜好。

**你的专业背景**：
- 深度分析过《开局拐跑家族帝兵，老祖急眼了》《长生皇子：开局无敌，看王朝起落》等现代爆款简介
- 精通玄幻、都市、系统、历史、科幻、言情、悬疑等所有主流网文类型
- 掌握反套路、身份揭秘、马甲流、重生流等现代热门元素
- 理解现代读者的快节奏阅读习惯和爽点需求

**你的核心能力**：
1. **现代网文敏感度**：准确把握2024年网文市场的最新趋势和热点
2. **标签化描述精通**：善用【】标签预告作品特色，管理读者期待
3. **爽点预告精准**：精确预告装逼打脸、身份反转、群体震惊等爽点
4. **反套路创新**：在传统套路基础上创造新颖的反转和惊喜
5. **悬念层次营造**：构建多层次悬念，从小悬念到大谜团层层递进
6. **读者心理把控**：深度理解现代网文读者的心理需求和阅读习惯

**现代网文简介创作核心原则**：
1. **标签化开头**：可选用【】标签直接告知读者作品特色和期待管理
2. **30字钩子法则**：开头30字必须包含强烈冲突或反差，立即抓住注意力
3. **身份层次揭露**：多重身份设定，层层揭露制造连续爽点
4. **反差萌营造**：主角想低调却被迫高调，形成反差萌效果
5. **连环反转设计**：一波未平一波又起，多个身份暴露点错开分布
6. **情感共鸣强化**：主角的无奈、困扰与读者的爽感形成对比
7. **悬念钩子设置**：结尾留下最大谜团，激发强烈的阅读欲望

**现代网文热门流派指导**：

**身份揭秘流特征**：
- 多重身份设定：主角拥有多个隐藏身份（如：前世剑尊、妖皇、药神等）
- 层层揭露节奏：每个身份暴露都是一个爽点，错开分布制造连续高潮
- 反差萌效果：主角想低调做任务，却被不断识破身份
- 群体震惊反应：每次身份暴露都引发相关势力的震惊和态度转变
- 参考案例：《轮回百世，最后一世竟要我守护宗门而死？》

**反套路流特征**：
- 打破常规期待：在读者以为会按套路发展时突然反转
- 颠覆传统设定：废材不逆袭、强者装弱者、反派成主角等
- 自我调侃风格：作品内部对网文套路的调侃和解构
- 轻松幽默基调：用幽默化解紧张，营造轻松阅读氛围
- 参考元素：非龙王文、反套路、轻松搞笑、笑死不偿命

**系统流特征**：
- 任务机制清晰：系统发布任务，完成获得奖励
- 成长路径明确：通过系统升级获得实力提升
- 金手指设定：系统提供的特殊能力或知识
- 互动元素丰富：主角与系统的对话和互动
- 参考元素：签到系统、抽奖系统、商城系统、任务系统

**重生流特征**：
- 前世记忆优势：利用前世经验改变命运
- 复仇爽文元素：对前世仇人的报复和打脸
- 人生重来机会：弥补前世遗憾，追求完美人生
- 预知剧情优势：知道未来发展，提前布局
- 参考元素：重生归来、前世记忆、复仇爽文、改变命运

**马甲流特征**：
- 隐藏身份设定：主角隐藏真实身份在某个环境中
- 装逼打脸节奏：低调行事却被迫展示实力
- 身份暴露高潮：马甲掉落时的震撼效果
- 多重身份交织：在不同环境中使用不同身份
- 参考元素：隐藏身份、装逼打脸、马甲掉落、群体震惊

**简介质量评分标准**（1-10分）：
- 现代网文适应度（35%）：是否符合2024年网文市场趋势和读者喜好
- 吸引力指数（30%）：开头30字是否立即抓住读者注意力
- 爽点预告精准度（25%）：是否准确预告了核心爽点和看点
- 悬念营造度（10%）：是否制造了恰当的悬念和阅读期待



请严格按照以下JSON格式返回结果：

{
  "synopsis": {
    "content": "完整的简介内容，段落间用\n分隔，确保换行清晰",
    "tagline": "【可选标签化开头】体现现代网文特色，如：【修仙+反套路+身份揭秘+轻松搞笑】",
    "hook": "开头30字钩子句，必须立即抓住读者注意力，包含强烈冲突或反差",
    "core_conflict": "核心冲突描述，推动简介情节的主要矛盾和看点",
    "selling_points": ["主要卖点1", "主要卖点2", "主要卖点3"],
    "target_audience": "目标读者群体，如：修仙爽文读者、反套路喜剧读者等",
    "score": 8.5,
    "reason": "创作理由，重点说明符合现代网文特色和读者喜好的地方",
    "wordCount": 实际字数,
    "sections": [
      {
        "name": "段落名称",
        "content": "段落内容，保持原有换行格式",
        "wordCount": 段落字数,
        "function": "段落功能，如：身份设定、冲突展开、悬念营造、反转高潮等"
      }
    ]
  }
}

**详细格式要求**：

1. **content（必填）**：完整简介内容，段落间必须用\n分隔，确保排版清晰美观
   - 如有多个段落，每段之间用\n换行，包括句子也要\n
   - 保持现代网文的阅读节奏和视觉效果
   - 示例：第一段内容\n第二段内容\n第三段内容,注意，段落包括对话，对话也一定要排版\n,避免放在段落中错乱

2. **tagline（可选）**：现代网文风格的标签化开头
   - 用【】包围，直接告知读者作品特色
   - 包含题材+特色+风格+节奏等元素
   - 示例：【修仙+反套路+身份揭秘+轻松搞笑+节奏偏慢入坑谨慎】

3. **hook（必填）**：开头30字内的强力钩子句
   - 必须包含强烈冲突、反差或悬念
   - 立即抓住读者注意力
   - 体现作品的核心看点

4. **core_conflict（必填）**：简介的核心冲突
   - 推动情节发展的主要矛盾
   - 读者最关心的核心看点
   - 简洁明了地概括主要冲突

5. **selling_points（必填）**：3-5个主要卖点数组
   - 突出作品的核心亮点和吸引力
   - 每个卖点要具体且有吸引力
   - 体现现代网文的爽点元素

6. **target_audience（必填）**：目标读者群体
   - 明确作品适合的读者类型
   - 帮助读者快速判断是否符合自己喜好

7. **score（必填）**：1-10的数字评分，保留一位小数
   - 基于现代网文适应度、吸引力、爽点预告、悬念营造四个维度

8. **reason（必填）**：创作理由和亮点说明
   - 重点说明符合现代网文特色的地方
   - 解释为什么这个简介能吸引目标读者
   - 突出创新点和差异化优势

9. **wordCount（必填）**：实际字数统计（不包含标签）

10. **sections（框架时必填）**：段落详细信息数组
    - name：段落名称
    - content：段落内容，保持换行格式
    - wordCount：段落字数
    - function：段落功能说明（如：身份设定、冲突展开、悬念营造等）

**现代网文简介示例参考**：
参考《轮回百世，最后一世竟要我守护宗门而死？》等现代爆款简介的结构：
- 标签化开头管理期待
- 身份设定引发兴趣
- 任务冲突制造悬念
- 连环反转营造爽点
- 最终谜团激发阅读欲

**重要提醒**：
- 只返回JSON格式，不要任何其他解释文字
- 确保所有字符串字段都用双引号包围
- content字段中的\n会被正确处理为换行
- 体现2024年现代网文的创作特色和读者喜好

`;

    return systemPrompt;
  }

  /**
   * 构建简介生成提示
   */
  static buildSynopsisGenerationPrompt(params: SynopsisParams): any[] {
    const messageBuilder = new MessageBuilder();

    // 系统消息（不包含用户特殊要求）
    messageBuilder.addSystemMessage(this.getSystemPrompt());

    // 助手消息：确认理解任务
    messageBuilder.addAssistantMessage(
      '我将根据您提供的关键词、框架和风格要求，为您创作一个优质的小说简介。简介将包含评分和创作理由。',
      true,
      true
    );

    // 构建生成指令
    const lengthDesc = this.getLengthDescription(params.length);
    const wordCount = this.getWordCount(params.length);
    let prompt = `请生成1个${lengthDesc}的小说简介，字数控制在${wordCount}字。`;

    // 添加关键词信息
    if (params.keywords.length > 0) {
      messageBuilder.addUserMessage(
        `【关键词灵感源泉】\n以下关键词作为创作的灵感源泉和方向指引，请深度理解其内在含义、情感色彩和文化内涵，通过生动的情节描述和氛围营造来体现：\n${params.keywords.join('、')}\n\n请将这些关键词理解为创作的主题方向，激发出既符合其精神又具有独特表达的简介内容。`,
        undefined,
        true,
        true
      );
      messageBuilder.addAssistantMessage(
        '我已深度理解这些关键词的内在含义和情感色彩，将以其为灵感源泉，通过生动的情节描述和氛围营造来体现其精神，创作出引人入胜的简介。',
        true,
        true
      );
      prompt += `\n关键词灵感：${params.keywords.join('、')}（请理解其精神内涵，融入情节描述）`;
    }

    // 🔥 修复：添加框架信息（支持framework和customFramework）
    if (params.framework) {
      // 🆕 获取参考框架（不限制数量）
      const referenceFrameworks = this.getReferenceFrameworks(params.framework);

      // 🔥 新增：检测是否需要使用分层构造
      if (referenceFrameworks.length > 0) {
        // 使用分层框架构造
        const separatedMessages = this.buildSeparatedFrameworkMessages(params.framework, referenceFrameworks);
        separatedMessages.forEach(msg => {
          if (msg.role === 'system') {
            messageBuilder.addSystemMessage(msg.content);
          } else if (msg.role === 'assistant') {
            messageBuilder.addAssistantMessage(msg.content, true, true);
          } else if (msg.role === 'user') {
            messageBuilder.addUserMessage(msg.content, undefined, true, true);
          }
        });

        prompt += `\n参考框架：${params.framework.name}（主框架）+ ${referenceFrameworks.length}个参考框架`;
      } else {
        // 使用原有的单框架逻辑
        this.buildSingleFrameworkMessage(messageBuilder, params.framework);
        prompt += `\n参考框架：${params.framework.name}`;
      }
    } else if ((params as any).customFramework) {
      // 🔥 修复：处理customFramework（当没有标准框架时）
      const customFramework = (params as any).customFramework;

      messageBuilder.addUserMessage(
        `【自定义框架】\n用户提供的自定义框架模式：${customFramework}\n\n请严格按照这个自定义框架模式来构建简介结构，确保生成的简介符合用户指定的框架要求。`,
        undefined,
        true,
        true
      );

      messageBuilder.addAssistantMessage(
        `我已理解您提供的自定义框架模式"${customFramework}"，将严格按照这个框架来构建简介结构，确保生成的内容符合您的框架要求。`,
        true,
        true
      );

      prompt += `\n自定义框架：${customFramework}`;
    }

    // 风格信息现在通过customRequirements处理，不再单独设置

    // 最终生成指令
    messageBuilder.addUserMessage(prompt);

    // 🔥 新增：用户特殊要求放在最后，提高权重
    if (params.customRequirements && params.customRequirements.trim()) {
      messageBuilder.addUserMessage(
        `【🎯 用户特殊要求 - 最高优先级】\n${params.customRequirements.trim()}\n\n⚠️ 请务必严格遵循以上特殊要求，这是用户的核心诉求，优先级最高！`,
        undefined,
        false, // 不是历史消息
        false  // 不是重要消息，但通过内容强调重要性
      );
    }

    // JSON格式要求
    messageBuilder.addUserMessage(this.getJSONFormatRequirement());

    return messageBuilder.build();
  }

  /**
   * 🔥 新增：构建分离的多框架消息（避免技巧杂糅）
   */
  private static buildSeparatedFrameworkMessages(
    mainFramework: SynopsisFramework,
    referenceFrameworks: SynopsisFramework[]
  ): Array<{ role: string; content: string }> {
    const messages: Array<{ role: string; content: string }> = [];

    // 添加多框架说明
    messages.push({
      role: 'user',
      content: `【多框架灵活融合模式】
您选择了1个主框架和${referenceFrameworks.length}个参考框架。请灵活结合这些框架，不要局限于单一框架的技巧和模板排版。

🔥 **核心要求**：
- **技巧交叉运用**：从不同框架中提取最优秀的写作技巧进行组合
- **模板创新融合**：结合多个框架的排版模式，创造更丰富的表达形式
- **风格多元化**：融合各框架的风格特色，避免单一框架的局限性
- **智能选择组合**：根据简介需求，智能选择和组合最适合的框架元素

⚠️ 重要提醒：强烈鼓励灵活结合多个框架的技巧、模板、排版等优势，创作出具有多层次吸引力的简介。`
    });

    // 构建主框架消息
    let mainFrameworkContent = `【主要框架】\n框架名称：${mainFramework.name}\n框架说明：${mainFramework.description}`;

    if (mainFramework.pattern) {
      mainFrameworkContent += `\n框架模式：${mainFramework.pattern}`;
    }

    mainFrameworkContent += `\n\n段落结构：`;
    mainFramework.structure.forEach((section, index) => {
      mainFrameworkContent += `\n${index + 1}. ${section.name}（${section.wordRange[0]}-${section.wordRange[1]}字）：${section.description}`;
    });

    // 添加主框架的写作技巧等信息
    const enhancedMainFramework = mainFramework as any;
    if (enhancedMainFramework.writingTechniques && enhancedMainFramework.writingTechniques.length > 0) {
      mainFrameworkContent += this.buildWritingTechniquesContent(enhancedMainFramework.writingTechniques);
    }

    if (enhancedMainFramework.styleCharacteristics) {
      mainFrameworkContent += this.buildStyleCharacteristicsContent(enhancedMainFramework.styleCharacteristics);
    }

    if (mainFramework.examples && mainFramework.examples.length > 0) {
      mainFrameworkContent += `\n\n参考示例：\n${mainFramework.examples.join('\n\n')}`;
    }

    messages.push({
      role: 'user',
      content: mainFrameworkContent
    });

    // 为每个参考框架单独构建消息
    referenceFrameworks.forEach((framework, index) => {
      let frameworkContent = `【参考框架 ${index + 1} - 可灵活融合】\n框架名称：${framework.name}\n框架说明：${framework.description}`;

      if (framework.pattern) {
        frameworkContent += `\n框架模式：${framework.pattern}`;
      }

      // 添加参考框架的写作技巧等信息
      const enhancedFramework = framework as any;
      if (enhancedFramework.writingTechniques && enhancedFramework.writingTechniques.length > 0) {
        frameworkContent += this.buildWritingTechniquesContent(enhancedFramework.writingTechniques);
      }

      if (enhancedFramework.styleCharacteristics) {
        frameworkContent += this.buildStyleCharacteristicsContent(enhancedFramework.styleCharacteristics);
      }

      if (framework.examples && framework.examples.length > 0) {
        frameworkContent += `\n\n参考示例：\n${framework.examples.slice(0, 2).join('\n\n')}`;
      }

      frameworkContent += `\n\n💡 **融合建议**：此框架的技巧、模板排版、风格特征都可以与其他框架灵活组合，创造更丰富的表达效果。`;

      messages.push({
        role: 'user',
        content: frameworkContent
      });
    });

    // 添加多框架使用指导
    messages.push({
      role: 'assistant',
      content: `我已理解主框架"${mainFramework.name}"和${referenceFrameworks.length}个参考框架的特点。在创作简介时，我会：

1. 🎯 **灵活结合多框架**：不局限于单一框架，而是智能融合多个框架的优秀元素
2. 🔄 **技巧交叉运用**：从不同框架中提取最适合的写作技巧、排版模式、风格特征进行组合
3. 📝 **模板创新融合**：结合多个框架的模板排版优势，创造更丰富的表达形式
4. ⚖️ **平衡而非单一**：避免只使用一个框架的技巧，确保简介具有多层次的吸引力
5. 🎨 **风格多元化**：融合各框架的风格特色，创作出既有主框架结构又有其他框架亮点的简介

现在我将为您创作一个灵活融合多框架技巧和模板排版的优质简介。`
    });

    return messages;
  }

  /**
   * 🔥 新增：构建单框架消息（原有逻辑的封装）
   */
  private static buildSingleFrameworkMessage(messageBuilder: any, framework: SynopsisFramework): void {
    let frameworkMessage = `【主要框架】\n框架名称：${framework.name}\n框架说明：${framework.description}`;

    // 如果有模式信息，添加到消息中
    if (framework.pattern) {
      frameworkMessage += `\n框架模式：${framework.pattern}`;
    }

    frameworkMessage += `\n\n段落结构：`;

    framework.structure.forEach((section, index) => {
      frameworkMessage += `\n${index + 1}. ${section.name}（${section.wordRange[0]}-${section.wordRange[1]}字）：${section.description}`;
    });

    // 添加写作技巧分析信息（使用类型断言处理扩展字段）
    const enhancedFramework = framework as any;
    if (enhancedFramework.writingTechniques && enhancedFramework.writingTechniques.length > 0) {
      frameworkMessage += this.buildWritingTechniquesContent(enhancedFramework.writingTechniques);
    }

    // 添加风格特征信息
    if (enhancedFramework.styleCharacteristics) {
      frameworkMessage += this.buildStyleCharacteristicsContent(enhancedFramework.styleCharacteristics);
    }

    // 添加复用模板信息（不限制数量）
    if (enhancedFramework.reusableTemplates && enhancedFramework.reusableTemplates.length > 0) {
      frameworkMessage += `\n\n【复用模板参考】\n以下模板可作为创作参考：`;
      enhancedFramework.reusableTemplates.forEach((template: any, index: number) => {
        frameworkMessage += `\n${index + 1}. ${template.name}：${template.pattern}`;
        if (template.description) {
          frameworkMessage += `\n   说明：${template.description}`;
        }
      });
    }

    frameworkMessage += `\n\n参考示例：\n${framework.examples.join('\n\n')}`;

    messageBuilder.addUserMessage(
      frameworkMessage,
      undefined,
      true,
      true
    );

    // 助手确认消息也要包含模式信息和技巧信息
    let assistantResponse = `我已理解"${framework.name}"框架结构，将按照${framework.structure.length}个段落的结构进行创作`;
    if (framework.pattern) {
      assistantResponse += `，并遵循"${framework.pattern}"的模式规律`;
    }

    // 添加对写作技巧的确认
    if (enhancedFramework.writingTechniques && enhancedFramework.writingTechniques.length > 0) {
      assistantResponse += `。我将运用框架中的${enhancedFramework.writingTechniques.length}个写作技巧`;

      // 统计技巧类别
      const categoryCount = enhancedFramework.writingTechniques.reduce((acc: any, technique: any) => {
        acc[technique.category] = (acc[technique.category] || 0) + 1;
        return acc;
      }, {});

      const categoryNames = Object.keys(categoryCount).map(cat => this.getCategoryName(cat)).join('、');
      if (categoryNames) {
        assistantResponse += `（包括${categoryNames}等技巧）`;
      }
    }

    assistantResponse += `，确保每个段落都符合其功能定位和字数要求。`;

    messageBuilder.addAssistantMessage(
      assistantResponse,
      true,
      true
    );
  }

  /**
   * 🔥 新增：构建写作技巧内容
   */
  private static buildWritingTechniquesContent(writingTechniques: any[]): string {
    let content = `\n\n【写作技巧指导】\n以下是从成功简介中分析提取的写作技巧，请在生成时运用这些技巧：`;

    // 按类别分组显示技巧
    const techniquesByCategory = writingTechniques.reduce((acc: any, technique: any) => {
      const category = technique.category;
      if (!acc[category]) acc[category] = [];
      acc[category].push(technique);
      return acc;
    }, {} as Record<string, any[]>);

    Object.entries(techniquesByCategory).forEach(([category, techniques]) => {
      const categoryName = this.getCategoryName(category);
      content += `\n\n${categoryName}技巧：`;
      (techniques as any[]).forEach((technique: any) => {
        content += `\n• ${technique.name}：${technique.description}`;
        if (technique.examples && technique.examples.length > 0) {
          content += `\n  示例：${technique.examples.slice(0, 2).join('；')}`;
        }
      });
    });

    return content;
  }

  /**
   * 🔥 新增：构建风格特征内容
   */
  private static buildStyleCharacteristicsContent(styleCharacteristics: any): string {
    let content = `\n\n【风格特征参考】`;

    if (styleCharacteristics.layoutTechniques?.paragraphStructure?.length > 0) {
      content += `\n排版技巧：${styleCharacteristics.layoutTechniques.paragraphStructure.join('、')}`;
    }

    if (styleCharacteristics.omissionAndEmphasis?.emphasizedElements?.length > 0) {
      content += `\n强调要素：${styleCharacteristics.omissionAndEmphasis.emphasizedElements.join('、')}`;
    }

    if (styleCharacteristics.coolPointLayout?.primaryCoolPoints?.length > 0) {
      content += `\n主要爽点：${styleCharacteristics.coolPointLayout.primaryCoolPoints.join('、')}`;
    }

    if (styleCharacteristics.creativeConcept?.coreCreativity?.length > 0) {
      content += `\n核心创意：${styleCharacteristics.creativeConcept.coreCreativity.join('、')}`;
    }

    return content;
  }

  /**
   * 获取长度描述
   */
  static getLengthDescription(length: 'short' | 'medium' | 'long'): string {
    switch (length) {
      case 'short':
        return '简洁型';
      case 'medium':
        return '标准型';
      case 'long':
        return '详细型';
      default:
        return '标准型';
    }
  }

  /**
   * 获取字数要求
   */
  static getWordCount(length: 'short' | 'medium' | 'long'): string {
    switch (length) {
      case 'short':
        return '100-150';
      case 'medium':
        return '200-300';
      case 'long':
        return '400-500';
      default:
        return '200-300';
    }
  }



  /**
   * 获取技巧类别中文名称
   */
  static getCategoryName(category: string): string {
    switch (category) {
      case 'layout':
        return '排版';
      case 'emphasis':
        return '强调';
      case 'coolpoint':
        return '爽点';
      case 'creativity':
        return '创意';
      default:
        return '其他';
    }
  }

  /**
   * 获取所有可用的简介框架（只使用用户保存的框架）
   */
  private static getAllAvailableFrameworks(): SynopsisFramework[] {
    try {
      // 只获取用户保存的简介框架，不使用预设框架
      let userFrameworks: SynopsisFramework[] = [];
      try {
        const savedFrameworks = localStorage.getItem('synopsis-frameworks');
        if (savedFrameworks) {
          const frameworks = JSON.parse(savedFrameworks);
          userFrameworks = frameworks.filter((f: any) => f && f.id && f.name);
        }
      } catch (error) {
        console.error('获取用户简介框架失败:', error);
      }

      console.log('📚 获取到的用户简介框架:', {
        userCount: userFrameworks.length,
        frameworks: userFrameworks.map(f => ({ id: f.id, name: f.name, effectiveness: f.effectiveness }))
      });

      return userFrameworks;
    } catch (error) {
      console.error('获取用户框架失败:', error);
      // 降级处理：返回空数组，不使用预设框架
      return [];
    }
  }

  /**
   * 获取参考框架（排除主框架，按效果评分排序，不限制数量）
   */
  private static getReferenceFrameworks(
    mainFramework: SynopsisFramework
  ): SynopsisFramework[] {
    try {
      const allFrameworks = this.getAllAvailableFrameworks();

      // 如果用户框架总数不足，直接返回空数组
      if (allFrameworks.length <= 1) {
        console.log('🔗 用户框架数量不足，跳过参考框架:', {
          mainFramework: mainFramework.name,
          totalFrameworks: allFrameworks.length,
          message: '需要至少2个框架才能启用多框架参考'
        });
        return [];
      }

      const referenceFrameworks = allFrameworks
        .filter(f => f.id !== mainFramework.id) // 排除主框架
        .sort((a, b) => (b.effectiveness || 0) - (a.effectiveness || 0)); // 按效果排序，不限制数量

      console.log('🔗 选择的参考框架（不限制数量）:', {
        mainFramework: mainFramework.name,
        totalAvailable: allFrameworks.length,
        referenceCount: referenceFrameworks.length,
        references: referenceFrameworks.map(f => ({ name: f.name, effectiveness: f.effectiveness }))
      });

      return referenceFrameworks;
    } catch (error) {
      console.error('获取参考框架失败:', error);
      return []; // 失败时返回空数组，回退到单框架模式
    }
  }

  /**
   * 构建参考框架消息（完整版，包含所有写作技巧和指导）
   */
  private static buildReferenceFrameworkMessage(frameworks: SynopsisFramework[]): string {
    if (frameworks.length === 0) {
      return '';
    }

    let message = `\n\n【参考框架】\n以下框架可作为创作参考，适当融合其优秀技巧：`;

    frameworks.forEach((framework, index) => {
      message += `\n\n${index + 1}. **${framework.name}**`;
      message += `\n   框架说明：${framework.description}`;

      // 添加核心特点
      if (framework.pattern) {
        message += `\n   模式：${framework.pattern}`;
      }

      // 添加完整的写作技巧分析信息
      const enhancedFramework = framework as any;
      if (enhancedFramework.writingTechniques && enhancedFramework.writingTechniques.length > 0) {
        message += `\n   【写作技巧】`;

        // 按类别分组显示技巧
        const techniquesByCategory = enhancedFramework.writingTechniques.reduce((acc: any, technique: any) => {
          const category = technique.category;
          if (!acc[category]) acc[category] = [];
          acc[category].push(technique);
          return acc;
        }, {} as Record<string, any[]>);

        Object.entries(techniquesByCategory).forEach(([category, techniques]) => {
          const categoryName = this.getCategoryName(category);
          message += `\n   ${categoryName}技巧：`;
          (techniques as any[]).forEach((technique: any) => {
            message += `\n   • ${technique.name}：${technique.description}`;
            if (technique.examples && technique.examples.length > 0) {
              message += `\n     示例：${technique.examples.join('；')}`;
            }
          });
        });
      }

      // 添加风格特征信息
      if (enhancedFramework.styleCharacteristics) {
        const characteristics = enhancedFramework.styleCharacteristics;
        message += `\n   【风格特征】`;

        if (characteristics.layoutTechniques?.paragraphStructure?.length > 0) {
          message += `\n   排版技巧：${characteristics.layoutTechniques.paragraphStructure.join('、')}`;
        }

        if (characteristics.omissionAndEmphasis?.emphasizedElements?.length > 0) {
          message += `\n   强调要素：${characteristics.omissionAndEmphasis.emphasizedElements.join('、')}`;
        }

        if (characteristics.coolPointLayout?.primaryCoolPoints?.length > 0) {
          message += `\n   主要爽点：${characteristics.coolPointLayout.primaryCoolPoints.join('、')}`;
        }

        if (characteristics.creativeConcept?.coreCreativity?.length > 0) {
          message += `\n   核心创意：${characteristics.creativeConcept.coreCreativity.join('、')}`;
        }
      }

      // 添加复用模板信息
      if (enhancedFramework.reusableTemplates && enhancedFramework.reusableTemplates.length > 0) {
        message += `\n   【复用模板】`;
        enhancedFramework.reusableTemplates.forEach((template: any, templateIndex: number) => {
          message += `\n   ${templateIndex + 1}. ${template.name}：${template.pattern}`;
          if (template.description) {
            message += `\n      说明：${template.description}`;
          }
        });
      }

      // 添加所有示例
      if (framework.examples && framework.examples.length > 0) {
        message += `\n   【参考示例】`;
        framework.examples.forEach((example, exampleIndex) => {
          message += `\n   ${exampleIndex + 1}. ${example}`;
        });
      }
    });

    message += `\n\n【融合指导】\n请以主要框架为基础，深度融合参考框架的优秀技巧、风格特征和创作模式，创作出更具创新性、多样性和吸引力的简介。充分利用各框架的写作技巧和成功经验。`;

    return message;
  }

  /**
   * 获取JSON格式要求
   */
  static getJSONFormatRequirement(): string {
    return `按照简介要求，完整生成对应内容，脑洞丰富，给力\n注意对话句子方面的换行的重要性
    注意，不要出现展望未来等情节，最后一定是断章，且最后的信息一定是设悬，而不是展望未来，如何如何，

    `;
  }
}
