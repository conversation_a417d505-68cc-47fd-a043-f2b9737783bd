import { Chapter } from '../dexie';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateChapter(chapter: Partial<Chapter>): ValidationResult {
  const errors: Record<string, string> = {};
  
  // 验证标题
  if (!chapter.title) {
    errors.title = '标题不能为空';
  } else if (chapter.title.length > 100) {
    errors.title = '标题不能超过100个字符';
  }
  
  // 验证书籍ID
  if (!chapter.bookId) {
    errors.bookId = '书籍ID不能为空';
  }
  
  // 验证顺序
  if (chapter.order !== undefined && chapter.order < 0) {
    errors.order = '顺序不能为负数';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
