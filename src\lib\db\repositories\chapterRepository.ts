import { v4 as uuidv4 } from 'uuid';
import { db, Chapter } from '../dexie';
import { bookRepository } from './bookRepository';

export interface IChapterRepository {
  getAllByBookId(bookId: string): Promise<Chapter[]>;
  getById(id: string): Promise<Chapter | undefined>;
  create(chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  update(id: string, chapter: Partial<Chapter>): Promise<void>;
  delete(id: string): Promise<void>;
  getNextOrder(bookId: string): Promise<number>;
  reorder(bookId: string, orderedIds: string[]): Promise<void>;
  calculateWordCount(content: string): number;
}

export class ChapterRepository implements IChapterRepository {
  async getAllByBookId(bookId: string): Promise<Chapter[]> {
    return await db.chapters
      .where('bookId')
      .equals(bookId)
      .sortBy('order');
  }

  async getById(id: string): Promise<Chapter | undefined> {
    return await db.chapters.get(id);
  }

  async create(chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const id = uuidv4();

    // 如果没有提供order，则获取下一个可用的order
    if (chapter.order === undefined) {
      chapter.order = await this.getNextOrder(chapter.bookId);
    }

    // 检查是否已存在相同order的章节（防重复创建）
    const existingChapter = await db.chapters
      .where('bookId')
      .equals(chapter.bookId)
      .and(ch => ch.order === chapter.order)
      .first();

    if (existingChapter) {
      console.warn(`章节已存在: bookId=${chapter.bookId}, order=${chapter.order}, 返回现有章节ID`);
      return existingChapter.id!;
    }

    // 计算字数
    if (chapter.wordCount === undefined) {
      chapter.wordCount = this.calculateWordCount(chapter.content);
    }

    await db.chapters.add({
      ...chapter,
      id,
      createdAt: now,
      updatedAt: now,
      characterIds: chapter.characterIds || [],
      terminologyIds: chapter.terminologyIds || [],
      worldBuildingIds: chapter.worldBuildingIds || []
    });

    // 更新书籍的总字数
    await bookRepository.updateWordCount(chapter.bookId);

    return id;
  }

  async update(id: string, chapter: Partial<Chapter>): Promise<void> {
    const existingChapter = await db.chapters.get(id);
    if (!existingChapter) {
      throw new Error(`Chapter with id ${id} not found`);
    }

    // 如果内容更新了，重新计算字数
    if (chapter.content !== undefined) {
      chapter.wordCount = this.calculateWordCount(chapter.content);
    }

    await db.chapters.update(id, {
      ...chapter,
      updatedAt: new Date()
    });

    // 更新书籍的总字数
    await bookRepository.updateWordCount(existingChapter.bookId);
  }

  async delete(id: string): Promise<void> {
    const chapter = await db.chapters.get(id);
    if (!chapter) {
      throw new Error(`Chapter with id ${id} not found`);
    }

    const bookId = chapter.bookId;

    await db.chapters.delete(id);

    // 重新排序剩余章节
    const remainingChapters = await db.chapters
      .where('bookId')
      .equals(bookId)
      .sortBy('order');

    // 更新剩余章节的顺序
    for (let i = 0; i < remainingChapters.length; i++) {
      await db.chapters.update(remainingChapters[i].id!, { order: i });
    }

    // 更新书籍的总字数
    await bookRepository.updateWordCount(bookId);
  }

  async getNextOrder(bookId: string): Promise<number> {
    const chapters = await db.chapters
      .where('bookId')
      .equals(bookId)
      .toArray();

    return chapters.length;
  }

  async reorder(bookId: string, orderedIds: string[]): Promise<void> {
    await db.transaction('rw', db.chapters, async () => {
      for (let i = 0; i < orderedIds.length; i++) {
        await db.chapters.update(orderedIds[i], { order: i });
      }
    });
  }

  calculateWordCount(content: string): number {
    if (!content) return 0;

    // 移除HTML标签
    const textOnly = content.replace(/<[^>]*>/g, '');

    // 按空白字符分割并过滤空字符串
    const words = textOnly.split(/\s+/).filter(word => word.length > 0);

    return words.length;
  }
}

// 创建并导出仓库实例
export const chapterRepository = new ChapterRepository();
