/* 增强型下拉框样式 */

.enhanced-select-container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
  font-family: var(--font-family, 'Inter', system-ui, sans-serif);
}

.enhanced-select-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--label-color, #374151);
}

.enhanced-select-required {
  color: var(--error-color, #ef4444);
  margin-left: 0.25rem;
}

.enhanced-select {
  position: relative;
  width: 100%;
  user-select: none;
  outline: none;
}

.enhanced-select:focus-within .enhanced-select-trigger {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.enhanced-select.has-error .enhanced-select-trigger {
  border-color: var(--error-color, #ef4444);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.enhanced-select.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.enhanced-select.is-disabled .enhanced-select-trigger {
  background-color: var(--disabled-bg, #f3f4f6);
  cursor: not-allowed;
}

.enhanced-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: 2.5rem;
}

.enhanced-select-value {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.875rem;
  color: var(--text-color, #1f2937);
}

.enhanced-select-value.is-placeholder {
  color: var(--placeholder-color, #9ca3af);
}

.enhanced-select-search {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0;
  font-size: 0.875rem;
  color: var(--text-color, #1f2937);
  outline: none;
  width: 100%;
}

.enhanced-select-actions {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.enhanced-select-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  padding: 0;
  margin-right: 0.5rem;
  background: transparent;
  border: none;
  border-radius: 50%;
  color: var(--text-muted, #9ca3af);
  cursor: pointer;
  transition: all 0.2s ease;
}

.enhanced-select-clear:hover {
  color: var(--text-color, #1f2937);
  background-color: rgba(0, 0, 0, 0.05);
}

.enhanced-select-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted, #9ca3af);
  transition: transform 0.2s ease;
}

.enhanced-select.is-open .enhanced-select-arrow {
  transform: rotate(180deg);
}

.enhanced-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 0.25rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow-y: auto;
  animation: dropdownFadeIn 0.2s ease;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-select-option {
  position: relative;
  cursor: pointer;
  transition: all 0.15s ease;
  animation: optionFadeIn 0.2s ease;
  animation-fill-mode: both;
}

.enhanced-select-option-content {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
}

@keyframes optionFadeIn {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.enhanced-select-option:nth-child(1) { animation-delay: 0.03s; }
.enhanced-select-option:nth-child(2) { animation-delay: 0.06s; }
.enhanced-select-option:nth-child(3) { animation-delay: 0.09s; }
.enhanced-select-option:nth-child(4) { animation-delay: 0.12s; }
.enhanced-select-option:nth-child(5) { animation-delay: 0.15s; }
.enhanced-select-option:nth-child(6) { animation-delay: 0.18s; }
.enhanced-select-option:nth-child(7) { animation-delay: 0.21s; }
.enhanced-select-option:nth-child(8) { animation-delay: 0.24s; }

.enhanced-select-option:hover,
.enhanced-select-option.is-highlighted {
  background: rgba(59, 130, 246, 0.05);
}

.enhanced-select-option.is-selected {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color, #3b82f6);
}

.enhanced-select-option.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: transparent;
}

.enhanced-select-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  margin-right: 0.5rem;
  color: var(--primary-color, #3b82f6);
}

.enhanced-select-option-text {
  flex: 1;
  min-width: 0;
}

.enhanced-select-option-label {
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.enhanced-select-option-description {
  font-size: 0.75rem;
  color: var(--text-muted, #9ca3af);
  margin-top: 0.125rem;
}

.enhanced-select-option-preview {
  margin-left: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.enhanced-select-no-options {
  padding: 0.75rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-muted, #9ca3af);
}

.enhanced-select-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--error-color, #ef4444);
}
