import { db } from '@/lib/db/dexie';
import { UserSettings, UserState, HistoryRecord } from '@/lib/db/dexie';

/**
 * 数据服务基类 - 提供统一的数据访问接口
 * 替代localStorage，使用IndexedDB进行持久化存储
 */
export abstract class BaseDataService {
  protected cache = new Map<string, any>();
  protected readonly maxCacheSize = 10000000; // 最大缓存条目数 - 从1000增加到10000
  protected importantKeys = new Set<string>(); // 重要数据标记
  protected lastCleanupTime = 0; // 上次清理时间

  /**
   * 标记重要数据，清理时跳过
   */
  protected markAsImportant(key: string): void {
    this.importantKeys.add(key);
  }

  /**
   * 检查是否需要清理缓存
   */
  protected shouldCleanup(): boolean {
    const now = Date.now();
    // 缓存超过120%容量且距离上次清理超过1秒
    return this.cache.size > this.maxCacheSize * 1.2 &&
           (now - this.lastCleanupTime) > 1000;
  }

  /**
   * 优化的缓存清理策略
   */
  protected cleanupCache(): void {
    if (!this.shouldCleanup()) {
      return;
    }

    const beforeSize = this.cache.size;
    const entries = Array.from(this.cache.entries());
    // 过滤出可删除的条目（非重要数据）
    const deletableEntries = entries.filter(([key]) => !this.importantKeys.has(key));

    // 只清理超出部分的30%，避免过度清理
    const excessCount = this.cache.size - this.maxCacheSize;
    const toDeleteCount = Math.min(
      deletableEntries.length,
      Math.max(excessCount, Math.floor(this.maxCacheSize * 0.3))
    );

    // 用户友好的清理通知
    if (toDeleteCount > 100) {
      console.info(`📊 系统优化：正在清理${toDeleteCount}条缓存数据以提升性能，重要数据已受保护`);
    }

    // 删除最旧的可删除条目
    deletableEntries.slice(0, toDeleteCount).forEach(([key]) => {
      this.cache.delete(key);
    });

    this.lastCleanupTime = Date.now();
    const afterSize = this.cache.size;
    const actualDeleted = beforeSize - afterSize;

    // 详细的清理报告
    if (actualDeleted > 0) {
      console.log(`🧹 缓存清理完成: 删除${actualDeleted}条, 剩余${afterSize}条, 重要数据${this.importantKeys.size}条受保护`);

      // 大量清理时的额外提示
      if (actualDeleted > 200) {
        console.info(`💡 提示：如果频繁看到大量缓存清理，建议检查数据访问模式或增加缓存容量`);
      }
    }
  }

  /**
   * 生成缓存键
   */
  protected getCacheKey(...parts: string[]): string {
    return parts.filter(Boolean).join(':');
  }
}

/**
 * 用户设置服务 - 管理全局用户设置
 * 替代localStorage中的API设置、宠物配置、标注偏好等
 */
export class UserSettingsService extends BaseDataService {
  /**
   * 获取设置值
   * @param category 设置分类 ('api', 'pet', 'annotation', 'dual-ai', 'recommendation')
   * @param key 设置键名
   * @param defaultValue 默认值
   */
  async get<T = any>(category: string, key: string, defaultValue?: T): Promise<T> {
    const cacheKey = this.getCacheKey(category, key);

    if (this.cache.has(cacheKey)) {
      // 标记为重要数据（最近访问的）
      this.markAsImportant(cacheKey);
      return this.cache.get(cacheKey);
    }

    try {
      const setting = await db.userSettings
        .where(['category', 'key'])
        .equals([category, key])
        .first();

      const value = setting?.value ?? defaultValue;
      this.cache.set(cacheKey, value);
      this.markAsImportant(cacheKey); // 新加载的数据标记为重要
      this.cleanupCache();

      return value;
    } catch (error) {
      console.error('获取用户设置失败:', error);
      return defaultValue as T;
    }
  }

  /**
   * 设置值
   * @param category 设置分类
   * @param key 设置键名
   * @param value 设置值
   */
  async set(category: string, key: string, value: any): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(category, key);

      // 先更新缓存并标记为重要数据
      this.cache.set(cacheKey, value);
      this.markAsImportant(cacheKey);

      // 立即持久化到IndexedDB
      const now = new Date();

      // 检查是否已存在记录
      const existing = await db.userSettings
        .where(['category', 'key'])
        .equals([category, key])
        .first();

      if (existing) {
        // 更新现有记录
        await db.userSettings.update(existing.id!, {
          value,
          updatedAt: now
        });
      } else {
        // 创建新记录，生成唯一ID
        const id = `${category}-${key}-${Date.now()}`;
        await db.userSettings.add({
          id,
          category,
          key,
          value,
          updatedAt: now,
          createdAt: now
        });
      }

      // 延迟清理，避免立即删除刚保存的数据
      setTimeout(() => this.cleanupCache(), 100);
    } catch (error) {
      console.error('保存用户设置失败:', error);
      throw error;
    }
  }

  /**
   * 删除设置
   * @param category 设置分类
   * @param key 设置键名
   * @param skipConfirmation 跳过确认检查（仅在UI层已确认时使用）
   */
  async remove(category: string, key: string, skipConfirmation = false): Promise<void> {
    // 安全检查：确保删除操作经过用户确认
    if (!skipConfirmation) {
      console.warn(`⚠️ 尝试删除用户设置: ${category}.${key} - 建议通过UI确认后调用`);

      // 开发环境强制要求确认
      if (process.env.NODE_ENV === 'development') {
        throw new Error(`删除操作需要用户确认！请在UI层确认后使用 remove(category, key, true)`);
      }

      // 生产环境记录警告但允许执行（向后兼容）
      console.warn(`🚨 生产环境检测到未确认的删除操作: ${category}.${key}`);
    }

    try {
      await db.userSettings
        .where(['category', 'key'])
        .equals([category, key])
        .delete();

      const cacheKey = this.getCacheKey(category, key);
      this.cache.delete(cacheKey);

      console.log(`✅ 用户设置删除成功: ${category}.${key}`);
    } catch (error) {
      console.error('删除用户设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取分类下的所有设置
   * @param category 设置分类
   */
  async getByCategory(category: string): Promise<Record<string, any>> {
    try {
      const settings = await db.userSettings
        .where('category')
        .equals(category)
        .toArray();

      const result: Record<string, any> = {};
      settings.forEach(setting => {
        result[setting.key] = setting.value;
        // 更新缓存
        const cacheKey = this.getCacheKey(category, setting.key);
        this.cache.set(cacheKey, setting.value);
      });

      this.cleanupCache();
      return result;
    } catch (error) {
      console.error('获取分类设置失败:', error);
      return {};
    }
  }

  /**
   * 批量设置
   * @param category 设置分类
   * @param settings 设置对象
   */
  async setBatch(category: string, settings: Record<string, any>): Promise<void> {
    try {
      const now = new Date();

      // 获取现有记录
      const existingSettings = await db.userSettings
        .where('category')
        .equals(category)
        .toArray();

      const existingMap = new Map(existingSettings.map(s => [s.key, s]));

      const userSettings: UserSettings[] = Object.entries(settings).map(([key, value]) => {
        const existing = existingMap.get(key);
        if (existing) {
          // 更新现有记录
          return {
            ...existing,
            value,
            updatedAt: now
          };
        } else {
          // 创建新记录，生成唯一ID
          const id = `${category}-${key}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          return {
            id,
            category,
            key,
            value,
            updatedAt: now,
            createdAt: now
          };
        }
      });

      await db.userSettings.bulkPut(userSettings);

      // 更新缓存
      Object.entries(settings).forEach(([key, value]) => {
        const cacheKey = this.getCacheKey(category, key);
        this.cache.set(cacheKey, value);
      });

      this.cleanupCache();
    } catch (error) {
      console.error('批量保存用户设置失败:', error);
      throw error;
    }
  }
}

/**
 * 用户状态服务 - 管理书籍相关的用户状态
 * 替代localStorage中的AI助手状态、框架选择、布局状态等
 */
export class UserStateService extends BaseDataService {
  /**
   * 获取状态值
   * @param bookId 书籍ID（可选，全局状态时为空）
   * @param category 状态分类 ('assistant', 'framework', 'layout', 'canvas', 'collapsed-nodes')
   * @param key 状态键名
   * @param defaultValue 默认值
   */
  async get<T = any>(bookId: string | null, category: string, key: string, defaultValue?: T): Promise<T> {
    const cacheKey = this.getCacheKey(bookId || 'global', category, key);

    if (this.cache.has(cacheKey)) {
      // 标记为重要数据（最近访问的）
      this.markAsImportant(cacheKey);
      return this.cache.get(cacheKey);
    }

    try {
      let query = db.userStates.where('category').equals(category).and(s => s.key === key);
      if (bookId) {
        query = query.and(s => s.bookId === bookId);
      } else {
        query = query.and(s => !s.bookId);
      }
      const state = await query.first();

      const value = state?.value ?? defaultValue;
      this.cache.set(cacheKey, value);
      this.markAsImportant(cacheKey); // 新加载的数据标记为重要
      this.cleanupCache();

      return value;
    } catch (error) {
      console.error('获取用户状态失败:', error);
      return defaultValue as T;
    }
  }

  /**
   * 设置状态值
   * @param bookId 书籍ID（可选，全局状态时为空）
   * @param category 状态分类
   * @param key 状态键名
   * @param value 状态值
   */
  async set(bookId: string | null, category: string, key: string, value: any): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(bookId || 'global', category, key);

      // 先更新缓存并标记为重要数据
      this.cache.set(cacheKey, value);
      this.markAsImportant(cacheKey);

      // 立即持久化到IndexedDB
      const now = new Date();

      // 检查是否已存在记录
      let query = db.userStates.where('category').equals(category).and(s => s.key === key);
      if (bookId) {
        query = query.and(s => s.bookId === bookId);
      } else {
        query = query.and(s => !s.bookId);
      }
      const existing = await query.first();

      if (existing) {
        // 更新现有记录
        await db.userStates.update(existing.id!, {
          value,
          updatedAt: now
        });
      } else {
        // 创建新记录，生成唯一ID
        const id = `${bookId || 'global'}-${category}-${key}-${Date.now()}`;
        await db.userStates.add({
          id,
          bookId: bookId || undefined,
          category,
          key,
          value,
          updatedAt: now,
          createdAt: now
        });
      }

      // 延迟清理，避免立即删除刚保存的数据
      setTimeout(() => this.cleanupCache(), 100);
    } catch (error) {
      console.error('保存用户状态失败:', error);
      throw error;
    }
  }

  /**
   * 删除状态
   * @param bookId 书籍ID
   * @param category 状态分类
   * @param key 状态键名
   * @param skipConfirmation 跳过确认检查（仅在UI层已确认时使用）
   */
  async remove(bookId: string | null, category: string, key: string, skipConfirmation = false): Promise<void> {
    // 安全检查：确保删除操作经过用户确认
    if (!skipConfirmation) {
      const stateId = `${bookId || 'global'}.${category}.${key}`;
      console.warn(`⚠️ 尝试删除用户状态: ${stateId} - 建议通过UI确认后调用`);

      // 开发环境强制要求确认
      if (process.env.NODE_ENV === 'development') {
        throw new Error(`删除操作需要用户确认！请在UI层确认后使用 remove(bookId, category, key, true)`);
      }

      // 生产环境记录警告但允许执行（向后兼容）
      console.warn(`🚨 生产环境检测到未确认的删除操作: ${stateId}`);
    }

    try {
      let query = db.userStates.where('category').equals(category).and(s => s.key === key);
      if (bookId) {
        query = query.and(s => s.bookId === bookId);
      } else {
        query = query.and(s => !s.bookId);
      }
      await query.delete();

      const cacheKey = this.getCacheKey(bookId || 'global', category, key);
      this.cache.delete(cacheKey);

      console.log(`✅ 用户状态删除成功: ${bookId || 'global'}.${category}.${key}`);
    } catch (error) {
      console.error('删除用户状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取书籍的所有状态
   * @param bookId 书籍ID
   * @param category 状态分类（可选）
   */
  async getByBook(bookId: string, category?: string): Promise<Record<string, any>> {
    try {
      let query = db.userStates.where('bookId').equals(bookId);

      if (category) {
        query = query.and(state => state.category === category);
      }

      const states = await query.toArray();
      const result: Record<string, any> = {};

      states.forEach(state => {
        const key = category ? state.key : `${state.category}:${state.key}`;
        result[key] = state.value;

        // 更新缓存
        const cacheKey = this.getCacheKey(bookId, state.category, state.key);
        this.cache.set(cacheKey, state.value);
      });

      this.cleanupCache();
      return result;
    } catch (error) {
      console.error('获取书籍状态失败:', error);
      return {};
    }
  }
}

// 导出单例实例
export const userSettingsService = new UserSettingsService();
export const userStateService = new UserStateService();
