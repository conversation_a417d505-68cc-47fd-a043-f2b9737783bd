"use client";

import { AIResponseParser } from './AIResponseParser';
import { 
  EnhancedSentenceSuggestion, 
  SuggestionOption, 
  SuggestionType, 
  SuggestionCategory 
} from '@/factories/ai/services/TextProcessingService';

/**
 * 增强的AI批注解析器
 * 支持多选项建议和丰富的元数据解析
 */
export class EnhancedAnnotationParser {
  /**
   * 解析增强的句子建议
   * @param response AI响应文本
   * @returns 解析后的增强建议或null
   */
  static parseEnhancedSuggestion(response: string): EnhancedSentenceSuggestion | null {
    try {
      const parsed = AIResponseParser.parseJSON(response, null);
      if (!parsed || !parsed.suggestions) return null;
      
      // 验证必要字段
      if (!this.validateSuggestionStructure(parsed)) {
        console.warn('建议结构验证失败，使用兜底解析');
        return this.fallbackParse(response);
      }
      
      // 为每个建议生成唯一ID
      parsed.suggestions = parsed.suggestions.map((suggestion: any, index: number) => ({
        ...suggestion,
        id: suggestion.id || `suggestion_${Date.now()}_${index}`,
        confidence: this.normalizeConfidence(suggestion.confidence),
        type: this.validateSuggestionType(suggestion.type),
        estimatedImpact: suggestion.estimatedImpact || 'moderate'
      }));
      
      return {
        ...parsed,
        selectedSuggestionIndex: 0,
        userModified: false,
        lastAccessed: Date.now()
      };
    } catch (error) {
      console.error('增强建议解析失败:', error);
      return this.fallbackParse(response);
    }
  }

  /**
   * 验证建议结构的有效性
   * @param parsed 解析后的对象
   * @returns 是否有效
   */
  private static validateSuggestionStructure(parsed: any): boolean {
    return (
      parsed.analysisResult &&
      Array.isArray(parsed.suggestions) &&
      parsed.suggestions.length > 0 &&
      parsed.suggestions.every((s: any) => s.type && s.reason) &&
      typeof parsed.sentenceIndex === 'number' &&
      typeof parsed.originalText === 'string'
    );
  }

  /**
   * 兜底解析逻辑
   * @param response 原始响应
   * @returns 基础的增强建议或null
   */
  private static fallbackParse(response: string): EnhancedSentenceSuggestion | null {
    // 尝试提取基本信息
    const basicSuggestion = this.extractBasicSuggestion(response);
    if (!basicSuggestion) return null;
    
    return {
      sentenceIndex: basicSuggestion.sentenceIndex || 0,
      originalText: basicSuggestion.originalText || '',
      analysisResult: {
        issues: ['解析异常'],
        confidence: 0.5,
        category: 'expression' as SuggestionCategory,
        severity: 'medium' as const
      },
      suggestions: [{
        id: `fallback_${Date.now()}`,
        type: 'modify' as SuggestionType,
        text: basicSuggestion.suggestion || '',
        reason: '自动解析建议',
        confidence: 0.5,
        estimatedImpact: 'minor' as const
      }],
      recommendedAction: 'modify',
      priority: 'medium' as const,
      selectedSuggestionIndex: 0,
      userModified: false,
      lastAccessed: Date.now()
    };
  }

  /**
   * 提取基本建议信息
   * @param response 响应文本
   * @returns 基本建议对象或null
   */
  private static extractBasicSuggestion(response: string): any {
    try {
      // 尝试使用正则表达式提取基本信息
      const sentenceIndexMatch = response.match(/"sentenceIndex"\s*:\s*(\d+)/);
      const originalTextMatch = response.match(/"originalText"\s*:\s*"([^"]+)"/);
      const suggestionMatch = response.match(/"suggestion"\s*:\s*"([^"]+)"/);
      
      if (sentenceIndexMatch && originalTextMatch) {
        return {
          sentenceIndex: parseInt(sentenceIndexMatch[1]),
          originalText: originalTextMatch[1],
          suggestion: suggestionMatch ? suggestionMatch[1] : ''
        };
      }
      
      return null;
    } catch (error) {
      console.error('提取基本建议信息失败:', error);
      return null;
    }
  }

  /**
   * 标准化置信度值
   * @param confidence 原始置信度
   * @returns 标准化后的置信度 (0-1)
   */
  private static normalizeConfidence(confidence: any): number {
    if (typeof confidence === 'number') {
      if (confidence > 1) {
        // 如果是百分比形式 (0-100)，转换为 0-1
        return Math.min(confidence / 100, 1);
      }
      return Math.max(0, Math.min(confidence, 1));
    }
    
    if (typeof confidence === 'string') {
      const numValue = parseFloat(confidence.replace('%', ''));
      if (!isNaN(numValue)) {
        return this.normalizeConfidence(numValue);
      }
    }
    
    return 0.5; // 默认值
  }

  /**
   * 验证建议类型
   * @param type 原始类型
   * @returns 有效的建议类型
   */
  private static validateSuggestionType(type: any): SuggestionType {
    const validTypes: SuggestionType[] = [
      'modify', 'delete', 'split', 'merge', 'reorder', 'enhance', 'simplify', 'keep'
    ];
    
    if (typeof type === 'string' && validTypes.includes(type as SuggestionType)) {
      return type as SuggestionType;
    }
    
    return 'modify'; // 默认值
  }

  /**
   * 解析多个增强建议
   * @param response AI响应文本
   * @returns 解析后的建议数组
   */
  static parseMultipleEnhancedSuggestions(response: string): EnhancedSentenceSuggestion[] {
    try {
      const parsed = AIResponseParser.parseJSON(response, { suggestions: [] });
      
      if (parsed.suggestions && Array.isArray(parsed.suggestions)) {
        return parsed.suggestions
          .map((suggestion: any) => this.parseEnhancedSuggestion(JSON.stringify(suggestion)))
          .filter((suggestion: EnhancedSentenceSuggestion | null) => suggestion !== null) as EnhancedSentenceSuggestion[];
      }
      
      return [];
    } catch (error) {
      console.error('解析多个增强建议失败:', error);
      return [];
    }
  }

  /**
   * 从流式响应中提取增强建议
   * @param response 流式响应文本
   * @returns 提取到的建议数组
   */
  static extractEnhancedSuggestionsFromStream(response: string): EnhancedSentenceSuggestion[] {
    const suggestions: EnhancedSentenceSuggestion[] = [];
    
    try {
      // 匹配完整的建议对象
      const suggestionPattern = /\{\s*"sentenceIndex"\s*:\s*(\d+)[\s\S]*?\}/g;
      let match;
      
      while ((match = suggestionPattern.exec(response)) !== null) {
        const suggestionText = match[0];
        const parsed = this.parseEnhancedSuggestion(suggestionText);
        
        if (parsed) {
          suggestions.push(parsed);
        }
      }
      
      return suggestions;
    } catch (error) {
      console.error('从流式响应提取建议失败:', error);
      return [];
    }
  }

  /**
   * 验证建议选项的有效性
   * @param option 建议选项
   * @returns 是否有效
   */
  static validateSuggestionOption(option: any): boolean {
    return (
      typeof option.type === 'string' &&
      typeof option.reason === 'string' &&
      typeof option.confidence === 'number' &&
      option.confidence >= 0 &&
      option.confidence <= 1
    );
  }
}
