import React, { useState } from 'react';
import { XIcon, FolderIcon, getCategoryIcon } from '../../common/icons/PrefixIcons';

interface FolderCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateFolder: (folderName: string, category: string) => void;
}

export const FolderCreateDialog: React.FC<FolderCreateDialogProps> = ({
  isOpen,
  onClose,
  onCreateFolder
}) => {
  const [folderName, setFolderName] = useState('');
  const [category, setCategory] = useState('custom');

  const handleCreate = () => {
    if (!folderName.trim()) {
      alert('请输入文件夹名称');
      return;
    }

    onCreateFolder(folderName.trim(), category);
    setFolderName('');
    setCategory('custom');
    onClose();
  };

  const handleClose = () => {
    setFolderName('');
    setCategory('custom');
    onClose();
  };

  if (!isOpen) return null;

  const categoryOptions = [
    { value: 'custom', label: '自定义', icon: 'custom' },
    { value: 'context_enhancement', label: '上下文增强', icon: 'context_enhancement' },
    { value: 'persona_enhancement', label: '人设强化', icon: 'persona_enhancement' },
    { value: 'format_specification', label: '格式规范', icon: 'format_specification' },
    { value: 'ace_framework', label: 'ACE框架', icon: 'ace_framework' }
  ];

  const getCategoryTheme = (cat: string) => {
    switch (cat) {
      case 'custom':
        return { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800' };
      case 'context_enhancement':
        return { bg: 'bg-teal-50', border: 'border-teal-200', text: 'text-teal-800' };
      case 'persona_enhancement':
        return { bg: 'bg-orange-50', border: 'border-orange-200', text: 'text-orange-800' };
      case 'format_specification':
        return { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800' };
      case 'ace_framework':
        return { bg: 'bg-indigo-50', border: 'border-indigo-200', text: 'text-indigo-800' };
      default:
        return { bg: 'bg-gray-50', border: 'border-gray-200', text: 'text-gray-800' };
    }
  };

  const selectedTheme = getCategoryTheme(category);

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10003] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <FolderIcon size={24} className="mr-2 text-blue-600" />
            创建新文件夹
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XIcon size={24} />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="p-6 space-y-6">
          {/* 文件夹名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文件夹名称 *
            </label>
            <input
              type="text"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              placeholder="输入文件夹名称..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoFocus
            />
          </div>

          {/* 类别选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文件夹类别
            </label>
            <div className="grid grid-cols-1 gap-2">
              {categoryOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                    category === option.value
                      ? `${selectedTheme.border} ${selectedTheme.bg}`
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="category"
                    value={option.value}
                    checked={category === option.value}
                    onChange={(e) => setCategory(e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center space-x-3">
                    {getCategoryIcon(option.icon, 20, category === option.value ? selectedTheme.text : 'text-gray-600')}
                    <span className={`font-medium ${category === option.value ? selectedTheme.text : 'text-gray-700'}`}>
                      {option.label}
                    </span>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 预览 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              预览
            </label>
            <div className={`p-4 border-2 rounded-lg ${selectedTheme.border} ${selectedTheme.bg}`}>
              <div className="flex items-center space-x-3">
                {getCategoryIcon(category, 24, selectedTheme.text)}
                <div>
                  <h3 className={`text-sm font-semibold ${selectedTheme.text}`}>
                    {folderName || '文件夹名称'}
                  </h3>
                  <p className="text-xs text-gray-600">
                    {categoryOptions.find(opt => opt.value === category)?.label} 类别
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部操作 */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleCreate}
              disabled={!folderName.trim()}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              创建文件夹
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
