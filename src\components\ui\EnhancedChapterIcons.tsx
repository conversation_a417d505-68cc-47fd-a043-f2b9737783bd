"use client";

import React from 'react';

interface ChapterIconProps {
  type: 'book-open' | 'book-sparkles' | 'book-section' | 'edit' | 'bookmark';
  className?: string;
  size?: number;
  color?: string;
}

/**
 * 增强的章节图标组件
 * 包含多种精美的SVG图标
 */
export const ChapterIcon: React.FC<ChapterIconProps> = ({
  type,
  className = "",
  size = 16,
  color = "currentColor"
}) => {
  const iconSvgs = {
    'book-open': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
      >
        <path 
          d="M12 10.4V20M12 10.4C12 8.15979 12 7.03969 11.564 6.18404C11.1805 5.43139 10.5686 4.81947 9.81596 4.43597C8.96031 4 7.84021 4 5.6 4H4.6C4.03995 4 3.75992 4 3.54601 4.10899C3.35785 4.20487 3.20487 4.35785 3.10899 4.54601C3 4.75992 3 5.03995 3 5.6V16.4C3 16.9601 3 17.2401 3.10899 17.454C3.20487 17.6422 3.35785 17.7951 3.54601 17.891C3.75992 18 4.03995 18 4.6 18H7.54668C8.08687 18 8.35696 18 8.61814 18.0466C8.84995 18.0879 9.0761 18.1563 9.29191 18.2506C9.53504 18.3567 9.75977 18.5065 10.2092 18.8062L12 20M12 10.4C12 8.15979 12 7.03969 12.436 6.18404C12.8195 5.43139 13.4314 4.81947 14.184 4.43597C15.0397 4 16.1598 4 18.4 4H19.4C19.9601 4 20.2401 4 20.454 4.10899C20.6422 4.20487 20.7951 4.35785 20.891 4.54601C21 4.75992 21 5.03995 21 5.6V16.4C21 16.9601 21 17.2401 20.891 17.454C20.7951 17.6422 20.6422 17.7951 20.454 17.891C20.2401 18 19.9601 18 19.4 18H16.4533C15.9131 18 15.643 18 15.3819 18.0466C15.15 18.0879 14.9239 18.1563 14.7081 18.2506C14.465 18.3567 14.2402 18.5065 13.7908 18.8062L12 20" 
          stroke={color} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'book-sparkles': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
      >
        <path 
          d="M5 19V6.2C5 5.0799 5 4.51984 5.21799 4.09202C5.40973 3.71569 5.71569 3.40973 6.09202 3.21799C6.51984 3 7.0799 3 8.2 3H15.8C16.9201 3 17.4802 3 17.908 3.21799C18.2843 3.40973 18.5903 3.71569 18.782 4.09202C19 4.51984 19 5.0799 19 6.2V17H7C5.89543 17 5 17.8954 5 19ZM5 19C5 20.1046 5.89543 21 7 21H19M18 17V21M10 6V10M14 10V14M8 8H12M12 12H16" 
          stroke={color} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'book-section': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
      >
        <path 
          d="M5 19V6.2C5 5.0799 5 4.51984 5.21799 4.09202C5.40973 3.71569 5.71569 3.40973 6.09202 3.21799C6.51984 3 7.0799 3 8.2 3H15.8C16.9201 3 17.4802 3 17.908 3.21799C18.2843 3.40973 18.5903 3.71569 18.782 4.09202C19 4.51984 19 5.0799 19 6.2V17H7C5.89543 17 5 17.8954 5 19ZM5 19C5 20.1046 5.89543 21 7 21H19M18 17V21M14.5 8V7.91667C14.5 6.85812 13.6419 6 12.5833 6H11.5C10.3954 6 9.5 6.89543 9.5 8C9.5 9.10457 10.3954 10 11.5 10H12.5C13.6046 10 14.5 10.8954 14.5 12C14.5 13.1046 13.6046 14 12.5 14H11.4583C10.3768 14 9.5 13.1232 9.5 12.0417V12" 
          stroke={color} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'edit': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
      >
        <path 
          d="M20.1498 7.93997L8.27978 19.81C7.21978 20.88 4.04977 21.3699 3.32977 20.6599C2.60977 19.9499 3.11978 16.78 4.17978 15.71L16.0498 3.84C16.5979 3.31801 17.3283 3.03097 18.0851 3.04019C18.842 3.04942 19.5652 3.35418 20.1004 3.88938C20.6356 4.42457 20.9403 5.14781 20.9496 5.90463C20.9588 6.66146 20.6718 7.39189 20.1498 7.93997V7.93997Z" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    ),
    
    'bookmark': (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
      >
        <path 
          d="M19.8978 16H7.89778C6.96781 16 6.50282 16 6.12132 16.1022C5.08604 16.3796 4.2774 17.1883 4 18.2235" 
          stroke={color} 
          strokeWidth="1.5"
        />
        <path 
          d="M8 7H16" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M8 10.5H13" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M13 16V19.5309C13 19.8065 13 19.9443 12.9051 20C12.8103 20.0557 12.6806 19.9941 12.4211 19.8708L11.1789 19.2808C11.0911 19.2391 11.0472 19.2182 11 19.2182C10.9528 19.2182 10.9089 19.2391 10.8211 19.2808L9.57889 19.8708C9.31943 19.9941 9.18971 20.0557 9.09485 20C9 19.9443 9 19.8065 9 19.5309V16.45" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
        <path 
          d="M10 22C7.17157 22 5.75736 22 4.87868 21.1213C4 20.2426 4 18.8284 4 16V8C4 5.17157 4 3.75736 4.87868 2.87868C5.75736 2 7.17157 2 10 2H14C16.8284 2 18.2426 2 19.1213 2.87868C20 3.75736 20 5.17157 20 8M14 22C16.8284 22 18.2426 22 19.1213 21.1213C20 20.2426 20 18.8284 20 16V12" 
          stroke={color} 
          strokeWidth="1.5" 
          strokeLinecap="round"
        />
      </svg>
    )
  };

  return iconSvgs[type] || null;
};

/**
 * 动画章节图标组件
 * 支持悬停和选中状态的动画效果
 */
interface AnimatedChapterIconProps extends ChapterIconProps {
  isActive?: boolean;
  isHovered?: boolean;
  animationType?: 'pulse' | 'bounce' | 'rotate' | 'scale';
}

export const AnimatedChapterIcon: React.FC<AnimatedChapterIconProps> = ({
  type,
  className = "",
  size = 16,
  color = "currentColor",
  isActive = false,
  isHovered = false,
  animationType = 'scale'
}) => {
  const getAnimationClass = () => {
    if (!isActive && !isHovered) return '';
    
    switch (animationType) {
      case 'pulse':
        return 'animate-pulse';
      case 'bounce':
        return 'animate-bounce';
      case 'rotate':
        return 'animate-spin';
      case 'scale':
        return 'transform transition-transform duration-200 hover:scale-110';
      default:
        return '';
    }
  };

  return (
    <div className={`inline-flex items-center justify-center ${getAnimationClass()}`}>
      <ChapterIcon 
        type={type}
        className={className}
        size={size}
        color={color}
      />
    </div>
  );
};

/**
 * 章节类型图标映射
 * 根据章节内容自动选择合适的图标
 */
export const getChapterIcon = (chapter: any): 'book-open' | 'book-sparkles' | 'book-section' | 'edit' | 'bookmark' => {
  const contentLength = chapter.content?.replace(/<[^>]*>/g, '').trim().length || 0;
  
  if (contentLength === 0) {
    return 'edit'; // 空章节显示编辑图标
  } else if (contentLength < 500) {
    return 'book-open'; // 少量内容显示打开的书
  } else if (contentLength < 2000) {
    return 'book-section'; // 中等内容显示章节图标
  } else {
    return 'book-sparkles'; // 丰富内容显示闪亮的书
  }
};
