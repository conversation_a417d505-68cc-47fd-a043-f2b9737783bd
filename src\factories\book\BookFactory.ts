import { IBookFactory } from './interfaces/IBookFactory';
import { ICreateBookDialogComponent } from './interfaces/ICreateBookDialogComponent';
import { DefaultCreateBookDialogComponent } from './components/DefaultCreateBookDialog';

/**
 * 默认书籍工厂实现
 */
class DefaultBookFactory implements IBookFactory {
  /**
   * 创建书籍对话框组件
   */
  createBookDialogComponent(): ICreateBookDialogComponent {
    return new DefaultCreateBookDialogComponent();
  }
}

/**
 * 创建书籍工厂
 * @param style 样式，默认为'default'
 * @returns 书籍工厂实例
 */
export function createBookFactory(style: 'default' | 'fancy' = 'default'): IBookFactory {
  switch (style) {
    case 'default':
    default:
      return new DefaultBookFactory();
  }
}
