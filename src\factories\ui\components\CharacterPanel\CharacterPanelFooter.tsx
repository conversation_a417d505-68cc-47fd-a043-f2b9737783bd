"use client";

import React from 'react';

interface CharacterPanelFooterProps {
  characterCount: number;
}

/**
 * 人物面板底部组件
 */
export const CharacterPanelFooter: React.FC<CharacterPanelFooterProps> = ({
  characterCount
}) => {
  return (
    <div className="flex justify-center items-center px-2 py-3">
      <div
        className="text-base rounded-full px-4 py-1"
        style={{
          color: 'var(--color-white)',
          backgroundColor: 'var(--color-primary)',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          transform: characterCount > 0 ? 'scale(1)' : 'scale(0.95)',
          opacity: characterCount > 0 ? 1 : 0.8
        }}
      >
        共 {characterCount} 个人物
      </div>
    </div>
  );
};
