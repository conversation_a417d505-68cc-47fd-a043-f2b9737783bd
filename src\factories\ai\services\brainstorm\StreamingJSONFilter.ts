/**
 * 流式JSON过滤器
 * 用于在流式响应中实时过滤JSON工具调用内容，确保用户看到自然的对话
 */

export class StreamingJSONFilter {
  private state: 'normal' | 'detecting_start' | 'in_json' | 'detecting_end' = 'normal';
  private buffer = '';
  private braceCount = 0;
  private lastChunk = '';

  /**
   * 过滤单个chunk中的JSON内容
   */
  public filter(chunk: string): string {
    let result = '';
    let i = 0;

    // 将当前chunk与上一个chunk的末尾拼接，以处理跨chunk的模式
    const fullText = this.lastChunk + chunk;
    this.lastChunk = chunk.slice(-20); // 保留最后20个字符用于下次检测

    while (i < chunk.length) {
      const char = chunk[i];
      const position = this.lastChunk.length - 20 + i;

      switch (this.state) {
        case 'normal':
          // 检测JSON开始标记
          if (this.detectJsonStart(fullText, position)) {
            this.state = 'detecting_start';
            this.buffer = '';
            this.braceCount = 0;
            // 跳过已检测的字符
            i = this.skipJsonStartMarker(chunk, i);
            continue;
          } else {
            result += char;
          }
          break;

        case 'detecting_start':
          this.buffer += char;
          if (char === '{') {
            this.state = 'in_json';
            this.braceCount = 1;
          } else if (char.trim() === '' || char === '\n') {
            // 跳过空白字符
          } else {
            // 不是JSON，恢复正常状态
            this.state = 'normal';
            result += this.buffer;
            this.buffer = '';
          }
          break;

        case 'in_json':
          this.buffer += char;
          if (char === '{') {
            this.braceCount++;
          } else if (char === '}') {
            this.braceCount--;
            if (this.braceCount === 0) {
              // JSON结束，检查是否有结束标记
              this.state = 'detecting_end';
            }
          }
          break;

        case 'detecting_end':
          this.buffer += char;
          if (this.detectJsonEnd(this.buffer)) {
            // 完整的JSON代码块，完全丢弃
            this.state = 'normal';
            this.buffer = '';
            // 跳过结束标记
            i = this.skipJsonEndMarker(chunk, i);
            continue;
          } else if (char.trim() === '' || char === '\n') {
            // 继续等待结束标记
          } else {
            // 没有结束标记，可能是裸JSON，也丢弃
            this.state = 'normal';
            this.buffer = '';
          }
          break;
      }

      i++;
    }

    return result;
  }

  /**
   * 检测JSON开始标记
   */
  private detectJsonStart(text: string, position: number): boolean {
    // 检查```json模式
    const beforePosition = Math.max(0, position - 10);
    const segment = text.slice(beforePosition, position + 10);
    
    return /```json\s*$/.test(segment.slice(0, segment.length - 10)) ||
           /```json\s*\{/.test(segment);
  }

  /**
   * 检测JSON结束标记
   */
  private detectJsonEnd(buffer: string): boolean {
    return /\}\s*```\s*$/.test(buffer);
  }

  /**
   * 跳过JSON开始标记
   */
  private skipJsonStartMarker(chunk: string, currentIndex: number): number {
    // 查找```json的结束位置
    const remaining = chunk.slice(currentIndex);
    const match = remaining.match(/^```json\s*/);
    if (match) {
      return currentIndex + match[0].length;
    }
    return currentIndex;
  }

  /**
   * 跳过JSON结束标记
   */
  private skipJsonEndMarker(chunk: string, currentIndex: number): number {
    // 查找```的结束位置
    const remaining = chunk.slice(currentIndex);
    const match = remaining.match(/^\s*```\s*/);
    if (match) {
      return currentIndex + match[0].length;
    }
    return currentIndex;
  }

  /**
   * 重置过滤器状态
   */
  public reset(): void {
    this.state = 'normal';
    this.buffer = '';
    this.braceCount = 0;
    this.lastChunk = '';
  }

  /**
   * 获取当前状态（用于调试）
   */
  public getState(): string {
    return this.state;
  }

  /**
   * 简化版过滤器，用于最终清理
   * 只移除工具调用指令，保留其他内容
   */
  public static cleanFinalResponse(content: string): string {
    console.log('🧹 开始清理最终响应，原始长度:', content.length);

    const cleaned = content
      // 移除包含action字段的JSON代码块（工具调用指令）
      .replace(/```json\s*\{\s*"action"[\s\S]*?\}\s*```/g, '')
      // 移除裸JSON工具调用指令（包含action字段的）
      .replace(/\{\s*"action"\s*:\s*"[^"]*"[\s\S]*?\}/g, '')
      // 移除工具调用相关的标记和EMOJI
      .replace(/🔧\s*工具调用[^`]*```?/g, '')
      .replace(/``\s*🔧\s*工具调用[^`]*```?/g, '')
      .replace(/```?\s*🔧[^`]*```?/g, '')
      // 移除残留的代码块标记
      .replace(/```json\s*```/g, '')
      .replace(/```json\s*$/gm, '')
      .replace(/^\s*```\s*$/gm, '')
      .replace(/``\s*$/gm, '')
      .replace(/^\s*``\s*$/gm, '')
      // 移除孤立的工具调用文本
      .replace(/^\s*🔧\s*工具调用[^\n]*$/gm, '')
      .replace(/^\s*工具调用[^\n]*$/gm, '')
      // 清理多余的空行，但保留正常的段落间距
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .replace(/^\s*\n+/g, '')
      .replace(/\n+\s*$/g, '')
      .trim();

    console.log('✅ 响应清理完成，清理后长度:', cleaned.length);

    return cleaned;
  }
}

/**
 * 全局JSON过滤器实例
 * 用于在整个对话过程中保持状态
 */
export class GlobalJSONFilter {
  private static instance: StreamingJSONFilter | null = null;

  public static getInstance(): StreamingJSONFilter {
    if (!this.instance) {
      this.instance = new StreamingJSONFilter();
    }
    return this.instance;
  }

  public static reset(): void {
    if (this.instance) {
      this.instance.reset();
    }
  }

  public static filter(chunk: string): string {
    return this.getInstance().filter(chunk);
  }
}
