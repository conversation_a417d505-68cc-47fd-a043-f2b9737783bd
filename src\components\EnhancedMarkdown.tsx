"use client";

import React, { useEffect, useState } from 'react';
import { enhanceBrackets } from '@/utils/markdown/MarkdownEnhancer';
import DOMPurify from 'isomorphic-dompurify';
import HTMLPreviewModal from './HTMLPreviewModal';

// 扩展Window接口，添加openHtmlPreviewModal函数和lastHtmlRenderContent变量
declare global {
  interface Window {
    openHtmlPreviewModal?: (htmlContent: string, title?: string) => void;
    lastHtmlRenderContent?: string;
  }
}

interface EnhancedMarkdownProps {
  content: string;
  className?: string;
  htmlMode?: boolean; // 是否启用HTML模式，允许直接渲染HTML标签
  allowScripts?: boolean; // 是否允许执行脚本，用于支持图表等交互式内容
}

/**
 * 增强的Markdown渲染组件
 * 支持彩色括号、语法高亮等增强功能，以及HTML模式
 * 支持HTML渲染代码块，可以在弹窗中查看渲染后的HTML内容
 */
const EnhancedMarkdown: React.FC<EnhancedMarkdownProps> = ({
  content,
  className = '',
  htmlMode = false,
  allowScripts = false
}) => {
  // 处理后的内容
  const [processedContent, setProcessedContent] = useState<string>('');

  // HTML预览模态窗口状态
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);
  const [previewHtmlContent, setPreviewHtmlContent] = useState<string>('');
  const [previewTitle, setPreviewTitle] = useState<string>('HTML预览');

  useEffect(() => {
    // 处理Markdown内容
    processMarkdown(content);
  }, [content, htmlMode, allowScripts]);

  /**
   * 处理Markdown内容
   * @param markdownContent Markdown内容
   */
  const processMarkdown = (markdownContent: string) => {
    if (!markdownContent) {
      setProcessedContent('');
      return;
    }

    // 配置DOMPurify选项，根据allowScripts决定是否允许脚本
    // 注意：我们扩展了允许的标签和属性列表，以支持更复杂的HTML结构
    const purifyOptions = {
      ADD_TAGS: [
        'svg', 'path', 'circle', 'rect', 'line', 'polyline', 'polygon', 'ellipse', 'g', 'text', 'tspan', 'canvas',
        'script', 'style', 'link', 'meta', 'title', 'head', 'body', 'html', 'div', 'span', 'p', 'a', 'img',
        'table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot', 'ul', 'ol', 'li', 'dl', 'dt', 'dd',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'button', 'input', 'select', 'option', 'textarea', 'form',
        'iframe', 'audio', 'video', 'source', 'track', 'embed', 'object', 'param'
      ],
      ADD_ATTR: [
        // SVG属性
        'viewBox', 'd', 'cx', 'cy', 'r', 'x', 'y', 'width', 'height', 'fill', 'stroke', 'stroke-width',
        'transform', 'class', 'style',
        // 事件处理属性
        'onclick', 'onload', 'onmouseover', 'onmouseout', 'onchange', 'onkeyup', 'onkeydown', 'onkeypress',
        'onfocus', 'onblur', 'oninput', 'onsubmit', 'onreset', 'onselect', 'onscroll',
        // 通用属性
        'id', 'name', 'data-*', 'src', 'href', 'target', 'rel', 'alt', 'title', 'placeholder', 'value',
        'type', 'method', 'action', 'enctype', 'disabled', 'checked', 'selected', 'readonly', 'required',
        'max', 'min', 'step', 'maxlength', 'pattern', 'for', 'xmlns', 'xmlns:*', 'version', 'charset',
        'content', 'http-equiv', 'language', 'media', 'srcset', 'sizes', 'crossorigin', 'integrity',
        'controls', 'autoplay', 'loop', 'muted', 'preload', 'poster', 'download', 'datetime',
        'colspan', 'rowspan', 'scope', 'headers', 'abbr', 'valign', 'align', 'border', 'cellpadding',
        'cellspacing', 'frame', 'rules', 'summary', 'bgcolor', 'background'
      ],
      // 根据allowScripts决定是否禁止脚本标签和属性
      FORBID_TAGS: allowScripts ? [] : ['script'],
      FORBID_ATTR: allowScripts ? [] : ['onerror', 'onload', 'onclick', 'onmouseover'],
      // 允许数据URI
      ADD_DATA_URI_TAGS: ['img', 'source', 'video', 'audio', 'track', 'embed', 'object', 'iframe'],
      // 允许未知协议
      ALLOW_UNKNOWN_PROTOCOLS: true,
      // 允许脚本URL
      ALLOW_SCRIPT_URLS: allowScripts
    };

    // 如果启用HTML模式且内容看起来像纯HTML（以<开头），直接使用原始内容或轻度净化
    if (htmlMode && markdownContent.trim().startsWith('<') && !markdownContent.includes('```')) {
      if (allowScripts) {
        // 如果允许脚本，直接使用原始内容，不进行净化
        console.log('HTML模式 + 允许脚本：使用原始HTML内容，不进行净化');
        setProcessedContent(markdownContent);
      } else {
        // 如果不允许脚本，使用DOMPurify进行轻度净化
        console.log('HTML模式：使用DOMPurify轻度净化HTML内容');
        const cleanHtml = DOMPurify.sanitize(markdownContent, purifyOptions);
        setProcessedContent(cleanHtml);
      }
      return;
    }

    // 分行处理内容
    const lines = markdownContent.split('\n');
    let inHtmlRenderBlock = false;
    let htmlRenderContent = '';
    let htmlRenderTitle = 'HTML预览';

    const processedLines = lines.map((line) => {
      // 检测HTML渲染代码块的开始
      if (line.match(/^```html-render(\s+(.*))?$/)) {
        inHtmlRenderBlock = true;
        htmlRenderContent = ''; // 重置HTML内容

        // 提取可能的标题
        const titleMatch = line.match(/^```html-render\s+(.*)$/);
        if (titleMatch && titleMatch[1]) {
          htmlRenderTitle = titleMatch[1];
        } else {
          htmlRenderTitle = 'HTML预览';
        }

        // 生成唯一ID
        const uniqueId = Date.now() + '-' + Math.floor(Math.random() * 1000);
        return `<div class="html-render-block bg-[rgba(var(--color-primary-rgb),0.05)] p-4 rounded-lg my-4 border border-[var(--color-primary)] border-opacity-30">
          <div class="flex justify-between items-center mb-2">
            <h4 class="text-[var(--color-primary)] font-medium">${htmlRenderTitle}</h4>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 bg-[var(--color-primary)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center"
                id="html-preview-btn-${uniqueId}"
                type="button"
                onclick="
                  event.stopPropagation();
                  event.preventDefault();
                  const container = this.closest('.html-render-block').querySelector('.html-preview-container');
                  const previewDiv = container.querySelector('.html-preview-${uniqueId}');
                  const codeBlock = this.closest('.html-render-block').querySelector('.html-content');

                  if (container.style.display === 'none') {
                    // 显示预览
                    container.style.display = 'block';
                    previewDiv.innerHTML = codeBlock.textContent;
                    this.innerHTML = '<svg xmlns=\\'http://www.w3.org/2000/svg\\' class=\\'h-4 w-4 mr-1\\' fill=\\'none\\' viewBox=\\'0 0 24 24\\' stroke=\\'currentColor\\'><path stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\' stroke-width=\\'2\\' d=\\'M6 18L18 6M6 6l12 12\\' /></svg>隐藏预览';
                  } else {
                    // 隐藏预览
                    container.style.display = 'none';
                    this.innerHTML = '<svg xmlns=\\'http://www.w3.org/2000/svg\\' class=\\'h-4 w-4 mr-1\\' fill=\\'none\\' viewBox=\\'0 0 24 24\\' stroke=\\'currentColor\\'><path stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\' stroke-width=\\'2\\' d=\\'M15 12a3 3 0 11-6 0 3 3 0 016 0z\\' /><path stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\' stroke-width=\\'2\\' d=\\'M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\\' /></svg>预览HTML';
                  }
                "
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                预览HTML
              </button>
              <button
                class="px-3 py-1 bg-[var(--color-secondary)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center html-preview-modal-btn"
                id="html-modal-btn-${uniqueId}"
                type="button"
                data-action="preview-modal"
                data-html-id="${uniqueId}"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                </svg>
                弹窗查看
              </button>
            </div>
          </div>
          <pre class="bg-[rgba(var(--color-primary-rgb),0.02)] p-3 rounded-lg overflow-x-auto"><code class="language-html html-content">`;
      }

      // 收集HTML渲染代码块的内容
      if (inHtmlRenderBlock && !line.match(/^```html-render(\s+(.*))?$/) && !line.match(/^```$/)) {
        htmlRenderContent += line + '\n';
      }

      // 检测HTML渲染代码块的结束
      if (inHtmlRenderBlock && line.match(/^```$/)) {
        inHtmlRenderBlock = false;
        // 生成唯一ID用于DOM元素
        const uniqueId = Date.now() + '-' + Math.floor(Math.random() * 1000);

        // 记录收集到的HTML内容
        console.log('收集到的HTML内容长度:', htmlRenderContent.length);
        if (htmlRenderContent.length > 0) {
          console.log('HTML内容预览:', htmlRenderContent.substring(0, 100) + '...');
        }

        // 将收集到的内容存储到全局变量中，以便在弹窗中使用
        // 确保内容被完整保存，不进行任何净化处理
        window.lastHtmlRenderContent = htmlRenderContent;

        // 检查是否包含D3.js代码
        const containsD3 = htmlRenderContent.includes('d3.') || htmlRenderContent.includes('d3.select');

        // 为D3.js内容添加特殊标记，以便在预览时进行特殊处理
        if (containsD3) {
          console.log('检测到D3.js代码，添加特殊标记');
          window.lastHtmlRenderContent = `<!-- D3_CONTENT_MARKER -->\n${htmlRenderContent}`;
        }

        return `</code></pre>
          <div class="html-preview-container mt-4 p-4 border border-dashed border-[var(--color-primary)] border-opacity-30 rounded-lg" style="display: none;">
            <div class="html-preview-${uniqueId}"></div>
          </div>
          <div class="mt-2 flex justify-end">
            <button
              class="px-3 py-1 bg-[var(--color-secondary)] text-white rounded-lg hover:bg-opacity-90 text-sm shadow-sm transition-all flex items-center html-preview-modal-btn"
              type="button"
              data-action="preview-modal"
              data-html-id="${uniqueId}"
              onclick="
                event.stopPropagation();
                event.preventDefault();
                if (window.openHtmlPreviewModal && window.lastHtmlRenderContent) {
                  const title = this.closest('.html-render-block').querySelector('h4')?.textContent || 'HTML预览';
                  window.openHtmlPreviewModal(window.lastHtmlRenderContent, title);
                } else {
                  console.error('预览功能不可用');
                  alert('预览功能不可用');
                }
              "
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              在弹窗中查看（完整渲染）
            </button>
          </div>
          <script>
            (function() {
              // 内嵌预览按钮事件
              const previewBtn = document.getElementById('html-preview-btn-${uniqueId}');
              if (previewBtn) {
                previewBtn.addEventListener('click', function() {
                  const container = this.closest('.html-render-block').querySelector('.html-preview-container');
                  const previewDiv = container.querySelector('.html-preview-${uniqueId}');
                  const codeBlock = this.closest('.html-render-block').querySelector('.html-content');

                  if (container.style.display === 'none') {
                    // 显示预览
                    container.style.display = 'block';
                    previewDiv.innerHTML = codeBlock.textContent;
                    this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>隐藏预览';
                  } else {
                    // 隐藏预览
                    container.style.display = 'none';
                    this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>预览HTML';
                  }
                });
              }
            })();
          </script>
        </div>`;
      }

      // 收集HTML渲染代码块的内容
      if (inHtmlRenderBlock) {
        htmlRenderContent += line + '\n';
        return line;
      }

      // 处理标题
      if (line.match(/^#{1,6}\s/)) {
        const headingMatch = line.match(/^(#{1,6})\s/);
        const level = headingMatch ? headingMatch[1].length : 1;
        const text = line.replace(/^#{1,6}\s/, '');

        // 处理标题中的内容
        const processedText = processInlineElements(enhanceBrackets(text));

        return `<h${level} class="enhanced-heading-${level} text-[var(--color-primary)] font-bold my-4">${processedText}</h${level}>`;
      }

      // 处理列表项 - 支持嵌套和复杂格式
      if (line.match(/^\s*[-*+]\s/)) {
        const indentMatch = line.match(/^(\s*)/);
        const indent = indentMatch ? indentMatch[0].length : 0;
        const text = line.replace(/^\s*[-*+]\s/, '');

        // 处理列表项中的内容
        const processedText = processInlineElements(enhanceBrackets(text));

        const indentClass = indent > 0 ? `ml-${indent * 4}` : '';
        return `<div class="flex items-start my-1 ${indentClass}">
          <span class="mr-2 text-[var(--color-success)] font-bold">•</span>
          <span class="enhanced-list-item flex-1">${processedText}</span>
        </div>`;
      }

      // 处理有序列表项 - 支持嵌套和复杂格式
      if (line.match(/^\s*\d+\.\s/)) {
        const indentMatch = line.match(/^(\s*)/);
        const indent = indentMatch ? indentMatch[0].length : 0;
        const numberMatch = line.match(/^\s*(\d+)\./);
        const number = numberMatch ? numberMatch[1] : '1';
        const text = line.replace(/^\s*\d+\.\s/, '');

        // 处理列表项中的内容
        const processedText = processInlineElements(enhanceBrackets(text));

        const indentClass = indent > 0 ? `ml-${indent * 4}` : '';
        return `<div class="flex items-start my-1 ${indentClass}">
          <span class="mr-2 text-[var(--color-info)] font-bold">${number}.</span>
          <span class="enhanced-ordered-list-item flex-1">${processedText}</span>
        </div>`;
      }

      // 处理引用
      if (line.match(/^\s*>\s/)) {
        const text = line.replace(/^\s*>\s/, '');

        // 处理引用中的内容
        const processedText = processInlineElements(enhanceBrackets(text));

        return `<blockquote class="enhanced-blockquote border-l-4 border-[var(--color-primary)] pl-4 italic my-2 text-[var(--color-text-secondary)]">${processedText}</blockquote>`;
      }

      // 处理普通代码块
      if (line.match(/^```(\w*)/)) {
        const langMatch = line.match(/^```(\w*)/);
        const lang = langMatch && langMatch[1] ? langMatch[1] : '';
        const langDisplay = lang || 'text';
        return `<pre class="enhanced-code-block bg-[rgba(var(--color-primary-rgb),0.05)] p-4 rounded-lg my-4 overflow-x-auto" data-language="${langDisplay}"><code class="language-${lang}">`;
      }

      if (line.match(/^```$/)) {
        return '</code></pre>';
      }

      // 处理水平线
      if (line.match(/^---+$/)) {
        return '<hr class="enhanced-hr border-t-2 border-[var(--color-secondary)] my-6" />';
      }

      // 处理特殊格式: * ** 格式 (带星号的粗体)
      if (line.match(/^\s*\*\s+\*\*(.*?)\*\*\s*$/)) {
        const match = line.match(/^\s*\*\s+\*\*(.*?)\*\*\s*$/);
        if (match && match[1]) {
          const text = match[1];
          // 处理内容
          const processedText = processInlineElements(enhanceBrackets(text));

          return `<div class="flex items-start my-2 enhanced-special-format">
            <span class="text-[var(--color-danger)] text-xl mr-2">*</span>
            <span class="font-bold text-[var(--color-primary)] flex-1">${processedText}</span>
          </div>`;
        }
      }

      // 处理空行
      if (line.trim() === '') {
        return '<div class="h-4"></div>';
      }

      // 处理普通文本
      // 处理文本中的括号和其他内联元素
      const processedText = processInlineElements(enhanceBrackets(line));

      return `<p class="enhanced-paragraph my-2">${processedText}</p>`;
    });

    // 合并处理后的行，并进行后处理
    let html = processedLines.join('\n');

    // 处理嵌套列表和其他结构
    html = postProcessHtml(html);

    // 配置DOMPurify选项，保留按钮ID和事件处理
    // 扩展允许的标签和属性列表，以支持更复杂的HTML结构
    const finalPurifyOptions = {
      ADD_TAGS: [
        'svg', 'path', 'circle', 'rect', 'line', 'polyline', 'polygon', 'ellipse', 'g', 'text', 'tspan', 'canvas',
        'script', 'style', 'link', 'meta', 'title', 'head', 'body', 'html', 'div', 'span', 'p', 'a', 'img',
        'table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot', 'ul', 'ol', 'li', 'dl', 'dt', 'dd',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'button', 'input', 'select', 'option', 'textarea', 'form',
        'iframe', 'audio', 'video', 'source', 'track', 'embed', 'object', 'param'
      ],
      ADD_ATTR: [
        // SVG属性
        'viewBox', 'd', 'cx', 'cy', 'r', 'x', 'y', 'width', 'height', 'fill', 'stroke', 'stroke-width',
        'transform', 'class', 'style',
        // 事件处理属性
        'onclick', 'onload', 'onmouseover', 'onmouseout', 'onchange', 'onkeyup', 'onkeydown', 'onkeypress',
        'onfocus', 'onblur', 'oninput', 'onsubmit', 'onreset', 'onselect', 'onscroll',
        // 通用属性
        'id', 'name', 'data-*', 'src', 'href', 'target', 'rel', 'alt', 'title', 'placeholder', 'value',
        'type', 'method', 'action', 'enctype', 'disabled', 'checked', 'selected', 'readonly', 'required',
        'max', 'min', 'step', 'maxlength', 'pattern', 'for', 'xmlns', 'xmlns:*', 'version', 'charset',
        'content', 'http-equiv', 'language', 'media', 'srcset', 'sizes', 'crossorigin', 'integrity',
        'controls', 'autoplay', 'loop', 'muted', 'preload', 'poster', 'download', 'datetime',
        'colspan', 'rowspan', 'scope', 'headers', 'abbr', 'valign', 'align', 'border', 'cellpadding',
        'cellspacing', 'frame', 'rules', 'summary', 'bgcolor', 'background',
        'stroke-linecap', 'stroke-linejoin'
      ],
      // 允许数据属性
      ALLOW_DATA_ATTR: true,
      // 允许未知协议
      ALLOW_UNKNOWN_PROTOCOLS: true,
      // 允许脚本URL
      ALLOW_SCRIPT_URLS: allowScripts,
      // 根据allowScripts决定是否禁止脚本标签和属性
      FORBID_TAGS: allowScripts ? [] : ['script'],
      FORBID_ATTR: allowScripts ? [] : ['onerror', 'onload', 'onclick', 'onmouseover']
    };

    // 使用DOMPurify清理HTML，但保留按钮ID和必要的属性
    // 如果允许脚本，则使用更宽松的净化设置
    let processedHtml;
    if (allowScripts) {
      console.log('允许脚本模式：使用轻度净化');
      processedHtml = DOMPurify.sanitize(html, finalPurifyOptions);
    } else {
      console.log('标准模式：使用标准净化');
      processedHtml = DOMPurify.sanitize(html, finalPurifyOptions);
    }

    setProcessedContent(processedHtml);
  };

  /**
   * 处理内联元素（粗体、斜体、代码等）
   * @param text 要处理的文本
   * @returns 处理后的HTML
   */
  const processInlineElements = (text: string): string => {
    // 处理粗体和斜体的组合 ***text***
    let processed = text.replace(/\*\*\*(.*?)\*\*\*/g, '<strong class="enhanced-strong"><em class="enhanced-em">$1</em></strong>');

    // 处理粗体 **text**
    processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong class="enhanced-strong text-[var(--color-primary)]">$1</strong>');

    // 处理斜体 *text*
    processed = processed.replace(/\*(.*?)\*/g, '<em class="enhanced-em text-[var(--color-info)] italic">$1</em>');

    // 处理下划线 __text__
    processed = processed.replace(/__(.*?)__/g, '<u class="enhanced-underline border-b-2 border-[var(--color-primary)]">$1</u>');

    // 处理删除线 ~~text~~
    processed = processed.replace(/~~(.*?)~~/g, '<del class="enhanced-del text-[var(--color-text-hint)] line-through">$1</del>');

    // 处理行内代码 `text`
    processed = processed.replace(/`(.*?)`/g, '<code class="enhanced-inline-code bg-[rgba(var(--color-primary-rgb),0.1)] px-1.5 py-0.5 rounded text-[var(--color-primary)]">$1</code>');

    // 处理链接 [text](url)
    processed = processed.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="enhanced-link text-[var(--color-info)] underline hover:text-[var(--color-primary)]">$1</a>');

    // 处理高亮 ==text==
    processed = processed.replace(/==(.*?)==/g, '<mark class="enhanced-mark bg-[rgba(var(--color-warning-rgb),0.2)] px-1">$1</mark>');

    return processed;
  };

  /**
   * 对生成的HTML进行后处理
   * @param html 原始HTML
   * @returns 处理后的HTML
   */
  const postProcessHtml = (html: string): string => {
    // 这里可以添加更多的后处理逻辑
    return html;
  };

  /**
   * 打开HTML预览模态窗口
   * @param htmlContent HTML内容
   * @param title 标题
   */
  const openHtmlPreviewModal = (htmlContent: string, title: string = 'HTML预览') => {
    setPreviewHtmlContent(htmlContent);
    setPreviewTitle(title);
    setIsPreviewModalOpen(true);
  };

  /**
   * 关闭HTML预览模态窗口
   */
  const closeHtmlPreviewModal = () => {
    setIsPreviewModalOpen(false);
  };

  // 将openHtmlPreviewModal函数添加到window对象，以便在内联脚本中调用
  useEffect(() => {
    // 添加全局函数
    window.openHtmlPreviewModal = openHtmlPreviewModal;

    // 添加事件委托，处理所有弹窗查看按钮的点击事件
    const handleDocumentClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      // 查找最近的按钮元素（通过ID前缀或类名）
      const button = target.closest('button[id^="html-modal-btn-"], button.html-preview-modal-btn');

      if (button) {
        e.preventDefault();
        e.stopPropagation();

        console.log('HTML预览按钮被点击:', button);

        // 查找HTML内容和标题
        const htmlRenderBlock = button.closest('.html-render-block');
        if (htmlRenderBlock) {
          const codeBlock = htmlRenderBlock.querySelector('.html-content');
          const titleElement = htmlRenderBlock.querySelector('h4');

          if (codeBlock && titleElement) {
            // 获取HTML内容，优先使用全局变量中存储的内容
            let htmlContent = '';

            // 首先尝试使用全局变量中存储的内容
            if (window.lastHtmlRenderContent && window.lastHtmlRenderContent.trim()) {
              console.log('使用全局变量中存储的HTML内容');
              htmlContent = window.lastHtmlRenderContent;
            }
            // 如果全局变量为空，尝试使用textContent
            else if (codeBlock.textContent && codeBlock.textContent.trim()) {
              console.log('使用textContent获取HTML内容');
              htmlContent = codeBlock.textContent;
            }
            // 如果textContent为空，尝试使用innerHTML
            else if (codeBlock.innerHTML && codeBlock.innerHTML.trim()) {
              console.log('使用innerHTML获取HTML内容');
              htmlContent = codeBlock.innerHTML;
            }
            // 如果都为空，尝试直接获取元素的outerHTML
            else if (codeBlock.outerHTML) {
              console.log('使用outerHTML获取HTML内容');
              // 提取<code>标签内的内容
              const match = codeBlock.outerHTML.match(/<code[^>]*>([\s\S]*?)<\/code>/i);
              if (match && match[1]) {
                htmlContent = match[1];
              }
            }

            // 检查是否是D3.js代码（通过内容或特殊标记）
            const isD3Content = htmlContent.includes('d3.') ||
                               htmlContent.includes('d3.select') ||
                               htmlContent.includes('<!-- D3_CONTENT_MARKER -->');

            if (isD3Content) {
              console.log('检测到D3.js代码，准备特殊处理');

              // 为D3.js内容添加特殊标记，以便在HTMLPreviewModal中识别
              // 保留原始内容，不进行任何包装，让HTMLPreviewModal处理
              htmlContent = `<!-- D3_CONTENT_MARKER -->\n${htmlContent}`;

              // 记录D3.js内容
              console.log('D3.js内容已标记，长度:', htmlContent.length);
              console.log('D3.js内容预览:', htmlContent.substring(0, 100) + '...');
            }

            const title = titleElement.textContent || 'HTML预览';

            console.log('打开HTML预览模态窗口:', {
              title,
              contentLength: htmlContent.length,
              contentPreview: htmlContent.substring(0, 100) + '...'
            });

            // 确保HTML内容非空
            if (htmlContent.trim()) {
              openHtmlPreviewModal(htmlContent, title);
            } else {
              console.error('HTML内容为空');
              alert('HTML内容为空，无法预览');
            }
          } else {
            console.error('找不到HTML内容或标题元素:', { codeBlock, titleElement });
          }
        } else {
          console.error('找不到HTML渲染块:', button);
        }
      }
    };

    // 添加全局点击事件监听器
    document.addEventListener('click', handleDocumentClick);

    // 清理函数
    return () => {
      delete window.openHtmlPreviewModal;
      document.removeEventListener('click', handleDocumentClick);
    };
  }, []);

  return (
    <>
      <div
        className={`enhanced-markdown ${className}`}
        dangerouslySetInnerHTML={{ __html: processedContent }}
      />

      {/* HTML预览模态窗口 */}
      <HTMLPreviewModal
        isOpen={isPreviewModalOpen}
        onClose={closeHtmlPreviewModal}
        htmlContent={previewHtmlContent}
        title={previewTitle}
      />
    </>
  );
};

// 已安装isomorphic-dompurify包

export default EnhancedMarkdown;
