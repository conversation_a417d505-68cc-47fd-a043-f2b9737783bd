/* Enhanced <PERSON><PERSON> Styles - 基于设计思考的现代化按钮样式 */

/* 基础动画关键帧 - 优化的动效系统 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

@keyframes success-celebration {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes breathing {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-1px);
  }
  75% {
    transform: translateX(1px);
  }
}

/* Enhanced <PERSON><PERSON> 基础类 */
.enhanced-button {
  position: relative;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  will-change: transform, box-shadow;
}

/* Primary Button 增强样式 - 最高视觉权重 */
.enhanced-button--primary {
  background: linear-gradient(135deg, #3b82f6, #6366f1, #8b5cf6);
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
  position: relative;
}

.enhanced-button--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  border-radius: inherit;
  pointer-events: none;
}

.enhanced-button--primary:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
  animation-duration: 2s;
}

.enhanced-button--primary:active {
  transform: translateY(-1px) scale(0.95);
  transition-duration: 0.15s;
}

.enhanced-button--primary:focus {
  animation: pulse-glow 1.5s infinite;
}

/* 主要按钮的呼吸动画 - 引导用户注意 */
.enhanced-button--primary.breathing {
  animation: breathing 3s ease-in-out infinite;
}

/* Secondary Button 增强样式 */
.enhanced-button--secondary {
  position: relative;
  background: transparent;
  border: 2px solid #3b82f6;
  color: #3b82f6;
}

.enhanced-button--secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.enhanced-button--secondary:hover::before {
  left: 100%;
}

.enhanced-button--secondary:hover {
  border-color: #2563eb;
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

/* Danger Button 增强样式 - 警告视觉权重 */
.enhanced-button--danger {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
  position: relative;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.enhanced-button--danger:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.5);
  animation: shake 0.5s ease-in-out;
}

.enhanced-button--danger:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.2));
  pointer-events: none;
  border-radius: inherit;
}

.enhanced-button--danger:active {
  transform: translateY(-1px) scale(0.95);
  transition-duration: 0.15s;
}

/* Text Button 增强样式 */
.enhanced-button--text {
  position: relative;
  background: transparent;
  color: #3b82f6;
}

.enhanced-button--text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: #3b82f6;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.enhanced-button--text:hover::after {
  width: 100%;
}

.enhanced-button--text:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* 加载状态样式 */
.enhanced-button--loading {
  pointer-events: none;
  position: relative;
}

.enhanced-button--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(1px);
}

/* 成功状态样式 - 庆祝效果 */
.enhanced-button--success {
  background: linear-gradient(135deg, #10b981, #059669, #047857) !important;
  animation: success-celebration 0.8s ease;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.enhanced-button--success::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: success-ripple 0.6s ease-out;
}

@keyframes success-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 波纹效果 */
.button-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: ripple 0.4s linear;
  pointer-events: none;
}

/* 尺寸变体 */
.enhanced-button--small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
}

.enhanced-button--medium {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
}

.enhanced-button--large {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  border-radius: 0.75rem;
}

/* 禁用状态 */
.enhanced-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  animation: none !important;
}

.enhanced-button:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-button {
    min-height: 44px; /* 增大移动端触摸区域 */
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  /* 移动端取消悬停效果 */
  .enhanced-button:hover {
    transform: none;
    box-shadow: none;
  }

  /* 增强移动端点击反馈 */
  .enhanced-button:active {
    transform: scale(0.95);
    transition-duration: 0.1s;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .enhanced-button--secondary {
    border-color: #60a5fa;
    color: #60a5fa;
  }

  .enhanced-button--secondary:hover {
    background-color: rgba(96, 165, 250, 0.1);
    border-color: #93c5fd;
    color: #93c5fd;
  }

  .enhanced-button--text {
    color: #60a5fa;
  }

  .enhanced-button--text::after {
    background: #60a5fa;
  }

  .enhanced-button--text:hover {
    background-color: rgba(96, 165, 250, 0.1);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .enhanced-button {
    border-width: 2px;
    border-style: solid;
  }

  .enhanced-button--primary {
    border-color: #1e40af;
  }

  .enhanced-button--danger {
    border-color: #991b1b;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-button,
  .enhanced-button::before,
  .enhanced-button::after {
    animation: none !important;
    transition-duration: 0.1s !important;
  }

  .enhanced-button:hover {
    transform: none !important;
  }
}

/* 布局优化样式 - 简化的工具栏和按钮组 */

/* 工具栏容器 */
.toolbar-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

@media (min-width: 1024px) {
  .toolbar-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 0;
  }
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

/* 按钮分隔符 */
.button-separator {
  width: 1px;
  height: 1.5rem;
  background-color: #e5e7eb;
  margin: 0 0.75rem;
}

@media (max-width: 1023px) {
  .button-separator {
    display: none;
  }
}

/* 记录统计 */
.record-count {
  font-size: 0.875rem;
  color: #6b7280;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 响应式间距优化 */
@media (max-width: 640px) {
  .button-group {
    gap: 0.375rem;
  }

  .toolbar-container {
    gap: 0.5rem;
  }
}

/* 性能优化 - 减少重排重绘 */
.toolbar-container,
.button-group {
  will-change: auto;
  contain: layout style;
}

/* 创建按钮特殊样式 - 基于SVGRepo优化设计 */
.create-button-enhanced {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  color: var(--color-primary);
  border: 2px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 创建按钮光晕扫描效果 */
.create-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    rgba(251, 191, 36, 0.2),
    transparent
  );
  transition: left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 12px;
}

.create-button-enhanced:hover::before {
  left: 100%;
}

/* 创建按钮悬停状态 */
.create-button-enhanced:hover {
  transform: translateY(-3px) scale(1.12);
  box-shadow:
    0 12px 30px rgba(59, 130, 246, 0.35),
    0 6px 15px rgba(59, 130, 246, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.1);
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border-color: rgba(59, 130, 246, 0.4);
}

/* 创建按钮点击状态 */
.create-button-enhanced:active {
  transform: translateY(-1px) scale(1.02);
  transition: transform 0.1s ease-out;
}

/* 创建按钮图标动画 */
.create-button-enhanced .icon-container {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.create-button-enhanced:hover .icon-container {
  transform: rotate(12deg) scale(1.1);
}

/* 文档图标特殊效果 */
.create-button-enhanced .document-icon {
  transition: all 0.3s ease;
}

.create-button-enhanced:hover .document-icon {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
}

/* 星光效果动画 */
@keyframes star-twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.create-button-enhanced .star-effect {
  animation: star-twinkle 1.5s ease-in-out infinite;
}

/* 多层文档效果 */
.create-button-enhanced .multi-document-layer {
  transition: all 0.3s ease;
}

.create-button-enhanced:hover .multi-document-layer {
  transform: translateX(2px) translateY(-2px);
}

/* 加号动画 */
@keyframes plus-expand {
  0% {
    stroke-width: 2;
  }
  50% {
    stroke-width: 3;
  }
  100% {
    stroke-width: 2;
  }
}

.create-button-enhanced:hover .plus-icon {
  animation: plus-expand 1s ease-in-out infinite;
}

/* 创建按钮禁用状态 */
.create-button-enhanced:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
  animation: none !important;
}

.create-button-enhanced:disabled::before {
  display: none;
}
