"use client";

import React, { useState, useEffect } from 'react';
import { WorldBuilding, PromptTemplate, PromptCategory } from '@/lib/db/dexie';
import { AIWorldBuildingExtractorAdapter } from '@/adapters/ai/AIWorldBuildingExtractorAdapter';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { saveChapterAssociation } from '@/utils/chapterAssociation';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  title?: string;
  content?: string;
  order?: number;
  bookId?: string;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorldBuildingBatchUpdaterDialogProps {
  worldBuildings: WorldBuilding[];
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (updatedWorldBuildings: WorldBuilding[]) => void;
  bookId: string;
}

/**
 * 世界观批量更新对话框组件
 */
export const WorldBuildingBatchUpdaterDialog: React.FC<WorldBuildingBatchUpdaterDialogProps> = ({
  worldBuildings,
  isOpen,
  onClose,
  onUpdate,
  bookId
}) => {
  // 章节列表
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  // 选中的章节ID列表
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);
  // 选中的世界观元素ID列表
  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>([]);
  // 自定义提示词
  const [customPrompt, setCustomPrompt] = useState('');
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 更新后的世界观
  const [updatedWorldBuildings, setUpdatedWorldBuildings] = useState<WorldBuilding[]>([]);
  // 当前处理的世界观索引
  const [currentIndex, setCurrentIndex] = useState(0);
  // 总处理数量
  const [totalToProcess, setTotalToProcess] = useState(0);
  // 已处理数量
  const [processedCount, setProcessedCount] = useState(0);
  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');
  // 成功消息
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  // 提示词模板名称
  const [templateName, setTemplateName] = useState('');
  // 提示词模板描述
  const [templateDescription, setTemplateDescription] = useState('');
  // 是否显示保存模板表单
  const [showSaveTemplateForm, setShowSaveTemplateForm] = useState(false);
  // 世界观AI适配器
  const worldBuildingAIAdapter = new AIWorldBuildingExtractorAdapter();
  // 提示词模板管理器状态
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);

  // 加载章节列表
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
    }
  }, [isOpen, bookId]);

  // 加载章节列表
  const loadChapters = async () => {
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 utils/chapterAssociation 中的 getBookChapters
      try {
        const { getBookChapters } = await import('@/utils/chapterAssociation');
        const chaptersData = await getBookChapters(bookId);

        console.log('通过 utils/chapterAssociation.getBookChapters 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          return;
        }
      } catch (error) {
        console.error('通过 utils/chapterAssociation.getBookChapters 获取章节数据失败:', error);
      }

      // 尝试使用 src/db/chapterRepository.ts
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 CharacterAIAdapter 的 getChapterList 方法
      try {
        const CharacterAIAdapter = (await import('@/adapters/ai/CharacterAIAdapter')).default;
        const characterAIAdapter = new CharacterAIAdapter();
        const chapterList = await characterAIAdapter.getChapterList(bookId);

        console.log('通过 CharacterAIAdapter.getChapterList 获取到章节数据:', chapterList);

        if (chapterList && chapterList.length > 0) {
          setChapters(chapterList);
          console.log('加载了章节:', chapterList.length);
          return;
        }
      } catch (error) {
        console.error('通过 CharacterAIAdapter.getChapterList 获取章节数据失败:', error);
      }

      // 尝试使用 db 直接查询
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
      setError('无法获取章节数据，请检查网络连接或联系管理员');
    } catch (error) {
      console.error('加载章节列表失败:', error);
      setError('加载章节列表失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 处理章节选择变化
  const handleChapterSelectionChange = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 处理全选/取消全选章节
  const handleSelectAllChapters = (checked: boolean) => {
    if (checked) {
      setSelectedChapterIds(chapters.map(chapter => chapter.id!).filter(Boolean));
    } else {
      setSelectedChapterIds([]);
    }
  };

  /**
   * 选择章节范围
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect') => {
    // 验证输入
    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end)) {
      setError('请输入有效的章节编号');
      return;
    }

    if (start > end) {
      setError('起始章节编号不能大于结束章节编号');
      return;
    }

    if (start < 1 || end > chapters.length) {
      setError(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  // 处理世界观元素选择变化
  const handleWorldBuildingSelectionChange = (worldBuildingId: string) => {
    setSelectedWorldBuildingIds(prev => {
      if (prev.includes(worldBuildingId)) {
        return prev.filter(id => id !== worldBuildingId);
      } else {
        return [...prev, worldBuildingId];
      }
    });
  };

  // 处理全选/取消全选世界观元素
  const handleSelectAllWorldBuildings = (checked: boolean) => {
    if (checked) {
      setSelectedWorldBuildingIds(worldBuildings.map(wb => wb.id!).filter(Boolean));
    } else {
      setSelectedWorldBuildingIds([]);
    }
  };

  // 批量更新世界观
  const updateWorldBuildings = async () => {
    if (selectedWorldBuildingIds.length === 0) {
      setError('请至少选择一个世界观元素');
      return;
    }

    // 不再要求必须选择章节

    setIsLoading(true);
    setError(null);
    setCurrentIndex(0);
    setProcessedCount(0);
    setTotalToProcess(selectedWorldBuildingIds.length);
    setUpdatedWorldBuildings([]);

    try {
      // 获取选中章节的内容
      const selectedChapters = chapters.filter(chapter => selectedChapterIds.includes(chapter.id!));

      // 获取选中的世界观元素
      const selectedWBs = worldBuildings.filter(wb => selectedWorldBuildingIds.includes(wb.id!));

      // 逐个更新世界观元素
      const updatedWBs: WorldBuilding[] = [];

      for (let i = 0; i < selectedWBs.length; i++) {
        setCurrentIndex(i);
        const wb = selectedWBs[i];

        try {
          // 使用AI更新世界观
          let result;

          // 如果有选择章节，则使用选择的章节内容
          if (selectedChapterIds.length > 0) {
            result = await worldBuildingAIAdapter.updateWorldBuildingFromChapters(
              wb,
              selectedChapters as any,
              {
                customPrompt,
                relatedWorldBuildings: [] // 可以根据需要添加关联世界观元素
              }
            );
          } else {
            // 如果没有选择章节，则使用关联章节内容（即使没有关联章节也会继续处理）
            result = await worldBuildingAIAdapter.updateWorldBuildingWithAssociatedChapters(
              wb,
              bookId,
              {
                customPrompt
              }
            );
          }

          // 应用更新建议
          const updatedWB = { ...wb };

          // 应用更新建议
          for (const suggestion of result) {
            const fieldPath = suggestion.field;

            if (fieldPath.startsWith('attributes.')) {
              // 更新属性字段
              const attributeName = fieldPath.split('.')[1];
              updatedWB.attributes = updatedWB.attributes || {};

              // 始终使用追加模式，确保不会覆盖现有内容
              const currentValue = updatedWB.attributes[attributeName] || '';

              // 检查新内容是否已经存在于当前内容中
              if (currentValue && currentValue.includes(suggestion.suggestedValue)) {
                console.log(`属性 ${attributeName} 的新内容已存在，跳过更新`);
                continue; // 跳过此更新
              }

              // 查找当前内容中的所有补充标记，确定下一个编号
              let nextNumber = 1;
              if (currentValue) {
                const regex = /【补充(\d+)】/g;
                let match;
                while ((match = regex.exec(currentValue)) !== null) {
                  const num = parseInt(match[1], 10);
                  if (!isNaN(num) && num >= nextNumber) {
                    nextNumber = num + 1;
                  }
                }
              }

              const appendPrefix = `【补充${nextNumber}】`;
              updatedWB.attributes[attributeName] = currentValue
                ? `${currentValue}\n\n${appendPrefix}\n${suggestion.suggestedValue}`
                : suggestion.suggestedValue;
            } else if (fieldPath === 'description') {
              // 更新描述
              // 始终使用追加模式，确保不会覆盖现有内容
              const currentDescription = updatedWB.description || '';

              // 检查新内容是否已经存在于当前内容中
              if (currentDescription && currentDescription.includes(suggestion.suggestedValue)) {
                console.log(`描述的新内容已存在，跳过更新`);
                continue; // 跳过此更新
              }

              // 查找当前内容中的所有补充标记，确定下一个编号
              let nextNumber = 1;
              if (currentDescription) {
                const regex = /【补充(\d+)】/g;
                let match;
                while ((match = regex.exec(currentDescription)) !== null) {
                  const num = parseInt(match[1], 10);
                  if (!isNaN(num) && num >= nextNumber) {
                    nextNumber = num + 1;
                  }
                }
              }

              const appendPrefix = `【补充${nextNumber}】`;
              updatedWB.description = currentDescription
                ? `${currentDescription}\n\n${appendPrefix}\n${suggestion.suggestedValue}`
                : suggestion.suggestedValue;
            } else if (fieldPath === 'category') {
              // 更新类别 - 只有在当前类别为空时才更新
              if (!updatedWB.category) {
                updatedWB.category = suggestion.suggestedValue;
              }
            }
          }

          // 更新提取自的章节ID
          updatedWB.extractedFromChapterIds = Array.from(new Set([
            ...(updatedWB.extractedFromChapterIds || []),
            ...selectedChapterIds
          ]));

          // 保存世界观与章节的关联关系
          if (updatedWB.id) {
            try {
              await saveChapterAssociation(updatedWB.id, 'worldbuilding', selectedChapterIds);
              console.log(`已保存世界观元素 ${updatedWB.name} 与章节的关联关系`);
            } catch (error) {
              console.error(`保存世界观元素 ${updatedWB.name} 与章节的关联关系失败:`, error);
            }
          }

          updatedWBs.push(updatedWB);
          setProcessedCount(prev => prev + 1);
        } catch (error) {
          console.error(`更新世界观元素 ${wb.name} 失败:`, error);
          // 继续处理下一个世界观元素
        }
      }

      // 设置更新后的世界观
      setUpdatedWorldBuildings(updatedWBs);

      // 直接应用更新
      onUpdate(updatedWBs);
      onClose();

      console.log('批量更新完成，成功更新:', updatedWBs.length);
    } catch (error) {
      console.error('批量更新世界观失败:', error);
      setError('批量更新世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 取消更新
  const cancelUpdate = () => {
    // 取消AI请求
    worldBuildingAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  // 应用更新
  const applyUpdate = () => {
    if (updatedWorldBuildings.length > 0) {
      console.log('应用批量更新:', updatedWorldBuildings);
      onUpdate(updatedWorldBuildings);
      onClose();
    } else {
      console.error('无法应用批量更新：updatedWorldBuildings 为空');
    }
  };

  // 打开提示词模板管理器
  const openPromptManager = () => {
    setIsPromptManagerOpen(true);
  };

  // 处理提示词模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    setCustomPrompt(template.content);
    setIsPromptManagerOpen(false);
  };

  // 切换显示保存模板表单
  const toggleSaveTemplateForm = () => {
    setShowSaveTemplateForm(!showSaveTemplateForm);
    // 重置表单
    if (!showSaveTemplateForm) {
      setTemplateName('');
      setTemplateDescription('');
    }
  };

  // 保存提示词模板
  const savePromptTemplate = async () => {
    if (!templateName.trim()) {
      setError('请输入模板名称');
      return;
    }

    if (!customPrompt.trim()) {
      setError('请输入模板内容');
      return;
    }

    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 创建新模板
      const newTemplate: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        category: PromptCategory.WORLD_BUILDING,
        name: templateName,
        content: customPrompt,
        description: templateDescription || undefined
      };

      // 保存模板
      await promptTemplateRepository.create(newTemplate);

      // 重置表单
      setTemplateName('');
      setTemplateDescription('');
      setShowSaveTemplateForm(false);

      // 显示成功消息
      setSuccessMessage('提示词模板保存成功');

      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('保存提示词模板失败:', error);
      setError('保存提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">批量更新世界观</h2>
          <p className="text-sm text-gray-600">从选定的章节中提取信息，批量更新选中的世界观元素</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-y-auto">
          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* 更新结果 */}
          {updatedWorldBuildings.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-800">更新结果</h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p className="text-green-700">成功更新 {updatedWorldBuildings.length} 个世界观元素。</p>
              </div>

              {/* 显示更新后的信息 */}
              <div className="max-h-96 overflow-y-auto">
                {updatedWorldBuildings.map((wb, index) => (
                  <div key={wb.id || index} className="mb-4 p-4 border rounded-lg">
                    <h4 className="font-medium text-gray-800">{wb.name}</h4>
                    <p className="text-sm text-gray-500">类别: {wb.category || '未设置'}</p>
                    <p className="text-sm text-gray-500 mt-2">描述:</p>
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">{wb.description || '未设置'}</p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 世界观元素选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">选择世界观元素</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="mb-2">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedWorldBuildingIds.length === worldBuildings.length}
                        onChange={(e) => handleSelectAllWorldBuildings(e.target.checked)}
                      />
                      <span className="ml-2 text-sm font-medium text-gray-700">全选</span>
                    </label>
                  </div>
                  <div className="space-y-1">
                    {worldBuildings.map((wb) => (
                      <label key={wb.id} className="flex items-center">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-blue-600"
                          checked={selectedWorldBuildingIds.includes(wb.id!)}
                          onChange={() => handleWorldBuildingSelectionChange(wb.id!)}
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {wb.name} {wb.category ? `(${wb.category})` : ''}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* 章节选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">选择章节</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="mb-2">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedChapterIds.length === chapters.length}
                        onChange={(e) => handleSelectAllChapters(e.target.checked)}
                      />
                      <span className="ml-2 text-sm font-medium text-gray-700">全选</span>
                    </label>
                  </div>

                  {/* 范围选择 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-100 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">范围选择:</span>
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <button
                      className="px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                      onClick={() => handleRangeSelect('select')}
                    >
                      选择
                    </button>
                    <button
                      className="px-2 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                      onClick={() => handleRangeSelect('deselect')}
                    >
                      取消选择
                    </button>
                  </div>

                  <div className="space-y-1">
                    {chapters.map((chapter) => (
                      <label key={chapter.id} className="flex items-center">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-blue-600"
                          checked={selectedChapterIds.includes(chapter.id!)}
                          onChange={() => handleChapterSelectionChange(chapter.id!)}
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {chapter.title || `章节 ${chapter.order !== undefined ? chapter.order + 1 : '未编号'}`}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* 自定义提示词 */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium text-gray-800">自定义提示词（可选）</h3>
                  <div className="flex space-x-2">
                    <button
                      className="px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors flex items-center"
                      onClick={toggleSaveTemplateForm}
                      title={showSaveTemplateForm ? "取消保存" : "保存为模板"}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                      </svg>
                      {showSaveTemplateForm ? "取消保存" : "保存模板"}
                    </button>
                    <button
                      className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors flex items-center"
                      onClick={openPromptManager}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      管理模板
                    </button>
                  </div>
                </div>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="输入特定要求，例如：关注特定的世界观元素、添加更多细节等。如果留空，将使用默认提示词。"
                />

                {/* 保存模板表单 */}
                {showSaveTemplateForm && (
                  <div className="mt-2 p-3 border rounded-md bg-gray-50">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">保存为提示词模板</h4>
                    <div className="mb-2">
                      <label className="block text-xs text-gray-600 mb-1">模板名称 <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm"
                        value={templateName}
                        onChange={(e) => setTemplateName(e.target.value)}
                        placeholder="输入模板名称..."
                      />
                    </div>
                    <div className="mb-2">
                      <label className="block text-xs text-gray-600 mb-1">模板描述</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm"
                        value={templateDescription}
                        onChange={(e) => setTemplateDescription(e.target.value)}
                        placeholder="输入模板描述（可选）..."
                      />
                    </div>
                    <div className="flex justify-end">
                      <button
                        type="button"
                        className="mr-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                        onClick={toggleSaveTemplateForm}
                      >
                        取消
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600"
                        onClick={savePromptTemplate}
                      >
                        保存
                      </button>
                    </div>
                  </div>
                )}

                {/* 成功消息 */}
                {successMessage && (
                  <div className="mt-2 p-2 bg-green-100 text-green-700 rounded-md">
                    {successMessage}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-blue-700">正在更新世界观元素...</h3>
                <span className="text-sm text-blue-600">{processedCount}/{totalToProcess}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${(processedCount / totalToProcess) * 100}%` }}
                ></div>
              </div>
              <p className="mt-2 text-sm text-blue-600">
                正在处理: {worldBuildings.find(wb => wb.id === selectedWorldBuildingIds[currentIndex])?.name || ''}
              </p>
            </div>
          )}
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-2">
          {isLoading ? (
            <button
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelUpdate}
            >
              取消
            </button>
          ) : updatedWorldBuildings.length > 0 ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={applyUpdate}
              >
                应用更新
              </button>
            </>
          ) : (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                onClick={updateWorldBuildings}
                disabled={selectedWorldBuildingIds.length === 0}
              >
                开始更新
              </button>
            </>
          )}
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.WORLD_BUILDING}
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />
    </div>
  );
};
