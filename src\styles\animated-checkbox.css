/* 高级动画复选框样式 */

/* 复选框容器基础样式 */
.animated-checkbox {
  position: relative;
  width: 20px;
  height: 20px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.animated-checkbox:hover {
  transform: scale(1.05);
}

.animated-checkbox:active {
  transform: scale(0.95);
}

/* 复选框背景 */
.checkbox-background {
  width: 100%;
  height: 100%;
  border: 2px solid;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.checkbox-background.unchecked {
  background-color: white;
  border-color: #d1d5db;
}

.checkbox-background.unchecked:hover {
  border-color: #60a5fa;
  transform: scale(1.05);
}

.checkbox-background.checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
  transform: scale(1.05);
}

/* 波纹动画效果 */
@keyframes checkboxRipple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.checkbox-ripple {
  position: absolute;
  inset: 0;
  background-color: #60a5fa;
  border-radius: 4px;
  opacity: 0.3;
  animation: checkboxRipple 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* SVG勾选图标动画 */
@keyframes checkboxIconScale {
  0% {
    transform: scale(0) rotate(-45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-10deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.checkbox-icon {
  position: relative;
  z-index: 10;
  animation: checkboxIconScale 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s both;
}

/* SVG路径重绘动画 */
.checkbox-path {
  stroke-dasharray: 0;
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 庆祝粒子动画 */
@keyframes checkboxParticle {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) translateY(-16px) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) translateY(-24px) scale(0);
    opacity: 0;
  }
}

.checkbox-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: #93c5fd;
  border-radius: 50%;
  pointer-events: none;
  left: 50%;
  top: 50%;
}

.checkbox-particle:nth-child(1) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.4s both;
  transform: translate(-50%, -50%) rotate(0deg) translateY(-8px);
}

.checkbox-particle:nth-child(2) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.5s both;
  transform: translate(-50%, -50%) rotate(60deg) translateY(-8px);
}

.checkbox-particle:nth-child(3) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.6s both;
  transform: translate(-50%, -50%) rotate(120deg) translateY(-8px);
}

.checkbox-particle:nth-child(4) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.7s both;
  transform: translate(-50%, -50%) rotate(180deg) translateY(-8px);
}

.checkbox-particle:nth-child(5) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.8s both;
  transform: translate(-50%, -50%) rotate(240deg) translateY(-8px);
}

.checkbox-particle:nth-child(6) {
  animation: checkboxParticle 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.9s both;
  transform: translate(-50%, -50%) rotate(300deg) translateY(-8px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .animated-checkbox {
    width: 18px;
    height: 18px;
  }
  
  .checkbox-background {
    border-width: 1.5px;
  }
  
  .checkbox-particle {
    width: 3px;
    height: 3px;
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .animated-checkbox,
  .checkbox-background,
  .checkbox-icon,
  .checkbox-path,
  .checkbox-particle {
    animation: none !important;
    transition: opacity 0.2s ease !important;
  }
  
  .checkbox-ripple {
    display: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .checkbox-background.unchecked {
    border-width: 3px;
    border-color: #000;
  }
  
  .checkbox-background.checked {
    background-color: #000;
    border-color: #000;
  }
  
  .checkbox-path {
    stroke-width: 4;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .checkbox-background.unchecked {
    background-color: #374151;
    border-color: #6b7280;
  }
  
  .checkbox-background.unchecked:hover {
    border-color: #60a5fa;
  }
  
  .checkbox-particle {
    background-color: #60a5fa;
  }
}
