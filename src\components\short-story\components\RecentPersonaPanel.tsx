"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { AIPersonaConfig, PhaseType } from '../../../types/ai-persona';

interface RecentPersonaPanelProps {
  recentPersonas: AIPersonaConfig[];
  onPersonaSelect: (persona: AIPersonaConfig) => void;
  onVersionManage: (persona: AIPersonaConfig) => void;
  onCategoryManage?: (persona: AIPersonaConfig) => void;
  getPhaseDisplayName: (phase: PhaseType) => string;
}

const RecentPersonaPanel: React.FC<RecentPersonaPanelProps> = ({
  recentPersonas,
  onPersonaSelect,
  onVersionManage,
  onCategoryManage,
  getPhaseDisplayName
}) => {
  // 获取阶段颜色
  const getPhaseColor = (phase: PhaseType): string => {
    const colors: Record<PhaseType, string> = {
      'intro': 'from-blue-500 to-blue-600',
      'setup': 'from-orange-500 to-orange-600',
      'compression': 'from-red-500 to-red-600',
      'climax': 'from-purple-500 to-purple-600',
      'resolution': 'from-green-500 to-green-600',
      'ending': 'from-gray-500 to-gray-600',
      'buildup': 'from-yellow-500 to-yellow-600',
      'custom': 'from-indigo-500 to-indigo-600'
    };
    return colors[phase] || colors.custom;
  };

  // 获取阶段图标
  const getPhaseIcon = (phase: PhaseType): string => {
    const icons: Record<PhaseType, string> = {
      'intro': '🚀',
      'setup': '🏗️',
      'compression': '💥',
      'climax': '⚡',
      'resolution': '🎯',
      'ending': '🏁',
      'buildup': '📈',
      'custom': '⚙️'
    };
    return icons[phase] || icons.custom;
  };

  // 格式化最后使用时间
  const formatLastUsed = (date?: Date): string => {
    if (!date) return '未使用';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '无效日期';

    const now = new Date();
    const diffMs = now.getTime() - validDate.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffHours < 1) return '刚刚';
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    return `${Math.floor(diffDays / 7)}周前`;
  };

  if (recentPersonas.length === 0) {
    return (
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">最近使用</h3>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 text-center">
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-500 dark:text-gray-400">暂无最近使用的人设</p>
          <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">开始使用人设后，这里会显示最近的3个</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">最近使用</h3>
        <span className="text-sm text-gray-500">快速访问</span>
      </div>

      {/* 横向滚动的最近使用人设列表 */}
      <div className="overflow-x-auto pb-2">
        <div className="flex space-x-4 min-w-max">
          {recentPersonas.map((persona, index) => (
            <motion.div
              key={persona.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.3,
                delay: index * 0.1,
                type: "spring",
                stiffness: 400,
                damping: 25
              }}
              className="flex-shrink-0 w-80"
            >
              <motion.div
                whileHover={{
                  y: -2,
                  scale: 1.02,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{ scale: 0.98 }}
                className="premium-card cursor-pointer relative overflow-hidden"
                onClick={() => onPersonaSelect(persona)}
              >
                {/* 渐变背景 */}
                <div className={`absolute inset-0 bg-gradient-to-r ${getPhaseColor(persona.phase)} opacity-10`} />

                {/* 内容 */}
                <div className="relative z-10 p-4">
                  {/* 头部 */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-xl mr-2">{getPhaseIcon(persona.phase)}</span>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {getPhaseDisplayName(persona.phase)}
                      </span>
                    </div>

                    {/* 操作按钮组 */}
                    <div className="flex items-center space-x-1">
                      {/* 分类管理按钮 */}
                      {onCategoryManage && (
                        <motion.button
                          className="p-1 rounded-md text-gray-400 hover:text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            onCategoryManage(persona);
                          }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="分类管理"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                        </motion.button>
                      )}

                      {/* 版本管理按钮 */}
                      <motion.button
                        className="p-1 rounded-md text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          onVersionManage(persona);
                        }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        title="版本管理"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </motion.button>
                    </div>
                  </div>

                  {/* 系统提示词预览 */}
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {persona.systemPrompt.length > 80
                      ? `${persona.systemPrompt.substring(0, 80)}...`
                      : persona.systemPrompt}
                  </p>

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{formatLastUsed(persona.metadata.lastUsedAt)}</span>
                    </div>

                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <span>{persona.metadata.usageCount || 0} 次</span>
                    </div>
                  </div>

                  {/* 默认人设标识 */}
                  {persona.metadata.isDefault && (
                    <div className="absolute top-2 right-2">
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-yellow-100 text-yellow-800 border border-yellow-200">
                        <svg className="w-2.5 h-2.5 mr-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        默认
                      </span>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RecentPersonaPanel;
