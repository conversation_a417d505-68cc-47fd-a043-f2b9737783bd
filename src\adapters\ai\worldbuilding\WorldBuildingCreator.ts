"use client";

import { IAISenderComponent } from '@/factories/ai/interfaces/IAISenderComponent';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import createAdvancedMessageBuilder from '@/utils/ai/AdvancedMessageBuilder';
import { WorldBuildingPrompts } from '@/utils/ai/prompts/WorldBuildingPrompts';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
import { ChapterContent, ExtractionOptions, IWorldBuildingCreator } from '../interfaces/WorldBuildingInterfaces';
import { WorldBuildingFieldStandardizer } from './WorldBuildingFieldStandardizer';
import { WorldBuildingInfoFormatter } from './WorldBuildingInfoFormatter';
import { SegmentOptions } from '@/utils/ai/ChapterSegmenter';
import { db } from '@/lib/db/dexie';

/**
 * 世界观创建器
 * 负责创建世界观元素
 */
export class WorldBuildingCreator implements IWorldBuildingCreator {
  private currentRequest: AbortController | null = null;
  private fieldStandardizer: WorldBuildingFieldStandardizer;
  private infoFormatter: WorldBuildingInfoFormatter;

  /**
   * 创建世界观创建器
   * @param aiSender AI发送器
   * @param segmenter 章节分段器
   */
  constructor(
    private aiSender: IAISenderComponent,
    private segmenter: any
  ) {
    this.fieldStandardizer = new WorldBuildingFieldStandardizer();
    this.infoFormatter = new WorldBuildingInfoFormatter();
  }

  /**
   * 从章节内容中创建世界观元素
   * @param chapters 章节内容列表
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  async createWorldBuildingsFromChapters(
    chapters: ChapterContent[],
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    try {
      // 创建请求控制器
      this.currentRequest = new AbortController();

      // 合并章节内容
      const combinedContent = chapters.map((chapter, index) => {
        const chapterTitle = chapter.title || `章节 ${index + 1}`;
        return `# ${chapterTitle}\n\n${chapter.content}`;
      }).join('\n\n');

      // 设置分段选项
      const segmentOptions: SegmentOptions = {
        maxSegmentLength: 3000,
        minSegmentLength: 500
      };

      // 分段处理章节内容
      const segments = this.segmenter.segmentChapter(combinedContent, segmentOptions);

      console.log(`章节内容已分为 ${segments.length} 个段落进行处理`);

      // 从每个段落中创建世界观元素
      const segmentResults = await Promise.all(
        segments.map((segment: string) =>
          this.createWorldBuildingsFromSegment(segment, options)
        )
      );

      // 合并所有段落的创建结果
      const mergedResults: Record<string, any> = {};

      for (const result of segmentResults) {
        for (const [elementName, elementInfo] of Object.entries(result)) {
          const info = elementInfo as Record<string, any>;

          if (!mergedResults[elementName]) {
            // 如果元素尚未存在于合并结果中，直接添加
            mergedResults[elementName] = info;
          } else {
            // 如果元素已存在，合并newInfo
            if (info.newInfo && mergedResults[elementName].newInfo) {
              for (const [key, value] of Object.entries(info.newInfo)) {
                // 如果新值比旧值更长（可能更详细），或者旧值不存在，则使用新值
                if (!mergedResults[elementName].newInfo[key] ||
                    (typeof value === 'string' &&
                     typeof mergedResults[elementName].newInfo[key] === 'string' &&
                     (value as string).length > (mergedResults[elementName].newInfo[key] as string).length)) {
                  mergedResults[elementName].newInfo[key] = value;
                }
              }
            }
          }
        }
      }

      return mergedResults;
    } catch (error) {
      console.error('从章节创建世界观元素失败:', error);
      throw new Error('从章节创建世界观元素失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 从章节段落中创建世界观元素
   * @param segment 章节段落
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  private async createWorldBuildingsFromSegment(
    segment: string,
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    try {
      // 获取最大元素数量、关联的世界观元素和自定义提示词
      const maxElements = options.maxWorldBuildings || 5;
      const relatedElements = options.relatedWorldBuildings || [];
      const customPrompt = options.customPrompt || '';

      // 构建基础提示词
      const basePrompt = WorldBuildingPrompts.creationBasePrompt;

      // 创建高级消息构建器
      const advancedBuilder = createAdvancedMessageBuilder()
        // 添加系统角色提示词 - 使用专门的创建操作提示词
        .addSystemPrompt(WorldBuildingPrompts.creationSystemRolePrompt)
        // 添加助手角色确认任务
        .addAssistantConfirmation(`我将根据提供的章节内容创建丰富、有深度的世界观元素，并以JSON格式返回结果。我会创建不超过${maxElements}个世界观元素。\n\n我会按照以下类别体系进行创建：\n\n${WorldBuildingPrompts.creationCategorySystemPrompt}`)
        // 添加基础提示词
        .addBasePrompt(basePrompt)
        // 添加章节内容
        .addChapterContent(segment);

      // 为每个关联世界观元素添加单独的消息
      if (relatedElements.length > 0) {
        // 创建一个临时的世界观对象，用于addRelatedWorldBuildings方法
        const tempWorldBuilding = {
          id: '',
          name: '新世界观元素',
          bookId: '',
          category: '',
          description: '',
          extractedFromChapterIds: [],
          relatedCharacterIds: [],
          relatedTerminologyIds: [],
          relatedWorldBuildingIds: [],
          attributes: {},
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // 添加关联世界观元素
        await advancedBuilder.addRelatedWorldBuildings(tempWorldBuilding, relatedElements);
      }

      // 添加输出格式指令
      advancedBuilder.addOutputFormat(WorldBuildingPrompts.creationOutputFormatPrompt);

      // 添加自定义提示词
      advancedBuilder.addCustomPrompt(customPrompt);

      // 构建消息数组
      const messages = advancedBuilder.build();

      // 调用AI模型
      const result = await this.aiSender.sendRequest('', {
        messages,
        temperature: 0.7, // 使用较高的温度，增加创造性
        max_tokens: 4000,
        signal: this.currentRequest?.signal
      });

      // 解析响应
      return this.parseResponse(result.text);
    } catch (error) {
      console.error('从章节段落创建世界观元素失败:', error);
      throw new Error('从章节段落创建世界观元素失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 使用关联章节内容创建世界观元素
   * @param bookId 书籍ID
   * @param chapterId 章节ID
   * @param options 创建选项
   * @returns 创建的世界观元素
   */
  async createWorldBuildingsFromAssociatedChapter(
    bookId: string,
    chapterId: string,
    options: ExtractionOptions = {}
  ): Promise<Record<string, any>> {
    try {
      // 获取章节内容
      const chapter = await db.chapters.get(chapterId);

      if (!chapter || chapter.bookId !== bookId) {
        throw new Error('无法获取章节内容或章节不属于指定书籍');
      }

      // 创建章节内容对象
      const chapterContent: ChapterContent = {
        id: chapterId,
        title: chapter.title || `章节 ${chapterId}`,
        content: chapter.content || ''
      };

      // 从章节内容中创建世界观元素
      return this.createWorldBuildingsFromChapters([chapterContent], options);
    } catch (error) {
      console.error('使用关联章节创建世界观元素失败:', error);
      throw new Error('使用关联章节创建世界观元素失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 解析AI响应
   * @param response AI响应
   * @returns 解析后的世界观信息
   */
  private parseResponse(response: string): Record<string, any> {
    try {
      // 使用通用的JSON解析工具方法
      const parsedData = AIResponseParser.parseJSON<Record<string, any>>(response, {});

      // 标准化解析后的数据
      const standardizedData: Record<string, any> = {};

      for (const [key, value] of Object.entries(parsedData)) {
        // 标准化每个世界观元素的信息
        standardizedData[key] = this.fieldStandardizer.standardizeWorldBuildingInfo(value as Record<string, any>);
      }

      return standardizedData;
    } catch (error) {
      console.error('解析AI响应失败:', error);
      throw new Error('解析AI响应失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.currentRequest = null;
    }
  }
}
