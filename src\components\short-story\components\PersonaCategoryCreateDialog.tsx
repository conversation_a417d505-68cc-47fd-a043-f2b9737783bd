"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CategoryPrompt } from '../../../types/ai-persona';

interface PersonaCategoryCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateCategory: (categoryData: { name: string; prompts?: CategoryPrompt[] }) => void;
}

const PersonaCategoryCreateDialog: React.FC<PersonaCategoryCreateDialogProps> = ({
  isOpen,
  onClose,
  onCreateCategory
}) => {
  const [categoryName, setCategoryName] = useState('');
  const [prompts, setPrompts] = useState<string[]>(['']);
  const [isCreating, setIsCreating] = useState(false);

  // 重置表单
  const resetForm = () => {
    setCategoryName('');
    setPrompts(['']);
    setIsCreating(false);
  };

  // 处理关闭
  const handleClose = () => {
    if (!isCreating) {
      resetForm();
      onClose();
    }
  };

  // 添加提示词输入框
  const addPromptInput = () => {
    setPrompts(prev => [...prev, '']);
  };

  // 删除提示词输入框
  const removePromptInput = (index: number) => {
    if (prompts.length > 1) {
      setPrompts(prev => prev.filter((_, i) => i !== index));
    }
  };

  // 更新提示词内容
  const updatePrompt = (index: number, content: string) => {
    setPrompts(prev => prev.map((prompt, i) => i === index ? content : prompt));
  };

  // 处理创建
  const handleCreate = async () => {
    if (!categoryName.trim()) return;

    try {
      setIsCreating(true);

      // 过滤非空提示词
      const validPrompts = prompts
        .map(content => content.trim())
        .filter(content => content.length > 0);

      // 创建提示词对象
      const categoryPrompts: CategoryPrompt[] = validPrompts.map((content, index) => ({
        id: `prompt-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
        content,
        order: index,
        createdAt: new Date()
      }));

      await onCreateCategory({
        name: categoryName.trim(),
        prompts: categoryPrompts
      });

      resetForm();
      onClose();
    } catch (err) {
      console.error('创建分类失败:', err);
    } finally {
      setIsCreating(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isCreating) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* 背景遮罩 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={handleClose}
      />

      {/* 对话框 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="relative w-full max-w-md mx-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl"
        onKeyDown={handleKeyDown}
      >
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              创建新分类
            </h3>
            <button
              onClick={handleClose}
              disabled={isCreating}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:cursor-not-allowed"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-4">
          {/* 分类名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              分类名称 *
            </label>
            <input
              type="text"
              value={categoryName}
              onChange={(e) => setCategoryName(e.target.value)}
              placeholder="输入分类名称..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              disabled={isCreating}
              autoFocus
            />
          </div>

          {/* 提示词列表 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                提示词 (可选)
              </label>
              <button
                onClick={addPromptInput}
                disabled={isCreating || prompts.length >= 10}
                className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                + 添加提示词
              </button>
            </div>

            <div className="space-y-2 max-h-48 overflow-y-auto">
              <AnimatePresence mode="popLayout">
                {prompts.map((prompt, index) => (
                  <motion.div
                    key={index}
                    layout
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="flex items-start space-x-2"
                  >
                    <textarea
                      value={prompt}
                      onChange={(e) => updatePrompt(index, e.target.value)}
                      placeholder={`提示词 ${index + 1}...`}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm resize-none"
                      rows={2}
                      disabled={isCreating}
                    />
                    {prompts.length > 1 && (
                      <button
                        onClick={() => removePromptInput(index)}
                        disabled={isCreating}
                        className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors disabled:cursor-not-allowed"
                        title="删除此提示词"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {prompts.length >= 10 && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                最多可添加 10 个提示词
              </p>
            )}
          </div>
        </div>

        {/* 底部操作 */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={handleClose}
              disabled={isCreating}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors disabled:cursor-not-allowed"
            >
              取消
            </button>
            <button
              onClick={handleCreate}
              disabled={!categoryName.trim() || isCreating}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {isCreating && (
                <motion.div
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
              )}
              {isCreating ? '创建中...' : '创建分类'}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default PersonaCategoryCreateDialog;
