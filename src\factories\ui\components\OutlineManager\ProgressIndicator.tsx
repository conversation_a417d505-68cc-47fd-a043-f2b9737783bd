"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Chapter } from '@/lib/db/dexie';
import { ExtractionMode } from './OutlineFrameworkExtractService';

interface ProgressIndicatorProps {
  mode: ExtractionMode;
  current: number;
  total: number;
  currentChapter?: Chapter | null;
  isAnalyzing: boolean;
  progress: number; // 0-100
}

const getModeDisplayName = (mode: ExtractionMode): string => {
  switch (mode) {
    case ExtractionMode.SUMMARY:
      return '总结提取';
    case ExtractionMode.SINGLE:
      return '单章节提取';
    case ExtractionMode.RANGE:
      return '范围提取';
    default:
      return '框架提取';
  }
};

const getModeIcon = (mode: ExtractionMode): string => {
  switch (mode) {
    case ExtractionMode.SUMMARY:
      return '📊';
    case ExtractionMode.SINGLE:
      return '🔍';
    case ExtractionMode.RANGE:
      return '📋';
    default:
      return '⚙️';
  }
};

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  mode,
  current,
  total,
  currentChapter,
  isAnalyzing,
  progress
}) => {
  const modeDisplayName = getModeDisplayName(mode);
  const modeIcon = getModeIcon(mode);

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        {/* 模式图标和标题 */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="mb-6"
        >
          <div className="text-6xl mb-4">{modeIcon}</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            {modeDisplayName}分析中...
          </h3>
          <p className="text-gray-600">
            AI正在深度分析您选择的章节内容
          </p>
        </motion.div>

        {/* 当前处理章节信息 */}
        <AnimatePresence mode="wait">
          {currentChapter && (
            <motion.div
              key={currentChapter.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg"
            >
              <div className="flex items-center justify-center space-x-3 mb-2">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-orange-900">
                    正在分析第{current}章：{currentChapter.title}
                  </h4>
                  <p className="text-sm text-orange-700">
                    字数：{currentChapter.wordCount}字 | 顺序：第{currentChapter.order}章
                  </p>
                </div>
              </div>
              
              {/* 章节摘要 */}
              {currentChapter.summary && (
                <p className="text-xs text-orange-600 bg-orange-100 rounded px-3 py-2">
                  {currentChapter.summary}
                </p>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* 进度条 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              分析进度
            </span>
            <span className="text-sm font-medium text-orange-600">
              {current}/{total} 章节
            </span>
          </div>
          
          {/* 主进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-3 mb-2 overflow-hidden">
            <motion.div
              className="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          
          {/* 百分比显示 */}
          <div className="flex items-center justify-center space-x-2">
            <span className="text-lg font-bold text-orange-600">
              {Math.round(progress)}%
            </span>
            {isAnalyzing && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-4 h-4"
              >
                <svg className="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </motion.div>
            )}
          </div>
        </div>

        {/* 章节进度指示器 */}
        {total > 1 && (
          <div className="mb-6">
            <p className="text-sm text-gray-600 mb-3">章节处理状态</p>
            <div className="flex items-center justify-center space-x-2">
              {Array.from({ length: total }, (_, index) => {
                const chapterNumber = index + 1;
                const isCompleted = chapterNumber < current;
                const isCurrent = chapterNumber === current;
                const isPending = chapterNumber > current;

                return (
                  <motion.div
                    key={index}
                    className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium
                      ${isCompleted 
                        ? 'bg-green-500 text-white' 
                        : isCurrent 
                          ? 'bg-orange-500 text-white' 
                          : 'bg-gray-200 text-gray-500'
                      }
                    `}
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: isCurrent ? [1, 1.1, 1] : 1,
                      backgroundColor: isCompleted 
                        ? '#10b981' 
                        : isCurrent 
                          ? '#f97316' 
                          : '#e5e7eb'
                    }}
                    transition={{ 
                      scale: { duration: 1, repeat: isCurrent ? Infinity : 0 },
                      backgroundColor: { duration: 0.3 }
                    }}
                  >
                    {isCompleted ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      chapterNumber
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        )}

        {/* 分析阶段提示 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="p-4 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-center justify-center space-x-2 mb-2">
            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium text-blue-900">分析阶段</span>
          </div>
          <div className="text-xs text-blue-700 space-y-1">
            {mode === ExtractionMode.SINGLE ? (
              <>
                <p>• 深度分析章节内容和写作技巧</p>
                <p>• 提取具体的创作方法和框架模式</p>
                <p>• 生成详细的技巧指导和应用建议</p>
              </>
            ) : (
              <>
                <p>• 逐章分析内容结构和写作手法</p>
                <p>• 提取共同的创作模式和技巧特征</p>
                <p>• 生成综合性的框架分析报告</p>
              </>
            )}
          </div>
        </motion.div>

        {/* 预计时间提示 */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
          className="text-xs text-gray-500 mt-4"
        >
          预计还需要 {Math.max(1, Math.ceil((100 - progress) / 10))} 分钟完成分析
        </motion.p>
      </div>
    </div>
  );
};

export default ProgressIndicator;
