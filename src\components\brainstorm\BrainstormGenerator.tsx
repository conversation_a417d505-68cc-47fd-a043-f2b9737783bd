"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { BrainstormType, BrainstormResult, BRAINSTORM_TYPES } from './types';
import BookTitleGenerator from './BookTitleGenerator';
import SynopsisGenerator from './SynopsisGenerator';

interface BrainstormGeneratorProps {
  selectedType: string;
  associationData?: any;
  selectedAssociations?: any;
  onGenerate: (request: any) => Promise<BrainstormResult[]>;
  onResultSelect: (result: BrainstormResult) => void;
}

/**
 * 脑洞生成器组件
 * 处理用户输入，调用AI生成，展示结果
 */
const BrainstormGenerator: React.FC<BrainstormGeneratorProps> = ({
  selectedType,
  associationData,
  selectedAssociations,
  onGenerate,
  onResultSelect
}) => {
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState<BrainstormResult[]>([]);

  const typeInfo = BRAINSTORM_TYPES.find(type => type.id === selectedType);

  const handleGenerate = async () => {
    if (!typeInfo || isGenerating) return;

    try {
      setIsGenerating(true);
      
      const request = {
        type: selectedType,
        input: inputText,
        context: {
          associationData,
          selectedAssociations,
          typeInfo
        }
      };

      const generatedResults = await onGenerate(request);
      setResults(generatedResults);
    } catch (error) {
      console.error('脑洞生成失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getPlaceholderText = () => {
    switch (selectedType) {
      case 'theme':
        return '描述你想要的题材风格、背景设定或主题方向...';
      case 'title':
        return '描述作品的风格、主要内容或想要的书名感觉...';
      case 'synopsis':
        return '描述作品的核心卖点、目标读者或简介风格...';
      case 'plot':
        return '描述当前剧情状况、想要的发展方向或冲突类型...';
      case 'character':
        return '描述角色的类型、性格特点或在故事中的作用...';
      case 'worldview':
        return '描述世界的类型、特殊规则或想要扩展的方面...';
      default:
        return '请描述你的创意需求...';
    }
  };

  // 如果是书名生成类型，使用专门的BookTitleGenerator组件
  if (selectedType === 'title') {
    return (
      <div className="h-full">
        <BookTitleGenerator
          associationData={associationData}
          selectedAssociations={selectedAssociations}
          onGenerate={onGenerate}
          onResultSelect={onResultSelect}
        />
      </div>
    );
  }

  // 如果是简介生成类型，使用专门的SynopsisGenerator组件
  if (selectedType === 'synopsis') {
    return (
      <div className="h-full">
        <SynopsisGenerator />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* 类型信息 */}
      {typeInfo && (
        <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 bg-gradient-to-r ${typeInfo.color} rounded-xl flex items-center justify-center`}>
              <span className="text-2xl">{typeInfo.icon}</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">{typeInfo.name}</h3>
              <p className="text-sm text-gray-600">{typeInfo.description}</p>
            </div>
          </div>
        </div>
      )}

      {/* 输入区域 */}
      <div className="p-4 border-b border-gray-200">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          创意需求描述
        </label>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder={getPlaceholderText()}
          className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
        />
        <p className="text-xs text-gray-500 mt-1">
          💡 描述越详细，生成的创意越精准。AI会结合你选择的关联数据进行生成。
        </p>
      </div>

      {/* 生成按钮 */}
      <div className="p-4 border-b border-gray-200">
        <motion.button
          onClick={handleGenerate}
          disabled={isGenerating}
          className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          whileHover={{ scale: isGenerating ? 1 : 1.02 }}
          whileTap={{ scale: isGenerating ? 1 : 0.98 }}
        >
          {isGenerating ? (
            <div className="flex items-center justify-center space-x-2">
              <motion.div
                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <span>正在生成创意...</span>
            </div>
          ) : (
            <span>🚀 生成脑洞创意</span>
          )}
        </motion.button>
      </div>

      {/* 结果展示 */}
      <div className="flex-1 overflow-y-auto">
        {results.length > 0 ? (
          <div className="p-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">
              ✨ 生成结果 ({results.length})
            </h4>
            <div className="space-y-4">
              {results.map((result, index) => (
                <motion.div
                  key={result.id}
                  className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => onResultSelect(result)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </span>
                      <span className="text-sm text-gray-500">
                        创意评分: {result.score}/5.0
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(result.score) ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                  
                  <p className="text-gray-800 leading-relaxed mb-3">{result.content}</p>
                  
                  {result.metadata.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {result.metadata.keywords.map((keyword, i) => (
                        <span key={i} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  <div className="mt-3 flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      点击选择此创意
                    </span>
                    <motion.button
                      className="px-3 py-1 bg-purple-100 text-purple-700 rounded-md text-sm hover:bg-purple-200"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      选择
                    </motion.button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">🎨</div>
              <p>点击生成按钮开始创作</p>
              <p className="text-sm mt-1">AI将基于你的描述和关联数据生成创意</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BrainstormGenerator;
