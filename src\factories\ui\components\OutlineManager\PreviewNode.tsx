"use client";

import React, { memo, useCallback } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { OutlineNodeType } from '../../types/outline';
import './PreviewNode.css';

interface PreviewNodeData extends OutlineNodeType {
  isSelected?: boolean;
  onToggleSelect?: (nodeId: string, selected: boolean) => void;
}

interface PreviewNodeProps extends NodeProps {
  data: PreviewNodeData;
}

/**
 * 预览节点组件
 * 用于在画布中显示虚化的待创建节点
 */
const PreviewNode: React.FC<PreviewNodeProps> = ({ data, isConnectable }) => {

  console.log('PreviewNode渲染:', data);

  // 获取节点类型图标
  const getNodeIcon = () => {
    switch (data.type) {
      case 'volume':
        return '📚';
      case 'event':
        return '⚡';
      case 'chapter':
        return '📖';
      case 'plot':
        return '🎬';
      case 'dialogue':
        return '💬';
      default:
        return '📄';
    }
  };

  // 获取节点类型标签
  const getNodeTypeLabel = () => {
    switch (data.type) {
      case 'volume':
        return '总纲/卷';
      case 'event':
        return '事件刚';
      case 'chapter':
        return '章节';
      case 'plot':
        return '剧情节点';
      case 'dialogue':
        return '对话节点';
      default:
        return data.type || '节点';
    }
  };

  // 处理选择切换
  const handleToggleSelect = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (data.onToggleSelect) {
      data.onToggleSelect(data.id, !data.isSelected);
    }
  }, [data.id, data.isSelected, data.onToggleSelect]);

  // 获取节点颜色
  const getNodeColor = () => {
    switch (data.type) {
      case 'volume':
        return '#a855f7';
      case 'chapter':
        return '#667eea';
      case 'plot':
        return '#f97316';
      case 'dialogue':
        return '#22c55e';
      default:
        return '#6b7280';
    }
  };

  const nodeColor = getNodeColor();

  return (
    <>
      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        style={{
          background: nodeColor,
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 0 6px rgba(0, 0, 0, 0.2)',
          opacity: 0.7,
          top: '-6px',
        }}
      />

      {/* 预览节点内容 */}
      <div
        className="preview-node-container"
        onClick={handleToggleSelect}
        style={{
          minWidth: '200px',
          maxWidth: '280px',
          padding: '12px 16px',
          background: 'rgba(255, 255, 255, 0.9)',
          border: `2px dashed ${nodeColor}`,
          borderRadius: '12px',
          cursor: 'pointer',
          opacity: data.isSelected ? 0.9 : 0.6,
          transform: data.isSelected ? 'scale(1.02)' : 'scale(1)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          backdropFilter: 'blur(1px)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 选中状态的光晕效果 */}
        {data.isSelected && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `linear-gradient(45deg, ${nodeColor}15, transparent, ${nodeColor}15)`,
              borderRadius: '10px',
              pointerEvents: 'none',
              animation: 'shimmer 2s ease-in-out infinite',
            }}
          />
        )}

        {/* 节点内容 */}
        <div className="flex items-center">
          {/* 节点图标 */}
          <div
            className="flex-shrink-0 mr-3 flex items-center justify-center w-8 h-8 rounded-lg"
            style={{
              background: `${nodeColor}20`,
              border: `1px solid ${nodeColor}40`,
              fontSize: '16px',
            }}
          >
            {getNodeIcon()}
          </div>

          {/* 节点信息 */}
          <div className="flex-1 min-w-0">
            <div
              className="font-medium text-sm mb-1 truncate"
              style={{ color: nodeColor }}
            >
              {data.title}
            </div>
            {data.description && (
              <div
                className="text-xs text-gray-600 line-clamp-2"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                }}
              >
                {data.description}
              </div>
            )}
          </div>

          {/* 选择状态指示器 */}
          <div className="flex-shrink-0 ml-2 flex items-center">
            {/* 类型标签 */}
            <div
              className="px-2 py-1 text-xs font-medium rounded-full text-white mr-2"
              style={{
                background: nodeColor,
                opacity: 0.8,
              }}
            >
              {getNodeTypeLabel()}
            </div>

            {/* 复选框 */}
            <div
              className="w-5 h-5 rounded border-2 flex items-center justify-center"
              style={{
                borderColor: nodeColor,
                background: data.isSelected ? nodeColor : 'transparent',
                transition: 'all 0.2s ease',
              }}
            >
              {data.isSelected && (
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="20,6 9,17 4,12" />
                </svg>
              )}
            </div>
          </div>
        </div>

        {/* 新建标识 */}
        <div
          className="absolute top-2 right-2 w-4 h-4 rounded-full flex items-center justify-center text-white text-xs font-bold"
          style={{
            background: nodeColor,
            fontSize: '10px',
            animation: 'pulse 2s ease-in-out infinite',
          }}
        >
          +
        </div>
      </div>

      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        style={{
          background: nodeColor,
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 0 6px rgba(0, 0, 0, 0.2)',
          opacity: 0.7,
          bottom: '-6px',
        }}
      />
    </>
  );
};

export default memo(PreviewNode);
