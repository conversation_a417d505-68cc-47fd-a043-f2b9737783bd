"use client";

/**
 * 创建AI拆书适配器实例
 * @returns AI拆书适配器实例
 */
export function createAIBookAnalysisAdapter() {
  return {
    /**
     * 创建AI拆书对话框组件
     * @param props 组件属性
     * @returns 对话框配置对象
     */
    createAIBookAnalysisDialog: (props: {
      isOpen: boolean;
      onClose: () => void;
      onAnalysisComplete?: (result: string) => void;
      bookId: string;
    }) => {
      // 返回一个配置对象，而不是JSX组件
      return {
        type: 'AIBookAnalysisDialog',
        props: {
          isOpen: props.isOpen,
          onClose: props.onClose,
          onAnalysisComplete: props.onAnalysisComplete,
          bookId: props.bookId
        }
      };
    }
  };
}

export default createAIBookAnalysisAdapter;
