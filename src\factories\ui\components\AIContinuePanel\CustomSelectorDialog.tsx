"use client";

import React, { useState } from 'react';

interface SelectorItem {
  id: string;
  name: string;
  description?: string;
  disabled?: boolean;
}

interface CustomSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  items: SelectorItem[];
  selectedIds: string[];
  onSelect: (ids: string[]) => void;
  isLoading?: boolean;
  loadingText?: string;
  emptyText?: string;
  showSearch?: boolean;
  showRangeSelect?: boolean; // 是否显示范围选择功能
}

/**
 * 自定义选择器对话框组件
 * 用于选择人物、术语、世界观等元素
 * 修复了确认按钮关闭整个对话框的问题
 */
const CustomSelectorDialog: React.FC<CustomSelectorDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  items,
  selectedIds,
  onSelect,
  isLoading = false,
  loadingText = '加载中...',
  emptyText = '没有找到项目',
  showSearch = true,
  showRangeSelect = false
}) => {
  // 搜索状态
  const [searchTerm, setSearchTerm] = useState('');

  // 范围输入状态
  const [rangeInput, setRangeInput] = useState<string>('');
  const [showRangeInput, setShowRangeInput] = useState<boolean>(false);

  // 过滤后的项目
  const filteredItems = React.useMemo(() => {
    if (!searchTerm) return items;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return items.filter(item =>
      item.name.toLowerCase().includes(lowerSearchTerm) ||
      (item.description && item.description.toLowerCase().includes(lowerSearchTerm))
    );
  }, [items, searchTerm]);

  // 处理选择变更
  const handleItemSelect = (id: string) => {
    if (selectedIds.includes(id)) {
      onSelect(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      onSelect([...selectedIds, id]);
    }
  };

  // 全选
  const handleSelectAll = () => {
    onSelect(filteredItems.map(item => item.id));
  };

  // 取消全选
  const handleDeselectAll = () => {
    onSelect([]);
  };

  // 显示范围选择输入框
  const handleShowRangeInput = () => {
    setShowRangeInput(true);
    setRangeInput('');
  };

  // 处理范围输入变化
  const handleRangeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRangeInput(e.target.value);
  };

  // 处理范围选择
  const handleRangeSelect = () => {
    if (!rangeInput.trim()) {
      setShowRangeInput(false);
      return;
    }

    try {
      // 解析范围输入
      // 支持格式：1-5, 1,3,5, 1-3,5-7
      const ranges = rangeInput.split(',').map(r => r.trim());
      const newSelectedIds = [...selectedIds];

      for (const range of ranges) {
        if (range.includes('-')) {
          // 处理范围格式 (如 1-5)
          const [startStr, endStr] = range.split('-').map(r => r.trim());
          const start = parseInt(startStr, 10) - 1; // 转为0-based索引
          const end = parseInt(endStr, 10) - 1;

          if (isNaN(start) || isNaN(end) || start < 0 || end >= filteredItems.length || start > end) {
            throw new Error(`无效的范围: ${range}`);
          }

          // 添加范围内的所有项目
          for (let i = start; i <= end; i++) {
            const id = filteredItems[i].id;
            if (!newSelectedIds.includes(id)) {
              newSelectedIds.push(id);
            }
          }
        } else {
          // 处理单个数字 (如 3)
          const index = parseInt(range, 10) - 1; // 转为0-based索引

          if (isNaN(index) || index < 0 || index >= filteredItems.length) {
            throw new Error(`无效的索引: ${range}`);
          }

          const id = filteredItems[index].id;
          if (!newSelectedIds.includes(id)) {
            newSelectedIds.push(id);
          }
        }
      }

      onSelect(newSelectedIds);
      setShowRangeInput(false);
    } catch (error: any) {
      alert(`范围格式错误: ${error.message}\n请使用正确的格式，例如：1-5, 1,3,5, 1-3,5-7`);
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50" onClick={(e) => e.stopPropagation()}>
      <div className="bg-white rounded-2xl shadow-xl w-[550px] max-h-[85vh] flex flex-col overflow-hidden" onClick={(e) => e.stopPropagation()}>
        {/* 头部 */}
        <div className="p-4 border-b flex justify-between items-center bg-gradient-to-r from-indigo-50 to-blue-50">
          <div className="flex items-center">
            <div className="bg-indigo-100 p-2 rounded-lg mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-indigo-800">{title}</h2>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索框 */}
        {showSearch && (
          <div className="p-4 border-b">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索..."
                className="w-full px-4 py-2.5 pl-11 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <div className="absolute left-4 top-3 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        )}

        {/* 选择按钮 */}
        <div className="px-4 py-3 border-b bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="text-sm font-medium text-indigo-700 bg-indigo-50 px-3 py-1 rounded-full">
                已选择 {selectedIds.length} / {filteredItems.length} 项
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleSelectAll}
                className="px-3 py-1.5 text-xs bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm font-medium"
                disabled={filteredItems.length === 0}
              >
                全选
              </button>
              <button
                type="button"
                onClick={handleDeselectAll}
                className="px-3 py-1.5 text-xs bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-sm font-medium"
                disabled={selectedIds.length === 0}
              >
                取消全选
              </button>
              {showRangeSelect && (
                <button
                  type="button"
                  onClick={handleShowRangeInput}
                  className="px-3 py-1.5 text-xs bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors shadow-sm font-medium"
                >
                  范围选择
                </button>
              )}
            </div>
          </div>

          {/* 范围选择输入框 */}
          {showRangeSelect && showRangeInput && (
            <div className="mt-3 flex items-center space-x-2">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={rangeInput}
                  onChange={handleRangeInputChange}
                  placeholder="输入范围，例如：1-5, 1,3,5, 1-3,5-7"
                  className="w-full px-3 py-1.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>
              <button
                type="button"
                onClick={handleRangeSelect}
                className="px-3 py-1.5 text-xs bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors shadow-sm font-medium"
              >
                确认
              </button>
              <button
                type="button"
                onClick={() => setShowRangeInput(false)}
                className="px-3 py-1.5 text-xs bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-sm font-medium"
              >
                取消
              </button>
            </div>
          )}
        </div>

        {/* 项目列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="relative">
                <div className="w-10 h-10 border-4 border-indigo-200 border-t-indigo-500 rounded-full animate-spin"></div>
                <div className="absolute top-0 left-0 w-10 h-10 border-4 border-transparent border-b-indigo-300 rounded-full animate-spin" style={{ animationDuration: '1.5s' }}></div>
              </div>
              <p className="text-indigo-700 ml-3 font-medium">{loadingText}</p>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-500">{emptyText}</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredItems.map((item) => (
                <div
                  key={item.id}
                  className={`p-3 rounded-xl border transition-all ${
                    item.disabled
                      ? 'bg-gray-100 border-gray-300 cursor-not-allowed'
                      : selectedIds.includes(item.id)
                        ? 'bg-indigo-50 border-indigo-300 shadow-sm cursor-pointer'
                        : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 cursor-pointer'
                  }`}
                  onClick={() => !item.disabled && handleItemSelect(item.id)}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`item-${item.id}`}
                      checked={selectedIds.includes(item.id)}
                      onChange={() => !item.disabled && handleItemSelect(item.id)}
                      disabled={item.disabled}
                      className={`mr-3 h-4 w-4 focus:ring-indigo-500 border-gray-300 rounded ${
                        item.disabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-indigo-600 cursor-pointer'
                      }`}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex-1">
                      <label
                        htmlFor={`item-${item.id}`}
                        className={`block text-sm font-medium ${
                          item.disabled
                            ? 'text-gray-500 cursor-not-allowed'
                            : 'text-gray-800 cursor-pointer'
                        }`}
                      >
                        {item.name}
                        {item.disabled && <span className="ml-2 text-xs text-blue-500">(当前章节)</span>}
                      </label>
                      {item.description && (
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">{item.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-3 bg-gradient-to-r from-gray-50 to-indigo-50">
          <button
            className="px-4 py-2 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-xl hover:from-indigo-600 hover:to-blue-700 transition-colors shadow-md font-medium"
            onClick={onConfirm}
          >
            确认选择
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomSelectorDialog;
