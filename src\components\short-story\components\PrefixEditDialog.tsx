import React, { useState, useEffect } from 'react';
import { SavedAIPrefix } from '../../../services/ai/AIGeneratedPrefixStorageService';
import { PhaseType } from '../../../types/ai-persona';
import { XIcon } from '../../common/icons/PrefixIcons';

interface PrefixEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  prefix: SavedAIPrefix | null;
  onSave: (updatedPrefix: SavedAIPrefix) => void;
  currentPhase?: PhaseType;
}

export const PrefixEditDialog: React.FC<PrefixEditDialogProps> = ({
  isOpen,
  onClose,
  prefix,
  onSave,
  currentPhase
}) => {
  const [formData, setFormData] = useState({
    content: '',
    description: '',
    category: 'custom',
    phase: '' as PhaseType | '',
    tags: [] as string[],
    isFavorite: false
  });
  const [tagInput, setTagInput] = useState('');

  // 初始化表单数据
  useEffect(() => {
    if (prefix) {
      setFormData({
        content: prefix.content,
        description: prefix.description,
        category: prefix.category,
        phase: prefix.phase || '',
        tags: [...prefix.tags],
        isFavorite: prefix.isFavorite
      });
    } else {
      // 新建模式
      setFormData({
        content: '',
        description: '',
        category: 'custom',
        phase: currentPhase || '',
        tags: [],
        isFavorite: false
      });
    }
    setTagInput('');
  }, [prefix, currentPhase, isOpen]);

  const handleSave = () => {
    if (!formData.content.trim()) {
      alert('请输入前置消息内容');
      return;
    }

    const updatedPrefix: SavedAIPrefix = {
      id: prefix?.id || `prefix_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: formData.content.trim(),
      description: formData.description.trim(),
      category: formData.category,
      phase: formData.phase || undefined,
      tags: formData.tags,
      isFavorite: formData.isFavorite,
      usageCount: prefix?.usageCount || 0,
      createdAt: prefix?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    onSave(updatedPrefix);
    onClose();
  };

  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.target === document.querySelector('input[placeholder="添加标签..."]')) {
      e.preventDefault();
      handleAddTag();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[10003] bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">
            {prefix ? '编辑前置消息' : '创建前置消息'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XIcon size={24} />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* 内容输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              前置消息内容 *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="输入前置消息内容..."
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>

          {/* 描述输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述说明
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="简要描述这个前置消息的用途..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 类别和阶段 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                类别
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="custom">自定义</option>
                <option value="context_enhancement">上下文增强</option>
                <option value="persona_enhancement">人设强化</option>
                <option value="format_specification">格式规范</option>
                <option value="ace_framework">ACE框架</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                阶段
              </label>
              <select
                value={formData.phase}
                onChange={(e) => setFormData(prev => ({ ...prev, phase: e.target.value as PhaseType | '' }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">通用</option>
                <option value="intro">导语</option>
                <option value="setup">铺垫期</option>
                <option value="compression">爆发情绪</option>
                <option value="climax">反转</option>
                <option value="resolution">解气</option>
                <option value="ending">结局</option>
                <option value="buildup">铺垫</option>
                <option value="custom">自定义</option>
              </select>
            </div>
          </div>

          {/* 标签管理 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              标签
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <XIcon size={12} />
                  </button>
                </span>
              ))}
            </div>
            <div className="flex gap-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="添加标签..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleAddTag}
                disabled={!tagInput.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                添加
              </button>
            </div>
          </div>

          {/* 收藏选项 */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isFavorite}
                onChange={(e) => setFormData(prev => ({ ...prev, isFavorite: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">标记为收藏</span>
            </label>
          </div>
        </div>

        {/* 底部操作 */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              {prefix ? '保存修改' : '创建'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
