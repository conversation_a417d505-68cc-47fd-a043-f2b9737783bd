"use client";

import React from 'react';
import { CoreMystery } from '@/factories/ai/services/types/ShortStoryTypes';

interface CoreMysteryPreviewPanelProps {
  coreMystery: CoreMystery | null;
  isLoading: boolean;
  onConfirm: () => void;
  onRegenerate: () => void;
}

/**
 * 核心悬念预览面板组件
 * 显示生成的核心悬念，让用户确认后再继续
 */
export const CoreMysteryPreviewPanel: React.FC<CoreMysteryPreviewPanelProps> = ({
  coreMystery,
  isLoading,
  onConfirm,
  onRegenerate
}) => {
  
  return (
    <div className="flex-1 bg-gray-50 rounded-xl p-6 overflow-hidden flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-800">核心悬念预览</h3>
          <p className="text-sm text-gray-600 mt-1">
            确认这个"透露一半，隐藏一半"的核心悬念，然后继续生成框架
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-xs text-gray-500 text-right">
            <div>🎯 悬念核心已生成</div>
            <div>📖 等待用户确认</div>
          </div>
        </div>
      </div>

      {/* 核心悬念内容 */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500 mx-auto mb-4"></div>
              <p className="text-gray-600">正在生成核心悬念...</p>
            </div>
          </div>
        ) : coreMystery ? (
          <div className="space-y-6">
            {/* 悬念标题 */}
            <div className="bg-white rounded-lg p-6 border-l-4 border-amber-500 shadow-sm">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl">📚</span>
                <h4 className="text-lg font-bold text-gray-800">短篇标题</h4>
              </div>
              <p className="text-xl font-semibold text-amber-700">{coreMystery.title}</p>
            </div>

            {/* 核心问题 */}
            <div className="bg-white rounded-lg p-6 border-l-4 border-blue-500 shadow-sm">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl">❓</span>
                <h4 className="text-lg font-bold text-gray-800">核心悬念问题</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">{coreMystery.coreQuestion}</p>
            </div>

            {/* 透露的一半 */}
            <div className="bg-white rounded-lg p-6 border-l-4 border-green-500 shadow-sm">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl">👁️</span>
                <h4 className="text-lg font-bold text-gray-800">透露的一半</h4>
                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">开篇展示</span>
              </div>
              <p className="text-gray-700 leading-relaxed">{coreMystery.revealedHalf}</p>
            </div>

            {/* 隐藏的一半 */}
            <div className="bg-white rounded-lg p-6 border-l-4 border-red-500 shadow-sm">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl">🔒</span>
                <h4 className="text-lg font-bold text-gray-800">隐藏的一半</h4>
                <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">逐步揭露</span>
              </div>
              <p className="text-gray-700 leading-relaxed">{coreMystery.hiddenHalf}</p>
            </div>

            {/* 最终真相 */}
            {coreMystery.finalTruth && (
              <div className="bg-white rounded-lg p-6 border-l-4 border-purple-500 shadow-sm">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-2xl">💡</span>
                  <h4 className="text-lg font-bold text-gray-800">最终真相</h4>
                  <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">结局揭示</span>
                </div>
                <p className="text-gray-700 leading-relaxed">{coreMystery.finalTruth}</p>
              </div>
            )}

            {/* 情感冲击 */}
            {coreMystery.emotionalImpact && (
              <div className="bg-white rounded-lg p-6 border-l-4 border-orange-500 shadow-sm">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-2xl">💥</span>
                  <h4 className="text-lg font-bold text-gray-800">情感冲击点</h4>
                  <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full">读者感受</span>
                </div>
                <p className="text-gray-700 leading-relaxed">{coreMystery.emotionalImpact}</p>
              </div>
            )}

            {/* 关联信息 */}
            {(coreMystery.mainCharacter || coreMystery.settingInfo) && (
              <div className="bg-white rounded-lg p-6 border-l-4 border-indigo-500 shadow-sm">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-2xl">🔗</span>
                  <h4 className="text-lg font-bold text-gray-800">关联信息</h4>
                </div>
                <div className="space-y-2">
                  {coreMystery.mainCharacter && (
                    <div>
                      <span className="font-medium text-indigo-600">主角：</span>
                      <span className="text-gray-700">{coreMystery.mainCharacter}</span>
                    </div>
                  )}
                  {coreMystery.settingInfo && (
                    <div>
                      <span className="font-medium text-indigo-600">背景：</span>
                      <span className="text-gray-700">{coreMystery.settingInfo}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-6xl mb-4">🤔</div>
              <p className="text-gray-600">还没有生成核心悬念</p>
            </div>
          </div>
        )}
      </div>

      {/* 底部操作按钮 */}
      {coreMystery && !isLoading && (
        <div className="flex items-center justify-between pt-6 border-t">
          <button
            onClick={onRegenerate}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
          >
            🔄 重新生成
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-500">
              确认这个悬念设定，继续生成框架
            </div>
            <button
              onClick={onConfirm}
              className="px-8 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl hover:from-amber-600 hover:to-orange-600 transition-all shadow-lg font-medium"
            >
              ✨ 确认并生成框架
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
