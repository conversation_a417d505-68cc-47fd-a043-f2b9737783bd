"use client";

import React, { useState } from 'react';
import { Character, Terminology, WorldBuilding } from '@/lib/db/dexie';

import { CharacterForm } from './CharacterForm';
import { EditIcon, DeleteIcon } from './icons';
import CharacterUpdateFromChapter from './CharacterUpdateFromChapter';
import CharacterUpdaterDialog from './CharacterUpdaterDialog';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface CharacterDetailProps {
  character: Character | null;
  bookId: string;
  characters: Character[];
  isEditing: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onSave: (character: Character) => void;
  onCancel: () => void;
  chapters?: GenericChapter[]; // 可选的章节列表，用于人物更新功能
  terminologies?: Terminology[]; // 可选的术语列表，用于关联选择
  worldBuildings?: WorldBuilding[]; // 可选的世界观列表，用于关联选择
}

/**
 * 人物详情组件
 */
export const CharacterDetail: React.FC<CharacterDetailProps> = ({
  character,
  bookId,
  characters,
  isEditing,
  onEdit,
  onDelete,
  onSave,
  onCancel,
  chapters = [], // 默认为空数组
  terminologies = [], // 默认为空数组
  worldBuildings = [] // 默认为空数组
}) => {
  // 整体更新对话框状态
  const [isUpdaterOpen, setIsUpdaterOpen] = useState(false);
  if (isEditing && character) {
    // 渲染编辑表单
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 overflow-auto">
          <CharacterForm
            character={character}
            bookId={bookId}
            isNew={!characters.some(c => c.id === character.id)}
            onSave={onSave}
            onCancel={onCancel}
            hideButtons={true}
            terminologies={terminologies}
            worldBuildings={worldBuildings}
          />
        </div>

        {/* 表单外侧的按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t mt-4">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-300 transform hover:scale-105"
            onClick={onCancel}
            style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}
          >
            取消
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 transform hover:scale-105"
            onClick={() => {
              // 获取表单组件的引用并调用其handleSave方法
              const formElement = document.getElementById('character-form');
              if (formElement) {
                // 触发表单的提交事件
                const saveEvent = new Event('submit', { bubbles: true, cancelable: true });
                formElement.dispatchEvent(saveEvent);
              } else {
                console.error('找不到表单元素');
                // 如果找不到表单元素，则回退到直接调用onSave
                onSave(character);
              }
            }}
            style={{
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              animation: 'pulse 2s infinite'
            }}
          >
            <style jsx>{`
              @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5); }
                70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
                100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
              }
            `}</style>
            保存
          </button>
        </div>
      </div>
    );
  }

  if (!character) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p>请选择一个人物查看详情</p>
          <p className="mt-2">或点击"创建新人物"按钮</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold" style={{ color: 'var(--color-primary)' }}>{character.name}</h2>
        <div className="flex space-x-2">
          {/* 从章节更新人物信息按钮 */}
          {chapters.length > 0 && (
            <CharacterUpdateFromChapter
              character={character}
              chapters={chapters}
              onUpdate={onSave}
            />
          )}

          {/* AI整体更新按钮 */}
          <button
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
            onClick={() => setIsUpdaterOpen(true)}
            title="使用AI整体更新人物信息"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>AI整体更新</span>
          </button>

          <button
            className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            onClick={onEdit}
          >
            <EditIcon className="w-4 h-4 mr-1" />
            <span>编辑</span>
          </button>
          <button
            className="flex items-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
            onClick={() => {
              console.log('点击删除按钮');
              onDelete();
            }}
          >
            <DeleteIcon className="w-4 h-4 mr-1" />
            <span>删除</span>
          </button>
        </div>
      </div>

      <div className="space-y-6">
        <style jsx>{`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }

          .animate-fadeIn {
            animation: fadeIn 0.5s ease forwards;
            opacity: 0;
          }
        `}</style>

        {/* 基本信息部分 */}
        <div className="border-l-4 border-blue-500 pl-4 py-1 mb-2">
          <h3 className="text-lg font-bold text-blue-700">基本信息</h3>
        </div>

        {/* 基本信息 */}
        <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.1s' }}>
          <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>基本描述</h3>
          <p className="text-gray-700 whitespace-pre-line">{character.description}</p>
        </div>

        {/* 角色原型 */}
        {character.characterArchetype && (
          <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.2s' }}>
            <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>角色原型</h3>
            <p className="text-gray-700 whitespace-pre-line">{character.characterArchetype}</p>
          </div>
        )}

        {/* 详细特征部分 */}
        {(character.appearance || character.personality || character.background || character.goals || character.growthArc) && (
          <>
            <div className="border-l-4 border-green-500 pl-4 py-1 mb-2 mt-8">
              <h3 className="text-lg font-bold text-green-700">详细特征</h3>
            </div>

            {/* 外貌 */}
            {character.appearance && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.3s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>外貌</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.appearance}</p>
              </div>
            )}

            {/* 性格 */}
            {character.personality && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.4s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>性格</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.personality}</p>
              </div>
            )}

            {/* 背景 */}
            {character.background && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.5s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>背景</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.background}</p>
              </div>
            )}

            {/* 目标 */}
            {character.goals && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.6s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>目标</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.goals}</p>
              </div>
            )}

            {/* 成长弧线 */}
            {character.growthArc && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.7s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>成长弧线</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.growthArc}</p>
              </div>
            )}
          </>
        )}

        {/* 隐藏信息素部分 */}
        {(character.hiddenMotivation || character.secretHistory || character.innerConflicts || character.symbolism) && (
          <>
            <div className="border-l-4 border-purple-500 pl-4 py-1 mb-2 mt-8">
              <h3 className="text-lg font-bold text-purple-700">隐藏信息素</h3>
              <p className="text-xs text-purple-600 mt-1">这些信息通常不会直接展现在故事中，但会影响人物的行为和决策</p>
            </div>

            {/* 隐藏动机 */}
            {character.hiddenMotivation && (
              <div className="p-4 bg-purple-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.8s' }}>
                <h3 className="text-lg font-medium mb-2 text-purple-700">隐藏动机</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.hiddenMotivation}</p>
              </div>
            )}

            {/* 秘密历史 */}
            {character.secretHistory && (
              <div className="p-4 bg-purple-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '0.9s' }}>
                <h3 className="text-lg font-medium mb-2 text-purple-700">秘密历史</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.secretHistory}</p>
              </div>
            )}

            {/* 内心冲突 */}
            {character.innerConflicts && (
              <div className="p-4 bg-purple-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '1.0s' }}>
                <h3 className="text-lg font-medium mb-2 text-purple-700">内心冲突</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.innerConflicts}</p>
              </div>
            )}

            {/* 象征意义 */}
            {character.symbolism && (
              <div className="p-4 bg-purple-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '1.1s' }}>
                <h3 className="text-lg font-medium mb-2 text-purple-700">象征意义</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.symbolism}</p>
              </div>
            )}
          </>
        )}

        {/* 关系部分 */}
        {((character.relationships && character.relationships.length > 0) || character.notes) && (
          <>
            <div className="border-l-4 border-yellow-500 pl-4 py-1 mb-2 mt-8">
              <h3 className="text-lg font-bold text-yellow-700">关系与备注</h3>
            </div>

            {/* 关系 */}
            {character.relationships && character.relationships.length > 0 && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '1.2s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>关系</h3>
                <ul className="list-disc pl-5">
                  {character.relationships.map((relationship, index) => (
                    <li key={index} className="mb-2 animate-fadeIn" style={{ animationDelay: `${1.3 + index * 0.1}s` }}>
                      <span className="font-medium">{relationship.relationshipType}：</span>
                      <span>{relationship.description}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* 备注 */}
            {character.notes && (
              <div className="p-4 bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-md animate-fadeIn" style={{ animationDelay: '1.4s' }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>备注</h3>
                <p className="text-gray-700 whitespace-pre-line">{character.notes}</p>
              </div>
            )}
          </>
        )}
      </div>

      {/* 人物整体更新对话框 */}
      {character && (
        <CharacterUpdaterDialog
          isOpen={isUpdaterOpen}
          onClose={() => setIsUpdaterOpen(false)}
          character={character}
          onUpdateCharacter={onSave}
          bookId={bookId}
        />
      )}
    </div>
  );
};
