"use client";

/**
 * AI拆书功能的提示词模板
 *
 * 本文件包含AI拆书功能使用的各种提示词模板，按照不同的分析模式和目的进行分类。
 * 这些模板可以在AI拆书功能的UI界面中选择使用，也可以通过代码直接调用。
 */

import { PromptCategory } from '@/lib/db/dexie';

/**
 * 提示词模板接口
 */
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: PromptCategory;
  tags: string[];
}

/**
 * 单本拆解提示词模板
 */
export const singleBookBreakdownPrompts: PromptTemplate[] = [
  {
    id: 'single-character-analysis',
    name: '人物深度分析',
    description: '深入分析小说中的人物形象、性格特点、成长轨迹和关系网络',
    content: `请重点分析小说中的人物，包括但不限于：
1. 主要人物的性格特点和心理动机
2. 人物的成长轨迹和转变过程
3. 人物之间的关系网络和互动模式
4. 人物塑造的艺术手法和创新点
5. 人物形象与主题的关联

请尽可能深入分析，提供具体的文本依据，并指出人物塑造的成功之处和不足之处。`,
    category: PromptCategory.BookAnalysis,
    tags: ['人物分析', '单本拆解']
  },
  {
    id: 'single-plot-analysis',
    name: '情节结构分析',
    description: '分析小说的情节结构、冲突设置、高潮转折和叙事节奏',
    content: `请重点分析小说的情节结构，包括但不限于：
1. 整体情节架构和叙事线索
2. 主要冲突的设置和发展
3. 高潮和转折点的安排
4. 叙事节奏的控制和变化
5. 情节设计的创新点和不足

请结合具体章节分析情节的起承转合，评价情节安排的合理性和吸引力。`,
    category: PromptCategory.BookAnalysis,
    tags: ['情节分析', '单本拆解']
  },
  {
    id: 'single-style-analysis',
    name: '写作风格分析',
    description: '分析小说的语言特点、叙事视角、修辞手法和情感基调',
    content: `请重点分析小说的写作风格，包括但不限于：
1. 语言特点和词汇选择
2. 叙事视角和叙述方式
3. 常用的修辞手法和表现技巧
4. 情感基调和氛围营造
5. 风格的独特性和创新点

请提供具体的文本示例，说明作者的风格特点，并评价其效果。`,
    category: PromptCategory.BookAnalysis,
    tags: ['风格分析', '单本拆解']
  },
  {
    id: 'single-worldbuilding-analysis',
    name: '世界观设定分析',
    description: '分析小说的世界观设定、背景构建、规则体系和文化元素',
    content: `请重点分析小说的世界观设定，包括但不限于：
1. 背景设定的完整性和合理性
2. 世界规则和运行机制
3. 文化、历史、地理等元素的构建
4. 世界观与情节、人物的融合度
5. 世界观设定的创新点和吸引力

请评价世界观设定的深度和广度，分析其对故事的支撑作用。`,
    category: PromptCategory.BookAnalysis,
    tags: ['世界观分析', '单本拆解']
  },
  {
    id: 'single-theme-analysis',
    name: '主题思想分析',
    description: '分析小说的核心主题、象征意义、哲学思考和社会价值',
    content: `请重点分析小说的主题思想，包括但不限于：
1. 核心主题和中心思想
2. 象征意义和隐喻表达
3. 哲学思考和价值观念
4. 社会意义和现实关联
5. 主题表达的艺术手法

请深入挖掘作品的思想内涵，分析主题的表达方式和深度。`,
    category: PromptCategory.BookAnalysis,
    tags: ['主题分析', '单本拆解']
  }
];

/**
 * 合并拆解提示词模板
 */
export const mergedBookBreakdownPrompts: PromptTemplate[] = [
  {
    id: 'merged-comparative-analysis',
    name: '比较分析',
    description: '比较多部作品的共同点和差异，找出创作规律和特点',
    content: `请对提供的多部作品进行比较分析，重点关注：
1. 人物塑造的共同模式和差异化处理
2. 情节结构的相似性和独特安排
3. 写作风格的共性和个性特点
4. 世界观设定的共通元素和创新点
5. 主题思想的共鸣和差异化表达

请找出这些作品的共同规律和各自特点，评价它们的优劣得失。`,
    category: PromptCategory.BookAnalysis,
    tags: ['比较分析', '合并拆解']
  },
  {
    id: 'merged-pattern-analysis',
    name: '创作模式分析',
    description: '分析多部作品中的创作模式、套路和技巧',
    content: `请分析这些作品中的创作模式和技巧，重点关注：
1. 常见的人物原型和角色设置
2. 重复出现的情节模式和结构框架
3. 一致的写作技巧和表现手法
4. 相似的世界观构建方法
5. 反复探讨的主题和思想

请总结这些作品的创作套路和技巧，评价其效果和局限性。`,
    category: PromptCategory.BookAnalysis,
    tags: ['模式分析', '合并拆解']
  }
];

/**
 * 混合拆解提示词模板
 */
export const mixedBookBreakdownPrompts: PromptTemplate[] = [
  {
    id: 'mixed-creative-fusion',
    name: '创意融合',
    description: '创造性地融合多部作品的元素，构建新的故事可能',
    content: `请创造性地融合这些作品的元素，构想：
1. 首先，请仔细分析并找出这些作品中的共同元素，包括：相似的人物原型、共通的情节模式、相近的主题思想、类似的世界观设定等
2. 如果这些作品中的人物同处一个故事中，会产生什么样的互动和冲突
3. 如何将不同作品的情节线索创造性地交织在一起
4. 不同写作风格的优势如何互补，形成新的表达方式
5. 如何构建一个包含所有作品元素的统一世界观
6. 通过混合分析，能够提炼出什么更深层次的主题

请大胆发挥创意，但要基于原作品的共同特点，保持合理性。特别强调寻找作品间的共性，这是混合拆解的关键。`,
    category: PromptCategory.BookAnalysis,
    tags: ['创意融合', '混合拆解']
  },
  {
    id: 'mixed-adaptation-analysis',
    name: '改编可能性分析',
    description: '分析多部作品混合改编的可能性和方向',
    content: `请分析这些作品混合改编的可能性，重点考虑：
1. 首先，请详细分析并列出这些作品的共同点，包括：人物特质、情节发展模式、叙事结构、主题思想等方面的相似之处
2. 哪些人物可以跨作品互动，产生新的故事线
3. 如何重组各作品的情节，创造连贯的新叙事
4. 不同风格的作品如何在改编中找到平衡点
5. 如何整合不同的世界观设定，避免逻辑冲突
6. 混合改编能够强化或拓展什么样的主题

请提出具体的改编构想，评估其可行性和吸引力。请特别注重寻找作品间的共同点，这是成功混合改编的基础。`,
    category: PromptCategory.BookAnalysis,
    tags: ['改编分析', '混合拆解']
  },
  {
    id: 'mixed-common-elements-analysis',
    name: '共同元素分析',
    description: '专注分析多部作品中的共同元素和模式',
    content: `请专注分析这些作品中的共同元素和模式，重点关注：
1. 人物层面：共同的人物原型、性格特质、成长模式和关系模式
2. 情节层面：相似的情节结构、冲突设置、转折点和结局类型
3. 风格层面：共通的叙事手法、语言特点、节奏控制和氛围营造
4. 世界观层面：类似的背景设定、规则系统、文化元素和时空构建
5. 主题层面：重复出现的核心思想、价值观念、哲学思考和社会议题

请深入挖掘这些作品的共性，分析它们反映的创作规律和文学传统。这种共同元素分析将为混合创作提供坚实基础。`,
    category: PromptCategory.BookAnalysis,
    tags: ['共同元素', '混合拆解']
  }
];

/**
 * 同步拆解提示词模板
 */
export const syncBookBreakdownPrompts: PromptTemplate[] = [
  {
    id: 'sync-parallel-analysis',
    name: '并行深度分析',
    description: '对多部作品进行并行的深度分析，保持各自的独立性',
    content: `请对每部作品进行独立的深度分析，每部作品都应包括：
1. 主要人物分析：性格特点、动机、成长轨迹
2. 情节分析：主要情节线、冲突设置、高潮转折
3. 写作风格分析：语言特点、叙事视角、修辞手法
4. 世界观设定分析：背景设定、规则体系、文化元素
5. 主题分析：核心主题、象征意义、哲学思考

请为每部作品创建独立的分析章节，保持分析的深度和全面性。

在完成各自分析后，请添加一个"共同点分析"章节，寻找这些作品在人物、情节、风格、世界观和主题等方面的共同点，这有助于理解作者的创作倾向和文学规律。`,
    category: PromptCategory.BookAnalysis,
    tags: ['并行分析', '同步拆解']
  },
  {
    id: 'sync-rating-analysis',
    name: '评分对比分析',
    description: '对多部作品的各个方面进行评分和对比分析',
    content: `请对每部作品的以下方面进行1-10分的评分和分析：
1. 人物塑造：评价人物的丰满度、可信度和吸引力
2. 情节设计：评价情节的紧凑性、合理性和吸引力
3. 写作技巧：评价语言表达、叙事手法和节奏控制
4. 世界构建：评价世界观的完整性、一致性和创新性
5. 主题深度：评价主题的深刻性、表达方式和思想价值

请为每部作品提供评分和简要分析，最后进行横向对比。

在评分对比后，请添加一个"共同特征分析"部分，详细分析这些作品在各个评分维度上的共同特征和模式，这将有助于识别作者的一贯创作风格和优势领域。`,
    category: PromptCategory.BookAnalysis,
    tags: ['评分分析', '同步拆解']
  },
  {
    id: 'sync-common-patterns-analysis',
    name: '共同模式分析',
    description: '在保持作品独立性的同时，分析多部作品的共同模式和特征',
    content: `请先对每部作品进行独立分析，然后重点寻找它们之间的共同模式和特征：

1. 为每部作品提供简要的独立分析，包括：
   - 核心人物和关系结构
   - 主要情节发展路线
   - 显著的写作风格特点
   - 基本世界观设定
   - 核心主题思想

2. 在完成独立分析后，请详细探讨这些作品的共同点：
   - 人物设计中的共同原型和特质
   - 情节结构中的相似模式和发展规律
   - 写作风格中的一致性特征
   - 世界观构建中的共通元素
   - 主题表达中的重复关注点

请在保持每部作品独立性的同时，深入挖掘它们之间的内在联系和共同特征，这将揭示作者的创作倾向和艺术追求。`,
    category: PromptCategory.BookAnalysis,
    tags: ['共同模式', '同步拆解']
  }
];

/**
 * 获取所有AI拆书提示词模板
 * @returns 所有提示词模板数组
 */
export function getAllBookBreakdownPrompts(): PromptTemplate[] {
  return [
    ...singleBookBreakdownPrompts,
    ...mergedBookBreakdownPrompts,
    ...mixedBookBreakdownPrompts,
    ...syncBookBreakdownPrompts
  ];
}

/**
 * 根据ID获取提示词模板
 * @param id 模板ID
 * @returns 提示词模板或undefined
 */
export function getPromptTemplateById(id: string): PromptTemplate | undefined {
  return getAllBookBreakdownPrompts().find(template => template.id === id);
}

/**
 * 根据分析模式获取提示词模板
 * @param mode 分析模式
 * @returns 提示词模板数组
 */
export function getPromptTemplatesByMode(mode: 'single' | 'merged' | 'mixed' | 'sync'): PromptTemplate[] {
  switch (mode) {
    case 'single':
      return singleBookBreakdownPrompts;
    case 'merged':
      return mergedBookBreakdownPrompts;
    case 'mixed':
      return mixedBookBreakdownPrompts;
    case 'sync':
      return syncBookBreakdownPrompts;
    default:
      return [];
  }
}
