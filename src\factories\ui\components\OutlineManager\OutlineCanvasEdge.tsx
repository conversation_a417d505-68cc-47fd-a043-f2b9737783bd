"use client";

import React, { memo, useEffect, useState, useCallback, useRef } from 'react';
import ReactDOM from 'react-dom';
import { getBezierPath, EdgeProps, useReactFlow } from 'reactflow';

interface OutlineCanvasEdgeProps extends EdgeProps {
  data?: {
    nodeType?: string;
    relationshipType?: 'parent-child' | 'sibling';
    sourceNodeType?: 'chapter' | 'scene' | 'note'; // 源节点类型
    targetNodeType?: 'chapter' | 'scene' | 'note'; // 目标节点类型
    isNew?: boolean; // 是否新创建的连接
  };
}

/**
 * 自定义大纲边组件
 * 用于在画布视图中显示节点之间的连接
 * 优化了连线渲染和稳定性
 */
const OutlineCanvasEdge: React.FC<OutlineCanvasEdgeProps> = (props) => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    style = {},
    data,
    selected,
  } = props;

  // 获取ReactFlow实例，用于删除边
  const { deleteElements } = useReactFlow();

  // 使用状态保存计算的路径，避免频繁重新计算导致的闪烁
  const [pathData, setPathData] = useState<string | null>(null);

  // 添加悬停状态
  const [isHovered, setIsHovered] = useState(false);

  // 添加动画状态
  const [isAnimating, setIsAnimating] = useState(false);

  // 添加右键菜单状态
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });

  // 创建引用，用于动画效果
  const pathRef = useRef<SVGPathElement>(null);
  const animationRef = useRef<SVGAnimateElement>(null);

  // 长按计时器
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  // 检测是否为触摸设备
  const isTouchDevice = useRef(false);
  useEffect(() => {
    isTouchDevice.current = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  // 处理触摸开始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    // 如果当前处于拖拽状态或冷却状态，不处理触摸事件
    if (document.body.classList.contains('dragging-active') ||
        document.body.classList.contains('dragging-cooldown')) {
      return;
    }

    // 开始长按计时
    longPressTimer.current = setTimeout(() => {
      // 获取触摸位置
      const touch = e.touches[0];

      // 触发右键菜单
      setContextMenuPosition({ x: touch.clientX, y: touch.clientY });
      setShowContextMenu(true);

      // 确保连线被选中
      if (!selected) {
        // 移除所有其他边的选中效果
        document.querySelectorAll('.selected-edge').forEach(el => {
          if (el.id !== id) {
            el.classList.remove('selected-edge');
          }
        });

        // 通知ReactFlow选中此边
        const edgePath = document.getElementById(id);
        if (edgePath) {
          edgePath.classList.add('selected-edge');
        }
      }

      // 触发动画效果
      setIsAnimating(true);

      // 清除计时器
      longPressTimer.current = null;
    }, 500); // 500ms长按触发
  }, [id, selected, setContextMenuPosition, setShowContextMenu, setIsAnimating]);

  // 处理触摸结束
  const handleTouchEnd = useCallback(() => {
    // 清除长按计时器
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  // 处理触摸移动
  const handleTouchMove = useCallback(() => {
    // 如果手指移动，取消长按
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  // 使用防抖处理鼠标悬停，避免频繁状态更新
  const handleMouseEnter = useCallback(() => {
    // 避免重复设置状态
    if (!isHovered) {
      setIsHovered(true);
      // 当鼠标悬停时启动动画
      setIsAnimating(true);
    }
  }, [isHovered]);

  // 处理鼠标离开，添加延迟避免频繁切换
  const handleMouseLeave = useCallback(() => {
    // 使用延迟，避免鼠标在边缘移动时频繁触发
    const timer = setTimeout(() => {
      setIsHovered(false);
      // 鼠标离开后保持动画一段时间，然后停止
      setTimeout(() => setIsAnimating(false), 1000);

      // 如果鼠标离开，关闭上下文菜单
      setShowContextMenu(false);
    }, 100); // 添加100ms延迟

    // 清理函数，如果在延迟期间再次进入，取消状态更改
    return () => clearTimeout(timer);
  }, []);

  // 处理右键点击
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 如果当前处于拖拽状态或冷却状态，不处理右键事件
    if (document.body.classList.contains('dragging-active') ||
        document.body.classList.contains('dragging-cooldown')) {
      return;
    }

    console.log('Right click on edge:', id, e.clientX, e.clientY); // 添加日志，便于调试

    // 设置上下文菜单位置
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);

    // 确保连线被选中，以便用户可以看到哪条连线将被删除
    if (!selected) {
      // 移除所有其他边的选中效果
      document.querySelectorAll('.selected-edge').forEach(el => {
        if (el.id !== id) {
          el.classList.remove('selected-edge');
        }
      });

      // 通知ReactFlow选中此边
      // 注意：这里我们不能直接调用ReactFlow的setSelectedElements，
      // 因为它不是通过props传递的。我们可以通过添加一个自定义类来模拟选中效果
      const edgePath = document.getElementById(id);
      if (edgePath) {
        edgePath.classList.add('selected-edge');
      }
    }

    // 触发动画效果
    setIsAnimating(true);

    // 阻止默认的上下文菜单
    setTimeout(() => {
      // 确保菜单显示在最上层
      const menuElement = document.querySelector('.react-flow__edge-context-menu');
      if (menuElement) {
        (menuElement as HTMLElement).style.zIndex = '10000';
      }
    }, 0);
  }, [id, selected, setContextMenuPosition, setShowContextMenu, setIsAnimating]);

  // 处理删除边
  const handleDeleteEdge = useCallback(() => {
    // 如果连线不是通过ReactFlow选中的，移除我们添加的选中效果
    if (!selected) {
      const edgePath = document.getElementById(id);
      if (edgePath) {
        edgePath.classList.remove('selected-edge');
      }
    }

    // 添加删除动画效果
    const edgePath = document.getElementById(id);
    if (edgePath) {
      // 添加淡出动画类
      edgePath.classList.add('edge-delete');
      edgePath.style.opacity = '0';
      edgePath.style.transition = 'opacity 0.3s ease';
    }

    // 延迟删除边，等待动画完成
    setTimeout(() => {
      // 删除边
      deleteElements({ edges: [{ id }] });
    }, 300);

    // 立即关闭上下文菜单
    setShowContextMenu(false);
  }, [deleteElements, id, selected]);

  // 处理点击其他区域关闭上下文菜单
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (showContextMenu) {
        setShowContextMenu(false);

        // 如果连线不是通过ReactFlow选中的，移除我们添加的选中效果
        if (!selected) {
          const edgePath = document.getElementById(id);
          if (edgePath) {
            edgePath.classList.remove('selected-edge');
          }
        }
      }
    };

    // 使用捕获阶段，确保在事件冒泡之前处理
    document.addEventListener('click', handleClickOutside, true);
    document.addEventListener('contextmenu', handleClickOutside, true);

    return () => {
      document.removeEventListener('click', handleClickOutside, true);
      document.removeEventListener('contextmenu', handleClickOutside, true);
    };
  }, [showContextMenu, id, selected]);

  // 跟踪是否已经播放过动画
  const animationPlayedRef = useRef(false);

  // 处理新创建的边的动画效果 - 只在初次创建时触发，移动节点时不触发
  useEffect(() => {
    if (pathRef.current && animationRef.current) {
      // 获取路径总长度
      const pathLength = pathRef.current.getTotalLength() || 1000;

      // 设置初始状态 - 使用虚线和偏移实现路径生长效果
      pathRef.current.style.strokeDasharray = `${pathLength}`;

      if (data?.isNew && !animationPlayedRef.current) {
        // 新创建的边，设置初始偏移，然后通过动画减少到0
        pathRef.current.style.strokeDashoffset = `${pathLength}`;
        // 重置动画
        animationRef.current.beginElement();
        // 标记动画已播放，避免重复播放
        animationPlayedRef.current = true;
      } else {
        // 已存在的边或已播放过动画的边，不需要动画效果
        pathRef.current.style.strokeDashoffset = '0';
      }
    }
  }, [data?.isNew]);

  // 当源点或目标点位置变化时重新计算路径
  useEffect(() => {
    // 获取贝塞尔曲线路径，使用固定曲率
    const [newPath] = getBezierPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
      curvature: 0.4 // 降低曲率，使连线更稳定
    });

    setPathData(newPath);
  }, [sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition]);

  // 根据节点类型和关系类型设置边的样式
  const getEdgeStyles = () => {
    // 基础样式
    const baseStyle = {
      strokeWidth: 2,
      strokeOpacity: 0.8,
      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.15))',
      transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    };

    // 获取源节点和目标节点的颜色
    const getNodeColor = (type?: string) => {
      switch (type) {
        case 'chapter':
          return {
            color: 'var(--outline-primary)',
            rgb: '59, 130, 246', // 蓝色
          };
        case 'scene':
          return {
            color: 'var(--outline-secondary)',
            rgb: '34, 197, 94', // 绿色
          };
        default:
          return {
            color: 'var(--outline-info)',
            rgb: '168, 85, 247', // 紫色
          };
      }
    };

    // 获取源节点和目标节点的颜色
    const sourceColor = getNodeColor(data?.sourceNodeType || data?.nodeType);
    const targetColor = getNodeColor(data?.targetNodeType || data?.nodeType);

    // 创建渐变ID
    const gradientId = `edge-gradient-${id}`;

    // 根据关系类型设置线条样式
    if (data?.relationshipType === 'sibling') {
      return {
        ...baseStyle,
        stroke: `url(#${gradientId})`, // 使用渐变
        strokeDasharray: '5,5',
        strokeOpacity: isHovered ? 0.8 : 0.6,
        strokeWidth: isHovered ? 2 : 1.5,
        sourceColor: sourceColor.color,
        targetColor: targetColor.color,
        sourceRgb: sourceColor.rgb,
        targetRgb: targetColor.rgb,
        gradientId,
      };
    } else {
      return {
        ...baseStyle,
        stroke: `url(#${gradientId})`, // 使用渐变
        strokeWidth: isHovered ? 3 : 2.5,
        strokeOpacity: isHovered ? 1 : 0.8,
        sourceColor: sourceColor.color,
        targetColor: targetColor.color,
        sourceRgb: sourceColor.rgb,
        targetRgb: targetColor.rgb,
        gradientId,
      };
    }
  };

  // 如果路径尚未计算，不渲染
  if (!pathData) return null;

  // 获取边的样式
  const edgeStyles = getEdgeStyles();
  const isSelected = selected || style?.strokeWidth === 5; // 使用props中的selected属性或检查strokeWidth

  return (
    <>
      {/* 右键菜单 - 使用React Portal渲染到body上，避免SVG环境的限制 */}
      {showContextMenu && ReactDOM.createPortal(
        <div
          className="edge-context-menu"
          style={{
            position: 'fixed',
            left: contextMenuPosition.x,
            top: contextMenuPosition.y,
            zIndex: 9999,
            pointerEvents: 'auto',
            transform: 'scale(0.95)',
            opacity: 0,
            animation: 'menuAppear 150ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
            width: isTouchDevice.current ? 150 : 120,
          }}
        >
          <div
            style={{
              background: 'white',
              borderRadius: '4px',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              overflow: 'hidden',
            }}
          >
            <div
              onClick={handleDeleteEdge}
              style={{
                padding: isTouchDevice.current ? '12px 16px' : '8px 12px',
                cursor: 'pointer',
                fontSize: isTouchDevice.current ? '16px' : '14px',
                display: 'flex',
                alignItems: 'center',
                color: '#e53e3e',
                transition: 'background-color 0.15s ease',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#fef2f2';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                style={{ marginRight: '8px' }}
              >
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
              删除连接
            </div>
          </div>
        </div>,
        document.body
      )}

      <g>
      {/* 定义渐变 */}
      <defs>
        <linearGradient id={edgeStyles.gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor={edgeStyles.sourceColor} />
          <stop offset="100%" stopColor={edgeStyles.targetColor} />
        </linearGradient>

        {/* 添加发光滤镜 */}
        <filter id={`glow-${id}`} x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>

      {/* 选中/悬停时的光晕效果 */}
      {(isSelected || isHovered) && (
        <path
          d={pathData}
          className="edge-glow"
          style={{
            stroke: `url(#${edgeStyles.gradientId})`,
            strokeWidth: edgeStyles.strokeWidth + 4,
            strokeOpacity: 0.2,
            fill: 'none',
            filter: `url(#glow-${id})`,
            transition: 'all 0.3s ease-in-out',
            pointerEvents: 'none', // 确保光晕效果不会干扰点击事件
          }}
        />
      )}

      {/* 主要连线路径 - 添加引用以便控制动画 */}
      <path
        ref={pathRef}
        id={id}
        className={`react-flow__edge-path ${isSelected ? 'selected-edge' : ''} ${isHovered ? 'hovered-edge' : ''}`}
        d={pathData}
        style={{
          ...edgeStyles,
          ...style,
          // 选中状态增强
          strokeWidth: isSelected ? edgeStyles.strokeWidth + 1 : edgeStyles.strokeWidth,
          strokeOpacity: isSelected ? 1 : edgeStyles.strokeOpacity,
          filter: isSelected
            ? `drop-shadow(0 0 3px rgba(${edgeStyles.sourceRgb}, 0.5))`
            : edgeStyles.filter,
          transition: 'all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1)',
        }}
        markerEnd={`url(#arrow-${id})`}
      >
        {/* 电影风格的动态连接效果 - 路径生长动画 */}
        <animate
          ref={animationRef}
          attributeName="stroke-dashoffset"
          from={pathRef.current?.getTotalLength() || 1000}
          to="0"
          dur="0.8s"
          begin={data?.isNew ? "0s" : "indefinite"} // 仅对新创建的连接自动播放
          fill="freeze"
          calcMode="spline"
          keySplines="0.4 0 0.2 1" // 缓动函数，模拟电影效果
          restart="whenNotActive"
          style={{
            pointerEvents: 'none', // 确保动画元素不会干扰点击事件
          }}
        />
      </path>

      {/* 交互区域，与线条宽度相同 */}
      <path
        d={pathData}
        strokeWidth={edgeStyles.strokeWidth + 2} // 只比实际线条宽度稍大一点
        stroke="rgba(255, 0, 0, 0.01)" // 添加一个几乎不可见的颜色，确保点击区域可见
        fill="none"
        className="edge-interaction-area" // 添加类名，便于CSS选择器控制
        onClick={(e) => {
          // 阻止事件冒泡，避免触发画布的点击事件
          e.preventDefault();
          e.stopPropagation();

          console.log('Edge interaction area clicked:', id); // 添加日志，便于调试

          // 如果当前不是拖拽状态，则处理点击事件
          if (!document.body.classList.contains('dragging-active') &&
              !document.body.classList.contains('dragging-cooldown')) {
            // 通知ReactFlow选中此边
            const edgePath = document.getElementById(id);
            if (edgePath) {
              // 移除所有其他边的选中效果
              document.querySelectorAll('.selected-edge').forEach(el => {
                if (el.id !== id) {
                  el.classList.remove('selected-edge');
                }
              });
              // 添加当前边的选中效果
              edgePath.classList.add('selected-edge');
              // 触发动画效果
              setIsAnimating(true);
            }
          }
        }}
        onContextMenu={(e) => {
          // 阻止事件冒泡，避免触发画布的右键菜单
          e.preventDefault();
          e.stopPropagation();

          console.log('Edge interaction area right clicked:', id); // 添加日志，便于调试

          // 如果当前不是拖拽状态，则处理右键点击事件
          if (!document.body.classList.contains('dragging-active') &&
              !document.body.classList.contains('dragging-cooldown')) {
            // 设置上下文菜单位置
            setContextMenuPosition({ x: e.clientX, y: e.clientY });
            setShowContextMenu(true);

            // 确保连线被选中
            const edgePath = document.getElementById(id);
            if (edgePath) {
              // 移除所有其他边的选中效果
              document.querySelectorAll('.selected-edge').forEach(el => {
                if (el.id !== id) {
                  el.classList.remove('selected-edge');
                }
              });
              // 添加当前边的选中效果
              edgePath.classList.add('selected-edge');
            }

            // 触发动画效果
            setIsAnimating(true);
          }
        }}
        onMouseEnter={handleMouseEnter} // 直接在透明区域处理悬停，减少事件冒泡
        onMouseLeave={handleMouseLeave} // 直接在透明区域处理离开，减少事件冒泡
        onTouchStart={handleTouchStart} // 添加触摸开始事件，用于长按触发右键菜单
        onTouchEnd={handleTouchEnd} // 添加触摸结束事件，清除长按计时器
        onTouchMove={handleTouchMove} // 添加触摸移动事件，取消长按
        style={{
          cursor: 'pointer', // 添加指针样式，提示可交互
          pointerEvents: 'all', // 修改为'all'，确保可以捕获所有点击事件
          zIndex: 1000, // 增加z-index，确保在其他元素之上
          position: 'absolute', // 修改为绝对定位，确保z-index生效
        }}
      />

      {/* 添加箭头标记 */}
      <defs>
        <marker
          id={`arrow-${id}`}
          viewBox="0 0 10 10"
          refX="5"
          refY="5"
          markerWidth={isSelected || isHovered ? 7 : 6}
          markerHeight={isSelected || isHovered ? 7 : 6}
          orient="auto-start-reverse"
        >
          <path
            d="M 0 0 L 10 5 L 0 10 z"
            fill={edgeStyles.targetColor} // 使用目标节点颜色
            style={{
              filter: isSelected || isHovered ? `drop-shadow(0 0 1px rgba(${edgeStyles.targetRgb}, 0.5))` : 'none',
              transition: 'all 0.2s ease',
              pointerEvents: 'none', // 确保箭头不会干扰点击事件
            }}
          />
        </marker>
      </defs>

      {/* 添加连线动画效果 - 始终渲染但控制可见性，避免频繁的条件渲染 */}
      <g style={{
        opacity: (data?.relationshipType === 'parent-child' || isAnimating || isSelected) ? 1 : 0,
        transition: 'opacity 0.3s ease',
        // 完全禁用所有指针事件，确保不会干扰连线的点击
        pointerEvents: 'none'
      }}>
        {/* 主要流动粒子 */}
        <circle
          cx="0"
          cy="0"
          r={isHovered || isSelected ? 4 : 3}
          fill={edgeStyles.targetColor}
          style={{
            opacity: isHovered || isSelected ? 0.9 : 0.7,
            transition: 'all 0.3s ease',
            pointerEvents: 'none', // 确保粒子不会干扰点击事件
          }}
        >
          <animateMotion
            dur={isHovered || isSelected ? "2s" : "3s"}
            repeatCount="indefinite"
            path={pathData}
            rotate="auto"
          >
            {/* 使用条件样式而非条件渲染 */}
            <animate
              attributeName="r"
              values="3;4;3"
              dur="1s"
              repeatCount="indefinite"
              // 当不悬停时暂停动画，而非移除元素
              begin={isHovered ? "0s" : "indefinite"}
            />
          </animateMotion>
        </circle>

        {/* 次要流动粒子 - 使用样式控制可见性 */}
        <circle
          cx="0"
          cy="0"
          r="2.5"
          fill={edgeStyles.sourceColor}
          style={{
            opacity: isHovered || isSelected ? 0.7 : 0,
            transition: 'opacity 0.3s ease',
            pointerEvents: 'none', // 确保粒子不会干扰点击事件
          }}
        >
          <animateMotion
            dur="3s"
            repeatCount="indefinite"
            path={pathData}
            rotate="auto"
            keyPoints="0.3;1" // 从30%位置开始
            keyTimes="0;1"
          />
        </circle>
      </g>
    </g>
    </>
  );
};

// 使用React.memo优化渲染性能
export default memo(OutlineCanvasEdge);
