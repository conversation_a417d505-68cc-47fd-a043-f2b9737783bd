/* 节点编辑对话框样式 */

/* 全局高度标准变量 */
:root {
  --modal-max-height: 75vh;
  --modal-max-height-mobile: 85vh;
  --canvas-max-height: 75vh;
  --canvas-max-height-mobile: 80vh;
}

@media (max-width: 768px) {
  :root {
    --modal-max-height: var(--modal-max-height-mobile);
    --canvas-max-height: var(--canvas-max-height-mobile);
  }
}

/* 弹窗容器样式 */
.node-edit-dialog {
  max-height: var(--modal-max-height);
  max-width: 1200px !important;
  width: 90vw !important;
}

/* 滚动区域优化 */
.node-edit-dialog .flex-1.overflow-y-auto {
  /* 滚动阴影提示 */
  background: 
    linear-gradient(white 30%, rgba(255,255,255,0)),
    linear-gradient(rgba(255,255,255,0), white 70%) 0 100%,
    radial-gradient(50% 0, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0)),
    radial-gradient(50% 100%, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0)) 0 100%;
  background-repeat: no-repeat;
  background-color: transparent;
  background-size: 100% 40px, 100% 40px, 100% 14px, 100% 14px;
  background-attachment: local, local, scroll, scroll;
}

/* 滚动条美化 */
.node-edit-dialog .flex-1.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.node-edit-dialog .flex-1.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.node-edit-dialog .flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #667eea, #764ba2);
  border-radius: 3px;
}

.node-edit-dialog .flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #5a6fd8, #6a4190);
}

/* 滚动指示器 */
.scroll-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(102, 126, 234, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.node-edit-dialog .flex-1.overflow-y-auto:hover .scroll-indicator {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-edit-dialog {
    max-width: 95vw !important;
    width: 95vw !important;
    margin: 0 auto;
  }
  
  .node-edit-dialog .p-4 {
    padding: 1rem;
  }
  
  .node-edit-dialog .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* 移动端优化按钮大小 */
  .node-edit-dialog button {
    min-height: 44px;
    touch-action: manipulation;
  }
  
  /* 移动端输入框优化 */
  .node-edit-dialog input,
  .node-edit-dialog textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

@media (max-width: 480px) {
  .node-edit-dialog {
    max-width: 100vw !important;
    width: 100vw !important;
    margin: 0;
    border-radius: 0;
  }
  
  .node-edit-dialog .p-4 {
    padding: 0.75rem;
  }
  
  .node-edit-dialog .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* 焦点管理 */
.node-edit-dialog input:focus,
.node-edit-dialog textarea:focus,
.node-edit-dialog select:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--outline-primary, #667eea);
  border-color: var(--outline-primary, #667eea);
}

/* 动画优化 */
.node-edit-dialog {
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 加载状态 */
.node-edit-dialog.loading {
  pointer-events: none;
}

.node-edit-dialog.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 错误状态 */
.node-edit-dialog .error-message {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* 成功状态 */
.node-edit-dialog .success-message {
  color: #059669;
  background: #ecfdf5;
  border: 1px solid #a7f3d0;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* 智能高度调整 */
.dynamic-height {
  min-height: 200px;
  max-height: var(--modal-max-height);
  height: auto;
}

/* 键盘导航优化 */
.node-edit-dialog [tabindex]:focus {
  outline: 2px solid var(--outline-primary, #667eea);
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .node-edit-dialog {
    border: 2px solid #000;
  }
  
  .node-edit-dialog .border-gray-200 {
    border-color: #000;
  }
  
  .node-edit-dialog .text-gray-700 {
    color: #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .node-edit-dialog {
    animation: none;
  }
  
  .node-edit-dialog * {
    transition: none !important;
  }
}
