/**
 * 配置服务
 * 用于管理应用程序的各种配置
 */

// AI配置接口
export interface AIConfig {
  temperature?: number;
  maxTokens?: number;
  model?: string;
  apiKey?: string;
  topP?: number;        // Top P参数 - 控制核心采样的概率质量 (0.0-1.0)
  topK?: number;        // Top K参数 - 控制候选词汇数量，范围通常为1-100
}

/**
 * 配置服务类
 */
class ConfigService {
  private aiConfig: AIConfig = {};

  /**
   * 获取AI配置
   * @returns AI配置对象
   */
  async getAIConfig(): Promise<AIConfig> {
    try {
      // 尝试从localStorage获取配置
      const storedConfig = localStorage.getItem('aiConfig');
      if (storedConfig) {
        const parsedConfig = JSON.parse(storedConfig);
        this.aiConfig = { ...this.aiConfig, ...parsedConfig };
      }

      // 尝试从API设置组件获取配置
      try {
        const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');
        const settingsFactory = createSettingsFactory();
        const apiSettings = settingsFactory.createAPISettingsDialogComponent();

        // 获取当前提供商和模型
        const provider = apiSettings.getCurrentProvider();
        const model = apiSettings.getCurrentModel();

        // 尝试获取maxTokens，如果方法不存在则使用默认值
        let maxTokens = 40000;
        let topP = 1.0;
        let topK = 40;

        try {
          // 使用any类型绕过类型检查，因为接口中可能没有定义此方法
          const apiSettingsAny = apiSettings as any;
          if (typeof apiSettingsAny.getMaxTokens === 'function') {
            maxTokens = apiSettingsAny.getMaxTokens() || 40000;
          }
          if (typeof apiSettingsAny.getTopP === 'function') {
            topP = apiSettingsAny.getTopP() || 1.0;
          }
          if (typeof apiSettingsAny.getTopK === 'function') {
            topK = apiSettingsAny.getTopK() || 40;
          }
        } catch (error) {
          console.warn('无法获取AI参数，使用默认值', error);
        }

        // 更新配置
        this.aiConfig = {
          ...this.aiConfig,
          model,
          maxTokens,
          temperature: 0.7, // 默认温度
          topP: topP,       // 从API设置获取Top P值
          topK: topK,       // 从API设置获取Top K值
          apiKey: apiSettings.getAPIKey(provider)
        };
      } catch (apiError) {
        console.warn('无法从API设置获取配置', apiError);
      }

      // 尝试从环境变量获取配置（环境变量优先级高于其他设置）
      if (process.env.NEXT_PUBLIC_AI_MODEL) {
        this.aiConfig.model = process.env.NEXT_PUBLIC_AI_MODEL;
      }

      if (process.env.NEXT_PUBLIC_AI_TEMPERATURE) {
        this.aiConfig.temperature = parseFloat(process.env.NEXT_PUBLIC_AI_TEMPERATURE);
      }

      if (process.env.NEXT_PUBLIC_AI_MAX_TOKENS) {
        this.aiConfig.maxTokens = parseInt(process.env.NEXT_PUBLIC_AI_MAX_TOKENS);
      }

      if (process.env.NEXT_PUBLIC_AI_API_KEY) {
        this.aiConfig.apiKey = process.env.NEXT_PUBLIC_AI_API_KEY;
      }

      if (process.env.NEXT_PUBLIC_AI_TOP_P) {
        this.aiConfig.topP = parseFloat(process.env.NEXT_PUBLIC_AI_TOP_P);
      }

      if (process.env.NEXT_PUBLIC_AI_TOP_K) {
        this.aiConfig.topK = parseInt(process.env.NEXT_PUBLIC_AI_TOP_K);
      }

      return this.aiConfig;
    } catch (error) {
      console.error('获取AI配置失败', error);

      // 返回基本默认配置，避免返回空对象
      return {
        temperature: 0.7,
        maxTokens: 40000,
        model: 'gemini-2.5-pro-exp-03-25',
        topP: 1.0,         // 默认Top P值 - 不限制概率质量
        topK: 40           // 默认Top K值 - 限制候选词汇为40个
      };
    }
  }

  /**
   * 设置AI配置
   * @param config AI配置对象
   */
  async setAIConfig(config: Partial<AIConfig>): Promise<void> {
    try {
      // 更新配置
      this.aiConfig = { ...this.aiConfig, ...config };

      // 保存到localStorage
      localStorage.setItem('aiConfig', JSON.stringify(this.aiConfig));

      // 尝试同步更新API设置组件
      try {
        const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');
        const settingsFactory = createSettingsFactory();
        const apiSettings = settingsFactory.createAPISettingsDialogComponent();

        // 如果有模型设置，更新API设置组件
        if (config.model) {
          apiSettings.setCurrentModel(config.model);
        }

        // 尝试更新maxTokens
        if (config.maxTokens) {
          // 使用any类型绕过类型检查
          const apiSettingsAny = apiSettings as any;
          if (typeof apiSettingsAny.setMaxTokens === 'function') {
            apiSettingsAny.setMaxTokens(config.maxTokens);
          }
        }

        // 尝试更新topP
        if (config.topP !== undefined) {
          const apiSettingsAny = apiSettings as any;
          if (typeof apiSettingsAny.setTopP === 'function') {
            apiSettingsAny.setTopP(config.topP);
          }
        }

        // 尝试更新topK
        if (config.topK !== undefined) {
          const apiSettingsAny = apiSettings as any;
          if (typeof apiSettingsAny.setTopK === 'function') {
            apiSettingsAny.setTopK(config.topK);
          }
        }

        // 如果有API密钥，更新当前提供商的API密钥
        if (config.apiKey) {
          const provider = apiSettings.getCurrentProvider();
          apiSettings.setAPIKey(config.apiKey, provider);
        }

        console.log('已同步更新API设置组件');
      } catch (apiError) {
        console.warn('无法同步更新API设置组件', apiError);
      }
    } catch (error) {
      console.error('设置AI配置失败', error);
    }
  }
}

// 导出配置服务实例
export const configService = new ConfigService();
