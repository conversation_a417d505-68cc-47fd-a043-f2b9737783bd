"use client";

import React from 'react';
import { SegmentStructure, CoreMystery } from '@/factories/ai/services/types/ShortStoryTypes';

interface StructurePreviewPanelProps {
  coreMystery: CoreMystery | null;
  structurePreview: SegmentStructure[];
  isLoading: boolean;
  onConfirm: () => void;
  onRegenerate: () => void;
  onGenerateSegment?: (segmentIndex: number) => void;
  showManualControls?: boolean;
}

/**
 * 结构预览面板组件
 * 显示生成的"一半灵一半"的JSON结构预览
 */
export const StructurePreviewPanel: React.FC<StructurePreviewPanelProps> = ({
  coreMystery,
  structurePreview,
  isLoading,
  onConfirm,
  onRegenerate,
  onGenerateSegment,
  showManualControls = false
}) => {
  
  // 计算统计信息
  const totalSegments = structurePreview.length;
  const avgTensionLevel = totalSegments > 0 
    ? (structurePreview.reduce((sum, seg) => sum + seg.tensionLevel, 0) / totalSegments).toFixed(1)
    : '0';
  const avgInfoLevel = totalSegments > 0 
    ? (structurePreview.reduce((sum, seg) => sum + seg.informationLevel, 0) / totalSegments).toFixed(1)
    : '0';

  // 按阶段分组
  const getPhaseSegments = (phase: string) => {
    const phaseMap: Record<string, number[]> = {
      '铺垫期': [1, 2, 3, 4, 5],
      '挤压期': [6, 7, 8, 9, 10, 11, 12, 13, 14],
      '高潮期': [15, 16, 17, 18, 19],
      '结局期': [20]
    };
    
    return structurePreview.filter(seg => 
      phaseMap[phase]?.includes(seg.segmentNumber) || false
    );
  };

  return (
    <div className="flex-1 bg-gray-50 rounded-xl p-6 overflow-hidden flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-800">结构预览</h3>
          <p className="text-sm text-gray-600 mt-1">
            查看AI生成的"一半灵一半"分段结构，确认后开始创作内容
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-xs text-gray-500 text-right">
            <div>总段数: {totalSegments}</div>
            <div>平均紧张感: {avgTensionLevel}/10</div>
            <div>平均信息量: {avgInfoLevel}/10</div>
          </div>
        </div>
      </div>

      {/* 核心悬念展示 */}
      {coreMystery && (
        <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-amber-500">
          <h4 className="font-semibold text-gray-800 mb-2">📖 核心悬念</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium text-amber-600">标题:</span> {coreMystery.title}</div>
            <div><span className="font-medium text-amber-600">核心问题:</span> {coreMystery.coreQuestion}</div>
            <div><span className="font-medium text-green-600">透露的一半:</span> {coreMystery.revealedHalf}</div>
            <div><span className="font-medium text-red-600">隐藏的一半:</span> {coreMystery.hiddenHalf}</div>
          </div>
        </div>
      )}

      {/* 结构预览 */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500 mx-auto mb-4"></div>
              <p className="text-gray-600">正在生成结构...</p>
            </div>
          </div>
        ) : structurePreview.length > 0 ? (
          <div className="space-y-6">
            {/* 四节奏分组显示 */}
            {['铺垫期', '挤压期', '高潮期', '结局期'].map((phase, phaseIndex) => {
              const phaseSegments = getPhaseSegments(phase);
              if (phaseSegments.length === 0) return null;

              const phaseColors = [
                'border-blue-400 bg-blue-50',
                'border-orange-400 bg-orange-50', 
                'border-red-400 bg-red-50',
                'border-green-400 bg-green-50'
              ];

              return (
                <div key={phase} className={`border-l-4 ${phaseColors[phaseIndex]} p-4 rounded-r-lg`}>
                  <h5 className="font-semibold text-gray-800 mb-3 flex items-center">
                    <span className="mr-2">{['🌱', '⚡', '🔥', '🌟'][phaseIndex]}</span>
                    {phase} ({phaseSegments.length}段)
                  </h5>
                  
                  <div className="grid gap-3">
                    {phaseSegments.map((segment) => (
                      <div 
                        key={segment.segmentNumber}
                        className="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
                                第{segment.segmentNumber}段
                              </span>
                              <span className="text-xs text-gray-500">
                                {segment.purpose}
                              </span>
                            </div>
                            
                            <div className="text-sm text-gray-700 mb-2">
                              目的: {segment.purpose}
                            </div>

                            <div className="flex items-center space-x-4 text-xs">
                              <div className="flex items-center space-x-1">
                                <span className="text-gray-500">信息量:</span>
                                <div className="flex space-x-1">
                                  {Array.from({ length: 10 }, (_, i) => (
                                    <div
                                      key={i}
                                      className={`w-2 h-2 rounded-full ${
                                        i < segment.informationLevel
                                          ? 'bg-blue-400'
                                          : 'bg-gray-200'
                                      }`}
                                    />
                                  ))}
                                </div>
                                <span className="text-blue-600 font-medium">
                                  {segment.informationLevel}/10
                                </span>
                              </div>

                              <div className="flex items-center space-x-1">
                                <span className="text-gray-500">紧张感:</span>
                                <div className="flex space-x-1">
                                  {Array.from({ length: 10 }, (_, i) => (
                                    <div
                                      key={i}
                                      className={`w-2 h-2 rounded-full ${
                                        i < segment.tensionLevel
                                          ? 'bg-red-400'
                                          : 'bg-gray-200'
                                      }`}
                                    />
                                  ))}
                                </div>
                                <span className="text-red-600 font-medium">
                                  {segment.tensionLevel}/10
                                </span>
                              </div>
                            </div>

                            {/* 手动生成按钮 */}
                            {showManualControls && onGenerateSegment && (
                              <div className="mt-3 pt-2 border-t border-gray-100">
                                {segment.content ? (
                                  <div className="flex items-center justify-between">
                                    <span className="text-xs text-green-600 flex items-center">
                                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                      已生成内容
                                    </span>
                                    <button
                                      onClick={() => onGenerateSegment(segment.segmentNumber - 1)}
                                      className="px-2 py-1 text-xs text-amber-600 hover:text-amber-800 border border-amber-300 rounded hover:bg-amber-50 transition-colors"
                                    >
                                      重新生成
                                    </button>
                                  </div>
                                ) : (
                                  <button
                                    onClick={() => onGenerateSegment(segment.segmentNumber - 1)}
                                    className="w-full px-3 py-2 text-xs bg-amber-500 text-white rounded hover:bg-amber-600 transition-colors font-medium"
                                  >
                                    生成第{segment.segmentNumber}段内容
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-4">📋</div>
              <p>暂无结构预览</p>
              <p className="text-sm mt-1">请先生成结构</p>
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      {structurePreview.length > 0 && !isLoading && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              {showManualControls
                ? "🎯 手动控制模式：点击上方按钮逐段生成内容"
                : "💡 这是AI设计的「透露一半，逐渐补全」的结构框架"
              }
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onRegenerate}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                重新生成结构
              </button>
              {!showManualControls && (
                <button
                  onClick={onConfirm}
                  className="px-6 py-2 bg-amber-500 text-white text-sm rounded-lg hover:bg-amber-600 transition-colors font-medium"
                >
                  确认结构，手动创作
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
