"use client";

import React, { useState } from 'react';
import { OutlineManagementDialog } from './OutlineManagementDialog';

interface OutlineManagementButtonProps {
  bookId: string;
  selectedOutlineNodeIds?: string[];
  onOutlineNodesChange?: (nodeIds: string[]) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
}

/**
 * 独立大纲管理按钮组件
 * 提供大纲节点的树级选择和画布预览功能
 */
export const OutlineManagementButton: React.FC<OutlineManagementButtonProps> = ({
  bookId,
  selectedOutlineNodeIds = [],
  onOutlineNodesChange,
  variant = 'default',
  className = '',
  disabled = false
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // 计算选中节点数量
  const selectedCount = selectedOutlineNodeIds.length;

  // 处理节点选择变化
  const handleNodesChange = (nodeIds: string[]) => {
    if (onOutlineNodesChange) {
      onOutlineNodesChange(nodeIds);
    }
  };

  // 获取按钮样式
  const getButtonStyles = () => {
    const baseStyles = "relative inline-flex items-center justify-center transition-all duration-200 font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2";

    switch (variant) {
      case 'compact':
        return `${baseStyles} px-3 py-2 text-sm bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700`;
      case 'icon-only':
        return `${baseStyles} w-10 h-10 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700`;
      default:
        return `${baseStyles} px-4 py-2 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700 shadow-sm`;
    }
  };

  return (
    <>
      <button
        className={`${getButtonStyles()} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={() => !disabled && setIsDialogOpen(true)}
        disabled={disabled}
        title="管理大纲节点"
      >
        {/* 大纲树状图标 */}
        <svg
          className={`${variant === 'icon-only' ? 'w-5 h-5' : 'w-4 h-4'} ${variant !== 'icon-only' ? 'mr-2' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
          />
        </svg>

        {/* 文本（非图标模式） */}
        {variant !== 'icon-only' && (
          <span>
            {variant === 'compact' ? '大纲' : '大纲管理'}
          </span>
        )}

        {/* 数量徽章 */}
        {selectedCount > 0 && (
          <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[20px] h-5">
            {selectedCount > 99 ? '99+' : selectedCount}
          </span>
        )}
      </button>

      {/* 大纲管理对话框 */}
      <OutlineManagementDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        bookId={bookId}
        selectedOutlineNodeIds={selectedOutlineNodeIds}
        onOutlineNodesChange={handleNodesChange}
      />
    </>
  );
};

export default OutlineManagementButton;
