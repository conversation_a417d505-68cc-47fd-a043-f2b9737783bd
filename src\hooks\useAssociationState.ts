import { useState, useEffect, useCallback } from 'react';

/**
 * 关联状态接口
 */
export interface AssociationState {
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
}

/**
 * 关联项目接口
 */
export interface AssociationItem {
  id: string;
  title: string;
  type: 'chapter' | 'character' | 'terminology' | 'worldBuilding';
  description?: string;
  metadata?: any;
}

/**
 * localStorage键名生成器
 */
const STORAGE_KEYS = {
  CHAPTERS: (bookId: string) => `ai-assistant-selected-chapters-${bookId}`,
  CHARACTERS: (bookId: string) => `ai-assistant-selected-characters-${bookId}`,
  TERMINOLOGIES: (bookId: string) => `ai-assistant-selected-terminologies-${bookId}`,
  WORLDBUILDINGS: (bookId: string) => `ai-assistant-selected-worldbuildings-${bookId}`,
  LAST_UPDATED: (bookId: string) => `ai-assistant-last-updated-${bookId}`,
};

/**
 * 从localStorage加载状态
 */
const loadFromStorage = (key: string): string[] => {
  try {
    const saved = localStorage.getItem(key);
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
    return [];
  }
};

/**
 * 保存状态到localStorage
 */
const saveToStorage = (key: string, value: string[]): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

/**
 * 关联状态管理Hook
 * 参考AI写作功能的localStorage持久化机制
 */
export const useAssociationState = (bookId: string) => {
  // 章节选择状态
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(() => 
    loadFromStorage(STORAGE_KEYS.CHAPTERS(bookId))
  );

  // 人物选择状态
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>(() => 
    loadFromStorage(STORAGE_KEYS.CHARACTERS(bookId))
  );

  // 术语选择状态
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>(() => 
    loadFromStorage(STORAGE_KEYS.TERMINOLOGIES(bookId))
  );

  // 世界观选择状态
  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>(() => 
    loadFromStorage(STORAGE_KEYS.WORLDBUILDINGS(bookId))
  );

  // 自动保存章节选择到localStorage
  useEffect(() => {
    if (bookId) {
      saveToStorage(STORAGE_KEYS.CHAPTERS(bookId), selectedChapterIds);
      saveToStorage(STORAGE_KEYS.LAST_UPDATED(bookId), [new Date().toISOString()]);
    }
  }, [selectedChapterIds, bookId]);

  // 自动保存人物选择到localStorage
  useEffect(() => {
    if (bookId) {
      saveToStorage(STORAGE_KEYS.CHARACTERS(bookId), selectedCharacterIds);
      saveToStorage(STORAGE_KEYS.LAST_UPDATED(bookId), [new Date().toISOString()]);
    }
  }, [selectedCharacterIds, bookId]);

  // 自动保存术语选择到localStorage
  useEffect(() => {
    if (bookId) {
      saveToStorage(STORAGE_KEYS.TERMINOLOGIES(bookId), selectedTerminologyIds);
      saveToStorage(STORAGE_KEYS.LAST_UPDATED(bookId), [new Date().toISOString()]);
    }
  }, [selectedTerminologyIds, bookId]);

  // 自动保存世界观选择到localStorage
  useEffect(() => {
    if (bookId) {
      saveToStorage(STORAGE_KEYS.WORLDBUILDINGS(bookId), selectedWorldBuildingIds);
      saveToStorage(STORAGE_KEYS.LAST_UPDATED(bookId), [new Date().toISOString()]);
    }
  }, [selectedWorldBuildingIds, bookId]);

  // 添加单个关联项
  const addAssociation = useCallback((item: AssociationItem) => {
    switch (item.type) {
      case 'chapter':
        setSelectedChapterIds(prev => 
          prev.includes(item.id) ? prev : [...prev, item.id]
        );
        break;
      case 'character':
        setSelectedCharacterIds(prev => 
          prev.includes(item.id) ? prev : [...prev, item.id]
        );
        break;
      case 'terminology':
        setSelectedTerminologyIds(prev => 
          prev.includes(item.id) ? prev : [...prev, item.id]
        );
        break;
      case 'worldBuilding':
        setSelectedWorldBuildingIds(prev => 
          prev.includes(item.id) ? prev : [...prev, item.id]
        );
        break;
    }
  }, []);

  // 移除单个关联项
  const removeAssociation = useCallback((type: AssociationItem['type'], id: string) => {
    switch (type) {
      case 'chapter':
        setSelectedChapterIds(prev => prev.filter(itemId => itemId !== id));
        break;
      case 'character':
        setSelectedCharacterIds(prev => prev.filter(itemId => itemId !== id));
        break;
      case 'terminology':
        setSelectedTerminologyIds(prev => prev.filter(itemId => itemId !== id));
        break;
      case 'worldBuilding':
        setSelectedWorldBuildingIds(prev => prev.filter(itemId => itemId !== id));
        break;
    }
  }, []);

  // 批量添加关联项
  const addMultipleAssociations = useCallback((items: AssociationItem[]) => {
    items.forEach(item => addAssociation(item));
  }, [addAssociation]);

  // 范围选择章节（参考AI书籍分析的实现）
  const selectChapterRange = useCallback((startOrder: number, endOrder: number, chapters: any[]) => {
    const chaptersInRange = chapters
      .filter(chapter => chapter.order >= startOrder && chapter.order <= endOrder)
      .map(chapter => chapter.id);
    
    setSelectedChapterIds(prev => [...new Set([...prev, ...chaptersInRange])]);
  }, []);

  // 清空所有关联
  const clearAllAssociations = useCallback(() => {
    setSelectedChapterIds([]);
    setSelectedCharacterIds([]);
    setSelectedTerminologyIds([]);
    setSelectedWorldBuildingIds([]);
  }, []);

  // 清空特定类型的关联
  const clearAssociationType = useCallback((type: AssociationItem['type']) => {
    switch (type) {
      case 'chapter':
        setSelectedChapterIds([]);
        break;
      case 'character':
        setSelectedCharacterIds([]);
        break;
      case 'terminology':
        setSelectedTerminologyIds([]);
        break;
      case 'worldBuilding':
        setSelectedWorldBuildingIds([]);
        break;
    }
  }, []);

  // 获取关联状态统计
  const getAssociationStats = useCallback(() => {
    return {
      chapterCount: selectedChapterIds.length,
      characterCount: selectedCharacterIds.length,
      terminologyCount: selectedTerminologyIds.length,
      worldBuildingCount: selectedWorldBuildingIds.length,
      totalCount: selectedChapterIds.length + selectedCharacterIds.length + 
                  selectedTerminologyIds.length + selectedWorldBuildingIds.length,
    };
  }, [selectedChapterIds, selectedCharacterIds, selectedTerminologyIds, selectedWorldBuildingIds]);

  // 检查是否有关联内容
  const hasAssociations = useCallback(() => {
    return getAssociationStats().totalCount > 0;
  }, [getAssociationStats]);

  // 获取完整的关联状态
  const getAssociationState = useCallback((): AssociationState => {
    return {
      selectedChapterIds,
      selectedCharacterIds,
      selectedTerminologyIds,
      selectedWorldBuildingIds,
    };
  }, [selectedChapterIds, selectedCharacterIds, selectedTerminologyIds, selectedWorldBuildingIds]);

  return {
    // 状态
    selectedChapterIds,
    selectedCharacterIds,
    selectedTerminologyIds,
    selectedWorldBuildingIds,
    
    // 状态设置器
    setSelectedChapterIds,
    setSelectedCharacterIds,
    setSelectedTerminologyIds,
    setSelectedWorldBuildingIds,
    
    // 操作方法
    addAssociation,
    removeAssociation,
    addMultipleAssociations,
    selectChapterRange,
    clearAllAssociations,
    clearAssociationType,
    
    // 查询方法
    getAssociationStats,
    hasAssociations,
    getAssociationState,
  };
};
