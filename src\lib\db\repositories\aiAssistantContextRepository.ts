import { db, AIAssistantContext, AIAssistantTemplate, AIAssistantContextType } from '../dexie';

/**
 * AI助手关联上下文数据访问层
 * 提供AI助手关联功能的数据操作接口
 */
export class AIAssistantContextRepository {

  /**
   * 获取指定书籍的所有活跃关联上下文
   * @param bookId 书籍ID
   * @returns 活跃的关联上下文列表
   */
  async getActiveContexts(bookId: string): Promise<AIAssistantContext[]> {
    return await db.aiAssistantContexts
      .where({ bookId, isActive: true })
      .orderBy('priority')
      .toArray();
  }

  /**
   * 获取指定书籍的所有关联上下文（包括非活跃的）
   * @param bookId 书籍ID
   * @returns 所有关联上下文列表
   */
  async getAllContexts(bookId: string): Promise<AIAssistantContext[]> {
    return await db.aiAssistantContexts
      .where('bookId')
      .equals(bookId)
      .orderBy('lastUsedAt')
      .reverse()
      .toArray();
  }

  /**
   * 根据类型获取关联上下文
   * @param bookId 书籍ID
   * @param contextType 上下文类型
   * @returns 指定类型的关联上下文列表
   */
  async getContextsByType(bookId: string, contextType: AIAssistantContextType): Promise<AIAssistantContext[]> {
    return await db.aiAssistantContexts
      .where({ bookId, contextType })
      .orderBy('usageCount')
      .reverse()
      .toArray();
  }

  /**
   * 添加或更新关联上下文
   * @param context 关联上下文数据
   * @returns 保存后的关联上下文
   */
  async saveContext(context: Omit<AIAssistantContext, 'id' | 'createdAt' | 'updatedAt'>): Promise<AIAssistantContext> {
    const now = new Date();

    // 检查是否已存在相同的关联
    const existing = await db.aiAssistantContexts
      .where({ bookId: context.bookId, contextType: context.contextType, contextId: context.contextId })
      .first();

    if (existing) {
      // 更新现有记录
      const updated: AIAssistantContext = {
        ...existing,
        ...context,
        usageCount: existing.usageCount + 1,
        lastUsedAt: now,
        updatedAt: now
      };

      await db.aiAssistantContexts.put(updated);
      return updated;
    } else {
      // 创建新记录
      const newContext: AIAssistantContext = {
        ...context,
        id: crypto.randomUUID(),
        usageCount: 1,
        lastUsedAt: now,
        createdAt: now,
        updatedAt: now
      };

      await db.aiAssistantContexts.add(newContext);
      return newContext;
    }
  }

  /**
   * 激活关联上下文
   * @param contextId 上下文ID
   */
  async activateContext(contextId: string): Promise<void> {
    await db.aiAssistantContexts.update(contextId, {
      isActive: true,
      lastUsedAt: new Date(),
      updatedAt: new Date()
    });
  }

  /**
   * 停用关联上下文
   * @param contextId 上下文ID
   */
  async deactivateContext(contextId: string): Promise<void> {
    await db.aiAssistantContexts.update(contextId, {
      isActive: false,
      updatedAt: new Date()
    });
  }

  /**
   * 批量激活关联上下文
   * @param contextIds 上下文ID列表
   */
  async activateContexts(contextIds: string[]): Promise<void> {
    const now = new Date();
    await db.transaction('rw', db.aiAssistantContexts, async () => {
      for (const contextId of contextIds) {
        await db.aiAssistantContexts.update(contextId, {
          isActive: true,
          lastUsedAt: now,
          updatedAt: now
        });
      }
    });
  }

  /**
   * 批量停用关联上下文
   * @param contextIds 上下文ID列表
   */
  async deactivateContexts(contextIds: string[]): Promise<void> {
    const now = new Date();
    await db.transaction('rw', db.aiAssistantContexts, async () => {
      for (const contextId of contextIds) {
        await db.aiAssistantContexts.update(contextId, {
          isActive: false,
          updatedAt: now
        });
      }
    });
  }

  /**
   * 删除关联上下文
   * @param contextId 上下文ID
   */
  async deleteContext(contextId: string): Promise<void> {
    await db.aiAssistantContexts.delete(contextId);
  }

  /**
   * 批量删除关联上下文
   * @param contextIds 上下文ID列表
   */
  async deleteContexts(contextIds: string[]): Promise<void> {
    await db.aiAssistantContexts.bulkDelete(contextIds);
  }

  /**
   * 清空指定书籍的所有关联上下文
   * @param bookId 书籍ID
   */
  async clearBookContexts(bookId: string): Promise<void> {
    await db.aiAssistantContexts.where('bookId').equals(bookId).delete();
  }

  /**
   * 更新上下文使用统计
   * @param contextId 上下文ID
   */
  async incrementUsage(contextId: string): Promise<void> {
    const context = await db.aiAssistantContexts.get(contextId);
    if (context) {
      await db.aiAssistantContexts.update(contextId, {
        usageCount: context.usageCount + 1,
        lastUsedAt: new Date(),
        updatedAt: new Date()
      });
    }
  }

  /**
   * 获取最常用的关联上下文（用于智能推荐）
   * @param bookId 书籍ID
   * @param limit 返回数量限制
   * @returns 最常用的关联上下文列表
   */
  async getMostUsedContexts(bookId: string, limit: number = 10): Promise<AIAssistantContext[]> {
    const contexts = await db.aiAssistantContexts
      .where('bookId')
      .equals(bookId)
      .toArray();

    // 在内存中排序，按使用次数降序
    return contexts
      .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
      .slice(0, limit);
  }

  /**
   * 获取最近使用的关联上下文
   * @param bookId 书籍ID
   * @param limit 返回数量限制
   * @returns 最近使用的关联上下文列表
   */
  async getRecentlyUsedContexts(bookId: string, limit: number = 10): Promise<AIAssistantContext[]> {
    const contexts = await db.aiAssistantContexts
      .where('bookId')
      .equals(bookId)
      .toArray();

    // 在内存中排序，因为Dexie的orderBy在where之后可能有问题
    return contexts
      .filter(context => context.lastUsedAt) // 只包含有lastUsedAt的记录
      .sort((a, b) => {
        const aTime = a.lastUsedAt ? new Date(a.lastUsedAt).getTime() : 0;
        const bTime = b.lastUsedAt ? new Date(b.lastUsedAt).getTime() : 0;
        return bTime - aTime; // 降序排列（最新的在前）
      })
      .slice(0, limit);
  }

  /**
   * 搜索关联上下文
   * @param bookId 书籍ID
   * @param query 搜索关键词
   * @param contextType 可选的上下文类型过滤
   * @returns 匹配的关联上下文列表
   */
  async searchContexts(
    bookId: string,
    query: string,
    contextType?: AIAssistantContextType
  ): Promise<AIAssistantContext[]> {
    let collection = db.aiAssistantContexts.where('bookId').equals(bookId);

    if (contextType) {
      collection = collection.and(item => item.contextType === contextType);
    }

    const results = await collection.toArray();

    // 客户端过滤（因为Dexie不支持复杂的文本搜索）
    const lowerQuery = query.toLowerCase();
    return results.filter(context =>
      context.contextTitle.toLowerCase().includes(lowerQuery)
    ).sort((a, b) => {
      // 优先显示标题开头匹配的
      const aStartsWith = a.contextTitle.toLowerCase().startsWith(lowerQuery);
      const bStartsWith = b.contextTitle.toLowerCase().startsWith(lowerQuery);

      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;

      // 然后按使用频率排序
      return b.usageCount - a.usageCount;
    });
  }
}

// 导出单例实例
export const aiAssistantContextRepository = new AIAssistantContextRepository();
