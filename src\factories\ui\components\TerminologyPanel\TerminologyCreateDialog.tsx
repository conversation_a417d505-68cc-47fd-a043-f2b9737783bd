"use client";

import React, { useState, useEffect } from 'react';
import { Terminology, PromptTemplate, PromptCategory } from '@/lib/db/dexie';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';
import { TerminologyPrompts } from '@/utils/ai/prompts/TerminologyPrompts';
import { createChapterSegmenter } from '@/utils/ai/ChapterSegmenter';

interface TerminologyCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (terminology: Partial<Terminology>) => Promise<void>;
  bookId: string;
  chapters?: any[]; // 章节列表
}

export interface TerminologyCreateOptions {
  category?: string;
  description?: string;
  importance?: string;
  count?: number;
  selectedChapterIds: string[]; // 选中的章节ID
}

/**
 * 术语创建对话框组件
 */
export const TerminologyCreateDialog: React.FC<TerminologyCreateDialogProps> = ({
  isOpen,
  onClose,
  onCreate,
  bookId,
  chapters = []
}) => {
  // 创建选项状态
  const [options, setOptions] = useState<TerminologyCreateOptions>({
    category: '',
    description: '',
    importance: '3',
    count: 5,
    selectedChapterIds: []
  });

  // 关联术语
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>([]);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 错误信息
  const [error, setError] = useState<string | null>(null);

  // AI相关状态
  const [aiSender] = useState(() => new DefaultAISenderComponent());
  const [apiSettings] = useState(() => {
    const settingsFactory = createSettingsFactory();
    return settingsFactory.createAPISettingsDialogComponent();
  });
  // 移除未使用的notification变量

  // 创建结果
  const [createdTerminologies, setCreatedTerminologies] = useState<any[]>([]);

  // 流式响应状态
  const [streamResponse, setStreamResponse] = useState('');

  // 自定义提示词状态
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isPromptManagerOpen, setIsPromptManagerOpen] = useState(false);

  // 当对话框打开时，重置状态并加载术语
  useEffect(() => {
    if (isOpen) {
      setStreamResponse('');
      setCreatedTerminologies([]);
      setError(null);
      loadTerminologies();
    }
  }, [isOpen]);

  // 加载术语
  const loadTerminologies = async () => {
    try {
      const { db } = await import('@/lib/db/dexie');
      const terminologiesData = await db.terminology.where('bookId').equals(bookId).toArray();
      setTerminologies(terminologiesData);
    } catch (err) {
      console.error('加载术语失败:', err);
    }
  };

  // 处理术语选择
  const handleTerminologySelection = (terminologyId: string) => {
    setSelectedTerminologyIds(prev => {
      if (prev.includes(terminologyId)) {
        return prev.filter(id => id !== terminologyId);
      } else {
        return [...prev, terminologyId];
      }
    });
  };

  // 处理提示词模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    setCustomPrompt(template.content);
    setIsPromptManagerOpen(false);
  };

  // 处理选项变更
  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setOptions(prev => ({ ...prev, [name]: checked }));
    } else {
      setOptions(prev => ({ ...prev, [name]: value }));
    }
  };

  // 流式响应回调
  const onStreamChunk = (chunk: string) => {
    setStreamResponse(prevResponse => {
      const updatedResponse = prevResponse + chunk;

      try {
        // 尝试解析JSON
        const jsonMatch = updatedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonStr = jsonMatch[0];
          const data = JSON.parse(jsonStr);

          // 如果解析成功，更新创建的术语
          if (data && typeof data === 'object') {
            const terminologies = Object.entries(data).map(([name, info]: [string, any]) => ({
              name,
              ...(info.newInfo || info)
            }));

            setCreatedTerminologies(terminologies);
          }
        }
      } catch (e) {
        // 解析失败，继续等待更多数据
        console.log('JSON解析失败，继续等待更多数据');
      }

      return updatedResponse;
    });
  };

  // 处理创建
  const handleCreate = async () => {
    setIsLoading(true);
    setError(null);
    setStreamResponse('');
    setCreatedTerminologies([]);

    try {
      // 获取API设置
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      // 发送请求
      setStreamResponse(prev => prev + `开始创建术语...\n`);

      // 构建提示词
      const messageBuilder = new MessageBuilder();

      // 系统消息 - 使用默认提示词
      messageBuilder.addSystemMessage(TerminologyPrompts.createSystemRolePrompt);

      // 添加助手确认消息
      messageBuilder.addAssistantMessage(`我将根据提供的信息，创建符合要求的术语。`);

      // 如果有关联术语，添加到消息中（每个术语一条消息）
      if (selectedTerminologyIds.length > 0) {
        // 先添加一条说明消息
        messageBuilder.addAssistantMessage(`以下是已有的关联术语，我会在创建新术语时考虑这些术语的关联性：`);

        // 为每个关联术语添加单独的消息
        const relatedTerms = terminologies.filter(term => selectedTerminologyIds.includes(term.id || ''));
        for (const term of relatedTerms) {
          messageBuilder.addAssistantMessage(`术语: ${term.name}\n描述: ${term.description || '无描述'}`);
        }
      }

      // 用户消息 - 使用基础提示词模板
      const userPrompt = TerminologyPrompts.createBasePrompt(
        options.category || '',
        '', // 不再使用描述要求
        parseInt(options.importance || '3'),
        options.count || 5
      );
      messageBuilder.addUserMessage(userPrompt);

      // 添加一条说明消息，表示将要处理章节内容
      if (options.selectedChapterIds.length > 0) {
        messageBuilder.addUserMessage(`以下是相关章节的内容，请参考这些内容创建术语：`);

        // 创建章节分段器
        const segmenter = createChapterSegmenter({
          maxSegmentLength: 2000,
          minSegmentLength: 500,
          addSegmentNumber: true
        });

        // 准备选中的章节
        const selectedChapters = chapters.filter((chapter: any) =>
          options.selectedChapterIds.includes(chapter.id)
        );

        // 计算总段落数
        let totalSegmentsCount = 0;
        for (const chapter of selectedChapters) {
          const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content || ''}`;
          const segments = segmenter.segmentChapter(chapterText);
          totalSegmentsCount += segments.length;
        }

        // 为每个章节的每个段落添加单独的消息
        let globalSegmentIndex = 0;
        for (const chapter of selectedChapters) {
          const chapterText = `# ${chapter.title || `第${chapter.chapterNumber || '?'}章`}\n\n${chapter.content || ''}`;
          const segments = segmenter.segmentChapter(chapterText);

          for (let i = 0; i < segments.length; i++) {
            globalSegmentIndex++;
            const segment = segments[i];
            const segmentWordCount = segment.split(/\s+/).length;

            // 按照要求的格式构建消息：第x章，第几段，多少字，内容
            const segmentPrompt = `第${chapter.title || `第${chapter.chapterNumber || '?'}章`}，第${i+1}/${segments.length}段，${segmentWordCount}字，内容：\n\n${segment}`;
            messageBuilder.addUserMessage(segmentPrompt);
            messageBuilder.addAssistantMessage(`我已阅读并分析了"${chapter.title || `第${chapter.chapterNumber || '?'}章`}"章节的第${i+1}段内容（总进度：${globalSegmentIndex}/${totalSegmentsCount}）。`);
          }
        }

        // 添加一条总结消息
        messageBuilder.addAssistantMessage(`我已完成所有章节的分析。`);
      }

      // 添加输出格式指令
      messageBuilder.addUserMessage(`请以JSON格式输出创建结果，格式如下：
{
  "术语名称1": {
    "category": "术语类别",
    "description": "术语描述",
    "importance": 数字(1-5)
  },
  "术语名称2": {
    "category": "术语类别",
    "description": "术语描述",
    "importance": 数字(1-5)
  }
}

重要说明：
1. 创建的术语应该符合指定的类别和要求
2. 每个术语必须包含类别、描述和重要性
3. 重要性使用1-5的数字表示，5表示最重要
4. 直接返回JSON对象，不要使用Markdown代码块
5. 不要在JSON前后添加任何额外的文本`);

      // 如果有自定义提示词，添加到最后一条消息（确保是真正的最后一条）
      if (customPrompt) {
        messageBuilder.addUserMessage(`${customPrompt}`);
      }

      // 发送请求
      setStreamResponse(prev => prev + `\n正在发送请求创建术语...\n`);

      const result = await aiSender.sendRequest('', {
        messages: messageBuilder.build(),
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true,
        onStreamChunk: (chunk: string) => {
          // 为响应添加前缀
          onStreamChunk(`[创建术语] ${chunk}`);
        }
      });

      // 解析结果
      const jsonMatch = result.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        const data = JSON.parse(jsonStr);

        // 更新状态
        const termCount = Object.keys(data).length;
        setStreamResponse(prev => prev + `\n创建完成，共创建${termCount}个术语。\n`);

        // 将创建的术语添加到结果中
        const createdTerms = [];
        for (const [name, info] of Object.entries(data)) {
          const typedInfo = info as any;
          createdTerms.push({
            name,
            ...typedInfo,
            relatedTerminologyIds: selectedTerminologyIds
          });
        }

        setCreatedTerminologies(createdTerms);

        // 为每个术语单独显示确认消息
        for (const term of createdTerms) {
          setStreamResponse(prev => prev + `\n术语: ${term.name} (重要性: ${term.importance || '未知'})\n描述: ${term.description || '无描述'}\n`);
        }
      } else {
        setStreamResponse(prev => prev + `\n未能创建有效的术语信息。\n`);
      }
    } catch (err) {
      console.error('创建术语失败:', err);
      setError(err instanceof Error ? err.message : '创建术语时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理保存术语
  const handleSaveTerminologies = async () => {
    if (createdTerminologies.length === 0) {
      setError('没有可保存的术语');
      return;
    }

    setIsLoading(true);
    try {
      for (const term of createdTerminologies) {
        const newTerminology: Partial<Terminology> = {
          name: term.name,
          bookId,
          category: term.category,
          description: term.description,
          attributes: {
            importance: term.importance ? String(term.importance) : '3'
          },
          relatedTerminologyIds: selectedTerminologyIds,
          extractedFromChapterIds: options.selectedChapterIds,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // 直接调用onCreate，不需要再调用handleSaveTerminology
        // 因为在TerminologyPanelComponent.tsx中，onCreate已经包含了handleSaveTerminology
        await onCreate(newTerminology);
      }

      // 不显示通知，直接关闭对话框
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存术语时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl w-[700px] max-h-[80vh] overflow-hidden flex flex-col"
        style={{
          backgroundColor: 'var(--color-primary-bg)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* 对话框头部 */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-primary)' }}>AI创建术语</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-6 overflow-y-auto flex-1">
          <div className="space-y-6">
            {/* 创建选项 */}
            <div>
              <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>创建选项</h3>

              {/* 术语类别 */}
              <div className="mb-4">
                <label htmlFor="category" className="block text-gray-700 mb-1">术语类别</label>
                <input
                  type="text"
                  id="category"
                  name="category"
                  value={options.category}
                  onChange={handleOptionChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：物品、地点、组织等"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
                <p className="text-sm text-gray-500 mt-1">
                  指定要创建的术语类别，留空则由AI自动决定
                </p>
              </div>



              {/* 重要性 */}
              <div className="mb-4">
                <label htmlFor="importance" className="block text-gray-700 mb-1">术语重要性</label>
                <select
                  id="importance"
                  name="importance"
                  value={options.importance}
                  onChange={handleOptionChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                >
                  <option value="1">⭐ 次要术语 - 背景补充</option>
                  <option value="2">⭐⭐ 支持术语 - 丰富世界观</option>
                  <option value="3">⭐⭐⭐ 重要术语 - 影响情节</option>
                  <option value="4">⭐⭐⭐⭐ 核心术语 - 关键设定</option>
                  <option value="5">⭐⭐⭐⭐⭐ 关键术语 - 世界基石</option>
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  指定创建的术语的重要性级别
                </p>
              </div>

              {/* 创建数量 */}
              <div className="mb-4">
                <label htmlFor="count" className="block text-gray-700 mb-1">创建数量</label>
                <input
                  type="number"
                  id="count"
                  name="count"
                  value={options.count}
                  onChange={handleOptionChange}
                  min={1}
                  max={20}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(139, 69, 19, 0.2)'
                  }}
                />
                <p className="text-sm text-gray-500 mt-1">
                  指定要创建的术语数量（1-20）
                </p>
              </div>

              {/* 关联章节 */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-gray-700 font-medium">关联章节</label>

                  {/* 章节范围选择 */}
                  {chapters.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => {
                          // 选择所有章节
                          setOptions(prev => ({
                            ...prev,
                            selectedChapterIds: chapters.map((ch: any) => ch.id)
                          }));
                        }}
                        className="px-2 py-1 text-xs rounded-md transition-colors"
                        style={{
                          backgroundColor: 'var(--color-primary)',
                          color: 'white',
                          boxShadow: '0 1px 3px rgba(139, 69, 19, 0.2)'
                        }}
                      >
                        全选
                      </button>

                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-gray-600">从</span>
                        <input
                          type="number"
                          min={1}
                          max={chapters.length}
                          className="w-12 px-1 py-0.5 text-xs border border-gray-300 rounded"
                          style={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderColor: 'rgba(139, 69, 19, 0.2)'
                          }}
                          id="startChapter"
                          name="startChapter"
                          defaultValue={1}
                        />
                        <span className="text-xs text-gray-600">到</span>
                        <input
                          type="number"
                          min={1}
                          max={chapters.length}
                          className="w-12 px-1 py-0.5 text-xs border border-gray-300 rounded"
                          style={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderColor: 'rgba(139, 69, 19, 0.2)'
                          }}
                          id="endChapter"
                          name="endChapter"
                          defaultValue={chapters.length}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            // 获取输入的起始和结束章节
                            const startChapter = parseInt((document.getElementById('startChapter') as HTMLInputElement).value);
                            const endChapter = parseInt((document.getElementById('endChapter') as HTMLInputElement).value);

                            // 验证输入
                            if (isNaN(startChapter) || isNaN(endChapter) ||
                                startChapter < 1 || endChapter > chapters.length ||
                                startChapter > endChapter) {
                              alert('请输入有效的章节范围');
                              return;
                            }

                            // 选择范围内的章节
                            const sortedChapters = [...chapters].sort((a: any, b: any) => {
                              const aNum = a.chapterNumber || 0;
                              const bNum = b.chapterNumber || 0;
                              return aNum - bNum;
                            });

                            const selectedChapters = sortedChapters.slice(startChapter - 1, endChapter);

                            setOptions(prev => ({
                              ...prev,
                              selectedChapterIds: selectedChapters.map((ch: any) => ch.id)
                            }));
                          }}
                          className="px-2 py-1 text-xs rounded-md transition-colors"
                          style={{
                            backgroundColor: 'var(--color-secondary)',
                            color: 'white',
                            boxShadow: '0 1px 3px rgba(210, 180, 140, 0.2)'
                          }}
                        >
                          选择
                        </button>
                      </div>

                      <button
                        type="button"
                        onClick={() => {
                          // 清除选择
                          setOptions(prev => ({
                            ...prev,
                            selectedChapterIds: []
                          }));
                        }}
                        className="px-2 py-1 text-xs rounded-md transition-colors"
                        style={{
                          backgroundColor: 'var(--color-danger)',
                          color: 'white',
                          boxShadow: '0 1px 3px rgba(178, 34, 34, 0.2)'
                        }}
                      >
                        清除
                      </button>
                    </div>
                  )}
                </div>

                <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[200px] overflow-y-auto"
                  style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
                >
                  {chapters.length === 0 ? (
                    <div className="text-gray-500 text-center py-2">
                      没有可关联的章节
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {chapters.map((chapter: any) => (
                        <div key={chapter.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`chapter-${chapter.id}`}
                            checked={options.selectedChapterIds.includes(chapter.id)}
                            onChange={() => {
                              setOptions(prev => {
                                const newSelectedChapterIds = prev.selectedChapterIds.includes(chapter.id)
                                  ? prev.selectedChapterIds.filter(id => id !== chapter.id)
                                  : [...prev.selectedChapterIds, chapter.id];
                                return { ...prev, selectedChapterIds: newSelectedChapterIds };
                              });
                            }}
                            className="mr-2 h-4 w-4"
                          />
                          <label htmlFor={`chapter-${chapter.id}`} className="text-gray-700 truncate">
                            {chapter.title || `第${chapter.chapterNumber || '?'}章`}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  选择与术语相关的章节，可以帮助AI更好地理解术语的上下文
                </p>
              </div>

              {/* 关联术语 */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <label className="text-gray-700 font-medium">关联术语</label>
                </div>
                <div className="ml-0 mt-2 border border-gray-200 rounded-lg p-3 max-h-[200px] overflow-y-auto"
                  style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
                >
                  {terminologies.length === 0 ? (
                    <div className="text-gray-500 text-center py-2">
                      没有可关联的术语
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {terminologies.map(terminology => (
                        <div key={terminology.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`terminology-${terminology.id}`}
                            checked={selectedTerminologyIds.includes(terminology.id!)}
                            onChange={() => handleTerminologySelection(terminology.id!)}
                            className="mr-2 h-4 w-4"
                          />
                          <label htmlFor={`terminology-${terminology.id}`} className="text-gray-700 truncate">
                            {terminology.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* 自定义提示词 */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <label className="text-gray-700 font-medium">自定义提示词</label>
                  <button
                    type="button"
                    onClick={() => setIsPromptManagerOpen(true)}
                    className="px-3 py-1 text-sm rounded-md transition-colors"
                    style={{
                      backgroundColor: 'var(--color-primary)',
                      color: 'white',
                      boxShadow: '0 2px 4px rgba(139, 69, 19, 0.2)'
                    }}
                  >
                    选择模板
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {customPrompt ? '已选择自定义提示词' : '使用默认提示词'}
                </p>

                {/* 自定义提示词输入区域 */}
                <div className="mt-2">
                  <textarea
                    id="custom-prompt-input"
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入自定义提示词，或从模板中选择..."
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      borderColor: 'rgba(139, 69, 19, 0.2)'
                    }}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    在这里输入自定义提示词，用于指导AI如何创建术语
                  </p>
                </div>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}

            {/* 流式响应 */}
            {streamResponse && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--color-primary)' }}>AI响应</h3>
                <div
                  className="p-3 border rounded-lg whitespace-pre-wrap max-h-[200px] overflow-y-auto"
                  style={{
                    borderColor: 'rgba(139, 69, 19, 0.2)',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    fontFamily: 'monospace',
                    fontSize: '0.9rem'
                  }}
                >
                  {streamResponse}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 创建结果 */}
        {createdTerminologies.length > 0 && (
          <div className="p-4 border-t border-gray-200" style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}>
            <h3 className="text-lg font-medium mb-3" style={{ color: 'var(--color-primary)' }}>创建结果</h3>
            <div className="max-h-[200px] overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {createdTerminologies.map((term, index) => (
                  <div
                    key={index}
                    className="p-3 border rounded-lg"
                    style={{
                      borderColor: 'rgba(139, 69, 19, 0.2)',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium" style={{ color: 'var(--color-primary)' }}>{term.name}</h4>
                      <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                        {term.category}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{term.description}</p>
                    <div className="mt-1 text-xs text-yellow-500">
                      {'⭐'.repeat(parseInt(term.importance) || 0)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 对话框底部 */}
        <div className="p-4 border-t border-gray-200 flex justify-between"
          style={{ borderColor: 'rgba(139, 69, 19, 0.1)' }}
        >
          <div>
            {createdTerminologies.length > 0 && (
              <button
                className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
                style={{
                  backgroundColor: 'var(--color-success)',
                  color: 'white',
                  boxShadow: '0 2px 8px rgba(85, 107, 47, 0.2)'
                }}
                onClick={handleSaveTerminologies}
                disabled={isLoading}
              >
                保存所有术语
              </button>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
              style={{
                backgroundColor: 'rgba(210, 180, 140, 0.2)',
                color: 'var(--color-primary)',
                border: '1px solid var(--color-secondary)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
              }}
              onClick={onClose}
              disabled={isLoading}
            >
              {createdTerminologies.length > 0 ? '关闭' : '取消'}
            </button>

            {createdTerminologies.length === 0 && (
              <button
                className="px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center"
                style={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'white',
                  boxShadow: '0 2px 8px rgba(139, 69, 19, 0.2)'
                }}
                onClick={handleCreate}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    处理中...
                  </>
                ) : '开始创建'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 提示词模板管理器 */}
      <PromptTemplateManager
        isOpen={isPromptManagerOpen}
        onClose={() => setIsPromptManagerOpen(false)}
        category={PromptCategory.TERMINOLOGY} // 使用术语专用分类
        onSelectTemplate={handleSelectTemplate}
        initialPrompt={customPrompt}
      />
    </div>
  );
};
