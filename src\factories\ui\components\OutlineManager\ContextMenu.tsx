import React, { useEffect, useState } from 'react';
import './ContextMenu.css';

// 分隔线类型
interface DividerItem {
  type: 'divider';
}

// 菜单项类型
interface MenuItem {
  label: string;
  onClick?: () => void;
  disabled?: boolean;
  icon?: React.ReactNode;
  shortcut?: string; // 快捷键提示
  className?: string; // 自定义样式类
  children?: ContextMenuItem[]; // 子菜单项
}

// 联合类型，可以是菜单项或分隔线
type ContextMenuItem = MenuItem | DividerItem;

interface ContextMenuProps {
  x: number;
  y: number;
  items: ContextMenuItem[];
  onClose: () => void;
}

/**
 * 菜单项组件
 */
const MenuItem: React.FC<{
  item: MenuItem;
  onClose: () => void;
  parentX: number;
  parentY: number;
  index: number;
}> = ({ item, onClose, parentX, parentY, index }) => {
  const [showSubMenu, setShowSubMenu] = useState(false);

  // 处理点击事件
  const handleClick = () => {
    if (item.children) {
      setShowSubMenu(!showSubMenu);
    } else if (item.onClick) {
      item.onClick();
      onClose();
    }
  };

  // 计算子菜单位置
  const subMenuPosition = {
    x: parentX + 160, // 子菜单在父菜单右侧
    y: parentY + index * 36 // 子菜单与父菜单项对齐
  };

  return (
    <div
      className={`context-menu-item ${item.disabled ? 'disabled' : ''} ${item.className || ''} ${item.children ? 'has-submenu' : ''} ${showSubMenu ? 'submenu-open' : ''}`}
      onClick={item.disabled ? undefined : handleClick}
      onMouseEnter={() => item.children && setShowSubMenu(true)}
      onMouseLeave={() => item.children && setShowSubMenu(false)}
    >
      {item.icon && <span className="context-menu-item-icon">{item.icon}</span>}
      <span className="context-menu-item-label">{item.label}</span>
      {item.shortcut && <span className="context-menu-item-shortcut">{item.shortcut}</span>}
      {item.children && (
        <span className="context-menu-submenu-arrow">▶</span>
      )}

      {showSubMenu && item.children && (
        <ContextMenu
          x={subMenuPosition.x}
          y={subMenuPosition.y}
          items={item.children}
          onClose={() => setShowSubMenu(false)}
          isSubMenu={true}
        />
      )}
    </div>
  );
};

/**
 * 上下文菜单组件
 * 用于显示右键菜单
 */
const ContextMenu: React.FC<ContextMenuProps & { isSubMenu?: boolean }> = ({
  x,
  y,
  items,
  onClose,
  isSubMenu = false
}) => {
  // 处理点击外部关闭菜单
  useEffect(() => {
    if (!isSubMenu) {
      const handleClickOutside = () => onClose();
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [onClose, isSubMenu]);

  // 处理按下 Escape 键关闭菜单
  useEffect(() => {
    if (!isSubMenu) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          onClose();
        }
      };
      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [onClose, isSubMenu]);

  // 确保菜单不超出视口
  const adjustedPosition = React.useMemo(() => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 假设菜单宽度为 160px，高度为 items.length * 36px
    const menuWidth = 160;
    const menuHeight = items.length * 36;

    let adjustedX = x;
    let adjustedY = y;

    // 如果菜单会超出右边界，则将其向左移动
    if (x + menuWidth > viewportWidth) {
      adjustedX = isSubMenu ? x - 320 : viewportWidth - menuWidth - 10; // 子菜单向左展开
    }

    // 如果菜单会超出下边界，则将其向上移动
    if (y + menuHeight > viewportHeight) {
      adjustedY = viewportHeight - menuHeight - 10;
    }

    return { x: adjustedX, y: adjustedY };
  }, [x, y, items.length, isSubMenu]);

  return (
    <div
      className={`context-menu ${isSubMenu ? 'submenu' : ''}`}
      style={{
        position: 'fixed',
        top: adjustedPosition.y,
        left: adjustedPosition.x,
        zIndex: isSubMenu ? 1001 : 1000,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {items.map((item, index) => (
        'type' in item && item.type === 'divider' ? (
          // 渲染分隔线
          <div key={index} className="context-menu-divider" />
        ) : (
          // 渲染菜单项
          <MenuItem
            key={index}
            item={item as MenuItem}
            onClose={onClose}
            parentX={adjustedPosition.x}
            parentY={adjustedPosition.y}
            index={index}
          />
        )
      ))}
    </div>
  );
};

export default ContextMenu;
