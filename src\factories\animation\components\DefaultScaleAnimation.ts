import { IScaleAnimation } from '../interfaces';

/**
 * 默认缩放动画实现
 */
export class DefaultScaleAnimation implements IScaleAnimation {
  private startScale: number = 0.95;
  private endScale: number = 1;
  private duration: number = 300;
  private delay: number = 0;
  private visible: boolean = true;
  
  /**
   * 设置起始缩放比例
   * @param scale 缩放比例
   */
  setStartScale(scale: number): void {
    this.startScale = scale;
  }
  
  /**
   * 设置结束缩放比例
   * @param scale 缩放比例
   */
  setEndScale(scale: number): void {
    this.endScale = scale;
  }
  
  /**
   * 设置动画持续时间
   * @param duration 持续时间（毫秒）
   */
  setDuration(duration: number): void {
    this.duration = duration;
  }
  
  /**
   * 设置动画延迟
   * @param delay 延迟时间（毫秒）
   */
  setDelay(delay: number): void {
    this.delay = delay;
  }
  
  /**
   * 设置是否显示
   * @param visible 是否显示
   */
  setVisible(visible: boolean): void {
    this.visible = visible;
  }
  
  /**
   * 获取CSS类名
   */
  getClassName(): string {
    const baseClass = 'transition-transform';
    const visibilityClass = this.visible ? 'opacity-100' : 'opacity-0';
    const scaleClass = this.visible ? `scale-${this.endScale * 100}` : `scale-${this.startScale * 100}`;
    
    return `${baseClass} ${visibilityClass} ${scaleClass}`;
  }
  
  /**
   * 获取CSS样式
   */
  getStyle(): React.CSSProperties {
    return {
      transform: this.visible ? `scale(${this.endScale})` : `scale(${this.startScale})`,
      transitionProperty: 'transform, opacity',
      transitionDuration: `${this.duration}ms`,
      transitionTimingFunction: 'ease-out',
      transitionDelay: `${this.delay}ms`,
    };
  }
}
