/**
 * 上下文链路服务
 * 负责构建和管理节点的上下文链路，包括层级关系和序列关系
 */

import { OutlineNodeType } from '../../../../../types/outline';

// 上下文链路类型定义
export interface ContextChain {
  type: 'hierarchy' | 'sequence';
  title: string;
  description: string;
  icon: string;
  nodes: ContextNode[];
  relationships: ContextRelationship[];
  priority: number;
}

export interface ContextNode {
  id: string;
  title: string;
  type: 'volume' | 'chapter' | 'plot' | 'dialogue' | 'book' | 'scene' | 'note';
  level: number;
  content: string;
  summary: string;
  relevanceScore: number;
  parentId?: string;
  children?: ContextNode[];
}

export interface ContextRelationship {
  fromNodeId: string;
  toNodeId: string;
  type: 'parent-child' | 'sibling' | 'reference';
  strength: number;
}

export interface ContextOptions {
  includeHierarchy: boolean;
  includeSequence: boolean;
  maxNodesPerChain: number;
  priorityThreshold: number;
}

// 缓存条目
interface CacheEntry {
  chain: ContextChain;
  timestamp: number;
}

export class ContextChainService {
  private cache = new Map<string, CacheEntry>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  constructor(private outline: any) {}

  /**
   * 获取节点的完整上下文链路
   */
  async getNodeContextChains(nodeId: string, options: ContextOptions): Promise<ContextChain[]> {
    console.log('🔗 开始构建上下文链路:', { nodeId, options });

    const node = this.findNodeInOutline(nodeId);
    if (!node) {
      console.warn('⚠️ 节点未找到:', nodeId);
      return [];
    }

    const chains: ContextChain[] = [];

    try {
      // 1. 层级链路（必需）
      if (options.includeHierarchy) {
        const hierarchyChain = await this.getOrBuildChain(
          nodeId,
          'hierarchy',
          () => this.buildHierarchyChain(node)
        );
        if (hierarchyChain.nodes.length > 1) {
          chains.push(hierarchyChain);
        }
      }

      // 2. 序列链路（推荐）
      if (options.includeSequence) {
        const sequenceChain = await this.getOrBuildChain(
          nodeId,
          'sequence',
          () => this.buildSequenceChain(node)
        );
        if (sequenceChain.nodes.length > 1) {
          chains.push(sequenceChain);
        }
      }

      console.log('✅ 上下文链路构建完成:', chains.length);
      return chains;
    } catch (error) {
      console.error('❌ 构建上下文链路失败:', error);
      return [];
    }
  }

  /**
   * 构建层级链路（父子关系）
   */
  private async buildHierarchyChain(node: OutlineNodeType): Promise<ContextChain> {
    console.log('📖 构建层级链路:', node.title);

    const ancestors = this.getAncestorNodes(node.id);
    const descendants = this.getDescendantNodes(node.id);
    const currentLevel = this.getNodeLevel(node.id);
    const currentContextNode = this.convertToContextNode(node, currentLevel);

    // 确保节点按层级顺序排列
    const allNodes = [...ancestors, currentContextNode, ...descendants];

    // 为每个节点添加更详细的关系信息
    const enrichedNodes = allNodes.map(contextNode => {
      const originalNode = this.findNodeInOutline(contextNode.id);
      return {
        ...contextNode,
        parentId: originalNode?.parentId,
        children: originalNode?.children || []
      };
    });

    console.log('📖 层级链路节点:', enrichedNodes.map(n => `L${n.level}: ${n.title}`));

    return {
      type: 'hierarchy',
      title: '层级结构',
      description: '显示节点的完整层级关系',
      icon: '📖',
      nodes: enrichedNodes,
      relationships: this.buildHierarchyRelationships(enrichedNodes),
      priority: 1
    };
  }

  /**
   * 构建序列链路（兄弟关系）
   */
  private async buildSequenceChain(node: OutlineNodeType): Promise<ContextChain> {
    console.log('🔗 构建序列链路:', node.title);

    const siblings = this.getSiblingNodes(node.id);
    const currentIndex = siblings.findIndex(s => s.id === node.id);

    if (currentIndex === -1) {
      console.warn('⚠️ 在兄弟节点中未找到当前节点');
      return {
        type: 'sequence',
        title: '序列关系',
        description: '显示节点在同级中的前后关系',
        icon: '🔗',
        nodes: [],
        relationships: [],
        priority: 2
      };
    }

    // 获取前后各2个兄弟节点
    const startIndex = Math.max(0, currentIndex - 2);
    const endIndex = Math.min(siblings.length, currentIndex + 3);
    const contextSiblings = siblings.slice(startIndex, endIndex);

    const level = this.getNodeLevel(node.id);
    const contextNodes = contextSiblings.map(sibling =>
      this.convertToContextNode(sibling, level)
    );

    return {
      type: 'sequence',
      title: '序列关系',
      description: '显示节点在同级中的前后关系',
      icon: '🔗',
      nodes: contextNodes,
      relationships: this.buildSequenceRelationships(contextNodes),
      priority: 2
    };
  }

  /**
   * 获取祖先节点
   */
  private getAncestorNodes(nodeId: string): ContextNode[] {
    const ancestors: ContextNode[] = [];
    let currentNode = this.findNodeInOutline(nodeId);
    let level = this.getNodeLevel(nodeId);

    while (currentNode && currentNode.parentId) {
      const parent = this.findNodeInOutline(currentNode.parentId);
      if (parent) {
        level--;
        ancestors.unshift(this.convertToContextNode(parent, level));
        currentNode = parent;
      } else {
        break;
      }
    }

    return ancestors;
  }

  /**
   * 获取后代节点（只获取直接子节点）
   */
  private getDescendantNodes(nodeId: string): ContextNode[] {
    const node = this.findNodeInOutline(nodeId);
    if (!node || !node.children || node.children.length === 0) {
      return [];
    }

    const level = this.getNodeLevel(nodeId) + 1;
    return node.children.map(child => this.convertToContextNode(child, level));
  }

  /**
   * 获取兄弟节点
   */
  private getSiblingNodes(nodeId: string): OutlineNodeType[] {
    const node = this.findNodeInOutline(nodeId);
    if (!node) return [];

    if (node.parentId) {
      const parent = this.findNodeInOutline(node.parentId);
      return parent?.children || [];
    } else {
      // 根节点的兄弟节点
      return this.outline?.nodes || [];
    }
  }

  /**
   * 在大纲中查找节点
   */
  private findNodeInOutline(nodeId: string): OutlineNodeType | null {
    const findNode = (nodes: OutlineNodeType[]): OutlineNodeType | null => {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node;
        }
        if (node.children) {
          const found = findNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findNode(this.outline?.nodes || []);
  }

  /**
   * 获取节点层级
   */
  private getNodeLevel(nodeId: string): number {
    let level = 0;
    let currentNode = this.findNodeInOutline(nodeId);

    while (currentNode && currentNode.parentId) {
      level++;
      currentNode = this.findNodeInOutline(currentNode.parentId);
    }

    return level;
  }

  /**
   * 转换为上下文节点
   */
  private convertToContextNode(node: OutlineNodeType, level: number): ContextNode {
    // 构建完整的内容，包含标题和描述
    let fullContent = '';

    if (node.title) {
      fullContent += `标题：${node.title}\n\n`;
    }

    if (node.description && node.description.trim()) {
      fullContent += `描述：${node.description}`;
    } else {
      fullContent += `描述：暂无详细描述`;
    }

    // 如果有额外的内容字段，也包含进来
    if ((node as any).content && (node as any).content.trim()) {
      fullContent += `\n\n详细内容：${(node as any).content}`;
    }

    return {
      id: node.id,
      title: node.title,
      type: node.type as any,
      level,
      content: fullContent, // 使用完整内容，不省略
      summary: node.description?.substring(0, 200) || '暂无描述',
      relevanceScore: 1.0,
      parentId: node.parentId,
      children: node.children
    };
  }

  /**
   * 构建层级关系
   */
  private buildHierarchyRelationships(nodes: ContextNode[]): ContextRelationship[] {
    const relationships: ContextRelationship[] = [];

    for (let i = 0; i < nodes.length - 1; i++) {
      const current = nodes[i];
      const next = nodes[i + 1];

      if (next.level === current.level + 1) {
        relationships.push({
          fromNodeId: current.id,
          toNodeId: next.id,
          type: 'parent-child',
          strength: 1.0
        });
      }
    }

    return relationships;
  }

  /**
   * 构建序列关系
   */
  private buildSequenceRelationships(nodes: ContextNode[]): ContextRelationship[] {
    const relationships: ContextRelationship[] = [];

    for (let i = 0; i < nodes.length - 1; i++) {
      relationships.push({
        fromNodeId: nodes[i].id,
        toNodeId: nodes[i + 1].id,
        type: 'sibling',
        strength: 0.8
      });
    }

    return relationships;
  }

  /**
   * 缓存机制
   */
  private async getOrBuildChain(
    nodeId: string,
    chainType: string,
    builder: () => Promise<ContextChain>
  ): Promise<ContextChain> {
    const cacheKey = `${nodeId}-${chainType}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log('📋 使用缓存的链路:', cacheKey);
      return cached.chain;
    }

    const chain = await builder();
    this.cache.set(cacheKey, {
      chain,
      timestamp: Date.now()
    });

    console.log('💾 链路已缓存:', cacheKey);
    return chain;
  }

  /**
   * 清除节点相关缓存
   */
  invalidateNode(nodeId: string): void {
    for (const [key] of this.cache) {
      if (key.startsWith(nodeId) || key.includes(nodeId)) {
        this.cache.delete(key);
      }
    }
    console.log('🗑️ 已清除节点缓存:', nodeId);
  }
}
