"use client";

import React, { useState, useEffect } from 'react';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { Character, Terminology, WorldBuilding, PromptCategory } from '@/lib/db/dexie';
import { aiRewriteService, ConversationMessage } from '@/factories/ai/services/AIRewriteService';
import SelectorDialog from '../AIWritingPanel/SelectorDialog';
import { PromptTemplateManager } from '@/factories/ui/components/PromptTemplateManager';

// 导入子组件
import AIRewriteLeftPanel from './AIRewriteLeftPanel';
import AIRewriteMiddlePanel from './AIRewriteMiddlePanel';
import AIRewritePreview from './AIRewritePreview';
import AIRewriteControls from './AIRewriteControls';

/**
 * 对话输入框组件
 */
interface DialogInputProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (prompt: string, mode?: 'continue' | 'rewrite' | 'analyze') => void;
}

const DialogInput: React.FC<DialogInputProps> = ({
  isOpen,
  onClose,
  onSubmit
}) => {
  const [prompt, setPrompt] = useState('');
  const [mode, setMode] = useState<'continue' | 'rewrite' | 'analyze'>('continue');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim()) {
      onSubmit(prompt, mode);
      setPrompt('');
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">继续对话</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* 模式选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              选择模式
            </label>
            <div className="flex space-x-2">
              <button
                type="button"
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'continue'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setMode('continue')}
              >
                继续创作
              </button>
              <button
                type="button"
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'rewrite'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setMode('rewrite')}
              >
                重写内容
              </button>
              <button
                type="button"
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  mode === 'analyze'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setMode('analyze')}
              >
                分析内容
              </button>
            </div>
          </div>

          {/* 提示输入 */}
          <div className="mb-4">
            <label htmlFor="continue-prompt" className="block text-sm font-medium text-gray-700 mb-1">
              请输入您的指示
            </label>
            <textarea
              id="continue-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              rows={3}
              placeholder="请输入您希望AI如何继续改写内容的指示..."
            />
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-3 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
              发送
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  terminologyIds?: string[];
  worldBuildingIds?: string[];
  summary?: string;
  notes?: string;
}

interface AIRewriteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRewritten: (content: string) => void;
  selectedText: string;
  beforeContext?: string;
  afterContext?: string;
  bookId?: string;
}

/**
 * AI改写对话框组件
 * 用于使用AI改写选中的文本内容
 */
const AIRewriteDialog: React.FC<AIRewriteDialogProps> = ({
  isOpen,
  onClose,
  onRewritten,
  selectedText: initialSelectedText,
  beforeContext: initialBeforeContext = '',
  afterContext: initialAfterContext = '',
  bookId = ''
}) => {
  // 选中文本和上下文
  const [selectedText, setSelectedText] = useState(initialSelectedText);
  const [beforeContext, setBeforeContext] = useState(initialBeforeContext);
  const [afterContext, setAfterContext] = useState(initialAfterContext);

  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节 - 使用localStorage持久化
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-rewrite-selected-chapters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  // 范围选择 - 使用localStorage持久化
  const [rangeStart, setRangeStart] = useState<string>(() => {
    const savedRangeStart = localStorage.getItem(`ai-rewrite-range-start-${bookId}`);
    // 检查是否为空或只有空白字符
    return savedRangeStart && savedRangeStart.trim() ? savedRangeStart : '';
  });

  const [rangeEnd, setRangeEnd] = useState<string>(() => {
    const savedRangeEnd = localStorage.getItem(`ai-rewrite-range-end-${bookId}`);
    // 检查是否为空或只有空白字符
    return savedRangeEnd && savedRangeEnd.trim() ? savedRangeEnd : '';
  });

  // 改写要求和剧情方向 - 使用localStorage持久化
  const [rewriteRequirements, setRewriteRequirements] = useState<string>(() => {
    const savedRequirements = localStorage.getItem(`ai-rewrite-requirements-${bookId}`);
    // 检查是否为空或只有空白字符
    return savedRequirements && savedRequirements.trim() ? savedRequirements : '';
  });

  const [plot, setPlot] = useState<string>(() => {
    const savedPlot = localStorage.getItem(`ai-rewrite-plot-${bookId}`);
    // 检查是否为空或只有空白字符
    return savedPlot && savedPlot.trim() ? savedPlot : '';
  });

  // 生成状态
  const [isLoading, setIsLoading] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // 流式响应状态
  const [streamResponse, setStreamResponse] = useState('');

  // 对话历史状态 - 使用localStorage持久化
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>(() => {
    // 从localStorage加载对话历史
    const savedHistory = localStorage.getItem(`ai-rewrite-history-${bookId}`);

    // 解析历史记录，并确保所有消息都有正确的isSystemGenerated标记
    if (savedHistory) {
      const parsedHistory = JSON.parse(savedHistory) as ConversationMessage[];

      // 确保每条消息都有isSystemGenerated属性
      // 默认情况下，系统消息和助手的初始响应被标记为系统生成的预设消息
      // 用户的实际输入和AI的实质性回复不被标记为系统生成的预设消息
      return parsedHistory.map(msg => {
        // 如果消息已经有isSystemGenerated属性，则保留原值
        if (msg.hasOwnProperty('isSystemGenerated')) {
          return msg;
        }

        // 否则，根据消息类型和内容设置默认值
        // 用户的实际输入和AI的实质性回复不被标记为系统生成的预设消息
        const isSystemGenerated =
          // 系统消息默认为系统生成
          msg.role === 'system' ||
          // 助手消息，如果包含特定的确认文本，则为系统生成
          (msg.role === 'assistant' && (
            msg.content.includes('我已阅读并分析了') ||
            msg.content.includes('我已记住') ||
            msg.content.includes('我将查看并分析') ||
            msg.content.includes('我已了解相关') ||
            msg.content.includes('我已完成所有章节的分析')
          )) ||
          // 用户消息，如果包含特定的系统生成文本，则为系统生成
          (msg.role === 'user' && (
            msg.content.includes('请根据以上信息，在【') ||
            msg.content.includes('请参考以下') ||
            msg.content.includes('上文 第') ||
            msg.content.includes('下文 第') ||
            msg.content.includes('【需要改写的文本】') ||
            msg.content.includes('请确保改写后的内容与上文自然衔接')
          ));

        return {
          ...msg,
          isSystemGenerated
        };
      });
    }

    return [];
  });

  // 关联元素数据
  const [characters, setCharacters] = useState<Character[]>([]);
  const [terminologies, setTerminologies] = useState<Terminology[]>([]);
  const [worldBuildings, setWorldBuildings] = useState<WorldBuilding[]>([]);
  const [outlines, setOutlines] = useState<any[]>([]);
  const [isLoadingRelatedElements, setIsLoadingRelatedElements] = useState(false);

  // 选中的关联元素 - 使用localStorage持久化
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-rewrite-selected-characters-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedTerminologyIds, setSelectedTerminologyIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-rewrite-selected-terminologies-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedWorldBuildingIds, setSelectedWorldBuildingIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-rewrite-selected-worldbuildings-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  const [selectedOutlineNodeIds, setSelectedOutlineNodeIds] = useState<string[]>(() => {
    const savedIds = localStorage.getItem(`ai-rewrite-selected-outlines-${bookId}`);
    return savedIds ? JSON.parse(savedIds) : [];
  });

  // 弹窗状态
  const [isChapterSelectorOpen, setIsChapterSelectorOpen] = useState(false);
  const [isCharacterSelectorOpen, setIsCharacterSelectorOpen] = useState(false);
  const [isTerminologySelectorOpen, setIsTerminologySelectorOpen] = useState(false);
  const [isWorldBuildingSelectorOpen, setIsWorldBuildingSelectorOpen] = useState(false);

  // 提示词模板管理状态
  const [isPromptTemplateManagerOpen, setIsPromptTemplateManagerOpen] = useState(false);
  const [promptTemplateCategory, setPromptTemplateCategory] = useState<PromptCategory>(PromptCategory.REWRITE_REQUIREMENTS);

  // 继续对话输入框状态
  const [isContinuePromptOpen, setIsContinuePromptOpen] = useState(false);
  const [continuePrompt, setContinuePrompt] = useState('');

  // 继续对话状态
  const [continueMode, setContinueMode] = useState<'continue' | 'rewrite' | 'analyze' | 'new'>('new');

  // API设置
  const [apiSettings] = useState(() => {
    const settingsFactory = createSettingsFactory();
    return settingsFactory.createAPISettingsDialogComponent();
  });

  // 当对话框打开时更新选中文本和上下文
  useEffect(() => {
    if (isOpen) {
      setSelectedText(initialSelectedText);
      setBeforeContext(initialBeforeContext);
      setAfterContext(initialAfterContext);
    }
  }, [isOpen, initialSelectedText, initialBeforeContext, initialAfterContext]);

  // 当前编辑器中的章节ID
  const [currentEditorChapterId, setCurrentEditorChapterId] = useState<string | null>(null);

  // 当对话框打开时加载章节和关联元素
  useEffect(() => {
    if (isOpen && bookId) {
      loadChapters();
      loadRelatedElements();

      // 尝试从localStorage获取当前编辑器中的章节ID
      try {
        const currentChapterId = localStorage.getItem(`current-editor-chapter-${bookId}`);
        if (currentChapterId) {
          setCurrentEditorChapterId(currentChapterId);
        }
      } catch (error) {
        console.error('获取当前编辑器章节ID失败:', error);
      }
    }
  }, [isOpen, bookId]);

  // 初始化对话历史和流式响应
  useEffect(() => {
    if (isOpen && bookId) {
      // 从localStorage加载对话模式
      const savedMode = localStorage.getItem(`ai-rewrite-mode-${bookId}`);
      if (savedMode) {
        try {
          const mode = JSON.parse(savedMode) as 'continue' | 'rewrite' | 'analyze' | 'new';
          setContinueMode(mode);
        } catch (error) {
          console.error('解析对话模式失败:', error);
          setContinueMode('new');
        }
      }

      // 从localStorage加载流式响应
      const savedResponse = localStorage.getItem(`ai-rewrite-response-${bookId}`);
      if (savedResponse) {
        setStreamResponse(savedResponse);
      }

      // 从localStorage加载生成内容
      const savedContent = localStorage.getItem(`ai-rewrite-content-${bookId}`);
      if (savedContent) {
        setGeneratedContent(savedContent);
      }
    }
  }, [isOpen, bookId]);

  // 监听关联元素选择变化并保存到localStorage
  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-selected-chapters-${bookId}`, JSON.stringify(selectedChapterIds));

      // 当选中章节变化时，自动处理跨章节上下文
      updateCrossChapterContext();
    }
  }, [selectedChapterIds, bookId]);

  /**
   * 更新跨章节上下文
   * 当选中章节变化时，自动提取前后章节的内容作为上下文
   */
  const updateCrossChapterContext = () => {
    if (!chapters || chapters.length === 0 || selectedChapterIds.length === 0) {
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : (a.chapterNumber || 0);
      const orderB = b.order !== undefined ? b.order : (b.chapterNumber || 0);
      return orderA - orderB;
    });

    // 找出选中章节在所有章节中的位置
    const selectedChapterIndices: number[] = [];

    sortedChapters.forEach((chapter, index) => {
      if (chapter.id && selectedChapterIds.includes(chapter.id)) {
        selectedChapterIndices.push(index);
      }
    });

    if (selectedChapterIndices.length === 0) {
      return;
    }

    // 获取第一个和最后一个选中章节的索引
    const firstSelectedIndex = Math.min(...selectedChapterIndices);
    const lastSelectedIndex = Math.max(...selectedChapterIndices);

    // 检查是否有前章节
    const hasPreviousChapter = firstSelectedIndex > 0;
    // 检查是否有后章节
    const hasNextChapter = lastSelectedIndex < sortedChapters.length - 1;

    // 处理前章节上下文
    let newBeforeContext = '';
    if (hasPreviousChapter) {
      const previousChapter = sortedChapters[firstSelectedIndex - 1];
      const previousChapterText = previousChapter.content || '';

      // 提取前章节最后几句话作为上下文
      const extractLastSentences = (text: string, count: number): string => {
        if (!text) return '';

        // 按句子分割文本
        const sentences = text.match(/[^.!?。！？]+[.!?。！？]+/g) || [text];

        // 如果句子数量小于要提取的数量，返回整个文本
        if (sentences.length <= count) return text;

        // 提取最后count个句子
        return sentences.slice(-count).join('');
      };

      // 提取前章节最后3句话作为上下文
      const lastSentences = extractLastSentences(previousChapterText, 3);
      if (lastSentences) {
        const previousChapterTitle = previousChapter.title || `第${previousChapter.chapterNumber || '?'}章`;
        newBeforeContext = `【${previousChapterTitle}的结尾】\n${lastSentences}`;
      }
    }

    // 处理后章节上下文
    let newAfterContext = '';
    if (hasNextChapter) {
      const nextChapter = sortedChapters[lastSelectedIndex + 1];
      const nextChapterText = nextChapter.content || '';

      // 提取后章节前几句话作为上下文
      const extractFirstSentences = (text: string, count: number): string => {
        if (!text) return '';

        // 按句子分割文本
        const sentences = text.match(/[^.!?。！？]+[.!?。！？]+/g) || [text];

        // 如果句子数量小于要提取的数量，返回整个文本
        if (sentences.length <= count) return text;

        // 提取前count个句子
        return sentences.slice(0, count).join('');
      };

      // 提取后章节前3句话作为上下文
      const firstSentences = extractFirstSentences(nextChapterText, 3);
      if (firstSentences) {
        const nextChapterTitle = nextChapter.title || `第${nextChapter.chapterNumber || '?'}章`;
        newAfterContext = `【${nextChapterTitle}的开头】\n${firstSentences}`;
      }
    }

    // 更新上下文，但不覆盖用户手动设置的上下文
    if (newBeforeContext && (!beforeContext || beforeContext === initialBeforeContext)) {
      setBeforeContext(newBeforeContext);
    }

    if (newAfterContext && (!afterContext || afterContext === initialAfterContext)) {
      setAfterContext(newAfterContext);
    }
  };

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-selected-characters-${bookId}`, JSON.stringify(selectedCharacterIds));
    }
  }, [selectedCharacterIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-selected-terminologies-${bookId}`, JSON.stringify(selectedTerminologyIds));
    }
  }, [selectedTerminologyIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-selected-worldbuildings-${bookId}`, JSON.stringify(selectedWorldBuildingIds));
    }
  }, [selectedWorldBuildingIds, bookId]);

  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-selected-outlines-${bookId}`, JSON.stringify(selectedOutlineNodeIds));
    }
  }, [selectedOutlineNodeIds, bookId]);

  // 保存对话历史到localStorage
  useEffect(() => {
    if (bookId) {
      localStorage.setItem(`ai-rewrite-history-${bookId}`, JSON.stringify(conversationHistory));
    }
  }, [conversationHistory, bookId]);

  // 监听流式响应更新事件
  useEffect(() => {
    const handleUpdateStreamResponse = (event: any) => {
      if (event.detail && event.detail.streamResponse) {
        setStreamResponse(event.detail.streamResponse);
      }
    };

    // 添加事件监听器
    window.addEventListener('update-stream-response', handleUpdateStreamResponse);

    // 清理函数
    return () => {
      window.removeEventListener('update-stream-response', handleUpdateStreamResponse);
    };
  }, []);

  // 保存范围选择到localStorage
  useEffect(() => {
    if (bookId) {
      // 只有当内容不为空时才保存
      if (rangeStart && rangeStart.trim()) {
        localStorage.setItem(`ai-rewrite-range-start-${bookId}`, rangeStart);
      } else {
        // 如果内容为空，则移除localStorage中的项
        localStorage.removeItem(`ai-rewrite-range-start-${bookId}`);
      }
    }
  }, [rangeStart, bookId]);

  useEffect(() => {
    if (bookId) {
      // 只有当内容不为空时才保存
      if (rangeEnd && rangeEnd.trim()) {
        localStorage.setItem(`ai-rewrite-range-end-${bookId}`, rangeEnd);
      } else {
        // 如果内容为空，则移除localStorage中的项
        localStorage.removeItem(`ai-rewrite-range-end-${bookId}`);
      }
    }
  }, [rangeEnd, bookId]);

  // 保存改写要求和剧情方向到localStorage
  useEffect(() => {
    if (bookId) {
      // 只有当内容不为空时才保存
      if (rewriteRequirements && rewriteRequirements.trim()) {
        localStorage.setItem(`ai-rewrite-requirements-${bookId}`, rewriteRequirements);
      } else {
        // 如果内容为空，则移除localStorage中的项
        localStorage.removeItem(`ai-rewrite-requirements-${bookId}`);
      }
    }
  }, [rewriteRequirements, bookId]);

  useEffect(() => {
    if (bookId) {
      // 只有当内容不为空时才保存
      if (plot && plot.trim()) {
        localStorage.setItem(`ai-rewrite-plot-${bookId}`, plot);
      } else {
        // 如果内容为空，则移除localStorage中的项
        localStorage.removeItem(`ai-rewrite-plot-${bookId}`);
      }
    }
  }, [plot, bookId]);

  /**
   * 过滤掉预制的消息，只保留用户真正的输入指令和AI的实质性回复
   * @param messages 对话消息数组
   * @returns 过滤后的对话消息数组
   */
  const filterPresetMessages = (messages: ConversationMessage[]): ConversationMessage[] => {
    if (!messages || messages.length === 0) {
      return [];
    }

    // 创建一个副本，避免修改原始数组
    let filteredMessages = [...messages];

    // 记录消息类型，用于调试
    console.log('过滤前的消息类型:', filteredMessages.map(msg => msg.role));

    // 使用isSystemGenerated标记过滤消息
    filteredMessages = filteredMessages.filter(msg => {
      // 过滤掉系统角色的消息
      if (msg.role === 'system') return false;

      // 过滤掉标记为系统生成的预设消息
      if (msg.isSystemGenerated) return false;

      // 过滤掉标记为上下文消息的消息
      if (msg.isContextMessage) return false;

      // 保留其他用户和助手消息
      return msg.role === 'user' || msg.role === 'assistant';
    });

    // 记录过滤后的消息类型，用于调试
    console.log('过滤后的消息类型:', filteredMessages.map(msg => msg.role));

    return filteredMessages;
  };

  /**
   * 计算文本的实际字数（忽略空格和换行符）
   * @param text 要计算字数的文本
   * @returns 实际字数
   */
  const calculateActualWordCount = (text: string): number => {
    if (!text) return 0;
    return text.replace(/\s+/g, '').length;
  };

  /**
   * 加载章节列表
   */
  const loadChapters = async () => {
    if (!bookId) return;

    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 ChapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 AppDatabase 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            setChapters(chaptersData);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
    } catch (error) {
      console.error('加载章节数据失败:', error);
      setError('获取章节数据失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingChapters(false);
    }
  };

  /**
   * 加载关联元素（人物、术语、世界观）
   */
  const loadRelatedElements = async () => {
    if (!bookId) return;

    setIsLoadingRelatedElements(true);
    setError(null);

    try {
      // 加载人物
      try {
        const { characterRepository } = await import('@/lib/db/repositories');
        const charactersData = await characterRepository.getAllByBookId(bookId);
        setCharacters(charactersData);
      } catch (error) {
        console.error('加载人物数据失败:', error);
      }

      // 加载术语
      try {
        const { terminologyRepository } = await import('@/lib/db/repositories');
        const terminologiesData = await terminologyRepository.getAllByBookId(bookId);
        setTerminologies(terminologiesData);
      } catch (error) {
        console.error('加载术语数据失败:', error);
      }

      // 加载世界观
      try {
        const { worldBuildingRepository } = await import('@/lib/db/repositories');
        const worldBuildingsData = await worldBuildingRepository.getAllByBookId(bookId);
        setWorldBuildings(worldBuildingsData);
      } catch (error) {
        console.error('加载世界观数据失败:', error);
      }

      // 加载大纲
      try {
        const { outlineRepository } = await import('@/lib/db/repositories');
        const outlinesData = await outlineRepository.getAllByBookId(bookId);
        setOutlines(outlinesData);
      } catch (error) {
        console.error('加载大纲数据失败:', error);
      }
    } catch (error) {
      console.error('加载关联元素失败:', error);
      setError('加载关联元素失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoadingRelatedElements(false);
    }
  };

  /**
   * 生成改写内容
   */
  const handleGenerate = async () => {
    if (!selectedText) {
      setError('请先选择要改写的文本');
      return;
    }

    setIsLoading(true);
    setError(null);

    // 只有在非继续对话模式下才清空流式响应和生成内容
    if (continueMode === 'new') {
      setStreamResponse('');
      setGeneratedContent('');
    }

    try {
      // 获取API设置
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        setError(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
        setIsLoading(false);
        return;
      }

      // 使用AIRewriteService生成内容
      const result = await aiRewriteService.rewriteContent(
        {
          // API设置
          provider: currentProvider,
          model: currentModel,
          apiKey: apiKey,
          apiEndpoint: apiEndpoint,

          // 内容设置
          selectedText: selectedText,
          rewriteRequirements: rewriteRequirements && rewriteRequirements.trim() ? rewriteRequirements : undefined,
          plot: plot && plot.trim() ? plot : undefined,
          beforeContext: beforeContext && calculateActualWordCount(beforeContext) > 0 ? beforeContext : undefined,
          afterContext: afterContext && calculateActualWordCount(afterContext) > 0 ? afterContext : undefined,

          // 关联元素
          chapters: chapters,
          selectedChapterIds: selectedChapterIds,
          characters: characters,
          selectedCharacterIds: selectedCharacterIds,
          terminologies: terminologies,
          selectedTerminologyIds: selectedTerminologyIds,
          worldBuildings: worldBuildings,
          selectedWorldBuildingIds: selectedWorldBuildingIds,

          // 大纲关联元素
          outlines: outlines,
          selectedOutlineNodeIds: selectedOutlineNodeIds,
          outlineContextMode: 'hierarchy',

          // 书籍ID
          bookId: bookId || '',

          // 对话历史
          conversationHistory: conversationHistory
        },
        {
          onStart: () => {
            console.log('开始改写内容');
          },
          onStreamChunk: (chunk: string) => {
            // 使用防抖函数减少更新频率
            // 累积内容，但减少状态更新和存储频率
            setStreamResponse(prevResponse => {
              // 如果是第一个块且有前缀，则添加前缀
              if (prevResponse === '' && continueMode !== 'new') {
                const prefix = localStorage.getItem(`ai-rewrite-prefix-${bookId}`) || '';
                const newResponse = prefix + chunk;

                // 使用自定义事件而不是直接更新localStorage，减少频繁IO操作
                // 只在累积一定数量的字符后才更新localStorage
                if (newResponse.length % 100 === 0) {
                  localStorage.setItem(`ai-rewrite-response-${bookId}`, newResponse);
                }

                return newResponse;
              } else {
                const newResponse = prevResponse + chunk;

                // 使用自定义事件而不是直接更新localStorage，减少频繁IO操作
                // 只在累积一定数量的字符后才更新localStorage
                if (newResponse.length % 100 === 0) {
                  localStorage.setItem(`ai-rewrite-response-${bookId}`, newResponse);
                }

                return newResponse;
              }
            });
          },
          onComplete: (result) => {
            console.log('改写内容完成', result);

            // 获取生成的内容
            let generated = result.text.trim();

            // 如果是继续对话模式，并且有前缀，则拼接前缀和生成的内容
            if (continueMode !== 'new') {
              const prefix = localStorage.getItem(`ai-rewrite-prefix-${bookId}`) || '';
              if (prefix && !streamResponse.startsWith(prefix)) {
                generated = prefix + generated;
              } else {
                // 如果流式响应已经包含了前缀，则使用流式响应
                generated = streamResponse;
              }
            }

            setGeneratedContent(generated);

            // 持久化存储生成内容
            localStorage.setItem(`ai-rewrite-content-${bookId}`, generated);

            // 更新对话历史，但先过滤掉预制消息
            if (result.conversationHistory) {
              // 过滤掉预制消息
              const filteredHistory = filterPresetMessages(result.conversationHistory);
              setConversationHistory(filteredHistory);

              // 持久化存储过滤后的对话历史
              localStorage.setItem(`ai-rewrite-history-${bookId}`, JSON.stringify(filteredHistory));
            }

            // 重置继续对话状态
            setContinueMode('new');

            // 清除前缀缓存
            localStorage.removeItem(`ai-rewrite-prefix-${bookId}`);
          },
          onError: (error) => {
            console.error('改写内容失败', error);
            setError('改写内容失败: ' + error.message);
          }
        }
      );
    } catch (error: any) {
      console.error('改写内容失败', error);
      setError('改写内容失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消生成
   */
  const handleCancel = () => {
    // 这里可以添加取消生成的逻辑，如果API支持的话
    setIsLoading(false);
  };

  /**
   * 插入生成的内容
   */
  const handleInsert = () => {
    if (generatedContent && onRewritten) {
      onRewritten(generatedContent);
      onClose();
    }
  };

  /**
   * 打开改写要求模板管理器
   */
  const handleOpenRewriteRequirementsTemplates = () => {
    setPromptTemplateCategory(PromptCategory.REWRITE_REQUIREMENTS);
    setIsPromptTemplateManagerOpen(true);
  };

  /**
   * 打开剧情方向模板管理器
   */
  const handleOpenPlotTemplates = () => {
    setPromptTemplateCategory(PromptCategory.REWRITE_PLOT);
    setIsPromptTemplateManagerOpen(true);
  };

  /**
   * 处理模板选择
   */
  const handleSelectTemplate = (template: any) => {
    if (promptTemplateCategory === PromptCategory.REWRITE_REQUIREMENTS) {
      setRewriteRequirements(template.content);
      // 保存到localStorage
      if (bookId) {
        // 只有当内容不为空时才保存
        if (template.content && template.content.trim()) {
          localStorage.setItem(`ai-rewrite-requirements-${bookId}`, template.content);
        } else {
          // 如果内容为空，则移除localStorage中的项
          localStorage.removeItem(`ai-rewrite-requirements-${bookId}`);
        }
      }
    } else if (promptTemplateCategory === PromptCategory.REWRITE_PLOT) {
      setPlot(template.content);
      // 保存到localStorage
      if (bookId) {
        // 只有当内容不为空时才保存
        if (template.content && template.content.trim()) {
          localStorage.setItem(`ai-rewrite-plot-${bookId}`, template.content);
        } else {
          // 如果内容为空，则移除localStorage中的项
          localStorage.removeItem(`ai-rewrite-plot-${bookId}`);
        }
      }
    }
    setIsPromptTemplateManagerOpen(false);
  };

  /**
   * 处理统一关联管理的变化
   */
  const handleAssociationsChange = (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
    outlineNodeIds: string[];
  }) => {
    setSelectedChapterIds(associations.chapterIds);
    setSelectedCharacterIds(associations.characterIds);
    setSelectedTerminologyIds(associations.terminologyIds);
    setSelectedWorldBuildingIds(associations.worldBuildingIds);
    setSelectedOutlineNodeIds(associations.outlineNodeIds);
  };

  /**
   * 处理继续对话
   * 打开继续对话输入框，让用户输入继续对话的提示
   */
  const handleContinue = () => {
    console.log('[AIRewriteDialog] 打开继续对话输入框');

    // 打开继续对话输入框
    setIsContinuePromptOpen(true);
  };

  /**
   * 处理继续对话提交
   * 当用户在继续对话输入框中提交提示后调用
   * @param prompt 用户输入的提示
   * @param mode 继续对话模式
   */
  const handleSubmitContinue = (prompt: string, mode: 'continue' | 'rewrite' | 'analyze' = 'continue') => {
    console.log('[AIRewriteDialog] 提交继续对话，提示:', prompt, '模式:', mode);

    // 保存用户输入的提示
    setContinuePrompt(prompt);

    // 保存继续对话模式
    setContinueMode(mode);
    localStorage.setItem(`ai-rewrite-mode-${bookId}`, JSON.stringify(mode));

    // 保存用户提示到localStorage
    localStorage.setItem(`ai-rewrite-user-prompt-${bookId}`, prompt);

    // 关闭继续对话输入框
    setIsContinuePromptOpen(false);

    // 不清空流式响应和生成内容，保留之前的对话历史
    setIsLoading(true);
    setError(null);

    // 清空流式响应，但保留生成内容和对话历史
    // 这样可以避免重复显示之前的内容
    setStreamResponse('');

    // 清除前缀缓存，因为我们现在直接使用对话历史
    localStorage.removeItem(`ai-rewrite-prefix-${bookId}`);

    // 记录当前对话历史，用于调试
    console.log('[AIRewriteDialog] 当前对话历史长度:', conversationHistory.length);

    // 记录对话历史中的消息类型，用于调试
    const messageTypes = conversationHistory.map(msg => msg.role);
    console.log('[AIRewriteDialog] 对话历史中的消息类型:', messageTypes);

    // 记录消息内容的前20个字符，用于调试
    const messageContents = conversationHistory.map(msg => ({
      role: msg.role,
      content: msg.content.substring(0, 20) + '...'
    }));
    console.log('[AIRewriteDialog] 对话历史中的消息内容预览:', messageContents);

    // 调用handleGenerate生成内容
    handleGenerate();
  };

  /**
   * 重新开始对话
   */
  const handleRestartConversation = () => {
    // 清空对话历史
    setConversationHistory([]);
    localStorage.removeItem(`ai-rewrite-history-${bookId}`);

    // 重置继续对话状态
    setContinueMode('new');
    localStorage.setItem(`ai-rewrite-mode-${bookId}`, JSON.stringify('new'));

    // 清空生成内容
    setGeneratedContent('');
    setStreamResponse('');

    // 清除localStorage中的流式响应和生成内容
    localStorage.removeItem(`ai-rewrite-response-${bookId}`);
    localStorage.removeItem(`ai-rewrite-content-${bookId}`);

    // 清除前缀缓存
    localStorage.removeItem(`ai-rewrite-prefix-${bookId}`);

    // 不清除左侧面板的内容，保留用户的设置
    // 如果需要清除左侧面板的内容，可以取消下面的注释
    /*
    // 清除改写要求和剧情方向
    setRewriteRequirements('');
    setPlot('');
    localStorage.removeItem(`ai-rewrite-requirements-${bookId}`);
    localStorage.removeItem(`ai-rewrite-plot-${bookId}`);

    // 清除范围选择
    setRangeStart('');
    setRangeEnd('');
    localStorage.removeItem(`ai-rewrite-range-start-${bookId}`);
    localStorage.removeItem(`ai-rewrite-range-end-${bookId}`);

    // 清除选中的章节、人物、术语和世界观
    setSelectedChapterIds([]);
    setSelectedCharacterIds([]);
    setSelectedTerminologyIds([]);
    setSelectedWorldBuildingIds([]);
    localStorage.removeItem(`ai-rewrite-selected-chapters-${bookId}`);
    localStorage.removeItem(`ai-rewrite-selected-characters-${bookId}`);
    localStorage.removeItem(`ai-rewrite-selected-terminologies-${bookId}`);
    localStorage.removeItem(`ai-rewrite-selected-worldbuildings-${bookId}`);
    */
  };

  // 注意：handleSubmitContinue方法已移至AIRewritePreview组件中处理

  /**
   * 处理范围选择
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect' = 'select') => {
    if (!rangeStart || !rangeEnd || !chapters || chapters.length === 0) {
      alert('请输入有效的章节范围');
      return;
    }

    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end) || start < 1 || end < 1) {
      alert('请输入有效的章节范围');
      return;
    }

    if (start > end) {
      alert('起始章节不能大于结束章节');
      return;
    }

    if (start > chapters.length || end > chapters.length) {
      alert(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      alert('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-2xl w-11/12 max-w-7xl h-[90vh] flex flex-col">
        {/* 对话框标题栏 */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-bold">AI选中改写</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="flex-1 overflow-hidden p-4">
          <div className="flex h-full space-x-4">
            {/* 左侧面板 - 关联元素和改写要求 */}
            <AIRewriteLeftPanel
              bookId={bookId}
              selectedChapterIds={selectedChapterIds}
              selectedCharacterIds={selectedCharacterIds}
              selectedTerminologyIds={selectedTerminologyIds}
              selectedWorldBuildingIds={selectedWorldBuildingIds}
              selectedOutlineNodeIds={selectedOutlineNodeIds}
              rewriteRequirements={rewriteRequirements}
              plot={plot}
              error={error}

              onRewriteRequirementsChange={setRewriteRequirements}
              onPlotChange={setPlot}
              onOpenRewriteRequirementsTemplates={handleOpenRewriteRequirementsTemplates}
              onOpenPlotTemplates={handleOpenPlotTemplates}
              onAssociationsChange={handleAssociationsChange}
              onOutlineNodesChange={setSelectedOutlineNodeIds}
            />

            {/* 中间面板 - 选中文本和上下文 */}
            <AIRewriteMiddlePanel
              selectedText={selectedText}
              beforeContext={beforeContext}
              afterContext={afterContext}
              onSelectedTextChange={setSelectedText}
            />

            {/* 右侧面板 - 预览 */}
            <div className="w-1/3 pl-4 flex flex-col">
              {/* 预览区域 */}
              <div className="flex-1 overflow-hidden">
                <AIRewritePreview
                  isLoading={isLoading}
                  streamResponse={streamResponse}
                  generatedContent={generatedContent}
                  conversationHistory={conversationHistory}
                  bookId={bookId}
                  onInsert={handleInsert}
                  onInsertBubble={handleInsert}
                  onRestart={handleRestartConversation}
                  onContinue={handleContinue}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 底部操作按钮区域 */}
        <div className="p-4 border-t flex justify-between items-center bg-gradient-to-r from-gray-50 to-indigo-50">
          <div>
            <button
              className="px-5 py-2.5 bg-white text-gray-700 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm font-medium"
              onClick={onClose}
              disabled={isLoading}
            >
              取消
            </button>
          </div>

          <div className="flex space-x-3">
            {/* 根据生成状态显示不同的按钮组 */}
            {generatedContent ? (
              /* 已生成内容，显示操作按钮组 */
              <>
                <button
                  onClick={handleRestartConversation}
                  className="px-4 py-2.5 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors shadow-sm flex items-center"
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  重新开始
                </button>

                <button
                  onClick={handleContinue}
                  className="px-4 py-2.5 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors shadow-sm flex items-center"
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                  </svg>
                  继续对话
                </button>

                <button
                  onClick={handleInsert}
                  className="px-4 py-2.5 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors shadow-sm flex items-center"
                  disabled={isLoading}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  替换选中文本
                </button>
              </>
            ) : (
              /* 未生成内容，显示开始改写按钮 */
              <AIRewriteControls
                isLoading={isLoading}
                onGenerate={handleGenerate}
                onCancel={handleCancel}
                disabled={!selectedText || isLoading}
              />
            )}
          </div>
        </div>

        {/* 继续对话输入框 */}
        <DialogInput
          isOpen={isContinuePromptOpen}
          onClose={() => setIsContinuePromptOpen(false)}
          onSubmit={handleSubmitContinue}
        />

        {/* 选择器对话框 */}
        <SelectorDialog
          isOpen={isChapterSelectorOpen}
          title="选择参考章节"
          items={chapters.map(chapter => {
            // 使用辅助函数计算实际字数
            const actualWordCount = calculateActualWordCount(chapter.content);

            // 检查是否为当前编辑器中的章节
            const isCurrentEditorChapter = chapter.id === currentEditorChapterId;

            return {
              id: chapter.id || '',
              name: chapter.title || `第${chapter.chapterNumber || '?'}章`,
              description: `${actualWordCount}字 · ${chapter.updatedAt ? new Date(chapter.updatedAt).toLocaleDateString('zh-CN') : '未知日期'}`,
              disabled: isCurrentEditorChapter // 当前编辑器中的章节不允许勾选
            };
          })}
          selectedIds={selectedChapterIds}
          onSelect={setSelectedChapterIds}
          onClose={() => setIsChapterSelectorOpen(false)}
          isLoading={isLoadingChapters}
          loadingText="加载章节中..."
          emptyText="没有找到章节"
          extraContent={
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
              <h4 className="text-sm font-medium text-blue-800 mb-2">章节范围选择</h4>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={rangeStart}
                  onChange={(e) => setRangeStart(e.target.value)}
                  placeholder="起始章节"
                  className="w-24 px-2 py-1 border border-blue-300 rounded-md text-sm"
                  min="1"
                />
                <span className="text-blue-500">至</span>
                <input
                  type="number"
                  value={rangeEnd}
                  onChange={(e) => setRangeEnd(e.target.value)}
                  placeholder="结束章节"
                  className="w-24 px-2 py-1 border border-blue-300 rounded-md text-sm"
                  min="1"
                />
                <button
                  onClick={() => handleRangeSelect('select')}
                  className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 transition-colors"
                >
                  选择范围
                </button>
                <button
                  onClick={() => handleRangeSelect('deselect')}
                  className="px-3 py-1 bg-red-500 text-white rounded-md text-sm hover:bg-red-600 transition-colors"
                >
                  取消范围
                </button>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                提示: 输入章节编号范围（如1至5）快速选择多个章节
              </p>
            </div>
          }
        />

        <SelectorDialog
          isOpen={isCharacterSelectorOpen}
          title="选择人物"
          items={characters.map(character => ({
            id: character.id || '',
            name: character.name || '',
            description: character.description || ''
          }))}
          selectedIds={selectedCharacterIds}
          onSelect={setSelectedCharacterIds}
          onClose={() => setIsCharacterSelectorOpen(false)}
        />

        <SelectorDialog
          isOpen={isTerminologySelectorOpen}
          title="选择术语"
          items={terminologies.map(terminology => ({
            id: terminology.id || '',
            name: terminology.name || '',
            description: terminology.description || ''
          }))}
          selectedIds={selectedTerminologyIds}
          onSelect={setSelectedTerminologyIds}
          onClose={() => setIsTerminologySelectorOpen(false)}
        />

        <SelectorDialog
          isOpen={isWorldBuildingSelectorOpen}
          title="选择世界观"
          items={worldBuildings.map(worldBuilding => ({
            id: worldBuilding.id || '',
            name: worldBuilding.name || '',
            description: worldBuilding.description || ''
          }))}
          selectedIds={selectedWorldBuildingIds}
          onSelect={setSelectedWorldBuildingIds}
          onClose={() => setIsWorldBuildingSelectorOpen(false)}
        />

        {/* 提示词模板管理器 */}
        <PromptTemplateManager
          isOpen={isPromptTemplateManagerOpen}
          onClose={() => setIsPromptTemplateManagerOpen(false)}
          category={promptTemplateCategory}
          onSelectTemplate={handleSelectTemplate}
          initialPrompt={promptTemplateCategory === PromptCategory.REWRITE_REQUIREMENTS ? rewriteRequirements : plot}
        />

        {/* 继续对话提示已移至弹窗式输入框 */}
      </div>
    </div>
  );
};

export default AIRewriteDialog;
