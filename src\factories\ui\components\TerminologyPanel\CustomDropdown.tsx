"use client";

import React, { useState, useRef, useEffect } from 'react';

interface Option {
  value: string;
  label: string;
  isGroupTitle?: boolean;
  mainCategory?: string;
  mainCategoryLabel?: string;
}

interface CustomDropdownProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  maxHeight?: number; // 最大高度，默认为300px
  enableSearch?: boolean; // 是否启用搜索，默认为true
  placeholder?: string; // 占位符文本
  name?: string; // 字段名称，用于表单提交
}

/**
 * 自定义下拉框组件
 */
export const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  value,
  onChange,
  className = '',
  maxHeight = 300,
  enableSearch = true,
  placeholder = '请选择',
  name
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(options);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 获取当前选中选项的标签
  const selectedOption = options.find(option => option.value === value);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理搜索
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOptions(options);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredOptions(
        options.filter(option =>
          !option.isGroupTitle && option.label.toLowerCase().includes(query)
        )
      );
    }
  }, [searchQuery, options]);

  // 处理选项点击
  const handleOptionClick = (option: Option) => {
    if (option.isGroupTitle) return;
    onChange(option.value);
    setIsOpen(false);
    setSearchQuery('');
  };

  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    } else if (e.key === 'ArrowDown' && isOpen) {
      e.preventDefault();
      // 实现向下导航逻辑
    } else if (e.key === 'ArrowUp' && isOpen) {
      e.preventDefault();
      // 实现向上导航逻辑
    } else if (e.key === 'Enter' && isOpen) {
      e.preventDefault();
      // 实现选择当前高亮选项的逻辑
    }
  };

  // 当下拉框打开时，聚焦搜索框
  useEffect(() => {
    if (isOpen && enableSearch && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, enableSearch]);

  return (
    <div
      ref={dropdownRef}
      className={`relative ${className} custom-dropdown`}
      onKeyDown={handleKeyDown}
      data-name={name}
      data-value={value}
    >
      <button
        type="button"
        className="appearance-none pl-3 pr-8 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary w-full text-left"
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderColor: 'rgba(139, 69, 19, 0.2)',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
          transition: 'all 0.3s ease',
          color: 'var(--color-primary)'
        }}
        onClick={() => setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        {selectedOption ? selectedOption.label : placeholder}
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg
            className={`h-4 w-4 text-gray-400 transition-transform duration-300 ${isOpen ? 'transform rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </span>
      </button>

      {isOpen && (
        <div
          className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden"
          style={{
            animation: 'dropdownFadeIn 0.2s ease forwards',
            borderColor: 'rgba(139, 69, 19, 0.2)',
            boxShadow: '0 4px 12px rgba(139, 69, 19, 0.1)'
          }}
        >
          <style jsx>{`
            @keyframes dropdownFadeIn {
              from { opacity: 0; transform: translateY(-10px); }
              to { opacity: 1; transform: translateY(0); }
            }

            .option-item {
              transition: all 0.2s ease;
            }

            .option-item:hover {
              background-color: rgba(235, 245, 255, 0.9);
              transform: translateX(5px);
            }
          `}</style>

          {/* 搜索框 */}
          {enableSearch && (
            <div className="p-2 border-b border-gray-100">
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-1.5 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="搜索..."
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderColor: 'rgba(139, 69, 19, 0.2)'
                }}
              />
            </div>
          )}

          {/* 选项列表 */}
          <div className="overflow-y-auto" style={{ maxHeight: enableSearch ? `${maxHeight - 50}px` : `${maxHeight}px` }}>
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 text-center">
                没有找到匹配项
              </div>
            ) : (
              filteredOptions.map((option) => (
                option.isGroupTitle ? (
                  // 渲染分组标题
                  <div
                    key={option.value}
                    className="px-3 py-2 text-xs font-semibold text-gray-500 bg-gray-50 uppercase tracking-wider"
                  >
                    {option.label}
                  </div>
                ) : (
                  // 渲染选项
                  <div
                    key={option.value}
                    className={`option-item px-3 py-2 cursor-pointer text-sm ${option.value === value ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                    onClick={() => handleOptionClick(option)}
                    style={
                      option.value === value
                        ? { backgroundColor: 'rgba(139, 69, 19, 0.1)', color: 'var(--color-primary)' }
                        : {}
                    }
                  >
                    {option.label}
                    {option.mainCategoryLabel && (
                      <span className="ml-2 text-xs text-gray-400">
                        {option.mainCategoryLabel}
                      </span>
                    )}
                  </div>
                )
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};
