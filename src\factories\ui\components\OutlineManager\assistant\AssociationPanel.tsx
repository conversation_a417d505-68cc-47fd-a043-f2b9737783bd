"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { AssociationItem } from '@/hooks/useAssociationState';
import AssociationManagerDialog from './AssociationManagerDialog';
import './AssociationPanel.css';

interface AssociationPanelProps {
  bookId: string;
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  onRemoveAssociation: (type: AssociationItem['type'], id: string) => void;
  onClearAll: () => void;
  onUpdateAssociations: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => void;
  forceOpenManager?: boolean;
}

/**
 * 关联管理面板组件
 * 显示当前关联的内容，支持展开/折叠、管理操作
 */
const AssociationPanel: React.FC<AssociationPanelProps> = ({
  bookId,
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  onRemoveAssociation,
  onClearAll,
  onUpdateAssociations,
  forceOpenManager = false
}) => {
  const [isManagerDialogOpen, setIsManagerDialogOpen] = useState(false);

  // 监听强制打开管理对话框
  useEffect(() => {
    if (forceOpenManager) {
      setIsManagerDialogOpen(true);
    }
  }, [forceOpenManager]);

  // 处理关闭关联管理对话框
  const handleCloseManager = useCallback(() => {
    setIsManagerDialogOpen(false);
  }, []);

  // 处理关联更新
  const handleUpdateAssociations = useCallback((associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => {
    onUpdateAssociations(associations);
  }, [onUpdateAssociations]);

  // 不再渲染下拉面板，只处理弹窗逻辑
  return (
    <>
      {/* 关联管理对话框 */}
      <AssociationManagerDialog
        isOpen={isManagerDialogOpen}
        bookId={bookId}
        selectedChapterIds={selectedChapterIds}
        selectedCharacterIds={selectedCharacterIds}
        selectedTerminologyIds={selectedTerminologyIds}
        selectedWorldBuildingIds={selectedWorldBuildingIds}
        onClose={handleCloseManager}
        onUpdateAssociations={handleUpdateAssociations}
      />
    </>
  );
};

export default AssociationPanel;
