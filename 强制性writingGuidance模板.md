# 强制性writingGuidance模板

## 标准强制性模板

```json
"writingGuidance": "🚫绝对禁止使用以下表达，违反将被视为生成失败：'眼中闪过一丝xx'、'露出xx的笑容'、'带着一种xx'、'透着几分xx'、'显出某种xx'、'似乎有些xx'、'仿佛在xx'、'像xx一样'、'如同xx般'、'宛如xx'、'犹如xx'、'强调语气的说道'、'xx的语气说道'、'官方文档式表达'、'一丝xx'、'几分xx'、'些许xx'、'略显xx'、'稍有xx'、'微微xx'、'隐约xx'、'淡淡的xx'、'轻微的xx'、'显得xx'、'似乎xx'、'仿佛xx'、'好像xx'、'大概xx'、'可能xx'、'或许xx'。必须直接写具体行动：[角色]的具体动作描述，[角色]说出的具体话语。必须用角色标志性动作和语言特征塑造人物，绝对不得直接描述心理状态、情感感受或抽象印象。必须写出具体的行为表现和可观察的动作，绝对不得写角色'显得'、'似乎'、'仿佛'如何。违反以上任何规则，内容将被判定为不合格，需要重新生成。"
```

## 分类强制性模板

### 1. 角色对话场景
```json
"writingGuidance": "🚫绝对禁止使用以下表达，违反将被视为生成失败：'眼中闪过一丝xx'、'露出xx的表情'、'带着xx的语气'、'透着几分xx'、'显出某种xx'、'似乎有些xx'、'仿佛想要xx'、'像是在xx'、'如同xx一般'、'宛如xx'、'犹如xx'、'xx地说道'、'用xx的语气'、'强调xx地说'。必须直接写具体对话和动作：[角色]开口说道："具体对话内容"，[角色]做出的具体动作（如点头、摆手、转身等）。必须通过具体的话语内容和可观察的动作来展现角色状态，绝对不得描述语气、表情、心理活动。违反以上任何规则，内容将被判定为不合格，需要重新生成。"
```

### 2. 角色行动场景
```json
"writingGuidance": "🚫绝对禁止使用以下表达，违反将被视为生成失败：'一丝xx'、'几分xx'、'些许xx'、'略显xx'、'稍有xx'、'微微xx'、'隐约xx'、'似乎xx'、'仿佛xx'、'好像xx'、'显得xx'、'像xx一样'、'如同xx'、'宛如xx'、'犹如xx'、'带着xx'、'透着xx'、'流露出xx'。必须直接写具体行动：[角色]走向xx，[角色]拿起xx，[角色]做出xx动作。必须描述角色的具体行为和可观察的动作结果，绝对不得描述感受、印象、心理活动或抽象状态。必须写角色做了什么，绝对不得写角色'显得'、'似乎'、'仿佛'如何。违反以上任何规则，内容将被判定为不合格，需要重新生成。"
```

### 3. 环境描写场景
```json
"writingGuidance": "🚫绝对禁止使用以下表达，违反将被视为生成失败：'弥漫着一种xx'、'透着几分xx'、'带着某种xx'、'似乎有些xx'、'仿佛在xx'、'像是xx'、'如同xx一般'、'宛如xx'、'犹如xx'、'显得xx'、'看起来xx'、'给人xx的感觉'。必须直接写具体的感官细节：看到的具体景象、听到的具体声音、闻到的具体气味、触摸到的具体质感。必须用客观的感官描述来构建场景，绝对不得添加主观感受或抽象印象。必须写出具体可观察的细节，绝对不得写'感觉'、'似乎'、'仿佛'的内容。违反以上任何规则，内容将被判定为不合格，需要重新生成。"
```

### 4. 冲突场景
```json
"writingGuidance": "🚫绝对禁止使用以下表达，违反将被视为生成失败：'眼中闪过一丝xx'、'脸上露出xx'、'带着一种xx'、'透着几分xx'、'显出某种xx'、'似乎想要xx'、'仿佛准备xx'、'像是要xx'、'如同xx一般'、'宛如xx'、'犹如xx'、'气氛变得xx'、'空气中弥漫着xx'。必须直接写冲突双方的具体行动：[角色A]做出的具体动作，[角色B]的具体反应，双方说出的具体话语。必须通过具体的行为对抗和言语交锋来展现冲突，绝对不得描述气氛、感受或心理活动。违反以上任何规则，内容将被判定为不合格，需要重新生成。"
```

## 使用说明

1. **选择合适的模板**：根据剧情点的类型选择对应的强制性模板
2. **直接替换**：将现有的writingGuidance内容完全替换为强制性模板
3. **保持结构**：保持JSON格式不变，只替换writingGuidance字段的内容
4. **全面覆盖**：确保所有剧情点都使用强制性模板

## 核心改进点

1. **强制性语言**：从"严禁"升级为"绝对禁止"，增加"违反将被视为生成失败"
2. **全面禁止列表**：覆盖所有常见的模糊表达变体
3. **明确后果**：每个模板都包含违反后果说明
4. **具体替代方案**：提供明确的正确写法指导
5. **抽象化处理**：使用[角色]、[目标]等占位符，避免具体人名限制
