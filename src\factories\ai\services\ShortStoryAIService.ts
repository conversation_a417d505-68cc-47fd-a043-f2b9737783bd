"use client";

import { aiServiceProvider } from '@/services/ai/AIServiceProvider';
import { AIShortStoryPrompts } from '../prompts/AIShortStoryPrompts';
import { MessageBuilder } from '@/utils/ai/MessageBuilder';
import { ShortStoryMessageArchitect } from './ShortStoryMessageArchitect';
import { UnifiedAIService, AIServiceType } from '@/services/ai/BaseAIService';
import {
  ShortStoryParams,
  CoreMystery,
  SegmentStructure,
  ShortStoryResult,
  ShortStoryCallbacks,
  ShortStoryMode,
  AssociatedElements
} from './types/ShortStoryTypes';

// ACE框架相关导入
interface ACEFramework {
  id: string;
  name: string;
  pattern?: string;
  description: string;
  effectiveness: number;
  examples: string[];
  category?: string;
  keywordElements?: Array<{
    text: string;
    hotness: number;
    tags: string[];
  }>;
  synopsisStructure?: {
    openingTechniques?: string[];
    hookStrategies?: string[];
    characterIntroduction?: string[];
    conflictSetup?: string[];
  };
  writingTechniques?: Array<{
    id: string;
    name: string;
    category: 'layout' | 'emphasis' | 'coolpoint' | 'creativity';
    description: string;
    examples: string[];
    techniqueType: string;
    effectiveness: number;
  }>;
  styleCharacteristics?: {
    layoutTechniques?: {
      paragraphStructure?: string[];
      lineBreakStrategy?: string[];
      rhythmControl?: string[];
      visualImpact?: number;
    };
    omissionAndEmphasis?: {
      omittedElements?: string[];
      emphasizedElements?: string[];
      contrastTechniques?: string[];
      suspensePoints?: string[];
    };
    coolPointLayout?: {
      primaryCoolPoints?: string[];
      coolPointTiming?: string[];
      coolPointIntensity?: number;
      anticipationBuilding?: string[];
    };
    creativeConcept?: {
      coreCreativity?: string[];
      conceptPresentation?: string[];
      uniquenessLevel?: number;
      marketAppeal?: string[];
    };
  };
  plotAnalysis?: {
    storyStructure?: string;
    conflictDesign?: string;
    rhythmControl?: string;
    plotPoints?: string[];
    behaviorFrameworks?: string[];
    plotPointsWithGuidance?: Array<{
      content: string;
      specificDescription: string;
      avoidanceGuidance: string;
    }>;
  };
}

/**
 * ACE框架管理器
 * 专门处理ACE框架的获取、解析和应用
 */
class ACEFrameworkManager {
  /**
   * 从localStorage获取所有类型的ACE框架
   */
  static getACEFrameworks(): ACEFramework[] {
    try {
      const allFrameworks: any[] = [];

      // 1. 获取简介关键词框架（从book-title-keywords）
      const bookTitleKeywords = localStorage.getItem('book-title-keywords');
      if (bookTitleKeywords) {
        const keywords = JSON.parse(bookTitleKeywords);
        if (Array.isArray(keywords) && keywords.length > 0) {
          allFrameworks.push({
            id: 'keywords-user-saved-title',
            name: '读者喜好关键词',
            category: 'synopsis-keywords',
            description: '用户在书名生成器中保存的关键词集合',
            effectiveness: 7,
            examples: keywords.slice(0, 3).map((k: any) => k.text || k),
            keywordElements: keywords.map((k: any) => ({
              text: k.text || k,
              hotness: k.hotness || 5,
              tags: k.tags || ['书名关键词']
            }))
          });
        }
      }

      // 2. 获取简介专用关键词框架（从synopsis-keywords）
      const synopsisKeywords = localStorage.getItem('synopsis-keywords');
      if (synopsisKeywords) {
        const keywords = JSON.parse(synopsisKeywords);
        if (Array.isArray(keywords) && keywords.length > 0) {
          allFrameworks.push({
            id: 'keywords-user-saved-synopsis',
            name: '用户简介关键词',
            category: 'synopsis-keywords',
            description: '用户在简介生成器中保存的专用关键词集合',
            effectiveness: 8,
            examples: keywords.slice(0, 3).map((k: any) => k.text || k),
            keywordElements: keywords.map((k: any) => ({
              text: k.text || k,
              hotness: k.hotness || 5,
              tags: k.tags || ['简介关键词']
            }))
          });
        }
      }

      // 3. 获取简介框架
      const synopsisFrameworks = localStorage.getItem('synopsis-frameworks');
      if (synopsisFrameworks) {
        const frameworks = JSON.parse(synopsisFrameworks);
        frameworks.forEach((framework: any) => {
          const convertedFramework = {
            id: framework.id,
            name: framework.name,
            category: 'synopsis-framework',
            pattern: framework.pattern,
            description: framework.description || '',
            effectiveness: framework.effectiveness || 5,
            examples: framework.examples || [],
            synopsisStructure: {
              openingTechniques: framework.writingTechniques?.filter((t: any) =>
                t.category === 'layout').map((t: any) => t.name) || [],
              conflictPresentation: framework.writingTechniques?.filter((t: any) =>
                t.category === 'emphasis').map((t: any) => t.name) || [],
              characterIntroduction: framework.writingTechniques?.filter((t: any) =>
                t.category === 'coolpoint').map((t: any) => t.name) || [],
              hookStrategies: framework.styleCharacteristics?.omissionAndEmphasis?.suspensePoints || []
            }
          };
          allFrameworks.push(convertedFramework);
        });
      }

      // 4. 获取大纲框架（从outline-frameworks）
      const outlineFrameworks = localStorage.getItem('outline-frameworks');
      if (outlineFrameworks) {
        const frameworks = JSON.parse(outlineFrameworks);
        frameworks.forEach((framework: any) => {
          const convertedFramework = {
            id: framework.id || `framework_${Date.now()}`,
            name: framework.frameworkName || framework.name || '未命名框架',
            category: 'outline-framework',
            pattern: framework.frameworkPattern || framework.pattern || '',
            description: framework.plotAnalysis ?
              `故事结构：${framework.plotAnalysis.storyStructure || ''}
冲突设计：${framework.plotAnalysis.conflictDesign || ''}
节奏控制：${framework.plotAnalysis.rhythmControl || ''}`.trim() :
              (framework.description || framework.frameworkPattern || ''),
            effectiveness: framework.usageCount || framework.effectiveness || 0,
            examples: framework.examples || [],
            writingTechniques: framework.writingTechniques || [],
            styleCharacteristics: framework.styleCharacteristics || {},
            plotAnalysis: framework.plotAnalysis || {}
          };
          allFrameworks.push(convertedFramework);
        });
      }

      // 5. 获取书名框架（从book-title-frameworks，也可能包含大纲相关）
      const titleFrameworks = localStorage.getItem('book-title-frameworks');
      if (titleFrameworks) {
        const frameworks = JSON.parse(titleFrameworks);
        frameworks.forEach((framework: any) => {
          // 避免重复添加（检查ID是否已存在）
          if (!allFrameworks.find(f => f.id === framework.id)) {
            const convertedFramework = {
              id: framework.id || `framework_${Date.now()}`,
              name: framework.name || framework.frameworkPattern || '未命名框架',
              category: 'outline-framework',
              pattern: framework.pattern || framework.frameworkPattern,
              description: framework.description || '',
              effectiveness: framework.effectiveness || 0,
              examples: framework.examples || [],
              writingTechniques: framework.writingTechniques || [],
              styleCharacteristics: framework.styleCharacteristics || {},
              plotAnalysis: framework.plotAnalysis || {}
            };
            allFrameworks.push(convertedFramework);
          }
        });
      }

      console.log('🔍 ShortStoryAIService获取到的框架数量:', allFrameworks.length);
      console.log('🔍 框架分类统计:', {
        'synopsis-keywords': allFrameworks.filter(f => f.category === 'synopsis-keywords').length,
        'synopsis-framework': allFrameworks.filter(f => f.category === 'synopsis-framework').length,
        'outline-framework': allFrameworks.filter(f => f.category === 'outline-framework').length
      });

      return allFrameworks
        .sort((a: any, b: any) => (b.effectiveness || 0) - (a.effectiveness || 0))
        .map((framework: any) => this.normalizeFramework(framework));
    } catch (error) {
      console.error('获取ACE框架失败:', error);
      return [];
    }
  }

  /**
   * 标准化框架数据结构
   */
  private static normalizeFramework(framework: any): ACEFramework {
    return {
      id: framework.id || `framework_${Date.now()}`,
      name: framework.name || framework.frameworkPattern || '未命名框架',
      pattern: framework.pattern || framework.frameworkPattern,
      description: framework.description || '',
      effectiveness: framework.effectiveness || 0,
      examples: framework.examples || [],
      writingTechniques: framework.writingTechniques || [],
      styleCharacteristics: framework.styleCharacteristics || {},
      plotAnalysis: framework.plotAnalysis || {},
      category: framework.category || 'unknown',
      keywordElements: framework.keywordElements || [],
      synopsisStructure: framework.synopsisStructure || {}
    };
  }

  /**
   * 提取框架的核心技巧
   */
  static extractCoreTechniques(frameworks: any[]): {
    plotTechniques: string[];
    rhythmTechniques: string[];
    suspenseTechniques: string[];
    characterTechniques: string[];
  } {
    const plotTechniques: string[] = [];
    const rhythmTechniques: string[] = [];
    const suspenseTechniques: string[] = [];
    const characterTechniques: string[] = [];

    frameworks.forEach(framework => {
      // 根据框架类型提取不同的技巧
      if (framework.category === 'synopsis-keywords' && framework.keywordElements) {
        framework.keywordElements.forEach((keyword: any) => {
          if (keyword.tags.includes('开局流')) {
            plotTechniques.push(`${keyword.text}技巧`);
          }
          if (keyword.tags.includes('情绪')) {
            suspenseTechniques.push(`${keyword.text}营造`);
          }
          if (keyword.tags.includes('时间')) {
            characterTechniques.push(`${keyword.text}设定`);
          }
        });
      } else if (framework.category === 'synopsis-framework' && framework.synopsisStructure) {
        if (framework.synopsisStructure.hookStrategies) {
          suspenseTechniques.push(...framework.synopsisStructure.hookStrategies);
        }
        if (framework.synopsisStructure.openingTechniques) {
          plotTechniques.push(...framework.synopsisStructure.openingTechniques);
        }
        if (framework.synopsisStructure.characterIntroduction) {
          characterTechniques.push(...framework.synopsisStructure.characterIntroduction);
        }
      } else {
        // 大纲框架或旧格式框架
        if (framework.plotAnalysis?.plotPointsWithGuidance) {
          framework.plotAnalysis.plotPointsWithGuidance.forEach((point: any) => {
            plotTechniques.push(point.specificDescription);
          });
        }

        // 从风格特征中提取技巧
        if (framework.styleCharacteristics?.layoutTechniques?.rhythmControl) {
          rhythmTechniques.push(...framework.styleCharacteristics.layoutTechniques.rhythmControl);
        }

        if (framework.styleCharacteristics?.omissionAndEmphasis?.suspensePoints) {
          suspenseTechniques.push(...framework.styleCharacteristics.omissionAndEmphasis.suspensePoints);
        }

        // 从写作技巧中提取
        if (framework.writingTechniques) {
          framework.writingTechniques.forEach((technique: any) => {
            if (technique.category === 'layout') {
              rhythmTechniques.push(technique.description);
            } else if (technique.category === 'emphasis') {
              suspenseTechniques.push(technique.description);
            } else if (technique.category === 'coolpoint') {
              characterTechniques.push(technique.description);
            }
          });
        }
      }
    });

    return {
      plotTechniques: [...new Set(plotTechniques)],
      rhythmTechniques: [...new Set(rhythmTechniques)],
      suspenseTechniques: [...new Set(suspenseTechniques)],
      characterTechniques: [...new Set(characterTechniques)]
    };
  }

  /**
   * 获取分类显示名称
   */
  private static getCategoryDisplayName(category: string): string {
    const categoryMap: Record<string, string> = {
      'synopsis-keywords': '简介关键词',
      'synopsis-framework': '简介框架',
      'outline-framework': '大纲框架',
      'unknown': '未知类型'
    };
    return categoryMap[category] || category;
  }

  /**
   * 构建ACE框架学习消息
   */
  static buildACELearningMessages(frameworkIds: string[], storyMode: ShortStoryMode): Array<{ role: string; content: string }> {
    if (!frameworkIds || frameworkIds.length === 0) {
      return [];
    }

    try {
      // 获取所有框架数据（包括简介关键词、简介框架、大纲框架）
      const { ACEFrameworkManager: ExternalACEFrameworkManager } = require('../../../services/ACEFrameworkManager');
      const allFrameworks = ExternalACEFrameworkManager.getAllFrameworks();

      console.log('🔍 ShortStoryAIService内部方法获取到的框架数量:', allFrameworks.length);
      console.log('🔍 框架分类统计:', {
        'synopsis-keywords': allFrameworks.filter((f: any) => f.category === 'synopsis-keywords').length,
        'synopsis-framework': allFrameworks.filter((f: any) => f.category === 'synopsis-framework').length,
        'outline-framework': allFrameworks.filter((f: any) => f.category === 'outline-framework').length
      });

      const selectedFrameworks = allFrameworks.filter((framework: any) =>
        frameworkIds.includes(framework.id)
      );

      if (selectedFrameworks.length === 0) {
        return [];
      }

      const messages: Array<{ role: string; content: string }> = [];

      // 添加ACE框架学习引导
      messages.push({
        role: 'user',
        content: `
        至高指导员 005：
        【ACE框架深度学习模式】
  你将学习${selectedFrameworks.length}个专业的ACE创作框架，这些框架包含了成功短篇小说/别的文学如长篇、电影、小说/或者其他的精神传递等/的核心技巧。请深度理解每个框架的独特特征，并在创作时灵活运用。

  学习重点：
  1. 情节设计技巧 - 如何设计引人入胜的剧情点
  2. 节奏控制方法 - 如何掌握叙述的快慢节奏
  3. 悬念营造技巧 - 如何制造和维持读者的好奇心
  4. 人物塑造方法 - 如何在短篇中快速建立鲜明角色

  当前创作模式：${storyMode}，请特别关注与此模式相关的技巧。`
      });

      // 为每个框架创建专门的学习消息
      selectedFrameworks.forEach((framework: any, index: number) => {
        const frameworkName = framework.name || '未命名框架';
        const category = framework.category || 'unknown';

        let frameworkContent = `=== ACE框架 ${index + 1}: ${frameworkName} ===\n`;
        frameworkContent += `分类：${ACEFrameworkManager.getCategoryDisplayName(category)}\n`;
        frameworkContent += `效果评级：${framework.effectiveness || 0}/10\n\n`;

        // 根据框架类型生成不同的学习内容
        if (framework.category === 'synopsis-keywords') {
          // 关键词框架 - 偏向灵感索引
          frameworkContent += `🔥 灵感关键词索引：\n`;
          if (framework.keywordElements) {
            framework.keywordElements.slice(0, 8).forEach((keyword: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${keyword.text} (热度:${keyword.hotness}) [${keyword.tags.join('、')}]\n`;
            });
          }
          frameworkContent += `\n💡 灵感应用指导：\n`;
          frameworkContent += `- 这些关键词代表了读者喜爱的元素和情感触点\n`;
          frameworkContent += `- 在构思悬念时，可以从这些关键词中寻找灵感\n`;
          frameworkContent += `- 将高热度关键词融入故事核心，增强读者共鸣\n`;
          frameworkContent += `- 注意关键词的标签分类，选择符合故事调性的元素\n\n`;

        } else if (framework.category === 'synopsis-framework') {
          // 简介框架 - 偏向剧情概要
          frameworkContent += `📖 剧情概要结构：\n`;
          if (framework.synopsisStructure) {
            if (framework.synopsisStructure.openingTechniques) {
              frameworkContent += `开场技巧：${framework.synopsisStructure.openingTechniques.join('、')}\n`;
            }
            if (framework.synopsisStructure.hookStrategies) {
              frameworkContent += `钩子策略：${framework.synopsisStructure.hookStrategies.join('、')}\n`;
            }
            if (framework.synopsisStructure.characterIntroduction) {
              frameworkContent += `角色引入：${framework.synopsisStructure.characterIntroduction.join('、')}\n`;
            }
            if (framework.synopsisStructure.conflictSetup) {
              frameworkContent += `冲突设置：${framework.synopsisStructure.conflictSetup.join('、')}\n`;
            }
          }
          frameworkContent += `\n🎯 剧情概要指导：\n`;
          frameworkContent += `- 这个框架提供了故事概要的结构模板\n`;
          frameworkContent += `- 在设计核心悬念时，参考其开场和钩子策略\n`;
          frameworkContent += `- 注意角色引入和冲突设置的节奏安排\n`;
          frameworkContent += `- 确保故事概要具有清晰的起承转合\n\n`;

        } else {
          // 大纲框架 - 保持原有的详细技巧
          if (framework.pattern) {
            frameworkContent += `📋 核心模式：\n${framework.pattern}\n\n`;
          }

          if (framework.description) {
            frameworkContent += `📝 框架描述：\n${framework.description}\n\n`;
          }

          // 剧情设计技巧
          if (framework.plotAnalysis?.plotPointsWithGuidance) {
            frameworkContent += `🎭 剧情设计技巧：\n`;
            framework.plotAnalysis.plotPointsWithGuidance.forEach((point: any, idx: number) => {
              frameworkContent += `${idx + 1}. ${point.content}\n`;
              frameworkContent += `   具体描写：${point.specificDescription}\n`;
              frameworkContent += `   避免事项：${point.avoidanceGuidance}\n\n`;
            });
          }
        }

        messages.push({
          role: 'user',
          content: frameworkContent
        });

        // AI确认学习 - 根据框架类型定制回复
        let confirmationContent = `好的，005指导员我已学习，我会在接下来引导"小懒"进行技巧等应用\n`;
        confirmationContent += `我已深度学习"${frameworkName}"${ACEFrameworkManager.getCategoryDisplayName(category)}框架。`;

        if (framework.category === 'synopsis-keywords') {
          confirmationContent += `我理解了：
1. 这些关键词代表的读者情感触点和喜好趋势
2. 如何从高热度关键词中提取创作灵感
3. 将关键词元素融入悬念设计的方法
4. 根据标签分类选择合适元素的技巧

我将在构思核心悬念时，从这些灵感关键词中寻找创意火花，确保故事具有读者共鸣。`;
        } else if (framework.category === 'synopsis-framework') {
          confirmationContent += `我理解了：
1. 这个框架的剧情概要结构模板
2. 开场技巧和钩子策略的具体应用
3. 角色引入和冲突设置的节奏安排
4. 如何构建清晰的起承转合结构

我将在设计故事结构时，参考这个概要框架，确保剧情发展有序且引人入胜。`;
        } else {
          confirmationContent += `我理解了：
1. 该框架的独特剧情设计方法
2. 节奏控制和悬念营造的具体技巧
3. 如何在短篇创作中应用这些技巧
4. 与${storyMode}模式的结合要点

我将在创作时灵活运用这些专业技巧，确保生成的内容具有该框架的精髓。`;
        }

        messages.push({
          role: 'assistant',
          content: confirmationContent
        });
      });

      // 综合学习总结
      const coreTechniques = ACEFrameworkManager.extractCoreTechniques(selectedFrameworks);
      messages.push({
        role: 'assistant',
        content: `
       好的， 005领导员我以学习，我会再接下来引导 "小懒"进行技巧等应用
        【ACE框架学习完成】
  我已完成${selectedFrameworks.length}个ACE框架的深度学习，掌握了以下核心技巧：

  🎭 剧情技巧 (${coreTechniques.plotTechniques.length}个)：
  ${coreTechniques.plotTechniques.slice(0, 3).join('、')}${coreTechniques.plotTechniques.length > 3 ? '等' : ''}

  🎵 节奏技巧 (${coreTechniques.rhythmTechniques.length}个)：
  ${coreTechniques.rhythmTechniques.slice(0, 3).join('、')}${coreTechniques.rhythmTechniques.length > 3 ? '等' : ''}

  🔍 悬念技巧 (${coreTechniques.suspenseTechniques.length}个)：
  ${coreTechniques.suspenseTechniques.slice(0, 3).join('、')}${coreTechniques.suspenseTechniques.length > 3 ? '等' : ''}

  我将在短篇/文学/各种形式创作/别的文学如长篇、电影、小说/或者其他的精神传递的三个阶段中系统性灵活使用地应用这些技巧，确保作品或者别的什么的质量。`
      });

      return messages;
    } catch (error) {
      console.error('构建ACE框架学习消息失败:', error);
      return [];
    }
  }
}


/**
 * AI短篇创作服务
 * 专门处理短篇小说的创作，实现"透露一半，逐渐补全"的核心理念
 */
export class ShortStoryAIService extends UnifiedAIService {
  private currentRequest?: AbortController;
  private apiSettings: any;

  constructor() {
    super(AIServiceType.SHORT_STORY);
    // 初始化API设置
    this.initializeAPISettings();
  }

  /**
   * 初始化API设置
   */
  private async initializeAPISettings() {
    try {
      const { createSettingsFactory } = await import('@/factories/settings/SettingsFactory');
      const settingsFactory = createSettingsFactory();
      this.apiSettings = settingsFactory.createAPISettingsDialogComponent();
    } catch (error) {
      console.warn('无法初始化API设置:', error);
    }
  }

  /**
   * 生成阶段效果策划（让AI规划每个阶段的具体效果）
   */
  async generatePhaseStrategy(
    coreMystery: CoreMystery,
    params: ShortStoryParams
  ): Promise<{
    success: boolean;
    phaseStrategy?: any;
    error?: string;
  }> {
    try {
      // 构建阶段策划消息
      const messages = ShortStoryMessageArchitect.buildPhaseStrategyMessages(
        coreMystery,
        params.targetSegments || 20,
        params.storyTone
      );

      // 使用统一的AI调用方法生成策划
      const response = await this.callAI(messages, {
        streaming: false // 策划生成不需要流式输出
      });

      if (!response.success || !response.text) {
        return {
          success: false,
          error: response.error || '生成阶段策划失败'
        };
      }

      // 解析策划结果
      let phaseStrategy: any;
      try {
        // 先尝试直接解析
        phaseStrategy = JSON.parse(response.text);
      } catch (parseError) {
        console.log('🔍 直接JSON解析失败，尝试提取JSON部分:', parseError);

        // 尝试多种JSON提取方式
        const extractionMethods = [
          // 方法1: 提取最外层大括号内容
          /\{[\s\S]*\}/,
          // 方法2: 提取```json代码块
          /```json\s*(\{[\s\S]*?\})\s*```/,
          // 方法3: 提取```代码块
          /```\s*(\{[\s\S]*?\})\s*```/,
          // 方法4: 查找第一个{到最后一个}
          /(\{[\s\S]*\})/
        ];

        let extracted = false;
        for (const method of extractionMethods) {
          const match = response.text.match(method);
          if (match) {
            try {
              const jsonText = match[1] || match[0];
              phaseStrategy = JSON.parse(jsonText);
              console.log('✅ JSON提取成功，使用方法:', method.source);
              extracted = true;
              break;
            } catch (innerError) {
              console.log('❌ JSON提取方法失败:', method.source, innerError);
              continue;
            }
          }
        }

        if (!extracted) {
          console.error('❌ 所有JSON提取方法都失败，原始响应:', response.text);
          return {
            success: false,
            error: `无法解析阶段策划结果。原始响应: ${response.text.substring(0, 200)}...`
          };
        }
      }

      // 验证JSON结构完整性
      const requiredPhases = ['setupPhase', 'compressionPhase', 'climaxPhase', 'resolutionPhase'];
      const missingPhases = requiredPhases.filter(phase => !phaseStrategy[phase]);

      if (missingPhases.length > 0) {
        console.warn('⚠️ JSON结构不完整，缺少阶段:', missingPhases);
        return {
          success: false,
          error: `阶段策划结构不完整，缺少: ${missingPhases.join(', ')}`
        };
      }

      // 验证每个阶段的必需字段
      for (const phaseName of requiredPhases) {
        const phase = phaseStrategy[phaseName];
        const requiredFields = ['phaseType', 'phaseGoal', 'revealStrategy', 'hideStrategy', 'plotDensityTactics', 'emotionalTarget', 'keyTechniques'];
        const missingFields = requiredFields.filter(field => !phase[field]);

        if (missingFields.length > 0) {
          console.warn(`⚠️ ${phaseName}缺少字段:`, missingFields);
          // 为缺少的字段提供默认值
          missingFields.forEach(field => {
            switch (field) {
              case 'plotDensityTactics':
              case 'keyTechniques':
                phase[field] = [];
                break;
              case 'phaseType':
                // 根据阶段名称设置默认的phaseType
                const phaseTypeMap: { [key: string]: string } = {
                  'setupPhase': '铺垫期',
                  'compressionPhase': '挤压期',
                  'climaxPhase': '高潮期',
                  'resolutionPhase': '结局期'
                };
                phase[field] = phaseTypeMap[phaseName] || '未知阶段';
                break;
              default:
                phase[field] = `待完善的${field}`;
            }
          });
        }

        // 特殊处理：为挤压期验证paymentHook字段
        if (phaseName === 'compressionPhase' && phase.paymentHook) {
          const hookRequiredFields = ['position', 'hookStrategy', 'cliffhangerDesign', 'readerPsychology'];
          const missingHookFields = hookRequiredFields.filter(field => !phase.paymentHook[field]);

          if (missingHookFields.length > 0) {
            console.warn(`⚠️ compressionPhase.paymentHook缺少字段:`, missingHookFields);
            // 为缺少的paymentHook字段提供默认值
            missingHookFields.forEach(field => {
              phase.paymentHook[field] = `待完善的${field}`;
            });
          }
        }
      }

      return {
        success: true,
        phaseStrategy
      };

    } catch (error: any) {
      console.error('❌ 生成阶段策划失败:', error);
      return {
        success: false,
        error: error.message || '生成阶段策划失败'
      };
    }
  }

  /**
   * 生成短篇结构（只到结构阶段，不自动生成内容）
   */
  async generateShortStoryStructure(
    params: ShortStoryParams,
    callbacks: ShortStoryCallbacks
  ): Promise<ShortStoryResult> {
    try {
      callbacks.onStart?.();

      // 第一步：生成核心悬念
      const mysteryResult = await this.generateCoreMystery(params);
      if (!mysteryResult.success || !mysteryResult.coreMystery) {
        return {
          success: false,
          error: mysteryResult.error || '生成核心悬念失败'
        };
      }

      const coreMystery = mysteryResult.coreMystery;
      callbacks.onMysteryGenerated?.(coreMystery);

      // 第二步：生成分段结构
      const structureResult = await this.generateSegmentStructure(
        coreMystery,
        params.targetSegments || 10,
        params
      );
      if (!structureResult.success || !structureResult.segments) {
        return {
          success: false,
          error: structureResult.error || '生成分段结构失败'
        };
      }

      const segments = structureResult.segments;
      callbacks.onStructureGenerated?.(segments);

      // 只返回结构，不生成内容
      const result: ShortStoryResult = {
        success: true,
        coreMystery,
        segments,
        fullText: '', // 暂时为空
        totalWordCount: 0,
        averageSegmentLength: 0,
        suspenseCurve: segments.map(seg => seg.tensionLevel)
      };

      callbacks.onComplete?.(result);
      return result;

    } catch (error: any) {
      console.error('❌ 短篇结构生成失败:', error);
      const errorMessage = error.message || '短篇结构生成失败';
      callbacks.onError?.(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 生成完整的短篇小说
   */
  async generateShortStory(
    params: ShortStoryParams,
    callbacks: ShortStoryCallbacks
  ): Promise<ShortStoryResult> {
    try {
      callbacks.onStart?.();

      // 第一步：生成核心悬念
      const mysteryResult = await this.generateCoreMystery(params);
      if (!mysteryResult.success || !mysteryResult.coreMystery) {
        return {
          success: false,
          error: mysteryResult.error || '生成核心悬念失败'
        };
      }

      const coreMystery = mysteryResult.coreMystery;
      callbacks.onMysteryGenerated?.(coreMystery);

      // 第二步：生成分段结构
      const structureResult = await this.generateSegmentStructure(
        coreMystery,
        params.targetSegments || 10,
        params
      );
      if (!structureResult.success || !structureResult.segments) {
        return {
          success: false,
          error: structureResult.error || '生成分段结构失败'
        };
      }

      const segments = structureResult.segments;

      // 第三步：生成各段落内容
      const generatedSegments: SegmentStructure[] = [];
      const previousContents: string[] = [];

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        
        const contentResult = await this.generateSegmentContent(
          segment,
          previousContents,
          coreMystery,
          params,
          callbacks.onStreamChunk ? (chunk: string) => callbacks.onStreamChunk!(chunk, i) : undefined
        );

        if (!contentResult.success || !contentResult.content) {
          return {
            success: false,
            error: `生成第${i + 1}段内容失败: ${contentResult.error}`
          };
        }

        const generatedSegment: SegmentStructure = {
          ...segment,
          content: contentResult.content,
          wordCount: contentResult.content.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length
        };

        generatedSegments.push(generatedSegment);
        previousContents.push(contentResult.content);
        
        callbacks.onSegmentGenerated?.(generatedSegment, i);
      }

      // 生成完整文本
      const fullText = this.assembleFullText(generatedSegments);
      
      // 计算统计信息
      const totalWordCount = generatedSegments.reduce((sum, seg) => sum + (seg.wordCount || 0), 0);
      const averageSegmentLength = totalWordCount / generatedSegments.length;
      const suspenseCurve = generatedSegments.map(seg => seg.tensionLevel);

      const result: ShortStoryResult = {
        success: true,
        coreMystery,
        segments: generatedSegments,
        fullText,
        totalWordCount,
        averageSegmentLength,
        suspenseCurve
      };

      callbacks.onComplete?.(result);
      return result;

    } catch (error: any) {
      console.error('❌ 短篇创作失败:', error);
      const errorMessage = error.message || '短篇创作失败';
      callbacks.onError?.(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 生成核心悬念
   */
  async generateCoreMystery(params: ShortStoryParams): Promise<{
    success: boolean;
    coreMystery?: CoreMystery;
    error?: string;
  }> {
    try {
      // 获取关联元素
      const associatedElements = await this.getAssociatedElements(params);

      // 使用专门的消息架构师构建消息
      const messages = ShortStoryMessageArchitect.buildCoreMysterMessages(params, associatedElements);

      // 添加ACE框架学习消息（005指导员专用）
      if (params.selectedACEFrameworkIds && params.selectedACEFrameworkIds.length > 0) {
        const aceMessages = ACEFrameworkManager.buildACELearningMessages(
          params.selectedACEFrameworkIds,
          params.storyTone as ShortStoryMode
        );
        messages.push(...aceMessages);
      }

      // 使用统一的AI调用方法生成核心悬念
      const response = await this.callAI(messages, {
        streaming: false // 核心悬念生成不需要流式输出
      });

      if (!response.success) {
        return {
          success: false,
          error: response.error || '生成核心悬念失败'
        };
      }

      // 解析响应
      const coreMystery = this.parseCoreMysteryResponse(response.text || '');

      return {
        success: true,
        coreMystery
      };

    } catch (error: any) {
      console.error('❌ 生成核心悬念失败:', error);
      return {
        success: false,
        error: error.message || '生成核心悬念失败'
      };
    }
  }

  /**
   * 生成分段结构
   */
  async generateSegmentStructure(
    coreMystery: CoreMystery,
    targetSegments: number = 10,
    params?: ShortStoryParams
  ): Promise<{
    success: boolean;
    segments?: SegmentStructure[];
    error?: string;
  }> {
    try {
      // 使用专门的消息架构师构建消息
      const messages = ShortStoryMessageArchitect.buildSegmentStructureMessages(
        coreMystery,
        targetSegments,
        params?.selectedACEFrameworkIds || []
      );

      // 添加ACE框架学习消息（005指导员专用）
      if (params?.selectedACEFrameworkIds && params.selectedACEFrameworkIds.length > 0) {
        const aceMessages = ACEFrameworkManager.buildACELearningMessages(
          params.selectedACEFrameworkIds,
          params.storyTone as ShortStoryMode || 'balanced'
        );
        messages.push(...aceMessages);
      }

      // 使用统一的AI调用方法生成结构
      const response = await this.callAI(messages, {
        streaming: false // 结构生成不需要流式输出
      });

      if (!response.success) {
        return {
          success: false,
          error: response.error || '生成分段结构失败'
        };
      }

      // 解析响应
      const segments = this.parseSegmentStructureResponse(response.text || '');

      return {
        success: true,
        segments
      };

    } catch (error: any) {
      console.error('❌ 生成分段结构失败:', error);
      return {
        success: false,
        error: error.message || '生成分段结构失败'
      };
    }
  }

  /**
   * 生成指定段落的内容（手动触发）
   */
  async generateSingleSegmentContent(
    segmentIndex: number,
    coreMystery: CoreMystery,
    segments: SegmentStructure[],
    params: ShortStoryParams,
    onChunk?: (chunk: string) => void
  ): Promise<{
    success: boolean;
    content?: string;
    error?: string;
  }> {
    try {
      if (segmentIndex < 0 || segmentIndex >= segments.length) {
        return {
          success: false,
          error: '段落索引超出范围'
        };
      }

      const segment = segments[segmentIndex];

      // 获取前面已生成的段落内容
      const previousContents: string[] = [];
      for (let i = 0; i < segmentIndex; i++) {
        if (segments[i].content) {
          previousContents.push(segments[i].content!);
        }
      }

      return await this.generateSegmentContent(
        segment,
        previousContents,
        coreMystery,
        params,
        onChunk
      );

    } catch (error: any) {
      console.error('❌ 生成单个段落内容失败:', error);
      return {
        success: false,
        error: error.message || '生成段落内容失败'
      };
    }
  }

  /**
   * 生成指定阶段的所有段落内容（一次性生成整个阶段）
   */
  async generatePhaseContent(
    phaseSegments: SegmentStructure[],
    allSegments: SegmentStructure[],
    coreMystery: CoreMystery,
    params: ShortStoryParams,
    onSegmentComplete?: (segmentIndex: number, content: string) => void,
    onChunk?: (chunk: string) => void
  ): Promise<{
    success: boolean;
    generatedSegments?: SegmentStructure[];
    error?: string;
  }> {
    try {
      // 过滤出未生成的段落
      const unfinishedSegments = phaseSegments.filter(seg => !seg.content);
      if (unfinishedSegments.length === 0) {
        return {
          success: true,
          generatedSegments: allSegments
        };
      }

      // 获取前面已生成的段落内容
      const previousContents: string[] = [];
      const firstSegmentIndex = unfinishedSegments[0].segmentNumber - 1;
      for (let i = 0; i < firstSegmentIndex; i++) {
        if (allSegments[i].content) {
          previousContents.push(allSegments[i].content!);
        }
      }

      // 一次性生成整个阶段的内容
      const phaseResult = await this.generatePhaseContentBatch(
        unfinishedSegments,
        previousContents,
        coreMystery,
        params,
        onChunk
      );

      if (!phaseResult.success || !phaseResult.phaseContents) {
        return {
          success: false,
          error: phaseResult.error || '生成阶段内容失败'
        };
      }

      // 更新段落内容
      const generatedSegments: SegmentStructure[] = [...allSegments];
      phaseResult.phaseContents.forEach((content, index) => {
        const segment = unfinishedSegments[index];
        const segmentIndex = segment.segmentNumber - 1;

        generatedSegments[segmentIndex] = {
          ...segment,
          content,
          wordCount: content.replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '').length
        };

        // 通知段落完成
        onSegmentComplete?.(segmentIndex, content);
      });

      return {
        success: true,
        generatedSegments
      };

    } catch (error: any) {
      console.error('❌ 生成阶段内容失败:', error);
      return {
        success: false,
        error: error.message || '生成阶段内容失败'
      };
    }
  }

  /**
   * 批量生成阶段内容（一次性生成整个阶段的所有段落）
   */
  private async generatePhaseContentBatch(
    segments: SegmentStructure[],
    previousContents: string[],
    coreMystery: CoreMystery,
    params: ShortStoryParams,
    onChunk?: (chunk: string) => void
  ): Promise<{
    success: boolean;
    phaseContents?: string[];
    error?: string;
  }> {
    try {
      // 构建关联元素
      const associatedElements: AssociatedElements = {
        characters: [],
        worldBuildings: [],
        terminologies: [],
        outlineNodes: [],
        chapters: []
      };

      // 构建阶段生成的消息
      const messages = ShortStoryMessageArchitect.buildPhaseContentMessages(
        segments,
        previousContents,
        coreMystery,
        associatedElements,
        params.selectedACEFrameworkIds || [],
        params.targetSegments || 20
      );

      let fullResponse = '';

      // 使用统一的AI流式调用方法生成内容
      const response = await this.callAIStreaming(
        messages,
        (chunk: string) => {
          fullResponse += chunk;
          onChunk?.(chunk);
        },
        {
          streaming: true
        }
      );

      if (!response.success || !response.text) {
        return {
          success: false,
          error: response.error || '生成阶段内容失败'
        };
      }

      // 先处理AI返回的内容，确保换行符正确
      const processedText = this.processAIContent(response.text);

      // 解析阶段内容（按段落分割）
      const phaseContents = this.parsePhaseContent(processedText, segments.length);

      // 对每个段落内容再次处理，确保格式正确
      const processedPhaseContents = phaseContents.map(content => this.processAIContent(content));

      return {
        success: true,
        phaseContents: processedPhaseContents
      };

    } catch (error: any) {
      console.error('❌ 批量生成阶段内容失败:', error);
      return {
        success: false,
        error: error.message || '批量生成阶段内容失败'
      };
    }
  }

  /**
   * 解析阶段内容，按段落分割
   */
  private parsePhaseContent(content: string, expectedSegments: number): string[] {
    console.log('🔍 开始解析阶段内容，期望段落数:', expectedSegments);
    console.log('🔍 原始内容长度:', content.length);

    // 尝试多种段落标记分割方式
    const segmentMarkers = [
      // 方法1: 第X段：
      { pattern: /第(\d+)段[：:]/g, name: '第X段标记' },
      // 方法2: 段落X：
      { pattern: /段落(\d+)[：:]/g, name: '段落X标记' },
      // 方法3: X.（数字加点）
      { pattern: /^(\d+)\./gm, name: '数字点标记' },
      // 方法4: 双换行分割
      { pattern: /\n\s*\n/g, name: '双换行分割' }
    ];

    for (const marker of segmentMarkers) {
      try {
        let parts: string[] = [];

        if (marker.name === '双换行分割') {
          // 特殊处理双换行分割
          parts = content.split(marker.pattern).filter(part => part.trim().length > 50);
        } else {
          // 使用正则表达式分割
          const matches = Array.from(content.matchAll(marker.pattern));
          if (matches.length > 0) {
            parts = content.split(marker.pattern).filter(part => part.trim().length > 20);
            // 移除第一个空元素（如果存在）
            if (parts[0] && parts[0].trim().length < 20) {
              parts.shift();
            }
          }
        }

        console.log(`🔍 使用${marker.name}分割，得到${parts.length}个段落`);

        if (parts.length >= expectedSegments) {
          const result = parts.slice(0, expectedSegments).map(part => {
            // 清理段落内容
            let cleaned = part.trim();
            // 移除段落标记
            cleaned = cleaned.replace(/^第\d+段[：:]\s*/, '');
            cleaned = cleaned.replace(/^段落\d+[：:]\s*/, '');
            cleaned = cleaned.replace(/^\d+\.\s*/, '');
            return cleaned;
          });

          console.log('✅ 段落分割成功，返回段落数:', result.length);
          return result;
        }
      } catch (error) {
        console.warn(`❌ ${marker.name}分割失败:`, error);
        continue;
      }
    }

    console.log('⚠️ 所有标记分割都失败，使用智能分割');

    // 智能分割：按句子和长度综合判断
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 10);
    const sentencesPerSegment = Math.ceil(sentences.length / expectedSegments);

    const segments: string[] = [];
    for (let i = 0; i < expectedSegments; i++) {
      const start = i * sentencesPerSegment;
      const end = Math.min((i + 1) * sentencesPerSegment, sentences.length);
      const segmentSentences = sentences.slice(start, end);

      if (segmentSentences.length > 0) {
        const segment = segmentSentences.join('。') + '。';
        segments.push(segment.trim());
      }
    }

    // 如果智能分割也失败，使用长度平均分割
    if (segments.length < expectedSegments) {
      console.log('⚠️ 智能分割失败，使用长度平均分割');
      const avgLength = Math.floor(content.length / expectedSegments);
      const fallbackSegments: string[] = [];

      for (let i = 0; i < expectedSegments; i++) {
        const start = i * avgLength;
        const end = i === expectedSegments - 1 ? content.length : (i + 1) * avgLength;
        const segment = content.substring(start, end).trim();
        if (segment.length > 0) {
          fallbackSegments.push(segment);
        }
      }

      console.log('📝 长度平均分割完成，段落数:', fallbackSegments.length);
      return fallbackSegments;
    }

    console.log('✅ 智能分割成功，段落数:', segments.length);
    return segments;
  }

  /**
   * 生成段落内容
   */
  async generateSegmentContent(
    segment: SegmentStructure,
    previousSegments: string[],
    coreMystery: CoreMystery,
    params: ShortStoryParams,
    onChunk?: (chunk: string) => void
  ): Promise<{
    success: boolean;
    content?: string;
    error?: string;
  }> {
    try {
      // 获取关联元素
      const associatedElements = await this.getAssociatedElements(params);

      // 使用专门的消息架构师构建消息
      const messages = ShortStoryMessageArchitect.buildSegmentContentMessages(
        segment,
        previousSegments,
        coreMystery,
        associatedElements,
        params.selectedACEFrameworkIds || [],
        params.targetSegments || 20
      );

      // 添加ACE框架学习消息（005指导员专用）
      if (params.selectedACEFrameworkIds && params.selectedACEFrameworkIds.length > 0) {
        const aceMessages = ACEFrameworkManager.buildACELearningMessages(
          params.selectedACEFrameworkIds,
          params.storyTone as ShortStoryMode || 'balanced'
        );
        messages.push(...aceMessages);
      }

      // 使用统一的AI流式调用方法
      const response = await this.callAIStreaming(
        messages,
        onChunk || (() => {}),
        {
          streaming: true
        }
      );

      if (!response.success) {
        return {
          success: false,
          error: response.error || '生成段落内容失败'
        };
      }

      // 处理AI返回的内容，确保换行符正确
      const processedContent = this.processAIContent(response.text || '');

      return {
        success: true,
        content: processedContent
      };

    } catch (error: any) {
      console.error('❌ 生成段落内容失败:', error);
      return {
        success: false,
        error: error.message || '生成段落内容失败'
      };
    }
  }

  /**
   * 处理AI返回的内容，确保换行符正确显示
   */
  private processAIContent(content: string): string {
    if (!content) return content;

    console.log('🔧 处理AI内容，原始长度:', content.length);
    console.log('🔧 原始内容前100字符:', content.substring(0, 100));

    // 将字面的\n字符串转换为真正的换行符
    let processedContent = content.replace(/\\n/g, '\n');

    // 清理多余的空白字符，但保留换行符
    processedContent = processedContent
      .replace(/[ \t]+/g, ' ')  // 将多个空格/制表符替换为单个空格
      .replace(/\n\s+/g, '\n')  // 移除换行符后的多余空格
      .replace(/\s+\n/g, '\n')  // 移除换行符前的多余空格
      .replace(/\n{3,}/g, '\n\n'); // 将多个连续换行符替换为双换行符

    // 清理开头和结尾的空白字符
    processedContent = processedContent.trim();

    console.log('🔧 处理后内容长度:', processedContent.length);
    console.log('🔧 处理后内容前100字符:', processedContent.substring(0, 100));
    console.log('🔧 是否包含真正的换行符:', processedContent.includes('\n'));

    return processedContent;
  }

  /**
   * 获取API配置
   */
  private async getAPIConfig() {
    try {
      // 确保API设置已初始化
      if (!this.apiSettings) {
        await this.initializeAPISettings();
      }

      // 获取API配置
      const provider = this.apiSettings?.getCurrentProvider() || 'openai';
      const model = this.apiSettings?.getCurrentModel() || 'gpt-4-turbo';
      const apiKey = this.apiSettings?.getAPIKey(provider);
      const apiEndpoint = this.apiSettings?.getAPIEndpoint(provider);
      const maxTokens = this.apiSettings?.getMaxTokens?.() || 40000;

      return {
        provider,
        model,
        apiKey,
        apiEndpoint,
        temperature: 0.8,
        maxTokens,
        streaming: true
      };
    } catch (error) {
      console.warn('获取API配置失败，使用默认值:', error);
      return {
        provider: 'openai',
        model: 'gpt-4-turbo',
        temperature: 0.8,
        maxTokens: 40000,
        streaming: true
      };
    }
  }

  /**
   * 构建关联元素上下文消息
   */
  private buildAssociatedElementsContext(elements: AssociatedElements): string | null {
    const contextParts: string[] = [];

    if (elements.characters && elements.characters.length > 0) {
      contextParts.push(`【关联人物】\n${elements.characters.map(char =>
        `- ${char.name}：${char.description || char.personality || '暂无描述'}`
      ).join('\n')}`);
    }

    if (elements.worldBuildings && elements.worldBuildings.length > 0) {
      contextParts.push(`【世界观设定】\n${elements.worldBuildings.map(wb =>
        `- ${wb.name}：${wb.description || '暂无描述'}`
      ).join('\n')}`);
    }

    if (elements.terminologies && elements.terminologies.length > 0) {
      contextParts.push(`【关键术语】\n${elements.terminologies.map(term =>
        `- ${term.name}：${term.definition || term.description || '暂无定义'}`
      ).join('\n')}`);
    }

    if (elements.outlineNodes && elements.outlineNodes.length > 0) {
      contextParts.push(`【大纲节点】\n${elements.outlineNodes.map(node =>
        `- ${node.title}：${node.description || node.content || '暂无内容'}`
      ).join('\n')}`);
    }

    return contextParts.length > 0 ? contextParts.join('\n\n') : null;
  }

  /**
   * 获取关联元素
   */
  private async getAssociatedElements(params: ShortStoryParams): Promise<AssociatedElements> {
    try {
      // 动态导入仓库
      const { characterRepository, worldBuildingRepository, terminologyRepository, chapterRepository } = await import('@/lib/db/repositories');

      // 并行获取所有关联元素
      const [characters, worldBuildings, terminologies, chapters] = await Promise.all([
        // 获取选中的人物
        params.selectedCharacterIds.length > 0
          ? Promise.all(params.selectedCharacterIds.map(id => characterRepository.getById(id)))
          : Promise.resolve([]),

        // 获取选中的世界观
        params.selectedWorldBuildingIds.length > 0
          ? Promise.all(params.selectedWorldBuildingIds.map(id => worldBuildingRepository.getById(id)))
          : Promise.resolve([]),

        // 获取选中的术语
        params.selectedTerminologyIds.length > 0
          ? Promise.all(params.selectedTerminologyIds.map(id => terminologyRepository.getById(id)))
          : Promise.resolve([]),

        // 获取书籍的所有章节（用于上下文）
        chapterRepository.getAllByBookId(params.bookId)
      ]);

      // 大纲节点暂时返回空数组，后续可以集成实际的大纲节点获取逻辑
      const outlineNodes: any[] = [];

      return {
        characters: characters.filter(Boolean),
        worldBuildings: worldBuildings.filter(Boolean),
        terminologies: terminologies.filter(Boolean),
        outlineNodes: outlineNodes.filter(Boolean),
        chapters: chapters || []
      };
    } catch (error) {
      console.error('获取关联元素失败:', error);
      return {
        characters: [],
        worldBuildings: [],
        terminologies: [],
        outlineNodes: [],
        chapters: []
      };
    }
  }



  /**
   * 解析核心悬念响应
   */
  private parseCoreMysteryResponse(response: string): CoreMystery {
    console.log('🔍 开始解析核心悬念响应，原始长度:', response.length);

    try {
      // 先尝试直接解析
      const directParsed = JSON.parse(response);
      if (this.isValidCoreMystery(directParsed)) {
        console.log('✅ 核心悬念直接解析成功');
        return this.normalizeCoreMystery(directParsed);
      }
    } catch (directError) {
      console.log('🔍 核心悬念直接解析失败，尝试提取JSON');
    }

    // 尝试多种JSON提取方式
    const extractionMethods = [
      /\{[\s\S]*\}/,
      /```json\s*(\{[\s\S]*?\})\s*```/,
      /```\s*(\{[\s\S]*?\})\s*```/,
      /(\{[\s\S]*\})/
    ];

    for (const method of extractionMethods) {
      try {
        const match = response.match(method);
        if (match) {
          const jsonText = match[1] || match[0];
          const parsed = JSON.parse(jsonText);
          if (this.isValidCoreMystery(parsed)) {
            console.log('✅ 核心悬念JSON提取成功');
            return this.normalizeCoreMystery(parsed);
          }
        }
      } catch (error) {
        continue;
      }
    }

    console.warn('⚠️ 核心悬念解析失败，使用默认值。原始响应:', response.substring(0, 200));

    // 返回默认值
    return {
      id: `mystery_${Date.now()}`,
      title: '短篇创作',
      coreQuestion: '基于用户需求的核心悬念',
      revealedHalf: '开篇异常现象',
      hiddenHalf: '背后的真相',
      finalTruth: '最终揭露',
      emotionalImpact: '情感冲击'
    };
  }

  /**
   * 验证核心悬念对象是否有效
   */
  private isValidCoreMystery(obj: any): boolean {
    return obj &&
           typeof obj.title === 'string' && obj.title.length > 0 &&
           typeof obj.coreQuestion === 'string' && obj.coreQuestion.length > 0 &&
           typeof obj.revealedHalf === 'string' && obj.revealedHalf.length > 0 &&
           typeof obj.hiddenHalf === 'string' && obj.hiddenHalf.length > 0;
  }

  /**
   * 标准化核心悬念对象
   */
  private normalizeCoreMystery(obj: any): CoreMystery {
    return {
      id: obj.id || `mystery_${Date.now()}`,
      title: obj.title || '未命名短篇',
      coreQuestion: obj.coreQuestion || '',
      revealedHalf: obj.revealedHalf || '',
      hiddenHalf: obj.hiddenHalf || '',
      finalTruth: obj.finalTruth || '',
      emotionalImpact: obj.emotionalImpact || '',
      mainCharacter: obj.mainCharacter || '',
      settingInfo: obj.settingInfo || '',
      keyTerms: obj.keyTerms || [],
      plotConnection: obj.plotConnection || ''
    };
  }

  /**
   * 解析分段结构响应
   */
  private parseSegmentStructureResponse(response: string): SegmentStructure[] {
    console.log('🔍 开始解析分段结构响应，原始长度:', response.length);

    try {
      // 先尝试直接解析
      const directParsed = JSON.parse(response);
      if (Array.isArray(directParsed) && directParsed.length > 0) {
        console.log('✅ 分段结构直接解析成功');
        return this.normalizeSegmentStructures(directParsed);
      }
    } catch (directError) {
      console.log('🔍 分段结构直接解析失败，尝试提取JSON数组');
    }

    // 尝试多种JSON数组提取方式
    const extractionMethods = [
      /\[[\s\S]*\]/,
      /```json\s*(\[[\s\S]*?\])\s*```/,
      /```\s*(\[[\s\S]*?\])\s*```/,
      /(\[[\s\S]*\])/
    ];

    for (const method of extractionMethods) {
      try {
        const match = response.match(method);
        if (match) {
          const jsonText = match[1] || match[0];
          const parsed = JSON.parse(jsonText);
          if (Array.isArray(parsed) && parsed.length > 0) {
            console.log('✅ 分段结构JSON提取成功');
            return this.normalizeSegmentStructures(parsed);
          }
        }
      } catch (error) {
        continue;
      }
    }

    console.warn('⚠️ 分段结构解析失败，使用默认值。原始响应:', response.substring(0, 200));

    // 返回默认的20段结构
    return Array.from({ length: 20 }, (_, index) => ({
      segmentNumber: index + 1,
      purpose: this.getDefaultPurpose(index + 1, 20),
      informationLevel: Math.min(Math.floor((index + 1) / 2) + 1, 10),
      tensionLevel: Math.min(Math.floor((index + 1) / 2) + 2, 10),
      paymentHookFlag: index === 7 || index === 8 || index === 9 // 第8-10段设置付费卡点
    }));
  }

  /**
   * 标准化分段结构数组
   */
  private normalizeSegmentStructures(segments: any[]): SegmentStructure[] {
    return segments.map((item, index) => ({
      segmentNumber: item.segmentNumber || (index + 1),
      purpose: item.purpose || this.getDefaultPurpose(index + 1, segments.length),
      informationLevel: Math.max(1, Math.min(10, item.informationLevel || 1)),
      tensionLevel: Math.max(1, Math.min(10, item.tensionLevel || 5)),
      content: item.content || undefined,
      cliffhanger: item.cliffhanger || undefined,
      paymentHookFlag: item.paymentHookFlag || false,
      mysteryElements: item.mysteryElements || undefined,
      revealedInfo: item.revealedInfo || undefined,
      hiddenInfo: item.hiddenInfo || undefined
    }));
  }

  /**
   * 获取默认段落目的
   */
  private getDefaultPurpose(segmentNumber: number, totalSegments: number): string {
    const position = segmentNumber / totalSegments;

    if (position <= 0.25) {
      return `铺垫阶段第${segmentNumber}段：建立悬念和人物困境`;
    } else if (position <= 0.75) {
      if (segmentNumber >= 8 && segmentNumber <= 10) {
        return `挤压阶段第${segmentNumber}段：付费卡点，最强钩子制造`;
      }
      return `挤压阶段第${segmentNumber}段：冲突升级，压迫感递增`;
    } else if (position <= 0.95) {
      return `高潮阶段第${segmentNumber}段：真相揭露，情绪爆发`;
    } else {
      return `结局阶段第${segmentNumber}段：收束情绪，留下余韵`;
    }
  }

  /**
   * 组装完整文本
   */
  private assembleFullText(segments: SegmentStructure[]): string {
    return segments
      .map(segment => `${segment.segmentNumber}\n\n${segment.content}`)
      .join('\n\n');
  }

  /**
   * 取消当前请求
   */
  cancel(): void {
    if (this.currentRequest) {
      this.currentRequest.abort();
      this.currentRequest = undefined;
    }
  }
}
