"use client";

import React, { useEffect, useState } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';

/**
 * 底部状态栏组件
 * 显示字数统计、保存状态、快捷操作等信息
 */
export const StatusBar: React.FC = () => {
  const {
    segments,
    fullText,
    editingState,
    currentStep,
    saveProgress
  } = useShortStoryStore();

  // 本地状态
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);

  // 计算统计信息
  const totalWords = segments.reduce((sum, segment) => sum + (segment.wordCount || 0), 0);
  const completedSegments = segments.filter(segment => segment.content).length;
  const totalSegments = segments.length;
  const completionPercent = totalSegments > 0 ? Math.round((completedSegments / totalSegments) * 100) : 0;

  // 调试信息 - 在开发环境中显示
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 StatusBar 阶段判断调试:', {
      totalWords,
      segments: segments.map(seg => ({
        content: seg.content ? `${seg.content.substring(0, 20)}...` : '无内容',
        wordCount: seg.wordCount,
        contentLength: seg.content?.length || 0
      })),
      阶段判断: totalWords < 150 ? '导语' : totalWords < 2000 ? '铺垫期' : '其他阶段'
    });
  }

  // 自动保存逻辑
  useEffect(() => {
    if (editingState.hasUnsavedChanges) {
      // 清除之前的定时器
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      // 设置新的自动保存定时器（5秒后保存）
      const timer = setTimeout(() => {
        saveProgress();
      }, 5000);

      setAutoSaveTimer(timer);
    }

    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
    };
  }, [editingState.hasUnsavedChanges, saveProgress]);

  // 格式化时间
  const formatTime = (date: Date | null) => {
    if (!date) return '未保存';

    // 确保date是有效的Date对象
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) return '时间无效';

    const now = new Date();
    const diff = now.getTime() - validDate.getTime();

    if (diff < 60000) return '刚刚保存';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前保存`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前保存`;
    return validDate.toLocaleDateString();
  };

  // 获取当前阶段信息 - 与RhythmControlPanel保持一致
  const getCurrentPhaseInfo = () => {
    if (totalWords === 0) return { phase: '准备阶段', color: 'text-gray-500' };
    if (totalWords < 150) return { phase: '导语', color: 'text-blue-500' };
    if (totalWords < 2000) return { phase: '铺垫期', color: 'text-green-500' };
    if (totalWords < 5000) return { phase: '爆发情绪', color: 'text-orange-500' };
    if (totalWords < 8000) return { phase: '反转', color: 'text-red-500' };
    if (totalWords < 10000) return { phase: '让读者解气', color: 'text-purple-500' };
    return { phase: '大结局', color: 'text-pink-500' };
  };

  const phaseInfo = getCurrentPhaseInfo();

  return (
    <div className="h-10 bg-white border-t border-gray-200 flex items-center justify-between px-6 text-sm">
      {/* 左侧：统计信息 */}
      <div className="flex items-center space-x-6">
        {/* 字数统计 */}
        <div className="flex items-center space-x-2">
          <span className="text-gray-500">字数:</span>
          <span className="font-medium text-gray-800">{totalWords.toLocaleString()}</span>
        </div>

        {/* 段落进度 */}
        <div className="flex items-center space-x-2">
          <span className="text-gray-500">段落:</span>
          <span className="font-medium text-gray-800">
            {completedSegments}/{totalSegments}
          </span>
          {totalSegments > 0 && (
            <span className="text-xs text-gray-500">({completionPercent}%)</span>
          )}
        </div>

        {/* 当前阶段 */}
        <div className="flex items-center space-x-2">
          <span className="text-gray-500">阶段:</span>
          <span className={`font-medium ${phaseInfo.color}`}>
            {phaseInfo.phase}
          </span>
        </div>

        {/* 预计阅读时间 */}
        {totalWords > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">阅读:</span>
            <span className="font-medium text-gray-800">
              约{Math.ceil(totalWords / 300)}分钟
            </span>
          </div>
        )}
      </div>

      {/* 中间：当前状态 */}
      <div className="flex items-center space-x-4">
        {/* 生成状态 */}
        {editingState.isGenerating && (
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin" />
            <span>AI创作中...</span>
          </div>
        )}

        {/* 当前编辑段落 */}
        {editingState.currentEditingSegment >= 0 && (
          <div className="text-gray-600">
            编辑第{editingState.currentEditingSegment + 1}段
          </div>
        )}
      </div>

      {/* 右侧：保存状态和快捷操作 */}
      <div className="flex items-center space-x-4">
        {/* 保存状态 */}
        <div className="flex items-center space-x-2">
          {editingState.hasUnsavedChanges ? (
            <>
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <span className="text-orange-600">有未保存更改</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-gray-600">
                {formatTime(editingState.lastSavedAt)}
              </span>
            </>
          )}
        </div>

        {/* 手动保存按钮 */}
        <button
          onClick={saveProgress}
          disabled={!editingState.hasUnsavedChanges}
          className={`
            px-3 py-1 rounded text-xs font-medium transition-colors
            ${editingState.hasUnsavedChanges
              ? 'bg-blue-500 text-white hover:bg-blue-600'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }
          `}
          title="Ctrl+S"
        >
          保存
        </button>

        {/* 快捷键提示 */}
        <div className="text-xs text-gray-400 hidden lg:block">
          Ctrl+S 保存 | Ctrl+Enter 生成下一段
        </div>
      </div>
    </div>
  );
};
