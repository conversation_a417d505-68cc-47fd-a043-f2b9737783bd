import { IUIComponent } from './IUIComponent';
import { Character } from '@/lib/db/dexie';

/**
 * 人物表单组件接口
 */
export interface ICharacterFormComponent extends IUIComponent {
  /**
   * 设置人物数据
   * @param character 人物数据
   */
  setCharacter(character: Character): void;
  
  /**
   * 设置书籍ID
   * @param bookId 书籍ID
   */
  setBookId(bookId: string): void;
  
  /**
   * 设置保存回调函数
   * @param handler 保存回调函数
   */
  onSave(handler: (character: Character) => void): void;
  
  /**
   * 设置取消回调函数
   * @param handler 取消回调函数
   */
  onCancel(handler: () => void): void;
  
  /**
   * 设置是否为新建
   * @param isNew 是否为新建
   */
  setIsNew(isNew: boolean): void;
  
  /**
   * 设置CSS类名
   * @param className CSS类名
   */
  setClassName(className: string): void;
}
