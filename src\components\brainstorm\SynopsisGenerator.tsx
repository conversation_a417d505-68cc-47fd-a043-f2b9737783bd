"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import './SynopsisGenerator.css';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SynopsisParams,
  SynopsisResult,
  SynopsisFramework,
  PRESET_SYNOPSIS_FRAMEWORKS,
  LENGTH_OPTIONS,
  LengthOption
} from '@/types/synopsis';
import { SynopsisPromptBuilder } from '@/factories/ai/services/brainstorm/builders/SynopsisPromptBuilder';
import { DefaultAISenderComponent } from '@/factories/ai/components/DefaultAISenderComponent';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { configService } from '@/services/configService';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';
import { BrainstormAIService } from '@/factories/ai/services/BrainstormAIService';
import {
  PRESET_KEYWORDS,
  KeywordElement as FactoryKeywordElement,
  TitleFramework as FactoryTitleFramework
} from '@/factories/ai/services/brainstorm';

// 导入书名生成器的管理组件
import {
  KeywordManagementDialog,
  FrameworkManagementDialog
} from './BookTitleGenerator';

// 导入简介分析组件
import SynopsisAnalysisDialog from './SynopsisAnalysisDialog';

// Toast通知系统
type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastMessage {
  id: string;
  type: ToastType;
  title: string;
  message: string;
  duration?: number;
}

// Toast管理Hook
const useToast = () => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const showToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = Date.now().toString();
    setToasts(prev => [...prev, { ...toast, id }]);

    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, toast.duration || 3000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  return { toasts, showToast, removeToast };
};

// Toast组件
const Toast: React.FC<{ toast: ToastMessage; onRemove: (id: string) => void }> = ({ toast, onRemove }) => {
  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-green-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      className={`${getToastStyles()} px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md`}
    >
      <span className="text-lg">{getIcon()}</span>
      <div className="flex-1">
        <div className="font-medium">{toast.title}</div>
        {toast.message && <div className="text-sm opacity-90">{toast.message}</div>}
      </div>
      <button
        onClick={() => onRemove(toast.id)}
        className="text-white hover:text-gray-200 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </motion.div>
  );
};

// Toast容器组件
const ToastContainer: React.FC<{ toasts: ToastMessage[]; onRemove: (id: string) => void }> = ({ toasts, onRemove }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast key={toast.id} toast={toast} onRemove={onRemove} />
        ))}
      </AnimatePresence>
    </div>
  );
};

// 专业SVG图标组件
const SynopsisIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.path
      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.8 }}
    />
    <motion.path
      d="M15 7h4l-4-4v4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    />
  </motion.svg>
);

const KeywordIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.rect
      x="3" y="6" width="6" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.1 }}
    />
    <motion.rect
      x="3" y="11" width="8" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2 }}
    />
    <motion.rect
      x="3" y="16" width="5" height="3" rx="1"
      fill="currentColor"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3 }}
    />
  </motion.svg>
);

// 专业操作图标组件
const HeartIcon: React.FC<{
  className?: string;
  isFavorite?: boolean;
  onClick?: () => void;
}> = ({ className = "w-5 h-5", isFavorite = false, onClick }) => (
  <motion.svg
    viewBox="0 0 24 24"
    className={className}
    fill="none"
    onClick={onClick}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.9 }}
    style={{ cursor: 'pointer' }}
  >
    <motion.path
      d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={isFavorite ? "currentColor" : "none"}
      initial={false}
      animate={{
        fill: isFavorite ? "currentColor" : "none",
        scale: isFavorite ? [1, 1.2, 1] : 1
      }}
      transition={{
        duration: 0.3,
        ease: [0.68, -0.55, 0.265, 1.55]
      }}
    />
  </motion.svg>
);

const CopyIcon: React.FC<{ className?: string; onClick?: () => void }> = ({
  className = "w-5 h-5",
  onClick
}) => (
  <motion.svg
    viewBox="0 0 24 24"
    className={className}
    fill="none"
    onClick={onClick}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.9 }}
    style={{ cursor: 'pointer' }}
  >
    <motion.rect
      x="9" y="9" width="13" height="13" rx="2" ry="2"
      stroke="currentColor"
      strokeWidth="2"
      fill="none"
      initial={{ x: 9, y: 9 }}
      whileHover={{ x: 10, y: 8 }}
      transition={{ duration: 0.2 }}
    />
    <motion.path
      d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
      stroke="currentColor"
      strokeWidth="2"
      fill="none"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </motion.svg>
);

const TrashIcon: React.FC<{ className?: string; onClick?: () => void }> = ({
  className = "w-5 h-5",
  onClick
}) => (
  <motion.svg
    viewBox="0 0 24 24"
    className={className}
    fill="none"
    onClick={onClick}
    whileHover={{
      scale: 1.1,
      x: [0, -1, 1, -1, 1, 0],
      transition: {
        x: { duration: 0.3, repeat: 1 },
        scale: { duration: 0.2 }
      }
    }}
    whileTap={{ scale: 0.9 }}
    style={{ cursor: 'pointer' }}
  >
    <motion.path
      d="M3 6h18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <motion.path
      d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <motion.line
      x1="10" y1="11" x2="10" y2="17"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <motion.line
      x1="14" y1="11" x2="14" y2="17"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </motion.svg>
);

const FrameworkIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <motion.svg viewBox="0 0 24 24" className={className} fill="none">
    <motion.path
      d="M4 4h4v4H4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4 }}
    />
    <motion.path
      d="M10 4h4v4h-4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.1 }}
    />
    <motion.path
      d="M16 4h4v4h-4z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.4, delay: 0.2 }}
    />
  </motion.svg>
);

interface SynopsisGeneratorProps {
  onClose?: () => void;
}

/**
 * 简介生成器组件
 * 专业的左右分栏布局，参考书名生成器设计
 */
const SynopsisGenerator: React.FC<SynopsisGeneratorProps> = ({ onClose }) => {
  // 生成参数状态（移除style字段）
  const [params, setParams] = useState<SynopsisParams>({
    keywords: [],
    framework: null,
    length: 'medium',
    customRequirements: ''
  });

  // 关键词管理状态 - 复用书名生成器的关键词
  const [keywordElements, setKeywordElements] = useState<FactoryKeywordElement[]>(() =>
    PRESET_KEYWORDS.map((preset, index) => ({
      ...preset,
      id: `preset_${index}`,
      frequency: 0,
      createdAt: new Date(),
    }))
  );

  // 框架管理状态 - 使用书名生成器的框架类型
  const [titleFrameworks, setTitleFrameworks] = useState<FactoryTitleFramework[]>([]);

  // 简介框架状态（保留用于显示）
  const [frameworks, setFrameworks] = useState<SynopsisFramework[]>(
    PRESET_SYNOPSIS_FRAMEWORKS.map(f => ({
      ...f,
      usageCount: 0,
      createdAt: new Date()
    }))
  );

  // 关键词输入状态
  const [keywordInput, setKeywordInput] = useState<string>('');

  // 自定义框架状态
  const [customFramework, setCustomFramework] = useState<string>('');

  // 选中的书名框架（用于管理弹窗）
  const [selectedTitleFramework, setSelectedTitleFramework] = useState<FactoryTitleFramework | null>(null);

  // 界面状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSynopsis, setGeneratedSynopsis] = useState<SynopsisResult[]>([]);
  const [historySynopsis, setHistorySynopsis] = useState<SynopsisResult[]>([]);
  const [activeTab, setActiveTab] = useState<'results' | 'history' | 'analysis'>('results');

  // 弹窗状态
  const [showKeywordDialog, setShowKeywordDialog] = useState(false);
  const [showFrameworkDialog, setShowFrameworkDialog] = useState(false);
  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [selectedSynopsisForSections, setSelectedSynopsisForSections] = useState<SynopsisResult | null>(null);

  // Toast通知系统
  const { toasts, showToast, removeToast } = useToast();

  // 特别要求状态
  const [customRequirements, setCustomRequirements] = useState<string>('');

  // 初始化AI服务
  const aiService = useMemo(() => {
    const { AIServiceFactory, AIServiceType } = require('@/services/ai/BaseAIService');
    return AIServiceFactory.getService(AIServiceType.BRAINSTORM);
  }, []);

  // BrainstormAI服务实例（用于工具调用）
  const brainstormAIService = useMemo(() => new BrainstormAIService(), []);

  // 从localStorage加载数据
  useEffect(() => {
    // 加载历史记录
    const savedHistory = localStorage.getItem('synopsis-history');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setHistorySynopsis(history);
      } catch (error) {
        console.error('加载简介历史失败:', error);
      }
    }

    // 加载关键词元素（复用书名生成器的）
    const savedKeywords = localStorage.getItem('book-title-keywords');
    if (savedKeywords) {
      try {
        const keywords = JSON.parse(savedKeywords);
        setKeywordElements(keywords);
      } catch (error) {
        console.error('加载关键词失败:', error);
      }
    }

    // 加载简介框架数据（使用独立的存储，不与书名框架混合）
    const savedSynopsisFrameworks = localStorage.getItem('synopsis-frameworks');
    if (savedSynopsisFrameworks) {
      try {
        const synopsisFrameworks = JSON.parse(savedSynopsisFrameworks);
        console.log('🔍 从localStorage加载简介框架:', {
          synopsisFrameworksCount: synopsisFrameworks.length,
          frameworks: synopsisFrameworks.map((f: any) => ({ id: f.id, name: f.name, hasStructureAnalysis: !!(f as any).structureAnalysis }))
        });
        setTitleFrameworks(synopsisFrameworks);
      } catch (error) {
        console.error('加载简介框架失败:', error);
      }
    }

    // 加载特别要求
    const savedRequirements = localStorage.getItem('synopsis-custom-requirements');
    if (savedRequirements) {
      setCustomRequirements(savedRequirements);
    }
  }, []);

  // 保存到历史记录
  const saveToHistory = useCallback((synopsis: SynopsisResult[]) => {
    const updatedHistory = [...synopsis, ...historySynopsis].slice(0, 50); // 最多保存50条
    setHistorySynopsis(updatedHistory);
    localStorage.setItem('synopsis-history', JSON.stringify(updatedHistory));
  }, [historySynopsis]);

  // 保存特别要求
  useEffect(() => {
    localStorage.setItem('synopsis-custom-requirements', customRequirements);
  }, [customRequirements]);

  // 同步框架选择到生成参数
  useEffect(() => {
    if (selectedTitleFramework) {
      // 检查是否有结构分析数据
      const hasStructureAnalysis = (selectedTitleFramework as any).structureAnalysis;

      console.log('🔍 框架结构分析检查:', {
        frameworkName: selectedTitleFramework.name,
        hasStructureAnalysis: !!hasStructureAnalysis,
        structureData: hasStructureAnalysis
      });

      // 将TitleFramework转换为SynopsisFramework，保留所有分析数据
      const synopsisFramework: SynopsisFramework = {
        id: selectedTitleFramework.id,
        name: selectedTitleFramework.name,
        description: selectedTitleFramework.description,
        pattern: selectedTitleFramework.pattern, // 保留模式信息
        structure: hasStructureAnalysis ? [
          {
            name: '开局',
            description: hasStructureAnalysis.opening,
            placeholder: hasStructureAnalysis.opening,
            wordRange: [30, 50]
          },
          {
            name: '发展',
            description: hasStructureAnalysis.development,
            placeholder: hasStructureAnalysis.development,
            wordRange: [50, 80]
          },
          {
            name: '结尾',
            description: hasStructureAnalysis.ending,
            placeholder: hasStructureAnalysis.ending,
            wordRange: [30, 50]
          }
        ] : [],
        examples: selectedTitleFramework.examples || [],
        category: 'classic',
        usageCount: 0,
        effectiveness: selectedTitleFramework.effectiveness || 8,
        createdAt: new Date(),
        // 🔥 新增：传递写作技巧分析数据
        ...(selectedTitleFramework.writingTechniques && {
          writingTechniques: selectedTitleFramework.writingTechniques
        }),
        ...(selectedTitleFramework.styleCharacteristics && {
          styleCharacteristics: selectedTitleFramework.styleCharacteristics
        }),
        ...(selectedTitleFramework.reusableTemplates && {
          reusableTemplates: selectedTitleFramework.reusableTemplates
        }),
        ...(selectedTitleFramework.techniqueAnalysisConfidence && {
          techniqueAnalysisConfidence: selectedTitleFramework.techniqueAnalysisConfidence
        })
      } as any; // 使用类型断言处理扩展字段

      console.log('✅ 框架转换完成:', {
        frameworkName: synopsisFramework.name,
        structureCount: synopsisFramework.structure.length,
        structure: synopsisFramework.structure.map(s => ({ name: s.name, description: s.description })),
        // 🔥 新增：显示写作技巧数据传递情况
        hasWritingTechniques: !!(synopsisFramework as any).writingTechniques?.length,
        writingTechniquesCount: (synopsisFramework as any).writingTechniques?.length || 0,
        hasStyleCharacteristics: !!(synopsisFramework as any).styleCharacteristics,
        hasReusableTemplates: !!(synopsisFramework as any).reusableTemplates?.length,
        reusableTemplatesCount: (synopsisFramework as any).reusableTemplates?.length || 0,
        techniqueAnalysisConfidence: (synopsisFramework as any).techniqueAnalysisConfidence
      });

      setParams(prev => ({ ...prev, framework: synopsisFramework }));
    } else {
      setParams(prev => ({ ...prev, framework: null }));
    }
  }, [selectedTitleFramework]);

  // 保存关键词到localStorage
  const saveKeywordsToStorage = useCallback((keywords: FactoryKeywordElement[]) => {
    localStorage.setItem('book-title-keywords', JSON.stringify(keywords));
  }, []);

  // 保存简介框架到独立存储
  const saveFrameworksToStorage = useCallback((frameworks: FactoryTitleFramework[]) => {
    // 简介框架使用独立存储，不与书名框架混合
    localStorage.setItem('synopsis-frameworks', JSON.stringify(frameworks));
    console.log('💾 简介框架已保存到独立存储:', frameworks.length);
  }, []);

  // 添加关键词元素
  const addKeywordElement = useCallback(() => {
    if (keywordInput.trim()) {
      const newKeyword: FactoryKeywordElement = {
        id: `keyword_${Date.now()}`,
        text: keywordInput.trim(),
        frequency: 1,
        hotness: 5,
        tags: [],
        createdAt: new Date()
      };

      const existingIndex = keywordElements.findIndex(k => k.text === newKeyword.text);
      let updatedKeywords: FactoryKeywordElement[];

      if (existingIndex >= 0) {
        updatedKeywords = [...keywordElements];
        updatedKeywords[existingIndex].frequency += 1;
      } else {
        updatedKeywords = [...keywordElements, newKeyword];
      }

      setKeywordElements(updatedKeywords);
      saveKeywordsToStorage(updatedKeywords);
      setKeywordInput('');
    }
  }, [keywordInput, keywordElements, saveKeywordsToStorage]);

  // 移除关键词元素
  const removeKeywordElement = useCallback((id: string) => {
    const updatedKeywords = keywordElements.filter(k => k.id !== id);
    setKeywordElements(updatedKeywords);
    saveKeywordsToStorage(updatedKeywords);

    // 同时从选中关键词中移除
    const removedKeyword = keywordElements.find(k => k.id === id);
    if (removedKeyword) {
      setParams(prev => ({
        ...prev,
        keywords: prev.keywords.filter(k => k !== removedKeyword.text)
      }));
    }
  }, [keywordElements, saveKeywordsToStorage]);

  // 处理分析完成
  const handleAnalysisComplete = useCallback((newKeywords: FactoryKeywordElement[], newFrameworks: FactoryTitleFramework[]) => {
    // 更新关键词
    if (newKeywords.length > 0) {
      const updatedKeywords = [...keywordElements, ...newKeywords];
      setKeywordElements(updatedKeywords);
      saveKeywordsToStorage(updatedKeywords);

      showToast({
        type: 'success',
        title: '关键词更新',
        message: `成功添加 ${newKeywords.length} 个新关键词`
      });
    }

    // 更新框架
    if (newFrameworks.length > 0) {
      const updatedFrameworks = [...titleFrameworks, ...newFrameworks];
      setTitleFrameworks(updatedFrameworks);
      saveFrameworksToStorage(updatedFrameworks);

      showToast({
        type: 'success',
        title: '框架更新',
        message: `成功添加 ${newFrameworks.length} 个新框架`
      });
    }

    if (newKeywords.length === 0 && newFrameworks.length === 0) {
      showToast({
        type: 'info',
        title: '分析完成',
        message: '没有发现新的关键词或框架'
      });
    }
  }, [keywordElements, titleFrameworks, saveKeywordsToStorage, saveFrameworksToStorage, showToast]);

  // 使用工具调用生成简介
  const handleGenerateWithToolCall = useCallback(async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);

      // 构建简介生成请求
      const keywords = params.keywords.join('、');
      const framework = params.framework ? params.framework.name : '';
      const requirements = customRequirements.trim();

      let userInput = `请生成一个小说简介`;

      if (keywords) {
        userInput += `，关键词：${keywords}`;
      }

      if (framework) {
        userInput += `，使用框架：${framework}`;
      }

      if (requirements) {
        userInput += `，特别要求：${requirements}`;
      }

      console.log('🔧 使用工具调用生成简介:', userInput);

      // 调用BrainstormAI服务（会自动检测简介关键词并使用工具调用）
      const response = await brainstormAIService.sendChatMessage(userInput);

      if (response.toolCallResult && response.toolCallResult.synopsis) {
        // 处理工具调用结果
        const newSynopsis: SynopsisResult = {
          id: `synopsis_${Date.now()}`,
          content: response.toolCallResult.synopsis,
          aiScore: 8.5,
          reason: '通过AI工具调用生成',
          parameters: params,
          generatedAt: new Date(),
          isFavorite: false,
          wordCount: response.toolCallResult.synopsis.length,
        };

        setGeneratedSynopsis([newSynopsis]);
        saveToHistory([newSynopsis]);
        setActiveTab('results');

        showToast({
          type: 'success',
          title: '生成成功',
          message: `已通过工具调用生成${newSynopsis.wordCount}字的优质简介`
        });
      } else {
        // 如果没有工具调用结果，使用普通响应
        if (response.content) {
          const newSynopsis: SynopsisResult = {
            id: `synopsis_${Date.now()}`,
            content: response.content,
            aiScore: 8.0,
            reason: '通过AI聊天生成',
            parameters: params,
            generatedAt: new Date(),
            isFavorite: false,
            wordCount: response.content.length,
          };

          setGeneratedSynopsis([newSynopsis]);
          saveToHistory([newSynopsis]);
          setActiveTab('results');

          showToast({
            type: 'success',
            title: '生成成功',
            message: `已生成${newSynopsis.wordCount}字的简介`
          });
        } else {
          throw new Error('AI响应为空');
        }
      }
    } catch (error) {
      console.error('简介生成失败:', error);
      showToast({
        type: 'error',
        title: '生成失败',
        message: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, params, customRequirements, brainstormAIService, saveToHistory, showToast]);

  // 生成简介（原有方法）
  const handleGenerate = useCallback(async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);

      // 构建生成参数（移除style字段）
      const generationParams: SynopsisParams = {
        ...params,
        customRequirements: customRequirements.trim() || undefined
      };

      // 获取API配置
      console.log('🔧 获取API配置...');
      const aiConfig = await configService.getAIConfig();
      console.log('📋 API配置详情:', {
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        model: aiConfig.model,
        hasApiKey: !!aiConfig.apiKey
      });

      // 构建提示词
      const messages = SynopsisPromptBuilder.buildSynopsisGenerationPrompt(generationParams);

      console.log('🔍 开始AI简介生成...');

      // 调用AI服务
      const response = await aiSender.sendRequest('', {
        messages: messages,
        temperature: aiConfig.temperature || 0.7,
        maxTokens: aiConfig.maxTokens || 2000
      });

      if (response.success && response.text) {
        console.log('📝 AI原始响应:', response.text);

        // 使用AIResponseParser解析响应
        const defaultValue = { synopsis: null };
        const aiResponse = AIResponseParser.parseJSON(response.text, defaultValue) as any;

        console.log('🔍 解析后的响应:', aiResponse);

        if (aiResponse.synopsis) {
            const synopsisData = aiResponse.synopsis;
            const newSynopsis: SynopsisResult = {
              id: `synopsis_${Date.now()}`,
              content: (synopsisData.content || '').replace(/\n/g, '\n'),
              aiScore: synopsisData.score || 8.0,
              reason: (synopsisData.reason || '生成成功').replace(/\\n/g, '\n'),
              parameters: generationParams,
              generatedAt: new Date(),
              isFavorite: false,
              wordCount: synopsisData.wordCount || synopsisData.content?.length || 0,
              sections: synopsisData.sections ? synopsisData.sections.map((section: any) => ({
                ...section,
                content: section.content.replace(/\\n/g, '\n')
              })) : undefined
            };

            setGeneratedSynopsis([newSynopsis]);
            saveToHistory([newSynopsis]);

            // 更新框架使用次数
            if (params.framework) {
              setFrameworks(prev => prev.map(f =>
                f.id === params.framework!.id
                  ? { ...f, usageCount: f.usageCount + 1, lastUsedAt: new Date() }
                  : f
              ));
            }

            setActiveTab('results');

            showToast({
              type: 'success',
              title: '生成成功',
              message: `已生成${newSynopsis.wordCount}字的优质简介`
            });
        } else {
          throw new Error('AI响应格式错误：缺少synopsis字段');
        }
      } else {
        throw new Error(response.error || '简介生成失败');
      }
    } catch (error) {
      console.error('简介生成失败:', error);
      showToast({
        type: 'error',
        title: '生成失败',
        message: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, params, customRequirements, aiSender, saveToHistory, showToast]);

  // 切换收藏状态
  const toggleFavorite = useCallback((synopsisId: string) => {
    // 更新生成结果中的收藏状态
    setGeneratedSynopsis(prev =>
      prev.map(synopsis =>
        synopsis.id === synopsisId
          ? { ...synopsis, isFavorite: !synopsis.isFavorite }
          : synopsis
      )
    );

    // 同步更新历史记录中的收藏状态
    setHistorySynopsis(prev => {
      const updated = prev.map(synopsis =>
        synopsis.id === synopsisId
          ? { ...synopsis, isFavorite: !synopsis.isFavorite }
          : synopsis
      );
      localStorage.setItem('synopsis-history', JSON.stringify(updated));
      return updated;
    });

    const synopsis = [...generatedSynopsis, ...historySynopsis].find(s => s.id === synopsisId);
    if (synopsis) {
      showToast({
        type: synopsis.isFavorite ? 'info' : 'success',
        title: synopsis.isFavorite ? '已取消收藏' : '收藏成功',
        message: `简介已${synopsis.isFavorite ? '取消收藏' : '收藏'}`
      });
    }
  }, [generatedSynopsis, historySynopsis, showToast]);

  // 复制简介到剪贴板
  const copyToClipboard = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      showToast({
        type: 'success',
        title: '复制成功',
        message: '简介内容已复制到剪贴板'
      });
    } catch (error) {
      console.error('复制失败:', error);
      showToast({
        type: 'error',
        title: '复制失败',
        message: '无法访问剪贴板，请手动复制'
      });
    }
  }, [showToast]);

  // 关键词高亮功能
  const highlightKeywords = useCallback((text: string, keywords: string[]): string => {
    let highlightedText = text;
    // 先处理换行符，转换为<br>标签
    highlightedText = highlightedText.replace(/\n/g, '<br>');
    // 然后处理关键词高亮
    keywords.forEach(keyword => {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        '<span class="bg-yellow-200 text-yellow-800 px-1 rounded">$1</span>'
      );
    });
    return highlightedText;
  }, []);

  // 删除简介
  const deleteSynopsis = useCallback((synopsisId: string) => {
    // 从生成结果中删除
    setGeneratedSynopsis(prev => prev.filter(s => s.id !== synopsisId));

    // 从历史记录中删除
    setHistorySynopsis(prev => {
      const updated = prev.filter(s => s.id !== synopsisId);
      localStorage.setItem('synopsis-history', JSON.stringify(updated));
      return updated;
    });

    showToast({
      type: 'success',
      title: '删除成功',
      message: '简介已删除'
    });
  }, [showToast]);

  // 清空所有历史记录
  const clearAllHistory = useCallback(() => {
    setHistorySynopsis([]);
    localStorage.removeItem('synopsis-history');
    showToast({
      type: 'success',
      title: '清空成功',
      message: '所有历史记录已清空'
    });
  }, [showToast]);

  // 删除确认状态
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 简介卡片组件
  const SynopsisCard: React.FC<{
    synopsis: SynopsisResult;
    index: number;
    onToggleFavorite: (id: string) => void;
    onCopy: (content: string) => void;
    onDelete: (id: string) => void;
  }> = ({ synopsis, index, onToggleFavorite, onCopy, onDelete }) => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-shadow border border-gray-100"
      >
        {/* 简介内容 */}
        <div className="space-y-4">
          {/* 始终显示完整的简介内容 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <p
              className="text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{
                __html: highlightKeywords(synopsis.content, synopsis.parameters.keywords)
              }}
            />
          </motion.div>

          {/* 如果有段落信息，显示查看详细分解按钮 */}
          {synopsis.sections && synopsis.sections.length > 0 && (
            <div className="mt-3">
              <button
                onClick={() => {
                  setSelectedSynopsisForSections(synopsis);
                  setShowSectionDialog(true);
                }}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 transition-colors"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                查看段落分解 ({synopsis.sections.length}段)
              </button>
            </div>
          )}
        </div>

        {/* 框架和参数信息 */}
        <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
          {synopsis.parameters.framework && (
            <div className="flex items-center space-x-1">
              <span>框架：</span>
              <span className="font-medium text-purple-600">
                {synopsis.parameters.framework.name}
              </span>
            </div>
          )}
          <div className="flex items-center space-x-1">
            <span>长度：</span>
            <span className="font-medium">
              {LENGTH_OPTIONS.find(l => l.id === synopsis.parameters.length)?.name}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span>字数：</span>
            <span className="font-medium">{synopsis.wordCount}</span>
          </div>
        </div>

        {/* 操作按钮组 */}
        <div className="mt-6 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">评分：</span>
            <div className="flex space-x-1">
              {[...Array(5)].map((_, i) => (
                <span
                  key={i}
                  className={`text-sm ${
                    i < Math.round(synopsis.aiScore / 2) ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                >
                  ⭐
                </span>
              ))}
            </div>
            <span className="text-sm font-medium text-gray-600">
              {synopsis.aiScore.toFixed(1)}
            </span>
          </div>

          <div className="flex space-x-2">
            <motion.button
              onClick={() => onToggleFavorite(synopsis.id)}
              className={`p-2 rounded-full transition-colors ${
                synopsis.isFavorite
                  ? 'text-red-500 bg-red-50'
                  : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
              }`}
              title={synopsis.isFavorite ? '取消收藏' : '收藏'}
              aria-label={synopsis.isFavorite ? '取消收藏' : '收藏'}
            >
              <HeartIcon
                isFavorite={synopsis.isFavorite}
                onClick={() => onToggleFavorite(synopsis.id)}
              />
            </motion.button>
            <motion.button
              onClick={() => onCopy(synopsis.content)}
              className="p-2 rounded-full text-gray-400 hover:text-blue-500 hover:bg-blue-50 transition-colors"
              title="复制内容"
              aria-label="复制内容"
            >
              <CopyIcon />
            </motion.button>
            <motion.button
              onClick={() => {
                if (window.confirm('确定要删除这条简介吗？')) {
                  onDelete(synopsis.id);
                }
              }}
              className="p-2 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors"
              title="删除简介"
              aria-label="删除简介"
            >
              <TrashIcon />
            </motion.button>
          </div>
        </div>

        {/* 创作理由 */}
        {synopsis.reason && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-500 mb-1">创作理由：</div>
            <div
              className="text-sm text-gray-600 prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{
                __html: synopsis.reason
                  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                  .replace(/\*(.*?)\*/g, '<em>$1</em>')
                  .replace(/`(.*?)`/g, '<code class="bg-gray-200 px-1 rounded text-xs">$1</code>')
                  .replace(/\n/g, '<br>')
              }}
            />
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className="flex h-full bg-white">
      {/* Toast通知容器 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
      
      {/* 左侧参数设置区域 (35%宽度) */}
      <div className="w-[35%] border-r border-gray-200 bg-gray-50 flex flex-col h-full">
        {/* 参数设置内容 */}
        <div className="flex-1 min-h-0 p-6 overflow-y-auto">
          <div className="space-y-6">
            {/* 关键词管理按钮 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">关键词元素</h3>
              <motion.button
                onClick={() => setShowKeywordDialog(true)}
                className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-all duration-300 bg-white hover:bg-blue-50 group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-blue-500 group-hover:rotate-12 transition-transform duration-300">
                      <KeywordIcon className="w-6 h-6" />
                    </div>
                    <span className="font-medium text-gray-700">管理关键词</span>
                  </div>
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-sm font-medium">
                    {params.keywords.length}
                  </span>
                </div>
              </motion.button>

              {/* 选中关键词的简要显示 */}
              {params.keywords.length > 0 && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <div className="text-xs text-gray-500 mb-2">已选关键词：</div>
                  <div className="flex flex-wrap gap-1">
                    {params.keywords.slice(0, 5).map((keyword) => (
                      <span
                        key={keyword}
                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs"
                      >
                        {keyword}
                      </span>
                    ))}
                    {params.keywords.length > 5 && (
                      <span className="px-2 py-1 bg-gray-200 text-gray-600 rounded text-xs">
                        +{params.keywords.length - 5}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 框架选择按钮 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">简介框架</h3>
              <motion.button
                onClick={() => setShowFrameworkDialog(true)}
                className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 transition-all duration-300 bg-white hover:bg-purple-50 group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-purple-500 group-hover:animate-pulse">
                      <FrameworkIcon className="w-6 h-6" />
                    </div>
                    <span className="font-medium text-gray-700">选择框架</span>
                  </div>
                  <span className="text-sm text-gray-500 max-w-32 truncate">
                    {params.framework?.name || '未选择'}
                  </span>
                </div>
              </motion.button>

              {/* 选中框架的简要显示 */}
              {params.framework && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <div className="text-xs text-gray-500 mb-2">当前框架：</div>
                  <div className="text-sm font-medium text-gray-700">
                    {params.framework.name}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {params.framework.structure.map(s => s.name).join(' → ')}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {params.framework.description}
                  </div>
                </div>
              )}
            </div>

            {/* 简介分析按钮 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">简介分析</h3>
              <motion.button
                onClick={() => setShowAnalysisDialog(true)}
                className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-emerald-500 transition-all duration-300 bg-white hover:bg-emerald-50 group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-emerald-500 group-hover:rotate-12 transition-transform duration-300">
                      🔍
                    </div>
                    <span className="font-medium text-gray-700">分析简介</span>
                  </div>
                  <span className="text-xs text-gray-500">
                    提取关键词和框架
                  </span>
                </div>
              </motion.button>
              <div className="mt-2 text-xs text-gray-500">
                分析现有简介，自动提取关键词和框架模式到词库
              </div>
            </div>

            {/* 长度控制滑块 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">简介长度</h3>
              <div className="space-y-3">
                <div className="relative">
                  <input
                    type="range"
                    min="0"
                    max="2"
                    value={LENGTH_OPTIONS.findIndex(l => l.id === params.length)}
                    onChange={(e) => {
                      const index = parseInt(e.target.value);
                      setParams(prev => ({ ...prev, length: LENGTH_OPTIONS[index].id }));
                    }}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    {LENGTH_OPTIONS.map((option) => (
                      <span key={option.id} className={params.length === option.id ? 'text-green-600 font-medium' : ''}>
                        {option.name}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  当前设置：{LENGTH_OPTIONS.find(l => l.id === params.length)?.description}
                  （{LENGTH_OPTIONS.find(l => l.id === params.length)?.wordRange.join('-')}字）
                </div>
              </div>
            </div>

            {/* 特别要求输入 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">特别要求</h3>
              <div className="space-y-3">
                <textarea
                  value={customRequirements}
                  onChange={(e) => setCustomRequirements(e.target.value)}
                  placeholder="描述你的特别要求，如：风格偏好、目标读者、特殊元素、情节要求等..."
                  className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none text-sm"
                  maxLength={500}
                />
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>为AI提供更精准的创作指导</span>
                  <span>{customRequirements.length}/500</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部生成按钮 */}
        <div className="flex-shrink-0 p-6 border-t border-gray-200 bg-white">
          <motion.button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="w-full py-4 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-xl font-medium text-lg disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
            whileHover={{ scale: isGenerating ? 1 : 1.02 }}
            whileTap={{ scale: isGenerating ? 1 : 0.98 }}
          >
            {isGenerating ? (
              <div className="flex items-center justify-center space-x-3">
                <motion.div
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>正在生成简介...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-3">
                <div className="text-white group-hover:scale-110 transition-transform duration-300">
                  <SynopsisIcon className="w-6 h-6" />
                </div>
                <span>生成创意简介</span>
              </div>
            )}
          </motion.button>
        </div>
      </div>

      {/* 右侧结果展示区域 (65%宽度) */}
      <div className="w-[65%] p-6 flex flex-col">
        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('results')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'results'
                ? 'bg-white text-green-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            生成结果 {generatedSynopsis.length > 0 && `(${generatedSynopsis.length})`}
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'history'
                ? 'bg-white text-green-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            历史记录 {historySynopsis.length > 0 && `(${historySynopsis.length})`}
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              activeTab === 'analysis'
                ? 'bg-white text-green-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            数据分析
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 min-h-0 overflow-y-auto">
          <AnimatePresence mode="wait">
            {activeTab === 'results' ? (
              <motion.div
                key="results"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                {generatedSynopsis.length > 0 ? (
                  <div className="space-y-4">
                    {generatedSynopsis.map((synopsis, index) => (
                      <SynopsisCard
                        key={synopsis.id}
                        synopsis={synopsis}
                        index={index}
                        onToggleFavorite={toggleFavorite}
                        onCopy={copyToClipboard}
                        onDelete={deleteSynopsis}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📝</div>
                      <p className="text-lg mb-2">等待生成简介</p>
                      <p className="text-sm">设置参数后点击生成按钮开始创作</p>
                    </div>
                  </div>
                )}
              </motion.div>
            ) : activeTab === 'history' ? (
              <motion.div
                key="history"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex flex-col h-full"
              >
                {historySynopsis.length > 0 ? (
                  <>
                    {/* 历史记录操作栏 */}
                    <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-600">
                          共 {historySynopsis.length} 条记录
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <motion.button
                          onClick={() => setShowClearConfirm(true)}
                          className="px-3 py-1.5 text-sm text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 rounded-lg transition-colors"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          清空历史
                        </motion.button>
                      </div>
                    </div>

                    {/* 历史记录列表 */}
                    <div className="flex-1 overflow-y-auto space-y-4">
                      {historySynopsis.map((synopsis, index) => (
                        <SynopsisCard
                          key={synopsis.id}
                          synopsis={synopsis}
                          index={index}
                          onToggleFavorite={toggleFavorite}
                          onCopy={copyToClipboard}
                          onDelete={deleteSynopsis}
                        />
                      ))}
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📚</div>
                      <p className="text-lg mb-2">暂无历史记录</p>
                      <p className="text-sm">生成的简介会自动保存到这里</p>
                    </div>
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="analysis"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                <div className="space-y-6">
                  {/* 生成统计 */}
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">生成统计</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {generatedSynopsis.length + historySynopsis.length}
                        </div>
                        <div className="text-sm text-gray-500">总生成数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {[...generatedSynopsis, ...historySynopsis].filter(s => s.isFavorite).length}
                        </div>
                        <div className="text-sm text-gray-500">收藏数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {[...generatedSynopsis, ...historySynopsis].reduce((sum, s) => sum + s.wordCount, 0)}
                        </div>
                        <div className="text-sm text-gray-500">总字数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {[...generatedSynopsis, ...historySynopsis].length > 0
                            ? ([...generatedSynopsis, ...historySynopsis].reduce((sum, s) => sum + s.aiScore, 0) / [...generatedSynopsis, ...historySynopsis].length).toFixed(1)
                            : '0.0'
                          }
                        </div>
                        <div className="text-sm text-gray-500">平均评分</div>
                      </div>
                    </div>
                  </div>

                  {/* 框架使用统计 */}
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">框架使用统计</h3>
                    <div className="space-y-3">
                      {frameworks.map((framework) => {
                        const usageCount = [...generatedSynopsis, ...historySynopsis].filter(
                          s => s.parameters.framework?.id === framework.id
                        ).length;
                        const maxUsage = Math.max(...frameworks.map(f =>
                          [...generatedSynopsis, ...historySynopsis].filter(
                            s => s.parameters.framework?.id === f.id
                          ).length
                        ), 1);
                        const percentage = (usageCount / maxUsage) * 100;

                        return (
                          <div key={framework.id} className="flex items-center space-x-3">
                            <div className="w-24 text-sm text-gray-600 truncate">
                              {framework.name}
                            </div>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <div className="w-8 text-sm text-gray-500 text-right">
                              {usageCount}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>


                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Toast通知容器 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* 关键词管理弹窗 */}
      <KeywordManagementDialog
        isOpen={showKeywordDialog}
        onClose={() => setShowKeywordDialog(false)}
        keywordElements={keywordElements}
        selectedKeywords={params.keywords}
        onSelectionChange={(keywords) => setParams(prev => ({ ...prev, keywords }))}
        onAddKeyword={addKeywordElement}
        onRemoveKeyword={removeKeywordElement}
        keywordInput={keywordInput}
        onKeywordInputChange={setKeywordInput}
      />

      {/* 框架管理弹窗 */}
      <FrameworkManagementDialog
        isOpen={showFrameworkDialog}
        onClose={() => setShowFrameworkDialog(false)}
        frameworks={titleFrameworks}
        selectedFramework={selectedTitleFramework}
        onFrameworkSelect={setSelectedTitleFramework}
        customFramework={customFramework}
        onCustomFrameworkChange={setCustomFramework}
        onSaveFramework={(newFramework) => {
          const updatedFrameworks = [...titleFrameworks, newFramework];
          setTitleFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
        }}
        onDeleteFramework={(frameworkId) => {
          const updatedFrameworks = titleFrameworks.filter(f => f.id !== frameworkId);
          setTitleFrameworks(updatedFrameworks);
          saveFrameworksToStorage(updatedFrameworks);
          if (selectedTitleFramework?.id === frameworkId) {
            setSelectedTitleFramework(null);
          }
        }}
      />

      {/* 简介分析弹窗 */}
      <SynopsisAnalysisDialog
        isOpen={showAnalysisDialog}
        onClose={() => setShowAnalysisDialog(false)}
        onAnalysisComplete={handleAnalysisComplete}
        existingKeywords={keywordElements}
        existingFrameworks={titleFrameworks}
      />

      {/* 段落详细弹窗 */}
      {showSectionDialog && selectedSynopsisForSections && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          >
            {/* 弹窗头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">段落详细分解</h3>
                <p className="text-sm text-gray-500 mt-1">查看简介的结构化段落内容</p>
              </div>
              <button
                onClick={() => {
                  setShowSectionDialog(false);
                  setSelectedSynopsisForSections(null);
                }}
                className="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">
                {selectedSynopsisForSections.sections?.map((section, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-50 rounded-xl p-4"
                  >
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <h4 className="text-lg font-medium text-gray-900">{section.name}</h4>
                    </div>
                    <p
                      className="text-gray-700 leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: highlightKeywords(section.content, selectedSynopsisForSections.parameters.keywords)
                      }}
                    />
                  </motion.div>
                ))}
              </div>

              {/* 完整简介对比 */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="text-lg font-medium text-gray-900 mb-4">完整简介</h4>
                <div className="bg-blue-50 rounded-xl p-4">
                  <p
                    className="text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: highlightKeywords(selectedSynopsisForSections.content, selectedSynopsisForSections.parameters.keywords)
                    }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* 清空历史记录确认弹窗 */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4"
          >
            <div className="text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">确认清空历史记录</h3>
              <p className="text-gray-600 mb-6">
                此操作将删除所有历史记录，包括 {historySynopsis.length} 条简介记录。此操作不可撤销。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    clearAllHistory();
                    setShowClearConfirm(false);
                  }}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  确认清空
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default SynopsisGenerator;
