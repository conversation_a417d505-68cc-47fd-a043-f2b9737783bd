"use client";

import React from 'react';
import { AIRewriteDialog } from '@/factories/ui/components/AIRewritePanel';

interface AIRewriteAdapterProps {
  selectedText?: string;
  beforeContext?: string;
  afterContext?: string;
  onRewritten?: (rewrittenText: string) => void;
  isOpen?: boolean;
  onClose?: () => void;
  bookId?: string;
}

/**
 * AI选中改写适配器组件
 * 用于将AI选中改写组件集成到React应用中
 */
export const AIRewriteAdapter: React.FC<AIRewriteAdapterProps> = ({
  selectedText = '',
  beforeContext = '',
  afterContext = '',
  onRewritten,
  isOpen = true,
  onClose = () => {},
  bookId = ''
}) => {
  return (
    <AIRewriteDialog
      isOpen={isOpen}
      onClose={onClose}
      onRewritten={onRewritten || (() => {})}
      selectedText={selectedText}
      beforeContext={beforeContext}
      afterContext={afterContext}
      bookId={bookId}
    />
  );
};

/**
 * 创建AI改写适配器
 * @returns AI改写适配器
 */
export function createAIRewriteAdapter() {
  return {
    /**
     * 创建AI改写对话框
     * @param props 对话框属性
     * @returns AI改写对话框组件
     */
    createAIRewriteDialog: (props: {
      isOpen: boolean;
      onClose: () => void;
      selectedText: string;
      beforeContext?: string;
      afterContext?: string;
      onRewritten?: (rewrittenText: string) => void;
      bookId?: string;
    }) => {
      return (
        <AIRewriteAdapter
          isOpen={props.isOpen}
          onClose={props.onClose}
          selectedText={props.selectedText}
          beforeContext={props.beforeContext}
          afterContext={props.afterContext}
          onRewritten={props.onRewritten}
          bookId={props.bookId}
        />
      );
    }
  };
}

export default createAIRewriteAdapter;
