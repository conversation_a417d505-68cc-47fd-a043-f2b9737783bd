"use client";

import React, { useState } from 'react';
import { Panel, useReactFlow } from 'reactflow';
import LayoutControlPanel, { LayoutConfig } from './LayoutControlPanel';
import StyleControlPanel from '../styles/StyleControlPanel';

interface CanvasMenuProps {
  layoutMode: 'tree' | 'force' | 'radial' | 'grid' | 'horizontal' | 'vertical';
  setLayoutMode: (mode: 'tree' | 'force' | 'radial' | 'grid' | 'horizontal' | 'vertical') => void;
  handleAutoLayout: (config?: LayoutConfig, forceApply?: boolean) => void;
  handlePreviewLayout: (config: LayoutConfig) => void;
  cancelPreviewLayout: () => void;
  isPreviewMode: boolean;
  currentLayoutConfig: LayoutConfig;
  scissorsModeActive: boolean;
  toggleScissorsMode: () => void;
  showShortcuts: boolean;
  setShowShortcuts: (show: boolean) => void;
  selectedNodeId: string | null;
  handleAddChild: (parentId: string | null, type: string) => void;
  updateStyles?: () => void; // 添加更新样式的回调函数
  // 新增布局控制相关属性
  layoutLocked?: boolean;
  isSaving?: boolean;
  lastSaveTime?: Date | null;
  manuallyPositionedCount?: number;
  onUnlockLayout?: () => void;
  onResetManualPositioning?: () => void;
  onForceApplyLayout?: (config?: LayoutConfig) => void;
  // 小地图控制相关属性
  showMiniMap?: boolean;
  onToggleMiniMap?: () => void;
  // 手动保存相关属性
  onManualSave?: () => Promise<void>;
}

const CanvasMenu: React.FC<CanvasMenuProps> = ({
  layoutMode,
  setLayoutMode,
  handleAutoLayout,
  handlePreviewLayout,
  cancelPreviewLayout,
  isPreviewMode,
  currentLayoutConfig,
  scissorsModeActive,
  toggleScissorsMode,
  showShortcuts,
  setShowShortcuts,
  selectedNodeId,
  handleAddChild,
  updateStyles,
  // 新增布局控制相关属性
  layoutLocked = false,
  isSaving = false,
  lastSaveTime = null,
  manuallyPositionedCount = 0,
  onUnlockLayout,
  onResetManualPositioning,
  onForceApplyLayout,
  // 小地图控制相关属性
  showMiniMap = true,
  onToggleMiniMap,
  // 手动保存相关属性
  onManualSave
}) => {
  const { zoomIn, zoomOut, fitView } = useReactFlow();
  const [showLayoutPanel, setShowLayoutPanel] = useState(false);
  const [showStylePanel, setShowStylePanel] = useState(false);

  return (
    <Panel position="top-right">
      <div className="menu-container bg-white bg-opacity-90 rounded-lg shadow-md p-2">
        {/* 视图控制组 */}
        <div className="menu-group flex space-x-1 mb-2 pb-2 border-b border-gray-200">
          <button
            className="menu-button"
            title="缩小视图"
            onClick={() => zoomOut()}
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 13H5v-2h14v2z" />
            </svg>
          </button>
          <button
            className="menu-button"
            title="放大视图"
            onClick={() => zoomIn()}
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
          </button>
          <button
            className="menu-button"
            title="重置视图"
            onClick={() => fitView()}
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5v14h18V5H3zm16 12H5V7h14v10z" />
            </svg>
          </button>
        </div>

        {/* 手动保存按钮 */}
        {onManualSave && (
          <div className="menu-group mb-2 pb-2 border-b border-gray-200">
            <button
              onClick={onManualSave}
              disabled={isSaving}
              className={`w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                isSaving
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600 active:scale-95'
              }`}
              title="手动保存"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              )}
              <span>{isSaving ? '保存中...' : '保存'}</span>
            </button>
          </div>
        )}

        {/* 保存状态指示器 */}
        {(isSaving || lastSaveTime) && (
          <div className="menu-group mb-2 pb-2 border-b border-gray-200">
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500"></div>
                  <span>保存中...</span>
                </>
              ) : lastSaveTime ? (
                <>
                  <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>已保存 {lastSaveTime.toLocaleTimeString()}</span>
                </>
              ) : null}
            </div>
          </div>
        )}

        {/* 小地图控制 */}
        {onToggleMiniMap && (
          <div className="menu-group mb-2 pb-2 border-b border-gray-200">
            <button
              onClick={onToggleMiniMap}
              className={`w-full flex items-center space-x-2 px-2 py-1 text-xs rounded transition-colors ${
                showMiniMap
                  ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title={showMiniMap ? '隐藏小地图' : '显示小地图'}
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {showMiniMap ? (
                  // 眼睛开启图标
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                ) : (
                  // 眼睛关闭图标
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                )}
              </svg>
              <span>{showMiniMap ? '隐藏小地图' : '显示小地图'}</span>
            </button>
          </div>
        )}

        {/* 布局状态指示器 */}
        {layoutLocked && (
          <div className="menu-group mb-2 pb-2 border-b border-gray-200">
            <div className="flex items-center space-x-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              <span>布局已锁定 ({manuallyPositionedCount}个手动节点)</span>
            </div>
          </div>
        )}

        {/* 布局控制组 */}
        <div className="menu-group flex space-x-1 mb-2 pb-2 border-b border-gray-200">
          <button
            className="menu-button"
            title="布局设置"
            onClick={() => setShowLayoutPanel(!showLayoutPanel)}
          >
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14H5v-3h7v3zm7 0h-5v-3h5v3zm0-5H5V5h14v7z" />
            </svg>
            <span>布局设置</span>
          </button>

          {/* 根据布局锁定状态显示不同的按钮 */}
          {layoutLocked ? (
            <>
              <button
                className="menu-button bg-orange-100 text-orange-700 border-orange-300"
                title="强制应用布局（将清除手动定位）"
                onClick={() => onForceApplyLayout && onForceApplyLayout()}
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>强制布局</span>
              </button>
              <button
                className="menu-button bg-blue-100 text-blue-700 border-blue-300"
                title="解锁布局"
                onClick={onUnlockLayout}
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                </svg>
                <span>解锁</span>
              </button>
              <button
                className="menu-button bg-red-100 text-red-700 border-red-300"
                title="重置手动定位"
                onClick={onResetManualPositioning}
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>重置</span>
              </button>
            </>
          ) : (
            <button
              className="menu-button"
              title="快速应用当前布局"
              onClick={() => handleAutoLayout()}
            >
              <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
              <span>应用布局</span>
            </button>
          )}

          <button
            className="menu-button"
            title="样式设置"
            onClick={() => setShowStylePanel(!showStylePanel)}
          >
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
            </svg>
            <span>样式设置</span>
          </button>
        </div>

        {/* 布局控制面板 */}
        {showLayoutPanel && (
          <div className="relative">
            <div className="absolute right-0 top-0 mt-2 z-50">
              <LayoutControlPanel
                onLayoutApply={(config) => {
                  // 如果布局被锁定，使用强制应用
                  if (layoutLocked && onForceApplyLayout) {
                    onForceApplyLayout(config);
                  } else {
                    handleAutoLayout(config);
                  }
                  setShowLayoutPanel(false);
                }}
                onPreview={handlePreviewLayout}
                onCancel={() => {
                  if (isPreviewMode) {
                    cancelPreviewLayout();
                  }
                  setShowLayoutPanel(false);
                }}
                currentLayout={currentLayoutConfig}
                isPreviewMode={isPreviewMode}
                layoutLocked={layoutLocked}
              />
            </div>
          </div>
        )}

        {/* 样式控制面板 */}
        {showStylePanel && (
          <div className="relative">
            <div className="absolute right-0 top-0 mt-2 z-50">
              <div className="bg-white rounded-lg shadow-lg p-4 w-64">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">样式设置</h3>
                  <button
                    className="text-gray-500 hover:text-gray-700"
                    onClick={() => setShowStylePanel(false)}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <StyleControlPanel
                  onApply={() => {
                    setShowStylePanel(false);

                    // 调用更新样式函数
                    if (updateStyles) {
                      console.log('调用updateStyles函数应用样式');
                      updateStyles();
                    } else {
                      // 如果没有提供updateStyles函数，显示通知
                      const showNotification = (message: string, type: string, duration: number, position: string) => {
                        console.log(`Notification (${type} @${position}, ${duration}ms): ${message}`);
                      };
                      showNotification('样式已应用，但可能需要刷新页面才能看到效果', 'warning', 5000, 'top-center');
                    }
                  }}
                />

              </div>
            </div>
          </div>
        )}

        {/* 工具组 */}
        <div className="menu-group flex space-x-1">
          <button
            className={`menu-button ${scissorsModeActive ? 'active' : ''}`}
            title="剪刀模式：删除连线 (Ctrl+X)"
            onClick={toggleScissorsMode}
          >
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3h-3z" />
            </svg>
            <span>剪刀模式</span>
          </button>
          <button
            className="menu-button"
            title="显示快捷键"
            onClick={() => setShowShortcuts(prev => !prev)}
          >
            <span className="mr-1">⌨️</span>
            <span>快捷键</span>
          </button>
          {selectedNodeId && (
            <div className="relative">
              <button
                className="menu-button"
                title="添加子节点"
                onClick={() => {
                  // 创建一个简单的下拉菜单来选择节点类型
                  const menu = document.createElement('div');
                  menu.className = 'absolute top-full right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50 min-w-[120px]';
                  menu.innerHTML = `
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="volume">
                      <span class="mr-2">📚</span>总纲/卷
                    </button>
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="event">
                      <span class="mr-2">⚡</span>事件刚
                    </button>
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="chapter">
                      <span class="mr-2">📄</span>章节
                    </button>
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="plot">
                      <span class="mr-2">🎬</span>剧情节点
                    </button>
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="dialogue">
                      <span class="mr-2">💬</span>对话节点
                    </button>
                    <button class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center" data-type="synopsis">
                      <span class="mr-2">📋</span>核心故事梗概
                    </button>
                  `;

                  // 添加点击事件
                  menu.addEventListener('click', (e) => {
                    const target = e.target as HTMLElement;
                    const button = target.closest('button[data-type]') as HTMLButtonElement;
                    if (button) {
                      const type = button.getAttribute('data-type');
                      if (type) {
                        handleAddChild(selectedNodeId, type);
                      }
                    }
                    menu.remove();
                  });

                  // 添加到DOM
                  const buttonElement = e.currentTarget as HTMLElement;
                  const container = buttonElement.parentElement;
                  if (container) {
                    container.appendChild(menu);

                    // 点击外部关闭菜单
                    const closeMenu = (event: MouseEvent) => {
                      if (!menu.contains(event.target as Node)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                      }
                    };
                    setTimeout(() => document.addEventListener('click', closeMenu), 0);
                  }
                }}
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
                <span>添加子节点</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </Panel>
  );
};

export default CanvasMenu;
