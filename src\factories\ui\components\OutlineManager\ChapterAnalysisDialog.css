/* 章节分析对话框样式 */

/* 基础布局 */
.chapter-analysis-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chapter-analysis-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.chapter-analysis-content {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.chapter-analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.chapter-analysis-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chapter-analysis-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.chapter-analysis-title h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chapter-analysis-title p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.step-number.active {
  background: #3b82f6;
  color: white;
}

.step-number.inactive {
  background: #e5e7eb;
  color: #6b7280;
}

.step-connector {
  width: 24px;
  height: 2px;
  transition: all 0.3s ease;
}

.step-connector.active {
  background: #3b82f6;
}

.step-connector.inactive {
  background: #e5e7eb;
}

/* 关闭按钮 */
.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 内容区域 */
.chapter-analysis-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 输入模式切换 */
.input-mode-tabs {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 24px;
}

.input-mode-tab {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.input-mode-tab.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-mode-tab:hover:not(.active) {
  color: #374151;
}

/* 文本输入区域 */
.text-input-container {
  margin-bottom: 24px;
}

.text-input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.text-input-textarea {
  width: 100%;
  min-height: 200px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.text-input-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.text-input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.word-count {
  font-weight: 500;
}

.word-count.warning {
  color: #f59e0b;
}

.word-count.error {
  color: #ef4444;
}

/* 章节选择区域 */
.chapter-selection-container {
  margin-bottom: 24px;
}

.chapter-selection-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
}

.chapter-selection-title {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.chapter-selection-count {
  font-size: 14px;
  color: #6b7280;
}

/* 配置面板 */
.config-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.config-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

.config-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16px;
}

.config-option {
  margin-bottom: 16px;
}

.config-option:last-child {
  margin-bottom: 0;
}

.config-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.config-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.config-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-slider {
  width: 100%;
  margin: 8px 0;
}

.config-slider-value {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  margin-top: 4px;
}

/* 分析进度 */
.analysis-progress {
  text-align: center;
  padding: 40px 20px;
}

.analysis-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.analysis-icon svg {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.analysis-title {
  font-size: 18px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.analysis-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
}

.analysis-log {
  background: #1f2937;
  color: #10b981;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  text-align: left;
  white-space: pre-wrap;
}

/* 结果展示 */
.result-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.result-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.result-card-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
}

.result-card-value {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

/* 错误提示 */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  color: #dc2626;
  font-size: 14px;
  margin-bottom: 16px;
}

/* 底部操作栏 */
.chapter-analysis-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.footer-info {
  font-size: 14px;
  color: #6b7280;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.footer-button.secondary {
  background: none;
  color: #6b7280;
}

.footer-button.secondary:hover {
  color: #374151;
}

.footer-button.primary {
  background: #3b82f6;
  color: white;
}

.footer-button.primary:hover {
  background: #2563eb;
}

.footer-button.success {
  background: #10b981;
  color: white;
}

.footer-button.success:hover {
  background: #059669;
}

.footer-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chapter-analysis-content {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .chapter-analysis-header {
    padding: 16px;
  }
  
  .chapter-analysis-body {
    padding: 16px;
  }
  
  .config-panel {
    grid-template-columns: 1fr;
  }
  
  .result-overview {
    grid-template-columns: 1fr;
  }
  
  .step-indicator {
    display: none;
  }
}
