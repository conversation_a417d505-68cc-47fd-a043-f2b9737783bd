"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import createCharacterExtractorAIAdapter, { CharacterExtractionResult } from '@/adapters/ai/CharacterExtractorAIAdapter';
import createChapterSegmenter from '@/utils/ai/ChapterSegmenter';
import createMessageBuilder from '@/utils/ai/MessageBuilder';
import { createAIFactory } from '@/factories/ai/AIFactory';
import { createSettingsFactory } from '@/factories/settings/SettingsFactory';
import { AIResponseParser } from '@/utils/ai/AIResponseParser';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
  characterIds?: string[];
  terminologyIds?: string[];
  worldBuildingIds?: string[];
  summary?: string;
  notes?: string;
}

interface CharacterCreatorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateCharacter: (characterInfo: any) => Promise<void>;
  bookId: string;
}

/**
 * 人物创建对话框组件
 * 用于使用AI直接创建人物
 */
const CharacterCreatorDialog = ({
  isOpen,
  onClose,
  onCreateCharacter,
  bookId
}: CharacterCreatorDialogProps) => {
  // 章节数据
  const [chapters, setChapters] = useState<GenericChapter[]>([]);
  const [isLoadingChapters, setIsLoadingChapters] = useState(false);

  // 选中的章节
  const [selectedChapterIds, setSelectedChapterIds] = useState<string[]>([]);

  // 范围选择
  const [rangeStart, setRangeStart] = useState<string>('');
  const [rangeEnd, setRangeEnd] = useState<string>('');

  // 章节搜索
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredChapters, setFilteredChapters] = useState<GenericChapter[]>([]);

  // 创建设置
  const [characterName, setCharacterName] = useState<string>('');
  const [characterCount, setCharacterCount] = useState<number>(1);
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [showCharacterCountInput, setShowCharacterCountInput] = useState<boolean>(false);

  // 创建状态
  const [isLoading, setIsLoading] = useState(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<CharacterExtractionResult | null>(null);
  const [generatedCharacters, setGeneratedCharacters] = useState<CharacterExtractionResult[]>([]);
  const [currentCharacterIndex, setCurrentCharacterIndex] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);

  // 创建人物提取AI适配器
  const characterExtractorAIAdapter = createCharacterExtractorAIAdapter();

  // 创建章节分段处理工具
  const segmenter = createChapterSegmenter();

  // 当对话框打开时加载章节
  useEffect(() => {
    if (isOpen) {
      loadChapters();
    }
  }, [isOpen, bookId]);

  // 当搜索查询变化时过滤章节
  useEffect(() => {
    if (chapters.length > 0) {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const filtered = chapters.filter(chapter =>
          (chapter.title?.toLowerCase().includes(query) || false) ||
          (chapter.content?.toLowerCase().includes(query) || false)
        );
        setFilteredChapters(filtered);
      } else {
        setFilteredChapters(chapters);
      }
    }
  }, [searchQuery, chapters]);

  /**
   * 加载章节列表
   */
  const loadChapters = async () => {
    setIsLoadingChapters(true);
    try {
      console.log('开始加载章节数据, bookId =', bookId);
      console.log('当前时间戳:', new Date().toISOString());

      // 尝试使用 src/db/chapterRepository.ts 中的 chapterRepository
      try {
        const { ChapterRepository } = await import('@/db/chapterRepository');
        const chapterRepo = new ChapterRepository();
        const chaptersData = await chapterRepo.getChaptersByBookId(bookId);

        console.log('通过 src/db/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/db/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法失败，尝试使用 src/lib/db/repositories/chapterRepository.ts
      try {
        const { chapterRepository } = await import('@/lib/db/repositories');
        const chaptersData = await chapterRepository.getAllByBookId(bookId);

        console.log('通过 src/lib/db/repositories/chapterRepository.ts 获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          setFilteredChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 src/lib/db/repositories/chapterRepository.ts 获取章节数据失败:', error);
      }

      // 如果上面的方法都失败，尝试使用 db 直接查询
      try {
        // 尝试使用 AppDatabase
        const { db: appDb } = await import('@/db/database');
        const chaptersData = await appDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 AppDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          setChapters(chaptersData);
          setFilteredChapters(chaptersData);
          console.log('加载了章节:', chaptersData.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 AppDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果 AppDatabase 失败，尝试使用 NovelDatabase
      try {
        const { db: novelDb } = await import('@/lib/db/dexie');
        const chaptersData = await novelDb.chapters.where('bookId').equals(bookId).toArray();

        console.log('通过 NovelDatabase 直接查询获取到章节数据:', chaptersData);

        if (chaptersData && chaptersData.length > 0) {
          // 按顺序排序章节
          const sortedChapters = [...chaptersData].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          setChapters(sortedChapters);
          setFilteredChapters(sortedChapters);
          console.log('加载了章节:', sortedChapters.length);
          setIsLoadingChapters(false);
          return;
        }
      } catch (error) {
        console.error('通过 NovelDatabase 直接查询获取章节数据失败:', error);
      }

      // 如果上述方法都失败，尝试使用 fetch API 从服务器获取
      try {
        const response = await fetch(`/api/books/${bookId}/chapters`);
        if (response.ok) {
          const chaptersData = await response.json();

          console.log('通过 fetch API 获取到章节数据:', chaptersData);

          if (chaptersData && chaptersData.length > 0) {
            // 按顺序排序章节
            const sortedChapters = [...chaptersData].sort((a, b) => {
              const orderA = a.order !== undefined ? a.order : 999999;
              const orderB = b.order !== undefined ? b.order : 999999;
              return orderA - orderB;
            });

            setChapters(sortedChapters);
            setFilteredChapters(sortedChapters);
            console.log('加载了章节:', sortedChapters.length);
            setIsLoadingChapters(false);
            return;
          }
        }
      } catch (error) {
        console.error('通过 fetch API 获取章节数据失败:', error);
      }

      // 所有方法都失败
      console.error('所有方法都无法获取章节数据');
      setChapters([]);
      setFilteredChapters([]);
    } catch (error) {
      console.error('加载章节失败:', error);
      setError('加载章节失败');
      setChapters([]);
      setFilteredChapters([]);
    } finally {
      setIsLoadingChapters(false);
    }
  };

  /**
   * 处理章节选择
   * @param chapterId 章节ID
   */
  const handleChapterSelect = (chapterId: string) => {
    setSelectedChapterIds(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  /**
   * 全选/取消全选章节
   */
  const handleSelectAllChapters = () => {
    if (selectedChapterIds.length === filteredChapters.length) {
      setSelectedChapterIds([]);
    } else {
      setSelectedChapterIds(filteredChapters.map(chapter => chapter.id!));
    }
  };

  /**
   * 选择章节范围
   * @param mode 选择模式：'select'（选择）或'deselect'（取消选择）
   */
  const handleRangeSelect = (mode: 'select' | 'deselect') => {
    // 验证输入
    const start = parseInt(rangeStart);
    const end = parseInt(rangeEnd);

    if (isNaN(start) || isNaN(end)) {
      setError('请输入有效的章节编号');
      return;
    }

    if (start > end) {
      setError('起始章节编号不能大于结束章节编号');
      return;
    }

    if (start < 1 || end > chapters.length) {
      setError(`章节编号必须在1到${chapters.length}之间`);
      return;
    }

    // 获取排序后的章节
    const sortedChapters = [...chapters].sort((a, b) => {
      const orderA = a.order !== undefined ? a.order : 999999;
      const orderB = b.order !== undefined ? b.order : 999999;
      return orderA - orderB;
    });

    // 选择范围内的章节
    const chaptersInRange = sortedChapters.slice(start - 1, end);

    if (chaptersInRange.length === 0) {
      setError('指定范围内没有章节');
      return;
    }

    // 获取范围内的章节ID
    const chapterIds = chaptersInRange.map(chapter => chapter.id!);

    // 更新选中的章节
    setSelectedChapterIds(prevSelected => {
      if (mode === 'select') {
        // 选择模式：合并已选中的章节和范围内的章节，去重
        return [...new Set([...prevSelected, ...chapterIds])];
      } else {
        // 取消选择模式：从已选中的章节中移除范围内的章节
        return prevSelected.filter(id => !chapterIds.includes(id));
      }
    });

    // 清空输入框
    setRangeStart('');
    setRangeEnd('');
  };

  /**
   * 创建人物
   */
  const createCharacter = async () => {
    // 移除名字必填的限制
    // 如果没有输入名字，使用"新人物"作为默认名称
    const nameToUse = characterName.trim() || '新人物';

    setIsLoading(true);
    setError(null);
    setGeneratedCharacter(null);
    setGeneratedCharacters([]);
    setCurrentCharacterIndex(0);

    try {
      // 获取API设置
      const settingsFactory = createSettingsFactory();
      const apiSettings = settingsFactory.createAPISettingsDialogComponent();
      const currentProvider = apiSettings.getCurrentProvider();
      const currentModel = apiSettings.getCurrentModel();
      const apiKey = apiSettings.getAPIKey(currentProvider);
      const apiEndpoint = apiSettings.getAPIEndpoint(currentProvider);

      if (!apiKey) {
        setError(`请先在设置中配置${currentProvider === 'openai' ? 'OpenAI' : currentProvider === 'google' ? 'Google' : '自定义'}的API密钥`);
        setIsLoading(false);
        return;
      }

      // 如果是自定义提供商，还需要检查API端点
      if (currentProvider === 'custom' && !apiEndpoint) {
        setError('请先在设置中配置自定义API端点');
        setIsLoading(false);
        return;
      }

      // 获取选中章节的内容
      let combinedContent = '';
      if (selectedChapterIds.length > 0) {
        console.log('选中的章节ID:', selectedChapterIds);

        // 获取选中章节的内容
        const selectedChapters = chapters.filter(chapter => selectedChapterIds.includes(chapter.id!));
        console.log('选中的章节数量:', selectedChapters.length);

        for (const chapter of selectedChapters) {
          if (chapter.content) {
            combinedContent += `# ${chapter.title || '无标题章节'}\n\n${chapter.content}\n\n`;
            console.log(`添加章节内容: ${chapter.title || '无标题章节'}, 内容长度: ${chapter.content.length}`);
          } else {
            console.warn(`章节 ${chapter.id} (${chapter.title || '无标题章节'}) 没有内容`);
          }
        }

        console.log('合并后的章节内容长度:', combinedContent.length);
      }

      // 创建AI工厂和发送组件
      const aiFactory = createAIFactory();
      const aiSender = aiFactory.createAISenderComponent();

      // 构建系统提示词
      const systemPrompt = `你是一位专业的小说人物创作专家，擅长根据需求创建丰富、立体的人物形象。请根据用户的要求，创建${characterCount > 1 ? `${characterCount}个` : '一个'}${nameToUse !== '新人物' ? `名为"${nameToUse}"的` : ''}人物，提供全面的人物信息。`;

      // 构建用户提示词
      let userPrompt = `请为我创建${characterCount > 1 ? `${characterCount}个` : '一个'}${nameToUse !== '新人物' ? `名为"${nameToUse}"的` : ''}小说人物。`;

      // 如果是多个人物，添加说明
      if (characterCount > 1) {
        userPrompt += `\n\n请创建${characterCount}个不同的人物，每个人物都应该有独特的特征和背景。`;
      }

      if (customPrompt) {
        userPrompt += `\n\n${customPrompt}`;
      }

      if (combinedContent) {
        userPrompt += `\n\n请参考以下章节内容，确保人物与故事背景相符：\n\n${combinedContent}`;
      }

      // 构建输出格式提示词
      let outputFormatPrompt = '';

      // 重要说明部分
      const importantNotes = `
重要说明：
1. 请确保所有字段都有详细、具体的描述，不要使用简单的一两个词
2. 字段名称必须严格按照上述格式，不要创建新的字段名
3. 直接返回JSON对象，不要使用Markdown代码块或其他格式标记
4. 不要在JSON前后添加任何额外的文本或解释
`;

      if (characterCount === 1) {
        // 单个人物的输出格式
        outputFormatPrompt = `
请以JSON格式输出人物信息，格式如下：

{
  "newInfo": {
    "name": "${nameToUse}",
    "description": "人物的简要概述",
    "appearance": "外貌描述（身高、体型、面部特征、服饰等）",
    "personality": "性格描述（性格特点、行为模式、情绪表现等）",
    "background": "背景描述（出身、经历、职业等）",
    "goals": "人物在故事中想要达成的目标或愿望",
    "characterArchetype": "角色原型（如英雄、导师、守门人、变形者等）",
    "growthArc": "人物在故事中的成长或变化轨迹",
    "hiddenMotivation": "人物表面行为背后的真实动机",
    "secretHistory": "人物不为人知的过去经历",
    "innerConflicts": "人物内心的矛盾和挣扎",
    "symbolism": "人物在故事中所代表的象征或主题意义"
  }
}
${importantNotes}`;
      } else {
        // 多个人物的输出格式
        outputFormatPrompt = `
请以JSON格式输出${characterCount}个人物信息，格式如下：

{
  "characters": [
    {
      "newInfo": {
        "name": "人物1的名称",
        "description": "人物的简要概述",
        "appearance": "外貌描述（身高、体型、面部特征、服饰等）",
        "personality": "性格描述（性格特点、行为模式、情绪表现等）",
        "background": "背景描述（出身、经历、职业等）",
        "goals": "人物在故事中想要达成的目标或愿望",
        "characterArchetype": "角色原型（如英雄、导师、守门人、变形者等）",
        "growthArc": "人物在故事中的成长或变化轨迹",
        "hiddenMotivation": "人物表面行为背后的真实动机",
        "secretHistory": "人物不为人知的过去经历",
        "innerConflicts": "人物内心的矛盾和挣扎",
        "symbolism": "人物在故事中所代表的象征或主题意义"
      }
    },
    {
      "newInfo": {
        "name": "人物2的名称",
        "description": "人物的简要概述",
        ...
      }
    },
    ...
  ]
}
${importantNotes}`;
      }

      // 使用MessageBuilder构建多角色消息数组
      const messages = createMessageBuilder()
        .addSystemMessage(systemPrompt)
        .addUserMessage(userPrompt)
        .addAssistantMessage(`我将为您创建一个${nameToUse !== '新人物' ? `名为"${nameToUse}"的` : ''}人物。`)
        .addUserMessage(outputFormatPrompt)
        .build();

      // 调用AI模型
      const result = await aiSender.sendRequest('', {
        messages,
        provider: currentProvider,
        model: currentModel,
        apiKey: apiKey,
        apiEndpoint: apiEndpoint,
        temperature: 0.7, // 使用较高的温度，增加创意性
        max_tokens: 2000
      });

      // 使用AIResponseParser解析JSON结果
      try {
        // 使用AIResponseParser解析JSON，它会自动处理<think>标记块和其他格式问题
        // 定义默认值和类型
        const defaultValue: any = characterCount === 1
          ? { newInfo: { name: nameToUse } }
          : { characters: [] };

        const parsedData = AIResponseParser.parseJSON<any>(result.text, defaultValue);

        if (characterCount === 1) {
          // 单个人物的处理
          // 确保newInfo字段存在
          if (!parsedData.newInfo) {
            parsedData.newInfo = {};
          }

          // 确保name字段存在
          if (!parsedData.newInfo.name) {
            parsedData.newInfo.name = nameToUse;
          }

          // 创建符合CharacterExtractionResult类型的对象
          const characterData: CharacterExtractionResult = {
            newInfo: parsedData.newInfo
          };

          setGeneratedCharacter(characterData);
          setGeneratedCharacters([characterData]);
          console.log('生成的人物数据:', characterData);
        } else {
          // 多个人物的处理
          if (parsedData.characters && Array.isArray(parsedData.characters)) {
            // 确保每个人物都有必要的字段
            const processedCharacters = parsedData.characters.map((char: any, index: number) => {
              if (!char.newInfo) {
                char.newInfo = {};
              }

              // 如果没有名称，使用默认名称
              if (!char.newInfo.name) {
                char.newInfo.name = `${nameToUse}${index + 1}`;
              }

              // 创建符合CharacterExtractionResult类型的对象
              return {
                newInfo: char.newInfo
              } as CharacterExtractionResult;
            });

            // 限制人物数量
            const limitedCharacters = processedCharacters.slice(0, characterCount);

            setGeneratedCharacters(limitedCharacters);
            setGeneratedCharacter(limitedCharacters[0]);
            console.log(`生成了${limitedCharacters.length}个人物数据:`, limitedCharacters);
          } else {
            // 如果返回的不是数组格式，尝试将单个人物转换为数组
            const newInfo = parsedData.newInfo ? parsedData.newInfo : { name: nameToUse };

            // 确保name字段存在
            if (!newInfo.name) {
              newInfo.name = nameToUse;
            }

            // 创建符合CharacterExtractionResult类型的对象
            const characterData: CharacterExtractionResult = { newInfo };

            setGeneratedCharacter(characterData);
            setGeneratedCharacters([characterData]);
            console.log('生成的单个人物数据 (非数组格式):', characterData);
          }
        }
      } catch (error) {
        console.error('解析AI返回的JSON失败:', error, '原始文本:', result.text);
        setError('解析生成的人物数据失败，请重试');
      }
    } catch (error: any) {
      console.error('创建人物失败:', error);
      setError('创建人物失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消创建
   */
  const cancelCreation = () => {
    // 取消AI请求
    // TODO: 实现取消AI请求的功能
    setIsLoading(false);
  };

  /**
   * 应用创建结果
   */
  const applyCreationResult = async () => {
    if (generatedCharacters.length === 0) {
      setError('没有生成的人物数据');
      return;
    }

    try {
      // 保存所有生成的人物
      for (const character of generatedCharacters) {
        // 创建完整的人物对象
        const now = new Date();
        const newCharacter: any = {
          bookId,
          name: character.newInfo?.name || '新人物',
          description: character.newInfo?.description || '',
          appearance: character.newInfo?.appearance || '',
          personality: character.newInfo?.personality || '',
          background: character.newInfo?.background || '',
          goals: character.newInfo?.goals || '',
          characterArchetype: character.newInfo?.characterArchetype || '',
          growthArc: character.newInfo?.growthArc || '',
          hiddenMotivation: character.newInfo?.hiddenMotivation || '',
          secretHistory: character.newInfo?.secretHistory || '',
          innerConflicts: character.newInfo?.innerConflicts || '',
          symbolism: character.newInfo?.symbolism || '',
          createdAt: now,
          updatedAt: now,
          extractedFromChapterIds: selectedChapterIds,
          relatedCharacterIds: [],
          relatedTerminologyIds: [],
          relatedWorldBuildingIds: []
        };

        console.log('创建的人物对象:', newCharacter);
        console.log('关联的章节ID:', selectedChapterIds);
        console.log('AI生成的原始数据:', character.newInfo);

        // 保存人物
        await onCreateCharacter(newCharacter);

        // 如果有选中章节，保存章节关联
        if (selectedChapterIds.length > 0 && newCharacter.id) {
          try {
            // 导入CharacterAIAdapter
            const CharacterAIAdapter = (await import('@/adapters/ai/CharacterAIAdapter')).default;
            const characterAIAdapter = new CharacterAIAdapter();

            // 保存章节关联
            await characterAIAdapter.saveCharacterChapterAssociation(newCharacter, selectedChapterIds);
            console.log('保存章节关联成功');
          } catch (error) {
            console.error('保存章节关联失败:', error);
          }
        }
      }

      // 关闭对话框
      onClose();
    } catch (error) {
      console.error('保存人物失败:', error);
      setError('保存人物失败');
    }
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-4/5 max-w-4xl max-h-[90vh] flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-purple-700">AI创建人物</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 主体内容 */}
        <div className="flex-1 overflow-auto p-4">
          {/* 创建表单 */}
          {!isLoading && !generatedCharacter && (
            <div className="space-y-6">
              {/* 人物名称 */}
              <div>
                <label htmlFor="character-name" className="block text-sm font-medium text-gray-700 mb-1">
                  人物名称
                </label>
                <input
                  type="text"
                  id="character-name"
                  value={characterName}
                  onChange={e => setCharacterName(e.target.value)}
                  className="w-full p-2 border rounded-lg"
                  placeholder="输入人物名称（可选，留空将创建'新人物'）"
                />
              </div>

              {/* 人物数量设置 */}
              <div>
                <div className="flex items-center mb-1">
                  <label htmlFor="character-count" className="block text-sm font-medium text-gray-700">
                    创建人物数量
                  </label>
                  <button
                    type="button"
                    className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                    onClick={() => setShowCharacterCountInput(!showCharacterCountInput)}
                  >
                    {showCharacterCountInput ? '隐藏' : '显示'}
                  </button>
                </div>
                {showCharacterCountInput && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      id="character-count"
                      min="1"
                      max="10"
                      value={characterCount}
                      onChange={e => setCharacterCount(parseInt(e.target.value))}
                      className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      value={characterCount}
                      onChange={e => setCharacterCount(parseInt(e.target.value) || 1)}
                      min="1"
                      max="10"
                      className="w-16 p-2 border rounded-lg text-center"
                    />
                    <span className="text-sm text-gray-500">
                      {characterCount > 1 ? `将创建 ${characterCount} 个人物` : '创建单个人物'}
                    </span>
                  </div>
                )}
              </div>

              {/* 自定义提示词 */}
              <div>
                <label htmlFor="custom-prompt" className="block text-sm font-medium text-gray-700 mb-1">
                  自定义提示词（可选）
                </label>
                <textarea
                  id="custom-prompt"
                  value={customPrompt}
                  onChange={e => setCustomPrompt(e.target.value)}
                  className="w-full p-2 border rounded-lg"
                  rows={3}
                  placeholder="输入自定义提示词，例如：请创建一个神秘的反派角色，具有复杂的动机和悲惨的过去"
                />
              </div>

              {/* 章节选择 */}
              <div>
                <div className="mb-2">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">选择关联章节（可选）</h3>
                    <button
                      className="text-sm text-blue-600 hover:text-blue-800"
                      onClick={handleSelectAllChapters}
                    >
                      {selectedChapterIds.length === filteredChapters.length ? '取消全选' : '全选'}
                    </button>
                  </div>

                  {/* 搜索框 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">搜索章节:</span>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="输入章节标题或内容关键词"
                        className="w-full p-1 text-sm border rounded"
                      />
                    </div>
                    {searchQuery && (
                      <button
                        className="px-2 py-1 text-sm text-gray-600 hover:text-gray-800"
                        onClick={() => setSearchQuery('')}
                      >
                        清除
                      </button>
                    )}
                    <div className="text-xs text-gray-500">
                      {searchQuery ? `找到: ${filteredChapters.length}/${chapters.length}` : ''}
                    </div>
                  </div>

                  {/* 范围选择 */}
                  <div className="mt-2 mb-2 flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                    <span className="text-sm text-gray-600">范围选择:</span>
                    <input
                      type="number"
                      value={rangeStart}
                      onChange={(e) => setRangeStart(e.target.value)}
                      placeholder="起始"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={rangeEnd}
                      onChange={(e) => setRangeEnd(e.target.value)}
                      placeholder="结束"
                      className="w-16 p-1 text-sm border rounded"
                      min="1"
                      max={chapters.length}
                    />
                    <button
                      className="px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                      onClick={() => handleRangeSelect('select')}
                    >
                      选择
                    </button>
                    <button
                      className="px-2 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                      onClick={() => handleRangeSelect('deselect')}
                    >
                      取消选择
                    </button>
                  </div>

                  {/* 章节列表 */}
                  <div className="max-h-60 overflow-y-auto border rounded-lg">
                    {isLoadingChapters ? (
                      <div className="p-4 text-center text-gray-500">加载章节中...</div>
                    ) : filteredChapters.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">没有找到章节</div>
                    ) : (
                      filteredChapters.map((chapter) => (
                        <div
                          key={chapter.id}
                          className={`p-2 flex items-center ${
                            selectedChapterIds.includes(chapter.id!)
                              ? 'bg-purple-100 border border-purple-200'
                              : 'hover:bg-gray-100 border border-transparent'
                          }`}
                        >
                          <input
                            type="checkbox"
                            id={`chapter-${chapter.id}`}
                            checked={selectedChapterIds.includes(chapter.id!)}
                            onChange={() => handleChapterSelect(chapter.id!)}
                            className="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 rounded"
                          />
                          <label
                            htmlFor={`chapter-${chapter.id}`}
                            className={`flex-1 cursor-pointer ${
                              selectedChapterIds.includes(chapter.id!) ? 'font-medium text-purple-800' : 'text-gray-700'
                            }`}
                          >
                            {chapter.title || `章节 ${chapter.chapterNumber || '未编号'}`}
                          </label>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 加载中 */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
              <p className="text-lg text-gray-700">正在创建人物...</p>
              <p className="text-sm text-gray-500 mt-2">这可能需要一些时间，请耐心等待</p>
            </div>
          )}

          {/* 生成结果 */}
          {!isLoading && generatedCharacter && (
            <div className="space-y-4">
              {/* 多个人物时显示导航 */}
              {generatedCharacters.length > 1 && (
                <div className="flex items-center justify-between mb-4">
                  <button
                    className="px-2 py-1 bg-gray-200 rounded-lg disabled:opacity-50"
                    onClick={() => setCurrentCharacterIndex(prev => Math.max(0, prev - 1))}
                    disabled={currentCharacterIndex === 0}
                  >
                    上一个
                  </button>
                  <div className="text-center">
                    <span className="font-medium">人物 {currentCharacterIndex + 1} / {generatedCharacters.length}</span>
                  </div>
                  <button
                    className="px-2 py-1 bg-gray-200 rounded-lg disabled:opacity-50"
                    onClick={() => setCurrentCharacterIndex(prev => Math.min(generatedCharacters.length - 1, prev + 1))}
                    disabled={currentCharacterIndex === generatedCharacters.length - 1}
                  >
                    下一个
                  </button>
                </div>
              )}

              <h3 className="font-bold text-lg">
                生成的人物: {generatedCharacters[currentCharacterIndex]?.newInfo?.name || '新人物'}
              </h3>

              <div className="border p-4 rounded-lg bg-gray-50">
                {/* 显示生成的人物信息 */}
                {generatedCharacters[currentCharacterIndex]?.newInfo &&
                  Object.entries(generatedCharacters[currentCharacterIndex].newInfo).map(([key, value]) => {
                    if (key === 'name') return null; // 跳过名称，因为已经在标题中显示

                    // 获取字段显示名称
                    let displayName = key;
                    switch(key) {
                      case 'description': displayName = '描述'; break;
                      case 'appearance': displayName = '外貌'; break;
                      case 'personality': displayName = '性格'; break;
                      case 'background': displayName = '背景'; break;
                      case 'goals': displayName = '目标'; break;
                      case 'characterArchetype': displayName = '角色原型'; break;
                      case 'growthArc': displayName = '成长弧线'; break;
                      case 'hiddenMotivation': displayName = '隐藏动机'; break;
                      case 'secretHistory': displayName = '秘密历史'; break;
                      case 'innerConflicts': displayName = '内心冲突'; break;
                      case 'symbolism': displayName = '象征意义'; break;
                    }

                    return (
                      <div key={key} className="mb-4">
                        <h4 className="font-semibold text-gray-700">{displayName}</h4>
                        <p className="text-gray-600 whitespace-pre-wrap">{value as string}</p>
                      </div>
                    );
                  })
                }
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="p-4 border-t flex justify-end space-x-2">
          {!isLoading && !generatedCharacter ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={onClose}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                onClick={createCharacter}
              >
                {characterCount > 1 ? `创建${characterCount}个人物` : '创建人物'}
              </button>
            </>
          ) : !isLoading && generatedCharacter ? (
            <>
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={() => setGeneratedCharacter(null)}
              >
                返回编辑
              </button>
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={applyCreationResult}
              >
                {generatedCharacters.length > 1 ? `保存全部${generatedCharacters.length}个人物` : '应用并保存'}
              </button>
            </>
          ) : (
            <button
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelCreation}
            >
              取消创建
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CharacterCreatorDialog;