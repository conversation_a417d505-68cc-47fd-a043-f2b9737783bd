"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { SynopsisAIService, CoreIdea, SynopsisFields } from './SynopsisAIService';
import { OutlineNodeType } from '../../../types/outline';
import { BrainholeDirectionStorage, SavedBrainholeDirection } from './services/BrainholeDirectionStorage';
import { CustomizationStorage, SavedCustomization } from './CustomizationStorage';
import './SynopsisAIDialog.css';

interface SynopsisAIDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (synopsisData: SynopsisFields) => void;
  availableFrameworks: any[];
  nodeData?: OutlineNodeType;
}

export const SynopsisAIDialog: React.FC<SynopsisAIDialogProps> = ({
  isOpen,
  onClose,
  onComplete,
  availableFrameworks,
  nodeData
}) => {
  const [stage, setStage] = useState<'input' | 'generating' | 'selecting' | 'previewing' | 'customizing' | 'filling'>('input');
  const [userInput, setUserInput] = useState('');
  const [coreIdeas, setCoreIdeas] = useState<CoreIdea[]>([]);
  const [selectedIdea, setSelectedIdea] = useState<CoreIdea | null>(null);
  const [previewingIdea, setPreviewingIdea] = useState<CoreIdea | null>(null);
  const [customizationOptions, setCustomizationOptions] = useState<CustomizationOptions | null>(null);
  const [userCustomizations, setUserCustomizations] = useState<UserCustomizations>({});
  const [savedCustomizations, setSavedCustomizations] = useState<SavedCustomization[]>([]);
  const [showCustomizationModal, setShowCustomizationModal] = useState(false);
  const [synopsisFields, setSynopsisFields] = useState<SynopsisFields | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [streamingText, setStreamingText] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [cardPosition, setCardPosition] = useState<DOMRect | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [savedDirections, setSavedDirections] = useState<SavedBrainholeDirection[]>([]);
  const [showDirectionModal, setShowDirectionModal] = useState(false);
  const [directionSearchQuery, setDirectionSearchQuery] = useState('');

  const synopsisAIService = new SynopsisAIService();

  // 重置状态
  const resetDialog = () => {
    setStage('input');
    setUserInput('');
    setCoreIdeas([]);
    setSelectedIdea(null);
    setPreviewingIdea(null);
    setSynopsisFields(null);
    setIsLoading(false);
    setStreamingText('');
    setError(null);
    setCardPosition(null);
    setIsAnimating(false);
  };

  useEffect(() => {
    if (isOpen) {
      resetDialog();
      // 加载已保存的脑洞方向
      setSavedDirections(BrainholeDirectionStorage.getAll());
      // 加载已保存的定制化选择
      setSavedCustomizations(CustomizationStorage.getAll());
    }
  }, [isOpen]);

  // 添加ESC键关闭功能
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // 第一步：生成核心梗概选项
  const handleGenerateCoreIdeas = async () => {
    if (!userInput.trim()) {
      setError('请输入您的故事需求');
      return;
    }

    setIsLoading(true);
    setStage('generating');
    setStreamingText('');
    setError(null);

    try {
      const response = await synopsisAIService.generateCoreIdeas(
        userInput,
        availableFrameworks,
        (chunk: string) => {
          setStreamingText(prev => prev + chunk);
        }
      );

      if (response.success && response.coreIdeas) {
        setCoreIdeas(response.coreIdeas);
        setStage('selecting');
      } else {
        setError(response.error || '生成核心梗概失败');
        setStage('input');
      }
    } catch (error: any) {
      setError(error.message || '生成过程中出现错误');
      setStage('input');
    } finally {
      setIsLoading(false);
      setStreamingText('');
    }
  };

  // 第二步：用户点击卡片进入预览
  const handleCardClick = (idea: CoreIdea, event: React.MouseEvent<HTMLDivElement>) => {
    if (isAnimating) return; // 防止动画期间重复点击

    const cardElement = event.currentTarget;
    const rect = cardElement.getBoundingClientRect();

    setCardPosition(rect);
    setPreviewingIdea(idea);
    setIsAnimating(true);
    setStage('previewing');
  };

  // 第三步：用户确认选择后生成定制化选项
  const handleConfirmIdea = async (idea: CoreIdea) => {
    setSelectedIdea(idea);
    setIsLoading(true);
    setStage('customizing');
    setStreamingText('');
    setError(null);
    setPreviewingIdea(null);
    setCardPosition(null);
    setIsAnimating(false);

    try {
      // 调用AI生成定制化选项
      const response = await synopsisAIService.generateCustomizationOptions(
        idea,
        availableFrameworks,
        (chunk: string) => {
          setStreamingText(prev => prev + chunk);
        },
        {
          temperature: 0.7,
          maxTokens: 10000
        }
      );

      if (response.success && response.customizationOptions) {
        setCustomizationOptions(response.customizationOptions);
        setIsLoading(false);
      } else {
        setError(response.error || '生成定制化选项失败');
        setStage('selecting');
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error.message || '生成定制化选项失败');
      setStage('selecting');
      setIsLoading(false);
    }
  };

  // 保存定制化选择
  const handleSaveCustomization = (name: string, description: string) => {
    if (Object.keys(userCustomizations).length === 0) return;

    const saved = CustomizationStorage.save(name, description, userCustomizations);
    setSavedCustomizations(CustomizationStorage.getAll());
    return saved;
  };

  // 加载保存的定制化选择
  const handleLoadCustomization = (saved: SavedCustomization) => {
    setUserCustomizations(saved.customizations);
    CustomizationStorage.updateUsage(saved.id);
    setSavedCustomizations(CustomizationStorage.getAll());
    setShowCustomizationModal(false);
  };

  // 第四步：用户完成定制化选择后填充字段
  const handleCustomizationComplete = async () => {
    if (!selectedIdea) return;

    setIsLoading(true);
    setStage('filling');
    setStreamingText('');
    setError(null);

    try {
      const response = await synopsisAIService.fillSynopsisFields(
        selectedIdea,
        availableFrameworks,
        (chunk: string) => {
          setStreamingText(prev => prev + chunk);
        },
        {
          customizations: userCustomizations // 传递用户的定制化选择
        }
      );

      if (response.success && response.synopsisFields) {
        setSynopsisFields(response.synopsisFields);
      } else {
        setError(response.error || '字段填充失败');
        setStage('customizing');
      }
    } catch (error: any) {
      setError(error.message || '填充过程中出现错误');
      setStage('customizing');
    } finally {
      setIsLoading(false);
      setStreamingText('');
    }
  };

  // 保存脑洞方向
  const handleSaveDirection = (idea: CoreIdea) => {
    try {
      // 提取关键词
      const keywords = idea.brainholeDirection.split(/[+、，,]/).map(k => k.trim()).filter(k => k);

      const savedDirection = BrainholeDirectionStorage.save({
        name: `脑洞方向_${Date.now().toString().slice(-6)}`, // 默认名称
        direction: idea.brainholeDirection,
        genre: keywords[0] || '未分类',
        targetAudience: '网文读者',
        keywords: keywords
      });

      // 更新本地状态
      setSavedDirections(BrainholeDirectionStorage.getAll());
      alert(`脑洞方向已保存：${savedDirection.name}`);
    } catch (error: any) {
      alert(`保存失败：${error.message}`);
    }
  };

  // 选择已保存的脑洞方向
  const handleSelectSavedDirection = (direction: SavedBrainholeDirection) => {
    setUserInput(`基于以下脑洞方向生成新的创意：\n\n${direction.direction}\n\n请生成同类型、同受众的不同脑洞内容。`);
    setShowDirectionModal(false);
    setDirectionSearchQuery('');

    // 更新使用统计
    BrainholeDirectionStorage.updateUsage(direction.id);
    setSavedDirections(BrainholeDirectionStorage.getAll());
  };

  // 获取过滤后的方向列表
  const getFilteredDirections = () => {
    if (!directionSearchQuery.trim()) {
      return savedDirections;
    }
    return BrainholeDirectionStorage.search(directionSearchQuery);
  };

  // 取消预览，返回选择阶段
  const handleCancelPreview = () => {
    setPreviewingIdea(null);
    setCardPosition(null);
    setIsAnimating(false);
    setStage('selecting');
  };

  // 完成并返回数据
  const handleComplete = () => {
    if (synopsisFields) {
      onComplete(synopsisFields);
      onClose();
    }
  };

  // 返回上一步
  const handleBack = () => {
    switch (stage) {
      case 'selecting':
        setStage('input');
        setCoreIdeas([]);
        break;
      case 'previewing':
        handleCancelPreview();
        break;
      case 'filling':
        setStage('selecting');
        setSelectedIdea(null);
        setSynopsisFields(null);
        setStreamingText('');
        break;
      default:
        break;
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="synopsis-ai-dialog-overlay">
      <div
        className={`synopsis-ai-dialog ${stage === 'selecting' ? 'selecting-stage' : ''} ${stage === 'previewing' ? 'previewing-stage' : ''}`}
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <div className="synopsis-ai-dialog-header">
          <h2>🧠 核心故事梗概AI生成</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="synopsis-ai-dialog-content">
          {/* 第一阶段：用户输入 */}
          {stage === 'input' && (
            <div className="input-stage">
              <div className="stage-header">
                <h3>📝 描述您的故事需求</h3>
                <p>请详细描述您想要创作的故事类型、主题、背景等信息</p>
              </div>
              
              <textarea
                className="user-input"
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="例如：我想写一个现代都市修仙的故事，主角是一个普通上班族，意外获得修仙传承..."
                rows={6}
              />

              {availableFrameworks.length > 0 && (
                <div className="frameworks-info">
                  <p>🎯 将基于 {availableFrameworks.length} 个ACE框架进行创作指导</p>
                </div>
              )}

              {error && (
                <div className="error-message">
                  ⚠️ {error}
                </div>
              )}

              <div className="stage-actions">
                {savedDirections.length > 0 && (
                  <button
                    className="saved-directions-button"
                    onClick={() => setShowDirectionModal(true)}
                  >
                    📚 选择已保存方向 ({savedDirections.length})
                  </button>
                )}
                <button
                  className="generate-button"
                  onClick={handleGenerateCoreIdeas}
                  disabled={!userInput.trim()}
                >
                  🚀 生成核心梗概选项
                </button>
              </div>


            </div>
          )}

          {/* 第二阶段：生成中 */}
          {stage === 'generating' && (
            <div className="generating-stage">
              <div className="stage-header">
                <h3>🤖 AI正在生成核心梗概...</h3>
                <p>基于ACE框架和市场分析，为您创造有商业价值的故事核心</p>
              </div>

              <div className="streaming-content">
                {streamingText && (
                  <div className="streaming-text">
                    {streamingText}
                  </div>
                )}
                
                <div className="loading-indicator">
                  <div className="loading-spinner"></div>
                  <span>正在分析市场趋势和读者心理...</span>
                </div>
              </div>
            </div>
          )}

          {/* 第三阶段：选择核心梗 */}
          {stage === 'selecting' && (
            <div className="selecting-stage">
              <div className="stage-header">
                <h3>🎯 选择您喜欢的核心梗概</h3>
                <p>AI为您生成了 {coreIdeas.length} 个基于市场分析的核心梗概选项</p>
              </div>

              <div className="core-ideas-list">
                {coreIdeas.map((idea, index) => (
                  <div
                    key={idea.id}
                    className="core-idea-card"
                    onClick={(e) => handleCardClick(idea, e)}
                  >
                    <div className="idea-header">
                      <span className="idea-number">#{index + 1}</span>
                    </div>
                    
                    <div className="idea-content">
                      <div className="idea-field">
                        <label>🎯 脑洞方向：</label>
                        <p>{idea.brainholeDirection}</p>
                      </div>
                      
                      <div className="idea-field">
                        <label>💡 脑洞内容：</label>
                        <p>{idea.brainhole}</p>
                      </div>
                      
                      <div className="idea-field">
                        <label>📈 应用效果：</label>
                        <p>{idea.applicationEffect}</p>
                      </div>
                    </div>
                    
                    <div className="idea-actions">
                      <button className="select-button">
                        选择此梗概 →
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="stage-actions">
                <button className="back-button" onClick={handleBack}>
                  ← 重新输入
                </button>
              </div>
            </div>
          )}

          {/* 第四阶段：定制化选择 */}
          {stage === 'customizing' && (
            <div className="customizing-stage">
              <div className="stage-header">
                <h3>🎨 个性化定制您的故事</h3>
                <p>AI已分析您的核心梗概，请选择您偏好的创作方向</p>
              </div>

              {selectedIdea && (
                <div className="selected-idea-summary">
                  <h4>已选择的核心梗概：</h4>
                  <p><strong>方向：</strong>{selectedIdea.brainholeDirection}</p>
                  <p><strong>脑洞：</strong>{selectedIdea.brainhole}</p>
                </div>
              )}

              {isLoading ? (
                <div className="loading-indicator">
                  <div className="loading-spinner"></div>
                  <span>AI正在分析梗概特点，生成个性化选择...</span>
                </div>
              ) : customizationOptions ? (
                <div className="customization-options">
                  {customizationOptions.categories.map((category: any) => (
                    <div key={category.id} className="customization-category">
                      <div className="category-header">
                        <h4>{category.title}</h4>
                        <p>{category.description}</p>
                      </div>
                      <div className="category-options">
                        {category.options.map((option: any) => (
                          <div
                            key={option.id}
                            className={`customization-option ${userCustomizations[category.id]?.optionId === option.id ? 'selected' : ''}`}
                            onClick={() => {
                              setUserCustomizations(prev => ({
                                ...prev,
                                [category.id]: {
                                  optionId: option.id,
                                  optionLabel: option.label,
                                  optionDescription: option.description
                                }
                              }));
                            }}
                          >
                            <div className="option-label">{option.label}</div>
                            <div className="option-description">{option.description}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  <div className="customization-actions">
                    <div className="action-group">
                      <button
                        className="load-customization-button"
                        onClick={() => setShowCustomizationModal(true)}
                      >
                        📚 加载已保存选择
                      </button>
                      <button
                        className="save-customization-button"
                        onClick={() => {
                          const name = prompt('请输入选择配置名称:');
                          if (name) {
                            const description = prompt('请输入配置描述（可选）:') || '';
                            handleSaveCustomization(name, description);
                          }
                        }}
                        disabled={Object.keys(userCustomizations).length === 0}
                      >
                        💾 保存当前选择
                      </button>
                    </div>
                  </div>

                  <div className="stage-actions">
                    <button className="back-button" onClick={() => setStage('selecting')}>
                      ← 重新选择梗概
                    </button>
                    <button
                      className="continue-button"
                      onClick={handleCustomizationComplete}
                      disabled={Object.keys(userCustomizations).length === 0}
                    >
                      继续生成 →
                    </button>
                  </div>
                </div>
              ) : error ? (
                <div className="error-message">
                  ⚠️ {error}
                  <div className="error-actions">
                    <button onClick={() => setStage('selecting')}>返回选择</button>
                  </div>
                </div>
              ) : null}
            </div>
          )}

          {/* 第五阶段：字段填充中 */}
          {stage === 'filling' && (
            <div className="filling-stage">
              <div className="stage-header">
                <h3>📋 正在填充Synopsis字段...</h3>
                <p>基于您选择的核心梗概，AI正在生成完整的Synopsis节点数据</p>
              </div>

              {selectedIdea && (
                <div className="selected-idea-summary">
                  <h4>已选择的核心梗概：</h4>
                  <p><strong>方向：</strong>{selectedIdea.brainholeDirection}</p>
                  <p><strong>脑洞：</strong>{selectedIdea.brainhole}</p>
                </div>
              )}

              <div className="streaming-content">
                {streamingText && (
                  <div className="streaming-text">
                    {streamingText}
                  </div>
                )}
                
                {isLoading && (
                  <div className="loading-indicator">
                    <div className="loading-spinner"></div>
                    <span>正在生成Synopsis字段数据...</span>
                  </div>
                )}
              </div>

              {synopsisFields && (
                <div className="synopsis-fields-preview">
                  <h4>📄 生成的Synopsis字段：</h4>
                  <div className="field-preview">
                    <strong>脑洞设定：</strong>
                    <p>{synopsisFields.synopsisBrainhole}</p>
                  </div>
                  <div className="field-preview">
                    <strong>故事类型：</strong>
                    <p>{synopsisFields.synopsisGenre}</p>
                  </div>
                  <div className="field-preview">
                    <strong>虎头开局：</strong>
                    <p>{synopsisFields.synopsisOpening}</p>
                  </div>
                  <div className="field-preview">
                    <strong>核心梗概：</strong>
                    <p>{synopsisFields.synopsisCoreOutline}</p>
                  </div>
                  <div className="field-preview">
                    <strong>细思极恐结尾：</strong>
                    <p>{synopsisFields.synopsisEnding}</p>
                  </div>
                  
                  <div className="stage-actions">
                    <button className="back-button" onClick={handleBack}>
                      ← 重新选择
                    </button>
                    <button className="complete-button" onClick={handleComplete}>
                      ✅ 完成创建
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 预览模态框 - 独立覆盖层 */}
        {stage === 'previewing' && previewingIdea && (
          <div className="idea-preview-overlay">
            <div className="idea-preview-modal">
              <div className="preview-header">
                <h3>📖 详细预览</h3>
                <button className="close-button" onClick={handleCancelPreview}>×</button>
              </div>

              <div className="preview-content">
                <div className="preview-field">
                  <label>🎯 脑洞方向</label>
                  <p className="full-text">{previewingIdea.brainholeDirection}</p>
                </div>

                <div className="preview-field">
                  <label>💡 脑洞内容</label>
                  <p className="full-text">{previewingIdea.brainhole}</p>
                </div>

                <div className="preview-field">
                  <label>📈 应用效果</label>
                  <p className="full-text">{previewingIdea.applicationEffect}</p>
                </div>
              </div>

              <div className="preview-actions">
                <button className="save-direction-btn" onClick={() => handleSaveDirection(previewingIdea)}>
                  💾 保存方向
                </button>
                <div className="preview-main-actions">
                  <button className="cancel-btn" onClick={handleCancelPreview}>
                    返回选择
                  </button>
                  <button className="confirm-btn" onClick={() => handleConfirmIdea(previewingIdea)}>
                    确认选择
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 定制化选择加载模态框 */}
        {showCustomizationModal && (
          <div className="customization-modal-overlay" onClick={() => setShowCustomizationModal(false)}>
            <div className="customization-modal" onClick={(e) => e.stopPropagation()}>
              <div className="customization-modal-header">
                <h3>📚 加载已保存的定制化选择</h3>
                <button className="close-button" onClick={() => setShowCustomizationModal(false)}>×</button>
              </div>

              <div className="customization-modal-content">
                <div className="customization-stats">
                  <span>共 {savedCustomizations.length} 个已保存配置</span>
                </div>

                <div className="customization-list">
                  {savedCustomizations.length > 0 ? (
                    savedCustomizations.map((customization) => (
                      <div
                        key={customization.id}
                        className="customization-item"
                        onClick={() => handleLoadCustomization(customization)}
                      >
                        <div className="customization-item-header">
                          <span className="customization-name">{customization.name}</span>
                          <div className="customization-meta">
                            <span className="customization-usage">使用 {customization.usageCount} 次</span>
                            <span className="customization-date">
                              {customization.lastUsedAt.toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        {customization.description && (
                          <div className="customization-item-description">
                            <p>{customization.description}</p>
                          </div>
                        )}
                        <div className="customization-item-details">
                          {Object.entries(customization.customizations).map(([categoryId, option]) => (
                            <span key={categoryId} className="customization-detail">
                              {categoryId}: {option.optionLabel}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="no-customizations">
                      还没有保存任何定制化选择
                    </div>
                  )}
                </div>
              </div>

              <div className="customization-modal-footer">
                <button
                  className="customization-modal-close"
                  onClick={() => setShowCustomizationModal(false)}
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 方向选择模态框 */}
        {showDirectionModal && (
          <div className="direction-modal-overlay" onClick={() => setShowDirectionModal(false)}>
            <div className="direction-modal" onClick={(e) => e.stopPropagation()}>
              <div className="direction-modal-header">
                <h3>📚 选择已保存的脑洞方向</h3>
                <button className="close-button" onClick={() => setShowDirectionModal(false)}>×</button>
              </div>

              <div className="direction-modal-search">
                <input
                  type="text"
                  placeholder="搜索方向、类型或关键词..."
                  value={directionSearchQuery}
                  onChange={(e) => setDirectionSearchQuery(e.target.value)}
                  className="direction-search-input"
                />
              </div>

              <div className="direction-modal-content">
                <div className="direction-stats">
                  <span>共 {savedDirections.length} 个已保存方向</span>
                  {directionSearchQuery && (
                    <span>，找到 {getFilteredDirections().length} 个匹配结果</span>
                  )}
                </div>

                <div className="direction-list">
                  {getFilteredDirections().length > 0 ? (
                    getFilteredDirections().map((direction) => (
                      <div
                        key={direction.id}
                        className="direction-item"
                        onClick={() => handleSelectSavedDirection(direction)}
                      >
                        <div className="direction-item-header">
                          <span className="direction-name">{direction.name}</span>
                          <div className="direction-meta">
                            <span className="direction-usage">使用 {direction.usageCount} 次</span>
                            <span className="direction-date">
                              {direction.lastUsedAt.toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="direction-item-content">
                          <p>{direction.direction}</p>
                        </div>
                        <div className="direction-item-tags">
                          <span className="direction-genre">{direction.genre}</span>
                          {direction.keywords.slice(0, 4).map((keyword, index) => (
                            <span key={index} className="direction-keyword">{keyword}</span>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="no-directions">
                      {directionSearchQuery ? '没有找到匹配的方向' : '还没有保存任何方向'}
                    </div>
                  )}
                </div>
              </div>

              <div className="direction-modal-footer">
                <button
                  className="direction-modal-close"
                  onClick={() => setShowDirectionModal(false)}
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

// 定制化选项接口
export interface CustomizationOption {
  id: string;
  label: string;
  description: string;
}

export interface CustomizationCategory {
  id: string;
  title: string;
  description: string;
  options: CustomizationOption[];
}

export interface CustomizationOptions {
  categories: CustomizationCategory[];
}

export interface UserCustomizations {
  [categoryId: string]: {
    optionId: string;
    optionLabel: string;
    optionDescription: string;
  };
}

export default SynopsisAIDialog;
