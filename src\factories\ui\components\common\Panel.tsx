"use client";

import React, { ReactNode } from 'react';
import { createAnimationFactory } from '@/factories/animation';
import { FeatherPenIcon } from '@/components/icons';

export type PanelSize = 'small' | 'medium' | 'large';

interface PanelProps {
  title: string;
  isOpen: boolean;
  size?: PanelSize;
  content?: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
  onClose?: () => void;
  className?: string;
  fixedHeight?: boolean;
  backgroundColor?: string;
  width?: string;
  height?: string;
  enhanced?: boolean; // 是否启用增强效果
  literaryTheme?: boolean; // 是否使用文学主题
}

/**
 * 面板组件
 * 用于显示模态对话框或面板
 */
const Panel: React.FC<PanelProps> = ({
  title,
  isOpen,
  size = 'medium',
  content,
  header,
  footer,
  onClose,
  className = '',
  fixedHeight = false,
  backgroundColor = 'rgba(255, 255, 255, 0.95)',
  width = '80%',
  height = '70%',
  enhanced = false,
  literaryTheme = false
}) => {
  if (!isOpen) return null;

  // 创建动画工厂
  const animationFactory = createAnimationFactory();
  const fadeAnimation = animationFactory.createFadeAnimation('none', 400, 0, true);
  const scaleAnimation = animationFactory.createScaleAnimation(0.9, 1.0, 400, 0, true);

  // 获取动画样式
  const fadeStyle = fadeAnimation.getStyle();
  const scaleStyle = scaleAnimation.getStyle();

  // 获取面板尺寸样式
  const sizeStyles = {
    small: 'max-w-md',
    medium: 'max-w-2xl',
    large: 'max-w-4xl',
  };

  // 增强效果类名
  const enhancedClass = enhanced ? 'panel-enhanced' : '';
  const literaryClass = literaryTheme ? 'panel-literary' : '';

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center z-[9999] transition-opacity duration-300 ${enhancedClass}`}
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(5px)',
        opacity: isOpen ? 1 : 0,
        ...fadeStyle
      }}
      onClick={onClose}
    >
      <div
        className={`rounded-lg shadow-xl overflow-hidden flex flex-col relative ${className} ${enhancedClass} ${literaryClass}`}
        style={{
          backgroundColor: backgroundColor,
          width: width,
          height: height,
          maxHeight: fixedHeight ? height : '80vh',
          maxWidth: sizeStyles[size],
          animation: enhanced ? 'panelEnter 400ms cubic-bezier(0.34, 1.56, 0.64, 1)' : undefined,
          ...scaleStyle
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 装饰元素 - 四角花纹 */}
        {enhanced && literaryTheme && (
          <>
            <div className="absolute top-3 left-3 w-6 h-6 opacity-40 pointer-events-none">
              <svg viewBox="0 0 24 24" fill="none" stroke="#D2B48C" strokeWidth="1">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
            <div className="absolute top-3 right-3 w-6 h-6 opacity-40 pointer-events-none transform rotate-90">
              <svg viewBox="0 0 24 24" fill="none" stroke="#D2B48C" strokeWidth="1">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
            <div className="absolute bottom-3 left-3 w-6 h-6 opacity-40 pointer-events-none transform -rotate-90">
              <svg viewBox="0 0 24 24" fill="none" stroke="#D2B48C" strokeWidth="1">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
            <div className="absolute bottom-3 right-3 w-6 h-6 opacity-40 pointer-events-none transform rotate-180">
              <svg viewBox="0 0 24 24" fill="none" stroke="#D2B48C" strokeWidth="1">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
          </>
        )}
        {/* 面板头部 */}
        <div className="px-6 py-4 border-b flex justify-between items-center">
          {header || (
            <>
              <div className="flex items-center gap-3">
                {literaryTheme && (
                  <FeatherPenIcon
                    size="md"
                    animated={enhanced}
                    className="text-amber-700"
                  />
                )}
                <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
              </div>
              <button
                className="text-gray-500 hover:text-gray-700 focus:outline-none transition-all duration-200 hover:scale-110"
                onClick={onClose}
                aria-label="关闭面板"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </>
          )}
        </div>

        {/* 面板内容 */}
        <div className="flex-1 overflow-auto p-6">
          {content}
        </div>

        {/* 面板底部 */}
        {footer && (
          <div className="px-6 py-4 border-t bg-gray-50">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default Panel;
