"use client";

import React, { useState } from 'react';
import { useShortStoryStore } from '../stores/shortStoryStore';
import { ShortStoryAIService } from '@/factories/ai/services/ShortStoryAIService';
import { UnifiedAssociationButton } from '@/components/ui/UnifiedAssociationButton';
import { OutlineManagementButton } from '@/components/ui/OutlineManagementButton';
import { ACEFrameworkButton } from '@/components/ui/ACEFrameworkButton';
import { ACEElementExtractorButton } from '@/components/ui/ACEElementExtractorButton';
import { ACEElementExtractorDialog } from '@/components/ui/ACEElementExtractorDialog';
import { ShortStoryMode } from '@/factories/ai/services/types/ShortStoryTypes';
import { ExtractedElement } from '@/types/ACEFrameworkTypes';

interface CreationControlPanelProps {
  bookId: string;
  onContentGenerated?: (content: string) => void;
}

/**
 * 左侧创作控制面板
 * 包含创作参数设置、流程控制、关联元素选择等功能
 */
export const CreationControlPanel: React.FC<CreationControlPanelProps> = ({
  bookId,
  onContentGenerated
}) => {
  const {
    currentStep,
    params,
    coreMystery,
    segments,
    editingState,
    error,
    setCurrentStep,
    updateParams,
    setCoreMystery,
    setSegments,
    setEditingState,
    setError
  } = useShortStoryStore();

  // 本地状态
  const [expandedSections, setExpandedSections] = useState({
    input: true,
    mode: false,
    segments: false,
    associations: false,
    aceFrameworks: false
  });

  // ACE拆解对话框状态
  const [isExtractorDialogOpen, setIsExtractorDialogOpen] = useState(false);

  // AI服务实例
  const [aiService] = useState(() => new ShortStoryAIService());

  // 切换折叠状态
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 清空错误信息
  const clearError = () => {
    setError(null);
  };

  // 处理拆解按钮点击
  const handleExtractorClick = () => {
    setIsExtractorDialogOpen(true);
  };

  // 处理拆解完成
  const handleExtracted = (elements: ExtractedElement[]) => {
    console.log('拆解完成，提取到元素:', elements);
    // 这里可以添加提示或其他处理逻辑
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 面板标题 */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <span className="mr-2">🔗</span>
          创作辅助
        </h2>
        <p className="text-sm text-gray-600 mt-1">关联元素和创作框架</p>
      </div>

      {/* 滚动内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* 错误提示 */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-center justify-between">
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={clearError}
              className="text-red-400 hover:text-red-600"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}

        {/* 使用说明 */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">💡 使用指南</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <div>• 在中间编辑区域直接编写短篇内容</div>
            <div>• 使用下方的关联元素辅助创作</div>
            <div>• 选择ACE框架获得专业指导</div>
            <div>• 编辑完成后点击"发送内容"</div>
          </div>
        </div>

        {/* 关联元素选择 */}
        <CollapsibleSection
          title="关联元素"
          icon="🔗"
          expanded={expandedSections.associations}
          onToggle={() => toggleSection('associations')}
        >
          <div className="space-y-3">
            <UnifiedAssociationButton
              bookId={bookId}
              selectedCharacterIds={params.selectedCharacterIds}
              selectedWorldBuildingIds={params.selectedWorldBuildingIds}
              selectedTerminologyIds={params.selectedTerminologyIds}
              onCharacterIdsChange={(ids) => updateParams({ selectedCharacterIds: ids })}
              onWorldBuildingIdsChange={(ids) => updateParams({ selectedWorldBuildingIds: ids })}
              onTerminologyIdsChange={(ids) => updateParams({ selectedTerminologyIds: ids })}
              variant="compact"
            />
            
            <OutlineManagementButton
              bookId={bookId}
              selectedOutlineNodeIds={params.selectedOutlineNodeIds}
              onOutlineNodeIdsChange={(ids) => updateParams({ selectedOutlineNodeIds: ids })}
              variant="compact"
            />
          </div>
        </CollapsibleSection>

        {/* ACE框架选择 */}
        <CollapsibleSection
          title="ACE框架"
          icon="🎯"
          expanded={expandedSections.aceFrameworks}
          onToggle={() => toggleSection('aceFrameworks')}
        >
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <ACEElementExtractorButton
                onExtract={handleExtractorClick}
                variant="compact"
                className="flex-1"
              />
              <ACEFrameworkButton
                selectedFrameworkIds={params.selectedACEFrameworkIds}
                onFrameworkIdsChange={(ids) => updateParams({ selectedACEFrameworkIds: ids })}
                variant="compact"
                className="flex-1"
              />
            </div>

            {/* ACE拆解功能说明 */}
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded flex items-center">
              <svg className="w-3 h-3 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
              点击"ACE拆解"可以智能分析文本内容，提取可复用的创作元素
            </div>
          </div>
        </CollapsibleSection>
      </div>

      {/* 底部提示 */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-center text-sm text-gray-500">
          <div className="mb-2">🎯 专注于中间区域的内容编辑</div>
          <div className="text-xs">关联元素和框架将为您的创作提供灵感</div>
        </div>
      </div>

      {/* ACE元素拆解对话框 */}
      <ACEElementExtractorDialog
        isOpen={isExtractorDialogOpen}
        onClose={() => setIsExtractorDialogOpen(false)}
        onExtracted={handleExtracted}
      />
    </div>
  );
};

/**
 * 可折叠区域组件
 */
const CollapsibleSection: React.FC<{
  title: string;
  icon: string;
  expanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}> = ({ title, icon, expanded, onToggle, children }) => {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full p-3 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
      >
        <div className="flex items-center space-x-2">
          <span>{icon}</span>
          <span className="font-medium text-gray-800">{title}</span>
        </div>
        <svg
          className={`w-4 h-4 text-gray-500 transition-transform ${expanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {expanded && (
        <div className="p-3 border-t border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
};
