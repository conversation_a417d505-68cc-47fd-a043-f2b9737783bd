"use client";

import React, { useState, useEffect } from 'react';
import { PromptTemplate, PromptCategory } from '@/lib/db/dexie';

interface PromptTemplateManagerProps {
  isOpen: boolean;
  onClose: () => void;
  category: PromptCategory;
  onSelectTemplate: (template: PromptTemplate) => void;
  initialPrompt?: string;
}

/**
 * 提示词模板管理组件
 * 用于保存、加载和管理提示词模板
 */
export const PromptTemplateManager: React.FC<PromptTemplateManagerProps> = ({
  isOpen,
  onClose,
  category,
  onSelectTemplate,
  initialPrompt = ''
}) => {
  // 提示词模板列表
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState<string | null>(null);
  // 成功消息
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  // 新模板名称
  const [newTemplateName, setNewTemplateName] = useState('');
  // 新模板内容
  const [newTemplateContent, setNewTemplateContent] = useState(initialPrompt);
  // 新模板描述
  const [newTemplateDescription, setNewTemplateDescription] = useState('');
  // 是否显示新建模板表单
  const [showNewTemplateForm, setShowNewTemplateForm] = useState(false);
  // 搜索关键词
  const [searchQuery, setSearchQuery] = useState('');
  // 过滤后的模板列表
  const [filteredTemplates, setFilteredTemplates] = useState<PromptTemplate[]>([]);
  // 编辑模式
  const [isEditMode, setIsEditMode] = useState(false);
  // 当前编辑的模板ID
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);

  // 加载提示词模板
  useEffect(() => {
    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen, category]);

  // 过滤模板列表
  useEffect(() => {
    if (templates.length > 0) {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const filtered = templates.filter(template =>
          template.name.toLowerCase().includes(query) ||
          template.content.toLowerCase().includes(query) ||
          (template.description && template.description.toLowerCase().includes(query))
        );
        setFilteredTemplates(filtered);
      } else {
        setFilteredTemplates([...templates]);
      }
    }
  }, [templates, searchQuery]);

  // 加载提示词模板
  const loadTemplates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 获取指定分类的提示词模板
      const templatesData = await promptTemplateRepository.getByCategory(category);

      // 确保所有模板都有tags属性
      const processedTemplates = templatesData.map(template => {
        if (!template.tags) {
          // 添加默认标签
          const tags = ['用户自定义'];

          // 尝试根据模板名称推断分析模式
          if (template.name.includes('单本')) {
            tags.push('单本拆解');
          } else if (template.name.includes('合并')) {
            tags.push('合并拆解');
          } else if (template.name.includes('混合')) {
            tags.push('混合拆解');
          } else if (template.name.includes('同步')) {
            tags.push('同步拆解');
          }

          // 尝试更新模板标签
          try {
            promptTemplateRepository.update(template.id!, { tags });
          } catch (e) {
            console.error('更新模板标签失败:', e);
          }

          // 返回添加了标签的模板
          return {
            ...template,
            tags
          };
        }
        return template;
      });

      setTemplates(processedTemplates);
      setFilteredTemplates(processedTemplates);
    } catch (error) {
      console.error('加载提示词模板失败:', error);
      setError('加载提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 保存提示词模板
  const saveTemplate = async () => {
    if (!newTemplateName.trim()) {
      setError('请输入模板名称');
      return;
    }

    if (!newTemplateContent.trim()) {
      setError('请输入模板内容');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 根据模板名称推断标签
      const tags = ['用户自定义'];

      // 根据模板名称推断分析模式标签
      if (newTemplateName.includes('单本')) {
        tags.push('单本拆解');
      } else if (newTemplateName.includes('合并')) {
        tags.push('合并拆解');
      } else if (newTemplateName.includes('混合')) {
        tags.push('混合拆解');
      } else if (newTemplateName.includes('同步')) {
        tags.push('同步拆解');
      }

      // 创建新模板
      const now = new Date();
      const newTemplate: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        category,
        name: newTemplateName,
        content: newTemplateContent,
        description: newTemplateDescription || undefined,
        tags: tags // 添加推断的标签
      };

      // 保存模板
      console.log('正在保存提示词模板:', newTemplate);
      const templateId = await promptTemplateRepository.create(newTemplate);
      console.log('提示词模板保存成功, ID:', templateId);

      // 重新加载模板列表
      await loadTemplates();

      // 重置表单
      setNewTemplateName('');
      setNewTemplateContent('');
      setNewTemplateDescription('');
      setShowNewTemplateForm(false);

      // 显示成功消息
      setSuccessMessage('提示词模板保存成功');

      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('保存提示词模板失败:', error);
      setError('保存提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 删除提示词模板
  const deleteTemplate = async (templateId: string) => {
    if (!window.confirm('确定要删除这个提示词模板吗？')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 删除模板
      await promptTemplateRepository.delete(templateId);

      // 重新加载模板列表
      await loadTemplates();

      // 显示成功消息
      setSuccessMessage('提示词模板删除成功');

      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('删除提示词模板失败:', error);
      setError('删除提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 编辑提示词模板
  const editTemplate = (template: PromptTemplate) => {
    setNewTemplateName(template.name);
    setNewTemplateContent(template.content);
    setNewTemplateDescription(template.description || '');
    setEditingTemplateId(template.id!);
    setIsEditMode(true);
    setShowNewTemplateForm(true);
  };

  // 更新提示词模板
  const updateTemplate = async () => {
    if (!editingTemplateId) {
      setError('编辑ID无效');
      return;
    }

    if (!newTemplateName.trim()) {
      setError('请输入模板名称');
      return;
    }

    if (!newTemplateContent.trim()) {
      setError('请输入模板内容');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 导入 promptTemplateRepository
      const { promptTemplateRepository } = await import('@/lib/db/repositories');

      // 根据模板名称推断标签
      const tags = ['用户自定义'];

      // 根据模板名称推断分析模式标签
      if (newTemplateName.includes('单本')) {
        tags.push('单本拆解');
      } else if (newTemplateName.includes('合并')) {
        tags.push('合并拆解');
      } else if (newTemplateName.includes('混合')) {
        tags.push('混合拆解');
      } else if (newTemplateName.includes('同步')) {
        tags.push('同步拆解');
      }

      // 更新模板
      const templateUpdate: Partial<PromptTemplate> = {
        name: newTemplateName,
        content: newTemplateContent,
        description: newTemplateDescription || undefined,
        tags: tags // 添加推断的标签
      };

      console.log('正在更新提示词模板:', editingTemplateId, templateUpdate);
      await promptTemplateRepository.update(editingTemplateId, templateUpdate);
      console.log('提示词模板更新成功');

      // 重新加载模板列表
      await loadTemplates();

      // 重置表单和编辑状态
      setNewTemplateName('');
      setNewTemplateContent('');
      setNewTemplateDescription('');
      setShowNewTemplateForm(false);
      setIsEditMode(false);
      setEditingTemplateId(null);

      // 显示成功消息
      setSuccessMessage('提示词模板更新成功');

      // 3秒后关闭成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('更新提示词模板失败:', error);
      setError('更新提示词模板失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // 选择提示词模板
  const selectTemplate = (template: PromptTemplate) => {
    console.log('选择模板:', template);
    console.log('调用onSelectTemplate回调');
    onSelectTemplate(template);
    console.log('调用onClose回调');
    onClose();
  };

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) {
    console.log('PromptTemplateManager: 对话框未打开，不渲染');
    return null;
  }

  console.log('PromptTemplateManager: 正在渲染，isOpen =', isOpen, 'category =', category);

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]"
      onClick={(e) => {
        console.log('PromptTemplateManager背景被点击');
        e.stopPropagation();
        onClose();
      }}
    >
      <div
        className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col"
        onClick={(e) => {
          console.log('PromptTemplateManager内容区域被点击');
          e.stopPropagation();
        }}
      >
        {/* 对话框标题 */}
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800">提示词模板管理</h2>
          <p className="text-sm text-gray-600">管理{getCategoryDisplayName(category)}的提示词模板</p>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 flex-1 overflow-y-auto">
          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* 成功消息 */}
          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
              {successMessage}
            </div>
          )}

          {/* 搜索框和新建按钮 */}
          <div className="mb-4 flex items-center">
            <div className="relative flex-1 mr-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索提示词模板..."
                className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              onClick={(e) => {
                console.log('新建模板按钮被点击');
                e.preventDefault();
                e.stopPropagation();

                if (isEditMode) {
                  // 如果在编辑模式，取消编辑
                  console.log('取消编辑模式');
                  setIsEditMode(false);
                  setEditingTemplateId(null);
                  setNewTemplateName('');
                  setNewTemplateContent('');
                  setNewTemplateDescription('');
                }

                console.log('切换新建模板表单显示状态:', !showNewTemplateForm);
                setShowNewTemplateForm(!showNewTemplateForm);
              }}
            >
              {showNewTemplateForm ? (isEditMode ? '取消编辑' : '取消新建') : '新建模板'}
            </button>
          </div>

          {/* 新建/编辑模板表单 */}
          {showNewTemplateForm && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {isEditMode ? '编辑提示词模板' : '新建提示词模板'}
              </h3>
              <div className="space-y-3">
                <div>
                  <label htmlFor="template-name" className="block text-sm font-medium text-gray-700 mb-1">
                    模板名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="template-name"
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入模板名称..."
                  />
                </div>
                <div>
                  <label htmlFor="template-description" className="block text-sm font-medium text-gray-700 mb-1">
                    模板描述
                  </label>
                  <input
                    type="text"
                    id="template-description"
                    value={newTemplateDescription}
                    onChange={(e) => setNewTemplateDescription(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入模板描述..."
                  />
                </div>
                <div>
                  <label htmlFor="template-content" className="block text-sm font-medium text-gray-700 mb-1">
                    模板内容 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="template-content"
                    value={newTemplateContent}
                    onChange={(e) => setNewTemplateContent(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入模板内容..."
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  {isEditMode ? (
                    <>
                      <button
                        className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                        onClick={() => {
                          setIsEditMode(false);
                          setEditingTemplateId(null);
                          setNewTemplateName('');
                          setNewTemplateContent('');
                          setNewTemplateDescription('');
                          setShowNewTemplateForm(false);
                        }}
                        disabled={isLoading}
                      >
                        取消编辑
                      </button>
                      <button
                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        onClick={updateTemplate}
                        disabled={isLoading}
                      >
                        {isLoading ? '更新中...' : '更新模板'}
                      </button>
                    </>
                  ) : (
                    <button
                      className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                      onClick={saveTemplate}
                      disabled={isLoading}
                    >
                      {isLoading ? '保存中...' : '保存模板'}
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 模板列表 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-800 mb-3">提示词模板列表</h3>
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <p className="text-center text-gray-500 py-4">
                {templates.length === 0 ? '暂无提示词模板' : '没有找到匹配的提示词模板'}
              </p>
            ) : (
              <div className="space-y-3">
                {filteredTemplates.map((template) => (
                  <div key={template.id} className="bg-white p-3 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-md font-medium text-gray-800">{template.name}</h4>
                        {template.description && (
                          <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          className="p-1 text-blue-500 hover:text-blue-700 transition-colors"
                          onClick={() => selectTemplate(template)}
                          title="使用此模板"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                        <button
                          className="p-1 text-green-500 hover:text-green-700 transition-colors"
                          onClick={() => editTemplate(template)}
                          title="编辑此模板"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          className="p-1 text-red-500 hover:text-red-700 transition-colors"
                          onClick={() => deleteTemplate(template.id!)}
                          title="删除此模板"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-700 max-h-32 overflow-y-auto">
                      <pre className="whitespace-pre-wrap">{template.content}</pre>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 对话框底部按钮 */}
        <div className="p-4 border-t flex justify-end">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

// 获取分类显示名称
function getCategoryDisplayName(category: PromptCategory): string {
  switch (category) {
    case PromptCategory.WRITING_REQUIREMENTS:
      return '写作要求';
    case PromptCategory.WRITING_STYLE:
      return '写作风格';
    case PromptCategory.CONTINUE_REQUIREMENTS:
      return '续写要求';
    case PromptCategory.CONTINUE_STYLE:
      return '续写风格';
    case PromptCategory.REWRITE_REQUIREMENTS:
      return '改写要求';
    case PromptCategory.REWRITE_PLOT:
      return '剧情方向';
    case PromptCategory.BOOK_ANALYSIS:
      return '书籍分析';
    case PromptCategory.WORLD_BUILDING:
      return '世界观提取';
    case PromptCategory.TERMINOLOGY:
      return '术语提取';
    default:
      return '未知分类';
  }
}
