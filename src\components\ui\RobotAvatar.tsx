"use client";

import React from 'react';
import { motion } from 'framer-motion';

export type RobotEmotion = 'default' | 'thinking' | 'excited' | 'loading' | 'confused';

interface RobotAvatarProps {
  emotion?: RobotEmotion;
  isAnimating?: boolean;
  className?: string;
}

/**
 * AI机器人动态表情头像组件
 * 支持多种表情状态和动画效果
 */
const RobotAvatar: React.FC<RobotAvatarProps> = ({
  emotion = 'default',
  isAnimating = false,
  className = ''
}) => {
  // 表情动画变体
  const emotionVariants = {
    default: {
      scale: [1, 1.02, 1],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    thinking: {
      scale: 1,
      rotate: [0, -2, 2, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    excited: {
      scale: [1, 1.05, 1],
      rotate: [0, -3, 3, 0],
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    },
    loading: {
      scale: 1,
      transition: {
        duration: 0.3
      }
    },
    confused: {
      scale: 1,
      rotate: [0, -5, 5, -5, 0],
      transition: {
        duration: 1,
        ease: "easeInOut"
      }
    }
  };

  // 眼睛动画
  const getEyeAnimation = () => {
    switch (emotion) {
      case 'thinking':
        return {
          x: [0, -1, 1, 0],
          transition: { duration: 2, repeat: Infinity }
        };
      case 'excited':
        return {
          scaleY: [1, 0.3, 1],
          transition: { duration: 0.3 }
        };
      case 'confused':
        return {
          scaleX: [1, 1.2, 1],
          transition: { duration: 0.5 }
        };
      default:
        return {};
    }
  };

  // 嘴巴路径
  const getMouthPath = () => {
    switch (emotion) {
      case 'thinking':
        return "M18 24 Q20 25 22 24"; // 小圆形
      case 'excited':
        return "M14 23 Q20 28 26 23"; // 大笑
      case 'confused':
        return "M16 25 L24 25"; // 直线
      case 'loading':
        return "M17 24 Q20 26 23 24"; // 轻微开口
      default:
        return "M15 24 Q20 27 25 24"; // 微笑
    }
  };

  // 天线指示灯动画
  const getAntennaAnimation = () => {
    if (isAnimating || emotion === 'thinking') {
      return {
        opacity: [0.3, 1, 0.3],
        scale: [1, 1.2, 1],
        transition: {
          duration: 0.8,
          repeat: Infinity,
          ease: "easeInOut"
        }
      };
    }
    return {
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    };
  };

  // 天线指示灯颜色
  const getAntennaColor = () => {
    switch (emotion) {
      case 'thinking':
        return '#F59E0B'; // 黄色
      case 'excited':
        return '#10B981'; // 绿色
      case 'confused':
        return '#EF4444'; // 红色
      case 'loading':
        return '#3B82F6'; // 蓝色
      default:
        return '#6366F1'; // 默认紫色
    }
  };

  return (
    <motion.div
      className={`flex-shrink-0 ${className}`}
      variants={emotionVariants}
      animate={emotion}
    >
      <svg
        viewBox="0 0 40 40"
        className="w-10 h-10"
        style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}
      >
        {/* 渐变定义 */}
        <defs>
          <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#4F46E5" />
            <stop offset="50%" stopColor="#7C3AED" />
            <stop offset="100%" stopColor="#8B5CF6" />
          </linearGradient>
          <linearGradient id="robotHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.3" />
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0" />
          </linearGradient>
        </defs>

        {/* 头部主体 */}
        <circle
          cx="20"
          cy="20"
          r="16"
          fill="url(#robotGradient)"
          stroke="#3730A3"
          strokeWidth="1"
        />

        {/* 头部高光 */}
        <ellipse
          cx="16"
          cy="14"
          rx="6"
          ry="4"
          fill="url(#robotHighlight)"
        />

        {/* 左眼 */}
        <motion.g animate={getEyeAnimation()}>
          <circle cx="15" cy="16" r="3" fill="#FFFFFF" />
          <circle cx="15" cy="16" r="1.5" fill="#1F2937" />
          {emotion === 'loading' && (
            <motion.circle
              cx="15"
              cy="16"
              r="1"
              fill="#3B82F6"
              animate={{
                rotate: 360,
                transition: { duration: 1, repeat: Infinity, ease: "linear" }
              }}
            />
          )}
        </motion.g>

        {/* 右眼 */}
        <motion.g animate={getEyeAnimation()}>
          <circle cx="25" cy="16" r="3" fill="#FFFFFF" />
          <circle cx="25" cy="16" r="1.5" fill="#1F2937" />
          {emotion === 'loading' && (
            <motion.circle
              cx="25"
              cy="16"
              r="1"
              fill="#3B82F6"
              animate={{
                rotate: 360,
                transition: { duration: 1, repeat: Infinity, ease: "linear" }
              }}
            />
          )}
        </motion.g>

        {/* 嘴巴 */}
        <motion.path
          d={getMouthPath()}
          stroke="#FFFFFF"
          strokeWidth="2"
          fill="none"
          strokeLinecap="round"
          animate={emotion === 'loading' ? {
            pathLength: [0, 1, 0],
            transition: { duration: 1.5, repeat: Infinity }
          } : {}}
        />

        {/* 天线 */}
        <line
          x1="20"
          y1="4"
          x2="20"
          y2="8"
          stroke="#3730A3"
          strokeWidth="2"
          strokeLinecap="round"
        />

        {/* 天线指示灯 */}
        <motion.circle
          cx="20"
          cy="4"
          r="2"
          fill={getAntennaColor()}
          animate={getAntennaAnimation()}
        />

        {/* 装饰性螺丝 */}
        <circle cx="12" cy="28" r="1" fill="#3730A3" opacity="0.6" />
        <circle cx="28" cy="28" r="1" fill="#3730A3" opacity="0.6" />

        {/* 思考时的问号 */}
        {emotion === 'thinking' && (
          <motion.text
            x="32"
            y="12"
            fontSize="8"
            fill="#F59E0B"
            fontWeight="bold"
            animate={{
              opacity: [0, 1, 0],
              y: [12, 8, 12],
              transition: { duration: 2, repeat: Infinity }
            }}
          >
            ?
          </motion.text>
        )}

        {/* 兴奋时的星星 */}
        {emotion === 'excited' && (
          <>
            <motion.text
              x="8"
              y="10"
              fontSize="6"
              fill="#10B981"
              animate={{
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
                transition: { duration: 0.8, delay: 0.2 }
              }}
            >
              ✨
            </motion.text>
            <motion.text
              x="30"
              y="8"
              fontSize="6"
              fill="#10B981"
              animate={{
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
                transition: { duration: 0.8, delay: 0.4 }
              }}
            >
              ✨
            </motion.text>
          </>
        )}
      </svg>
    </motion.div>
  );
};

export default RobotAvatar;
