"use client";

import React, { useState, useCallback, createContext, useContext, ReactNode, useEffect } from 'react';
import { Outline, OutlineNodeType } from '../types/outline';
import { outlineService } from '../../api/outlineService';

interface OutlineManagerContextType {
  isOpen: boolean;
  outline: Outline | null;
  openOutlineManager: () => Promise<void>;
  closeOutlineManager: () => void;
  saveOutline: (outline: Outline) => Promise<void>;
  updateNode: (nodeId: string, updates: Partial<OutlineNodeType>) => void;
  addNode: (parentId: string | null, node: Partial<OutlineNodeType>) => void;
  deleteNode: (nodeId: string) => void;
  moveNode: (nodeId: string, targetParentId: string | null, position: number) => void;
  addNodeRelation: (nodeId: string, relationType: 'character' | 'worldBuilding' | 'terminology', relationId: string) => Promise<void>;
  removeNodeRelation: (nodeId: string, relationType: 'character' | 'worldBuilding' | 'terminology', relationId: string) => Promise<void>;
  getNodeRelations: (nodeId: string) => Promise<{
    characters: any[];
    worldBuildings: any[];
    terminologies: any[];
  }>;
}

const OutlineManagerContext = createContext<OutlineManagerContextType | undefined>(undefined);

interface OutlineManagerProviderProps {
  children: ReactNode;
  bookId?: string;
}

export const OutlineManagerProvider: React.FC<OutlineManagerProviderProps> = ({
  children,
  bookId
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [outline, setOutline] = useState<Outline | null>(null);
  const [currentBookId, setCurrentBookId] = useState<string | undefined>(bookId);

  // 打开大纲管理器
  const openOutlineManager = useCallback(async () => {
    if (!currentBookId) {
      console.error('未指定书籍ID');
      return;
    }

    console.log('打开大纲管理器:', currentBookId);

    try {
      // 初始化数据库
      try {
        const { initializeDatabase, validateOutlineData } = await import('@/lib/db/initialize');
        await initializeDatabase();

        // 验证大纲数据
        const validatedOutline = await validateOutlineData(currentBookId);
        if (validatedOutline) {
          console.log('大纲数据验证成功:', validatedOutline);
        }
      } catch (initError) {
        console.error('初始化数据库失败:', initError);
        // 继续执行，尝试正常流程
      }

      // 从API获取大纲数据
      const outlineData = await outlineService.getOutline(currentBookId);

      if (outlineData) {
        console.log('获取到现有大纲:', outlineData);

        // 确保nodes字段存在
        if (!outlineData.nodes) {
          console.log('大纲缺少nodes字段，添加空数组');
          outlineData.nodes = [];
        }

        setOutline(outlineData);
      } else {
        console.log('未找到大纲，创建新大纲');

        // 创建新大纲对象
        const newOutline = {
          id: `outline-${Date.now()}`,
          workId: currentBookId,
          title: '大纲',
          nodes: [],
          lastModified: new Date(),
          version: 1
        };

        console.log('新大纲对象:', newOutline);

        // 保存新大纲
        try {
          console.log('保存新大纲');
          const savedOutline = await outlineService.saveOutline(currentBookId, newOutline);
          console.log('新大纲保存成功:', savedOutline);
          setOutline(savedOutline);
        } catch (saveError) {
          console.error('保存新大纲失败:', saveError);
          // 如果保存失败，仍然设置本地状态，以便用户可以看到大纲
          setOutline(newOutline);
        }
      }

      setIsOpen(true);
    } catch (error) {
      console.error('加载大纲失败:', error);

      // 如果加载失败，创建一个空大纲
      const emptyOutline = {
        id: `outline-${Date.now()}`,
        workId: currentBookId,
        title: '大纲',
        nodes: [],
        lastModified: new Date(),
        version: 1
      };

      setOutline(emptyOutline);
      setIsOpen(true);
    }
  }, [currentBookId]);

  // 当bookId变化时更新currentBookId
  useEffect(() => {
    if (bookId) {
      setCurrentBookId(bookId);
    }
  }, [bookId]);

  // 监听自定义事件
  useEffect(() => {
    console.log('设置自定义事件监听器');

    const handleOpenOutlineManager = async () => {
      console.log('收到openOutlineManager事件，调用openOutlineManager函数');
      await openOutlineManager();
    };

    // 获取父元素
    const outlineManagerProvider = document.querySelector('[data-outline-manager-provider="true"]');

    if (outlineManagerProvider) {
      console.log('找到大纲管理器提供者元素，添加事件监听器');

      // 添加事件监听器
      outlineManagerProvider.addEventListener('openOutlineManager', handleOpenOutlineManager);

      // 清理函数
      return () => {
        console.log('移除事件监听器');
        outlineManagerProvider.removeEventListener('openOutlineManager', handleOpenOutlineManager);
      };
    } else {
      console.error('未找到大纲管理器提供者元素');
    }
  }, [openOutlineManager]);

  // 关闭大纲管理器
  const closeOutlineManager = useCallback(() => {
    setIsOpen(false);
  }, []);

  // 保存大纲
  const saveOutline = useCallback(async (updatedOutline: Outline) => {
    if (!currentBookId) {
      console.error('未指定书籍ID');
      return;
    }

    try {
      console.log('准备保存大纲:', updatedOutline);

      // 创建一个新对象，避免直接修改传入的对象
      const outlineToSave = { ...updatedOutline };

      // 确保大纲有ID和workId
      if (!outlineToSave.id) {
        console.log('大纲缺少ID，添加ID');
        outlineToSave.id = `outline-${Date.now()}`;
      }

      if (!outlineToSave.workId) {
        console.log('大纲缺少workId，添加workId:', currentBookId);
        outlineToSave.workId = currentBookId;
      }

      // 更新lastModified和version
      outlineToSave.lastModified = new Date();
      outlineToSave.version = outlineToSave.version ? outlineToSave.version + 1 : 1;

      // 确保nodes字段存在
      if (!outlineToSave.nodes) {
        console.log('初始化空nodes数组');
        outlineToSave.nodes = [];
      }

      console.log('保存大纲数据到API:', outlineToSave);

      // 尝试最多3次保存
      let savedOutline = null;
      let lastError = null;

      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`保存尝试 #${attempt}`);
          savedOutline = await outlineService.saveOutline(currentBookId, outlineToSave);
          console.log('大纲保存成功:', savedOutline);
          break;
        } catch (error) {
          console.error(`保存尝试 #${attempt} 失败:`, error);
          lastError = error;

          if (attempt < 3) {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      if (savedOutline) {
        // 保存成功，更新本地状态
        setOutline(savedOutline);
        return savedOutline;
      } else {
        // 所有尝试都失败，但仍然更新本地状态以保持UI一致性
        console.error('所有保存尝试都失败，更新本地状态:', lastError);
        setOutline(outlineToSave);
        return outlineToSave;
      }
    } catch (error) {
      console.error('保存大纲过程中发生错误:', error);
      throw error;
    }
  }, [currentBookId]);

  // 更新节点
  const updateNode = useCallback((nodeId: string, updates: Partial<OutlineNodeType>) => {
    if (!outline) return;

    const updateNodeInTree = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, ...updates };
        }

        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: updateNodeInTree(node.children)
          };
        }

        return node;
      });
    };

    const updatedNodes = updateNodeInTree(outline.nodes);
    setOutline({
      ...outline,
      nodes: updatedNodes
    });
  }, [outline]);

  // 添加节点
  const addNode = useCallback((parentId: string | null, nodeData: Partial<OutlineNodeType>) => {
    if (!outline) return;

    const newNode: OutlineNodeType = {
      id: `node-${Date.now()}`,
      title: nodeData.title || '新节点',
      type: nodeData.type || 'chapter',
      ...nodeData,
      children: nodeData.children || []
    };

    if (!parentId) {
      // 添加到根节点
      setOutline({
        ...outline,
        nodes: [...outline.nodes, newNode]
      });
      return;
    }

    // 添加到指定父节点
    const addNodeToParent = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.map(node => {
        if (node.id === parentId) {
          return {
            ...node,
            children: [...(node.children || []), newNode],
            expanded: true // 自动展开父节点
          };
        }

        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: addNodeToParent(node.children)
          };
        }

        return node;
      });
    };

    const updatedNodes = addNodeToParent(outline.nodes);
    setOutline({
      ...outline,
      nodes: updatedNodes
    });
  }, [outline]);

  // 删除节点
  const deleteNode = useCallback((nodeId: string) => {
    if (!outline) return;

    const deleteNodeFromTree = (nodes: OutlineNodeType[]): OutlineNodeType[] => {
      return nodes.filter(node => {
        if (node.id === nodeId) {
          return false;
        }

        if (node.children && node.children.length > 0) {
          node.children = deleteNodeFromTree(node.children);
        }

        return true;
      });
    };

    const updatedNodes = deleteNodeFromTree(outline.nodes);
    setOutline({
      ...outline,
      nodes: updatedNodes
    });
  }, [outline]);

  // 移动节点
  const moveNode = useCallback((nodeId: string, targetParentId: string | null, position: number) => {
    if (!outline) return;

    // 查找节点
    let nodeToMove: OutlineNodeType | null = null;
    let sourceParentId: string | null = null;

    const findNode = (nodes: OutlineNodeType[], parentId: string | null): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === nodeId) {
          nodeToMove = nodes[i];
          sourceParentId = parentId;
          return true;
        }

        if (nodes[i].children && nodes[i].children.length > 0) {
          if (findNode(nodes[i].children, nodes[i].id)) {
            return true;
          }
        }
      }

      return false;
    };

    findNode(outline.nodes, null);

    if (!nodeToMove) return;

    // 删除原节点
    const removeNode = (nodes: OutlineNodeType[], parentId: string | null): OutlineNodeType[] => {
      if (parentId === sourceParentId) {
        return nodes.filter(node => node.id !== nodeId);
      }

      return nodes.map(node => {
        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: removeNode(node.children, node.id)
          };
        }

        return node;
      });
    };

    let updatedNodes = removeNode(outline.nodes, null);

    // 插入到新位置
    const insertNode = (nodes: OutlineNodeType[], parentId: string | null): OutlineNodeType[] => {
      if (parentId === targetParentId) {
        const result = [...nodes];
        result.splice(position, 0, nodeToMove!);
        return result;
      }

      return nodes.map(node => {
        if (node.id === targetParentId) {
          const children = node.children || [];
          const updatedChildren = [...children];
          updatedChildren.splice(position, 0, nodeToMove!);

          return {
            ...node,
            children: updatedChildren,
            expanded: true // 自动展开目标父节点
          };
        }

        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: insertNode(node.children, node.id)
          };
        }

        return node;
      });
    };

    updatedNodes = insertNode(updatedNodes, null);

    setOutline({
      ...outline,
      nodes: updatedNodes
    });
  }, [outline]);

  // 添加节点关联
  const addNodeRelation = useCallback(async (
    nodeId: string,
    relationType: 'character' | 'worldBuilding' | 'terminology',
    relationId: string
  ) => {
    if (!currentBookId) {
      console.error('未指定书籍ID');
      return;
    }

    try {
      const updatedOutline = await outlineService.addNodeRelation(
        currentBookId,
        nodeId,
        relationType,
        relationId
      );

      if (updatedOutline) {
        setOutline(updatedOutline);
      }
    } catch (error) {
      console.error('添加节点关联失败:', error);
    }
  }, [currentBookId]);

  // 移除节点关联
  const removeNodeRelation = useCallback(async (
    nodeId: string,
    relationType: 'character' | 'worldBuilding' | 'terminology',
    relationId: string
  ) => {
    if (!currentBookId) {
      console.error('未指定书籍ID');
      return;
    }

    try {
      const updatedOutline = await outlineService.removeNodeRelation(
        currentBookId,
        nodeId,
        relationType,
        relationId
      );

      if (updatedOutline) {
        setOutline(updatedOutline);
      }
    } catch (error) {
      console.error('移除节点关联失败:', error);
    }
  }, [currentBookId]);

  // 获取节点关联元素
  const getNodeRelations = useCallback(async (nodeId: string) => {
    if (!currentBookId) {
      console.error('未指定书籍ID');
      return {
        characters: [],
        worldBuildings: [],
        terminologies: []
      };
    }

    try {
      return await outlineService.getNodeRelations(currentBookId, nodeId);
    } catch (error) {
      console.error('获取节点关联元素失败:', error);
      return {
        characters: [],
        worldBuildings: [],
        terminologies: []
      };
    }
  }, [currentBookId]);

  return (
    <OutlineManagerContext.Provider
      value={{
        isOpen,
        outline,
        openOutlineManager,
        closeOutlineManager,
        saveOutline,
        updateNode,
        addNode,
        deleteNode,
        moveNode,
        addNodeRelation,
        removeNodeRelation,
        getNodeRelations
      }}
    >
      {children}
    </OutlineManagerContext.Provider>
  );
};

export const useOutlineManager = () => {
  const context = useContext(OutlineManagerContext);
  if (!context) {
    throw new Error('useOutlineManager must be used within an OutlineManagerProvider');
  }
  return context;
};
