"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AIPersonaConfig, PhaseType } from '../../../types/ai-persona';
import { PersonaStorageService } from '../../../services/ai-persona/PersonaStorageService';
import PersonaCard from './PersonaCard';
import RecentPersonaPanel from './RecentPersonaPanel';

interface PersonaFolderManagerProps {
  phase: PhaseType;
  onPersonaSelect?: (persona: AIPersonaConfig) => void;
  onVersionManage?: (persona: AIPersonaConfig) => void;
  onCategoryManage?: (persona: AIPersonaConfig) => void;
}

const PersonaFolderManager: React.FC<PersonaFolderManagerProps> = ({
  phase,
  onPersonaSelect,
  onVersionManage,
  onCategoryManage
}) => {
  const [allPersonas, setAllPersonas] = useState<AIPersonaConfig[]>([]);
  const [recentPersonas, setRecentPersonas] = useState<AIPersonaConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [personaStorageService] = useState(() => PersonaStorageService.getInstance());

  // 加载人设数据
  const loadPersonaData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 获取所有人设配置
      const allConfigs = await personaStorageService.getAllPersonaConfigs();
      const allPersonasArray = Object.values(allConfigs);

      // 筛选出当前阶段的人设
      const currentPhasePersonas = allPersonasArray.filter(persona => persona.phase === phase);
      setAllPersonas(currentPhasePersonas);

      // 获取最近使用的人设（也要筛选当前阶段）
      const allRecent = await personaStorageService.getRecentPersonas(10); // 获取更多，然后筛选
      const currentPhaseRecent = allRecent.filter(persona => persona.phase === phase).slice(0, 3);
      setRecentPersonas(currentPhaseRecent);

      console.log('人设数据加载完成:', {
        phase: phase,
        total: currentPhasePersonas.length,
        recent: currentPhaseRecent.length,
        allPersonas: allPersonasArray.length
      });
    } catch (err) {
      console.error('加载人设数据失败:', err);
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时加载数据，以及当阶段改变时重新加载
  useEffect(() => {
    loadPersonaData();
  }, [phase]); // 依赖phase，当阶段改变时重新加载

  // 处理人设选择
  const handlePersonaSelect = (persona: AIPersonaConfig) => {
    console.log('选择人设:', persona.id);
    onPersonaSelect?.(persona);
  };

  // 处理版本管理
  const handleVersionManage = (persona: AIPersonaConfig) => {
    console.log('管理版本:', persona.id);
    onVersionManage?.(persona);
  };

  // 处理分类管理
  const handleCategoryManage = (persona: AIPersonaConfig) => {
    console.log('管理分类:', persona.id);
    onCategoryManage?.(persona);
  };

  // 处理人设编辑
  const handlePersonaEdit = async (persona: AIPersonaConfig) => {
    try {
      // 更新使用统计
      await personaStorageService.updateUsageStats(persona.id);
      // 重新加载数据以更新最近使用列表
      await loadPersonaData();
      // 选择人设
      handlePersonaSelect(persona);
    } catch (err) {
      console.error('编辑人设失败:', err);
    }
  };

  // 获取阶段显示名称
  const getPhaseDisplayName = (phase: PhaseType): string => {
    const phaseNames: Record<PhaseType, string> = {
      'intro': '导语阶段',
      'setup': '铺垫期',
      'compression': '爆发情绪阶段',
      'climax': '反转阶段',
      'resolution': '让读者解气阶段',
      'ending': '大结局阶段',
      'buildup': '铺垫期',
      'custom': '自定义阶段'
    };
    return phaseNames[phase] || phase;
  };

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <motion.div
          className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <p className="text-gray-600">正在加载人设数据...</p>
      </div>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-red-600 mb-2">加载失败</p>
        <p className="text-gray-500 text-sm mb-4">{error}</p>
        <button
          onClick={loadPersonaData}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          重试
        </button>
      </div>
    </div>
  );

  // 渲染主内容
  const renderContent = () => {
    if (isLoading) return renderLoadingState();
    if (error) return renderErrorState();

    return (
      <div className="space-y-8">
        {/* 最近使用的人设面板 */}
        <RecentPersonaPanel
          recentPersonas={recentPersonas}
          onPersonaSelect={handlePersonaEdit}
          onVersionManage={handleVersionManage}
          onCategoryManage={handleCategoryManage}
          getPhaseDisplayName={getPhaseDisplayName}
        />

        {/* 当前阶段人设的文件夹视图 */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800">{getPhaseDisplayName(phase)} 人设</h3>
              <p className="text-sm text-gray-500 mt-1">当前阶段的人设管理</p>
            </div>
            <span className="text-sm text-gray-500">{allPersonas.length} 个人设</span>
          </div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            layout
          >
            <AnimatePresence mode="popLayout">
              {allPersonas.map((persona, index) => (
                <motion.div
                  key={persona.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    type: "spring",
                    stiffness: 400,
                    damping: 25
                  }}
                >
                  <PersonaCard
                    persona={persona}
                    onSelect={() => handlePersonaEdit(persona)}
                    onVersionManage={() => handleVersionManage(persona)}
                    onCategoryManage={() => handleCategoryManage(persona)}
                    getPhaseDisplayName={getPhaseDisplayName}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {/* 空状态 */}
          {allPersonas.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <p className="text-gray-500 mb-2">当前阶段暂无人设</p>
                <p className="text-gray-400 text-sm">在编辑页面创建 {getPhaseDisplayName(phase)} 的人设配置</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full p-4">
      {renderContent()}
    </div>
  );
};

export default PersonaFolderManager;
