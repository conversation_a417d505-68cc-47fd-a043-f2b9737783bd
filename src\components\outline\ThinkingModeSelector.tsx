"use client";

import React from 'react';
import { motion } from 'framer-motion';

export type ThinkingMode = 'standard' | 'thinking';

interface ThinkingModeSelectorProps {
  mode: ThinkingMode;
  onModeChange: (mode: ThinkingMode) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * 思考模式选择器组件
 * 支持在标准模式和思考模式之间切换
 */
const ThinkingModeSelector: React.FC<ThinkingModeSelectorProps> = ({
  mode,
  onModeChange,
  disabled = false,
  className = ''
}) => {
  const modes = [
    {
      id: 'standard' as ThinkingMode,
      label: '标准模式',
      description: '直接生成大纲节点',
      icon: '⚡',
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'thinking' as ThinkingMode,
      label: '思考模式',
      description: '先思考再写大纲',
      icon: '💭',
      color: 'from-blue-500 to-purple-500'
    }
  ];

  return (
    <div className={`thinking-mode-selector ${className}`}>
      {/* 标题 */}
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-700 mb-1">生成模式</h4>
        <p className="text-xs text-gray-500">选择AI的工作方式</p>
      </div>
      
      {/* 模式选择器 */}
      <div className="relative bg-gray-100 rounded-lg p-1">
        {/* 滑动背景 */}
        <motion.div
          className="absolute top-1 bottom-1 bg-white rounded-md shadow-sm"
          initial={false}
          animate={{
            left: mode === 'standard' ? '4px' : '50%',
            right: mode === 'standard' ? '50%' : '4px'
          }}
          transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        />
        
        {/* 模式选项 */}
        <div className="relative flex">
          {modes.map((modeOption) => {
            const isSelected = mode === modeOption.id;
            
            return (
              <button
                key={modeOption.id}
                onClick={() => !disabled && onModeChange(modeOption.id)}
                disabled={disabled}
                className={`
                  flex-1 relative z-10 px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200
                  ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
                  ${isSelected 
                    ? 'text-gray-900' 
                    : 'text-gray-600 hover:text-gray-900'
                  }
                `}
              >
                <div className="flex flex-col items-center gap-1">
                  <div className="flex items-center gap-1">
                    <span className="text-base">{modeOption.icon}</span>
                    <span>{modeOption.label}</span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {modeOption.description}
                  </span>
                </div>
              </button>
            );
          })}
        </div>
      </div>
      
      {/* 模式说明 */}
      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-start gap-2">
          <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${modes.find(m => m.id === mode)?.color} flex items-center justify-center flex-shrink-0`}>
            <span className="text-white text-sm">
              {modes.find(m => m.id === mode)?.icon}
            </span>
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900 mb-1">
              {modes.find(m => m.id === mode)?.label}
            </div>
            <div className="text-xs text-gray-600">
              {mode === 'standard' ? (
                <>
                  AI直接根据您的需求生成大纲节点，速度快，适合简单的创作需求。
                </>
              ) : (
                <>
                  AI先进行深度思考分析，然后基于思考内容生成大纲节点。您可以查看和编辑AI的思考过程，获得更高质量的创作建议。
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* 功能特点对比 */}
      <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
        <div className={`p-2 rounded border-2 transition-colors ${
          mode === 'standard' 
            ? 'border-green-200 bg-green-50' 
            : 'border-gray-200 bg-gray-50'
        }`}>
          <div className="font-medium text-green-700 mb-1">⚡ 标准模式</div>
          <ul className="text-gray-600 space-y-1">
            <li>• 快速生成</li>
            <li>• 直接输出</li>
            <li>• 适合简单需求</li>
          </ul>
        </div>
        
        <div className={`p-2 rounded border-2 transition-colors ${
          mode === 'thinking' 
            ? 'border-blue-200 bg-blue-50' 
            : 'border-gray-200 bg-gray-50'
        }`}>
          <div className="font-medium text-blue-700 mb-1">💭 思考模式</div>
          <ul className="text-gray-600 space-y-1">
            <li>• 深度分析</li>
            <li>• 可编辑思考</li>
            <li>• 高质量输出</li>
          </ul>
        </div>
      </div>
      
      {/* 使用提示 */}
      {mode === 'thinking' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs">💡</span>
            </div>
            <div className="text-xs text-blue-700">
              <div className="font-medium mb-1">思考模式使用提示：</div>
              <ul className="space-y-1">
                <li>1. AI会先生成思考画布，展示分析过程</li>
                <li>2. 您可以查看和编辑思考内容</li>
                <li>3. 基于思考内容生成高质量大纲节点</li>
              </ul>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ThinkingModeSelector;
