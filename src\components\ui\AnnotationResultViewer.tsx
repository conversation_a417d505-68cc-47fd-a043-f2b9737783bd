"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TextSegment, Sentence } from '@/factories/ai/services/TextProcessingService';
import { useSelectionMode } from '@/hooks/useSelectionMode';
import { SentenceCheckbox } from './SentenceCheckbox';
import { SelectionModeToggle, SelectionModeIndicator } from './SelectionModeToggle';
import { SelectionToolbar } from './SelectionToolbar';

interface AnnotationResultViewerProps {
  segments: TextSegment[];
  onSentenceApply?: (segmentId: string, sentenceId: string) => void;
  onSentenceReject?: (segmentId: string, sentenceId: string) => void;
  onBatchApply?: (segmentId: string) => void;
  onBatchReject?: (segmentId: string) => void;
  onSentenceEdit?: (segmentId: string, sentenceId: string, newSuggestion: string) => void;
  onTextChange?: (newText: string) => void;
  onSegmentsUpdate?: (updatedSegments: TextSegment[]) => void;
  onSave?: (segments: TextSegment[]) => void;
  className?: string;
  // 选择功能相关
  enableSelectionMode?: boolean; // 是否启用选择模式功能
  onStartAnalysis?: (selectedSentenceIds: string[]) => void; // 开始分析选中句子的回调
}

/**
 * 标注结果显示组件
 * 左侧显示原文（绿色背景），右侧显示修改建议
 */
const AnnotationResultViewer: React.FC<AnnotationResultViewerProps> = ({
  segments,
  onSentenceApply,
  onSentenceReject,
  onBatchApply,
  onBatchReject,
  onSentenceEdit,
  onTextChange,
  onSegmentsUpdate,
  onSave,
  className = '',
  enableSelectionMode = false,
  onStartAnalysis
}) => {
  const [selectedSentence, setSelectedSentence] = useState<string | null>(null);
  const [expandedSegments, setExpandedSegments] = useState<Set<string>>(new Set());
  const [leftPanelWidth, setLeftPanelWidth] = useState(60); // 左侧面板宽度百分比
  const [editingSentence, setEditingSentence] = useState<string | null>(null);
  const [editingText, setEditingText] = useState<string>('');
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [showHelpTooltip, setShowHelpTooltip] = useState(false);

  // 选择模式管理
  const {
    isSelectionMode,
    selectedSentenceIds,
    selectedCount,
    totalSentenceCount,
    toggleSelectionMode,
    toggleSentenceSelection,
    batchSelect,
    isSentenceSelected,
    getSelectedSentences,
    clearSelection,
    undoSelection,
    selectionHistory
  } = useSelectionMode(segments);
  const [activeTab, setActiveTab] = useState<'suggestion' | 'creation' | 'alternatives' | 'analysis'>('suggestion');
  const [selectedAlternative, setSelectedAlternative] = useState<any | null>(null);

  // 键盘快捷键支持
  useEffect(() => {
    // 只有启用选择模式功能时才添加键盘监听
    if (!enableSelectionMode) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // 只在选择模式下处理快捷键
      if (!isSelectionMode) return;

      // 避免在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.shiftKey && e.key === 'A') {
        e.preventDefault();
        batchSelect('all');
        console.log('🔄 快捷键：全选');
      } else if (e.shiftKey && e.key === 'D') {
        e.preventDefault();
        batchSelect('none');
        console.log('🔄 快捷键：取消全选');
      } else if (e.key === 'Escape') {
        e.preventDefault();
        toggleSelectionMode();
        console.log('🔄 快捷键：退出选择模式');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enableSelectionMode, isSelectionMode, batchSelect, toggleSelectionMode]);


  // 切换段落展开状态
  const toggleSegmentExpansion = useCallback((segmentId: string) => {
    setExpandedSegments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(segmentId)) {
        newSet.delete(segmentId);
      } else {
        newSet.add(segmentId);
      }
      return newSet;
    });
  }, []);

  // 开始编辑建议
  const startEditingSuggestion = useCallback((sentenceId: string, currentSuggestion: string) => {
    setEditingSentence(sentenceId);
    setEditingText(currentSuggestion);
  }, []);

  // 保存编辑的建议
  const saveEditedSuggestion = useCallback(() => {
    if (!editingSentence || !editingText.trim()) return;

    // 找到对应的段落ID
    let segmentId = '';
    for (const segment of segments) {
      if (segment.sentences.find(s => s.id === editingSentence)) {
        segmentId = segment.id;
        break;
      }
    }

    if (segmentId) {
      onSentenceEdit?.(segmentId, editingSentence, editingText.trim());
    }

    setEditingSentence(null);
    setEditingText('');
  }, [editingSentence, editingText, segments, onSentenceEdit]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingSentence(null);
    setEditingText('');
  }, []);

  // 检查段落是否已处理完成
  const isSegmentProcessed = useCallback((segment: TextSegment) => {
    return segment.sentences.every(sentence =>
      sentence.aiSuggestion !== undefined || sentence.modificationType === 'keep'
    );
  }, []);

  // 计算段落处理状态
  const getSegmentStatus = useCallback((segment: TextSegment) => {
    const sentences = segment.sentences as any[];

    // 统计各种状态的句子数量
    const pendingCount = sentences.filter(s =>
      !s.processingStatus || s.processingStatus === 'pending'
    ).length;

    const processingCount = sentences.filter(s =>
      s.processingStatus === 'processing' || s.processingStatus === 'retrying'
    ).length;

    const completedCount = sentences.filter(s =>
      s.processingStatus === 'completed' || s.aiSuggestion !== undefined
    ).length;

    const failedCount = sentences.filter(s =>
      s.processingStatus === 'failed'
    ).length;

    console.log('📊 段落状态统计:', {
      segmentId: segment.id,
      totalSentences: sentences.length,
      pendingCount,
      processingCount,
      completedCount,
      failedCount
    });

    // 状态优先级：processing > failed > completed > pending
    if (processingCount > 0) return 'processing';
    if (failedCount > 0 && completedCount > 0) return 'partial_failed';
    if (failedCount > 0) return 'failed';
    if (completedCount === sentences.length) return 'completed';
    if (pendingCount === sentences.length) return 'pending';

    return 'processing'; // 默认为处理中
  }, []);

  // 重试失败的句子
  const retryFailedSentences = useCallback(async (segmentId: string) => {
    console.log('🔄 开始重试失败的句子:', { segmentId });

    const segment = segments.find(s => s.id === segmentId);
    if (!segment) {
      console.error('❌ 未找到段落:', segmentId);
      return;
    }

    const failedSentences = segment.sentences.filter(s =>
      (s as any).processingStatus === 'failed'
    );

    if (failedSentences.length === 0) {
      console.log('ℹ️ 没有失败的句子需要重试');
      return;
    }

    console.log(`🔄 找到 ${failedSentences.length} 个失败的句子，开始重试`);

    // 标记为重试状态
    const updatedSegments = segments.map(seg => {
      if (seg.id === segmentId) {
        return {
          ...seg,
          sentences: seg.sentences.map(sentence => {
            if ((sentence as any).processingStatus === 'failed') {
              return {
                ...sentence,
                processingStatus: 'retrying' as const,
                processingError: undefined,
                lastProcessedAt: new Date()
              };
            }
            return sentence;
          })
        };
      }
      return seg;
    });

    onSegmentsUpdate?.(updatedSegments);

    // 这里应该调用AI服务重新处理失败的句子
    // 暂时模拟重试逻辑
    console.log('⚠️ 重试功能需要集成到AI注释服务中');
  }, [segments, onSegmentsUpdate]);

  // 获取段落状态指示器
  const getSegmentStatusIndicator = useCallback((segment: TextSegment) => {
    const status = getSegmentStatus(segment);
    const sentences = segment.sentences as any[];

    const completedCount = sentences.filter(s =>
      s.processingStatus === 'completed' || s.aiSuggestion !== undefined
    ).length;
    const failedCount = sentences.filter(s => s.processingStatus === 'failed').length;
    const totalCount = sentences.length;

    switch (status) {
      case 'completed':
        return (
          <div className="flex items-center space-x-1 text-green-600">
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-xs font-medium">已完成</span>
          </div>
        );
      case 'processing':
        return (
          <div className="flex items-center space-x-1 text-blue-600">
            <motion.div
              className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <span className="text-xs font-medium">处理中 ({completedCount}/{totalCount})</span>
          </div>
        );
      case 'partial_failed':
        return (
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-orange-600">
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-xs font-medium">部分失败 ({completedCount}/{totalCount})</span>
            </div>
            <motion.button
              onClick={() => retryFailedSentences(segment.id)}
              className="text-xs px-2 py-1 bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              重试失败项
            </motion.button>
          </div>
        );
      case 'failed':
        return (
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-red-600">
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span className="text-xs font-medium">处理失败</span>
            </div>
            <motion.button
              onClick={() => retryFailedSentences(segment.id)}
              className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              重试
            </motion.button>
          </div>
        );
      case 'pending':
      default:
        return (
          <div className="flex items-center space-x-1 text-gray-400">
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-xs font-medium">等待处理</span>
          </div>
        );
    }
  }, [getSegmentStatus, retryFailedSentences]);

  // 直接应用建议修改文本
  const applyModification = useCallback((sentence: Sentence) => {
    const modifiedText = (sentence as any).modifiedText;
    // 允许处理所有有建议的句子，包括KEEP类型和DELETE类型
    if (!sentence.aiSuggestion && sentence.modificationType !== 'delete') return;

    let newText = '';

    // 🔧 修复：优先检查是否有创建内容，无论修改类型如何
    const hasCreation = (sentence as any).hasCreation;
    const createdSentences = (sentence as any).createdSentences || [];
    const insertPosition = (sentence as any).insertPosition || 'after';

    if (hasCreation && createdSentences.length > 0) {
      // 🔧 智能合并创建操作：同时处理修改和创建
      // 1. 确定基础文本（优先使用修改后的文本，然后是AI建议，最后是原文）
      let baseText = modifiedText || sentence.aiSuggestion || sentence.text;

      // 2. 如果有删除类型，基础文本为空
      if (sentence.modificationType === 'delete') {
        baseText = '';
      }

      // 3. 智能处理创建句子的拼接，使用换行分隔
      const createdText = createdSentences.map((createdSentence: string) => {
        // 确保句子以适当的标点结尾
        let cleanSentence = createdSentence.trim();
        if (!cleanSentence.match(/[。！？.!?]$/)) {
          cleanSentence += '。';
        }
        return cleanSentence;
      }).join('\n'); // 🔧 修复：使用换行分隔创建的句子

      // 4. 智能连接基础文本和创建文本
      if (baseText) {
        // 确保基础文本以适当的标点结尾
        if (!baseText.match(/[。！？.!?]$/)) {
          baseText += '。';
        }

        // 根据插入位置合并，使用换行分隔
        if (insertPosition === 'before') {
          newText = createdText + '\n' + baseText;
        } else {
          newText = baseText + '\n' + createdText;
        }
      } else {
        // 如果基础文本为空（删除类型），只使用创建的文本
        newText = createdText;
      }

      console.log('🔧 智能合并创建内容:', {
        原文: sentence.text.substring(0, 30) + '...',
        修改: (modifiedText || sentence.aiSuggestion || '无').substring(0, 30) + '...',
        创建句子数: createdSentences.length,
        插入位置: insertPosition,
        合并结果: newText.substring(0, 50) + '...'
      });
    } else {
      // 没有创建内容，按照原有逻辑处理
      if (sentence.modificationType === 'modify') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'delete') {
        newText = '';
      } else if (sentence.modificationType === 'split') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'merge') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'enhance') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'simplify') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'reorder') {
        newText = modifiedText || sentence.aiSuggestion || sentence.text;
      } else if (sentence.modificationType === 'create') {
        newText = modifiedText || sentence.text;
      } else if (sentence.modificationType === 'keep') {
        newText = sentence.text;
      } else {
        newText = sentence.text;
      }
    }

    // 找到对应的段落和句子
    let segmentId = '';
    let sentenceIndex = -1;

    for (const segment of segments) {
      const index = segment.sentences.findIndex(s => s.id === sentence.id);
      if (index !== -1) {
        segmentId = segment.id;
        sentenceIndex = index;
        break;
      }
    }

    if (segmentId && sentenceIndex !== -1) {
      // 更新句子文本
      const updatedSegments = segments.map(segment => {
        if (segment.id === segmentId) {
          return {
            ...segment,
            sentences: segment.sentences.map((s, index) => {
              if (index === sentenceIndex) {
                return {
                  ...s,
                  text: newText,
                  isAccepted: true,
                  // 对于KEEP类型，保留建议说明，因为这有助于用户理解AI的判断
                  aiSuggestion: sentence.modificationType === 'keep' ? sentence.aiSuggestion : '',
                  modificationType: 'keep' as const // 重置为保持状态
                };
              }
              return s;
            })
          };
        }
        return segment;
      });

      // 生成新的完整文本，保留原始换行符和格式
      const newFullText = updatedSegments.map(segment => {
        // 直接连接句子，保持原有的换行符
        return segment.sentences
          .map(s => s.text)
          .filter(text => text.trim()) // 过滤空句子
          .join('\n'); // 直接连接，不添加额外分隔符，保持原有格式
      }).join('\n\n'); // 段落之间用双换行分隔

      console.log('✅ 应用修改:', {
        sentenceId: sentence.id,
        modificationType: sentence.modificationType,
        originalText: sentence.text.substring(0, 30) + '...',
        newText: newText.substring(0, 30) + '...',
        newFullTextLength: newFullText.length,
        segmentId,
        sentenceIndex
      });

      // 先更新segments状态
      onSegmentsUpdate?.(updatedSegments);

      // 然后通知文本变更
      setTimeout(() => {
        onTextChange?.(newFullText);
      }, 0);
    }
  }, [segments, onTextChange, onSegmentsUpdate]);

  // 批量应用修改 - 优化版本，避免多次状态更新
  const batchApplyModifications = useCallback((segmentId: string) => {
    const segment = segments.find(s => s.id === segmentId);
    if (!segment) return;

    console.log('🔄 开始批量应用修改:', { segmentId, sentencesCount: segment.sentences.length });

    // 批量处理所有修改，一次性更新状态
    const updatedSegments = segments.map(seg => {
      if (seg.id === segmentId) {
        return {
          ...seg,
          sentences: seg.sentences.map(sentence => {
            // 处理有建议的句子，包括KEEP类型（KEEP类型也应该显示建议）
            if (sentence.aiSuggestion) {
              const modifiedText = (sentence as any).modifiedText;
              let newText = '';

              // 🔧 修复：优先检查是否有创建内容，无论修改类型如何
              const hasCreation = (sentence as any).hasCreation;
              const createdSentences = (sentence as any).createdSentences || [];
              const insertPosition = (sentence as any).insertPosition || 'after';

              if (hasCreation && createdSentences.length > 0) {
                // 🔧 智能合并创建操作：同时处理修改和创建
                // 1. 确定基础文本（优先使用修改后的文本，然后是AI建议，最后是原文）
                let baseText = modifiedText || sentence.aiSuggestion || sentence.text;

                // 2. 如果有删除类型，基础文本为空
                if (sentence.modificationType === 'delete') {
                  baseText = '';
                }

                // 3. 智能处理创建句子的拼接，使用换行分隔
                const createdText = createdSentences.map((createdSentence: string) => {
                  // 确保句子以适当的标点结尾
                  let cleanSentence = createdSentence.trim();
                  if (!cleanSentence.match(/[。！？.!?]$/)) {
                    cleanSentence += '。';
                  }
                  return cleanSentence;
                }).join('\n'); // 🔧 修复：使用换行分隔创建的句子

                // 4. 智能连接基础文本和创建文本
                if (baseText) {
                  // 确保基础文本以适当的标点结尾
                  if (!baseText.match(/[。！？.!?]$/)) {
                    baseText += '。';
                  }

                  // 根据插入位置合并，使用换行分隔
                  if (insertPosition === 'before') {
                    newText = createdText + '\n' + baseText;
                  } else {
                    newText = baseText + '\n' + createdText;
                  }
                } else {
                  // 如果基础文本为空（删除类型），只使用创建的文本
                  newText = createdText;
                }
              } else {
                // 没有创建内容，按照原有逻辑处理
                if (sentence.modificationType === 'modify') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'delete') {
                  newText = '';
                } else if (sentence.modificationType === 'split') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'merge') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'enhance') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'simplify') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'reorder') {
                  newText = modifiedText || sentence.aiSuggestion || sentence.text;
                } else if (sentence.modificationType === 'create') {
                  newText = modifiedText || sentence.text;
                } else if (sentence.modificationType === 'keep') {
                  newText = sentence.text;
                } else {
                  newText = sentence.text;
                }
              }

              console.log('✅ 批量应用句子修改:', {
                sentenceId: sentence.id,
                modificationType: sentence.modificationType,
                originalText: sentence.text.substring(0, 30) + '...',
                newText: newText.substring(0, 30) + '...'
              });

              return {
                ...sentence,
                text: newText,
                isAccepted: true,
                // 对于KEEP类型，保留建议说明，因为这有助于用户理解AI的判断
                aiSuggestion: sentence.modificationType === 'keep' ? sentence.aiSuggestion : '',
                modificationType: 'keep' as const // 重置为保持状态
              };
            }
            return sentence;
          })
        };
      }
      return seg;
    });

    // 生成新的完整文本，保留原始换行符和格式
    const newFullText = updatedSegments.map(segment => {
      // 直接连接句子，保持原有的换行符
      return segment.sentences
        .map(s => s.text)
        .filter(text => text.trim()) // 过滤空句子
        .join('\n'); // 直接连接，不添加额外分隔符，保持原有格式
    }).join('\n\n'); // 段落之间用双换行分隔

    console.log('✅ 批量应用完成:', {
      segmentId,
      newFullTextLength: newFullText.length,
      appliedCount: segment.sentences.filter(s => s.aiSuggestion).length
    });

    // 一次性更新状态
    onSegmentsUpdate?.(updatedSegments);

    // 延迟通知文本变更，确保状态更新完成
    setTimeout(() => {
      onTextChange?.(newFullText);
    }, 0);
  }, [segments, onSegmentsUpdate, onTextChange]);

  // 全局应用所有修改 - 新增功能
  const globalApplyAllModifications = useCallback(() => {
    console.log('🌍 开始全局应用所有修改');

    // 统计总的修改数量
    const totalModifications = segments.reduce((total, segment) => {
      return total + segment.sentences.filter(s => s.aiSuggestion).length;
    }, 0);

    if (totalModifications === 0) {
      console.log('ℹ️ 没有修改建议需要应用');
      return;
    }

    console.log(`📊 将应用 ${totalModifications} 个修改建议，涉及 ${segments.length} 个段落`);

    // 依次应用每个段落的修改
    segments.forEach((segment, index) => {
      console.log(`🔄 处理段落 ${index + 1}/${segments.length}: ${segment.id}`);
      batchApplyModifications(segment.id);
    });

    console.log('✅ 全局应用完成');
  }, [segments, batchApplyModifications]);



  // 验证句子ID唯一性
  const validateSentenceIds = useCallback(() => {
    const allIds = new Set<string>();
    const duplicates: string[] = [];
    let totalSentences = 0;

    console.log('🔍 开始验证句子ID唯一性:');

    segments.forEach((segment, segmentIndex) => {
      console.log(`📁 检查段落 ${segmentIndex + 1} (${segment.id}):`, {
        sentenceCount: segment.sentences.length
      });

      segment.sentences.forEach((sentence, localIndex) => {
        totalSentences++;
        if (allIds.has(sentence.id)) {
          duplicates.push(sentence.id);
          console.error(`❌ 发现重复ID: ${sentence.id} 在段落 ${segment.id} 的第 ${localIndex + 1} 句`);
        } else {
          allIds.add(sentence.id);
          console.log(`✅ 句子ID唯一: ${sentence.id}`);
        }
      });
    });

    const isValid = duplicates.length === 0;
    console.log('📊 ID唯一性验证结果:', {
      totalSentences,
      uniqueIds: allIds.size,
      duplicateIds: duplicates.length,
      isValid,
      duplicates: duplicates
    });

    return isValid;
  }, [segments]);

  // 自动验证句子ID唯一性
  useEffect(() => {
    if (segments.length > 0) {
      console.log('🔄 segments数据变化，自动验证句子ID唯一性');
      validateSentenceIds();
    }
  }, [segments, validateSentenceIds]);

  // 处理句子点击
  const handleSentenceClick = useCallback((sentenceId: string) => {
    console.log('🎯 句子点击事件:', {
      clickedSentenceId: sentenceId,
      currentSelectedSentence: selectedSentence,
      timestamp: new Date().toISOString()
    });

    // 首先验证ID唯一性
    if (!validateSentenceIds()) {
      console.error('❌ 句子ID不唯一，无法进行准确选择');
      return;
    }

    // 验证句子ID的有效性
    let foundSentence = null;
    let foundSegmentId = '';
    let globalIndex = 0;

    for (const segment of segments) {
      for (let i = 0; i < segment.sentences.length; i++) {
        const sentence = segment.sentences[i];
        if (sentence.id === sentenceId) {
          foundSentence = sentence;
          foundSegmentId = segment.id;
          console.log('✅ 找到对应句子:', {
            sentenceId: sentence.id,
            segmentId: segment.id,
            localIndex: i,
            globalIndex: globalIndex,
            sentenceText: sentence.text.substring(0, 50) + '...'
          });
          break;
        }
        globalIndex++;
      }
      if (foundSentence) break;
    }

    if (!foundSentence) {
      console.error('❌ 未找到对应的句子:', {
        clickedSentenceId: sentenceId,
        availableSentenceIds: segments.flatMap(seg => seg.sentences.map(s => s.id))
      });
      return;
    }

    setSelectedSentence(sentenceId);
  }, [selectedSentence, segments, validateSentenceIds]);

  // 选择替代建议
  const selectAlternative = useCallback((alternative: any) => {
    setSelectedAlternative(alternative);

    // 找到当前选中的句子并更新其修改内容
    if (selectedSentence) {
      let segmentId = '';
      for (const segment of segments) {
        if (segment.sentences.find(s => s.id === selectedSentence)) {
          segmentId = segment.id;
          break;
        }
      }

      if (segmentId) {
        // 更新句子的modifiedText为选中的替代建议
        const updatedSegments = segments.map(segment => {
          if (segment.id === segmentId) {
            return {
              ...segment,
              sentences: segment.sentences.map(s => {
                if (s.id === selectedSentence) {
                  return {
                    ...s,
                    modifiedText: alternative.text,
                    aiSuggestion: `已选择替代方案：${alternative.reason || ''}`,
                    modificationType: 'modify' as const
                  };
                }
                return s;
              })
            };
          }
          return segment;
        });

        onSegmentsUpdate?.(updatedSegments);
      }
    }
  }, [selectedSentence, segments, onSegmentsUpdate]);

  // 应用选中的替代建议
  const applySelectedAlternative = useCallback(() => {
    if (selectedAlternative && selectedSentence) {
      // 找到对应的句子并应用修改
      for (const segment of segments) {
        const sentence = segment.sentences.find(s => s.id === selectedSentence);
        if (sentence) {
          applyModification({
            ...sentence,
            modifiedText: selectedAlternative.text,
            modificationType: 'modify'
          });
          break;
        }
      }
      setSelectedAlternative(null);
    }
  }, [selectedAlternative, selectedSentence, segments, applyModification]);

  // 动画SVG图标组件
  const AnimatedCheckIcon = ({ isVisible }: { isVisible: boolean }) => (
    <motion.svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      className="text-green-600"
      initial={{ scale: 0, rotate: -180 }}
      animate={{
        scale: isVisible ? 1 : 0,
        rotate: isVisible ? 0 : -180
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
    >
      <motion.path
        d="M20 6L9 17l-5-5"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: isVisible ? 1 : 0 }}
        transition={{ duration: 0.3, delay: isVisible ? 0.1 : 0 }}
      />
    </motion.svg>
  );

  const AnimatedXIcon = ({ isVisible }: { isVisible: boolean }) => (
    <motion.svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      className="text-red-600"
      initial={{ scale: 0, rotate: 180 }}
      animate={{
        scale: isVisible ? 1 : 0,
        rotate: isVisible ? 0 : 180
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
    >
      <motion.path
        d="M18 6L6 18"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: isVisible ? 1 : 0 }}
        transition={{ duration: 0.2, delay: isVisible ? 0.05 : 0 }}
      />
      <motion.path
        d="M6 6l12 12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: isVisible ? 1 : 0 }}
        transition={{ duration: 0.2, delay: isVisible ? 0.1 : 0 }}
      />
    </motion.svg>
  );

  // 获取修改类型的颜色样式
  const getModificationTypeStyle = (type: string) => {
    switch (type) {
      case 'modify':
        return 'border-orange-300 bg-orange-50';
      case 'delete':
        return 'border-red-300 bg-red-50';
      case 'split':
        return 'border-purple-300 bg-purple-50';
      case 'merge':
        return 'border-indigo-300 bg-indigo-50';
      case 'enhance':
        return 'border-green-300 bg-green-50';
      case 'create':
        return 'border-emerald-300 bg-emerald-50';
      case 'simplify':
        return 'border-blue-300 bg-blue-50';
      case 'reorder':
        return 'border-yellow-300 bg-yellow-50';
      case 'keep':
        return 'border-green-300 bg-green-50';
      default:
        return 'border-gray-300 bg-gray-50';
    }
  };

  // 获取修改类型的图标
  const getModificationIcon = (type: string) => {
    switch (type) {
      case 'modify':
        return (
          <svg className="w-4 h-4 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      case 'delete':
        return (
          <svg className="w-4 h-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        );
      case 'split':
        return (
          <svg className="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
          </svg>
        );
      case 'merge':
        return (
          <svg className="w-4 h-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
          </svg>
        );
      case 'enhance':
        return (
          <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      case 'create':
        return (
          <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      case 'simplify':
        return (
          <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        );
      case 'reorder':
        return (
          <svg className="w-4 h-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
          </svg>
        );
      case 'keep':
        return (
          <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      default:
        return null;
    }
  };

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'grammar':
        return (
          <svg className="w-3 h-3 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'style':
        return (
          <svg className="w-3 h-3 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2" />
          </svg>
        );
      case 'logic':
        return (
          <svg className="w-3 h-3 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        );
      case 'expression':
        return (
          <svg className="w-3 h-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'structure':
        return (
          <svg className="w-3 h-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'clarity':
        return (
          <svg className="w-3 h-3 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // 获取分类标签
  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'grammar': return '语法问题';
      case 'style': return '风格问题';
      case 'logic': return '逻辑问题';
      case 'expression': return '表达问题';
      case 'structure': return '结构问题';
      case 'clarity': return '清晰度问题';
      default: return '未知分类';
    }
  };

  // 获取严重程度图标
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low':
        return <div className="w-2 h-2 bg-green-400 rounded-full" />;
      case 'medium':
        return <div className="w-2 h-2 bg-yellow-400 rounded-full" />;
      case 'high':
        return <div className="w-2 h-2 bg-red-400 rounded-full" />;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full" />;
    }
  };

  // 获取严重程度标签
  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'low': return '轻微';
      case 'medium': return '中等';
      case 'high': return '严重';
      default: return '未知';
    }
  };

  // 获取影响程度图标
  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'minor':
        return (
          <svg className="w-3 h-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'moderate':
        return (
          <svg className="w-3 h-3 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'significant':
        return (
          <svg className="w-3 h-3 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // 获取影响程度标签
  const getImpactLabel = (impact: string) => {
    switch (impact) {
      case 'minor': return '轻微影响';
      case 'moderate': return '中等影响';
      case 'significant': return '显著影响';
      default: return '未知影响';
    }
  };

  // 帮助按钮组件
  const HelpButton = () => (
    <div className="relative">
      <motion.button
        type="button"
        onClick={() => setShowHelpDialog(true)}
        onMouseEnter={() => setShowHelpTooltip(true)}
        onMouseLeave={() => setShowHelpTooltip(false)}
        className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200"
        whileHover={{
          scale: 1.1,
          boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.4)"
        }}
        whileTap={{
          scale: 0.95,
          transition: { duration: 0.1 }
        }}
      >
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </motion.button>

      {/* 工具提示 */}
      <AnimatePresence>
        {showHelpTooltip && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full right-0 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-50"
          >
            字段类型说明
            <div className="absolute -top-1 right-3 w-2 h-2 bg-gray-900 transform rotate-45"></div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  // 帮助弹窗组件
  const HelpDialog = () => (
    <AnimatePresence>
      {showHelpDialog && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowHelpDialog(false)}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 弹窗头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">字段类型说明</h3>
                  <p className="text-sm text-gray-600">AI批注系统增强字段详解</p>
                </div>
              </div>
              <motion.button
                type="button"
                onClick={() => setShowHelpDialog(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <div className="space-y-6">
                {/* 处理类型 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="bg-orange-50 border border-orange-200 rounded-xl p-4"
                >
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-orange-900">🔧 处理类型</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">modify:</span>
                      <span className="text-orange-700">修改句子内容</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">delete:</span>
                      <span className="text-orange-700">删除句子</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">split:</span>
                      <span className="text-orange-700">拆分为多个句子</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">merge:</span>
                      <span className="text-orange-700">与下一句合并</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">enhance:</span>
                      <span className="text-orange-700">增强表达效果</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">simplify:</span>
                      <span className="text-orange-700">简化表达</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">reorder:</span>
                      <span className="text-orange-700">调整句子顺序</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-orange-800">keep:</span>
                      <span className="text-orange-700">保持原样</span>
                    </div>
                  </div>
                </motion.div>

                {/* 问题分类 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-purple-50 border border-purple-200 rounded-xl p-4"
                >
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-purple-900">🏷️ 问题分类</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">grammar:</span>
                      <span className="text-purple-700">语法问题</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">style:</span>
                      <span className="text-purple-700">风格问题</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">logic:</span>
                      <span className="text-purple-700">逻辑问题</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">expression:</span>
                      <span className="text-purple-700">表达问题</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">structure:</span>
                      <span className="text-purple-700">结构问题</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-purple-800">clarity:</span>
                      <span className="text-purple-700">清晰度问题</span>
                    </div>
                  </div>
                </motion.div>

                {/* 其他字段 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="space-y-4"
                >
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <h4 className="font-medium text-green-900 mb-2">📊 评估指标</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-green-800">严重程度:</span>
                        <span className="text-green-700 ml-2">low(轻微) / medium(中等) / high(严重)</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-800">影响程度:</span>
                        <span className="text-green-700 ml-2">minor(轻微影响) / moderate(中等影响) / significant(显著影响)</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-800">置信度:</span>
                        <span className="text-green-700 ml-2">AI建议的可信度 (0-1)，数值越高表示AI越确信</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <h4 className="font-medium text-blue-900 mb-2">🎯 增强功能</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-blue-800">替代建议:</span>
                        <span className="text-blue-700 ml-2">提供多个不同风格的修改选项</span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">特征标签:</span>
                        <span className="text-blue-700 ml-2">描述修改特点的标签，如"语法修正"、"表达优化"等</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>点击左侧句子查看详细的AI分析和建议</span>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // 渲染句子
  const renderSentence = (sentence: Sentence, segmentId: string, index: number) => {
    const isSelected = selectedSentence === sentence.id;
    const isSelectedForProcessing = isSentenceSelected(sentence.id);
    const hasSuggestion = sentence.aiSuggestion; // 包括KEEP类型，因为KEEP也有建议说明
    const processingStatus = (sentence as any).processingStatus;
    const processingError = (sentence as any).processingError;

    // 计算全局索引用于调试
    let globalIndex = 0;
    let found = false;
    for (const segment of segments) {
      for (let i = 0; i < segment.sentences.length; i++) {
        if (segment.id === segmentId && i === index) {
          found = true;
          break;
        }
        globalIndex++;
      }
      if (found) break;
    }

    // 添加调试信息到DOM元素
    const debugInfo = {
      sentenceId: sentence.id,
      segmentId: segmentId,
      localIndex: index,
      globalIndex: globalIndex,
      displayNumber: index + 1,
      isSelected: isSelected,
      hasSuggestion: !!hasSuggestion,
      processingStatus: processingStatus
    };

    console.log('🎨 渲染句子:', debugInfo);

    // 根据处理状态确定样式
    const getStatusStyle = () => {
      if (processingStatus === 'failed') {
        return 'border-red-300 bg-red-50';
      }
      if (processingStatus === 'processing' || processingStatus === 'retrying') {
        return 'border-blue-300 bg-blue-50';
      }
      if (processingStatus === 'completed' || hasSuggestion) {
        return getModificationTypeStyle(sentence.modificationType || 'keep');
      }
      return 'border-gray-200 bg-white hover:border-gray-300';
    };

    // 获取状态指示器
    const getStatusIndicator = () => {
      if (processingStatus === 'failed') {
        return (
          <div className="flex items-center space-x-1 text-red-600">
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span className="text-xs">处理失败</span>
            {processingError && (
              <span className="text-xs text-red-500" title={processingError}>
                ⚠️
              </span>
            )}
          </div>
        );
      }
      if (processingStatus === 'processing') {
        return (
          <div className="flex items-center space-x-1 text-blue-600">
            <motion.div
              className="w-2 h-2 border border-blue-600 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <span className="text-xs">处理中</span>
          </div>
        );
      }
      if (processingStatus === 'retrying') {
        return (
          <div className="flex items-center space-x-1 text-orange-600">
            <motion.div
              className="w-2 h-2 border border-orange-600 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 0.8, repeat: Infinity, ease: "linear" }}
            />
            <span className="text-xs">重试中</span>
          </div>
        );
      }
      if (processingStatus === 'completed' || hasSuggestion) {
        return (
          <div className="flex items-center space-x-1 text-green-600">
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-xs">已完成</span>
          </div>
        );
      }
      return null;
    };

    return (
      <motion.div
        key={sentence.id}
        data-sentence-id={sentence.id}
        data-segment-id={segmentId}
        data-local-index={index}
        data-global-index={globalIndex}
        data-display-number={index + 1}
        className={`relative p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-400 ring-opacity-50 shadow-lg' : ''
        } ${isSelectedForProcessing ? 'ring-2 ring-emerald-400 ring-opacity-50 border-emerald-400 bg-emerald-50' : ''} ${getStatusStyle()}`}
        onClick={() => {
          console.log('🖱️ 句子点击 - 渲染层:', debugInfo);
          handleSentenceClick(sentence.id);
        }}
        whileHover={{
          scale: 1.02,
          y: -2,
          transition: { type: "spring", stiffness: 400, damping: 25 }
        }}
        whileTap={{ scale: 0.98 }}
        animate={{
          scale: isSelected ? 1.02 : 1,
          boxShadow: isSelected
            ? "0 10px 25px -5px rgba(59, 130, 246, 0.15), 0 4px 6px -2px rgba(59, 130, 246, 0.05)"
            : "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        layout
      >
        {isSelected && (
          <motion.div
            className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/10 to-purple-400/10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
        )}
        <div className="flex items-start space-x-2 relative z-10">
          {/* 选择模式下的勾选框 */}
          <AnimatePresence>
            {enableSelectionMode && isSelectionMode && (
              <motion.div
                initial={{ opacity: 0, x: -20, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: -20, scale: 0.8 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 25,
                  delay: index * 0.05 // stagger动画
                }}
                className="flex-shrink-0"
              >
                <SentenceCheckbox
                  checked={isSelectedForProcessing}
                  onChange={() => toggleSentenceSelection(sentence.id)}
                  size="md"
                  ariaLabel={`选择句子 ${index + 1} 进行修改`}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <motion.span
            className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-200 ${
              isSelected
                ? 'bg-blue-500 text-white shadow-lg'
                : 'bg-gray-100 text-gray-600'
            }`}
            animate={{
              scale: isSelected ? 1.1 : 1,
              backgroundColor: isSelected ? "#3b82f6" : "#f3f4f6"
            }}
            transition={{
              scale: { type: "spring", stiffness: 400, damping: 25 },
              backgroundColor: { duration: 0.2 }
            }}
            title={`句子ID: ${sentence.id} | 段落: ${segmentId} | 本地索引: ${index} | 全局索引: ${globalIndex}`}
          >
            {index + 1}
          </motion.span>
          <div className="flex-1">
            <p className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">{sentence.text}</p>

            {/* 处理状态指示器 */}
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {hasSuggestion && (
                  <>
                    {getModificationIcon(sentence.modificationType || 'keep')}
                    <span className="text-xs font-medium text-gray-600">
                      {sentence.modificationType === 'modify' && '建议修改'}
                      {sentence.modificationType === 'delete' && '建议删除'}
                      {sentence.modificationType === 'split' && '建议拆分'}
                      {sentence.modificationType === 'merge' && '建议合并'}
                      {sentence.modificationType === 'enhance' && '建议增强'}
                      {sentence.modificationType === 'create' && '建议创建'}
                      {sentence.modificationType === 'simplify' && '建议简化'}
                      {sentence.modificationType === 'reorder' && '建议重排'}
                      {sentence.modificationType === 'keep' && '无需修改'}
                    </span>
                  </>
                )}

                {/* 独立的创建操作标签 */}
                {(sentence as any).hasCreation && (
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span className="text-xs font-medium text-emerald-600 bg-emerald-50 px-2 py-0.5 rounded">
                      句子创建
                    </span>
                  </div>
                )}
              </div>

              {/* 状态指示器 */}
              {getStatusIndicator()}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  // 渲染标签页导航
  const renderTabNavigation = (selectedSentenceData: any) => {
    const alternatives = selectedSentenceData?.alternatives || [];
    const hasCreation = (selectedSentenceData as any)?.hasCreation;
    const createdSentences = (selectedSentenceData as any)?.createdSentences || [];

    const tabs = [
      {
        id: 'suggestion' as const,
        label: '修改建议',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        ),
        count: null
      },
      {
        id: 'creation' as const,
        label: '句子创建',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        ),
        // 🔧 修复：句子创建标签页永远显示，不再条件显示
        count: hasCreation ? createdSentences.length : 0,
        color: 'emerald',
        alwaysVisible: true // 标记为永远可见
      },
      {
        id: 'alternatives' as const,
        label: '替代方案',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
          </svg>
        ),
        count: alternatives.length
      },
      {
        id: 'analysis' as const,
        label: '详细分析',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        ),
        count: null
      }
    ];

    return (
      <div className="flex border-b border-gray-200 mb-4">
        {tabs.map((tab) => (
          <motion.button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === tab.id
                ? tab.color === 'emerald'
                  ? 'border-emerald-500 text-emerald-600 bg-emerald-50'
                  : 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {tab.icon}
            <span>{tab.label}</span>
            {/* 🔧 修复：句子创建标签页永远显示计数，即使为0 */}
            {(tab.count !== null && tab.count > 0) || (tab.color === 'emerald' && tab.count !== null) ? (
              <span className={`px-2 py-0.5 text-xs rounded-full ${
                activeTab === tab.id
                  ? tab.color === 'emerald'
                    ? 'bg-emerald-100 text-emerald-700'
                    : 'bg-blue-100 text-blue-700'
                  : tab.color === 'emerald'
                    ? 'bg-emerald-100 text-emerald-600'
                    : 'bg-gray-100 text-gray-600'
              }`}>
                {tab.count}
              </span>
            ) : null}
          </motion.button>
        ))}
      </div>
    );
  };

  // 渲染修改建议面板
  const renderSuggestionPanel = () => {
    if (!selectedSentence) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
            </svg>
            <p className="text-sm">点击左侧句子查看修改建议</p>
          </div>
        </div>
      );
    }

    // 找到选中的句子
    let selectedSentenceData: Sentence | null = null;
    let selectedSegmentId = '';

    for (const segment of segments) {
      const sentence = segment.sentences.find(s => s.id === selectedSentence);
      if (sentence) {
        selectedSentenceData = sentence;
        selectedSegmentId = segment.id;
        break;
      }
    }

    if (!selectedSentenceData) {
      return <div className="p-4 text-gray-500">未找到选中的句子</div>;
    }

    // 检查是否有建议：包括aiSuggestion或独立的创建操作
    const hasSuggestion = selectedSentenceData.aiSuggestion ||
                         (selectedSentenceData as any).hasCreation ||
                         (selectedSentenceData as any).modifiedText ||
                         (selectedSentenceData as any).createdSentences;

    return (
      <div className="space-y-4">
        <div className="border-b pb-4">
          <h4 className="text-md font-medium text-gray-900 mb-2">原文</h4>
          <p className="text-sm text-gray-700 bg-green-50 p-3 rounded-lg border border-green-200">
            {selectedSentenceData.text}
          </p>
        </div>

        {/* 标签页导航 */}
        {renderTabNavigation(selectedSentenceData)}

        {/* 标签页内容 */}
        <div className="min-h-[400px]">
          {activeTab === 'suggestion' && renderSuggestionTab(selectedSentenceData, selectedSegmentId, !!hasSuggestion)}
          {activeTab === 'creation' && renderCreationTab(selectedSentenceData, selectedSegmentId)}
          {activeTab === 'alternatives' && renderAlternativesTab(selectedSentenceData)}
          {activeTab === 'analysis' && renderAnalysisTab(selectedSentenceData)}
        </div>
      </div>
    );
  };

  // 渲染修改建议标签页
  const renderSuggestionTab = (selectedSentenceData: any, selectedSegmentId: string, hasSuggestion: boolean) => {
    return (
      <div className="space-y-4">
        {hasSuggestion ? (
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getModificationIcon(selectedSentenceData.modificationType || 'keep')}
                  <h4 className="text-md font-medium text-gray-900">
                    {selectedSentenceData.modificationType === 'modify' && 'AI修改建议'}
                    {selectedSentenceData.modificationType === 'delete' && 'AI删除建议'}
                    {selectedSentenceData.modificationType === 'split' && 'AI拆分建议'}
                    {selectedSentenceData.modificationType === 'merge' && 'AI合并建议'}
                    {selectedSentenceData.modificationType === 'enhance' && 'AI增强建议'}
                    {selectedSentenceData.modificationType === 'create' && 'AI句子创建建议'}
                    {selectedSentenceData.modificationType === 'simplify' && 'AI简化建议'}
                    {selectedSentenceData.modificationType === 'reorder' && 'AI重排建议'}
                    {selectedSentenceData.modificationType === 'keep' && 'AI分析结果'}
                  </h4>
                </div>
                {selectedSentenceData.modificationType === 'modify' && (
                  <motion.button
                    type="button"
                    onClick={() => selectedSentence && startEditingSuggestion(selectedSentence, selectedSentenceData.aiSuggestion || '')}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>编辑</span>
                  </motion.button>
                )}
              </div>

              {editingSentence === selectedSentence ? (
                <div className="space-y-3">
                  <textarea
                    value={editingText}
                    onChange={(e) => setEditingText(e.target.value)}
                    className="w-full h-24 p-3 text-sm border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="编辑修改建议..."
                  />
                  <div className="flex space-x-2">
                    <motion.button
                      type="button"
                      onClick={saveEditedSuggestion}
                      className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      保存
                    </motion.button>
                    <motion.button
                      type="button"
                      onClick={cancelEditing}
                      className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400 transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      取消
                    </motion.button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* 具体修改内容 */}
                  {(selectedSentenceData as any).modifiedText && selectedSentenceData.modificationType !== 'create' && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        <span className="text-sm font-medium text-green-800">修改后内容</span>
                      </div>
                      <p className="text-sm text-green-900 bg-white p-2 rounded border border-green-200">
                        {(selectedSentenceData as any).modifiedText}
                      </p>
                    </div>
                  )}

                  {/* 独立创建操作的特殊展示 */}
                  {(selectedSentenceData as any).hasCreation && (
                    <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                      <div className="font-medium text-emerald-800 mb-3 flex items-center">
                        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        句子创建详情
                      </div>

                      <div className="space-y-3">
                        {/* 插入位置指示 */}
                        <div className="flex items-center justify-between">
                          <span className="text-emerald-700 font-medium">插入位置:</span>
                          <span className="text-emerald-600 bg-emerald-100 px-2 py-1 rounded">
                            {(selectedSentenceData as any).insertPosition === 'before' ? '句子前' : '句子后'}
                          </span>
                        </div>

                        {/* 内容类型标识 */}
                        <div className="flex items-center justify-between">
                          <span className="text-emerald-700 font-medium">内容类型:</span>
                          <span className="text-emerald-600 bg-emerald-100 px-2 py-1 rounded flex items-center">
                            {(selectedSentenceData as any).contentType === 'dialogue' && '💬 对话'}
                            {(selectedSentenceData as any).contentType === 'description' && '📝 描述'}
                            {(selectedSentenceData as any).contentType === 'action' && '🎬 动作'}
                            {(selectedSentenceData as any).contentType === 'emotion' && '❤️ 情感'}
                            {!(selectedSentenceData as any).contentType && '📄 其他'}
                          </span>
                        </div>

                        {/* 创建模式 */}
                        <div className="flex items-center justify-between">
                          <span className="text-emerald-700 font-medium">创建模式:</span>
                          <span className="text-emerald-600 bg-emerald-100 px-2 py-1 rounded">
                            {(selectedSentenceData as any).insertMode === 'batch' ? '批量创建' : '单句创建'}
                          </span>
                        </div>

                        {/* 新句子列表 */}
                        {(selectedSentenceData as any).createdSentences &&
                         Array.isArray((selectedSentenceData as any).createdSentences) &&
                         (selectedSentenceData as any).createdSentences.length > 0 && (
                          <div>
                            <div className="text-emerald-700 font-medium mb-2">创建的句子:</div>
                            <div className="bg-white rounded-lg border border-emerald-200 max-h-40 overflow-y-auto">
                              {(selectedSentenceData as any).createdSentences.map((sentence: string, index: number) => (
                                <div key={index} className="p-2 border-b border-emerald-100 last:border-b-0 flex items-start">
                                  <span className="text-emerald-500 font-medium mr-2 mt-0.5 text-sm">
                                    {index + 1}.
                                  </span>
                                  <span className="text-emerald-700 text-sm flex-1">
                                    {sentence}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* 创建理由 */}
                        {(selectedSentenceData as any).contextHint && (
                          <div>
                            <div className="text-emerald-700 font-medium mb-1">创建理由:</div>
                            <div className="text-emerald-600 text-sm bg-emerald-50 p-2 rounded">
                              {(selectedSentenceData as any).contextHint}
                            </div>
                          </div>
                        )}

                        {/* 🔧 智能合并预览 */}
                        <div>
                          <div className="text-emerald-700 font-medium mb-1">合并效果预览:</div>
                          <div className="text-emerald-600 text-sm bg-white p-3 rounded border border-emerald-200 whitespace-pre-line">
                            {(() => {
                              // 智能合并预览逻辑
                              const originalText = selectedSentenceData.text;
                              const modifiedText = (selectedSentenceData as any).modifiedText;
                              const createdSentences = (selectedSentenceData as any).createdSentences || [];
                              const insertPosition = (selectedSentenceData as any).insertPosition || 'after';

                              // 确定基础文本
                              const baseText = modifiedText || originalText;

                              if (createdSentences.length === 0) {
                                return baseText;
                              }

                              // 🔧 修复：智能处理创建句子，使用换行分隔
                              const createdText = createdSentences.map((sentence: string) => {
                                let cleanSentence = sentence.trim();
                                if (!cleanSentence.match(/[。！？.!?]$/)) {
                                  cleanSentence += '。';
                                }
                                return cleanSentence;
                              }).join('\n'); // 使用换行分隔创建的句子

                              // 智能连接
                              let result = baseText;
                              if (!result.match(/[。！？.!?]$/)) {
                                result += '。';
                              }

                              // 🔧 修复：使用换行分隔基础文本和创建文本
                              if (insertPosition === 'before') {
                                return createdText + '\n' + result;
                              } else {
                                return result + '\n' + createdText;
                              }
                            })()}
                          </div>

                          {/* 合并说明 */}
                          <div className="mt-2 text-xs text-emerald-600 bg-emerald-50 p-2 rounded">
                            <div className="flex items-center space-x-1 mb-1">
                              <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className="font-medium">合并说明：</span>
                            </div>
                            <ul className="space-y-1 text-emerald-600">
                              {(selectedSentenceData as any).modifiedText && (
                                <li>• 应用了句子修改建议</li>
                              )}
                              {(selectedSentenceData as any).createdSentences?.length > 0 && (
                                <li>• 在句子{(selectedSentenceData as any).insertPosition === 'before' ? '前' : '后'}添加了{(selectedSentenceData as any).createdSentences.length}个新句子</li>
                              )}
                              <li>• 自动处理了标点符号连接</li>
                              <li>• 可直接复制粘贴使用</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 建议说明 */}
                  {selectedSentenceData.aiSuggestion && (
                    <div className={`border rounded-lg p-3 ${
                      selectedSentenceData.modificationType === 'keep'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-blue-50 border-blue-200'
                    }`}>
                      <div className="flex items-center space-x-2 mb-2">
                        <svg className={`w-4 h-4 ${
                          selectedSentenceData.modificationType === 'keep'
                            ? 'text-green-600'
                            : 'text-blue-600'
                        }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          {selectedSentenceData.modificationType === 'keep' ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          ) : (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          )}
                        </svg>
                        <span className={`text-sm font-medium ${
                          selectedSentenceData.modificationType === 'keep'
                            ? 'text-green-800'
                            : 'text-blue-800'
                        }`}>
                          {selectedSentenceData.modificationType === 'keep' ? 'AI分析说明' : '建议说明'}
                        </span>
                      </div>
                      <p className={`text-sm bg-white p-2 rounded border ${
                        selectedSentenceData.modificationType === 'keep'
                          ? 'text-green-900 border-green-200'
                          : 'text-blue-900 border-blue-200'
                      }`}>
                        {selectedSentenceData.modificationType === 'keep'
                          ? `AI认为此句子无需修改：${selectedSentenceData.aiSuggestion}`
                          : selectedSentenceData.aiSuggestion
                        }
                      </p>
                    </div>
                  )}

                  {/* 如果没有具体修改内容，显示原有的建议 */}
                  {!(selectedSentenceData as any).modifiedText && selectedSentenceData.aiSuggestion && (
                    <p className={`text-sm p-3 rounded-lg border ${getModificationTypeStyle(selectedSentenceData.modificationType || 'keep')}`}>
                      {selectedSentenceData.aiSuggestion}
                    </p>
                  )}

                  {/* 显示增强的建议信息 */}
                  {(selectedSentenceData as any).category && (
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="bg-gray-50 p-2 rounded-lg">
                        <div className="font-medium text-gray-700 mb-1">问题分类</div>
                        <div className="flex items-center space-x-1">
                          {getCategoryIcon((selectedSentenceData as any).category)}
                          <span className="text-gray-600">{getCategoryLabel((selectedSentenceData as any).category)}</span>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-2 rounded-lg">
                        <div className="font-medium text-gray-700 mb-1">严重程度</div>
                        <div className="flex items-center space-x-1">
                          {getSeverityIcon((selectedSentenceData as any).severity)}
                          <span className="text-gray-600">{getSeverityLabel((selectedSentenceData as any).severity)}</span>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-2 rounded-lg">
                        <div className="font-medium text-gray-700 mb-1">影响程度</div>
                        <div className="flex items-center space-x-1">
                          {getImpactIcon((selectedSentenceData as any).impact)}
                          <span className="text-gray-600">{getImpactLabel((selectedSentenceData as any).impact)}</span>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-2 rounded-lg">
                        <div className="font-medium text-gray-700 mb-1">置信度</div>
                        <div className="flex items-center space-x-1">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${((selectedSentenceData as any).confidence || 0.7) * 100}%` }}
                            />
                          </div>
                          <span className="text-gray-600 ml-2">{Math.round(((selectedSentenceData as any).confidence || 0.7) * 100)}%</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 显示标签 */}
                  {(selectedSentenceData as any).tags && (selectedSentenceData as any).tags.length > 0 && (
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="font-medium text-gray-700 mb-2 text-xs">特征标签</div>
                      <div className="flex flex-wrap gap-1">
                        {(selectedSentenceData as any).tags.map((tag: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 显示替代建议 */}
                  {(selectedSentenceData as any).alternatives && (selectedSentenceData as any).alternatives.length > 0 && (
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="font-medium text-gray-700 mb-2 text-xs">替代建议</div>
                      <div className="space-y-2">
                        {(selectedSentenceData as any).alternatives.map((alt: any, index: number) => (
                          <div key={index} className="bg-white p-2 rounded border border-green-200">
                            <div className="text-sm text-gray-800 mb-1">{alt.text}</div>
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>风格: {alt.style}</span>
                              <span>{alt.reason}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {editingSentence !== selectedSentence && (
              <div className="flex space-x-3">
                <motion.button
                  type="button"
                  onClick={() => {
                    const sentence = selectedSentenceData;
                    if (sentence) {
                      applyModification(sentence);
                    }
                  }}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl"
                  whileHover={{
                    scale: 1.02,
                    y: -1,
                    boxShadow: "0 20px 25px -5px rgba(34, 197, 94, 0.2), 0 10px 10px -5px rgba(34, 197, 94, 0.1)"
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <AnimatedCheckIcon isVisible={true} />
                  <span>
                    {selectedSentenceData.modificationType === 'keep' ? '确认保持' : '应用修改'}
                  </span>
                </motion.button>
                <motion.button
                  type="button"
                  onClick={() => selectedSentence && onSentenceReject?.(selectedSegmentId, selectedSentence)}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl"
                  whileHover={{
                    scale: 1.02,
                    y: -1,
                    boxShadow: "0 20px 25px -5px rgba(239, 68, 68, 0.2), 0 10px 10px -5px rgba(239, 68, 68, 0.1)"
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <AnimatedXIcon isVisible={true} />
                  <span>拒绝建议</span>
                </motion.button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <svg className="w-8 h-8 mx-auto mb-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <p className="text-sm text-gray-600">此句子无需修改</p>
          </div>
        )}
      </div>
    );
  };

  // 渲染替代方案标签页
  const renderAlternativesTab = (selectedSentenceData: any) => {
    const alternatives = selectedSentenceData?.alternatives || [];

    if (alternatives.length === 0) {
      return (
        <div className="text-center py-12">
          <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
          </svg>
          <p className="text-sm text-gray-500">暂无替代方案</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="text-sm text-gray-600 mb-4">
          为您提供 {alternatives.length} 个替代修改方案，点击选择您喜欢的版本：
        </div>

        {alternatives.map((alternative: any, index: number) => (
          <motion.div
            key={index}
            className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
              selectedAlternative?.text === alternative.text
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
            }`}
            whileHover={{ scale: 1.01, y: -1 }}
            whileTap={{ scale: 0.99 }}
            onClick={() => selectAlternative(alternative)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs font-medium text-gray-500">方案 {index + 1}</span>
                  {alternative.style && (
                    <span className="px-2 py-0.5 text-xs bg-purple-100 text-purple-700 rounded-full">
                      {alternative.style}
                    </span>
                  )}
                </div>

                <p className="text-sm text-gray-800 mb-2 leading-relaxed">
                  {alternative.text}
                </p>

                {alternative.reason && (
                  <p className="text-xs text-gray-500">
                    💡 {alternative.reason}
                  </p>
                )}
              </div>

              <motion.button
                onClick={(e) => {
                  e.stopPropagation();
                  selectAlternative(alternative);
                }}
                className={`ml-3 px-3 py-1.5 text-xs rounded-lg transition-all duration-200 ${
                  selectedAlternative?.text === alternative.text
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-blue-100 hover:text-blue-700'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {selectedAlternative?.text === alternative.text ? '已选择' : '选择此方案'}
              </motion.button>
            </div>
          </motion.div>
        ))}

        {selectedAlternative && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 border border-green-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm font-medium text-green-800">已选择替代方案</span>
              </div>

              <motion.button
                onClick={applySelectedAlternative}
                className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                应用此方案
              </motion.button>
            </div>

            <p className="text-sm text-green-700 mt-2">
              {selectedAlternative.text}
            </p>
          </motion.div>
        )}
      </div>
    );
  };

  // 渲染句子创建标签页
  const renderCreationTab = (selectedSentenceData: any, selectedSegmentId: string | null) => {
    const hasCreation = (selectedSentenceData as any)?.hasCreation;
    const createdSentences = (selectedSentenceData as any)?.createdSentences || [];
    const insertPosition = (selectedSentenceData as any)?.insertPosition || 'after';
    const contentType = (selectedSentenceData as any)?.contentType || 'dialogue';
    const insertMode = (selectedSentenceData as any)?.insertMode || 'batch';
    const contextHint = (selectedSentenceData as any)?.contextHint || '';

    // 🔧 修复：句子创建标签页永远可见，提供更友好的无内容状态
    if (!hasCreation) {
      return (
        <div className="text-center py-12">
          <div className="bg-emerald-50 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-emerald-700 mb-2">句子创建功能</h3>
          <p className="text-sm text-emerald-600 mb-3">当前句子暂无创建建议</p>
          <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-xs text-emerald-700 mb-2">💡 <strong>功能说明：</strong></p>
            <ul className="text-xs text-emerald-600 space-y-1 text-left">
              <li>• AI会分析上下文，在合适的位置建议创建新句子</li>
              <li>• 支持对话、描述、动作、情感等多种内容类型</li>
              <li>• 可以在句子前后插入，丰富故事情节</li>
              <li>• 创建的句子会与原文自然融合</li>
            </ul>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* 创建操作概览 */}
        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <svg className="w-5 h-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <h5 className="text-sm font-medium text-emerald-800">句子创建详情</h5>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* 插入位置 */}
            <div className="bg-white p-3 rounded-lg border border-emerald-100">
              <div className="font-medium text-emerald-700 mb-1 text-xs">插入位置</div>
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={insertPosition === 'before' ? "M7 16l-4-4m0 0l4-4m-4 4h18" : "M17 8l4 4m0 0l-4 4m4-4H3"} />
                </svg>
                <span className="text-sm text-emerald-700">
                  {insertPosition === 'before' ? '句子前' : '句子后'}
                </span>
              </div>
            </div>

            {/* 内容类型 */}
            <div className="bg-white p-3 rounded-lg border border-emerald-100">
              <div className="font-medium text-emerald-700 mb-1 text-xs">内容类型</div>
              <div className="flex items-center space-x-2">
                <span className="text-lg">
                  {contentType === 'dialogue' && '💬'}
                  {contentType === 'description' && '📝'}
                  {contentType === 'action' && '🎬'}
                  {contentType === 'emotion' && '❤️'}
                </span>
                <span className="text-sm text-emerald-700">
                  {contentType === 'dialogue' && '对话'}
                  {contentType === 'description' && '描述'}
                  {contentType === 'action' && '动作'}
                  {contentType === 'emotion' && '情感'}
                </span>
              </div>
            </div>

            {/* 创建模式 */}
            <div className="bg-white p-3 rounded-lg border border-emerald-100">
              <div className="font-medium text-emerald-700 mb-1 text-xs">创建模式</div>
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={insertMode === 'batch' ? "M4 6h16M4 10h16M4 14h16M4 18h16" : "M20 12H4"} />
                </svg>
                <span className="text-sm text-emerald-700">
                  {insertMode === 'batch' ? '批量创建' : '单句创建'}
                </span>
              </div>
            </div>

            {/* 句子数量 */}
            <div className="bg-white p-3 rounded-lg border border-emerald-100">
              <div className="font-medium text-emerald-700 mb-1 text-xs">句子数量</div>
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                </svg>
                <span className="text-sm text-emerald-700">
                  {createdSentences.length} 个句子
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 创建的句子列表 */}
        {createdSentences.length > 0 && (
          <div className="bg-white border border-emerald-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-emerald-800 mb-3 flex items-center space-x-2">
              <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>创建的句子</span>
            </h5>

            <div className="space-y-3 max-h-60 overflow-y-auto">
              {createdSentences.map((sentence: string, index: number) => (
                <div key={index} className="bg-emerald-50 border border-emerald-100 rounded-lg p-3">
                  <div className="flex items-start space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-emerald-200 text-emerald-700 rounded-full flex items-center justify-center text-xs font-medium">
                      {index + 1}
                    </span>
                    <p className="text-sm text-emerald-800 flex-1 leading-relaxed">
                      {sentence}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 创建理由 */}
        {contextHint && (
          <div className="bg-white border border-emerald-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-emerald-800 mb-3 flex items-center space-x-2">
              <svg className="w-4 h-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>创建理由</span>
            </h5>

            <p className="text-sm text-emerald-700 leading-relaxed bg-emerald-50 p-3 rounded-lg">
              {contextHint}
            </p>
          </div>
        )}

        {/* 应用操作 */}
        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-emerald-800">应用创建建议</span>
            </div>

            <div className="flex items-center space-x-2">
              <motion.button
                type="button"
                onClick={() => {
                  if (selectedSentenceData) {
                    applyModification(selectedSentenceData);
                  }
                }}
                className="px-4 py-2 bg-emerald-600 text-white text-sm rounded-lg hover:bg-emerald-700 transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                应用创建
              </motion.button>

              <motion.button
                type="button"
                onClick={() => {
                  // 拒绝创建建议的逻辑
                }}
                className="px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                拒绝
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染详细分析标签页
  const renderAnalysisTab = (selectedSentenceData: any) => {
    const hasAnalysis = selectedSentenceData?.category || selectedSentenceData?.severity;

    if (!hasAnalysis) {
      return (
        <div className="text-center py-12">
          <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p className="text-sm text-gray-500">暂无详细分析数据</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* 问题分析 */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
            <svg className="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>问题分析</span>
          </h5>

          <div className="grid grid-cols-2 gap-4">
            {selectedSentenceData.category && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-1 text-xs">问题分类</div>
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(selectedSentenceData.category)}
                  <span className="text-sm text-gray-600">{getCategoryLabel(selectedSentenceData.category)}</span>
                </div>
              </div>
            )}

            {selectedSentenceData.severity && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-1 text-xs">严重程度</div>
                <div className="flex items-center space-x-2">
                  {getSeverityIcon(selectedSentenceData.severity)}
                  <span className="text-sm text-gray-600">{getSeverityLabel(selectedSentenceData.severity)}</span>
                </div>
              </div>
            )}

            {selectedSentenceData.impact && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-1 text-xs">影响程度</div>
                <div className="flex items-center space-x-2">
                  {getImpactIcon(selectedSentenceData.impact)}
                  <span className="text-sm text-gray-600">{getImpactLabel(selectedSentenceData.impact)}</span>
                </div>
              </div>
            )}

            {selectedSentenceData.confidence && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-1 text-xs">置信度</div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(selectedSentenceData.confidence || 0.7) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600">{Math.round((selectedSentenceData.confidence || 0.7) * 100)}%</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 特征标签 */}
        {selectedSentenceData.tags && selectedSentenceData.tags.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <svg className="w-4 h-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              <span>特征标签</span>
            </h5>

            <div className="flex flex-wrap gap-2">
              {selectedSentenceData.tags.map((tag: string, index: number) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 详细原因 */}
        {selectedSentenceData.reason && (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <svg className="w-4 h-4 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>详细分析</span>
            </h5>

            <p className="text-sm text-gray-700 leading-relaxed">
              {selectedSentenceData.reason}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`flex bg-gray-50 ${className}`} style={{ height: 'calc(100vh - 200px)' }}>
      {/* 左侧：原文显示区域 */}
      <div className="flex flex-col" style={{ width: `${leftPanelWidth}%` }}>
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">原文内容</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">布局调整:</span>
            <motion.button
              onClick={() => setLeftPanelWidth(50)}
              className={`px-2 py-1 text-xs rounded transition-all duration-200 ${leftPanelWidth === 50 ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              50/50
            </motion.button>
            <motion.button
              onClick={() => setLeftPanelWidth(60)}
              className={`px-2 py-1 text-xs rounded transition-all duration-200 ${leftPanelWidth === 60 ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              60/40
            </motion.button>
            <motion.button
              onClick={() => setLeftPanelWidth(70)}
              className={`px-2 py-1 text-xs rounded transition-all duration-200 ${leftPanelWidth === 70 ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              70/30
            </motion.button>
          </div>
        </div>
        <div className="flex-1 p-4 overflow-y-auto" style={{ scrollBehavior: 'smooth' }}>
          <div className="space-y-4">
            {segments.map((segment, segmentIndex) => (
            <motion.div
              key={segment.id}
              className="bg-white rounded-xl shadow-sm border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: segmentIndex * 0.1 }}
            >
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-sm font-medium text-gray-700">
                      段落 {segmentIndex + 1} ({segment.sentences.length} 个句子)
                    </h3>
                    {getSegmentStatusIndicator(segment)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <motion.button
                      type="button"
                      onClick={() => batchApplyModifications(segment.id)}
                      className="flex items-center space-x-1 px-3 py-1.5 text-xs bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200 border border-green-200"
                      whileHover={{ scale: 1.05, y: -1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <AnimatedCheckIcon isVisible={true} />
                      <span>全部应用</span>
                    </motion.button>
                    <motion.button
                      type="button"
                      onClick={() => onBatchReject?.(segment.id)}
                      className="flex items-center space-x-1 px-3 py-1.5 text-xs bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-all duration-200 border border-red-200"
                      whileHover={{ scale: 1.05, y: -1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <AnimatedXIcon isVisible={true} />
                      <span>全部拒绝</span>
                    </motion.button>
                  </div>
                </div>
              </div>
              <div className="p-4 space-y-3">
                {segment.sentences.map((sentence, sentenceIndex) =>
                  renderSentence(sentence, segment.id, sentenceIndex)
                )}
              </div>
            </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧：修改建议区域 */}
      <div className="flex flex-col bg-white border-l border-gray-200" style={{ width: `${100 - leftPanelWidth}%` }}>
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <h3 className="text-lg font-medium text-gray-900">修改建议</h3>
              {enableSelectionMode && (
                <SelectionModeIndicator
                  isSelectionMode={isSelectionMode}
                  selectedCount={selectedCount}
                  totalCount={totalSentenceCount}
                />
              )}
            </div>
            <div className="flex items-center space-x-2">
              {/* 选择模式切换 */}
              {enableSelectionMode && (
                <SelectionModeToggle
                  isSelectionMode={isSelectionMode}
                  onToggle={toggleSelectionMode}
                />
              )}
              <HelpButton />
              {/* 全局全部应用按钮 */}
              <motion.button
                type="button"
                onClick={globalApplyAllModifications}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors shadow-md"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>全部应用</span>
              </motion.button>
              {onSave && (
                <motion.button
                  type="button"
                  onClick={() => onSave(segments)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors shadow-md"
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <span>保存进度</span>
                </motion.button>
              )}
            </div>
          </div>

          {/* 选择工具栏 */}
          {enableSelectionMode && (
            <SelectionToolbar
              isVisible={isSelectionMode}
              selectedCount={selectedCount}
              totalCount={totalSentenceCount}
              onBatchSelect={batchSelect}
              onStartAnalysis={onStartAnalysis ? () => {
                const selectedIds = Array.from(selectedSentenceIds);
                onStartAnalysis(selectedIds);
              } : undefined}
              onClearSelection={clearSelection}
              onUndoSelection={undoSelection}
              canUndo={selectionHistory.length > 1}
            />
          )}
        </div>
        <div className="flex-1 overflow-y-auto" style={{ scrollBehavior: 'smooth' }}>
          <div className="p-4">
            {renderSuggestionPanel()}
          </div>
        </div>
      </div>

      {/* 帮助弹窗 */}
      <HelpDialog />
    </div>
  );
};

export default AnnotationResultViewer;
