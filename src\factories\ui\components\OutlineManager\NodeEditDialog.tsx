"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { createPortal } from 'react-dom';
import { OutlineNodeType } from '../../types/outline';
import { Button } from '@/adapters/ui';
import { VolumeEditor, VolumeEditorRef } from './editors/VolumeEditor';
import { EventEditor, EventEditorRef } from './editors/EventEditor';
import { PhaseGroupEditor, PhaseGroupEditorRef } from './editors/PhaseGroupEditor';
import ChapterEditor from './editors/ChapterEditor';
import PlotEditor from './editors/PlotEditor';
import DialogueEditor from './editors/DialogueEditor';
import './NodeEditDialog.css';

interface NodeEditDialogProps {
  node: OutlineNodeType | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedNode: OutlineNodeType) => void;
  allNodes?: OutlineNodeType[]; // 传入所有节点，用于VolumeEditor的章节选择
}

/**
 * 节点编辑对话框组件
 * 用于编辑节点的标题和描述
 */
const NodeEditDialog: React.FC<NodeEditDialogProps> = ({ node, isOpen, onClose, onSave, allNodes }) => {
  const [currentNode, setCurrentNode] = useState<OutlineNodeType | null>(null);
  const [type, setType] = useState<'volume' | 'phaseGroup' | 'chapter' | 'plot' | 'dialogue'>('chapter');
  const [isClosing, setIsClosing] = useState(false);
  const [isCreatingPhaseGroups, setIsCreatingPhaseGroups] = useState(false);
  const [phaseGroupCreationMessage, setPhaseGroupCreationMessage] = useState<string>('');

  // 编辑器引用
  const volumeEditorRef = useRef<VolumeEditorRef>(null);
  const eventEditorRef = useRef<EventEditorRef>(null);
  const phaseGroupEditorRef = useRef<PhaseGroupEditorRef>(null);

  // 当节点变化时更新表单
  useEffect(() => {
    if (node) {
      setCurrentNode({ ...node });
      setType(node.type || 'chapter');
    }
  }, [node]);

  // 处理节点数据变化
  const handleNodeChange = useCallback((updatedNode: OutlineNodeType) => {
    setCurrentNode(updatedNode);
  }, []);

  // 计算阶段分组节点位置
  const calculatePhaseGroupPosition = useCallback((volumeNode: OutlineNodeType, index: number) => {
    const baseX = (volumeNode.position?.x || 0) + 300; // 向右偏移300px
    const baseY = (volumeNode.position?.y || 0) + (index * 120); // 垂直排列，间距120px
    return { x: baseX, y: baseY };
  }, []);

  // 更新章节的父节点ID
  const updateChapterParent = useCallback(async (chapterId: string, newParentId: string) => {
    if (!allNodes) return;

    const chapterNode = allNodes.find(node => node.id === chapterId);
    if (chapterNode) {
      const updatedChapterNode: OutlineNodeType = {
        ...chapterNode,
        parentId: newParentId
      };

      console.log(`📝 更新章节 ${chapterNode.title} 的父节点为: ${newParentId}`);
      onSave(updatedChapterNode);
    }
  }, [allNodes, onSave]);

  // 从卷纲创建阶段分组节点
  const createPhaseGroupsFromVolume = useCallback(async (volumeNode: OutlineNodeType) => {
    const mappings = volumeNode.cycleChapterMappings || [];
    const validMappings = mappings.filter(mapping => mapping.phase && mapping.chapterIds.length > 0);

    if (validMappings.length === 0) {
      console.log('📋 没有有效的章节映射，跳过阶段分组创建');
      return [];
    }

    console.log(`🔄 开始创建 ${validMappings.length} 个阶段分组节点`);
    setIsCreatingPhaseGroups(true);
    setPhaseGroupCreationMessage(`正在创建 ${validMappings.length} 个阶段分组...`);

    const createdPhaseGroups: OutlineNodeType[] = [];

    try {
      for (let i = 0; i < validMappings.length; i++) {
        const mapping = validMappings[i];

        // 创建阶段分组节点
        const phaseGroupNode: OutlineNodeType = {
          id: `phaseGroup_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          title: mapping.phase,
          type: 'phaseGroup',
          description: `${mapping.phase}阶段包含${mapping.chapterIds.length}个章节`,
          parentId: volumeNode.id,
          position: calculatePhaseGroupPosition(volumeNode, i),
          phaseName: mapping.phase,
          phaseDescription: `${mapping.phase}阶段包含${mapping.chapterIds.length}个章节`,
          phasePosition: i,
          parentVolumeId: volumeNode.id
        };

        console.log(`📁 创建阶段分组: ${phaseGroupNode.title}`);

        // 保存阶段分组节点
        onSave(phaseGroupNode);
        createdPhaseGroups.push(phaseGroupNode);

        // 更新章节的parentId
        for (const chapterId of mapping.chapterIds) {
          await updateChapterParent(chapterId, phaseGroupNode.id);
        }
      }

      setPhaseGroupCreationMessage(`✅ 成功创建 ${createdPhaseGroups.length} 个阶段分组`);
      console.log(`✅ 阶段分组创建完成，共创建 ${createdPhaseGroups.length} 个节点`);

      // 2秒后清除消息
      setTimeout(() => {
        setPhaseGroupCreationMessage('');
      }, 2000);

    } catch (error) {
      console.error('❌ 创建阶段分组失败:', error);
      setPhaseGroupCreationMessage('❌ 创建阶段分组失败，请重试');

      // 3秒后清除错误消息
      setTimeout(() => {
        setPhaseGroupCreationMessage('');
      }, 3000);

      throw error;
    } finally {
      setIsCreatingPhaseGroups(false);
    }

    return createdPhaseGroups;
  }, [calculatePhaseGroupPosition, updateChapterParent, onSave]);

  // 处理关闭
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300); // 动画持续时间
  };

  // 处理节点类型变化
  const handleTypeChange = useCallback((newType: 'volume' | 'event' | 'phaseGroup' | 'chapter' | 'plot' | 'dialogue') => {
    if (currentNode) {
      const updatedNode = { ...currentNode, type: newType };
      setCurrentNode(updatedNode);
      setType(newType);
    }
  }, [currentNode]);

  // 处理保存
  const handleSave = () => {
    if (!currentNode) return;

    // 对于VolumeEditor和PhaseGroupEditor，触发其内部保存
    if (type === 'volume' && volumeEditorRef.current) {
      volumeEditorRef.current.triggerSave();
      return;
    }

    if (type === 'phaseGroup' && phaseGroupEditorRef.current) {
      phaseGroupEditorRef.current.triggerSave();
      return;
    }

    // 对于其他编辑器，使用原有逻辑
    const updatedNode: OutlineNodeType = {
      ...currentNode,
      // 显式保留位置信息，确保不会丢失
      position: currentNode.position
    };

    onSave(updatedNode);
    handleClose();
  };

  // 如果对话框未打开，不渲染
  if (!isOpen && !isClosing) return null;

  // 获取节点类型颜色（添加fallback值）
  const getNodeColor = () => {
    switch (type) {
      case 'volume':
        return 'var(--outline-volume, #8B5CF6)';
      case 'chapter':
        return 'var(--outline-primary, #5B6AF0)';
      case 'plot':
        return 'var(--outline-secondary, #F97316)';
      case 'dialogue':
        return 'var(--outline-info, #10B981)';
      default:
        return 'var(--outline-primary, #5B6AF0)';
    }
  };

  return createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center"
      style={{
        zIndex: 9997, // 比其他弹窗稍低
        opacity: isClosing ? 0 : 1,
        transition: 'opacity 0.3s ease-out'
      }}
      onClick={handleClose}
      >
        <div
          className="node-edit-dialog bg-white rounded-lg shadow-xl w-full overflow-hidden flex flex-col"
          style={{
            transform: isClosing ? 'scale(0.95)' : 'scale(1)',
            opacity: isClosing ? 0 : 1,
            transition: 'transform 0.3s ease-out, opacity 0.3s ease-out',
            maxHeight: 'var(--modal-max-height, 75vh)',
            width: '90vw',
            maxWidth: '1200px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 对话框标题 - 固定头部 */}
          <div className="flex-shrink-0 p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-700">编辑节点</h3>
            <button
              className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
              onClick={handleClose}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 表单内容 - 可滚动区域 */}
          <div className="flex-1 overflow-y-auto p-4 min-h-0">
            {/* 节点类型选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">节点类型</label>
              <div className="grid grid-cols-2 gap-2 mb-2">
                <button
                  className={`py-2 px-3 rounded-md border ${
                    type === 'volume'
                      ? 'text-white border-transparent'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  style={type === 'volume' ? {
                    backgroundColor: getNodeColor(),
                    borderColor: getNodeColor()
                  } : {}}
                  onClick={() => handleTypeChange('volume')}
                >
                  总纲/卷
                </button>
                <button
                  className={`py-2 px-3 rounded-md border ${
                    type === 'phaseGroup'
                      ? 'text-white border-transparent'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  style={type === 'phaseGroup' ? {
                    backgroundColor: getNodeColor(),
                    borderColor: getNodeColor()
                  } : {}}
                  onClick={() => handleTypeChange('phaseGroup')}
                >
                  阶段分组
                </button>
                <button
                  className={`py-2 px-3 rounded-md border ${
                    type === 'chapter'
                      ? 'text-white border-transparent'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  style={type === 'chapter' ? {
                    backgroundColor: getNodeColor(),
                    borderColor: getNodeColor()
                  } : {}}
                  onClick={() => handleTypeChange('chapter')}
                >
                  章节
                </button>
              </div>
              <div className="flex space-x-2">
                <button
                  className={`flex-1 py-2 px-3 rounded-md border ${
                    type === 'plot'
                      ? 'text-white border-transparent'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  style={type === 'plot' ? {
                    backgroundColor: getNodeColor(),
                    borderColor: getNodeColor()
                  } : {}}
                  onClick={() => handleTypeChange('plot')}
                >
                  剧情节点
                </button>
                <button
                  className={`flex-1 py-2 px-3 rounded-md border ${
                    type === 'dialogue'
                      ? 'text-white border-transparent'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  style={type === 'dialogue' ? {
                    backgroundColor: getNodeColor(),
                    borderColor: getNodeColor()
                  } : {}}
                  onClick={() => handleTypeChange('dialogue')}
                >
                  对话设计
                </button>
              </div>
            </div>

            {/* 差异化编辑器 */}
            <div className="mb-4">
              {currentNode && type === 'volume' && (
                <VolumeEditor
                  ref={volumeEditorRef}
                  node={currentNode}
                  onSave={async (data) => {
                    const updatedNode = { ...currentNode, ...data };
                    handleNodeChange(updatedNode);

                    // 先保存卷纲节点
                    onSave(updatedNode);

                    // 检查是否需要创建阶段分组
                    const dataWithFlags = data as any;
                    if (dataWithFlags.shouldGeneratePhaseGroups && data.cycleChapterMappings) {
                      try {
                        await createPhaseGroupsFromVolume(updatedNode);
                      } catch (error) {
                        console.error('创建阶段分组失败:', error);
                        // 不阻止对话框关闭，但显示错误信息
                      }
                    }

                    handleClose(); // 保存后关闭对话框
                  }}
                  onCancel={() => {}}
                  allNodes={allNodes}
                />
              )}

              {currentNode && type === 'event' && (
                <EventEditor
                  ref={eventEditorRef}
                  node={currentNode}
                  onSave={(data) => {
                    const updatedNode = { ...currentNode, ...data };
                    handleNodeChange(updatedNode);
                    onSave(updatedNode); // 保存事件刚节点
                    handleClose(); // 保存后关闭对话框
                  }}
                  onCancel={() => {}}
                  allNodes={allNodes}
                />
              )}
              {currentNode && type === 'phaseGroup' && (
                <PhaseGroupEditor
                  ref={phaseGroupEditorRef}
                  node={currentNode}
                  onSave={(data) => {
                    const updatedNode = { ...currentNode, ...data };
                    handleNodeChange(updatedNode);
                    onSave(updatedNode); // 直接保存数据
                    handleClose(); // 保存后关闭对话框
                  }}
                  onCancel={() => {}}
                />
              )}
              {currentNode && type === 'chapter' && (
                <ChapterEditor
                  node={currentNode}
                  onChange={handleNodeChange}
                />
              )}
              {currentNode && type === 'plot' && (
                <PlotEditor
                  node={currentNode}
                  onChange={handleNodeChange}
                />
              )}
              {currentNode && type === 'dialogue' && (
                <DialogueEditor
                  node={currentNode}
                  onChange={handleNodeChange}
                />
              )}
            </div>
          </div>

          {/* 阶段分组创建状态显示 */}
          {phaseGroupCreationMessage && (
            <div className="flex-shrink-0 px-4 py-2 bg-blue-50 border-t border-blue-200">
              <div className="flex items-center space-x-2">
                {isCreatingPhaseGroups && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                )}
                <span className="text-sm text-blue-700">{phaseGroupCreationMessage}</span>
              </div>
            </div>
          )}

          {/* 底部按钮 - 固定底部 */}
          <div className="flex-shrink-0 px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
            <Button
              text="取消"
              type="ghost"
              onClick={handleClose}
              className="hover:bg-gray-100"
              disabled={isCreatingPhaseGroups}
            />
            <Button
              text={isCreatingPhaseGroups ? "创建中..." : "保存"}
              type="primary"
              onClick={handleSave}
              className="shadow-sm"
              disabled={isCreatingPhaseGroups}
              style={{
                backgroundColor: getNodeColor(),
                borderColor: getNodeColor()
              }}
            />
          </div>
        </div>
      </div>,
    document.body
  );
};

export default NodeEditDialog;
