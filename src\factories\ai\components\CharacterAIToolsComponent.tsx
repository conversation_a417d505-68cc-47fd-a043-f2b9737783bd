"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import { createUIFactory } from '@/factories/ui/UIFactory.ts';
import { createNotificationFactory } from '@/factories/notification';

interface CharacterAIToolsComponentProps {
  character: Character;
  bookId: string;
  onGenerateDescription: (character: Character) => Promise<string>;
  onExpandCharacter: (character: Character, fields?: string[]) => Promise<Character>;
  onAnalyzeRelationships: (character: Character, otherCharacters: Character[]) => Promise<any[]>;
  onExtractCharacters: (text: string, bookId: string) => Promise<Character[]>;
  onCancel: () => void;
  loading: boolean;
}

/**
 * 人物AI工具组件
 * 提供高级参数设置、关联章节内容和智能分段工具
 */
export const CharacterAIToolsComponent: React.FC<CharacterAIToolsComponentProps> = ({
  character,
  bookId,
  onGenerateDescription,
  onExpandCharacter,
  onAnalyzeRelationships,
  onExtractCharacters,
  onCancel,
  loading
}) => {
  const [advancedParams, setAdvancedParams] = useState({
    temperature: 0.7,
    maxTokens: 0, // 0表示不限制
    customPrompt: ''
  });
  const [showAdvancedParams, setShowAdvancedParams] = useState(false);
  const [selectedChapters, setSelectedChapters] = useState<string[]>([]);
  const [chapterContent, setChapterContent] = useState('');
  const [showChapterSelector, setShowChapterSelector] = useState(false);
  const [availableChapters, setAvailableChapters] = useState<{id: string, title: string}[]>([]);
  const [segmentedText, setSegmentedText] = useState<string[]>([]);
  const [showSegmentTool, setShowSegmentTool] = useState(false);
  const [textToSegment, setTextToSegment] = useState('');

  // 创建UI工厂
  const uiFactory = createUIFactory();

  // 创建通知工厂
  const notificationFactory = createNotificationFactory();
  const notification = notificationFactory.createNotificationComponent();

  // 模拟获取章节列表
  useEffect(() => {
    // 实际应用中，这里应该从数据库获取章节列表
    const fetchChapters = async () => {
      // 模拟API调用
      setTimeout(() => {
        setAvailableChapters([
          { id: '1', title: '第1章 开始' },
          { id: '2', title: '第2章 冒险' },
          { id: '3', title: '第3章 危机' },
          { id: '4', title: '第4章 转折' },
          { id: '5', title: '第5章 结局' }
        ]);
      }, 500);
    };

    fetchChapters();
  }, [bookId]);

  // 处理高级参数变更
  const handleParamChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setAdvancedParams(prev => ({
      ...prev,
      [name]: name === 'temperature' || name === 'maxTokens' ? parseFloat(value) : value
    }));
  };

  // 处理章节选择
  const handleChapterSelect = (chapterId: string) => {
    setSelectedChapters(prev => {
      if (prev.includes(chapterId)) {
        return prev.filter(id => id !== chapterId);
      } else {
        return [...prev, chapterId];
      }
    });
  };

  // 加载选中章节内容
  const loadChapterContent = () => {
    // 实际应用中，这里应该从数据库获取章节内容
    // 模拟API调用
    notification.showInfo('正在加载章节内容...');
    setTimeout(() => {
      const content = selectedChapters.map(id => {
        const chapter = availableChapters.find(ch => ch.id === id);
        return `# ${chapter?.title || '未知章节'}\n\n这是${chapter?.title || '未知章节'}的内容。这里应该包含人物${character.name}的相关描述和对话。`;
      }).join('\n\n');

      setChapterContent(content);
      notification.showSuccess('章节内容加载完成');
      setShowChapterSelector(false);
    }, 1000);
  };

  // 智能分段文本
  const segmentText = async () => {
    if (!textToSegment.trim()) {
      notification.showWarning('请输入要分段的文本');
      return;
    }

    notification.showInfo('正在进行智能分段...');

    // 实际应用中，这里应该调用AI接口进行智能分段
    // 模拟API调用
    setTimeout(() => {
      const segments = textToSegment.split(/\\n\\n|\\n/).filter(s => s.trim());
      setSegmentedText(segments);
      notification.showSuccess('智能分段完成');
    }, 1000);
  };

  // 使用关联章节内容生成人物描述
  const generateWithChapters = async () => {
    if (!chapterContent.trim()) {
      notification.showWarning('请先加载章节内容');
      return;
    }

    notification.showInfo('正在基于章节内容生成人物描述...');

    try {
      // 这里应该将章节内容作为上下文传递给AI
      // 实际实现时需要修改onGenerateDescription方法以接受额外的上下文参数
      const description = await onGenerateDescription(character);
      notification.showSuccess('基于章节内容的人物描述生成成功');
    } catch (error) {
      notification.showError('生成失败，请重试');
      console.error(error);
    }
  };

  // 使用自定义提示词扩展人物信息
  const expandWithCustomPrompt = async () => {
    if (!advancedParams.customPrompt.trim()) {
      notification.showWarning('请输入自定义提示词');
      return;
    }

    notification.showInfo('正在使用自定义提示词扩展人物信息...');

    try {
      // 这里应该将自定义提示词传递给AI
      // 实际实现时需要修改onExpandCharacter方法以接受自定义提示词
      const updatedCharacter = await onExpandCharacter(character);
      notification.showSuccess('使用自定义提示词扩展人物信息成功');
    } catch (error) {
      notification.showError('扩展失败，请重试');
      console.error(error);
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-blue-600">人物AI工具</h2>

      {/* 人物信息摘要 */}
      <div className="mb-6 p-3 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">当前人物: {character.name}</h3>
        <p className="text-sm text-gray-600">{character.description || '无描述'}</p>
      </div>

      {/* 工具按钮组 */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <button
          className="p-4 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={() => onGenerateDescription(character)}
          disabled={loading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
          <span>生成人物描述</span>
        </button>

        <button
          className="p-4 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={() => onExpandCharacter(character)}
          disabled={loading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          </svg>
          <span>扩展人物信息</span>
        </button>

        <button
          className="p-4 bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={() => setShowAdvancedParams(!showAdvancedParams)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span>高级参数设置</span>
        </button>

        <button
          className="p-4 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={() => setShowChapterSelector(!showChapterSelector)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>关联章节内容</span>
        </button>

        <button
          className="p-4 bg-gradient-to-r from-blue-500 to-cyan-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={() => setShowSegmentTool(!showSegmentTool)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
          <span>智能分段工具</span>
        </button>

        <button
          className="p-4 bg-gradient-to-r from-gray-500 to-gray-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center"
          onClick={onCancel}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span>关闭</span>
        </button>
      </div>

      {/* 高级参数设置 */}
      {showAdvancedParams && (
        <div className="mb-6 p-4 border border-yellow-300 rounded-lg bg-yellow-50 animate-fadeIn">
          <h3 className="text-lg font-semibold mb-3 text-yellow-800">高级参数设置</h3>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">温度 (0.1-1.0)</label>
              <input
                type="range"
                name="temperature"
                min="0.1"
                max="1.0"
                step="0.1"
                value={advancedParams.temperature}
                onChange={handleParamChange}
                className="w-full"
              />
              <div className="text-center text-sm">{advancedParams.temperature}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">最大令牌数 (0表示不限制)</label>
              <input
                type="number"
                name="maxTokens"
                min="0"
                max="100000"
                step="100"
                value={advancedParams.maxTokens}
                onChange={handleParamChange}
                className="w-full p-2 border rounded"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">自定义提示词</label>
            <textarea
              name="customPrompt"
              value={advancedParams.customPrompt}
              onChange={handleParamChange}
              placeholder="输入自定义提示词，指导AI如何生成或扩展人物信息..."
              className="w-full p-2 border rounded h-24"
            ></textarea>
          </div>

          <div className="flex justify-end">
            <button
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              onClick={expandWithCustomPrompt}
              disabled={loading}
            >
              使用自定义提示词
            </button>
          </div>
        </div>
      )}

      {/* 章节选择器 */}
      {showChapterSelector && (
        <div className="mb-6 p-4 border border-red-300 rounded-lg bg-red-50 animate-fadeIn">
          <h3 className="text-lg font-semibold mb-3 text-red-800">关联章节内容</h3>

          <div className="mb-4">
            <h4 className="font-medium mb-2">选择包含该人物的章节:</h4>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto p-2 border rounded bg-white">
              {availableChapters.map(chapter => (
                <div key={chapter.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`chapter-${chapter.id}`}
                    checked={selectedChapters.includes(chapter.id)}
                    onChange={() => handleChapterSelect(chapter.id)}
                    className="mr-2"
                  />
                  <label htmlFor={`chapter-${chapter.id}`}>{chapter.title}</label>
                </div>
              ))}
            </div>
          </div>

          {selectedChapters.length > 0 && (
            <div className="flex justify-between">
              <button
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                onClick={loadChapterContent}
                disabled={loading}
              >
                加载选中章节内容
              </button>

              {chapterContent && (
                <button
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  onClick={generateWithChapters}
                  disabled={loading}
                >
                  基于章节内容生成
                </button>
              )}
            </div>
          )}

          {chapterContent && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">章节内容预览:</h4>
              <div className="p-3 border rounded bg-white max-h-40 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm">{chapterContent}</pre>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 智能分段工具 */}
      {showSegmentTool && (
        <div className="mb-6 p-4 border border-blue-300 rounded-lg bg-blue-50 animate-fadeIn">
          <h3 className="text-lg font-semibold mb-3 text-blue-800">智能分段工具</h3>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">输入要分段的文本</label>
            <textarea
              value={textToSegment}
              onChange={(e) => setTextToSegment(e.target.value)}
              placeholder="粘贴长文本，AI将帮助你智能分段..."
              className="w-full p-2 border rounded h-32"
            ></textarea>
          </div>

          <div className="flex justify-end mb-4">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              onClick={segmentText}
              disabled={loading}
            >
              智能分段
            </button>
          </div>

          {segmentedText.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">分段结果:</h4>
              <div className="p-3 border rounded bg-white max-h-60 overflow-y-auto">
                {segmentedText.map((segment, index) => (
                  <div key={index} className="mb-2 p-2 border-b">
                    <p className="whitespace-pre-wrap">{segment}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-lg">处理中，请稍候...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CharacterAIToolsComponent;
