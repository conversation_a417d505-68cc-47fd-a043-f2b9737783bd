"use client";

import React, { useState, useEffect } from 'react';
import { Character } from '@/lib/db/dexie';
import createCharacterExtractorAIAdapter, { UpdateSuggestion } from '@/adapters/ai/CharacterExtractorAIAdapter';

// 定义通用的章节接口，兼容不同的Chapter类型
interface GenericChapter {
  id?: string;
  bookId?: string;
  title?: string;
  content: string;
  order?: number;
  chapterNumber?: number;
  wordCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface CharacterUpdateFromChapterProps {
  character: Character;
  chapters: GenericChapter[];
  onUpdate: (updatedCharacter: Character) => void;
}

/**
 * 人物更新组件
 * 用于从章节内容中更新人物信息
 */
const CharacterUpdateFromChapter: React.FC<CharacterUpdateFromChapterProps> = ({
  character,
  chapters,
  onUpdate
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedChapterId, setSelectedChapterId] = useState<string | null>(null);
  const [updateSuggestions, setUpdateSuggestions] = useState<UpdateSuggestion[]>([]);
  const [selectedSuggestions, setSelectedSuggestions] = useState<{ [field: string]: boolean }>({});
  const [error, setError] = useState<string | null>(null);

  // 创建人物提取AI适配器
  const characterExtractorAIAdapter = createCharacterExtractorAIAdapter();

  // 初始化选中的建议
  useEffect(() => {
    const initialSelectedSuggestions: { [field: string]: boolean } = {};
    updateSuggestions.forEach(suggestion => {
      initialSelectedSuggestions[suggestion.field] = true;
    });
    setSelectedSuggestions(initialSelectedSuggestions);
  }, [updateSuggestions]);

  /**
   * 更新人物信息
   */
  const updateCharacter = async () => {
    if (!selectedChapterId) {
      setError('请选择一个章节');
      return;
    }

    const selectedChapter = chapters.find(c => c.id === selectedChapterId);
    if (!selectedChapter || !selectedChapter.content) {
      setError('章节内容为空，无法更新人物信息');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 从章节内容中更新人物信息
      const suggestions = await characterExtractorAIAdapter.updateCharacterFromChapter(
        character,
        selectedChapter.content
      );

      // 设置更新建议
      setUpdateSuggestions(suggestions);

      // 如果没有更新建议
      if (suggestions.length === 0) {
        setError('未从章节中找到可更新的人物信息');
      }
    } catch (error: any) {
      console.error('更新人物信息失败:', error);
      setError('更新人物信息失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消更新
   */
  const cancelUpdate = () => {
    characterExtractorAIAdapter.cancelRequest();
    setIsLoading(false);
  };

  /**
   * 切换建议选中状态
   * @param field 字段名
   */
  const toggleSuggestion = (field: string) => {
    setSelectedSuggestions(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  /**
   * 应用选中的更新建议
   */
  const applySelectedSuggestions = () => {
    // 创建更新后的人物对象
    const updatedCharacter = { ...character };

    // 应用选中的更新建议
    updateSuggestions.forEach(suggestion => {
      if (selectedSuggestions[suggestion.field]) {
        updatedCharacter[suggestion.field as keyof Character] = suggestion.suggestedValue as any;
      }
    });

    // 调用更新回调
    onUpdate(updatedCharacter);

    // 清空更新建议
    setUpdateSuggestions([]);
  };

  return (
    <div>
      {/* 更新按钮 */}
      <button
        className="ml-2 p-1 text-green-600 hover:text-green-800 transition-colors"
        onClick={() => document.getElementById('chapter-selector-modal')?.classList.remove('hidden')}
        title="从章节更新人物信息"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </button>

      {/* 章节选择模态框 */}
      <div id="chapter-selector-modal" className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
          <h3 className="text-xl font-bold mb-4 text-purple-700">选择要更新的章节</h3>

          <select
            className="w-full p-2 border rounded-lg mb-4"
            value={selectedChapterId || ''}
            onChange={e => setSelectedChapterId(e.target.value)}
          >
            <option value="">-- 请选择章节 --</option>
            {chapters.map(chapter => (
              <option key={chapter.id} value={chapter.id}>
                {chapter.title || `章节 ${chapter.chapterNumber}`}
              </option>
            ))}
          </select>

          <div className="flex justify-end space-x-2">
            <button
              className="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              onClick={() => document.getElementById('chapter-selector-modal')?.classList.add('hidden')}
            >
              取消
            </button>
            <button
              className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              onClick={() => {
                document.getElementById('chapter-selector-modal')?.classList.add('hidden');
                updateCharacter();
              }}
              disabled={!selectedChapterId}
            >
              更新
            </button>
          </div>
        </div>
      </div>

      {/* 加载中状态 */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <div className="flex items-center justify-center mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700"></div>
            </div>
            <p className="text-center">正在更新人物信息...</p>
            <button
              className="mt-4 w-full p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={cancelUpdate}
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 更新建议 */}
      {!isLoading && updateSuggestions.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-4xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold mb-4 text-purple-700">更新建议</h3>

            <div className="space-y-4">
              {updateSuggestions.map((suggestion, index) => (
                <div key={index} className="border p-4 rounded-lg">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`suggestion-${index}`}
                      checked={selectedSuggestions[suggestion.field] || false}
                      onChange={() => toggleSuggestion(suggestion.field)}
                      className="mr-2"
                    />
                    <label htmlFor={`suggestion-${index}`} className="font-bold">
                      {suggestion.field}
                    </label>
                  </div>

                  <div className="mt-2 grid grid-cols-2 gap-4">
                    <div className="bg-gray-100 p-2 rounded">
                      <p className="text-sm font-semibold text-gray-500">当前值:</p>
                      <p className="whitespace-pre-wrap">{suggestion.currentValue || '(无)'}</p>
                    </div>
                    <div className="bg-green-50 p-2 rounded">
                      <p className="text-sm font-semibold text-green-500">建议值:</p>
                      <p className="whitespace-pre-wrap">{suggestion.suggestedValue}</p>
                    </div>
                  </div>

                  <p className="mt-2 text-sm text-gray-500">
                    <span className="font-semibold">原因:</span> {suggestion.reason}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-end space-x-2">
              <button
                className="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                onClick={() => setUpdateSuggestions([])}
              >
                取消
              </button>
              <button
                className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                onClick={applySelectedSuggestions}
              >
                应用选中的更新
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <div className="mx-auto mb-4 text-red-500 flex justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <p className="text-center">{error}</p>
            <button
              className="mt-4 w-full p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              onClick={() => setError(null)}
            >
              关闭
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CharacterUpdateFromChapter;
