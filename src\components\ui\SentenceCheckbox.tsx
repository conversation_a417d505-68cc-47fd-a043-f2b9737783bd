"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SentenceCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  ariaLabel?: string;
}

/**
 * 句子勾选框组件
 * 带有emerald主题色和SVG路径重绘动画
 */
export const SentenceCheckbox: React.FC<SentenceCheckboxProps> = ({
  checked,
  onChange,
  className = '',
  disabled = false,
  size = 'md',
  ariaLabel = '选择句子进行修改'
}) => {
  const [animationKey, setAnimationKey] = useState(0);
  const [showRipple, setShowRipple] = useState(false);
  const pathRef = useRef<SVGPathElement>(null);

  // 尺寸配置
  const sizeConfig = {
    sm: { container: 'w-4 h-4', icon: 'w-2.5 h-2.5', strokeWidth: 2.5 },
    md: { container: 'w-5 h-5', icon: 'w-3 h-3', strokeWidth: 3 },
    lg: { container: 'w-6 h-6', icon: 'w-4 h-4', strokeWidth: 3.5 }
  };

  const config = sizeConfig[size];

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled) return;

    // 触发波纹动画
    setShowRipple(true);
    setTimeout(() => setShowRipple(false), 600);

    // 更新动画key强制重新渲染
    setAnimationKey(prev => prev + 1);
    
    onChange(!checked);
    
    console.log('🔘 勾选框点击:', { checked: !checked, disabled });
  };

  // SVG路径重绘动画
  useEffect(() => {
    if (checked && pathRef.current) {
      const path = pathRef.current;
      const length = path.getTotalLength();
      
      // 设置初始状态
      path.style.strokeDasharray = `${length}`;
      path.style.strokeDashoffset = `${length}`;
      
      // 触发动画
      setTimeout(() => {
        path.style.transition = 'stroke-dashoffset 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        path.style.strokeDashoffset = '0';
      }, 50);
    }
  }, [checked, animationKey]);

  return (
    <motion.div
      className={`relative cursor-pointer select-none ${config.container} ${className}`}
      onClick={handleClick}
      whileHover={!disabled ? { scale: 1.05 } : {}}
      whileTap={!disabled ? { scale: 0.95 } : {}}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
      role="checkbox"
      aria-checked={checked}
      aria-label={ariaLabel}
      tabIndex={disabled ? -1 : 0}
      onKeyDown={(e) => {
        if ((e.key === ' ' || e.key === 'Enter') && !disabled) {
          e.preventDefault();
          handleClick(e as any);
        }
      }}
    >
      {/* 复选框背景 */}
      <motion.div
        className={`
          w-full h-full border-2 rounded flex items-center justify-center
          transition-all duration-200 relative overflow-hidden
          ${disabled 
            ? 'border-gray-300 bg-gray-100 cursor-not-allowed' 
            : checked
              ? 'border-emerald-500 bg-emerald-500'
              : 'border-emerald-300 bg-white hover:border-emerald-400 hover:bg-emerald-50'
          }
        `}
        animate={{
          scale: checked ? 1.05 : 1,
          backgroundColor: checked
            ? '#10b981' // emerald-500
            : disabled
              ? '#f3f4f6' // gray-100
              : '#ffffff'
        }}
        transition={{
          scale: { type: "spring", stiffness: 400, damping: 25 },
          backgroundColor: { duration: 0.2 }
        }}
      >
        {/* 波纹动画效果 */}
        <AnimatePresence>
          {showRipple && !disabled && (
            <motion.div
              className="absolute inset-0 bg-emerald-400 rounded"
              initial={{ scale: 0, opacity: 0.6 }}
              animate={{ scale: 1.5, opacity: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />
          )}
        </AnimatePresence>

        {/* SVG勾选图标 */}
        <AnimatePresence>
          {checked && !disabled && (
            <motion.svg
              key={`check-${animationKey}`}
              className={`${config.icon} text-white relative z-10`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              initial={{ scale: 0, rotate: -45, opacity: 0 }}
              animate={{ scale: 1, rotate: 0, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 25,
                delay: 0.1
              }}
            >
              <path
                ref={pathRef}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={config.strokeWidth}
                d="M5 13l4 4L19 7"
              />
            </motion.svg>
          )}
        </AnimatePresence>

        {/* 禁用状态图标 */}
        {disabled && (
          <svg
            className={`${config.icon} text-gray-400`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        )}
      </motion.div>

      {/* 焦点指示器 */}
      <motion.div
        className="absolute inset-0 rounded border-2 border-emerald-400 opacity-0"
        animate={{ opacity: 0 }}
        whileFocus={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
    </motion.div>
  );
};

/**
 * 批量选择按钮组件
 */
interface BatchSelectButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md';
  disabled?: boolean;
  className?: string;
}

export const BatchSelectButton: React.FC<BatchSelectButtonProps> = ({
  children,
  onClick,
  variant = 'secondary',
  size = 'sm',
  disabled = false,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-emerald-600 text-white hover:bg-emerald-700 disabled:bg-gray-300',
    secondary: 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200 disabled:bg-gray-100 disabled:text-gray-400',
    outline: 'border border-emerald-300 text-emerald-700 hover:bg-emerald-50 disabled:border-gray-300 disabled:text-gray-400'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm'
  };

  return (
    <motion.button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={onClick}
      disabled={disabled}
      whileHover={!disabled ? { scale: 1.02, y: -1 } : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
    >
      {children}
    </motion.button>
  );
};
