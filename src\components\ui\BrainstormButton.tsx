"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface BrainstormButtonProps {
  bookId: string;
  chapterContent?: string;
  onBrainstormStart?: () => void;
  onBrainstormComplete?: (result: any) => void;
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  disabled?: boolean;
}

/**
 * AI脑洞按钮组件
 * 用于启动AI脑洞生成功能，包括题材、书名、简介等创意内容
 */
const BrainstormButton: React.FC<BrainstormButtonProps> = ({
  bookId,
  chapterContent,
  onBrainstormStart,
  onBrainstormComplete,
  variant = 'default',
  className = '',
  disabled = false
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleClick = async () => {
    if (disabled || isProcessing) return;

    try {
      setIsProcessing(true);
      onBrainstormStart?.();
      
      // TODO: 这里将来会打开脑洞生成对话框
      console.log('🧠 启动AI脑洞功能:', { bookId, chapterContent });
      
      // 模拟处理过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onBrainstormComplete?.({ success: true });
    } catch (error) {
      console.error('AI脑洞功能启动失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const baseClasses = "inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

  const renderButton = () => {
    // 魔法棒+星星组合SVG图标 - 基于学习的设计创作原创图标
    const brainstormIcon = (
      <svg className={variant === 'compact' ? 'w-3.5 h-3.5' : 'w-4 h-4'} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <defs>
          <linearGradient id="brainstormGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8B5CF6" />
            <stop offset="100%" stopColor="#A855F7" />
          </linearGradient>
        </defs>
        {/* 魔法棒主体 */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
              d="M3 21l14-14" />
        {/* 魔法棒顶端星星 */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
              d="M18 3l1.5 1.5L18 6l-1.5-1.5L18 3z"
              fill="currentColor" />
        {/* 飞散的星星效果 */}
        <circle cx="8" cy="16" r="0.5" fill="currentColor" opacity="0.8" />
        <circle cx="11" cy="13" r="0.5" fill="currentColor" opacity="0.6" />
        <circle cx="14" cy="10" r="0.5" fill="currentColor" opacity="0.4" />
        {/* 小星星装饰 */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1}
              d="M6 18l0.5 0.5L6 19l-0.5-0.5L6 18z"
              opacity="0.7" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1}
              d="M20 8l0.5 0.5L20 9l-0.5-0.5L20 8z"
              opacity="0.5" />
      </svg>
    );

    const loadingIcon = (
      <motion.div
        className={`${variant === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} border-2 border-white border-t-transparent rounded-full`}
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      />
    );
    
    switch (variant) {
      case 'compact':
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing}
            className={`${baseClasses} px-3 py-1 text-xs bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 focus:ring-purple-500 shadow-sm ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.02 }}
            whileTap={{ scale: disabled ? 1 : 0.98 }}
          >
            {isProcessing ? (
              <>
                {loadingIcon}
                <span className="ml-1">生成中</span>
              </>
            ) : (
              <>
                {brainstormIcon}
                <span className="ml-1">AI脑洞</span>
              </>
            )}
          </motion.button>
        );

      case 'icon-only':
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing}
            className={`${baseClasses} p-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 focus:ring-purple-500 shadow-md ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.05 }}
            whileTap={{ scale: disabled ? 1 : 0.95 }}
            title="AI脑洞"
          >
            {isProcessing ? loadingIcon : brainstormIcon}
          </motion.button>
        );

      default:
        return (
          <motion.button
            type="button"
            onClick={handleClick}
            disabled={disabled || isProcessing}
            className={`${baseClasses} px-4 py-2 text-sm bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 focus:ring-purple-500 shadow-md ${className}`}
            whileHover={{ scale: disabled ? 1 : 1.02 }}
            whileTap={{ scale: disabled ? 1 : 0.98 }}
          >
            {isProcessing ? (
              <>
                {loadingIcon}
                <span className="ml-2">正在生成创意...</span>
              </>
            ) : (
              <>
                {brainstormIcon}
                <span className="ml-2">AI脑洞</span>
              </>
            )}
          </motion.button>
        );
    }
  };

  return renderButton();
};

export default BrainstormButton;
