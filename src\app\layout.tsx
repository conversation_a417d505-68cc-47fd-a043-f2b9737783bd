import type { Metadata } from "next";
import "./globals.css";
import Head from "next/head";
import dynamic from "next/dynamic";
import { NotificationProvider } from "@/components/ui/NotificationSystem";

// 动态导入通知管理器，避免SSR问题
const NotificationManager = dynamic(
  () => import("@/factories/ui/components/common/NotificationManager"),
  { ssr: false }
);

// 不再使用Next.js内置字体，改为自定义字体

export const metadata: Metadata = {
  title: "AI小说平台",
  description: "AI辅助创作小说的工具平台",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="stylesheet" href="/fonts/fonts.css" />
      </head>
      <body className="font-roboto">
        <NotificationProvider>
          {children}
          {/* Portal容器 */}
          <div id="outline-menu-portal"></div>
          <div id="outline-relation-portal"></div>
          <div id="node-edit-dialog-portal"></div>

          {/* 通知管理器 */}
          <NotificationManager />
        </NotificationProvider>
      </body>
    </html>
  );
}
