"use client";

import { WorldBuilding } from '@/lib/db/dexie';

/**
 * 世界观信息格式化器
 * 负责格式化世界观信息，用于AI请求和显示
 */
export class WorldBuildingInfoFormatter {
  /**
   * 格式化世界观信息
   * @param worldBuilding 世界观对象
   * @returns 格式化后的世界观信息字符串
   */
  formatWorldBuildingInfo(worldBuilding: WorldBuilding): string {
    let info = `名称: ${worldBuilding.name}\n`;

    if (worldBuilding.category) {
      info += `类别: ${worldBuilding.category}\n`;
    }

    if (worldBuilding.description) {
      info += `描述: ${worldBuilding.description}\n`;
    }

    // 添加自定义属性
    if (worldBuilding.attributes) {
      for (const [key, value] of Object.entries(worldBuilding.attributes)) {
        info += `${key}: ${value}\n`;
      }
    }

    return info;
  }

  /**
   * 格式化世界观元素的完整信息，包括类别特有字段
   * @param worldBuilding 世界观对象
   * @returns 格式化后的世界观信息字符串
   */
  async formatWorldBuildingFullInfo(worldBuilding: WorldBuilding): Promise<string> {
    let info = `世界观元素: ${worldBuilding.name}\n`;

    // 添加类别
    const category = worldBuilding.category || '未知';
    info += `类别: ${category}\n`;

    // 添加主类别
    const mainCategory = this.getCategoryMainLabel(category);
    if (mainCategory) {
      info += `主类别: ${mainCategory}\n`;
    }

    // 添加描述
    if (worldBuilding.description) {
      info += `描述: ${worldBuilding.description}\n`;
    } else {
      info += `描述: 无描述\n`;
    }

    // 添加类别特有字段
    info += this.formatCategorySpecificFields(worldBuilding);

    // 添加通用属性
    const commonFields = ['重要性', '时间跨度', '影响范围', '起源', '规则', '象征意义', '冲突'];
    let hasCommonFields = false;

    if (worldBuilding.attributes) {
      for (const field of commonFields) {
        if (worldBuilding.attributes[field]) {
          if (!hasCommonFields) {
            info += `\n通用属性:\n`;
            hasCommonFields = true;
          }
          info += `- ${field}: ${worldBuilding.attributes[field]}\n`;
        }
      }
    }

    // 添加关联信息
    if (worldBuilding.relatedCharacterIds && worldBuilding.relatedCharacterIds.length > 0) {
      info += `\n关联人物: ${worldBuilding.relatedCharacterIds.length}个\n`;
      // 这里可以添加获取人物名称的逻辑，类似于下面的章节名称获取
    }

    if (worldBuilding.relatedWorldBuildingIds && worldBuilding.relatedWorldBuildingIds.length > 0) {
      info += `关联世界观: ${worldBuilding.relatedWorldBuildingIds.length}个\n`;
      // 这里可以添加获取世界观名称的逻辑，类似于下面的章节名称获取
    }

    if (worldBuilding.relatedTerminologyIds && worldBuilding.relatedTerminologyIds.length > 0) {
      info += `关联术语: ${worldBuilding.relatedTerminologyIds.length}个\n`;
      // 这里可以添加获取术语名称的逻辑，类似于下面的章节名称获取
    }

    // 添加提取自哪些章节，显示具体章节名称
    if (worldBuilding.extractedFromChapterIds && worldBuilding.extractedFromChapterIds.length > 0) {
      try {
        // 导入数据库
        const { db } = await import('@/lib/db/dexie');

        // 获取所有相关章节
        const chapterIds = worldBuilding.extractedFromChapterIds;
        const chapters = await db.chapters.where('id').anyOf(chapterIds).toArray();

        if (chapters.length > 0) {
          info += `\n提取自章节 (${chapters.length}个):\n`;

          // 按章节顺序排序
          const sortedChapters = [...chapters].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB;
          });

          // 添加章节名称
          for (const chapter of sortedChapters) {
            const chapterTitle = chapter.title || (chapter.order !== undefined ? `第${chapter.order + 1}章` : '无标题章节');
            info += `- ${chapterTitle}\n`;
          }
        } else {
          info += `\n提取自章节: ${chapterIds.length}个 (无法获取章节名称)\n`;
        }
      } catch (error) {
        console.error('获取章节信息失败:', error);
        info += `\n提取自章节: ${worldBuilding.extractedFromChapterIds.length}个 (无法获取章节名称)\n`;
      }
    }

    return info;
  }

  /**
   * 格式化世界观元素的完整信息（同步版本，用于向后兼容）
   * @param worldBuilding 世界观对象
   * @returns 格式化后的世界观信息字符串
   */
  formatWorldBuildingFullInfoSync(worldBuilding: WorldBuilding): string {
    let info = `世界观元素: ${worldBuilding.name}\n`;

    // 添加类别
    const category = worldBuilding.category || '未知';
    info += `类别: ${category}\n`;

    // 添加主类别
    const mainCategory = this.getCategoryMainLabel(category);
    if (mainCategory) {
      info += `主类别: ${mainCategory}\n`;
    }

    // 添加描述
    if (worldBuilding.description) {
      info += `描述: ${worldBuilding.description}\n`;
    } else {
      info += `描述: 无描述\n`;
    }

    // 添加类别特有字段
    info += this.formatCategorySpecificFields(worldBuilding);

    // 添加通用属性
    const commonFields = ['重要性', '时间跨度', '影响范围', '起源', '规则', '象征意义', '冲突'];
    let hasCommonFields = false;

    if (worldBuilding.attributes) {
      for (const field of commonFields) {
        if (worldBuilding.attributes[field]) {
          if (!hasCommonFields) {
            info += `\n通用属性:\n`;
            hasCommonFields = true;
          }
          info += `- ${field}: ${worldBuilding.attributes[field]}\n`;
        }
      }
    }

    // 添加关联信息
    if (worldBuilding.relatedCharacterIds && worldBuilding.relatedCharacterIds.length > 0) {
      info += `\n关联人物: ${worldBuilding.relatedCharacterIds.length}个\n`;
    }

    if (worldBuilding.relatedWorldBuildingIds && worldBuilding.relatedWorldBuildingIds.length > 0) {
      info += `关联世界观: ${worldBuilding.relatedWorldBuildingIds.length}个\n`;
    }

    if (worldBuilding.relatedTerminologyIds && worldBuilding.relatedTerminologyIds.length > 0) {
      info += `关联术语: ${worldBuilding.relatedTerminologyIds.length}个\n`;
    }

    // 添加提取自哪些章节
    if (worldBuilding.extractedFromChapterIds && worldBuilding.extractedFromChapterIds.length > 0) {
      info += `\n提取自章节: ${worldBuilding.extractedFromChapterIds.length}个\n`;
      // 注意：同步版本无法获取章节名称，只显示数量
    }

    return info;
  }

  /**
   * 格式化类别特有字段
   * @param worldBuilding 世界观对象
   * @returns 格式化后的类别特有字段字符串
   */
  private formatCategorySpecificFields(worldBuilding: WorldBuilding): string {
    const category = worldBuilding.category || '';
    const attributes = worldBuilding.attributes || {};
    let info = '';

    // 根据不同类别添加特有字段
    switch (category) {
      case 'geography':
        if (attributes['significance'] || attributes['inhabitants']) {
          info += `\n地理特有属性:\n`;
          if (attributes['significance']) {
            info += `- 在故事中的意义: ${attributes['significance']}\n`;
          }
          if (attributes['inhabitants']) {
            info += `- 居民/生物: ${attributes['inhabitants']}\n`;
          }
        }
        break;

      case 'history':
        if (attributes['event'] || attributes['timeframe'] || attributes['keyFigures'] || attributes['impact']) {
          info += `\n历史特有属性:\n`;
          if (attributes['event']) {
            info += `- 事件描述: ${attributes['event']}\n`;
          }
          if (attributes['timeframe']) {
            info += `- 时间框架: ${attributes['timeframe']}\n`;
          }
          if (attributes['keyFigures']) {
            info += `- 关键人物: ${attributes['keyFigures']}\n`;
          }
          if (attributes['impact']) {
            info += `- 对当前世界的影响: ${attributes['impact']}\n`;
          }
        }
        break;

      case 'culture':
        if (attributes['overview'] || attributes['customs'] || attributes['beliefs'] || attributes['arts']) {
          info += `\n文化特有属性:\n`;
          if (attributes['overview']) {
            info += `- 文化概述: ${attributes['overview']}\n`;
          }
          if (attributes['customs']) {
            info += `- 重要习俗: ${attributes['customs']}\n`;
          }
          if (attributes['beliefs']) {
            info += `- 信仰与禁忌: ${attributes['beliefs']}\n`;
          }
          if (attributes['arts']) {
            info += `- 艺术与表达: ${attributes['arts']}\n`;
          }
        }
        break;

      case 'race':
        if (attributes['characteristics'] || attributes['society'] || attributes['relations'] || attributes['specialAbilities']) {
          info += `\n种族特有属性:\n`;
          if (attributes['characteristics']) {
            info += `- 种族特征: ${attributes['characteristics']}\n`;
          }
          if (attributes['society']) {
            info += `- 社会结构: ${attributes['society']}\n`;
          }
          if (attributes['relations']) {
            info += `- 与其他种族的关系: ${attributes['relations']}\n`;
          }
          if (attributes['specialAbilities']) {
            info += `- 特殊能力: ${attributes['specialAbilities']}\n`;
          }
        }
        break;

      case 'magic':
        if (attributes['principles'] || attributes['limitations'] || attributes['practitioners'] || attributes['commonSpells']) {
          info += `\n魔法系统特有属性:\n`;
          if (attributes['principles']) {
            info += `- 魔法原理: ${attributes['principles']}\n`;
          }
          if (attributes['limitations']) {
            info += `- 限制与代价: ${attributes['limitations']}\n`;
          }
          if (attributes['practitioners']) {
            info += `- 习得方式: ${attributes['practitioners']}\n`;
          }
          if (attributes['commonSpells']) {
            info += `- 常见法术: ${attributes['commonSpells']}\n`;
          }
        }
        break;

      case 'organization':
        if (attributes['purpose'] || attributes['structure'] || attributes['keyMembers'] || attributes['resources']) {
          info += `\n组织特有属性:\n`;
          if (attributes['purpose']) {
            info += `- 组织目的: ${attributes['purpose']}\n`;
          }
          if (attributes['structure']) {
            info += `- 组织结构: ${attributes['structure']}\n`;
          }
          if (attributes['keyMembers']) {
            info += `- 关键成员: ${attributes['keyMembers']}\n`;
          }
          if (attributes['resources']) {
            info += `- 资源与影响力: ${attributes['resources']}\n`;
          }
        }
        break;

      case 'item':
        if (attributes['origin'] || attributes['powers'] || attributes['significance']) {
          info += `\n物品特有属性:\n`;
          if (attributes['origin']) {
            info += `- 来源/历史: ${attributes['origin']}\n`;
          }
          if (attributes['powers']) {
            info += `- 能力/功能: ${attributes['powers']}\n`;
          }
          if (attributes['significance']) {
            info += `- 在故事中的意义: ${attributes['significance']}\n`;
          }
        }
        break;

      case 'religion':
        if (attributes['beliefs'] || attributes['practices'] || attributes['organization'] || attributes['influence']) {
          info += `\n宗教特有属性:\n`;
          if (attributes['beliefs']) {
            info += `- 核心信仰: ${attributes['beliefs']}\n`;
          }
          if (attributes['practices']) {
            info += `- 仪式与实践: ${attributes['practices']}\n`;
          }
          if (attributes['organization']) {
            info += `- 宗教组织: ${attributes['organization']}\n`;
          }
          if (attributes['influence']) {
            info += `- 社会影响: ${attributes['influence']}\n`;
          }
        }
        break;

      case 'politics':
        if (attributes['system'] || attributes['keyFigures'] || attributes['factions'] || attributes['conflicts']) {
          info += `\n政治特有属性:\n`;
          if (attributes['system']) {
            info += `- 政治体系: ${attributes['system']}\n`;
          }
          if (attributes['keyFigures']) {
            info += `- 关键人物: ${attributes['keyFigures']}\n`;
          }
          if (attributes['factions']) {
            info += `- 派系与利益集团: ${attributes['factions']}\n`;
          }
          if (attributes['conflicts']) {
            info += `- 当前冲突: ${attributes['conflicts']}\n`;
          }
        }
        break;

      default:
        if (attributes['mainFeatures'] || attributes['relevance'] || attributes['uniqueAspects']) {
          info += `\n特有属性:\n`;
          if (attributes['mainFeatures']) {
            info += `- 主要特征: ${attributes['mainFeatures']}\n`;
          }
          if (attributes['relevance']) {
            info += `- 与故事的关联: ${attributes['relevance']}\n`;
          }
          if (attributes['uniqueAspects']) {
            info += `- 独特之处: ${attributes['uniqueAspects']}\n`;
          }
        }
        break;
    }

    return info;
  }

  /**
   * 获取类别的主类别标签
   * @param categoryId 类别ID
   * @returns 主类别标签
   */
  private getCategoryMainLabel(categoryId: string): string | null {
    const categoryMap: Record<string, string> = {
      'geography': '物理世界',
      'natural_phenomena': '物理世界',
      'architecture': '物理世界',
      'politics': '社会结构',
      'economy': '社会结构',
      'organization': '社会结构',
      'culture': '文化体系',
      'religion': '文化体系',
      'language': '文化体系',
      'art': '文化体系',
      'race': '生命体系',
      'magic': '知识体系',
      'technology': '知识体系',
      'history': '时间体系',
      'item': '物品体系',
      'other': '其他'
    };

    return categoryMap[categoryId] || null;
  }

  /**
   * 获取关联世界观元素的完整信息
   * @param relatedElements 关联世界观元素的信息数组，格式为 "id:name"
   * @param bookId 书籍ID
   * @returns 关联世界观元素的完整信息
   */
  async getRelatedWorldBuildingsInfo(relatedElements: string[], bookId?: string): Promise<{name: string, info: string}[]> {
    const result: {name: string, info: string}[] = [];

    if (relatedElements.length === 0) {
      return result;
    }

    try {
      // 导入 worldBuildingRepository
      const { worldBuildingRepository } = await import('@/lib/db/repositories');

      // 解析关联元素信息
      const parsedElements = relatedElements.map(element => {
        // 检查是否是 "id:name" 格式
        const parts = element.split(':');
        if (parts.length === 2) {
          return { id: parts[0], name: parts[1] };
        }
        // 如果不是，则假设是名称
        return { id: '', name: element };
      });

      console.log('解析后的关联元素:', parsedElements);

      // 如果提供了bookId，则只查询该书籍的世界观元素
      if (bookId) {
        const worldBuildings = await worldBuildingRepository.getAllByBookId(bookId);
        console.log(`获取到书籍 ${bookId} 的世界观元素:`, worldBuildings.length);

        // 遍历关联世界观元素
        for (const element of parsedElements) {
          // 首先尝试通过ID查找
          let relatedWorldBuilding = element.id ?
            worldBuildings.find(wb => wb.id === element.id) : null;

          // 如果通过ID没有找到，则尝试通过名称查找
          if (!relatedWorldBuilding) {
            relatedWorldBuilding = worldBuildings.find(wb => wb.name === element.name);
          }

          if (relatedWorldBuilding) {
            console.log(`找到世界观元素: ${relatedWorldBuilding.name} (ID: ${relatedWorldBuilding.id})`);
            // 使用格式化器生成完整的世界观元素信息
            const worldBuildingInfo = await this.formatWorldBuildingFullInfo(relatedWorldBuilding);
            result.push({
              name: relatedWorldBuilding.name,
              info: worldBuildingInfo
            });
          } else {
            console.log(`未找到世界观元素: ${element.name} (ID: ${element.id})`);
            // 如果没有找到详细信息，只添加名称
            result.push({
              name: element.name,
              info: `世界观元素: ${element.name}\n类别: 未知\n描述: 无可用信息`
            });
          }
        }
      } else {
        // 如果没有提供bookId，则查询所有书籍的世界观元素
        const { db } = await import('@/lib/db/dexie');
        const books = await db.books.toArray();
        console.log('获取到所有书籍:', books.length);

        // 遍历关联世界观元素
        for (const element of parsedElements) {
          let found = false;

          // 首先尝试通过ID直接查找（如果有ID）
          if (element.id) {
            try {
              const worldBuilding = await worldBuildingRepository.getById(element.id);
              if (worldBuilding) {
                console.log(`通过ID直接找到世界观元素: ${worldBuilding.name} (ID: ${worldBuilding.id})`);
                const worldBuildingInfo = await this.formatWorldBuildingFullInfo(worldBuilding);
                result.push({
                  name: worldBuilding.name,
                  info: worldBuildingInfo
                });
                found = true;
                continue; // 跳过后续的书籍遍历
              }
            } catch (error) {
              console.error(`通过ID ${element.id} 查找世界观元素失败:`, error);
            }
          }

          // 如果通过ID没有找到，则遍历所有书籍
          for (const book of books) {
            if (found) break;

            const worldBuildings = await worldBuildingRepository.getAllByBookId(book.id!);
            console.log(`获取到书籍 ${book.id} 的世界观元素:`, worldBuildings.length);

            // 首先尝试通过ID查找
            let relatedWorldBuilding = element.id ?
              worldBuildings.find(wb => wb.id === element.id) : null;

            // 如果通过ID没有找到，则尝试通过名称查找
            if (!relatedWorldBuilding) {
              relatedWorldBuilding = worldBuildings.find(wb => wb.name === element.name);
            }

            if (relatedWorldBuilding) {
              console.log(`在书籍 ${book.id} 中找到世界观元素: ${relatedWorldBuilding.name} (ID: ${relatedWorldBuilding.id})`);
              // 使用格式化器生成完整的世界观元素信息
              const worldBuildingInfo = await this.formatWorldBuildingFullInfo(relatedWorldBuilding);
              result.push({
                name: relatedWorldBuilding.name,
                info: worldBuildingInfo
              });
              found = true;
            }
          }

          // 如果在所有书籍中都没有找到，只添加名称
          if (!found) {
            console.log(`在所有书籍中都未找到世界观元素: ${element.name} (ID: ${element.id})`);
            result.push({
              name: element.name,
              info: `世界观元素: ${element.name}\n类别: 未知\n描述: 无可用信息`
            });
          }
        }
      }
    } catch (error) {
      console.error('获取关联世界观元素信息失败:', error);

      // 如果出错，只添加名称
      for (const element of relatedElements) {
        // 提取名称（如果是 "id:name" 格式）
        const name = element.includes(':') ? element.split(':')[1] : element;
        result.push({
          name,
          info: `世界观元素: ${name}`
        });
      }
    }

    return result;
  }
}
