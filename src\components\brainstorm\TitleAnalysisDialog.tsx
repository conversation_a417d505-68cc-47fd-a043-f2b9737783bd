"use client";

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { KeywordElement, TitleFramework } from '@/factories/ai/services/brainstorm';

// 网文类别枚举
export enum NovelCategory {
  FANTASY = 'fantasy',      // 玄幻
  URBAN = 'urban',          // 都市
  HISTORY = 'history',      // 历史
  SCIFI = 'scifi',          // 科幻
  XIANXIA = 'xianxia',      // 仙侠
  MILITARY = 'military',    // 军事
  MYSTERY = 'mystery',      // 悬疑
  ROMANCE = 'romance',      // 言情
  GAME = 'game',            // 游戏
  SPORTS = 'sports',        // 体育
  ANIME = 'anime',          // 二次元
  OTHER = 'other'           // 其他
}

// 类别配置
export const NOVEL_CATEGORIES = {
  [NovelCategory.FANTASY]: {
    name: '玄幻',
    color: '#8B5CF6',
    bgColor: '#F3E8FF',
    keywords: ['修仙', '法宝', '境界', '天才', '废材', '逆天', '神通', '仙界']
  },
  [NovelCategory.URBAN]: {
    name: '都市',
    color: '#3B82F6',
    bgColor: '#DBEAFE',
    keywords: ['都市', '总裁', '豪门', '校园', '职场', '商战', '医生', '律师']
  },
  [NovelCategory.HISTORY]: {
    name: '历史',
    color: '#D97706',
    bgColor: '#FEF3C7',
    keywords: ['穿越', '古代', '皇帝', '朝廷', '战争', '谋士', '将军', '王朝']
  },
  [NovelCategory.SCIFI]: {
    name: '科幻',
    color: '#059669',
    bgColor: '#D1FAE5',
    keywords: ['未来', '科技', '机甲', '星际', '外星', '基因', '虚拟', '时空']
  },
  [NovelCategory.XIANXIA]: {
    name: '仙侠',
    color: '#DC2626',
    bgColor: '#FEE2E2',
    keywords: ['仙侠', '修真', '飞升', '渡劫', '元婴', '金丹', '筑基', '炼气']
  },
  [NovelCategory.MILITARY]: {
    name: '军事',
    color: '#7C2D12',
    bgColor: '#FED7AA',
    keywords: ['军事', '战争', '特种兵', '军官', '战场', '武器', '战略', '部队']
  },
  [NovelCategory.MYSTERY]: {
    name: '悬疑',
    color: '#374151',
    bgColor: '#F3F4F6',
    keywords: ['悬疑', '推理', '犯罪', '侦探', '谋杀', '真相', '线索', '破案']
  },
  [NovelCategory.ROMANCE]: {
    name: '言情',
    color: '#EC4899',
    bgColor: '#FCE7F3',
    keywords: ['言情', '恋爱', '霸道', '甜宠', '虐恋', '婚姻', '情感', '浪漫']
  },
  [NovelCategory.GAME]: {
    name: '游戏',
    color: '#7C3AED',
    bgColor: '#EDE9FE',
    keywords: ['游戏', '网游', '竞技', '电竞', '副本', '装备', '技能', '公会']
  },
  [NovelCategory.SPORTS]: {
    name: '体育',
    color: '#0891B2',
    bgColor: '#CFFAFE',
    keywords: ['体育', '足球', '篮球', '运动', '比赛', '冠军', '训练', '球员']
  },
  [NovelCategory.ANIME]: {
    name: '二次元',
    color: '#BE185D',
    bgColor: '#FCE7F3',
    keywords: ['二次元', '动漫', '萝莉', '御姐', '后宫', '魔法', '学园', '异世界']
  },
  [NovelCategory.OTHER]: {
    name: '其他',
    color: '#6B7280',
    bgColor: '#F9FAFB',
    keywords: []
  }
};

// 扩展的关键词元素接口
interface EnhancedKeywordElement extends KeywordElement {
  category?: NovelCategory;
}

interface AnalysisResult {
  title: string;
  extractedKeywords: string[];
  keywords: Array<{
    text: string;
    category: NovelCategory;
  }>;
  detectedFramework: string;
  frameworkPattern: string;
  bookCategory: NovelCategory;
  confidence: number;
}

interface TitleAnalysisDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAnalysisComplete: (keywords: KeywordElement[], frameworks: TitleFramework[]) => void;
  existingKeywords: KeywordElement[];
  existingFrameworks: TitleFramework[];
}

/**
 * 书名拆解分析弹窗
 * 用于分析现有书名，提取关键词和框架模式
 */
const TitleAnalysisDialog: React.FC<TitleAnalysisDialogProps> = ({
  isOpen,
  onClose,
  onAnalysisComplete,
  existingKeywords,
  existingFrameworks
}) => {
  const [inputTitles, setInputTitles] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [selectedResults, setSelectedResults] = useState<Set<number>>(new Set());

  // 分析书名 - 使用真实AI服务
  const analyzeTitles = useCallback(async () => {
    if (!inputTitles.trim()) return;

    setIsAnalyzing(true);
    try {
      const titles = inputTitles.split('\n').filter(t => t.trim());

      // 构建AI分析请求
      const analysisPrompt = `你是一位专业的网文书名分析专家，请分析以下网文书名，提取其中的关键词和框架模式，并识别网文类别。

网文类别包括：
- fantasy（玄幻）：修仙、法宝、境界、天才、废材、逆天、神通、仙界
- urban（都市）：都市、总裁、豪门、校园、职场、商战、医生、律师
- history（历史）：穿越、古代、皇帝、朝廷、战争、谋士、将军、王朝
- scifi（科幻）：未来、科技、机甲、星际、外星、基因、虚拟、时空
- xianxia（仙侠）：仙侠、修真、飞升、渡劫、元婴、金丹、筑基、炼气
- military（军事）：军事、战争、特种兵、军官、战场、武器、战略、部队
- mystery（悬疑）：悬疑、推理、犯罪、侦探、谋杀、真相、线索、破案
- romance（言情）：言情、恋爱、霸道、甜宠、虐恋、婚姻、情感、浪漫
- game（游戏）：游戏、网游、竞技、电竞、副本、装备、技能、公会
- sports（体育）：体育、足球、篮球、运动、比赛、冠军、训练、球员
- anime（二次元）：二次元、动漫、萝莉、御姐、后宫、魔法、学园、异世界
- other（其他）：不属于以上任何类别

书名列表：
${titles.map((title, index) => `${index + 1}. ${title}`).join('\n')}

请对每个书名进行分析，提取：
1. 关键词列表（提取书名中的核心词汇，如"开局"、"拐跑"、"急眼了"等，并为每个关键词标注所属网文类别）
2. 框架模式（书名的结构模式，用{}表示变量，如"开局{动作}，{反应}"）
3. 框架名称（给这种模式起个简洁的名字，如"开局反应模式"）
4. 书名类别（判断整个书名所属的主要网文类别）
5. 置信度（0-1之间的数值，表示分析的准确性）

请严格按照以下JSON格式返回，不要添加任何其他内容：
{
  "analysis": [
    {
      "title": "书名",
      "keywords": [
        {
          "text": "关键词",
          "category": "网文类别代码"
        }
      ],
      "frameworkPattern": "框架模式",
      "frameworkName": "框架名称",
      "bookCategory": "网文类别代码",
      "confidence": 0.9
    }
  ]
}`;

      // 调用统一AI服务
      const { AIServiceFactory, AIServiceType } = await import('@/services/ai/BaseAIService');
      const aiService = AIServiceFactory.getService(AIServiceType.TOOL_CALL);

      console.log('🔍 开始AI书名分析...');

      const response = await aiService.callAI([
        { role: 'user', content: analysisPrompt }
      ], {
        streaming: false // 分析不需要流式输出
      });

      if (!response.success || !response.text) {
        throw new Error(response.error || 'AI分析失败');
      }

      console.log('📝 AI分析响应:', response.text);

      // 解析AI响应
      let analysisData;
      try {
        // 尝试直接解析JSON
        analysisData = JSON.parse(response.text);
      } catch (parseError) {
        // 如果直接解析失败，尝试提取JSON部分
        const jsonMatch = response.text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('无法解析AI响应为JSON格式');
        }
      }

      if (!analysisData.analysis || !Array.isArray(analysisData.analysis)) {
        throw new Error('AI响应格式不正确');
      }

      const results: AnalysisResult[] = analysisData.analysis.map((item: any) => {
        // 处理关键词数据
        let extractedKeywords: string[] = [];
        let keywords: Array<{ text: string; category: NovelCategory }> = [];

        if (Array.isArray(item.keywords)) {
          if (item.keywords.length > 0 && typeof item.keywords[0] === 'object') {
            // 新格式：包含类别信息的关键词
            keywords = item.keywords.map((kw: any) => ({
              text: kw.text || '',
              category: kw.category || NovelCategory.OTHER
            }));
            extractedKeywords = keywords.map(kw => kw.text);
          } else {
            // 旧格式：纯字符串关键词
            extractedKeywords = item.keywords;
            keywords = extractedKeywords.map(text => ({
              text,
              category: NovelCategory.OTHER
            }));
          }
        }

        return {
          title: item.title || '',
          extractedKeywords,
          keywords,
          detectedFramework: item.frameworkName || '未知模式',
          frameworkPattern: item.frameworkPattern || '{未知模式}',
          bookCategory: item.bookCategory || NovelCategory.OTHER,
          confidence: typeof item.confidence === 'number' ? item.confidence : 0.5
        };
      });

      console.log('✅ 分析完成，共解析', results.length, '个书名');

      setAnalysisResults(results);
      // 默认选中所有结果
      setSelectedResults(new Set(results.map((_, index) => index)));
    } catch (error) {
      console.error('❌ 分析失败:', error);
      // 显示用户友好的错误信息
      alert(`分析失败：${error instanceof Error ? error.message : '未知错误'}\n\n请检查：\n1. 网络连接是否正常\n2. API密钥是否配置正确\n3. 输入的书名格式是否正确`);
    } finally {
      setIsAnalyzing(false);
    }
  }, [inputTitles]);

  // 从模式中提取变量
  const extractVariablesFromPattern = useCallback((pattern: string): string[] => {
    const variables: string[] = [];
    const matches = pattern.match(/\{([^}]+)\}/g);
    if (matches) {
      matches.forEach(match => {
        const variable = match.slice(1, -1); // 移除大括号
        if (!variables.includes(variable)) {
          variables.push(variable);
        }
      });
    }
    return variables;
  }, []);

  // 切换结果选择
  const toggleResultSelection = useCallback((index: number) => {
    setSelectedResults(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  // 确认并保存分析结果 - 重点是合并相同的
  const confirmAnalysis = useCallback(() => {
    const selectedAnalysis = analysisResults.filter((_, index) => selectedResults.has(index));

    // 合并相同的关键词 - 支持类别信息
    const newKeywords: KeywordElement[] = [];
    const keywordMap: Map<string, { frequency: number; categories: Set<NovelCategory> }> = new Map();

    selectedAnalysis.forEach(result => {
      // 优先使用带类别的关键词数据
      if (result.keywords?.length > 0) {
        result.keywords.forEach(keyword => {
          const existing = keywordMap.get(keyword.text) || { frequency: 0, categories: new Set() };
          existing.frequency += 1;
          existing.categories.add(keyword.category);
          keywordMap.set(keyword.text, existing);
        });
      } else {
        // 兼容旧格式
        result.extractedKeywords.forEach(keyword => {
          const existing = keywordMap.get(keyword) || { frequency: 0, categories: new Set() };
          existing.frequency += 1;
          existing.categories.add(NovelCategory.OTHER);
          keywordMap.set(keyword, existing);
        });
      }
    });

    // 处理关键词：相同的合并，不同的创建
    keywordMap.forEach((data, text) => {
      const existingKeyword = existingKeywords.find(k => k.text === text);
      if (existingKeyword) {
        // 相同的关键词：合并频次
        existingKeyword.frequency += data.frequency;
        existingKeyword.lastUsedAt = new Date();
        // 如果是分析提取的，添加标签
        if (!existingKeyword.tags) {
          existingKeyword.tags = [];
        }
        if (!existingKeyword.tags.includes('分析提取')) {
          existingKeyword.tags.push('分析提取');
        }
        // 添加类别标签
        data.categories.forEach(category => {
          const categoryName = NOVEL_CATEGORIES[category]?.name;
          if (categoryName && !existingKeyword.tags!.includes(categoryName)) {
            existingKeyword.tags!.push(categoryName);
          }
        });
      } else {
        // 不同的关键词：创建新的
        const categoryTags = Array.from(data.categories).map(cat => NOVEL_CATEGORIES[cat]?.name).filter(Boolean);
        newKeywords.push({
          id: `analyzed_${Date.now()}_${Math.random()}`,
          text,
          frequency: data.frequency,
          hotness: Math.min(10, 5 + data.frequency), // 根据频次计算热度
          tags: ['分析提取', ...categoryTags],
          createdAt: new Date(),
          lastUsedAt: new Date()
        });
      }
    });

    // 合并相同的框架 - 支持参考书名系统
    const newFrameworks: TitleFramework[] = [];
    const frameworkMap: Map<string, {
      pattern: string;
      examples: string[];
      referenceBooks: Array<{
        title: string;
        source: 'analysis' | 'collection';
        category: NovelCategory;
        addedAt: Date;
        confidence?: number;
      }>;
      count: number;
      categories: Set<NovelCategory>;
    }> = new Map();

    selectedAnalysis.forEach(result => {
      const key = result.detectedFramework;
      if (!frameworkMap.has(key)) {
        frameworkMap.set(key, {
          pattern: result.frameworkPattern,
          examples: [],
          referenceBooks: [],
          count: 0,
          categories: new Set()
        });
      }
      const framework = frameworkMap.get(key)!;
      framework.examples.push(result.title);
      framework.referenceBooks.push({
        title: result.title,
        source: 'analysis',
        category: result.bookCategory,
        addedAt: new Date(),
        confidence: result.confidence
      });
      framework.categories.add(result.bookCategory);
      framework.count++;
    });

    // 处理框架：相同的合并，不同的创建 - 支持参考书名
    frameworkMap.forEach((data, name) => {
      const existingFramework = existingFrameworks.find(f => f.name === name);
      if (existingFramework) {
        // 相同的框架：合并使用次数和例子
        existingFramework.usageCount += data.count;
        existingFramework.lastUsedAt = new Date();

        // 合并例子（去重）
        const newExamples = data.examples.filter(example =>
          !existingFramework.examples.includes(example)
        );
        existingFramework.examples.push(...newExamples.slice(0, 5 - existingFramework.examples.length));

        // 合并参考书名（扩展现有框架的参考书名）
        if (!(existingFramework as any).referenceBooks) {
          (existingFramework as any).referenceBooks = [];
        }
        const existingTitles = new Set((existingFramework as any).referenceBooks?.map((book: any) => book.title) || []);
        const newReferenceBooks = data.referenceBooks.filter(book => !existingTitles.has(book.title));
        (existingFramework as any).referenceBooks.push(...newReferenceBooks);

        // 更新类别分布统计
        if (!(existingFramework as any).categoryDistribution) {
          (existingFramework as any).categoryDistribution = {};
        }
        data.categories.forEach(category => {
          const current = (existingFramework as any).categoryDistribution[category] || 0;
          (existingFramework as any).categoryDistribution[category] = current + 1;
        });

        // 更新描述，包含更多书名信息
        const totalExamples = existingFramework.examples.length;
        const totalReferences = (existingFramework as any).referenceBooks?.length || 0;
        existingFramework.description = `从${existingFramework.usageCount}个书名中分析提取的框架模式，包含${totalExamples}个例子和${totalReferences}个参考书名`;
      } else if (data.count >= 1) {
        // 不同的框架：创建新的
        const categoryDistribution: Partial<Record<NovelCategory, number>> = {};
        data.categories.forEach(category => {
          categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
        });

        const newFramework = {
          id: `analyzed_framework_${Date.now()}_${Math.random()}`,
          name,
          pattern: data.pattern,
          description: `从${data.count}个书名中分析提取的框架模式，参考案例：${data.examples.slice(0, 2).join('、')}${data.examples.length > 2 ? '等' : ''}`,
          examples: data.examples.slice(0, 5), // 保存最多5个例子
          variables: extractVariablesFromPattern(data.pattern), // 从模式中提取变量
          usageCount: data.count,
          effectiveness: Math.min(10, 5 + data.count), // 根据出现次数计算效果评分
          createdAt: new Date(),
          lastUsedAt: new Date(),
          // 扩展字段
          referenceBooks: data.referenceBooks,
          categoryDistribution
        } as TitleFramework & {
          referenceBooks: Array<{
            title: string;
            source: 'analysis' | 'collection';
            category: NovelCategory;
            addedAt: Date;
            confidence?: number;
          }>;
          categoryDistribution: Record<NovelCategory, number>;
        };

        newFrameworks.push(newFramework);
      }
    });

    console.log('🔍 TitleAnalysisDialog confirmAnalysis 完成:', {
      newKeywords: newKeywords.length,
      newFrameworks: newFrameworks.length,
      newFrameworksData: newFrameworks.map(f => ({ id: f.id, name: f.name, pattern: f.pattern }))
    });

    onAnalysisComplete(newKeywords, newFrameworks);
    onClose();
  }, [analysisResults, selectedResults, existingKeywords, existingFrameworks, onAnalysisComplete, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="bg-white rounded-xl shadow-2xl w-full max-w-4xl h-[80vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">🔍 关键词框架拆解</h2>
              <p className="text-sm text-gray-500 mt-1">分析现有书名，提取关键词和框架模式</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <span className="text-gray-400 text-xl">×</span>
            </button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 p-6 overflow-hidden">
            <div className="h-full flex gap-6">
              {/* 左侧：输入区域 */}
              <div className="w-1/2 flex flex-col">
                <h3 className="text-sm font-medium text-gray-700 mb-3">输入书名（每行一个）</h3>
                <textarea
                  value={inputTitles}
                  onChange={(e) => setInputTitles(e.target.value)}
                  placeholder="开局拐跑家族帝兵，老祖急眼了&#10;长生皇子：开局无敌，看王朝起落&#10;蜀山：挂机百年，出世已是剑仙&#10;洪荒：刚成尸祖，你让我骂醒女娲"
                  className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <motion.button
                  onClick={analyzeTitles}
                  disabled={isAnalyzing || !inputTitles.trim()}
                  className="mt-4 py-3 bg-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: isAnalyzing ? 1 : 1.02 }}
                  whileTap={{ scale: isAnalyzing ? 1 : 0.98 }}
                >
                  {isAnalyzing ? (
                    <div className="flex items-center justify-center space-x-2">
                      <motion.div
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      <span>分析中...</span>
                    </div>
                  ) : (
                    '🔍 开始分析'
                  )}
                </motion.button>
              </div>

              {/* 右侧：分析结果 */}
              <div className="w-1/2 flex flex-col">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-700">分析结果</h3>
                  {analysisResults.length > 0 && (
                    <span className="text-xs text-gray-500">
                      已选择 {selectedResults.size}/{analysisResults.length} 项
                    </span>
                  )}
                </div>
                
                <div className="flex-1 overflow-y-auto space-y-3">
                  {analysisResults.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-400">
                      <div className="text-center">
                        <div className="text-4xl mb-2">📝</div>
                        <div>输入书名并点击分析</div>
                      </div>
                    </div>
                  ) : (
                    analysisResults.map((result, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          selectedResults.has(index)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => toggleResultSelection(index)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="font-medium text-sm text-gray-800 flex-1">
                            {result.title}
                          </div>
                          <div className="ml-2">
                            <input
                              type="checkbox"
                              checked={selectedResults.has(index)}
                              onChange={() => toggleResultSelection(index)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-1 text-xs">
                          {/* 书名类别 */}
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500">类别：</span>
                            <span
                              className="px-2 py-0.5 rounded-full text-xs font-medium"
                              style={{
                                backgroundColor: NOVEL_CATEGORIES[result.bookCategory]?.bgColor || '#F9FAFB',
                                color: NOVEL_CATEGORIES[result.bookCategory]?.color || '#6B7280'
                              }}
                            >
                              {NOVEL_CATEGORIES[result.bookCategory]?.name || '其他'}
                            </span>
                            <span className="text-gray-400">({Math.round(result.confidence * 100)}%)</span>
                          </div>

                          <div>
                            <span className="text-gray-500">框架：</span>
                            <span className="text-purple-600">{result.detectedFramework}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">模式：</span>
                            <span className="text-gray-700">{result.frameworkPattern}</span>
                          </div>

                          {/* 关键词显示 - 优先显示带类别的关键词 */}
                          {(result.keywords?.length > 0 || result.extractedKeywords.length > 0) && (
                            <div>
                              <span className="text-gray-500">关键词：</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {result.keywords?.length > 0 ? (
                                  // 显示带类别的关键词
                                  result.keywords.map((keyword, i) => (
                                    <span
                                      key={i}
                                      className="px-1.5 py-0.5 rounded text-xs font-medium"
                                      style={{
                                        backgroundColor: NOVEL_CATEGORIES[keyword.category]?.bgColor || '#F3F4F6',
                                        color: NOVEL_CATEGORIES[keyword.category]?.color || '#374151'
                                      }}
                                      title={`${keyword.text} - ${NOVEL_CATEGORIES[keyword.category]?.name || '其他'}`}
                                    >
                                      {keyword.text}
                                    </span>
                                  ))
                                ) : (
                                  // 兼容旧格式
                                  result.extractedKeywords.map((keyword, i) => (
                                    <span
                                      key={i}
                                      className="px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded text-xs"
                                    >
                                      {keyword}
                                    </span>
                                  ))
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {analysisResults.length > 0 && (
                <>将提取 {selectedResults.size} 个分析结果到关键词和框架库</>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                取消
              </button>
              <motion.button
                onClick={confirmAnalysis}
                disabled={selectedResults.size === 0}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: selectedResults.size === 0 ? 1 : 1.02 }}
                whileTap={{ scale: selectedResults.size === 0 ? 1 : 0.98 }}
              >
                确认录入
              </motion.button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TitleAnalysisDialog;
