$dirs = @(
    "src\factories\ai\interfaces",
    "src\factories\ai\components",
    "src\factories\editor\interfaces",
    "src\factories\editor\components",
    "src\factories\character\interfaces",
    "src\factories\character\components",
    "src\factories\terminology\interfaces",
    "src\factories\terminology\components",
    "src\factories\worldbuilding\interfaces",
    "src\factories\worldbuilding\components",
    "src\adapters\ai",
    "src\adapters\editor",
    "src\adapters\character",
    "src\adapters\terminology",
    "src\adapters\worldbuilding",
    "src\lib\utils",
    "src\lib\hooks",
    "src\lib\db",
    "src\fonts"
)

foreach ($dir in $dirs) {
    New-Item -ItemType Directory -Path $dir -Force
    Write-Host "Created directory: $dir"
}
