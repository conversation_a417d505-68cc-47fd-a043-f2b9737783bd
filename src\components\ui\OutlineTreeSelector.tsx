"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from 'react';
import ViewSwitcher, { OutlineViewType } from '@/factories/ui/components/OutlineManager/ViewSwitcher';
import OutlineCanvas from '@/factories/ui/components/OutlineManager/OutlineCanvas';

// CSS动画样式
const styles = `
  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 1000px;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes checkboxPop {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes checkmarkDraw {
    0% {
      stroke-dashoffset: 16;
    }
    100% {
      stroke-dashoffset: 0;
    }
  }

  @keyframes nodeHover {
    0% {
      transform: translateY(0) scale(1);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    100% {
      transform: translateY(-2px) scale(1.01);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .animate-slideDown {
    animation: slideDown 0.3s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s infinite;
  }

  .animate-checkbox-pop {
    animation: checkboxPop 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .animate-checkmark-draw {
    animation: checkmarkDraw 0.3s ease-out forwards;
  }

  .animate-node-hover {
    animation: nodeHover 0.2s ease-out forwards;
  }

  .tree-node-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tree-node-container:hover {
    transform: translateY(-1px);
  }

  .custom-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    cursor: pointer;
  }

  .custom-checkbox input {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
  }

  .custom-checkbox .checkbox-bg {
    width: 100%;
    height: 100%;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    transition: all 0.2s ease;
    position: relative;
  }

  .custom-checkbox input:checked + .checkbox-bg {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .custom-checkbox input:indeterminate + .checkbox-bg {
    background: #f59e0b;
    border-color: #f59e0b;
  }

  .custom-checkbox .checkmark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    stroke: white;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    stroke-dasharray: 16;
    stroke-dashoffset: 16;
    transition: stroke-dashoffset 0.3s ease;
  }

  .custom-checkbox input:checked + .checkbox-bg .checkmark {
    stroke-dashoffset: 0;
  }

  .custom-checkbox input:indeterminate + .checkbox-bg .checkmark {
    stroke-dashoffset: 0;
  }
`;

// 注入样式
if (typeof document !== 'undefined' && !document.getElementById('outline-tree-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'outline-tree-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

// SVG图标组件
const NodeIcon: React.FC<{ type: string; className?: string }> = ({ type, className = "w-5 h-5" }) => {
  switch (type) {
    case 'volume':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      );
    case 'event':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
    case 'chapter':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      );
    case 'plot':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
        </svg>
      );
    case 'dialogue':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      );
    case 'scene':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12l2 2 4-4" />
        </svg>
      );
    case 'note':
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    default:
      return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
  }
};

// 自定义动画checkbox组件
const AnimatedCheckbox: React.FC<{
  checked: boolean;
  indeterminate?: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
}> = ({ checked, indeterminate = false, onChange, className = "" }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation(); // 防止事件冒泡
    onChange(e.target.checked);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止事件冒泡
    onChange(!checked);
  };

  return (
    <div
      className={`custom-checkbox ${className} ${checked ? 'animate-checkbox-pop' : ''}`}
      onClick={handleClick}
    >
      <input
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        ref={(input) => {
          if (input) {
            input.indeterminate = indeterminate && !checked;
          }
        }}
      />
      <div className="checkbox-bg">
        <svg className="checkmark" width="12" height="12" viewBox="0 0 12 12">
          {indeterminate && !checked ? (
            <path d="M3 6h6" stroke="white" strokeWidth="2" strokeLinecap="round" />
          ) : (
            <path
              d="M2.5 6l2 2 5-5"
              className={checked ? 'animate-checkmark-draw' : ''}
            />
          )}
        </svg>
      </div>
    </div>
  );
};

// 展开/折叠图标组件
const ExpandIcon: React.FC<{ expanded: boolean; onClick: () => void }> = ({ expanded, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="mr-2 w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-200 hover:scale-110"
      style={{ transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)' }}
    >
      <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
        <path d="M4.5 2L8.5 6L4.5 10V2Z" />
      </svg>
    </button>
  );
};

// 树状节点接口
interface OutlineTreeNode {
  id: string;
  title: string;
  type: 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue' | 'synopsis';
  description?: string;
  creativeNotes?: string; // AI生成的创作建议，包含台词设计、心理描写、节奏控制等指导

  // 总纲/卷专有字段
  volumeTheme?: string;
  volumeArc?: string;
  chapterCount?: number;
  cycleTemplate?: string;
  targetWordCount?: number;
  rhythmStrategy?: string;
  cyclePhases?: string[];

  // 事件刚专有字段
  eventStart?: string;
  eventEnd?: string;
  eventTrigger?: string;
  eventConsequence?: string;
  eventScope?: string;

  // 章节专有字段
  chapterStyle?: string;
  chapterTechniques?: string[];
  chapterGoals?: string;

  // 剧情节点专有字段
  plotType?: 'conflict' | 'twist' | 'climax' | 'resolution';
  plotPoints?: any[];
  relatedCharacters?: string[];

  // 对话设计专有字段
  dialogueScene?: string;
  participants?: string[];
  dialoguePurpose?: string;
  dialogueContent?: any[];

  // 核心故事梗概专有字段
  synopsisBrainhole?: string;
  synopsisGenre?: string;
  synopsisCoreOutline?: string;
  synopsisStoryDescription?: string;
  synopsisAceReferences?: string;

  children: OutlineTreeNode[];
  parent?: OutlineTreeNode;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  isPartiallySelected: boolean;
}

// 选择状态接口
interface OutlineSelectionState {
  selectedNodeIds: Set<string>;
  expandedNodeIds: Set<string>;
  partiallySelectedNodeIds: Set<string>;
  searchQuery: string;
}

// 主组件Props
interface OutlineTreeSelectorProps {
  outline: any; // 原始大纲数据
  selectedNodeIds: string[];
  onSelectionChange: (nodeIds: string[]) => void;
  showCanvasPreview?: boolean;
}

// 树状节点组件Props
interface TreeNodeProps {
  node: OutlineTreeNode;
  selectionState: OutlineSelectionState;
  onNodeSelect: (nodeId: string, selected: boolean) => void;
  onNodeExpand: (nodeId: string, expanded: boolean) => void;
  onNodeClick?: (node: OutlineTreeNode) => void;
}

/**
 * 树状节点组件
 */
const TreeNode: React.FC<TreeNodeProps> = React.memo(({
  node,
  selectionState,
  onNodeSelect,
  onNodeExpand,
  onNodeClick
}) => {
  const isSelected = selectionState.selectedNodeIds.has(node.id);
  const isPartiallySelected = selectionState.partiallySelectedNodeIds.has(node.id);
  const isExpanded = selectionState.expandedNodeIds.has(node.id);
  const hasChildren = node.children.length > 0;

  // 节点样式 - 现代化卡片设计
  const nodeClasses = `
    flex items-center p-3 rounded-xl cursor-pointer transition-all duration-300
    bg-white border border-gray-100 shadow-sm
    hover:shadow-md hover:border-gray-200 hover:-translate-y-0.5
    ${isSelected ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-blue-100' : ''}
    ${isPartiallySelected ? 'bg-gradient-to-r from-amber-50 to-orange-50 border-orange-200 shadow-orange-100' : ''}
    group
  `;

  return (
    <div className="tree-node-container">
      <div
        className={nodeClasses}
        style={{ marginLeft: `${node.level * 16}px` }}
      >
        {/* 展开/折叠图标 */}
        {hasChildren && (
          <ExpandIcon
            expanded={isExpanded}
            onClick={() => onNodeExpand(node.id, !isExpanded)}
          />
        )}

        {/* 自定义动画复选框 */}
        <div className="mr-3">
          <AnimatedCheckbox
            checked={isSelected}
            indeterminate={isPartiallySelected && !isSelected}
            onChange={(checked) => onNodeSelect(node.id, checked)}
          />
        </div>

        {/* SVG节点图标 */}
        <div className={`mr-3 transition-colors duration-200 ${
          isSelected ? 'text-blue-600' :
          isPartiallySelected ? 'text-orange-500' : 'text-gray-500'
        } group-hover:text-blue-500`}>
          <NodeIcon type={node.type} className="w-5 h-5" />
        </div>

        {/* 节点内容 */}
        <div
          className="flex-1 min-w-0 cursor-pointer"
          onClick={() => onNodeClick?.(node)}
        >
          <div className={`font-medium truncate transition-colors duration-200 ${
            isSelected ? 'text-blue-900' : 'text-gray-900'
          } group-hover:text-blue-800`}>
            {node.title}
          </div>
          {node.description && (
            <div className={`text-xs truncate mt-1 transition-colors duration-200 ${
              isSelected ? 'text-blue-600' : 'text-gray-500'
            } group-hover:text-blue-600`}>
              {node.description}
            </div>
          )}
        </div>

        {/* 现代化节点类型标签 */}
        <span className={`
          px-3 py-1 text-xs font-medium rounded-full transition-all duration-200
          ${node.type === 'volume' ? 'bg-purple-100 text-purple-700 group-hover:bg-purple-200' : ''}
          ${node.type === 'event' ? 'bg-amber-100 text-amber-700 group-hover:bg-amber-200' : ''}
          ${node.type === 'chapter' ? 'bg-blue-100 text-blue-700 group-hover:bg-blue-200' : ''}
          ${node.type === 'plot' ? 'bg-orange-100 text-orange-700 group-hover:bg-orange-200' : ''}
          ${node.type === 'dialogue' ? 'bg-green-100 text-green-700 group-hover:bg-green-200' : ''}
          ${node.type === 'synopsis' ? 'bg-indigo-100 text-indigo-700 group-hover:bg-indigo-200' : ''}
          ${isSelected ? 'shadow-sm' : ''}
        `}>
          {node.type === 'volume' ? '总纲/卷' :
           node.type === 'event' ? '事件刚' :
           node.type === 'chapter' ? '章节' :
           node.type === 'plot' ? '剧情节点' :
           node.type === 'synopsis' ? '核心故事梗概' : '对话设计'}
        </span>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div className="tree-children animate-slideDown">
          {node.children.map(child => (
            <TreeNode
              key={child.id}
              node={child}
              selectionState={selectionState}
              onNodeSelect={onNodeSelect}
              onNodeExpand={onNodeExpand}
              onNodeClick={onNodeClick}
            />
          ))}
        </div>
      )}
    </div>
  );
});

TreeNode.displayName = 'TreeNode';

/**
 * 大纲树状选择器主组件
 */
export const OutlineTreeSelector: React.FC<OutlineTreeSelectorProps> = ({
  outline,
  selectedNodeIds,
  onSelectionChange,
  showCanvasPreview = false
}) => {
  // 视图模式状态
  const [currentView, setCurrentView] = useState<OutlineViewType>('tree');

  // 选中节点信息状态
  const [selectedNodeInfo, setSelectedNodeInfo] = useState<OutlineTreeNode | null>(null);

  // 状态管理
  const [selectionState, setSelectionState] = useState<OutlineSelectionState>({
    selectedNodeIds: new Set(selectedNodeIds),
    expandedNodeIds: new Set(),
    partiallySelectedNodeIds: new Set(),
    searchQuery: ''
  });

  // 节点类型排序优先级
  const getNodeTypePriority = (type: string): number => {
    const priorities = {
      'synopsis': 0,  // 核心故事梗概 - 最高优先级
      'volume': 1,    // 总纲/卷
      'event': 2,     // 事件刚
      'chapter': 3,   // 章节
      'plot': 4,      // 剧情节点
      'dialogue': 5,  // 对话节点
    };
    return priorities[type as keyof typeof priorities] || 999;
  };

  // 将原始大纲数据转换为树状结构
  const treeNodes = useMemo(() => {
    if (!outline?.nodes) return [];

    const convertToTreeNode = (node: any, parent?: OutlineTreeNode, level = 0): OutlineTreeNode => {
      const treeNode: OutlineTreeNode = {
        id: node.id,
        title: node.title,
        type: node.type || 'dialogue',
        description: node.description,
        creativeNotes: node.creativeNotes, // 传递创作建议字段
        // 传递总纲/卷专有字段
        volumeTheme: node.volumeTheme,
        volumeArc: node.volumeArc,
        chapterCount: node.chapterCount,
        cycleTemplate: node.cycleTemplate,
        targetWordCount: node.targetWordCount,
        rhythmStrategy: node.rhythmStrategy,
        cyclePhases: node.cyclePhases,
        // 传递事件刚专有字段
        eventStart: node.eventStart,
        eventEnd: node.eventEnd,
        eventTrigger: node.eventTrigger,
        eventConsequence: node.eventConsequence,
        eventScope: node.eventScope,
        // 传递章节专有字段
        chapterStyle: node.chapterStyle,
        chapterTechniques: node.chapterTechniques,
        chapterGoals: node.chapterGoals,
        // 传递剧情节点专有字段
        plotType: node.plotType,
        plotPoints: node.plotPoints,
        relatedCharacters: node.relatedCharacters,
        // 传递对话设计专有字段
        dialogueScene: node.dialogueScene,
        participants: node.participants,
        dialoguePurpose: node.dialoguePurpose,
        dialogueContent: node.dialogueContent,
        // 传递核心故事梗概专有字段
        synopsisBrainhole: node.synopsisBrainhole,
        synopsisGenre: node.synopsisGenre,
        synopsisCoreOutline: node.synopsisCoreOutline,
        synopsisStoryDescription: node.synopsisStoryDescription,
        synopsisAceReferences: node.synopsisAceReferences,
        children: [],
        parent,
        level,
        isExpanded: level < 2, // 默认展开前两层
        isSelected: false,
        isPartiallySelected: false
      };

      if (node.children && node.children.length > 0) {
        treeNode.children = node.children
          .map((child: any) => convertToTreeNode(child, treeNode, level + 1))
          .sort((a, b) => {
            // 首先按节点类型排序（volume > chapter > plot > dialogue）
            const priorityA = getNodeTypePriority(a.type);
            const priorityB = getNodeTypePriority(b.type);
            if (priorityA !== priorityB) {
              return priorityA - priorityB;
            }
            // 类型相同时按标题排序
            return a.title.localeCompare(b.title);
          });
      }

      return treeNode;
    };

    const nodes = outline.nodes
      .map((node: any) => convertToTreeNode(node))
      .sort((a, b) => {
        // 根节点也按类型排序（volume > chapter > plot > dialogue）
        const priorityA = getNodeTypePriority(a.type);
        const priorityB = getNodeTypePriority(b.type);
        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }
        // 类型相同时按标题排序
        return a.title.localeCompare(b.title);
      });

    // 设置默认展开状态
    const defaultExpandedIds = new Set<string>();
    const collectExpandedIds = (nodes: OutlineTreeNode[]) => {
      nodes.forEach(node => {
        if (node.level < 2 && node.children.length > 0) {
          defaultExpandedIds.add(node.id);
        }
        collectExpandedIds(node.children);
      });
    };
    collectExpandedIds(nodes);

    setSelectionState(prev => ({
      ...prev,
      expandedNodeIds: defaultExpandedIds
    }));

    return nodes;
  }, [outline]);

  // 更新选择状态
  const updateSelectionState = useCallback((nodeId: string, selected: boolean) => {
    setSelectionState(prev => {
      const newSelectedIds = new Set(prev.selectedNodeIds);
      const newPartiallySelectedIds = new Set(prev.partiallySelectedNodeIds);

      // 找到目标节点
      const findNode = (nodes: OutlineTreeNode[], id: string): OutlineTreeNode | null => {
        for (const node of nodes) {
          if (node.id === id) return node;
          const found = findNode(node.children, id);
          if (found) return found;
        }
        return null;
      };

      const targetNode = findNode(treeNodes, nodeId);
      if (!targetNode) return prev;

      // 更新当前节点
      if (selected) {
        newSelectedIds.add(nodeId);
      } else {
        newSelectedIds.delete(nodeId);
      }

      // 递归更新子节点
      const updateChildren = (node: OutlineTreeNode, parentSelected: boolean) => {
        if (parentSelected) {
          newSelectedIds.add(node.id);
        } else {
          newSelectedIds.delete(node.id);
        }
        newPartiallySelectedIds.delete(node.id);
        node.children.forEach(child => updateChildren(child, parentSelected));
      };

      updateChildren(targetNode, selected);

      // 递归更新父节点的部分选择状态
      const updateParentPartialState = (node: OutlineTreeNode) => {
        if (!node.parent) return;

        const siblings = node.parent.children;
        const selectedSiblings = siblings.filter(s => newSelectedIds.has(s.id));
        const partialSiblings = siblings.filter(s => newPartiallySelectedIds.has(s.id));

        if (selectedSiblings.length === siblings.length) {
          // 所有子节点都被选中
          newSelectedIds.add(node.parent.id);
          newPartiallySelectedIds.delete(node.parent.id);
        } else if (selectedSiblings.length > 0 || partialSiblings.length > 0) {
          // 部分子节点被选中
          newSelectedIds.delete(node.parent.id);
          newPartiallySelectedIds.add(node.parent.id);
        } else {
          // 没有子节点被选中
          newSelectedIds.delete(node.parent.id);
          newPartiallySelectedIds.delete(node.parent.id);
        }

        updateParentPartialState(node.parent);
      };

      if (targetNode.parent) {
        updateParentPartialState(targetNode);
      }

      return {
        ...prev,
        selectedNodeIds: newSelectedIds,
        partiallySelectedNodeIds: newPartiallySelectedIds
      };
    });
  }, [treeNodes]);

  // 展开/折叠节点
  const handleNodeExpand = useCallback((nodeId: string, expanded: boolean) => {
    setSelectionState(prev => {
      const newExpandedIds = new Set(prev.expandedNodeIds);
      if (expanded) {
        newExpandedIds.add(nodeId);
      } else {
        newExpandedIds.delete(nodeId);
      }
      return {
        ...prev,
        expandedNodeIds: newExpandedIds
      };
    });
  }, []);

  // 处理视图切换
  const handleViewChange = useCallback((view: OutlineViewType) => {
    setCurrentView(view);
  }, []);

  // 处理节点点击（显示节点信息）
  const handleNodeClick = useCallback((node: OutlineTreeNode) => {
    setSelectedNodeInfo(node);
  }, []);

  // 处理画布大纲变更（只读模式，但需要处理选择状态）
  const handleCanvasOutlineChange = useCallback((updatedOutline: any) => {
    // 在只读模式下，我们不修改大纲结构，但可以处理选择状态
    // 这里可以添加选择状态同步逻辑
    console.log('画布大纲变更（只读模式）:', updatedOutline);
  }, []);

  // 获取所有可见节点ID的辅助函数
  const getAllVisibleNodeIds = useCallback((nodes: OutlineTreeNode[], searchQuery: string): string[] => {
    const result: string[] = [];
    const traverse = (nodeList: OutlineTreeNode[]) => {
      nodeList.forEach(node => {
        if (!searchQuery || node.title.toLowerCase().includes(searchQuery.toLowerCase())) {
          result.push(node.id);
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return result;
  }, []);

  // 获取可见节点总数
  const getAllVisibleNodesCount = useCallback(() => {
    return getAllVisibleNodeIds(treeNodes, selectionState.searchQuery).length;
  }, [treeNodes, selectionState.searchQuery, getAllVisibleNodeIds]);

  // 全选功能
  const handleSelectAll = useCallback(() => {
    const allVisibleNodeIds = getAllVisibleNodeIds(treeNodes, selectionState.searchQuery);
    setSelectionState(prev => ({
      ...prev,
      selectedNodeIds: new Set(allVisibleNodeIds),
      partiallySelectedNodeIds: new Set()
    }));
  }, [treeNodes, selectionState.searchQuery, getAllVisibleNodeIds]);

  // 取消全选功能
  const handleDeselectAll = useCallback(() => {
    setSelectionState(prev => ({
      ...prev,
      selectedNodeIds: new Set(),
      partiallySelectedNodeIds: new Set()
    }));
  }, []);

  // 反选功能
  const handleInvertSelection = useCallback(() => {
    const allVisibleNodeIds = getAllVisibleNodeIds(treeNodes, selectionState.searchQuery);
    const currentSelected = selectionState.selectedNodeIds;
    const inverted = allVisibleNodeIds.filter(id => !currentSelected.has(id));
    setSelectionState(prev => ({
      ...prev,
      selectedNodeIds: new Set(inverted),
      partiallySelectedNodeIds: new Set()
    }));
  }, [treeNodes, selectionState.searchQuery, selectionState.selectedNodeIds, getAllVisibleNodeIds]);

  // 编辑相关状态
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState<{
    title: string;
    description: string;
    creativeNotes: string;
    type: 'volume' | 'event' | 'chapter' | 'plot' | 'dialogue' | 'synopsis';
    // 章节专有字段
    chapterStyle?: string;
    chapterTechniques?: string[];
    chapterGoals?: string;
    // 剧情节点专有字段
    plotType?: 'conflict' | 'twist' | 'climax' | 'resolution';
    plotPoints?: any[];
    relatedCharacters?: string[];
    // 对话设计专有字段
    dialogueScene?: string;
    participants?: string[];
    dialoguePurpose?: string;
    dialogueContent?: any[];
    // 核心故事梗概专有字段
    synopsisBrainhole?: string;
    synopsisGenre?: string;
    synopsisCoreOutline?: string;
    synopsisStoryDescription?: string;
    synopsisAceReferences?: string;
  } | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [editError, setEditError] = useState<string | null>(null);

  // 编辑模式判断
  const isEditing = editingNodeId === selectedNodeInfo?.id;

  // 开始编辑
  const handleStartEdit = useCallback((node: OutlineTreeNode) => {
    setEditingNodeId(node.id);
    setEditingContent({
      title: node.title,
      description: node.description || '',
      creativeNotes: node.creativeNotes || '',
      type: node.type,
      // 章节专有字段
      chapterStyle: node.chapterStyle || '',
      chapterTechniques: node.chapterTechniques || [],
      chapterGoals: node.chapterGoals || '',
      // 剧情节点专有字段
      plotType: node.plotType || undefined,
      plotPoints: node.plotPoints || [],
      relatedCharacters: node.relatedCharacters || [],
      // 对话设计专有字段
      dialogueScene: node.dialogueScene || '',
      participants: node.participants || [],
      dialoguePurpose: node.dialoguePurpose || '',
      dialogueContent: node.dialogueContent || [],
      // 核心故事梗概专有字段
      synopsisBrainhole: node.synopsisBrainhole || '',
      synopsisGenre: node.synopsisGenre || '',
      synopsisCoreOutline: node.synopsisCoreOutline || '',
      synopsisStoryDescription: node.synopsisStoryDescription || '',
      synopsisAceReferences: node.synopsisAceReferences || '',
    });
    setEditError(null);
  }, []);

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    if (!editingContent || !editingNodeId || !selectedNodeInfo) return;

    // 验证输入
    if (!editingContent.title.trim()) {
      setEditError('节点标题不能为空');
      return;
    }

    setIsSaving(true);
    setEditError(null);

    try {
      // 更新节点的递归函数
      const updateNodeInTree = (nodes: OutlineTreeNode[]): OutlineTreeNode[] => {
        return nodes.map(node => {
          if (node.id === editingNodeId) {
            return {
              ...node,
              title: editingContent.title,
              description: editingContent.description,
              creativeNotes: editingContent.creativeNotes,
              type: editingContent.type,
              // 章节专有字段
              chapterStyle: editingContent.type === 'chapter' ? editingContent.chapterStyle : undefined,
              chapterTechniques: editingContent.type === 'chapter' ? editingContent.chapterTechniques : undefined,
              chapterGoals: editingContent.type === 'chapter' ? editingContent.chapterGoals : undefined,
              // 剧情节点专有字段
              plotType: editingContent.type === 'plot' ? editingContent.plotType : undefined,
              plotPoints: editingContent.type === 'plot' ? editingContent.plotPoints : undefined,
              relatedCharacters: editingContent.type === 'plot' ? editingContent.relatedCharacters : undefined,
              // 对话设计专有字段
              dialogueScene: editingContent.type === 'dialogue' ? editingContent.dialogueScene : undefined,
              participants: editingContent.type === 'dialogue' ? editingContent.participants : undefined,
              dialoguePurpose: editingContent.type === 'dialogue' ? editingContent.dialoguePurpose : undefined,
              dialogueContent: editingContent.type === 'dialogue' ? editingContent.dialogueContent : undefined,
            };
          }
          if (node.children && node.children.length > 0) {
            return {
              ...node,
              children: updateNodeInTree(node.children)
            };
          }
          return node;
        });
      };

      // 更新本地树状结构
      const updatedTreeNodes = updateNodeInTree(treeNodes);

      // 更新选中节点信息
      setSelectedNodeInfo({
        ...selectedNodeInfo,
        title: editingContent.title,
        description: editingContent.description,
        creativeNotes: editingContent.creativeNotes,
        type: editingContent.type,
        // 章节专有字段
        chapterStyle: editingContent.type === 'chapter' ? editingContent.chapterStyle : undefined,
        chapterTechniques: editingContent.type === 'chapter' ? editingContent.chapterTechniques : undefined,
        chapterGoals: editingContent.type === 'chapter' ? editingContent.chapterGoals : undefined,
        // 剧情节点专有字段
        plotType: editingContent.type === 'plot' ? editingContent.plotType : undefined,
        plotPoints: editingContent.type === 'plot' ? editingContent.plotPoints : undefined,
        relatedCharacters: editingContent.type === 'plot' ? editingContent.relatedCharacters : undefined,
        // 对话设计专有字段
        dialogueScene: editingContent.type === 'dialogue' ? editingContent.dialogueScene : undefined,
        participants: editingContent.type === 'dialogue' ? editingContent.participants : undefined,
        dialoguePurpose: editingContent.type === 'dialogue' ? editingContent.dialoguePurpose : undefined,
        dialogueContent: editingContent.type === 'dialogue' ? editingContent.dialogueContent : undefined,
      });

      // 如果有大纲数据，保存到大纲服务
      if (outline) {
        // 更新大纲数据中的节点
        const updateOutlineNode = (nodes: any[]): any[] => {
          return nodes.map(node => {
            if (node.id === editingNodeId) {
              return {
                ...node,
                title: editingContent.title,
                description: editingContent.description,
                creativeNotes: editingContent.creativeNotes,
                type: editingContent.type,
                // 章节专有字段
                chapterStyle: editingContent.type === 'chapter' ? editingContent.chapterStyle : undefined,
                chapterTechniques: editingContent.type === 'chapter' ? editingContent.chapterTechniques : undefined,
                chapterGoals: editingContent.type === 'chapter' ? editingContent.chapterGoals : undefined,
                // 剧情节点专有字段
                plotType: editingContent.type === 'plot' ? editingContent.plotType : undefined,
                plotPoints: editingContent.type === 'plot' ? editingContent.plotPoints : undefined,
                relatedCharacters: editingContent.type === 'plot' ? editingContent.relatedCharacters : undefined,
                // 对话设计专有字段
                dialogueScene: editingContent.type === 'dialogue' ? editingContent.dialogueScene : undefined,
                participants: editingContent.type === 'dialogue' ? editingContent.participants : undefined,
                dialoguePurpose: editingContent.type === 'dialogue' ? editingContent.dialoguePurpose : undefined,
                dialogueContent: editingContent.type === 'dialogue' ? editingContent.dialogueContent : undefined,
              };
            }
            if (node.children && node.children.length > 0) {
              return {
                ...node,
                children: updateOutlineNode(node.children)
              };
            }
            return node;
          });
        };

        const updatedOutline = {
          ...outline,
          nodes: updateOutlineNode(outline.nodes)
        };

        // 如果有大纲服务，保存到数据库
        try {
          const { outlineService } = await import('@/factories/api/outlineService');
          await outlineService.saveOutline(outline.workId, updatedOutline);
          console.log('节点编辑保存成功');
        } catch (saveError) {
          console.error('保存到数据库失败:', saveError);
          // 不阻止编辑完成，只是记录错误
        }
      }

      // 退出编辑模式
      setEditingNodeId(null);
      setEditingContent(null);

    } catch (error) {
      console.error('保存节点失败:', error);
      setEditError('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  }, [editingContent, editingNodeId, selectedNodeInfo, treeNodes, outline]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingNodeId(null);
    setEditingContent(null);
    setEditError(null);
  }, []);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isEditing) {
        if (e.key === 'Escape') {
          handleCancelEdit();
        } else if ((e.ctrlKey || e.metaKey) && e.key === 's') {
          e.preventDefault();
          handleSaveEdit();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isEditing, handleCancelEdit, handleSaveEdit]);

  // 同步选择状态到父组件
  useEffect(() => {
    const selectedIds = Array.from(selectionState.selectedNodeIds);
    onSelectionChange(selectedIds);
  }, [selectionState.selectedNodeIds, onSelectionChange]);

  // 监听大纲数据变化，更新selectedNodeInfo
  useEffect(() => {
    if (selectedNodeInfo && outline?.nodes) {
      // 在更新的大纲数据中查找当前选中的节点
      const findUpdatedNode = (nodes: any[], targetId: string): any | null => {
        for (const node of nodes) {
          if (node.id === targetId) return node;
          if (node.children) {
            const found = findUpdatedNode(node.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const updatedNode = findUpdatedNode(outline.nodes, selectedNodeInfo.id);
      if (updatedNode) {
        // 将更新的节点数据转换为TreeNode格式
        const updatedTreeNode: OutlineTreeNode = {
          ...selectedNodeInfo,
          title: updatedNode.title,
          description: updatedNode.description,
          creativeNotes: updatedNode.creativeNotes,
          // 更新章节专有字段
          chapterStyle: updatedNode.chapterStyle,
          chapterTechniques: updatedNode.chapterTechniques,
          chapterGoals: updatedNode.chapterGoals,
          // 更新剧情节点专有字段
          plotType: updatedNode.plotType,
          plotPoints: updatedNode.plotPoints,
          relatedCharacters: updatedNode.relatedCharacters,
          // 更新对话设计专有字段
          dialogueScene: updatedNode.dialogueScene,
          participants: updatedNode.participants,
          dialoguePurpose: updatedNode.dialoguePurpose,
          dialogueContent: updatedNode.dialogueContent,
        };
        setSelectedNodeInfo(updatedTreeNode);
        console.log('✅ 节点详情已更新:', updatedTreeNode);
      }
    }
  }, [outline, selectedNodeInfo?.id]);

  // 统计信息
  const stats = useMemo(() => {
    const findNodeType = (nodes: OutlineTreeNode[], targetId: string): string | null => {
      for (const node of nodes) {
        if (node.id === targetId) return node.type;
        const found = findNodeType(node.children, targetId);
        if (found) return found;
      }
      return null;
    };

    return {
      total: selectionState.selectedNodeIds.size,
      volumes: Array.from(selectionState.selectedNodeIds).filter(id =>
        findNodeType(treeNodes, id) === 'volume'
      ).length,
      chapters: Array.from(selectionState.selectedNodeIds).filter(id =>
        findNodeType(treeNodes, id) === 'chapter'
      ).length,
      plots: Array.from(selectionState.selectedNodeIds).filter(id =>
        findNodeType(treeNodes, id) === 'plot'
      ).length,
      dialogues: Array.from(selectionState.selectedNodeIds).filter(id =>
        findNodeType(treeNodes, id) === 'dialogue'
      ).length
    };
  }, [selectionState.selectedNodeIds, treeNodes]);

  if (!outline?.nodes || outline.nodes.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="p-4 bg-gray-100 rounded-full mb-6 mx-auto w-fit">
            <svg className="w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div className="text-lg font-semibold mb-2 text-gray-700">暂无大纲内容</div>
          <div className="text-sm text-gray-500">请先创建大纲节点</div>
        </div>
      </div>
    );
  }

  return (
    <div className="outline-tree-selector h-full flex">
      {/* 左侧：选择操作区 - 紧凑设计 */}
      <div className={`${currentView === 'canvas' ? 'w-full' : 'w-1/3'} flex flex-col ${currentView === 'tree' ? 'border-r border-gray-200' : ''}`}>
        {/* 顶部工具栏 */}
        <div className="p-3 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-base font-medium text-gray-800">节点选择</h4>
            {/* 视图切换器 */}
            <ViewSwitcher currentView={currentView} onViewChange={handleViewChange} />
          </div>

          {/* 搜索栏 - 只在树状视图显示 */}
          {currentView === 'tree' && (
            <input
              type="text"
              placeholder="搜索节点..."
              value={selectionState.searchQuery}
              onChange={(e) => setSelectionState(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          )}
        </div>

        {/* 统计信息 - 现代化设计 */}
        <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">
                已选择 {stats.total} 个节点
                {stats.total > 0 && (
                  <span className="text-xs text-gray-500 ml-2">
                    ({stats.volumes > 0 && `${stats.volumes}卷 `}
                    {stats.chapters > 0 && `${stats.chapters}章 `}
                    {stats.plots > 0 && `${stats.plots}剧情 `}
                    {stats.dialogues > 0 && `${stats.dialogues}对话`})
                  </span>
                )}
              </span>
            </div>

            {/* 新增：批量操作按钮组 */}
            <div className="flex items-center space-x-2">
              {stats.total < getAllVisibleNodesCount() && (
                <button
                  onClick={handleSelectAll}
                  className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors duration-200 flex items-center space-x-1"
                  title="选择所有可见节点"
                >
                  <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>全选</span>
                </button>
              )}
              {stats.total > 0 && (
                <button
                  onClick={handleDeselectAll}
                  className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1"
                  title="取消所有选择"
                >
                  <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span>取消全选</span>
                </button>
              )}
              {stats.total > 0 && stats.total < getAllVisibleNodesCount() && (
                <button
                  onClick={handleInvertSelection}
                  className="px-3 py-1 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 transition-colors duration-200 flex items-center space-x-1"
                  title="反转当前选择"
                >
                  <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                  <span>反选</span>
                </button>
              )}
            </div>
          </div>

          {/* 现有的统计信息显示 */}
          <div className="flex items-center justify-between mt-3">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-purple-600">
                <NodeIcon type="volume" className="w-4 h-4" />
                <span className="text-xs font-medium">{stats.volumes}</span>
              </div>
              <div className="flex items-center space-x-1 text-blue-600">
                <NodeIcon type="chapter" className="w-4 h-4" />
                <span className="text-xs font-medium">{stats.chapters}</span>
              </div>
              <div className="flex items-center space-x-1 text-orange-600">
                <NodeIcon type="plot" className="w-4 h-4" />
                <span className="text-xs font-medium">{stats.plots}</span>
              </div>
              <div className="flex items-center space-x-1 text-green-600">
                <NodeIcon type="dialogue" className="w-4 h-4" />
                <span className="text-xs font-medium">{stats.dialogues}</span>
              </div>
            </div>

            {/* 显示总数信息 */}
            <div className="text-xs text-gray-500">
              共 {getAllVisibleNodesCount()} 个节点
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {currentView === 'tree' ? (
            /* 树状视图 - 现代化容器 */
            <div className="h-full overflow-y-auto p-4 bg-gradient-to-b from-white to-gray-50">
              <div className="space-y-2">
                {treeNodes.map((node: OutlineTreeNode) => (
                  <TreeNode
                    key={node.id}
                    node={node}
                    selectionState={selectionState}
                    onNodeSelect={updateSelectionState}
                    onNodeExpand={handleNodeExpand}
                    onNodeClick={handleNodeClick}
                  />
                ))}
              </div>
            </div>
          ) : (
            /* 画布视图 - 选择模式 */
            <div className="h-full relative">
              <OutlineCanvas
                outline={outline}
                bookId={outline?.workId || ''}
                onChange={handleCanvasOutlineChange}
                selectionMode={true}
                selectedNodeIds={Array.from(selectionState.selectedNodeIds)}
                onNodeSelectionChange={(nodeId: string, selected: boolean) => {
                  console.log('Canvas node selection change received:', nodeId, selected);
                  updateSelectionState(nodeId, selected);
                }}
              />

              {/* 画布选择模式按键指示器 */}
              <div className="absolute top-4 left-4 z-10">
                <div className="flex items-center space-x-3">
                  {/* 选择模式按键 */}
                  <div className="bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2 text-sm font-medium">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>选择模式</span>
                  </div>

                  {/* 选择数量指示器 */}
                  {selectionState.selectedNodeIds.size > 0 && (
                    <div className="bg-white border border-gray-200 px-3 py-2 rounded-lg shadow-md flex items-center space-x-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-gray-700 font-medium">
                        已选择 {selectionState.selectedNodeIds.size} 个
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 右侧：信息展示区 - 只在树状视图显示 */}
      {currentView === 'tree' && (
        <div className="w-2/3 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-lg font-medium text-gray-800">节点详情</h4>
          <p className="text-sm text-gray-500 mt-1">查看选中节点的详细信息和内容预览</p>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {selectedNodeInfo ? (
            <div className="space-y-6">
              {/* 编辑模式或查看模式 */}
              {isEditing ? (
                /* 编辑模式 */
                <div className="space-y-6 bg-blue-50 p-6 rounded-xl border-2 border-blue-200">
                  <div className="flex items-center justify-between mb-4">
                    <h5 className="text-lg font-semibold text-blue-900 flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      <span>编辑节点</span>
                    </h5>
                    <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                      Ctrl+S 保存 | Esc 取消
                    </div>
                  </div>

                  {/* 错误提示 */}
                  {editError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                      <svg className="w-5 h-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-red-700 text-sm">{editError}</span>
                    </div>
                  )}

                  {/* 编辑标题 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      节点标题 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingContent?.title || ''}
                      onChange={(e) => setEditingContent(prev => prev ? {
                        ...prev, title: e.target.value
                      } : null)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="请输入节点标题"
                      disabled={isSaving}
                    />
                  </div>

                  {/* 编辑类型 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      节点类型
                    </label>
                    <select
                      value={editingContent?.type || 'dialogue'}
                      onChange={(e) => setEditingContent(prev => prev ? {
                        ...prev, type: e.target.value as 'volume' | 'chapter' | 'plot' | 'dialogue'
                      } : null)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                      disabled={isSaving}
                    >
                      <option value="volume">📚 总纲/卷</option>
                      <option value="chapter">📖 章节</option>
                      <option value="plot">🎬 剧情节点</option>
                      <option value="dialogue">💬 对话设计</option>
                    </select>
                  </div>

                  {/* 编辑描述 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      节点描述
                    </label>
                    <textarea
                      value={editingContent?.description || ''}
                      onChange={(e) => setEditingContent(prev => prev ? {
                        ...prev, description: e.target.value
                      } : null)}
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none transition-colors"
                      placeholder="请输入节点描述..."
                      disabled={isSaving}
                    />
                  </div>

                  {/* 编辑创作建议 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                      <span>💡</span>
                      <span>创作建议</span>
                    </label>
                    <textarea
                      value={editingContent?.creativeNotes || ''}
                      onChange={(e) => setEditingContent(prev => prev ? {
                        ...prev, creativeNotes: e.target.value
                      } : null)}
                      rows={4}
                      className="w-full px-4 py-3 border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 resize-none transition-colors bg-gradient-to-r from-blue-50 to-indigo-50"
                      placeholder="输入创作建议，如台词设计、心理描写、节奏控制等..."
                      disabled={isSaving}
                    />
                  </div>

                  {/* 章节专有字段编辑 */}
                  {editingContent?.type === 'chapter' && (
                    <div className="space-y-4 border-t pt-4">
                      <h6 className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                        📖 章节专有字段
                      </h6>

                      {/* 写作风格 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          写作风格
                        </label>
                        <input
                          type="text"
                          value={editingContent?.chapterStyle || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, chapterStyle: e.target.value
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          placeholder="如：悬疑紧张、温馨治愈、激烈战斗等"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 写作手法 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          写作手法
                        </label>
                        <input
                          type="text"
                          value={editingContent?.chapterTechniques?.join(', ') || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, chapterTechniques: e.target.value.split(',').map(t => t.trim()).filter(t => t)
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          placeholder="如：倒叙、插叙、对比、象征等（用逗号分隔）"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 章节目标 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          章节目标
                        </label>
                        <textarea
                          value={editingContent?.chapterGoals || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, chapterGoals: e.target.value
                          } : null)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none transition-colors"
                          placeholder="如：推进主线剧情、展现角色成长、营造氛围等"
                          disabled={isSaving}
                        />
                      </div>
                    </div>
                  )}

                  {/* 剧情节点专有字段编辑 */}
                  {editingContent?.type === 'plot' && (
                    <div className="space-y-4 border-t pt-4">
                      <h6 className="text-sm font-semibold text-green-800 flex items-center gap-2">
                        🎭 剧情节点专有字段
                      </h6>

                      {/* 剧情类型 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          剧情类型
                        </label>
                        <div className="grid grid-cols-2 gap-2">
                          {[
                            { value: 'conflict', label: '冲突' },
                            { value: 'twist', label: '转折' },
                            { value: 'climax', label: '高潮' },
                            { value: 'resolution', label: '解决' }
                          ].map(({ value, label }) => (
                            <button
                              key={value}
                              type="button"
                              className={`py-2 px-3 rounded-lg border text-sm transition-colors ${
                                editingContent?.plotType === value
                                  ? 'bg-green-500 text-white border-green-500'
                                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                              }`}
                              onClick={() => setEditingContent(prev => prev ? {
                                ...prev, plotType: value as 'conflict' | 'twist' | 'climax' | 'resolution'
                              } : null)}
                              disabled={isSaving}
                            >
                              {label}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* 关联角色 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          关联角色
                        </label>
                        <input
                          type="text"
                          value={editingContent?.relatedCharacters?.join(', ') || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, relatedCharacters: e.target.value.split(',').map(c => c.trim()).filter(c => c)
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          placeholder="参与此剧情的角色（用逗号分隔）"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 剧情点编辑 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          剧情点内容
                        </label>
                        <div className="space-y-4">
                          {(editingContent?.plotPoints || []).map((point, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                              <div className="flex items-start space-x-2 mb-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-700 rounded-full flex items-center justify-center text-xs font-medium mt-1">
                                  {index + 1}
                                </span>
                                <div className="flex-1">
                                  <textarea
                                    value={typeof point === 'string' ? point : point?.content || ''}
                                    onChange={(e) => {
                                      const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                      if (typeof newPlotPoints[index] === 'string') {
                                        newPlotPoints[index] = e.target.value;
                                      } else {
                                        newPlotPoints[index] = { ...newPlotPoints[index], content: e.target.value };
                                      }
                                      setEditingContent(prev => prev ? {
                                        ...prev, plotPoints: newPlotPoints
                                      } : null);
                                    }}
                                    rows={2}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 resize-none transition-colors"
                                    placeholder={`第${index + 1}个剧情点...`}
                                    disabled={isSaving}
                                  />
                                </div>
                                <button
                                  type="button"
                                  onClick={() => {
                                    const newPlotPoints = (editingContent?.plotPoints || []).filter((_, i) => i !== index);
                                    setEditingContent(prev => prev ? {
                                      ...prev, plotPoints: newPlotPoints
                                    } : null);
                                  }}
                                  className="flex-shrink-0 p-1 text-red-500 hover:text-red-700 transition-colors"
                                  disabled={isSaving}
                                >
                                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v1M4 7h16" />
                                  </svg>
                                </button>
                              </div>

                              {/* 避免描写 */}
                              <div className="mb-3">
                                <label className="block text-xs font-medium text-red-600 mb-1">🚫 避免描写</label>
                                <textarea
                                  value={typeof point === 'object' ? point?.avoidWriting || '' : ''}
                                  onChange={(e) => {
                                    const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                    if (typeof newPlotPoints[index] === 'string') {
                                      newPlotPoints[index] = { content: newPlotPoints[index], avoidWriting: e.target.value };
                                    } else {
                                      newPlotPoints[index] = { ...newPlotPoints[index], avoidWriting: e.target.value };
                                    }
                                    setEditingContent(prev => prev ? {
                                      ...prev, plotPoints: newPlotPoints
                                    } : null);
                                  }}
                                  rows={2}
                                  className="w-full px-2 py-1 text-xs border border-red-300 rounded focus:ring-1 focus:ring-red-500 resize-none transition-colors bg-red-50"
                                  placeholder="避免'一丝xx'、'几分xx'、'些许xx'等模糊表达；避免'他xx他如何如何'的主观描述"
                                  disabled={isSaving}
                                />
                              </div>

                              {/* 应该描写 */}
                              <div className="mb-3">
                                <label className="block text-xs font-medium text-green-600 mb-1">✅ 应该描写</label>
                                <textarea
                                  value={typeof point === 'object' ? point?.shouldWriting || '' : ''}
                                  onChange={(e) => {
                                    const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                    if (typeof newPlotPoints[index] === 'string') {
                                      newPlotPoints[index] = { content: newPlotPoints[index], shouldWriting: e.target.value };
                                    } else {
                                      newPlotPoints[index] = { ...newPlotPoints[index], shouldWriting: e.target.value };
                                    }
                                    setEditingContent(prev => prev ? {
                                      ...prev, plotPoints: newPlotPoints
                                    } : null);
                                  }}
                                  rows={3}
                                  className="w-full px-2 py-1 text-xs border border-green-300 rounded focus:ring-1 focus:ring-green-500 resize-none transition-colors bg-green-50"
                                  placeholder="背靠着冰冷的断墙残垣，粗重地喘息。\n额前的碎发被汗水打湿，黏在皮肤上。\n视线漫无目的地抬起，捕捉到遥远天幕上几个缓缓移动的黑点。"
                                  disabled={isSaving}
                                />
                              </div>

                              {/* 写作风格方法 */}
                              <div className="mb-3 bg-blue-50 p-2 rounded border border-blue-200">
                                <label className="block text-xs font-medium text-blue-700 mb-2">🎨 写作风格方法</label>
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <label className="block text-xs text-blue-600">技巧：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.styleMethod?.technique || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          styleMethod: {
                                            ...currentPoint.styleMethod,
                                            technique: e.target.value
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                                      placeholder="对比手法"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-blue-600">风格：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.styleMethod?.style || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          styleMethod: {
                                            ...currentPoint.styleMethod,
                                            style: e.target.value
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                                      placeholder="简洁明快"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-blue-600">语调：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.styleMethod?.tone || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          styleMethod: {
                                            ...currentPoint.styleMethod,
                                            tone: e.target.value
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                                      placeholder="紧张"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-blue-600">视角：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.styleMethod?.perspective || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          styleMethod: {
                                            ...currentPoint.styleMethod,
                                            perspective: e.target.value
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                                      placeholder="第三人称"
                                      disabled={isSaving}
                                    />
                                  </div>
                                </div>
                                <div className="mt-2">
                                  <label className="block text-xs text-blue-600">重点：</label>
                                  <input
                                    type="text"
                                    value={typeof point === 'object' ? point?.styleMethod?.emphasis || '' : ''}
                                    onChange={(e) => {
                                      const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                      const currentPoint = typeof newPlotPoints[index] === 'string'
                                        ? { content: newPlotPoints[index] }
                                        : newPlotPoints[index] || {};
                                      newPlotPoints[index] = {
                                        ...currentPoint,
                                        styleMethod: {
                                          ...currentPoint.styleMethod,
                                          emphasis: e.target.value
                                        }
                                      };
                                      setEditingContent(prev => prev ? {
                                        ...prev, plotPoints: newPlotPoints
                                      } : null);
                                    }}
                                    className="w-full px-1 py-1 text-xs border border-blue-200 rounded"
                                    placeholder="动作描写为主"
                                    disabled={isSaving}
                                  />
                                </div>
                              </div>

                              {/* 格式规范 */}
                              <div className="bg-green-50 p-2 rounded border border-green-200">
                                <label className="block text-xs font-medium text-green-700 mb-2">📏 格式规范</label>
                                <div className="grid grid-cols-3 gap-2 mb-2">
                                  <div>
                                    <label className="block text-xs text-green-600">最少字数：</label>
                                    <input
                                      type="number"
                                      value={typeof point === 'object' ? point?.formatSpecs?.wordCount?.min || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          formatSpecs: {
                                            ...currentPoint.formatSpecs,
                                            wordCount: {
                                              ...currentPoint.formatSpecs?.wordCount,
                                              min: parseInt(e.target.value) || 150
                                            }
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                                      placeholder="150"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-green-600">最多字数：</label>
                                    <input
                                      type="number"
                                      value={typeof point === 'object' ? point?.formatSpecs?.wordCount?.max || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          formatSpecs: {
                                            ...currentPoint.formatSpecs,
                                            wordCount: {
                                              ...currentPoint.formatSpecs?.wordCount,
                                              max: parseInt(e.target.value) || 400
                                            }
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                                      placeholder="400"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-green-600">目标字数：</label>
                                    <input
                                      type="number"
                                      value={typeof point === 'object' ? point?.formatSpecs?.wordCount?.target || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          formatSpecs: {
                                            ...currentPoint.formatSpecs,
                                            wordCount: {
                                              ...currentPoint.formatSpecs?.wordCount,
                                              target: parseInt(e.target.value) || 250
                                            }
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                                      placeholder="250"
                                      disabled={isSaving}
                                    />
                                  </div>
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <label className="block text-xs text-green-600">对话格式：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.formatSpecs?.punctuationRules?.dialogueFormat || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          formatSpecs: {
                                            ...currentPoint.formatSpecs,
                                            punctuationRules: {
                                              ...currentPoint.formatSpecs?.punctuationRules,
                                              dialogueFormat: e.target.value
                                            }
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                                      placeholder="「」"
                                      disabled={isSaving}
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-green-600">分段规则：</label>
                                    <input
                                      type="text"
                                      value={typeof point === 'object' ? point?.formatSpecs?.paragraphRules?.paragraphBreakRules || '' : ''}
                                      onChange={(e) => {
                                        const newPlotPoints = [...(editingContent?.plotPoints || [])];
                                        const currentPoint = typeof newPlotPoints[index] === 'string'
                                          ? { content: newPlotPoints[index] }
                                          : newPlotPoints[index] || {};
                                        newPlotPoints[index] = {
                                          ...currentPoint,
                                          formatSpecs: {
                                            ...currentPoint.formatSpecs,
                                            paragraphRules: {
                                              ...currentPoint.formatSpecs?.paragraphRules,
                                              paragraphBreakRules: e.target.value
                                            }
                                          }
                                        };
                                        setEditingContent(prev => prev ? {
                                          ...prev, plotPoints: newPlotPoints
                                        } : null);
                                      }}
                                      className="w-full px-1 py-1 text-xs border border-green-200 rounded"
                                      placeholder="逻辑完整后分段"
                                      disabled={isSaving}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                          <button
                            type="button"
                            onClick={() => {
                              const newPlotPoint = {
                                content: '',
                                avoidWriting: '避免\'一丝xx\'、\'几分xx\'、\'些许xx\'等模糊表达；避免\'他xx他如何如何\'的主观描述',
                                shouldWriting: '背靠着冰冷的断墙残垣，粗重地喘息。\n额前的碎发被汗水打湿，黏在皮肤上。\n视线漫无目的地抬起，捕捉到遥远天幕上几个缓缓移动的黑点。',
                                styleMethod: {
                                  technique: '直接描写',
                                  style: '客观叙述',
                                  tone: '中性',
                                  perspective: '第三人称',
                                  emphasis: '行动和对话并重'
                                },
                                formatSpecs: {
                                  wordCount: {
                                    min: 150,
                                    max: 400,
                                    target: 250
                                  },
                                  paragraphRules: {
                                    maxSentencesPerParagraph: 4,
                                    paragraphBreakRules: '逻辑完整后分段'
                                  },
                                  punctuationRules: {
                                    dialogueFormat: '「」',
                                    emphasisFormat: '适度使用',
                                    pauseFormat: '自然停顿'
                                  },
                                  lineBreakRules: {
                                    sceneTransition: '明确分隔',
                                    timeTransition: '清晰标记',
                                    speakerChange: '独立成行'
                                  }
                                }
                              };
                              const newPlotPoints = [...(editingContent?.plotPoints || []), newPlotPoint];
                              setEditingContent(prev => prev ? {
                                ...prev, plotPoints: newPlotPoints
                              } : null);
                            }}
                            className="w-full py-2 px-3 border-2 border-dashed border-green-300 rounded-lg text-green-600 hover:border-green-400 hover:text-green-700 transition-colors flex items-center justify-center space-x-2"
                            disabled={isSaving}
                          >
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            <span>添加剧情点</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 对话设计专有字段编辑 */}
                  {editingContent?.type === 'dialogue' && (
                    <div className="space-y-4 border-t pt-4">
                      <h6 className="text-sm font-semibold text-purple-800 flex items-center gap-2">
                        💬 对话设计专有字段
                      </h6>

                      {/* 对话场景 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          对话场景
                        </label>
                        <input
                          type="text"
                          value={editingContent?.dialogueScene || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, dialogueScene: e.target.value
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          placeholder="对话发生的场景描述"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 参与角色 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          参与角色
                        </label>
                        <input
                          type="text"
                          value={editingContent?.participants?.join(', ') || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, participants: e.target.value.split(',').map(p => p.trim()).filter(p => p)
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          placeholder="参与对话的角色（用逗号分隔）"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 对话目的 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          对话目的
                        </label>
                        <textarea
                          value={editingContent?.dialoguePurpose || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, dialoguePurpose: e.target.value
                          } : null)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none transition-colors"
                          placeholder="如：信息传递、情感表达、冲突升级等"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 对话内容编辑 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          对话内容
                        </label>
                        <div className="space-y-3">
                          {(editingContent?.dialogueContent || []).map((dialogue, index) => (
                            <div key={index} className="border border-purple-200 rounded-lg p-3 bg-purple-50">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-purple-700">对话 {index + 1}</span>
                                <button
                                  type="button"
                                  onClick={() => {
                                    const newDialogueContent = (editingContent?.dialogueContent || []).filter((_, i) => i !== index);
                                    setEditingContent(prev => prev ? {
                                      ...prev, dialogueContent: newDialogueContent
                                    } : null);
                                  }}
                                  className="p-1 text-red-500 hover:text-red-700 transition-colors"
                                  disabled={isSaving}
                                >
                                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v1M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                              <div className="grid grid-cols-1 gap-2">
                                <div>
                                  <label className="block text-xs font-medium text-gray-600 mb-1">说话人</label>
                                  <input
                                    type="text"
                                    value={typeof dialogue === 'object' && dialogue?.speaker ? dialogue.speaker : ''}
                                    onChange={(e) => {
                                      const newDialogueContent = [...(editingContent?.dialogueContent || [])];
                                      const currentDialogue = typeof newDialogueContent[index] === 'object'
                                        ? newDialogueContent[index]
                                        : { speaker: '', text: '' };
                                      newDialogueContent[index] = {
                                        ...currentDialogue,
                                        speaker: e.target.value
                                      };
                                      setEditingContent(prev => prev ? {
                                        ...prev, dialogueContent: newDialogueContent
                                      } : null);
                                    }}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 transition-colors"
                                    placeholder="角色名称"
                                    disabled={isSaving}
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-600 mb-1">对话内容</label>
                                  <textarea
                                    value={typeof dialogue === 'object' && dialogue?.text ? dialogue.text :
                                           typeof dialogue === 'string' ? dialogue : ''}
                                    onChange={(e) => {
                                      const newDialogueContent = [...(editingContent?.dialogueContent || [])];
                                      const currentDialogue = typeof newDialogueContent[index] === 'object'
                                        ? newDialogueContent[index]
                                        : { speaker: '', text: '' };
                                      newDialogueContent[index] = {
                                        ...currentDialogue,
                                        text: e.target.value
                                      };
                                      setEditingContent(prev => prev ? {
                                        ...prev, dialogueContent: newDialogueContent
                                      } : null);
                                    }}
                                    rows={2}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 resize-none transition-colors"
                                    placeholder="对话内容..."
                                    disabled={isSaving}
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                          <button
                            type="button"
                            onClick={() => {
                              const newDialogueContent = [...(editingContent?.dialogueContent || []), { speaker: '', text: '' }];
                              setEditingContent(prev => prev ? {
                                ...prev, dialogueContent: newDialogueContent
                              } : null);
                            }}
                            className="w-full py-2 px-3 border-2 border-dashed border-purple-300 rounded-lg text-purple-600 hover:border-purple-400 hover:text-purple-700 transition-colors flex items-center justify-center space-x-2"
                            disabled={isSaving}
                          >
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            <span>添加对话</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 核心故事梗概专有字段编辑 */}
                  {editingContent?.type === 'synopsis' && (
                    <div className="space-y-4 border-t pt-4">
                      <h6 className="text-sm font-semibold text-indigo-800 flex items-center gap-2">
                        📖 核心故事梗概专有字段
                      </h6>

                      {/* 脑洞 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          脑洞
                        </label>
                        <textarea
                          value={editingContent?.synopsisBrainhole || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, synopsisBrainhole: e.target.value
                          } : null)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 resize-none transition-colors"
                          placeholder="描述故事的核心创意和独特想法"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 类型 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          类型
                        </label>
                        <input
                          type="text"
                          value={editingContent?.synopsisGenre || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, synopsisGenre: e.target.value
                          } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 transition-colors"
                          placeholder="如：悬疑、言情、科幻、历史等"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 梗概 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          梗概
                        </label>
                        <textarea
                          value={editingContent?.synopsisCoreOutline || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, synopsisCoreOutline: e.target.value
                          } : null)}
                          rows={4}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 resize-none transition-colors"
                          placeholder="对核心梗的使用，对核心的概况，核心极致"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 故事 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          故事
                        </label>
                        <textarea
                          value={editingContent?.synopsisStoryDescription || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, synopsisStoryDescription: e.target.value
                          } : null)}
                          rows={5}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 resize-none transition-colors"
                          placeholder="准备讲一个完整的什么故事"
                          disabled={isSaving}
                        />
                      </div>

                      {/* 引用 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          引用
                        </label>
                        <textarea
                          value={editingContent?.synopsisAceReferences || ''}
                          onChange={(e) => setEditingContent(prev => prev ? {
                            ...prev, synopsisAceReferences: e.target.value
                          } : null)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 resize-none transition-colors"
                          placeholder="引用了那些ACE框架的灵感"
                          disabled={isSaving}
                        />
                      </div>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex justify-end space-x-3 pt-4 border-t border-blue-200">
                    <button
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      取消
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      disabled={isSaving || !editingContent?.title.trim()}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                    >
                      {isSaving && (
                        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      )}
                      <span>{isSaving ? '保存中...' : '保存'}</span>
                    </button>
                  </div>
                </div>
              ) : (
                /* 查看模式 */
                <div className="space-y-6">
                  {/* 节点基本信息 - 现代化卡片设计 */}
                  <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-6 shadow-sm">
                    <div className="flex items-start space-x-5">
                      <div className={`p-3 rounded-xl shadow-sm ${
                        selectedNodeInfo.type === 'volume' ? 'bg-purple-100 text-purple-600' :
                        selectedNodeInfo.type === 'chapter' ? 'bg-blue-100 text-blue-600' :
                        selectedNodeInfo.type === 'plot' ? 'bg-orange-100 text-orange-600' :
                        'bg-green-100 text-green-600'
                      }`}>
                        <NodeIcon type={selectedNodeInfo.type} className="w-8 h-8" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-4">
                          <h5 className="text-2xl font-bold text-gray-900">{selectedNodeInfo.title}</h5>
                          <span className={`
                            px-4 py-2 text-sm rounded-full font-semibold shadow-sm
                            ${selectedNodeInfo.type === 'volume' ? 'bg-purple-100 text-purple-700 border border-purple-200' : ''}
                            ${selectedNodeInfo.type === 'chapter' ? 'bg-blue-100 text-blue-700 border border-blue-200' : ''}
                            ${selectedNodeInfo.type === 'plot' ? 'bg-orange-100 text-orange-700 border border-orange-200' : ''}
                            ${selectedNodeInfo.type === 'dialogue' ? 'bg-green-100 text-green-700 border border-green-200' : ''}
                            ${selectedNodeInfo.type === 'synopsis' ? 'bg-indigo-100 text-indigo-700 border border-indigo-200' : ''}
                          `}>
                            {selectedNodeInfo.type === 'volume' ? '总纲/卷' :
                             selectedNodeInfo.type === 'chapter' ? '章节' :
                             selectedNodeInfo.type === 'plot' ? '剧情节点' :
                             selectedNodeInfo.type === 'synopsis' ? '核心故事梗概' : '对话设计'}
                          </span>
                        </div>

                        {selectedNodeInfo.description && (
                          <div className="prose prose-sm max-w-none">
                            <p className="text-gray-700 leading-relaxed text-base">
                              {selectedNodeInfo.description}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 编辑按钮 */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => handleStartEdit(selectedNodeInfo)}
                        className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 font-medium"
                      >
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        <span>编辑节点</span>
                      </button>
                    </div>
                  </div>

                  {/* 节点统计信息 - 更详细的展示 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-gray-700 mb-3">基础信息</h6>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>节点ID:</span>
                          <span className="font-mono text-xs">{selectedNodeInfo.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>节点类型:</span>
                          <span className="capitalize">{selectedNodeInfo.type}</span>
                        </div>
                        {selectedNodeInfo.description && (
                          <div className="flex justify-between">
                            <span>描述长度:</span>
                            <span>{selectedNodeInfo.description.length} 字符</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-blue-700 mb-3">选择状态</h6>
                      <div className="space-y-2 text-sm text-blue-600">
                        <div className="flex justify-between">
                          <span>是否选中:</span>
                          <span className="font-medium">
                            {selectionState.selectedNodeIds.has(selectedNodeInfo.id) ? '✓ 已选中' : '○ 未选中'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>层级深度:</span>
                          <span>第 {(selectedNodeInfo as any).level + 1} 层</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 内容预览区域 - 新增功能 */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                    <h6 className="text-sm font-semibold text-gray-700 mb-3">内容预览</h6>
                    <div className="text-sm text-gray-600">
                      {selectedNodeInfo.description ? (
                        <div className="space-y-2">
                          <p>节点描述已加载，包含 {selectedNodeInfo.description.length} 个字符。</p>
                          <div className="bg-white rounded p-3 border-l-4 border-blue-400">
                            <p className="italic">"{selectedNodeInfo.description.substring(0, 100)}
                            {selectedNodeInfo.description.length > 100 ? '...' : ''}"</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">该节点暂无描述内容</p>
                      )}
                    </div>
                  </div>

                  {/* 创作建议区域 - 新增功能 */}
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <h6 className="text-sm font-semibold text-amber-800 mb-3 flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                      创作建议
                    </h6>
                    <div className="text-sm text-amber-700">
                      {(selectedNodeInfo as any).creativeNotes ? (
                        <div className="space-y-2">
                          <div className="bg-white rounded p-3 border-l-4 border-amber-400">
                            <p className="whitespace-pre-wrap">{(selectedNodeInfo as any).creativeNotes}</p>
                          </div>
                          <p className="text-xs text-amber-600">
                            💡 这些建议将在AI创作时作为重要参考
                          </p>
                        </div>
                      ) : (
                        <div className="text-amber-600 italic flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          暂无创作建议
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 章节专有字段显示 */}
                  {selectedNodeInfo.type === 'chapter' && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-blue-800 mb-3 flex items-center">
                        📖 章节专有字段
                      </h6>
                      <div className="space-y-3 text-sm">
                        {selectedNodeInfo.chapterStyle && (
                          <div>
                            <span className="font-medium text-blue-700">写作风格：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-blue-400">
                              {selectedNodeInfo.chapterStyle}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.chapterTechniques && selectedNodeInfo.chapterTechniques.length > 0 && (
                          <div>
                            <span className="font-medium text-blue-700">写作手法：</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {selectedNodeInfo.chapterTechniques.map((technique, index) => (
                                <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                                  {technique}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.chapterGoals && (
                          <div>
                            <span className="font-medium text-blue-700">章节目标：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-blue-400">
                              {selectedNodeInfo.chapterGoals}
                            </div>
                          </div>
                        )}
                        {!selectedNodeInfo.chapterStyle && !selectedNodeInfo.chapterTechniques?.length && !selectedNodeInfo.chapterGoals && (
                          <div className="text-blue-600 italic">暂无章节专有字段内容</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 剧情节点专有字段显示 */}
                  {selectedNodeInfo.type === 'plot' && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-green-800 mb-3 flex items-center">
                        🎭 剧情节点专有字段
                      </h6>
                      <div className="space-y-3 text-sm">
                        {selectedNodeInfo.plotType && (
                          <div>
                            <span className="font-medium text-green-700">剧情类型：</span>
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                              {selectedNodeInfo.plotType === 'conflict' ? '冲突' :
                               selectedNodeInfo.plotType === 'twist' ? '转折' :
                               selectedNodeInfo.plotType === 'climax' ? '高潮' :
                               selectedNodeInfo.plotType === 'resolution' ? '解决' : selectedNodeInfo.plotType}
                            </span>
                          </div>
                        )}
                        {selectedNodeInfo.relatedCharacters && selectedNodeInfo.relatedCharacters.length > 0 && (
                          <div>
                            <span className="font-medium text-green-700">关联角色：</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {selectedNodeInfo.relatedCharacters.map((character, index) => (
                                <span key={index} className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                                  {character}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.plotPoints && selectedNodeInfo.plotPoints.length > 0 && (
                          <div>
                            <span className="font-medium text-green-700">剧情点：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-green-400">
                              <div className="space-y-3">
                                {selectedNodeInfo.plotPoints.map((point, index) => (
                                  <div key={index} className="border-b border-gray-100 pb-2 last:border-b-0">
                                    <div className="flex items-start space-x-2">
                                      <span className="flex-shrink-0 w-5 h-5 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                                        {index + 1}
                                      </span>
                                      <div className="flex-1">
                                        <div className="text-sm text-gray-800 mb-1">
                                          {typeof point === 'string' ? point :
                                           typeof point === 'object' && point?.content ? point.content :
                                           typeof point === 'object' ? JSON.stringify(point) : String(point)}
                                        </div>
                                        {typeof point === 'object' && point?.avoidWriting && (
                                          <div className="text-xs text-gray-600 bg-red-50 border border-red-200 rounded px-2 py-1 mb-1">
                                            <span className="font-medium text-red-700">🚫 避免描写：</span>
                                            {point.avoidWriting}
                                          </div>
                                        )}
                                        {typeof point === 'object' && point?.shouldWriting && (
                                          <div className="text-xs text-gray-600 bg-green-50 border border-green-200 rounded px-2 py-1 mb-1">
                                            <span className="font-medium text-green-700">✅ 应该描写：</span>
                                            {point.shouldWriting}
                                          </div>
                                        )}
                                        {typeof point === 'object' && point?.styleMethod && (
                                          <div className="text-xs text-gray-600 bg-blue-50 border border-blue-200 rounded px-2 py-1 mb-1">
                                            <span className="font-medium text-blue-700">🎨 写作风格：</span>
                                            {point.styleMethod.technique} | {point.styleMethod.style} | {point.styleMethod.tone}
                                            <br />
                                            <span className="font-medium text-blue-700">📐 视角重点：</span>
                                            {point.styleMethod.perspective} | {point.styleMethod.emphasis}
                                          </div>
                                        )}
                                        {typeof point === 'object' && point?.formatSpecs && (
                                          <div className="text-xs text-gray-600 bg-green-50 border border-green-200 rounded px-2 py-1">
                                            <span className="font-medium text-green-700">📏 字数要求：</span>
                                            {point.formatSpecs.wordCount?.min}-{point.formatSpecs.wordCount?.max}字（目标{point.formatSpecs.wordCount?.target}字）
                                            <br />
                                            <span className="font-medium text-green-700">📝 格式规范：</span>
                                            {point.formatSpecs.paragraphRules?.paragraphBreakRules} | {point.formatSpecs.punctuationRules?.dialogueFormat}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                        {!selectedNodeInfo.plotType && !selectedNodeInfo.relatedCharacters?.length && !selectedNodeInfo.plotPoints?.length && (
                          <div className="text-green-600 italic">暂无剧情节点专有字段内容</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 对话设计专有字段显示 */}
                  {selectedNodeInfo.type === 'dialogue' && (
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-purple-800 mb-3 flex items-center">
                        💬 对话设计专有字段
                      </h6>
                      <div className="space-y-3 text-sm">
                        {selectedNodeInfo.dialogueScene && (
                          <div>
                            <span className="font-medium text-purple-700">对话场景：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-purple-400">
                              {selectedNodeInfo.dialogueScene}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.participants && selectedNodeInfo.participants.length > 0 && (
                          <div>
                            <span className="font-medium text-purple-700">参与角色：</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {selectedNodeInfo.participants.map((participant, index) => {
                                // 安全处理participant，确保它是可渲染的字符串
                                const displayParticipant = typeof participant === 'string'
                                  ? participant
                                  : typeof participant === 'object' && participant !== null
                                    ? (participant as any).name || (participant as any).title || JSON.stringify(participant)
                                    : String(participant);

                                return (
                                  <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                                    {displayParticipant}
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.dialoguePurpose && (
                          <div>
                            <span className="font-medium text-purple-700">对话目的：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-purple-400">
                              {selectedNodeInfo.dialoguePurpose}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.dialogueContent && selectedNodeInfo.dialogueContent.length > 0 && (
                          <div>
                            <span className="font-medium text-purple-700">对话内容：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-purple-400">
                              <div className="space-y-2">
                                {selectedNodeInfo.dialogueContent.map((content, index) => (
                                  <div key={index} className="text-sm">
                                    {typeof content === 'string' ? content :
                                     typeof content === 'object' && content?.speaker && content?.text ?
                                       `${content.speaker}: ${content.text}` :
                                     typeof content === 'object' && content?.content ? content.content :
                                     typeof content === 'object' ? JSON.stringify(content) : String(content)}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                        {!selectedNodeInfo.dialogueScene && !selectedNodeInfo.participants?.length && !selectedNodeInfo.dialoguePurpose && !selectedNodeInfo.dialogueContent?.length && (
                          <div className="text-purple-600 italic">暂无对话设计专有字段内容</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 核心故事梗概专有字段显示 */}
                  {selectedNodeInfo.type === 'synopsis' && (
                    <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                      <h6 className="text-sm font-semibold text-indigo-800 mb-3 flex items-center">
                        📖 核心故事梗概专有字段
                      </h6>
                      <div className="space-y-3 text-sm">
                        {selectedNodeInfo.synopsisBrainhole && (
                          <div>
                            <span className="font-medium text-indigo-700">脑洞：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-indigo-400">
                              {selectedNodeInfo.synopsisBrainhole}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.synopsisGenre && (
                          <div>
                            <span className="font-medium text-indigo-700">类型：</span>
                            <span className="ml-2 px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs">
                              {selectedNodeInfo.synopsisGenre}
                            </span>
                          </div>
                        )}
                        {selectedNodeInfo.synopsisCoreOutline && (
                          <div>
                            <span className="font-medium text-indigo-700">梗概：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-indigo-400">
                              {selectedNodeInfo.synopsisCoreOutline}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.synopsisStoryDescription && (
                          <div>
                            <span className="font-medium text-indigo-700">故事：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-indigo-400">
                              {selectedNodeInfo.synopsisStoryDescription}
                            </div>
                          </div>
                        )}
                        {selectedNodeInfo.synopsisAceReferences && (
                          <div>
                            <span className="font-medium text-indigo-700">引用：</span>
                            <div className="mt-1 bg-white rounded p-2 border-l-4 border-indigo-400">
                              {selectedNodeInfo.synopsisAceReferences}
                            </div>
                          </div>
                        )}
                        {!selectedNodeInfo.synopsisBrainhole && !selectedNodeInfo.synopsisGenre &&
                         !selectedNodeInfo.synopsisCoreOutline && !selectedNodeInfo.synopsisStoryDescription &&
                         !selectedNodeInfo.synopsisAceReferences && (
                          <div className="text-indigo-600 italic">暂无核心故事梗概专有字段内容</div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <div className="p-6 bg-gray-100 rounded-full mb-6">
                <svg className="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h5 className="text-xl font-semibold text-gray-700 mb-3">选择节点查看详情</h5>
              <p className="text-sm text-center text-gray-500 max-w-md leading-relaxed">
                点击左侧任意节点，这里将显示该节点的详细信息、内容预览和相关统计数据
              </p>
              <div className="mt-8 flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>支持多选节点</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <span>支持搜索过滤</span>
                </div>
              </div>
            </div>
          )}
        </div>
        </div>
      )}
    </div>
  );
};

export default OutlineTreeSelector;
