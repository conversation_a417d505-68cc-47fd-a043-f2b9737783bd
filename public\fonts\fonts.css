/* 字体定义 */

/* 衬线字体 - Georgia */
@font-face {
  font-family: 'Georgia';
  src: local('Georgia');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Georgia';
  src: local('Georgia Bold');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Georgia';
  src: local('Georgia Italic');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

/* 无衬线字体 - Roboto */
@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 无衬线字体 - Inter */
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Bold.woff2') format('woff2');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* 思源字体 - Source Han Sans SC */
@font-face {
  font-family: 'Source Han Sans SC';
  src: local('Source Han Sans SC Regular'),
       local('SourceHanSansSC-Regular'),
       local('Noto Sans CJK SC Regular'),
       local('NotoSansCJKsc-Regular');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Han Sans SC';
  src: local('Source Han Sans SC Medium'),
       local('SourceHanSansSC-Medium'),
       local('Noto Sans CJK SC Medium'),
       local('NotoSansCJKsc-Medium');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Han Sans SC';
  src: local('Source Han Sans SC Bold'),
       local('SourceHanSansSC-Bold'),
       local('Noto Sans CJK SC Bold'),
       local('NotoSansCJKsc-Bold');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* 等宽字体 - Courier New */
@font-face {
  font-family: 'Courier New';
  src: local('Courier New');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Courier New';
  src: local('Courier New Bold');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* 字体预设 */
.font-serif {
  font-family: Georgia, 'Times New Roman', serif;
}

.font-sans {
  font-family: Roboto, Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.font-roboto {
  font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.font-inter {
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.font-siyuan {
  font-family: 'Source Han Sans SC', 'Noto Sans CJK SC', 'Microsoft YaHei', sans-serif;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

/* 字体平滑过渡 */
body, input, textarea, button {
  transition: font-family 0.2s ease-in-out;
}
