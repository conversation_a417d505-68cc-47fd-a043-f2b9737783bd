"use client";

import React, { useEffect, useRef } from 'react';
import { Book } from '@/db';
import { createPortal } from 'react-dom';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  book: Book | null;
  onClose: () => void;
  onConfirm: () => void;
}

/**
 * 删除确认对话框组件
 */
const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  book,
  onClose,
  onConfirm
}) => {
  // 创建对话框引用
  const confirmButtonRef = useRef<HTMLButtonElement>(null);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'Enter') {
        onConfirm();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, onConfirm]);

  // 当对话框打开时，聚焦到确认按钮
  useEffect(() => {
    if (isOpen && confirmButtonRef.current) {
      confirmButtonRef.current.focus();
    }
  }, [isOpen]);

  // 如果对话框未打开，不渲染任何内容
  if (!isOpen) return null;

  // 使用 Portal 将对话框渲染到 body 的末尾
  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* 对话框内容 */}
      <div
        className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 z-10 overflow-hidden"
        onClick={e => e.stopPropagation()}
      >
        {/* 对话框标题 */}
        <div className="bg-red-50 px-6 py-4 border-b border-red-100">
          <h3 className="text-lg font-medium text-red-700">
            删除作品 "{book?.title || '未命名作品'}"
          </h3>
        </div>

        {/* 对话框内容 */}
        <div className="px-6 py-4">
          <p className="text-gray-700">
            确定要删除作品 "{book?.title || '未命名作品'}" 吗？此操作将删除所有相关章节和数据，且不可撤销。
          </p>
        </div>

        {/* 对话框按钮 */}
        <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            取消
          </button>
          <button
            ref={confirmButtonRef}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
            onClick={onConfirm}
          >
            确认删除
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default DeleteConfirmDialog;
