"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { PersonaCategory, CategoryPrompt } from '../../../types/ai-persona';
import PromptCard from './PromptCard';
import PromptEditDialog from './PromptEditDialog';

interface CategoryPromptManagerProps {
  isOpen: boolean;
  category: PersonaCategory | null;
  onClose: () => void;
  onSave: (prompts: CategoryPrompt[]) => void;
  onUsePrompt?: (prompt: CategoryPrompt) => void;
}

/**
 * 分类提示词管理弹窗
 * 专门管理单个分类内的提示词，支持查看、添加、编辑、删除操作
 */
const CategoryPromptManager: React.FC<CategoryPromptManagerProps> = ({
  isOpen,
  category,
  onClose,
  onSave,
  onUsePrompt
}) => {
  const [prompts, setPrompts] = useState<CategoryPrompt[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'order' | 'name' | 'created'>('order');
  const [newPromptName, setNewPromptName] = useState('');
  const [newPromptContent, setNewPromptContent] = useState('');
  const [isAddingPrompt, setIsAddingPrompt] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<CategoryPrompt | null>(null);

  // 初始化提示词列表
  useEffect(() => {
    if (category) {
      setPrompts([...category.prompts]);
    }
  }, [category]);

  // 过滤和排序提示词
  const filteredAndSortedPrompts = React.useMemo(() => {
    let filtered = prompts.filter(prompt => {
      const searchLower = searchQuery.toLowerCase();
      const name = prompt.name || '';
      const content = prompt.content || '';
      return name.toLowerCase().includes(searchLower) ||
             content.toLowerCase().includes(searchLower);
    });

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          const nameA = a.name || a.content.substring(0, 20);
          const nameB = b.name || b.content.substring(0, 20);
          return nameA.localeCompare(nameB);
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'order':
        default:
          return a.order - b.order;
      }
    });

    return filtered;
  }, [prompts, searchQuery, sortBy]);

  // 添加新提示词
  const handleAddPrompt = () => {
    if (!newPromptContent.trim()) return;

    const newPrompt: CategoryPrompt = {
      id: `prompt-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      name: newPromptName.trim() || undefined,
      content: newPromptContent.trim(),
      order: prompts.length,
      createdAt: new Date()
    };

    setPrompts(prev => [...prev, newPrompt]);
    setNewPromptName('');
    setNewPromptContent('');
    setIsAddingPrompt(false);
  };

  // 打开编辑提示词弹窗
  const handleEditPrompt = (prompt: CategoryPrompt) => {
    setEditingPrompt(prompt);
    setShowEditDialog(true);
  };

  // 保存编辑的提示词
  const handleSaveEditedPrompt = (updatedPrompt: CategoryPrompt) => {
    setPrompts(prev => prev.map(prompt =>
      prompt.id === updatedPrompt.id ? updatedPrompt : prompt
    ));
    setShowEditDialog(false);
    setEditingPrompt(null);
  };

  // 关闭编辑弹窗
  const handleCloseEditDialog = () => {
    setShowEditDialog(false);
    setEditingPrompt(null);
  };

  // 删除提示词
  const handleDeletePrompt = (promptId: string) => {
    setPrompts(prev => prev.filter(prompt => prompt.id !== promptId));
  };

  // 使用提示词（可选功能）
  const handleUsePrompt = (prompt: CategoryPrompt) => {
    if (onUsePrompt) {
      onUsePrompt(prompt);
    } else {
      // 默认行为：复制到剪贴板
      const textToCopy = prompt.content;
      navigator.clipboard.writeText(textToCopy).then(() => {
        console.log('提示词已复制到剪贴板:', prompt);
        // 这里可以添加一个toast提示
      }).catch(err => {
        console.error('复制失败:', err);
      });
    }
  };

  // 保存更改
  const handleSave = () => {
    // 重新排序
    const reorderedPrompts = prompts.map((prompt, index) => ({
      ...prompt,
      order: index
    }));

    onSave(reorderedPrompts);
    onClose();
  };

  // 取消更改
  const handleCancel = () => {
    if (category) {
      setPrompts([...category.prompts]);
    }
    setSearchQuery('');
    setNewPromptName('');
    setNewPromptContent('');
    setIsAddingPrompt(false);
    onClose();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      if (isAddingPrompt) {
        setIsAddingPrompt(false);
        setNewPromptName('');
        setNewPromptContent('');
      } else {
        handleCancel();
      }
    }
  };

  if (!isOpen || !category) return null;

  return createPortal(
    <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <motion.div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleCancel}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      />

      {/* 弹窗内容 */}
      <motion.div
        className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-5xl max-h-[85vh] flex flex-col"
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              管理提示词 - {category.name}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              当前分类共有 {prompts.length} 个提示词
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和操作栏 */}
        <div className="p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-3 mb-3">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="搜索提示词名称或内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
              <svg className="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            >
              <option value="order">按顺序排序</option>
              <option value="name">按名称排序</option>
              <option value="created">按创建时间排序</option>
            </select>

            <button
              onClick={() => setIsAddingPrompt(true)}
              className="px-4 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span>添加提示词</span>
            </button>
          </div>

          <div className="text-xs text-gray-500 dark:text-gray-400">
            显示 {filteredAndSortedPrompts.length} / {prompts.length} 个提示词
          </div>
        </div>

        {/* 添加提示词表单 */}
        <AnimatePresence>
          {isAddingPrompt && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-800"
            >
              <div className="p-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    提示词名称（可选）
                  </label>
                  <input
                    type="text"
                    value={newPromptName}
                    onChange={(e) => setNewPromptName(e.target.value)}
                    placeholder="输入提示词名称..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    提示词内容 *
                  </label>
                  <textarea
                    value={newPromptContent}
                    onChange={(e) => setNewPromptContent(e.target.value)}
                    placeholder="输入提示词内容..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
                  />
                </div>

                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => {
                      setIsAddingPrompt(false);
                      setNewPromptName('');
                      setNewPromptContent('');
                    }}
                    className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleAddPrompt}
                    disabled={!newPromptContent.trim()}
                    className="px-3 py-1.5 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    添加
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 提示词列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredAndSortedPrompts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <AnimatePresence>
                {filteredAndSortedPrompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    prompt={prompt}
                    onEdit={handleEditPrompt}
                    onDelete={handleDeletePrompt}
                    onUse={handleUsePrompt}
                  />
                ))}
              </AnimatePresence>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
              <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-lg font-medium mb-2">
                {searchQuery ? '没有找到匹配的提示词' : '暂无提示词'}
              </p>
              <p className="text-sm">
                {searchQuery ? '尝试调整搜索条件' : '点击"添加提示词"按钮创建第一个提示词'}
              </p>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            共 {prompts.length} 个提示词
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors"
            >
              保存更改
            </button>
          </div>
        </div>
      </motion.div>

      {/* 提示词编辑弹窗 */}
      <PromptEditDialog
        isOpen={showEditDialog}
        prompt={editingPrompt}
        onClose={handleCloseEditDialog}
        onSave={handleSaveEditedPrompt}
      />
    </div>,
    document.body
  );
};

export default CategoryPromptManager;
