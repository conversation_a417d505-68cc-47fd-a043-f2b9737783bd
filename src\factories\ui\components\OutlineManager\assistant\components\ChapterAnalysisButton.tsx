/**
 * 章节分析按钮组件
 * 用于在AI对话界面中添加"分析章节内容"功能
 */

import React, { useState } from 'react';
import { ChapterAnalysisDialog } from './ChapterAnalysisDialog';
import { ExampleInjectionManager, ExampleConfig } from '../services/ExampleInjectionManager';

interface ChapterAnalysisButtonProps {
  availableChapters?: any[];
  aceFrameworks?: any[];
  apiKey?: string;
  baseURL?: string;
  onExampleActivated?: (example: ExampleConfig) => void;
}

export const ChapterAnalysisButton: React.FC<ChapterAnalysisButtonProps> = ({
  availableChapters = [],
  aceFrameworks = [],
  apiKey = '',
  baseURL = 'https://api.openai.com/v1',
  onExampleActivated
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeExample, setActiveExample] = useState<ExampleConfig | null>(null);

  // 检查当前激活的示例
  React.useEffect(() => {
    const currentActive = ExampleInjectionManager.getActiveExample();
    setActiveExample(currentActive);
  }, []);

  // 处理示例生成完成
  const handleExampleGenerated = (example: ExampleConfig) => {
    setActiveExample(example);
    onExampleActivated?.(example);
  };

  // 激活示例
  const handleActivateExample = (exampleId: string) => {
    const success = ExampleInjectionManager.activateExample(exampleId);
    if (success) {
      const newActive = ExampleInjectionManager.getActiveExample();
      setActiveExample(newActive);
      onExampleActivated?.(newActive!);
    }
  };

  // 停用示例
  const handleDeactivateExample = () => {
    const success = ExampleInjectionManager.deactivateCurrentExample();
    if (success) {
      setActiveExample(null);
      onExampleActivated?.(null as any);
    }
  };

  // 获取所有示例
  const allExamples = ExampleInjectionManager.getAllExamples();

  return (
    <div className="flex flex-col gap-2">
      {/* 主要按钮 */}
      <button
        onClick={() => setIsDialogOpen(true)}
        className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        title="分析章节内容并生成JSON示例"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        分析章节内容
      </button>

      {/* 当前激活的示例状态 */}
      {activeExample && (
        <div className="bg-green-50 border border-green-200 rounded p-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-green-800">当前激活示例</h4>
              <p className="text-sm text-green-600">{activeExample.name}</p>
              <p className="text-xs text-green-500">
                {activeExample.plotPoints.length}个剧情点 | {activeExample.overallStyle}
              </p>
            </div>
            <button
              onClick={handleDeactivateExample}
              className="text-green-600 hover:text-green-800"
              title="停用当前示例"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* 示例管理 */}
      {allExamples.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded p-3">
          <h4 className="font-medium text-gray-800 mb-2">已保存的示例 ({allExamples.length})</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {allExamples.map((example) => (
              <div
                key={example.id}
                className={`flex items-center justify-between p-2 rounded text-sm ${
                  example.isActive 
                    ? 'bg-blue-100 border border-blue-300' 
                    : 'bg-white border border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex-1">
                  <p className="font-medium">{example.name}</p>
                  <p className="text-xs text-gray-500">
                    {example.createdAt.toLocaleDateString()} | {example.plotPoints.length}个剧情点
                  </p>
                </div>
                <div className="flex gap-1">
                  {!example.isActive && (
                    <button
                      onClick={() => handleActivateExample(example.id)}
                      className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                      title="激活此示例"
                    >
                      激活
                    </button>
                  )}
                  <button
                    onClick={() => ExampleInjectionManager.deleteExample(example.id)}
                    className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                    title="删除此示例"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 分析对话框 */}
      <ChapterAnalysisDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onExampleGenerated={handleExampleGenerated}
        availableChapters={availableChapters}
        aceFrameworks={aceFrameworks}
        apiKey={apiKey}
        baseURL={baseURL}
      />
    </div>
  );
};
