"use client";

import React from 'react';

interface AIRewriteMiddlePanelProps {
  selectedText: string;
  beforeContext: string;
  afterContext: string;
  onSelectedTextChange: (text: string) => void;
}

/**
 * AI改写中间面板组件
 * 用于显示选中的文本和上下文
 */
const AIRewriteMiddlePanel: React.FC<AIRewriteMiddlePanelProps> = ({
  selectedText,
  beforeContext,
  afterContext,
  onSelectedTextChange
}) => {
  return (
    <div className="w-1/3 px-4 overflow-y-auto flex flex-col h-full">
      <div className="space-y-4 flex flex-col flex-1">
        {/* 上文 */}
        {beforeContext && (
          <div className="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-2">上文</h3>
            <div className="prose prose-sm max-w-none text-gray-600 whitespace-pre-wrap max-h-32 overflow-auto">
              {beforeContext}
            </div>
          </div>
        )}

        {/* 选中文本 - 使用flex-1使其占据剩余空间 */}
        <div className="bg-blue-50 p-4 rounded-xl border border-blue-200 shadow-sm flex-1 flex flex-col">
          <h3 className="text-base font-medium text-blue-800 mb-2">选中文本</h3>
          <textarea
            value={selectedText}
            onChange={(e) => onSelectedTextChange(e.target.value)}
            className="w-full px-3 py-2 border border-blue-200 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white flex-1"
            placeholder="请选择要改写的文本..."
            style={{ minHeight: '200px' }} // 设置最小高度
          />
        </div>

        {/* 下文 */}
        {afterContext && (
          <div className="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-2">下文</h3>
            <div className="prose prose-sm max-w-none text-gray-600 whitespace-pre-wrap max-h-32 overflow-auto">
              {afterContext}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIRewriteMiddlePanel;
