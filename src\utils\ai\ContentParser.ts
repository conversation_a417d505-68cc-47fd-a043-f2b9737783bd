/**
 * 内容解析器
 * 
 * 负责解析上下文内容，识别章节信息和内容类型
 * 将复杂的上下文内容转换为结构化数据
 */

export interface ParsedContent {
  sourceChapter: number;      // 内容来源章节号（1基索引）
  contentType: 'before' | 'after' | 'cross' | 'current'; // 内容类型
  originalTitle: string;      // 原始标题
  content: string;           // 内容文本
  isCurrentChapter: boolean; // 是否为当前章节内容
}

export class ContentParser {
  /**
   * 解析上下文内容
   * 
   * @param contextContent 原始上下文内容
   * @param currentChapter 当前章节号（1基索引）
   * @returns 解析后的内容数组
   */
  parseContextContent(contextContent: string, currentChapter: number): ParsedContent[] {
    if (!contextContent || !contextContent.trim()) {
      return [];
    }

    // 按章节标记分割内容，保留分隔符
    const sections = contextContent.split(/(【[^】]+】)/);
    const parsedContents: ParsedContent[] = [];

    let currentTitle = '';
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];

      if (section.startsWith('【') && section.endsWith('】')) {
        // 这是一个标题
        currentTitle = section.slice(1, -1); // 去掉【】
      } else if (section.trim() && currentTitle) {
        // 这是内容
        const parsed = this.parseSection(currentTitle, section.trim(), currentChapter);
        if (parsed) {
          parsedContents.push(parsed);
        }
        currentTitle = ''; // 重置标题
      }
    }

    return parsedContents;
  }

  /**
   * 解析单个章节内容
   * 
   * @param title 章节标题
   * @param content 章节内容
   * @param currentChapter 当前章节号
   * @returns 解析后的内容对象
   */
  private parseSection(title: string, content: string, currentChapter: number): ParsedContent | null {
    if (!content.trim()) {
      return null;
    }

    // 提取章节号
    const sourceChapter = this.extractChapterNumber(title);
    
    // 判断内容类型
    const contentType = this.determineContentType(title);
    
    // 判断是否为当前章节
    const isCurrentChapter = sourceChapter === currentChapter || sourceChapter === 0;

    return {
      sourceChapter: sourceChapter || currentChapter, // 如果没有提取到章节号，使用当前章节
      contentType,
      originalTitle: title,
      content,
      isCurrentChapter
    };
  }

  /**
   * 从标题中提取章节号
   * 
   * @param title 标题字符串
   * @returns 章节号（1基索引），如果未找到返回0
   */
  private extractChapterNumber(title: string): number {
    const match = title.match(/第(\d+)章/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * 判断内容类型
   * 
   * @param title 标题字符串
   * @returns 内容类型
   */
  private determineContentType(title: string): 'before' | 'after' | 'cross' | 'current' {
    // 跨章节内容的判断
    if (this.isCrossChapterContent(title)) {
      return 'cross';
    }

    // 前文内容的判断
    if (this.isBeforeContent(title)) {
      return 'before';
    }

    // 后文内容的判断
    if (this.isAfterContent(title)) {
      return 'after';
    }

    // 默认为当前内容
    return 'current';
  }

  /**
   * 判断是否为跨章节内容
   * 
   * @param title 标题字符串
   * @returns 是否为跨章节内容
   */
  private isCrossChapterContent(title: string): boolean {
    return (
      title.includes('前文章节') ||
      title.includes('后文章节') ||
      title.includes('完整内容') ||
      title.includes('的完整内容') ||
      (title.includes('第') && title.includes('章') && !title.includes('前文') && !title.includes('后文'))
    );
  }

  /**
   * 判断是否为前文内容
   * 
   * @param title 标题字符串
   * @returns 是否为前文内容
   */
  private isBeforeContent(title: string): boolean {
    return (
      title.includes('前文') && 
      !title.includes('前文章节') &&
      !title.includes('后文')
    );
  }

  /**
   * 判断是否为后文内容
   * 
   * @param title 标题字符串
   * @returns 是否为后文内容
   */
  private isAfterContent(title: string): boolean {
    return (
      title.includes('后文') && 
      !title.includes('后文章节') &&
      !title.includes('前文')
    );
  }

  /**
   * 将内容按句子分段
   *
   * @param content 原始内容
   * @param sentencesPerSegment 每段句子数，默认10句
   * @returns 分段后的内容数组
   */
  segmentContent(content: string, sentencesPerSegment: number = 10): string[] {
    if (!content || !content.trim()) {
      return [];
    }

    // 预处理：统一换行符
    const normalizedContent = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // 按句号、感叹号、问号分割句子
    // 修复：移除 \s* 以保留换行符和空白字符
    const sentences = normalizedContent.split(/(?<=[。！？.!?])/).filter(s => s.trim());
    const segments: string[] = [];

    for (let i = 0; i < sentences.length; i += sentencesPerSegment) {
      const segmentSentences = sentences.slice(i, i + sentencesPerSegment);
      // 修复：直接连接句子，保持原有换行结构
      let segmentText = segmentSentences.join('').trim();

      // 规范化换行：将多个连续换行符替换为双换行符
      segmentText = segmentText.replace(/\n{3,}/g, '\n\n');

      if (segmentText) {
        segments.push(segmentText);
      }
    }

    // 如果没有分割出任何段落，回退到原始内容
    if (segments.length === 0 && normalizedContent.trim()) {
      segments.push(normalizedContent.trim());
    }

    return segments;
  }

  /**
   * 按类型分组内容
   * 
   * @param parsedContents 解析后的内容数组
   * @returns 按类型分组的内容
   */
  groupContentsByType(parsedContents: ParsedContent[]): {
    beforeContents: ParsedContent[];
    afterContents: ParsedContent[];
    crossContents: ParsedContent[];
    currentContents: ParsedContent[];
  } {
    return {
      beforeContents: parsedContents.filter(p => p.contentType === 'before'),
      afterContents: parsedContents.filter(p => p.contentType === 'after'),
      crossContents: parsedContents.filter(p => p.contentType === 'cross'),
      currentContents: parsedContents.filter(p => p.contentType === 'current')
    };
  }

  /**
   * 验证解析结果
   * 
   * @param parsedContents 解析后的内容数组
   * @returns 验证结果
   */
  validateParsedContents(parsedContents: ParsedContent[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const content of parsedContents) {
      // 检查必要字段
      if (!content.content || !content.content.trim()) {
        errors.push(`内容为空: ${content.originalTitle}`);
      }

      if (!content.originalTitle) {
        errors.push('标题为空');
      }

      // 检查章节号的合理性
      if (content.sourceChapter <= 0) {
        warnings.push(`章节号异常: ${content.originalTitle} (章节号: ${content.sourceChapter})`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
