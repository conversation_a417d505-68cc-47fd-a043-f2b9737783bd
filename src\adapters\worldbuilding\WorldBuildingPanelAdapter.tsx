"use client";

import React from 'react';
import { WorldBuilding } from '@/lib/db/dexie';
import { worldBuildingRepository } from '@/lib/db/repositories';
import { WorldBuildingPanelComponent } from '@/factories/ui/components/WorldBuildingPanel';

interface WorldBuildingPanelAdapterProps {
  bookId: string;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

/**
 * 世界观面板适配器组件
 * 用于将世界观面板工厂组件集成到React应用中
 */
export const WorldBuildingPanelAdapter: React.FC<WorldBuildingPanelAdapterProps> = ({
  bookId,
  isOpen,
  onClose,
  className
}) => {
  // 创建世界观面板组件实例
  const worldBuildingPanelComponent = new WorldBuildingPanelComponent();

  // 设置属性
  worldBuildingPanelComponent.setBookId(bookId);
  worldBuildingPanelComponent.setIsOpen(isOpen);
  worldBuildingPanelComponent.onClose(onClose);
  if (className) worldBuildingPanelComponent.setClassName(className);

  // 设置回调函数
  worldBuildingPanelComponent.onCreate(async (worldBuilding: WorldBuilding) => {
    try {
      console.log('WorldBuildingPanelAdapter - 创建世界观:', worldBuilding);

      // 确保所有必要字段都存在
      const worldBuildingToCreate = {
        ...worldBuilding,
        bookId: worldBuilding.bookId || bookId,
        createdAt: worldBuilding.createdAt || new Date(),
        updatedAt: worldBuilding.updatedAt || new Date(),
        extractedFromChapterIds: worldBuilding.extractedFromChapterIds || [],
        relatedCharacterIds: worldBuilding.relatedCharacterIds || [],
        relatedTerminologyIds: worldBuilding.relatedTerminologyIds || [],
        relatedWorldBuildingIds: worldBuilding.relatedWorldBuildingIds || []
      };

      // 创建世界观
      const id = await worldBuildingRepository.create(worldBuildingToCreate);
      console.log('世界观创建成功:', worldBuilding.name, '新ID:', id);

      // 不再刷新页面，依赖组件内部状态更新
    } catch (error) {
      console.error('创建世界观失败:', error);
      alert('创建世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  worldBuildingPanelComponent.onUpdate(async (worldBuilding: WorldBuilding) => {
    try {
      console.log('WorldBuildingPanelAdapter - 更新世界观:', worldBuilding);

      // 更新世界观
      if (worldBuilding.id) {
        // 确保更新时间
        const worldBuildingToUpdate = {
          ...worldBuilding,
          updatedAt: new Date()
        };

        await worldBuildingRepository.update(worldBuilding.id, worldBuildingToUpdate);
        console.log('世界观更新成功:', worldBuilding.name);

        // 不再刷新页面，依赖组件内部状态更新
      } else {
        console.error('更新世界观失败: 缺少ID');
        alert('更新世界观失败: 缺少ID');
      }
    } catch (error) {
      console.error('更新世界观失败:', error);
      alert('更新世界观失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  });

  worldBuildingPanelComponent.onDelete(async (worldBuildingId: string) => {
    try {
      // 删除世界观
      await worldBuildingRepository.delete(worldBuildingId);
      console.log('世界观删除成功:', worldBuildingId);
    } catch (error) {
      console.error('删除世界观失败:', error);
    }
  });

  return <>{worldBuildingPanelComponent.render()}</>;
};
