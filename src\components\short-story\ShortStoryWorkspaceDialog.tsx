"use client";

import React from 'react';
import { ShortStoryWorkspace } from './layout/ShortStoryWorkspace';

interface ShortStoryWorkspaceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onShortStoryGenerated: (content: string) => void;
  bookId: string;
}

/**
 * AI短篇创作工作区对话框包装器
 * 将新的三栏布局工作区包装为对话框形式，保持与原有接口的兼容性
 */
export const ShortStoryWorkspaceDialog: React.FC<ShortStoryWorkspaceDialogProps> = ({
  isOpen,
  onClose,
  onShortStoryGenerated,
  bookId
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-[98vw] h-[95vh] max-w-none flex flex-col overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-800">AI短篇创作工作区</h2>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 工作区内容 */}
        <div className="flex-1 overflow-hidden">
          <ShortStoryWorkspace
            bookId={bookId}
            onContentGenerated={onShortStoryGenerated}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
};
