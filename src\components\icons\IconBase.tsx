"use client";

import React from 'react';

export type IconSize = 'sm' | 'md' | 'lg';

export interface IconBaseProps {
  size?: IconSize;
  color?: string;
  className?: string;
  animated?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

/**
 * SVG图标基础组件
 * 提供统一的尺寸、颜色和动画支持
 */
const IconBase: React.FC<IconBaseProps> = ({
  size = 'md',
  color = 'currentColor',
  className = '',
  animated = false,
  children,
  onClick
}) => {
  const sizeMap: Record<IconSize, number> = {
    sm: 16,
    md: 24,
    lg: 32
  };

  const baseClasses = [
    'icon',
    animated ? 'icon-animated' : '',
    onClick ? 'icon-clickable' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <svg
      width={sizeMap[size]}
      height={sizeMap[size]}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={baseClasses}
      style={{ color }}
      onClick={onClick}
      role={onClick ? 'button' : 'img'}
      tabIndex={onClick ? 0 : undefined}
    >
      {children}
    </svg>
  );
};

export default IconBase;
