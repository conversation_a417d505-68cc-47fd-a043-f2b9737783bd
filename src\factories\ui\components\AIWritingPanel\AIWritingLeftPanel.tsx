"use client";

import React from 'react';
import { UnifiedAssociationButton } from '@/components/ui/UnifiedAssociationButton';
import { OutlineManagementButton } from '@/components/ui/OutlineManagementButton';
import { AssociationOverview } from '@/components/ui/AssociationOverview';

interface AIWritingLeftPanelProps {
  bookId: string; // 新增bookId属性
  selectedChapterIds: string[];
  selectedCharacterIds: string[];
  selectedTerminologyIds: string[];
  selectedWorldBuildingIds: string[];
  selectedOutlineNodeIds?: string[]; // 新增大纲节点选择
  writingStyle: string;
  requirements: string;
  corePlot: string;
  error: string | null;

  onWritingStyleChange: (value: string) => void;
  onRequirementsChange: (value: string) => void;
  onCorePlotChange: (value: string) => void;
  onOpenStyleTemplates: () => void;
  onOpenRequirementsTemplates: () => void;
  // 新增统一关联管理回调
  onAssociationsChange?: (associations: {
    chapterIds: string[];
    characterIds: string[];
    terminologyIds: string[];
    worldBuildingIds: string[];
  }) => void;
  // 新增大纲节点管理回调
  onOutlineNodesChange?: (nodeIds: string[]) => void;
}

/**
 * AI写作左侧面板组件
 * 包含关联元素和提示词输入
 */
const AIWritingLeftPanel: React.FC<AIWritingLeftPanelProps> = ({
  bookId,
  selectedChapterIds,
  selectedCharacterIds,
  selectedTerminologyIds,
  selectedWorldBuildingIds,
  selectedOutlineNodeIds = [],
  writingStyle,
  requirements,
  corePlot,
  error,

  onWritingStyleChange,
  onRequirementsChange,
  onCorePlotChange,
  onOpenStyleTemplates,
  onOpenRequirementsTemplates,
  onAssociationsChange,
  onOutlineNodesChange
}) => {
  return (
    <div className="w-2/5 pr-5 overflow-y-auto">
      <div className="space-y-5">
        {/* 关联元素选择区域 */}
        <div className="bg-gray-50 p-4 rounded-xl border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-gray-800">关联元素</h3>
            {/* 统一关联管理按钮 */}
            <UnifiedAssociationButton
              bookId={bookId}
              selectedChapterIds={selectedChapterIds}
              selectedCharacterIds={selectedCharacterIds}
              selectedTerminologyIds={selectedTerminologyIds}
              selectedWorldBuildingIds={selectedWorldBuildingIds}
              onAssociationsChange={onAssociationsChange}
              variant="compact"
              className="text-xs"
            />
          </div>
          {/* 关联内容概览 */}
          <AssociationOverview
            chapterCount={selectedChapterIds.length}
            characterCount={selectedCharacterIds.length}
            terminologyCount={selectedTerminologyIds.length}
            worldBuildingCount={selectedWorldBuildingIds.length}
          />

          {/* 操作提示 */}
          <div className="mt-3 text-xs text-gray-500 text-center">
            💡 点击"统一管理"按钮来选择和管理所有类型的关联内容
          </div>
        </div>

        {/* 写作风格 */}
        <div className="bg-blue-50 p-4 rounded-xl border border-blue-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-blue-800">写作风格</h3>
            <button
              type="button"
              onClick={onOpenStyleTemplates}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 shadow-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              选择模板
            </button>
          </div>
          <textarea
            value={writingStyle}
            onChange={(e) => onWritingStyleChange(e.target.value)}
            className="w-full px-3 py-2 border border-blue-200 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
            rows={3}
            placeholder="描述您希望的写作风格，例如：悬疑、恐怖、浪漫、科幻等，或者特定作家的风格..."
          />
        </div>

        {/* 写作要求 */}
        <div className="bg-purple-50 p-4 rounded-xl border border-purple-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-purple-800">写作要求</h3>
            <button
              type="button"
              onClick={onOpenRequirementsTemplates}
              className="px-3 py-1 text-xs bg-purple-600 text-white rounded-lg hover:bg-purple-700 shadow-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              选择模板
            </button>
          </div>
          <textarea
            value={requirements}
            onChange={(e) => onRequirementsChange(e.target.value)}
            className="w-full px-3 py-2 border border-purple-200 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white"
            rows={3}
            placeholder="描述您对内容的具体要求，例如：字数、结构、主题、情感基调等..."
          />
        </div>

        {/* 核心剧情 */}
        <div className="bg-amber-50 p-4 rounded-xl border border-amber-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-medium text-amber-800">核心剧情</h3>
            {/* 独立大纲管理按钮 */}
            <OutlineManagementButton
              bookId={bookId}
              selectedOutlineNodeIds={selectedOutlineNodeIds}
              onOutlineNodesChange={onOutlineNodesChange}
              variant="compact"
              className="text-xs"
            />
          </div>
          <textarea
            value={corePlot}
            onChange={(e) => onCorePlotChange(e.target.value)}
            className="w-full px-3 py-2 border border-amber-200 rounded-lg shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 bg-white"
            rows={4}
            placeholder="描述您希望的核心剧情发展，例如：主角遇到的挑战、关键转折点、情节发展方向等..."
          />
          {/* 大纲节点选择状态显示 */}
          {selectedOutlineNodeIds.length > 0 ? (
            <div className="mt-2 p-3 bg-amber-100 rounded-lg border border-amber-200">
              <div className="flex items-center text-xs text-amber-700 font-medium mb-1">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                已选择 {selectedOutlineNodeIds.length} 个大纲节点
              </div>
              <div className="text-xs text-amber-600">
                📋 AI将参考这些大纲节点的结构和内容进行创作
              </div>
            </div>
          ) : (
            <div className="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
              <div className="text-xs text-gray-500 text-center">
                💡 点击"大纲管理"按钮选择相关大纲节点，让AI创作更符合故事结构
              </div>
            </div>
          )}
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm shadow-sm">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIWritingLeftPanel;
