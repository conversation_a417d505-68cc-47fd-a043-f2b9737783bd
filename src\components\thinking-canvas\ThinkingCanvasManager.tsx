"use client";

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ThinkingCanvasProvider } from '@/contexts/ThinkingCanvasContext';
import { ThinkingCanvasData } from '@/types/thinking-canvas';
import CanvasSidebar from './CanvasSidebar';
import CanvasEditor from './CanvasEditor';

interface ThinkingCanvasManagerProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: ThinkingCanvasData;
  onSave?: (data: ThinkingCanvasData) => void;
}

const ThinkingCanvasManagerContent: React.FC<ThinkingCanvasManagerProps> = ({
  isOpen,
  onClose,
  initialData,
  onSave
}) => {
  // 大小调整状态
  const [size, setSize] = useState({ width: 1000, height: 600 }); // 更小的默认尺寸
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeStartRef = useRef({ x: 0, y: 0, startWidth: 0, startHeight: 0 });

  // 拖拽状态
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef({ x: 0, y: 0, startX: 0, startY: 0 });

  // 调整大小事件处理
  const handleResizeMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;

    setIsResizing(true);
    resizeStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      startWidth: size.width,
      startHeight: size.height
    };

    e.preventDefault();
    e.stopPropagation();
  }, [size]);

  const handleResizeMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - resizeStartRef.current.x;
    const deltaY = e.clientY - resizeStartRef.current.y;

    const newWidth = Math.max(800, Math.min(1600, resizeStartRef.current.startWidth + deltaX));
    const newHeight = Math.max(500, Math.min(1000, resizeStartRef.current.startHeight + deltaY));

    setSize({
      width: newWidth,
      height: newHeight
    });
  }, [isResizing]);

  const handleResizeMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  // 拖拽事件处理
  const handleDragMouseDown = useCallback((e: React.MouseEvent) => {
    // 只有点击标题栏才能拖拽
    if (isResizing) return;

    setIsDragging(true);
    dragStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      startX: position.x,
      startY: position.y
    };

    e.preventDefault();
    e.stopPropagation();
  }, [position, isResizing]);

  const handleDragMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - dragStartRef.current.x;
    const deltaY = e.clientY - dragStartRef.current.y;

    setPosition({
      x: dragStartRef.current.startX + deltaX,
      y: dragStartRef.current.startY + deltaY
    });
  }, [isDragging]);

  const handleDragMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 绑定调整大小的全局鼠标事件
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMouseMove);
      document.addEventListener('mouseup', handleResizeMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleResizeMouseMove);
        document.removeEventListener('mouseup', handleResizeMouseUp);
      };
    }
  }, [isResizing, handleResizeMouseMove, handleResizeMouseUp]);

  // 绑定拖拽的全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleDragMouseMove);
      document.addEventListener('mouseup', handleDragMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleDragMouseMove);
        document.removeEventListener('mouseup', handleDragMouseUp);
      };
    }
  }, [isDragging, handleDragMouseMove, handleDragMouseUp]);

  // ESC键关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);
  
  if (!isOpen) return null;
  
  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* 主容器 */}
          <motion.div
            ref={containerRef}
            className={`fixed bg-white rounded-xl shadow-2xl z-50 flex overflow-hidden ${
              isResizing ? 'cursor-nw-resize' : isDragging ? 'cursor-grabbing' : ''
            }`}
            style={{
              left: `calc(50% + ${position.x}px)`,
              top: `calc(50% + ${position.y}px)`,
              transform: 'translate(-50%, -50%)',
              width: `${size.width}px`,
              height: `${size.height}px`,
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              filter: 'drop-shadow(0 0 20px rgba(147, 51, 234, 0.1))',
              willChange: isResizing || isDragging ? 'width, height, left, top' : 'auto',
              opacity: isDragging ? 0.95 : 1
            }}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            {/* 标题栏 */}
            <div
              className={`absolute top-0 left-0 right-0 h-12 bg-gradient-to-r from-purple-600 to-indigo-600 text-white flex items-center justify-between px-4 rounded-t-xl ${
                isDragging ? 'cursor-grabbing' : 'cursor-grab'
              }`}
              onMouseDown={handleDragMouseDown}
            >
              <div className="flex items-center space-x-3 flex-1">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h1 className="font-semibold">思考画布管理器</h1>
                {/* 拖拽提示图标 */}
                <svg className="w-4 h-4 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* 帮助按钮 */}
                <button
                  className="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
                  title="快捷键帮助"
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </button>

                {/* 关闭按钮 */}
                <button
                  onClick={onClose}
                  className="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
                  title="关闭 (ESC)"
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* 主内容区域 */}
            <div className="flex w-full h-full pt-12">
              {/* 左侧功能区 */}
              <CanvasSidebar />
              
              {/* 右侧编辑区 */}
              <CanvasEditor />
            </div>
            
            {/* 调整大小手柄 - 右下角 */}
            <div
              className={`
                absolute bottom-0 right-0 w-6 h-6 cursor-nw-resize z-10
                bg-gradient-to-br from-purple-400 to-indigo-500
                hover:from-purple-500 hover:to-indigo-600
                transition-all duration-200
                ${isResizing ? 'opacity-100 scale-110' : 'opacity-70 hover:opacity-100 hover:scale-105'}
              `}
              onMouseDown={handleResizeMouseDown}
              style={{
                clipPath: 'polygon(100% 0%, 0% 100%, 100% 100%)'
              }}
              title="拖拽调整大小"
            >
              {/* 调整手柄的纹理线条 */}
              <div className="absolute bottom-1 right-1 w-3 h-3">
                <div className="absolute bottom-0 right-0 w-full h-0.5 bg-white/60"></div>
                <div className="absolute bottom-1 right-0 w-full h-0.5 bg-white/60"></div>
                <div className="absolute bottom-2 right-0 w-full h-0.5 bg-white/60"></div>
                <div className="absolute bottom-0 right-0 w-0.5 h-full bg-white/60"></div>
                <div className="absolute bottom-0 right-1 w-0.5 h-full bg-white/60"></div>
                <div className="absolute bottom-0 right-2 w-0.5 h-full bg-white/60"></div>
              </div>
            </div>

            {/* 快捷键提示（悬浮在左下角，避免与调整手柄冲突） */}
            <div className="absolute bottom-4 left-4 bg-black/80 text-white text-xs px-3 py-2 rounded-lg opacity-60 hover:opacity-100 transition-opacity">
              <div className="space-y-1">
                <div>ESC - 关闭</div>
                <div>Ctrl+S - 保存</div>
                <div>Ctrl+E - 切换预览</div>
                <div>标题栏 - 拖拽移动</div>
                <div>右下角 - 调整大小</div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
  
  // 使用Portal渲染到body
  return typeof document !== 'undefined' 
    ? createPortal(modalContent, document.body)
    : null;
};

// 主组件（包含Provider）
const ThinkingCanvasManager: React.FC<ThinkingCanvasManagerProps> = (props) => {
  return (
    <ThinkingCanvasProvider initialData={props.initialData} onSave={props.onSave}>
      <ThinkingCanvasManagerContent {...props} />
    </ThinkingCanvasProvider>
  );
};

export default ThinkingCanvasManager;
