/**
 * 脑洞方向存储服务
 * 负责管理用户保存的脑洞方向数据
 */

/**
 * 保存的脑洞方向数据结构
 */
export interface SavedBrainholeDirection {
  id: string;
  name: string; // 用户自定义名称
  direction: string; // 原始脑洞方向内容
  genre: string; // 类型标签
  targetAudience: string; // 目标受众
  keywords: string[]; // 关键词标签
  createdAt: Date;
  usageCount: number; // 使用次数
  lastUsedAt: Date;
}

/**
 * 脑洞方向存储服务类
 */
export class BrainholeDirectionStorage {
  private static readonly STORAGE_KEY = 'saved_brainhole_directions';
  private static readonly MAX_DIRECTIONS = 50; // 最大保存数量

  /**
   * 获取所有保存的脑洞方向
   */
  static getAll(): SavedBrainholeDirection[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (!data) return [];
      
      const directions = JSON.parse(data);
      // 转换日期字符串为Date对象
      return directions.map((dir: any) => ({
        ...dir,
        createdAt: new Date(dir.createdAt),
        lastUsedAt: new Date(dir.lastUsedAt)
      }));
    } catch (error) {
      console.error('Failed to load saved brainhole directions:', error);
      return [];
    }
  }

  /**
   * 保存新的脑洞方向
   */
  static save(direction: Omit<SavedBrainholeDirection, 'id' | 'createdAt' | 'usageCount' | 'lastUsedAt'>): SavedBrainholeDirection {
    const directions = this.getAll();
    
    // 检查是否已存在相同内容
    const existing = directions.find(d => d.direction === direction.direction);
    if (existing) {
      throw new Error('该脑洞方向已存在');
    }

    // 如果超过最大数量，删除最旧的
    if (directions.length >= this.MAX_DIRECTIONS) {
      directions.sort((a, b) => a.lastUsedAt.getTime() - b.lastUsedAt.getTime());
      directions.splice(0, directions.length - this.MAX_DIRECTIONS + 1);
    }

    const newDirection: SavedBrainholeDirection = {
      ...direction,
      id: this.generateId(),
      createdAt: new Date(),
      usageCount: 0,
      lastUsedAt: new Date()
    };

    directions.push(newDirection);
    this.saveToStorage(directions);
    
    return newDirection;
  }

  /**
   * 更新脑洞方向使用统计
   */
  static updateUsage(id: string): void {
    const directions = this.getAll();
    const direction = directions.find(d => d.id === id);
    
    if (direction) {
      direction.usageCount++;
      direction.lastUsedAt = new Date();
      this.saveToStorage(directions);
    }
  }

  /**
   * 删除脑洞方向
   */
  static delete(id: string): boolean {
    const directions = this.getAll();
    const index = directions.findIndex(d => d.id === id);
    
    if (index !== -1) {
      directions.splice(index, 1);
      this.saveToStorage(directions);
      return true;
    }
    
    return false;
  }

  /**
   * 更新脑洞方向信息
   */
  static update(id: string, updates: Partial<Pick<SavedBrainholeDirection, 'name' | 'keywords'>>): boolean {
    const directions = this.getAll();
    const direction = directions.find(d => d.id === id);
    
    if (direction) {
      Object.assign(direction, updates);
      this.saveToStorage(directions);
      return true;
    }
    
    return false;
  }

  /**
   * 根据关键词搜索
   */
  static search(query: string): SavedBrainholeDirection[] {
    const directions = this.getAll();
    const lowerQuery = query.toLowerCase();
    
    return directions.filter(direction => 
      direction.name.toLowerCase().includes(lowerQuery) ||
      direction.direction.toLowerCase().includes(lowerQuery) ||
      direction.genre.toLowerCase().includes(lowerQuery) ||
      direction.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 按使用频率排序
   */
  static sortByUsage(): SavedBrainholeDirection[] {
    const directions = this.getAll();
    return directions.sort((a, b) => b.usageCount - a.usageCount);
  }

  /**
   * 按创建时间排序
   */
  static sortByDate(): SavedBrainholeDirection[] {
    const directions = this.getAll();
    return directions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 获取统计信息
   */
  static getStats() {
    const directions = this.getAll();
    const totalUsage = directions.reduce((sum, dir) => sum + dir.usageCount, 0);
    const genres = [...new Set(directions.map(dir => dir.genre))];
    
    return {
      total: directions.length,
      totalUsage,
      averageUsage: directions.length > 0 ? totalUsage / directions.length : 0,
      genres: genres.length,
      mostUsed: directions.length > 0 ? Math.max(...directions.map(dir => dir.usageCount)) : 0
    };
  }

  /**
   * 导出数据
   */
  static export(): string {
    const directions = this.getAll();
    return JSON.stringify(directions, null, 2);
  }

  /**
   * 导入数据
   */
  static import(data: string): { success: number; errors: string[] } {
    try {
      const importedDirections = JSON.parse(data);
      const errors: string[] = [];
      let success = 0;

      for (const dir of importedDirections) {
        try {
          this.save({
            name: dir.name,
            direction: dir.direction,
            genre: dir.genre,
            targetAudience: dir.targetAudience,
            keywords: dir.keywords || []
          });
          success++;
        } catch (error) {
          errors.push(`导入失败: ${dir.name} - ${error}`);
        }
      }

      return { success, errors };
    } catch (error) {
      return { success: 0, errors: ['数据格式错误'] };
    }
  }

  /**
   * 清空所有数据
   */
  static clear(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * 私有方法：保存到localStorage
   */
  private static saveToStorage(directions: SavedBrainholeDirection[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(directions));
    } catch (error) {
      console.error('Failed to save brainhole directions:', error);
      throw new Error('保存失败，可能是存储空间不足');
    }
  }

  /**
   * 私有方法：生成唯一ID
   */
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
